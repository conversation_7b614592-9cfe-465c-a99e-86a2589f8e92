
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>代言人统计</title>
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="../../../../../css/searchList.css" />
<link rel="stylesheet" type="text/css" href="../../../../../css/spokeListManage.css" />
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" 
	src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
<script type="text/javascript" src="spokeListCtrl.js"></script>
</head>
<body ng-app='myApp' ng-controller='spokeListController' ng-init="init();">
    <div class="cooperation-manage" >
        <div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_MERCHANTSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;
        	<span class="second-tab" ng-bind="'COMMON_SPOKESSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;
        	<span class="second-tab" ng-bind="'SPOKES_WATCHDETAIL'|translate"></span></div>
        <top:menu chose-index="1" page-url="/qycy/ecpmp/view/InfoStatistics/merchantStatistics/spokesStatistics/spokeListManage" 
        	list-index="15"></top:menu>
        <!-- <top:menu chose-index="0" page-url="/qycy/ecpmp/view/spokeListManage" list-index="5" ng-if="!isSuperManager"></top:menu> -->
        <div class="cooperation-search">
            <form class="form-horizontal">
                <div class="form-group form-inline">
                	<label for="activityName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" 
                		style="white-space:nowrap" ng-bind="'COMMON_ACTIVITYNAME'|translate">
	                </label>
	                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                            <input autocomplete="off" type="text" class="form-control" id="activityName" 
                            placeholder="{{'COMMON_PLEASEINPUTACTIVITYMANAGE'|translate}}" 
                            ng-model="querySpokesActivityCond.activityName">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1">
	                    <button ng-click="querySpokeList()" type="submit" class="btn search-btn">
	                    <icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span>
	                    </button>
	                </div>
                </div>
            </form>
        </div>
        <div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="exportFile()" id="exportSpokeList"><icon class="export-iocn"></icon>
            	<span ng-bind="'COMMON_EXPORT'|translate"></span></button>
            <!-- <button type="submit" class="btn add-btn" id="exportspokeList"><icon class="export-iocn"></icon>
            	<a href="{{exportUrl}}" download target="_blank" ng-bind="'COMMON_EXPORT'|translate" style="color:#705de1"></a>
            </button> -->
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'COMMON_SPOKESSTATISTICS'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th ng-bind="'MERCHANT_MERCHANTNAME'|translate"></th>
                        <th ng-bind="'COMMON_ACTIVITYNAME'|translate"></th>
                        <th ng-bind="'SPOKES_SPOKESTIME'|translate"></th>
                        <th ng-bind="'SPOKES_SPOKESDAYS'|translate"></th>
                        <th ng-bind="'SPOKES_SPOKESSCREENCOUNT'|translate"></th><!-- style="width: 140px;" -->
                        <th ng-bind="'SPOKES_SPOKESENDPHONECOUNT'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in spokeListData">
                        <td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
                        <td title="{{item.activityName}}">{{item.activityName}}</td>
                        <td title="{{formatDate(item.spokeBeginTime)}}-{{formatDate(item.spokeEndTime)}}">
                        	{{formatDate(item.spokeBeginTime)}}-{{formatDate(item.spokeEndTime)}}</td>
                        <td title="{{item.dayCount}}">{{item.dayCount}}</td>
                        <td title="{{item.screenCount}}">{{item.screenCount}}</td>
                        <td title="{{item.endPhoneCount}}">{{item.endPhoneCount}}</td>
                    </tr>
                    <tr ng-show="spokeListData.length<=0">
                        <td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    	<div>
	    	<button type="submit" class="btn btn-back" ng-click="goBack()" style="margin: 20px"
								ng-bind="'COMMON_BACK'|translate"></button>
	        <ptl-page tableId="0" change="querySpokeList('justPage')"></ptl-page>
      	</div>
    </div>

<!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838'>
                                {{tip|translate}}
                            </p></div>
                        </div>
                        <div class="modal-footer">
                            <!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
                            	ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>
    
</body>
</html>