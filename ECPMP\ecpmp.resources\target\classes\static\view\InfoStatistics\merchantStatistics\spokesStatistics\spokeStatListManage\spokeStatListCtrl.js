var app = angular.module("myApp",["util.ajax",'page',"top.menu","angularI18n","service.common"])
app.controller('spokeStatListController', ['$scope','$rootScope','$location','RestClientUtil','CommonUtils',function ($scope,$rootScope,$location,RestClientUtil,CommonUtils) {
	// $(function(){
	//     $('#exportSpokeStatList').bind("click", function () {
	//     	window.open($scope.exportUrl);  
	//     });    
    // });
    // $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/spokeService/downSpokeStatCsvFile?msisdn="+$scope.querySpokeStatCond.msisdn;
	$scope.init=function(){
        $scope.token=$.cookie('token')||'';
        //初始化分页信息
        $scope.pageInfo =[
            {
              "totalPage": 1,
              "totalCount": 0,
              "pageSize": '10',
              "currentPage": 1
            }
          ];
        $scope.spokeStatListData=[];

        //初始化搜索条件querySpokeStatCond.
        $scope.querySpokeStatCond={
        	"msisdn": "",//15588888888
        };
        $scope.queryspokeStatList();
        $scope.exportFile=function(){
            var req = {
              "param":{
                "msisdn":$scope.querySpokeStatCond.msisdn,
                "token":$scope.token,
                "isExport":1
              },
              "url":"/qycy/ecpmp/ecpmpServices/spokeService/downSpokeStatCsvFile",
              "method":"get"
            }
            CommonUtils.exportFile(req);
          }
    };
    $scope.formatDate=function(str){
        if(!str){
            return 'format error';
        }
        var newDateStr="";
        newDateStr=str.substr(0,4)+'-'+str.substr(4,2)+'-'+str.substr(6,2);
        return newDateStr;

    };
    $scope.gotoDetail=function(item){
        //传入msisdn，作为唯一标识
    	$.cookie("spokeMsisdn",item.msisdn,{path: '/' });
        location.href='../spokeListManage/spokeListManage.html';
    }
	$scope.queryspokeStatList = function (condition) {
        if(condition!='justPage'){
            var req={
                "querySpokeStatCond":$scope.querySpokeStatCond||'',
                "page":{
                    "pageNum":1,
                    "pageSize":parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal":"1",
                }
            };
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/spokeService/downSpokeStatCsvFile?msisdn="+$scope.querySpokeStatCond.msisdn;
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.page.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
			type: 'POST',
			url: "/ecpmp/ecpmpServices/spokeService/querySpokeStatList",
			data: JSON.stringify(req),
			success: function (result) {
				$rootScope.$apply(function () {
                    var data = result.result;
                    if(data.resultCode=='1030100000'){
                        $scope.spokeStatListData=result.spokeStatList||[];
                        $scope.pageInfo[0].totalCount=parseInt(result.totalcount)||0;
                        $scope.pageInfo[0].totalPage = result.totalcount !==0 ?Math.ceil(result.totalcount / parseInt($scope.pageInfo[0].pageSize)) :1;
                    }else{
                        $scope.spokeStatListData=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
				})
				
            },
            error:function(){
                $rootScope.$apply(function(){
                    $scope.tip="**********";
                    $('#myModal').modal();
                    }
                )
            }
		});
          
	}
}])
app.config(['$locationProvider', function($locationProvider) {
    $locationProvider.html5Mode({
        enabled:true,
        requireBase:false
    });
  }])