var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n", "cy.uploadify"])
/**/
app.controller("settingController", ['$scope', '$rootScope', '$location', 'RestClientUtil',
    function ($scope, $rootScope, $location, RestClientUtil) {
        $scope.init = function () {
            $scope.invalid = {};
            $scope.enterpriseType = $.cookie('enterpriseType') || '';
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
            $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
            $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
            //获取enterpriseName
            $scope.enterpriseName = $.cookie('enterpriseName');
            //判断最终调接口的enterpriseID,enterpriseName
            if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
                $scope.enterpriseID = $scope.subEnterpriseID;
                $scope.enterpriseName = $scope.subEnterpriseName;
            }
            let req = {
                "enterpriseID": $scope.enterpriseID
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryPortList",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        $scope.enterprisePortList = result.enterprisePortList;
                        $scope.object = result.enterprisePortList[0];
                        if ($scope.object.groupSendFlashPort) {
                            $scope.groupSendFlashPort = $scope.object.groupSendFlashPort.join("，");
                        }
                        else
                        {
                            $scope.groupSendFlashPort = '10658086';
                        }
                        if ($scope.object.groupSendSmsPort) {
                            $scope.groupSendSmsPort = $scope.object.groupSendSmsPort.join("，");

                        }
                        else
                        {
                            $scope.groupSendSmsPort = '10658086';
                        }
                        if ($scope.object.groupSendMmsPort) {
                            $scope.groupSendMmsPort = $scope.object.groupSendMmsPort.join("，");

                        }
                        if ($scope.object.groupSendFluid) {
                            $scope.groupSendFluid = $scope.object.groupSendFluid;
                        } else {
                            $scope.groupSendFluid = 30;
                        }
                    })
                }


            })
        };

        $scope.changeFluid = function () {
            $scope.invalid.groupSendFluid_max = false;
            if ($scope.groupSendFluid > 50) {
                $scope.invalid.groupSendFluid_max = true;
            } else {
                $scope.invalid.groupSendFluid_max = false;
            }
        }

        $scope.saveFluid = function () {
            let groupSendSmsPort = "";
            if ($scope.object.groupSendSmsPort) {
                groupSendSmsPort = $scope.object.groupSendSmsPort.join("|");
            }
            let req = {
                "enterpriseID": $scope.enterpriseID,
                "groupSendFluid": $scope.groupSendFluid,
                "hotlineSmsPort": groupSendSmsPort
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/updateSendPort",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        if (result.result.resultCode == "1030100000") {
                            $scope.tip = "保存成功";
                            $('#myModal').modal();
                        } else {
                            $scope.tip = result.result.resultDesc;
                            $('#myModal').modal();

                        }
                    })
                }
            });
        };
    }]);
