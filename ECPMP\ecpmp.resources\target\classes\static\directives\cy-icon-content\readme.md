# cy-icon-content

## 简介

`cy-icon-content` 是一个基于 AngularJS 的提示组件，当用户将鼠标悬停在图标上时，会以弹出框形式展示提示内容。它支持多种位置定位、HTML内容显示，并能从 localStorage 中读取预设内容。

## 特性

- 悬停显示提示内容
- 支持 12 种不同的显示位置
- 内容支持 HTML 格式
- 内容支持换行符转换
- 可从 localStorage 读取预设内容
- 支持自定义图标
- 提示框内容支持滚动，适应长内容

## 安装与引入

### 文件结构

```
cy-icon-content/
├── cy-icon-content.js     # 组件逻辑
├── cy-icon-content.html   # 组件模板
├── cy-icon-content.css    # 组件样式
└── README.md              # 本文档
```

### 引入方式

```html
<!-- 引入样式文件 -->
<link rel="stylesheet" href="/qycy/ecpmp/directives/cy-icon-content/cy-icon-content.css">

<!-- 引入脚本文件 -->
<script src="/qycy/ecpmp/directives/cy-icon-content/cy-icon-content.js"></script>

<!-- 在应用模块中添加依赖 -->
<script>
  angular.module('yourApp', ['cy.icon.content']);
</script>
```

## 属性说明

| 属性名 | 类型 | 默认值 | 描述 |
|-------|-----|-------|------|
| tip-content | String | "" | 直接指定提示内容，支持 HTML |
| position | String | "right-top" | 提示框显示位置 |
| content-type | String | "" | 内容类型编号，用于从 localStorage 获取内容 |
| img-url | String | "/qycy/ecpmp/assets/images/ques.png" | 图标图片地址 |

## 位置选项

组件支持以下 12 种位置选项：

- `top-left`: 左上
- `top`: 正上
- `top-right`: 右上
- `right-top`: 右上侧
- `right`: 正右
- `right-bottom`: 右下侧
- `bottom-left`: 左下
- `bottom`: 正下
- `bottom-right`: 右下
- `left-top`: 左上侧
- `left`: 正左
- `left-bottom`: 左下侧

## 使用示例

### 基础用法

```html
<!-- 基础用法 -->
<cy:icon:content
  tip-content="这是一段提示文字">
</cy:icon:content>

<!-- 指定位置 -->
<cy:icon:content
  tip-content="这是一段提示文字"
  position="top">
</cy:icon:content>

<!-- 自定义图标 -->
<cy:icon:content
  tip-content="这是一段提示文字"
  img-url="/custom/icon.png">
</cy:icon:content>
```

### 使用内容类型

组件可以从 localStorage 中读取预设内容，localStorage 的 `hintContent` 项应该包含以下结构：

```javascript
[
  {
    "type": "1",
    "hintContent": "主叫彩印提示内容..."
  },
  {
    "type": "2",
    "hintContent": "被叫彩印提示内容..."
  },
  // 其他内容类型...
]
```

使用方式：

```html
<!-- 使用内容类型 -->
<cy:icon:content content-type="1"></cy:icon:content>

<!-- 动态内容类型 -->
<cy:icon:content content-type="{{dynamicType}}"></cy:icon:content>
```

特殊说明：当 `content-type="3"` 时，组件会自动组合类型 1 和类型 2 的内容。

### 表单场景示例

```html
<div class="form-group">
  <label class="control-label">
    用户名
    <cy:icon:content
      tip-content="用户名需要包含字母和数字，长度在6-20个字符之间">
    </cy:icon:content>
  </label>
  <input type="text" class="form-control">
</div>
```

### 在页面不同位置使用

```html
<!-- 标题字段提示 -->
<div class="form-group">
  <label class="control-label">标题：</label>
  <div class="col-md-4">
    <input type="text" class="form-control">
  </div>
  <cy:icon:content content-type="1"></cy:icon:content>
</div>

<!-- 运营商信息提示 -->
<div class="form-group">
  <label class="control-label">运营商：</label>
  <div class="col-md-10">
    <!-- 运营商选项 -->
    <cy:icon:content content-type="5"></cy:icon:content>
  </div>
</div>

<!-- 签名信息提示 -->
<div class="form-group">
  <label class="control-label">签名：</label>
  <div class="col-md-4">
    <input type="text" class="form-control">
  </div>
  <cy:icon:content content-type="4"></cy:icon:content>
</div>
```

## 本地存储内容初始化

可以在应用启动时初始化提示内容：

```javascript
// 在应用启动时初始化提示内容
function initHintContent() {
  // 默认提示内容
  const defaultHintContent = [
    {
      type: "1",
      hintContent: "主叫彩印：<br/>1）只能展示身份，或在身份展示基础上可附带添加企业文化宣传（如：企业文化、企业创始年份、品牌宣传口号、与具体营销场景非强相关联的品牌理念等）或关怀语；<br/>2）严禁营销、商业信息（营销、商业内容界定包括但不限于：产品、业务范围、服务范围、业务咨询方式介绍等内容）"
    },
    {
      type: "2",
      hintContent: "被叫彩印：可投递商业营销信息"
    },
    // 其他默认内容
  ];

  try {
    // 如果本地存储中没有数据，则使用默认数据
    if (!localStorage.getItem("hintContent")) {
      localStorage.setItem("hintContent", JSON.stringify(defaultHintContent));
    }
  } catch (e) {
    console.error("初始化提示内容失败:", e);
  }
}
```

## 自定义样式

可以通过修改 CSS 来自定义组件样式：

```css
/* 自定义图标大小 */
.custom-tooltip-wrapper .img-wrap img {
  width: 18px;
  height: 18px;
}

/* 自定义提示框样式 */
.tooltip-content {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 自定义内容样式 */
.tooltip-inner {
  font-size: 13px;
  line-height: 1.5;
  color: #444;
}
```

## 常见问题

### 内容没有更新

**问题**: 切换 content-type 后提示内容没有更新。

**解决方案**:
- 确保使用动态绑定 `content-type="{{yourVariable}}"`
- 检查 localStorage 中是否有对应类型的内容
- 在控制器中检查变量值是否正确更新

### 位置显示异常

**问题**: 提示框位置不正确或被截断。

**解决方案**:
- 调整 position 属性值
- 检查父容器是否设置了 `overflow: hidden`
- 确保组件周围有足够的空间

### 富文本内容不生效

**问题**: HTML 标签没有正确渲染。

**解决方案**:
- 确保正确引入了 Angular 的 `$sanitize` 模块
- 检查 HTML 标签格式是否正确
- 使用 `<br/>` 代替 `\n` 表示换行

## 浏览器兼容性

组件兼容以下浏览器：
- Chrome 50+
- Firefox 45+
- Safari 10+
- Edge 14+
- IE 11

## 注意事项

1. 组件依赖 AngularJS 和 jQuery，请确保已正确引入
2. 组件使用本地存储，确保用户浏览器允许本地存储
3. 长内容会出现滚动条，最大高度为 300px
4. 当前版本不支持移动端触摸事件，仅支持鼠标事件

## 维护与贡献

组件由 ECPMP 团队维护，如有问题或建议请联系：

- 邮箱: <EMAIL>
- 内部工单: IT支持系统 > ECPMP > 前端组件
