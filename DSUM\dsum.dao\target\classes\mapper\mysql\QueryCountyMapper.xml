<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.QueryCountyMapper">

    <resultMap id="provinceMap" type="com.huawei.jaguar.dsum.dao.domain.CountyWrapper">
        <result property="cityID" column="cityID"/>
        <result property="countyID" column="countyID"/>
        <result property="countyName" column="countyName"/>
    </resultMap>

    <select id="queryCountyList" resultMap="provinceMap">
        SELECT t.cityID,t.countyID,t.countyName
        from dsum_t_county t
        where 1=1
        <if test="_parameter != null and _parameter!=''">
           and t.cityID = #{cityID}
        </if>
    </select>
</mapper>