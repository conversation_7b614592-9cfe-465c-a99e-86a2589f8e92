var app = angular.module("myApp", ["util.ajax", "top.menu","angularI18n","cy.uploadify", "preview","service.common"])
app.controller('enterpriseController', function ($scope, $rootScope, $http, $location, RestClientUtil,CommonUtils) {
	
	  $scope.filePicker = "filePicker";
	  $scope.accepttype = "jpg,jpeg,png,pdf";
	  $scope.isValidate = false;
	  $scope.filesize = 20;
	  $scope.mimetypes = ".jpg,.jpeg,.png,.pdf";
	  
	  $scope.isCreateThumbnail = true;
	  $scope.uploadurl ='/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
	  $scope.uploadDesc = "仅支持一张图片，仅支持jpg，jpeg，png，pdf格式";
	  $scope.numlimit = 1;
	  $scope.uploadParam = {
	    enterpriseId: $scope.id ||'',
	    fileUse: 'businessLicense'
	  };
	
	  $scope.$on("uploadifyid1",function(event,fileUrl){
		  if(fileUrl){
			  $scope.urlList = [fileUrl];
			  $scope.urlList2 = [fileUrl];
		  }else{
			  $scope.urlList = [];
			  $scope.urlList2 = [];
		  }
          $scope.businessLicenseURL = fileUrl;
	  });

  /* 从url中获取参数 */
  $scope.convertUrlToPara = function ($scope) {
    var url = angular.copy(window.location.href);
    url = url.split("?")[1];
    /*.substring(0, url.length-2)*/
    var res = {};
    if (!!url) {
      var para = url.split("&");
      var arr = [];
      var len = para.length;
      for (var i = 0; i < len; i++) {
        arr = para[i].split("=");
        res[arr[0]] = arr[1];
      }
    }
    return res;
  };

  $scope.formatDate=function(str){
      if(!str){
          return 'format error';
      }
      var newDateStr="";
      newDateStr=str.substring(0, 4) + "-" + str.substring(4, 6) + "-" + str.substring(6, 8) + " " 
      	+ str.substring(8, 10) + ":" + str.substring(10, 12);
      return newDateStr;
  }
  
  /* 查询省市 */
  $scope.queryProvinceAndCity = function ($scope) {
    var queryProvinceListReq = {};
    /*查询省份*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
      data: JSON.stringify(queryProvinceListReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.provinceList = data.provinceList;
            if (!!$scope.provinceList) {
              var provinceIds = [];
              jQuery.each($scope.provinceList, function (i, e) {
                provinceIds[i] = e.provinceID;
              });
              var queryCityListReq = {};
              queryCityListReq.provinceIDs = provinceIds;
              
              /*查询地市*/
              RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
                data: JSON.stringify(queryCityListReq),
                success: function (data) {
                  $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                      $scope.cityList = $scope.mapToList(data.cityList);
                      if ($scope.operate == 'detail' || $scope.operate == 'edit') {
                        $scope.changeSelectedProvince($scope.enterpriseInfo.provinceID);
                      }
                    }else {
    	                $scope.tip =result.resultCode;
    	                $('#myModal').modal();
    	              }
                  })
                },
                error:function(){
                    $rootScope.$apply(function(){
                        $scope.tip='**********';
                        $('#myModal').modal();
                        }
                    )
                }
              });
            }
          }else {
              $scope.tip=data.result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
        	  	  $scope.tip = '**********';
                  $('#myModal').modal();
              }
          )
      }
    });
  };


  /* 初始化新增企业 */
  $scope.initEnterprise = function ($scope) {
    $scope.enterpriseInfo = {};
    $scope.accountInfo = {};
    $scope.fileUrl ='';
    $scope.urlList =[];
    $scope.urlList2 =[];
    var loginRoleType=$.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
    $scope.isZhike = (loginRoleType=='zhike');
    $scope.isAgent = (loginRoleType=='agent');
    $scope.isProvincial = (loginRoleType=='provincial');
    //获取operationType和enterpriseType
    var para = $scope.convertUrlToPara($scope);
    if (para.operationType) {
      $.cookie("operationType",para.operationType,{path: '/' });
      $scope.operate = para.operationType;
    }else{
      $scope.operate = $.cookie("operationType");
      if(!$scope.isSuperManager){
    	  $scope.operate = 'edit';
      }
    }
    $scope.enterpriseType = 2;
    $scope.enterpriseNameValidate = true;
    $scope.custIDValidate = true;
    $scope.organizationIDValidate = true;
    $scope.contractValidate = true;
    $scope.msisdnValidate = true;
    $scope.accountNameValidate = true;
    $scope.accountNameExist = false;
    $scope.passwordValidate = true;
    $scope.rePasswordValidate = true;
    $scope.businessLicenseVali ='true';
    $scope.passwordValidateDesc = '';
    $scope.queryProvinceAndCity($scope);

    if ($scope.operate == 'add') {
      $scope.enterpriseInfo.showUpload = true;
      $scope.id = '';
    }
    if ($scope.operate == 'edit') {
    	$scope.id = JSON.parse($.cookie("enterpriseID"));
	    $scope.enterpriseInfo.showUpload = true;
	    $scope.queryEnterpriseDetails($scope);
	  }
    if ($scope.operate == 'detail') {
    	$scope.id = JSON.parse($.cookie("enterpriseID"));
    	$scope.queryEnterpriseDetails($scope);
    }

  };

  /*查询企业详情 */
  $scope.queryEnterpriseDetails = function ($scope) {
    var req = {};
    req.id = $scope.id;
    var pageParameter = {};
    pageParameter.pageNum = 1;
    pageParameter.pageSize = 1000;
    pageParameter.isReturnTotal = 1;
    req.pageParameter = angular.copy(pageParameter);
    /*查询企业列表*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.enterpriseInfo = data.enterprise;
            $scope.setEnterpriseDetails($scope);
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };

  /* 非新增时企业详情 赋值*/
  $scope.setEnterpriseDetails = function ($scope) {
    $scope.passwordValidate = true;
    $scope.enterpriseNameTemp = $scope.enterpriseInfo.enterpriseName;
    $scope.custIDTemp = $scope.enterpriseInfo.custID;
    $scope.organizationIDTemp = $scope.enterpriseInfo.organizationID;
    $scope.contractTemp = $scope.enterpriseInfo.contract;
    $scope.msisdnTemp = $scope.enterpriseInfo.msisdn;
    $scope.cityIDTemp = $scope.enterpriseInfo.cityID;
    $scope.provinceIDTemp = $scope.enterpriseInfo.provinceID;
    $scope.businessLicenseURL = $scope.enterpriseInfo.businessLicenseURL;
    $scope.businessLicenseURLTemp = $scope.enterpriseInfo.businessLicenseURL;
    $scope.accountInfo = $scope.enterpriseInfo.accountInfo;
    if($scope.enterpriseInfo.businessLicenseURL){
    	if (($scope.enterpriseInfo.businessLicenseURL).substring(0,4) == 'http') 
    	{
    		$scope.fileUrl = $scope.enterpriseInfo.businessLicenseURL
		}
    	else
    	{
    		$scope.fileUrl = CommonUtils.formatPic($scope.enterpriseInfo.businessLicenseURL).review;
    	}
        $scope.urlList=[$scope.fileUrl];
        $scope.urlList2=[$scope.enterpriseInfo.businessLicenseURL];
    }
    $scope.createTimeTemp = $scope.formatDate($scope.enterpriseInfo.createTime);
    if ($scope.cityList) {
      $scope.changeSelectedProvince($scope.enterpriseInfo.provinceID);
      if ($scope.operate == 'detail' || $scope.operate == 'edit') {
	    	$scope.provinceID = $scope.enterpriseInfo.provinceID;
	    	if($scope.subCityList){
	    		jQuery.each($scope.subCityList, function (i, e) {
	    	        if (e.cityID == $scope.enterpriseInfo.cityID) {
	    	        	$scope.selectedCity = e;
	    	        }
	    	      });
	    		$scope.selectedCityID = $scope.selectedCity.cityID;
	    	}
	    }
    }
  };

  /* 验证企业名称 */
  $scope.checkEnterpriseName = function (enterpriseName,condition) {
	  $scope.condition =condition;
	  $scope.enterpriseNameValidate =true;
	  $scope.enterpriseInfo.enterpriseNameDesc ='';
	  if($scope.enterpriseNameTemp == enterpriseName && $scope.operate=='edit'){
		  $scope.enterpriseNameValidate =true;
		  $scope.enterpriseInfo.enterpriseNameDesc = '';
		  $scope.enterpriseNameExist =false;
		  if($scope.condition =='save'){
			  $scope.checkPassword('save');
		  }else{
			  return;
		  }
	  }else{
		  $scope.enterpriseNameExist = false;
	      var QueryAccountListReq = {};
	      QueryAccountListReq.content = angular.copy(enterpriseName);
	      QueryAccountListReq.serviceType = 1;
	      /*模糊匹配企业名称，匹配到则飘红*/
	      RestClientUtil.ajaxRequest({
	        type: 'POST',
	        url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
	        data: JSON.stringify(QueryAccountListReq),
	        success: function (data) {
	          $rootScope.$apply(function () {
	            var result = data.result;
	            $scope.enterpriseInfo.enterpriseNameDesc = '';
	            //是否存在重复的用户名称
	            if (result.resultCode =='**********') {
	              $scope.enterpriseNameExist = true;
	              $scope.enterpriseInfo.enterpriseNameDesc = 'ENTERPRISE_ENTERPRISENAMEEXIST';
	            }
	            if(result.resultCode !='**********' && result.resultCode !='**********'){
	            	$scope.tip = result.resultCode;
	                $('#myModal').modal();
	            }
	            if($scope.condition =='save' && result.resultCode =='**********'){
	            	if($scope.operate == 'add'){
	            		$scope.checkPassword('save');
	            	}
	            	if($scope.operate == 'edit'){
	            		if($scope.accountInfo.password){
	            			$scope.checkPassword('save');
	            		}else{
	            			$scope.update();
	            		}
	            	}
	            }
	          })
	        },
	        error:function(){
	            $rootScope.$apply(function(){
	                $scope.tip='**********';
	                $('#myModal').modal();
	                }
	            )
	        }
	      });
		  /*
		  $scope.enterpriseNameValidate = $scope.validate(enterpriseName, 256, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
		    if (!$scope.enterpriseNameValidate) {
		    	//$scope.enterpriseInfo.enterpriseNameDesc = 'ENTERPRISE_ENTERPRISENAMEDESC';
		    } else {
		      $scope.enterpriseNameExist = false;
		      var QueryAccountListReq = {};
		      QueryAccountListReq.content = angular.copy(enterpriseName);
		      QueryAccountListReq.serviceType = 1;
		      模糊匹配企业名称，匹配到则飘红
		      RestClientUtil.ajaxRequest({
		        type: 'POST',
		        url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
		        data: JSON.stringify(QueryAccountListReq),
		        success: function (data) {
		          $rootScope.$apply(function () {
		            var result = data.result;
		            $scope.enterpriseInfo.enterpriseNameDesc = '';
		            //是否存在重复的用户名称
		            if (result.resultCode =='**********') {
		              $scope.enterpriseNameExist = true;
		              $scope.enterpriseInfo.enterpriseNameDesc = 'ENTERPRISE_ENTERPRISENAMEEXIST';
		            }
		            if(result.resultCode !='**********' && result.resultCode !='**********'){
		            	$scope.tip = result.resultCode;
		                $('#myModal').modal();
		            }
		            if($scope.condition =='save' && result.resultCode =='**********'){
		            	if($scope.operate == 'add'){
		            		$scope.checkPassword('save');
		            	}
		            	if($scope.operate == 'edit'){
		            		if($scope.accountInfo.password){
		            			$scope.checkPassword('save');
		            		}else{
		            			$scope.update();
		            		}
		            	}
		            }
		          })
		        },
		        error:function(){
		            $rootScope.$apply(function(){
		                $scope.tip='**********';
		                $('#myModal').modal();
		                }
		            )
		        }
		      });
		    }
	  */}
  };

  $scope.mapToList = function (map) {
    var result = [];
    jQuery.each(map, function (_, o) {
    	if(_=='000'){
    		return;
    	}
        if (o) {
            o.key =o[0].provinceID;
            result.push(o);
        }
    });
    return result;
  };
  /* 新增时修改市 */
  $scope.changeSelectedCity = function (cityID){
	  $scope.selectedCityID = cityID;
  };
  /* 新增时修改省份 */
  $scope.changeSelectedProvince = function (selectedProvinceID) {
	  angular.forEach($scope.cityList, function (e, i) {
			if (e.key == selectedProvinceID) {
		        $scope.subCityList = angular.copy(e);
		      }
	    });
	    if (!selectedProvinceID || selectedProvinceID =='000') {
	      $scope.subCityList = null;
	      $scope.selectedCityID = null;
	    }else{
	    	if($scope.subCityList){
	    		$scope.selectedCity =$scope.subCityList[0];
	    		$scope.selectedCityID =$scope.selectedCity.cityID;
	    		$scope.selectedCityName =$scope.selectedCity.cityName;
	        	delete($scope.subCityList.key);
	    	}
	    }
	    if ($scope.subCityList) {
	        if ($scope.operate == 'detail' || $scope.operate == 'edit') {
	  	    	$scope.provinceID = selectedProvinceID;
  	    		jQuery.each($scope.subCityList, function (i, e) {
  	    	        if (e.cityID == $scope.enterpriseInfo.cityID) {
  	    	        	$scope.selectedCity = e;
  	    	        }
  	    	      });
  	    		$scope.selectedCityID = $scope.selectedCity.cityID;
	  	    }
	      }
  }


  /* 验证cust */
  $scope.checkCustID = function (custID) {
    $scope.custIDValidate = $scope.validate(custID, 64, /^[0-9a-zA-Z]+$/, false);
  };

  /* 验证organizationID */
  $scope.checkOrganizationID = function (organizationID) {
    $scope.organizationIDValidate = $scope.validate(organizationID, 32, /^[0-9a-zA-Z]+$/, false);
  };

  /* 验证contract */
  $scope.checkContract = function (contract) {
    $scope.contractValidate = 
    	$scope.validate(contract, 32, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
  };
  
  /* 验证手机号 */
  $scope.checkMsisdn = function (msisdn) {
    $scope.msisdnValidate = $scope.validate(msisdn, 11, /^[0-9]{11}$/, true);
  };

  /* 验证用户名称 */
  $scope.checkAccountName = function (accountName,condition) {
	$scope.condition = condition;
	$scope.accountNameValidate = true;
	$scope.accountNameExist = false;
    $scope.accountNameValidate = $scope.validate(accountName, 32, /^(?![0-9]+$)[0-9A-Za-z]+$/, true);
    if(!$scope.accountNameValidate){
    	return;
    }
    if ($scope.accountNameValidate) {
      $scope.accountNameExist = false;
      var pageParameter = {};
      pageParameter.pageNum = 1;
      pageParameter.pageSize = 1000;
      pageParameter.isReturnTotal = '0';
      var QueryAccountListReq = {};
      QueryAccountListReq.content = angular.copy(accountName);
      QueryAccountListReq.serviceType = 4;
      QueryAccountListReq.accountType= 3;
      /*模糊匹配企业名称，匹配到则飘红*/
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
        data: JSON.stringify(QueryAccountListReq),
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            //对比企业总列表，是否存在重复的企业名称
            if (result.resultCode  =='**********') {
              $scope.accountNameExist = true;
            }
            if(result.resultCode !='**********' && result.resultCode !='**********'){
            	$scope.tip=result.resultCode;
                $('#myModal').modal();
            }
            if($scope.condition =='save'){
            	$scope.save();//开始保存
            }
          })
        },
        error:function(){
            $rootScope.$apply(function(){
                $scope.tip='**********';
                $('#myModal').modal();
                }
            )
        }
      });
    }
  };

  /*验证密码，通过后台调用checkPwdRule接口 */
  $scope.checkPassword = function (condition) {
	$scope.condition =condition;
    $scope.passwordValidate = true;
    $scope.passwordValidateDesc = "";
    var pwd = $scope.accountInfo.password;
    var rePwd = $scope.accountInfo.rePassword;
    if(!$scope.accountInfo.password && $scope.operate =='edit'){
    	$scope.checkRePassword(pwd, rePwd,$scope.condition);
    	return;
    }else{
    	var checkPwdRuleReq = {};
        checkPwdRuleReq.password = pwd;
        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
          data: JSON.stringify(checkPwdRuleReq),
          success: function (data) {
            $rootScope.$apply(function () {
              var result = data.result;
              if (result.resultCode != '**********') {
                $scope.passwordValidate = false;
                  var passwordRuleList = data.passwordRuleList;
                  if(result.resultCode == '**********'){
                	  $scope.passwordValidateDesc ='ENTERPRISE_PASSWORDDESC';
                  }else{
                	  if(!passwordRuleList){
                		  $scope.passwordValidateDesc = result.resultDesc;
                	  }else{
                		  for (var i = 0; i < passwordRuleList.length; i++) {
                            	// $scope.passwordValidateDesc  = $scope.passwordValidateDesc + passwordRuleList[i].ruleName
                              $scope.passwordValidateDesc  = passwordRuleList[i].ruleName;

                            	// $scope.passwordValidateDesc  = $scope.passwordValidateDesc + ";";
                              }
                		  $scope.passwordValidateDesc=$scope.passwordValidateDesc.substring(0, $scope.passwordValidateDesc.length - 1);
                	  }
                  }
              } else {
            	if(!!rePwd){
            		$scope.checkRePassword(pwd, rePwd,$scope.condition);
            	}
              }
            })
          },
          error:function(){
              $rootScope.$apply(function(){
                  $scope.tip='**********';
                  $('#myModal').modal();
                  }
              )
          }
        });
    }
  };

  /*验证确认密码，与密码相同 */
  $scope.checkRePassword = function (pwd, rePwd,condition) {
	$scope.condition =condition;
    $scope.rePasswordValidate = true;
    if (!rePwd) {
    	if(!pwd && $scope.operate =='edit'){
    		if($scope.condition =='save'){
        	    $scope.update();
        	}else{
        		return;
        	}
    	}else{
    		$scope.rePasswordValidate = false;
    	}
    }else{
        if (pwd != rePwd) {
          $scope.rePasswordValidate = false;
        }else{
        	if($scope.condition =='save'){
        		if($scope.operate =='add'){
        	    	$scope.checkAccountName($scope.accountInfo.accountName,'save');
        	    }else{
        	    	$scope.update();
        	    }
        	}
        }
    }
  };

  /*保存前验证与赋值 */
  $scope.beforeSave = function () {
	$scope.condition ='save';
    $scope.enterpriseInfo.enterpriseType = $scope.enterpriseType;
    $scope.accountInfo.msisdn = $scope.enterpriseInfo.msisdn;//创建用户的手机号为创建企业的联系人手机号
    //为创建企业的省份赋值
    if ($scope.provinceID) {
    	$scope.enterpriseInfo.provinceID = $scope.provinceID;
      if($scope.selectedCity && $scope.provinceID !='000'){
    	  $scope.enterpriseInfo.cityID = $scope.selectedCity.cityID;
      }
      //为创建企业的城市赋值
      if ($scope.selectedCityID) {
    	  $scope.enterpriseInfo.cityID = $scope.selectedCityID;
      }
    }
    
    /* 验证cust */
    $scope.checkCustID($scope.enterpriseInfo.custID);
    /* 验证organizationID */
    $scope.checkOrganizationID($scope.enterpriseInfo.organizationID);
    /* 验证contract */
    $scope.checkContract($scope.enterpriseInfo.contract);
    /* 验证手机号 */
    $scope.checkMsisdn($scope.enterpriseInfo.msisdn);
    
    if($scope.custIDValidate && $scope.organizationIDValidate && $scope.contractValidate &&$scope.msisdnValidate){
    	//保存前再校验一遍唯一性与密码规则
        $scope.checkEnterpriseName($scope.enterpriseInfo.enterpriseName,$scope.condition);
    }else{
    	return;
    }
  };
  /*保存*/
  $scope.save = function () {
	if ($scope.enterpriseNameExist == true || $scope.accountNameExist == true ||
        $scope.passwordValidate == false || $scope.rePasswordValidate == false) {
			return;
    }
    var createEnterpriseReq = {};
    createEnterpriseReq.enterprise = angular.copy($scope.enterpriseInfo);
    createEnterpriseReq.enterprise.accountInfo = angular.copy($scope.accountInfo);
    createEnterpriseReq.enterprise.accountInfo.accountType = 3;
    createEnterpriseReq.enterprise.operatorID = $.cookie("accountID");
    createEnterpriseReq.enterprise.accountInfo.operatorID = $.cookie("accountID");
    createEnterpriseReq.enterprise.businessLicenseURL = $scope.businessLicenseURL;
    if (createEnterpriseReq.enterprise.enterpriseType == '2') {
        createEnterpriseReq.enterprise.accountInfo.roleList = [
          {'roleID': '1002'}
        ];
      }
    delete(createEnterpriseReq.enterprise.accountInfo.rePassword);
    delete(createEnterpriseReq.enterprise.enterpriseNameDesc);
    delete(createEnterpriseReq.enterprise.passwordValidate);
    delete(createEnterpriseReq.enterprise.accountInfo.passwordValidateDesc);
    delete(createEnterpriseReq.enterprise.showUpload);
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/createEnterprise",
      data: JSON.stringify(createEnterpriseReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            location.href = '../enterpriseList/enterpriseList.html';
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };

  /*更新*/
  $scope.update = function () {
  if ($scope.enterpriseNameExist == true || $scope.accountNameExist == true ||
        $scope.passwordValidate == false || $scope.rePasswordValidate == false) {
			return;
    }
    var updateEnterpriseReq = {
    		"enterprise":{
    			"accountInfo":{
    				"msisdn":$scope.enterpriseInfo.msisdn,
    				"password":$scope.accountInfo.password
    			},
    			"id":$scope.enterpriseInfo.id,
    			"enterpriseName":$scope.enterpriseInfo.enterpriseName,
    			"enterpriseType":$scope.enterpriseType,
    			"custID":$scope.enterpriseInfo.custID,
    			"organizationID":$scope.enterpriseInfo.organizationID,
    			"contract":$scope.enterpriseInfo.contract,
    			"msisdn":$scope.enterpriseInfo.msisdn,
    			"cityID":$scope.enterpriseInfo.cityID,
    			"provinceID":$scope.enterpriseInfo.provinceID,
    			"businessLicenseURL":$scope.businessLicenseURL,
    		}
    };
    if($scope.enterpriseNameTemp ==updateEnterpriseReq.enterprise.enterpriseName){
    	delete(updateEnterpriseReq.enterprise.enterpriseName);
    }
    if($scope.custIDTemp ==updateEnterpriseReq.enterprise.custID){
    	delete(updateEnterpriseReq.enterprise.custID);
    }
    if($scope.organizationIDTemp ==updateEnterpriseReq.enterprise.organizationID){
    	delete(updateEnterpriseReq.enterprise.organizationID);
    }
    if($scope.contractTemp ==updateEnterpriseReq.enterprise.contract){
    	delete(updateEnterpriseReq.enterprise.contract);
    }
    if($scope.msisdnTemp ==updateEnterpriseReq.enterprise.msisdn){
    	delete(updateEnterpriseReq.enterprise.msisdn);
    	delete(updateEnterpriseReq.enterprise.accountInfo.msisdn);
    }
    if($scope.provinceIDTemp ==updateEnterpriseReq.enterprise.provinceID){
    	delete(updateEnterpriseReq.enterprise.provinceID);
    }
    if($scope.cityIDTemp ==updateEnterpriseReq.enterprise.cityID){
    	delete(updateEnterpriseReq.enterprise.cityID);
    }
    if($scope.businessLicenseURLTemp ==updateEnterpriseReq.enterprise.businessLicenseURL){
    	delete(updateEnterpriseReq.enterprise.businessLicenseURL);
    }
    updateEnterpriseReq.enterprise.operatorID = $.cookie("accountID");
    if(!updateEnterpriseReq.enterprise.accountInfo.password){
    	delete(updateEnterpriseReq.enterprise.accountInfo.password);
    }
    if(!updateEnterpriseReq.enterprise.accountInfo.msisdn){
    	delete(updateEnterpriseReq.enterprise.accountInfo.msisdn);
    }
    if(JSON.stringify(updateEnterpriseReq.enterprise.accountInfo) == "{}"){
    	delete(updateEnterpriseReq.enterprise.accountInfo);
    }else{
    	updateEnterpriseReq.enterprise.accountInfo.accountID =  $scope.accountInfo.accountID;
    	updateEnterpriseReq.enterprise.accountInfo.enterpriseID =  $scope.accountInfo.enterpriseID;
    	updateEnterpriseReq.enterprise.accountInfo.operatorID = $.cookie("accountID");
    	updateEnterpriseReq.enterprise.accountInfo.accountType = 3;
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/updateEnterprise",
      data: JSON.stringify(updateEnterpriseReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
        	  if($scope.isSuperManager){
        		  location.href = '../enterpriseList/enterpriseList.html';
        	  }else{
        		  $scope.tip = 'COMMON_SAVESUCCESS';
                  $('#myModal').modal();
        	  }
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };
  
  
  /*点击取消返回列表页面*/
  $scope.cancelToEnterpriseList = function ($scope) {
    if ($scope.operate == 'detail') {
    	location.href = '../enterpriseList/enterpriseList.html';
    }
    else {
    	$scope.tip='COMMON_RETURNTOLIST';
        $('#ensureToList').modal();
    }
  };
  
  /*确认返回列表页面*/
  $scope.ensureToList = function ($scope) {
	    location.href = '../enterpriseList/enterpriseList.html';
  };
  /*校验各个必填字段*/
  $scope.validate = function (context, maxlength, reg, required) {
    if (!context && required) {
      return false;
    }
    if(context){
    	if (context.length > maxlength) {
            return false;
          } else {
            if (!reg.test(context)) {
              return false;
            } else {
              return true;
            }
          }
    }
    return true;
  };
});




