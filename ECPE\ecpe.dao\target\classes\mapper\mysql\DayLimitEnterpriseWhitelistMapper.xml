<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.DayLimitEnterpriseWhitelistMapper">
	<resultMap id="object"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.DayLimitEnterpriseWhitelistWrapper">
		<result property="id" column="ID" />
		<result property="servType" column="servType" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="createTime" column="createTime" />
		<result property="enterpriseCode" column="enterpriseCode" />
		<result property="enterpriseName" column="enterpriseName" />
		<result property="enterpriseType" column="enterpriseType" />
		<result property="updateTime" column="updateTime" />

	</resultMap>


	<select id="queryObjectList" resultType="com.huawei.jaguar.dsdp.ecpe.dao.domain.DayLimitEnterpriseWhitelistWrapper">
		select
		id,servType,enterpriseID,createTime,enterpriseCode,enterpriseName,enterpriseType,updateTime,parentEnterpriseID,parentEnterpriseName
		from ecpe_t_daylimit_enterprise_whitelist
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseName != null and enterpriseName != ''">
				and	enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseId != null  and enterpriseId != '' ">
				and	enterpriseId=#{enterpriseId}
			</if>
			<if test="enterpriseType != null and enterpriseType != '' ">
				and	enterpriseType=#{enterpriseType}
			</if>

		</trim>
		order by createTime DESC
		<if test="pageParameter != null">
			limit #{pageParameter.pageNum},#{pageParameter.pageSize}
		</if>

	</select>


	<select id="queryObjectById" resultType="com.huawei.jaguar.dsdp.ecpe.dao.domain.DayLimitEnterpriseWhitelistWrapper">
		select
		id,servType,enterpriseID,createTime,enterpriseCode,enterpriseName,enterpriseType,updateTime
		from ecpe_t_daylimit_enterprise_whitelist

		where id = #{id}


	</select>

	<select id="queryObjectListCount" resultType="java.lang.Integer">
		select
		count(*)
		from ecpe_t_daylimit_enterprise_whitelist
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseName != null  and enterpriseName != ''">
				AND	enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseId != null  and enterpriseId != '' ">
				AND	enterpriseId=#{enterpriseId}
			</if>
			<if test="enterpriseType != null and enterpriseType != '' ">
				and	enterpriseType=#{enterpriseType}
			</if>
		</trim>
	</select>


	<delete id="deleteObjectByID" parameterType="java.lang.Integer">
		delete from ecpe_t_daylimit_enterprise_whitelist where ID=#{ID}
	</delete>

	<insert id="insertObject" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.DayLimitEnterpriseWhitelistWrapper">
		INSERT into ecpe_t_daylimit_enterprise_whitelist (
			servType,enterpriseID,createTime,enterpriseCode,enterpriseName,enterpriseType,updateTime,parentEnterpriseID,parentEnterpriseName
		) values (
			#{servType},#{enterpriseID},now(),#{enterpriseCode},#{enterpriseName},#{enterpriseType},now(),#{parentEnterpriseID},#{parentEnterpriseName}
		);
	</insert>

	<insert id="updateObject" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.DayLimitEnterpriseWhitelistWrapper">
		UPDATE ecpe_t_daylimit_enterprise_whitelist
		SET servType = #{servType},
			enterpriseID = #{enterpriseID},
			enterpriseCode = #{enterpriseCode},
			enterpriseName = #{enterpriseName},
			enterpriseType = #{enterpriseType},
			updateTime = now()
		WHERE
			id = #{id};
	</insert>
</mapper>
