<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryRuleMapper">
	<resultMap id="deliveryRuleMap" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryRuleWrapper">
		<result property="id" column="id"/>
		<result property="deliveryType" column="deliveryType"/>
		<result property="msgType" column="msgType"/>
		<result property="pushType" column="pushType"/>
		<result property="deliveryFirstAction" column="deliveryFirstAction"/>
		<result property="deliveryFailedAction" column="deliveryFailedAction"/>
		<result property="reserved1" column="reserved1"/>
		<result property="reserved2" column="reserved2"/>
		<result property="reserved3" column="reserved3"/>
		<result property="reserved4" column="reserved4"/>
		<result property="createTime" column="createTime"/>
		<result property="updateTime" column="updateTime"/>
	</resultMap>


	<sql id="deliveryRuleCol">
		id,
		deliveryType,
		msgType,
		pushType,
		deliveryFirstAction,
		deliveryFailedAction,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime
	</sql>

    <select id="queryDeliveryRulesByMsgtype" resultMap="deliveryRuleMap">
		select <include refid="deliveryRuleCol"/>
		  from ecpe_t_thirdparty_delivery_rule
		 where msgType = #{msgType}
	</select>
	
	<select id="queryDeliveryRulesByID" resultMap="deliveryRuleMap" parameterType="java.lang.Integer">
		select <include refid="deliveryRuleCol"/>
		  from ecpe_t_thirdparty_delivery_rule
		 where id = #{id}
	</select>
</mapper>