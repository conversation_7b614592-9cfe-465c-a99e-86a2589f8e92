var app = angular.module("myApp", ["util.ajax", "page", "angularI18n"])
app.controller("AccountManagementController", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    $scope.isSuperManager = false;
    $scope.loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
    $scope.onlySuperManager = ($scope.loginRoleType == 'superrManager');
    $scope.enterpriseType = "";
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    //下拉框
    $scope.enterpriseTypeList = [
      {
        id: "1",
        name: "直客"
      },
      {
        id: "2",
        name: "代理商"
      },
      {
        id: "5",
        name: "分省企业"
      },
      {
        id: "4",
        name: "商户H5"
      },
      {
        id: "6",
        name: "小微商户"
      },
     {
      id: "3",
      name: "子企业"
     },
    ];

    $scope.queryPartnerList();
  };

  $scope.accountContentInfoData = [];

  //查询
  $scope.queryPartnerList = function (condition) {
    var req = {};
    if (condition != 'justPage') {
      req = {
        "accountQueryType": 2,
        "accountName": $scope.accountName || '',
        "enterpriseType": $scope.enterpriseType,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        },
        "sortField":1,
        "sortType":2
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryAccountInfoListTemp = angular.copy(req);
    } else {
      req = $scope.queryAccountInfoListTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    req.getRole=0;
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/queryAccountList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(result);
          if (data.resultCode == '**********') {
            $scope.accountContentInfoData = result.accountInfoList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.accountContentInfoData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.accountContentInfoData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }

  //重置密码弹窗
  $scope.resPwd = function (item) {
    $scope.accountItem = item;
    $("#resPassword").modal();
  }



  //重置密码
  $scope.resetPassword = function () {
    var req = {
      account: {
        accountName: $scope.accountItem.accountName,
        accountType: $scope.accountItem.accountType

      },
      token: "noNeedCheck"
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/loginService/resetPwd",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $('#resetBlack').click();
            $scope.tip = "重置密码成功";
            $('#myModal').modal();
          } else {
            $('#resetBlack').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#resetBlack').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }

  //解锁账号
  $scope.unlock = function (item) {
    $scope.accountItem = item;
    $("#unLockAccount").modal();
  }
  //解锁账号
  $scope.unLockAccount = function () {
    var req = {

        accountID: $scope.accountItem.accountID

    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/unlockAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $("#unLockAccount").modal('hide');
            $scope.tip = "解锁成功";
            $('#myModal').modal();

          } else {
            $("#unLockAccount").modal('hide');
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
          $scope.queryPartnerList();
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $("#unLockAccount").modal('hide');
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
  //解锁账号
  $scope.getType = function (item) {
    var type = "";
    if(item.enterpriseType){
      switch (item.enterpriseType) {
        case 1:
          type = "直客";
          break;
        case 2:
          type = "代理商";
          break;
        case 3:
          type = "子企业";
          break
        case 4:
          type = "商户H5";
          break;
        case 5:
          type = "分省企业";
          break;
        case 6:
          type = "小微商户";
          break;
        case 7:
          type = "商客";
          break;
        default:
          break;
      }
    }else{
      if(item.reservedsEcpmp&&item.reservedsEcpmp.reserved1){
         type = "小微商户";
      }else{
        type = "商户H5";
      }

    }
    item.type = type;
    return type;
  }

  //账号状态
  $scope.getAccountStatus = function (accountStatus) {
       switch(accountStatus) {
           case 1:
            return "正常";
           case 2:
            return "冻结";
           case 3:
            return "停用";
           default:
            return "";
       }
  }
  // 停用
  $scope.unBlock = function (item) {
      $scope.accountItem = item;
      $("#unBlockAccount").modal();
  }
  $scope.unBlockAccount = function () {
      var req = {
          "accountInfo": {
           ...$scope.accountItem,
           accountStatus:3
          }
        };
      console.log("req", req)
       RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/accountManageService/updateAccount",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if (data.resultCode == '**********') {
                $("#unBlockAccount").modal('hide');
                $scope.tip = "该账号停用成功！";
                $('#myModal').modal();

              } else {
                $("#unBlockAccount").modal('hide');
                $scope.tip = data.resultCode;
                $('#myModal').modal();
              }
              $scope.queryPartnerList();
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $("#unBlockAccount").modal('hide');
              $scope.tip = '**********';
              $('#myModal').modal();
            })
          }
        });

  }

  // 停用
  $scope.enable = function (item) {
    $scope.accountItem = item;
    $("#enableAccount").modal();
  }
  $scope.enableAccount = function () {
    var req = {
      "accountInfo": {
        ...$scope.accountItem,
        accountStatus:1
      }
    };
    console.log("req", req)
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/updateAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $("#enableAccount").modal('hide');
            $scope.tip = "该账号启用成功！";
            $('#myModal').modal();

          } else {
            $("#enableAccount").modal('hide');
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
          $scope.queryPartnerList();
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $("#enableAccount").modal('hide');
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    })}
});


app.filter("cootype", function () {
  return function (roleList, accountType) {
    var type = "";
    if (roleList.length === 0) {
      switch (accountType) {
        case 1:
          type = "直客";
          break;
        case 2:
          type = "代理商";
          break;
        case 3:
          type = "代理商";
          break
        case 4:
          type = "商户H5";
          break;
        case 5:
          type = "分省企业";
          break;
        case 6:
          type = "小微商户";
          break;
        default:
          break;
      }
    } else {
      switch (roleList[0].roleID) {
        case 1001:
          type = "直客";
          break;
        case 1002:
          type = "代理商";
          break;
        case 1003:
          type = "分省企业";
        case 1004:
          type = "商户";
          break;
        default:
          break;
      }
    }
    return type;
  }
});

app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
    }
    return "";
  }
})