angular.module('angularI18n', ["pascalprecht.translate"])
    .config(function ($translateProvider) {
      var lang = window.localStorage.lang || 'zh';
      $translateProvider.preferredLanguage(lang);
      $translateProvider.useStaticFilesLoader({
        files:[
          {
            prefix: '/qycy/ecpmp/i18n/error/error_',
            suffix: '.json'
          },
          {
            prefix: '/qycy/ecpmp/i18n/message/message_',
            suffix: '.json'
          }
        ]

      });
    })
    .factory('T', ['$translate', function ($translate) {
      var T = {
        T: function (key) {
          if (key) {
            return $translate.instant(key);
          }
          return key;
        }
      }
      return T;
    }]);