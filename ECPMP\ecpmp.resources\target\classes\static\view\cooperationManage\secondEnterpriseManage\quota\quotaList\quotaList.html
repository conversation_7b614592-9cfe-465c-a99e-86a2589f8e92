<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>订单管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>

	<!--日期选择组件-->
	<script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
	<link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">

	<script type="text/javascript" src="quotaListCtrl.js"></script>
	<style>
		.cooperation-manage .coorPeration-table th:first-child,
		.cooperation-manage .coorPeration-table td:first-child {
			padding-left: 30px !important;
		}

		.cooperation-manage label {
			min-width: 110px;
		}
	</style>
</head>
<body ng-app='myApp' class="body-min-width" ng-controller='orderListController' ng-init="init();">
	<div class="cooperation-manage container-fluid ">
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate" ng-show="isSuperManager"></span>
			<span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate" ng-show="isAgent"></span>
			&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_QUOTAMANAGE'|translate"></span>
		</div>

		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="businessType" class="col-xs-1 control-label" style="white-space:nowrap;"
								 ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
					<div class="col-xs-2 cond-div" style="min-width: 200px">
						<select id="businessType" class="form-control" ng-model="businessType"
										ng-options="x.id as x.name for x in businessTypeList">
						</select>
					</div>
					<label class="col-xs-1 control-label" ng-bind="'COMMON_EXPIRYDATE'|translate"></label>
					<div class="input-daterange input-group col-xs-3 cond-div" id="datepicker"
							 style="float: left;padding-left: 15px;padding-right: 15px;min-width: 350px">
						<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="start"/>
						<span class="input-group-addon" ng-bind="'TO'|translate"></span>
						<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="end"/>
					</div>
					<div class="col-xs-1 cond-div" style="margin-left: 40px">
						<button type="submit" class="btn search-btn" ng-click="queryOrderList()" ng-disabled="search">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="gotoAdd()">
				<icon class="add-iocn"></icon>
				<span ng-bind="'ADD_ORDER_QUOTA'|translate"></span></button>
		</div>
		<div style="margin-left: 20px;margin-bottom: 20px;">
			<p style="font-size: 16px;font-weight: 500;" ng-bind="'ORDER_MANAGE'|translate"></p>
		</div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:12%" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>
					<th style="width:8%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>

					<th style="width:30%" >配额</th>
					<th style="width:18%" ng-bind="'COMMON_EXPIRYDATE'|translate"></th>
					<th style="width:15%" ng-bind="'COMMON_OPERATE'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in orderListData">
					<td title="{{item.enterpriseName}}" ng-bind="item.enterpriseName"></td>
					<td ng-attr-title="{{businessTypeChoise[item.servType]}}">{{businessTypeChoise[item.servType]}}</td>
					<!--
					<td title="{{item.px}}" ng-bind="item.px"></td>
					<td title="{{item.screenShowMonthNum===null?'--':item.screenShowMonthNum}}">
						<span ng-bind="item.screenShowMonthNum===null?'--':item.screenShowMonthNum"></span>
					</td>

					挂机短信按次-->
					<!--
					<td title="{{item.isSMSLimit===0?'不限':item.isSMSLimit===1?item.smsNum:'--'}}">
						<span ng-bind="item.isSMSLimit===0?'不限':item.isSMSLimit===1?item.smsNum:'--'"></span>
					</td>

					<td title="{{item.gd}}" ng-bind="item.gd"></td>-->
					<!--挂机短信包月
					<td title="{{item.guaDuanShowMonthNum===null?'--':item.guaDuanShowMonthNum}}">
						<span ng-bind="item.guaDuanShowMonthNum===null?'--':item.guaDuanShowMonthNum"></span>
					</td>-->
					<!--挂机彩信按次
					<td title="{{item.isColorSMSLimit===0?'不限':item.isColorSMSLimit===1?item.colorSMSNum:'--'}}">
						<span ng-bind="item.isColorSMSLimit===0?'不限':item.isColorSMSLimit===1?item.colorSMSNum:'--'"></span>
					</td>-->
					<!--挂机彩信包月
					<td title="{{item.guaCaiShowMonthNum===null?'--':item.guaCaiShowMonthNum}}">
						<span ng-bind="item.guaCaiShowMonthNum===null?'--':item.guaCaiShowMonthNum"></span>
					</td>-->

					<!--新增配额列-->
					<td >
						<div ng-repeat="quotauseinfo in (item.operatorQuota)" class="peTable">
							<!--1-移动，2-联通，3-电信  -->
							<div class="peL fontRd">
                                        <span title="{{quotauseinfo.mobilePlatform}}" ng-if="quotauseinfo.mobilePlatform == 1">移动
                                        </span>
								<span title="{{quotauseinfo.mobilePlatform}}" ng-if="quotauseinfo.mobilePlatform == 2">联通
                                        </span>
								<span title="{{quotauseinfo.mobilePlatform}}" ng-if="quotauseinfo.mobilePlatform == 3">电信
                                        </span>
							</div>
							<!-- -->
							<div class="peR">
								<p>
                                            <span ng-if="quotauseinfo.subServType == 1">主叫屏显
                                            </span>
                                            <span ng-if="quotauseinfo.subServType == 2">被叫屏显
                                            </span>
                                            <span ng-if="quotauseinfo.subServType == 4">挂机短信
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==8 && quotauseinfo.servType != 4">挂机彩信
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==16 && quotauseinfo.servType == 2">挂机增彩
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==3 && quotauseinfo.servType != 4">屏显(主被叫)
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==10">屏显+挂彩
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==16 && quotauseinfo.servType == 4">增彩
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==17">短信
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==3 && quotauseinfo.servType == 4">屏显
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==8 && quotauseinfo.servType == 4">彩信
                                            </span>
									<span ng-if="quotauseinfo.chargeType == 1">(
                                                <span ng-if="quotauseinfo.isLimit == 0">不限量</span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.amount}}">配额:<span class="fontRd">{{quotauseinfo.amount}}</span></span>
                                                <span title="{{quotauseinfo.actualUseAmount}}">已使用:<span class="fontRd">{{quotauseinfo.actualUseAmount}}</span></span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.remainUseAmount}}">剩余:<span class="fontRd">{{quotauseinfo.remainUseAmount}}</span></span>)
                                            </span>
									<span ng-if="quotauseinfo.chargeType == 2">(
                                                <span ng-if="quotauseinfo.isLimit == 0">不限量</span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.memberCount}}">配额:<span class="fontRd">{{quotauseinfo.memberCount}}</span></span>
                                                <span title="{{quotauseinfo.actualUseMemberCount}}">已使用:<span class="fontRd">{{quotauseinfo.actualUseMemberCount}}</span></span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.remainUseMemberCount}}">剩余:<span class="fontRd">{{quotauseinfo.remainUseMemberCount}}</span></span>)
                                            </span>
								</p>
							</div>
						</div>

					<td >
					{{item.effictiveTime | dataFormat }} - {{item.expireTime | dataFormat }}</td>
					<td>
						<div class="handle">
							<ul>
								<li class="query" ng-click="gotoDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'COMMON_WATCH'|translate"></span>
								</li>
								<li class="edit" ng-click="gotoUpdate(item)">
									<icon class="edit-icon"></icon>
									<span ng-bind="'MODIFY_PEIE'|translate"></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="orderListData.length<=0">
					<td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="queryOrderList('justPage')"></ptl-page>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{tip|translate}}
					</p></div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</body>
<style>
	/*配额*/
	.peTable{
		display: table;
	}
	.peL{
		display: table-cell;
		padding-right: 10px;
		vertical-align: middle;
	}
	.peR{
		display: table-cell;
	}
	.fontRd{
		color: red;
	}
	.peld{
		vertical-align: middle;
	}

</style>
</html>