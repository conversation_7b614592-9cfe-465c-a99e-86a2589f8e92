<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ExternalProductSubscribeMapper">

	<select id="queryExternalProductSubscribe" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ExternalProductSubscribeWrapper">
		select ID,
		extProID,
		msisdn,
		effectiveTime,
		expiryTime
		from ecpm_t_external_product_subscribe
		<trim prefix="where" prefixOverrides="and|or">
			<if test="extProID != null">
				extProID=#{extProID}
			</if>
		</trim>
	</select>
	
	<select id="queryProductSubscribeToProduct" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ExternalProductWrapper">
		SELECT 
			s.ID,p.productType,p.limitQuotaNum,p.productID,s.originalExpiryTime
		FROM 
			ecpm_t_external_product_subscribe s,ecpm_t_external_product p 
		WHERE 
			s.extProID = p.productID	
			AND IFNULL(s.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
			AND (NOW() &lt;= IFNULL(s.expiryTime, '2999-12-31 23:59:59') or NOW() &lt;= s.originalExpiryTime )
			AND msisdn=#{msisdn}
			AND productPortal=#{productPortal}
			AND subServType=#{subServType}
	</select>

	<select id="queryProductSubscribeByMsisdn" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberProductExtWrapper">
		SELECT
		m.id,orl.enterpriseID ,orl.orgCode,orl.orgId

-- 		    ,p.productID,p.productType,p.subServType,p.limitQuotaNum
		FROM
		ecpm_t_member m
		LEFT JOIN ecpm_t_org_rel orl ON orl.id = m.ID
-- 		LEFT JOIN ecpm_t_external_product_subscribe eps on eps.msisdn = m.msisdn
-- 		and (eps.expiryTime is null or eps.expiryTime&lt;=now())
-- 		and (eps.effectiveTime is null or eps.effectiveTime>=now())
-- 		LEFT JOIN ecpm_t_external_product p on eps.extProID = p.productID
		WHERE m.msisdn = #{msisdn}
	</select>


	<insert id="createExternalProductSubscribe">
		insert into ecpm_t_external_product_subscribe
		(
		extProID,
		msisdn,
		effectiveTime,
		expiryTime,
		createTime,
		updateTime
		)
		values
		(
		#{extProID},
		#{msisdn},
		#{effectiveTime},
		#{expiryTime},
		 now(),
		 now()
		)
	</insert>

	<delete id="deleteExternalProductSubscribe">
		delete from ecpm_t_external_product_subscribe where ID =
		#{ID}
	</delete>
	
	<update id="updateProductSubscribeToProduct">
		update ecpm_t_external_product_subscribe set
        expiryTime=if(#{expiryTime}>expiryTime,expiryTime,#{expiryTime}),
		originalExpiryTime = null
		where
		msisdn in 
		<foreach collection="msisdnList" item="id" open="(" separator=","
			close=");">
			#{id}
		</foreach>

		update ecpm_t_member_ext_delivery set deliveryStatus = 2
		where
		1=1
		<if test="enterpriseID != null">
			and enterpriseID = #{enterpriseID}
		</if>
		and msisdn in
		<foreach collection="msisdnList" item="id" open="(" separator=","
				 close=");">
			#{id}
		</foreach>
	</update>
	
	<update id="updateExpiryTime">
		update ecpm_t_external_product_subscribe set
        expiryTime=null
		where
		ID=#{id}
	</update>
	
	<update id="updateExpiryTimeByExpiryTime">
		update 
			ecpm_t_external_product_subscribe 
		set
        	expiryTime = #{expiryTime},
			updateTime = now()
		where
			msisdn = #{msisdn}
		 	and (
		 		expiryTime is null
     	 		or (expiryTime is not null
         			and #{expiryTimeCond} &lt; expiryTime
         		)
         	)
	</update>
	
	
	<update id="updateByExpiryTime">
		UPDATE 
			ecpm_t_external_product_subscribe 
		SET
        	expiryTime = IF(originalExpiryTime IS NULL, #{expiryTimeCond}, expiryTime),
        	originalExpiryTime = IF(originalExpiryTime IS NULL, originalExpiryTime, #{expiryTimeCond}),
			updateTime = NOW()
		WHERE
			msisdn = #{msisdn}
			and (expiryTime &gt; #{expiryTimeCond} or originalExpiryTime &gt; #{expiryTimeCond})
	</update>
	
	<update id="updateByMsisdn">
		UPDATE 
			ecpm_t_external_product_subscribe 
		SET
        	expiryTime = IF(originalExpiryTime IS NULL, #{expiryTimeCond}, expiryTime),
        	originalExpiryTime = IF(originalExpiryTime IS NULL, originalExpiryTime, #{expiryTimeCond}),
			updateTime = NOW()
		WHERE
			msisdn = #{msisdn}
			and extProID <![CDATA[<>]]> #{extProID}
			and (IFNULL(expiryTime,STR_TO_DATE('2099-01-31 23:59:59','%Y-%m-%d %H:%i:%s')) &gt; #{expiryTimeCond}
			 or originalExpiryTime &gt; #{expiryTimeCond})
	</update>
	
	<update id="updateByEnterpriseID">
		UPDATE 
			ecpm_t_external_product_subscribe t,ecpm_t_org_rel o,ecpm_t_member m
		SET
        	t.expiryTime = IF(originalExpiryTime IS NULL, #{expiryTimeCond}, expiryTime),
        	t.originalExpiryTime = IF(originalExpiryTime IS NULL, originalExpiryTime, #{expiryTimeCond}),
			t.updateTime = NOW()
		WHERE
		t.msisdn=m.msisdn AND o.ID=m.ID
		<if test="oldExtProID != null">
			AND t.extProID=#{oldExtProID}
		</if>
		AND o.enterpriseID=#{enterpriseID}
		AND (IFNULL(t.expiryTime,STR_TO_DATE('2099-01-31 23:59:59','%Y-%m-%d %H:%i:%s')) &gt; #{expiryTimeCond}
			 or t.originalExpiryTime &gt; #{expiryTimeCond})
	</update>
	
	<insert id="insertIntoSelectExternal">
		INSERT INTO ecpm_t_external_product_subscribe(extProID,msisdn,effectiveTime,expiryTime,originalExpiryTime,createTime,updateTime)
		SELECT 
		#{extProID} extProID,t.msisdn msisdn,#{expiryTimeCond} effectiveTime,t.expiryTime expiryTime,t.originalExpiryTime originalExpiryTime,NOW() createTime,NOW() updateTime
		FROM ecpm_t_external_product_subscribe t, ecpm_t_org_rel o, ecpm_t_member m, ecpm_t_external_product p
		WHERE 
		t.msisdn=m.msisdn 
		AND o.ID=m.ID
		AND t.extProID=p.productID
		AND p.productType IN (0,2)
		<if test="msisdn != null">
			AND t.msisdn=#{msisdn}
		</if>
		<if test="oldExtProID != null">
			AND t.extProID=#{oldExtProID}
		</if>
		AND o.enterpriseID=#{enterpriseID} 
		AND (IFNULL(t.expiryTime,STR_TO_DATE('2099-01-31 23:59:59','%Y-%m-%d %H:%i:%s')) &gt; #{expiryTimeCond} 
			OR t.originalExpiryTime &gt; #{expiryTimeCond})
	</insert>
	
	<update id="updateExpiryTimeByMsisdn">
		update
			ecpm_t_external_product_subscribe
		set
			originalExpiryTime = if(expiryTime is null,CONCAT( "2099-01-01"  ," 23:59:59"),expiryTime),
			updateTime = now(),
		    expiryTime = now()
		where
			msisdn = #{msisdn}
		  and originalExpiryTime is null
		  and (
				expiryTime is null
				or (now() &lt;= expiryTime)
			)
	</update>
	<update id="updateOriginalExpiryTimeByMsisdn">
		update
			ecpm_t_external_product_subscribe
		set
			expiryTime = originalExpiryTime,
			originalExpiryTime = null,
			updateTime = now()

		where
			msisdn = #{msisdn}
			and originalExpiryTime is not null;
	</update>

	<select id="queryCountByProductId" resultType="java.lang.Integer">
		SELECT count(t3.deliveryCount) count FROM ecpm_t_external_product_subscribe t1
		LEFT JOIN ecpm_t_external_product t2  ON t1.extProID = t2.productID
		LEFT JOIN ecpm_t_member t4 ON t1.msisdn = t4.msisdn
		LEFT JOIN ecpm_t_org_rel t5 ON t4.ID = t5.ID
		LEFT JOIN ecpm_t_member_ext_delivery t3  ON t1.msisdn = t3.msisdn and t3.subServType = t2.subServType

		WHERE extProID = #{extProID} and t5.enterpriseID = #{enterpriseID}
		AND IFNULL(t1.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
		AND NOW() &lt;= IFNULL(t1.expiryTime, '2999-12-31 23:59:59')
	</select>
	<update id="updateByMs">
		UPDATE
		ecpm_t_external_product_subscribe
		SET
		expiryTime = now(),
		updateTime = NOW()
		WHERE
		msisdn = #{msisdn}
	</update>

	<select id="selectExternalProductSubscribeByMsisdn" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ExternalProductSubscribeWrapper">
		SELECT * FROM ecpm_t_external_product_subscribe
		WHERE (date_format(effectiveTime, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')
		or date_format(expiryTime, '%Y-%m') = DATE_FORMAT(now(), '%Y-%m')
		or (IFNULL(effectiveTime,'2000-01-01 00:00:00') &lt;= NOW() and IFNULL(expiryTime,'2999-12-31 23:59:59') > NOW()))
        and msisdn = #{msisdn}
	</select>
</mapper>