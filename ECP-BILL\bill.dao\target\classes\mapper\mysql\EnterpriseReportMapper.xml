<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper2.EnterpriseReportMapper">
    <resultMap id="enterpriseReportWrapper" type="com.huawei.jaguar.dsdp.bill.dao.domain2.EnterpriseReportWrapper">
        <result property="id" column="id" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="operType" column="operType" />
		<result property="operTime" column="operTime" />
        <result property="enterpriseCode" column="enterpriseCode" />
        <result property="enterpriseName" column="enterpriseName" />
        <result property="enterpriseDesc" column="enterpriseDesc" />
        <result property="enterpriseType" column="enterpriseType" />
        <result property="organizationID" column="organizationID" />
        <result property="custID" column="custID" />
        <result property="businessLicenseID" column="businessLicenseID" />
        <result property="businessLicenseURL" column="businessLicenseURL" />
        <result property="idCardPositiveURL" column="IDCardPositiveURL" />
        <result property="idCardOppositeURL" column="IDCardOppositeURL" />
		<result property="auditStatus" column="auditStatus" />
		<result property="auditDesc" column="auditDesc" />
		<result property="msisdn" column="msisdn" />
		<result property="parentEnterpriseID" column="parentEnterpriseID" />
		<result property="provinceID" column="provinceID" />
		<result property="cityID" column="cityID" />
        <result property="contract" column="contract" />
		<result property="status" column="status" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="reserved5" column="reserved5" />
		<result property="reserved6" column="reserved6" />
		<result property="reserved7" column="reserved7" />
		<result property="reserved8" column="reserved8" />
		<result property="reserved9" column="reserved9" />
		<result property="reserved10" column="reserved10"/>
        <result property="parentEnterpriseName" column="parentEnterpriseName" />
		<result property="countyID" column="countyID"/>
    </resultMap>

    <select id="queryEnterpriseReportByOperTime" resultMap="enterpriseReportWrapper">
		SELECT
		t.id,
		t.enterpriseID,
		t.operType,
		t.operTime,
		t.enterpriseCode,
		REPLACE(IFNULL(t.enterpriseName,''),char(10),' ') as enterpriseName,
		REPLACE(IFNULL(t.enterpriseDesc,''),char(10),' ') as enterpriseDesc,
		t.enterpriseType,
		t.organizationID,
		t.custID,
		t.businessLicenseID,
		t.businessLicenseURL,
		t.idCardPositiveURL,
		t.idCardOppositeURL,
		t.auditStatus,
		t.auditDesc,
		t.msisdn,
		t.parentEnterpriseID,
		t.provinceID,
		t.cityID,
		t.contract,
		t.status,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10,
		t.parentEnterpriseName,
		t.countyID
		from dsum_t_enterprise_report t where
		t.operTime <![CDATA[ > ]]> #{operTimeBegin}
		and t.operTime <![CDATA[ < ]]> #{operTimeEnd}
		ORDER BY t.opertime ASC
			limit #{startIndex},#{pageNum}
	</select>

	<select id="queryEnterpriseReportByOperTimeCount" resultType="java.lang.Integer">
		SELECT
			count(1)
		from dsum_t_enterprise_report t where
			t.operTime <![CDATA[ > ]]> #{operTimeBegin}
			and t.operTime <![CDATA[ < ]]> #{operTimeEnd}
		ORDER BY t.opertime ASC
	</select>

</mapper>