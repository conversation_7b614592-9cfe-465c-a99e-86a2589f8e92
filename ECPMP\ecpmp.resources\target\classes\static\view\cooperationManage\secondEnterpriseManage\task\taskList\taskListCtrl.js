var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n", "top.menu", "service.common", "cy.uploadify", "cy.uploadifyfile",])

//自定义filter,格式化日期
app.filter('newDate', function () {
    return function (date) {
        var new_date = date.substr(0, 4) + "-" + date.substr(4, 2) + "-" + date.substr(6.2);
        return new_date;
    }
});
app.controller('TaskCtrl', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    //初始化参数
    $scope.init = function () {
        $scope.enterpriseID = $.cookie('subEnterpriseID');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.operatorID = $.cookie('accountID');
        $scope.enterpriseName = $.cookie('subEnterpriseName') || '';
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.isAgent = ($scope.loginRoleType == 'agent');
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            },
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        //彩印内容类型
        $scope.sceneServiceType = {
            "11": "17",
            "13": "20",
            "9": "18",
            "10": "19",
            "8": "16"
        };
        //彩印内容类型
        $scope.deliveryTypeMap = {
            "17": "0000100",
            "18": "1000000",
            "20": "0000001",
            "10": "0000010",
            "8": "0000010"
        };
        $scope.serviceTypeMap = {
            "16": "增彩",
            "17": "短信",
            "18": "USSD",
            "19": "闪信",
            "20": "彩信",
        };
        $scope.statusMap = {
            "3": "待执行",
            "4": "发送完成",
            "6": "执行中",
            "7": "配额扣减失败",
            "9": "取消"
        };
        $scope.newStatusMap = {
            "1": "失败",
            "2": "失败",
            "3": "失败",
            "4": "失败",
            "5": "处理中",
            "6": "失败",
            "7": "失败"
        };

        $scope.auditStatusChoise = [
            // {
            //     id: 1,
            //     name: "待审核"
            // },
            // {
            //     id: 2,
            //     name: "驳回"
            // },
            // {
            //     id: 3,
            //     name: "审核通过"
            // },
            {
                id: 3,
                name: "待执行"
            },
            {
                id: 4,
                name: "发送完成"
            },
            {
                id: 6,
                name: "执行中"
            },
            {
                id: 7,
                name: "配额扣减失败"
            },
            {
                id: 9,
                name: "取消"
            }
        ];
        $scope.newAuditStatusChoise = [
            {
                id: 5,
                name: "处理中"
            },
            {
                id: 1,
                name: "失败"
            }
        ];
        $scope.newStatus = null;
        //搜索时初始化参数
        $scope.initSel = {
            taskName: "",
            status: ""
        };
        //导出失败列表
        $scope.exportFile = function () {
            var afterStatus = [];
            if ($scope.newStatus == null) {
                afterStatus = [1, 2, 3, 4, 5, 6,7];
            }
            if ($scope.newStatus == 1) {
                afterStatus = [1, 2, 3, 4,6,7];
            }
            if ($scope.newStatus == 5) {
                afterStatus = [5];
            }
            var req = {
                "param": {
                    "taskID": parseInt($scope.item.objectID),
                    "status": afterStatus
                },
                "url": "/qycy/ecpmp/ecpmpServices/commonService/downFailList",
                "method": "get"
            }
            CommonUtils.exportFile(req);
        }
        $scope.taskList();


        let req = {
            "enterpriseID": $scope.enterpriseID
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryPortList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.enterprisePortList = result.enterprisePortList;
                    $scope.object = result.enterprisePortList[0];
                    $scope.groupSendFlashPort = ['10658086'];
                    $scope.groupSendSmsPort = ['10658086'];
                    $scope.groupSendMmsPort = $scope.object.groupSendMmsPort;
                    if ($scope.object.groupSendFluid) {
                        $scope.groupSendFluid = $scope.object.groupSendFluid;
                    } else {
                        $scope.groupSendFluid = 20;
                    }
                })
            }


        })

        //创建任务
        // 上传excel
        $scope.accepttypeExcel = "xlsx";
        $scope.isValidateExcel = true;
        $scope.filesizeExcel = 20;
        $scope.mimetypesExcel = ".xlsx,.xls";
        $scope.autoExcel = true;
        $scope.isCreateThumbnailExcel = false;
        $scope.uploadurlExcel = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDescExcel = "仅支持xlsx格式的文件";
        $scope.numlimitExcel = 1;
        $scope.urlListExcel = [];
        $scope.uploadParamExcel = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms',
            use: 'msidsnList'
        };
        // 上传excel  END
        $scope.$on("uploadifyidExcel", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileNameExcel = broadData.file.name;
            } else {
                $scope.fileNameExcel = "";
            }
            $scope.fileUrlExcel = fileUrl;
        });

        $scope.uploadMsisdns = 0;
    };

    $scope.formatDate = function (str) {
        if (!str) {
            return '－';
        }
        var newDateStr = "";
        newDateStr = str.substring(0, 4) + "-" + str.substring(4, 6) + "-" + str.substring(6, 8) + " "
            + str.substring(8, 10) + ":" + str.substring(10, 12);
        return newDateStr;
    }

    $("[name='taskType']").click(function (e){
        $scope.groupSendTaskInfo.taskType = e.target.value;
        if ($scope.groupSendTaskInfo.taskType == 1){
            $scope.groupSendTaskInfo.taskType1 = true;
            $scope.groupSendTaskInfo.taskType2 = false;
        } else {
            $scope.groupSendTaskInfo.taskType1 = false;
            $scope.groupSendTaskInfo.taskType2 = true;
        }
        console.log($scope.groupSendTaskInfo.taskType)
    });


    //跳转至修改页面
    $scope.toModify = function (item) {
        $.cookie("objectID", item.objectID, {path: '/'});
        location.href = '../createTask/createTask.html?operate=modify';
    }

    $scope.isTempContent = function(content){
        let re = true;
        if(content && content.indexOf("#*#")>=0){
            re = false;
        }
        return re;

    };

    //删除按钮
    $scope.popDelete = function (item) {
        $scope.deleteItem = item;
        $('#deletePop').modal();
    };

    //确认删除
    $scope.toDelete = function (item) {
        var req = {
            "taskID": parseInt(item.objectID),
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/cancelGroupSendTask",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1020100000') {
                        $scope.tip = "取消成功";
                        $('#myModal').modal();
                        $scope.taskList();
                    } else {
                        $scope.tip = result.result.resultDesc;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }

    //弹出错误列表
    $scope.popFailList = function (item) {
        $scope.item = item;
        $scope.newStatus = null;
        $scope.queryFailList('');
    }
    //查询错误列表
    $scope.queryFailList = function (condition) {
        if (condition != 'justPage') {
            var afterStatus = [];
            if ($scope.newStatus == null) {
                afterStatus = [1, 2, 3, 4, 5,6,7];
            }
            if ($scope.newStatus == 1) {
                afterStatus = [1, 2, 3, 4,6,7];
            }
            if ($scope.newStatus == 5) {
                afterStatus = [5];
            }
            var req = {
                "taskID": parseInt($scope.item.objectID),
                "status": afterStatus,
                "page": {
                    "pageNum": 1,
                    "pageSize": $scope.pageInfo[1].pageSize,
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTempFail = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTempFail;
            req.page.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[1].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/queryGroupSendTaskMsisdn",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1030100000') {
                        $scope.failList = result.groupSendTaskMsisdns;
                        //获取页面的总条数与总页面
                        $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
                        if ($scope.pageInfo[1].totalCount == 0) {
                            $scope.pageInfo[1].currentPage = 1;
                            $scope.pageInfo[1].totalCount = 0;
                            $scope.pageInfo[1].totalPage = 1;
                        } else {
                            $scope.pageInfo[1].totalPage = Math.ceil(parseInt(result.totalNum) / parseInt($scope.pageInfo[1].pageSize));
                            angular.forEach($scope.failList, function (e, i) {
                                var failInfo = angular.copy(e);
                                if (e.status == 5) {
                                    failInfo.errdesc = '网关未回执，待网关回复后更新原因';
                                }
                                $scope.failList[i] = failInfo;
                            });
                        }
                        $('#failListPop').modal();
                    } else {
                        $scope.failList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }

    //获取queryGroupSendTaskList接口的数据
    $scope.taskList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "enterpriseID": $scope.enterpriseID,
                "taskName": $scope.taskName,
                "status": $scope.status,
                "page": {
                    "pageNum": 1,
                    "pageSize": $scope.pageInfo[0].pageSize,
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/queryGroupSendTaskList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1030100000') {
                        $scope.queryTaskList = result.groupSendTaskList;
                        //获取页面的总条数与总页面
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        if ($scope.pageInfo[0].totalCount == 0) {
                            $scope.pageInfo[0].currentPage = 1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage = 1;
                        } else {
                            $scope.pageInfo[0].totalPage = Math.ceil(parseInt(result.totalNum) / parseInt($scope.pageInfo[0].pageSize));
                        }
                    } else {
                        $scope.queryTaskList = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }


    $scope.taskNameValidate = true;
    $scope.msisdnListValidate = true;


    //跳转至详情页面
    $scope.toDetail = function (item) {
        $scope.groupSendTaskInfo = {
            enterpriseID: item.enterpriseID,
            taskName: item.taskName,
            status: $scope.statusMap[item.status],
            contentId: item.contentId,
            groupSendTaskMsisdn: [],
            taskId:item.objectID
        };
        $scope.validate = {};
        $scope.contentScene = null;
        $('#detailTask').modal();
    };

    //跳转至新增页面
    $scope.toAdd = function () {
        $scope.groupSendTaskInfo = {
            enterpriseID: $scope.enterpriseID,
            taskName: '',
            serviceType: null,
            taskType: 1,
            content: {},
            groupSendTaskMsisdn: [],
            timingTime:null,
            timingTimeTemp:""
        };
        $('#datepicker').val("");
        $scope.validate = {};
        $scope.contentScene = null;
        $('#createTask').modal();
        $scope.fileNameExcel = '';
        $scope.groupSendTaskInfo.taskType = 2;

    };
    $scope.openDate = function(){
        laydate({
            elem: '#datepicker',
            istime: true,
            format: 'YYYY-MM-DD hh:mm',
            min:(new Date()).toLocaleString(),
            choose:function(value){
                console.log(value);
                $scope.timingTime = value;
                $scope.groupSendTaskInfo.timingTime = value;
            }
        });
    }
    /*校验各个字段*/
    $scope.validateV = function (context, maxlength, reg) {
        if (!context) {
            return false;
        } else {
            if (context.length > maxlength) {
                return false;
            } else {
                if (reg) {
                    if (!reg.test(context)) {
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    return true;
                }
            }
        }
    };
    //校验任务名称
    $scope.checkTaskName = function (taskName) {
        $scope.taskNameValidate = $scope.validateV(taskName, 30, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
    };

    $scope.addMsisdn = function () {
        $scope.groupSendTaskInfo.groupSendTaskMsisdn.push({
            msisdn: ''
        });
    };

    $scope.deleteMsisdn = function (index) {
        $scope.groupSendTaskInfo.groupSendTaskMsisdn.splice(index, 1);
        $scope.checkMsisdnList($scope.groupSendTaskInfo.groupSendTaskMsisdn);
    };

    //校验接收号码
    $scope.checkMsisdnList = function (msisdnList) {
        $scope.msisdnListValidate = true;
        $scope.msisdnListDesc = '';
        var msisdnListTemp = [];
        if (msisdnList) {
            jQuery.each(msisdnList, function (i, e) {
                msisdnListTemp.push(e.msisdn);
            });
            var msisdnList_unique = msisdnListTemp.filter(function (element, index, array) {
                return array.indexOf(element) === index;
            });
            if (msisdnList_unique.length < msisdnList.length) {
                $scope.msisdnListValidate = false;
                $scope.msisdnListDesc = '接收号码重复';
            }
            jQuery.each(msisdnList, function (i, e) {
                if (!e.msisdn) {
                    $scope.msisdnListValidate = false;
                    $scope.msisdnListDesc = '接收号码必填，仅支持数字输入';
                }
            });
            if (!$scope.msisdnListValidate) {
                return;
            }
        }
    };
    $scope.validate = {};
    $scope.dateFormat = function (fmt, date) {
        let ret;
        let opt = {
            "Y+": date.getFullYear().toString(),        // 年
            "m+": (date.getMonth() + 1).toString(),     // 月
            "d+": date.getDate().toString(),            // 日
            "H+": date.getHours().toString(),           // 时
            "M+": date.getMinutes().toString(),         // 分
            "S+": date.getSeconds().toString()          // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
            ret = new RegExp("(" + k + ")").exec(fmt);
            if (ret) {
                fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
            }
        }
        return fmt;
    };
    $scope.checkTaskContentId = function (contentId) {
        if (!contentId) {
            $scope.validate.contentId_null = true;
            return;
        } else {
            $scope.validate.contentId_null = false;
        }

        let req = {
            "contentIDList": [contentId],
            "approveStatus": 3,
            "enterpriseID": $scope.enterpriseID

        };
        $scope.contentScene = null;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    $scope.validate.contentId_statue = true;
                    $scope.validate.isTempContent = false;
                    if (data.resultCode == '1030100000') {
                        if (result.contentInfoList.length > 0) {
                            if(result.contentInfoList[0].status !== 0){
                                $scope.validate.contentId_statue = true;
                                return;
                            }
                            $scope.validate.isTempContent = !$scope.isTempContent(result.contentInfoList[0].content);

                            $scope.validate.contentId_statue = false;
                            if (!result.contentInfoList[0].scene) {
                                $scope.validate.contentId_scene = true;
                            } else {
                                $scope.validate.contentId_scene = false;
                                $scope.contentScene = result.contentInfoList[0].scene;
                                $scope.ports = [];
                                if ($scope.contentScene === 11) {
                                    $scope.ports = $scope.groupSendSmsPort;
                                } else if ($scope.contentScene === 10) {
                                    $scope.ports = $scope.groupSendFlashPort;
                                } else if ($scope.contentScene === 8 || $scope.contentScene === 13) {
                                    $scope.ports = $scope.groupSendMmsPort;
                                }
                                if ($scope.ports.length > 0) {
                                    $scope.groupSendTaskInfo.port = $scope.ports[0];
                                }
                            }
                        }
                    }
                })
            }

        });
    };


    $scope.createTask = function () {
        if ($scope.groupSendTaskInfo.timingTime) {
            var date = new Date($scope.groupSendTaskInfo.timingTime);
            if (!date.getTime() || date <= new Date()) {
                $scope.tip = "任务时间不得小于等于当前时间";
                $('#myModal').modal();
                return;
            }
            $scope.groupSendTaskInfo.timingTime = date;
            $scope.groupSendTaskInfo.taskType = 2;
        }
        $scope.groupSendTaskInfo.serviceType = $scope.sceneServiceType[$scope.contentScene];
        $scope.groupSendTaskInfo.deliveryType = $scope.deliveryTypeMap[$scope.groupSendTaskInfo.serviceType];
        if ($scope.fileNameExcel == "") {
            $scope.fileUrlExcel = "";
        }
        let req = {
            "groupSendTaskInfo":$scope.groupSendTaskInfo,
            "fileUrl":$scope.fileUrlExcel
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/createGroupSendTask",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == "1030100000") {
                        $scope.tip = "保存成功";
                        $('#myModal').modal();
                    }else if ( result.result.resultCode =="1030120081"){
                        $scope.tip = "号码未输入";
                        $('#myModal').modal();
                    }else if ( result.result.resultCode =="1030199999"){
                        $scope.tip = result.result.resultDesc;
                        if(result.result.resultDesc == "使用了未配置的端口号"){
                            $scope.tip = "请联系管理员配置拓展码"
                        }
                        $('#myModal').modal();
                    }else {
                        $scope.tip = "其他错误";
                        $('#myModal').modal();
                    }
                    $scope.taskList();
                })
            }

        });
    };

    $scope.exportFile2 = function () {
        var req = {
            "param": {
                "taskID": parseInt($scope.groupSendTaskInfo.taskId),
                //"token": $.cookie("token"),
                //"isExport": 0
            },
            "url": "/qycy/ecpmp/ecpmpServices/groupSendTaskService/downGroupSendMsisdnCsvFileService",
            "method": "get"
        }
        CommonUtils.exportFile(req);
    };

    $scope.export = function () {
        var req = JSON.stringify($scope.reqTemp);
        req = {
            "param": {
                "req": req,
                "method": "taskList"
            },
            "url": "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downCsvFile",
            "method": "get"
        };
        CommonUtils.exportFile(req);

    };
});
