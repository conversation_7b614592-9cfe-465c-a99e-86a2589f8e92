<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ProvinceConfigMapper">
    <resultMap id="provinceConfigWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceConfigWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer"/>
        <result property="provinceID" column="provinceID" javaType="java.lang.String"/>
        <result property="callBackUrl" column="callBackUrl" javaType="java.lang.String"/>
        <result property="memberCallbackUrl" column="memberCallbackUrl" javaType="java.lang.String"/>
    </resultMap>


    <select id="queryByprovinceID" resultMap="provinceConfigWrapper">
        select
        ID,
        provinceID,
        callBackUrl,
        memberCallbackUrl
        from ecpm_t_province_config
        where provinceID = #{provinceID}
    </select>
</mapper>