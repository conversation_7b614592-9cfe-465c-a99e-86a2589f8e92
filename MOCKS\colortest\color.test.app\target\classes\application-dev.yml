spring.datasource:
    url: ******************************************************************************************************************
    username: dsumdb
    password: lk55LKCsQzCtKVvG5NYcMQ==
    driverClassName: com.mysql.jdbc.Driver
    max-active: 20
    max-idle: 8
    min-idle: 8
    initial-size: 10


spring:
  http:
    encoding:
      force: true
      charset: UTF-8
  freemarker:
    allow-request-override: false
    cache: false
    check-template-location: true
    charset: UTF-8
    content-type: text/html; charset=utf-8
    prefer-file-system-access: false
    expose-request-attributes: false
    expose-session-attributes: false
    expose-spring-macro-helpers: false
    suffix: .ftl
    template-loader-path: classpath:/templates

eureka:
   client:
     serviceUrl:
       defaultZone: http://127.0.0.1:19000/eureka/
   instance:
     preferIpAddress: true
     instanceId: ${server.ip}:${server.port}
