<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<!-- <base href="/" /> -->
	<title>新增彩印</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
	<link href="../../../../../css/reset.css" rel="stylesheet" />
	<link href="../../../../../css/addContent.css" rel="stylesheet" />
	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
		src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datetimepicker.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datetimepicker.zh-CN.js"></script>
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
	<!--<link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css" />-->
	<link href="../../../../../css/bootstrap-datetimepicker.css" rel="stylesheet" type="text/css" />
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
	<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
	<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
	<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
	<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet" />
	<script type="text/javascript" src="createTask.js"></script>
	<style>
		[ng-cloak] {
			display: none !important;
		}

		.cursor-def {
			cursor: default !important;
		}

		.form-group {
    		margin-bottom: 20px;
		}

		.form-group label {
			text-align: right;
		}

		.pic-wrapper,.ctn-wrapper {
			overflow: hidden;
			padding: 10px 0;
			position: relative;
		}
		.bg-disabled{
			background: #a69aec;
		}
		.redBorder{
			border-color:red;
		}
		.addMsinsdn{
		    display: inline-block;
		    width: 23px;
		    height: 23px;
		    background: url(../../../../../assets/images/add.png) no-repeat;
		    vertical-align: middle;
		    background-position: 0 0;
		}
		.deleteMsinsdn{
		    display: inline-block;
		    width: 23px;
		    height: 23px;
		    background: url(../../../../../assets/images/delete.png) no-repeat;
		    vertical-align: middle;
		    background-position: 0 0;
		}
		.switch{
			display: table-cell;
		}
		.datCursor{
			cursor: default !important;
		}
	</style>
</head>

<body ng-app='myApp' ng-controller='CreateTaskController' ng-init="init();">
	<div style="min-width: 1024px;" class="order-manage" ng-cloak>
		<!--&lt;!&ndash; 代理商自己登陆 &ndash;&gt;-->
		<!--<div ng-if="loginRoleType=='agent'" class="cooperation-head">-->
			<!--<span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;-->
			<!--<span class="second-tab" ng-bind="'SENDTASKMANAGE'|translate"></span>&nbsp;&gt;&nbsp;-->
			<!--<span ng-show="operate=='add'" class="second-tab" ng-bind="'ADDTASK'|translate"></span>-->
			<!--<span ng-show="operate=='modify'" class="second-tab" ng-bind="'MODIFYTASK'|translate"></span>-->
			<!--<span ng-show="operate=='detail'" class="second-tab" ng-bind="'TASKDETAIL'|translate"></span>-->
		<!--</div>-->
		<!--&lt;!&ndash; 管理员登陆查看代理商 &ndash;&gt;-->
		<!--<div ng-if="isSuperManager" class="cooperation-head">-->
			<!--<span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;-->
			<!--<span class="second-tab" ng-bind="'SENDTASKMANAGE'|translate"></span>&nbsp;&gt;&nbsp;-->
			<!--<span ng-show="operate=='add'" class="second-tab" ng-bind="'ADDTASK'|translate"></span>-->
			<!--<span ng-show="operate=='modify'" class="second-tab" ng-bind="'MODIFYTASK'|translate"></span>-->
			<!--<span ng-show="operate=='detail'" class="second-tab" ng-bind="'TASKDETAIL'|translate"></span>-->
		<!--</div>-->
		<!-- 直客企业自己登陆 -->
		<!-- 管理员登陆查看直客 -->
		<div  class="cooperation-head">
			<span class="frist-tab" >代理商管理 > 子企业管理 > 企业通知</span>
		</div>
		<div class="cooper-tab">
			<form class="form-horizontal" name="myForm" novalidate>
				<div class="form-group">
					<div class="row">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'TASKNAME'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
							<input class="form-control"
								type="text" id="taskName" name="taskName" ng-model="groupSendTaskInfo.taskName"
								placeholder="{{'INPUTTASKNAME2'|translate}}"
								ng-blur="checkTaskName(groupSendTaskInfo.taskName)"
								ng-disabled="operate =='detail'"
								ng-class="{'redBorder':!taskNameValidate}"
								title={{groupSendTaskInfo.taskName}}
								autocomplete="off">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
								ng-show="!taskNameValidate">
							<span class="redFont" ng-show="!taskNameValidate">
								{{'TASKNAMEDESC'|translate}}</span>
						</div>
					</div>
				</div>

				<div class="form-group">
					<div class="row">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
							<select class="form-control" ng-model="groupSendTaskInfo.serviceType"
								ng-options="x.id as x.name for x in serviceTypeChoise" ng-change="changeServiceType(groupSendTaskInfo.serviceType)"
								ng-disabled="operate =='detail' || operate =='modify'"></select>
						</div>
						<div class="col-lg-3 col-xs-3  col-sm-3 col-md-3">
							<icon style="color:red;" ng-show="isStatusOpen==0 && operate!='detail'">*</icon>
							<span class="redFont" ng-show="isStatusOpen==0 && operate!='detail'" style="color:red;">
									{{'ZCSTATUSCLOSEDESC'|translate}}
							</span>
						</div>
					</div>
				</div>

				<div class="form-group" ng-show="operate =='detail'">
					<div class="row">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'GROUP_STATUS'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-5  col-sm-5 col-md-5">
							<p ng-disabled="true" class="form-control" title="statusMap[groupSendTaskInfo.status]"
								ng-bind="statusMap[groupSendTaskInfo.status]"></p>
						</div>
					</div>
				</div>

				<div class="form-group" ng-show="operate !='detail'">
					<div class="row">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span ng-bind="'RECEIVED'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
							<icon ng-show="groupSendTaskInfo.groupSendTaskMsisdn.length==0" ng-click="addMsisdn()"
								class="addMsinsdn">
							</icon>
							<div style="overflow:hidden;" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
								<input class="form-control" style="float:left;margin-bottom:11px"
									type="text"  id="{{'msisdn'+$index}}" name="{{'msisdn'+$index}}" ng-model="item.msisdn"
									placeholder="{{'ENTERPRISE_PLEASEINPUTRECEIVENUM'|translate}}"
									ng-blur="checkMsisdnList(groupSendTaskInfo.groupSendTaskMsisdn)"
									ng-disabled="operate =='detail'"
									title={{item.msisdn}}
									pattern="[0-9]*[0-9][0-9]*$"
									ng-class="{'redBorder':!msisdnListValidate}">
								</input>
							</div>
						</div>
						<div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
							<div style="overflow:hidden;padding:1px 0px 10px 0px" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
								<icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length<50" ng-click="addMsisdn()"
									class="addMsinsdn">
								</icon>
								<icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length>=50" ng-click=""
									class="addMsinsdn">
								</icon>
								<icon ng-click="deleteMsisdn($index)"
									class="deleteMsinsdn">
								</icon>
							</div>
						</div>
						<div  class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="padding:1px 0px 10px 0px"
								ng-show="(myForm['txtContent'+$index].$dirty && myForm['msisdn'+$index].$invalid) ||!msisdnListValidate"
								ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
							<!-- <span ng-show="myForm['msisdn'+$index].$error.pattern" ng-bind="'RECEIVEDDESC'|translate"></span> -->
							<span class="redFont" ng-show="!msisdnListValidate">{{msisdnListDesc}}</span>
						</div>
					</div>
				</div>

				<div class="form-group" ng-show="operate !='detail'">
					<div class="row">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span ng-bind="'COMMON_FILENAME'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
							<input type="text" class="form-control" ng-model="fileNameExcel"
										 placeholder="请导入.xlsx,.xls格式文件" style="width: 100%;" ng-disabled="true"/>
						</div>
						<cy:uploadifyfile filelistid="fileListExcel" filepickerid="filePickerExcel" accepttype="accepttypeExcel"
							uploadifyid="uploadifyidExcel" validate="isValidateExcel" filesize="filesizeExcel"
							mimetypes="mimetypesExcel"
							formdata="uploadParamExcel" uploadurl="uploadurlExcel" desc="uploadDescExcel" numlimit="numlimitExcel"
							urllist="urlListExcel" createthumbnail="isCreateThumbnailExcel" auto="auto" fileUse="msidsnList"
							style="margin-left: 15px;float: left;">
						</cy:uploadifyfile>
						<div class="downloadRow" style="margin: 10px 0 0 29px;">
							<a target="_blank" href="/qycy/ecpmp/assets/importMsisdnTemplate.xlsx" class="downMod"
								 style="margin-right: 40px;"
								 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate" ng-show="operate =='add'"></a>
							<a ng-click="exportFile()"
								title="{{msisdnDownloadUrl}}" style="margin-right: 40px;"
								ng-bind="'MSISDNDOWNLOD'|translate" ng-show="operate =='modify'">
							</a>
						</div>
					</div>
				</div>
				<div class="form-group" ng-show="groupSendTaskInfo.serviceType != 16 ">
					<div class="row">
						<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
							<span ng-bind="'SEND_TIME_DELAY'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4 time">
							<input readonly="true" ng-disabled="operate =='detail'"
								   id="datepicker"  autocomplete="off" type="text"  name="timingTime"
								   ng-model="groupSendTaskInfo.timingTimeTemp"  class="form-control" ng-class="{'datCursor':operate !='detail'}">
							<i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
							<span style="color:red" ng-show="timeError">
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
									 align="absmiddle">
								<!--<span ng-show="myForm.time.$error.required"-->
									  <!--ng-bind="'CONTENT_PUSHTIMEREQUIRE'|translate"></span>-->
								<span ng-show="timeError" ng-bind="'CONTENT_TIMEERRORTIP'|translate"></span>
							</span>
						</div>
					</div>
				</div>
				<div class="form-group" ng-show="groupSendTaskInfo.serviceType != 16 && groupSendTaskInfo.serviceType != 20">
					<div class="row">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span ng-bind="'SIGNATURE'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-5  col-sm-5 col-md-5">
							<input class="form-control" ng-show="operate !='detail'"
								   type="text" id="signName" name="signName" ng-model="groupSendTaskInfo.content.signature"
								   placeholder="{{'SIGN_NAME'|translate}}"
								   ng-blur="checkSignName(groupSendTaskInfo.content.signature)"
								   ng-disabled="operate =='detail'">
							<input class="form-control" ng-show="operate =='detail'"
								   type="text" id="signName" name="signName" ng-model="groupSendTaskInfo.content.signature"
								   ng-blur="checkSignName(groupSendTaskInfo.content.signature)"
								   ng-disabled="operate =='detail'">
								   <!--ng-class="{'redBorder':!signName}"-->
								   <!--title={{groupSendTaskInfo.taskName}}-->
								   <!--autocomplete="off">-->
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
								 ng-show="!signatureValidate">
							<span class="redFont" ng-show="!signatureValidate">
								{{'SIGN_NAME'|translate}}</span>
						</div>
					</div>
				</div>
                <div class="form-group" ng-show="groupSendTaskInfo.serviceType != 16 && groupSendTaskInfo.serviceType != 20">
                    <div class="row">
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <icon>*</icon><span ng-bind="'TASKCONTENT'|translate"></span>：
                        </label>
                        <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5">
                            <textarea style="float:left;height: 74px;"
                                      placeholder="{{msg}}" class="form-control" rows="3"
                                      name="{{'content'}}" ng-model="groupSendTaskInfo.content.content"
                                      ng-blur="checkSmsContent(groupSendTaskInfo.content.content,3,'')"
                                      ng-disabled="operate=='detail'">
                            </textarea>
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
								 ng-show="!contentValidate">
                            <span class="redFont" ng-show="!contentValidate">
									{{'SMS_CONTENT_LENGTH_MAX_750'|translate}}
								</span>
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
								 ng-show="!contentValidateScreen">
                            <span class="redFont" ng-show="!contentValidateScreen">
									{{'SMS_CONTENT_LENGTH_MAX_62'|translate}}
								</span>
                        </div>
                    </div>
                </div>

				<!-- 增彩 -->
				<div class="form-group" ng-show="groupSendTaskInfo.serviceType == 16">
					<div style="overflow:hidden;" ng-cloak>
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="margin-left:-8px">
							<icon>*</icon><span ng-bind="'TASKCONTENT'|translate"></span>：
						</label>
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="max-width:116px;margin-left:-12px">
								<icon>*</icon><span ng-bind="'ZCCONTENTTITLE'|translate"></span>：
							</label>
							<div class="col-lg-3 col-xs-3  col-sm-3 col-md-3">
								<input class="form-control"
									type="text" id="contentTitle" name="contentTitle" ng-model="groupSendTaskInfo.content.contentTitle"
									placeholder="{{'INPUTZCCONTENTTITLE'|translate}}"
									ng-blur="checkContentTitle(groupSendTaskInfo.content.contentTitle)"
									ng-disabled="operate =='detail'"
									ng-class="{'redBorder':!contentTitleValidate}"
									title={{groupSendTaskInfo.content.contentTitle}}>
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
									ng-show="!contentTitleValidate">
								<span class="redFont" ng-show="!contentTitleValidate">
									{{'ZCCONTENTTITLEDESC'|translate}}
								</span>
							</div>
						</div>

						<div class="form-group">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"
									style="max-width:108px;margin-left:15.8%">
								<icon>*</icon><span ng-bind="'ZCCONTENT'|translate"></span>：
							</label>
							<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
								<div style="padding-top:7px;color:rgb(153,153,153)" ng-show="contentList.length==0">
									<p ng-bind="'ZC_ADDCONTENT_PLH'|translate" ></p>
								</div>
								<div class="ctn-pic-list" ng-repeat="item in contentList">
									<div class="pic-wrapper" ng-if="item.frameType ==1">
										<img style="float:left;max-width: 250px;border: 1px solid darkgrey;"
											ng-src="{{item.formatFramePicUrl}}" alt="">
										<button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
											type="submit" class="btn btn-primary search-btn "
											style="position: absolute;left: 290px;top:50%;margin-top: -17px;">
											<span ng-bind="'COMMON_DELETE'|translate"></span></button>
									</div>
									<div class="pic-wrapper" ng-if="item.frameType ==3">
										<video style="float:left;max-width: 350px;border: 1px solid darkgrey;" controls="controls">
										  <source ng-src="{{item.formatFramePicUrl}}"  type="video/mp4">
-                                         <source ng-src="{{item.formatFramePicUrl}}"  type="video/3gp">
										</video>

										<button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
											type="submit" class="btn btn-primary search-btn "
											style="position: absolute;margin-left: 15px;top:50%;margin-top: -17px;">
											<span ng-bind="'COMMON_DELETE'|translate"></span>
										</button>
									</div>
									<div class="ctn-wrapper" ng-if="!item.framePicUrl && (item.frameType !=3 && item.frameType !=1)">
										<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4" style="padding-left:0px">
											<textarea style="float:left;height: 74px;"
												placeholder="请输入彩印内容0~1000字" class="form-control" rows="3"
												name="{{'contentzc'+$index}}" ng-model="item.frameTxt" required
												ng-blur="sensitiveCheck(ctnTextSum,2,'')"
												ng-disabled="operate=='detail'">
											</textarea>
										</div>
										<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
											<button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
												type="submit" class="btn btn-primary search-btn "
												style="float: left;margin: 20px 20px;">
												<span ng-bind="'COMMON_DELETE'|translate" ></span>
											</button>
										</div>
										<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
												ng-show="(myForm['contentzc'+$index].$dirty && myForm['contentzc'+$index].$invalid)"
												style="padding-top:18px">
											<span style="color:red">
												<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
													align="absmiddle">
												<span ng-show="myForm['contentzc'+$index].$error.required"
													ng-bind="'ZCCONTENTREQUIRE'|translate"></span>
											</span>
										</div>
										<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
												ng-show="myForm['contentzc'+$index].$dirty && sensitiveWords[2].length>0 &&item.frameTxt"
												style="padding-top:18px">
											<span style="color:red">
											<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
												align="absmiddle">
											<span>
												{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[2]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
											</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!-- 添加图片和添加文本按钮 -->
					<div style="overflow:hidden;" ng-show="operate!='detail'">
						<p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
							style="color:red;padding-left: 15px;" ng-bind="'TASK_CONTENTPICTEXT_LIMITTIP'|translate">
						</p>
						<p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
							ng-show="ctnTextSumLength>1000" style="color:red;padding-left:12px">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
								align="absmiddle">
							<span ng-bind="'CONTENT_TEXT_MAXLENGTHTIP3'|translate"></span>
						</p>
						<p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
							ng-show="errorInfoVideo" style="color:red;padding-left:12px">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
								align="absmiddle">
							<span>{{errorInfoVideo|translate}}</span>
						</p>
						<div class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-7 col-xs-8 col-sm-8 col-md-8 clear-preview">
							<cy:uploadifyfile filelistid="fileListVideo" filepickerid="filePickerVideo" accepttype="accepttypeVideo"
								uploadifyid="uploadifyidVideo" validate="isValidateVideo" filesize="filesizeVideo"
								mimetypes="mimetypesVideo"
								formdata="uploadParamVideo" uploadurl="uploadurlVideo" desc="uploadDescVideo" numlimit="numlimitVideo"
								urllist="urlListVideo" createthumbnail="isCreateThumbnailVideo" auto="auto"
								style="margin-left: 15px;float: left;" ng-show="videoLength<3" >
							</cy:uploadifyfile>
							<!-- 视频最大数时展示的上传视频框 -->
							<button ng-disabled="videoLength>2" style="float:left;margin-right: 15px;"
									ng-show="videoLength>2" type="submit"
									class="btn btn-primary search-btn">
									<icon class="file-icon" style="margin-top: -4px;float: left;"></icon>
									<span style="float:left" ng-bind="'点击添加视频'|translate"></span>
							</button>
							<button ng-disabled="ctnTextMount>=3" ng-click="addTextCtn()" style="float:left"
								ng-if="operate !='detail'" type="submit"
								class="btn btn-primary search-btn">
								<span ng-bind="'CONTENT_CLICK_ADDTEXT'|translate"></span>
							</button>
							<cy:uploadify class="col-lg-6 col-xs-6 col-sm-6 col-md-6" ng-show="picLength<3 && operate !='detail'"
								filelistid="fileListImg" filepickerid="filePickerImg" accepttype="accepttypeImg"
								uploadifyid="uploadifyidImg" validate="isValidateImg" filesize="filesizeImg"
								mimetypes="mimetypesImg" formdata="uploadParamImg" uploadurl="uploadurlImg" desc=""
								numlimit="numlimitImg" createthumbnail="isCreateThumbnailImg" style="float: left;">
							</cy:uploadify>
							<!-- 图片最大张数时展示的上传图片框 -->
							<button ng-disabled="picLength>2" style="float:left;margin-right: 15px;margin-left:12px"
									ng-show="picLength>2" type="submit"
									class="btn btn-primary search-btn">
									<icon class="file-icon" style="margin-top: -4px;float: left;"></icon>
									<span style="float:left" ng-bind="'点击添加图片'|translate"></span>
							</button>
						</div>
					</div>
				</div>
				<!-- 彩信 -->
				<div class="form-group" ng-show="groupSendTaskInfo.serviceType == 20">
					<div style="overflow:hidden;" ng-cloak>
						<!--<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="margin-left:-8px">-->
							<!--<icon>*</icon><span ng-bind="'TASKCONTENT'|translate"></span>：-->
						<!--</label>-->
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<icon>*</icon><span ng-bind="'CXCONTENTTITLE'|translate"></span>：
							</label>
							<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
								<input class="form-control"
									type="text" id="contentTitle" name="contentTitle" ng-model="groupSendTaskInfo.content.contentTitle"
									placeholder="{{'INPUTCXCONTENTTITLE'|translate}}"
									ng-blur="checkCXContentTitle(groupSendTaskInfo.content.contentTitle)"
									ng-disabled="operate =='detail'"
									ng-class="{'redBorder':!contentTitleValidate}"
									title={{groupSendTaskInfo.content.contentTitle}}>
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
									ng-show="!contentTitleValidate">
								<span class="redFont" ng-show="!contentTitleValidate">
									{{'CONTENTTITLEDESC20'|translate}}
								</span>
							</div>
						</div>

						<div class="form-group">
							<div class="row">
							<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
								<icon>*</icon><span ng-bind="'CXCONTENT'|translate"></span>：
							</label>
							<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
								<div style="padding-top:7px;color:rgb(153,153,153)" ng-show="contentList.length==0">
									<p ng-bind="'CX_ADDCONTENT_PLH'|translate" ></p>
								</div>
								<div class="ctn-pic-list" ng-repeat="item in contentList">
									<div class="pic-wrapper" ng-if="item.frameType ==1">
										<div class="col-lg-9 col-xs-9 col-sm-9 col-md-9" style="padding-left:0px">
										<img style="float:left;width: 100%;border: 1px solid darkgrey;"
											 ng-src="{{item.formatFramePicUrl}}" alt="">
										</div>
										<div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
											<button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
											type="submit" class="btn btn-primary search-btn">
											<span ng-bind="'COMMON_DELETE'|translate"></span></button>
										</div>
									</div>
									<div class="ctn-wrapper" ng-if="!item.framePicUrl && (item.frameType !=3 && item.frameType !=1)">
										<div class="col-lg-9 col-xs-9 col-sm-9 col-md-9" style="padding-left:0px">
											<textarea style="float:left;height: 74px;"
												placeholder="请输入彩印内容0~750字" class="form-control" rows="3"
												name="{{'content'+$index}}" ng-model="item.frameTxt" required
												ng-blur="sensitiveCheck(ctnTextSum,2,'')"
												ng-disabled="operate=='detail'">
											</textarea>
										</div>
										<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
											<button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
												type="submit" class="btn btn-primary search-btn "
												style="float: left;margin: 20px 0px;">
												<span ng-bind="'COMMON_DELETE'|translate" ></span>
											</button>
										</div>
										<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
												ng-show="(myForm['content'+$index].$dirty && myForm['content'+$index].$invalid)"
												style="padding-top:18px">
											<span style="color:red">
												<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
													align="absmiddle">
												<span ng-show="myForm['content'+$index].$error.required"
													ng-bind="'CXCONTENTREQUIRE'|translate"></span>
											</span>
										</div>
										<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
												ng-show="myForm['content'+$index].$dirty && sensitiveWords[2].length>0 &&item.frameTxt"
												style="padding-top:18px">
											<span style="color:red">
											<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
												align="absmiddle">
											<span>
												{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[2]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
											</span>
										</div>
									</div>
								</div>
							</div>
						</div>
						</div>
					</div>
					<!-- 添加图片和添加文本按钮 -->
					<div style="overflow:hidden;" ng-show="operate!='detail'">
						<p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
							style="color:red;padding-left: 15px;" ng-bind="'TASK_CXCONTENTPICTEXT_LIMITTIP'|translate">
						</p>
						<p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
							ng-show="ctnTextSumLength>750" style="color:red;padding-left:12px">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
								align="absmiddle">
							<span ng-bind="'CONTENT_TEXT_MAXLENGTHTIP'|translate"></span>
						</p>
						<p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
						   ng-show="allPicSize>286720" style="color:red;">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
								 align="absmiddle">
							<span ng-bind="'CONTENT_FILESIZE_MAXTIP'|translate"></span>
						</p>
						<div class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-7 col-xs-8 col-sm-8 col-md-8 clear-preview">
							<button ng-disabled="ctnTextMount>=5" ng-click="addTextCtn()" style="float:left"
								ng-if="operate !='detail'" type="submit"
								class="btn btn-primary search-btn">
								<span ng-bind="'CONTENT_CLICK_ADDTEXT'|translate"></span>
							</button>
							<cy:uploadify class="col-lg-6 col-xs-6 col-sm-6 col-md-6" ng-show="picLength<5 && operate !='detail'"
								filelistid="fileListImgCaixin" filepickerid="filePickerImgCaixin" accepttype="accepttypeImgCaixin"
								uploadifyid="uploadifyidImgCaixin" validate="isValidateImgCaixin" filesize="filesizeImgCaixin"
								mimetypes="mimetypesImgCaixin" formdata="uploadParamImgCaixin" uploadurl="uploadurlImgCaixin" desc=""
								numlimit="numlimitImgCaixin" createthumbnail="isCreateThumbnailImgCaixin" style="float: left;">
							</cy:uploadify>
							<!-- 图片最大张数时展示的上传图片框 -->
							<button ng-disabled="picLength>4" style="float:left;margin-right: 15px;margin-left:12px"
									ng-show="picLength>4" type="submit"
									class="btn btn-primary search-btn">
									<icon class="file-icon" style="margin-top: -4px;float: left;"></icon>
									<span style="float:left" ng-bind="'点击添加图片'|translate"></span>
							</button>
						</div>
					</div>
				</div>

				<div class="form-group" ng-show="operate =='detail'">
					<div class="row">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span ng-bind="'RECEIVED'|translate"></span>：
						</label>
						<div class="col-lg-4 col-xs-5  col-sm-5 col-md-5" style="padding-top: 7px;">
							<a ng-click="exportFile()"
								title="{{msisdnDownloadUrl}}" style="margin-right: 40px;"
								ng-bind="'MSISDNDOWNLOD'|translate">
							</a>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
	<div class="order-btn row">
		<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
			style="padding-left: 30px;">
			<button type="submit" ng-if="operate!='detail'" class="btn btn-primary search-btn"
				ng-disabled="myForm.$invalid||isSensitive[0]||isSensitive[1]||isSensitive[2]||
								contentList.length==0||ctnTextSumLength>1000||
								!groupSendTaskInfo.taskName||!groupSendTaskInfo.serviceType
								||isStatusOpen==0
								||!groupSendTaskInfo.content.contentTitle||contentList.length==0||
								!taskNameValidate||!msisdnListValidate||!signatureValidate
								||((groupSendTaskInfo.serviceType == 17 ||groupSendTaskInfo.serviceType == 18||groupSendTaskInfo.serviceType == 19)
								  && !groupSendTaskInfo.content.content||
								  !contentValidate)
								||((groupSendTaskInfo.serviceType == 18||groupSendTaskInfo.serviceType == 19)
								  && !groupSendTaskInfo.content.content||!contentValidateScreen)
								||(groupSendTaskInfo.serviceType == 20 && allPicSize>286720)
								"
" ng-click="checkBeforeSubmit()"><span
					ng-bind="'COMMON_SAVE'|translate"></span></button>
			<button type="submit" class="btn btn-back" ng-click="goBack()"><span
					ng-bind="'COMMON_BACK'|translate"></span></button>
		</div>
	</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate">
						</button>
					</div>
				</div>
			</div>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="updatePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_OK'|translate" ng-click="updateGroupSendTask(req)">
						</button>
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_CANCLE'|translate">
						</button>
					</div>
				</div>
			</div>
		</div>

</body>
<style>
	.modal-dialog {
		width: 300px !important;
	}

	button.preview {
		float: left;
		margin-left: 30px;
	}

	.clear-preview {
		padding-left: 15px;
	}

	.cursorDefault {
		cursor: default !important;
	}

	.ng-dirty.ng-invalid {
		border-color: red;
	}
	.ng-dirty.invalid {
		border-color: red;
	}
	#filePicker div:nth-child(2) {
        width: 100% !important;
        height: 100% !important;
    }
</style>

</html>