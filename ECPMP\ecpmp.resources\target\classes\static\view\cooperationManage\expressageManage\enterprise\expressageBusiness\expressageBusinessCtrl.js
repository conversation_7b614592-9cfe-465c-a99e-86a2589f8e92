var app = angular.module("myApp", ["util.ajax", "page", "angularI18n"])
app.controller("expressageBusinessController", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
	$scope.serviceType = 1;
	$scope.pkgType = "0";
	$scope.pkgQuota = "1";
	$scope.pkgPrice = Number(5.0);
	$scope.enterpriseID=$.cookie('enterpriseID');
    if($scope.enterpriseID != null)
	{
    	var req={"enterpriseID":$scope.enterpriseID};
    	RestClientUtil.ajaxRequest({
    	      type: 'POST',
    	      url: "/ecpmp/ecpmpServices/intelligentCallService/queryWltService",
    	      data: JSON.stringify(req),
    	      success: function (result) {
    	        $rootScope.$apply(function () {
    	          var data = result.result;
    	          if (data.resultCode == '1030100000' && result.wltServiceInfo != null) {
    	        		  $scope.serviceType = result.wltServiceInfo.serviceType;
    	        		  $scope.changeServiceType($scope.serviceType);
    	        		  $scope.pkgType = result.wltServiceInfo.pkgType+"";
    	        		  $scope.pkgQuota = result.wltServiceInfo.pkgQuota;
    	        		  $scope.changePkgQuota($scope.pkgQuota);
    	        		  $scope.pkgPrice = Number(result.wltServiceInfo.pkgPrice);
    	        		  $scope.id = result.wltServiceInfo.id;
        		  }else {
        			  	$scope.serviceType = 1;
	    				$scope.pkgType = "0";
	    				$scope.pkgQuota = "1";
	    				$scope.pkgPrice = Number(5.0);
        		  }
    	        })
    	      },
    	      error: function () {
    	        $rootScope.$apply(function () {
    	          $scope.tip = '1030120500';
    	          $('#myModal').modal();
    	        })
    	      }
    	    });
	} else {
		$scope.serviceType = 1;
		$scope.pkgType = "0";
		$scope.pkgQuota = "1";
		$scope.pkgPrice = Number(5.0);
	}
    
  };

  $scope.goBack = function () {
    location.href = "../../enterpriseList/enterpriseList.html"
  };
    $scope.changeServiceType = function (val) {
          // $('.black .redio-li').find('span').removeClass('checked');
          // $('.black .redio-li').eq(val-1).find('span').addClass('checked');
          $scope.serviceType = val;
    }
    $scope.changePkgQuota = function (val) {
    	$('.white .redio-li').find('span').removeClass('checked');
        $('.white .redio-li').eq(val-1).find('span').addClass('checked');
        if(val == 1){
        	$scope.pkgPrice = 5;
        }
        if(val == 2){
        	$scope.pkgPrice = 50;
        }
        if(val == 3){
        	$scope.pkgPrice = 100;
        }
        if(val == 4){
        	$scope.pkgPrice = 500;
        }
        $scope.pkgQuota = val;
    }

  $scope.saveWltService = function () {
    var req = {
      "wltServiceInfo": {
        "serviceType": $scope.serviceType,
        "pkgType": $scope.pkgType,
        "pkgQuota": $scope.pkgQuota,
        "pkgPrice": $scope.pkgPrice
      }
    };
    if($scope.enterpriseID != null)
	{
    	req.wltServiceInfo.enterpriseID = $scope.enterpriseID;
	}
    if($scope.id != null)
	{
    	req.wltServiceInfo.id = $scope.id;
	}
    console.log(req);
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/intelligentCallService/submitWltService",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.tip = 'COMMON_SAVESUCCESS';
            $('#myModal').modal();
          } else {
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
    
  }

  $scope.closeTip = function () {
//    if($scope.tip == "COMMON_SAVESUCCESS"){
//      window.history.back();
//    }
  }
/*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
});