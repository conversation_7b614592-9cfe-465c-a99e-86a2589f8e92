---
#服务端口
server:
  port: 19004
  
spring:
#配置服务名
  application:
    name: cy-config-server
  cloud:
    inetutils:
      ignored-interfaces[0]: ens160
#      ignored-interfaces[1]: docker0
#      default-ip-address: *************
    #配置git地址，请配置实际地址
    config:
      label: master
      server:
        git:
          uri: http://*************:8080/zhengweidong/cyconfig.git
          search-paths: dev_configs
          username: <PERSON><PERSON><PERSON><PERSON><PERSON>@e-byte.com
          password: a15710619223
          force-pull: true
    stream:
      kafka:
        binder:
          zkNodes: **************:2181,**************:2181,**************:2181
          brokers: **************:9092,**************:9092,**************:9092
      default-binder: kafka
  jmx:
    enabled: false
#eureka 用于服务发现，配置多个eureka地址
eureka: 
  client:
    region: region1
    availability-zones: 
      region1: cy_eureka
    service-url: 
      cy_eureka: http://127.0.0.1:19000/eureka/
  instance:
    metadata-map:
      cluster: main
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    
endpoints:
  shutdown:
    enabled: true
    sensitive: false
management:
  security:
     enabled: false
