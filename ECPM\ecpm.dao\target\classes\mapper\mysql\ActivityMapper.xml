<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityMapper">
	<resultMap id="activityWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivityWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="activityCode" column="activityCode" javaType="java.lang.String" />
		<result property="activityName" column="activityName" javaType="java.lang.String" />
		<result property="activityRuleDesc" column="activityRuleDesc"
			javaType="java.lang.String" />
		<result property="rewardExplain" column="rewardExplain"
			javaType="java.lang.String" />
		<result property="rewardNotify" column="rewardNotify" javaType="java.lang.String" />
		<result property="maxPushNum" column="maxPushNum" javaType="java.lang.Integer" />
		<result property="totalNum" column="totalNum" javaType="java.lang.Integer" />
		<result property="memo" column="memo" javaType="java.lang.String" />
		<result property="templateID" column="templateID" javaType="java.lang.Integer" />
		<result property="backLogoURL" column="backLogoURL" javaType="java.lang.String" />
		<result property="activityUrl" column="activityUrl" javaType="java.lang.String" />
		<result property="activityQrCode" column="activityQrCode"
			javaType="java.lang.String" />
		<result property="effectiveTime" column="effectivetime"
			javaType="Date" />
		<result property="expireTime" column="expiretime" javaType="Date" />
		<result property="auditStatus" column="auditStatus" javaType="java.lang.Integer" />
		<result property="approveIdea" column="approveIdea" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="isPrematureTerminated" column="isPrematureTerminated"
			javaType="java.lang.Integer" />
		<result property="terminatedReason" column="terminatedReason"
			javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>



	<select id="getActivityInfoOnline" resultMap="activityWrapper">
		select
		t.ID,
		t.activityCode,
		t.activityName,
		t.activityRuleDesc,
		t.rewardExplain,
		t.rewardNotify,
		t.maxPushNum,
		t.totalNum,
		t.memo,
		t.templateID,
		t.backLogoURL,
		t.activityUrl,
		t.activityQrCode,
		t.effectivetime,
		t.expiretime,
		t.auditStatus,
		t.approveIdea,
		t.enterpriseID,
		t.enterpriseName,
		t.isPrematureTerminated,
		t.terminatedReason,
		t.createTime,
		t.updateTime,
		t.operatorID
		from
		ecpm_t_activity t
		where t.auditStatus=2
		and
		t.isPrematureTerminated =1
		<if test="nowTime != null">
			and t.effectivetime <![CDATA[ <= ]]>
			#{nowTime}
			and t.expiretime <![CDATA[ > ]]>
			#{nowTime}
		</if>

	</select>


	<select id="getActivityInfoOfflineTask" resultMap="activityWrapper">
		select
		t.ID,
		t.activityCode,
		t.activityName,
		t.activityRuleDesc,
		t.rewardExplain,
		t.rewardNotify,
		t.maxPushNum,
		t.totalNum,
		t.memo,
		t.templateID,
		t.backLogoURL,
		t.activityUrl,
		t.activityQrCode,
		t.effectivetime,
		t.expiretime,
		t.auditStatus,
		t.approveIdea,
		t.enterpriseID,
		t.enterpriseName,
		t.isPrematureTerminated,
		t.terminatedReason,
		t.createTime,
		t.updateTime,
		t.operatorID
		from
		ecpm_t_activity t
		where t.auditStatus=2
		and
		((t.isPrematureTerminated !=1 and t.isPrematureTerminated !=2 and t.isPrematureTerminated !=3) or t.isPrematureTerminated is null)
		<if test="nowTime != null">
			and t.effectivetime <![CDATA[ < ]]>
			#{nowTime}
			and t.expiretime <![CDATA[ > ]]>
			#{nowTime}
		</if>
	</select>


	<select id="getActivityInfoByActivityID" resultMap="activityWrapper">
		select
		ID,
		activityCode,
		activityName,
		activityRuleDesc,
		rewardExplain,
		rewardNotify,
		maxPushNum,
		totalNum,
		memo,
		templateID,
		backLogoURL,
		activityUrl,
		activityQrCode,
		effectivetime,
		expiretime,
		auditStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		isPrematureTerminated,
		terminatedReason,
		createTime,
		updateTime,
		operatorID
		from ecpm_t_activity
		where
		ID=#{activityID}
	</select>

	<select id="getEffInfoByActivityID" resultMap="activityWrapper">
		SELECT
		ID,
		activityCode,
		activityName,
		activityRuleDesc,
		rewardExplain,
		rewardNotify,
		maxPushNum,
		totalNum,
		memo,
		templateID,
		backLogoURL,
		activityUrl,
		activityQrCode,
		effectivetime,
		expiretime,
		auditStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		isPrematureTerminated,
		terminatedReason,
		createTime,
		updateTime,
		operatorID
		FROM
		ecpm_t_activity
		WHERE
		ID = #{activityID} and
		auditStatus =2
		and effectivetime <![CDATA[ <= ]]>
		now()
		and expiretime <![CDATA[ > ]]>
		now()
	</select>

	<select id="getActivityInfo" resultMap="activityWrapper">
		select distinct t.ID,
		t.activityCode,
		t.activityName,
		t.activityRuleDesc,
		t.rewardExplain,
		t.rewardNotify,
		t.maxPushNum,
		t.totalNum,
		t.memo,
		t.templateID,
		t.backLogoURL,
		t.activityUrl,
		t.activityQrCode,
		t.effectivetime,
		t.expiretime,
		t.auditStatus,
		t.approveIdea,
		t.enterpriseID,
		t.enterpriseName,
		t.isPrematureTerminated,
		t.terminatedReason,
		t.createTime,
		t.updateTime,
		t.operatorID
		from ecpm_t_activity t
		<if test="areaCode != null  and areaCode !=''  ">
			, ecpm_t_activity_area a
		</if>
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityName != null  and activityName !=''  ">
				and lower(t.activityName) like #{activityName}
			</if>
			<if test="enterpriseID != null">
				and t.enterpriseID = #{enterpriseID}
			</if>
			<if test="authEnterpriseIDs !=null and authEnterpriseIDs.size>0">
				and t.enterpriseID in
				<foreach item="enterpriseID" index="index" collection="authEnterpriseIDs" open="(" separator="," close=")">
					#{enterpriseID}
				</foreach>
			</if>
			<if test="enterpriseName != null  and enterpriseName !=''  ">
				and t.enterpriseName like #{enterpriseName}
			</if>
			<if test="auditStatus != null">
				<if test="auditStatus == 3">
					and (t.auditStatus = #{auditStatus} or t.auditStatus =
					0)
				</if>
				<if test="auditStatus !=3">
					and t.auditStatus = #{auditStatus}
				</if>
			</if>
			<if test="auditStatus == 2 and processStatus == 1">
				and t.effectivetime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="auditStatus == 2 and processStatus == 2">
				and t.effectivetime <![CDATA[ < ]]>
				#{now}
				and t.expiretime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="auditStatus == 2 and processStatus == 3">
				and t.expiretime <![CDATA[ < ]]>
				#{now}
			</if>
			<if test="areaCode != null  and areaCode !=''  ">
				and (a.provinceID = #{areaCode} or a.cityID =
				#{areaCode})
			</if>
			<if test="areaCode != null  and areaCode !=''  ">
				and t.ID = a.activityID
			</if>
			<if test="id != null  and id !=''  ">
				and t.ID = #{id}
			</if>
			<if test="reserved3 != null  and reserved3 !=''  ">
				and t.reserved3 = #{reserved3}
			</if>
			and (isPrematureTerminated != 2 or isPrematureTerminated is null)
		</trim>
		order by ${sortField} ${sortType},t.ID ${sortType}
		limit #{pageNum},#{pageSize}
	</select>
	<select id="getActivityInfoCount" resultType="java.lang.Integer">
		select distinct count(1)
		from ecpm_t_activity t
		<if test="areaCode != null  and areaCode !=''  ">
			, ecpm_t_activity_area a
		</if>
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityName != null  and activityName !=''  ">
				and lower(t.activityName) like #{activityName}
			</if>
			<if test="enterpriseID != null">
				and t.enterpriseID = #{enterpriseID}
			</if>
			<if test="authEnterpriseIDs !=null and authEnterpriseIDs.size>0">
				and t.enterpriseID in
				<foreach item="enterpriseID" index="index" collection="authEnterpriseIDs" open="(" separator="," close=")">
					#{enterpriseID}
				</foreach>
			</if>
			<if test="enterpriseName != null  and enterpriseName !=''  ">
				and t.enterpriseName like #{enterpriseName}
			</if>
			<if test="auditStatus != null">
				<if test="auditStatus == 3">
					and (t.auditStatus = #{auditStatus} or t.auditStatus =
					0)
				</if>
				<if test="auditStatus !=3">
					and t.auditStatus = #{auditStatus}
				</if>
			</if>
			<if test="auditStatus == 2 and processStatus == 1">
				and t.effectivetime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="auditStatus == 2 and processStatus == 2">
				and t.effectivetime <![CDATA[ < ]]>
				#{now}
				and t.expiretime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="auditStatus == 2 and processStatus == 3">
				and t.expiretime <![CDATA[ < ]]>
				#{now}
			</if>
			<if test="areaCode != null  and areaCode !=''  ">
				and (a.provinceID = #{areaCode} or a.cityID =
				#{areaCode})
			</if>
			<if test="areaCode != null  and areaCode !=''  ">
				and t.ID = a.activityID
			</if>
			<if test="id != null  and id !=''  ">
				and t.ID = #{id}
			</if>
			<if test="reserved3 != null  and reserved3 !=''  ">
				and t.reserved3 = #{reserved3}
			</if>
		</trim>
	</select>

	<select id="getActivityInfoWithProcessStatus" resultMap="activityWrapper">
		select ID,
		activityCode,
		activityName,
		activityRuleDesc,
		rewardExplain,
		rewardNotify,
		maxPushNum,
		totalNum,
		memo,
		templateID,
		backLogoURL,
		activityUrl,
		activityQrCode,
		effectivetime,
		expiretime,
		auditStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		isPrematureTerminated,
		terminatedReason,
		createTime,
		updateTime,
		operatorID from ecpm_t_activity
		t
		<trim prefix="where" prefixOverrides="and|or">
			t.ID in
			<foreach item="activityID" index="index" collection="activityIDs"
				open="(" separator="," close=")">
				#{activityID}
			</foreach>
			<if test="activityName != null  and activityName !=''  ">
				and lower(t.activityName) like #{activityName}
			</if>
			<if test="processStatus == 1">
				and t.effectivetime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="processStatus == 2">
				and t.effectivetime <![CDATA[ < ]]>
				#{now}
				and t.expiretime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="processStatus == 3">
				and t.expiretime <![CDATA[ < ]]>
				#{now}
			</if>
		</trim>
		order by t.updateTime desc, t.ID desc
		limit #{pageNum},#{pageSize}
	</select>

	<select id="countActivityInfo" resultType="java.lang.Integer">
		select count(1)
		from ecpm_t_activity t
		<trim prefix="where" prefixOverrides="and|or">
			t.ID in
			<foreach item="activityID" index="index" collection="activityIDs"
				open="(" separator="," close=")">
				#{activityID}
			</foreach>
			<if test="activityName != null  and activityName !=''  ">
				and lower(t.activityName) like #{activityName}
			</if>
			<if test="processStatus == 1">
				and t.effectivetime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="processStatus == 2">
				and t.effectivetime <![CDATA[ < ]]>
				#{now}
				and t.expiretime <![CDATA[ > ]]>
				#{now}
			</if>
			<if test="processStatus == 3">
				and t.expiretime <![CDATA[ < ]]>
				#{now}
			</if>
		</trim>
	</select>

	<update id="updateActivityInfoTerminated">
		update ecpm_t_activity
		set
		isPrematureTerminated=#{isPrematureTerminated},
		updateTime=#{updateTime},terminatedReason=#{terminatedReason}
		where
		ID=#{id}
	</update>

	<!-- 查询有效的活动的数量 -->
	<select id="countEffectiveActivity" resultType="java.lang.Integer">
		select count(1)
		from ecpm_t_activity t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				and t.ID = #{activityID}
			</if>
			<if test="now != null">
				and t.effectivetime <![CDATA[ < ]]>
				#{now}
				and t.expiretime <![CDATA[ > ]]>
				#{now}
			</if>
			and t.auditStatus =2
		</trim>
	</select>
	<insert id="createActivity" keyProperty="id" useGeneratedKeys="true">
		insert into ecpm_t_activity
		(
		activityCode,
		activityName,
		activityRuleDesc,
		rewardExplain,
		rewardNotify,
		maxPushNum,
		totalNum,
		memo,
		templateID,
		backLogoURL,
		activityUrl,
		activityQrCode,
		effectivetime,
		expiretime,
		auditStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		isPrematureTerminated,
		terminatedReason,
		createTime,
		updateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		values
		(
		#{activityCode},
		#{activityName},
		#{activityRuleDesc},
		#{rewardExplain},
		#{rewardNotify},
		#{maxPushNum},
		#{totalNum},
		#{memo},
		#{templateID},
		#{backLogoURL},
		#{activityUrl},
		#{activityQrCode},
		#{effectiveTime},
		#{expireTime},
		#{auditStatus},
		#{approveIdea},
		#{enterpriseID},
		#{enterpriseName},
		#{isPrematureTerminated},
		#{terminatedReason},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10})
	</insert>

	<select id="queryActivityExist" resultMap="activityWrapper">
		select
		ID,
		activityCode,
		activityName,
		activityRuleDesc,
		rewardExplain,
		rewardNotify,
		maxPushNum,
		totalNum,
		memo,
		templateID,
		backLogoURL,
		activityUrl,
		activityQrCode,
		effectivetime,
		expiretime,
		auditStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		isPrematureTerminated,
		terminatedReason,
		createTime,
		updateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from ecpm_t_activity
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityName != null  and activityName !=''  ">
				activityName = #{activityName}
			</if>
			<if test="null != enterpriseID">
				and enterpriseID = #{enterpriseID}
			</if>
			<if test="null != activityID">
				and ID = #{activityID}
			</if>
			<if test="null != uuid and '' != uuid">
				and activityCode = #{uuid}
			</if>
		</trim>
	</select>
	<select id="getActivityInfoByID" resultMap="activityWrapper">
		select
		ID,
		activityCode,
		activityName,
		activityRuleDesc,
		rewardExplain,
		rewardNotify,
		maxPushNum,
		totalNum,
		memo,
		templateID,
		backLogoURL,
		activityUrl,
		activityQrCode,
		effectivetime,
		expiretime,
		auditStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		isPrematureTerminated,
		terminatedReason,
		createTime,
		updateTime,
		operatorID
		from ecpm_t_activity
		<trim prefix="where" prefixOverrides="and|or">
			<if test="id != null">
				id=#{id}
			</if>
		</trim>
	</select>

	<update id="updateActivityStatus">
		update ecpm_t_activity
		set auditStatus =
		#{status},updateTime = #{updateTime},operatorID =
		#{operatorID}
		,approveIdea = #{approveIdea}
		where ID = #{activityID}
	</update>

	<!-- 更新活动表内容，传啥更新啥 -->
	<update id="updateActivity">
		update ecpm_t_activity
		set
		<if test="null != activityCode and '' != activityCode">
			activityCode = #{activityCode},
		</if>
		<if test="null != activityName and '' != activityName">
			activityName = #{activityName},
		</if>
		<if test="null != rewardExplain and '' != rewardExplain">
			rewardExplain = #{rewardExplain},
		</if>
		<if test="null != activityRuleDesc and '' != activityRuleDesc">
			activityRuleDesc = #{activityRuleDesc},
		</if>
		<if test="null != rewardNotify and '' != rewardNotify">
			rewardNotify = #{rewardNotify},
		</if>
		<if test="null != rewardNotify and '' != rewardNotify">
			rewardNotify = #{rewardNotify},
		</if>
		<if test="null != maxPushNum">
			maxPushNum = #{maxPushNum},
		</if>
		<if test="null != totalNum">
			totalNum = #{totalNum},
		</if>
		<if test="null != memo and '' != memo">
			memo = #{memo},
		</if>
		<if test="null != templateID">
			templateID = #{templateID},
		</if>
		<if test="null != backLogoURL and '' != backLogoURL">
			backLogoURL = #{backLogoURL},
		</if>
		<if test="null != activityUrl and '' != activityUrl">
			activityUrl = #{activityUrl},
		</if>
		<if test="null != activityQrCode and '' != activityQrCode">
			activityQrCode = #{activityQrCode},
		</if>
		<if test="null != effectiveTime">
			effectivetime = #{effectiveTime},
		</if>
		<if test="null != expireTime">
			expiretime = #{expireTime},
		</if>
		<if test="null != auditStatus">
			auditStatus = #{auditStatus},
		</if>
		<if test="null != approveIdea and '' != approveIdea">
			approveIdea = #{approveIdea},
		</if>
		<if test="null != enterpriseID">
			enterpriseID = #{enterpriseID},
		</if>
		<if test="null != enterpriseName and '' != enterpriseName">
			enterpriseName = #{enterpriseName},
		</if>
		<if test="null != isPrematureTerminated">
			isPrematureTerminated = #{isPrematureTerminated},
		</if>
		<if test="null != terminatedReason and '' != terminatedReason">
			terminatedReason = #{terminatedReason},
		</if>
		<if test="null != updateTime">
			updateTime = #{updateTime},
		</if>
		<if test="null != operatorID">
			operatorID = #{operatorID},
		</if>
		<if test="null != reserved1 and '' != reserved1">
			reserved1 = #{reserved1},
		</if>
		<if test="null != reserved2 and '' != reserved2">
			reserved2 = #{reserved2},
		</if>
		<if test="null != reserved3 and '' != reserved3">
			reserved3 = #{reserved3},
		</if>
		<if test="null != reserved4 and '' != reserved4">
			reserved4 = #{reserved4},
		</if>
		<if test="null != reserved5 and '' != reserved5">
			reserved5 = #{reserved5},
		</if>
		<if test="null != reserved6 and '' != reserved6">
			reserved6 = #{reserved6},
		</if>
		<if test="null != reserved7 and '' != reserved7">
			reserved7 = #{reserved7},
		</if>
		<if test="null != reserved8 and '' != reserved8">
			reserved8 = #{reserved8},
		</if>
		<if test="null != reserved9 and '' != reserved9">
			reserved9 = #{reserved9},
		</if>
		<if test="null != reserved10 and '' != reserved10">
			reserved10 = #{reserved10},
		</if>
		<if test="null != extInfo and '' != extInfo">
			extInfo = #{extInfo}
		</if>
		where ID = #{id}
	</update>

	<update id="updateActivityTerminated">
		update ecpm_t_activity set
		isPrematureTerminated =
		#{isPrematureTerminated},
		terminatedReason = #{terminatedReason},
		updateTime = #{now},
		operatorID = #{operatorID}
		where enterpriseID =
		#{enterpriseID}
		and isPrematureTerminated = #{isPrematureTerminatedCon}
		and ID in
		(select activityID from (
		SELECT t.activityID
		FROM
		ecpm_t_activity_stat t, ecpm_t_activity t1
		WHERE t1.totalNum <![CDATA[ > ]]>
		(t.screenCount + t.endPhoneCount) AND t.activityID =
		t1.ID
		) AS temp)
	</update>

	<select id="queryInProcessActivity" resultMap="activityWrapper">
		select
		ID,
		activityCode,
		activityName,
		activityRuleDesc,
		rewardExplain,
		rewardNotify,
		maxPushNum,
		totalNum,
		memo,
		templateID,
		backLogoURL,
		activityUrl,
		activityQrCode,
		effectivetime,
		expiretime,
		auditStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		isPrematureTerminated,
		terminatedReason,
		createTime,
		updateTime,
		operatorID
		from ecpm_t_activity
		where
		auditStatus = #{auditStatus}
		and effectivetime <![CDATA[ <= ]]>
		#{today}
		and expiretime <![CDATA[ >= ]]>
		#{yesterday}
		order by id desc
		limit #{startNum},#{pageSize}
	</select>

	<select id="queryInProcessActivityAccount" resultType="java.lang.Integer">
		select count(1) from ecpm_t_activity where
		auditStatus = #{auditStatus}
		and
		effectivetime <![CDATA[ <= ]]>
		#{today}
		and expiretime <![CDATA[ >= ]]>
		#{yesterday}
	</select>

	<update id="updateEnterpriseName">
		update ecpm_t_activity t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>

	<update id="updateParentEnterpriseName">
		update ecpm_t_activity t
		set t.updateTime = #{now},
		t.reserved1 = #{enterpriseName}
		where
		t.reserved3 = #{enterpriseID}
	</update>


	<select id="selectActivityByContentIDList" resultMap="activityWrapper">
		select
		t.ID,
		t.activityCode,
		t.activityName,
		t.activityRuleDesc,
		t.rewardExplain,
		t.rewardNotify,
		t.maxPushNum,
		t.totalNum,
		t.memo,
		t.templateID,
		t.backLogoURL,
		t.activityUrl,
		t.activityQrCode,
		t.effectivetime,
		t.expiretime,
		t.auditStatus,
		t.approveIdea,
		t.enterpriseID,
		t.enterpriseName,
		t.isPrematureTerminated,
		t.terminatedReason,
		t.createTime,
		t.updateTime,
		t.operatorID
		from
		ecpm_t_activity t
		left join ecpm_t_activity_contentrel t1
		on t.ID = t1.activityID
		where t.auditStatus=2
		and t.effectivetime <![CDATA[ <= ]]>
			now()
		and t.expiretime <![CDATA[ > ]]>
			now()
		and t1.contentID in
		<foreach collection="list" item="contentId" index="index" open="(" close=")" separator=",">
			#{contentId}
		</foreach>

	</select>

	<select id="selectActivityByContentID" resultMap="activityWrapper">
		select
		t.ID,
		t.activityCode,
		t.activityName,
		t.activityRuleDesc,
		t.rewardExplain,
		t.rewardNotify,
		t.maxPushNum,
		t.totalNum,
		t.memo,
		t.templateID,
		t.backLogoURL,
		t.activityUrl,
		t.activityQrCode,
		t.effectivetime,
		t.expiretime,
		t.auditStatus,
		t.approveIdea,
		t.enterpriseID,
		t.enterpriseName,
		t.isPrematureTerminated,
		t.terminatedReason,
		t.createTime,
		t.updateTime,
		t.operatorID
		from
		ecpm_t_activity t
		left join ecpm_t_activity_contentrel t1
		on t.ID = t1.activityID
		where t1.contentID in
		<foreach collection="list" item="contentId" index="index" open="(" close=")" separator=",">
			#{contentId}
		</foreach>

	</select>

	<select id="queryOtherContentID" resultType="java.lang.Long">
		SELECT
			contentID 
		FROM
			ecpm_t_activity_contentrel 
		WHERE
			activityID IN ( SELECT activityID FROM ecpm_t_activity_contentrel WHERE contentID = #{contentID} ) 
			AND contentID <![CDATA[<>]]> #{contentID}
	</select>

	<update id="updateActivityByContentID">
		update 
			ecpm_t_activity
		set
			isPrematureTerminated = #{isPrematureTerminated},
			terminatedReason = #{terminatedReason},
			updateTime = #{updateTime}
		where
			ID in (select activityID from ecpm_t_activity_contentrel where contentID = #{contentID})
	</update>
	
</mapper>