<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>小微商户批设管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
	<link href="../../../../css/reset.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
	<!--分页-->
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
	<link rel="stylesheet" type="text/css" href="../../../../css/searchList.css"/>
	<link rel="stylesheet" type="text/css" href="../../../../css/font-awesome.min.css">
	<link rel="stylesheet" type="text/css" href="../../../../css/webuploader.css">	    
	<script type="text/javascript" src="../../../../frameworkJs/webuploader.js"></script>
	<script src="../../../../directives/cy-uploadify/cy-uploadify.js"></script>
	<link href="../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet" />
	<script src="../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<link href="../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
	<script type="text/javascript" src="xwBatchsubList.js"></script>

	<style>
		label {
			min-width: 120px;
		}

		.cond-div {
			min-width: 240px;
		}
		.border-red{
        border: 1px solid red;
    }
    .dialog-690{
        width: 696px;
    }
    .dialog-800{
        width: 800px;
    }
    .dialog-900{
        width: 900px;
    }
    .dialog-1000{
        width: 1000px;
    }
    .handle ul li icon.manage-icon {
        background-position: -126px 0;
    }
    .handle ul li icon.add-icon {
        background-position: -55px 0;
    }
    .handle ul li icon.import-icon {
        background-position: -90px 0;
    }
    .table th.adjustable-width{
        width: 25%;
    }
    #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }
    #filePicker_ div:nth-child(2) {
        width: 100% !important;
        height: 100% !important;
    }

    .form-group div li {
        display: inline-block;
        margin-right: 10px;
        padding-right: 10px;
        cursor: pointer;
    }

    .form-group div li span {
        vertical-align: middle;
        margin-right: 4px;
    }
        /* media for adjustable search-table width  */
    @media (max-width: 1850px) {
        .table th.adjustable-width {
            width: 28%;
        }
        .handle ul li {
            margin-right: 10px;
        }
    }
    @media (max-width: 1600px) {
        .table th.adjustable-width {
            width: 30%;
        }

        .handle ul li {
            margin-right: 10px;
        }
    }
	@media (max-width: 1300px){
        .table th.adjustable-width{
            width: 33%;
            }
        .handle ul li{
            margin-right: 10px;
        }
    }
    @media (max-width: 1100px){
        .table th.adjustable-width{
            width: 42%;
            }
        .handle ul li{
            margin-right: 10px;
        }
    }
	   .ng-dirty.ng-invalid {
            border-color: red;
        }
        .ng-dirty.invalid {
            border-color: red;
        }
        .modal-footer {
    		text-align: center;
		}
	</style>

</head>
<body ng-app="myApp" ng-controller="XwBatchsubListController" ng-init="init()" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'XW_BATCHSUB_MANAGER' | translate">小微商户批设管理</span>
		</div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="taskId" class="col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'XW_BATCHSUB_TASKID'|translate"></label>

					<div class="cond-div col-xs-2">
						<input type="text" autocomplete="off" class="form-control" id="taskId"
									 placeholder="{{'XW_BATCHSUB_COMMON_TASKID'|translate}}" ng-model="taskId">
					</div>

					<label for="dealStatus" class="col-xs-1 control-label" ng-bind="'XW_BATCHSUB_DEALSTATUS'|translate"></label>
					<div class="cond-div col-xs-2">
						<select class="form-control" ng-model="dealStatus" id="dealStatus"
										ng-options="x.id as x.name for x in dealStatusList">
							<option value="">不限</option>
						</select>
					</div>
					<div class="cond-div">
						<button type="submit" class="btn search-btn" ng-click="queryH5BatchSubTask()" style="margin-left: 20px">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
			
				</div>

			</form>
		</div>
		<div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="createBatchSubTaskModel()">
                <icon class="add-iocn"></icon>
                <span>新建任务</span>
            </button>
        </div>
		<div style="margin: 20px">
			<p style="font-size: 16px" ng-bind="'XW_BATCHSUB_LIST'|translate"></p>
		</div>
	</div>
	<div class="coorPeration-table">
		<table class="table table-striped table-hover">
			<thead>
			<tr>
				<th style="width:13%" >任务ID</th>
				<th style="width:12%" >任务时间</th>
				<th style="width:10%" >开通数量</th>
				<th style="width:15%" >开通成功</th>
				<th style="width:15%" >开通失败</th>
				<th style="width:15%" >处理状态</th>
				<th style="width:20%" ng-bind="'COMMON_OPERATE'|translate"></th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="item in h5BatchSubTaskData">
				<td><span title="{{item.id}}" ng-bind="item.id"></span></td>
				<td><span title="{{item.createTime|formatDate}}" ng-bind="item.createTime|formatDate"></span></td>
				<td><span title="{{item.memberCount}}" ng-bind="item.memberCount"></span></td>
				<td><span title="{{item.successCount}}" ng-bind="item.successCount"></span></td>
				<td><span title="{{item.faildCount}}" ng-bind="item.faildCount"></span></td>
				<td><span title="{{item.dealStatus|dealStatus}}" ng-bind="item.dealStatus|dealStatus"></span></td>
				<td class="coorPeration-table-a">
					<div class="handle">
						<ul>
							<li class="query" ng-click="toDetail(item)">
								<icon class="query-icon"></icon>
								<span>详情</span>
							</li>
						</ul>
					</div>
				</td>
			</tr>
			<tr ng-show="h5BatchSubTaskData===null||h5BatchSubTaskData.length===0">
				<td style="text-align:center" colspan="7">暂无数据</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div>
		<ptl-page tableId="0" change="queryH5BatchSubTask('justPage')"></ptl-page>
	</div>

	<!-- 导入批设内容 -->
	<div class="modal fade" id="createBatchSubTask" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
              <div class="modal-content">
                  <div class="modal-header">
                      <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                      <h4 class="modal-title">导入批设内容</h4>
                  </div>
                  <div class="cooper-tab">
                      <form class="form-horizontal" name="myForm" novalidate >
                      	<!-- 导入内容 -->
                      	<div class="form-group" style="margin-top: 20px;">
							<label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label" 
										 ng-bind="'COMMON_FILENAME'|translate" style="padding-top: 7px"></label>
							<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
								<input type="text" class="form-control" ng-model="fileName" id="addGroupName"
											 placeholder="请导入.xlsx表格格式文件" style="width: 100%;style="padding-top: 7px;"" ng-disabled="true"/>
								<!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
							</div>
							<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
																uploadifyid="uploadifyid" validate="isValidate" filesize="filesize"
																mimetypes="mimetypes"
																formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
																urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto"
																style="margin-left: 15px;float: left;">
							</cy:uploadifyfile>
						</div>
						<div class="form-group" style="margin-top: 20px;">
							<label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label"></label>
							<div class="downloadRow col-lg-4 col-xs-4 col-sm-4 col-md-4">
							<a target="_blank" href="/qycy/ecpmp/assets/xwBatchSubImportTemplate.xlsx" class="downMod"
								 style="margin-right: 40px;"
								 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
							</div>
							<div style="color:#ff0000;" ng-show="errorInfo!==''">
							<span class="uplodify-error-img"></span>
							<span ng-bind="errorInfo|translate"></span>
							</div>
						</div>
								
                          <!-- 营业执照 -->
                          <div class="form-group industry" style="margin-bottom: 0px;">
                              <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label" style="padding-top: 7px">
                                  <span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span></label>
                              <div class="col-lg-9 col-xs-9  col-sm-9 col-md-9">
                                  <cy:uploadify filelistid="fileList_" filepickerid="filePicker_" accepttype="accepttype_"
                                                uploadifyid="uploadifyid_2" validate="isValidate_" filesize="filesize_" mimetypes_="mimetypes_"
                                                formdata="uploadParam_"  uploadurl="uploadurl_" desc="uploadDesc_" numlimit="numlimit_"
                                                urllist="urlList_" createthumbnail="isCreateThumbnail_" namelistid="nameList_"
                                                ng-if="showUpload" >
                                  </cy:uploadify>
                                  <input class="form-control" name="businessLicenseURL_" ng-model="businessLicenseURL_" ng-hide="true">
                              </div>
                          </div>
                        <div class="form-group" ng-show="businessLicenseURL_!=''">
                            <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label"></label>
                            <div class="col-lg-9 col-xs-9  col-sm-9 col-md-9">
                            <button type="button" class="btn btn-primary search-btn" ng-click='showBusinessURL()'>预览</button>
                            </div>
                        </div>

                      </form>
                  </div>
                  <div class="modal-footer">
                      <button type="submit" ng-disabled="errorInfo!==''||fileUrl==''" class="btn btn-primary search-btn"  ng-click="importBatchSub()">提交</button>
                  	  <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="addCreateCancel">取消</button>
                  </div>
              </div>
          </div>
	</div>
	<div class="modal fade bs-example-modal-sm" id="showBusinessURL" tabindex="-1" style="overflow: auto;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" ng-click="hideBusinessURL()"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">预览</h4>
                </div>
                <div class="modal-body">
                    <div class="img-wrap" ng-repeat='item in urlList_2'>
                        <img ng-src="{{item}}" alt="">
                    </div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="button" class="btn "  ng-click="hideBusinessURL()" >确定</button>
                </div>
            </div>
        </div>
    </div>
	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
			 style="z-index:555555;">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838;text-align: center'>
							{{tip|translate}}
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align: center">
					<button type="submit" class="btn btn-primary" data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
</body>
</html>