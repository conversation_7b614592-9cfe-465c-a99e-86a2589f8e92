<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberSubFaildMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubFaildWrapper" >
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="memberName" property="memberName" jdbcType="VARCHAR" />
        <result column="msisdn" property="msisdn" jdbcType="VARCHAR" />
        <result column="orgID" property="orgID" jdbcType="INTEGER" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="errCode" property="errCode" jdbcType="VARCHAR" />
        <result column="errDesc" property="errDesc" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, memberName, msisdn, orgID, createTime, errCode, errDesc
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_member_sub_faild
        where ID = #{id,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
        delete from ecpm_t_member_sub_faild
        where ID = #{id,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubFaildWrapper" >
        insert into ecpm_t_member_sub_faild (ID, memberName, msisdn, 
            orgID, createTime, errCode, 
            errDesc,batchNo)
        values (#{id,jdbcType=VARCHAR}, #{memberName,jdbcType=VARCHAR}, #{msisdn,jdbcType=VARCHAR}, 
            #{orgID,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{errCode,jdbcType=VARCHAR}, 
            #{errDesc,jdbcType=VARCHAR},#{batchNo})
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubFaildWrapper" >
        insert into ecpm_t_member_sub_faild
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="ID != null" >
                ID,
            </if>
            <if test="memberName != null" >
                memberName,
            </if>
            <if test="msisdn != null" >
                msisdn,
            </if>
            <if test="orgID != null" >
                orgID,
            </if>
            <if test="createTime != null" >
                createTime,
            </if>
            <if test="errCode != null" >
                errCode,
            </if>
            <if test="errDesc != null" >
                errDesc,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="ID != null" >
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="memberName != null" >
                #{memberName,jdbcType=VARCHAR},
            </if>
            <if test="msisdn != null" >
                #{msisdn,jdbcType=VARCHAR},
            </if>
            <if test="orgID != null" >
                #{orgID,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="errCode != null" >
                #{errCode,jdbcType=VARCHAR},
            </if>
            <if test="errDesc != null" >
                #{errDesc,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubFaildWrapper" >
        update ecpm_t_member_sub_faild
        <set >
            <if test="memberName != null" >
                memberName = #{memberName,jdbcType=VARCHAR},
            </if>
            <if test="msisdn != null" >
                msisdn = #{msisdn,jdbcType=VARCHAR},
            </if>
            <if test="orgID != null" >
                orgID = #{orgID,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="errCode != null" >
                errCode = #{errCode,jdbcType=VARCHAR},
            </if>
            <if test="errDesc != null" >
                errDesc = #{errDesc,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubFaildWrapper" >
        update ecpm_t_member_sub_faild
        set memberName = #{memberName,jdbcType=VARCHAR},
            msisdn = #{msisdn,jdbcType=VARCHAR},
            orgID = #{orgID,jdbcType=INTEGER},
            createTime = #{createTime,jdbcType=TIMESTAMP},
            errCode = #{errCode,jdbcType=VARCHAR},
            errDesc = #{errDesc,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=VARCHAR}
    </update>

    <delete id="deleteMemberSubFaild" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubFaildWrapper" >
        delete from ecpm_t_member_sub_faild
        where msisdn = #{msisdn,jdbcType=VARCHAR}
            and orgID = #{orgID,jdbcType=INTEGER}
    </delete>

    <select id="queryByOrgID" resultMap="BaseResultMap" parameterType="java.util.Map" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_member_sub_faild
        where orgID = #{orgID}
		limit #{startIndex}, #{pageSize}
    </select>

    <select id="queryByOrgIDForCount" resultType="java.lang.Integer" parameterType="java.util.Map" >
        select 
        	count(0)
        from 
        	ecpm_t_member_sub_faild
        where 
        	orgID = #{orgID}
    </select>
</mapper>