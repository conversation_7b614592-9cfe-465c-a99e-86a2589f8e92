spring:
  application:
    name: cy-eureka
server:
  port: 19008
#eureka
eureka: 
  instance: 
    hostname: mg-eureka
    metadata-map:
      cluster: main
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  client: 
#  集群配置
    use-dns-for-fetching-service-urls: false
    prefer-same-zone-eureka: true
    fetch-registry: true
    register-with-eureka: true
    filter-only-up-instances: true
    region: region1
    availability-zones: 
      region1: mg-eureka
    service-url: 
      mg-eureka: http://127.0.0.1:19008/eureka/,http://127.0.0.1:19009/eureka/,http://127.0.0.1:19010/eureka/
  server:
    enable-self-preservation: false
    eviction-interval-timer-in-ms: 30000
endpoints:
  shutdown:
    enabled: true
    sensitive: false
