.table-tab{
    float: right;
    margin: 0 20px;
}
.table-tab nav ul.pagination{
    float: left;
}
.table-tab .pageChoose{
    float: left;
    margin: 20px 20px 20px 0;
    max-width: 65px;
}
.table-tab .toPage{
    float: left;
    margin: 20px 0;
}
.page-span{
    float: left;
    height: 34px;
    line-height: 34px;
}
.pagination>.active>a{
    background-image: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
    border: 1px solid #705de1;
}
.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
    border: none;
    padding: 12px 8px;
}
.table-striped>tbody>tr:nth-child(odd){
    background-color:#f2f2f2;
}
.pagination>li>a, .pagination>li>span{
    color: #777;
}
.tabtn{
display: inline-block;
border-radius: 0.5rem;
background-color: #FFFFFF;
font-size: 16px;
height: 44px;
line-height: 44px;
color: #c3c3c3;
padding: 0 40px;
}
.cur-tabtn{
color: #705de1;
box-shadow: 0 1px 6px #e5e5e5;
}
.pagination-first-a:hover{
    background: #fff!important;
}
.no_click a{
    cursor: default;
    background: #f2f2f2!important;
}
.no_click a:hover{
    cursor: default;
    /* background: #fff!important; */
}
.li-gotoPage a{
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #383838 !important;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius:4px; 
}
.pre-page a{
    background-color: #fff!important;
}
.next-page a{
    background-color: #fff!important;
}