<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.HotlineMapper">

	<resultMap type="com.huawei.jaguar.dsdp.ecpm.dao.domain.HotlineWrapper"
		id="hotlineMapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="hotlineNo" column="hotlineNo" javaType="java.lang.String" />
		<result property="hotlinePicUrl" column="hotlinePicUrl"
			javaType="java.lang.String" />
		<result property="isUse" column="isUse" javaType="java.lang.Integer" />
		<result property="approveStatus" column="approveStatus"
			javaType="java.lang.Integer" />
		<result property="approveIdea" column="approveIdea" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
	</resultMap>

	<sql id="hotlineColumn">
		enterpriseID,
		hotlineNo,
		hotlinePicUrl,
		isUse,
		approveStatus,
		approveIdea,
		createTime,
		updateTime,
		operatorID,
		queryHotlineNo
	</sql>
	<sql id="hotlineFullColumn">
		id,
		enterpriseID,
		hotlineNo,
		hotlinePicUrl,
		isUse,
		approveStatus,
		approveIdea,
		createTime,
		updateTime,
		operatorID
	</sql>


	<insert id="batchInsertHotline">
		INSERT INTO ecpm_t_hotline
		(
		<include refid="hotlineColumn" />
		)
		VALUES
		<foreach collection="list" item="hotlineMapper" separator=",">
			(
			#{hotlineMapper.enterpriseID},
			#{hotlineMapper.hotlineNo},
			#{hotlineMapper.hotlinePicUrl},
			#{hotlineMapper.isUse},
			#{hotlineMapper.approveStatus},
			#{hotlineMapper.approveIdea},
			#{hotlineMapper.createTime},
			#{hotlineMapper.updateTime},
			#{hotlineMapper.operatorID},
			CONCAT("hotlineNo", #{hotlineMapper.hotlineNo})
			)
		</foreach>
	</insert>

	<update id="updateHotline">
		UPDATE ecpm_t_hotline SET
		<trim suffixOverrides="," suffix="where id = #{id}">
			<if test="hotlineNo!=null and hotlineNo!=''">hotlineNo= #{hotlineNo}, queryHotlineNo=CONCAT("hotlineNo", #{hotlineNo}),</if>
			hotlinePicUrl= #{hotlinePicUrl},
			<if test="updateTime!=null">updateTime= #{updateTime},</if>
			<if test="operatorID!=null">operatorID= #{operatorID},</if>
		</trim>
	</update>

	<delete id="batchDeleteHotlineByID" parameterType="java.util.List">
		DELETE FROM ecpm_t_hotline WHERE id IN
		<if test="list !=null and list.size() > 0">
			<foreach item="id" index="index" collection="list" open="("
				separator="," close=")">
				#{id}
			</foreach>
		</if>
	</delete>

	<delete id="batchDeleteHotlineByNo" parameterType="java.util.List">
		DELETE FROM ecpm_t_hotline WHERE hotlineNo IN
		<foreach item="hotlineNo" index="index" collection="list"
			open="(" separator="," close=")">
			#{hotlineNo}
		</foreach>
	</delete>


	<select id="checkHotlineByNo" resultType="java.lang.Integer">
		SELECT count(1) FROM
		ecpm_t_hotline p WHERE p.hotlineNo = #{hotlineNo} limit 1
	</select>


	<select id="checkHotlineByID" parameterType="java.lang.Integer">
		SELECT 1 FROM
		ecpm_t_hotline p WHERE p.id = #{id} limit 1
	</select>


	<select id="queryHotlineByNo" resultMap="hotlineMapper">
		SELECT
		<include refid="hotlineColumn" />
		FROM ecpm_t_hotline p WHERE p.hotlineNo = #{hotlineNo};
	</select>

	<select id="queryHotline" resultMap="hotlineMapper">
		SELECT
		<include refid="hotlineColumn" />
		FROM ecpm_t_hotline p WHERE p.id = #{id};
	</select>


	<select id="batchQueryHotlineByNo" resultType="java.lang.String">
		SELECT
		hotlineNo
		FROM ecpm_t_hotline WHERE
		<if test="list !=null and list.size() > 0">
			hotlineNo in
			<foreach collection="list" item="hotlineNo" open="("
				separator="," close=")">
				#{hotlineNo}
			</foreach>
		</if>
	</select>

	<select id="batchQueryHotlineByID" resultType="java.lang.Integer">
		SELECT id
		FROM ecpm_t_hotline WHERE
		<if test="list !=null and list.size() > 0">
			id in
			<foreach collection="list" item="id" open="(" separator=","
				close=")">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="batchQueryHotline" resultMap="hotlineMapper">
		SELECT
		<include refid="hotlineFullColumn" />
		FROM ecpm_t_hotline where
		hotlineNo in
		<foreach item="hotlineNo" index="index" collection="hotlineNos"
			open="(" separator="," close=")">
			#{hotlineNo}
		</foreach>
	</select>

	<select id="queryHotlineByCreateTime" resultMap="hotlineMapper">
		SELECT
		hl.hotlineNo,
		es.enterpriseName as hotlinePicUrl,
		es.reserved5 as operatorID,
		hl.createTime
		FROM
		ecpm_t_hotline hl
		LEFT JOIN ecpm_t_enterprise_simple es ON hl.enterpriseID = es.ID
		WHERE
		UNIX_TIMESTAMP(hl.createTime) &gt;= UNIX_TIMESTAMP(#{startTime})
		AND UNIX_TIMESTAMP(hl.createTime) &lt;= UNIX_TIMESTAMP(#{endTime});
	</select>


	<select id="queryHotlineList" resultMap="hotlineMapper">
		SELECT
		<include refid="hotlineFullColumn" />
		FROM ecpm_t_hotline e
		WHERE e.id IN (SELECT id FROM (SELECT id FROM ecpm_t_hotline
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID!=null and enterpriseID!='' ">and enterpriseID= #{enterpriseID}</if>
			<if test="hotlineNo!=null and hotlineNo!=''">and hotlineNo like "%"#{hotlineNo}"%"</if>
			<if test="hotlineNoList !=null and hotlineNoList.size() > 0">
				and hotlineNo in
				<foreach collection="hotlineNoList" item="hotlineNo" open="("
					separator="," close=")">
					#{hotlineNo}
				</foreach>
			</if>
		</trim>
		ORDER BY updateTime DESC, id DESC
		limit #{pageNo},#{pageSize}) s) ORDER BY e.updateTime DESC, e.id DESC;
	</select>


	<select id="countHotlineList" resultType="java.lang.Integer">
		SELECT
		count(*)
		FROM ecpm_t_hotline
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID!=null and enterpriseID!=''">and enterpriseID= #{enterpriseID}</if>
			<if test="hotlineNo!=null and hotlineNo!=''">and hotlineNo like "%"#{hotlineNo}"%"</if>
			<if test="hotlineNoList !=null and hotlineNoList.size() > 0">
				and hotlineNo in
				<foreach collection="hotlineNoList" item="hotlineNo" open="("
					separator="," close=")">
					#{hotlineNo}
				</foreach>
			</if>
		</trim>
	</select>

	<select id="queryHotlineList2" resultMap="hotlineMapper">
		SELECT
		<include refid="hotlineFullColumn" />
		FROM ecpm_t_hotline e 
		FORCE INDEX(idx_hotline_queryHotlineNo_enterpriseID)
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID!=null and enterpriseID!='' ">and enterpriseID= #{enterpriseID}</if>
			<if test="hotlineNo!=null and hotlineNo!=''">and queryHotlineNo like "hotlineNo%"#{hotlineNo}"%"</if>
			<if test="hotlineNoList !=null and hotlineNoList.size() > 0">
				and hotlineNo in
				<foreach collection="hotlineNoList" item="hotlineNo" open="("
					separator="," close=")">
					#{hotlineNo}
				</foreach>
			</if>
		</trim>
		ORDER BY e.updateTime DESC, e.id DESC
		limit #{pageNo},#{pageSize}
	</select>


	<select id="countHotlineList2" resultType="java.lang.Integer">
		SELECT
		count(*)
		FROM ecpm_t_hotline 
		FORCE INDEX(idx_hotline_queryHotlineNo_enterpriseID)
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID!=null and enterpriseID!=''">and enterpriseID= #{enterpriseID}</if>
			<if test="hotlineNo!=null and hotlineNo!=''">and queryHotlineNo like "hotlineNo%"#{hotlineNo}"%"</if>
			<if test="hotlineNoList !=null and hotlineNoList.size() > 0">
				and hotlineNo in
				<foreach collection="hotlineNoList" item="hotlineNo" open="("
					separator="," close=")">
					#{hotlineNo}
				</foreach>
			</if>
		</trim>
	</select>
	<select id="checkHotlineByNoAndEnterpriseId" resultType="java.lang.Integer">
		SELECT count(1) FROM
		ecpm_t_hotline p WHERE p.hotlineNo = #{no} AND p.enterpriseID =#{enterpriseId} limit 1
	</select>

	<select id="batchQueryHotlineByEnterpriseIDAndNo"  resultMap="hotlineMapper">
		SELECT
		<include refid="hotlineFullColumn" />
		FROM ecpm_t_hotline p where  p.hotlineNo = #{No} AND p.enterpriseID =#{EnterpriseID}
	</select>

</mapper>