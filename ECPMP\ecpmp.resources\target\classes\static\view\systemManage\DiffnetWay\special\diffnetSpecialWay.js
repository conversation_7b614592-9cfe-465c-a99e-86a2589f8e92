var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n" ,"cy.uploadify", "cy.uploadifyfile", "service.common"])
app.controller('specialController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        $scope.accountID = $.cookie('accountID') || '1000';
        $scope.isSuperManager = false;
        $scope.isAgent = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }];


        $scope.wayTypeMap = {
            "2": "东盟",
            "5": "号百",
            "6": "联通在线",
            "8": "彩讯"
        }
        $scope.unionArr = ['0','0', '0','0'];
        $scope.telecomArr = ['0', '0','0'];

        $scope.errorInfo = "";
        $scope.fileUrl = "";
        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "必填，仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
            enterpriseId: 'diffnetWayType',
            fileUse: 'batchAddDiffnetWayType'
        };
        $scope.addExport = false;
        $scope.addExportParamsJson = "";
        $scope.contentListData = [];
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        //初始化搜索条件
        $scope.initSel = {
            contentNo: "",
            enterpriseID: "",
            enterpriseName: "",
            startTime: "",
            endTime: "",
            search: false,
        };
        $scope.queryDiffnetDeliveryWayContent();
    };
    $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
        if (broadData.file !== "") {
            $scope.fileName = broadData.file.name;
        } else {
            $scope.fileName = "";
        }
        $scope.uploader = broadData.uploader;
        $scope.errorInfo = broadData.errorInfo;
        $scope.fileUrl = fileUrl;
    });
    
    $('.input-daterange').datepicker({
  	  format: "yyyy-mm-dd",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true
    });
    
    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });
    
    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        $scope.initSel.startTime = document.getElementById("start").value;
        $scope.initSel.endTime = document.getElementById("end").value;

        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }
    }
    
    $scope.gotoAdd = function () {

        $scope.operateTitle = "新建";
        $scope.operType = 0;
        $scope.diffnetDeliveryWayContent = null;
        $scope.unionArr = ['0','0', '0','0'];
        $scope.telecomArr = ['0', '0','0'];
        $('.union .check-li').find('span').removeClass('checked');
        $('.telecom .check-li').find('span').removeClass('checked');

        $('#addDiffnetContent').modal();
    }

    $scope.gotoModify = function (item) {

        $scope.operateTitle = "编辑";
        $scope.operType = 1;
        $scope.diffnetDeliveryWayContent = {};
        $scope.unionArr = ['0', '0', '0','0'];
        $scope.telecomArr = ['0', '0','0'];
        $('.union .check-li').find('span').removeClass('checked');
        $('.telecom .check-li').find('span').removeClass('checked');

        $scope.diffnetDeliveryWayContent.contentID = item.contentID;
        var unionWayType = item.unicomWayType;
        if (unionWayType == 2) {
            $scope.changeUnionPlatform(0);
        } else if (unionWayType == 5) {
            $scope.changeUnionPlatform(1);
        }
        else if (unionWayType == 6) {
            $scope.changeUnionPlatform(2);
        }else if(unionWayType == 8) {
            $scope.changeUnionPlatform(3);
        }
        var telcomWayType = item.telcomWayType;
        if (telcomWayType == 2) {
            $scope.changeTelecomPlatform(0);
        } else if (telcomWayType == 5) {
            $scope.changeTelecomPlatform(1);
        }else if(telcomWayType == 8) {
            $scope.changeTelecomPlatform(2);
        }


        $('#addDiffnetContent').modal();
    }
    $scope.gotoDelete = function (item) {
        $scope.operType = 2;
        if (item) {
            $scope.diffnetDeliveryWayContent = item;
        } else {
            $scope.diffnetDeliveryWayContent = null;
        }
        $('#deleteDiffWay').modal();
    }

    $scope.canclePop = function (event) {
        event.preventDefault()
    }

    $scope.changeUnionPlatform = function (val) {
        if ($scope.unionArr[val] == 1) {

            $('.union .check-li').eq(val).find('span').removeClass('checked');
            $scope.unionArr[val] = 0;
        } else {
            $('.union .check-li').find('span').removeClass('checked');
            $('.union .check-li').eq(val).find('span').addClass('checked');

            for (var i = 0; i < $scope.unionArr.length; i++) {
                if (i == val) {
                    $scope.unionArr[i] = 1;
                } else {
                    $scope.unionArr[i] = 0;
                }
            }
        }
    };
    $scope.changeTelecomPlatform = function (val) {
        if ($scope.telecomArr[val] == 1) {

            $('.telecom .check-li').eq(val).find('span').removeClass('checked');
            $scope.telecomArr[val] = 0;
        } else {
            $('.telecom .check-li').find('span').removeClass('checked');
            $('.telecom .check-li').eq(val).find('span').addClass('checked');

            for (var i = 0; i < $scope.telecomArr.length; i++) {
                if (i == val) {
                    $scope.telecomArr[i] = 1;
                } else {
                    $scope.telecomArr[i] = 0;
                }
            }
        }
    };


    $scope.importBatch = function () {
        var excelfileUrl = $scope.fileUrl;
        var req = {
            "excelfileUrl": excelfileUrl
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/importDiffnetWayType",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1010100000') {
                        $('#importPop').modal("hide");
                        $scope.tip = "导入成功！";
                        $('#myModal').modal();
                        $scope.queryDiffnetDeliveryWayContent();
                    } else {
                    	$('#importPop').modal("hide");
                        $scope.tip = "本次导入信息中存在" + data.failCount + "条错误信息无法正常导入！";
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '导入失败！';
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.save = function () {
        if ($scope.operType != 2) {
            var diffnetDeliveryWayList = [];
            var orgPlatforms = [3, 2];
            var unionWayType;
            for (var i = 0; i < $scope.unionArr.length; i++) {
                if (i == 0 && $scope.unionArr[i] == 1) {
                    unionWayType = 2;
                } else if (i == 1 && $scope.unionArr[i] == 1) {
                    unionWayType = 5;
                } else if (i == 2 && $scope.unionArr[i] == 1) {
                    unionWayType = 6;
                }else if (i == 3 && $scope.unionArr[i] == 1) {
                    unionWayType = 8;
                }
            }

            var telecomWayType;
            for (var i = 0; i < $scope.telecomArr.length; i++) {
                if (i == 0 && $scope.telecomArr[i] == 1) {
                    telecomWayType = 2;
                } else if (i == 1 && $scope.telecomArr[i] == 1) {
                    telecomWayType = 5;
                } else if (i == 2 && $scope.telecomArr[i] == 1) {
                    telecomWayType = 8;
                }
            }
            if(unionWayType !== undefined){
                $scope.diffnetDeliveryWayContent.unicomWayType = unionWayType;
            }

            if(telecomWayType !== undefined){
                $scope.diffnetDeliveryWayContent.telcomWayType = telecomWayType;
            }
        }

        var contentList = [];
        if ($scope.diffnetDeliveryWayContent) {
            contentList.push($scope.diffnetDeliveryWayContent);
        } else {
            if ($scope.operType == 2) {
                contentList = $scope.selectedListTemp;
            }
        }
        if (!contentList || contentList.length == 0) {
            $('#deleteDiffWayCancel').click();
            $('#addDiffnetContent').modal("hide");

            $scope.tip = "请选择模板记录";
            $('#myModal').modal();
            return;
        }
        var req = {
            "operType": $scope.operType,
            "contentList": contentList
        };
        if($scope.operType == 0) {
        	$scope.addExportParamsJson = JSON.stringify(req);
        }
        console.log(req);
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/setDiffnetDeliveryWayContent",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1010100000') {
                        $scope.queryDiffnetDeliveryWayContent();
                        if ($scope.operType == 0) {
                            //$scope.tip = "新增成功！";
                        	$scope.exportFile2();
                        } else if ($scope.operType == 1) {
                            $scope.tip = "编辑成功！";
                        } else if ($scope.operType == 2) {
                            $scope.tip = "删除成功！"
                        }
                        $('#addDiffnetContent').modal("hide");
                        $('#deleteDiffWayCancel').click();
                        if ($scope.operType != 0) {
                        	$('#myModal').modal();
                        }
                    } else {
                        if ($scope.operType == 0) {
                            $scope.tip = "本次新增模板操作失败，请稍后再试！";
                        } else if ($scope.operType == 1) {
                            $scope.tip = "本次编辑模板操作失败，请稍后再试！";
                        } else if ($scope.operType == 2) {
                            $scope.tip = "本次删除失败，请稍后再试！";
                        }
                        $('#addDiffnetContent').modal("hide");
                        $('#deleteDiffWayCancel').click();

                        $('#myModal').modal();

                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $('#addDiffnetContent').modal("hide");
                        $('#deleteDiffWayCancel').click();

                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });
    }

    //更改全选框
    $scope.ifSelected = function () {
        $scope.allChoose = !$scope.allChoose;
        angular.forEach($scope.selectedListTemp, function (itemTemp) {
            $scope.selectedList.splice($.inArray(itemTemp.contentID, $scope.selectedList), 1);

        });
        if ($scope.allChoose) {
            $scope.selectedListTemp = [];
            angular.forEach($scope.contentListData, function (item) {
                item.checked = true;
                $scope.selectedList.push(item);
                $scope.selectedListTemp.push(item);
            })
        } else {
            angular.forEach($scope.contentListData, function (item) {
                item.checked = false;
                $scope.selectedListTemp = [];
            })

        }
    };


    //改变选择框
    $scope.changeSelected = function (item) {
        if ($.inArray(item, $scope.selectedListTemp) == -1) {
            $scope.selectedListTemp.push(item);
            $scope.selectedList.push(item);
        } else {
            $scope.selectedListTemp.splice($.inArray(item, $scope.selectedListTemp), 1);
            $scope.selectedList.splice($.inArray(item, $scope.selectedList), 1);
        }
        if ($scope.selectedListTemp.length == $scope.contentListData.length) {
            $scope.allChoose = true;
        } else {
            $scope.allChoose = false;
        }
    };

    // 导入
    $scope.importDiffWayType = function () {
        $('#importPop').modal();
        $('#importPop').on('hidden.bs.modal', function () {
            $rootScope.$apply(function () {
                $("#filePicker").find("span").text("导入文件");
                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.errorInfo = "";
                $scope.fileName = "";
                $scope.fileUrl = "";
            })
        });
    };
    /*
    * 导出文件弹窗
    */
    $scope.exportFile = function () {
        $scope.fileNameSpan = "异网投递通道指定内容导入失败清单";
        $scope.remarks = "";
        $scope.isGreaterFileName = false;
        $scope.isEmptyFileName = false;
        $scope.isSpecialCharacters = false;
        $scope.isFileNamePass = true;
        $scope.addExport = false;
        $scope.addExportParamsJson = "";
        $("#exportFile").modal("show");
    };
    /*
     * 保存后导出文件弹窗
     */
     $scope.exportFile2 = function () {
         $scope.fileNameSpan = "特殊通道内容";
         $scope.remarks = "";
         $scope.isGreaterFileName = false;
         $scope.isEmptyFileName = false;
         $scope.isSpecialCharacters = false;
         $scope.isFileNamePass = true;
         $scope.addExport = true;
         $("#exportFile").modal("show");
     };
    /*
       * 文件名校验
       */
    $scope.checkEmpty = function(){
        $scope.isFileNamePass = true;
        $scope.isGreaterFileName = false;


        var reg = /^[a-z0-9\u4e00-\u9fa5]+$/i
        // if(!$scope.remarks){
        //     $scope.isGreaterFileName = false;
        //     $scope.isEmptyFileName = true;
        //     $scope.isFileNamePass = false;
        //     $scope.isSpecialCharacters = false
        // }else if(!reg.test($scope.remarks)){
        //     $scope.isGreaterFileName = false;
        //     $scope.isEmptyFileName = false;
        //     $scope.isFileNamePass = false;
        //     $scope.isSpecialCharacters = true
        // }else

        if($scope.remarks.length > 255){
            $scope.isEmptyFileName = false;
            $scope.isGreaterFileName = true;
            $scope.isFileNamePass = false;
            $scope.isSpecialCharacters = false
        }
        //     else {
        //     $scope.isEmptyFileName = false;
        //     $scope.isGreaterFileName = false;
        //     $scope.isSpecialCharacters = false
        //     $scope.isFileNamePass = true;
        // }
    };
    $scope.JSON = {
        "dataType": "1"
    };
    /*
       * 提交导出文件信息
       */
    $scope.submitExportTask = function(){
        $("#exportFile").modal("hide");
        var createExportTaskInfoRequest = {};
        if($scope.addExport) {
        	createExportTaskInfoRequest = {
    			"exportTaskInfo": {
                    "fileName":null,
                    "remarks":$scope.remarks,
                    "taskType": 13,
                    "taskStatus": 0,
                    "operatorID": $scope.accountID,
                    "paramsJson": $scope.addExportParamsJson
                }	
        	}
        } else {
        	var paramsJson = JSON.stringify($scope.JSON);
            createExportTaskInfoRequest = {
                "exportTaskInfo": {
                    "fileName":null,
                    "remarks":$scope.remarks,
                    "taskType": 10,
                    "taskStatus": 0,
                    "operatorID": $scope.accountID,
                    "paramsJson": paramsJson
                }
            }
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/exportTaskService/createExportTask",
            data: JSON.stringify(createExportTaskInfoRequest),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        if(data.desc){
                            $scope.tip = data.desc;
                            $('#myModal').modal();
                        }

                    }else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error:function(){
                $rootScope.$apply(function(){
                        $scope.tip='**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };


    $scope.exportContentFile = function () {

        var req = {
            "param":{"req":JSON.stringify($scope.reqTemp)},
            "url":"/qycy/ecpmp/ecpmpServices/contentService/downdiffnetInfoCsvFileService",
            "method":"get"
        }
        if($scope.reqTemp != undefined)
        {
            CommonUtils.exportFile(req);
        }
    }

    $scope.queryDiffnetDeliveryWayContent = function (condition) {
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        if (condition != 'justPage') {
            if( (!($scope.initSel.enterpriseID != null && $scope.initSel.enterpriseID != ''
                && Number.isNaN(+$scope.initSel.enterpriseID)) && parseInt($scope.initSel.enterpriseID) > 2147483647) ||
                ($scope.initSel.enterpriseID != null && $scope.initSel.enterpriseID != ''
                && Number.isNaN(+$scope.initSel.enterpriseID))){
                $scope.tip = '企业编号输入值非法';
                $('#myModal').modal();
                return;
            }
            var req = {
                "contentID": $scope.initSel.contentNo || '',
                "enterpriseID": $scope.initSel.enterpriseID || '',
                "enterpriseName": $scope.initSel.enterpriseName || '',
                "createBeginTime": $scope.initSel.startTime || '',
                "createEndTime": $scope.initSel.endTime || '',
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryDiffnetDeliveryWayContent",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                	$scope.allChoose = false;

                    var data = result.result;
                    if (data.resultCode == '1010100000') {
                        $scope.contentListData = result.contentList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.contentListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultDesc;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });

    }
}])

app.filter("formatDate", function () {
	  return function (date) {
		    if (date) {
		      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12) + ":" + date.substring(12, 14);
		    }
		    return "";
		  }
});



app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])