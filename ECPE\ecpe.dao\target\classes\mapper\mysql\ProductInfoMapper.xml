<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ProductInfoMapper">

	<resultMap id="productInfoModel" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ProductInfoWrapper">
		<result property="productID" column="ID" />
		<result property="productCode" column="productCode" />
		<result property="productName" column="productName" />
		<result property="productDesc" column="productDesc" />
		<result property="productType" column="productType" />
		<result property="unitPrice" column="unitPrice" />
		<result property="currency" column="currency" />
		<result property="productStatus" column="productStatus" />
		<result property="isExperience" column="isExperience" />
		<result property="servType" column="servType" />
		<result property="subServType" column="subServType" />
		<result property="isLimit" column="isLimit" />
		<result property="chargeType" column="chargeType" />
		<result property="amount" column="amount" />
		<result property="memberCount" column="memberCount" />
		<result property="maxAmountPerPerson" column="maxAmountPerPerson" />
		<result property="effictiveTime" column="effictiveTime" />
		<result property="expireTime" column="expireTime" />
		<result property="displayNo" column="displayNo" />
		<result property="isUse" column="isUse" />
		<result property="createTime" column="createTime" />
		<result property="operatorID" column="operatorID" />
		<result property="lastUpdateTime" column="lastUpdateTime" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>
    
    <select id="queryProductInfo" resultMap="productInfoModel">
   	 	 SELECT ID, productCode, productName, productDesc,productType, unitPrice, currency, productStatus, isExperience, 
		    	servType, subServType, isLimit, chargeType, amount, memberCount, maxAmountPerPerson, effictiveTime, 
		    	expireTime, displayNo,isUse,createTime, operatorID, lastUpdateTime,
		    	reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, reserved10
		   FROM ecpe_t_product where 1=1

		<if test="productID!=null">
			and ID = #{productID}
		</if>
		<if test="productCode!=null  and productCode!=''">
			and productCode = #{productCode}
		</if>
		<if test="productName!=null  and productName!=''">
			and productName like #{productName}
		</if>
		<if test="productDesc!=null  and productDesc!=''">
			and productDesc = #{productDesc}
		</if>
		<if test="productType!=null">
			and productType = #{productType}
		</if>
		<if test="unitPrice!=null  and unitPrice!=''">
			and unitPrice = #{unitPrice}
		</if>
		<if test="currency!=null  and currency!=''">
			and currency = #{currency}
		</if>
		<if test="productStatus!=null">
			and productStatus = #{productStatus}
		</if>
		<if test="isExperience!=null">
			and isExperience = #{isExperience}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
		<if test="subServType!=null">
			and subServType = #{subServType}
		</if>
		<if test="isLimit!=null">
			and isLimit = #{isLimit}
		</if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="amount!=null">
			and amount = #{amount}
		</if>
		<if test="memberCount!=null">
			and memberCount = #{memberCount}
		</if>
		<if test="maxAmountPerPerson!=null">
			and maxAmountPerPerson = #{maxAmountPerPerson}
		</if>
		<if test="displayNo!=null">
			and displayNo = #{displayNo}
		</if>
		<if test="isUse!=null">
			and isUse = #{isUse}
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		<if test="reserved2!=null  and reserved2!=''">
			and reserved2 = #{reserved2}
		</if>
		<if test="reserved3!=null  and reserved3!=''">
			and reserved3 = #{reserved3}
		</if>
		<if test="reserved4!=null  and reserved4!=''">
			and reserved4 = #{reserved4}
		</if>
		<if test="reserved5!=null  and reserved5!=''">
			and reserved5 = #{reserved5}
		</if>
		<if test="reserved6!=null  and reserved6!=''">
			and reserved6 = #{reserved6}
		</if>
		<if test="reserved7!=null  and reserved7!=''">
			and reserved7 = #{reserved7}
		</if>
		<if test="reserved8!=null  and reserved8!=''">
			and reserved8 = #{reserved8}
		</if>
		<if test="reserved9!=null  and reserved9!=''">
			and reserved9 = #{reserved9}
		</if>
		<if test="reserved10!=null  and reserved10!=''">
			and reserved10 = #{reserved10}
		</if>
		<if test="isMonthByQuota!=null  and isMonthByQuota!=''">
			and reserved3 = #{isMonthByQuota}
		</if>
		
		ORDER BY displayNo
		limit #{pageNo},#{pageSize}
    </select>
    
    <select id="queryProductInfoTotalNum" resultType="java.lang.Integer">
		SELECT count(*) FROM ecpe_t_product where 1=1
		<if test="productID!=null">
			and ID = #{productID}
		</if>
		<if test="productCode!=null  and productCode!=''">
			and productCode = #{productCode}
		</if>
		<if test="productName!=null  and productName!=''">
			and productName like #{productName}
		</if>
		<if test="productDesc!=null  and productDesc!=''">
			and productDesc = #{productDesc}
		</if>
		<if test="productType!=null">
			and productType = #{productType}
		</if>
		<if test="unitPrice!=null  and unitPrice!=''">
			and unitPrice = #{unitPrice}
		</if>
		<if test="currency!=null  and currency!=''">
			and currency = #{currency}
		</if>
		<if test="productStatus!=null">
			and productStatus = #{productStatus}
		</if>
		<if test="isExperience!=null">
			and isExperience = #{isExperience}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
		<if test="subServType!=null">
			and subServType = #{subServType}
		</if>
		<if test="isLimit!=null">
			and isLimit = #{isLimit}
		</if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="amount!=null">
			and amount = #{amount}
		</if>
		<if test="memberCount!=null">
			and memberCount = #{memberCount}
		</if>
		<if test="maxAmountPerPerson!=null">
			and maxAmountPerPerson = #{maxAmountPerPerson}
		</if>
		<if test="displayNo!=null">
			and displayNo = #{displayNo}
		</if>
		<if test="isUse!=null">
			and isUse = #{isUse}
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		<if test="reserved2!=null  and reserved2!=''">
			and reserved2 = #{reserved2}
		</if>
		<if test="reserved3!=null  and reserved3!=''">
			and reserved3 = #{reserved3}
		</if>
		<if test="reserved4!=null  and reserved4!=''">
			and reserved4 = #{reserved4}
		</if>
		<if test="reserved5!=null  and reserved5!=''">
			and reserved5 = #{reserved5}
		</if>
		<if test="reserved6!=null  and reserved6!=''">
			and reserved6 = #{reserved6}
		</if>
		<if test="reserved7!=null  and reserved7!=''">
			and reserved7 = #{reserved7}
		</if>
		<if test="reserved8!=null  and reserved8!=''">
			and reserved8 = #{reserved8}
		</if>
		<if test="reserved9!=null  and reserved9!=''">
			and reserved9 = #{reserved9}
		</if>
		<if test="reserved10!=null  and reserved10!=''">
			and reserved10 = #{reserved10}
		</if>
		<if test="isMonthByQuota!=null  and isMonthByQuota!=''">
			and reserved3 = #{isMonthByQuota}
		</if>
    </select>
    
    <select id="getAutoID" resultType="java.lang.Integer">
    	SELECT AUTO_INCREMENT
		  FROM INFORMATION_SCHEMA.TABLES
		 WHERE TABLE_NAME = 'ecpe_t_product'
    </select>
        
    <select id="getProductID" resultType="java.lang.Integer">
    	select next value for MYCATSEQ_ECPE_T_PRODUCT
    </select>
        
  
    <insert id="insert">
	INSERT INTO ecpe_t_product (ID, productCode, productName, productDesc,productType,
				unitPrice, currency, productStatus, isExperience,
				servType, subServType, isLimit, chargeType, amount, memberCount,
				maxAmountPerPerson, effictiveTime, expireTime, displayNo,isUse,createTime,
				operatorID, lastUpdateTime, reserved1)
		VALUES (#{productID},
				#{productCode},
				#{productName},
				#{productDesc},
				#{productType},
				#{unitPrice},
				#{currency},
				#{productStatus},
				#{isExperience},
				#{servType},
				#{subServType},
				#{isLimit},
				#{chargeType},
				#{amount},
				#{memberCount},
				#{maxAmountPerPerson},
				#{effictiveTime},
				#{expireTime},
				#{displayNo},
				#{isUse},
				#{createTime},
				#{operatorID},
				#{lastUpdateTime},
				#{reserved1})
    </insert>
    
     <select id="queryProductByID" resultMap="productInfoModel">
   	 	 SELECT ID, productCode, productName, productDesc,productType, unitPrice, currency, productStatus, isExperience, 
		    	servType, subServType, isLimit, chargeType, amount, memberCount, maxAmountPerPerson, effictiveTime, 
		    	expireTime, displayNo,isUse,createTime, operatorID, lastUpdateTime,
		    	reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, reserved10
		   FROM ecpe_t_product
        where ID in 
	  <foreach collection="list"  item="productIDList" index="index" open="(" separator="," close=")">  
		 #{productIDList}
	  </foreach>
    </select>

	<select id="queryPoductByCode" resultMap="productInfoModel">
		SELECT ID, productCode, productName, productDesc,productType, unitPrice, currency, productStatus, isExperience,
		servType, subServType, isLimit, chargeType, amount, memberCount, maxAmountPerPerson, effictiveTime,
		expireTime, displayNo,isUse,createTime, operatorID, lastUpdateTime,
		reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, reserved10
		FROM ecpe_t_product
		where productCode = #{productCode}
	</select>
	<select id="queryProductByPackageID" resultMap="productInfoModel">
	SELECT
        ID, productCode, productName, productDesc,productType, unitPrice, currency, productStatus, isExperience,
		servType, subServType, isLimit, chargeType, amount, memberCount, maxAmountPerPerson, effictiveTime,
		expireTime, displayNo,isUse,createTime, operatorID, lastUpdateTime,
		reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, reserved10
        FROM
        ecpe_t_product t3
        WHERE
        t3.ID IN (
        SELECT
        t1.productID
        FROM
        ecpe_t_product_package_rule t1
        WHERE t1.packageID = #{packageID}
        )
	</select>
</mapper>