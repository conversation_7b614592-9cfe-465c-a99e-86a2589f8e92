<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>直客管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="enterpriseListCtrl.js"></script>
	<style>
	/* media for adjustable search-table width  */
	@media (max-width: 1366px){
  	.control-label{
		width: 86px;
		padding-left: 0;
		padding-right: 0;
	}
}
	</style>
</head>

<body ng-app="myApp" class="body-min-width">
	<div class="cooperation-manage container-fluid" ng-controller="EnterpriselistCtrl" ng-init="init();" ng-cloak>
		<div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_COOPERATION'|translate"> </span> > <span class="second-tab">直客管理</span></div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group" style="min-width: 1000px;">
					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap">企业名称</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<input type="text" id="enterpriseName" class="form-control" placeholder="请输入企业名称" ng-model="enterpriseName">
					</div>


					<label for="organizationID" class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap">企业机构代码</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<input style="min-width:160px;" type="text" id="organizationID" class="form-control" placeholder="请输入企业机构代码"
									 ng-model="organizationID">
					</div>

					<label for="province" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap;max-width: 70px;">归属地</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.provinceName for x in provinceList"
										ng-change="changeSelectedProvince(selectedProvince)">
							<option value="">不限</option>
						</select>
					</div>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity"
										ng-options="x.cityName for x in subCityList"
										ng-disabled="selectedProvince===null||selectedProvince.provinceID==='000'">
							<option value="" ng-if="false"></option>
						</select>
					</div>
					<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
						<button type="submit" class="btn search-btn" ng-click="enterpriseList()" style="float: right">
							<icon class="search-iocn"></icon>
							搜索
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="gotoAdd()">
				<icon class="add-iocn"></icon>
				新增直客
			</button>
			<a href="/qycy/ecpmp/ecpmpServices/fileService/download?path=111"></a>
		</div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:10%">企业编号</th>
					<th style="width:10%">企业名称</th>
					<th style="width:14%">企业机构代码</th>
					<th style="width:15%">custID（营账）</th>
					<th style="width:15%">账号</th>
					<th style="width:13%">归属地</th>
					<th style="width:13%">创建时间</th>
					<th style="width:10%">操作</th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in queryEnterpriseList">
					<td><span title="{{item.id}}">{{item.id}}</span></td>
					<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
					<td style="min-width: 100px"><span title="{{item.organizationID}}">{{item.organizationID}}</span></td>
					<td><span title="{{item.custID}}">{{item.custID}}</span></td>
					<td><span title="{{item.accountInfo.accountName}}">{{item.accountInfo.accountName||""}}</span></td>
					<td><span>{{provinceList2[item.provinceID]}}</span></td>
					<td><span>{{item.createTime|newDate|limitTo:10}}</span></td>

					<td>
						<div class="handle">
							<ul>
								<!-- <li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									查看
								</li> -->
								<li class="edit" ng-click="toEdit(item)">
									<icon class="edit-icon"></icon>
									设置
								</li>
								<!-- <li class="delete">
										<icon class="delete-icon"></icon>删除</li> -->
								<!-- <li class="set">
										<icon class="set-icon"></icon>设置</li> -->
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="queryEnterpriseList.length<=0">
					<td style="text-align:center" colspan="9">暂无数据</td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="enterpriseList('justPage')"></ptl-page>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel">提示</h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close">确定</button>
					</div>
				</div>
			</div>
		</div>


	</div>
</body>

</html>