<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.cutover.dao.ecpm.mapper.EnterpriseMonthStatMapper">
	<resultMap id="enterpriseMonthStatWrapper"
		type="com.huawei.jaguar.cutover.dao.domain.EnterpriseMonthStatWrapper">
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="enterpriseType" column="enterpriseType"
			javaType="java.lang.Integer" />
		<result property="parentEnterpriseID" column="parentEnterpriseID"
			javaType="java.lang.Integer" />
		<result property="provinceID" column="provinceID" javaType="java.lang.String" />
		<result property="cityID" column="cityID" javaType="java.lang.String" />
		<result property="statMonth" column="statMonth" javaType="java.lang.String" />
		<result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="chargeType" column="chargeType" javaType="java.lang.Integer" />
		<result property="memberCount" column="memberCount" javaType="java.lang.Integer" />
		<result property="deliveryMemberCount" column="deliveryMemberCount" 
			javaType="java.lang.Integer" />
		<result property="useCount" column="useCount" javaType="java.lang.Long" />
		<result property="experienceCount" column="experienceCount"
			javaType="java.lang.Long" />
		<result property="experienceStartTime" column="experienceStartTime" 
			javaType="java.util.Date" />
		<result property="experienceEndTime" column="experienceEndTime" 
			javaType="java.util.Date" />	
		<result property="experienceTotalCount" column="experienceTotalCount" 
			javaType="java.lang.Long" />
		<result property="experienceRemainCount" column="experienceRemainCount" 
			javaType="java.lang.Long" />
		<result property="ussdCount" column="ussdCount" javaType="java.lang.Long" />
		<result property="flashCount" column="flashCount" javaType="java.lang.Long" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="useCountMobile" column="useCountMobile" javaType="java.lang.Long" />
		<result property="useCountUnicom" column="useCountUnicom" javaType="java.lang.Long" />
		<result property="useCountTelecom" column="useCountTelecom" javaType="java.lang.Long" />
	</resultMap>
	
	<!-- 以企业为维度查询企业统计信息 -->
	<select id="queryEnterpriseMonthStat" resultMap="enterpriseMonthStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceStartTime,
		experienceEndTime,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom
		from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		order by statMonth desc
		limit #{pageNum},#{pageSize}
	</select>

	<!-- 以企业为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseMonthStatCount" resultType="java.lang.Integer">
		SELECT
		count(0) from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
	</select>
	
	<!-- 以省为维度查询企业统计信息 -->
	<select id="queryEnterpriseMonthStatByProvince" resultMap="enterpriseMonthStatWrapper">
		select
		provinceID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		SUM(memberCount) AS memberCount,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		SUM(experienceTotalCount) AS experienceTotalCount,
		SUM(experienceRemainCount) AS experienceRemainCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom
		from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, statMonth, serviceType, subServType, chargeType
		order by statMonth desc
		limit #{pageNum},#{pageSize}
	</select>
	
	<!-- 以省为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseMonthStatCountByProvince" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	count(0)
		 from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, statMonth, serviceType, subServType, chargeType) t
	</select>
	
	<!-- 以市为维度查询企业统计信息 -->
	<select id="queryEnterpriseMonthStatByCity" resultMap="enterpriseMonthStatWrapper">
		select
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		SUM(memberCount) AS memberCount,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		SUM(experienceTotalCount) AS experienceTotalCount,
		SUM(experienceRemainCount) AS experienceRemainCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom
		from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, cityID, statMonth, serviceType, subServType, chargeType
		order by statMonth desc
		limit #{pageNum},#{pageSize}
	</select>
	
	<!-- 以市为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseMonthStatCountByCity" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	count(0)
		 from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, cityID, statMonth, serviceType, subServType, chargeType) t
	</select>
	
	<select id="queryEnterpriseMonthStatByDate" resultMap="enterpriseMonthStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceStartTime,
		experienceEndTime,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		updateTime,
		status,
		useCountMobile,
		useCountUnicom,
		useCountTelecom
		from ecpm_t_enterprise_month_stat t1
		where 
		t1.enterpriseID=#{enterpriseID}
		and t1.serviceType=#{serviceType}
		and t1.subServType=#{subServType}
		and t1.chargeType=#{chargeType}
		and t1.statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
	</select>
	
	<update id="updateEnterpriseMonthCountStat">
		update ecpm_t_enterprise_month_stat
		set
		<trim suffixOverrides="," suffix="where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType} and chargeType=#{chargeType} and statMonth=#{statMonth}">
			<if test="deliveryMemberCount != null">
				deliveryMemberCount=#{deliveryMemberCount},
			</if>
			<if test="ussdCount != null">
				ussdCount=#{ussdCount},
			</if>
			<if test="flashCount != null">
				flashCount=#{flashCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="memberCount != null">
				memberCount=#{memberCount},
			</if>
			<if test="useCount != null">
				useCount=#{useCount},
			</if>
			<if test="experienceCount != null">
				experienceCount=#{experienceCount},
			</if>
			<if test="experienceStartTime != null">
				experienceStartTime=#{experienceStartTime},
			</if>
			<if test="experienceEndTime != null">
				experienceEndTime=#{experienceEndTime},
			</if>
			<if test="experienceTotalCount != null">
				experienceTotalCount=#{experienceTotalCount},
			</if>
			<if test="experienceRemainCount != null">
				experienceRemainCount=#{experienceRemainCount},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
			<if test="useCountMobile != null">
				useCountMobile=#{useCountMobile},
			</if>
			<if test="useCountUnicom != null">
				useCountUnicom=#{useCountUnicom},
			</if>
			<if test="useCountTelecom != null">
				useCountTelecom=#{useCountTelecom},
			</if>
		</trim>
	</update>
	
	<insert id="insertEnterpriseMonthCountStat">
		insert into
		ecpm_t_enterprise_month_stat
		(
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceStartTime,
		experienceEndTime,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		updateTime,
		status,
		useCountMobile,
		useCountUnicom,
		useCountTelecom
		)
		values
		(
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseType},
		#{parentEnterpriseID},
		#{provinceID},
		#{cityID},
		#{statMonth},
		#{serviceType},
		#{subServType},
		#{chargeType},
		#{memberCount},
		#{deliveryMemberCount},
		#{useCount},
		#{experienceCount},
		#{experienceStartTime},
		#{experienceEndTime},
		#{experienceTotalCount},
		#{experienceRemainCount},
		#{ussdCount},
		#{flashCount},
		#{updateTime},
		#{status},
		#{useCountMobile},
		#{useCountUnicom},
		#{useCountTelecom}
		)
	</insert>
	
	<update id="updateEnterpriseMonthCountStatForUseCount">
		update ecpm_t_enterprise_month_stat
		set
		<trim suffixOverrides="," suffix="where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType} and chargeType=#{chargeType} and statMonth=#{statMonth}">
			<if test="deliveryMemberCount != null">
				deliveryMemberCount=#{deliveryMemberCount},
			</if>
			<if test="ussdCount != null">
				ussdCount=#{ussdCount},
			</if>
			<if test="flashCount != null">
				flashCount=#{flashCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="memberCount != null">
				memberCount=#{memberCount},
			</if>
			<if test="useCount != null">
				useCount= useCount - #{useCount},
			</if>
			<if test="experienceCount != null">
				experienceCount=#{experienceCount},
			</if>
			<if test="experienceStartTime != null">
				experienceStartTime=#{experienceStartTime},
			</if>
			<if test="experienceEndTime != null">
				experienceEndTime=#{experienceEndTime},
			</if>
			<if test="experienceTotalCount != null">
				experienceTotalCount=#{experienceTotalCount},
			</if>
			<if test="experienceRemainCount != null">
				experienceRemainCount=#{experienceRemainCount},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
		</trim>
	</update>
	
		<!-- 更新企业日统计使用量 -->
	<update id="updateEnterpriseMonthStat">
		update ecpm_t_enterprise_month_stat
		set
		<if test="useCount != null">
			useCount=useCount + #{useCount},
		</if>
		<if test="updateTime != null">
			updateTime=#{updateTime}
		</if>
		where chargeType=#{chargeType}
		<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
			and enterpriseID in
			<foreach item="enterpriseID" index="index" collection="enterpriseIDList"
				open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		and serviceType=#{serviceType} and subServType=#{subServType} and
		statMonth=#{statMonth}
	</update>
	
	<select id="queryEnterpriseMonthStatForSum" resultMap="enterpriseMonthStatWrapper">
	    select enterpriseid,sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount ,sum(ifnull(useCount,0)) as useCount ,avg(ifnull(status,0)) status,
	    sum(ifnull(ussdCount,0)) as ussdCount,sum(ifnull(flashCount,0)) as flashCount,
	    sum(ifnull(experienceCount,0)) as experienceCount
	    from ecpm_t_enterprise_month_stat 
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType} 
        and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
		and statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
		group by enterpriseid 
	</select>
	
		<delete id="deleteEnterpriseMonthStatWrapper">
		delete from ecpm_t_enterprise_month_stat where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType} and statMonth=#{statMonth}
		and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
	</delete>
	
	<select id="queryEnterpriseMonthStatByDateForSp" resultMap="enterpriseMonthStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceStartTime,
		experienceEndTime,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		updateTime,
		status
		from ecpm_t_enterprise_month_stat t1
		where 
		t1.enterpriseID=#{enterpriseID}
		and t1.serviceType=#{serviceType}
		and t1.subServType=#{subServType}
		and t1.chargeType=#{chargeType}
		and t1.statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
	</select>
	
	<delete id="deleteEnterpriseMonthStatWrapperForBefore">
		delete from ecpm_t_enterprise_month_stat 
		where statMonth=#{statMonth} 
		and enterpriseID in
		<foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="("
			separator="," close=")">
			#{enterpriseID}
		</foreach>
	</delete>
	
	<select id="queryEnterpriseMonthStatForAgentSum" resultMap="enterpriseMonthStatWrapper">
	    select sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount ,sum(ifnull(useCount,0)) as useCount ,avg(ifnull(status,0)) status,
	    sum(ifnull(ussdCount,0)) as ussdCount,sum(ifnull(flashCount,0)) as flashCount,
	    sum(ifnull(experienceCount,0)) as experienceCount,sum(ifnull(experienceRemainCount,0)) as experienceRemainCount
	    from ecpm_t_enterprise_month_stat 
	    where parentEnterpriseID = #{parentEnterpriseID} and serviceType=#{serviceType} and subServType =#{subServType} 
	    and chargeType=#{chargeType}
	    and statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
        group by parententerpriseid
	</select>
	
	<update id="updateEnterpriseName">
		update ecpm_t_enterprise_month_stat t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>
</mapper>