<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>内容管理</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/searchList.css" rel="stylesheet"/>
    <link href="../../../../../css/addContent.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-sanitize.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css"/>
    <script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="ContentManagement.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../css/hotlineContentManage.css"/>
    <link href="../../../../../css/laydate.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../../../../../frameworkJs/laydate.js"></script>
    <style>
        .col-lg-2.col-xs-2.col-sm-2.col-md-2.control-label{
            padding-top: 5px;
        }
        .switch {
            display: table-cell;
        }
        .col-lg-4.col-xs-4.col-sm-4.col-md-4{
            width: 39.333333%
        }
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            text-align: right;
        }

        .addMsinsdn{
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../../../assets/images/add.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
        }
        .deleteMsinsdn{
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../../../assets/images/delete.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
        }
        .col-md-4{
            width: 60%;
        }
        .col-md-2.control-label{
            width: 25%;
        }
        .col-lg-4{
            width: 60%;
        }
        .col-md-offset-2.col-lg-7.col-xs-8.col-sm-8.col-md-8.clear-preview{
            width: 75%;
        }
        .form-group div {
            line-height: 34px;
        }
        .row{
            margin-block: 10px
        }
        .pic-wrapper,.ctn-wrapper {
            overflow: hidden;
            padding: 10px 0;
            position: relative;
        }
        .handle ul li icon.manage-icon {
            background-position: -126px 0;
        }

        .handle ul li icon.add-icon {
            background-position: -55px 0;
        }

        .handle ul li icon.import-icon {
            background-position: -90px 0;
        }

        .table th.adjustable-width {
            width: 25%;
        }

        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        #filePicker_ div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        .form-group div li {
            display: inline-block;
            margin-right: 10px;
            padding-right: 10px;
            cursor: pointer;
        }

        .form-group div li span {
            vertical-align: middle;
            margin-right: 4px;
        }

        /* media for adjustable search-table width  */
        @media (max-width: 1850px) {
            .table th.adjustable-width {
                width: 28%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1600px) {
            .table th.adjustable-width {
                width: 30%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1300px) {
            .table th.adjustable-width {
                width: 33%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1100px) {
            .table th.adjustable-width {
                width: 42%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }



        .label-supply {
            display: inline-block;
            float: left;
            padding-right: 15px;
            padding-left: 15px;
        }


    </style>
</head>
<!--
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()" class="">
-->
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()" class="body-min-width-new">
<div class="cooperation-manage" style="overflow-x: scroll;">

    <!-- 管理员登陆查看直客 -->
    <div  class="cooperation-head">
        <span class="frist-tab" >代理商管理 > 子企业管理 > 企业通知</span>
    </div>

    <!-- 管理员登陆查看直客 -->
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/secondEnterpriseManage/task"
              list-index="78"></top:menu>
    <div class="cooperation-search">

        <form class="form-horizontal">
            <div class="form-group form-inline">
                <div class="control-label label-supply">
                    <label for="contentName"
                           ng-bind="'CONTENTAUDIT_HOTCYCONTENT'|translate"></label>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                    <input ng-model='initSel.content' type="text" autocomplete="off" class="form-control" id="contentName"
                           placeholder="{{'HOTLINECONTENT_PLEASEINPUTCONTENTKEYWORDS'|translate}}">
                </div>

                <div class="control-label label-supply">
                    <label for="contentStatus" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3">
                    <select style="max-width:200px;width: 100%;" class="form-control"
                            ng-model="initSel.auditStatus" id="contentStatus"
                            ng-options="x.id as x.name for x in auditStatusChoise"></select>
                </div>

                <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
                    <button ng-click="queryHotlineContentInfoList()" type="submit" class="btn search-btn"
                            style="margin-left: 86px">
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div class="add-table">
        <button type="submit" class="btn add-btn" ng-click="operaContent()">
            <icon class="add-iocn"></icon>
            <span style="color:#705de1" ng-bind="'HOTLINECONTENT_ADDHOTLINECONTENT'|translate"></span>
        </button>
        <button id="exportContentInfoList" class="btn add-btn" ng-click="export()">
            <icon class="export-icon"></icon>
            <span ng-bind="'DETAIL_EXPORT'|translate" style="color: #705de1;margin-left: 8px;"></span>
        </button>
    </div>

    <div style="margin-left: 20px;margin-bottom: 20px;">
        <p style="font-size: 16px;font-weight: 500;" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></p>
    </div>
    <div class="coorPeration-table">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th style="width:6%" ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></th>
                <th style="width:7%" ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate"></th>
                <th style="width:7%" ng-bind="'CONTENTAUDIT_AUDITSTATE_UNICOM'|translate"></th>
                <th style="width:7%" ng-bind="'CONTENTAUDIT_AUDITSTATE_TELECOM'|translate"></th>
                <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_MOVE'|translate"></th>
                <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_UNICOM'|translate"></th>
                <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_TELECOM'|translate"></th>
                <th style="width:7%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
                <th style="width:15%" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></th>
                <th style="width:34%" class="adjustable-width" ng-bind="'COMMON_OPERATE'|translate"></th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="item in hotContentInfoListData">
                <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
                <!-- <td><span title="{{item.contentBelongOrgList[0].hotlineNo}}">{{item.contentBelongOrgList[0].hotlineNo}}</span></td> -->
                <td style="min-width: 100px"><span
                        title="{{hotlineStatusMap[item.approveStatus]}}">{{hotlineStatusMap[item.approveStatus]}}</span>
                </td>
                <td style="min-width: 100px"><span
                        title="{{unicomApproveStatusMap[item.unicomApproveStatus]}}">{{unicomApproveStatusMap[item.unicomApproveStatus]}}</span>
                </td>
                <td style="min-width: 100px"><span
                        title="{{unicomApproveStatusMap[item.telecomApproveStatus]}}">{{unicomApproveStatusMap[item.telecomApproveStatus]}}</span>
                </td>
                <td><span title="{{getApproveIdea(item)}}">{{getApproveIdea(item)}}</span></td>
                <td>
                  <span
                          ng-bind-html="CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType, item.subServType).htmlVersion"
                          title="{{ CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType,item.subServType).titleVersion}}">
                  </span>
                </td>
                <td>
                  <span
                          ng-bind-html="CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType, item.subServType).htmlVersion"
                          title="{{ CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType,item.subServType).titleVersion}}">
                  </span>
                </td>
                <td><span title="{{sceneMap[item.scene]}}">{{sceneMap[item.scene]}}</span></td>
                <td>
                    <span title="{{item.content}}" ng-if="item.signature&&item.signature!=null&&item.signature!=''">【{{item.signature}}】{{item.content}}</span>
                    <span title="{{item.content}}"
                          ng-if="item.signature==null||item.signature==''">{{item.content}}</span>
                </td>
                <td>
                    <div class="handle">
                        <ul>
                            <!-- 删除 -->
                            <li class="delete" ng-show="(item.approveStatus == 3 ||item.approveStatus == 4 )&& item.createTask "
                                ng-click="deleteHotlineContent(item.contentID)">
                                <icon class="delete-icon"></icon>
                                <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
                            </li>
                            <!-- 编辑 -->
                            <li ng-show="(item.approveStatus == 3 ||item.approveStatus == 4 )&& item.createTask " class="edit"
                                ng-click="operaContent(item,'modify')">
                                <icon class="edit-icon"></icon>
                                <span style="color:#705de1" ng-bind="'GROUP_EDIT'|translate"></span>
                            </li>
                            <!-- 查看 -->
                            <li  class="edit"
                                ng-click="operaContent(item,'detail')">
                                <icon class="edit-icon"></icon>
                                <span style="color:#705de1" ng-bind="'COMMON_WATCH'|translate"></span>
                            </li>
                            <!-- 新增任务 -->
                            <li ng-show="item.approveStatus == 3 &&item.status==0 && isTempContent(item.content)" class="edit"
                                ng-click="toAdd(item)">
                                <icon class="edit-icon"></icon>
                                <span style="color:#705de1" ng-bind="'ADDTASK'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
            <tr ng-show="hotContentInfoListData.length<=0">
                <td style="text-align:center" colspan="10" ng-bind="'COMMON_NODATA'|translate"></td>
            </tr>
            </tbody>
        </table>
    </div>

    <div>
        <ptl-page tableId="0" change="queryHotlineContentInfoList('justPage')"></ptl-page>
    </div>

</div>


<!-- 删除热线内容弹窗 -->
<div class="modal fade bs-example-modal-sm" id="deleteHotlineContent" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content" style="width:390px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838' >是否确认删除？</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                        ng-click="delHotlineContent()"></button>
                <button id="deleteHotlineContentCancel" type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" id="addHotlineContentCancel" ng-bind="'NO'|translate"></button>
            </div>
        </div>
    </div>
</div>


<!--新增编辑查看内容弹窗-->
<div class="modal fade bs-example-modal-sm" id="operaContent" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" style="overflow: auto">
    <div class="modal-dialog modal-sm" role="document" style="width: 780px  ">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="operaContentLabel">{{operaName}}</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" name="myForm" novalidate>
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
                                <span ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></span>：
                            </label>
                            <div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
                                <select class="form-control" ng-model="scene"
                                        ng-options="x.id as x.name for x in serviceTypeChoise" ng-change="changeServiceType(scene)"
                                        ng-disabled="operate =='detail' || operate =='modify'"></select>
                            </div>
                            <div class="col-lg-3 col-xs-3  col-sm-3 col-md-3">
                                <icon style="color:red;" ng-show="isStatusOpen==0 && operate!='detail'">*</icon>
                                <span class="redFont" ng-show="isStatusOpen==0 && operate!='detail'" style="color:red;">
									{{'ZCSTATUSCLOSEDESC'|translate}}
							</span>
                            </div>
                        </div>
                    </div>
					<!-- 运营商 -->
		            <div class="form-group" ng-show="!inArray(initPrintInfo.subServType)">
		            	<div class="row platforms">
			                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
			                    <icon>*</icon>
			                    <span ng-bind="'PLATFORM'|translate"></span>：
			                </label>
			                <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5" ng-click="checkSignatureRequired()">
			                    <li class="check-li">
			                        <span class="check-btn checked-btn"> </span>
			                        {{'MOBILE'|translate}}
			                    </li>
			                    <li class="check-li">
			                        <span class="check-btn checked-btn"> </span>
			                        {{'UNICOM'|translate}}
			                    </li>
			                    <li class="check-li">
			                        <span class="check-btn checked-btn"> </span>
			                        {{'TELECOM'|translate}}
			                    </li>
			
			                </div>
		                </div>
		            </div>
                    <div class="form-group" ng-show="serviceType != 16 && serviceType != 20">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon ng-model="signatureRequired"
                                  ng-show="signatureRequired =='1'&&noSign == '1'">*</icon>
                                <span ng-bind="'SIGNATURE'|translate"></span>：
                            </label>
                            <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5">
                                <input class="form-control" ng-show="operate !='detail'"
                                       type="text" id="signName" name="signName" ng-model="signature"
                                       placeholder="请输入1~67字"
                                       ng-change="checkSignName(signature)"
                                       ng-disabled="operate =='detail'">
                                <input class="form-control" ng-show="operate =='detail'"
                                       type="text" id="signName" name="signName" ng-model="signature"
                                       placeholder="请输入1~67字"
                                       ng-blur="checkSignName(signature)"
                                       ng-disabled="operate =='detail'">
                                <!--ng-class="{'redBorder':!signName}"-->
                                <!--title={{taskName}}-->
                                <!--autocomplete="off">-->
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
                                     ng-show="!signatureValidate">
                                <span class="redFont" ng-show="!signatureValidate" ng-bind="'SIGNATURE_MAXLENTH_67'|translate">
								</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="serviceType != 16 && serviceType != 20">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                                <icon>*</icon><span ng-bind="'TASKCONTENT'|translate"></span>：
                            </label>
                            <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5">
                            <textarea style="float:left;height: 74px;"
                                      placeholder="{{msg}}" class="form-control" rows="3"
                                      name="{{'content'}}" ng-model="content"
                                      ng-blur="sensitiveCheck(content,3,'')"
                                      ng-change="checkSmsContent(content,3,'')"
                                      ng-disabled="operate=='detail'">
                            </textarea>
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
                                     ng-show="!contentValidate">
                                <span class="redFont" ng-show="!contentValidate">
									{{'SMS_CONTENT_LENGTH_MAX_750'|translate}}
								</span>
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
                                     ng-show="!contentValidateScreen">
                                <span class="redFont" ng-show="!contentValidateScreen">
									{{tipMsg|translate}}
								</span>
                                <!-- 110迭代：增加敏感词提示 取消isSensitive[3]，使用sensitive代替-->
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
                                     ng-show="sensitive">
                                <span class="redFont" ng-show="sensitive">{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[3]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>

                            </div>
                        </div>

                    </div>

                    <!-- 增彩 -->
                    <div class="form-group" ng-show="serviceType == 16">
                        <div style="overflow:hidden;" ng-cloak>
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="margin-left:-8px">
                                <icon>*</icon><span ng-bind="'TASKCONTENT'|translate"></span>：
                            </label>
                            <div class="row">
                                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="max-width:116px;margin-left:-12px">
                                    <icon>*</icon><span ng-bind="'ZCCONTENTTITLE'|translate"></span>：
                                </label>
                                <div class="col-lg-3 col-xs-3  col-sm-3 col-md-3">
                                    <input class="form-control"
                                           type="text" id="contentTitle" name="contentTitle" ng-model="contentTitle"
                                           placeholder="{{'INPUTZCCONTENTTITLE'|translate}}"
                                           ng-blur="checkContentTitle(contentTitle)"
                                           ng-disabled="operate =='detail'"
                                           ng-class="{'redBorder':!contentTitleValidate}"
                                           title={{contentTitle}}>
                                    <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
                                         ng-show="!contentTitleValidate">
                                    <span class="redFont" ng-show="!contentTitleValidate">
									{{'ZCCONTENTTITLEDESC'|translate}}
								</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"
                                       style="max-width:108px;margin-left:23.2%">
                                    <icon>*</icon><span ng-bind="'ZCCONTENT'|translate"></span>：
                                </label>
                                <div class="col-lg-8 col-xs-8 col-sm-8 col-md-8" style="width: 60%">
                                    <div style="padding-top:7px;color:rgb(153,153,153)" ng-show="contentList.length==0">
                                        <p ng-bind="'ZC_ADDCONTENT_PLH'|translate" ></p>
                                    </div>
                                    <div class="ctn-pic-list" ng-repeat="item in contentList">
                                        <div class="pic-wrapper" ng-if="item.frameType ==1">
                                            <img style="float:left;max-width: 250px;border: 1px solid darkgrey;"
                                                 ng-src="{{item.formatFramePicUrl}}" alt="">
                                            <button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
                                                    type="submit" class="btn btn-primary search-btn "
                                                    style="position: absolute;left: 290px;top:50%;margin-top: -17px;">
                                                <span ng-bind="'COMMON_DELETE'|translate"></span></button>
                                        </div>
                                        <div class="pic-wrapper" ng-if="item.frameType ==3">
                                            <video style="float:left;max-width: 350px;border: 1px solid darkgrey;" controls="controls">
                                                <source ng-src="{{item.formatFramePicUrl}}"  type="video/mp4">
                                                -                                         <source ng-src="{{item.formatFramePicUrl}}"  type="video/3gp">
                                            </video>

                                            <button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
                                                    type="submit" class="btn btn-primary search-btn "
                                                    style="position: absolute;margin-left: 15px;top:50%;margin-top: -17px;">
                                                <span ng-bind="'COMMON_DELETE'|translate"></span>
                                            </button>
                                        </div>
                                        <div class="ctn-wrapper" ng-if="!item.framePicUrl && (item.frameType !=3 && item.frameType !=1)">
                                            <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4" style="padding-left:0px">
											<textarea style="float:left;height: 74px;"
                                                      placeholder="请输入彩印内容0~1000字" class="form-control" rows="3"
                                                      name="{{'contentzc'+$index}}" ng-model="item.frameTxt" required
                                                      ng-blur="sensitiveCheckX(item,ctnTextSum,$index,1000)"
                                                      ng-disabled="operate=='detail'">
											</textarea>
                                            </div>
                                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                                <button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
                                                        type="submit" class="btn btn-primary search-btn "
                                                        style="float: left;margin: 20px 20px;">
                                                    <span ng-bind="'COMMON_DELETE'|translate" ></span>
                                                </button>
                                            </div>
                                            <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
                                                 ng-show="!item.Validate&&!item.sensitiveWordsStr"
                                                 style="padding-top:18px">
											<span style="color:red">
												<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                                     align="absmiddle">
												<span ng-show="!item.Validate&&!item.sensitiveWordsStr"
                                                      ng-bind="'ZCCONTENTREQUIRE'|translate"></span>
											</span>
                                            </div>
                                            <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
                                                 ng-show="!item.Validate&&item.sensitiveWordsStr"
                                                 style="padding-top:18px">
											<span style="color:red">
											<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                                 align="absmiddle">
											<span>
												{{'CONTENT_DETECTION'|translate}}{{item.sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
											</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 添加图片和添加文本按钮 -->
                        <div style="overflow:hidden;" ng-show="operate!='detail'">
                            <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                               style="color:red;padding-left: 15px;" ng-bind="'TASK_CONTENTPICTEXT_LIMITTIP'|translate">
                            </p>
                            <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                               ng-show="ctnTextSumLength>1000" style="color:red;padding-left:12px">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-bind="'CONTENT_TEXT_MAXLENGTHTIP3'|translate"></span>
                            </p>
                            <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                               ng-show="errorInfoVideo" style="color:red;padding-left:12px">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span>{{errorInfoVideo|translate}}</span>
                            </p>
                            <div class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-7 col-xs-8 col-sm-8 col-md-8 clear-preview">
                                <cy:uploadifyfile filelistid="fileListVideo" filepickerid="filePickerVideo" accepttype="accepttypeVideo"
                                                  uploadifyid="uploadifyidVideo" validate="isValidateVideo" filesize="filesizeVideo"
                                                  mimetypes="mimetypesVideo"
                                                  formdata="uploadParamVideo" uploadurl="uploadurlVideo" desc="uploadDescVideo" numlimit="numlimitVideo"
                                                  urllist="urlListVideo" createthumbnail="isCreateThumbnailVideo" auto="auto"
                                                  style="margin-left: 15px;float: left;" ng-show="videoLength<3" >
                                </cy:uploadifyfile>
                                <!-- 视频最大数时展示的上传视频框 -->
                                <button ng-disabled="videoLength>2" style="float:left;margin-right: 15px;"
                                        ng-show="videoLength>2" type="submit"
                                        class="btn btn-primary search-btn">
                                    <icon class="file-icon" style="margin-top: -4px;float: left;"></icon>
                                    <span style="float:left" ng-bind="'点击添加视频'|translate"></span>
                                </button>
                                <button ng-disabled="ctnTextMount>=3" ng-click="addTextCtn()" style="float:left"
                                        ng-if="operate !='detail'" type="submit"
                                        class="btn btn-primary search-btn">
                                    <span ng-bind="'CONTENT_CLICK_ADDTEXT'|translate"></span>
                                </button>
                                <cy:uploadify class="col-lg-6 col-xs-6 col-sm-6 col-md-6" ng-show="picLength<3 && operate !='detail'"
                                              filelistid="fileListImg" filepickerid="filePickerImg" accepttype="accepttypeImg"
                                              uploadifyid="uploadifyidImg" validate="isValidateImg" filesize="filesizeImg"
                                              mimetypes="mimetypesImg" formdata="uploadParamImg" uploadurl="uploadurlImg" desc=""
                                              numlimit="numlimitImg" createthumbnail="isCreateThumbnailImg" style="float: left;">
                                </cy:uploadify>
                                <!-- 图片最大张数时展示的上传图片框 -->
                                <button ng-disabled="picLength>2" style="float:left;margin-right: 15px;margin-left:12px"
                                        ng-show="picLength>2" type="submit"
                                        class="btn btn-primary search-btn">
                                    <icon class="file-icon" style="margin-top: -4px;float: left;"></icon>
                                    <span style="float:left" ng-bind="'点击添加图片'|translate"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- 彩信 -->
                    <div class="form-group" ng-show="serviceType == 20">
                        <div style="overflow:hidden;" ng-cloak>
                            <!--<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="margin-left:-8px">-->
                            <!--<icon>*</icon><span ng-bind="'TASKCONTENT'|translate"></span>：-->
                            <!--</label>-->
                            <div class="row">
                                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                                    <icon>*</icon><span ng-bind="'CXCONTENTTITLE'|translate"></span>：
                                </label>
                                <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                                    <input class="form-control"
                                           type="text" id="contentTitle" name="contentTitle" ng-model="contentTitle"
                                           placeholder="{{'INPUTCXCONTENTTITLE'|translate}}"
                                           ng-blur="checkCXContentTitle(contentTitle)"
                                           ng-disabled="operate =='detail'"
                                           ng-class="{'redBorder':!contentTitleValidate}"
                                           title={{contentTitle}}>
                                    <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
                                         ng-show="!contentTitleValidate">
                                    <span class="redFont" ng-show="!contentTitleValidate">
									{{'CONTENTTITLEDESC20'|translate}}
								</span>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="row">
                                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                                        <icon>*</icon><span ng-bind="'CXCONTENT'|translate"></span>：
                                    </label>
                                    <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                                        <div style="padding-top:7px;color:rgb(153,153,153)" ng-show="contentList.length==0">
                                            <p ng-bind="'CX_ADDCONTENT_PLH'|translate" ></p>
                                        </div>
                                        <div class="ctn-pic-list" ng-repeat="item in contentList">
                                            <div class="pic-wrapper" ng-if="item.frameType ==1">
                                                <div class="col-lg-9 col-xs-9 col-sm-9 col-md-9" style="padding-left:0px">
                                                    <img style="float:left;width: 100%;border: 1px solid darkgrey;"
                                                         ng-src="{{item.formatFramePicUrl}}" alt="">
                                                </div>
                                                <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
                                                    <button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
                                                            type="submit" class="btn btn-primary search-btn">
                                                        <span ng-bind="'COMMON_DELETE'|translate"></span></button>
                                                </div>
                                            </div>
                                            <div class="ctn-wrapper" ng-if="!item.framePicUrl && (item.frameType !=3 && item.frameType !=1)">
                                                <div class="col-lg-9 col-xs-9 col-sm-9 col-md-9" style="padding-left:0px">
											<textarea style="float:left;height: 74px;"
                                                      placeholder="请输入彩印内容0~750字" class="form-control" rows="3"
                                                      name="{{'content'+$index}}" ng-model="item.frameTxt" required
                                                      ng-blur="sensitiveCheckX(item,ctnTextSum,$index,750)"
                                                      ng-disabled="operate=='detail'">
											</textarea>
                                                </div>
                                                <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                                    <button ng-hide="operate=='detail'" ng-click="deleteCtnOrPic($index)"
                                                            type="submit" class="btn btn-primary search-btn "
                                                            style="float: left;margin: 20px 0px;">
                                                        <span ng-bind="'COMMON_DELETE'|translate" ></span>
                                                    </button>
                                                </div>
                                                <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
                                                     ng-show="!item.Validate&&!item.sensitiveWordsStr"
                                                     style="padding-top:18px">
											<span style="color:red">
												<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                                     align="absmiddle">
												<span ng-show="!item.Validate&&!item.sensitiveWordsStr"
                                                      ng-bind="'CXCONTENTREQUIRE'|translate"></span>
											</span>
                                                </div>
                                                <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6"
                                                     ng-show="!item.Validate&&item.sensitiveWordsStr"
                                                     style="padding-top:18px">
											<span style="color:red">
											<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                                 align="absmiddle">
											<span>
												{{'CONTENT_DETECTION'|translate}}{{item.sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
											</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 添加图片和添加文本按钮 -->
                        <div style="overflow:hidden;" ng-show="operate!='detail'">
                            <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                               style="color:red;padding-left: 15px;" ng-bind="'TASK_CXCONTENTPICTEXT_LIMITTIP'|translate">
                            </p>
                            <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                               ng-show="ctnTextSumLength>750" style="color:red;padding-left:12px">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-bind="'CONTENT_TEXT_MAXLENGTHTIP'|translate"></span>
                            </p>
                            <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                               ng-show="allPicSize>286720" style="color:red;">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-bind="'CONTENT_FILESIZE_MAXTIP'|translate"></span>
                            </p>
                            <div class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-7 col-xs-8 col-sm-8 col-md-8 clear-preview">
                                <button ng-disabled="ctnTextMount>=5" ng-click="addTextCtn()" style="float:left"
                                        ng-if="operate !='detail'" type="submit"
                                        class="btn btn-primary search-btn">
                                    <span ng-bind="'CONTENT_CLICK_ADDTEXT'|translate"></span>
                                </button>
                                <cy:uploadify class="col-lg-6 col-xs-6 col-sm-6 col-md-6" ng-show="picLength<5 && operate !='detail'"
                                              filelistid="fileListImgCaixin" filepickerid="filePickerImgCaixin" accepttype="accepttypeImgCaixin"
                                              uploadifyid="uploadifyidImgCaixin" validate="isValidateImgCaixin" filesize="filesizeImgCaixin"
                                              mimetypes="mimetypesImgCaixin" formdata="uploadParamImgCaixin" uploadurl="uploadurlImgCaixin" desc=""
                                              numlimit="numlimitImgCaixin" createthumbnail="isCreateThumbnailImgCaixin" style="float: left;">
                                </cy:uploadify>
                                <!-- 图片最大张数时展示的上传图片框 -->
                                <button ng-disabled="picLength>4" style="float:left;margin-right: 15px;margin-left:12px"
                                        ng-show="picLength>4" type="submit"
                                        class="btn btn-primary search-btn">
                                    <icon class="file-icon" style="margin-top: -4px;float: left;"></icon>
                                    <span style="float:left" ng-bind="'点击添加图片'|translate"></span>
                                </button>
                            </div>
                        </div>
                    </div>
            		<!-- 其他资质 -->
		            <div class="form-group" ng-if="isShowAdd">
		            	<div class="row">
		                <div style="overflow:hidden;margin:0px" class="row" ng-cloak>
		                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
		                        <icon>*</icon><span ng-bind="'ENTERPRISE_CERTIFICATEURLLIST'|translate"></span>：
		                    </label>
		                    <div style="display: table;" class="col-lg-6 col-xs-7 col-sm-7 col-md-7">
		                        <div class="ctn-pic-list" ng-repeat="item in colorContentAndFileList">
		                            <div class="pic-wrapper" ng-if="item.frameFileUrl">
		                                <a ng-click="item.frameFileUrl?exportFile(item.frameFileUrl):false"
		                                   title="{{item.filename}}" style="display: inline-block;width: 285px;{{operateType!='detail' ? 'overflow: hidden;' :''}};white-space: nowrap;text-overflow: ellipsis;"
		                                   ng-style="" class="ng-binding">
		                                    {{item.filename}}</a>
		                                <button ng-hide="operate=='detail'" ng-click="deleteCtnOrFile($index)"
		                                        type="submit" class="btn btn-primary search-btn "
		                                        style="position: absolute;left: 290px;top:50%;margin-top: -17px;">
		                                    <span ng-bind="'COMMON_DELETE'|translate"></span></button>
		                            </div>
		                        </div>
		                        <div ng-show="operate!='detail'" style="margin-left: 0;padding-left: 0;"
		                             class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview">
		                            <cy:uploadify ng-show="fileLength<6" ng-if="operate !='detail'" reset-err-tip="true"
		                                          filelistid="fileList2" filepickerid="filePicker_certi" accepttype="accepttype2"
		                                          uploadifyid="uploadifyid2" validate="isValidate2" filesize="filesize2"
		                                          mimetypes="mimetypes2" formdata="uploadParam2" uploadurl="uploadurl2" desc="uploadCertiDesc"
		                                          numlimit="numlimit2" createthumbnail="isCreateThumbnail2" style="float: left;">
		                            </cy:uploadify>
		                            <!-- 图片最大张数时展示的上传图片框(只用于展示，没有作用) -->
		                            <button  disabled style="float:left;margin-right: 15px;"
		                                    ng-show="fileLength>5" type="submit"
		                                    class="btn btn-primary search-btn">
		                                <span style="float:left" ng-bind="'上传文件'|translate"></span></button>
		                            <br>
		                            <div style="color: #c3c3c3;float:left" ng-show="fileLength>5" class="ng-binding">
                                        最多支持6张图片，仅支持jpg，bmp，png，jpeg格式
		                            </div>
									<div class="downloadRow col-sm-12">
										<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】号码使用声明书.docx" style="margin-left: -15px;"
											 ng-bind="'NUMBER_USE_TEMP_DOWNLOD'|translate"></a>
									</div>
<!--									<div class="downloadRow col-sm-12">-->
<!--										<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】品牌归属声明书.docx" style="margin-left: -15px;"-->
<!--											 ng-bind="'BRAND_ATTR_TEMP_DOWNLOD'|translate"></a>-->
<!--									</div>-->
									<div class="downloadRow col-sm-12">
										<a target="_blank" href="/qycy/ecpmp/assets/签名授权书-移动模板.docx" style="margin-left: -15px;"
											 ng-bind="'SIGN_AUTH_TEMP_DOWNLOD'|translate"></a>
									</div>
		                        </div>
		                    </div>
		                </div>
		                <div style="overflow:hidden;margin:0px" class="row">
		
		                </div>
						</div>
		            </div>
                </form>
            </div>
            <div class="modal-footer">
                <!--110迭代：807、808行的scene == 13、scene == 8后面的括号中取消isSensitive[2]|| 和 ||!contentListValidate-->
                <button type="submit" ng-if="operate!='detail'" class="btn btn-primary search-btn"
                        ng-disabled="(scene == 11 &&(!signatureValidate||!contentValidate||!contentValidateScreen||(signatureRequired == '1' && !signature && noSign == '1')||!content||colorContentAndFileList.length == 0)
                        ||scene == 9 &&(!signatureValidate||!contentValidateScreen||(signatureRequired == '1' && !signature && noSign == '1')||!content||colorContentAndFileList.length == 0)
                        ||scene == 10 &&(!signatureValidate||!contentValidateScreen||(signatureRequired == '1' && !signature && noSign == '1')||!content||colorContentAndFileList.length == 0)
                        ||scene == 13 &&(!contentTitleValidate||!contentTitle||contentList.length==0||ctnTextSumLength>750||allPicSize>286720||colorContentAndFileList.length == 0)
                        ||scene == 8 &&(!contentTitleValidate||!contentTitle||contentList.length==0||ctnTextSumLength>1000||allPicSize>2097152||colorContentAndFileList.length == 0)

                        )"

                        ng-click="diffNetAuthMaterialsConfirm()"><span
                    ng-bind="'COMMON_SAVE'|translate"></span></button>
                <button class="btn " data-dismiss="modal" aria-label="Close" ng-click="closeAdd()" ng-bind="'COMMON_CANCLE'|translate"></button>

            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="createTask" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div role="document" class="modal-dialog" style="width:732px">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">创建任务</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" name="myForm" novalidate>
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon style="color:red">*</icon>
                                <span ng-bind="'TASKNAME'|translate"></span>：
                            </label>
                            <div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
                                <input class="form-control"
                                       type="text" id="taskName2" name="taskName" ng-model="groupSendTaskInfo.taskName"
                                       placeholder="{{'INPUTTASKNAME2'|translate}}"
                                       ng-blur="checkTaskName(groupSendTaskInfo.taskName)"
                                       ng-class="{'redBorder':!taskNameValidate}"
                                       title={{groupSendTaskInfo.taskName}}
                                       autocomplete="off">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
                                     ng-show="!taskNameValidate">
                                <span class="redFont" ng-show="!taskNameValidate">{{'TASKNAMEDESC'|translate}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon style="color:red">*</icon>
                                <span >内容编号</span>：
                            </label>
                            <div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
                                <input class="form-control"
                                       type="" id="contentId" name="contentId" ng-model="groupSendTaskInfo.contentId"
                                       placeholder="请输入内容编号"
                                       ng-blur="checkTaskContentId(groupSendTaskInfo.contentId)"
                                       ng-disabled="true"
                                       ng-class="{'redBorder':!taskNameValidate}"
                                       title={{groupSendTaskInfo.contentId}}
                                       autocomplete="off">
                            </div>
                            <div class="col-lg-3 col-xs-3  col-sm-3 col-md-3">
									<span class="redFont" ng-show="validate.contentId_null" style="color:red;">
										请输入内容编号</span>
                                <span class="redFont" ng-show="validate.contentId_statue" style="color:red;">
										请输入内容审核状态为审核通过，状态为启用的的内容</span>
                                <span class="redFont" ng-show="validate.contentId_scene" style="color:red;">
										内容应用场景不存在</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="row">
                            <label for="" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                                <icon>*</icon>
                                <span>发送类型</span>：
                            </label>
                            <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                <label style="width: 73px;margin-top: 7px;"><input class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="taskType" ng-model="groupSendTaskInfo.taskType" type="radio" value="1"><span>立即发送</span></label>
                                <label style="width: 88px;margin-top: 7px;"><input class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="taskType" ng-model="groupSendTaskInfo.taskType" type="radio" value="2"><span>定时发送</span></label>
                            </div>
                        </div>
                    </div>


                    <div class="form-group" ng-show="operate !='detail'">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                                <span ng-bind="'RECEIVED'|translate"></span>：
                            </label>
                            <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                                <icon ng-show="groupSendTaskInfo.groupSendTaskMsisdn.length==0" ng-click="addMsisdn()"
                                      class="addMsinsdn">
                                </icon>
                                <div style="overflow:hidden;" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
                                    <input class="form-control" style="float:left;margin-bottom:11px"
                                           type="text"  id="{{'msisdn'+$index}}" name="{{'msisdn'+$index}}" ng-model="item.msisdn"
                                           placeholder="{{'ENTERPRISE_PLEASEINPUTRECEIVENUM'|translate}}"
                                           ng-blur="checkMsisdnList(groupSendTaskInfo.groupSendTaskMsisdn)"
                                           ng-disabled="operate =='detail'"
                                           title={{item.msisdn}}
                                           pattern="[0-9]*[0-9][0-9]*$"
                                           ng-class="{'redBorder':!msisdnListValidate}">
                                    </input>
                                </div>
                            </div>
                            <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
                                <div style="overflow:hidden;padding:1px 0px 10px 0px" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
                                    <icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length<50" ng-click="addMsisdn()"
                                          class="addMsinsdn">
                                    </icon>
                                    <icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length>=50" ng-click=""
                                          class="addMsinsdn">
                                    </icon>
                                    <icon ng-click="deleteMsisdn($index)"
                                          class="deleteMsinsdn">
                                    </icon>
                                </div>
                            </div>
                            <div  class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="padding:1px 0px 10px 0px"
                                  ng-show="(myForm['txtContent'+$index].$dirty && myForm['msisdn'+$index].$invalid) ||!msisdnListValidate"
                                  ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                <!-- <span ng-show="myForm['msisdn'+$index].$error.pattern" ng-bind="'RECEIVEDDESC'|translate"></span> -->
                                <span style="color:red;" class="redFont" ng-show="!msisdnListValidate">{{msisdnListDesc}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="operate !='detail'">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                                <span ng-bind="'COMMON_FILENAME'|translate"></span>：
                            </label>
                            <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                                <input type="text" class="form-control" ng-model="fileNameExcel"
                                       placeholder="请导入.xlsx,.xls格式文件" style="width: 100%;" ng-disabled="true"/>
                            </div>
                            <cy:uploadifyfile filelistid="fileListExcel" filepickerid="filePickerExcel" accepttype="accepttypeExcel"
                                              uploadifyid="uploadifyidExcel" validate="isValidateExcel" filesize="filesizeExcel"
                                              mimetypes="mimetypesExcel"
                                              formdata="uploadParamExcel" uploadurl="uploadurlExcel" desc="uploadDescExcel" numlimit="numlimitExcel"
                                              urllist="urlListExcel" createthumbnail="isCreateThumbnailExcel" auto="auto" fileUse="msidsnList"
                                              style="margin-left: 15px;float: left;">
                            </cy:uploadifyfile>
                            <div class="downloadRow" style="margin: 10px 0 0 29px;">
                                <a target="_blank" href="/qycy/ecpmp/assets/importMsisdnTemplate.xlsx" class="downMod"
                                   style="margin-right: 40px;"
                                   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="contentScene">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                                <!--<icon style="color:red">*</icon>-->
                                <span >端口号:</span>
                            </label>
                            <div class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="padding-top: 5px;">
                                <label style="	width: 300px;margin-right: 10px;font-size: 17px; text-align: left;" ng-repeat="item in ports">
                                    <input class="" style="width: 20px;height:15px;margin-right: 5px;"
                                           type="radio" ng-value="item" value="item" name="contentId" ng-model="groupSendTaskInfo.port"
                                           autocomplete="off">{{item}}</label>
                                <!--<span style="color: red" ng-show="!ports||ports.length==0">请联系管理员配置拓展码</span>-->

                            </div>

                        </div>
                    </div>
                    <div class="form-group" ng-show="groupSendTaskInfo.taskType != 1">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                                <span ng-bind="'SEND_TIME_DELAY'|translate"></span>：
                            </label>
                            <div class="col-lg-4 col-xs-4  col-sm-4 col-md-4 time" style="position: relative">
                                <input readonly="true" ng-disabled="operate =='detail'"
                                       id="datepicker"  autocomplete="off" type="text"  name="timingTime"
                                       ng-focus="openDate()"
                                       ng-model="groupSendTaskInfo.timingTimeTemp"  class="form-control" ng-class="{'datCursor':operate !='detail'}">
                                <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
                                <span style="color:red" ng-show="timeError">
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                    <!--<span ng-show="myForm.time.$error.required"-->
                                    <!--ng-bind="'CONTENT_PUSHTIMEREQUIRE'|translate"></span>-->
								<span ng-show="timeError" ng-bind="'CONTENT_TIMEERRORTIP'|translate"></span>
							</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn btn-primary search-btn" data-dismiss="modal"  ng-click="createTask()"
                        ng-disabled= "!groupSendTaskInfo.contentId
                        ||!groupSendTaskInfo.taskName
                        ||!taskNameValidate
                        ||validate.contentId_null
                        ||validate.contentId_statue
                        ||validate.contentId_scene
                        "
                        ng-bind="'COMMON_SAVE'|translate"></button>
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_CANCLE'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!--提交异网授权材料二次确认弹出框-->
<div class="modal fade" id="diffNetAuthMaterials" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
			</div>
			<div class="modal-body">
				<form class="form-horizontal">
					<div class="form-group">
						<div class="row" style="width: 400px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
							<div class="text-center">
								<span ng-bind="'DIFFNET_AUTH_MATERIALS'|translate"></span>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_UPLOADED'|translate"
                        ng-click="diffNetAuthMaterialsUploaded()"></button>
                <button type="submit" id="diffNetAuthMaterialsCancel" class="btn" data-dismiss="modal"
                        aria-label="Close" ng-bind="'COMMON_NOT_UPLOAD'|translate"></button>
			</div>
		</div>
	</div>
</div>
	
</body>

</html>