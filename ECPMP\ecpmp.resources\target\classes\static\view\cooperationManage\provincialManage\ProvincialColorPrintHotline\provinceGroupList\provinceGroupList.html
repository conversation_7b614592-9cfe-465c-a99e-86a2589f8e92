<!DOCTYPE html>
<html>

<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>分组管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link rel="stylesheet" type="text/css" href="../../../../../css/searchList.css"/>
	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
	<!-- 引入菜单组件 -->
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<!-- 引入分页组件 -->
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<link rel="stylesheet" type="text/css" href="../../../../../css/groupList.css"/>
	<script type="text/javascript" src="provinceGroupList.js"></script>
	<!-- 导入文件组件 -->
	<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
	<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datetimepicker.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datetimepicker.zh-CN.js"></script>

	<link href="../../../../../css/bootstrap-datetimepicker.css" rel="stylesheet" type="text/css" />
	<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
	<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
	<style>
		#filePicker div:nth-child(2) {
			width: 100% !important;
			height: 100% !important;
		}
		.delete .btn:focus,.delete .btn:hover,.delete .btn:active {outline: none; box-shadow: none;color: #ff2549;}
		.edit .btn:focus,.edit .btn:hover,.edit .btn:active {outline: none; box-shadow: none;color: #7360e2;}
		.impoMebr .btn:focus,.impoMebr .btn:hover,.impoMebr .btn:active  ,.batchDelMebr ,.batchDelMebr .btn:focus, .batchDelMebr .btn:hover, .batchDelMebr .btn:active{
			outline: none; box-shadow: none;color: #7360e2
		}
		.switch {
			display: table-cell;
		}
		.li-disabled{
			opacity: 0.5;
			cursor: not-allowed!important;
		}
		.handle ul li[disabled]{
			opacity: 0.5;
			cursor: not-allowed!important;
		}
		.handle ul li {
			margin-right: 7px;
		}
		.handle ul li icon {
			margin-right: 2px;
		}

	    .zyzq .tabtn-menu{

		 BACKGROUND: #7361e2;
		 padding: 4px 10px;
		 line-height: 38px;
		 height: 46px;
		 margin: 0;
		 color: white;
	    }
		.zyzq .tabtn-menu:nth-child(-n+3){
			background: #fd6f9a;
		}
		.zyzq .tabtn-menu:nth-child(3){
			margin-right: 50px;
		}
		.zyzq .cur-tabtn-menu {
			opacity: 0.6;
		}
	</style>
</head>

<body ng-app='myApp' ng-controller='groupListController' ng-init="init();" class="body-min-width">
	<div class="cooperation-manage" ng-show="isAllow">
		<div class="cooperation-head" ng-if="isSuperManager && enterpriseType =='1'">
			<span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>
		</div><!-- 管理员直客 -->
		<div class="cooperation-head" ng-if="isSuperManager && enterpriseType=='5'">
			<span class="frist-tab" ng-bind="'COMMON_PROENTERPRISE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-if="!(isZYZQ)" ng-bind="'CONTENTAUDIT_HOTLINE_PROVINCE'|translate"></span>
			<span class="second-tab" ng-if="isZYZQ" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></span>&nbsp;&gt;&nbsp;

			<span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>


		</div><!-- 管理员分省-->
		<div class="cooperation-head" ng-if="isSuperManager && enterpriseType=='3'">
			<span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>
		</div><!-- 管理员二级企业-->
		<div class="cooperation-head" ng-if="isZhike">
			<span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>
		</div><!-- 直客 -->
		<div class="cooperation-head" ng-if="isProvincial">
			<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-if="!(isZYZQ)" ng-bind="'CONTENTAUDIT_HOTLINE_PROVINCE'|translate"></span>
			<span class="second-tab" ng-if="isZYZQ" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>
		</div><!-- 分省-->
		<div class="cooperation-head" ng-if="isAgent">
			<span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_MEMBERMANAGE'|translate"></span>
		</div><!-- 代理商-->
		<top:menu chose-index="3" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList" list-index="10"
							ng-if="isSuperManager && enterpriseType =='1'"></top:menu><!-- 管理员直客 -->
		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList" list-index="11"
							ng-if="isSuperManager && enterpriseType =='3'"></top:menu><!-- 管理员二级企业 -->
		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
							list-index="11" ng-if="isZhike && enterpriseType =='1'"></top:menu><!-- 直客 -->
		<top:menu chose-index="{{choseIndex}}" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/ProvincialColorPrintHotline/provinceGroupList" list-index="56"
							apply-val="{{proSupServerType}}" ng-if="isSuperManager && enterpriseType =='5'"></top:menu><!-- 管理员分省一级-->

		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/ProvincialColorPrintHotline/provinceGroupList"
							list-index="57" ng-if="enterpriseType =='5' && !(isZYZQ && reserved4)" ng-class="{true:'',false:'second-topmenu'}[isProvincial]"></top:menu><!-- 分省一级-->
		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/ProvincialColorPrintHotline/provinceGroupList"
				  list-index="83" ng-if="enterpriseType =='5' && isZYZQ && reserved4" ng-class="{true:'',false:'second-topmenu zyzq'}[isProvincial]"></top:menu><!-- 分省一级-->
		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList" list-index="11"
							ng-if="isAgent"></top:menu><!-- 代理商 -->
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group form-inline">
					<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
						<label for="groupName" ng-bind="'GROUP_GROUPNAME'|translate" style="padding-top:6px"></label>
					</div>
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-input">
						<input autocomplete="off" type="text" class="form-control" id="queryOrgName"
									 placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
									 ng-model="queryOrgName">
					</div>
					<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-btn">
						<button ng-click="queryGroupList()" type="submit" class="btn search-btn">
							<icon class="search-iocn">
							</icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="addGroup()" ng-disabled="isAllowMangementMember==2">
				<icon class="add-iocn"></icon>
				<span ng-bind="'GROUP_ADDGROUP'|translate"></span>
			</button>
			<button type="submit" class="btn add-btn" ng-click="addMenbOutList()" ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2" ng-show="!allButton">
				<icon class="add-iocn"></icon>
				<span ng-bind="'GROUP_ADDMEMB'|translate"></span>
			</button>
			<button class="btn add-btn" ng-click="delMenbOutList()"
					ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2">
				<icon class="query-icon"></icon>
				<span ng-bind="'GROUP_EXPORTDELMEMBRECORDS'|translate"></span>
			</button>
		</div>
		<div style="margin-left:20px;margin-bottom:20px;">
			<p style="font-size:16px;font-weight:500;" ng-bind="'COMMON_SERVICEINFO'|translate"></p>
		</div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th>
						<div ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="enterpriseType!='3'"></div>
						<div ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate" ng-show="enterpriseType=='3'"></div>
					</th>
					<th ng-bind="'GROUP_ORGNAME'|translate"></th>
					<th style="width:10%" ng-bind="'COMMON_CREATETIME'|translate"></th>
					<th style="width:10%" ng-bind="'GROUP_ORGTYPE'|translate"></th>
					<th style="width:600px" ng-bind="'COMMON_OPERATE'|translate"></th>
				</tr>
				</thead>
				<tbody>

				<tr ng-repeat="item in groupListData">
					<td><span title="{{enterpriseName}}">{{enterpriseName}}</span></td>
					<td><span title="{{item.orgName}}">{{item.orgName}}</span></td>
					<td><span title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</span></td>
					<td title="{{orgTypeMap[item.orgType]}}">{{orgTypeMap[item.orgType]}}</td>
					<td>
						<div class="handle">
							<ul>
								<li class="set" ng-click="gotoSet(item)">
									<icon class="set-icon"></icon>
									<span ng-bind="'GROUP_EDIT'|translate"></span>
								</li>
								<li class="delete" ng-show="!item.isRemove">
									<button class="btn add-btn" ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2" ng-click="gotoDelete(item)"  style="padding: 0;" >
									<icon class="delete-icon"></icon>
									<span ng-bind="'COMMON_DELETE'|translate"></span>
									</button>
								</li>
								<li class="delete" ng-show="item.isRemove">
									<button class="btn add-btn" ng-disabled="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType==5" ng-click="checkResult(item)" style="padding: 0;">
									<icon class="delete-icon"></icon>
									<span ng-bind="'CHECK_RESULT'|translate"></span>
									</button>
								</li>
								<li class="query" ng-click="gotoDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'GROUP_SEARCHMEMB'|translate"></span>
								</li>
								<li class="edit">
									<button class="btn add-btn" ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2" ng-click="allButton?'':addMenbInList(item)" style="padding: 0;" ng-class="{true:'',false:'li-disabled'}[!allButton]">
									<icon class="addNumber-icon"></icon>
									<span ng-bind="'GROUP_ADDMEMB'|translate"></span>
									</button>
								</li>
								<li class="impoMebr">
									<button class="btn add-btn" ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2" ng-click="allButton?'':impoMebr(item)"  style="padding: 0;" ng-class="{true:'',false:'li-disabled'}[!allButton]">
									<icon class="import-icon"></icon>
									<span ng-bind="'GROUP_IPTMEMB'|translate"></span>
									</button>
								</li>
								<li class="batchDelMebr">
									<!--                                i.	若当前企业不为杭研（当前企业不为指定外部接入子企业，只针对子企业管理）、咪咕音乐（当前企业信息enterpriseType=5，且reserved10=113，只针对分省企业管理），且同步业务规则列表中存在reserved2不为1（不允许成员操作，只针对分省企业管理）-->
									<button class="btn add-btn"
											ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2"
											ng-click="batchDelButton?'':impoDelMebr(item)" style="padding: 0;" ng-class="{true:'',false:'li-disabled'}[!batchDelButton]">
										<icon class="import-icon"></icon>
										<span ng-bind="'GROUP_IPTDELMEMB'|translate"></span>
									</button>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="groupListData.length<=0">
					<td style="text-align:center" colspan="5" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="queryGroupList('justPage')"></ptl-page>
		</div>
	</div>

	<div class="modal fade" id="bussinessNo" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    	<div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p class="tip ng-binding" ng-bind="'BUSSINESS_NOT_ALLOW'|translate">
                            </p></div>
                        </div>
                        <div class="modal-footer" style="text-align:center">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
     </div>
	<!--新增分组弹出框-->
	<div class="modal fade" id="addGroupPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_ADDGROUP'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<div class="row">
								<label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"><icon>*</icon><span
												ng-bind="'GROUP_GROUPNAME'|translate"></span>：</label>
								<div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
									<input type="text" class="form-control" ng-model="addGroupInfo.orgName" id="addGroupName"
												 placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
												 ng-blur="checkDataUnique(addGroupInfo.orgName,3,'')"
												 ng-show="groupNameVali && !groupNameExist"/>
									<input type="text" class="form-control" ng-model="addGroupInfo.orgName" id="addGroupName"
												 placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
												 ng-blur="checkDataUnique(addGroupInfo.orgName,3,'')"
												 style="border-color:red;" ng-show="!groupNameVali || groupNameExist"/>
									<span style="color:red" ng-show="!groupNameVali || groupNameExist">
										<img src="../../../../../assets/images/reject-icon.png" width="18" height="18"
												 align="absmiddle">
										<span>{{groupNameDesc|translate}}</span>
									</span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn"
									ng-click="checkDataUnique(addGroupInfo.orgName,3,'create')"
									ng-disabled="!groupNameVali || groupNameExist || !addGroupInfo.orgName"
									ng-bind="'COMMON_OK'|translate"></button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addOrgCancel"
									ng-bind="'COMMON_BACK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	<!--删除弹出框-->
	<div class="modal fade" id="deleteGroupPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
								<div class="text-center">
									<p ng-bind="'GROUP_SUREDELETEGROUP'|translate"></p>
									<p style="color:red;margin-top:15px;" ng-bind="'GROUP_SUREDELETEGROUP2'|translate"></p>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn" ng-click="removeOrg()"
									ng-bind="'COMMON_OK'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="delOrgCancel"
									ng-bind="'COMMON_BACK'|translate"></button>
				</div>
			</div>
		</div>
	</div>


	<!--编辑弹出框-->
	<div class="modal fade" id="setGroupPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_EDITGROUP'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<div class="row">
								<label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label">
									<span ng-bind="'GROUP_GROUPNAME'|translate"></span>：
								</label>
								<div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
									<input type="text" class="form-control" ng-model="setGroupInfo.orgName" id="setGroupName addGroupName"
												 placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
												 ng-blur="checkDataUnique(setGroupInfo.orgName,4,'')"
												 ng-show="groupNameVali && !groupNameExist"/>
									<input type="text" class="form-control" ng-model="setGroupInfo.orgName" id="setGroupName addGroupName"
												 placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
												 ng-blur="checkDataUnique(setGroupInfo.orgName,4,'')"
												 style="border-color:red;" ng-show="!groupNameVali || groupNameExist"/>
									<span style="color:red" ng-show="!groupNameVali || groupNameExist">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
												 align="absmiddle">
										<span>{{groupNameDesc|translate}}</span>
									</span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn"
									ng-click="checkDataUnique(setGroupInfo.orgName,4,'create')"
									ng-disabled="!groupNameVali || groupNameExist || !setGroupInfo.orgName || setGroupInfo.orgName ==groupNameTemp"
									ng-bind="'COMMON_SAVE'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="setOrgCancel"
									ng-bind="'COMMON_BACK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	<!--导入成员弹出框-->
	<div class="modal fade" id="impoMebrPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_IPTMEMB'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;"
										 ng-bind="'COMMON_FILENAME'|translate"></label>
							<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
								<input type="text" class="form-control" ng-model="fileName" id="addGroupName"
											 placeholder="请导入.xlsx表格格式文件" style="width: 100%;" ng-disabled="true"/>
								<!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
							</div>
							<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
																uploadifyid="uploadifyid" validate="isValidate" filesize="filesize"
																mimetypes="mimetypes"
																formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
																urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto"
																style="margin-left: 15px;float: left;">
							</cy:uploadifyfile>
						</div>
						<div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 110px;" ng-show="errorInfo===''"></div>
						<div style="color:#ff0000;margin: 10px 0 0 110px;" ng-show="errorInfo!==''">
							<span class="uplodify-error-img"></span>
							<span ng-bind="errorInfo|translate"></span>
						</div>
						<div class="downloadRow" style="margin: 10px 0 0 29px;">
							<a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate3.xlsx" class="downMod"
								 style="margin-right: 40px;"
								 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
							     ng-show="enterpriseType == 5 && !isZYZQ"></a>
							<a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate.xlsx" class="downMod"
							   style="margin-right: 40px;"
							   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
							   ng-show="enterpriseType != 5 && !isZYZQ"></a>
							<a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate4.xlsx" class="downMod"
							   style="margin-right: 40px;"
							   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
							   ng-show="isZYZQ && provinceId != 12"></a>
							<a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate5.xlsx" class="downMod"
							   style="margin-right: 40px;"
							   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
							   ng-show="isZYZQ && provinceId == 12"></a>   
							<span style="color: #705de1 !important; font-size: 12px;" ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn" ng-click="importMember()" ng-disabled="errorInfo!==''||fileUrl==''">确认导入</button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="addOrgCancel">返回</button>
				</div>
			</div>
		</div>
	</div>

	<!--导入删除成员弹出框-->
	<div class="modal fade" id="impoDelMebrPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_IPTDELMEMB'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;">
								<icon>*</icon>
								<span ng-bind="'COMMON_FILENAME'|translate"></span>
							</label>
							<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
								<input type="text" class="form-control" ng-model="fileName" id="addGroupName"
									   placeholder="必填，仅支持xlsx格式" style="width: 100%;" ng-disabled="true"/>
								<!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
							</div>
							<cy:uploadifyfile filelistid="fileList2" filepickerid="filePicker2" accepttype="accepttype"
											  uploadifyid="uploadifyid2" validate="isValidate" filesize="filesize"
											  mimetypes="mimetypes"
											  formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
											  urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto"
											  style="margin-left: 15px;float: left;">
							</cy:uploadifyfile>
						</div>
						<div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 110px;" ng-show="errorInfo===''"></div>
						<div style="color:#ff0000;margin: 10px 0 0 110px;" ng-show="errorInfo!==''">
							<span class="uplodify-error-img"></span>
							<span ng-bind="errorInfo|translate"></span>
						</div>
						<div class="downloadRow" style="margin: 10px 0 0 29px;">
							<a target="_blank" href="/qycy/ecpmp/assets/deleteMemberTemplate.xlsx" class="downMod"
							   style="margin-right: 40px;"
							   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
							<span style="color: #705de1 !important; font-size: 12px;"
								  ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn" ng-click="importDelMember()"
							ng-disabled="errorInfo!==''||fileUrl==''">确认导入
					</button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addOrgCancel">返回</button>
				</div>
			</div>
		</div>
	</div>


	<!--c)	导出成员删除记录弹窗-->
	<div class="modal fade" id="detailDelFailListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div role="document" class="modal-dialog dialog-1000">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="mydelModalLabel" ng-bind="'GROUP_EXPORTDELMEMBRECORDS'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="form-horizontal">
						<div class="form-group">
							<div class="row">
								<label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
									   ng-bind="'GROUP_NEWMEMBMSISDN'|translate"></label>
								<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
									<input type="text" class="form-control" id=""
										   placeholder="{{'GROUP_PLEASEINPUTMEMBMSISDN'|translate}}"
										   ng-model="initSel.msisdn">
								</div>
								<label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
									   ng-bind="'GROUP_ORGNAME'|translate"></label>
								<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
									<input type="text" class="form-control" id=""
										   placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
										   ng-model="groupName">
								</div>
								<label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
									   ng-bind="'COMMON_CREATETIME'|translate"></label>
								<div class="col-lg-4 col-md-3 col-sm-3 col-xs-3 time" style="margin-right: 5px">
									<div class="input-daterange input-group" id="datepicker">
										<input type="text" class="input-md form-control" autocomplete="off" id="start" style="padding: 2px"
											   ng-keyup="searchOn()"/>
										<span class="input-group-addon" ng-bind="'TO'|translate"></span>
										<input type="text" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
									</div>
								</div>
								<!--                            退订时间-->
								<label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
									   ng-bind="'UNSUB_TIME_COUNT'|translate"></label>
								<div class="col-lg-4 col-md-3 col-sm-3 col-xs-3 time" style="margin-top:5px">
									<div class="input-daterange input-group" id="datepicker">
										<input type="text" class="input-md form-control" autocomplete="off" id="unsubStart" style="padding: 2px"
											   ng-keyup="unsubSearchOn()"/>
										<span class="input-group-addon" ng-bind="'TO'|translate"></span>
										<input type="text" class="input-md form-control" autocomplete="off" id="unsubEnd" ng-keyup="unsubSearchOn()"/>
									</div>
								</div>
								<!--                            退订状态-->
								<label class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
									   style="white-space:nowrap" ng-bind="'UNSUB_STATUS'|translate"></label>
								<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top:5px">
									<select class="form-control" ng-model="initSel.unsubStatus"
											ng-options="x.id as x.name for x in servTypeStatus">
									</select>
								</div>
								<!--                            退订渠道-->
								<label class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
									   style="white-space:nowrap" ng-bind="'UNSUB_CHANNEL'|translate"></label>
								<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top:5px">
									<select class="form-control" ng-model="initSel.channel"
											ng-options="x.id as x.name for x in servTypeChannel">
									</select>
								</div>
								<!--                          搜索、 导出-->
								<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top:5px">
									<button class="btn bg_purple search-btn btn1" ng-click="queryDelFailMemberList()">
										<span class="icon btnIcon search"></span>
										<span ng-bind="'COMMON_SEARCH'|translate"></span>
									</button>
									<button class="btn bg_purple search-btn btn1" ng-click="exportUnsubFailBatch()">
										<span class="icon btnIcon search"></span>
										<span ng-bind="'DETAIL_EXPORT'|translate"></span>
									</button>
								</div>
							</div>
						</div>
					</div>

					<div class="coorPeration-table">
						<table class="table table-striped table-hover">
							<thead>
							<tr>
								<!--                            <th><span title="序号">序号</span></th>-->
								<th><span title="分组名称">成员号码</span></th>
								<th><span title="分组名称">分组名称</span></th>
								<th><span title="创建日期">创建时间</span></th>
								<th><span title="创建日期">退订时间</span></th>
								<th><span title="创建日期">退订状态</span></th>
								<th><span title="创建日期">退订渠道</span></th>
								<th><span title="创建日期">失败原因</span></th>
								<th><span title="创建日期">错误码</span></th>
								<!--                            <th><span title="操作">操作</span></th>-->
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="item in unsubFailBatchList">
								<!--                            <td>{{$index + 1}}</td>-->
								<td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
								<td><span title="{{item.orgName}}">{{item.orgName}}</span></td>
								<td><span title="{{changeTimeFormtat(item.subTime)}}">{{changeTimeFormtat(item.subTime)}}</span></td>
								<td><span title="{{changeTimeFormtat(item.unsubTime)}}">{{changeTimeFormtat(item.unsubTime)}}</span></td>
								<td><span title="{{unsubStatusMap[item.unsubStatus]}}">{{unsubStatusMap[item.unsubStatus]}}</span></td>
								<td><span title="{{channelStatusMap[item.channel]}}">{{channelStatusMap[item.channel]}}</span></td>
								<td><span title="{{(item.resultDesc && item.resultDesc != '0') ? item.resultDesc : '-'}}">{{(item.resultDesc && item.resultDesc != '0'&& item.unsubStatus != '1') ? item.resultDesc : '-'}}</span></td>
								<td><span title="{{(item.resultCode && item.resultCode != '0') ? item.resultCode : '-'}}">{{(item.resultCode && item.resultCode != '0'&& item.unsubStatus != '1') ? item.resultCode : '-'}}</span></td>
								<!--                            <td>-->
								<!--                                <button class="btn bg_purple search-btn btn1" ng-click="exportUnsubFailBatch(item)">导出</button>-->
								<!--                            </td>-->
							</tr>
							<tr ng-show="unsubFailBatchList.length === 0">
								<td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
							</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="modal-footer">
					<ptl-page tableId="1" change="queryDelFailMemberList('justPage')"></ptl-page>
				</div>
			</div>
		</div>
		<div class="modal-footer">
		</div>
	</div>
	<!--新增成员弹出框-->
	<div class="modal fade" id="addMenbPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_ADDMEMB'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">

						<div class="form-group">
							<div class="row">
								<label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
									<span ng-bind="'GROUP_MEMBNAME'|translate"></span>：</label>
								<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
									<input type="text" class="form-control" ng-model="addMenbInfo.memberName"
												 placeholder="{{'GROUP_PLEASEINPUTMEMBNAME'|translate}}"
												 ng-blur="checkMemberName(addMenbInfo.memberName)"
												 ng-show="memberNameVali"/>
									<input type="text" class="form-control" ng-model="addMenbInfo.memberName"
												 placeholder="{{'GROUP_PLEASEINPUTMEMBNAME'|translate}}"
												 ng-blur="checkMemberName(addMenbInfo.memberName)"
												 style="border-color:red;" ng-show="!memberNameVali"/>
									<span style="color:red" ng-show="!memberNameVali">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
												 align="absmiddle">
										<span>{{memberNameDesc|translate}}</span>
									</span>
								</div>
							</div>
						</div>
						<!-- 号码类型 -->
						<div class="form-group" ng-show="enterpriseType == 5">
							<div class="row">
								<label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
									<icon>*</icon><span ng-bind="'GROUP_MSISDNTYPE'|translate"></span>：</label>
								<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
									<select class="form-control"
											id="msisdnType1"
											ng-model="selectedId"
											ng-options="x.id as x.msisdnType for x in msisdnTypeList"
											ng-click="selectedType(selectedId)"
											ng-change="numberCorrespondingOperator()">
										<option value="" ng-bind="'COMMON_SELECT'|translate"></option>
									</select>
									<span style="color:red;" ng-show="checkMsisdnType == 'false'">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
											 align="absmiddle">
										<span ng-bind="'GROUP_MSISDNTYPESELECT'|translate"></span>
									</span>
								</div>
							</div>
						</div>

						<div class="form-group">
							<div class="row">
								<label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
									<icon>*</icon><span ng-bind="'GROUP_NEWMEMBMSISDN'|translate"></span>：</label>
								<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8" ng-show="selectedMsisdnID == 3||selectedMsisdnID == 9">
									<div style="display: inline-block;width: 100px;" >
										<input type="text" class="form-control" ng-model="areaCode"
											   placeholder="{{selectedMsisdnID == 3?'GROUP_PLEASEINPUTNEWMEMBAREACODE':'GROUP_PLEASEINPUTNEWMEMBAREACODE2'|translate}}"
											   maxlength="{{selectedMsisdnID == 3?4:5}}"
											   ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')"
											   ng-show="memberMsisdnValiCode && !memberMsisdnExistCode"/>
										<input type="text" class="form-control" ng-model="areaCode"
											   placeholder="{{selectedMsisdnID == 3?'GROUP_PLEASEINPUTNEWMEMBAREACODE':'GROUP_PLEASEINPUTNEWMEMBAREACODE2'|translate}}"
											   maxlength="{{selectedMsisdnID == 3?4:5}}"
											   ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')" style="border-color:red;"
											   ng-show="!memberMsisdnValiCode || memberMsisdnExistCode"/>
									</div>
									<label style="display: inline-block;width: 15px;text-align: center;">-</label>
									<div style="display: inline-block;width: 168px;" >
										<input type="text" class="form-control" ng-model="addMenbInfo.msisdn"
											   placeholder="{{'GROUP_PLEASEINPUTNEWMEMBMSISDN_1'|translate}}"
											   ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')"
											   ng-show="memberMsisdnVali && !memberMsisdnExist"/>
										<input type="text" class="form-control" ng-model="addMenbInfo.msisdn"
											   placeholder="{{'GROUP_PLEASEINPUTNEWMEMBMSISDN_1'|translate}}"
											   ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')" style="border-color:red;"
											   ng-show="!memberMsisdnVali || memberMsisdnExist"/>
									</div>
									<span style="float: left;color:red" ng-show="!memberMsisdnVali || memberMsisdnExist || !memberMsisdnValiCode || memberMsisdnExistCode">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
											 align="absmiddle">
										<span>{{memberMsisdnDesc|translate}}</span>
									</span>
								</div>
								<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8" ng-show="selectedMsisdnID != 3 && selectedMsisdnID != 9">
									<input type="text" class="form-control" ng-model="addMenbInfo.msisdn"
										   placeholder="{{'GROUP_PLEASEINPUTNEWMEMBMSISDN'|translate}}"
										   ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')"
										   ng-show="memberMsisdnVali && !memberMsisdnExist"/>
									<input type="text" class="form-control" ng-model="addMenbInfo.msisdn"
										   placeholder="{{'GROUP_PLEASEINPUTNEWMEMBMSISDN'|translate}}"
										   ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')" style="border-color:red;"
										   ng-show="!memberMsisdnVali || memberMsisdnExist"/>
									<span style="color:red" ng-show="!memberMsisdnVali || memberMsisdnExist">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
											 align="absmiddle">
										<span>{{memberMsisdnDesc|translate}}</span>
									</span>
								</div>
							</div>
						</div>

						<div class="form-group">
							<div class="row">
								<label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
									<icon>*</icon><span ng-bind="'GROUP_MEMBORG'|translate"></span>：</label>
								<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
									<select class="form-control" ng-model="addMenbInfo.selectedOrg"
													title="{{addMenbInfo.selectedOrg.orgName}}"
													ng-options="x.orgName for x in groupListType"
													ng-disabled="fromList =='true'" ng-blur="checkSelect()"
													ng-show="orgSelected =='true'" id="addMenbSelect1">
										<option value="" ng-bind="'COMMON_SELECT'|translate"></option>
									</select>
									<select class="form-control" ng-model="addMenbInfo.selectedOrg"
													title="{{addMenbInfo.selectedOrg.orgName}}"
													ng-options="x.orgName for x in groupListType"
													ng-disabled="fromList =='true'" ng-blur="checkSelect()"
													style="border-color:red;"
													ng-show="orgSelected =='false'" id="addMenbSelect2">
										<option value="" ng-bind="'COMMON_SELECT'|translate"></option>
									</select>
									<span style="color:red;" ng-show="orgSelected =='false'">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
												 align="absmiddle">
										<span ng-bind="'GROUP_MEMBORGSELECT'|translate"></span>
									</span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn"
							        ng-show="enterpriseType != 5"
									ng-click="checkDataUnique(addMenbInfo.msisdn,2,'create')"
									ng-disabled="!addMenbInfo.msisdn || !addMenbInfo.selectedOrg || !memberMsisdnVali
									|| !memberNameVali || memberMsisdnExist"
									ng-bind="'COMMON_SAVE'|translate"></button>

					<button type="submit" class="btn btn-primary search-btn"
							ng-show="enterpriseType == 5"
							ng-click="checkDataUnique(addMenbInfo.msisdn,2,'create')"
							ng-disabled="!addMenbInfo.msisdn || !addMenbInfo.selectedOrg || !memberMsisdnVali || !memberMsisdnValiCode
									|| !memberNameVali || memberMsisdnExist || memberMsisdnExistCode || !selectedMsisdnID"
							ng-bind="'COMMON_SAVE'|translate"></button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addMemCancel"
									ng-bind="'COMMON_CANCLE'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	<!--成员查询弹出框-->
	<div class="modal fade" id="detailListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div role="document" class="modal-dialog dialog-1000">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_MEMBSEARCH'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="form-horizontal">
						<div class="form-group">
							<div class="row">
								<label class="col-lg-1 col-xs-3 col-sm-3 col-md-2 control-label"
											 ng-bind="'GROUP_MEMBMSISDN'|translate"></label>
								<div class="col-lg-5 col-xs-6 col-sm-6 col-md-3">
									<input type="text" class="form-control" id=""
												 placeholder="{{'GROUP_PLEASEINPUTMEMBMSISDN'|translate}}"
												 ng-model="msisdn">
								</div>
								<label class="col-lg-1 col-xs-3 col-sm-3 col-md-1 control-label"
											 ng-bind="'GROUP_STATUS'|translate" ng-show="enterpriseType == 5 && isZYZQ"></label>
								<select class="col-lg-3 col-xs-3 col-sm-3 col-md-1 form-control"
											style="width: 150px;"
											id="msisdnType1"
											ng-model="status"
											ng-options="x.id as x.name for x in statusChoise"
											ng-show="enterpriseType == 5 && isZYZQ">
									 <option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
								</select>
								<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
									<button class="btn bg_purple search-btn btn1" ng-click="queryMemberList(selectedItem)">
										<span class="icon btnIcon search"></span>
										<span ng-bind="'COMMON_SEARCH'|translate"></span>
									</button>
								</div>
							</div>
						</div>
					</div>


					<div class="add-table" style="margin-left:12px;margin-top:12px;">
						<button class="btn" style="width:105px;color:#7360e1" type="button" ng-click="exportMember()"
								ng-bind="'GROUP_MEMBEXPORT'|translate"></button>
						<button class="btn" ng-disabled="selectedListTemp.length==0 || isAllowMangementMember==1||isAllowMangementMember==2 "
										style="width:105px; margin-left:10px;color:#7360e1"
										type="button" ng-click="removeMemberPop('all')" ng-bind="'GROUP_BATCHDELETE'|translate"></button>
						<button class="btn" ng-disabled="selectedListTemp.length==0 || (isAllowMoveMember && enterpriseType == 5) "
								style="width:105px; margin-left:10px;color:#7360e1"
								type="button" ng-click="moveMemberPop('all')" ng-bind="'GROUP_BATCHMOVE'|translate"></button>
					    <button ng-if="isAllowMangementMember == 0 && enterpriseType == 5 && !isZYZQ" class="btn" style="width:105px;color:#7360e1" type="button"
                            ng-click="exportFaildList()"
                            ng-bind="'FAILDLIST_EXPORT'|translate"></button>
						<button ng-if="enterpriseType == 5 && isZYZQ" ng-disabled="selectedListTemp.length==0" class="btn" style="width:105px;color:#7360e1" type="button" ng-click="bsync('all')"
								ng-bind="'BATCH_GROUP_SYNCMEMB'|translate"></button>
					</div>

					<div class="coorPeration-table" style="max-height: 530px;overflow: auto">
						<table class="table table-striped table-hover">
							<thead>
							<tr>
								<th style="padding-left:30px;width: 10%;"><input type="checkbox" ng-model="allChoose" ng-click="ifSelected()"></th>
								<th style="width: 22%;" ng-bind="'GROUP_MEMBNAME'|translate"></th>
								<th ng-bind="'GROUP_MEMBMSISDN'|translate"></th>
								<th ng-bind="'GROUP_MSISDNTYPE'|translate" ng-show="enterpriseType == 5"></th>
								<th ng-bind="'GROUP_STATUS'|translate"></th>
								<th ng-bind="'GROUP_RSPDESC'|translate" ng-show="enterpriseType == 5 && isZYZQ"></th>
								<th style="width: 23%;" ng-bind="'COMMON_OPERATE'|translate"></th>
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="item in memberListData">
								<td><input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked" ng-disabled="item.status == 1 || item.status == 9 || ((item.status == 12 || item.status == 11) && item.rspDesc != '接口响应超时')"></td>
								<td><span title="{{item.memberName}}">{{item.memberName}}</span></td>
								<td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
								<td ng-show="enterpriseType == 5"><span title="{{item.ecpmReserveds.reserved2}}">{{item.ecpmReserveds.reserved2}}</span></td>
								<td><span title="{{statusMap[item.status]}}">{{statusMap[item.status]}}</span></td>
								<td ng-show="enterpriseType == 5 && isZYZQ"><span ng-show="item.ecpmReserveds.reserved2!='SIP语音' && item.ecpmReserveds.reserved2!='中间号' && item.ecpmReserveds.reserved2!='点击拨号' && item.ecpmReserveds.reserved2!='语音通知'" title="{{item.rspDesc}}">{{item.rspDesc}}</span></td>
								<td>
									<div class="handle">
										<ul>
											<li class="removeMemP" ng-click="isAllowMangementMember==1||isAllowMangementMember==2 || removeMemberPop(item)" ng-if="item.status != 1 && item.status != 9 && item.status != 12 &&  item.status != 11 && item.status != 13" ng-bind="'COMMON_DELETE'|translate" ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2"></li>
											<li class="removeMemP" ng-click="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5 || removeMemberPop(item)" ng-if="item.status == 13" ng-bind="'COMMON_DELETE_AGAIN'|translate" ng-disabled="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5"></li>
											<li class="moveMemP" ng-click="isAllowMoveMember && enterpriseType == 5 || moveMemberPop(item)"  ng-if="item.status == 3" ng-bind="'COMMON_MOVE'|translate" ng-disabled="isAllowMoveMember&& enterpriseType == 5"></li>
											<li class="sync" ng-click="sync(item)" ng-bind="'GROUP_SYNCMEMB'|translate" ng-if="!(enterpriseType == 5 && isZYZQ) && (item.status ==2 || item.status ==4 || item.status ==8)"></li>
											<li class="bsync" ng-click="bsync(item)" ng-bind="'GROUP_SYNCMEMB'|translate" ng-if="enterpriseType == 5 && isZYZQ && (item.status ==2 || item.status ==4 || item.status ==8)"></li>
											<li class="bsync" ng-click="bbsync(item)" ng-bind="'GROUP_SYNCMEMB'|translate" ng-if="enterpriseType == 5 && isZYZQ && (item.status ==11 && item.rspDesc=='接口响应超时')"></li>
											<li class="bsync" ng-click="bbsync(item)" ng-bind="'GROUP_SYNCMEMB_UNSUBSCRIBE'|translate" ng-if="enterpriseType == 5 && isZYZQ && (item.status ==12 && item.rspDesc=='接口响应超时')"></li>
										</ul>
								</td>
								<!--<td style="font-size: small;">-->
									<!--<button class="btn add-btn" ng-click="removeMemberPop(item)" ng-bind="'COMMON_DELETE'|translate" ng-disabled="isAllowMangementMember==1||isAllowMangementMember==2"-->
									<!--&gt;-->
									<!--</button>-->
									<!--<button ng-click="moveMemberPop(item)"  ng-if="item.status == 3" class="btn add-btn" ng-bind="'COMMON_MOVE'|translate" ng-disabled="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5" >-->

									<!--</button>-->
									<!--<button class="btn add-btn" ng-click="sync(item)" ng-bind="'GROUP_SYNCMEMB'|translate"-->
										<!--ng-if="item.status ==2 || item.status ==4">-->
									<!--</button>-->
								<!--</td>-->
							</tr>
							<tr ng-show="memberListData.length<=0">
								<td style="text-align:center" colspan="7" ng-bind="'COMMON_NODATA'|translate"></td>
							</tr>
							</tbody>
						</table>
					</div>
					<div>
						<ptl-page tableId="1" change="queryMemberList(selectedItem,'justPage')"></ptl-page>
					</div>
				</div>
				<div class="modal-footer">
				</div>
			</div>
		</div>
	</div>

<!--成员删除确认框弹出框-->
	<div class="modal fade" id="deleteMemberPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
								<div class="text-center">
									<span >请确认是否删除分组成员</span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn" ng-click="oneSelect?remove('one'):remove('all')"
									ng-bind="'COMMON_OK'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="delMemCancel"
									ng-bind="'COMMON_BACK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	<!--成员移动框弹出框-->
	<div class="modal fade" id="moveMemberPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width: 400px; margin: 0 auto;">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title"  ng-bind="'COMMON_TIPMOVE'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div style="text-align: center;">
							<span>成员分组</span>
							<select class="form-control" ng-model="selectedOrg"
									title="{{addMenbInfo.selectedOrg.orgName}}"
									ng-options="x.orgName for x in groupListType"
									ng-disabled="" ng-blur="checkSelect()" style="width: 250px; display: inline;" >
								<option value="" ng-bind="'COMMON_SELECT'|translate"></option>
							</select>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn" ng-click="oneSelect?move('one'):move('all')"
							ng-bind="'COMMON_OK'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="delMemCancel"
							ng-bind="'COMMON_BACK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

	<!--小弹出框-->
	<!-- <button type="button" class="btn btn-primary" id="open-model" data-toggle="modal" data-target=".bs-example-modal-sm">Large modal</button> -->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p>
					</div>
				</div>
				<div class="modal-footer">
					<!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

    <!-- 导入失败弹窗-->
	<div class="modal fade bs-example-modal-sm" id="importResultModel" tabindex="-1" role="dialog" aria-labelledby="importResultLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="importResultLabel" ng-bind="'GROUP_IPTMBRRESULT'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 16px;color:#6E6E6E'>
							{{tip|translate}}
						</p>
					</div>
				</div>
				<div class="modal-footer">
					<button id="downloadFail" class="btn btn-primary search-btn"
							ng-click="downloadFailFile()"
							ng-bind="'GROUP_DOWNLOADFAILFILE'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_CLOSE'|translate"></button>
				</div>
			</div>
		</div>
	</div>
</body>


</html>