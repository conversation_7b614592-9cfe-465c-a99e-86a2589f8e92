<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MHWhiteMapper">
    <resultMap id="MHWhiteWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteWrapper">
        <result property="whitePhoneId" column="whitePhoneId"/>
        <result property="commandId" column="commandId"/>
        <result property="whiteType" column="whiteType"/>
        <result property="whiteContent" column="whiteContent"/>
        <result property="province" column="province"/>
		<result property="startTime" column="startTime"/>
		<result property="endTime" column="endTime" />
		<result property="status" column="status"/>
		<result property="objectType" column="objectType"/>
		<result property="createTime" column="createTime"/>
		<result property="updateTime" column="updateTime" />
		<result property="checkFailReason" column="checkFailReason" />
	</resultMap>

    <select id="selectByMsisdn" resultMap="MHWhiteWrapper">
		SELECT
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime
		FROM
			ecpm_t_mhwhite
		WHERE
			whiteContent = #{whiteContent}
	</select>
	<insert id="insertMHWhiteHistory">
		INSERT into ecpm_t_mhwhite_history (whitePhoneId,commandId,whiteType,whiteContent,province,startTime,endTime,STATUS,objectType,createTime,updateTime) SELECT
		   whitePhoneId,
		   commandId,
		   whiteType,
		   whiteContent,
		   province,
		   startTime,
		   endTime,
		   STATUS,
		   objectType,
		   createTime,
		   updateTime
		FROM
			ecpm_t_mhwhite
		WHERE
			whiteContent = #{whiteContent}
	</insert>

	<insert id="insertMHWhiteHistory2">
		INSERT INTO ecpm_t_mhwhite_history
		(
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime
		)
		VALUES
		(
			#{whitePhoneId},
			#{commandId},
			#{whiteType},
			#{whiteContent},
			#{province},
			#{startTime},
			#{endTime},
			#{status},
			#{objectType},
			#{createTime},
			#{updateTime}
		)
	</insert>

	<insert id="insertMHWhite">
		INSERT INTO ecpm_t_mhwhite
		(
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime,
			checkFailReason
		)
		VALUES
		(
			#{whitePhoneId},
			#{commandId},
			#{whiteType},
			#{whiteContent},
			#{province},
			#{startTime},
			#{endTime},
			#{status},
			#{objectType},
			#{createTime},
			#{updateTime},
		    #{checkFailReason}
		)
	</insert>

	<delete id="deleteMHWhiteByWhiteContent">
		delete from ecpm_t_mhwhite where whiteContent = #{whiteContent}
	</delete>

	<select id="selectCreateMember" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteWrapper">
		SELECT
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime
		FROM
			ecpm_t_mhwhite
		WHERE
			STATUS = 0 and objectType = 1 and (endTime > now() or endTime is null)
	</select>

	<select id="selectInvalidMember" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteWrapper">
		SELECT
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime,
			checkFailReason
		FROM
			ecpm_t_mhwhite
		WHERE
			STATUS = 0 and objectType = 1 and endTime &lt; now()
	</select>


	<select id="selectDeleteMember" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteWrapper">
		SELECT
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime
		FROM
			ecpm_t_mhwhite
		WHERE
			(STATUS = 0 and objectType = 0) or (STATUS = 1 and objectType = 1 and endTime &lt; now())
	</select>


	<select id="selectDeleteMemberProactive" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteWrapper">
		SELECT
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime
		FROM
			ecpm_t_mhwhite
		WHERE
			(STATUS = 0 and objectType = 0)
	</select>

	<select id="selectDeleteMemberExpires" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteWrapper">
		SELECT
			whitePhoneId,
			commandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime,
			STATUS,
			objectType,
			createTime,
			updateTime
		FROM
			ecpm_t_mhwhite
		WHERE
			(STATUS = 1 and objectType = 1 and endTime &lt; now())
	</select>


	<update id="updateMHWhiteStatus">
		update ecpm_t_mhwhite set
		<trim suffixOverrides="," suffix="where whiteContent = #{whiteContent} and  commandId = #{commandId}">
			<if test="status!=null">STATUS = #{status},</if>
			<if test="checkFailReason!=null and checkFailReason!=''">checkFailReason= #{checkFailReason},</if>
		</trim>
	</update>


	<insert id="insertMHWhiteTask">
		INSERT INTO ecpm_t_mhwhite_task ( commandId, counter, whiteType, objectType, callbackStatus, createTime,updateTime,isFinish )
		SELECT commandId,(select sum(counter) from (
													   SELECT count(1) counter FROM ecpm_t_mhwhite
													   where commandId =  #{commandId}
													   UNION
													   SELECT count(1) counter  FROM  ecpm_t_mhwhite_history
													   where commandId =  #{commandId}
												   ) as total) as counter,1,objectType,0,now(),now(),0 FROM ecpm_t_mhwhite
		where commandId = #{commandId}
		GROUP BY commandId,objectType

	</insert>
	
	<insert id="insertMHWhiteTaskMapper">
		INSERT INTO ecpm_t_mhwhite_task ( commandId, counter, whiteType, objectType, callbackStatus, createTime,updateTime,isFinish )
		VALUES
		(
			#{commandId},
			#{counter},
			#{whiteType},
			#{objectType},
			#{callbackStatus},
			now(),
			now(),
			#{isFinish}
		)
	</insert>


	<update id="updateMHWhiteTaskStatus">
		update ecpm_t_mhwhite_task set
			callbackStatus = #{callbackStatus}
		<if test="isFinish != null">
			,isFinish=#{isFinish}
		</if>
		where commandId = #{commandId}
	</update>


	<select id="selectDealMHWhiteTask" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteTaskWrapper">
		SELECT
		commandId,
		counter,
		whiteType,
		objectType,
		callbackStatus,
		callbackTime,
		createTime,
		updateTime
		FROM
		ecpm_t_mhwhite_task
		WHERE
		callbackStatus = #{callbackStatus}
		and isFinish = #{isFinish}
		AND createTime &lt;= #{time};
	</select>

	<select id="selectMHWhiteTaskByCommandId" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteTaskWrapper">
		SELECT
			commandId,
			counter,
			whiteType,
			objectType,
			callbackStatus,
			callbackTime,
			createTime,
			updateTime
		FROM
			ecpm_t_mhwhite_task
		WHERE
			commandId = #{commandId}
	</select>

	<select id="selectCommandIdAndCount" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.CommandIdCountWrapper">
		SELECT commandid,SUM(t.status!=0) AS notEqualToZero,SUM(t.status=0) AS equalToZero,objectType FROM (
		SELECT commandid,whiteContent,STATUS,objectType FROM ecpm_t_mhwhite
		WHERE commandid IN (
		<foreach collection="workOrderIDList" item="workOrderID" separator="," index="index">
			#{workOrderID}
		</foreach>
		)
		UNION
		SELECT commandid,whiteContent,3,objectType FROM ecpm_t_mhwhite_history
		WHERE commandid IN (
		<foreach collection="workOrderIDList" item="workOrderID" separator="," index="index">
			#{workOrderID}
		</foreach>
		)
		) t GROUP BY commandid
	</select>


	<select id="selectDealMHWhiteMemberByCommandId" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteMemberWrapper">
		SELECT
			m.*,if(msf.errCode is null,ms.errCode,msf.errCode) errCode, if(msf.errDesc is null,ms.errDesc,msf.errDesc) errDesc,ms.status memberStatus,ms.reserved4
		FROM
			ecpm_t_mhwhite m
				LEFT JOIN ecpm_t_member_subscribe ms on m.whiteContent = ms.msisdn and (m.commandId = ms.batchNo or m.commandId = ms.deletebatchno)
				LEFT JOIN ecpm_t_member_sub_faild msf on m.whiteContent = msf.msisdn and m.commandId = msf.batchNo
		WHERE m.commandId = #{commandId}
		UNION all

		SELECT m.whitePhoneId,m.commandId,m.whiteType,m.whiteContent,m.province,m.startTime,m.endTime,m.STATUS,m.objectType,m.createTime,m.updateTime,null checkFailReason, "99" errCode, "该号码已由新的工单处理" errDesc,99 memberStatus, null reserved4  FROM ecpm_t_mhwhite_history m
		WHERE m.commandId = #{commandId}
	</select>
	<select id="selectDealMHWhiteMemberByStatus" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteMemberWrapper">
		SELECT
			m.*,if(msf.errCode is null,ms.errCode,msf.errCode) errCode, if(msf.errDesc is null,ms.errDesc,msf.errDesc) errDesc,ms.status memberStatus,ms.reserved4
		FROM
			ecpm_t_mhwhite m
				LEFT JOIN ecpm_t_member_subscribe ms on m.whiteContent = ms.msisdn and (m.commandId = ms.batchNo or m.commandId = ms.deletebatchno)
				LEFT JOIN ecpm_t_member_sub_faild msf on m.whiteContent = msf.msisdn and m.commandId = msf.batchNo
		WHERE (ms.status is null or ms.status not in (1))
		  and  m.status = 4
		  and m.commandId = #{commandId}
		  and (ms.reserved4 is null or ms.reserved4 not in (1,2))
		UNION all
		SELECT m.whitePhoneId,m.commandId,m.whiteType,m.whiteContent,m.province,m.startTime,m.endTime,m.STATUS,m.objectType,m.createTime,m.updateTime,null checkFailReason, "99" errCode, "该号码已由新的工单处理" errDesc,99 memberStatus, null reserved4  FROM ecpm_t_mhwhite_history m
		where m.status = 4 and m.commandId = #{commandId}
	</select>

	<select id="selectBusinessMHWhiteMemberByStatus" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubscribeWrapper">
		SELECT
			ec.whiteContent,
			ec.whitePhoneId,
			ec.province,
			if(ms.subSuccessTime is null,eb.subSuccessTime,ms.subSuccessTime) subSuccessTime,
			eb.createTime unsubSuccessTime
		FROM  ecpm_t_mhwhite_collect ec
				  LEFT JOIN ecpm_t_member_subscribe ms
							on ec.whiteContent = ms.msisdn and  (ec.addCommandId = ms.batchNo or ec.deleteCommandId = ms.deleteBatchNo)
				  LEFT JOIN ecpm_t_delete_back eb
							on ec.whiteContent = eb.msisdn and  (ec.addCommandId = eb.batchNo or ec.deleteCommandId = eb.deleteBatchNo)
	</select>

	<select id="selectBusinessMHWhiteByStatusAndCommandId" resultType="java.lang.Integer">
		SELECT count(*) FROM  ecpm_t_mhwhite where status = 0 and commandId = #{commandId}
	</select>



</mapper>