var app = angular.module("myApp", ["util.ajax", "angularI18n"])
app.controller('loginController', function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    $scope.rePasswordValidate = true;
    $scope.passwordValidate = true;
    $scope.sendSms = true;
    $scope.initImgCode()


    $scope.isGroupRemind = $scope.getQueryVariable("isGroupRemind");
    if($scope.isGroupRemind === 'true'){
      $(".login .login-cont").css("background" ,"url(../../assets/images/login-img2.png)no-repeat center center");
      $(".login .login-cont").css("background-size","100% 100%")
    }
  };

  //获取短信验证
  $scope.getSmsVerifyCode = function () {
    var smsReq = {
      "account": {
        "accountName": $scope.forgetAccount.trim(),
        "accountType": 3
      },
      "verifyType": $scope.isGroupRemind === 'true'? "8":"2"
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/loginService/getSmsVerifyCode",
      data: JSON.stringify(smsReq),
      success: function (result) {
        $rootScope.$apply(function () {
          $scope.timeChange();
        })
      },
      error: function () {
        $rootScope.$apply(function (result) {
              $scope.tip = "**********";
              $('#myModal').modal();
            }
        )
      }
    });
  };

  //定时器
  $scope.timeChange = function () {
    $("#getSmsCode").addClass("disabled");
    var count = 60;
    $scope.timer = window.setInterval(function () {
      count -= 1;
      $("#getSmsCode").text(count + "s");
      if (count <= 0) {
        $("#getSmsCode").removeClass("disabled");
        window.clearInterval($scope.timer);
        $("#getSmsCode").text("获取短信验证");
      }
    }, 1000);
  };


  //修改密码
  $scope.modifyPwd = function () {
    if("" == $scope.imgCodeToken || $scope.imgCodeToken==undefined){
      document.getElementById("rtnFalse-error").innerHTML="<i class='fa fa-times-circle'></i>&ensp;"+"验证码不能为空";
      document.getElementById("rtnFalse-error").style.display="block";
      return false;
    }else{
      document.getElementById("rtnFalse-error").innerHTML= ""
    }
    var modifyPwdReq = {
      "account": {
        "accountName": $scope.forgetAccount.trim(),
        "accountType": 3
      },
      "newPwd": $scope.forgetPwd,
      "verifyCode": $scope.smsVerify.trim(),
      "token": $scope.imgCodeToken
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/loginService/resetPwd",
      data: JSON.stringify(modifyPwdReq),
      success: function (result) {
        $rootScope.$apply(function () {
          if (result.result.resultCode == '**********') {
            window.location.href = '../login/login.html'
                + ($scope.isGroupRemind==='true'?("?isGroupRemind=" + $scope.isGroupRemind):"");
          } else if (result.result.resultCode == '**********' || result.result.resultCode == '**********') {
            //将这两个错误合并展示
            $scope.tip = "账号不存在或验证码错误";
            $('#myModal').modal();
            document.getElementById("rtnFalse-error").style.display="none";
            $scope.imgCodeToken = undefined;
            //刷新图形验证码
            $scope.loadDragCode();
          } else if(result.result.resultCode == '1030199999'){
            document.getElementById("rtnFalse-error").innerHTML="<i class='fa fa-times-circle'></i>&ensp;"+result.result.resultDesc;
            document.getElementById("rtnFalse-error").style.display="block";
            $scope.imgCodeToken = undefined;
            //刷新图形验证码
            $scope.loadDragCode();
          }else {
            $scope.tip = result.result.resultCode;
            $('#myModal').modal();
          }
        })

      },
      error: function () {
        $rootScope.$apply(function (result) {
              $scope.tip = "**********";
              $('#myModal').modal();
            }
        )
      }
    });
  };

  //确认密码
  $scope.checkRePassword = function () {
    var pwd = $scope.forgetPwd;
    var rePwd = $scope.confirmForgetPwd;
    $scope.rePasswordValidate = true;
    if (!rePwd) {
      $scope.rePasswordValidate = false;
    }
    if (pwd != rePwd) {
      $scope.rePasswordValidate = false;
    }
  };
  $scope.initImgCode=function () {
    var imgCodeToken;

    // 区分内外网
    var clientUrl = location.href;
    var url = "";
    var req = {
      "clientUrl": clientUrl
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/loginService/checkNetwork",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          url = result.netUrl;
          var head = document.getElementsByTagName('head')[0];
          var script = document.createElement('script');
          script.type = 'text/javascript';
          script.src = url;
          head.appendChild(script);
          if (script.readyState) {
            script.onreadystatechange = function() {
              if (script.readyState == "loaded"
                  || script.readyState == "complete") {
                script.onreadystatechange = null;
                loadDragCode();
              }
            };
          } else {
            script.onload = function() {
              loadDragCode();
            };
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              console.log(result);
            }
        )
      }

    });




    // 加载图形验证
    function loadDragCode() {
      var sessionid = undefined;
      createDrapImgCode("imgCode", sessionid, function(token) {
        $scope.imgCodeToken = token;
      });
    }
    function hiddenError() {
      $("#div_error").css({
        visibility : "hidden"
      });
    }

  }
// 加载图形验证
  $scope.loadDragCode = function () {
    var sessionid = undefined;
    createDrapImgCode("imgCode", sessionid, function(token) {
      $scope.imgCodeToken = token;
    });
  }
  //先校验图形验证码，验证码正确，再执行修改方法
  /*$scope.checkImgCodeToken = function () {
    if("" == $scope.imgCodeToken || $scope.imgCodeToken==undefined){
      document.getElementById("rtnFalse-error").innerHTML="<i class='fa fa-times-circle'></i>&ensp;"+"验证码不能为空";
      document.getElementById("rtnFalse-error").style.display="block";
      return false;
    }
    var req = {
      "token": $scope.imgCodeToken
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/loginService/checkImgCodeToken",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          if (result.result.resultCode == '**********') {
            console.log("图形验证码验证成功")
              $('.close').click();
              $scope.modifyPwd();
          }else {
            document.getElementById("rtnFalse-error").innerHTML="<i class='fa fa-times-circle'></i>&ensp;"+result.returnMsg;
            document.getElementById("rtnFalse-error").style.display="block";
            //刷新图形验证码
            $scope.loadDragCode();
          }

        })
      },
      error: function () {
        $rootScope.$apply(function () {
              console.log(result);
            }
        )
      }

    });
  }*/
  //检查密码
  $scope.checkPwd = function () {
    $scope.passwordValidate = true;
    $scope.passwordValidateDesc = "";
    var pwd = $scope.forgetPwd;
    var rePwd = $scope.confirmForgetPwd;
    var checkPwdRuleReq = {};
    checkPwdRuleReq.password = pwd;
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
      data: JSON.stringify(checkPwdRuleReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode != '**********') {
            $scope.passwordValidate = false;
            var passwordRuleList = data.passwordRuleList;
            if (result.resultCode == '1030120000') {
              $scope.passwordValidateDesc = "新密码必填";
            } else {
              if (!passwordRuleList) {
                $scope.passwordValidateDesc = result.resultDesc;
              } else {
                for (var i = 0; i < passwordRuleList.length; i++) {
                  // $scope.passwordValidateDesc = $scope.passwordValidateDesc + passwordRuleList[i].ruleName;
                  $scope.passwordValidateDesc = passwordRuleList[i].ruleName;
                  // $scope.passwordValidateDesc = $scope.passwordValidateDesc + ";";
                }
                $scope.passwordValidateDesc.substring(0, $scope.passwordValidateDesc.length - 1);
              }
            }
          } else {
            if (rePwd) {
              $scope.checkRePassword();
            }
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = '**********';
              $('#myModal').modal();
            }
        )
      }
    });
  };
  $scope.getQueryVariable = function (variable) {
    const query = window.location.search.substring(1);
    const vars = query.split("&");
    for (let i = 0; i < vars.length; i++) {
      const pair = vars[i].split("=");
      if (pair[0] == variable) {
        return pair[1];
      }
    }
    return (false);
  };
  $scope.goBack = function () {
    window.location.href = "../login/login.html"
        + ($scope.isGroupRemind==='true'?("?isGroupRemind=" + $scope.isGroupRemind):"");
  }
})