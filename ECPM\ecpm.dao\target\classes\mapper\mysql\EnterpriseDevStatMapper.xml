<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EnterpriseDevStatMapper">
    <resultMap id="enterpriseDevStatWrapper"
               type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseDevStatWrapper">
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="enterpriseCode" column="enterpriseCode"
                javaType="java.lang.String" />
        <result property="enterpriseName" column="enterpriseName"
                javaType="java.lang.String" />
        <result property="enterpriseType" column="enterpriseType"
                javaType="java.lang.Integer" />
        <result property="subProvinceType" column="subProvinceType" javaType="java.lang.Integer"/>
        <result property="provinceID" column="provinceID" javaType="java.lang.String" />
        <result property="cityID" column="cityID" javaType="java.lang.String" />
        <result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
        <result property="memberCount" column="memberCount" javaType="java.lang.Long" />
        <result property="newMemberCount" column="newMemberCount" javaType="java.lang.Long" />
        <result property="enterpriseCount" column="enterpriseCount" javaType="java.lang.Long" />
        <result property="newEnterpriseCount" column="newEnterpriseCount" javaType="java.lang.Long" />
        <result property="isNewEnterprise" column="isNewEnterprise" javaType="java.lang.Integer" />
        <result property="unsubMemberCount" column="unsubMemberCount" javaType="java.lang.Long" />
    </resultMap>

    <select id="queryEnterpriseDevStatInfo" resultMap="enterpriseDevStatWrapper">
        SELECT t.`provinceID`,t.`cityID`,t.`enterpriseType`,t.`subProvinceType`,t.`serviceType`,
        IFNULL(SUM(IF(t.`statDate` BETWEEN ${startDate} AND ${endDate},t.newMemberCount,0)),0) newMemberCount,
        IFNULL(SUM(IF(t.`statDate` BETWEEN ${startDate} AND ${endDate},t.isNewEnterprise,0)),0) newEnterpriseCount,
        SUM(IF(t.`statDate`=${endDate},t.`memberCount`,0)) memberCount,
        IFNULL(SUM(IF(t.`statDate` BETWEEN ${startDate} AND ${endDate},t.unsubMemberCount,0)),0) unsubMemberCount,
        SUM(IF(t.`statDate`=${endDate},1,0)) enterpriseCount
        FROM ecpm_t_enterprise_day_stat_service t
        force index(idx_statDate)
        <trim prefix="where" prefixOverrides="and|or">
            <if test="provinceIDs != null and provinceIDs.size()>0">
                and provinceID in
                <foreach item="provinceID" index="index" collection="provinceIDs"
                         open="(" separator="," close=")">
                    ${provinceID}
                </foreach>
            </if>
            <if test="cityIDs != null and cityIDs.size()>0">
                and cityID in
                <foreach item="cityID" index="index" collection="cityIDs"
                         open="(" separator="," close=")">
                    ${cityID}
                </foreach>
            </if>

            <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
                and enterpriseID in ( SELECT
                e.id
                FROM
                ecpm_t_enterprise_simple e
                LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
                where
                (
                (
                e.enterpriseType != 5 and e.enterpriseType != 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
            or
            (
            e.enterpriseType = 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e2.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
                or
                (
                e.enterpriseType = 5
                <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                    and (e.provinceID in
                    <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                        #{provinceID}
                    </foreach>
                    <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                        or e.provinceID is null
                    </if>
                    )
                </if>
                <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                    and (e.cityID in
                    <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                        #{cityID}
                    </foreach>
                    <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                        or e.cityID is null
                    </if>

                    )
                </if>
                <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                    and (e.countyID in
                    <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                        #{countyID}
                    </foreach>
                    <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                        or e.countyID is null
                    </if>
                    )
                </if>
                )
                )
                )
            </if>
            <if test="subProvinceType !=null and subProvinceType !=''">
                and subProvinceType=${subProvinceType}
            </if>

            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and subProvinceType in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    ${dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and subProvinceType not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    ${dAuth}
                </foreach>
            </if>
            <if test="serviceType !=null and serviceType !=''">
                and serviceType=${serviceType}
            </if>
            <if test="startDate != null and endDate != null">
                and (t.`statDate` BETWEEN CONCAT("",${startDate}) AND CONCAT("",${endDate}))
            </if>
            AND t.`serviceType` IN(1,5)

        </trim>
        GROUP BY t.`provinceID`,t.`cityID`,t.`enterpriseType`,t.`subProvinceType`,t.`serviceType`
        limit ${pageNum},${pageSize}
    </select>

    <select id="queryEnterpriseDevStatCount" resultType="java.lang.Integer">
        SELECT
        COUNT( DISTINCT IFNULL(t.`provinceID`,'null'),IFNULL(t.`cityID`,'null'),t.`enterpriseType`,IFNULL(t.`subProvinceType`,'null'),t.`serviceType`)
        FROM ecpm_t_enterprise_day_stat_service t
        force index(idx_statDate)
        <trim prefix="where" prefixOverrides="and|or">
            <if test="provinceIDs != null and provinceIDs.size()>0">
                and provinceID in
                <foreach item="provinceID" index="index" collection="provinceIDs"
                         open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>

            <if test="cityIDs != null and cityIDs.size()>0">
                and cityID in
                <foreach item="cityID" index="index" collection="cityIDs"
                         open="(" separator="," close=")">
                    #{cityID}
                </foreach>
            </if>
            <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
                and enterpriseID in ( SELECT
                e.id
                FROM
                ecpm_t_enterprise_simple e
                LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
                where
                ((
                e.enterpriseType != 5 and e.enterpriseType != 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
            or
            (
            e.enterpriseType = 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e2.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
                or
                (
                e.enterpriseType = 5
                <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                    and (e.provinceID in
                    <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                        #{provinceID}
                    </foreach>
                    <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                        or e.provinceID is null
                    </if>
                    )
                </if>
                <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                    and (e.cityID in
                    <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                        #{cityID}
                    </foreach>
                    <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                        or e.cityID is null
                    </if>

                    )
                </if>
                <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                    and (e.countyID in
                    <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                        #{countyID}
                    </foreach>
                    <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                        or e.countyID is null
                    </if>
                    )
                </if>
                )
                )
                )
            </if>
            <if test="subProvinceType !=null  and subProvinceType !=''">
                and subProvinceType=#{subProvinceType}
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and subProvinceType in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and subProvinceType not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>
            <if test="serviceType !=null and serviceType !=''">
                and serviceType=#{serviceType}
            </if>
            <if test="startDate != null and endDate != null">
                and (t.`statDate` BETWEEN CONCAT("",${startDate}) AND CONCAT("",${endDate}))
            </if>
            AND t.`serviceType` IN(1,5)
        </trim>

    </select>


    <select id="queryEnterpriseDevStatDetailInfo" resultMap="enterpriseDevStatWrapper">
        SELECT t.enterpriseID,s.enterpriseCode,s.enterpriseName,s.enterpriseType,t.subProvinceType,
        SUM(IFNULL(t.newMemberCount,0)) newMemberCount,IFNULL(MAX(t.isNewEnterprise),0) isNewEnterprise, SUM(IFNULL(t.unsubMemberCount,0)) unsubMemberCount
        FROM ecpm_t_enterprise_day_stat_service t,ecpm_t_enterprise_simple s
        where s.ID = t.enterpriseID
            <if test="provinceIDs != null and provinceIDs.size()>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceIDs"
                         open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            <if test="cityIDs != null and cityIDs.size()>0">
                and t.cityID in
                <foreach item="cityID" index="index" collection="cityIDs"
                         open="(" separator="," close=")">
                    #{cityID}
                </foreach>
            </if>
        <if test="provinceIDs == null or provinceIDs.size()==0">
            and t.provinceID is null
          </if>
        <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
            and enterpriseID in ( SELECT
            e.id
            FROM
            ecpm_t_enterprise_simple e
            LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
            where
            ((
            e.enterpriseType != 5 and e.enterpriseType != 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
            or
            (
            e.enterpriseType = 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e2.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
            or
            (
            e.enterpriseType = 5
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (e.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or e.provinceID is null
                </if>
                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (e.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or e.cityID is null
                </if>

                )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (e.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or e.countyID is null
                </if>
                )
            </if>
            )
            )
            )
        </if>
        <if test="subProvinceType !=null and subProvinceType !=''">
            and subProvinceType=#{subProvinceType}
        </if>
        <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
            and subProvinceType in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>

        <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
            and subProvinceType not in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>
            <if test="serviceType !=null and serviceType !=''">
                and serviceType=#{serviceType}
            </if>
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and s.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>
            <if test="startDate != null and endDate != null">
                and (t.`statDate` BETWEEN #{startDate} AND #{endDate})
            </if>
        AND t.`serviceType` IN(1,5)
        GROUP BY t.enterpriseID,s.enterpriseCode,s.enterpriseName,s.enterpriseType,t.subProvinceType
        limit #{pageNum},#{pageSize};
    </select>


    <select id="queryEnterpriseDevStatDetailCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM
        (SELECT t.enterpriseID,s.enterpriseCode,s.enterpriseName,s.enterpriseType,t.subProvinceType,
        SUM(IFNULL(t.newMemberCount,0)) newMemberCount,IFNULL(MAX(t.isNewEnterprise),0) isNewEnterprise
        FROM ecpm_t_enterprise_day_stat_service t,ecpm_t_enterprise_simple s
        where s.ID = t.enterpriseID
        <if test="provinceIDs != null and provinceIDs.size()>0">
            and t.provinceID in
            <foreach item="provinceID" index="index" collection="provinceIDs"
                     open="(" separator="," close=")">
                #{provinceID}
            </foreach>
        </if>

        <if test="cityIDs != null and cityIDs.size()>0">
            and t.cityID in
            <foreach item="cityID" index="index" collection="cityIDs"
                     open="(" separator="," close=")">
                #{cityID}
            </foreach>
        </if>
        <if test="provinceIDs == null or provinceIDs.size()==0">
            and t.provinceID is null
        </if>
        <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
            and enterpriseID in ( SELECT
            e.id
            FROM
            ecpm_t_enterprise_simple e
            LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
            where
            ((
            e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
            or
            (
            e.enterpriseType = 5
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (e.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or e.provinceID is null
                </if>
                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (e.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or e.cityID is null
                </if>

                )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (e.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or e.countyID is null
                </if>
                )
            </if>
            )
            )
            )
        </if>
        <if test="subProvinceType !=null and subProvinceType !=''">
            and subProvinceType=#{subProvinceType}
        </if>
        <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
            and subProvinceType in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>

        <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
            and subProvinceType not in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>
        <if test="serviceType !=null and serviceType !=''">
            and serviceType=#{serviceType}
        </if>
        <if test="enterpriseName !=null  and enterpriseName !=''">
            and s.enterpriseName like concat("%", #{enterpriseName}, "%")
        </if>
        <if test="startDate != null and endDate != null">
            and (t.`statDate` BETWEEN #{startDate} AND #{endDate})
        </if>
        AND t.`serviceType` IN(1,5)
        GROUP BY t.enterpriseID,s.enterpriseCode,s.enterpriseName,s.enterpriseType,t.subProvinceType) s

    </select>

    <select id="queryEnterpriseDevStatInfoForArea" resultMap="enterpriseDevStatWrapper">
        SELECT t.`provinceID`,t.`cityID`,t.`enterpriseType`,t.`subProvinceType`,t.`serviceType`,
        SUM(t.newMemberCount) newMemberCount,
        SUM(t.newEnterpriseCount) newEnterpriseCount,
        SUM(IF(t.`statDate`=${endDate},t.`memberCount`,0)) memberCount,
        SUM(unsubMemberCount) unsubMemberCount,
        SUM(IF(t.`statDate`=${endDate},t.`enterpriseCount`,0)) enterpriseCount
        FROM ecpm_t_enterprise_day_stat_service_area t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
                ((1=1
                <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                    and (provinceID in
                    <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                        #{provinceID}
                    </foreach>
                    )
                </if>
                <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                    and (cityID in
                    <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                        #{cityID}
                    </foreach>
                    )
                </if>
                <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                    and (countyID in
                    <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                        #{countyID}
                    </foreach>
                    )
                </if>
                )
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or provinceID is null
                </if>
                )
            </if>
            <if test="subProvinceType !=null and subProvinceType !=''">
                and subProvinceType=${subProvinceType}
            </if>

            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and subProvinceType in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    ${dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and subProvinceType not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    ${dAuth}
                </foreach>
            </if>
            <if test="serviceType !=null and serviceType !=''">
                and serviceType=${serviceType}
            </if>
            <if test="startDate != null and endDate != null">
                and (statDate <![CDATA[ >= ]]> #{startDate}
                and statDate <![CDATA[ <= ]]> #{endDate})
            </if>
            <if test="startDate != null and endDate == null">
                and statDate <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="startDate == null and endDate != null">
                and statDate <![CDATA[ <= ]]> #{endDate}
            </if>
            AND t.`serviceType` IN(1,5)
        </trim>
        GROUP BY t.`provinceID`,t.`cityID`,t.`enterpriseType`,t.`subProvinceType`,t.`serviceType`
        limit ${pageNum},${pageSize}
    </select>

    <select id="queryEnterpriseDevStatCountForArea" resultType="java.lang.Integer">
        SELECT count(1) FROM (SELECT provinceID
        FROM ecpm_t_enterprise_day_stat_service_area t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
                ((1=1
                <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                    and (provinceID in
                    <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                        #{provinceID}
                    </foreach>
                    )
                </if>
                <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                    and (cityID in
                    <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                        #{cityID}
                    </foreach>
                    )
                </if>
                <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                    and (countyID in
                    <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                        #{countyID}
                    </foreach>
                    )
                </if>
                )
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or provinceID is null
                </if>
                )
            </if>
            <if test="subProvinceType !=null and subProvinceType !=''">
                and subProvinceType=${subProvinceType}
            </if>

            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and subProvinceType in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    ${dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and subProvinceType not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    ${dAuth}
                </foreach>
            </if>
            <if test="serviceType !=null and serviceType !=''">
                and serviceType=${serviceType}
            </if>
            <if test="startDate != null and endDate != null">
                and (statDate <![CDATA[ >= ]]> #{startDate}
                and statDate <![CDATA[ <= ]]> #{endDate})
            </if>
            <if test="startDate != null and endDate == null">
                and statDate <![CDATA[ >= ]]> #{startDate}
            </if>
            <if test="startDate == null and endDate != null">
                and statDate <![CDATA[ <= ]]> #{endDate}
            </if>
            AND t.`serviceType` IN(1,5)
        </trim>
        GROUP BY t.`provinceID`,t.`cityID`,t.`enterpriseType`,t.`subProvinceType`,t.`serviceType`) a
    </select>
</mapper>