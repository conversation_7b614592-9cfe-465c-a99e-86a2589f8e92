<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>USSD接入设置</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
    <link href="../../../../../css/reset.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <!--tab页切换-->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>

    <script type="text/javascript" src="ussdAccessSettings.js"></script>
    <style>
        .switch-info{
            color: #aaa;
            margin-left: 30px;
            display: inline-block;
            vertical-align: middle;
        }
        .switch{
            width: 40px;
            height: 20px;
            margin: 7px 0;
            vertical-align: middle;
        }
        .switch .switch-icon{
            width: 18px;
            height: 18px;
        }
        .offtip {
            height: 20px;
            display: inline-block;
            vertical-align: top;
            color: #999999;
            padding-left: 20px;
        }
        .auto-width{
            width: -moz-max-content;
            width: max-content;
        }
    </style>
</head>
<body  ng-app="myApp" ng-controller="ussdSettingController" ng-init="init()" >
    <div class="cooperation-manage" >
        <div class="cooperation-head">
            <span ng-show="!isSuperManager" class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>
			<span ng-show="isSuperManager" class="frist-tab" ng-bind="'COMMON_PROVINCEENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'USSDACCESSSETTINGS'|translate"></span>
        </div>
        <top:menu  ng-if="isSuperManager" chose-index="{{choseIndex}}" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/hotLineColorPrint/hotLineManage"
        list-index="40" apply-val="{{proSupServerType}}"></top:menu>
        <div class="cooper-tab">
            <div>
                <form class="form-horizontal ng-pristine ng-invalid ng-invalid-required ng-valid-maxlength" name="myForm"
                      novalidate="">
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label" ng-bind="'FLOWCONTROLFLUID'|translate"></label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input ng-disabled="!isSuperManager" type="text" class="form-control" placeholder="{{'PLEASEENTERFLOWCONTROL'|translate}}"
                                   ng-model="fluid" name="fluid" pattern="^[0-9]{1,32}$">
                            <span style="color:red"
                                   ng-show="myForm.fluid.$dirty && myForm.fluid.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                <span ng-show="myForm.fluid.$error.pattern" ng-bind="'HOTLINE_MAXINPUTDESC'|translate"></span>
                            </span>
                            <div style="color:#f30404">每秒请求数</div>
                        </div>
                        <!-- 流量控制开关 -->
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6" style="min-width: 310px">
                            <span class="switch switch3" ng-class="{'off':fluidSwitch=='0'}"
                                ng-click="changeStatus('fluidSwitch')">
                                <icon class="switch-icon"></icon>
                            </span>
                            <span class='offtip' ng-show="fluidSwitch=='1'" ng-bind="'USSD_LIUKONGSWITCH'|translate"  class="switch-info"></span>

                        </div>
                    </div>

                    <!-- 接入能力开关 -->
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                            ng-bind="'USSD_ABLITYACCESSSWITCH'|translate"></label>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6" style="min-width: 310px">
                            <span class="switch switch1" ng-class="{'off':status=='2'}"
                                ng-click="changeStatus('status')">
                                <icon class="switch-icon"></icon>
                            </span>
                            <span class='offtip' ng-show="status=='0'" ng-bind="'USSD_ABLITYSWITCH'|translate" class="switch-info"></span>
                        </div>
                    </div>
                    
                    <!-- 异网是否支持USSD开关 -->
                    <!--<div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                            ng-bind="'USSD_DIFFNETUSSDACCESSSWITCH'|translate"></label>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6" style="min-width: 310px">
                            <span class="switch switch1" ng-class="{'off':diffnetUssdSwitch=='0'}"
                                ng-click="changeStatus('diffnetUssdSwitch')">
                                <icon class="switch-icon"></icon>
                            </span>
                            <span class='offtip' ng-show="diffnetUssdSwitch=='1'" ng-bind="'USSD_DIFFNETUSSDSWITCH'|translate" class="switch-info"></span>
                        </div>
                    </div>-->
                    <!-- 通知方式 -->
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                            ng-bind="'通知方式'|translate"></label>
                        <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="min-width: 310px">
                            <!--<li class="auto-width" class="check-li"><span ng-click="changemsgType(0)" style="vertical-align:middle;"
                                    ng-class="{'checked':msgtypeArr[0]==1}" class="check-btn checked-btn"> </span>
                                <span ng-click="changemsgType(0)">{{'USSD_NOTIFY_TYPE1'|translate}}</span></li>-->
                            <li class="auto-width" class="check-li"><span ng-click="changemsgType(1)" style="vertical-align:middle;"
                                    ng-class="{'checked':msgtypeArr[1]==1}" class="check-btn checked-btn"> </span>
                                <span ng-click="changemsgType(1)">{{'USSD_NOTIFY_TYPE2'|translate}}</span></li>
                            <!--<li class="auto-width" class="check-li"><span ng-click="changemsgType(2)" style="vertical-align:middle;"
                                    ng-class="{'checked':msgtypeArr[2]==1}" class="check-btn checked-btn"> </span>
                                <span ng-click="changemsgType(2)">{{'USSD_NOTIFY_TYPE3'|translate}}</span></li>-->
                            <li class="auto-width" class="check-li"><span  ng-click="changemsgType(3)" style="vertical-align:middle;"
                                    ng-class="{'checked':msgtypeArr[3]==1}" class="check-btn checked-btn"> </span>
                                <span  ng-click="changemsgType(3)">{{'USSD_NOTIFY_TYPE4'|translate}}</span></li>
                            <!--<li class="auto-width" class="check-li"><span ng-click="changemsgType(4)" style="vertical-align:middle;"
                                    ng-class="{'checked':msgtypeArr[4]==1}" class="check-btn checked-btn"> </span>
                                <span ng-click="changemsgType(4)">{{'USSD_NOTIFY_TYPE5'|translate}}</span></li>-->
                            <li class="auto-width" class="check-li"><span ng-click="changemsgType(5)" style="vertical-align:middle;"
                                    ng-class="{'checked':msgtypeArr[5]==1}" class="check-btn checked-btn"> </span>
                                <span ng-click="changemsgType(5)">{{'USSD_NOTIFY_TYPE6'|translate}}</span></li>
                            <!-- 暂时未用 -->
                             <!--<li class="auto-width" ng-click="changemsgType(6)" class="check-li"><span ng-class="{'checked':msgtypeArr[6]==1}" class="check-btn checked-btn"  style="vertical-align:middle;"> </span> {{'USSD_NOTIFY_TYPE7'|translate}}</li>-->
                            <!--<li class="auto-width" class="check-li"><span ng-click="changemsgType(7)" style="vertical-align:middle;"
                                    ng-class="{'checked':msgtypeArr[7]==1}" class="check-btn checked-btn"> </span>
                                <span ng-click="changemsgType(7)">{{'USSD_NOTIFY_TYPE8'|translate}}</span></li>-->
                            <li class="auto-width" class="check-li"><span ng-click="changemsgType(8)"
                                                                      style="vertical-align:middle;"
                                                                      ng-class="{'checked':msgtypeArr[8]==1}"
                                                                      class="check-btn checked-btn"> </span>
                            <span ng-click="changemsgType(8)">{{'USSD_NOTIFY_TYPE9'|translate}}</span></li>
                        	<li class="auto-width" class="check-li"><span ng-click="changemsgType(9)"
                                                                      style="vertical-align:middle;"
                                                                      ng-class="{'checked':msgtypeArr[9]==1}"
                                                                      class="check-btn checked-btn"> </span>
                            <span ng-click="changemsgType(9)">{{'USSD_NOTIFY_TYPE10'|translate}}</span></li>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn  col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 "
                        ng-disabled="myForm.fluid.$invalid "
                        ng-bind="'COMMON_SAVE'|translate" ng-click="saveUssd()" ng-show="isSuperManager" ></button>
                <button type="submit" class="btn btn-back" ng-click="goBack()"><span ng-bind="'COMMON_BACK'|translate"></span></button>
            </div>
        </div>

        <!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                            </p></div>
                        </div>
                        <div class="modal-footer" style="text-align:center; margin-left:0px;">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</body>
<style>
    body {
        background: #f2f2f2;
    }
    .modal-footer{
        text-align: left;
    }
    .fontGreen{
        color: rgb(48, 147,25)
    }
    .fontRed{
        color:rgb(252,70,93);
    }
    .cooperation-manage{
        min-width: 1024px;
    }
    .cooperation-head {
        padding: 20px;
    }
    .cooperation-head .frist-tab {
        font-size: 16px;
    }
    .cooperation-head .second-tab {
        font-size: 14px;
    }
    .cooper-tab {
        margin: 0 20px;
        background: #fff;
        border-radius: 2px;
        padding: 36px 10px 16px;
    }
    .form-group .control-label icon {
        color: #ff254c;
        vertical-align: sub;
        margin-right: 2px;
    }
    .form-group div {
        line-height: 34px;
    }
    .form-group {
        margin-bottom: 35px;
    }
</style>
</html>