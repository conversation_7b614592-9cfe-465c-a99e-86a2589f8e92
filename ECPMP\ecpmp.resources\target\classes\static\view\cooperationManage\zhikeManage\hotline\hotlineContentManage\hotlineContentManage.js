var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common","ngSanitize"])
app.controller('hotlineContentController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.CommonUtils = CommonUtils;
    $scope.init = function () {
        $scope.operate = 'add';
        $scope.noSign = '0';
        $scope.number = 70;
        $scope.dsc = "HOTLINE_CONTENTDESC";
        $scope.msg = "请输入彩印内容1~70字";
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.parentEnterpriseID = $scope.enterpriseID ;
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        //获取enterpriseName
        $scope.enterpriseName = $.cookie('enterpriseName');
        //判断最终调接口的enterpriseID,enterpriseName
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            $scope.enterpriseID = $scope.subEnterpriseID;
            $scope.enterpriseName = $scope.subEnterpriseName;
        }
        //标题
        $scope.contentTitle = '';
        //标题校验
        $scope.contentTitleValidate = true;

        $scope.contentValidate = true;
        //图片数量
        $scope.picLength = 0;
        //视频数量
        $scope.videoLength = 0;
        //文本框的个数
        $scope.ctnTextMount = 0;
        $scope.hotContentInfoListData = [];
        $scope.selectedList = [];
        $scope.hotMsisdnVali = true;
        $scope.contentVali = true;
        $scope.operatorID = $.cookie('accountID');
        $scope.hotlineMsisdnAdd = '';
        $scope.hotlineMsisdnDel = '';
        $scope.hotlineList = [];
        $scope.contentBelongOrgList = [];
        $scope.chooseAllAddHotLine = false;
        //新增热线号码是否可以点击批量接口
        $scope.hasChoseAddHotLine = false;
        $scope.hasChoseDelHotLine = false;

        $scope.accepttype2 = "jpeg,jpg,png,bmp";
        $scope.isValidate2 = false;
        $scope.filesize2 = 100;
        $scope.mimetypes2 = ".jpeg,.jpg,.png,.bmp";
        $scope.isCreateThumbnail2 = false;
        $scope.uploadurl2 = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.numlimit2 = 1000;
        $scope.uploadCertiDesc = "最多支持6张图片，仅支持jpg，bmp，png，jpeg格式";
        $scope.uploadParam2 = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'certiFile'
        };
        $scope.colorContentAndFileList = [];
        $scope.certificateUrlList = [];
        //文件数
        $scope.fileLength = 0;

        $scope.$watch('hotlineList', function (newVal, oldVal) {
            $scope.hasChoseAddHotLine = false;
            if (newVal.length > 0) {
                //判断是否至少已经勾选了一项
                for (var i in newVal) {
                    if (newVal[i].checked) {
                        $scope.hasChoseAddHotLine = true;
                        break;
                    }
                }
                var str = JSON.stringify(newVal);
                var index = str.indexOf('"checked":false');
                //同步全选按钮
                $scope.chooseAllAddHotLine = index === -1 ? true : false;
            }
        }, true);
        if ($scope.operate != 'detail') {
            $scope.$watch('contentList', function (newVal, oldVal) {
                $scope.ctnTextSumLength = 0;
                $scope.picLength = 0;
                $scope.videoLength = 0;
                $scope.ctnTextSum = "";
                $scope.ctnTextMount = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameType == 1) {
                        $scope.picLength += 1;
                    } else if (newVal[i].frameType == 3) {
                        $scope.videoLength += 1;
                    } else {
                        $scope.ctnTextMount += 1;
                    }
                    $scope.ctnTextSumLength += newVal[i].frameTxt.length;
                    $scope.ctnTextSum += newVal[i].frameTxt;
                }
                // //删除的话，就去再次校验敏感词
                // if (newVal.length < oldVal.length) {
                //     $scope.sensitiveCheck($scope.ctnTextSum, 2)
                // }
            }, true)
            $scope.$watch('colorContentAndFileList', function (newVal, oldVal) {
                $scope.fileLength = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameFileUrl) {
                        $scope.fileLength += 1;
                    }
                }

            }, true)
        }
        $scope.$watch('contentBelongOrgList', function (newVal, oldVal) {
            $scope.hasChoseDelHotLine = false;
            if (newVal.length > 0) {
                for (var i in newVal) {
                    if (newVal[i].checked) {
                        $scope.hasChoseDelHotLine = true;
                        break;
                    }
                }
                var str = JSON.stringify(newVal);
                var index = str.indexOf('"checked":false');
                //同步全选按钮
                $scope.chooseAllDelHotLine = index === -1 ? true : false;
            }
        }, true)
        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'importContentTemplate'
        };
        $scope.errorInfo = '';
        $scope.fileUrl = '';
        // 上传excel  END
        $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileName = broadData.file.name;
            } else {
                $scope.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.errorInfo = broadData.errorInfo;
            $scope.fileUrl = fileUrl;
        });
        // 上传video
        $scope.accepttypeVideo = "mp4,3gp";
        $scope.isValidateVideo = true;
        $scope.filesizeVideo = 2;
        $scope.mimetypesVideo = ".mp4,.3gp,.MP4,.3GP";
        $scope.autoVideo = true;
        $scope.isCreateThumbnailVideo = false;
        $scope.uploadurlVideo = '/qycy/ecpmp/ecpmpServices/fileService/uploadVideo';
        $scope.uploadDescVideo = "仅支持mp4,3gp格式的文件";
        $scope.numlimitVideo = 3;
        $scope.urlListVideo = [];
        $scope.uploadParamVideo = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms',
            use: 'video'
        };
        // 上传Video  END
        $scope.$on("uploadifyidVideo", function (event, fileUrl, index, broadData) {

            $scope.uploaderVideo = broadData.uploader;

            if (fileUrl) {
                $scope.contentList.push({
                    frameType: 3,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).watch,
                    frameTxt: "",
                    filesize: broadData.file.size
                })
            }
            $scope.errorInfoVideo = broadData.errorInfo;
        });

        //内容限制
        $scope.frameContentMaxNum = 3;
        $scope.frameContentMaxLength = 1000;
        //上传图片
        $scope.accepttypeImg = "jpg,gif,JPG,GIF";
        $scope.isValidateImg = true;
        $scope.filesizeImg = 0.2;
        $scope.imgMaxMum = 3;
        $scope.mimetypesImg = ".jpg,.gif,.JPG,.GIF";
        $scope.isCreateThumbnailImg = false;
        $scope.uploadurlImg = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.numlimitImg = 1000;
        $scope.uploadParamImg = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms'
        };

        $scope.maxSizeError = false;
        $scope.$on("uploadifyidImg", function (event, fileUrl, fileData) {
            $scope.uploaderImg = fileData.uploader;
            if (fileUrl) {
                $scope.maxSizeError = false;
                var totalSize = 0;
                for (var j in $scope.contentList) {
                    var tepm = $scope.contentList[j];
                    if (tepm.frameType === 1) {
                        totalSize += tepm.filesize;
                    }
                }
                if ($scope.totalFileSizeImg && totalSize + fileData.file.size > $scope.totalFileSizeImg * 1024 * 1024) {
                    $scope.maxSizeError = true;
                    return;
                }


                $scope.contentList.push({
                    frameType: 1,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).review,
                    frameTxt: "",
                    filesize: fileData.file.size
                })
            } else if (fileData != '' || fileData != undefined) {
            }
        });
        $scope.contentList = [];

        //上传文件
        $scope.$on("uploadifyid2", function (event, fileUrl, fileData) {
            if (fileUrl != '') {
                if($scope.colorContentAndFileList.length >= 6){
                    return;
                }
                $scope.colorContentAndFileList.push({
                    frameFileUrl: fileUrl,
                    formatFrameFileUrl: CommonUtils.formatPic(fileUrl).download,
                    filesize: fileData.file.size,
                    filename: fileData.file.name
                })
            } else if (fileData != '' || fileData != undefined) {
                // $scope.contentPicUrlList.splice(index, 1);
                // if($scope.urlList){
                //     $scope.urlList.splice(index,1);
                // }
            }
            console.log(fileUrl);
            console.log($scope.colorContentAndFileList);
            console.log($scope.fileLength);
        });

        //彩印内容类型
        $scope.typeMap = {
            "1": "主叫彩印",
            "2": "被叫彩印",
            "3": "主被叫彩印",
            "4": "被叫挂机短信",
            "8": "挂机彩信",
            "16": "挂机增彩",
            // "7": "交互彩印",
        };
        //状态信息
        $scope.hotlineStatusMap = {
            "1": "审核失败",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        };
        $scope.unicomApproveStatusMap = {
        		"1": "审核失败",
                "-1": "--",
                "2": "待审核",
                "3": "审核通过",
                "4": "审核驳回"
        }

        $scope.telecomApproveStatusMap = {
            "null": "待审核",
            "-1": "--",
            "0": "审核通过",
            "1": "审核失败"
        }
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.auditStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 2,
                name: "待审核"
            },
            {
                id: 3,
                name: "审核通过"
            },
            {
                id: 4,
                name: "审核驳回"
            },
            {
                id: 1,
                name: "审核失败"
            },
        ];
        $scope.subServChoise = [
            {
                id: "1,2,3,4,8,16",
                name: "不限"
            },
            {
                id: "1",
                name: "主叫彩印"
            },
            {
                id: "2",
                name: "被叫彩印"
            },
            {
                id: "3",
                name: "主被叫彩印"
            },
            {
                id: "4",
                name: "被叫挂机短信"
            },
            {
                id: "8",
                name: "挂机彩信"
            },
            {
                id: "16",
                name: "挂机增彩"
  /*          },
            {
                id: "7",
                name: "交互彩印"*/
            }
        ];
        //初始化搜索条件
        $scope.initSel = {
            contentNo:"",
            subServType:"1,2,3,4,8,16",
            auditStatus:""
        };
        //配置的企业id
        $scope.ids = ["30000093", "30000141", "直客企业id", "子企业id", "子企业id"];
        $scope.queryHotLineList('', 'search');
        $scope.queryHotlineContentInfoList();
        
        // 2102：子企业的新增页在查询订购关系之后再初始化投递方式
        if (!$scope.subEnterpriseID || $scope.enterpriseType != 3 || $scope.operate != 'add') {
            //下拉框(投递方式)
            $scope.subServTypeChoise = [
                {
                    id: "1",
                    name: "主叫彩印"
                },
                {
                    id: "2",
                    name: "被叫彩印"
                },
                {
                    id: "3",
                    name: "主被叫彩印"
                },
                {
                    id: "4",
                    name: "被叫挂机短信"
                },
                {
                    id: "8",
                    name: "挂机彩信"
                },
                {
                    id: "16",
                    name: "挂机增彩"
/*                },
                {
                    id: "7",
                    name: "交互彩印"*/
                }
            ];
        }


        //REQ-113 REQ-122
        //查询企业服务开关
        $scope.queryPlatformStatus();

        //查询所属行业列表
        $scope.queryIndustry($scope);

        $scope.initBusinessURLContainer($scope);

        //判断内容长度
        $scope.checkenterpriseid();

        //校验标题
        $scope.checkContentTitle = function (contentTitle) {
            if ($scope.addHotlineContentInfo.subServType == 8) {
                $scope.contentTitleValidate = $scope.validate(contentTitle, 20, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
            } else {
                $scope.contentTitleValidate = $scope.validate(contentTitle, 9, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);

            }
        };

        $('#addHotlineContent').on('hidden.bs.modal', function (e) {
            $rootScope.$apply(function () {
                if ($scope.uploaderVideo) {
                    $scope.uploaderVideo.reset();
                }
                if ($scope.uploaderImg) {
                    $scope.uploaderImg.reset();
                }

                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.showUpload = false;
                $scope.businessLicenseURL_ = "";
                $scope.fileUrl_ = "";
                $scope.urlList_ = [];
                //清空表单验证
                $scope.myForm.$setPristine();
            })
            $scope.contentList = [];
        });
        $('#addHotlineContent').on('show.bs.modal', function (e) {
            $scope.showUpload = true;
        })

    };
    //内容长度校验
    $scope.checkenterpriseid = function () {
        var arr = $scope.ids;
        var enterpriseId = $scope.enterpriseID;
        if (arr.toString().indexOf(enterpriseId) > -1) {
            $scope.flag = true;
            $scope.number = 748;
            $scope.dsc = "HOTLINE_CONTENTDESC1";
            $scope.msg = "请输入彩印内容1~750字";
        }
    }

    //其他资质文件下载
    $scope.exportFile = function (downloadUrl) {
        var req = {
            "param":{
                "path": downloadUrl,
                "token": $.cookie("token"),
                "isExport": 0
            },
            "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
            "method":"get"
        }
        CommonUtils.exportFile(req);
    };

    $scope.exportContentFile = function () {

        let param = angular.copy($scope.queryHotlineContentInfoListTemp);
        param.pageParameter.pageSize = 1000;
        let req = {
            "param": {
                "req": JSON.stringify(param),
                "type": 2
            },
            "url": "/qycy/ecpmp/ecpmpServices/contentService/downContentInfoCsvFileService",
            "method": "get"
        };

        if($scope.queryHotlineContentInfoListTemp != undefined)
        {
            CommonUtils.exportFile(req);
        }
    }

    $scope.changeSubServerType = function (val) {

    	// 2102：根据选中的业务子类型，从订购关系map取计费方式map的key列表，对key列表中存在的计费方式，支持选中（默认选中第一个），否则置灰
    	if ($scope.subEnterpriseID && $scope.enterpriseType == 3 
    			&& $scope.addHotlineContentInfo.subServType
    			&& $scope.subscribeMap) {
    		// 计费方式默认为1
    		$scope.platformInit(1);
    	}
    	
        $scope.showUpload = false;
        setTimeout(function () {
            $scope.showUpload = true;
        }, 1);
        $scope.errorInfoVideo = "";
        $scope.errorInfo = "";
        $scope.contentTitleValidate = true;
        $scope.contentTitle = "";
        $scope.contentList = [];
        $scope.maxSizeError = false;
        $scope.addHotlineContentInfo.content = "";
        $scope.isSensitive = false;
        $scope.contentVali = true;

        var subServTypeChoise = $("#select").val();
        if (val) {
            subServTypeChoise = "string:" + val;
        }
        //判断是否为被叫挂机短信
        if ("string:4" == subServTypeChoise) {
            $scope.number = 750;
            $scope.dsc = "HOTLINE_CONTENTDESC1";
            $scope.msg = "请输入彩印内容1~750字";
        } else {
            $("#filePickerVideo div:eq(1)").attr("style", "position: absolute; top: 0px; left: 0px; width: 107px; height: 35px; overflow: hidden; bottom: auto; right: auto;");
            $("#filePickerImg div:eq(1)").attr("style", "position: absolute; top: 0px; left: 0px; width: 137px; height: 35px; overflow: hidden; bottom: auto; right: auto;");
            $scope.number = 70;
            $scope.dsc = "HOTLINE_CONTENTDESC";
            $scope.msg = "请输入彩印内容1~70字";
        }

        //彩信标题20字，图片5张，大小280K格式为jpg，jpeg，png不区分大小写
        if ("string:8" == subServTypeChoise) {
            $scope.totalFileSizeImg = 0.28;
            $scope.filesizeImg = 0.3;
            $scope.mimetypesImg = ".jpg,.jpeg,.png,.JPG,.JPEG,.PNG";
            $scope.accepttypeImg = "jpg,jpeg,png";
            $scope.imgMaxMum = 5;
            $scope.frameContentMaxNum = 5;
            $scope.frameContentMaxLength = 750;
            $scope.uploadParamImg = {
                enterpriseId: $scope.enterpriseID,
                fileUse: 'ebanhanceMms_cx'
            };

        } else {
            $scope.filesizeImg = 0.2;
            $scope.totalFileSizeImg = null;
            $scope.accepttypeImg = "jpg,gif";
            $scope.mimetypesImg = ".jpg,.gif,.JPG,.GIF";
            $scope.imgMaxMum = 3;
            $scope.frameContentMaxNum = 3;
            $scope.frameContentMaxLength = 1000;
            $scope.uploadParamImg = {
                enterpriseId: $scope.enterpriseID,
                fileUse: 'ebanhanceMms'
            };
        }
        $scope.addHotlineContentInfo.subContentList = [];
        if("string:7" == subServTypeChoise){
            $scope.addHotlineContentInfo.subContentList.push({
                subContentType: 1,
                replyvalid: false,
                replyisensitive: "",
                content: "",
                instruct: ""
            });
        }
        if (subServTypeChoise != '4')
        {
        	$scope.addHotlineContentInfo.sceneDesc = "";
        }
        $scope.checkenterpriseid();
    };

    $scope.addTextCtn = function () {
        $scope.contentList.push({
            framePicUrl: "",
            formatFramePicUrl: "",
            frameTxt: "",
            filesize: "",
            frameTxtValidate: true
        })
        $scope.contentValidate = false;

    };
    $scope.deleteCtnOrPic = function (index) {
        $scope.contentList.splice(index, 1);

        for (let i = 0; i < $scope.contentList.length; i++) {
            let temp = $scope.contentList[i];
            if (temp.frameType != 3 && temp.frameType != 1 && !temp.frameTxt) {
                $scope.contentValidate = false;
                return;
            }
        }

        $scope.contentValidate = true;
    };
    $scope.deleteCtnOrFile = function (index) {
        $scope.colorContentAndFileList.splice(index, 1);
    }


    //导入号码
    $scope.importHotLinePop = function (item) {
        $scope.selectedItem = item;
        $scope.importContentID = item.contentID;
        $scope.contentVal = item.content;
        $('#impotHotLineNoPop').modal();
        $('#impotHotLineNoPop').on('hidden.bs.modal', function () {
            $rootScope.$apply(function () {
                $("#filePicker").find("span").text("导入文件");
                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.errorInfo = "";
                $scope.fileName = "";
                $scope.fileUrl = "";
            })
        });
    };
    $scope.commitImportHotLineNo = function () {
        var req = {
            "templateID": $scope.selectedItem.contentID,
            "enterpriseID": $scope.enterpriseID,
            "operatorID": $scope.operatorID,
            "path": $scope.fileUrl,
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/batchAddTemplateHotlineRel",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $('#impotHotLineNoPop').modal("hide");
                        $scope.tip = "导入成功";
                        $('#myModal').modal();
                    } else if (data.url) {
                        $('#impotHotLineNoPop').modal("hide");
                        $scope.tip = data.failNum + "条导入失败，请查看失败文件";
                        $('#myModal').modal();
                        var req1 = {
                            "param": {
                                "path": data.url,
                                "token": $scope.token,
                                "isExport": 0
                            },
                            "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
                            "method": "get"
                        }
                        CommonUtils.exportFile(req1);
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                    $scope.queryHotLineList('', 'search');
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.goback = function () {
        $('.close').click();
    }
    // 新增热线内容弹窗
    $scope.addHotlineContent = function () {
        $scope.operate = 'add';
        $scope.addHotlineContentInfo = {};
        $scope.subscribeList = null;
        $scope.subscribeMap = null;
        $scope.addHotlineContentInfo.subContentList = [];
        $rootScope.$broadcast("event.resetErrTip");
        //交互ussd 错误指令内容
        $scope.errorSubContent = {
            ID: "",
            instruct: "",
            subContentType: 2,
            content: "您输入的内容有误，感谢您的参与！",
            contentVali: true,
            isSensitive: false,
            contentDesc: ""
        };
        //交互ussd 闪信内容
        $scope.flashSubContent = {
            ID: "",
            instruct: "",
            subContentType: 3,
            content: "",
            contentVali: true,
            isSensitive: false,
            contentDesc: ""
        };
        $scope.addHotlineContentInfo.contentVali = true;
        $scope.addHotlineContentInfo.isSensitive = false;
        $scope.errorcontentVali = true;
        $scope.addHotlineContentInfo.content = '';
        $scope.isSensitive = false;
        $scope.hotMsisdnVali = true;
        $scope.hotMsisdnExist = false;
        $scope.contentVali = true;
		
        // 2102：子企业查询订购关系列表接口，用来初始化投递方式下拉框的值
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
        	// 下拉框(投递方式)
        	$scope.querySubscribeList();
        } else {
        	$scope.addHotlineContentInfo.subServType = "1";
        	//初始化 运营商勾选转态
        	var platformStatus = $scope.platformStatus;
            //查询企业免签名配置
            $scope.checkEnterpriseWithoutSignAdd(platformStatus);
        	// $scope.platformInitAdd(platformStatus);
        }
        
        //初始化所属行业
        $scope.selectedIndustryID = "";
        $scope.selectedIndustry = "";
        //初始化营业执照
        $scope.businessLicenseURL_ = "";
        //初始化其他资质
        $scope.colorContentAndFileList = [];
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];

        $scope.hotMsisdnDesc = '';
        $scope.contentDesc = '';
        $('#addHotlineContent').modal();

    };

    $scope.checkEnterpriseWithoutSignAdd = function (platformStatus){
        //判断父企业id是否等于子企业id
        var req;
        if( $scope.parentEnterpriseID === $scope.enterpriseID){
            req = {
                "enterpriseID": $scope.enterpriseID
            };
        }else{
            req = {
                "enterpriseID": $scope.parentEnterpriseID
            };
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                        if($scope.enterpriseWithoutSignListData.length === 0){
                            //沒有配置过免签名,按原来的流程走
                            $scope.platformInitAdd(platformStatus);
                        }else{
                            //签名不用必填
                            $scope.platformInitAddNoSign(platformStatus);
                        }
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }


    $scope.checkEnterpriseWithoutSignEdit = function (platformStatus){
        //判断父企业id是否等于子企业id
        var req;
        if( $scope.parentEnterpriseID === $scope.enterpriseID){
            req = {
                "enterpriseID": $scope.enterpriseID
            };
        }else{
            req = {
                "enterpriseID": $scope.parentEnterpriseID
            };
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                        if($scope.enterpriseWithoutSignListData.length === 0){
                            //沒有配置过免签名,按原来的流程走
                            $scope.platformInitEdit(platformStatus);
                        }else{
                            //签名不用必填
                            $scope.platformInitEditNoSign(platformStatus);
                        }
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }


    // 编辑热线内容弹窗
    /*$scope.updateHotlineContent = function (item) {
        $scope.addHotlineContentInfo = {};
        $scope.subscribeList = null;
        $scope.subscribeMap = null;
        $scope.addHotlineContentInfo.contentVali = true;
        $scope.addHotlineContentInfo.isSensitive = false;

        $scope.businessLicenseURL_ = "";
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];
        $scope.urlList_2 = [];

        //待审核状态不可修改
        if (item.approveStatus == 2) {
            $scope.tip = "**********";
            $('#myModal').modal();
            return;
        }
        $scope.operate = 'update';
        $scope.contentVali = true;

        $scope.hotMsisdnVali = true;
        $scope.hotMsisdnExist = false;
        $scope.addHotlineContentInfo.subServType = item.subServType.toString();
        $scope.changeSubServerType($scope.addHotlineContentInfo.subServType);
        $scope.addHotlineContentInfo.contentID = item.contentID;
        $scope.addHotlineContentInfo.content = item.content || '';
        $scope.hotMsisdnDesc = '';
        $scope.contentDesc = '';
        
        $scope.contentTitle = item.contentTitle;
        $scope.contentList = [];
        for (var j in item.contentFrameMappingList) {
        	var tepm = item.contentFrameMappingList[j];
        	if (tepm.frameType == 1) {
        		$scope.contentList.push({
        			frameType: 1,
        			framePicUrl: tepm.framePicUrl,
        			formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
        			filesize: tepm.framePicSize
        		});
        	} else if (tepm.frameType == 3) {
        		$scope.contentList.push({
        			frameType: 3,
        			framePicUrl: tepm.framePicUrl,
        			formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
        			filesize: tepm.framePicSize
        		});
        	} else {
        		$scope.contentList.push({
        			frameType: 2,
        			frameTxt: tepm.frameTxt,
        			frameTxtValidate: true
        		});
        	}
        }
        
        //初始化运营商勾选状态
        // 2102：子企业查询订购关系列表接口，用来初始化投递方式下拉框的值
    	$scope.platforms = item.platforms;
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
        	// 下拉框(投递方式)
        	$scope.querySubscribeList();
        } else {
        	var platformStatus = $scope.platformStatus;
        	$scope.platformInitEdit(platformStatus);
        }
        
        //初始化signatrue;
        $scope.addHotlineContentInfo.signature = item.signature;
        $scope.addHotlineContentInfo.sceneDesc = item.sceneDesc;

        //初始化所属行业
        $scope.selectedIndustryID = item.industryType;
        $scope.industryListOld  = item.industryType;;
        if ($scope.industryList) {
            jQuery.each($scope.industryList, function (i, e) {
                if (e.industryID == item.industryType) {
                    $scope.selectedIndustry = e;
                }
            });
            $scope.changeIsSensitive($scope.selectedIndustry)
        }
        $scope.businessLicenseURLOld_ = "";
        //初始化营业执照
        if (item.businessLicenseURL) {
            $scope.businessLicenseURLOld_ = item.businessLicenseURL;
            $scope.businessLicenseURL_ = item.businessLicenseURL;
            $scope.fileUrl_ = CommonUtils.formatPic(item.businessLicenseURL).review;
            $scope.urlList_ = [$scope.fileUrl_];
            $scope.urlList_2 = [$scope.fileUrl_];
        }
        //初始化其他资质
        $scope.colorContentAndFileList = [];
        $scope.certificateUrlListOld = [];
        if (item.certificateUrlList && item.certificateUrlList.length > 0) {
            $scope.certificateUrlListOld=item.certificateUrlList;
            for (var j in item.certificateUrlList) {
                var certiparam = item.certificateUrlList[j];
                var name = certiparam.substring(certiparam.lastIndexOf("/")+1);
                $scope.colorContentAndFileList.push({
                    frameFileUrl: certiparam,
                    formatFrameFileUrl: CommonUtils.formatPic(certiparam).download,
                    filename:name
                })
            }
        }
        //交互ussd
        $scope.addHotlineContentInfo.subContentList = [];
        //交互ussd 闪信内容
        $scope.flashSubContent = {
            subContentType: 3,
            contentVali: true,
            isSensitive: false,
            contentDesc: ""
        };
        $scope.flashSubContentOld = {
            subContentType: 3,
            contentVali: true,
            isSensitive: false,
            contentDesc: ""
        };;
        $scope.addHotlineContentInfo.subContentListOld = [];
        for (var o in item.subset) {
            //为闪信指令
            let object = item.subset[o];
            if (!object.instruct) {
                //交互ussd 闪信内容
                $scope.flashSubContent = {
                    ID: object.contentID,
                    subContentType: 3,
                    content: object.content,
                    contentVali: true,
                    isSensitive: false,
                    contentDesc: ""
                };
                $scope.flashSubContentOld = {
                    ID: object.contentID,
                    subContentType: 3,
                    content: object.content,
                    contentVali: true,
                    isSensitive: false,
                    contentDesc: ""
                };
            } else if (object.instruct === "$$ERROR") {
                //错误指令
                //交互ussd 错误指令内容
                $scope.errorSubContent = {
                    ID: object.contentID,
                    instruct: "",
                    subContentType: 2,
                    content: object.content,
                    contentVali: true,
                    isSensitive: false,
                    contentDesc: ""
                };
                $scope.errorSubContentOld = $scope.errorSubContent;
            } else {
                //普通指令
                $scope.addHotlineContentInfo.subContentList.push({
                    ID: object.contentID,
                    subContentType: 1,
                    replyvalid: false,
                    replyisensitive: "",
                    content: object.content,
                    instruct: object.instruct
                });
                $scope.addHotlineContentInfo.subContentListOld.push({
                    ID: object.contentID,
                    subContentType: 1,
                    replyvalid: false,
                    replyisensitive: "",
                    content: object.content,
                    instruct: object.instruct
                });
            }
        }
        $scope.contentOld =item.content;
        $('#addHotlineContent').modal();
    };*/

    // 编辑热线内容弹窗
    // 111版本
    $scope.updateHotlineContent = function (item) {
        $scope.addHotlineContentInfo = {};
        $scope.hotContentInfoSingle = null;
        $scope.subscribeList = null;
        $scope.subscribeMap = null;
        $scope.addHotlineContentInfo.contentVali = true;
        $scope.addHotlineContentInfo.isSensitive = false;
        $rootScope.$broadcast("event.resetErrTip");
        $scope.businessLicenseURL_ = "";
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];
        $scope.urlList_2 = [];

        //待审核状态不可修改
        if (item.approveStatus == 2) {
            $scope.tip = "**********";
            $('#myModal').modal();
            return;
        }
        $scope.operate = 'update';
        $scope.contentVali = true;

        $scope.hotMsisdnVali = true;
        $scope.hotMsisdnExist = false;

            var req = {
                "contentName": $scope.content || '',
                "enterpriseID": $scope.enterpriseID,
                "servTypeList": [2],
                "contentTypeList": [1, 2],
                "approveStatus":$scope.initSel.auditStatus,
                "getBelongOrg": 0,
                "getFrame": 1,
                "getPushTime": 0,
                "getSwitchState": 0,
                "contentIDList":[item.contentID],
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryHotlineContentInfoListTemp = angular.copy(req);

        req.getBelongOrg = 0;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        // $scope.hotContentInfoListData = result.contentInfoList || [];
                        for (let i = 0; i < result.contentInfoList.length; i++) {
                            $scope.hotContentInfoSingle = result.contentInfoList[i];
                        }
                        $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;

                        $scope.addHotlineContentInfo.subServType = item.subServType.toString();
                        $scope.changeSubServerType($scope.addHotlineContentInfo.subServType);
                        $scope.addHotlineContentInfo.contentID = $scope.hotContentInfoSingle.contentID;
                        $scope.addHotlineContentInfo.content = $scope.hotContentInfoSingle.content || '';
                        $scope.hotMsisdnDesc = '';
                        $scope.contentDesc = '';

                        $scope.contentTitle = $scope.hotContentInfoSingle.contentTitle;
                        $scope.contentList = [];
                        for (var j in $scope.hotContentInfoSingle.contentFrameMappingList) {
                            var tepm = $scope.hotContentInfoSingle.contentFrameMappingList[j];
                            if (tepm.frameType == 1) {
                                $scope.contentList.push({
                                    frameType: 1,
                                    framePicUrl: tepm.framePicUrl,
                                    formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                                    filesize: tepm.framePicSize
                                });
                            } else if (tepm.frameType == 3) {
                                $scope.contentList.push({
                                    frameType: 3,
                                    framePicUrl: tepm.framePicUrl,
                                    formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                                    filesize: tepm.framePicSize
                                });
                            } else {
                                $scope.contentList.push({
                                    frameType: 2,
                                    frameTxt: tepm.frameTxt,
                                    frameTxtValidate: true
                                });
                            }
                        }

                        //初始化运营商勾选状态
                        // 2102：子企业查询订购关系列表接口，用来初始化投递方式下拉框的值
                        $scope.platforms = $scope.hotContentInfoSingle.platforms;
                        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
                            // 下拉框(投递方式)
                            $scope.querySubscribeList();
                        } else {
                            var platformStatus = $scope.platformStatus;
                            //查询企业免签名配置
                            $scope.checkEnterpriseWithoutSignEdit(platformStatus);
                            // $scope.platformInitEdit(platformStatus);
                        }

                        //初始化signatrue;
                        $scope.addHotlineContentInfo.signature = $scope.hotContentInfoSingle.signature;
                        $scope.addHotlineContentInfo.sceneDesc = $scope.hotContentInfoSingle.sceneDesc;

                        //初始化所属行业
                        $scope.selectedIndustryID = $scope.hotContentInfoSingle.industryType;
                        $scope.industryListOld  = $scope.hotContentInfoSingle.industryType;;
                        if ($scope.industryList) {
                            jQuery.each($scope.industryList, function (i, e) {
                                if (e.industryID == $scope.hotContentInfoSingle.industryType) {
                                    $scope.selectedIndustry = e;
                                }
                            });
                            $scope.changeIsSensitive($scope.selectedIndustry)
                        }
                        $scope.businessLicenseURLOld_ = "";
                        //初始化营业执照
                        if (item.businessLicenseURL) {
                            $scope.businessLicenseURLOld_ = $scope.hotContentInfoSingle.businessLicenseURL;
                            $scope.businessLicenseURL_ = $scope.hotContentInfoSingle.businessLicenseURL;
                            $scope.fileUrl_ = CommonUtils.formatPic($scope.hotContentInfoSingle.businessLicenseURL).review;
                            $scope.urlList_ = [$scope.fileUrl_];
                            $scope.urlList_2 = [$scope.fileUrl_];
                        }
                        //初始化其他资质
                        $scope.colorContentAndFileList = [];
                        $scope.certificateUrlListOld = [];
                        if (item.certificateUrlList && $scope.hotContentInfoSingle.certificateUrlList.length > 0) {
                            $scope.certificateUrlListOld=$scope.hotContentInfoSingle.certificateUrlList;
                            for (var j in $scope.hotContentInfoSingle.certificateUrlList) {
                                var certiparam = $scope.hotContentInfoSingle.certificateUrlList[j];
                                var name = certiparam.substring(certiparam.lastIndexOf("/")+1);
                                $scope.colorContentAndFileList.push({
                                    frameFileUrl: certiparam,
                                    formatFrameFileUrl: CommonUtils.formatPic(certiparam).download,
                                    filename:name
                                })
                            }
                        }
                        //交互ussd
                        $scope.addHotlineContentInfo.subContentList = [];
                        //交互ussd 闪信内容
                        $scope.flashSubContent = {
                            subContentType: 3,
                            contentVali: true,
                            isSensitive: false,
                            contentDesc: ""
                        };
                        $scope.flashSubContentOld = {
                            subContentType: 3,
                            contentVali: true,
                            isSensitive: false,
                            contentDesc: ""
                        };;
                        $scope.addHotlineContentInfo.subContentListOld = [];
                        for (var o in $scope.hotContentInfoSingle.subset) {
                            //为闪信指令
                            let object = $scope.hotContentInfoSingle.subset[o];
                            if (!object.instruct) {
                                //交互ussd 闪信内容
                                $scope.flashSubContent = {
                                    ID: object.contentID,
                                    subContentType: 3,
                                    content: object.content,
                                    contentVali: true,
                                    isSensitive: false,
                                    contentDesc: ""
                                };
                                $scope.flashSubContentOld = {
                                    ID: object.contentID,
                                    subContentType: 3,
                                    content: object.content,
                                    contentVali: true,
                                    isSensitive: false,
                                    contentDesc: ""
                                };
                            } else if (object.instruct === "$$ERROR") {
                                //错误指令
                                //交互ussd 错误指令内容
                                $scope.errorSubContent = {
                                    ID: object.contentID,
                                    instruct: "",
                                    subContentType: 2,
                                    content: object.content,
                                    contentVali: true,
                                    isSensitive: false,
                                    contentDesc: ""
                                };
                                $scope.errorSubContentOld = $scope.errorSubContent;
                            } else {
                                //普通指令
                                $scope.addHotlineContentInfo.subContentList.push({
                                    ID: object.contentID,
                                    subContentType: 1,
                                    replyvalid: false,
                                    replyisensitive: "",
                                    content: object.content,
                                    instruct: object.instruct
                                });
                                $scope.addHotlineContentInfo.subContentListOld.push({
                                    ID: object.contentID,
                                    subContentType: 1,
                                    replyvalid: false,
                                    replyisensitive: "",
                                    content: object.content,
                                    instruct: object.instruct
                                });
                            }
                        }
                        $scope.contentOld =$scope.hotContentInfoSingle.content;
                        $('#addHotlineContent').modal();
                    } else {
                        $scope.hotContentInfoListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#addHotlineContent').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.hotContentInfoListData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '**********';
                    $('#addHotlineContent').modal();
                })
            }
        });

    };
    // 删除热线内容弹窗
    $scope.deleteHotlineContent = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteHotlineContent').modal();
    };

    //暂停热线内容
    $scope.suspendHotlineContent = function (item, operaterType) {
        console.log(item);
        var removeReq = {
            "operaterType": operaterType,   //操作类型：0启动，1暂停
            "servType": "2",
            "contentID": item.contentID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/suspendContent",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $scope.queryHotlineContentInfoList();
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
    // 新增号码弹窗
    $scope.addHotLinePop = function (item) {
        $scope.hotlineMsisdnAdd = "";
        if ($scope.hotlineList.length == 0) {
            $scope.tip = "1030120800";
            $('#myModal').modal();
            return;
        }
        $scope.selectedItem = item;
        $scope.pageInfo[1].pageSize = '10';
        $scope.queryHotLineList(item, 'search');
        $('#addHotlinePop').modal();
    };
    // 号码管理弹窗
    $scope.manageHotLinePop = function (item) {
        $scope.hotlineMsisdnDel = "";
        $scope.selectedItem = item;
        $scope.pageInfo[2].pageSize = '10';
        $scope.queryContentRelObjectList(item, 'search');
        $('#manageHotLinePop').modal();
    };
    $scope.queryContentRelObjectList = function (item, condition) {
        $scope.hasChoseDelHotLine = false;
        if (condition != 'justPage') {
            var req = {
                "enterpriseID": parseInt($scope.enterpriseID),
                "contentID": item.contentID,
                "ownerType": 2,
                "hotlineNo": $scope.hotlineMsisdnDel,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[2].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[2].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[2].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[2].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/queryContentRelObjectList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.contentBelongOrgList = result.contentBelongOrgList || [];
                        if ($scope.contentBelongOrgList.length > 0) {
                            for (var i in $scope.contentBelongOrgList) {
                                $scope.contentBelongOrgList[i].checked = false;
                            }
                        }
                        $scope.pageInfo[2].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[2].totalPage = $scope.pageInfo[2].totalCount !== 0 ? Math.ceil($scope.pageInfo[2].totalCount / parseInt($scope.pageInfo[2].pageSize)) : 1;
                    } else {
                        $scope.contentBelongOrgList = [];
                        $scope.pageInfo[2].currentPage = 1;
                        $scope.pageInfo[2].totalCount = 0;
                        $scope.pageInfo[2].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    $scope.queryHotLineList = function (item, condition) {
        $scope.hasChoseAddHotLine = false;
        if (condition != 'justPage') {
            var req = {
                "enterpriseID": parseInt($scope.enterpriseID),
                "servTypeList": [2],
                "hotlineNo": $scope.hotlineMsisdnAdd,
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[1].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[1].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/queryHotlineList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.hotlineList = result.hotlineList || [];
                        for (var i in $scope.hotlineList) {
                            $scope.hotlineList[i].checked = false;
                        }
                        $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[1].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;
                    } else {
                        $scope.hotlineList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    $scope.selectAllHotLine = function () {
        $scope.chooseAllAddHotLine = !$scope.chooseAllAddHotLine;
        if ($scope.chooseAllAddHotLine) {
            for (var i in $scope.hotlineList) {
                $scope.hotlineList[i].checked = true;
            }
        } else {
            for (var i in $scope.hotlineList) {
                $scope.hotlineList[i].checked = false;
            }
        }
    }
    $scope.selectAllDelHotLine = function () {
        $scope.chooseAllDelHotLine = !$scope.chooseAllDelHotLine;
        if ($scope.chooseAllDelHotLine) {
            for (var i in $scope.contentBelongOrgList) {
                $scope.contentBelongOrgList[i].checked = true;
            }
        } else {
            for (var i in $scope.contentBelongOrgList) {
                $scope.contentBelongOrgList[i].checked = false;
            }
        }
    }
    $scope.singleOrBatchAddHotLine = function (condition, item) {
        //单个添加热线号码
        var req = {
            "templateHotlineRel": {
                "templateID": $scope.selectedItem.contentID,
                "enterpriseID": parseInt($scope.enterpriseID)
            }
        };
        if (condition == 'single') {
            req.templateHotlineRel.hotlineList = [item.hotlineNo.toString()]
        } else if (condition == 'batch') {//批量添加
            req.templateHotlineRel.hotlineList = [];
            for (var i in $scope.hotlineList) {
                if ($scope.hotlineList[i].checked) {
                    req.templateHotlineRel.hotlineList.push($scope.hotlineList[i].hotlineNo.toString())
                }
            }
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/addTemplateHotlineRel",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    $scope.tip = data.resultCode;
                    if (data.resultCode == '1030100000') {
                        $scope.tip = '添加成功';
                        $('#myModal').modal();
                        $scope.queryHotLineList($scope.selectedItem, 'search');
                    } else {
                        $scope.tip = data.resultCode;
                        if ($scope.tip == '1030120051') {
                            $scope.tip = '热线内容中热线号码已关联';
                        }
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });

    }
    $scope.sureDelHotLine = function (condition, item) {
        $scope.singleOrBatch = condition;
        $scope.delItem = item;
        $('#deleteHotLinePop').modal();
    }
    $scope.singleOrBatchDelHotLine = function (condition) {
        $('#deleteHotLinePop').modal('hide');
        //单个删除热线号码
        var req = {
            "templateHotlineRel": {
                "templateID": $scope.selectedItem.contentID,
                "enterpriseID": parseInt($scope.enterpriseID)
            }
        };
        if (condition == 'single') {
            req.templateHotlineRel.hotlineList = [$scope.delItem.hotlineNo.toString()]
        } else if (condition == 'batch') {//批量添加
            req.templateHotlineRel.hotlineList = [];
            for (var i in $scope.contentBelongOrgList) {
                if ($scope.contentBelongOrgList[i].checked) {
                    req.templateHotlineRel.hotlineList.push($scope.contentBelongOrgList[i].hotlineNo.toString())
                }
            }
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteTemplateHotlineRel ",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.tip = '删除成功';
                        $('#myModal').modal();
                        $scope.queryContentRelObjectList($scope.selectedItem, 'search');
                        $scope.chooseAllDelHotLine = false;
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });

    }
    /*校验各个字段*/
    $scope.validate = function (context, maxlength, reg) {
        if (!context) {
            return false;
        } else {
            if (context.length > maxlength) {
                return false;
            } else {
                if (!reg.test(context)) {
                    return false;
                } else {
                    return true;
                }
            }
        }
    };
    
    $scope.$watch('addHotlineContentInfo.signature',function(){
	        $scope.checkHotlineContent();
	},true)
	
	

    //212企管平台彩印签名长度优化需求
    $scope.checkHotlineContent = function () {
        $scope.contentVali = true;
        var subServTypeChoise = $("#select").val();
        if ($scope.addHotlineContentInfo != null  && $scope.addHotlineContentInfo.content != null && $scope.addHotlineContentInfo.content != '') {
            var sign = 0;
            if ($scope.addHotlineContentInfo.signature != null && $scope.addHotlineContentInfo.signature != '') {
                sign = $scope.addHotlineContentInfo.signature.length + 2;
            }
            var content = $scope.addHotlineContentInfo.content.length;
            var str_len = sign + content;
            if (str_len > $scope.number) {
            	$scope.contentVali = false;
            } else {
                $scope.contentVali = true;
            }
            if ("string:4" == subServTypeChoise) {
                $scope.dsc = "SIGNATURE_HOTLINE_CONTENTDESC1";
            } else {
                $scope.dsc = "SIGNATURE_HOTLINE_CONTENTDESC";
            }
        } else {
            $scope.sensitiveWords = [];
            $scope.isSensitive = false;
            $scope.sensitiveWordsStr = "";
            $scope.contentVali = false;
            if ("string:4" == subServTypeChoise) {
            	$scope.dsc = "HOTLINE_CONTENTDESC1";
            } else {
            	$scope.dsc = "HOTLINE_CONTENTDESC";
            }
        }
        if (!$scope.contentVali) {
            $scope.contentDesc = $scope.dsc;
        } else {
            $scope.contentDesc = "";
        }
    };
    //彩印内容必填，最长62个字
    $scope.checkHotlineContentUSSD = function (content,type) {
        content.contentVali = true;
        if (content.content) {
            if (content.content.length > 62) {
                content.contentVali = false;
            } else {
                content.contentVali = true;
            }
        }
        else {
            content.sensitiveWords = [];
            content.isSensitive = false;
            content.sensitiveWordsStr = "";
            if(!type){
                content.contentVali = false;
            }
        }
        if (!content.contentVali) {
            content.contentDesc = $scope.dsc;
        } else {
            content.contentDesc = "";
        }
    };

    //敏感词校验
    $scope.sensitiveCheck = function () {
        $scope.checkHotlineContent();
        if (!$scope.contentVali || !$scope.hotMsisdnVali) {
            // return;
        } else {
            $scope.sensitiveWords = [];
            $scope.isSensitive = false;
            $scope.sensitiveWordsStr = "";
            var req = {
                "content": $scope.addHotlineContentInfo.content || '',
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '1030120017') {
                            $scope.sensitiveWords = result.sensitiveWords || [];
                            if ($scope.sensitiveWords.length > 0) {
                                // 110迭代：将 $scope.isSensitive 修改为false
                                $scope.isSensitive = true;
                                $scope.sensitiveWordsStr = $scope.sensitiveWords.join('、');
                            } else {
                                $scope.isSensitive = false;
                            }
                        } else if (data.resultCode == '1030100000') {
                            $scope.sensitiveWords = [];
                            $scope.isSensitive = false;
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                            $scope.tip = '**********';
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }
    };
    
    // 2109：提交时，若运营商选中异网（联通、电信），则二次确认
    $scope.diffNetAuthMaterialsConfirm = function () {
    	if ($scope.addHotlineContentInfo.subServType != 16 
    			&& $scope.addHotlineContentInfo.subServType != 8 
    			&& $scope.addHotlineContentInfo.subServType != 7
    			&& $scope.signatureRequired == '1') {
    		$('#diffNetAuthMaterials').modal();
        } else {
        	$scope.beforeCommit();
        }
    }
    $scope.diffNetAuthMaterialsUploaded = function () {
    	$('#diffNetAuthMaterialsCancel').click();
    	$scope.beforeCommit();
    }

    //确定提交前敏感词校验
    $scope.beforeCommit = function () {

        //彩信 增彩另外校验
        if ($scope.addHotlineContentInfo.subServType == 16 || $scope.addHotlineContentInfo.subServType == 8 || $scope.addHotlineContentInfo.subServType == 7) {
            $scope.createHotlineContent();
            return;
        }
    	if ($scope.addHotlineContentInfo.content != null && $scope.addHotlineContentInfo.content != '') {
            var sign = 0;
            if ($scope.addHotlineContentInfo.signature != null && $scope.addHotlineContentInfo.signature != '') {
                sign = $scope.addHotlineContentInfo.signature.length + 2;
            }
            var content = $scope.addHotlineContentInfo.content.length;
            var str_len = sign + content;
            var totalNum = $scope.addHotlineContentInfo.subServType == '4' ? 750 : 70;;
            if ($scope.flag) {
                totalNum = 748;
            }
            if (str_len > totalNum) {
                $scope.tip = totalNum != 70 ? '1030120085' : '1030120084';
                $('#myModal').modal();
                return;
            }
        }
        $scope.checkHotlineContent();
        if (!$scope.contentVali || !$scope.hotMsisdnVali) {
            return;
        }
        var req = {
            "content": $scope.addHotlineContentInfo.content || '',
        };
        //110迭代：
        $scope.createHotlineContent();
        // 110迭代，提交时取消敏感词校验
        /*RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030120017') {
                        $scope.sensitiveWords = result.sensitiveWords || [];
                        if ($scope.sensitiveWords.length > 0) {

                            $scope.isSensitive = true;
                            $scope.sensitiveWordsStr = $scope.sensitiveWords.join('、');
                        } else {
                            $scope.isSensitive = false;

                            // $scope.createHotlineContent();
                        }

                    } else if (data.resultCode == '1030100000') {
                        $scope.sensitiveWords = [];
                        $scope.isSensitive = false;
                        $scope.createHotlineContent();
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                        // $scope.createHotlineContent();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                        // $scope.createHotlineContent();
                    }
                )
            }
        });*/
    }
    $scope.arrayIsEqual = function(arr1, arr2)  {
        if (!arr1 || !arr2) {
            return false;
        }
        if (arr1.length !== arr2.length) {
            return false;
        }
        for (var i = 0; i < arr1.length; i++) {
            if (arr1[i] instanceof Array && arr2[i] instanceof Array) {
                if (!arr1[i].equals(arr2[i]))
                    return false;
            } else if (arr1[i] !== arr2[i]) {
                return false;
            }
        }
        return true;
    },
    //新增(编辑)热线内容
    $scope.createHotlineContent = function () {
        $scope.addHotlineContentInfo.operatorID = parseInt($scope.operatorID);
        $scope.addHotlineContentInfo.enterpriseID = parseInt($scope.enterpriseID);
        $scope.addHotlineContentInfo.enterpriseName = $scope.enterpriseName;
        $scope.addHotlineContentInfo.subServType = parseInt($scope.addHotlineContentInfo.subServType);
        $scope.addHotlineContentInfo.platforms = $scope.platforms;
        $scope.addHotlineContentInfo.signature = $scope.addHotlineContentInfo.signature;
        $scope.addHotlineContentInfo.industryType = $scope.selectedIndustryID;
        $scope.addHotlineContentInfo.businessLicenseURL = $scope.businessLicenseURL_;
        //提交前清空之前的其他资质list，防止重复
        $scope.certificateUrlList = [];
        jQuery.each($scope.colorContentAndFileList, function (i, e) {
            $scope.certificateUrlList.push(e.frameFileUrl)
        });
        $scope.addHotlineContentInfo.certificateUrlList = $scope.certificateUrlList;
        $scope.addHotlineContentInfo.sceneDesc = $scope.addHotlineContentInfo.sceneDesc;

        // if($scope.hotlineList){
        //   jQuery.each($scope.hotlineList, function (i, e) {
        //         if (e.hotlineNo == $scope.addHotlineContentInfo.hotlineNo) {
        //           $scope.addHotlineContentInfo.hotlineID = e.id;
        //         }
        //       });
        // }
        //彩信增彩增加媒体文件
        if ($scope.addHotlineContentInfo.subServType == 16 || $scope.addHotlineContentInfo.subServType == 8) {
            $scope.addHotlineContentInfo.contentTitle = $scope.contentTitle;
            $scope.addHotlineContentInfo.contentFrameMappingList = [];
            $scope.addHotlineContentInfo.platforms = null;
            var totalSize = 0;
            for (var j in $scope.contentList) {
                var item = $scope.contentList[j];
                if (item.frameType == 1) {
                    $scope.addHotlineContentInfo.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 1,
                        framePicUrl: item.framePicUrl,
                        framePicSize: item.filesize
                    });
                } else if (item.frameType == 3) {
                    $scope.addHotlineContentInfo.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 3,
                        framePicUrl: item.framePicUrl,
                        framePicSize: item.filesize
                    });
                } else {
                    $scope.addHotlineContentInfo.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 2,
                        frameTxt: item.frameTxt,
                    });
                }
                totalSize += item.filesize;
            }
            if ($scope.addHotlineContentInfo.subServType == 16 && totalSize > 2 * 1024 * 1024) {
                $scope.tip = "增彩包大小超过2M";
                $('#myModal').modal();
                return;
            }

        }
        //交互ussd 添加字内容
        if ($scope.addHotlineContentInfo.subServType == 7) {
            //再次校验内容
            // 110版本迭代：交互ussd提交时取消敏感次校验
            // $scope.sensitiveCheckUSSD($scope.addHotlineContentInfo);
            if (!$scope.contentVali || !$scope.hotMsisdnVali) {
                return;
            }
            var tempArry = [];
            //校验指令是否有重复
            for(var f = 0;f<$scope.addHotlineContentInfo.subContentList.length;f++){
                if(!tempArry.includes($scope.addHotlineContentInfo.subContentList[f].instruct)) {
                    tempArry.push($scope.addHotlineContentInfo.subContentList[f].instruct)
                }
            }
            if(tempArry.length!=$scope.addHotlineContentInfo.subContentList.length){
                $scope.tip = "存在重复指令";
                $('#myModal').modal();
                return;
            }


            //判断第一次和第二次提交内容是否一样
            var flag = false;
            if($scope.addHotlineContentInfo.subContentListOld&&$scope.addHotlineContentInfo.subContentListOld.length == $scope.addHotlineContentInfo.subContentList.length){
                for(var i = 0;i<$scope.addHotlineContentInfo.subContentListOld.length;i++){
                    if($scope.addHotlineContentInfo.subContentListOld[i].content!=$scope.addHotlineContentInfo.subContentList[i].content){
                        flag = true;
                    }
                    if($scope.addHotlineContentInfo.subContentListOld[i].instruct!=$scope.addHotlineContentInfo.subContentList[i].instruct){
                        flag = true;
                    }
                }
            }else {
                flag = true;
            }
            if((!flag&&($scope.flashSubContentOld&&$scope.flashSubContentOld.content != $scope.flashSubContent.content))||(!$scope.flashSubContentOld&&$scope.flashSubContent.content)){
                flag = true;
            }
            if(!flag&&$scope.contentOld !=$scope.addHotlineContentInfo.content){
                flag = true;
            }

            if(!flag&&$scope.contentOld !=$scope.addHotlineContentInfo.content){
                flag = true;
            }
            if(!flag&&$scope.businessLicenseURL_ !=$scope.businessLicenseURLOld_){
                flag = true;
            }
            if(!flag&&$scope.selectedIndustryID !=$scope.industryListOld){
                flag = true;
            }
            var listFlag = $scope.arrayIsEqual($scope.certificateUrlList,$scope.certificateUrlListOld);

            if(!flag&&!listFlag){
                flag = true;
            }

            if(!flag){
                $scope.queryHotlineContentInfoList();
                $('#addHotlineContentCancel').click();
                return;
            }

            //添加错误指令内容
            $scope.addHotlineContentInfo.subContentList.push({
                ID: $scope.errorSubContent.ID,
                instruct: $scope.errorSubContent.instruct,
                content: $scope.errorSubContent.content,
                subContentType: 2
            });

            //添加闪信指令内容 //内容存在才添加
            //110迭代：if条件取消 && !$scope.flashSubContent.isSensitive
            if ($scope.flashSubContent.content  ) {
                $scope.addHotlineContentInfo.subContentList.push({
                    ID: $scope.flashSubContent.ID,
                    instruct: $scope.flashSubContent.instruct,
                    content: $scope.flashSubContent.content,
                    subContentType: 3
                });
            }
            
            $scope.addHotlineContentInfo.platforms = '111';
        }
        var req = {
            "hotlineContent": $scope.addHotlineContentInfo
        };
        var serviceUrl = '/ecpmp/ecpmpServices/hotlineService/addHotlineContent';
        if ($scope.operate == 'update') {
            serviceUrl = '/ecpmp/ecpmpServices/hotlineService/updateHotlineContent';
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: serviceUrl,
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.queryHotlineContentInfoList();
                        $('#addHotlineContentCancel').click();
                    }else if(data.resultCode == '1030120090'){
                        $('#addHotlineContentCancel').click();
                        $scope.tip = "信控业务已关闭";
                        $('#myModal').modal();
                    }else {
                        $('#addHotlineContentCancel').click();
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#addHotlineContentCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //删除热线内容
    $scope.delHotlineContent = function () {
        var item = $scope.selectedItemDel;
        console.log(item);
        var removeReq = {
            "operaterType": "2",    //1名片，2热线，3广告
            "id": item.contentID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $('#deleteHotlineContentCancel').click();
                        //$scope.selectedList.splice($.inArray(item.contentID, $scope.selectedList), 1);
                        $scope.queryHotlineContentInfoList();
                    } else {
                        $('#deleteHotlineContentCancel').click();
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteHotlineContentCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //查询热线内容列表
    $scope.queryHotlineContentInfoList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "contentName": $scope.content || '',
                "enterpriseID": $scope.enterpriseID,
                "servTypeList": [2],
                "contentTypeList": [1, 2],
                "approveStatus":$scope.initSel.auditStatus,
                "getBelongOrg": 0,
    	        "getFrame": 0,
    	        "getPushTime": 0,
    	        "getSwitchState": 0,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            if($scope.initSel.subServType!=''){
            	var str =$scope.initSel.subServType.split(',');
            	if(str.length>1){
            		for (var int = 0; int < str.length; int++) {
						str[int]=parseInt(str[int])
					}
            	}else{
            		str[0]=parseInt(str[0])
            	}
                req.subServTypeList=str;
            }else{
                req.subServTypeList=[1,2,3,4,8,16];
            }
            if($scope.initSel.contentNo!=''){
                req.contentIDList=[parseInt($scope.initSel.contentNo)];
            }
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryHotlineContentInfoListTemp = angular.copy(req);
        } else {
            //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.queryHotlineContentInfoListTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        req.getBelongOrg = 0;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.hotContentInfoListData = result.contentInfoList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.hotContentInfoListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.hotContentInfoListData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //REQ-113 REQ-122
    //初始化营业执照上传容器
    $scope.initBusinessURLContainer = function ($scope) {
        $scope.showUpload = false;
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];
        $scope.urlList_2 = [];
        //初始化营业执照上传容器
        $scope.filePicker_ = "filePicker_";
        $scope.accepttype_ = "jpg,jpeg,png";
        $scope.isValidate_ = false;
        $scope.filesize_ = 20;
        $scope.mimetypes_ = ".jpg,.jpeg,.png";
        $scope.isCreateThumbnail_ = true;
        $scope.uploadurl_ = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.uploadDesc_ = "仅支持一张图片，仅支持jpg，jpeg，png格式";
        $scope.numlimit_ = 1;
        $scope.uploadParam_ = {
            enterpriseId: $scope.enterpriseID || '',
            fileUse: 'businessLicense'
        };

        $scope.$on("uploadifyid_1", function (event, fileUrl_) {
            if (fileUrl_) {
                $scope.urlList_ = [fileUrl_];
                $scope.urlList_2 = [CommonUtils.formatPic(fileUrl_).review];
            } else {
                $scope.urlList_ = [];
                $scope.urlList_2 = [];
            }
            $scope.businessLicenseURL_ = fileUrl_;
        });
    }


    //所属行业是否为敏感行业
    $scope.changeIsSensitive = function (selectedIndustry) {
        if (selectedIndustry) {
            $scope.selectedIndustryID = selectedIndustry.industryID;
            $scope.isSensitiveIndustry = selectedIndustry.isSensitiveIndustry;
        } else {
            $scope.selectedIndustryID = '';
            $scope.selectedIndustryName = '';
        }
    };
    //审核意见合并
    $scope.getApproveIdea = function (item) {
        let approveIdea = item.approveIdea||"";
        if (item.subset && item.subset.length > 0) {
            for (let i = 0; i<item.subset.length; i++) {
                if(item.subset[i].approveIdea){
                    if(approveIdea){
                        approveIdea +="|"+item.subset[i].approveIdea;
                    }else {
                        approveIdea =item.subset[i].approveIdea;

                    }

                }
            }
        }
        return approveIdea;
    }
    //查询所属行业
    $scope.queryIndustry = function ($scope) {
        //默认非敏感行业:0-非敏感；1-敏感
        $scope.selectedIndustryID = '';
        $scope.isSensitiveIndustry = '';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $scope.industryList = data.industryList;
                    } else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    //查询企业服务开关
    $scope.queryPlatformStatus = function () {
        var queryServiceControlReq = {
            "enterpriseID": $scope.enterpriseID
        }
        $scope.signatureRequired = '0';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceControl",
            data: JSON.stringify(queryServiceControlReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.platformStatus = result.platformStatus;

                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.initAddHotlineContentPlatforms = function () {
        var platformStatus = $scope.platformStatus;
        $scope.platforms = platformStatus;
        //初始化signature必填状态
        $scope.signatureRequired = '0';
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }
        for (var i = 0; i < 3; i++) {
            $(".platforms .check-li").eq(i).removeAttr("style", "");
            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            $('.platforms .check-li').eq(i).unbind("click");
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
            } else {
                //初始化勾选状态
                $(".platforms .check-li").find('span').eq(i).addClass('checked')
                //绑定点击事件
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';
                    } else {
                        $scope.signatureRequired = '0';
                        $scope.addHotlineContentInfo.sceneDesc = "";

                    }
                    $scope.platforms = _platforms;
                });
            }


        }

    }
    //校验敏感词
    $scope.contentCheck = function (index) {
        console.log($scope.contentList[index]);
        var content = $scope.contentList[index].frameTxt;
        if (!content) {
            $scope.contentValidate = false;
            return;
        }
        $scope.contentValidate = true;
        for (let i = 0; i < $scope.contentList.length; i++) {
            let temp = $scope.contentList[i];
            if (temp.frameType != 3 && temp.frameType != 1 && !temp.frameTxt) {
                $scope.contentValidate = false;
            }
        }
        content = content.replace(/\s/g, '');
        var req = {
            "content": content || '',
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030120017') {
                    //110迭代：
                    $scope.contentList[index].frameTxtValidate = false;
                    $scope.contentList[index].sensitiveWords = result.sensitiveWords || [];
                    $scope.contentList[index].sensitiveWords.join('、');
                } else if (data.resultCode == '1030100000') {
                    $scope.contentList[index].frameTxtValidate = true;

                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
                // })
            },
            error: function () {
                // $rootScope.$apply(function () {
                $scope.tip = '**********';
                $('#myModal').modal();
                // })
            }
        });
    };
    $scope.showBusinessURL = function () {

        $("#showBusinessURL").modal('show');

    }

    $scope.hideBusinessURL = function () {

        $("#showBusinessURL").modal('hide');

    }

    //新增指令回复
    $scope.addMsisdn = function () {
        if ($scope.addHotlineContentInfo.subContentList.length < 3) {
            $scope.addHotlineContentInfo.subContentList.push({
                ID: "",
                instruct: "",
                content: "",
                replyvalid: false,
                replyisensitive: "",
                subContentType: 1
            })
        }
    };
    //新增指令回复
    $scope.delMsisdn = function () {
        if ($scope.addHotlineContentInfo.subContentList.length > 1) {
            $scope.addHotlineContentInfo.subContentList.pop();
        }
    };
//ussd
    $scope.replyCheckissen = function (que) {
        $scope.replycontentDesc = '';
        var replyContent = "";
        $scope.replycontentDesc = 'HOTLINE_CONTENTDESC';
        replyContent = $scope.addHotlineContentInfo.subContentList[que].content;
        if (replyContent.length > 62) {
            $scope.addHotlineContentInfo.subContentList[que].replyvalid = true;
            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
        } else if (replyContent) {
            $scope.addHotlineContentInfo.subContentList[que].replyvalid = false;
            $scope.sensitiveChecknew(replyContent, que);
        }else {
            $scope.addHotlineContentInfo.subContentList[que].replyvalid = true;
            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
        }
    };
    //敏感词校验
    $scope.sensitiveChecknew = function (content, que) {
        var sensitiveWords = [];
//      var isSensitive = false;
        var sensitiveStr = "";
        var req = {
            "content": content
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030120017') {
//            	var sensitiveWords = [];
                        sensitiveWords = result.sensitiveWords || [];
                        if (sensitiveWords.length > 0) {
                            sensitiveStr = sensitiveWords.join('、');
                        }
                        else {
                            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = "";
                        }
                        if (sensitiveStr) {
                            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = sensitiveStr;

                        }
                        else {
                            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
                        }
                    } else if (data.resultCode == '1030100000') {
                        sensitiveWords = [];
                        $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });

    };

    //敏感词校验USSD
    $scope.sensitiveCheckUSSD = function (content,type) {

        $scope.checkHotlineContentUSSD(content,type);
        // if ((!content.contentVali || !content.hotMsisdnVali) || !content.errorcontentVali) {
        //     // return;
        // } else {
        content.sensitiveWords = [];
        content.isSensitive = false;
        content.sensitiveWordsStr = "";
        var req = {
            "content": content.content || ''
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030120017') {
                        content.sensitiveWords = result.sensitiveWords || [];
                        if (content.sensitiveWords.length > 0) {
                            content.isSensitive = true;
                            content.sensitiveWordsStr = content.sensitiveWords.join('、');

                        } else {
                            content.isSensitive = false;
                        }
                    } else if (data.resultCode == '1030100000') {
                        content.sensitiveWords = [];
                        content.isSensitive = false;
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
        // }
    };
    
    // 2102：计费方式选中后，根据选中的计费方式，控制运营商选项
    $scope.platformInit = function (chargeType) {
    	// 以platformStatus作为初始值，运营商对应占位若在运营商清单中不存在，则置为0
    	var platformStatus = $scope.platformStatus;
    	var platformList = [];
    	if ($scope.subscribeMap.get(parseInt($scope.addHotlineContentInfo.subServType))
    			&& $scope.subscribeMap.get(parseInt($scope.addHotlineContentInfo.subServType)).get(chargeType)) 
    	{
    		platformList = $scope.subscribeMap.get(parseInt($scope.addHotlineContentInfo.subServType)).get(chargeType);
    	}
    	var yd = platformStatus.substring(0, 1);
    	var lt = platformStatus.substring(1, 2);
    	var dx = platformStatus.substring(2);
    	if (yd == "1" && platformList.indexOf("1") < 0) {
    		yd = "0";
    	}
    	if (lt == "1" && platformList.indexOf("2") < 0) {
    		lt = "0";
    	}
    	if (dx == "1" && platformList.indexOf("3") < 0) {
    		dx = "0";
    	}
    	platformStatus = yd + lt + dx;
    	
        //初始化勾选状态
        if ($scope.operate == 'add') {
            $scope.checkEnterpriseWithoutSignAdd(platformStatus);
        	// $scope.platformInitAdd(platformStatus);
        } else {
            $scope.checkEnterpriseWithoutSignEdit(platformStatus);
        	// $scope.platformInitEdit(platformStatus);
        }
    };
    
    // 查询订购关系列表接口，获取有效订购关系（失效时间>当前时间）清单，转为订购关系map<业务子类型, map<计费方式, 运营商清单>>
    $scope.querySubscribeList = function () {
    	var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID": $scope.enterpriseID,
                "servType": 2,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                        $scope.subscribeList = data.subscribeInfoList || [];
                        if ($scope.subscribeList.length > 0) 
                        {
                        	// map<业务子类型, map<计费方式, 运营商清单>>
                        	$scope.subscribeMap = $scope.getSubscribeMap();
                            if ($scope.operate == 'add')
                            {
                            	var subServTypeChoise = [];
                            	var subServTypes = [];
                            	for(let key of $scope.subscribeMap.keys()) {
                            		subServTypes.push(key);
                            	};
                            	subServTypes = subServTypes.sort(function(a, b){return a - b});
                            	for(let key of subServTypes) {
                            		var choise = {
                            				"id": key + '',
                            				"name": $scope.typeMap[key]
                            		}
                                    if (7 != key && 11 != key && 4 != key) {
                                        subServTypeChoise.push(choise);
                                    }
                            	};
                            	$scope.subServTypeChoise = subServTypeChoise;
                            	$scope.addHotlineContentInfo.subServType = subServTypeChoise[0].id;
                            }else{
                                var subServTypeChoise = [];
                                var subServTypes = [];
                                for(let key of $scope.subscribeMap.keys()) {
                                    subServTypes.push(key);
                                };
                                subServTypes = subServTypes.sort(function(a, b){return a - b});
                                for(let key of subServTypes) {
                                    var choise = {
                                        "id": key + '',
                                        "name": $scope.typeMap[key]
                                    }
                                    if (7 != key && 11 != key) {
                                        subServTypeChoise.push(choise);
                                    }
                                };
                                $scope.subServTypeChoise = subServTypeChoise;
                            }
                            // 计费方式默认为1
                            $scope.platformInit(1);

                        }
                        else
                        {
                        	if ($scope.operate == 'add')
                            {
                        		$scope.subServTypeChoise = [];
                            }
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }

    //根据订购关系生成map<业务子类型, map<计费方式, 运营商清单>>
    $scope.getSubscribeMap = function () {
    	var resultMap = new Map();
    	for (let i in $scope.subscribeList)
    	{
    		var subscribe = $scope.subscribeList[i];
    		// 计费方式map
    		var chargeTypeMap = resultMap.get(subscribe.subServType);
    		if (!chargeTypeMap)
    		{
    			chargeTypeMap = new Map();
    			resultMap.set(subscribe.subServType, chargeTypeMap);
    		}
    		// 运营商清单
    		var platformList = chargeTypeMap.get(subscribe.chargeType);
    		if (!platformList)
    		{
    			platformList = [];
    			chargeTypeMap.set(subscribe.chargeType, platformList);
    		}
    		var telecomsOperator = !subscribe.reservedsEcpmp ? "1"
                    : !subscribe.reservedsEcpmp.reserved1 ? "1"
                            : subscribe.reservedsEcpmp.reserved1;
    		platformList.push(telecomsOperator);
    	}
    	// 屏显需要加上主叫，被叫和USSD
    	if (resultMap.get(3))
    	{
    		if (!resultMap.get(1))
    		{
    			resultMap.set(1, resultMap.get(3));
    		}
    		if (!resultMap.get(2))
    		{
    			resultMap.set(2, resultMap.get(3));
    		}
    		if (!resultMap.get(7))
    		{
    			resultMap.set(7, resultMap.get(3));
    		}
    	}
    	
    	return resultMap;
    }

    // 获取当前时间YYYYMMDDHHMMSS
    $scope.getCurrentDate = function () {
    	var today = new Date();
        var year = today.getFullYear()+'';
    	var month = today.getMonth() + 1;
    	month = month < 10 ? '0'+month : month;
    	var day = today.getDate() < 10 ? '0'+today.getDate() : today.getDate();
    	var hours = today.getHours() < 10 ? '0'+today.getHours() : today.getHours();
    	var mins = today.getMinutes() < 10 ? '0'+today.getMinutes() : today.getMinutes();
    	var secs = today.getSeconds() < 10 ? '0'+today.getSeconds() : today.getSeconds();
        
        return year + month + day + hours + mins + secs;
    }
    
    // 新增页初始化运营商勾选状态
    $scope.platformInitAdd = function (platformStatus) {
        $scope.platforms = platformStatus;
        //初始化signature必填状态
        $scope.noSign = '1';
        $scope.signatureRequired = '0';
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
	            $('.platforms .check-li').eq(i).unbind("click");
	            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                $(".platforms .check-li").find('span').eq(i).addClass('checked')
                //绑定点击事件
	            $('.platforms .check-li').eq(i).unbind("click");
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';

                    } else {
                        $scope.signatureRequired = '0';
                        $scope.addHotlineContentInfo.sceneDesc = "";

                    }
                    $scope.platforms = _platforms;
                });
            }
        }
    }

    $scope.platformInitAddNoSign = function (platformStatus) {
        $scope.platforms = platformStatus;
        $scope.signatureRequired = '0';
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                $('.platforms .check-li').eq(i).unbind("click");
                $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                $(".platforms .check-li").find('span').eq(i).addClass('checked')
                //绑定点击事件
                $('.platforms .check-li').eq(i).unbind("click");
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';

                    } else {
                        $scope.signatureRequired = '0';
                        $scope.addHotlineContentInfo.sceneDesc = "";

                    }
                    $scope.platforms = _platforms;
                });
            }
        }
    }


    // 编辑页初始化运营商勾选状态
    $scope.platformInitEdit = function (platformStatus) {
    	var platforms = $scope.platforms;
        $scope.noSign = '1';
	    //初始化signature必填状态
	    if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
	        $scope.signatureRequired = '1';
	    }
	    for (var i = 0; i < 3; i++) {
	        if (platformStatus.charAt(i) == '0' && platforms.charAt(i) == '0') {
	            $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
	            $('.platforms .check-li').eq(i).unbind("click");
	            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
	        } else {
	            //初始化勾选状态
	        	$(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
	            if (platforms.charAt(i) == '1') {
	                $(".platforms .check-li").find('span').eq(i).addClass('checked')
	            }
	
	            //绑定点击事件
	            $('.platforms .check-li').eq(i).unbind("click");
	            $('.platforms .check-li').eq(i).on('click', function () {
	            	if ($(this).find('span').hasClass('checked') &&
	            			($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {
	            		
	            		$(this).find('span').removeClass('checked');
	            	} else {
	            		$(this).find('span').addClass('checked')
	            	}
	            	var _platforms = '';
	            	for (var i = 0; i < 3; i++) {
	            		if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
	            			_platforms += '1';
	            		} else {
	            			_platforms += '0';
	            		}
	            	}
	            	if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
	            		$scope.signatureRequired = '1';
	            		
	            	} else {
	            		$scope.signatureRequired = '0';
	            		$scope.addHotlineContentInfo.sceneDesc = "";
	            		
	            	}
	            	$scope.platforms = _platforms;
	            });
	        }
	    }
    }

    $scope.platformInitEditNoSign = function (platformStatus) {
        var platforms = $scope.platforms;
        if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0' && platforms.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                $('.platforms .check-li').eq(i).unbind("click");
                $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                if (platforms.charAt(i) == '1') {
                    $(".platforms .check-li").find('span').eq(i).addClass('checked')
                }

                //绑定点击事件
                $('.platforms .check-li').eq(i).unbind("click");
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';

                    } else {
                        $scope.signatureRequired = '0';
                        $scope.addHotlineContentInfo.sceneDesc = "";

                    }
                    $scope.platforms = _platforms;
                });
            }
        }
    }

}]);
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}]);
