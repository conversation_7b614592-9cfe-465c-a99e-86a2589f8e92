<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EcMemberRecordMapper">
	<resultMap id="ecMemberRecordWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EcMemberRecordWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="pkgSeq" column="pkgSeq" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="orgID" column="orgID" javaType="java.lang.String" />
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="memberNum" column="memberNum" javaType="java.lang.String" />
		<result property="operationType" column="operationType" javaType="java.lang.String" />
		<result property="batchNum" column="batchNum" javaType="java.lang.String" />
		<result property="memberCount" column="memberCount" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
		<result property="status" column="status" javaType="java.lang.String" />
		<result property="productCode" column="productCode" javaType="java.lang.String" />
		<result property="prodordSkuNum" column="prodordSkuNum" javaType="java.lang.String" />
		<result property="rspCode" column="rspCode" javaType="java.lang.String" />
		<result property="rspDesc" column="rspDesc" javaType="java.lang.String" />
	</resultMap>

	<insert id="insert">
	insert into ecpm_t_ecMember_record
		(
		id,
		pkgSeq,
		enterpriseID,
		orgID,
		servType,
		memberNum,
		operationType,
		batchNum,
		memberCount,
		createTime,
		updateTime,
		status,
		productCode,
		prodordSkuNum,
		rspCode,
		rspDesc
		)
		values
			(
			#{id},
			#{pkgSeq},
			#{enterpriseID},
			#{orgID},
			#{servType},
			#{memberNum},
			#{operationType},
			#{batchNum},
			#{memberCount},
			#{createTime},
			#{updateTime},
			#{status},
			#{productCode},
			#{prodordSkuNum},
			#{rspCode},
			#{rspDesc}
			)
	</insert>
	<select id="search" resultMap="ecMemberRecordWrapper">
		SELECT id,
		pkgSeq,
		enterpriseID,
		orgID,
		servType,
		memberNum,
		operationType,
		batchNum,
		memberCount,
		createTime,
		updateTime,
		status,
		productCode,
		prodordSkuNum
		FROM ecpm_t_ecMember_record
		where 1=1
		<if test="enterpriseID!=null">
			and enterpriseID=#{enterpriseID}
		</if>
		<if test="pkgSeq!=null">
			and pkgSeq=#{pkgSeq}
		</if>
		<if test = "prodordSkuNum != null">
			and prodordSkuNum = #{prodordSkuNum}
		</if>
		<if test="servType!=null">
			and servType =#{servType}
		</if>
		<if test="orgID!=null">
			and orgID =#{orgID}
		</if>
		<if test="operationType!=null">
			and operationType =#{operationType}
		</if>
		<if test="memberNum!=null">
			and memberNum =#{memberNum}
		</if>
		<if test="status!=null">
			and status =#{status}
		</if>
	</select>
	<select id="batchSearch" resultMap="ecMemberRecordWrapper">
		SELECT t.id,
		t.pkgSeq,
		t.enterpriseID,
		t.orgID,
		t.servType,
		t.memberNum,
		t.operationType,
		t.batchNum,
		t.memberCount,
		t.createTime,
		t.updateTime,
		t.status,
		t.productCode,
		t.prodordSkuNum
		FROM 
			ecpm_t_ecMember_record t,
			(SELECT t.memberNum,MAX(t.createTime) createTime FROM ecpm_t_ecMember_record t  
			WHERE 1=1
			<if test="enterpriseID!=null">
				and t.enterpriseID=#{enterpriseID}
			</if>
			<if test="orgID!=null">
				and t.orgID =#{orgID}
			</if>
			<if test="memberNumList!=null">
				and t.memberNum in
				<foreach item="item" index="index" collection="memberNumList" open="("
					separator="," close=")">
					#{item}
				</foreach>
			</if>
     		GROUP BY t.memberNum) s
		WHERE 1=1
		<if test="enterpriseID!=null">
			and t.enterpriseID=#{enterpriseID}
		</if>
		<if test="orgID!=null">
			and t.orgID =#{orgID}
		</if>
		<if test="memberNumList!=null">
			and t.memberNum in
			<foreach item="item" index="index" collection="memberNumList" open="("
				separator="," close=")">
				#{item}
			</foreach>
		</if>
		AND t.memberNum=s.memberNum AND t.createTime=s.createTime
	</select>
	<update id="update">
	UPDATE ecpm_t_ecMember_record 
		SET
		<trim suffixOverrides="," suffix="where memberNum = #{memberNum} and pkgSeq = #{pkgSeq}">
			<if test="status !=null">
				status= #{status},
			</if>
			<if test="rspCode !=null">
				rspCode= #{rspCode},
			</if>
			<if test="rspDesc !=null">
				rspDesc= #{rspDesc},
			</if>
			updateTime = SYSDATE()
		</trim>
	</update>
	<update id="updatePkgSeq" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="item" open="" separator=";">
			UPDATE ecpm_t_ecMember_record 
			SET
			pkgSeq = #{item.pkgSeq},
			prodordSkuNum = #{item.prodordSkuNum}
			where
			ID=#{item.id}
		</foreach>
	</update>
	 <select id="findSyncSuccessByBatch" resultType="java.lang.Integer">
        select count(1) from ecpm_t_ecMember_record t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="batchNum != null">
                and t.batchNum = #{batchNum}
            </if>
           	<if test="status !=null">
				and t.status != #{status}
			</if>
        </trim>
    </select>
	<update id="batchUpdateStatus" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
				 item="item" open="" separator=";">
			UPDATE ecpm_t_ecMember_record
			SET
			status = #{item.status},updateTime = SYSDATE()
			where
			ID=#{item.id}
		</foreach>
	</update>
</mapper>