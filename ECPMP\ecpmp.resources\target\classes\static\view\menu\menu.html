<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <title>企业彩印管理平台</title>

        <link class="faviconIco" rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon">
        <link href="../../css/bootstrap.min.css" rel="stylesheet"/>
        <link href="../../css/reset.css" rel="stylesheet"/>
        <link href="../../css/layout.css" rel="stylesheet"/>
        <link href="../../css/animate.min.css" rel="stylesheet"/>

        <!--操作指南样式-->
        <style>
		@charset "UTF-8";
		[ng-cloak] {
			display: none !important;
		}

        .menu_Bar span{
            flex: 1;
            text-align: center;
        }


        </style>
    </head>

    <body ng-app="myNav" ng-controller="navCtrl" ng-cloak>
        <div class="container">
            <div class="main_left">
                <p class="logo" ng-show="agentSsoFlag == false">
                    <img src="../../assets/images/cylogo.png" width="100%"/>
                </p>
                <ul class="sideNav">
                    <li ng-class="{active: nav.currsor}" class="navcss" ng-repeat="nav in navlist track by $index">
                        <div class="navItem" ng-click="navSelect(nav,navlist)">
                            <span class="dib navIcon" ng-class="'icon_{{nav.id}}'"></span>
                            <span class="dib navName hoverName" ng-bind="nav.menuName"></span>
                            <span class="dib moreArr animated" ng-if='nav.id!==1000'></span>
                        </div>
                        <div ng-show="nav.currsor" class="subNav transition">
                            <ul>
                                <li class="subnavcss" ng-class="{active: subnav.currsor}" 
                                    ng-repeat="subnav in navlist2" ng-show="!isAgent || subnav.menuName!='订单管理'">
                                    <a  ng-click="subNavSelect(subnav,navlist2)" class="dib navName"
                                       ng-bind="subnav.menuName"></a>
                                </li>
                            </ul>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="main_right">
            	<p style="position: absolute; line-height: 58px; color: #ffec0d; padding-left: 12px; font-weight: bold;" ng-show="isAgent && isDisplay">
                    	<span ng-bind="'MENU_BALANCE'|translate"></span>&nbsp;&nbsp;
                        <span class="admin-name" style="cursor: default" ng-bind="realBalance" ></span><span ng-bind="'MENU_YUAN'|translate"></span>
                </p>
                <div class="menu_Bar">
                    <span ng-show="agentSsoFlag == false">需要技术支撑请发送至技术支撑邮箱：<EMAIL></span>
                    <ul style="right: 0">
                        <li><span ng-bind="'MENU_HELLO'|translate"></span>&nbsp;&nbsp;
                            <span class="admin-name" ng-bind="accountName" style="cursor: default"></span>
                        </li>
                        <li ng-click="accountManageInfo()">
                            <icon class="admin set-icon"></icon>
                            <span ng-bind="'SETTINGS'|translate"></span>
                        </li>
                        <!--操作手册,样式要改一下-->
                        <!--<li><a href="/qycy/ecpmp/doc/代理商.docx">代理商.doc</a></li>
                        <li><a href="/qycy/ecpmp/doc/省侧部分.docx">省侧部分.doc</a></li>-->

                        <li ng-show="agentSsoFlag == false">
                            <div class="btn-group">

                                <span class="btn dropdown-toggle" data-toggle="dropdown" style="color: #ffffff;padding-top: 4px;"
                                      href="#" ng-bind="'OPERATION_GUIDE'|translate"></span>

                                <ul class="dropdown-menu" style="min-width:125px;right:0px">
                                    <li>
                                        <a href="/qycy/ecpmp/doc/代理商.docx" ng-bind="'Agent_doc'|translate" style="margin-right: 0;" download></a>
                                    </li>
                                    <li>
                                        <a href="/qycy/ecpmp/doc/省侧管理员.docx" ng-bind="'ProvinceManager_doc'|translate" style="margin-right: 0;"  download></a>
                                    </li>
                                    <li>
                                        <a href="/qycy/ecpmp/doc/省侧企业.docx" ng-bind="'ProvinceEnterprise_doc'|translate" style="margin-right: 0;"  download></a>
                                    </li>
                                </ul>
                            </div>

                        </li>


                        <li ng-click="logout()">
                            <icon class="admin back-icon"></icon>
                            <span ng-bind="'MENU_LOGOUT'|translate"></span>
                        </li>
                    </ul>
                </div>
                <div class="cont_main">
                    <iframe id="iframe_w" frameborder="0" width="100%" height="100%"></iframe>


                </div>
            </div>
        </div>
        <!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="menuModal" tabindex="-1" role="dialog"
             aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer" style="text-align:center">
                        <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                                ng-bind="'COMMON_OK'|translate"></button>
                    </div>
                </div>
            </div>
        </div>
        <script src="../../frameworkJs/angular.min.js"></script>
        <script src="../../frameworkJs/jquery-3.5.0.min.js"></script>
        <script src="../../frameworkJs/jquery.cookie.js"></script>
        <script type="text/javascript" src="../../frameworkJs/angular-translate/angular-translate.js"></script>
        <script type="text/javascript"
                src="../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
        <script type="text/javascript" src="../../service/angular-i18n/angular-i18n.js"></script>
        <script src="../../frameworkJs/bootstrap.min.js"></script>
        <script src="../../service/utils/service-ajax.js"></script>
        <script src="menu.js"></script>

    </body>

</html>