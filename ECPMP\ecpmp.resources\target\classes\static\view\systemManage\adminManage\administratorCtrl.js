var app = angular.module("myApp", ["util.ajax", "page",  "angularI18n"])
app.controller("administratorController", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    $scope.queryAccountInfoList();
    $scope.accountStatus = "";
  };

  $scope.gotoAdd=function(){
    location.href='addAccount/addAccount.html';
  };
  $scope.gotoUpdate = function (item){
    location.href = "updateAccount/updateAccount.html?accountID="+item.accountID;
  };
  $scope.gotoSelect = function (item) {
    location.href = "selectAccount/selectAccount.html?accountID="+item.accountID;
  }

  //查询
  $scope.queryAccountInfoList = function (condition) {
    if (condition != 'justPage') {
      var req = {
        "accountQueryType": 1,
        "accountName": $scope.accountName || '',
        "roleName": $scope.roleName || '',
        "accountStatus": $scope.accountStatus || '',
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        },
        "sortField":1,
        "sortType":2
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryAccountInfoListTemp = angular.copy(req);
    } else {
      var req = $scope.queryAccountInfoListTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/queryAccountList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(result);
          if (data.resultCode == '**********') {
            $scope.accountInfoList = result.accountInfoList;
            //组装角色权限
            angular.forEach($scope.accountInfoList, function (item) {
              item.roleName = "";
              angular.forEach(item.roleList, function (role) {
                item.roleName = item.roleName + role.roleName + "/";
              });
              item.roleName = item.roleName.substr(0, item.roleName.length - 1)
            });
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.accountContentInfoData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.accountContentInfoData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
  
  //删除弹窗
  $scope.deleteAccount = function (item){
    $scope.selectedItemDel = item;
    $("#deleteAccount").modal();
  }
  //删除
  $scope.delAccount = function () {
    var item = $scope.selectedItemDel;
    var req = {
      "accountID": item.accountID,
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/deleteAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $('#delAccountCancel').click();
            $scope.queryAccountInfoList();
          } else {
            $('#delAccountCancel').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function (err) {
        $rootScope.$apply(function () {
          $('#delAccountCancel').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  //账号状态
 $scope.getAccountStatus = function (accountStatus) {
      switch(accountStatus) {
          case 1:
           return "正常";
          case 2:
           return "冻结";
          case 3:
           return "停用";
          default:
           return "";
      }
 }
// 账号状态下拉框
$scope.accountStatusList = [
  {
    id: 1,
    name: "正常"
  },
  {
    id: 2,
    name: "冻结"
  },
];

$scope.handleUpdate = function (item) {
    var req = {
          "accountInfo":{
            ...item,
            accountStatus:1,
        }
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/updateAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.queryAccountInfoList();
            $scope.tip = 'COMMON_SAVESUCCESS';
            $('#myModal').modal();
          } else {
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip='**********';
          $('#myModal').modal();
        })
      }
    });

}


});
app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
    }
    return "";
  }
})