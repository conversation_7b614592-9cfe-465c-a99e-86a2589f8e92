var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n", "cy.uploadify","cy.uploadifyfile","cy.icon.content", "preview", "service.common", "page"])
app.controller('addPrintController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils, $http) {
    $scope.operateType = $location.search().operateType || 'add';
    $scope.init =  function () {
    	$scope.htmlflat = false;
        $scope.package = false;
        $scope.guajiLimit = "";
        $scope.mpUnshowSubservtype = [];
        $scope.noSign = '0';

        $http({
            url: "/qycy/ecpmp/i18n/message/message_zh.json",
            method: 'GET'
        }).then(function (response) {
            $scope.guajiLimit = response.guajiLimit;
            $scope.guajiLimitContent = '请输入彩印内容1~' + $scope.guajiLimit + '字';
            $scope.guajiLimitError = '签名+彩印内容不能超过' + $scope.guajiLimit + '位';

            //初始化需要屏蔽的子业务类型，只屏蔽名片
            $scope.mpUnshowSubservtype = response.mpUnshowSubservtype.split(",");

        }).catch(function (data, header, config, status) {
            $scope.guajiLimit = '750';
            $scope.guajiLimitContent = '请输入彩印内容1~' + $scope.guajiLimit + '字';
            $scope.guajiLimitError = '签名+彩印内容不能超过' + $scope.guajiLimit + '位'
        });

        //初始化分组分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '30',
                "currentPage": 1
            }, {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '30',
                "currentPage": 1
            }
        ];

        $scope.allOrgList = [];
        //获取enterpriseID
        $scope.enterpriseID = $.cookie('enterpriseID');
        $scope.parentEnterpriseID = $scope.enterpriseID ;
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.contentID = $location.search().contentID || '';
        $scope.operatorID = $.cookie('accountID');
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.pushObjArrTemp = [];
        $scope.timeError = false;
        $scope.lastMemberNum = 0;
        $scope.chosePushObj = true;
        //彩印标题和彩印内容的敏感校验标识,0表示彩印标题，1表示非挂机彩信的内容，2表示挂机彩信的内容
        $scope.isSensitive = [false, false, false];
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        $scope.isSubEnterpirse = ($.cookie('enterpriseType') == 3);
        //  查询异网通道内容
        $scope.queryDiffnetDeliveryWay();
        if ($scope.operateType == 'add') {
        	$scope.hasItem = true;
        	$scope.hasMonth = true;
            $scope.pictureComplete = $scope.operateType =='add';
        } else {
        	$scope.hasItem = false;
        	$scope.hasMonth = false;
        }
        
        //判断最终调接口的enterpriseID
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            $scope.enterpriseID = $scope.subEnterpriseID;
            $scope.enterpriseName = $scope.subEnterpriseName;
        }
        $scope.accepttype = "jpg,jpeg,png";
        $scope.isValidate = true;
        $scope.filesize = 0.28;
        $scope.mimetypes = ".jpg,.jpeg,.png";
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.numlimit = 1000;
        $scope.urlList1 = [];
        $scope.urlList2 = [];

        $scope.accepttype2 = "jpeg,jpg,png,bmp";
        $scope.isValidate2 = false;
        $scope.filesize2 = 100;
        $scope.mimetypes2 = ".jpeg,.jpg,.png,.bmp";
        $scope.isCreateThumbnail2 = false;
        $scope.uploadurl2 = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.numlimit2 = 1000;
        $scope.uploadCertiDesc = "最多支持6张图片，仅支持jpg，bmp，png，jpeg格式";
        $scope.uploadParam2 = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'certiFile'
        };
        $scope.colorContentAndFileList = [];
        $scope.certificateUrlList = [];
        //文件数
        $scope.fileLength = 0;

        $scope.OrganizationListTotal = 0;
        $scope.checkOrganizationList = [];
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'contentPic'
        };
        // $scope.contentPicUrlList = [];
        $scope.colorContentAndPicList = [];
        $scope.currentRemainQuota = "";
        //总的文本区域长度
        $scope.ctnTextSumLength = 0;
        //总图片大小
        $scope.allPicSize = 0;
        //图片的张数
        $scope.picLength = 0;
        //文本框的个数
        $scope.ctnTextMount = 0;
        $scope.ctnTextSum = "";
        if ($scope.operateType != 'detail') {
            $scope.$watch('colorContentAndPicList', function (newVal, oldVal) {
                $scope.ctnTextSumLength = 0;
                $scope.allPicSize = 0;
                $scope.picLength = 0;
                $scope.ctnTextSum = "";
                $scope.ctnTextMount = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].framePicUrl) {
                        $scope.picLength += 1;
                    } else {
                        $scope.ctnTextMount += 1;
                    }
                    $scope.ctnTextSumLength += newVal[i].frameTxt.length;
                    $scope.ctnTextSum += newVal[i].frameTxt;
                    $scope.allPicSize += newVal[i].filesize;
                }
                //删除的话，就去再次校验敏感词
                if (newVal.length < oldVal.length) {
                    $scope.sensitiveCheck($scope.ctnTextSum, 2)
                }
                if ($scope.picLength >= 5) {
                    $('.webuploader-element-invisible').eq(0).prop('disabled', true).siblings('label').css('cursor', 'default');
                    // $('#filePicker').addClass('bg-disabled');
                } else {
                    $('.webuploader-element-invisible').eq(0).prop('disabled', false).siblings('label').css('cursor', 'pointer')
                    // $('#filePicker').removeClass('bg-disabled');
                }
            }, true)
            $scope.$watch('colorContentAndFileList', function (newVal, oldVal) {
                $scope.fileLength = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameFileUrl) {
                        $scope.fileLength += 1;
                    }
                }

            }, true)
        }
        $scope.$on("uploadifyid1", function (event, fileUrl, fileData) {
            if (fileUrl != '') {
                // $scope.contentPicUrlList.push(fileUrl);
                $scope.colorContentAndPicList.push({
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).review,
                    frameTxt: "",
                    filesize: fileData.file.size
                })
            } else if (fileData != '' || fileData != undefined) {
                // $scope.contentPicUrlList.splice(index, 1);
                // if($scope.urlList){
                //     $scope.urlList.splice(index,1);
                // }
            }
            console.log(fileUrl);
            console.log($scope.colorContentAndPicList);
        });
        //上传文件
        $scope.$on("uploadifyid2", function (event, fileUrl, fileData) {
            if (fileUrl != '') {
                if($scope.colorContentAndFileList.length >= 6){
                    return;
                }
                $scope.colorContentAndFileList.push({
                    frameFileUrl: fileUrl,
                    formatFrameFileUrl: CommonUtils.formatPic(fileUrl).download,
                    filesize: fileData.file.size,
                    filename: fileData.file.name
                })
            } else if (fileData != '' || fileData != undefined) {
                // $scope.contentPicUrlList.splice(index, 1);
                // if($scope.urlList){
                //     $scope.urlList.splice(index,1);
                // }
            }
            console.log(fileUrl);
            console.log($scope.colorContentAndFileList);
            console.log($scope.fileLength);
        });

        //初始化显示的信息
        $scope.sensitiveWords = {
            "0": [],
            "1": [],
            "2": []
        };
        $scope.sensitiveWordsStr = {
            "0": '',
            "1": '',
            "2": ''
        }
        $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];
        $scope.initPrintInfo = {
            thirdpartyType: 0,
            servType: 1,
            subServType: 1,
            hangupType: '',
            deliveryDate: '0000000',
            colorContent: '',
            colorCallContent: '',
            colorTitle: '',
            contentPushTime: {
                startTime: '',
                endTime: '',
            },
            blackwhiteListType: 0,
            isRefYsmb: 0,
            isCallRefYsmb: 0,
            refYsmbID: '',
            refYsmbCallID: '',
            chargeType: 1
        };
        $scope.statusMap = {
            1: "主叫彩印",
            2: "被叫彩印",
            3: "主被叫彩印",
            4: "被叫挂机短信",
            4.1: "主叫挂机短信",
            4.2: "被叫挂机短信",
            4.3: "行业挂机短信",
            8: "挂机彩信"
        };
        $scope.contentType = $scope.initPrintInfo.subServType;

        $scope.ysmbMap = {};
        
        $scope.pakageMap = function (initproduct) {
            return initproduct.productName;
        };
        $scope.pakageChoise = [];
        
        // 2102：子企业的新增页在queryServiceControl接口返回后再初始化投递方式
        if (!$scope.subEnterpriseID || $scope.enterpriseType != 3 || $scope.operateType != 'add') {
            //下拉框(投递方式)
            $scope.subServTypeChoise = [
                {
                    id: 1,
                    name: "主叫彩印"
                },
                {
                    id: 2,
                    name: "被叫彩印"
                },
                {
                    id: 3,
                    name: "主被叫彩印"
                },
                {
                    id: 4.1,
                    name: "主叫挂机短信"
                },
                {
                    id: 4.2,
                    name: "被叫挂机短信"
                },
                {
                    id: 4.3,
                    name: "行业挂机短信"
                },
                {
                    id: 8,
                    name: "挂机彩信"
                },
            ];
        }
        
        $scope.initproduct = {};


        //是否可选择黑名单
        $scope.showBlack = false;
        //是否可选择白名单
        $scope.showWhite = false;

        if ($scope.operateType == 'add') {
            $scope.firstQryMemCntByChgType();
            //查询企业服务开关,当为add时初始化运营商选项按钮和勾选状态
            // 2102：编辑页等查出内容之后再调用
            $scope.queryPlatformStatus();
            // 子企业会调用querySubscribeList接口，最终会调用到queryYsmbList方法。非子企业需单独调一次
            if (!$scope.subEnterpriseID || $scope.enterpriseType != 3) {
                //  查询异网通道内容
                $scope.queryDiffnetDeliveryWay();
            	// 预设固定模板查询
            	// $scope.queryYsmbList($scope.initPrintInfo.subServType);
            }
        }

        //查询所属行业列表
        $scope.queryIndustry($scope);

        //初始化营业执照容器
        $scope.initBusinessURLContainer($scope);

        $scope.queryOrg();
        $scope.CommitUpdateCheck();
        $scope.initPushObjTempURLContainer($scope)


    }

    // 批量导入配置对象上传容器
    $scope.initPushObjTempURLContainer = function ($scope) {
            $scope.accepttypePushObjTemp = "xlsx";
            $scope.isValidatePushObjTemp = false;
            $scope.filesizePushObjTemp = 20;
            $scope.mimetypesPushObjTemp = ".xlsx,.xls";
            $scope.autoPushObjTemp = true;
            $scope.isCreateThumbnailPushObjTemp = false;
            $scope.uploadurlPushObjTemp = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
            $scope.uploadDescPushObjTemp = "仅支持xlsx格式的文件";
            $scope.numlimitPushObjTemp = 1;
            $scope.urlListPushObjTemp = [];
            $scope.uploadParamPushObjTemp = {
                enterpriseId: $scope.enterpriseID,
                fileUse: 'importContentTemplate',
                use:"batch"
            };
            $scope.errorInfoPushObjTemp = '';
            $scope.fileUrlPushObjTemp = '';
            // 上传excel  END
            $scope.$on("uploadifyidPushObjTemp", function (event, fileUrl, index, broadData) {
                if (broadData.file !== "") {
                    $scope.fileNamePushObjTemp = broadData.file.name;
                } else {
                    $scope.fileNamePushObjTemp = "";
                }
                $scope.uploaderPushObjTemp = broadData.uploader;
                $scope.errorInfoPushObjTemp = broadData.errorInfo;
                $scope.fileUrlPushObjTemp = fileUrl;
                $scope.checkOrgData();
            });
    }
    // 校验分组信息
    $scope.checkOrgData = function () {
        var req = {
           "enterpriseID": $scope.enterpriseID,
           "path": $scope.fileUrlPushObjTemp
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/checkOrgData",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$applyAsync(function () {
                    if(result.result.resultCode == '**********'){
                    var organizationList = result?.organizationList || [];
                     if(organizationList.length == 0) {
                        // 提示分组导入异常，请重新确认后导入
                        $scope.tip="分组导入异常，请重新确认后导入";
                        $('#myModal').modal();
                        return;
                      }
                      if($scope.OrganizationListTotal > 50){
                        $scope.checkOrganizationList = organizationList;
                        if(organizationList.length >50) {
                            $scope.queryCheckedOrg();
                        }
                      }else { // <=50
                        var indexList = [];
                        organizationList.forEach(item => {
                            var   index =   $scope.orgList.findIndex(innerItem => innerItem.id == item.id);
                            if(index !=-1) {
                            indexList.push(`${index}`);
                        }
                        })
                        let checkedObject = $("#groupListCls .check-btn");
                        console.log(checkedObject,"checkedObject");
                        for (let key in checkedObject) {
                            if (checkedObject.hasOwnProperty(key)) {
                                if(indexList.includes(key)){
                                     if(!checkedObject.eq(key).hasClass("checked")){
                                         checkedObject.eq(key).click();
                                     }
                                 }
                            }
                        }
                      }
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })

    }


    // 查询默认通道信息
    $scope.queryDiffnetDeliveryWay = function (condition) {
        var req = {
            "isDefault": 1
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryDiffnetDeliveryWay",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1010100000') {
                        var diffnetDeliveryWayList = result.diffnetDeliveryWayList || [];
                        $scope.diffnetDeliveryWayList = diffnetDeliveryWayList;
                        if (diffnetDeliveryWayList)
                        {
                            angular.forEach(diffnetDeliveryWayList, function(item){
                                console.log("查找到的item:",item)
                                // 新增查找当前企业类型、查找当前业务类型。匹配上取wayType值
                                if(item.enterpriseType.includes($scope.enterpriseType)){
                                    if(item.servType.includes('1')){
                                        if(item.platforms.includes('2')){
                                            console.log("默认查找到的联通默认通道:",item.wayType)
                                            $scope.ltWayType = item.wayType
                                        }
                                        if(item.platforms.includes('3')){
                                            console.log("默认查找到的电信默认通道:",item.wayType)
                                            $scope.dxWayType = item.wayType
                                        }
                                    }
                                }
                            });
                            $scope.queryDiffnetDeliveryWay0()
                        }
                        $scope.isSave = false;

                    }
                    else {
                        $scope.tip = data.resultDesc;
                        $('#myModal').modal();
                        $scope.isSave = false;

                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.isSave = false;

                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });


    };
    // 查询默认为0定制通道信息
    $scope.queryDiffnetDeliveryWay0 = function (condition) {
        var req = {
            "isDefault": 0
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryDiffnetDeliveryWay",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1010100000') {
                        var diffnetDeliveryWayList = result.diffnetDeliveryWayList || [];
                        $scope.diffnetDeliveryWayList = diffnetDeliveryWayList;
                        if (diffnetDeliveryWayList)
                        {
                            angular.forEach(diffnetDeliveryWayList, function(item){
                                console.log("查找到的item:",item)
                                // 新增查找当前企业类型、查找当前业务类型。匹配上取wayType值
                                if(item.enterpriseType.includes($scope.enterpriseType)){
                                    if(item.servType.includes('1')){
                                        if(item.platforms.includes('2')){
                                            console.log("定制查找到的联通默认通道:",item.wayType)
                                            $scope.ltWayType = item.wayType
                                        }
                                        if(item.platforms.includes('3')){
                                            console.log("定制查找到的电信默认通道:",item.wayType)
                                            $scope.dxWayType = item.wayType
                                        }
                                    }
                                }
                            });
                        }
                        $scope.isSave = false;
                        $scope.queryYsmbList($scope.initPrintInfo.subServType);
                    }
                    else {
                        $scope.tip = data.resultDesc;
                        $('#myModal').modal();
                        $scope.isSave = false;

                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.isSave = false;

                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });

    };
    // 批量导入配置对象-删除附件
    $scope.deleteTemplate = function () {
          $scope.fileUrlPushObjTemp='';
          $scope.fileNamePushObjTemp = "";
     }
    $scope.checkEnterpriseWithoutSignAdd = function (platformStatus){
        $scope.queryEnterpriseName().then(() => {
        //判断父企业id是否为空
                var req;
                if( $scope.parentEnterpriseID === $scope.enterpriseID){
                    req = {
                        "enterpriseID": $scope.enterpriseID
                    };
                }else{
                    req = {
                        "enterpriseID": $scope.parentEnterpriseID
                    };
                }

                RestClientUtil.ajaxRequest({
                    type: 'POST',
                    url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
                    data: JSON.stringify(req),
                    success: function (result) {
                        $rootScope.$apply(function () {

                            if(result.result.resultCode == '**********'){
                                $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                                if($scope.enterpriseWithoutSignListData.length === 0){
                                    //沒有配置过免签名,按原来的流程走
                                    $scope.platformInitAdd(platformStatus);
                                }else{
                                    //签名不用必填
                                    $scope.platformInitAddNoSign(platformStatus);
                                }
                            }else{
                                $scope.tip=result.result.resultCode;
                                $('#myModal').modal();
                            }
                        })

                    },
                    error:function(){
                        $rootScope.$apply(function(data){
                                $scope.tip="1030120500";
                                $('#myModal').modal();
                            }
                        )
                    }
                })
        });

    }

    $scope.checkEnterpriseWithoutSignEdit = function (platformStatus){
        $scope.queryEnterpriseName().then(() => {
     //判断父企业id是否等于子企业id
             var req;
             if( $scope.parentEnterpriseID === $scope.enterpriseID){
                 req = {
                     "enterpriseID": $scope.enterpriseID
                 };
             }else{
                 req = {
                     "enterpriseID": $scope.parentEnterpriseID
                 };
             }
             RestClientUtil.ajaxRequest({
                 type: 'POST',
                 url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
                 data: JSON.stringify(req),
                 success: function (result) {
                     $rootScope.$apply(function () {
                         if(result.result.resultCode == '**********'){
                             $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                             if($scope.enterpriseWithoutSignListData.length === 0){
                                 //沒有配置过免签名,按原来的流程走
                                 $scope.platformInitEdit(platformStatus);
                             }else{
                                 //签名不用必填
                                 $scope.platformInitEditNoSign(platformStatus);
                             }
                         }else{
                             $scope.tip=result.result.resultCode;
                             $('#myModal').modal();
                         }
                     })

                 },
                 error:function(){
                     $rootScope.$apply(function(data){
                             $scope.tip="1030120500";
                             $('#myModal').modal();
                         }
                     )
                 }
             })
        });

    }

    $scope.mont = function () {
    	if ($scope.operateType == 'add' && $scope.hasMonth) {
    		$(".useOrder .redio-li").find('span').removeClass('checked');
			$(".useOrder .redio-li").find('span').eq(1).addClass('checked');
			$scope.package = true;
			$scope.queryproduct(parseInt($scope.initPrintInfo.subServType));
    		$scope.platformInit(2);
    	}
    }
    $scope.strip = function () {
    	if ($scope.operateType == 'add' && $scope.hasItem) {
    		$(".useOrder .redio-li").find('span').removeClass('checked');
			$(".useOrder .redio-li").find('span').first().addClass('checked');
    		$scope.package = false;
    		$scope.productId = "";
    		$scope.productName = "";
    		$scope.productprice = "";
    		$scope.productReserved3 = "";
    		$scope.platformInit(1);
    	}
    }
    
    // 2102：计费方式选中后，根据选中的计费方式，控制运营商选项
    $scope.platformInit = function (chargeType) {

        if($scope.initPrintInfo.isRefYsmb){
            return
        }

    	// 以platformStatus作为初始值，运营商对应占位若在运营商清单中不存在，则置为0
        let subServTypeTemp1 = $scope.initPrintInfo.subServType;
        if (subServTypeTemp1 == 4.1 || subServTypeTemp1 == 4.2 || subServTypeTemp1 == 4.3) {
            subServTypeTemp1 = 4;
        }
    	var platformStatus = $scope.platformStatus;
    	// console.log("44444444 = " + platformStatus);
    	var platformList = [];
    	if ($scope.subscribeMap.get(subServTypeTemp1)
    			&& $scope.subscribeMap.get(subServTypeTemp1).get(chargeType))
    	{
    		var platformList = $scope.subscribeMap.get(subServTypeTemp1).get(chargeType);
    	}
    	var yd = platformStatus.substring(0, 1);
    	var lt = platformStatus.substring(1, 2);
    	var dx = platformStatus.substring(2);
    	if (yd == "1" && platformList.indexOf("1") < 0) {
    		yd = "0";
    	}
    	if (lt == "1" && platformList.indexOf("2") < 0) {
    		lt = "0";
    	}
    	if (dx == "1" && platformList.indexOf("3") < 0) {
    		dx = "0";
    	}
    	platformStatus = yd + lt + dx;
    	
        //初始化勾选状态
        if ($scope.operateType == 'add') {
            $scope.checkEnterpriseWithoutSignAdd(platformStatus);
        	// $scope.platformInitAdd(platformStatus);
        } else {
            $scope.checkEnterpriseWithoutSignEdit(platformStatus);
        	// $scope.platformInitEdit(platformStatus);
        }
    }

    //其他资质文件下载
    $scope.exportFile = function (downloadUrl) {
        var req = {
            "param":{
                "path": downloadUrl,
                "token": $.cookie("token"),
                "isExport": 0
            },
            "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
            "method":"get"
        }
        CommonUtils.exportFile(req);
    };

    $scope.changeSubServerType = function () {
        // 处理?的展示内容
        if($scope.initPrintInfo.subServType == 1 || $scope.initPrintInfo.subServType == 2 || $scope.initPrintInfo.subServType == 3){
            $scope.contentType = $scope.initPrintInfo.subServType;
        }else {
            $scope.contentType = 0; // 不展示?
        }
    	// 2102：根据选中的业务子类型，从订购关系map取计费方式map的key列表，对key列表中存在的计费方式，支持选中（默认选中第一个），否则置灰
    	if ($scope.operateType == 'add' && $scope.subEnterpriseID && $scope.enterpriseType == 3 && $scope.initPrintInfo.subServType) {
    	    var subServTypeTemp = $scope.initPrintInfo.subServType;
    	    if (subServTypeTemp == 4.1 || subServTypeTemp == 4.2 || subServTypeTemp == 4.3 ) {
                subServTypeTemp = 4;
            }
    		var chargeTypeMap = $scope.subscribeMap.get(subServTypeTemp);
    		// 是否有按条计费
    		var hasItem = false;
    		// 是否有包月计费 
    		var hasMonth = false;
    		for(let key of chargeTypeMap?.keys()) {
    			// 按条计费
    			if (key == 1) {
    				hasItem = true;
    			}
    			// 包月计费 
    			if (key == 2 || key == 3) {
    				hasMonth = true;
    			}
    		}

    		$scope.hasItem = hasItem;
    		$scope.hasMonth = hasMonth;
    		// 存在按条计费
    		if (hasItem) {
    			// 新增页则取消置灰
    		    if ($scope.operateType == 'add') {
    		    	$(".useOrder .redio-li").find('span').first().css({cursor: 'pointer', color: '#333'});
    		    }
    			$scope.initPrintInfo.chargeType = 1;
    			$scope.strip();
    		} else {
    			// 置灰
    			$(".useOrder .redio-li").find('span').first().css({cursor: 'not-allowed', color: 'gray'});
    		}
    		
    		// 存在包月计费
    		if (hasMonth) {
    			// 新增页则取消置灰
    		    if ($scope.operateType == 'add') {
    		    	$(".useOrder .redio-li").find('span').eq(1).css({cursor: 'pointer', color: '#333'});
    		    }
    		    // 没有按条计费，则选中包月计费
    			if (!hasItem)
    			{
    				$scope.initPrintInfo.chargeType = 2;
    				$scope.mont();
    			}
    		} else {
    			// 置灰
    			$(".useOrder .redio-li").find('span').eq(1).css({cursor: 'not-allowed', color: 'gray'});
    		}

            if ($scope.initPrintInfo.subServType == 4.1) {
                $scope.initPrintInfo.hangupType = 1;
            } else if ($scope.initPrintInfo.subServType == 4.2) {
                $scope.initPrintInfo.hangupType = 2;
            }else if ($scope.initPrintInfo.subServType == 4.3) {
                $scope.initPrintInfo.hangupType = 3;
            } else {
                $scope.initPrintInfo.hangupType = '';
            }
    	}
    	
        if ($scope.preSubServType == 8) {
            $scope.initPrintInfo.colorContent = "";
        }
        if ($scope.initPrintInfo.subServType != '8') {
            // $scope.contentPicUrlList = [];
            $scope.colorContentAndPicList = [];
            $scope.isSensitive[0] = false;
            $scope.isSensitive[2] = false;
            $scope.sensitiveWords[0] = [];
            $scope.sensitiveWords[2] = [];
            $scope.sensitiveWordsStr[0] = $scope.sensitiveWordsStr[2] = '';
            $scope.initPrintInfo.colorTitle = "";
        } else {
            $scope.isSensitive[1] = false;
            $scope.sensitiveWords[1] = [];
            $scope.sensitiveWordsStr[1] = '';
            $scope.initPrintInfo.colorContent = "";
        }
        if ((!$scope.subEnterpriseID || $scope.enterpriseType != 3)
        		&& ($scope.initPrintInfo.subServType == '8' || $scope.initPrintInfo.subServType == '4'
                    || $scope.initPrintInfo.subServType == '4.1' || $scope.initPrintInfo.subServType == '4.2' || $scope.initPrintInfo.subServType == '4.3')) {
            $(".useOrder .redio-li").find('span').removeClass('checked');
            $(".useOrder .redio-li").find('span').first().addClass('checked');
            $scope.package = false;
            $scope.firstQryMemCntByChgType();
        } else {
            $scope.firstQryMemCntByChgType();
        }

        if ($scope.initPrintInfo.subServType != 4)
        {
        	$scope.initPrintInfo.sceneDesc = "";
        }
        $scope.preSubServType = $scope.initPrintInfo.subServType;
        //黑名单查询
        $scope.queryBlackWhiteListFun(1);
        //白名单查询
        $scope.queryBlackWhiteListFun(2);
        $scope.queryproduct(parseInt($scope.initPrintInfo.subServType));

        if ($scope.operateType != 'detail') {
        	//预设固定模板查询
        	$scope.ysmbMap = {};
        	$scope.initPrintInfo.isRefYsmb = 0;
        	$scope.initPrintInfo.isCallRefYsmb = 0;
        	$scope.initContentTypeChecked();
        	$scope.initPrintInfo.refYsmbID = '';
        	$scope.initPrintInfo.refYsmbCallID = '';
            $scope.queryDiffnetDeliveryWay()
        	// $scope.queryYsmbList(parseInt($scope.initPrintInfo.subServType));
        }
    }
    
    $scope.queryYsmbList = function (subServType) {
    	if (subServType != '1' && subServType != '2' && subServType != '3' && subServType != '4' && subServType != '4.1' && subServType != '4.2' && subServType != '4.3') {
    		// 固定模板内容置灰
    		$scope.hasYsmb = false;
    		$('.content-type .redio-li').find('span').removeClass('checked');
    		$('.content-type .redio-li').find('span').eq(0).addClass('checked');
    		$('.content-type .redio-li').find('span').eq(1).css({cursor: 'not-allowed', color: 'gray'});
    		return;
    	}
    	var subServTypeQuery = subServType;
    	// if (subServType == '1' || subServType == '2') {
    	// 	subServTypeQuery = '3';
    	// }
    	var supportEnterpriseType = parseInt($scope.enterpriseType);
    	if ($scope.loginRoleType == 'zhike') {
    		supportEnterpriseType = 1;
    	} else if ($scope.loginRoleType == 'agent') {
    		supportEnterpriseType = 3;
    	}
    	if(subServType == '3'){
            subServTypeQuery = '1';
        }
    	if (subServType == '4.1' || subServType == '4.2' || subServType == '4.3'){
            subServTypeQuery = '4';
        }
        var req = {
	        "supportEnterpriseType": supportEnterpriseType,
	        "approveStatus": 3,
	        "contentTypeList": [6],
	        "servTypeList": [1],
	        "subServTypeList": [subServTypeQuery],
	        "getBelongOrg": 0,
	        "getFrame": 0,
	        "getPushTime": 0,
	        "getSwitchState": 0,
            // 新增ltWayType、dxWayType渠道值及ysmbType预设模板/套用模板类型
            "ltWayType":$scope.ltWayType,
            "dxWayType":$scope.dxWayType,
            "ysmbType":1,  //1 为预设固定模板 2为套用模板

	        "pageParameter": {
	            "pageNum": 1,
	            "pageSize": 1024,
	            "isReturnTotal": "0"
	        }
	    };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        if (result.contentInfoList && result.contentInfoList.length > 0) {
                            $scope.ysmbList = result.contentInfoList
                        	// 固定模板内容取消置灰
                    		$scope.hasYsmb = true;
                        	$('.content-type .redio-li').find('span').eq(1).css({cursor: 'pointer', color: '#333'});
                            var ysmbChoise = [];
                            for(let ysmb of result.contentInfoList) {
                        		let realContent = ysmb.content.split('||||')[0];
                            	var choise = {
                    				"id": ysmb.contentID,
                    				"name": realContent
                        		}
                        		ysmbChoise.push(choise);
                        		$scope.ysmbMap[ysmb.contentID] = realContent;
                        	};
                        	// 已关联的预设模板可能被删除，需要将这种模板也加入预设模板列表
                        	if ($scope.initPrintInfo.refYsmbID && !$scope.ysmbMap[$scope.initPrintInfo.refYsmbID]) {
                        		var choise = {
                    				"id": $scope.initPrintInfo.refYsmbID,
                    				"name": $scope.initPrintInfo.colorContent
                        		}
                        		ysmbChoise.push(choise);
                        		$scope.ysmbMap[$scope.initPrintInfo.refYsmbID] = $scope.initPrintInfo.colorContent;
                        	}
                        	$scope.ysmbChoise = ysmbChoise;
                        } else {
                        	// 固定模板内容置灰
                    		$scope.hasYsmb = false;
                    		$('.content-type .redio-li').find('span').eq(1).css({cursor: 'not-allowed', color: 'gray'});
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        });
        if(subServType == '3'){
            req.subServTypeList =  [2];
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '**********') {
                            if (result.contentInfoList && result.contentInfoList.length > 0) {
                                // 固定模板内容取消置灰
                                $scope.hasYsmb = true;
                                $('.callcontent-type .redio-li').find('span').eq(1).css({cursor: 'pointer', color: '#333'});
                                var ysmbCallChoise = [];
                                for(let ysmb of result.contentInfoList) {
                                    let realContent = ysmb.content.split('||||')[0];
                                    var choise = {
                                        "id": ysmb.contentID,
                                        "name": realContent
                                    }
                                    ysmbCallChoise.push(choise);
                                    $scope.ysmbMap[ysmb.contentID] = realContent;
                                };
                                // // 已关联的预设模板可能被删除，需要将这种模板也加入预设模板列表
                                // if ($scope.initPrintInfo.refYsmbID && !$scope.ysmbMap[$scope.initPrintInfo.refYsmbID]) {
                                //     var choise = {
                                //         "id": $scope.initPrintInfo.refYsmbID,
                                //         "name": $scope.initPrintInfo.colorContent
                                //     }
                                //     ysmbChoise.push(choise);
                                //     $scope.ysmbMap[$scope.initPrintInfo.refYsmbID] = $scope.initPrintInfo.colorContent;
                                //     // 主叫和被叫关联同一个的话，直接加入被叫下拉列表
                                //     if ($scope.initPrintInfo.refYsmbID == $scope.initPrintInfo.refYsmbCallID) {
                                //         ysmbCallChoise.push(choise);
                                //     }
                                // }
                                // $scope.ysmbChoise = ysmbChoise;
                                // 处理被叫关联的预设模板
                                if ($scope.initPrintInfo.refYsmbCallID && !$scope.ysmbMap[$scope.initPrintInfo.refYsmbCallID]) {
                                    var choise = {
                                        "id": $scope.initPrintInfo.refYsmbCallID,
                                        "name": $scope.initPrintInfo.colorCallContent
                                    }
                                    ysmbCallChoise.push(choise);
                                    $scope.ysmbMap[$scope.initPrintInfo.refYsmbCallID] = $scope.initPrintInfo.colorCallContent;
                                }
                                $scope.ysmbCallChoise = ysmbCallChoise;
                            } else {
                                // 固定模板内容置灰
                                $scope.hasYsmb = false;
                                $('.callcontent-type .redio-li').find('span').eq(1).css({cursor: 'not-allowed', color: 'gray'});

                            }
                        } else {
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    })
                }
            });

        }
    }

    $scope.changeproduct = function () {
    	if ($(".useOrder .redio-li").find('span').first().hasClass('checked')) {
    		return;
    	}
    	
        $scope.productId = $scope.initproduct.productName;

        for (var int = 0; int < $scope.pakageChoise.length; int++) {
            if ($scope.productId == $scope.pakageChoise[int].id) {
                $scope.productName = $scope.pakageChoise[int].name;
                $scope.productprice = $scope.pakageChoise[int].price;
                $scope.productReserved3 = $scope.pakageChoise[int].reserved3;
            }
        }
        // 按条包月，需根据按条包月订购关系初始化运营商
        if ($scope.productReserved3 == '1') {
        	$scope.platformInit(3);
        } else {
        	$scope.platformInit(2);
        }

    }

    //黑白名单选择
    $scope.changeBlackwhiteListType = function () {
        //使用订单类型
        var _f0 = $('.black-white .redio-li').eq(0).find('span').hasClass('checked'),
            _f1 = $('.black-white .redio-li').eq(1).find('span').hasClass('checked'),
            _f2 = $('.black-white .redio-li').eq(2).find('span').hasClass('checked');

        if (_f1) {
            $scope.initPrintInfo.blackwhiteListType = 1;
        } else if (_f2) {
            $scope.initPrintInfo.blackwhiteListType = 2;
        } else if (_f0) {
            $scope.initPrintInfo.blackwhiteListType = 0;
        }
    }
    // 切换模板 重置运营商勾选
    $scope.changeYsmb = function (id) {
        const ysmbContent = $scope.ysmbList.find(item=>item.contentID == id)
        if(ysmbContent){
            $scope.changeYsmbStatus = true;
            $scope.platformInitAdd(ysmbContent.platforms)
        }
    }

    // 内容类型选择
    $scope.changeContentType = function (data) {
    	if ($scope.operateType == 'detail') {
    		return;
    	}
    	if (data == 0) {
            $scope.initPrintInfo.refYsmbID = '';
    		$('.content-type .redio-li').find('span').removeClass('checked');
            $('.content-type .redio-li').find('span').eq(0).addClass('checked');
    		$scope.initPrintInfo.isRefYsmb = data;
    	} else if (data == 1 && $scope.hasYsmb) {
    		$('.content-type .redio-li').find('span').removeClass('checked');
            $('.content-type .redio-li').find('span').eq(1).addClass('checked');
            $scope.initPrintInfo.isRefYsmb = data;
        }
        if(data){
            if($scope.initPrintInfo.subServType=='3' && $scope.initPrintInfo.isCallRefYsmb){
                $scope.noSign = '0'
            }else{
                $scope.noSign = '0'
            }
        }else{
            $scope.noSign = '1'
            $scope.changeYsmbStatus = false;
            $scope.platformInitAdd($scope.platformStatus1)
        }
    }
    // 内容类型选择之被叫
    $scope.changeCallContentType = function (data) {
    	if ($scope.operateType == 'detail') {
    		return;
    	}
    	if (data == 0) {
    		$scope.initPrintInfo.refYsmbCallID = '';
    		$('.callcontent-type .redio-li').find('span').removeClass('checked');
            $('.callcontent-type .redio-li').find('span').eq(0).addClass('checked');
    		$scope.initPrintInfo.isCallRefYsmb = data;
    	} else if (data == 1 && $scope.hasYsmb) {
    		$('.callcontent-type .redio-li').find('span').removeClass('checked');
            $('.callcontent-type .redio-li').find('span').eq(1).addClass('checked');
            $scope.initPrintInfo.isCallRefYsmb = data;

        }
        if(data){
            if($scope.initPrintInfo.isRefYsmb){
                $scope.noSign = '0'
            }else{
                $scope.noSign = '1'
            }
        }else{
            $scope.noSign = '1'
            $scope.changeYsmbStatus = false;
        }
    }

    $scope.addTextCtn = function () {
        $scope.colorContentAndPicList.push({
            framePicUrl: "",
            formatFramePicUrl: "",
            frameTxt: "",
            filesize: ""
        })
    }
    $scope.deleteCtnOrPic = function (index) {
        $scope.colorContentAndPicList.splice(index, 1);
    }
    $scope.deleteCtnOrFile = function (index) {
        $scope.colorContentAndFileList.splice(index, 1);
    }
    $scope.mapDataToHtmlNum = 0;
    $scope.mapDataToHtml = function () {
        if($scope.mapDataToHtmlNum>0){
            return;
        }
        $scope.mapDataToHtmlNum++;
        $scope.initPrintInfo.hangupType = $scope.contentInfoDetail.hangupType;
        $scope.initPrintInfo.subServType = $scope.contentInfoDetail.subServType;
        $scope.initPrintInfo.deliveryDate = $scope.contentInfoDetail.deliveryDate;
        if ($scope.contentInfoDetail.subServType == 3) {
            $scope.initPrintInfo.colorContent = $scope.contentInfoDetail.content.split('||||')[0];
            $scope.initPrintInfo.colorCallContent = $scope.contentInfoDetail.content.split('||||')[1];
        } else {
            $scope.initPrintInfo.colorContent = $scope.contentInfoDetail.content;
        }
        $scope.initPrintInfo.colorTitle = $scope.contentInfoDetail.contentTitle;
        $scope.initPrintInfo.chargeType = $scope.contentInfoDetail.chargeType;
        $scope.initPrintInfo.blackwhiteListType = $scope.contentInfoDetail.blackwhiteListType;
        // 是否关联预设固定模板
        if ($scope.contentInfoDetail.refYsmbID) {
        	if ($scope.initPrintInfo.subServType == 3) {
        		var refYsmbIDs = $scope.contentInfoDetail.refYsmbID.split('||||');
        		if (refYsmbIDs[0]) {
        			$scope.initPrintInfo.isRefYsmb = 1;
        			$scope.initPrintInfo.refYsmbID = parseInt(refYsmbIDs[0]);
        		}
        		if (refYsmbIDs[1]) {
        			$scope.initPrintInfo.isCallRefYsmb = 1;
        			$scope.initPrintInfo.refYsmbCallID = parseInt(refYsmbIDs[1]);
        		}
                if(refYsmbIDs[0] && refYsmbIDs[1]){
                    $scope.changeYsmbStatus = true;
                }else{
                    $scope.changeYsmbStatus = false;
                }
        	} else {
        		$scope.initPrintInfo.isRefYsmb = 1;
        		$scope.initPrintInfo.refYsmbID = parseInt($scope.contentInfoDetail.refYsmbID);
                $scope.changeYsmbStatus = true;
        	}
        }
        //REQ-113 REQ-122  运营商 签名 所属行业 营业执照
        $scope.platforms = $scope.contentInfoDetail.platforms;
        $scope.platformInitAdd($scope.contentInfoDetail.platforms)
        $scope.initPrintInfo.signature = $scope.contentInfoDetail.signature;
        $scope.initPrintInfo.sceneDesc = $scope.contentInfoDetail.sceneDesc;

        $scope.selectedIndustryID = $scope.contentInfoDetail.industryType;
        if ($scope.industryList) {
            jQuery.each($scope.industryList, function (i, e) {
                if (e.industryID == $scope.contentInfoDetail.industryType) {
                    $scope.selectedIndustry = e;
                }
            });
            $scope.changeIsSensitive($scope.selectedIndustry)

        }

        if ($scope.contentInfoDetail.businessLicenseURL) {
            $scope.businessLicenseURL_ = $scope.contentInfoDetail.businessLicenseURL;
            $scope.fileUrl_ = CommonUtils.formatPic($scope.contentInfoDetail.businessLicenseURL).review;
            $scope.urlList_ = [$scope.fileUrl_];
            $scope.urlList_2 = [$scope.contentInfoDetail.businessLicenseURL];
        }

        $scope.initPrintInfo.isMonthByQuota = $scope.contentInfoDetail.isMonthByQuota;
        $scope.firstQryMemCntByChgType();
        // 2102：非子企业或者编辑页才在这里初始化，子企业新增在changeSubServerType方法中初始化
        if (!$scope.subEnterpriseID || $scope.enterpriseType != 3 || $scope.operateType != 'add')
        {
        	$(".useOrder .redio-li").find('span').removeClass('checked');
        	if ($scope.initPrintInfo.chargeType == 1) {
        		$scope.showpaksge = false;
        		$(".useOrder .redio-li").find('span').first().addClass('checked');
        	} else if ($scope.initPrintInfo.chargeType == 2) {
        		$scope.showpaksge = true;
        		$(".useOrder .redio-li").find('span').eq(1).addClass('checked');
        	}
        }
        //黑白名单匹配
        $(".black-white .redio-li").find('span').removeClass('checked');
        if ($scope.initPrintInfo.blackwhiteListType == 1) {
            $(".black-white .redio-li").find('span').eq(1).addClass('checked');
        } else if ($scope.initPrintInfo.blackwhiteListType == 2) {
            $(".black-white .redio-li").find('span').eq(2).addClass('checked');
        } else {
            $(".black-white .redio-li").find('span').eq(0).addClass('checked');
        }
        //内容类型
        $scope.initContentTypeChecked();
        $scope.queryDiffnetDeliveryWay()
        //预设固定模板查询
        // $scope.queryYsmbList($scope.initPrintInfo.subServType);

        $scope.initPrintInfo.contentPushTime.startTime = $scope.contentInfoDetail.contentPushTime.startTime;
        $scope.initPrintInfo.contentPushTime.endTime = $scope.contentInfoDetail.contentPushTime.endTime;
        $scope.time = $scope.initPrintInfo.contentPushTime.startTime + ' ~ ' + $scope.initPrintInfo.contentPushTime.endTime;
        $(".pushDate .check-li").find('span').removeClass('checked');
        if ($scope.initPrintInfo.deliveryDate == '1111111') {
            $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];
            $(".pushDate .check-li").find('span').first().addClass('checked');
        } else {
            $scope.deliveryDateArr = $scope.initPrintInfo.deliveryDate.split('');
            for (var i in $scope.deliveryDateArr) {
                if ($scope.deliveryDateArr[i] == '1') {
                    var _i = parseInt(i);
                    $(".pushDate .check-li").find('span').eq(_i + 1).addClass('checked');
                }
            }
        }
        if ($scope.contentInfoDetail.contentFrameMappingList && $scope.contentInfoDetail.contentFrameMappingList.length > 0) {
            for (var j in $scope.contentInfoDetail.contentFrameMappingList) {
                var item = $scope.contentInfoDetail.contentFrameMappingList[j];
                //图片类型
                if (item.frameType === 1) {
                    $scope.colorContentAndPicList.push({
                        framePicUrl: item.framePicUrl,
                        formatFramePicUrl: CommonUtils.formatPic(item.framePicUrl).review,
                        frameTxt: "",
                        filesize: item.framePicSize
                    })
                } else if (item.frameType === 2) {
                    $scope.colorContentAndPicList.push({
                        framePicUrl: "",
                        formatFramePicUrl: "",
                        frameTxt: item.frameTxt,
                        filesize: ""
                    })
                }
            }
        }

        if ($scope.contentInfoDetail.certificateUrlList && $scope.contentInfoDetail.certificateUrlList.length > 0) {
            for (var j in $scope.contentInfoDetail.certificateUrlList) {
                var item = $scope.contentInfoDetail.certificateUrlList[j];
                var name = item.substring(item.lastIndexOf("/")+1);
                $scope.colorContentAndFileList.push({
                    frameFileUrl: item,
                    formatFrameFileUrl: CommonUtils.formatPic(item).download,
                    filename:name
                })
            }
        }

        //推送对象匹配
        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr0);
        var _span = $('.pushObj .check-li span'),
            ctnBelongOrg = $scope.contentInfoDetail.contentBelongOrgList;
        if (ctnBelongOrg && ctnBelongOrg.length > 0) {
        	var orgMap = new Map();
            _span.first().removeClass('checked');
            for (var n in ctnBelongOrg) {
                var value = ctnBelongOrg[n].ownerID;
                orgMap.set(value, 1);
                for (var m in $scope.orgList) {
                    if (value == $scope.orgList[m].id) {
                        $scope.pushObjArrTemp[m] = '1';
                        var _m = parseInt(m);
                        _span.eq(_m + 1).addClass('checked');
                    }
                }
            }
            //判断推送对象是否全部
            // if ($scope.orgList.length == orgMap.size) {
            //     _span.removeClass('checked');
            //     _span.first().addClass('checked');
            // } else {
                _span.first().removeClass('checked');
            // }
        } else {    //如果修改查询时组织已经被清空
            _span.first().removeClass('checked');
            $scope.chosePushObj = false;
        }

        // $scope.checkOrganizationList = $scope.contentInfoDetail.contentBelongOrgList;
        $scope.isReserved2 = 0;
        $scope.isReserved8 = 0;
        for(let i in $scope.contentInfoDetail.contentBelongOrgList){
            let org = $scope.findFromGroupList($scope.contentInfoDetail.contentBelongOrgList[i].ownerID, $scope.allOrgList);
            if (org&&!$scope.findFromGroupList($scope.contentInfoDetail.contentBelongOrgList[i].ownerID, $scope.checkOrganizationList)) {
            	if (org.ecpmReserveds.reserved2 == 1)
                {
            		$scope.isReserved2 = 1;
                }
                if (org.ecpmReserveds.reserved8 == 1)
                {
                    $scope.isReserved8 = 1;
                }
                $scope.checkOrganizationList.push(org);
            }
        }
        if($scope.checkOrganizationList.length>50){
            $scope.queryCheckedOrg();
        }
        $scope.htmlflat = true;
        $scope.pictureComplete = $scope.operateType =='modify';
    }
    $scope.firstQryMemCntByChgType = function () {
        var menberCheckReq = {
            "enterpriseID": $scope.enterpriseID,
            "orgIDList": [],
            "servType": 1,
            "subServType": parseInt($scope.initPrintInfo.subServType),
            "chargeType": 2
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryMemberCntByChgType",
            data: JSON.stringify(menberCheckReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.currentRemainQuota = result.memberRemainQuota || 0;
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    }
    //修改和详情进来需要查询一遍
    $scope.queryContentDetail = function () {
        var req = {
            "enterpriseID": $scope.enterpriseID,
            "contentIDList": [parseInt($scope.contentID)],
            "contentTypeList": [1],
            // 新增ltWayType、dxWayType渠道值及ysmbType预设模板/套用模板类型
            // "ltWayType":$scope.ltWayType,
            // "dxWayType":$scope.dxWayType,
            // "ysmbType":1,  //1 为预设固定模板 2为套用模板
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 10,
                "isReturnTotal": "1"
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                var data = result.result;
                if (data.resultCode == '**********') {
                    if (result.contentInfoList && result.contentInfoList.length > 0) {
                        $scope.contentInfoDetail = result.contentInfoList[0];
                        if ($scope.contentInfoDetail.subServType == 4 && $scope.contentInfoDetail.hangupType != undefined && $scope.contentInfoDetail.hangupType != '') {
                            if ($scope.contentInfoDetail.hangupType == 2) {
                                $scope.contentInfoDetail.subServType = 4.2;
                            } else if ($scope.contentInfoDetail.hangupType == 1) {
                                $scope.contentInfoDetail.subServType = 4.1;
                            }else if ($scope.contentInfoDetail.hangupType == 3) {
                                $scope.contentInfoDetail.subServType = 4.3;
                            }
                        }

                        if ($scope.contentInfoDetail.subServType == 4 && ($scope.contentInfoDetail.hangupType == undefined || $scope.contentInfoDetail.hangupType == '')) {
                            $scope.contentInfoDetail.subServType = 4.2;
                        }

                        $scope.pakageinfo = $scope.contentInfoDetail.extInfo;
                        $scope.mapDataToHtml();
                        $scope.initproduct.productName = $scope.getpro($scope.contentInfoDetail.extInfo);
                        $scope.initproduct.objectID = $scope.contentInfoDetail.extInfo.pkgID;
                        if($scope.pakageinfo){
                            if($scope.pakageinfo.pkgID && $scope.pakageinfo.pkgID !="null"){
                                $scope.productId= $scope.pakageinfo.pkgID;
                            }
                            if($scope.pakageinfo.pkgName && $scope.pakageinfo.pkgName !="null"){
                                $scope.productName= $scope.pakageinfo.pkgName;
                            }
                            if($scope.pakageinfo.pkgPrice && $scope.pakageinfo.pkgPrice !="null"){
                                $scope.productprice= $scope.pakageinfo.pkgPrice;
                            }
                        }
                        //查询企业服务开关,当为add时初始化运营商选项按钮和勾选状态
                        $scope.queryPlatformStatus();
                        //黑名单查询
                        $scope.queryBlackWhiteListFun(1);
                        //白名单查询
                        $scope.queryBlackWhiteListFun(2);
                        //再次进行配额校验，校验上次的
                        $scope.orgIds = [];
                        if ($scope.operateType != 'detail') {
                            for (var i in $scope.pushObjArrTemp) {
                                if ($scope.pushObjArrTemp[i] == '1') {
                                    $scope.orgIds.push($scope.orgList[i].id);
                                }
                            }
                            $scope.lastOrgIds = angular.copy($scope.orgIds);
                            if ($scope.contentInfoDetail.chargeType == 2) {
                                $scope.mont();
                            }
                        }

                    }

                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        });
    };
    $scope.getpro = function (extInfo) {
        return extInfo.pkgName;
    }
    //获取queryEnterpriseList接口的数据
    $scope.queryBlackWhiteListFun = function (blackWhiteListType) {
        //查询条件 enterpriseID ， servType ， subServType ， blackWhiteListType
    	$scope.tempSubServType = parseInt($scope.initPrintInfo.subServType)
    	if($scope.initPrintInfo.hangupType != undefined 
    			&& $scope.initPrintInfo.hangupType != '' 
    			&& $scope.initPrintInfo.hangupType == 1) {
    		$scope.tempSubServType = 14;
    	}
        var req = {
            "blackWhite": {
                "blackWhiteListType": blackWhiteListType,
                "servType": 1,
                "enterpriseID": $scope.enterpriseID,
                "subServType": $scope.tempSubServType
            },
            "page": {
                "isReturnTotal": "1",
            }
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/blackWhiteService/queryBlackWhiteList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '**********') {
                        //获取页面的总条数与总页面
                        if ((parseInt(result.totalNum) || 0) != 0) {
                            //黑名单
                            if (blackWhiteListType === 1) {
                                $scope.showBlack = true;
                            }
                            else {
                                $scope.showWhite = true;
                            }
                        }
                        else {
                            //黑名单
                            if (blackWhiteListType === 1) {
                                $scope.showBlack = false;
                            }
                            else {
                                $scope.showWhite = false;
                            }
                        }
                    } else {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        })
    }
    $scope.isMonthByQuota = null;
    //获取queryEnterpriseList接口的数据
    $scope.queryproduct = function (subServiceType) {
        $scope.pakageChoise = [];
        //查询条件 enterpriseID ， servType ， subServType ， blackWhiteListType
        var flag = $(".useOrder .redio-li").find('span').first().hasClass('checked');
        $scope.initPrintInfo.chargeType = flag ? 1 : 2;
        let isMonthByQuota;
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            isMonthByQuota =  $scope.isMonthByQuotaMap.get(parseInt(subServiceType));
        }
        else if ($scope.enterpriseType == 1)
        {
        	isMonthByQuota = 0;
        }
        var req = {
            "productQuery": {
                "productType": 2,
                "subServType": parseInt(subServiceType),
                "isExperience": 0,
                "getMonthByQuota":isMonthByQuota == 2? null:isMonthByQuota
    },
            "pageParameter": {
                "pageNum": 0,
                "pageSize": 100,
                "isReturnTotal": 0
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/productServices/queryProductList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000') {
                        $scope.isMonthByQuota = null;
                        $scope.productList = result.productList;
                        var nochange = result.productList;
                        $scope.productLists = nochange;             
                        for (var int = 0; int < $scope.productList.length; int++) {
                            console.log($scope.productList[int]);
                            $scope.pakageChoise[int] =
                                {
                                    id: $scope.productList[int].objectID,
                                    name: $scope.productList[int].productName,
                                    price: $scope.productList[int].unitPrice,
                                    reserved3: $scope.productList[int].ecpmReserveds.reserved3
                            };
                        }
                        if(result.productList && result.productList.length >0){
                       	 $scope.initproduct.productName = result.productList[0].objectID;
                       	 $scope.changeproduct();
                       }          
                    } else {
                        $scope.tip = result.result.resultCode;
//                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                    $scope.tip = "1030120500";
//                    $('#myModal').modal();
                })
            }
        })
    };
    $scope.queryProductReserved3 = function (subServiceType) {
        let isMonthByQuota;
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            isMonthByQuota =  $scope.isMonthByQuotaMap.get(subServiceType);
        }
        var req = {
            "productQuery": {
                "productType": 2,
                "subServType": subServiceType,
                "isExperience": 0,
                "getMonthByQuota":isMonthByQuota == 2? null:isMonthByQuota
            },
            "pageParameter": {
                "pageNum": 0,
                "pageSize": 100,
                "isReturnTotal": 0
            }
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/productServices/queryProductList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000') {
                    	var reserved3 = '0';
                        for (var i = 0; i < result.productList.length; i++) {
                            if ($scope.initproduct.objectID == result.productList[i].objectID) {
                            	reserved3 = result.productList[i].ecpmReserveds.reserved3;
                            	break;
                            }
                        }
                        // 按条包月，需根据按条包月订购关系初始化运营商
                        if (reserved3 == '1') {
                        	$scope.platformInit(3);
                        } else {
                        	$scope.platformInit(2);
                        }
                    } else {
                        $scope.tip = result.result.resultCode;
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                    $scope.tip = "1030120500";
                })
            }
        })
    };
    $scope.queryCheckedOrg = function () {
        let page =  $scope.pageInfo[1];
        let par = $scope.group($scope.checkOrganizationList, parseInt(page.pageSize));
        page.totalCount = $scope.checkOrganizationList.length;
        page.totalPage = page.totalCount !== 0 ? Math.ceil($scope.checkOrganizationList.length / parseInt(page.pageSize)) : 1;
        $scope.checkOrganizationListTemp = par[page.currentPage - 1];
    };
    $scope.groupSelectAll = function () {

        // 处理函数
        let checkedObject = $("#groupListPop .pushObj .checked");
        for (let i in checkedObject) {
            checkedObject.eq(i).click();
        }
        let gourpObject = $("#groupListPop .pushObj .group-info");
        for (let i in gourpObject) {
            gourpObject.eq(i).click();
        }

    };


    $scope.resetGroupSelect = function () {
        let checkedObject = $("#groupListPop .pushObj .checked");
        for (let i in checkedObject) {
            checkedObject.eq(i).click();
        }
        $scope.checkedOrgListTemp = [];

    };


    $scope.queryOrg = function (pageInfo, condition) {
    	var req = {
                "enterpriseID": $scope.enterpriseID,
                "isReturnMemberCount": 1,
                "branchType": "22",
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": 20000,
                    "isReturnTotal": "1"
                }
            };
    	if (pageInfo) {
    		if (condition != 'justPage') {
            	var req = {
                        "enterpriseID": $scope.enterpriseID,
                        "isReturnMemberCount": 1,
                        "branchType": "22",
                        "pageParameter": {
                            "pageNum": 1,
                            "pageSize": pageInfo[0].pageSize,
                            "isReturnTotal": "1"
                        }
                    };
            }
            else
            {
            	var req = {
                        "enterpriseID": $scope.enterpriseID,
                        "isReturnMemberCount": 1,
                        "branchType": "22",
                        "pageParameter": {
                            "pageNum": pageInfo[0].currentPage,
                            "pageSize": pageInfo[0].pageSize,
                            "isReturnTotal": "1"
                        }
                    };
                
            }
    		if($scope.groupName){
                req.orgName = $scope.groupName;
            }
    	}
        
        
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryOrganizationList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.orgList = result.organizationList || [];
                        for (let i = $scope.orgList.length - 1; i >= 0 ; i--) {
                            if ($scope.orgList[i].ecpmReserveds.reserved1 == $scope.enterpriseID + '_remind'){
                                $scope.orgList.splice(i , 1);
                            }
                        }

                        //不存在分页为第一次查全量数据 //原流程
                        if (!pageInfo) {
                            $scope.allOrgList = result.organizationList || [];
                            $scope.OrganizationListTotal = result.totalNum || 0;
                            $scope.pushObjArr0 = [];
                            $scope.pushObjArr1 = [];
                            if ($scope.orgList.length == 0 && $scope.operateType != 'detail') {
                                $scope.tip = "1030120600";
                                $('#myModal').modal();
                            }
                            for (var i in $scope.orgList) {
                                $scope.pushObjArr0.push('0');
                                $scope.pushObjArr1.push('1');
                            }
                        } else {
                            $scope.pageInfo[0].totalCount = result.totalNum || 0;
                            $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;

                            //渲染勾选
                            if (!$scope.isNoLimitTemp) {
                                $('.pushObj.max .check-li span.isLimit').removeClass("checked");
                                setTimeout(function () {
                                    for (let i = 0; i < $scope.checkedOrgListTemp.length; i++) {
                                        $('.pushObj.max .check-li span[value="' + $scope.checkedOrgListTemp[i].id + '"]').addClass("checked")
                                    }
                                }, 300);
                            }
                        }


                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                    if (!pageInfo) {
                        if ($scope.operateType != 'add') {
                            $scope.queryContentDetail();
                        }
                        else {
                            //新增的查询黑名单和白名单
                            //黑名单查询
                            //$scope.queryBlackWhiteListFun(1);
                            //白名单查询
                            //$scope.queryBlackWhiteListFun(2);
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                    if ($scope.operateType != 'add') {
                        $scope.queryContentDetail();
                    }
                    ;

                })
            }
        });
    }
    $('#time-config').daterangepicker({
        "timePicker": true,
        "timePicker24Hour": true,
        "linkedCalendars": false,
        "autoUpdateInput": true,
        "locale": {
            format: 'HH:mm:ss',
            separator: ' ~ ',
            applyLabel: "应用",
            cancelLabel: "取消",
            resetLabel: "重置",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
        },
        drops: "up",

    }, function (start, end, label) {
        var _self = this,
            start1 = this.startDate.format(this.locale.format),
            end1 = this.endDate.format(this.locale.format);
        $scope.initPrintInfo.contentPushTime.startTime = start1;
        $scope.initPrintInfo.contentPushTime.endTime = end1;

        $rootScope.$apply(function () {
            var start2 = start1.replace(new RegExp(/(:)/g), ""),
                end2 = end1.replace(new RegExp(/(:)/g), "");
            if (parseInt(start2) >= parseInt(end2)) {
                $scope.timeError = true;
            } else {
                $scope.timeError = false;
            }
            $scope.time = start1 + _self.locale.separator + end1;
        })
        console.log(_self.startDate.format(_self.locale.format));
        if (!this.startDate) {
            this.element.val('');
        } else {
            this.element.val(start1 + this.locale.separator + end1);
        }
    });
    $scope.formatDate = function (str) {
        if (!str) {
            return 'format error';
        }
        var newDateStr = "";
        newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
        return newDateStr;

    }
    $scope.goBack = function () {
        window.location.href = '../queryContentInfoList/queryContentInfoList.html';
    }
    
    // 2109：提交时，若运营商选中异网（联通、电信），则二次确认
    $scope.diffNetAuthMaterialsConfirm = function () {
    	if (!$scope.inArray($scope.initPrintInfo.subServType)
    			&& $scope.signatureRequired == '1') {
    		$('#diffNetAuthMaterials').modal();
        } else {
                $scope.sensitiveCheckBeforeSubmit();
        }
    }
    $scope.diffNetAuthMaterialsUploaded = function () {
    	$('#diffNetAuthMaterialsCancel').click();
    	$scope.sensitiveCheckBeforeSubmit();
    }
    
    //提交前校验敏感词
    $scope.sensitiveCheckBeforeSubmit = function () {

        //校验内容不能为空
        if ($scope.initPrintInfo.subServType !== 8) {
            var colorContentLength = $scope.initPrintInfo.colorContent ? $scope.initPrintInfo.colorContent.length : 0;
            colorContentLength = $scope.initPrintInfo.isRefYsmb === 0 ? colorContentLength : 1;
            var colorCallContentLength = $scope.initPrintInfo.colorCallContent ? $scope.initPrintInfo.colorCallContent.length : 0;
            colorCallContentLength = $scope.initPrintInfo.isCallRefYsmb === 0 ? colorCallContentLength : 1;
            var contentLength = colorContentLength + colorCallContentLength;
            if (contentLength <= 0) {
                $scope.tip = '请填写彩印内容！';
                $('#myModal').modal();
                return;
            }
        }

        // 校验签名输入值各字符包括在企业名称中
        // if (($scope.initPrintInfo.isRefYsmb == 0) || ($scope.initPrintInfo.subServType=='3' && $scope.initPrintInfo.isCallRefYsmb == 0)) {
        //     var signature = $scope.initPrintInfo.signature;
        //     if(signature?.length){
        //         for (var i=0; i<signature.length; i++) {
        //             if ($scope.enterpriseName.indexOf(signature.charAt(i)) < 0) {
        //                 $scope.tip = '签名名称需为企业名称中所包含的文字！';
        //                 $('#myModal').modal();
        //                 return;
        //             }
        //         }
        //     }
        // }
        
        if ($scope.initPrintInfo.colorContent != null || $scope.initPrintInfo.colorCallContent != null || $scope.colorContentAndPicList != null) {
            var sign = 0;
            console.log('signature:' + $scope.initPrintInfo.signature);
            if ($scope.initPrintInfo.signature != null) {
                sign = $scope.initPrintInfo.signature.length;
            }

            var total_len = 70;
            var str_len = 0;
            var callContent = null;
            var callStr_len = 0;
            if($scope.initPrintInfo.subServType !== 8){
                var content = $scope.initPrintInfo.colorContent ? $scope.initPrintInfo.colorContent.length : 0;
                callContent = $scope.initPrintInfo.colorCallContent ? $scope.initPrintInfo.colorCallContent.length : null;
                str_len = sign + content;
                callStr_len = sign + callContent;
                total_len = !($scope.initPrintInfo.subServType === 4.1 || $scope.initPrintInfo.subServType === 4.2 || $scope.initPrintInfo.subServType === 4.3 || $scope.initPrintInfo.subServType === 4) ? 70 : 750;
                if (str_len > total_len || (callContent && callStr_len > total_len)) {
                    $scope.tip = '签名加内容文本的长度大于' + total_len;
                    $('#myModal').modal();
                    return;
                }
            }else{
                var newContent = 0;
                for (var j in $scope.colorContentAndPicList) {
                    newContent = newContent + $scope.colorContentAndPicList[j].frameTxt.length;
                }
                str_len = newContent;
                total_len = 750;
                if (str_len > total_len ) {
                    $scope.tip = '内容文本的长度大于' + total_len;
                    $('#myModal').modal();
                    return;
                }
            }
            // var total_len = $scope.initPrintInfo.subServType == "4" || $scope.initPrintInfo.subServType == "8" ? 70 : 62
            // if ($scope.initPrintInfo.subServType == "4.1" || $scope.initPrintInfo.subServType == "4.2"){
            //     total_len = 750;
            // }
        }
        $scope.noRepeat = false;
        // 110迭代：取消敏感词校验
        // if ($scope.initPrintInfo.subServType == '8') {
            // $scope.sensitiveCheck($scope.initPrintInfo.colorTitle, 0);
            // $scope.sensitiveCheck($scope.ctnTextSum, 2);
        // } else {
            // $scope.sensitiveCheck($scope.initPrintInfo.colorContent, 1);
        // }
        // if ($scope.isSensitive[0] || $scope.isSensitive[1] || $scope.isSensitive[2]) {
        //     $scope.tip = '1030120017';
        //     $('#myModal').modal();
        //     return;
        // }
        
        $scope.queryMemberCntByChgType();
        
    }
    //配额校验
    $scope.queryMemberCntByChgType = function () {
        $scope.contentBelongOrgList = [];
        $scope.orgIds = [];
        $scope.memberNum = $scope.memberRemainQuota = "";
        $scope.memberNumTemp = 0;
        $scope.remainQuota = 1;
        $scope.failOrgName = "";
        $scope.noRepeat = false;
        $scope.secondMemberNum = 1;
        //进行配额校验,flag为true的时候表示选择的是按条计费
        var flag = $(".useOrder .redio-li").find('span').first().hasClass('checked');

        $scope.initPrintInfo.chargeType = flag ? 1 : 2;
        if ($scope.productReserved3 == 1)
        {
            $scope.initPrintInfo.chargeType = 1;
        }

        if ($scope.operateType == 'modify' && $scope.initPrintInfo.isMonthByQuota == 1)
        {
        	$scope.initPrintInfo.chargeType = 1;
        }
        if ($scope.OrganizationListTotal > 50) {
            for (let i in $scope.checkOrganizationList) {
                $scope.contentBelongOrgList.push({
                    ownerType: 1,
                    ownerID: $scope.checkOrganizationList[i].id,
                    orgName: $scope.checkOrganizationList[i].orgName,
                    reserved2:$scope.checkOrganizationList[i].ecpmReserveds.reserved2,
                    reserved8:$scope.checkOrganizationList[i].ecpmReserveds.reserved8

                })
                $scope.orgIds.push($scope.checkOrganizationList[i].id);
            }

        } else {
            for (var i in $scope.pushObjArrTemp) {
                if ($scope.pushObjArrTemp[i] == '1' && i != "-1") {
                    $scope.contentBelongOrgList.push({
                        ownerType: 1,
                        ownerID: $scope.orgList[i].id,
                        orgName: $scope.orgList[i].orgName,
                        reserved2:$scope.orgList[i].ecpmReserveds.reserved2,
                        reserved8:$scope.orgList[i].ecpmReserveds.reserved8

                    })
                    $scope.orgIds.push($scope.orgList[i].id);
                }
            }
        }

        $scope.quotaOrgIds = $scope.operateType == 'modify' ? [] : $scope.orgIds;
        //配额校验只传新增的
        if ($scope.operateType == 'modify') {
            var _index;
            for (var i in $scope.orgIds) {
                if ($scope.contentInfoDetail.approveStatus == '4') {
                    $scope.quotaOrgIds.push($scope.orgIds[i]);
                }
                else {
                    _index = $scope.lastOrgIds.indexOf($scope.orgIds[i]);
                    if (_index == -1) {
                        $scope.quotaOrgIds.push($scope.orgIds[i]);
                    }
                }
            }
        }
        var menberCheckReq = {
            "enterpriseID": $scope.enterpriseID,
            "orgIDList": $scope.quotaOrgIds,
            "servType": 1,
            "subServType": parseInt($scope.initPrintInfo.subServType),
            "chargeType": $scope.initPrintInfo.chargeType
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryMemberCntByChgType",
            data: JSON.stringify(menberCheckReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        //如果当前选择的分组下没有成员
                        if (result.memberNum === '0') {
//                            if ($scope.operateType == 'add') {
//                                $scope.tip = '1030120700';
//                                $('#myModal').modal();
//                                return;
//                            }
                            // 特殊场景。修改时，此时需要传入所有勾选分组，再掉一次配额接口
                            var menberCheckReq1 = {
                                "enterpriseID": $scope.enterpriseID,
                                "orgIDList": $scope.orgIds,
                                "servType": 1,
                                "subServType": parseInt($scope.initPrintInfo.subServType),
                                "chargeType": $scope.initPrintInfo.chargeType
                            }
                            RestClientUtil.ajaxRequest({
                                type: 'POST',
                                url: "/ecpmp/ecpmpServices/organizationService/queryMemberCntByChgType",
                                data: JSON.stringify(menberCheckReq1),
                                async: false,
                                success: function (result1) {
                                    var data = result1.result;
                                    if (data.resultCode == '**********') {
//                                        //如果当前选择的分组下没有成员
//                                        if (result1.memberNum === '0') {
//                                            $scope.secondMemberNum = '0';
//                                            return;
//                                        }
                                    } else {
                                        $scope.tip = data.resultCode;
                                        $('#myModal').modal();
                                    }
                                },
                                error: function () {
                                    $scope.tip = "1030120500";
                                    $('#myModal').modal();
                                }
                            });
                        }
//                        if ($scope.secondMemberNum === '0') {
//                            $scope.tip = '1030120700';
//                            $('#myModal').modal();
//                            return;
//                        }
                        //按成员计费，根据memberNum和memberRemainQuota两个字段判断
                        if ($scope.initPrintInfo.chargeType == 2) {
                            $scope.memberNum = result.memberNum ? parseInt(result.memberNum) : 0;
                            $scope.memberRemainQuota = result.memberRemainQuota ? parseInt(result.memberRemainQuota) : 0;
                            // 修改时如果没有新增的分组，就不去比较配额，直接调更新接
//                            if ($scope.contentInfoDetail) {
//                                if ($scope.contentInfoDetail.approveStatus == '4') {
//                                    if ($scope.memberNum > $scope.memberRemainQuota) {
//                                        $('#myModal1').modal();
//                                        return;
//                                    } else {
//                                        $scope.submitReview();
//                                    }
//                                } else {
//                                    if ($scope.memberNum > $scope.memberRemainQuota && $scope.quotaOrgIds.length > 0) {
//                                        $('#myModal1').modal();
//                                        return;
//                                    } else {
//                                        $scope.submitReview();
//                                    }
//                                }
//                            } else {
//                                if ($scope.memberNum > $scope.memberRemainQuota && $scope.quotaOrgIds.length > 0) {
//                                    $('#myModal1').modal();
//                                    return;
//                                } else {
//                                    $scope.submitReview();
//                                }
//                            }
                            $scope.submitReview();
                        } else if ($scope.initPrintInfo.chargeType == 1) {//按次的话，判断remainQuota字段
                            $scope.remainQuota = result.remainQuota || 0;
                            if ($scope.remainQuota <= 0) {
                                $('#myModal1').modal();
                                return;
                            } else {
                                        $scope.submitReview();
                            }
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    }
    $scope.submitReview = function () {

        if ($scope.initPrintInfo.chargeType == 2 && !$scope.productId && $scope.operateType == 'add') {
            alert("请选择套餐包");
            return;
        }
        $scope.noRepeat = false;
        var extInfo = {
            "pkgID": $scope.productId,
            "pkgName": $scope.productName,
            "pkgPrice": $scope.productprice
        }
        //提交前清空之前的其他资质list，防止重复
        $scope.certificateUrlList = [];
        jQuery.each($scope.colorContentAndFileList, function (i, e) {
            $scope.certificateUrlList.push(e.frameFileUrl)
        });
        var req = {
            "contentInfo": {
                "enterpriseID": $scope.enterpriseID,
                "enterpriseName": $scope.enterpriseName,
                "subServType": $scope.initPrintInfo.subServType,
                "hangupType": $scope.initPrintInfo.hangupType,
                "servType": 1,
                "contentPushTime": {
                    "startTime": $scope.initPrintInfo.contentPushTime.startTime,
                    "endTime": $scope.initPrintInfo.contentPushTime.endTime,
                },
                "content": $scope.initPrintInfo.colorContent,
                "calledContent": $scope.initPrintInfo.colorCallContent,
                "contentType": 1,
                "thirdpartyType": 0,
                "deliveryDate": $scope.deliveryDateArr.join(''),
                "operatorID": $scope.operatorID,
                "chargeType": $scope.initPrintInfo.chargeType,
                "contentTitle": $scope.initPrintInfo.colorTitle,
                "signature": $scope.initPrintInfo.signature,
                "platforms": $scope.platforms,
                "industryType": $scope.selectedIndustryID,
                "businessLicenseURL": $scope.businessLicenseURL_,
                "certificateUrlList": $scope.certificateUrlList,
                "extInfo": extInfo,
                "sceneDesc": $scope.initPrintInfo.sceneDesc,
                "isMonthByQuota":$scope.productReserved3
            },
        };
        // 关联预设固定模板的处理
        if ($scope.initPrintInfo.isRefYsmb == 1 || $scope.initPrintInfo.isCallRefYsmb == 1) {
        	var refYsmbID = '';
        	if ($scope.initPrintInfo.isRefYsmb == 1) {
        		refYsmbID = $scope.initPrintInfo.refYsmbID;
        		req.contentInfo.content = $scope.ysmbMap[$scope.initPrintInfo.refYsmbID];
        	}
        	if ($scope.initPrintInfo.subServType == '3' ) {
        		refYsmbID += '||||';
        		if ($scope.initPrintInfo.isCallRefYsmb == 1) {
        			refYsmbID = refYsmbID + $scope.initPrintInfo.refYsmbCallID;
        			req.contentInfo.calledContent = $scope.ysmbMap[$scope.initPrintInfo.refYsmbCallID];
        		}
        	}
        	req.contentInfo.refYsmbID = refYsmbID;
        } else {
        	req.contentInfo.refYsmbID = '-1';
        }
        console.log(req);
        if ($scope.contentBelongOrgList.length > 0) {
            req.contentInfo.contentBelongOrgList = $scope.contentBelongOrgList;
        }
        let isMonthByQuota =  $scope.productReserved3;
        if ($scope.operateType == 'modify' && $scope.initPrintInfo.isMonthByQuota == 1)
        {
        	isMonthByQuota = "1";
        	req.contentInfo.isMonthByQuota = isMonthByQuota;
        }
        if($scope.initPrintInfo.chargeType == 2 && (!isMonthByQuota || isMonthByQuota === "0")){
            for(let i = 0;i<req.contentInfo.contentBelongOrgList.length;i++){
                let org = req.contentInfo.contentBelongOrgList[i];
                if(org.reserved2){
                    $scope.tip = "请选择对应套餐类型分组";
                    $('#myModal').modal();
                    return;
                }
            }
        }
        else if(isMonthByQuota === "1"){
            for(let i = 0;i<req.contentInfo.contentBelongOrgList.length;i++){
                let org = req.contentInfo.contentBelongOrgList[i];
                if(!org.reserved2){
                    $scope.tip = "请选择对应套餐类型分组";
                    $('#myModal').modal();
                    return;
                }
            }
            $scope.currentContent = req.contentInfo
            if ($scope.operateType == 'modify'){
                //i.	提交审核时，若当前内容为按条包月（isMonthByQuota=1）
                //1)	若原内容信息关联的分组列表为空，则额外调用查询内容列表接口（queryContentInfoList），oriContentID=当前内容ID，获取分支内容列表
                if ($scope.contentInfoDetail.contentBelongOrgList.length == 0) {
                    // 2)	若分支内容列表为空
                    if ($scope.branchContentRes.contentInfoList.length == 0) {
                        //若当前内容为屏显（subServType=1/2/3），校验新关联的分组列表是否存在reserved2=1，是则校验失败，提示：请选择与原分组本异网区分相同的分组
                        if ($scope.currentContent.subServType == 1 || $scope.currentContent.subServType == 2 || $scope.currentContent.subServType == 3) {
                            for (let i in $scope.currentContent.contentBelongOrgList) {
                                let item = $scope.currentContent.contentBelongOrgList[i];
                                if (item.reserved2 == '1') {
                                    $scope.tip = "请选择与原分组本异网区分相同的分组";
                                    $('#myModal').modal();
                                    return;
                                }
                            }
                        }
                        //若当前内容为挂短（subServType=4），校验新关联的分组列表是否存在reserved8=1，是则校验失败，提示：请选择与原分组本异网区分相同的分组
                        if ($scope.currentContent.subServType == 4 || $scope.currentContent.subServType == 4.1 || $scope.currentContent.subServType == 4.2 || $scope.currentContent.subServType == 4.3 ) {
                            for (let i in $scope.currentContent.contentBelongOrgList) {
                                let item = $scope.currentContent.contentBelongOrgList[i];
                                if (item.reserved8 == '1') {
                                    $scope.tip = "请选择与原分组本异网区分相同的分组";
                                    $('#myModal').modal();
                                    return;
                                }
                            }
                        }
                    }

                    //3)	若分支内容列表非空（视原内容区分本异网）
                    if ($scope.branchContentRes.contentInfoList.length != 0) {
                        let reserved2List = new Array();
                        let reserved8List = new Array();
                        //若当前内容为屏显（subServType=1/2/3），校验新关联的分组列表是否存在reserved2=1，不存在则校验失败，提示：请选择与原分组本异网区分相同的分组
                        for (let i in $scope.currentContent.contentBelongOrgList) {
                            let item = $scope.currentContent.contentBelongOrgList[i];
                            reserved2List.push(item.reserved2);
                            reserved8List.push(item.reserved8)
                        }
                        if ("1/2/3".includes($scope.currentContent.subServType.toString())) {
                            if (!reserved2List.includes('1')) {
                                $scope.tip = "请选择与原分组本异网区分相同的分组";
                                $('#myModal').modal();
                                return;
                            }
                        }
                        //若当前内容为挂短（subServType=4），校验新关联的分组列表是否存在reserved8=1，不存在则校验失败：请选择与原分组本异网区分相同的分组
                        if ($scope.currentContent.subServType == 4 || $scope.currentContent.subServType == 4.1 || $scope.currentContent.subServType == 4.2 || $scope.currentContent.subServType == 4.3) {
                            if (!reserved8List.includes('1')) {
                                $scope.tip = "请选择与原分组本异网区分相同的分组";
                                $('#myModal').modal();
                                return;
                            }
                        }
                    }
                }
            }

        }
        else if ($scope.initPrintInfo.chargeType == 1 && isMonthByQuota !== "1")
        {
        	for(let i = 0;i<req.contentInfo.contentBelongOrgList.length;i++){
                let org = req.contentInfo.contentBelongOrgList[i];
                if(org.reserved2){
                    $scope.tip = "请选择对应套餐类型分组";
                    $('#myModal').modal();
                    return;
                }
            }
        }

            if ($scope.operateType == 'modify') {
                if ($scope.contentInfoDetail.contentBelongOrgList.length > 0) {

                if ($scope.initPrintInfo.subServType == 4 || $scope.initPrintInfo.subServType == 4.1 || $scope.initPrintInfo.subServType == 4.2 || $scope.initPrintInfo.subServType == 4.3) {
                    if ($scope.isReserved8 == 1) {
                        var flat = true;
                        for (let i = 0; i < req.contentInfo.contentBelongOrgList.length; i++) {
                            let org = req.contentInfo.contentBelongOrgList[i];
                            if (org.reserved8 == 1) {
                                flat = false;
                            }
                        }
                        if (flat) {
                            $scope.tip = "请选择与原分组本异网区分相同的分组";
                            $('#myModal').modal();
                            return;
                        }
                    }
                    if ($scope.isReserved8 == 0) {
                        var flat = false;
                        for (let i = 0; i < req.contentInfo.contentBelongOrgList.length; i++) {
                            let org = req.contentInfo.contentBelongOrgList[i];
                            if (org.reserved8 == 1) {
                                flat = true;
                            }
                        }
                        if (flat) {
                            $scope.tip = "请选择与原分组本异网区分相同的分组";
                            $('#myModal').modal();
                            return;
                        }
                    }
                } else if ($scope.initPrintInfo.subServType == 1 || $scope.initPrintInfo.subServType == 2 || $scope.initPrintInfo.subServType == 3) {
                    if ($scope.isReserved2 == 1) {
                        var flat = true;
                        for (let i = 0; i < req.contentInfo.contentBelongOrgList.length; i++) {
                            let org = req.contentInfo.contentBelongOrgList[i];
                            if (org.reserved2 == 1) {
                                flat = false;
                            }
                        }
                        if (flat) {
                            $scope.tip = "请选择与原分组本异网区分相同的分组";
                            $('#myModal').modal();
                            return;
                        }
                    }
                    if ($scope.isReserved2 == 0) {
                        var flat = false;
                        for (let i = 0; i < req.contentInfo.contentBelongOrgList.length; i++) {
                            let org = req.contentInfo.contentBelongOrgList[i];
                            if (org.reserved2 == 1) {
                                flat = true;
                            }
                        }
                        if (flat) {
                            $scope.tip = "请选择与原分组本异网区分相同的分组";
                            $('#myModal').modal();
                            return;
                        }
                    }
                }
            }
        }

        // 封装图片内容
        if ($scope.initPrintInfo.subServType == '8') {
            req.contentInfo.contentFrameMappingList = [];
            for (var j in $scope.colorContentAndPicList) {
                var item = $scope.colorContentAndPicList[j];
                if (item.framePicUrl) {
                    req.contentInfo.contentFrameMappingList.push({
                        frameNo: j + 1,
                        frameType: 1,
                        framePicUrl: item.framePicUrl,
                        framePicSize: item.framePicSize
                    })
                } else {
                    req.contentInfo.contentFrameMappingList.push({
                        frameNo: j + 1,
                        frameType: 2,
                        frameTxt: item.frameTxt,
                    })
                }
            }
        }

        //使用订单类型
        var _f0 = $('.black-white .redio-li').eq(0).find('span').hasClass('checked'),
            _f1 = $('.black-white .redio-li').eq(1).find('span').hasClass('checked'),
            _f2 = $('.black-white .redio-li').eq(2).find('span').hasClass('checked');

        if (_f1) {
            req.contentInfo.blackwhiteListType = 1;
        } else if (_f2) {
            req.contentInfo.blackwhiteListType = 2;
        } else if (_f0) {
            req.contentInfo.blackwhiteListType = 0;
        }
        if ($scope.noContentRepeat)
        {
            return;
        }
        if ($scope.operateType == 'add') {
        	var isUnshow = false;
        	for (i = 0; i < $scope.mpUnshowSubservtype.length; i++) {
                if ($scope.mpUnshowSubservtype[i] == $scope.initPrintInfo.subServType) {

                    //挂彩，这里默认0
                	isUnshow = true;
                }

            }
            if ($scope.signatureRequired == '0' && isUnshow) {
                req.contentInfo.platforms = 100;
            }
            $scope.noContentRepeat = true;
            console.log(req)
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/createContent",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        $scope.noContentRepeat = false;
                        var data = result.result;
                        if (data.resultCode == '**********') {
                            window.location.href = '../queryContentInfoList/queryContentInfoList.html';
                        } else if (data.resultCode == '1030120030') {
                            $scope.failOrgName = result.extInfo.failOrgName.replace(/\|/g, ',');
                            $scope.noRepeat = true;
                            $scope.tip = "";
                            $('#myModal').modal();
                        } else if (data.resultCode == '1010120082') {
                            $scope.tip = data.resultDesc;
                            $('#myModal').modal();
                        } else if (data.resultCode == '1030120078') {
                            $scope.noRepeat = false;
                            $scope.tip = "1030120083";
                            $('#myModal').modal();
                        } else if (data.resultCode == '1030120089') {
                            $('#impoMebrPop').modal("hide");
                            $('#addMemCancel').click();
                            $scope.tip = data.resultDesc;
                            $('#myModal').modal();
                        }
                        else if (data.resultCode == '1030120090') {
                            $('#impoMebrPop').modal("hide");
                            $('#addMemCancel').click();
                            $scope.tip = "当前企业信控关闭，无法新增内容";
                            $('#myModal').modal();
                        } else {
                            $scope.noRepeat = false;
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })

                },
                error: function () {
                    $rootScope.$apply(function () {
                        	$scope.noContentRepeat = false;

                            $scope.noRepeat = false;
                            $scope.tip = "1030120500";
                            $('#myModal').modal();
                        }
                    )
                }
            });
        } else if ($scope.operateType == 'modify') {
            req.contentInfo.contentID = $scope.contentID;
            $scope.noContentRepeat = true;

            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/updateContent",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        $scope.noContentRepeat = false;

                        var data = result.result;
                        if (data.resultCode == '**********') {
                            window.location.href = '../queryContentInfoList/queryContentInfoList.html';
                        } else if (data.resultCode == '1030120030') {
                            $scope.noRepeat = true;
                            $scope.tip = "";
                            $scope.failOrgName = result.extInfo.failOrgName.replace(/\|/g, ',');
                            $('#myModal').modal();
                        } else if (data.resultCode == '1010120082') {
                            $scope.tip = data.resultDesc;
                            $('#myModal').modal();
                        } else if (data.resultCode == '1010120080') {
                            $scope.tip = "内容正在处理中，请稍后再试！";
                            $('#myModal').modal();
                        } else if (data.resultCode == '1030120090') {
                            $scope.noRepeat = false;
                            $scope.tip = "营帐业务关闭";
                            $('#myModal').modal();
                        } else if (data.resultCode == '1030120091') {
                            $scope.noRepeat = false;
                            $scope.tip = "内容正在待审核，请稍后再试。";
                            $('#myModal').modal();
                        } else {
                            $scope.noRepeat = false;
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                            $scope.noContentRepeat = false;

                            $scope.noRepeat = false;
                            $scope.tip = "1030120500";
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }

    };
    $scope.CommitUpdateCheck = function () {
        //1)调用查询内容列表接口（queryContentInfoList），oriContentID=当前内容ID，获取分支内容列表
            var reqest = {
                "oriContentID": $scope.contentID,
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
                data: JSON.stringify(reqest),
                success: function (result) {
                    var data = result.result;
                    $scope.branchContentRes=result
                }
            });
    };
    $scope.sensitiveCheck = function (content, index) {
        if (!content) {
            return;
        }
        content = content.replace(/\s/g, '');
        var req = {
            "content": content || '',
        };
        $scope.sensitiveWords[index] = [];
        $scope.sensitiveWordsStr[index] = '';
        $scope.isSensitive[index] = false;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030120017') {
                    $scope.sensitiveWords[index] = result.sensitiveWords || [];
                    if ($scope.sensitiveWords[index].length > 0) {
                        $scope.isSensitive[index] = true;
                        $scope.sensitiveWordsStr[index] = $scope.sensitiveWords[index].join('、');
                    } else {
                        $scope.isSensitive[index] = false;
                    }
                } else if (data.resultCode == '**********') {
                    $scope.sensitiveWords[index] = [];
                    $scope.isSensitive[index] = false;
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
            },
            error: function () {
                // $rootScope.$apply(function () {
                $scope.tip = '1030120500';
                $('#myModal').modal();
                // })
            }
        });
    };
    $scope.$on("ngRepeatFinished", function (repeatFinishedEvent, element) {
        //默认全是1
        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr1);
        if ($scope.operateType !== 'detail') {
            $('.pushObj .check-li').unbind("click");
            $('.pushObj .check-li').on('click', function (e) {
                $(this).find('span').toggleClass('checked');
                var _index = $(this).index();
                if ($(this).find('span').hasClass('checked')) {
//                    $rootScope.$apply(function () {
                    $scope.chosePushObj = true;
                    if (_index == 0) {
                        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr1);
                        $('.pushObj .check-li span').not(":first").removeClass('checked')
                    } else {
                        $($('.pushObj .check-li span')[0]).removeClass('checked');
                        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr0);
                        $('.pushObj .check-li').not(":first").each(function () {
                            if ($(this).find('span').hasClass('checked')) {
                                var _index1 = $(this).index();
                                $scope.pushObjArrTemp[_index1 - 1] = '1';
                            }
                        })
                    }
//                    })
                } else {
//                    $rootScope.$apply(function () {
                    if (_index == 0) {
                        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr0);
                    } else {
                        $scope.pushObjArrTemp[_index - 1] = '0';
                    }
//                    })
                }
                // console.log($scope.pushObjArrTemp)

                //50条时换成弹窗
                if ($scope.OrganizationListTotal > 50) {
                    let orgId = $(e.currentTarget).find("span").attr("value");
                    if ($(e.currentTarget).find("span").hasClass("checked")) {
                        //选中
                        let org = $scope.findFromGroupList(orgId, $scope.checkedOrgListTemp);
                        //原未选中才添加
                        if (!org) {
                            org = $scope.findFromGroupList(orgId, $scope.allOrgList);
                            if (org) {
                                $scope.checkedOrgListTemp.push(org);
                            }
                        }
                        if (orgId === "all") {
                            $scope.isNoLimitTemp = true;
                        } else {
                            $scope.isNoLimitTemp = false;
                        }
                    } else {
                        $scope.moveFromGroupList(orgId, $scope.checkedOrgListTemp);
                        if (orgId === "all") {
                            $scope.isNoLimitTemp = false;
                        }
                    }
                    if (!$scope.isNoLimitTemp) {
                        //重新渲染
                        setTimeout(function () {
                            for (let i = 0; i < $scope.checkedOrgListTemp.length; i++) {
                                $('.pushObj.max .check-li span[value="' + $scope.checkedOrgListTemp[i].id + '"]').addClass("checked")
                            }
                        }, 300);
                    }

                }
            });
        }
    });
    if ($scope.operateType !== 'detail') {
        $(function () {
            $('.pushDate .check-li').on('click', function () {
                $(this).find('span').toggleClass('checked');
                var _index = $(this).index();
                if ($(this).find('span').hasClass('checked')) {
                    if (_index == 0) {
                        $('.pushDate .check-li span').not(":first").removeClass('checked')
                        $rootScope.$apply(function () {
                            $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];
                        })
                    } else {
                        $($('.pushDate .check-li span')[0]).removeClass('checked');
                        $scope.deliveryDateArr = ['0', '0', '0', '0', '0', '0', '0'];
                        $('.pushDate .check-li').not(":first").each(function () {
                            if ($(this).find('span').hasClass('checked')) {
                                var _index1 = $(this).index();
                                $scope.deliveryDateArr[_index1 - 1] = '1';
                            }
                        })
                        // if($scope.deliveryDateArr.indexOf('1')==-1){
                        //     $($('.pushDate .check-li span')[0]).addClass('checked');
                        //     $scope.deliveryDateArr=['1','1','1','1','1','1','1'];
                        // }
                    }
                } else {
                    if (_index == 0) {
                        $rootScope.$apply(function () {
                            // $scope.deliveryDateArr=['0','0','0','0','0','0','0'];
                            $($('.pushDate .check-li span')[0]).addClass('checked');
                            $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];

                        })
                    } else {
                        $scope.deliveryDateArr[_index - 1] = '0';
                        //判断当前是否是最后一个非不限选择，是的话不给置0
                        if ($scope.deliveryDateArr.indexOf('1') == -1) {
                            $scope.deliveryDateArr[_index - 1] = '1';
                            $(this).find('span').addClass('checked');
                        }
                    }
                }
                console.log($scope.deliveryDateArr)
            })
            $('.glyphicon-calendar').click(function () {
                $('#time-config').trigger('click');

            })
        })

        // 初始化单选框
        $('.black-white .redio-li').on('click', function () {
        	$(this).find('span').addClass('checked');
        	$(this).siblings('.redio-li').find('span').removeClass('checked')
        });
    }


    //初始化营业执照上传容器
    $scope.initBusinessURLContainer = function () {
        //初始化营业执照上传容器
        $scope.filePicker_ = "filePicker2";
        $scope.accepttype_ = "jpg,jpeg,png";
        $scope.isValidate_ = false;
        $scope.filesize_ = 20;
        $scope.mimetypes_ = ".jpg,.jpeg,.png";
        $scope.isCreateThumbnail_ = true;
        $scope.uploadurl_ = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.uploadDesc_ = "仅支持一张图片，仅支持jpg，jpeg，png格式";
        $scope.numlimit_ = 1;
        $scope.urlList_ = [];
        $scope.uploadParam_ = {
            enterpriseId: $scope.enterpriseID || '',
            fileUse: 'businessLicense'
        };

        $scope.$on("uploadifyid_1", function (event, fileUrl_) {
            if (fileUrl_) {
                $scope.urlList_ = [fileUrl_];
                $scope.urlList_2 = [fileUrl_];
            } else {
                $scope.urlList_ = [];
                $scope.urlList_2 = [];
            }
            $scope.businessLicenseURL_ = fileUrl_;
        });
    }


    //所属行业是否为敏感行业
    $scope.changeIsSensitive = function (selectedIndustry) {
        if (selectedIndustry) {
            $scope.selectedIndustryID = selectedIndustry.industryID;
            $scope.isSensitiveIndustry = selectedIndustry.isSensitiveIndustry;
        } else {
            $scope.selectedIndustryID = '';
            $scope.selectedIndustryName = '';
        }
    }

    //查询所属行业
    $scope.queryIndustry = function ($scope) {
        //默认非敏感行业:0-非敏感；1-敏感
        $scope.selectedIndustryID = '';
        $scope.isSensitiveIndustry = '';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.industryList = data.industryList;
                    } else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '1030120500';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    //对象选择临时存放
    $scope.checkedOrgListTemp = [];
    $scope.isNoLimit = false;
    $scope.isNoLimitTemp = false;
    //分组配置弹出
    $scope.groupListPop = function (item) {
        $scope.groupName = null;
        $scope.pageInfo[0].currentPage = 1;
        $('#groupListPop').modal();
        $scope.queryOrg($scope.pageInfo);
        $scope.isNoLimitTemp = $scope.isNoLimit;

        if ($scope.isNoLimit) {
            $('.pushObj.max .check-li span.isLimit').addClass("checked");
            $scope.checkedOrgListTemp = [];
        } else {
            $scope.checkedOrgListTemp = $scope.checkOrganizationList.concat();
        }

    };

    //配置对象选择提交
    $scope.submitSelect = function (item) {
        //判断是否不限
        let oldIsNoLimit = $scope.isNoLimit;
        $scope.isNoLimit = $('.pushObj.max .check-li span.isLimit').hasClass("checked");
        if ($scope.isNoLimit) {
            $scope.checkOrganizationList = $scope.allOrgList;
            $('#groupListPop').modal("hide");
            return;
        } else {
            if (oldIsNoLimit) {
                $scope.checkOrganizationList = [];
            }
        }
        $scope.checkOrganizationList = $scope.checkedOrgListTemp.concat();
        $('#groupListPop').modal("hide");

        if( $scope.checkOrganizationList.length>50){
            $scope.queryCheckedOrg();
        }
    };
    //找分组
    $scope.findFromGroupList = function (orgId, groupList) {
        for (let i = 0; i < groupList.length; i++) {
            if (groupList[i].id == orgId) {
                return groupList[i];
            }
        }
    }
    //移除分组
    $scope.moveFromGroupList = function (orgId, groupList) {
        groupList.remove = function (v) {
            if (isNaN(v) || v > this.length) {
                return false
            }
            for (let i = 0, j = 0; i < this.length; i++) {
                if (this[i] != this[v]) {
                    this[j++] = this[i]
                }
            }
            this.length -= 1
        };
        for (let i = 0; i < groupList.length; i++) {
            if (groupList[i].id == orgId) {
                groupList.remove(i);
                return;
            }
        }
    };
    //数组分组
    $scope.group = function (array, subGroupLength) {
        let index = 0;
        let newArray = [];
        while (index < array.length) {
            newArray.push(array.slice(index, index += subGroupLength));
        }
        return newArray;
    };
    //查询企业服务开关
    $scope.queryPlatformStatus = function () {
        var queryServiceControlReq = {
            "enterpriseID": $scope.enterpriseID
        }
        $scope.signatureRequired = '0';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceControl",
            data: JSON.stringify(queryServiceControlReq),
            success: function (result) {
                
                $rootScope.$apply(function () {
                    $scope.platformStatus = result.platformStatus;
                    $scope.platformStatus1 = result.platformStatus;
                    $scope.hangupType = result.hangupType;
                    // 2102：子企业查询订购关系列表接口，用来初始化投递方式下拉框的值
                    if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
                    	// 下拉框(投递方式)
                    	$scope.querySubscribeList();
                    }
                    else
                    {
                    	var platformStatus = $scope.platformStatus;
                    	if ($scope.operateType == 'add') {
                            //查询企业免签名配置
                            $scope.checkEnterpriseWithoutSignAdd(platformStatus);
                    	} else {
                    		$scope.checkEnterpriseWithoutSignEdit(platformStatus);
                    	}
                    }
                	
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        });
    }

    //判断集合是否包含参数
    $scope.inArray = function (param) {
        // console.log("222222222222 = " + $scope.platforms);
        for (i = 0; i < $scope.mpUnshowSubservtype.length; i++) {
            if ($scope.mpUnshowSubservtype[i] == param) {
                //挂彩，这里默认0
                $scope.signatureRequired = '0';

                return true;
            }

        }

        if ($scope.platforms != null && ($scope.platforms.charAt(1) == '1' || $scope.platforms.charAt(2) == '1')) {
            $scope.signatureRequired = '1';
        } else {
        	$scope.signatureRequired = '0';
        }
        return false;
    }
    
    // 查询订购关系列表接口，获取有效订购关系（失效时间>当前时间）清单，转为订购关系map<业务子类型, map<计费方式, 运营商清单>>
    $scope.querySubscribeList = function () {
    	var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID": $scope.enterpriseID,
                "servType": 1,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '**********') {
                        $scope.subscribeList = data.subscribeInfoList || [];
                        for (let i = $scope.subscribeList.length - 1; i >= 0 ; i--) {
                            if ($scope.subscribeList[i].subServType == '34' || $scope.subscribeList[i].subServType == '35' || $scope.subscribeList[i].subServType == '36'){
                                $scope.subscribeList.splice(i , 1);
                            }
                        }
                        if ($scope.subscribeList.length > 0) 
                        {
                        	// map<业务子类型, map<计费方式, 运营商清单>>
                        	$scope.subscribeMap = $scope.getSubscribeMap();
                            if ($scope.operateType == 'add')
                            {
                            	var subServTypeChoise = [];
                            	var subServTypes = [];
                            	for(let key of $scope.subscribeMap.keys()) {
                            		subServTypes.push(key);
                            	};
                            	subServTypes = subServTypes.sort(function(a, b){return a - b});
                            	for(let key of subServTypes) {
                            	    if (key == 4) {
                                        var choise1 = {
                                            "id": 4.1,
                                            name: "主叫挂机短信"
                                        }
                                        var choise2 = {
                                            "id": 4.2,
                                            name: "被叫挂机短信"
                                        }

                                        var choise3 = {
                                            "id":4.3,
                                            name:"行业挂机短信"
                                        }
                                        if($scope.hangupType && $scope.hangupType.indexOf("1")!=-1) {
                                            subServTypeChoise.push(choise1);
                                        }
                                        if ($scope.hangupType && $scope.hangupType.indexOf("3")!=-1){
                                            subServTypeChoise.push(choise3);
                                        }
                                        if ((!$scope.hangupType && $scope.operateType != 'add') || ($scope.hangupType && $scope.hangupType.indexOf("2")!=-1 && $scope.operateType != 'add') ) {
                                            subServTypeChoise.push(choise2);
                                        }
                                    } else {
                                        var choise = {
                                            "id": key,
                                            "name": $scope.statusMap[key]
                                        }
                                        subServTypeChoise.push(choise);
                                    }
                            	};
                            	$scope.subServTypeChoise = subServTypeChoise;
                            	$scope.initPrintInfo.subServType = subServTypeChoise[0].id;
                            	$scope.changeSubServerType();
                            }
                            else
                            {
                            	if ($scope.initPrintInfo.chargeType == 2) {
                            		$scope.queryProductReserved3(parseInt($scope.initPrintInfo.subServType));
                            	} else {
                            		$scope.platformInit($scope.initPrintInfo.chargeType);
                            	}
                            }
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }

    //根据订购关系生成map<业务子类型, map<计费方式, 运营商清单>>
    $scope.getSubscribeMap = function () {
    	var resultMap = new Map();
        $scope.isMonthByQuotaMap = new Map();
        console.log($scope.subscribeList,"subscribeList")
        for (let i in $scope.subscribeList)
    	{
    		var subscribe = $scope.subscribeList[i];

    		let key = subscribe.subServType;
    		if(subscribe.reservedsEcpmp.reserved4 == 1){
               let isMonthByQuota =  $scope.isMonthByQuotaMap.get(key);
               if(null != isMonthByQuota&&isMonthByQuota == 0){
                   isMonthByQuota = 2
               }else if(!isMonthByQuota){
                   isMonthByQuota = 1
               }
                $scope.isMonthByQuotaMap.set(key,isMonthByQuota)
            }
            if(subscribe.chargeType == 2 && subscribe.reservedsEcpmp.reserved4 == 0){
                let isMonthByQuota =  $scope.isMonthByQuotaMap.get(key);
                if(null != isMonthByQuota&&isMonthByQuota == 1){
                    isMonthByQuota = 2
                }else if(!isMonthByQuota){
                    isMonthByQuota = 0
                }
                $scope.isMonthByQuotaMap.set(key,isMonthByQuota)
            }

    		// 计费方式map
    		var chargeTypeMap = resultMap.get(subscribe.subServType);
    		if (!chargeTypeMap)
    		{
    			chargeTypeMap = new Map();
                resultMap.set(subscribe.subServType, chargeTypeMap);
    			// if (subscribe.subServType == 4) {
    			//     if (!resultMap.get(4.1)) {
                //         resultMap.set(4.1, chargeTypeMap);
                //     }
                //     if (!resultMap.get(4.2)) {
                //         resultMap.set(4.2, chargeTypeMap);
                //     }
                // } else {
                //     resultMap.set(subscribe.subServType, chargeTypeMap);
                // }
    		}
    		// 运营商清单
    		var chargeType = subscribe.chargeType;
    		if (subscribe.reservedsEcpmp.reserved4 == 1)
    	    {
    			// 3代表按条包月，为了跟按条计费和包月计费区分开
    			chargeType = 3;
    	    }
    		var platformList = chargeTypeMap.get(chargeType);
    		if (!platformList)
    		{
    			platformList = [];
    			chargeTypeMap.set(chargeType, platformList);
    		}
    		var telecomsOperator = !subscribe.reservedsEcpmp ? "1"
                    : !subscribe.reservedsEcpmp.reserved1 ? "1"
                            : subscribe.reservedsEcpmp.reserved1;
    		platformList.push(telecomsOperator);
    	}
    	
    	// 屏显需要加上主叫，被叫
    	if (resultMap.get(3))
    	{
    		if (!resultMap.get(1))
    		{
    			resultMap.set(1, resultMap.get(3));
    		}
    		else
    		{
    			var chargeTypeMap1 = resultMap.get(1);
    			var chargeTypeMap3 = resultMap.get(3);
    			for(let key of chargeTypeMap3.keys()) {
            		if (!chargeTypeMap1.get(key))
            		{
            			chargeTypeMap1.set(key, chargeTypeMap3.get(key))
            		}
            	};
    		}
    		if (!resultMap.get(2))
    		{
    			resultMap.set(2, resultMap.get(3));
    		}
    		else
    		{
    			var chargeTypeMap2 = resultMap.get(2);
    			var chargeTypeMap3 = resultMap.get(3);
    			for(let key of chargeTypeMap3.keys()) {
            		if (!chargeTypeMap2.get(key))
            		{
            			chargeTypeMap2.set(key, chargeTypeMap3.get(key))
            		}
            	};
    		}
    	}
    	console.log(resultMap,"resultMap")
    	return resultMap;
    }

    // 获取当前时间YYYYMMDDHHMMSS
    $scope.getCurrentDate = function () {
    	var today = new Date();
        var year = today.getFullYear()+'';
    	var month = today.getMonth() + 1;
    	month = month < 10 ? '0'+month : month;
    	var day = today.getDate() < 10 ? '0'+today.getDate() : today.getDate();
    	var hours = today.getHours() < 10 ? '0'+today.getHours() : today.getHours();
    	var mins = today.getMinutes() < 10 ? '0'+today.getMinutes() : today.getMinutes();
    	var secs = today.getSeconds() < 10 ? '0'+today.getSeconds() : today.getSeconds();
        
        return year + month + day + hours + mins + secs;
    }


    // 新增页初始化运营商勾选状态
    $scope.platformInitAdd = function (platformStatus) {
        $scope.platforms = platformStatus;
        
        //初始化signature必填状态.切换模板不重置noSign
        if(!$scope.changeYsmbStatus){
            $scope.noSign = '1';
        }
        $scope.signatureRequired = '0';
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
            $scope.isSceneDesc = '1';
        } else if (platformStatus.charAt(1) == '0' && platformStatus.charAt(2) == '0') {
            $scope.isSceneDesc = '0';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
	            $('.platforms .check-li').eq(i).unbind("click");
	            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                $(".platforms .check-li").find('span').eq(i).addClass('checked')
                //绑定点击事件
                $('.platforms .check-li').eq(i).unbind("click");
                if($scope.initPrintInfo.isRefYsmb != 1){
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';
                        $scope.isSceneDesc = '1';

                    } else {
                        $scope.signatureRequired = '0';
                        $scope.initPrintInfo.sceneDesc = "";
                        $scope.isSceneDesc = '0';

                    }
                    $scope.platforms = _platforms;
                });
                }

            }
        }
    }

    $scope.platformInitAddNoSign = function (platformStatus) {
        $scope.platforms = platformStatus;
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
            $scope.isSceneDesc = '1';
        } else if (platformStatus.charAt(1) == '0' && platformStatus.charAt(2) == '0') {
            $scope.isSceneDesc = '0';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                $('.platforms .check-li').eq(i).unbind("click");
                $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                $(".platforms .check-li").find('span').eq(i).addClass('checked')
                //绑定点击事件
                $('.platforms .check-li').eq(i).unbind("click");
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';
                        $scope.isSceneDesc = '1';

                    } else {
                        $scope.signatureRequired = '0';
                        $scope.initPrintInfo.sceneDesc = "";
                        $scope.isSceneDesc = '0';
                    }

                    $scope.platforms = _platforms;
                });
            }
        }

    }

    $scope.pushSubServTypeMap = function (subServType, hangupType) {
        console.log(subServType,hangupType,"渲染")
        if (subServType == 4 && undefined != hangupType && hangupType == 1) {
            return "主叫挂机短信";
        } else if (subServType == 4 && undefined != hangupType && hangupType == 2) {
            return "被叫挂机短信";
        }
        else if (subServType == 4 && undefined != hangupType && hangupType == 3) {
            return "行业挂机短信";
        }
        return $scope.statusMap[subServType];
    }

    // 编辑页初始化运营商勾选状态
    $scope.platformInitEditNoSign = function (platformStatus) {
        var platforms = $scope.platforms;
        if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
            $scope.signatureRequired = '1';
            $scope.isSceneDesc = '1';
            // $scope.noSign = '1';
        } else if (platforms.charAt(1) == '0' && platforms.charAt(2) == '0') {
            $scope.isSceneDesc = '0';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0' && platforms.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                $('.platforms .check-li').eq(i).unbind("click");
                $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                if (platforms.charAt(i) == '1' && platformStatus.charAt(i) == '1') {
                    $(".platforms .check-li").find('span').eq(i).addClass('checked');
                }
                // else{
                //     $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                // }

                if ($scope.operateType != 'detail' && $scope.initPrintInfo.isRefYsmb != 1) {
                    //绑定点击事件
                    $('.platforms .check-li').eq(i).unbind("click");
                    $('.platforms .check-li').eq(i).on('click', function () {
                        if ($(this).find('span').hasClass('checked') &&
                            ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                            $(this).find('span').removeClass('checked');
                        } else {
                            $(this).find('span').addClass('checked')
                        }
                        var _platforms = '';
                        for (var i = 0; i < 3; i++) {
                            if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                                _platforms += '1';
                            } else {
                                _platforms += '0';
                            }
                        }
                        if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                            $scope.signatureRequired = '1';
                            $scope.isSceneDesc = '1';

                        } else {
                            $scope.signatureRequired = '0';
                            $scope.initPrintInfo.sceneDesc = "";
                            $scope.isSceneDesc = '0';

                        }
                        $scope.platforms = _platforms;
                    });
                }
                if (!((platformStatus).charAt(i) == '1')) {
                    $('.platforms .check-li').eq(i).unbind("click");
                    $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});

                }
            }
        }
    }

    $scope.platformInitEdit = function (platformStatus) {
        var platforms = $scope.platforms;
        if(!$scope.changeYsmbStatus){
            $scope.noSign = '1';
        }
        //初始化signature必填状态
        if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
            $scope.signatureRequired = '1';
            $scope.isSceneDesc = '1';
        } else if (platforms.charAt(1) == '0' && platforms.charAt(2) == '0') {
            $scope.isSceneDesc = '0';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0' && platforms.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                $('.platforms .check-li').eq(i).unbind("click");
                $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                if (platforms.charAt(i) == '1' && platformStatus.charAt(i) == '1') {
                    $(".platforms .check-li").find('span').eq(i).addClass('checked');
                }
                // else{
                //     $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                // }

                if ($scope.operateType != 'detail' && $scope.initPrintInfo.isRefYsmb != 1) {
                    //绑定点击事件
                    $('.platforms .check-li').eq(i).unbind("click");
                    $('.platforms .check-li').eq(i).on('click', function () {
                        if ($(this).find('span').hasClass('checked') &&
                            ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                            $(this).find('span').removeClass('checked');
                        } else {
                            $(this).find('span').addClass('checked')
                        }
                        var _platforms = '';
                        for (var i = 0; i < 3; i++) {
                            if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                                _platforms += '1';
                            } else {
                                _platforms += '0';
                            }
                        }
                        if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                            $scope.signatureRequired = '1';
                            $scope.isSceneDesc = '1';

                        } else {
                            $scope.signatureRequired = '0';
                            $scope.initPrintInfo.sceneDesc = "";
                            $scope.isSceneDesc = '0';

                        }
                        $scope.platforms = _platforms;
                    });
                }
                if (!(platformStatus.charAt(i) == '1')) {
                    $('.platforms .check-li').eq(i).unbind("click");
                    $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                }
            }
        }
    }

    
    $scope.initContentTypeChecked = function () {
        $('.content-type .redio-li').find('span').removeClass('checked');
        if ($scope.initPrintInfo.isRefYsmb == 1) {
        	$('.content-type .redio-li').find('span').eq(1).addClass('checked');
        } else {
        	$('.content-type .redio-li').find('span').eq(0).addClass('checked');
        }
        $('.callcontent-type .redio-li').find('span').removeClass('checked');
        if ($scope.initPrintInfo.isCallRefYsmb == 1) {
        	$('.callcontent-type .redio-li').find('span').eq(1).addClass('checked');
        } else {
        	$('.callcontent-type .redio-li').find('span').eq(0).addClass('checked');
        }
    }
    $scope.is5GEnterprise = false;
    $scope.queryEnterpriseName = function () {
        let id  = $scope.enterpriseID;
        if($scope.enterpriseType == '3' && $.cookie('subEnterpriseID')){
            id = parseInt($.cookie('subEnterpriseID'));
        }
    	var req = {
    			"id": id,
    			"pageParameter": {
    				"pageNum": 1,
    				"pageSize": 100,
    				"isReturnTotal": "1"
    			}
    	}
    	/*查询企业列表*/
    	return new Promise((resolve) => {
            if (!$scope.enterpriseName || $scope.enterpriseType == "3") {
               RestClientUtil.ajaxRequest({
                               		type: 'POST',
                               		url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
                               		data: JSON.stringify(req),
                               		success: function (data) {
                               			$rootScope.$apply(function () {
                               				var result = data.result;
                               				if (result.resultCode == '**********') {
                               					$scope.enterpriseName = data.enterprise.enterpriseName;
                               					$.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
                                                   $scope.parentEnterpriseID = data.enterprise.parentEnterpriseID;
                                                   resolve();
                                                   CommonUtils.getProperties(["ecpmp.5GCard.enterprises"],function(params){
                                                       let enterprises = params["ecpmp.5GCard.enterprises"];
                                                       if(enterprises.split(",").includes($scope.parentEnterpriseID+"")){
                                                           $scope.is5GEnterprise = true;
                                                       }
                                                   });
                               				}
                               			})
                               		}
                               	});
            }else{
               resolve();
            }
    	});
    }

    $scope.$watch('initPrintInfo.signature',function(){
            $scope.maximumLengthOfSignature();
            $scope.checkSignAndContent();
    },true)


    $scope.checkSignAndContent = function () {
        $scope.checkSign = 0;
        $scope.checkCallSign = 0;
        var signature = $scope.initPrintInfo.signature;
            if (signature) {
            	//212企管平台彩印签名长度优化需求
                $scope.signAndContent = ($scope.initPrintInfo.colorContent) ? ($scope.initPrintInfo.colorContent.length + signature.length + 2) : signature.length + 2;
                $scope.signAndCallContent = ($scope.initPrintInfo.colorCallContent) ? ($scope.initPrintInfo.colorCallContent.length + signature.length + 2) : signature.length + 2;
            } else {
                $scope.signAndContent = ($scope.initPrintInfo.colorContent) ? $scope.initPrintInfo.colorContent.length : 0;
                $scope.signAndCallContent = ($scope.initPrintInfo.colorCallContent) ? $scope.initPrintInfo.colorCallContent.length : 0;
            }
            if($scope.initPrintInfo.subServType === 4.1 || $scope.initPrintInfo.subServType === 4.2 || $scope.initPrintInfo.subServType === 4.3 || $scope.initPrintInfo.subServType === 4){
                if($scope.guajiLimit < $scope.signAndContent){
                    $scope.checkSign = 1;
                }else {
                    $scope.checkSign = 0;
                }
                if($scope.guajiLimit < $scope.signAndCallContent){
                    $scope.checkCallSign = 1;
                }else {
                    $scope.checkCallSign = 0;
                }
            }else{
                if(70 < $scope.signAndContent){
                    $scope.checkSign = 1;
                }else {
                    $scope.checkSign = 0;
                }
                if(70 < $scope.signAndCallContent){
                    $scope.checkCallSign = 1;
                }else {
                    $scope.checkCallSign = 0;
                }
            }
    }

    $scope.$watch('initPrintInfo.colorContent',function(){
        $scope.maximumLengthOfSignature();
        $scope.checkSignAndContent();
    },true)

    $scope.maximumLengthOfSignature = function () {
        if($scope.initPrintInfo.signature){
            $scope.maxSignLength =($scope.initPrintInfo.subServType === 4.1 || $scope.initPrintInfo.subServType === 4.2 || $scope.initPrintInfo.subServType === 4.3 || $scope.initPrintInfo.subServType === 4) ? ($scope.guajiLimit-2-$scope.initPrintInfo.signature.length) :(70-2-$scope.initPrintInfo.signature.length);
        }else {
            $scope.maxSignLength =($scope.initPrintInfo.subServType === 4.1 || $scope.initPrintInfo.subServType === 4.2 || $scope.initPrintInfo.subServType === 4.3 || $scope.initPrintInfo.subServType === 4) ? $scope.guajiLimit :70;
        }
    }

    $scope.$watch('initPrintInfo.colorCallContent',function(){
        $scope.maximumLengthOfSignature();
        $scope.checkSignAndContent();
    },true)

    $scope.$watch('initPrintInfo.subServType', function(newVal, oldVal) {
          if (newVal !== oldVal) {
              // 重新计算contentType的值
              $scope.updateContentType();
          }
      });

      $scope.updateContentType = function() {
          // 根据subServType设置正确的contentType
          if ($scope.initPrintInfo.subServType == '3'
          || $scope.initPrintInfo.subServType == '1'
          || $scope.initPrintInfo.subServType == '2') {
              $scope.contentType =  $scope.initPrintInfo.subServType;
          } else {
              $scope.contentType = 0
          }
      };
})
app.directive('onRepeatFinishedRender', function ($timeout) {
    return {
        restrict: 'A',
        link: function (scope, element, attr) {
            if (scope.$last === true) {
            	scope.$emit('ngRepeatFinished', element);
                if (scope.htmlflat)
                {
              	  scope.mapDataToHtml();
                }
                $timeout(function () {
                    //这里element, 就是ng-repeat渲染的最后一个元素
                });
            }
        }
    };
});

app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])