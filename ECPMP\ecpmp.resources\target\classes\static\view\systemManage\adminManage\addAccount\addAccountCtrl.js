var app = angular.module("myApp", ["util.ajax", "page", "angularI18n"])
app.controller("addAccountController", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    $scope.operatorID = $.cookie('accountID');
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      },
      {
          "totalPage": 1,
          "totalCount": 0,
          "pageSize": '10',
          "currentPage": 1
      }
    ];

    $scope.password = '';
    $scope.rePassword = '';
    $scope.rePasswordValidate = true;
    $scope.emailValidate = true;
    $scope.managerRightType = 1;
    $scope.queryDepartment();
  };
  $scope.email = "";
  $scope.isMiguStaff = '0';
  $scope.goBack = function () {
    location.href = "../administrator.html"
  };

  //角色查询弹出
  $scope.selAccount = function () {
    $scope.queryRoleList();
    $("#selectAccount").modal();
  };

    $scope.queryDepartment = function(){
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/accountManageService/queryOaDeptList",
            data: null,
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.oaDeptInfoList = result.oaDeptInfoList;}
                })
            }
        })
    };
    //角色查询弹出
    $scope.selOAAccount = function () {
        $scope.pageInfo[1].currentPage = 1;
        $scope.deptId = null;
        $scope.realName = null;
        $scope.queryOAAccount();
        $("#selectOAAccount").modal();
    };

    $scope.clickOAAccount = function (item) {
        if(item.isMiguStaff===1){
            $scope.tip = '该账号已被绑定';
            $('#myModal').modal();
            return;
        }
        $scope.oaAccount = item.oaAccount;
        $scope.fullName = item.realName;
        $scope.msisdn = item.contactPhone;
        $scope.email = item.email;
        $('#selectOAAccount').modal("hide");
    };


  //角色查询
  $scope.queryRoleList = function (condition) {
    if (condition != 'justPage') {
      var req = {
        "roleName": $scope.roleName,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.roleReq = angular.copy(req);
    } else {
      var req = $scope.roleReq;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryRoleList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.roleList = result.roleList;
            //组装角色权限
            angular.forEach($scope.roleList, function (item) {
              item.authNameString = "";
              angular.forEach(item.functionAuthList, function (row1) {
                if (row1.parentAuthID !== null) {
                  item.authNameString = item.authNameString + row1.authName + "/"
                }
              });
              angular.forEach(item.dateAuthList, function (row2) {
                if (row2.fieldName === "cityID") {
                  item.authNameString = item.authNameString + row2.authName + "/"
                }
              });
              item.authNameString = item.authNameString.substr(0, item.authNameString.length - 1)
            });
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize));
          } else {
            $scope.roleList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.roleContentInfoData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

    //OA账号查询
    $scope.queryOAAccount = function (condition) {
        if("search" === condition){
            $scope.pageInfo[1].currentPage = 1;
        }
        var req = {
            "deptId": $scope.deptId,
            "realName": $scope.realName,
            "pageParameter": {
                "pageNum": $scope.pageInfo[1].currentPage,
                "pageSize": parseInt($scope.pageInfo[1].pageSize),
                "isReturnTotal": "1"
            }
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/accountManageService/queryOaAccountList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.accountInfoList = result.accountInfoList;
                        $scope.pageInfo[1].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[1].totalPage = Math.ceil(result.totalAmount / parseInt($scope.pageInfo[1].pageSize));
                    } else {
                        $scope.roleList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.roleContentInfoData = [];
                    $scope.pageInfo[1].currentPage = 1;
                    $scope.pageInfo[1].totalCount = 0;
                    $scope.pageInfo[1].totalPage = 1;
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    //账号类型选择
    $scope.changeBlackwhiteListType = function (val) {
          $('.black-white .redio-li').eq(val==0?1:0).find('span').removeClass('checked');
          $('.black-white .redio-li').eq(val).find('span').addClass('checked');
          $scope.managerRightType = val;
    };
    //是否是咪咕员工选择
    $scope.changeIsOAAccount = function (val) {
        $('.OA-account .redio-li').eq(val).find('span').removeClass('checked');
        $('.OA-account .redio-li').eq(val===0?1:0).find('span').addClass('checked');
        $scope.isMiguStaff = val;
    };
  //添加角色
  $scope.roleAdd = function (role) {
    $scope.selectedRole = [];
    $scope.roleName_selected = role.roleName;
    $scope.selectedRole.push(role);
    $('#selectAccount').click();
  }

  // 账号
  $scope.accountNameValidate = true;
  $scope.checkAccountname = function (accountName) {
    $scope.accountNameValidate = $scope.validate(accountName, 32, /^(?![0-9]+$)[0-9A-Za-z]{1,32}$/);
  };

  // 联系人
  $scope.fullNameValidate = true;
  $scope.checkFullName = function (fullName) {
    $scope.fullNameValidate = $scope.validate(fullName, 32, /[A-Za-z\u4e00-\u9fa5]{1,32}$/);
  }

  // 手机号
  $scope.msisdnValidate = true;
  $scope.checkMsisdn = function (msisdn) {
    $scope.msisdnValidate = $scope.validate(msisdn, 11, /[0-9]{11}$/);
  }

  // 邮箱
  $scope.emailValidate = true;
  $scope.checkEmail = function (email) {
    if(email == ""){
      $scope.emailValidate = true;
    }else if($scope.emailValidate = $scope.validate(email, 64, /^(?=\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$).{0,64}$/)){
      $scope.emailValidate = true;
    }
  }
    $scope.oaAccountValidate = true;
    $scope.checkOaAccount = function (oaAccount) {
        if(oaAccount == "" && $scope.isMiguStaff === 1){
            $scope.oaAccountValidate = false;
        }else{
            $scope.oaAccountValidate = $scope.validate(oaAccount, 32, /^(?![0-9]+$)[0-9A-Za-z]{1,32}$/);
        }
    }
  /*验证确认密码，与密码相同 */
  $scope.checkRePassword = function (pwd, rePwd,condition) {
    $scope.condition =condition;
      $scope.rePasswordValidate = true;
      if (!rePwd) {
        if(!pwd && $scope.operate =='edit'){
          if($scope.condition =='save'){
                $scope.update();
            }else{
              return;
            }
        }else{
          $scope.rePasswordValidate = false;
        }
      }else{
          if (pwd != rePwd) {
            $scope.rePasswordValidate = false;
          }else{
            if($scope.condition =='save'){
              if($scope.operate =='add'){
                  $scope.checkAccountName($scope.accountInfo.accountName,'save');
                }
            }
          }
      }
    };

  $scope.selectedRole = [];
  $scope.saveAccount = function () {
    var req = {
      "accountInfo": {
        "accountType": 3,
        "operatorID": $scope.operatorID,
        "accountName": $scope.accountName,
        "roleList": $scope.selectedRole,
        "fullName": $scope.fullName,
        "msisdn": $scope.msisdn,
        "email": $scope.email,
        "password": $scope.password,
         "managerRightType": $scope.managerRightType
      }
    };
    if($scope.isMiguStaff==1){
        req.accountInfo.isMiguStaff = $scope.isMiguStaff;
        req.accountInfo.oaAccount = $scope.oaAccount;
    }
      var pwd = $scope.password;
      var rePwd = $scope.rePassword;
      if( pwd != rePwd){
          return
      }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/createAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.tip = 'COMMON_SAVESUCCESS';
            $('#myModal').modal();
          } else {
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
    
  }
  
  /*验证密码，通过后台调用checkPwdRule接口 */
  $scope.checkPassword = function (condition) {
    $scope.condition =condition;
      $scope.passwordValidate = true;
      $scope.passwordValidateDesc = "";
      var pwd = $scope.password;
      var rePwd = $scope.rePassword;
      if(!$scope.password && $scope.operate =='edit'){
        $scope.checkRePassword(pwd, rePwd,$scope.condition);
        return;
      }else{
        var checkPwdRuleReq = {};
          checkPwdRuleReq.password = pwd;
          RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
            data: JSON.stringify(checkPwdRuleReq),
            success: function (data) {
              $rootScope.$apply(function () {
                var result = data.result;
                if (result.resultCode != '**********') {
                  $scope.passwordValidate = false;
                    var passwordRuleList = data.passwordRuleList;
                    if(result.resultCode == '1030120000'){
                      $scope.passwordValidateDesc ='ENTERPRISE_PASSWORDDESC';
                    }else{
                      if(!passwordRuleList){
                        $scope.passwordValidateDesc = result.resultDesc;
                      }else{
                        for (var i = 0; i < passwordRuleList.length; i++) {
                                // $scope.passwordValidateDesc  = $scope.passwordValidateDesc + passwordRuleList[i].ruleName;
                                $scope.passwordValidateDesc  =  passwordRuleList[i].ruleName;

                                // $scope.passwordValidateDesc  = $scope.passwordValidateDesc + ";";
                                }
                        $scope.passwordValidateDesc=$scope.passwordValidateDesc.substring(0, $scope.passwordValidateDesc.length - 1);
                      }
                    }
                } else {
                if(!!rePwd){
                  $scope.checkRePassword(pwd, rePwd,$scope.condition);
                }
                }
              })
            },
            error:function(){
                $rootScope.$apply(function(){
                    $scope.tip='**********';
                    $('#myModal').modal();
                    }
                )
            }
          });
      }
    };

  $scope.closeTip = function () {
    if($scope.tip == "COMMON_SAVESUCCESS"){
      window.history.back();
    }
  }
/*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
});