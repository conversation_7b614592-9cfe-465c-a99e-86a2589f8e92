<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>签名配置管理</title>
    <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
    <link href="../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../css/searchList.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
    <script type="text/javascript" src="../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/preview/preview.css"/>
    <script src="../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <script src="../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
    <script type="text/javascript" src="EnterpriseWithoutSign.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../css/hotlineContentManage.css"/>
    <style>
        .cooperation-manage .coorPeration-table th,td{
            padding-left: 20px !important;
        }

        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        #filePicker_ div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        .form-group div li {
            display: inline-block;
            margin-right: 10px;
            padding-right: 10px;
            cursor: pointer;
        }

        .form-group div li span {
            vertical-align: middle;
            margin-right: 4px;
        }

        .label-supply {
            display: inline-block;
            float: left;
            padding-right: 15px;
            padding-left: 15px;
        }
        .clearf:after{
            content:'';
            clear:both;
            height:0;
            display:block;
        }
    </style>
</head>
<body ng-app='myApp' ng-controller='enterpriseWithoutSignListController' ng-init="init()">
<div class="cooperation-manage" style="overflow-x: scroll;">

    <div class="cooperation-head">
			<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate">
			</span>&nbsp;&gt;&nbsp;<span class="second-tab">签名配置管理</span>
    </div>

    <div class="cooperation-search">
        <form class="form-horizontal">
            <div class="form-group form-inline" style="margin-left: 0px;margin-right: 0px;">
                <div class="control-label label-supply" style="padding-right: 5px;padding-left: 5px;">
                    <label ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></label>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width: 20%">
                    <input ng-model='enterpriseID'  type="text" autocomplete="off" class="form-control" id="enterpriseID"
                           placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISEID'|translate}}">
                </div>

                <div class="control-label label-supply" style="padding-right: 5px;padding-left: 5px;">
                    <label ng-bind="'ENTERPRISE_SUBENTERPRISENAME1'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:20%;">
                    <input ng-model='enterpriseName'  type="text" autocomplete="off" class="form-control" id="enterpriseName"
                           placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}">
                </div>

                <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2" >
                    <button ng-click="queryEnterpriseWithoutSignList()" type="submit" class="btn search-btn" >
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
                <div class="clearf"> </div>
            </div>
        </form>
    </div>
    <div class="add-table">
        <button type="submit" class="btn add-btn" ng-click="addEnterpriseWithoutSign()">
            <icon class="add-iocn"></icon>
            <span style="color:#705de1" ng-bind="'NEW_ADD'|translate"></span>
        </button>
    </div>

    <div style="margin-left: 20px;margin-bottom: 20px;">
        <p style="font-size: 16px;font-weight: 500;" ng-bind="'SIGNATURE_CONFIGURATION_MANAGEMENT_LIST'|translate"></p>
    </div>
    <div class="coorPeration-table">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></th>
                <th style="width:10%" ng-bind="'ENTERPRISE_SUBENTERPRISENAME1'|translate"></th>
                <th style="width:10%" ng-bind="'ITEM_EFFICTIVETIME'|translate"></th>
                <th style="width:10%" ng-bind="'COMMON_OPERATE'|translate"></th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="item in enterpriseWithoutSignListData">
                <td style="display:none;"><span title="{{item.id}}">{{item.id}}</span></td>
                <td><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
                <td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
                <td ng-bind="item.createTime|formatDate" title="{{item.createTime|formatDate}}"></td>
                <td>
                    <div class="handle">
                        <ul>
                            <!-- 删除 -->
                            <li class="delete" ng-click="deleteEnterpriseWithoutSign(item)">
                                <icon class="delete-icon"></icon>
                                <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
            <tr ng-show="hotContentInfoListData.length<=0">
                <td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
            </tr>
            </tbody>
        </table>
    </div>
    <div style="width: 1000px;">
        <ptl-page tableId="0" change="queryEnterpriseWithoutSignList('justPage')"></ptl-page>
    </div>

    <div class="modal fade addMenPop" id="addEnterpriseWithoutSign" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-mousedown="canclePop($event)"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'SIGNATURE_FREE_BUSINESS'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" name="enterpriseWithoutSignForm">
                        <div class="form-group" style="padding-top:5px">
                            <label  class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                <icon>*</icon>
                                <span ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></span>
                            </label>
                            <div class="col-lg-5 col-xs-5  col-sm-5 col-md-5">
                                <input class="form-control"  style="margin: 0px; width: 267px;position: absolute;z-index: 1;"
                                       name="addEnterpriseID"
                                       ng-model="addEnterpriseWithoutSign.enterpriseID"
                                       placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISEID'|translate}}"
                                       >
                            </div>
                            <div style="float:right;">
                                <button ng-click="getEnterpriseName()" type="submit" class="btn search-btn">
                                    <icon class="search-iocn"></icon>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                            </div>
                        </div>
                        <div class="form-group" style="padding-top:5px">
                            <label  class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                <span ng-bind="'ENTERPRISE_SUBENTERPRISENAME1'|translate"></span>
                            </label>
                            <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                <span name="addEnterpriseName">{{lastName}}</span>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit"
                            class="btn btn-primary search-btn" ng-bind="'COMMON_SAVE'|translate"  ng-disabled="!addEnterpriseWithoutSign.enterpriseID"
                            ng-click="beforeCommit()"></button>
                    <button type="submit" class="btn btn-back addMemCancel" data-dismiss="modal"
                            aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"
                            ng-mousedown="canclePop($event)" id="addEnterpriseWithoutSignCancel"></button>
                </div>
            </div>
        </div>
    </div>

    <!--删除弹出框-->
    <div class="modal fade" id="deleteEnterpriseWithoutSign" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
                                <div class="text-center">
                                    <span ng-bind="'GROUP_SUREDELETE'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                            ng-click="delEnterpriseWithoutSign()"></button>
                    <button id="delEnterpriseWithoutSign" type="submit" class="btn " data-dismiss="modal"
                            aria-label="Close" ng-bind="'NO'|translate"></button>
                </div>
            </div>
        </div>
    </div>

    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title"  ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838'>
                            {{tip|translate}}
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
</div>

</body>

</html>