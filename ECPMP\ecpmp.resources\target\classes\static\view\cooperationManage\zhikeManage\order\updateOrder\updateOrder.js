var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n", "service.common","cy.uploadifyfile",])
app.controller('updateOrderController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
	$scope.getAddOrder = function (data,index) {
        $scope.addOrderListInfo[index].imgName1 = data.file.name;
        $scope.addOrderListInfo[index].upload = data.fileUrl;
        $scope.addOrderListInfo[index].add = true; 
    }
    
    $scope.getAddOrderdetail = function (data,index) {
        $scope.addOrderListInfo[index].imgName2 = data.file.name;
        $scope.addOrderListInfo[index].upload2 = data.fileUrl;
        $scope.addOrderListInfo[index].addorder = true; 
    }
    
    $scope.init = function () {
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.orderCode = $location.search().orderCode || '';
        $scope.objectID = $location.search().objectID || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.hasPX = false;
        $scope.hasPXCUCC = false;
        $scope.hasPXCTCC = false;
        $scope.hasGD = false;
        $scope.hasGDCUCC = false;
        $scope.hasGDCTCC = false;
        $scope.hasGC = false;
        $scope.hasAddOrder = false;
        $scope.hasCMCC = false;
        $scope.hasCUCC = false;
        $scope.hasCTCC = false;
        $scope.px_noLimit_cmcc = false;
        $scope.gd_noLimit_cmcc = false;
        $scope.gd_noLimit_cmccCUCC = false;
        $scope.gd_noLimit_cmccCTCC = false;
        $scope.gd_ByLimit_cmcc = false;

        $scope.gc_noLimit_cmcc = false;
        $scope.gc_ByLimit_cmcc = false;


        $scope.px_noLimit_cucc = false;
        $scope.px_noLimit_ctcc = false;
        $scope.statusMap = {
            "1": "生效",
            "2": "失效",
            "3": "预生效"
        };
        $scope.businessTypeMap = {
            "1": "名片彩印",
            "2": "热线彩印",
            "3": "广告彩印",
            "4": "增彩业务"
        };
        
        $scope.accepttype = "jpg,jpeg,png,xlsx,doc,pdf";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".jpg,.jpeg,.png,.xlsx,.doc,.pdf";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadAllTypeFile';
        $scope.uploadDesc = "必填，仅支持一张图片，仅支持jpg，jpeg，png，pdf格式";
        $scope.numlimit = 1;
        $scope.urlList1 = [];
        $scope.urlList2 = [];
        $scope.button_disabled = "";
        $scope.button_disabled_val = 1;
        $scope.transferAttachURL={};
        $scope.transferAttachURL.upload = "";
        $scope.orderDetailURL = {};
        $scope.orderDetailURL.upload = "";


        
        //编辑功能
        $scope.zzdown = true;
        $scope.$on("uploadupdateid77", function (event, fileUrl, index, broadData) {
            if (broadData) {
                if (broadData.file !== "") {
                    $scope.initOrderInfo.imgName1 = broadData.file.name;
                    $scope.zzdown = false;
                }
                $scope.uploader = broadData.uploader;
//                $scope.orderDetailURL.errorInfo = broadData.errorInfo;
            }
            $scope.transferAttachURL.upload = fileUrl;
        });
        $scope.orderdown = true;
        $scope.$on("uploadupdateid88", function (event, fileUrl, index, broadData) {
            if (broadData) {
                if (broadData.file !== "") {
                    $scope.initOrderInfo.imgName2 = broadData.file.name;
                    $scope.orderdown = false;
                }
                $scope.uploader = broadData.uploader;
//                $scope.transferAttachURL.errorInfo = broadData.errorInfo;
            }
            $scope.orderDetailURL.upload = fileUrl;
        });

        //体验申请单
        $scope.tydown = true;
        $scope.$on("uploadupdateid99", function (event, fileUrl, index, broadData) {
            if (broadData) {
                if (broadData.file !== "") {
                	$scope.initOrderInfo.imgName1 = broadData.file.name;
                	$scope.tydown = false;
                } 
                $scope.uploader = broadData.uploader;
//                $scope.transferAttachURL.errorInfo = broadData.errorInfo;
            }
            $scope.transferAttachURL.upload = fileUrl;
        });

        
        // 初始化显示的信息
        $scope.initOrderInfo = {
            enterpriseName: $scope.enterpriseName,
            orderCode: "",// 订单编号
            isExperienceVersion: 1,
            orderAmount: 0,// 订单金额
            orderName: '',
            orderStatus: 1,
            transferAttachURL: '',
            orderDetailURL: '',
            imgName1: '',
            imgName2: '',
            effictiveTime: "",
            expireTime: "",
            caller: {
                memberCount: '',
                pxAmountPerPerson: ''
            },
            called: {
                memberCount: '',
                pxAmountPerPerson: ''
            },
            callerAndcalled: {
                memberCount: '',
                pxAmountPerPerson: ''
            },
            guaduanAmount: "",
            guaduanAmountCUCC: "",
            guaduanAmountCTCC: "",
            guacaiAmount: "",

            pxByTimes: "",// 屏显按次
            gdByTimes: "",// 挂短按次
            gcByTimes: "",// 挂彩按次


            pxByTimes_cucc: "",// 屏显按次
            pxByTimes_ctcc: "",// 屏显按次


            isOldData: false,
            quantity: '',
            
        };
        // 初始化增补订单显示的信息
        $scope.initAddOrderInfo = {
            orderAmount: 0,// 订单金额
            orderName: '',
            orderStatus: 1,
            transferAttachURL: '',
            orderDetailURL: '',
            imgName1: '',
            imgName2: '',
            effictiveTime: "",
            expireTime: "",
            caller: {
                memberCount: '',
                pxAmountPerPerson: ''
            },
            called: {
                memberCount: '',
                pxAmountPerPerson: ''
            },
            callerAndcalled:{
                memberCount: '',
                pxAmountPerPerson: ''
            },
            guaduanAmount: "",
            guacaiAmount: "",
            pxByTimes: "",// 屏显按次
            
            
        };
        
      //默认企业名称
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'transferAttach'
        };
        $scope.uploadParam2 = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'orderDetail'
        };
        $scope.queryOrderDetail();
        $scope.queryAddOrderDetail();
        
    };
    
    $scope.formatDate = function (str) {
        if (!str) {
            return '';
        }
        var newDateStr = "";
        newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
        return newDateStr;
    };
    
    $scope.gotoList = function () {
        location.href = '../orderList/orderList.html?enterpriseID=' + $scope.enterpriseID;
    }
    $scope.queryAddOrderDetail = function () {
        var req = {
            "ownerOrderID": $scope.orderCode,
            "isReturnOrderItem": 1,
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.addOrderListInfo = result.orderList || [];
                        if ($scope.addOrderListInfo.length > 0) {
                            $scope.hasAddOrder = true;  // 增补订单
                            $scope.addOrderListInfo.sort(function (a, b) {
                                return a.createTime - b.createTime;
                            })
                        }
                        for (var i = 0, len = $scope.addOrderListInfo.length; i < len; i++) {
                            var item = $scope.addOrderListInfo[i];
                            item.id = i;
                            item.idde = '10'+i;
                            item.idty = '20'+ i;
                            
                            if (item.orderType == 2) {
                                if (item.transferAttachURL !== null) {
                                    var a = item.transferAttachURL.split('/');
                                    item.imgName1 = a[a.length - 1];
                                }
                                if (item.orderDetailURL !== null) {
                                    var b = item.orderDetailURL.split('/');
                                    item.imgName2 = b[b.length - 1];
                                }
                                item.transferAttachURL = CommonUtils.formatPic(item.transferAttachURL);
                                item.orderDetailURL = CommonUtils.formatPic(item.orderDetailURL);
                                item.effictiveTime = $scope.formatDate(item.effictiveTime);
                                $scope.startTime = item.effictiveTime;
                                item.expireTime = $scope.formatDate(item.expireTime);
                                // 增补订单的子项遍历
                                item.caller = {
                                    memberCount: '',
                                    pxAmountPerPerson: ''
                                };
                                item.called = {
                                    memberCount: '',
                                    pxAmountPerPerson: ''
                                };
                                item.callerAndcalled = {
                                     memberCount: '',
                                     pxAmountPerPerson: ''
                                };
                                item.guaduanAmount = "";
                                item.guaduanAmountCUCC = "";
                                item.guaduanAmountCTCC = "";
                                item.guacaiAmount = "";
                                item.pxByTimes = "";
                                item.pxByTimes_cucc = "";
                                item.pxByTimes_ctcc = "";
                                item.hasAddGC = false;
                                item.hasAddGCBy = false;
                                item.hasAddGDBy = false;
                                item.hasAddGD = false;
                                item.hasAddGDCUCC = false;
                                item.hasAddGDCTCC = false;
                                item.hasAddPX = false;
                                item.hasAddPXCUCC = false;
                                item.hasAddPXCTCC = false;
                                item.hasAddPX_cucc = false;
                                item.hasAddPX_ctcc = false;
                                item.isOldData = false;
                                item.hasAddCMCC = false;
                                item.hasAddCUCC = false;
                                item.hasAddCTCC = false;
                                for (var j = 0, len1 = item.orderItemList.length; j < len1; j++) {
                                    var innerItem = item.orderItemList[j].product;
                                    switch (innerItem.subServType) {
                                        case 1:
                                            // 主叫需要对老数据兼容
                                            item.hasAddPX = true;
                                            item.hasAddPXCUCC = true;
                                            item.hasAddPXCTCC = true;
                                            item.hasAddCMCC = true;
                                            item.caller.memberCount = innerItem.memberCount;
                                            item.caller.pxAmountPerPerson = innerItem.maxAmountPerPerson;
                                            // 老数据
                                            if (innerItem.productType === 0) {
                                                item.isOldData = true;
                                            } else {
                                                item.quantity = item.orderItemList[j].quantity;
                                                item.taocanTotalAmount = item.orderItemList[j].totalAmount;
                                                item.taocanName = innerItem.productName;
                                            }
                                            break;
                                        case 2: // 被叫需要对老数据接口兼容
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                item.hasAddPX = true;
                                                item.hasAddCMCC = true;
                                                item.called.memberCount = innerItem.memberCount;
                                                item.called.pxAmountPerPerson = innerItem.maxAmountPerPerson;
                                                // 老数据
                                                if (innerItem.productType === 0) {
                                                    item.isOldData = true;
                                                } else {
                                                    item.taocanTotalAmount = item.orderItemList[j].totalAmount;
                                                    item.quantity = item.orderItemList[j].quantity;
                                                    item.taocanName = innerItem.productName;
                                                }
                                            }

                                            if (innerItem.reservedsEcpmp.reserved1 === "2") {
                                                item.hasAddPXCUCC = true;
                                                if (innerItem.chargeType == 1) {
                                                    item.hasAddPX_cucc = true;
                                                    item.hasAddCUCC = true;
                                                    if (innerItem.isLimit == 1) {
                                                        item.pxByTimes_cucc = innerItem.amount * item.orderItemList[j].quantity;
                                                    } else {
                                                        item.addPX_noLimit_cucc = true;
                                                    }
                                                }
                                            }
                                            if (innerItem.reservedsEcpmp.reserved1 === "3") {
                                                item.hasAddPXCTCC = true;
                                                if (innerItem.chargeType == 1) {
                                                    item.hasAddPX_ctcc = true;
                                                    item.hasAddCTCC = true;
                                                    if (innerItem.isLimit == 1) {
                                                        item.pxByTimes_ctcc = innerItem.amount * item.orderItemList[j].quantity;
                                                    } else {
                                                        item.addPX_noLimit_ctcc = true;
                                                    }
                                                }
                                            }
                                            break;
                                        case 3:
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                item.hasAddPX = true;
                                                item.hasAddCMCC = true;

                                                if (innerItem.isLimit == 1) {
                                                    if(innerItem.chargeType == 2){                  // 按人包月

                                                        item.callerAndcalled.memberCount = innerItem.memberCount;
                                                        item.callerAndcalled.pxAmountPerPerson = innerItem.maxAmountPerPerson;

                                                        item.taocanTotalAmount = item.orderItemList[j].totalAmount;
                                                        item.quantity = item.orderItemList[j].quantity;
                                                        item.taocanName = innerItem.productName;

                                                    }else{                                          // 按次

                                                        item.pxByTimes = innerItem.amount * item.orderItemList[j].quantity;
                                                    }

                                                }else{
                                                    item.addPX_noLimit_cmcc = true;
                                                }


// if (innerItem.isLimit == 1) {
// item.pxByTimes = innerItem.amount * item.orderItemList[j].quantity;
// } else {
// item.addPX_noLimit_cmcc = true;
// }
                                            }
                                            if (innerItem.reservedsEcpmp.reserved1 === "2") {
                                                item.hasAddPXCUCC = true;
                                                item.hasAddPX_cucc = true;
                                                item.hasAddCUCC = true;
                                                if (innerItem.isLimit == 1) {
                                                    item.pxByTimes_cucc = innerItem.amount * item.orderItemList[j].quantity;
                                                } else {
                                                    item.addPX_noLimit_cucc = true;
                                                }
                                            }
                                            if (innerItem.reservedsEcpmp.reserved1 === "3") {
                                                item.hasAddPXCTCC = true;
                                                item.hasAddPX_ctcc = true;
                                                item.hasAddCTCC = true;
                                                if (innerItem.isLimit == 1) {
                                                    item.pxByTimes_ctcc = innerItem.amount * item.orderItemList[j].quantity;
                                                } else {
                                                    item.addPX_noLimit_ctcc = true;
                                                }
                                            }

                                            break;
                                        case 4:
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {   // 加if判断
																								// 20191107
                                                item.hasAddGD = true;
                                                item.hasAddCMCC = true;

                                                if (innerItem.isLimit == 1) {
                                                    if (innerItem.chargeType == 1) {
                                                        item.guaduanAmount = innerItem.amount * item.orderItemList[j].quantity;
                                                    } else {
                                                        item.hasAddGDBy = true;
                                                        item.gdtaocanTotalAmount = item.orderItemList[j].totalAmount;
                                                        item.gdquantity = item.orderItemList[j].quantity;
                                                        item.gdtaocanName = innerItem.productName;
                                                    }
                                                }
                                            }

                                            if (innerItem.reservedsEcpmp.reserved1 === "2") {   // 联通挂短
                                                item.hasAddGDCUCC = true;
                                                item.hasAddCUCC = true;

                                                if (innerItem.isLimit == 1) {
                                                    item.guaduanAmountCUCC = innerItem.amount * item.orderItemList[j].quantity;

                                                }
                                            }

                                            if (innerItem.reservedsEcpmp.reserved1 === "3") {   // 电信挂短
                                                item.hasAddGDCTCC = true;
                                                item.hasAddCTCC = true;

                                                if (innerItem.isLimit == 1) {
                                                    item.guaduanAmountCTCC = innerItem.amount * item.orderItemList[j].quantity;

                                                }
                                            }

                                            break;
                                        case 8:
                                            item.hasAddGC = true;
                                            item.hasAddCMCC = true;

                                            if (innerItem.chargeType == 1) {
                                                item.guacaiAmount = innerItem.amount * item.orderItemList[j].quantity;
                                            } else {
                                                item.hasAddGCBy = true;
                                                item.gctaocanTotalAmount = item.orderItemList[j].totalAmount;
                                                item.gcquantity = item.orderItemList[j].quantity;
                                                item.gctaocanName = innerItem.productName;
                                            }
                                            break;
                                        case 10:
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                item.hasAddPX = true;
                                                item.hasAddCMCC = true;
                                                if (innerItem.isLimit == 1) {
                                                    item.pxByTimes = innerItem.amount * item.orderItemList[j].quantity;
                                                } else {
                                                    item.addPX_noLimit_cmcc = true;
                                                }
                                            }
                                            break;
                                          case 16:
                                           item.hasAddCMCC = true;
                                           item.hasAddZC = true;
                                            item.zcAmount = innerItem.amount * item.orderItemList[j].quantity;
                                           break;
                                        case 17:
                                            // 移动
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                item.hasAddCMCC = true;
                                                item.hasAddGroupSendSMSCMCC = true;
                                                item.groupSendSMSCUCCAmount = innerItem.amount * item.orderItemList[j].quantity;
                                            }else if(innerItem.reservedsEcpmp.reserved1 === "2"){
                                                item.hasAddCUCC = true;
                                                item.hasGroupSendSMSCUCC = true;
                                                item.groupSendSMSCUCCAmount = innerItem.amount * item.orderItemList[j].quantity;
                                            }else if(innerItem.reservedsEcpmp.reserved1 === "3"){
                                                item.hasAddCTCC = true;
                                                item.hasGroupSendSMSCTCC = true;
                                                item.groupSendSMSCTCCAmount = innerItem.amount * item.orderItemList[j].quantity;
                                            }

                                            break;
                                        default:
                                            break;
                                    }
                                }
                            }
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();

                })
            }
        });
    };
    $scope.queryOrderDetail = function () {
        var req = {
            "orderCode": $scope.orderCode,
            "isReturnOrderItem": 1,
            "objectID": $scope.objectID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.orderListInfo = result.orderList || [];

                        for (var i = 0, len = $scope.orderListInfo.length; i < len; i++) {
                            var item = $scope.orderListInfo[i];
                            // 主订单详情
                            $scope.initOrderInfo.orderCode = item.orderCode;
                            $scope.servType = item.servType;
                            $scope.initOrderInfo.orderAmount = item.amount;
                            $scope.initOrderInfo.orderName = item.orderName;
                            $scope.initOrderInfo.orderStatus = item.status;
                            $scope.initOrderInfo.isExperienceVersion = item.isExperience;
                            if ($scope.initOrderInfo.isExperienceVersion == 1) 
                            {
                            	$("#UPZZMX").css("display", "none");
                                $("#UPDDMX").css("display", "none");
                                $("#UPTYSQD").css("visibility", "visible");
                    		}
                            else
                            {
                                $("#UPZZMX").css("display", "");
                                $("#UPDDMX").css("display", "");
                                $("#UPTYSQD").css("visibility", "hidden");
                            }
                            
//                            if(1 == item.isExperience)
//                            {
//                            	$("#MXANDDD").css("display","none");
//                            }else
//                            {
//                            	$("#TYDD").css("display","none");
//                            }
                            
                            $scope.initOrderInfo.transferAttachURL = CommonUtils.formatPic(item.transferAttachURL);
                            $scope.initOrderInfo.orderDetailURL = CommonUtils.formatPic(item.orderDetailURL);
                            if (item.transferAttachURL !== null) {
                                var a = item.transferAttachURL.split('/');
                                $scope.initOrderInfo.imgName1 = a[a.length - 1];
                            }
                            if (item.orderDetailURL !== null) {
                                var b = item.orderDetailURL.split('/');
                                $scope.initOrderInfo.imgName2 = b[b.length - 1];
                            }
                            $scope.initOrderInfo.effictiveTime = $scope.formatDate(item.effictiveTime);
                            
                            $scope.startTime = item.effictiveTime;
                            $scope.initOrderInfo.expireTime = $scope.formatDate(item.expireTime);
                            // 主订单的子项遍历
                            for (var j = 0, len1 = item.orderItemList.length; j < len1; j++) {
                                var innerItem = item.orderItemList[j].product;


                                switch (innerItem.subServType) {
                                    case 1: // 主叫需要对老数据兼容
                                        $scope.hasPX = true;
                                        $scope.hasPXCUCC = true;
                                        $scope.hasPXCTCC = true;
                                        $scope.hasCMCC = true;
                                        $scope.initOrderInfo.caller.memberCount = innerItem.memberCount;
                                        $scope.initOrderInfo.caller.pxAmountPerPerson = innerItem.maxAmountPerPerson;
                                        // 老数据
                                        if (innerItem.productType === 0) {
                                            $scope.initOrderInfo.isOldData = true;
                                        } else {
                                            $scope.initOrderInfo.quantity = item.orderItemList[j].quantity;
                                            $scope.initOrderInfo.taocanTotalAmount = item.orderItemList[j].totalAmount;
                                            $scope.initOrderInfo.taocanName = innerItem.productName;
                                        }
                                        break;
                                    case 2:// 被叫需要对老数据接口兼容
                                        if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                            $scope.hasPX = true;
                                            $scope.hasCMCC = true;
                                            $scope.initOrderInfo.called.memberCount = innerItem.memberCount;
                                            $scope.initOrderInfo.called.pxAmountPerPerson = innerItem.maxAmountPerPerson;
                                            // 老数据
                                            if (innerItem.productType === 0) {
                                                $scope.initOrderInfo.isOldData = true;
                                            } else {
                                                $scope.initOrderInfo.taocanTotalAmount = item.orderItemList[j].totalAmount;
                                                $scope.initOrderInfo.quantity = item.orderItemList[j].quantity;
                                                $scope.initOrderInfo.taocanName = innerItem.productName;
                                            }
                                        }

                                        if (innerItem.reservedsEcpmp.reserved1 === "2") {
                                            $scope.hasPXCUCC = true;
                                            if (innerItem.chargeType == 1) {
                                                $scope.hasPX_cucc = true;
                                                $scope.hasCUCC = true;
                                                if (innerItem.isLimit == 1) {
                                                    $scope.initOrderInfo.pxByTimes_cucc = innerItem.amount * item.orderItemList[j].quantity;
                                                } else {
                                                    $scope.px_noLimit_cucc = true;
                                                }
                                            }
                                        }
                                        if (innerItem.reservedsEcpmp.reserved1 === "3") {
                                            $scope.hasPXCTCC = true;
                                            if (innerItem.chargeType == 1) {
                                                $scope.hasPX_ctcc = true;
                                                $scope.hasCTCC = true;
                                                if (innerItem.isLimit == 1) {
                                                    $scope.initOrderInfo.pxByTimes_ctcc = innerItem.amount * item.orderItemList[j].quantity;
                                                } else {
                                                    $scope.px_noLimit_ctcc = true;
                                                }
                                            }
                                        }
                                        break;
                                    case 3:
                                        if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                            $scope.hasPX = true;
                                            $scope.hasCMCC = true;

                                            if(innerItem.isLimit == 1){
                                                if(innerItem.chargeType == 2){              // 按人包月

                                                    $scope.initOrderInfo.callerAndcalled.memberCount = innerItem.memberCount;
                                                    $scope.initOrderInfo.callerAndcalled.pxAmountPerPerson = innerItem.maxAmountPerPerson;

                                                    $scope.initOrderInfo.taocanTotalAmount = item.orderItemList[j].totalAmount;
                                                    $scope.initOrderInfo.quantity = item.orderItemList[j].quantity;
                                                    $scope.initOrderInfo.taocanName = innerItem.productName;

                                                }else{                                     // 按次

                                                    $scope.initOrderInfo.pxByTimes = innerItem.amount * item.orderItemList[j].quantity;
                                                }

                                            }else{
                                                $scope.px_noLimit_cmcc = true;
                                            }


// if (innerItem.isLimit == 1) {
// $scope.initOrderInfo.pxByTimes = innerItem.amount *
// item.orderItemList[j].quantity;
// }else{
// $scope.px_noLimit_cmcc = true;
// }
                                        }
                                        if (innerItem.reservedsEcpmp.reserved1 === "2") {
                                            $scope.hasPXCUCC = true;
                                            $scope.hasPX_cucc = true;
                                            $scope.hasCUCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.pxByTimes_cucc = innerItem.amount * item.orderItemList[j].quantity;
                                            } else {
                                                $scope.px_noLimit_cucc = true;
                                            }
                                        }
                                        if (innerItem.reservedsEcpmp.reserved1 === "3") {
                                            $scope.hasPXCTCC = true;
                                            $scope.hasPX_ctcc = true;
                                            $scope.hasCTCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.pxByTimes_ctcc = innerItem.amount * item.orderItemList[j].quantity;
                                            } else {
                                                $scope.px_noLimit_ctcc = true;
                                            }
                                        }
                                        break;
                                    case 4:
                                        if (innerItem.reservedsEcpmp.reserved1 === "1") {    // ADD
																								// 20191107
                                            $scope.hasGD = true;
                                            $scope.hasCMCC = true;
                                            if (innerItem.isLimit == 1) {
                                                if (innerItem.chargeType == 2) {

                                                $scope.gd_ByLimit_cmcc = true;

                                                $scope.initOrderInfo.gdtaocanTotalAmount = item.orderItemList[j].totalAmount;
                                                $scope.initOrderInfo.gdquantity = item.orderItemList[j].quantity;
                                                $scope.initOrderInfo.gdtaocanName = innerItem.productName;

                                                } else {
                                                    $scope.initOrderInfo.guaduanAmount = innerItem.amount * item.orderItemList[j].quantity;
                                                }
                                            } else {
                                                $scope.gd_noLimit_cmcc = true;
                                            }
                                        }

                                        if (innerItem.reservedsEcpmp.reserved1 === "2") {    // 联通挂短
                                            $scope.hasGDCUCC = true;
                                            $scope.hasCUCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.guaduanAmountCUCC = innerItem.amount * item.orderItemList[j].quantity;
                                            } else {
                                                $scope.gd_noLimit_cmccCUCC = true;
                                            }
                                        }

                                        if (innerItem.reservedsEcpmp.reserved1 === "3") {    // 电信挂短
                                            $scope.hasGDCTCC = true;
                                            $scope.hasCTCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.guaduanAmountCTCC = innerItem.amount * item.orderItemList[j].quantity;
                                            } else {
                                                $scope.gd_noLimit_cmccCTCC = true;
                                            }
                                        }

                                        break;
                                    case 8:
                                        $scope.hasGC = true;
                                        $scope.hasCMCC = true;
                                        if (innerItem.isLimit == 1) {
                                            if (innerItem.chargeType == 2) {
                                                $scope.gc_ByLimit_cmcc = true;
                                                $scope.initOrderInfo.gctaocanTotalAmount = item.orderItemList[j].totalAmount;
                                                $scope.initOrderInfo.gcquantity = item.orderItemList[j].quantity;
                                                $scope.initOrderInfo.gctaocanName = innerItem.productName;
                                            } else {
                                                $scope.initOrderInfo.guacaiAmount = innerItem.amount * item.orderItemList[j].quantity;
                                            }
                                        } else {
                                            $scope.gc_noLimit_cmcc = true;
                                        }
                                        break;
                                    case 10:
                                        // 移动广告
                                        if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                            $scope.hasPX = true;
                                            $scope.hasCMCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.pxByTimes = innerItem.amount * item.orderItemList[j].quantity;
                                            } else {
                                                $scope.px_noLimit_cmcc = true;
                                            }
                                        }
                                        break;
                                    case 16:
                                        $scope.hasZC = true;
                                         $scope.hasCMCC = true;
                                         if (innerItem.isLimit == 1) {
                                            $scope.initOrderInfo.zcAmount = innerItem.amount * item.orderItemList[j].quantity;
                                            $scope.zx_noLimit_cmcc = false;
                                        } else {
                                            $scope.zx_noLimit_cmcc = true;
                                        }
                                        break;
                                    case 17:
                                        // 移动
                                        if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                            $scope.hasCMCC = true;
                                            $scope.hasGroupSendSMSCMCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.groupSendSMSCMCCAmount = innerItem.amount * item.orderItemList[j].quantity;
                                                $scope.groupSendSMSCMCCNoLimit = false;
                                            } else {
                                                $scope.groupSendSMSCMCCNoLimit = true;
                                            }

                                        }else if(innerItem.reservedsEcpmp.reserved1 === "2"){
                                            $scope.hasCUCC = true;
                                            $scope.hasGroupSendSMSCUCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.groupSendSMSCUCCAmount = innerItem.amount * item.orderItemList[j].quantity;
                                                $scope.groupSendSMSCUCCNoLimit = false;
                                            } else {
                                                $scope.groupSendSMSCUCCNoLimit = true;
                                            }
                                        }else if(innerItem.reservedsEcpmp.reserved1 === "3"){
                                            $scope.hasCTCC = true;
                                            $scope.hasGroupSendSMSCTCC = true;
                                            if (innerItem.isLimit == 1) {
                                                $scope.initOrderInfo.groupSendSMSCTCCAmount = innerItem.amount * item.orderItemList[j].quantity;
                                                $scope.groupSendSMSCTCCNoLimit = false;
                                            } else {
                                                $scope.groupSendSMSCTCCNoLimit = true;
                                            }
                                        }

                                        break;
                                    default :
                                        break;
                                }
                            }
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();

                })
            }
        });
    };
    
    //后续post的函数
    $scope.updateOrder = function () {
        if ($scope.expireTime) {
        	var aa = $scope.startTime.toString();
        	var start = aa.replace(/-/g,"");
            var end=$scope.expireTime.substring(0,8);
            var serverTime = convertDateFromString(CommonUtils.getServerTime());
            if (start>end || serverTime>end) 
            {
				alert("失效日期不能早于生效日期和当前时间");
				return;
			}
		}
        $scope.updateAddOrder();
        var req = {
            "order": {
            	"orderCode": $scope.orderCode,
                "objectID": $scope.objectID,
                "amount": $scope.initOrderInfo.orderAmount,
                "expireTime": $scope.expireTime,
                "enterpriseID":$scope.enterpriseID,
                "transferAttachURL": $scope.transferAttachURL.upload,
                "orderDetailURL": $scope.orderDetailURL.upload
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/updateOrder",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.tip = "ORDER_EDIT_SUCCESS_MSG";
                        $('#myModal').modal();

                        setTimeout(function () {
                            location.href = '../orderList/orderList.html';
                        }, 1000);
                    }

                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $('#formSub').removeAttr('disabled');
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    };
    
    $scope.updateAddOrder = function (){
        for (var i = 0, len = $scope.addOrderListInfo.length; i < len; i++) {
        	var req = {
                    "order": {
                    	"orderCode": $scope.addOrderListInfo[i].orderCode,
                        "objectID": $scope.addOrderListInfo[i].objectID,
                        "amount": $scope.addOrderListInfo[i].amount,
                        "expireTime": $scope.expireTime,
                        "productID":1,
                        "enterpriseID":$scope.addOrderListInfo[i].enterpriseID,
                        "transferAttachURL":$scope.addOrderListInfo[i].upload,
                    	"orderDetailURL":$scope.addOrderListInfo[i].upload2
                    		}
        			};
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/orderManageService/updateOrder",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '1030100000') {
                            $scope.tip = "ORDER_EDIT_SUCCESS_MSG";
                            $('#myModal').modal();

                            setTimeout(function () {
                                location.href = '../orderList/orderList.html';
                            }, 1000);
                        }

                        else {
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                            $('#formSub').removeAttr('disabled');
                            $scope.tip = "1030120500";
                            $('#myModal').modal();
                        }
                    )
                }
            });	
        }
    	
    }
    
    function convertDateFromString(date) {
    	if (date) 
    	{ 
    	var str = date.year+date.month+date.day;
    	return str;
    	}
    }
    
    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        language: "zh-CN",
        minDate : $scope.startTime,
        autoclose: true,
        startDate : new Date()
    });

    
    $('#end').on('changeDate', function () {
        var endTime = document.getElementById("end").value;
        $rootScope.$apply(function () {
            $scope.expireTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + '235959';
        })
    });
});

app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])