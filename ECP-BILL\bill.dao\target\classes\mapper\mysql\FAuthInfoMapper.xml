<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper2.FAuthInfoMapper">
	<resultMap id="fAuthInfoModel" type="com.huawei.jaguar.dsdp.bill.dao.domain2.FAuthInfoWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="authCode" column="authCode" javaType="java.lang.String" />
		<result property="authName" column="authName" javaType="java.lang.String" />
		<result property="authDesc" column="authDesc" javaType="java.lang.String" />
		<result property="authType" column="authType" javaType="java.lang.Integer" />
		<result property="parentAuthID" column="parentAuthID" javaType="java.lang.Integer" />
		<result property="authBelongType" column="authBelongType" javaType="java.lang.Integer" />
		<result property="operateTime" column="operateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>

     <sql id="fauthColumn">
        id,
		authCode,
		authName,
		authDesc,
		authType,
		parentAuthID,
		authBelongType,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
     </sql>
     
	<select id="getFAuthInfoByID" resultMap="fAuthInfoModel">
			select
				id,
				authCode,
				authName,
				authDesc,
				authType,
				parentAuthID,
				authBelongType,
				operateTime,
				operatorID,
				extInfo,
				reserved1,
				reserved2,
				reserved3,
				reserved4,
				reserved5,
				reserved6,
				reserved7,
				reserved8,
				reserved9,
				reserved10 from
				dsum_t_fauth where id = #{id}
    </select>
    
    <select id="getFAuthInfo" resultMap="fAuthInfoModel">
			select
				id,
				authCode,
				authName,
				authDesc,
				authType,
				parentAuthID,
				authBelongType,
				operateTime,
				operatorID,
				extInfo,
				reserved1,
				reserved2,
				reserved3,
				reserved4,
				reserved5,
				reserved6,
				reserved7,
				reserved8,
				reserved9,
				reserved10 from
				dsum_t_fauth where id in
			<foreach item="ids" index="index" collection="list"
				open="(" separator="," close=")">
				#{ids}
			</foreach>
    </select>   

    <select id="getFAuthInfoListByRoleIDs" resultType="com.huawei.jaguar.dsdp.bill.dao.domain2.RoleFAuthInfoRelWrapper">
			select
			    t.roleID,
				t1.id,
				t1.authCode,
				t1.authName,
				t1.authDesc,
				t1.authType,
				t1.parentAuthID,
				t1.authBelongType,
				t1.operateTime,
				t1.operatorID			 
				from
				dsum_t_role_auth t,dsum_t_fauth t1				
				where t.authID = t1.ID
                and t.authType = 1
                and t.roleID in
			   <foreach item="roleIDs" index="index" collection="list"
				open="(" separator="," close=")">
				#{roleIDs}
			  </foreach>
    </select>
    

   	<select id="queryFAuthInfo" resultMap="fAuthInfoModel">
	    SELECT
		 <include refid="fauthColumn"/>
		FROM dsum_t_fauth e 
		<trim prefix="where" prefixOverrides="and|or">
		   <if test="authName!=null and authName!=''">and authName like concat("%", #{authName}, "%")</if>
		   <if test="authType!=null and authType!=''">and authType = #{authType}</if>
		   <if test="authBelongType!=null and authBelongType!=''">and (authBelongType = #{authBelongType} or authBelongType is null)</if>
		 </trim>
	</select>
	
	<select id="queryFAuthIDByID" resultType="java.lang.Integer">
			select id from dsum_t_fauth 
			where id in
			<foreach item="ids" index="index" collection="list"
				open="(" separator="," close=")">
				#{ids}
			 </foreach>
    </select>
    
    <select id="queryParentAuthIDByID" resultType="java.lang.Integer">
			select parentAuthID from dsum_t_fauth 
			where id in
			<foreach item="ids" index="index" collection="collection"
				open="(" separator="," close=")">
				#{ids}
			 </foreach>
    </select>

</mapper>