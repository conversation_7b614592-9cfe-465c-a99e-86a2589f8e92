var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n"])

app.controller('defaultFrequencyController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {

        $scope.accountID = $.cookie('accountID');
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');

        //初始化所有参数
        $scope.initAllInfo = {
            mp: {
                periodType: 0, //默认0
                oneTemplateDayLimit: null,
                oneDeliveryInterval: null,
                status: "0",      //默认0
                id: null,
                servRuleCode: null,
                operatorID: null,
                subServType: 0, //默认0
                isUse: 1 //默认1
            },
            rx: {
                oneTemplateDayLimit: null,
                oneDeliveryInterval: null,
                status: "0",      //默认0
                id: null,
                servRuleCode: null,
                operatorID: null,
                subServType: 0, //默认0
                isUse: 1 //默认1
            },
            gg: {
                periodType: 0, //默认0
                oneTemplateDayLimit: null,
                oneDeliveryInterval: null,
                status: "0",      //默认0
                id: null,
                servRuleCode: null,
                operatorID: null,
                subServType: 0, //默认0
                isUse: 1 //默认1
            },
            TZSms: {
                periodType: 0, //默认0
                oneTemplateDayLimit: 10,
                oneDeliveryInterval: null,
                status: "0",      //默认0
                id: null,
                servRuleCode: null,
                operatorID: null,
                subServType: 0, //默认0
                isUse: 1, //默认1
                servType: 4
            }
            // TZFlash: {
            //     periodType: 0, //默认0
            //     oneTemplateDayLimit: 10,
            //     oneDeliveryInterval: null,
            //     status: "0",      //默认0
            //     id: null,
            //     servRuleCode: null,
            //     operatorID: null,
            //     subServType: 0, //默认0
            //     isUse: 1, //默认1
            //     servType: 4,
            //     reserved1: 10
            // },
            // TZMms: {
            //     periodType: 0, //默认0
            //     oneTemplateDayLimit: 10,
            //     oneDeliveryInterval: null,
            //     status: "0",      //默认0
            //     id: null,
            //     servRuleCode: null,
            //     operatorID: null,
            //     subServType: 0, //默认0
            //     isUse: 1, //默认1
            //     servType: 4,
            //     reserved1: 8
            // },
            // TZUSSD: {
            //     periodType: 0, //默认0
            //     oneTemplateDayLimit: 10,
            //     oneDeliveryInterval: null,
            //     status: "0",      //默认0
            //     id: null,
            //     servRuleCode: null,
            //     operatorID: null,
            //     subServType: 0, //默认0
            //     isUse: 1, //默认1
            //     servType: 4,
            //     reserved1: 9
            // },
            // TZCX: {
            //     periodType: 0, //默认0
            //     oneTemplateDayLimit: 10,
            //     oneDeliveryInterval: null,
            //     status: "0",      //默认0
            //     id: null,
            //     servRuleCode: null,
            //     operatorID: null,
            //     subServType: 0, //默认0
            //     isUse: 1, //默认1
            //     servType: 4,
            //     reserved1: 13
            // }
        };

        //下拉框
        $scope.periodTypeList = [
            {
                id: 0,
                name: "日"
            },
            {
                id: 1,
                name: "周"
            },
            {
                id: 2,
                name: "月"
            }
        ];

        $scope.changMPOption = function () {
            if ($scope.initAllInfo.mp.periodType == 0) {
                $scope.initAllInfo.mp.oneTemplateDayLimit = "50"
            } else {
                $scope.initAllInfo.mp.oneTemplateDayLimit = "2000"
            }
        }

        $scope.changGGOption = function () {
            if ($scope.initAllInfo.gg.periodType == 0) {
                $scope.initAllInfo.gg.oneTemplateDayLimit = "50"
            } else {
                $scope.initAllInfo.gg.oneTemplateDayLimit = "2000"
            }
        }

        // 查询默认频控
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/defaultFrequencyService/queryDefaultServiceRuleList",
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {

                        if (data.serviceRuleList !== null && data.serviceRuleList.length > 0) {
                            $scope.operateType = "2";
                            $scope.serviceRuleListInfo = data.serviceRuleList;
                            for (var i in $scope.serviceRuleListInfo) {
                                var item = $scope.serviceRuleListInfo[i];

                                if (item.servType === 1) {
                                    // 周期类型
                                    $scope.initAllInfo.mp.periodType = item.periodType;
                                    $scope.initAllInfo.mp.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                    $scope.initAllInfo.mp.oneDeliveryInterval = item.oneDeliveryInterval;
                                    $scope.initAllInfo.mp.id = item.id;

                                }
                                if (item.servType === 2) {
                                    $scope.initAllInfo.rx.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                    $scope.initAllInfo.rx.oneDeliveryInterval = item.oneDeliveryInterval;
                                    $scope.initAllInfo.rx.id = item.id;
                                }

                                if (item.servType === 3) {
                                    // 周期类型
                                    $scope.initAllInfo.gg.periodType = item.periodType;
                                    $scope.initAllInfo.gg.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                    $scope.initAllInfo.gg.oneDeliveryInterval = item.oneDeliveryInterval;
                                    $scope.initAllInfo.gg.id = item.id;
                                }


                                if (item.servType === 4) {
                                    // if(item.reserved1 == 11){
                                        $scope.initAllInfo.TZSms.periodType = item.periodType;
                                        $scope.initAllInfo.TZSms.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                        $scope.initAllInfo.TZSms.oneDeliveryInterval = item.oneDeliveryInterval;
                                        $scope.initAllInfo.TZSms.id = item.id;
                                    // }
                                    // if(item.reserved1 == 10){
                                    //     $scope.initAllInfo.TZFlash.periodType = item.periodType;
                                    //     $scope.initAllInfo.TZFlash.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                    //     $scope.initAllInfo.TZFlash.oneDeliveryInterval = item.oneDeliveryInterval;
                                    //     $scope.initAllInfo.TZFlash.id = item.id;
                                    // }
                                    // if(item.reserved1 == 8){
                                    //     $scope.initAllInfo.TZMms.periodType = item.periodType;
                                    //     $scope.initAllInfo.TZMms.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                    //     $scope.initAllInfo.TZMms.oneDeliveryInterval = item.oneDeliveryInterval;
                                    //     $scope.initAllInfo.TZMms.id = item.id;
                                    // }
                                    // if(item.reserved1 == 9){
                                    //     $scope.initAllInfo.TZUSSD.periodType = item.periodType;
                                    //     $scope.initAllInfo.TZUSSD.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                    //     $scope.initAllInfo.TZUSSD.oneDeliveryInterval = item.oneDeliveryInterval;
                                    //     $scope.initAllInfo.TZUSSD.id = item.id;
                                    // }
                                    // if(item.reserved1 == 13){
                                    //     $scope.initAllInfo.TZCX.periodType = item.periodType;
                                    //     $scope.initAllInfo.TZCX.oneTemplateDayLimit = item.oneTemplateDayLimit;
                                    //     $scope.initAllInfo.TZCX.oneDeliveryInterval = item.oneDeliveryInterval;
                                    //     $scope.initAllInfo.TZCX.id = item.id;
                                    // }
                                }
                            }
                        } else {
                            $scope.operateType = "1";
                        }
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            }
        });

    }

    $scope.getFatherConfig = function () {
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/defaultFrequencyService/queryDefaultServiceRuleList",
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        if (data.serviceRuleList !== null && data.serviceRuleList.length > 0) {
                            $scope.serviceRuleListInfo = data.serviceRuleList;
                            for (var i in $scope.serviceRuleListInfo) {
                                var item = $scope.serviceRuleListInfo[i];
                            }
                        }
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
    $scope.refresh = function () {
        if ($scope.tip == '**********') {
            setTimeout(function () {
                location.reload();
            }, 500)
        }
    }

    //新增或修改业务规则
    $scope.UpdateRuleList = function () {
        var req = {
            "serviceRuleList": [],
            "operateType": $scope.operateType
        };
        // 周期类型
        if (!$scope.initAllInfo.mp.periodType) {
            $scope.initAllInfo.mp.periodType == undefined ? 0 : $scope.initAllInfo.mp.periodType
        }
        if (!$scope.initAllInfo.mp.oneTemplateDayLimit) {
            $scope.initAllInfo.mp.oneTemplateDayLimit = null;
        }
        if (!$scope.initAllInfo.mp.oneDeliveryInterval) {
            $scope.initAllInfo.mp.oneDeliveryInterval = null;
        }
        if (!$scope.initAllInfo.rx.oneTemplateDayLimit) {
            $scope.initAllInfo.rx.oneTemplateDayLimit = null;
        }
        if (!$scope.initAllInfo.rx.oneDeliveryInterval) {
            $scope.initAllInfo.rx.oneDeliveryInterval = null;
        }
        // 周期类型
        if (!$scope.initAllInfo.gg.periodType) {
            $scope.initAllInfo.gg.periodType == undefined ? 0 : $scope.initAllInfo.gg.periodType
        }
        if (!$scope.initAllInfo.gg.oneTemplateDayLimit) {
            $scope.initAllInfo.gg.oneTemplateDayLimit = null;
        }
        if (!$scope.initAllInfo.gg.oneDeliveryInterval) {
            $scope.initAllInfo.gg.oneDeliveryInterval = null;
        }
        if ($scope.enterpriseType == 3) {
            $scope.enterpriseIDParam = $scope.subEnterpriseID
        } else {
            $scope.enterpriseIDParam = $scope.enterpriseID
        }

        req.serviceRuleList.push({
            id: $scope.initAllInfo.mp.id,
            servRuleCode: $scope.initAllInfo.mp.servRuleCode,
            servType: "1",
            enterpriseID: $scope.enterpriseIDParam,
            // 周期类型
            periodType: $scope.initAllInfo.mp.periodType,
            oneTemplateDayLimit: $scope.initAllInfo.mp.oneTemplateDayLimit,
            oneDeliveryInterval: $scope.initAllInfo.mp.oneDeliveryInterval,
            status: $scope.initAllInfo.mp.status,
            creatorID: $scope.accountID,
            operatorID: $scope.accountID,
            subServType: $scope.initAllInfo.mp.subServType,
            isUse: $scope.initAllInfo.mp.isUse,
            extInfo: {"periodType": $scope.initAllInfo.mp.periodType}
        })

        req.serviceRuleList.push({
            id: $scope.initAllInfo.rx.id,
            servRuleCode: $scope.initAllInfo.rx.servRuleCode,
            servType: "2",
            enterpriseID: $scope.enterpriseIDParam,
            oneTemplateDayLimit: $scope.initAllInfo.rx.oneTemplateDayLimit,
            oneDeliveryInterval: $scope.initAllInfo.rx.oneDeliveryInterval,
            status: $scope.initAllInfo.rx.status,
            creatorID: $scope.accountID,
            operatorID: $scope.accountID,
            subServType: $scope.initAllInfo.rx.subServType,
            isUse: $scope.initAllInfo.rx.isUse,
            extInfo: {"periodType": $scope.initAllInfo.rx.periodType}

        })

        req.serviceRuleList.push({
            id: $scope.initAllInfo.gg.id,
            servRuleCode: $scope.initAllInfo.gg.servRuleCode,
            servType: "3",
            enterpriseID: $scope.enterpriseIDParam,
            // 周期类型
            periodType: $scope.initAllInfo.gg.periodType,
            oneTemplateDayLimit: $scope.initAllInfo.gg.oneTemplateDayLimit,
            oneDeliveryInterval: $scope.initAllInfo.gg.oneDeliveryInterval,
            status: $scope.initAllInfo.gg.status,
            creatorID: $scope.accountID,
            operatorID: $scope.accountID,
            subServType: $scope.initAllInfo.gg.subServType,
            isUse: $scope.initAllInfo.gg.isUse,
            extInfo: {"periodType": $scope.initAllInfo.gg.periodType}

        })


        req.serviceRuleList.push({
            id: $scope.initAllInfo.TZSms.id,
            servRuleCode: $scope.initAllInfo.TZSms.servRuleCode,
            servType: "4",
            enterpriseID: $scope.enterpriseIDParam,
            // 周期类型
            periodType: $scope.initAllInfo.TZSms.periodType,
            oneTemplateDayLimit: $scope.initAllInfo.TZSms.oneTemplateDayLimit,
            oneDeliveryInterval: $scope.initAllInfo.TZSms.oneDeliveryInterval,
            status: $scope.initAllInfo.TZSms.status,
            creatorID: $scope.accountID,
            operatorID: $scope.accountID,
            subServType: $scope.initAllInfo.TZSms.subServType,
            isUse: $scope.initAllInfo.TZSms.isUse,
            extInfo: {"periodType": $scope.initAllInfo.TZSms.periodType},
            reservedsEcpmp:{reserved1:$scope.initAllInfo.TZSms.reserved1,}

        });
        // req.serviceRuleList.push({
        //     id: $scope.initAllInfo.TZFlash.id,
        //     servRuleCode: $scope.initAllInfo.TZFlash.servRuleCode,
        //     servType: "4",
        //     enterpriseID: $scope.enterpriseIDParam,
        //     // 周期类型
        //     periodType: $scope.initAllInfo.TZFlash.periodType,
        //     oneTemplateDayLimit: $scope.initAllInfo.TZFlash.oneTemplateDayLimit,
        //     oneDeliveryInterval: $scope.initAllInfo.TZFlash.oneDeliveryInterval,
        //     status: $scope.initAllInfo.TZFlash.status,
        //     creatorID: $scope.accountID,
        //     operatorID: $scope.accountID,
        //     subServType: $scope.initAllInfo.TZFlash.subServType,
        //     isUse: $scope.initAllInfo.TZFlash.isUse,
        //     extInfo: {"periodType": $scope.initAllInfo.TZFlash.periodType},
        //     reservedsEcpmp:{reserved1:$scope.initAllInfo.TZFlash.reserved1,}
        //
        // });
        // req.serviceRuleList.push({
        //     id: $scope.initAllInfo.TZMms.id,
        //     servRuleCode: $scope.initAllInfo.TZMms.servRuleCode,
        //     servType: "4",
        //     enterpriseID: $scope.enterpriseIDParam,
        //     // 周期类型
        //     periodType: $scope.initAllInfo.TZMms.periodType,
        //     oneTemplateDayLimit: $scope.initAllInfo.TZMms.oneTemplateDayLimit,
        //     oneDeliveryInterval: $scope.initAllInfo.TZMms.oneDeliveryInterval,
        //     status: $scope.initAllInfo.TZMms.status,
        //     creatorID: $scope.accountID,
        //     operatorID: $scope.accountID,
        //     subServType: $scope.initAllInfo.TZMms.subServType,
        //     isUse: $scope.initAllInfo.TZMms.isUse,
        //     extInfo: {"periodType": $scope.initAllInfo.TZMms.periodType},
        //     reservedsEcpmp:{reserved1:$scope.initAllInfo.TZMms.reserved1,}
        //
        //
        // });
        // req.serviceRuleList.push({
        //     id: $scope.initAllInfo.TZUSSD.id,
        //     servRuleCode: $scope.initAllInfo.TZUSSD.servRuleCode,
        //     servType: "4",
        //     enterpriseID: $scope.enterpriseIDParam,
        //     // 周期类型
        //     periodType: $scope.initAllInfo.TZUSSD.periodType,
        //     oneTemplateDayLimit: $scope.initAllInfo.TZUSSD.oneTemplateDayLimit,
        //     oneDeliveryInterval: $scope.initAllInfo.TZUSSD.oneDeliveryInterval,
        //     status: $scope.initAllInfo.TZUSSD.status,
        //     creatorID: $scope.accountID,
        //     operatorID: $scope.accountID,
        //     subServType: $scope.initAllInfo.TZUSSD.subServType,
        //     isUse: $scope.initAllInfo.TZUSSD.isUse,
        //     extInfo: {"periodType": $scope.initAllInfo.TZUSSD.periodType},
        //     reservedsEcpmp:{reserved1:$scope.initAllInfo.TZUSSD.reserved1,}
        //
        //
        // });
        // req.serviceRuleList.push({
        //     id: $scope.initAllInfo.TZCX.id,
        //     servRuleCode: $scope.initAllInfo.TZCX.servRuleCode,
        //     servType: "4",
        //     enterpriseID: $scope.enterpriseIDParam,
        //     // 周期类型
        //     periodType: $scope.initAllInfo.TZCX.periodType,
        //     oneTemplateDayLimit: $scope.initAllInfo.TZCX.oneTemplateDayLimit,
        //     oneDeliveryInterval: $scope.initAllInfo.TZCX.oneDeliveryInterval,
        //     status: $scope.initAllInfo.TZCX.status,
        //     creatorID: $scope.accountID,
        //     operatorID: $scope.accountID,
        //     subServType: $scope.initAllInfo.TZCX.subServType,
        //     isUse: $scope.initAllInfo.TZCX.isUse,
        //     extInfo: {"periodType": $scope.initAllInfo.TZCX.periodType},
        //     reservedsEcpmp:{reserved1:$scope.initAllInfo.TZCX.reserved1,}
        //
        //
        //
        // });


        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/updateDefaultFrequencyControl",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.returnUp = function () {
        location.href = '../enterprise/enterpriseList/enterpriseList.html';
    };
}])
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])