<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.PackageOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.PackageOrder">
        <id column="ID" property="id" />
        <result column="enterpriseID" property="enterpriseID" />
        <result column="packageCode" property="packageCode" />
        <result column="status" property="status" />
        <result column="channel" property="channel" />
        <result column="insertTime" property="insertTime" />
        <result column="updateTime" property="updateTime" />
        <result column="effictiveTime" property="effictiveTime" />
        <result column="expireTime" property="expireTime" />
        <result column="reserved1" property="reserved1" />
        <result column="reserved2" property="reserved2" />
        <result column="reserved3" property="reserved3" />
        <result column="reserved4" property="reserved4" />
        <result column="reserved5" property="reserved5" />
        <result column="reserved6" property="reserved6" />
        <result column="reserved7" property="reserved7" />
        <result column="reserved8" property="reserved8" />
        <result column="reserved9" property="reserved9" />
        <result column="reserved10" property="reserved10" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, enterpriseID, packageCode, status, channel, insertTime, updateTime, effictiveTime, expireTime, reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, reserved10
    </sql>

    <select id="queryPackageByEnterpriseID" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        ecpe_t_package_order
        WHERE
        enterpriseID = #{enterpriseID}
    </select>

    <update id="insertPackageOrder" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.PackageOrder" >
        <selectKey keyProperty="count" resultType="int" order="BEFORE">
            select count(*) as count  from ecpe_t_package_order where enterpriseID = #{enterpriseID} and packageCode=#{packageCode}
        </selectKey>
        <if test="count > 0">
            update ecpe_t_package_order
            set status=0
            where enterpriseID = #{enterpriseID} and packageCode=#{packageCode}
        </if>
        <if test="count==0">
            insert into  ecpe_t_package_order
            (
            ID,
            enterpriseID,
            packageCode,
            status,
            channel,
            insertTime,
            updateTime,
            effictiveTime,
            expireTime,
            reserved1,
            reserved2,
            reserved3,
            reserved4,
            reserved5,
            reserved6,
            reserved7,
            reserved8,
            reserved9,
            reserved10
            )
            values

            (
            next value for  MYCATSEQ_ECPE_T_PACKAGE_ORDER,
            #{enterpriseID},
            #{packageCode},
            #{status},
            #{channel},
            #{insertTime},
            #{updateTime},
            #{effictiveTime},
            #{expireTime},
            #{reserved1},
            #{reserved2},
            #{reserved3},
            #{reserved4},
            #{reserved5},
            #{reserved6},
            #{reserved7},
            #{reserved8},
            #{reserved9},
            #{reserved10}
            )
        </if>
    </update>

    <delete id="deletePackageOrder" parameterType="Map">
        delete from  ecpe_t_package_order  where 1=1
        <if test="enterpriseID!=null and enterpriseID!=''">and enterpriseID = #{enterpriseID}</if>
        <if test="packageCode!=null and packageCode!=''">and packageCode = #{packageCode}</if>
    </delete>

    <update id="updateOrder" parameterType="Map">
        UPDATE ecpe_t_package_order
        SET
        updateTime = now()
        <if test="status!=null and status!=''">,status = #{status}</if>
        <if test="effictiveTime!=null">,effictiveTime = #{effictiveTime}</if>
        <if test="expireTime!=null">,expireTime = #{expireTime}</if>

        WHERE
        enterpriseID =  #{enterpriseID}
        AND packageCode = #{packageCode}
    </update>
</mapper>