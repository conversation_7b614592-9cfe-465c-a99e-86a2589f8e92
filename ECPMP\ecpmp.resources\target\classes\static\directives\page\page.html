<div class="table-tab">
        <nav aria-label="...">
            <ul class="pagination">
                <li>
                    <a class="pagination-first-a" style="cursor: default"  href="JavaScript:;" aria-label="Previous">
                        <span aria-hidden="true">
                        共{{pageInfoDirective.totalPage}}页{{pageInfoDirective.totalCount}}条记录
                        </span>
                    </a>
                </li>

                <li ng-click="changePageDirectives(pageInfoDirective.currentPage-1)" 
                    ng-class="{'no_click':1==pageInfoDirective.currentPage,'pre-page':pageInfoDirective.currentPage>1}">
                    <a href="#" aria-label="Previous"><span aria-hidden="true">上一页</span></a>
                </li>
                <li ng-click="changePageDirectives(1)" ng-class="{'active':1==pageInfoDirective.currentPage}" >
                    <a href="#" aria-label="Previous"><span aria-hidden="true">1</span></a>
                </li>
                <li ng-click="changePageDirectives(2)" ng-show="pageInfoDirective.totalPage>2&&2!=pageInfoDirective.totalPage"
                    ng-class="{'active':2==pageInfoDirective.currentPage}" >
                    <a href="#" aria-label="Previous"><span aria-hidden="true">2</span></a>
                </li>
                <li ng-click="changePageDirectives(3)" ng-show="pageInfoDirective.totalPage>3"
                    ng-class="{'active':3==pageInfoDirective.currentPage}" >
                    <a href="#" aria-label="Previous"><span aria-hidden="true">3</span></a>
                </li>
                <li class="" ng-show="pageInfoDirective.currentPage>5">
                    <a style="cursor: default" href="#">... <span class="sr-only"></span></a>
                </li>
                <li ng-click="changePageDirectives(pageInfoDirective.currentPage-1)" ng-show="pageInfoDirective.currentPage>4 && pageInfoDirective.currentPage-1<=pageInfoDirective.totalPage-2">
                    <a href="#">{{pageInfoDirective.currentPage-1}}<span class="sr-only"></span></a>
                </li>
                <li class="active" ng-show="pageInfoDirective.currentPage>3 && pageInfoDirective.currentPage<pageInfoDirective.totalPage-1">
                    <a href="#">{{pageInfoDirective.currentPage}}<span class="sr-only"></span></a>
                </li>
                <li ng-click="changePageDirectives(pageInfoDirective.currentPage+1)" ng-show="pageInfoDirective.currentPage>4 && pageInfoDirective.currentPage+1<pageInfoDirective.totalPage-2">
                    <a href="#">{{pageInfoDirective.currentPage+1}}<span class="sr-only"></span></a>
                </li>
                <li class="pagination-first-a" ng-show="pageInfoDirective.totalPage>5&&pageInfoDirective.currentPage<(pageInfoDirective.totalPage-2)">
                    <a style="cursor: default" href="#">... <span class="sr-only"></span></a>
                </li>
                <li ng-show="pageInfoDirective.totalPage>4" ng-class="{'active':pageInfoDirective.totalPage-1==pageInfoDirective.currentPage}"
                    ng-click="changePageDirectives(pageInfoDirective.totalPage-1)">
                    <a href="#"><span>{{pageInfoDirective.totalPage-1}}</span></a>
                </li>
                <li ng-show="pageInfoDirective.totalPage>1" ng-class="{'active':pageInfoDirective.totalPage==pageInfoDirective.currentPage}"
                    ng-click="changePageDirectives(pageInfoDirective.totalPage)">
                    <a href="#" aria-label="Next"><span>{{pageInfoDirective.totalPage}}</span></a>
                </li>
                <li ng-click="changePageDirectives(pageInfoDirective.currentPage+1)" 
                    ng-class="{'no_click':pageInfoDirective.totalPage==pageInfoDirective.currentPage||pageInfoDirective.totalPage==0,'next-page':pageInfoDirective.currentPage<pageInfoDirective.totalPage&&pageInfoDirective.totalPage>1}" >
                    <a href="#" aria-label="Next"><span>下一页</span></a>
                </li>

            </ul>
            <div class="pageChoose">
                <select class="form-control" ng-model="pageInfoDirective.pageSize" ng-change="changeSizeDirectives()">
                      <option value="10" >10</option>
                      <option value="20" >20</option>
                      <option value="50" >50</option>
               </select>
            </div>
            <div class="toPage">
                <div style="float:left;overflow:hidden"><span class="page-span">去第</span><input style="float:left;max-width: 75px;" class="form-control" autocomplete="off" type="number" ng-model='toPage' /><span class="page-span">页</span></div>
                <div style="float:left;margin-left:10px ">
                    <!-- <button ng-click="gotoPage()" class="btn">确定</button> -->
                    <li class='li-gotoPage' ng-click="gotoPage()" >
                        <a href="#" aria-label="Next"><span>确定</span></a>
                    </li>
                </div>
            </div>
        </nav>
        
</div>