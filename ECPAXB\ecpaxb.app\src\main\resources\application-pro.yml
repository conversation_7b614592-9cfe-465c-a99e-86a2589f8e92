spring.datasource:
    url: *******************************************************************************************************************************************************************************
    username: ecpe
    password: SAR8MUAJ870wa/LMWtZbKg==
    driverClassName: com.mysql.jdbc.Driver
#    driverClassName: com.mysql.cj.jdbc.Driver
    max-active: 200
    max-idle: 8
    min-idle: 8
    initial-size: 50
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: true
    testOnReturn: true
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 100

redis:
  server:
    connect: 10.124.70.229:17379|10.124.70.230:17379|10.124.70.231:17379
    password: Da1#df2#vc
    connectionTimeout: 2000
    maxIdle: 80
    maxWaitMillis: 60000
    maxTotal: 100
    soTimeout: 2000
    singleKeyTimeOut: 100
    mutiKeyTimeOut: 200
elasticsearch:
  server:
    clustername: cy-es
    connect: 10.124.72.185:9300|10.124.72.186:9300|10.124.72.187:9300|10.124.72.188:9300|10.124.72.189:9300|10.124.72.226:9300|10.124.72.227:9300|10.124.72.228:9300|10.124.72.229:9300|10.124.72.230:9300