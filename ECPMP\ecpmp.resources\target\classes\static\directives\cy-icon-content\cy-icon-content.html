<div class="custom-tooltip-wrapper" id="{{tooltipId}}" ng-show="processedContent">
  <div
    class="img-wrap tooltip-trigger"
    ng-mouseenter="showTip()"
    ng-mouseleave="startHide()"
  >
    <img ng-src="{{imgUrl}}" />
  </div>
  <div
    class="tooltip-content"
    ng-show="isShow && processedContent"
    ng-class="position"
    ng-mouseenter="showTip()"
    ng-mouseleave="startHide()"
  >
    <div class="tooltip-inner" ng-bind-html="processedContent"></div>
  </div>
</div>
