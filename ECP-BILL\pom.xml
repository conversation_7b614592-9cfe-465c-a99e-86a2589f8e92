<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.huawei.jaguar.dsdp.bill</groupId>
	<artifactId>ecp-bill</artifactId>
	<version>0.0.2-SNAPSHOT</version>
	<packaging>pom</packaging>

	<modules>
		<module>bill.api</module>
		<module>bill.core</module>
		<module>bill.commons</module>
		<module>bill.dao</module>
        <module>bill.spi</module>
		<module>bill.service</module>
		<module>bill.schedule</module>
		<module>bill.app</module>
	</modules>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath />
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>21</java.version>
		<ecp-bill.version>0.0.2-SNAPSHOT</ecp-bill.version>
	    <start-class>com.huawei.jaguar.bill.StartApp</start-class>
		<commons.version>V500R005C12B180-SNAPSHOT</commons.version>
		<fastjson.version>1.2.83</fastjson.version>
		<commoncollections.version>4.2</commoncollections.version> 
		<mybatis.spring.boot.version>3.0.4</mybatis.spring.boot.version>
		<elasticsearch.version>5.6.9</elasticsearch.version>
		<jsch.version>0.1.53</jsch.version>
		<commons.compress.version>1.27.1</commons.compress.version>
		<org.apache.poi.version>5.4.0</org.apache.poi.version>
		<jackson.version>2.10.3</jackson.version>
	</properties>
	<profiles>
		<profile>
			<!-- 本地开发环境 -->
			<id>dev</id>
			<properties>
				<cy_eureka.url>http://127.0.0.1:19000/eureka/</cy_eureka.url>
				<kafka.zkNodes>10.124.129.229:2181,10.124.129.230:2181,10.124.129.231:2181</kafka.zkNodes>
				<kafka.brokers>10.124.129.229:9092,10.124.129.230:9092,10.124.129.231:9092</kafka.brokers>
				<config.label>master</config.label>
				<repository.releases.id>nexus-releases</repository.releases.id>
				<repository.releases.url>http://192.168.120.172:8081/nexus/content/repositories/releases/</repository.releases.url>
				<repository.snapshots.id>nexus-snapshots</repository.snapshots.id>
				<repository.snapshots.url>http://192.168.120.172:8081/nexus/content/repositories/snapshots/</repository.snapshots.url>
				<active>dev</active>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<!-- 生产环境 -->
			<id>production</id>
			<properties>
				<cy_eureka.url>http://10.124.70.218:19000/eureka/,http://10.124.70.219:19000/eureka/,http://10.124.70.220:19000/eureka/</cy_eureka.url>
				<kafka.zkNodes>10.124.70.246:2181,10.124.70.247:2181,10.124.70.248:2181</kafka.zkNodes>
				<kafka.brokers>10.124.70.246:9092,10.124.70.247:9092,10.124.70.248:9092</kafka.brokers>
				<config.label>main</config.label>
				<repository.releases.id>nexus-releases</repository.releases.id>
				<repository.releases.url>http://10.124.72.27:8081/nexus/content/repositories/releases/</repository.releases.url>
				<repository.snapshots.id>nexus-snapshots</repository.snapshots.id>
				<repository.snapshots.url>http://10.124.72.27:8081/nexus/content/repositories/snapshots/</repository.snapshots.url>
				<active>pro</active>
			</properties>

		</profile>
		<profile>
			<!-- 测试环境 -->
			<id>test</id>
			<properties>
				<cy_eureka.url>http://10.124.130.248:31001/eureka/,http://10.124.130.249:31001/eureka/</cy_eureka.url>
				<kafka.zkNodes>10.124.129.229:2181,10.124.129.230:2181,10.124.129.231:2181</kafka.zkNodes>
				<kafka.brokers>10.124.129.229:9092,10.124.129.230:9092,10.124.129.231:9092</kafka.brokers>
				<config.label>master</config.label>
				<repository.releases.id>nexus-releases</repository.releases.id>
				<repository.releases.url>http://192.168.120.172:8081/nexus/content/repositories/releases/</repository.releases.url>
				<repository.snapshots.id>nexus-snapshots</repository.snapshots.id>
				<repository.snapshots.url>http://192.168.120.172:8081/nexus/content/repositories/snapshots/</repository.snapshots.url>
				<active>test</active>
			</properties>
		</profile>

	</profiles>
	<dependencyManagement>

		<dependencies>

			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-framework</artifactId>
				<version>2.9.1</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
					<exclusion>
						<groupId>org.slf4j</groupId>
						<artifactId>slf4j-log4j12</artifactId>
					</exclusion>
					<exclusion>
						<artifactId>netty</artifactId>
						<groupId>org.jboss.netty</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-client</artifactId>
				<version>2.9.1</version>
				<exclusions>
					<exclusion>
						<artifactId>slf4j-api</artifactId>
						<groupId>org.slf4j</groupId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>org.apache.curator</groupId>
				<artifactId>curator-recipes</artifactId>
				<version>2.9.1</version>
				<exclusions>
					<exclusion>
						<groupId>org.apache.zookeeper</groupId>
						<artifactId>zookeeper</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

           <!--   <dependency>
                <groupId>com.huawei.jaguar</groupId>
                <artifactId>commons.configuration</artifactId>
                <version>${commons.version}</version>
            </dependency>-->

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>2024.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

			<dependency>
				<artifactId>netty-codec-http</artifactId>
				<groupId>io.netty</groupId>
				<version>4.1.108.Final</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>33.4.0-jre</version>
				<scope>compile</scope>
			</dependency>
			<dependency>
				<groupId>redis.clients</groupId>
				<artifactId>jedis</artifactId>
				<version>3.8.0</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>4.5.14</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<distributionManagement>
		<repository>
			<id>${repository.releases.id}</id>
			<url>${repository.releases.url}</url>
		</repository>
		<snapshotRepository>
			<id>${repository.snapshots.id}</id>
			<url>${repository.snapshots.url}</url>
		</snapshotRepository>
	</distributionManagement>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--  <dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>-->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-config</artifactId>-->
<!--		</dependency>-->

<!--		&lt;!&ndash;监控+refresh配置&ndash;&gt;-->
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter-actuator</artifactId>-->
<!--		</dependency>-->

<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-bus-amqp</artifactId>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-bus-kafka</artifactId>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.springframework.cloud</groupId>-->
<!--			<artifactId>spring-cloud-starter-stream-kafka</artifactId>-->
<!--		</dependency>-->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
			<exclusions>
                 <exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-core</artifactId>
				 </exclusion>
                 <exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-api</artifactId>
				</exclusion>
				<exclusion>
						<groupId>org.apache.logging.log4j</groupId>
						<artifactId>log4j-slf4j-impl</artifactId>
				</exclusion>
             </exclusions>
		</dependency>
		<dependency>
   		 	<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-1.2-api</artifactId>
		    <version>2.21.0</version>
   		 </dependency>
		<dependency>
   		 	<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
		    <version>2.21.0</version>
   		 </dependency>
   		 <dependency>
   		 	<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
		     <version>2.21.0</version>
   		 </dependency>
   		 <dependency>
   		 	<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
		     <version>2.21.0</version>
   		 </dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.20</version>
		</dependency>
		<dependency>
			<groupId>jakarta.validation</groupId>
			<artifactId>jakarta.validation-api</artifactId>
			<version>3.0.2</version>  <!-- 选择适合的版本 -->
		</dependency>
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>3.0.0</version> <!-- 使用适当的版本 -->
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
	</dependencies>
	
	

</project>