<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.EnterpriseMapper">
    <resultMap id="enterpriseWrapper" type="com.huawei.jaguar.dsum.dao.domain.EnterpriseWrapper">
        <result property="id" column="id" />
        <result property="enterpriseCode" column="enterpriseCode" />
        <result property="enterpriseName" column="enterpriseName" />
        <result property="enterpriseDesc" column="enterpriseDesc" />
        <result property="enterpriseType" column="enterpriseType" />
        <result property="organizationID" column="organizationID" />
        <result property="custID" column="custID" />
        <result property="businessLicenseID" column="businessLicenseID" />
        <result property="businessLicenseURL" column="businessLicenseURL" />
        <result property="idCardPositiveURL" column="IDCardPositiveURL" />
        <result property="idCardOppositeURL" column="IDCardOppositeURL" />
        <result property="contract" column="contract" />
        <result property="auditStatus" column="auditStatus" />
        <result property="auditDesc" column="auditDesc" />
        <result property="msisdn" column="msisdn" />
        <result property="createTime" column="createTime" />
        <result property="operatorID" column="operatorID" />
        <result property="lastUpdateTime" column="lastUpdateTime" />
        <result property="parentEnterpriseID" column="parentEnterpriseID" />
        <result property="parentEnterpriseName" column="parentEnterpriseName" />
        <result property="provinceID" column="provinceID" />
        <result property="status" column="status" />
        <result property="extInfo" column="extInfo" />
        <result property="reserved1" column="reserved1" />
        <result property="reserved2" column="reserved2" />
        <result property="reserved3" column="reserved3" />
        <result property="reserved4" column="reserved4" />
        <result property="reserved5" column="reserved5" />
        <result property="reserved6" column="reserved6" />
        <result property="reserved7" column="reserved7" />
        <result property="reserved8" column="reserved8" />
        <result property="reserved9" column="reserved9" />
        <result property="reserved10" column="reserved10"/>
        <result property="businessStatus" column="businessStatus"/>
        <result property="cityName" column="cityName"/>
        <result property="provinceName" column="provinceName"/>

        <result property="servTypesString" column="servTypesString"/>
        <result property="needServiceProduct" column="needServiceProduct"/>

        
    </resultMap>

    <resultMap id="industryList" type="com.huawei.jaguar.dsum.model.Industry">
        <result property="industryID" column="industryID" />
        <result property="industryName" column="industryName" />
        <result property="isSensitiveIndustry" column="isSensitiveIndustry" />
    </resultMap>

    <insert id="insertEnterprise">
		INSERT INTO dsum_t_enterprise
		(id,
		enterpriseCode,
		enterpriseName,
		enterpriseDesc,
		enterpriseType,
		organizationID,
		custID,
		businessLicenseID,
		businessLicenseURL,
		idCardPositiveURL,
		idCardOppositeURL,
		contract,
		auditStatus,
		auditDesc,
		msisdn,
		createTime,
		operatorID,
		lastUpdateTime,
		parentEnterpriseID,
		parentEnterpriseName,
		provinceID,
		cityID,
		status,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		businessStatus
        <if test="countyID != null and countyID!=''">,countyID</if>
        <if test="needServiceProduct != null ">,needServiceProduct</if>
		)
		VALUES
		(
		#{id},
		#{enterpriseCode},
		#{enterpriseName},
		#{enterpriseDesc},
		#{enterpriseType},
		#{organizationID},
		#{custID},
		#{businessLicenseID},
		#{businessLicenseURL},
		#{idCardPositiveURL},
		#{idCardOppositeURL},
		#{contract},
		#{auditStatus},
		#{auditDesc},
		#{msisdn},
		#{createTime},
		#{operatorID},
		#{lastUpdateTime},
		#{parentEnterpriseID},
		#{parentEnterpriseName},
		#{provinceID},
		#{cityID},
		#{status},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10},
		#{businessStatus}
        <if test="countyID != null and countyID!=''">,#{countyID}</if>
        <if test="needServiceProduct != null ">,#{needServiceProduct}</if>
		)
	</insert>

    <select id="getEnterpriseID" resultType="java.lang.Integer">
        select
        <if test="_parameter !=null and _parameter == 1">nextval('seq_sequence_enterpriseID_directcustomer');</if>
        <if test="_parameter !=null and _parameter == 2">nextval('seq_sequence_enterpriseID_agents');</if>
        <if test="_parameter !=null and _parameter == 3">nextval('seq_sequence_enterpriseID_agents_dep');</if>
        <if test="_parameter !=null and _parameter == 4">nextval('seq_sequence_enterpriseID_normal');</if>
        <if test="_parameter !=null and _parameter == 5">nextval('seq_sequence_enterpriseID_provinceside');</if>
        <if test="_parameter !=null and _parameter == 6">nextval('seq_sequence_enterpriseID_small');</if>
        <if test="_parameter !=null and _parameter == 7">nextval('seq_sequence_enterpriseID_businessCustomers');</if>

    </select>


    <select id="queryEnterpriseInfo" resultMap="enterpriseWrapper">
		SELECT
		t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
		t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
		t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
		t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
		t.reserved10,t.parentEnterpriseName,t.businessStatus,t.countyID,t.needServiceProduct,c.cityName,p.provinceName
		from dsum_t_enterprise t
		LEFT JOIN dsum_t_city c ON c.cityID = t.cityID
		LEFT JOIN dsum_t_province p ON p.provinceID = t.provinceID
		where t.id = #{id} and t.status in (1, 3,4)
	</select>

    <select id="queryEnterpriseInfoByCode" resultMap="enterpriseWrapper">
        SELECT
            t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
            t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
            t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
            t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
            t.reserved10,t.parentEnterpriseName,t.businessStatus,t.countyID,t.needServiceProduct,c.cityName,p.provinceName
        from dsum_t_enterprise t
                 LEFT JOIN dsum_t_city c ON c.cityID = t.cityID
                 LEFT JOIN dsum_t_province p ON p.provinceID = t.provinceID
        where t.enterpriseCode = #{enterpriseCode}
    </select>
    <select id="queryEnterpriseInfoWithParent" resultMap="enterpriseWrapper">
		SELECT
		t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
		t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
		t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
		t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
		t.reserved10,t.parentEnterpriseName,t.businessStatus,t.countyID,t.needServiceProduct from dsum_t_enterprise t where t.id = #{id} and t.status in (1, 3)
		and t.parentEnterpriseID =#{parentEnterpriseID}
	</select>

    <select id="queryEnterpriseInfoByID" resultMap="enterpriseWrapper">
		SELECT
		t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
		t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
		t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
		t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
		t.reserved10,t.parentEnterpriseName,t.countyID from dsum_t_enterprise t where t.id = #{id}
	</select>

    <select id="queryEnterpriseInfoListByID"  parameterType="java.util.List" resultMap="enterpriseWrapper">
        SELECT
            t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
            t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
            t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
            t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
            t.reserved10,t.parentEnterpriseName,t.countyID from dsum_t_enterprise t where t.id in
        <foreach collection="list" item="ids" index="no" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>

    <delete id="deleteEnterprise" parameterType="java.util.List">
        delete from dsum_t_enterprise where id in
        <foreach collection="list" item="ids" index="no" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </delete>


    <delete id="deleteEnterpriseByConditions">
		delete from dsum_t_enterprise
		where reserved3 = #{copyID} and reserved2 =1
	</delete>

    <update id="modifyEnterprise">
        update dsum_t_enterprise set
        <trim suffixOverrides="," suffix="where id = #{id}">
            <if test="enterpriseCode!=null and enterpriseCode!=''">enterpriseCode= #{enterpriseCode},</if>
            <if test="enterpriseName!=null and enterpriseName!=''">enterpriseName= #{enterpriseName},</if>
            <if test="enterpriseDesc!=null and enterpriseDesc!=''">enterpriseDesc= #{enterpriseDesc},</if>
            <if test="enterpriseType!=null">enterpriseType= #{enterpriseType},</if>
            <if test="organizationID!=null">organizationID= #{organizationID},</if>
            <if test="custID!=null">custID= #{custID},</if>
            <if test="businessLicenseID!=null and businessLicenseID!=''">businessLicenseID= #{businessLicenseID},</if>
            <if test="businessLicenseURL!=null and businessLicenseURL!=''">businessLicenseURL= #{businessLicenseURL},
            </if>
            <if test="idCardPositiveURL!=null and idCardPositiveURL!=''">idCardPositiveURL= #{idCardPositiveURL},</if>
            <if test="idCardOppositeURL!=null and idCardOppositeURL!=''">idCardOppositeURL= #{idCardOppositeURL},</if>
            <if test="auditStatus !=null and auditStatus !=''">auditStatus= #{auditStatus},</if>
            <if test="auditDesc !=null and auditDesc !=''">auditDesc= #{auditDesc},</if>
            <if test="contract!=null and contract!=''">contract= #{contract},</if>
            <if test="msisdn!=null and msisdn!=''">msisdn= #{msisdn},</if>
            <if test="operatorID!=null">operatorID= #{operatorID},</if>
            <if test="lastUpdateTime!=null">lastUpdateTime= #{lastUpdateTime},</if>
            <if test="parentEnterpriseID!=null and parentEnterpriseID!=''">parentEnterpriseID= #{parentEnterpriseID},
            </if>
            <if test="parentEnterpriseName!=null and parentEnterpriseName!=''">parentEnterpriseName=
                #{parentEnterpriseName},
            </if>
            <if test="provinceID!=null and provinceID!=''">provinceID= #{provinceID},</if>
            <if test="cityID!=null and cityID!=''">cityID= #{cityID},</if>
            <if test="countyID!=null and countyID!=''">countyID= #{countyID},</if>
            <if test="status!=null">status= #{status},</if>
            <if test="extInfo!=null">extInfo= #{extInfo},</if>
            <if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
            <if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
            <if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
            <if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
            <if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
            <if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
            <if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
            <if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
            <if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
            <if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
        </trim>
    </update>

    <select id="queryEnterpriseList" resultMap="enterpriseWrapper">
        SELECT
        t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
        t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,
        t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
        t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
        t.reserved10,t.parentEnterpriseName,businessStatus,t.countyID,t.needServiceProduct,GROUP_CONCAT(DISTINCT es2.servType) servTypesString
        from dsum_t_enterprise t
        LEFT JOIN dsum_t_enterprise_service es ON es.enterpriseID = t.id
                                                      and es.effectiveTime <![CDATA[ <= ]]> now()
                                                      and es.expiryTime <![CDATA[ >= ]]> now()
        LEFT JOIN dsum_t_enterprise_service es2 ON es2.enterpriseID = t.id
                                                        and es2.effectiveTime <![CDATA[ <= ]]> now()
                                                        and es2.expiryTime <![CDATA[ >= ]]> now()
        where (t.reserved2 != 1 or t.reserved2 is null)
        <trim prefix="and" prefixOverrides="and|or">
            <if test="enterpriseIDs != null and enterpriseIDs.size()>0">
                and t.id in
                <foreach item="id" index="index" collection="enterpriseIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>

            <if test="channelSrc !=null  and channelSrc !=''">
                and t.reserved10 = #{channelSrc}
            </if>

            <if test="parentEnterpriseName !=null  and parentEnterpriseName !=''">
                and t.parentEnterpriseName like concat("%", #{parentEnterpriseName}, "%")
            </if>

            <if test="createStartTime !=null and createEndTime !=null">
                and (t.createTime <![CDATA[ <= ]]> #{createEndTime}
                and t.createTime <![CDATA[ >= ]]> #{createStartTime})
            </if>

            <if test="createStartTime != null and createEndTime == null">
                and t.createTime <![CDATA[ >= ]]> #{createStartTime}
            </if>

            <if test="createStartTime == null and createEndTime != null">
                and t.createTime <![CDATA[ <= ]]> #{createEndTime}
            </if>

            <if test="organizationID !=null  and organizationID !=''">
                and t.organizationID like concat("%", #{organizationID}, "%")
            </if>

            <if test="enterpriseCodeLike !=null  and enterpriseCodeLike !=''">
                and t.enterpriseCode like concat("%", #{enterpriseCodeLike  }, "%")
            </if>

            <if test="cityIDs != null and cityIDs.size()>0">
                and t.cityID in
                <foreach item="cityIDs" index="index" collection="cityIDs" open="(" separator="," close=")">
                    #{cityIDs}
                </foreach>
            </if>
            <if test="countyIDs != null and countyIDs.size()>0">
                and t.countyID in
                <foreach item="countyIDs" index="index" collection="countyIDs" open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
            <if test="enterpriseCode !=null and enterpriseCode !='' ">
                and t.enterpriseCode = #{enterpriseCode}
            </if>

            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>
            
            <if test="isZyzq !=null and isZyzq == 0">
                and ((t.reserved10 != '111' and t.reserved10 != '112' and t.reserved10 != '113' ) OR t.reserved10 IS NULL )
            </if>
            
            <if test="isZyzq !=null and isZyzq == 1">
                and t.reserved10 = '111'
            </if>
            <if test="isZyzq !=null and isZyzq == 2">
                and t.reserved10 = '112'
            </if>
            <if test="isZyzq !=null and isZyzq == 3">
                and t.reserved10 = '113'
            </if>
            <if test="parentEnterpriseID !=null">
                and t.parentEnterpriseID = #{parentEnterpriseID}
            </if>

            <if test="provinceIDs !=null and provinceIDs.size>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceIDs" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
			<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>

                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or t.provinceID is null
                </if>
                    )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (t.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or t.cityID is null
                </if>

                )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (t.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>

                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or t.countyID is null
                </if>
                    )
            </if>
            <if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseIDs" index="index" collection="parentEnterpriseIDs" open="("
                    separator="," close=")">
                    #{parentEnterpriseIDs}
                </foreach>
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and t.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and t.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>
            <if test="auditStatusList != null and auditStatusList.size()>0">
                and t.auditStatus in
                <foreach item="auditStatusList" index="index" collection="auditStatusList" open="(" separator=","
                    close=")">
                    #{auditStatusList}
                </foreach>
            </if>

            <if test="auditStatus !=null ">
                and t.auditStatus = #{auditStatus}
            </if>

            <if test="effectiveTime !=null ">
                and t.createTime &lt;= #{effectiveTimeEnd}
                and (t.status = 1 or t.lastupdatetime &gt;= #{effectiveTimeStart})
            </if>

            <if test="enterpriseTypeList != null and enterpriseTypeList.size()>0">
                and t.enterpriseType in
                <foreach item="enterpriseType" index="index" collection="enterpriseTypeList" open="(" separator=","
                    close=")">
                    #{enterpriseType}
                </foreach>
            </if>
            <choose>
                <when test="statusList != null and statusList.size()>0">
                    and t.status in
                    <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    <if test="isQueryAllStatus == null or isQueryAllStatus == 0">
                        and t.status = 1
                    </if>
                </otherwise>
            </choose>
        </trim>
        <if test="servType !=null ">
            and es.servType = #{servType}
        </if>
        GROUP BY  t.id
        <if test="sortField !=null and sortField !='' and sortType !=null and sortType !=''">
            order by ${sortType} ${sortField}
        </if>
        <if test="pageNum !=null and pageSize !=null ">
            limit #{pageNum},#{pageSize}
        </if>

    </select>


    <select id="queryEnterpriseByName" resultMap="enterpriseWrapper">
		SELECT
		t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
		t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
		t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
		t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
		t.reserved10,t.parentEnterpriseName from dsum_t_enterprise t
		where t.enterpriseName = #{enterpriseName} and t.status=2
	</select>

    <select id="queryEnterpriseListCount" resultType="java.lang.Integer">
        SELECT
        count(0) from
                      (select t.id from dsum_t_enterprise t
        LEFT JOIN dsum_t_enterprise_service es ON es.enterpriseID = t.id
                                                      and es.effectiveTime <![CDATA[ <= ]]> now()
                                                      and es.expiryTime <![CDATA[ >= ]]> now()
        where (t.reserved2 != 1 or t.reserved2 is null)
        <trim prefix="and" prefixOverrides="and|or">
            <if test="enterpriseIDs != null and enterpriseIDs.size()>0">
                and t.id in
                <foreach item="id" index="index" collection="enterpriseIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>

            <if test="channelSrc !=null  and channelSrc !=''">
                and t.reserved10 = #{channelSrc}
            </if>

            <if test="parentEnterpriseName !=null  and parentEnterpriseName !=''">
                and t.parentEnterpriseName like concat("%", #{parentEnterpriseName}, "%")
            </if>

            <if test="createStartTime !=null and createEndTime !=null">
                and (t.createTime <![CDATA[ <= ]]> #{createEndTime}
                and t.createTime <![CDATA[ >= ]]> #{createStartTime})
            </if>
            <if test="effectiveTime !=null ">
                and t.createTime &lt;= #{effectiveTimeEnd}
                and (t.status = 1 or t.lastupdatetime &gt;= #{effectiveTimeStart})
            </if>
            <if test="createStartTime != null and createEndTime == null">
                and t.createTime <![CDATA[ >= ]]> #{createStartTime}
            </if>

            <if test="createStartTime == null and createEndTime != null">
                and t.createTime <![CDATA[ <= ]]> #{createEndTime}
            </if>
            <if test="enterpriseCodeLike !=null  and enterpriseCodeLike !=''">
                and t.enterpriseCode like concat("%", #{enterpriseCodeLike  }, "%")
            </if>
            <if test="organizationID !=null  and organizationID !=''">
                and t.organizationID like concat("%", #{organizationID}, "%")
            </if>

            <if test="cityIDs != null and cityIDs.size()>0">
                and t.cityID in
                <foreach item="cityIDs" index="index" collection="cityIDs" open="(" separator="," close=")">
                    #{cityIDs}
                </foreach>
            </if>
            <if test="countyIDs != null and countyIDs.size()>0">
                and t.countyID in
                <foreach item="countyIDs" index="index" collection="countyIDs" open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
            <if test="enterpriseCode !=null and enterpriseCode !='' ">
                and t.enterpriseCode = #{enterpriseCode}
            </if>

            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>
            
            <if test="isZyzq !=null and isZyzq == 0">
                and ((t.reserved10 != '111' and t.reserved10 != '112' and t.reserved10 != '113') OR t.reserved10 IS NULL )
            </if>
            
            <if test="isZyzq !=null and isZyzq == 1">
                and t.reserved10 = '111'
            </if>
            <if test="isZyzq !=null and isZyzq == 2">
                and t.reserved10 = '112'
            </if>
            <if test="isZyzq !=null and isZyzq == 3">
                and t.reserved10 = '113'
            </if>
            <if test="parentEnterpriseID !=null">
                and t.parentEnterpriseID = #{parentEnterpriseID}
            </if>

            <if test="provinceIDs !=null and provinceIDs.size>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceIDs" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
			<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or provinceID is null
                </if>

                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (t.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or cityID is null
                </if>
                    )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (t.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or countyID is null
                </if>
                    )
            </if>
            <if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseIDs" index="index" collection="parentEnterpriseIDs" open="("
                    separator="," close=")">
                    #{parentEnterpriseIDs}
                </foreach>
            </if>

            <if test="auditStatusList != null and auditStatusList.size()>0">
                and t.auditStatus in
                <foreach item="auditStatusList" index="index" collection="auditStatusList" open="(" separator=","
                    close=")">
                    #{auditStatusList}
                </foreach>
            </if>

            <if test="auditStatus !=null ">
                and t.auditStatus = #{auditStatus}
            </if>
            <if test="enterpriseTypeList != null and enterpriseTypeList.size()>0">
                and t.enterpriseType in
                <foreach item="enterpriseType" index="index" collection="enterpriseTypeList" open="(" separator=","
                    close=")">
                    #{enterpriseType}
                </foreach>
            </if>
            <choose>
                <when test="statusList != null and statusList.size()>0">
                    and t.status in
                    <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    <if test="isQueryAllStatus == null or isQueryAllStatus == 0">
                        and t.status = 1
                    </if>
                </otherwise>
            </choose>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and t.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and t.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>
        </trim>
        <if test="servType !=null ">
            and es.servType = #{servType}
        </if>
        GROUP BY  t.id ) t

<!--        <if test="sortField !=null and sortField !='' and sortType !=null and sortType !=''">-->
<!--            order by ${sortType} ${sortField}-->
<!--        </if>-->

    </select>

    <select id="queryEnterpriseNameIsExists" resultType="java.lang.Integer">
	SELECT
	count(*) from dsum_t_enterprise t where t.enterpriseName =
	#{enterpriseName} and status = 1
	</select>

    <select id="querySubEnterpriseNameIsExists" resultType="java.lang.Integer">
	SELECT
	count(*) from dsum_t_enterprise t where t.enterpriseName =
	#{enterpriseName} and status = 1 and enterpriseType = 3
	</select>

    <update id="modifyEnterpriseStatus">
		update dsum_t_enterprise set
		auditStatus=#{auditStatus},
		auditDesc=#{auditDesc},
		operatorID=#{operatorID}
		where
		id=#{id}
	</update>

    <select id="queryEnterpriseID" resultType="java.lang.Integer">
	SELECT ID from dsum_t_enterprise t where t.enterpriseCode =
	#{enterpriseCode}
	</select>

    <insert id="insertEnterpriseHistory">
		INSERT INTO dsum_t_enterprise_history
		(id,
		enterpriseCode,
		enterpriseName,
		enterpriseDesc,
		enterpriseType,
		organizationID,
		custID,
		businessLicenseID,
		businessLicenseURL,
		idCardPositiveURL,
		idCardOppositeURL,
		contract,
		auditStatus,
		auditDesc,
		msisdn,
		createTime,
		operatorID,
		lastUpdateTime,
		parentEnterpriseID,
		parentEnterpriseName,
		provinceID,
		cityID,
		status,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10)
		VALUES
		(
		#{id},
		#{enterpriseCode},
		#{enterpriseName},
		#{enterpriseDesc},
		#{enterpriseType},
		#{organizationID},
		#{custID},
		#{businessLicenseID},
		#{businessLicenseURL},
		#{idCardPositiveURL},
		#{idCardOppositeURL},
		#{contract},
		#{auditStatus},
		#{auditDesc},
		#{msisdn},
		#{createTime},
		#{operatorID},
		#{lastUpdateTime},
		#{parentEnterpriseID},
		#{parentEnterpriseName},
		#{provinceID},
		#{cityID},
		#{status},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10}
		)
	</insert>

    <select id="queryEnterpriseIDs" resultType="java.lang.Integer">
        SELECT t.id from dsum_t_enterprise t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseIDs != null and enterpriseIDs.size()>0">
                and t.id in
                <foreach item="id" index="index" collection="enterpriseIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>
            <if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseID" index="index" collection="parentEnterpriseIDs" open="(" separator="," close=")">
                    #{parentEnterpriseID}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="queryContractIsExists" resultType="java.lang.Integer">
	SELECT
	    count(*) from dsum_t_enterprise t where t.contract = #{contract}
	</select>

    <!--查询所属行业信息-->
    <select id="queryIndustryList" resultMap="industryList">
		select industryID,industryName,isSensitiveIndustry from dsum_t_industry order by isSensitiveIndustry desc,industryID asc
	</select>

    <!--查询所属行业信息-->
    <select id="queryIndustryById" resultMap="industryList">
        select industryID,industryName,isSensitiveIndustry from dsum_t_industry where industryID=#{id}
    </select>

    <update id="modifyParentEnterpriseName">
		update dsum_t_enterprise set
		parentEnterpriseName = #{parentEnterpriseName},
		lastupdatetime = #{now}
		where
		parentEnterpriseID=#{parentEnterpriseID}
	</update>


    <select id="queryEntListsByParentEntID" resultMap="enterpriseWrapper">
		SELECT
		t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
		t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
		t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
		t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
		t.reserved10,t.parentEnterpriseName from dsum_t_enterprise t where  t.status in (1, 3)
		and t.parentEnterpriseID =#{id}
	</select>

    <select id="queryEnterpriseIDsByProvinceIDs" resultType="java.lang.Integer">
        SELECT t.id from dsum_t_enterprise t
        <trim prefix="where" prefixOverrides="and|or">
        	<if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>

            <if test="id !=null  and id !=''">
                and t.id = #{id}
            </if>
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>
                )
            </if>
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>

                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or provinceID is null
                </if>
            )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (t.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or cityID is null
                </if>
                )
            </if>

            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (t.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or countyID is null
                </if>
                )
            </if>
            <if test="isEp == 1">
                and t.enterpriseType = 5
            </if>
            <if test="isEp != 1">
            	and t.enterpriseType != 5
            </if>
            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>
            <if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseID" index="index" collection="parentEnterpriseIDs" open="(" separator="," close=")">
                    #{parentEnterpriseID}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="queryEnterpriseChannelSrc" resultType="com.huawei.jaguar.dsum.dao.domain.EnterpriseWrapper" parameterType="java.util.Map">
        SELECT id,
        enterpriseCode,
        enterpriseName,
        enterpriseType,
        provinceID,
        cityID,
        status,
        reserved10 AS channelSrc
       where status in(1,3)
       <if test="enterpriseType!=null">
           and enterpriseType=#{enterpriseType}
       </if>
        <if test="enterpriseId!=null">
           and id=#{enterpriseId}
       </if>
        <if test="enterpriseCode!=null">
           and enterpriseCode=#{enterpriseCode}
       </if>
    </select>
    
    <select id="queryEnterpriseIds" resultType="java.lang.Integer">
        SELECT id from dsum_t_enterprise 
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseType!=null">
                and enterpriseType =#{enterpriseType}
            </if>
        </trim>
    </select>
    <select id="queryFaidsby" resultType="java.lang.Integer" parameterType="java.util.List">
        SELECT id from dsum_t_enterprise where parentEnterpriseID in
        <foreach collection="list" item="ids" index="no" open="(" separator="," close=")">
            #{ids}
        </foreach>
    </select>
    <select id="queryIdListsByParentEntID" resultMap="enterpriseWrapper">
		SELECT id from dsum_t_enterprise  where  parentEnterpriseID =#{id}
	</select>
	
	<select id="queryEnterpriseByCustID" resultMap="enterpriseWrapper">
        SELECT
        t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
        t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
        t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
        t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
        t.reserved10,t.parentEnterpriseName,t.businessStatus from dsum_t_enterprise t
        where (reserved2 != 1 or reserved2 is null)
        <trim prefix="and" prefixOverrides="and|or">
            <if test="custID != null">
                and t.custID = #{custID}
			</if>
        </trim>
    </select>
    
    <update id="updatebusinessStatus">
    	update dsum_t_enterprise set businessStatus = #{businessStatus}
    	<if test="controlReason != null">
    		,controlReason = #{controlReason}
    	</if>
    	WHERE id = #{id}
    	OR parentEnterpriseID = #{id}
    </update>
    
    <select  id="querychildenterprise" resultType="java.lang.Integer">
    	select id from dsum_t_enterprise
    	where parentEnterpriseID = #{id}
    </select>
    <select  id="querychildenterprises" resultType="java.lang.Integer">
    	select id from dsum_t_enterprise
    	where parentEnterpriseID = #{id}
    </select>
</mapper>