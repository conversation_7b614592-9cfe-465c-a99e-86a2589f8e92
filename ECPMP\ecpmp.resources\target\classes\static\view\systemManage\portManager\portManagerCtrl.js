var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "service.common"])
app.controller("administratorController", function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.queryPortList();
        $scope.basePortList = ["10658086", "10658233", "10658099"];

    };

    $scope.getBasePort = function (port) {
        if (port) {
            for (let i = 0; i < $scope.basePortList.length; i++) {
                if (port.indexOf($scope.basePortList[i]) >= 0) {
                    return $scope.basePortList[i];
                }
            }
        }
    };

    $scope.replaceBasePort = function (port) {
        // for (let i = 0; i < $scope.basePortList.length; i++) {
        //     port = port.replaceAll($scope.basePortList[i],"");
        // }
        return port;
    };

    $scope.export = function () {
        var req = JSON.stringify($scope.queryPortListTemp);
        req = {
            "param": {
                "req": req,
                "method": "portManager"
            },
            "url": "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downCsvFile",
            "method": "get"
        };
        CommonUtils.exportFile(req);

    };
    //查询
    $scope.queryPortList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "parentEnterpriseName": $scope.parentEnterpriseName || null,
                "enterpriseName": $scope.enterpriseName || null,
                "enterpriseType":3,
                "port": $scope.portName || null,
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1"
                },

            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryPortListTemp = angular.copy(req);
        } else {
            var req = $scope.queryPortListTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryPortList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.enterprisePortList = result.enterprisePortList;
                    $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                    $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    if ($scope.enterprisePortList) {
                        for (let i = 0; i < $scope.enterprisePortList.length; i++) {
                            let item = $scope.enterprisePortList[i];
                            if (item.hotlineSmsPort) {
                                let tempList = item.hotlineSmsPort;
                                item.rxport = $scope.replaceBasePort(tempList.join("|"));
                                item.rxPortList = [];
                                for (let i = 0; i < tempList.length; i++) {
                                    let basePort = $scope.getBasePort(tempList[i]);
                                    item.rxPortList.push(
                                        {"index": i, "port": tempList[i].replaceAll(basePort, ""), "basePort": basePort}
                                    )
                                }

                            }
                            if (item.groupSendSmsPort) {
                                let tempList = item.groupSendSmsPort;
                                item.tzsmsport = $scope.replaceBasePort(tempList.join("|"));
                                item.tzsmsportList = [];
                                for (let i = 0; i < tempList.length; i++) {
                                    let basePort = $scope.getBasePort(tempList[i]);
                                    item.tzsmsportList.push(
                                        {"index": i, "port": tempList[i].replaceAll(basePort, ""), "basePort": basePort}
                                    )
                                }

                            }
                            if (item.groupSendFlashPort) {
                                let tempList = item.groupSendFlashPort;
                                item.tzflashPort = $scope.replaceBasePort(tempList.join("|"));

                                item.tzflashPortList = [];
                                for (let i = 0; i < tempList.length; i++) {
                                    let basePort = $scope.getBasePort(tempList[i]);

                                    item.tzflashPortList.push(
                                        {"index": i, "port": tempList[i].replaceAll(basePort, ""), "basePort": basePort}
                                    )
                                }
                            }
                            if (item.groupSendMmsPort) {
                                let tempList = item.groupSendMmsPort;
                                item.tzmmsport = $scope.replaceBasePort(tempList.join("|"));

                                item.tzmmsportList = [];
                                for (let i = 0; i < tempList.length; i++) {
                                    let basePort = $scope.getBasePort(tempList[i]);
                                    item.tzmmsportList.push(
                                        {"index": i, "port": tempList[i].replaceAll(basePort, ""), "basePort": basePort}
                                    )
                                }
                            }
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.accountContentInfoData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    Array.prototype.remove = function (v) {
        if (isNaN(v) || v > this.length) {
            return false
        }
        for (let i = 0, j = 0; i < this.length; i++) {
            if (this[i] != this[v]) {
                this[j++] = this[i]
            }
        }
        this.length -= 1
    };
    //查看弹窗
    $scope.detailPort = function (item, type) {
        $scope.detailItem = item;
        $scope.detailType = type === 'rxPortList' ? 1 : 2;
        $("#operLogDetail").modal();
    };

    $scope.addMsisdn = function (type) {
        if ($scope.saveObject[type].length < 5) {
            $scope.saveObject[type].push({
                "index": $scope.saveObject[type].length,
                "port": "",
                "basePort": "10658086"
            });
        }
        $scope.checkMsisdnList(type);
    };
    $scope.invalid = {};
    $scope.checkMsisdnList = function (type) {
        $scope.invalid[type] = false;
        let list = $scope.saveObject[type];
        let list_temp = [];
        for (let i = 0; i < list.length; i++) {
            list[i].$invalid = false;
            list[i].$repeat = false;
            for (let f = 0; f < list_temp.length; f++) {
                if ((list_temp[f].basePort + list_temp[f].port) === (list[i].basePort + list[i].port)) {
                    list[i].$repeat = true;
                    $scope.invalid[type] = true;
                }
            }

            // if (type === 'rxPortList') {
            //     if (list[i].port && !(/^[0-9]{0,12}$/.test(list[i].port))) {
            //         list[i].$invalid = true;
            //         $scope.invalid[type] = true;
            //     }
            //     list_temp.push(list[i]);
            //
            // } else {
                if (list[i].port && !(/^[0-9]{1,11}$/.test(list[i].port))) {
                    list[i].$invalid = true;
                    $scope.invalid[type] = true;
                }
                if (list[i].port) {
                    list_temp.push(list[i]);
                }
            // }


        }
    };
    $scope.savePort = function () {
        $scope.checkMsisdnList("rxPortList");
        $scope.checkMsisdnList("tzsmsportList");
        $scope.checkMsisdnList("tzflashPortList");
        $scope.checkMsisdnList("tzmmsportList");

        if ($scope.detailType !== 1 &&
            ($scope.invalid["tzsmsportList"] || $scope.invalid["tzflashPortList"] || $scope.invalid["tzmmsportList"])) {
            $('#savePort').modal('hide');
            return;
        }
        let enterpriseID = $scope.saveObject.item.enterpriseID;
        if ($scope.saveObject.isSynAll) {
            enterpriseID = $scope.saveObject.item.parentEnterpriseID;
        }

        let hotlineSmsPort = null;
        let groupSendSmsPort = null;
        let groupSendFlashPort = null;
        let groupSendMmsPort = null;

        if ($scope.detailType === 1&&$scope.saveObject.rxPortList) {
            hotlineSmsPort = $scope.getPortFromListX($scope.saveObject.rxPortList);
        }
        if ($scope.detailType !== 1&&$scope.saveObject.tzsmsportList) {
            groupSendSmsPort = $scope.getPortFromList($scope.saveObject.tzsmsportList);
        }
        if ($scope.detailType !== 1&&$scope.saveObject.tzflashPortList) {
            groupSendFlashPort = $scope.getPortFromList($scope.saveObject.tzflashPortList);
        }
        if ($scope.detailType !== 1&&$scope.saveObject.tzmmsportList) {
            groupSendMmsPort = $scope.getPortFromList($scope.saveObject.tzmmsportList);
        }


        let req = {
            "enterpriseID": enterpriseID,
            "hotlineSmsPort": hotlineSmsPort ,
            "groupSendSmsPort": groupSendSmsPort ,
            "groupSendFlashPort": groupSendFlashPort,
            "groupSendMmsPort": groupSendMmsPort,
            "groupSendFluid":$scope.saveObject.groupSendFluid
        };


        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/updateSendPort",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                if (result.result.resultCode == "1030100000") {
                    $scope.tip = "COMMON_SAVESUCCESS";
                    $('#myModal').modal();
                    $('#savePort').modal('hide');
                    $scope.queryPortList();
                }else{
                    $scope.tip =result.result.resultDesc;
                    $('#myModal').modal();
                    $('#savePort').modal('hide');

                }
                })
            }
        });
    };

    $scope.getPortFromList = function (list) {
        let array = [];
        for (let i = 0; i < list.length; i++) {
            if (list[i].port && list[i].basePort) {
                array.push(list[i].basePort + list[i].port);
            }
        }
        return array.join("|");
    };
    $scope.getPortFromListX = function (list) {
        let array = [];
        for (let i = 0; i < list.length; i++) {
            if (list[i].basePort === "请选择") {
                continue;
            }
            if (list[i].port && list[i].basePort) {
                array.push(list[i].basePort + list[i].port);
            }
            // else if (list[i].basePort) {
            //     array.push(list[i].basePort);
            // }
        }
        return array.join("|");
    };

    $scope.deleteMsinsdn = function (type, index) {
        $scope.saveObject[type].remove(index);
        $scope.checkMsisdnList(type);
    };
    //新增弹窗
    $scope.addPort = function (item, type) {
        // $scope.detailItem = item;

        // $scope.invalid[type] = true;
        $scope.saveObject = {};
        $scope.saveObject.item = item;
        $scope.saveObject.rxPortList = [];
        $scope.saveObject.tzsmsportList = [];
        $scope.saveObject.tzflashPortList = [];
        $scope.saveObject.tzmmsportList = [];
        $scope.saveObject.rxPortList.push({"index": 0, "port": "", "basePort": "10658086"});
        $scope.saveObject.tzsmsportList.push({"index": 0, "port": "", "basePort": "10658086"});
        $scope.saveObject.tzflashPortList.push({"index": 0, "port": "", "basePort": "10658086"});
        $scope.saveObject.tzmmsportList.push({"index": 0, "port": "", "basePort": "10658086"});
        $scope.optionType = "新增端口号";
        $scope.detailType = type === 'rxPortList' ? 1 : 2;
        $scope.saveObject.groupSendFluid = item.groupSendFluid||20;
        $("#savePort").modal();
    };

    //新增弹窗
    $scope.updatePort = function (item, type) {
        $scope.detailType = type === 'rxPortList' ? 1 : 2;
        $scope.invalid[type] = true;
        $scope.saveObject = {};
        $scope.saveObject.item = item;
        if(item.rxPortList&&item.rxPortList.length>0){
            $scope.saveObject.rxPortList = angular.copy(item.rxPortList);
        }else{
            $scope.saveObject.rxPortList = [];
            $scope.saveObject.rxPortList.push({"index": 0, "port": "", "basePort": "10658086"});
        }

        if(item.tzflashPortList&&item.tzflashPortList.length>0){
            $scope.saveObject.tzflashPortList = angular.copy(item.tzflashPortList);
        }else{
            $scope.saveObject.tzflashPortList= [];
            $scope.saveObject.tzflashPortList.push({"index": 0, "port": "", "basePort": "10658086"});
        }
        if(item.tzsmsportList&&item.tzsmsportList.length>0){
            $scope.saveObject.tzsmsportList = angular.copy(item.tzsmsportList);
        }else{
            $scope.saveObject.tzsmsportList = [];
            $scope.saveObject.tzsmsportList.push({"index": 0, "port": "", "basePort": "10658086"});
        }
        if(item.tzmmsportList&&item.tzmmsportList.length>0){
            $scope.saveObject.tzmmsportList = angular.copy(item.tzmmsportList);
        }else{
            $scope.saveObject.tzmmsportList = [];
            $scope.saveObject.tzmmsportList.push({"index": 0, "port": "", "basePort": "10658086"});
        }
        $scope.saveObject.groupSendFluid = item.groupSendFluid||20;
        $scope.optionType = "编辑端口号";
        $scope.detailType = type === 'rxPortList' ? 1 : 2;
        $("#savePort").modal();

        $scope.checkMsisdnList("rxPortList");
        $scope.checkMsisdnList("tzsmsportList");
        $scope.checkMsisdnList("tzflashPortList");
        $scope.checkMsisdnList("tzmmsportList");

    }


});
app.filter("formatDate", function () {
    return function (date) {
        if (date) {
            return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
        }
        return "";
    }
})