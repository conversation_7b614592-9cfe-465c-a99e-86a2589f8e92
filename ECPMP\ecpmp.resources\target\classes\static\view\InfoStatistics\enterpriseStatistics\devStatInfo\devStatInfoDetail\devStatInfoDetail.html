<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/mian.css" rel="stylesheet"/>
<link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
<link href="../../../../../css/layout.css" rel="stylesheet"/>
<link href="../../../../../css/searchList.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
<link rel="stylesheet" href="../../../../../css/font-awesome.min.css"/>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<link href="../../../../../css/statistics.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="devStatInfoDetail.js"></script>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
	padding: 12px 6px;
}
.clearf:after{
    content:'';
    clear:both;
        height:0;
        display:block;
}
</style>

</head>
<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" style="min-width: 1400px">
	<div class="cooperation-manage">
		<div class="cooperation-head"><span class="frist-tab" ng-bind="'ENTERPRISESTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span
				class="second-tab" ng-bind="'DEVDETAIL'|translate"></span></div>
			<form class="form-horizontal">
				<div class="form-group">

					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="!isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div" ng-show="!isProvincial">
						<input type="text" autocomplete="off" class="form-control" id="enterpriseName" placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}" ng-model="enterpriseName">
					</div>


					<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
							<button ng-click="queryEnterpriseDevStatInfo()" type="submit" class="btn search-btn" ng-disabled="initSel.search"><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
				</div>
			</form>

			<div class="add-table">
					<button id="exportSpokesList" class="btn add-btn" ng-click="exportFile()"><icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span></button>
					&nbsp;&nbsp;
					<button id="goBack" class="btn add-btn" ng-click="goBack()"><span ng-bind="'COMMON_BACK'|translate"></span></button>
			</div>

<!--			<div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-bind="'DEVDETAIL'|translate"></div>-->
			<div style="font-weight:bold;padding:0px 20px 10px 20px">
					<span style="font-size:14px;padding:20px 20px 0px 0px" ng-bind="'DEVDETAIL'|translate">
						{{ enterpriseStatCollect.enterpriseCount }}
					</span>
					<span  style="color:red">
						<span style="padding:20px 20px 0px 0px">
							时间：{{getTime(searchParam.startDate)}} 至 {{getTime(searchParam.endDate)}}
						</span>
						<span style="padding:20px 20px 0px 0px">
							省份：{{provinceList2[itemCache.provinceID]}}
						</span>
						<span style="padding:20px 20px 0px 0px">
							地市：{{cityList2[itemCache.cityID]}}
						</span>
						<span style="padding:20px 20px 0px 0px">
							企业类型：{{getSubProvinceType(itemCache.subProvinceType)}}
						</span>
						<span style="padding:20px 20px 0px 0px">
							业务类型：{{getServiceType(itemCache.serviceType)}}
						</span>
						<span style="padding:20px 20px 0px 0px">
							新增企业：{{ itemCache.newEnterpriseCount }}
						</span>
						<span style="padding:20px 20px 0px 0px">
							新增成员：{{ itemCache.newMemberCount }}
						</span>
						<span style="padding:20px 20px 0px 0px">
							退订成员：{{ itemCache.unsubMemberCount }}
						</span>
					</span>
			</div>
			<div class="coorPeration-table">
					<table class="table table-striped table-hover">
						<thead>
							<tr>
								<th style="width:1%" ng-bind=""></th>
								<th style="width:6%" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>
								<th style="width:6%" ng-bind="'ENTERPRISEID'|translate"></th>
<!--								<th style="width:7%" ng-bind="'OTHER_PROVICE_CONTENTAUDIT_FIRSTTYPE'|translate"></th>-->
								<th style="width:6%" ng-bind="'GROUP_ADDMEMB'|translate"></th>
								<th style="width:6%" ng-bind="'UNSUB_MEMBER_COUNT'|translate"></th>								
							</tr>
						</thead>
						<tbody>
								<tr ng-repeat="item in StatInfoListData">
									<td><span class="starIcon" title="" style="margin-left: 8px;" ng-show="{{item.isNewEnterprise==1}}"></span></td>
									<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
									<td><span title="{{item.enterpriseCode}}">{{item.enterpriseCode}}</span></td>
<!--									<td><span title="{{getSubProvinceType(item.subProvinceType)}}">{{getSubProvinceType(item.subProvinceType)}}</span></td>-->
									<td><span title="{{item.newMemberCount}}">{{item.newMemberCount}}</span></td>
									<td><span title="{{item.unsubMemberCount}}">{{item.unsubMemberCount}}</span></td>
								</tr>
								<tr ng-show="StatInfoListData.length<=0">
									<td style="text-align:center" colspan="13" ng-bind="'COMMON_NODATA'|translate"></td>
								</tr>
							</tbody>
					</table>
				</div>

				<div>
					<ptl-page tableId="0" change="queryEnterpriseDevStatInfo('justPage')"></ptl-page>
				</div>
		</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>