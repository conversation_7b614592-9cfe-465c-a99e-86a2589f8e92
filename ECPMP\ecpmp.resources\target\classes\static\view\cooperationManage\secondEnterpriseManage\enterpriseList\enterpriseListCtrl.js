var app = angular.module("myApp",["util.ajax",'page',"angularI18n"])
//自定义filter,格式化日期
app.filter('newDate',function(){
    return function(date){
        var new_date =  date.substr(0,4)+"-"+date.substr(4,2)+"-"+date.substr(6.2);
        return new_date;
    }
});
app.controller('EnterpriselistCtrl', function ($scope,$rootScope,$filter,$location,RestClientUtil) {
    //初始化参数
    $scope.init = function () {
        $.cookie("enterpriseType",3,{path:'/'});
        var loginRoleType=$.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
        $scope.isZhike = (loginRoleType=='zhike');
        $scope.isAgent = (loginRoleType=='agent');
        $scope.isProvincial = (loginRoleType=='provincial');
        $scope.enterpriseType=$.cookie('enterpriseType');
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        $scope.statusMap={
            "1":"审核中",
            "2":"审核通过",
            "3":"驳回",
            "4":"变更审核中",
            "5":"变更驳回"
        };

        //搜索时初始化参数
        $scope.initSel = {
        	parentEnterpriseID:"",
        	parentEnterpriseName:"",
            enterpriseName: "",//企业名称
            enterpriseID: "",//
            startTime:"",
			endTime:"",
        };
        if($scope.isAgent){
        	$scope.parentEnterpriseID=$.cookie('enterpriseID');
        	$scope.parentEnterpriseName=$.cookie('enterpriseName');
        }
        $scope.initDatePicker();
        $scope.enterpriseList();
        $scope.enterpriseinfo();
    };
    $scope.createAccount = function (item) {
        if (item.accountInfo == null || (item.accountInfo != null &&item.accountInfo.accountName == null)){
            let createSubEnterpriseAccountReq = {
                "enterpriseId":item.id
            }
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/accountManageService/createSubEnterpriseAccount",
                data: JSON.stringify(createSubEnterpriseAccountReq),
                success: function (result) {
                    $rootScope.$apply(function () {
                        if (result.result.resultCode == '**********') {
                            console.log(result)
                            item.accountInfo.accountName = result.accountInfo.accountName;
                            $scope.accountDetail(item,"add")
                        } else {
                            $scope.tip = result.result.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    })
                }
            });

        }
    };
    $scope.accountDetail = function (item,op) {
            $scope.accountDetailOPName="查看账号";
            if(op!=null && op === "add"){
                $scope.accountDetailOPName="生成账号";
            }
            if (item.auditStatus != null){
                $scope.accountDetailEnterpriseName=item.enterpriseName;
                $scope.enterpriseAccountName=item.accountInfo.accountName;
                $('#accountDetailWindow').modal();
            }else {
                $scope.tip="**********";
                $('#myModal').modal();
            }
    }
    $scope.formatDate=function(str){
        if(!str){
            return '';
        }
        var newDateStr="";
        newDateStr=str.substring(0, 4) + "-" + str.substring(4, 6) + "-" + str.substring(6, 8) + " "
        	+ str.substring(8, 10) + ":" + str.substring(10, 12);
        return newDateStr;
    }

    $scope.gotoAdd=function(){
        location.href='../../agentManage/subEnterprise/createEnterprise/createEnterprise.html?operationType=add';
    }

    $scope.toHeiBai=function(item){
    	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../../provincialManage/blackWhiteList/blackWhiteList.html';
    }

    $scope.toHeiBaiSecond=function(item){
        $.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../../provincialManage/blackWhiteList/blackWhiteListSecondLevel.html';
    }

    $scope.toTask=function(item){
    	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../task/contentManage/ContentManagement.html';
    }

    $scope.toAccessSetting=function(item){
        	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
            $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
            $.cookie("subEnterpriseID",item.id,{path:'/'});
            $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
            location.href='../../zhikeManage/hotline/accessSettings/accessSettings.html';
        }

    $scope.toBussiness=function(item,isBussiness){
    	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        if($scope.isAgent&&!isBussiness){
        	if(item.auditStatus==1 || item.auditStatus==4){
        		location.href='../../agentManage/subEnterprise/createEnterprise/createEnterprise.html?operationType=detail';
            }else{
            	location.href='../../agentManage/subEnterprise/createEnterprise/createEnterprise.html?operationType=edit';
            }
        }else{
            // location.href='../secLevelBusinessSetting/businessSetting.html';
            location.href='../../zhikeManage/businessSetting/businessSetting.html';
        }
    }
    $scope.toBussiness2=function(item){
        $.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../../zhikeManage/businessSetting/businessSetting.html?' +
            'enterpriseID=' + item.id +
            '&enterpriseName=' + item.enterpriseName;
    }
    $scope.toQuota=function(item){
    	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../quota/quotaList/quotaList.html';
    }

    $scope.toMingpian=function(item){
    	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../../zhikeManage/groupManage/groupList/groupList.html';
    }
    // 注销二次确认弹窗
    $scope.logoutAccount = function (item){
        $scope.selectedItemLogout = item;
        $("#deleteAccount").modal();
    }
    // 注销
    $scope.delAccount = function () {
        var item = $scope.selectedItemLogout;
        var req = {
            "operatorID": item.operatorID,
            "enterpriseId": item.id
        };
        console.log(req,item.operatorID,item.id)
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/cancelEnterprise",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $('#delAccountCancel').click();
                        $scope.enterpriseList();
                    } else {
                        $('#delAccountCancel').click();
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function (err) {
                $rootScope.$apply(function () {
                    $('#delAccountCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.toHotLine=function(item){
    	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../../zhikeManage/hotline/HotlineManagement/HotlineManagement.html';
    }

    $scope.toAdver=function(item){
    	$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
        $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../../zhikeManage/advertisePrint/list/advertisePrint_list.html';
    }

    $scope.initDatePicker = function(){
        $('.input-daterange').datepicker({
                format: "yyyy-mm-dd",
                weekStart: 0,
                clearBtn: true,
                language: "zh-CN",
                autoclose: true
              });
              $('#startSm').on('changeDate', function () {
                var startTime = document.getElementById("startSm").value;

                $rootScope.$applyAsync(function () {
                	var dt= startTime.substring(0,4) + '/' +startTime.substring(5,7) + '/' +startTime.substring(8,10) + ' '+'00:00:00';
                	$scope.startTime =new Date(dt);
                });
                var dt= startTime.substring(0,4) + '/' +startTime.substring(5,7) + '/' +startTime.substring(8,10) + ' '+'00:00:00';
                $scope.startTime =new Date(dt);
                console.log("startTime:"+$scope.startTime)
              });
              if(sessionStorage.getItem("cacheStartTime") && sessionStorage.getItem("cacheStartTime") !== null && sessionStorage.getItem("cacheStartTime") != "null" && sessionStorage.getItem("cacheStartTime") != "undefined")
              {
                    $('#startSm').datepicker('setDate', new Date(JSON.parse(sessionStorage.getItem("cacheStartTime"))));
              }

              $('#endSm').on('changeDate', function () {
                var endTime = document.getElementById("endSm").value;

                $rootScope.$applyAsync(function () {
                	var dt = endTime.substring(0,4) + '/' +endTime.substring(5,7) +'/' + endTime.substring(8,10) + ' ' + '23:59:59';
                	$scope.endTime =new Date(dt);
                    console.log("endTime:"+$scope.endTime)
                })
                var dt = endTime.substring(0,4) + '/' +endTime.substring(5,7) +'/' + endTime.substring(8,10) + ' ' + '23:59:59';
                $scope.endTime =new Date(dt);
              });
              if(sessionStorage.getItem("cacheEndTime") && sessionStorage.getItem("cacheEndTime") !== null && sessionStorage.getItem("cacheEndTime") != "null" && sessionStorage.getItem("cacheEndTime") != "undefined")
              {
                    $('#endSm').datepicker('setDate', new Date(JSON.parse(sessionStorage.getItem("cacheEndTime"))));
              }
              $('#startAg').on('changeDate', function () {
                  var startTime = document.getElementById("startAg").value;

                  $rootScope.$apply(function () {
                  	var dt= startTime.substring(0,4) + '/' +startTime.substring(5,7) + '/' +startTime.substring(8,10) + ' '+'00:00:00';
                  	$scope.startTime =new Date(dt);
                  })
                });


                $('#endAg').on('changeDate', function () {
                  var endTime = document.getElementById("endAg").value;

                  $rootScope.$apply(function () {
                  	var dt = endTime.substring(0,4) + '/' +endTime.substring(5,7) +'/' + endTime.substring(8,10) + ' ' + '23:59:59';
                  	$scope.endTime =new Date(dt);
                  })
                });
    }

    $scope.enterpriseinfo = function () 
    {
    	if (null == $scope.parentEnterpriseID) 
    	{
			return;
		}
    	var req = {
    	        "id": $scope.parentEnterpriseID,
    	        "pageParameter": {
    	          "pageNum": 1,
    	          "pageSize": 100,
    	          "isReturnTotal": "1"
    	        }
    	      }
    	      /*查询企业列表*/
    	      RestClientUtil.ajaxRequest({
    	        type: 'POST',
    	        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
    	        data: JSON.stringify(req),
    	        success: function (data) {
    	          $rootScope.$apply(function () {
    	            var result = data.result;
    	            if (result.resultCode == '**********') {
    	            	console.log(data.enterprise);
    	              $scope.enterpriseName = data.enterprise.enterpriseName;
    	              $scope.businessStatus = data.enterprise.businessStatus;
    	              $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
    	              console.log($scope.businessStatus);
    	              if ($scope.businessStatus == 1) {
    	            	  $('#Modalisaengt').modal();
    	              }
    	            }
    	          })
    	        }
    	      });
    }
    //获取queryEnterpriseList接口的数据
    $scope.enterpriseList = function (condition) {
        if(condition!='justPage'){
            var req = {
                "enterpriseType":3,
                "sortType":2,
                "sortField":1,
                "parentEnterpriseIDs":$scope.parentEnterpriseID?[$scope.parentEnterpriseID]:[],
                "parentEnterpriseName":$scope.parentEnterpriseName?$scope.parentEnterpriseName:null,
                "enterpriseName":$scope.enterpriseName?$scope.enterpriseName:null,
                "enterpriseIds":$scope.enterpriseID?[$scope.enterpriseID]:[],
                "createStartTime":$scope.startTime?$scope.startTime:null,
                "createEndTime":$scope.endTime?$scope.endTime:null,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize":$scope.pageInfo[0].pageSize,
                    "isReturnTotal": "1"
                }
            };
            /*&&$scope.initSel.startTime==""
                        &&$scope.initSel.endTime==""*/
            if(!$scope.isAgent){
            if(req.parentEnterpriseIDs.length==0&&$scope.parentEnterpriseName==undefined&&
            $scope.enterpriseName==undefined&&req.enterpriseIds.length==0){
            $scope.parentEnterpriseID = "";
            $scope.parentEnterpriseName = "";
            $scope.enterpriseName = "";
            $scope.enterpriseID = "";
            $scope.startTime = "";
            $scope.endTime = "";
            } else {
                sessionStorage.setItem("cacheParentEnterpriseIDs",$scope.parentEnterpriseID);
                sessionStorage.setItem("cacheParentEnterpriseName",$scope.parentEnterpriseName);
                sessionStorage.setItem("cacheEnterpriseIds",$scope.enterpriseID);
                sessionStorage.setItem("cacheEnterpriseName",$scope.enterpriseName);
                            sessionStorage.setItem("cacheStartTime",JSON.stringify($scope.startTime));
                            sessionStorage.setItem("cacheEndTime",JSON.stringify($scope.endTime));
            }
            if(sessionStorage.getItem("cacheParentEnterpriseIDs")){
                $scope.parentEnterpriseID = sessionStorage.getItem("cacheParentEnterpriseIDs");
                req.parentEnterpriseIDs = [sessionStorage.getItem("cacheParentEnterpriseIDs")];
            } else {
                $scope.parentEnterpriseID = "";
            }
            if(sessionStorage.getItem("cacheParentEnterpriseName")){
                $scope.parentEnterpriseName = sessionStorage.getItem("cacheParentEnterpriseName");
                req.parentEnterpriseName = sessionStorage.getItem("cacheParentEnterpriseName");
            }else {
                    $scope.parentEnterpriseName = "";
                  }
            if(sessionStorage.getItem("cacheEnterpriseIds")){
                $scope.enterpriseID = sessionStorage.getItem("cacheEnterpriseIds");
                req.enterpriseIds = [sessionStorage.getItem("cacheEnterpriseIds")];
            } else {
                   $scope.enterpriseIds = [];
                   }
            if(sessionStorage.getItem("cacheEnterpriseName")){
                $scope.enterpriseName = sessionStorage.getItem("cacheEnterpriseName");
                req.enterpriseName = sessionStorage.getItem("cacheEnterpriseName");
            } else {
                    $scope.enterpriseName = "";
            }
	    }
            if(sessionStorage.getItem("cacheStartTime")){
                $scope.startTime = JSON.parse(sessionStorage.getItem("cacheStartTime"));
                req.createStartTime = JSON.parse(sessionStorage.getItem("cacheStartTime"));
            } else {
                $scope.startTime = null;
            }
            if(sessionStorage.getItem("cacheEndTime")){
                $scope.endTime = JSON.parse(sessionStorage.getItem("cacheEndTime"));
                req.createEndTime = JSON.parse(sessionStorage.getItem("cacheEndTime"));
            } else {
                $scope.endTime = null;
                }

            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.pageParameter.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.queryEnterpriseList=result.enterpriseList;
                        if($scope.queryEnterpriseList){

                        $scope.querySubscribeInfoList($scope.queryEnterpriseList);
                        }

                        //获取页面的总条数与总页面
                        $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                        if($scope.pageInfo[0].totalCount==0){
                            $scope.pageInfo[0].currentPage=1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage=1;
                        }else{
                            $scope.pageInfo[0].totalPage=Math.ceil(parseInt(result.totalNum)/parseInt($scope.pageInfo[0].pageSize));
                        }
                    }else{
                        $scope.queryEnterpriseList=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
    };

            $scope.querySubscribeInfoList = function(arr) {
            var enterpriseIDs = new Array();
            if(arr){
            jQuery.each(arr, function (i, e) {
                enterpriseIDs[i] = JSON.stringify(e.id);
              });
            }
            var req = {
            "enterpriseIDs":enterpriseIDs,
            "pageParameter": {
                                "pageNum": 1,
                                // "pageSize":$scope.pageInfo[0].pageSize,
                                "pageSize":99,
                                "isReturnTotal": "1"
                            }
            };
            RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){



                        $scope.SubscribeInfoList=result.subscriptionInfoMap;
                        jQuery.each(arr, function (i, e) {
                        var enterpriseid = e.id;
                                                jQuery.each($scope.SubscribeInfoList, function (a, b) {
                                                if(enterpriseid == b.enterpriseID){
                                                e.mmquotastatus = b.mpservtype;
                                                e.rxquotastatus = b.rxservtype;
                                                e.ggquotastatus = b.ggservtype;
                                                e.qyquotastatus = b.qyservtype;
                                                }
                                              });
                                              });
                                   console.log("queryEnterpriseList:"+JSON.stringify($scope.queryEnterpriseList));
                        } else {
                                   $scope.tip = data.result.resultCode;
                                   $('#myModal').modal();
                        }
                        })
                        },
                        error:function(){
                            $rootScope.$apply(function(data){
                                    $scope.tip="**********";
                                    $('#myModal').modal();
                                }
                            )
                        }
            })


    };

})
