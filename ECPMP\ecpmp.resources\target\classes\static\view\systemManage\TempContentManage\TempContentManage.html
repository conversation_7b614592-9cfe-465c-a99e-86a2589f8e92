<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>预设固定模板</title>
    <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
    <link href="../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../css/searchList.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
    <script type="text/javascript" src="../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/preview/preview.css"/>
    <script src="../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <script src="../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
    <script type="text/javascript" src="TempContentManage.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../css/hotlineContentManage.css"/>
    <style>
        .cooperation-manage .coorPeration-table th,td{
            padding-left: 20px !important;
        }

        .delMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../assets/images/delete.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 136px;
            z-index:99;
        }
        .addMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../assets/images/add.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 106px;
            z-index:99;
        }
        .handle ul li icon.manage-icon {
            background-position: -126px 0;
        }

        .handle ul li icon.add-icon {
            background-position: -55px 0;
        }

        .handle ul li icon.import-icon {
            background-position: -90px 0;
        }

        .table th.adjustable-width {
            width: 25%;
        }

        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        #filePicker_ div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        .form-group div li {
            display: inline-block;
            margin-right: 10px;
            padding-right: 10px;
            cursor: pointer;
        }

        .form-group div li span {
            vertical-align: middle;
            margin-right: 4px;
        }

        /* media for adjustable search-table width  */
        @media (max-width: 1850px) {
            .table th.adjustable-width {
                width: 28%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1600px) {
            .table th.adjustable-width {
                width: 30%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1300px) {
            .table th.adjustable-width {
                width: 33%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1100px) {
            .table th.adjustable-width {
                width: 42%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        .ng-dirty.ng-invalid {
            border-color: red;
        }

        .ng-dirty.invalid {
            border-color: red;
        }

        .label-supply {
            display: inline-block;
            float: left;
            padding-right: 15px;
            padding-left: 15px;
        }
        .clearf:after{
            content:'';
            clear:both;
            height:0;
            display:block;
        }
    </style>
</head>
<!--
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()" class="">
-->
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()">
<div class="cooperation-manage" style="overflow-x: scroll;">

    <div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate">
			</span>&nbsp;&gt;&nbsp;<span class="second-tab">预设固定模板</span>
    </div>

    <div class="cooperation-search">
        <!--<form class="form-inline">
            <div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
                <label style="padding-right:30px;" for="contentName"
                       ng-bind="'CONTENTAUDIT_HOTCYCONTENT'|translate"></label>
                <input ng-model='content' type="text" autocomplete="off" class="form-control" id="contentName"
                       placeholder="{{'HOTLINECONTENT_PLEASEINPUTCONTENTKEYWORDS'|translate}}">
            </div>
            <div class="form-group col-lg-1 col-xs-1  col-sm-1 col-md-1">
                <button ng-click="queryHotlineContentInfoList()" type="submit" class="btn search-btn">
                    <icon class="search-iocn"></icon>
                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                </button>
            </div>
        </form>-->
        <form class="form-horizontal">
            <div class="form-group form-inline" style="margin-left: 0px;margin-right: 0px;">
                <div class="control-label label-supply" style="padding-right: 5px;padding-left: 5px;">
                    <label for="contentName"
                           ng-bind="'TEMPLATE_CONTENT'|translate"></label>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width: 21.666667%">
                    <input ng-model='content' type="text" autocomplete="off" class="form-control" id="contentName"
                           placeholder="{{'HOTLINECONTENT_PLEASEINPUTCONTENTKEYWORDS'|translate}}">
                </div>

                <div class="control-label label-supply" style="padding-right: 5px;padding-left: 5px;">
                    <label for="contentStatus" ng-bind="'ENTERPRISE_TYPE'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:16%;">
                    <select style="max-width:200px;width: 100%;" class="form-control"
                            ng-model="initSel.supportEnterpriseType" id="enterpriseType"
                            ng-options="x.id as x.name for x in enterpriseTypeChoise"></select>
                </div>


                <div class="control-label label-supply" style="padding-right: 5px;padding-left: 5px;">
                    <label for="contentStatus" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:15%;">
                    <select style="max-width:200px;width: 100%;" class="form-control"
                            ng-model="initSel.auditStatus" id=""
                            ng-options="x.id as x.name for x in auditStatusChoise"></select>
                </div>
                <div class="control-label label-supply" style="padding-right: 5px;padding-left: 5px;">
                    <label for="" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:20%;margin-bottom: 20px;">
                    <select style="max-width:200px;width: 100%;" class="form-control"
                            ng-model="initSel.servType"
                            ng-options="x.id as x.name for x in servChoise"></select>
                </div>
                <div class="clearf"> </div>
                <div class="control-label label-supply" style="padding-right: 5px;padding-left: 5px;text-align: left;">
                    <label for="" ng-bind="'DELIVERYTYPE'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:15%;">
                    <select style="max-width:200px;width: 100%;" class="form-control"
                            ng-model="initSel.subServType"
                            ng-options="x.id as x.name for x in subServChoise"></select>
                </div>
                <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2" >
                    <button ng-click="queryHotlineContentInfoList()" type="submit" class="btn search-btn" >
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
                <div class="clearf"> </div>
            </div>
        </form>
    </div>
    <div class="add-table">
        <button type="submit" class="btn add-btn" ng-click="addHotlineContent()">
            <icon class="add-iocn"></icon>
            <span style="color:#705de1" ng-bind="'HOTLINECONTENT_ADDHOTLINECONTENT'|translate"></span>
        </button>
        <button id="exportYsmbInfoList" class="btn add-btn" ng-click="exportYsmbFile()">
            <icon class="export-icon"></icon>
            <span style="color:#705de1" ng-bind="'DETAIL_EXPORT'|translate" style="margin-left: 5px;"></span>
        </button>
    </div>

    <div style="margin-left: 20px;margin-bottom: 20px;">
        <p style="font-size: 16px;font-weight: 500;" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></p>
    </div>
    <div class="coorPeration-table">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th style="width:10%" ng-bind="'ENTERPRISE_TYPE'|translate"></th>
                <th style="width:10%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
                <th style="width:10%" ng-bind="'DELIVERYTYPE'|translate"></th>
                <th style="width:10%" ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></th>
                <th style="width:18%" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></th>
                <th style="width:13%" ng-bind="'CONTENTAUDIT_SUBMITTIME'|translate"></th>
                <th style="width:13%" ng-bind="'CONTENTAUDIT_AUDITTIME'|translate"></th>
<!--                <th style="width:10%" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>-->
                <!--新增移动审核、电信审核、联通审核、移动审核意见-->
                <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate"></th>
                <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_UNICOM'|translate"></th>
                <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_TELECOM'|translate"></th>
                <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_MOVE'|translate"></th>
                <th style="width:19%" class="adjustable-width" ng-bind="'COMMON_OPERATE'|translate"></th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="item in contentInfoListData">

                <td><span title="{{enterpriseTypeMap[item.supportEnterpriseType]}}">{{enterpriseTypeMap[item.supportEnterpriseType]}}</span></td>
                <td><span title="{{serviceTypeMap[item.servType]}}">{{serviceTypeMap[item.servType]}}</span></td>
                <td><span title="{{typeMap[item.subServType]}}">{{typeMap[item.subServType]}}</span></td>
                <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
                <td>
                    <span title="{{item.content}}" ng-if="item.signature&&item.signature!=null&&item.signature!=''">【{{item.signature}}】{{item.content}}</span>
                    <span title="{{item.content}}"
                          ng-if="item.signature==null||item.signature==''">{{item.content}}</span>
                </td>
                <td ng-bind="item.createTime|formatDate" title="{{item.createTime|formatDate}}"></td>
                <td ng-bind="item.auditTime|formatDate" title="{{item.auditTime|formatDate}}"></td>

                <td style="min-width: 100px"><span
                        title="{{item.servType == '2' ? hotlineStatusMap[item.approveStatus] : hotlineStatusMap[item.mobileApproveStatus]}}">{{item.servType == '2' ? hotlineStatusMap[item.approveStatus] : hotlineStatusMap[item.mobileApproveStatus]}}</span></td>

<!--                <td ><span title="{{approveStatusMap[item.approveStatus]}}">{{approveStatusMap[item.approveStatus]}}</span></td>-->
                <td ><span title="{{unicomApproveStatusMap[item.unicomApproveStatus]}}">{{unicomApproveStatusMap[item.unicomApproveStatus]}}</span></td>
                <td><span
                        title="{{telecomApproveStatusMap[item.telecomApproveStatus]}}">{{telecomApproveStatusMap[item.telecomApproveStatus]}}</span>
                </td>
                <td><span title="{{item.approveIdea}}">{{item.approveIdea}}</span></td>
                <td>
                    <div class="handle">
                        <ul>
                            <!-- 编辑 -->
                            <li class="delete" ng-click="editHotlineContent(item)">
                                <icon class="edit-icon"></icon>
                                <span style="color:#705de1" ng-bind="'COMMON_UPDATE'|translate"></span>
                            </li>
                            <!-- 删除 -->
                            <li ng-show = "item.approveStatus != '2'" class="delete" ng-click="deleteHotlineContent(item)">
                                <icon class="delete-icon"></icon>
                                <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
            <tr ng-show="hotContentInfoListData.length<=0">
                <td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
            </tr>
            </tbody>
        </table>
    </div>

    <div style="width: 1000px;">
        <ptl-page tableId="0" change="queryHotlineContentInfoList('justPage')"></ptl-page>
    </div>

</div>

<!-- 新增(编辑)热线内容弹窗 -->
<div class="modal fade" id="addHotlineContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     style="overflow: auto">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="operateTitle|translate"></h4>
            </div>
            <div class="cooper-tab">
                <form class="form-horizontal" name="myForm" novalidate>

                    <div class="form-group" style="margin-top: 10px;">
                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ENTERPRISE_TYPE'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <label style="width: 88px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'" class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="addSupportEnterpriseType" ng-model="addHotlineContentInfo.supportEnterpriseType3" type="radio" value="3"><span>代理商</span></label>
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'" class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="addSupportEnterpriseType" ng-model="addHotlineContentInfo.supportEnterpriseType1" type="radio" value="1"><span>直客</span></label>
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'" class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="addSupportEnterpriseType" ng-model="addHotlineContentInfo.supportEnterpriseType5" type="radio" value="5"><span>分省</span></label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input id="serviceType2" ng-disabled="operate=='edit'" class="" style="margin-right: 2px;height: 11px;width: 15px;" name="serviceType" ng-model="addHotlineContentInfo.serviceType2" type="radio" value="2"><span>热线彩印</span></label>
                        	<label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'" class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="serviceType" ng-model="addHotlineContentInfo.serviceType1" type="radio" value="1"><span>名片彩印</span></label>
                            <label style="width: 120px;margin-top: 8px;font-size: 14px;"><input ng-disabled="(!addHotlineContentInfo.supportEnterpriseType5)||operate=='edit'" class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="serviceType" ng-model="addHotlineContentInfo.serviceType5" type="radio" value="5"><span>热线彩印省份版</span></label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'DELIVERYTYPE'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <label ng-show="!addHotlineContentInfo.serviceType2" style="width: 80px;margin-top: 8px;font-size: 14px;"><input  class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.subServiceType1" type="checkbox" value="1"><span>主叫彩印</span></label>
                            <label ng-show="!addHotlineContentInfo.serviceType2" style="width: 80px;margin-top: 8px;font-size: 14px;"><input  class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.subServiceType2" type="checkbox" value="2"><span>被叫彩印</span></label>
                            <label ng-show="!addHotlineContentInfo.serviceType2" style="width: 120px;margin-top: 8px;font-size: 14px;"><input  class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.subServiceType4" type="checkbox" value="4"><span>挂机短信</span></label>
                            <label ng-show="addHotlineContentInfo.serviceType2" style="width: 120px;margin-top: 8px;font-size: 14px;"><input  class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.subServiceType3" type="checkbox" value="3"><span>主被叫彩印</span></label>
                        </div>
                    </div>

                    <!--运营商-->
                    <div class="form-group">
                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'PLATFORM'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'"  class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px; cursor:not-allowed" name="subServiceType" ng-model="addHotlineContentInfo.operator1" type="checkbox" onclick="return false;"><span>移动</span></label>
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'" class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.operator2" type="checkbox"><span>联通</span></label>
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'" class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.operator3" type="checkbox"><span>电信</span></label>
                        </div>
                    </div>
                    <!--内容类型-->
                    <div class="form-group">
                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CONTENT_TYPE'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'"  class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.ysmbType1" type="checkbox" value="1"><span>预设模板</span></label>
                            <label style="width: 80px;margin-top: 8px;font-size: 14px;"><input ng-disabled="operate=='edit'"  class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="subServiceType" ng-model="addHotlineContentInfo.ysmbType2" type="checkbox" value="1"><span>套用模板</span></label>
                        </div>
                    </div>

                    <div class="form-group" style="padding-top:5px"
                         ng-show="addHotlineContentInfo.subServType != 16 && addHotlineContentInfo.subServType != 8 && addHotlineContentInfo.subServType != 7">

                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'TEMPLATE_CONTENT'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                <textarea class="form-control" rows="6" style="margin: 0px; width: 267px; height: 98px;"
                                          id="colorContent"
                                          name="colorContent" ng-class="{'border-red':!contentVali||isSensitive}"
                                          placeholder="{{msg}}"
                                          ng-model="addHotlineContentInfo.content" ng-blur="checkHotlineContent()">
                                  </textarea>

                            <span style="color:red" ng-show="!contentVali||isSensitive">
                                    <img src="../../../assets/images/reject-icon.png" width="20" height="20"
                                         align="absmiddle">
                                    <span ng-show='contentDesc'>{{ contentDescText }}</span>
                                    <span ng-show='contentDesc'></span>

                                    <span
                                      ng-show='isSensitive'>{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
                                </span>
                        </div>
                    </div>
                    <div class="form-group" style="padding-top:5px"
                         ng-show="addHotlineContentInfo.subServType != 16 && addHotlineContentInfo.subServType != 8 && addHotlineContentInfo.subServType != 7">

                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'VAR_NAME'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                <textarea class="form-control" rows="6" style="margin: 0px; width: 267px; height: 98px;"
                                          id="colorContentVar"
                                          name="colorContent"
                                          placeholder="{{msg2}}"
                                          ng-model="addHotlineContentInfo.variableNames">
                                  </textarea>

<!--                            <span style="color:red" ng-show="!contentVali||isSensitive">-->
<!--                                    <img src="../../../assets/images/reject-icon.png" width="20" height="20"-->
<!--                                         align="absmiddle">-->
<!--                                    <span ng-show='contentDesc'>{{ contentDescText }}</span>-->
<!--                                    <span ng-show='contentDesc'></span>-->

<!--                                    <span-->
<!--                                            ng-show='isSensitive'>{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>-->
<!--                                </span>-->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <!--110迭代：(!contentVali||isSensitive) 中取消 ||isSensitive -->
                <button type="submit" ng-disabled="(!addHotlineContentInfo.content||myForm.$invalid)
                ||!(addHotlineContentInfo.supportEnterpriseType1||addHotlineContentInfo.supportEnterpriseType5||addHotlineContentInfo.supportEnterpriseType3)
                ||!(addHotlineContentInfo.serviceType2||addHotlineContentInfo.serviceType1||addHotlineContentInfo.serviceType5)
                ||(!(addHotlineContentInfo.subServiceType1||addHotlineContentInfo.subServiceType2||addHotlineContentInfo.subServiceType4))&&!addHotlineContentInfo.serviceType2
                ||(!(addHotlineContentInfo.subServiceType3))&&addHotlineContentInfo.serviceType2
                ||(!contentVali)
"
                        class="btn btn-primary search-btn" ng-bind="'COMMON_SUBMIT'|translate"
                        ng-click="beforeCommit()"></button>
                <button ng-show="false"  type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" id="addHotlineContentCancel" ng-bind="'NO'|translate"></button>
            </div>

            <div class="form-group" style="padding-top:2px">
                <span style="color: #ff0000;margin-left: 70px">说明：预设模板为免审内容;</span>
            </div>
            <div class="form-group" style="padding-top:2px">
                <span style="color: #ff0000;margin-left: 70px">套用模板为带变量需进行审核</span>
            </div>

        </div>
    </div>
</div>

<!-- 删除热线内容弹窗 -->
<div class="modal fade bs-example-modal-sm" id="deleteHotlineContent" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content" style="width:390px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838'>是否确认删除？</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                        ng-click="delHotlineContent()"></button>
                <button id="deleteHotlineContentCancel" type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" ng-bind="'NO'|translate"></button>
            </div>
        </div>
    </div>
</div>






<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>
</body>

</html>