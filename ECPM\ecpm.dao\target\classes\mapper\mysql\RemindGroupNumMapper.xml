<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.RemindGroupNumMapper">
	    <resultMap id="remindGroupNumWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.RemindGroupNumWrapper">
        <result property="ID" column="ID" javaType="java.lang.Integer" />
        <result property="remindGroupID" column="remindGroupID" javaType="java.lang.String" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />     
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
    </resultMap>

	<select id="queryMsisdnByGroupId" resultType="java.lang.String">
		select msisdn
		from ecpm_t_remind_group_num
		where remindGroupID = #{remindGroupID}
	</select>
	
	<insert id="batchInsert">
        insert into
		ecpm_t_remind_group_num
		(
		remindGroupID, 
		msisdn, 
		createTime,
		updateTime
		)
		values
		<foreach collection="list" item="wrapper" separator=",">
			(
			#{wrapper.remindGroupID},
			#{wrapper.msisdn},
			now(),
			now()
			)
		</foreach>
    </insert>
    
    <delete id="batchDeleteByGroupId">
    	delete from ecpm_t_remind_group_num
    	where 
		remindGroupID in
		<foreach item="remindGroupID" index="index" collection="list" open="(" separator="," close=")">
					#{remindGroupID}
		</foreach>
    </delete>
    
    <delete id="batchDeleteByMsisdn">
    	delete from ecpm_t_remind_group_num
    	where 
    	remindGroupID = #{remindGroupID}
    	and
		msisdn in
		<foreach item="deleteMsisdn" index="index" collection="deleteMsisdnList" open="(" separator="," close=")">
					#{deleteMsisdn}
		</foreach>
    </delete>
</mapper>