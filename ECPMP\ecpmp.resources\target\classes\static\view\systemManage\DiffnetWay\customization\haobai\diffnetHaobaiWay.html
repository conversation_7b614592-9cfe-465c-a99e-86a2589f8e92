<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/mian.css" rel="stylesheet"/>
<link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
<link href="../../../../../css/layout.css" rel="stylesheet"/>
<link href="../../../../../css/searchList.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-1.11.1.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" href="../../../../../css/font-awesome.min.css"/>
<link href="../../../../../css/daterangepicker.min.css" rel="stylesheet">
<link href="../../../../../css/addContent.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>

<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link rel="stylesheet" href="../../../../../css/font-awesome.min.css"/>
<link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<link href="../../../../../css/statistics.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="diffnetHaobaiWay.js"></script>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
	font-weight: normal;
	white-space: nowrap;
}
.daterangepicker td, .daterangepicker th {
	width:auto;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
.modal-footer{
        text-align: left;
    }
</style>

</head>
<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head" ng-show="isSuperManager"><span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab">异网通道设置</span></div>
		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/systemManage/DiffnetWay" list-index="79" ng-show="isSuperManager"></top:menu>
		<top:menu chose-index="1" page-url="/qycy/ecpmp/view/systemManage/DiffnetWay/customization"
				  list-index="80"  ng-class="{true:'second-topmenu',false:'second-topmenu'}[isAgent]"></top:menu>
			<div class="cooper-tab">
            <div>
                <form class="form-horizontal" name="myForm"
                      novalidate="">
                    <div class="form-group platform">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span>运营商：</span>
                        </label>
                        <div class="col-lg-10 col-xs-9  col-sm-10 col-md-10">
                            <li class="check-li" ng-click="changePlatform('0')"><span class="check-btn checked-btn"> </span>电信
                    		</li>
                        </div>
                    </div>
                    <div class="form-group enterpriseType">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span>企业类型：</span>
                        </label>
                        <div class="col-lg-4 col-xs-4 col-sm-4">
                            <li class="check-li" ng-click="changeEnterpriseType('0')"><span class="check-btn checked-btn"> </span>直客
                    		</li>
							<li class="check-li" ng-click="changeEnterpriseType('1')"><span class="check-btn checked-btn"> </span>代理商
                    		</li>
                    		<li class="check-li" ng-click="changeEnterpriseType('2')"><span class="check-btn checked-btn"> </span>分省
                    		</li>
                    		<li class="check-li" ng-click="changeEnterpriseType('3')"><span class="check-btn checked-btn"> </span>集客
                    		</li>
                    		<li class="check-li" ng-click="changeEnterpriseType('4')"><span class="check-btn checked-btn"> </span>小微商户
                    		</li>
                    		<li class="check-li" ng-click="changeEnterpriseType('5')"><span class="check-btn checked-btn"> </span>商户H5
                    		</li>
							<li class="check-li" ng-click="changeEnterpriseType('6')"><span class="check-btn checked-btn"> </span>移动云PAAS
							</li>
							<li class="check-li" ng-click="changeEnterpriseType('7')"><span class="check-btn checked-btn"> </span>咪咕音乐
							</li>
                        </div>
                    </div>
                    <div class="form-group servType">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span>业务类型：</span>
                        </label>
                        <div class="col-lg-4 col-xs-4 col-sm-4">
                            <li class="check-li" ng-click="changeServType('0')"><span class="check-btn checked-btn"> </span>热线彩印
                    		</li>
							<li class="check-li" ng-click="changeServType('1')"><span class="check-btn checked-btn"> </span>名片彩印
                    		</li>
                    		<li class="check-li" ng-click="changeServType('2')"><span class="check-btn checked-btn"> </span>热线彩印省份版
                    		</li>
                    		<li class="check-li" ng-click="changeServType('3')"><span class="check-btn checked-btn"> </span>企业通知
                    		</li>
                    		<li class="check-li" ng-click="changeServType('4')"><span class="check-btn checked-btn"> </span>广告彩印
                    		</li>

                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn  col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 "
                ng-bind="'COMMON_SAVE'|translate" ng-click="save()"></button>
            </div>
        </div>
		</div>
		
	<!-- 文件导出弹窗 -->
	<div class="modal fade bs-example-modal-sm" id="exportFile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
					<h4 class="modal-title ng-binding" id="exportFileTitle" ng-bind="'DETAIL_EXPORT'|translate"></h4>
				</div>

				<div class="modal-body">
					<form class="form-horizontal ng-pristine ng-invalid ng-invalid-required" name="myForm" novalidate="">
						<div class="form-group">
							<label class="col-sm-2 control-label ng-binding" ng-bind="'COMMON_REMARKS'|translate"></label>

							<div class="cond-div col-sm-6">
								<input type="text" class="form-control ng-pristine ng-untouched ng-invalid ng-invalid-required" placeholder="{{'COMMON_INPUT_REMARKS'|translate}}" name="remarks" ng-model="remarks" ng-change="checkEmpty()" required="">
							</div>
							<div class="cond-div col-sm-4">
							<span style="color:red" ng-show="isEmptyFileName" class="ng-hide">
								<span ng-bind="'COMMON_FILENAME_MUST_FILL'|translate" class="ng-binding"></span>
							</span>
								<span style="color:red" ng-show="isGreaterFileName" class="ng-hide">
								<span ng-bind="'COMMON_FILENAME_SIZE'|translate" class="ng-binding"></span>
							</span>
								<span style="color:red" ng-show="isSpecialCharacters" class="ng-hide">
								<span ng-bind="'SPECIAL_CHARACTERS'|translate" class="ng-binding"></span>
							</span>
							</div>
							<div class="cond-div col-sm-10">
								<span style="font-size: 10px;color: red;">导出后请到业务管理-导出文件下载对应文件</span>
<!--								<span style="font-size: 10px;color: red;">定制通道内容</span>-->
								<span style="font-size: 10px;color: red;" ng-bind="'EXPORT_TIPS'|translate"></span>

							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button class="btn bg_purple ng-binding" ng-click="submitExportTask()" type="submit" ng-disabled="!isFileNamePass" ng-bind="'COMMON_OK'|translate"></button>
					<button class="btn  ng-binding" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
				</div>
			</div>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>