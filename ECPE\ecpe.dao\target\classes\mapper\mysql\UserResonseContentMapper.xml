<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.UserResponseContentMapper">
	<resultMap id="userResponseContent"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.UserResponseContentWrapper">
		<result property="id" column="id" javaType="java.lang.Long"/>
		<result property="accessCode" column="accessCode" javaType="java.lang.String"/>
		<result property="userPhone" column="userPhone" javaType="java.lang.String"/>
		<result property="feedbackContent" column="feedbackContent" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="isNotice" column="isNotice" javaType="java.lang.Integer" />
		<result property="noticeResult" column="noticeResult" javaType="java.lang.Integer"/>
		<result property="noticeFailedTimes" column="noticeFailedTimes" javaType="java.lang.Integer"/>
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>


	<insert id="insertUserResponseContent">
		insert into ecpe_t_user_response_content
		(
		id,
		accessCode,
		userPhone,
		feedbackContent,
		createTime,
		updateTime,
		isNotice,
		noticeResult,
		noticeFailedTimes,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		VALUES
			(
			#{id},
			#{accessCode},
			#{userPhone},
			#{feedbackContent},
			#{createTime},
			#{updateTime},
			#{isNotice},
			#{noticeResult},
			#{noticeFailedTimes},
			#{reserved1},
			#{reserved2},
			#{reserved3},
			#{reserved4},
			#{reserved5},
			#{reserved6},
			#{reserved7},
			#{reserved8},
			#{reserved9},
			#{reserved10}
			)
	</insert>

	<select id="getID" resultType="java.lang.Long">
		select next value for MYCATSEQ_ECPE_T_USER_RESPONSE_CONTENT
	</select>

	<select id="queryUserResponseByMap" parameterType="Map" resultMap="userResponseContent">
		select
		id,
		accessCode,
		userPhone,
		feedbackContent,
		createTime,
		updateTime,
		isNotice,
		noticeResult,
		noticeFailedTimes,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from ecpe_t_user_response_content where 1 = 1
		<if test="isNotice!=null and isNotice!=''">and isNotice = #{isNotice}</if>
		<if test="noticeFailedTimes!=null and noticeFailedTimes!=''">and noticeFailedTimes <![CDATA[ < ]]>#{noticeFailedTimes}</if>
		and noticeResult = 0
	</select>

	<update id="updateUserResponseContent">
		UPDATE ecpe_t_user_response_content SET
		<trim suffixOverrides="," suffix="where id = #{id}">
			<if test="updateTime!=null">
				updateTime = #{updateTime},
			</if>
			<if test="noticeResult!=null">
				noticeResult = #{noticeResult},
			</if>
			<if test="noticeFailedTimes!=null">
				noticeFailedTimes = IFNULL(noticeFailedTimes,0) + 1,
			</if>
			<if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
			<if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
			<if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
			<if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
			<if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
			<if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
			<if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
			<if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
			<if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
			<if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
		</trim>
	</update>

	<update id="batchUpdateUserResponseContent" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
				 item="UserResponseContentWrapper" open="" separator=";">
			update ecpe_t_user_response_content
			<trim prefix="set" suffixOverrides=",">
				<if test="UserResponseContentWrapper.updateTime!=null">
					updateTime = #{UserResponseContentWrapper.updateTime},
				</if>
				<if test="UserResponseContentWrapper.noticeResult!=null">
					noticeResult = #{UserResponseContentWrapper.noticeResult},
				</if>
				<if test="UserResponseContentWrapper.noticeFailedTimes!=null">
					noticeFailedTimes = IFNULL(noticeFailedTimes,0) + 1,
				</if>
			</trim>
			where id = #{UserResponseContentWrapper.id}
		</foreach>
	</update>
</mapper>
