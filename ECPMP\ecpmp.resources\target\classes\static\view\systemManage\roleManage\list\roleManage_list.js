var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n"])
app.controller('roleListCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    $scope.enterpriseID = $location.search().enterpriseID;
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 2,
        "totalCount": 20,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    $scope.activityName = "";
    $scope.queryRoleList();
  };

  $scope.queryRoleList = function (condition) {
    var req;
    if (condition != 'justPage') {
      req = {
        "roleName": $scope.roleName,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      req = $scope.reqTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryRoleList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.roleList = result.roleList;

            //组装角色权限
            angular.forEach($scope.roleList, function (item) {
              item.authNameString = "";
              angular.forEach(item.functionAuthList, function (row1) {
                if (row1.parentAuthID !== null) {
                  item.authNameString = item.authNameString + row1.authName + "/"
                }
              });
              angular.forEach(item.dateAuthList, function (row2) {
                if (row2.fieldName === "cityID") {
                  item.authNameString = item.authNameString + row2.authName + "/"
                }
              });
              item.authNameString = item.authNameString.substr(0, item.authNameString.length - 1)
            });


            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize));
            if ($scope.pageInfo[0].totalPage == 0) {
              $scope.pageInfo[0].totalPage = 1;
            }
          } else {
            $scope.activityStatList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })

      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
        )
      }
    });

  };

  $scope.toDetail = function (role) {
    window.location.href = "../view/roleManage_view.html?roleID=" + role.roleID
  };
  $scope.toEdit = function (role) {
    window.location.href = "../edit/roleManage_edit.html?roleID=" + role.roleID
  };
  $scope.toCreate = function () {
    window.location.href = "../create/roleManage_create.html"
  };
  $scope.deleteRole = function () {
    var req = {
      roleIDList: [$scope.roleItem.roleID]
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/deleteRole",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          $('#deleteModal').modal("hide");
          if (data.result.resultCode == '1030100000') {
            if (data.roleIDList.length===0) {
              $scope.tip = "删除成功";
              $scope.queryRoleList();
              $('#myModal').modal();
            }else{
              $scope.tip = data.roleIDList[0].resultCode;
              $('#myModal').modal();
            }

          } else {
            $scope.tip = data.result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $('#deleteModal').modal("hide");
              $scope.tip = data.result.resultCode;
              $('#myModal').modal();
            }
        )
      }
    });
  };
  $scope.deleteRolePop = function (role) {
    $scope.roleItem = role;
    $scope.deleteTip = "确认删除 ?";
    $('#deleteModal').modal();
  }


}])

app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8);
    }
    return "";
  }
})