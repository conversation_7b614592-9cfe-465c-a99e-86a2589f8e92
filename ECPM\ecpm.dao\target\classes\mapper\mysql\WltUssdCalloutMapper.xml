<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.WltUssdCalloutMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltUssdCalloutWrapper" >
        <id column="contentID" property="contentID" jdbcType="BIGINT" />
        <result column="wltServiceID" property="wltServiceID" jdbcType="BIGINT" />
        <result column="reply" property="reply" jdbcType="VARCHAR" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="receiveNum" property="receiveNum" jdbcType="VARCHAR" />
        <result column="userNum" property="userNum" jdbcType="VARCHAR" />
        <result column="trackingNum" property="trackingNum" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="planTime" property="planTime" jdbcType="TIMESTAMP" />
    </resultMap>
    
    <resultMap id="WltUssdCalloutMap" type="com.huawei.jaguar.dsdp.ecpm.model.WltUssdCallout" >
        <result column="contentID" property="contentID"/>
        <result column="subServType" property="subServType"/>
        <result column="content" property="content"/>
        <result column="approveStatus" property="approveStatus"/>
        <result column="status" property="status"/>
        <result column="reply" property="reply"/>
        <result column="address" property="address"/>
        <result column="receiveNum" property="receiveNum"/>
        <result column="userNum" property="userNum"/>
        <result column="trackingNum" property="trackingNum"/>
        <result column="status" property="status"/>
        <result column="planTime" property="planTime"/>
    </resultMap>

    <sql id="Base_Column_List" >
        contentID, wltServiceID, reply, address, receiveNum, userNum,
        trackingNum, status, planTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_wlt_ussd_callout
        where contentID = #{contentID,jdbcType=BIGINT}
    </select>
    
    <select id="selectContentIDAndWltServiceID" resultMap="BaseResultMap" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltUssdCalloutWrapper" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_wlt_ussd_callout
        where wltServiceID = #{wltServiceID,jdbcType=BIGINT}
        and contentID = #{contentID,jdbcType=BIGINT}
    </select>
    
    <select id="selectContentIDsAndWltServiceID" resultMap="BaseResultMap" parameterType="com.huawei.jaguar.dsdp.ecpm.request.QueryWltServiceReq" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_wlt_ussd_callout
        where wltServiceID = #{wltServiceID,jdbcType=BIGINT}
        and contentID in
	        <foreach item="contentID" index="index" collection="contentIDs" open="(" separator="," close=")">
	            #{contentID}
	        </foreach>
    </select>
    
    <select id="queryWltServiceByCondition" resultMap="WltUssdCalloutMap" >
        SELECT 
		t.contentID AS contentID,c.subServType AS subServType,
		c.content AS content,c.approveStatus AS approveStatus,
		c.status AS status,t.reply AS reply,
		t.address AS address,
		t.receiveNum AS receiveNum,t.userNum AS userNum,
		t.trackingNum AS trackingNum,
		DATE_FORMAT(t.planTime,'%Y%m%d%H%i%s') AS planTime
		FROM ecpm_t_wlt_ussd_callout t,ecpm_t_content c
		WHERE c.ID=t.contentID 
		<if test="contentID != null and contentID != ''">
			AND t.contentID = #{contentID}
		</if>
		<if test="content != null and content != ''">
			AND c.content LIKE CONCAT("%", #{content}, "%")
		</if>
		<if test="enterpriseID != null and enterpriseID != ''">
			AND c.enterpriseID = #{enterpriseID}
		</if>
		<if test="planTimeEnd != null and planTimeEnd != ''">
		   AND t.planTime <![CDATA[ <= ]]> #{planTimeEnd}
		</if>
		<if test="planTimeStart != null and planTimeStart != ''">
			AND t.planTime <![CDATA[ >= ]]> #{planTimeStart}
		</if>
		ORDER BY t.contentID DESC
		<if test="pageNum != null and pageSize != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>
    
    <select id="queryWltServiceByConditionCount" resultType="java.lang.Integer" >
        select count(1) from (SELECT 
		t.contentID AS contentID,c.subServType AS subServType,
		c.content AS content, t.reply AS reply,
		t.address AS address,
		t.receiveNum AS receiveNum,t.userNum AS userNum,
		t.trackingNum AS trackingNum,
		DATE_FORMAT(t.planTime,'%Y%m%d%k%i%s') AS planTime
		FROM ecpm_t_wlt_ussd_callout t,ecpm_t_content c
		WHERE c.ID=t.contentID 
		<if test="contentID != null and contentID != ''">
			AND t.contentID = #{contentID}
		</if>
		<if test="content != null and content != ''">
			AND c.content LIKE CONCAT("%", #{content}, "%")
		</if>
		<if test="enterpriseID != null and enterpriseID != ''">
			AND c.enterpriseID = #{enterpriseID}
		</if>
		<if test="planTimeEnd != null and planTimeEnd != ''">
		   AND t.planTime <![CDATA[ <= ]]> #{planTimeEnd}
		</if>
		<if test="planTimeStart != null and planTimeStart != ''">
			AND t.planTime <![CDATA[ >= ]]> #{planTimeStart}
		</if>
		)a
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
        delete from ecpm_t_wlt_ussd_callout
        where contentID = #{contentID,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltUssdCalloutWrapper" >
        insert into ecpm_t_wlt_ussd_callout (contentID, wltServiceID, reply, 
            address, receiveNum, userNum, trackingNum, status, planTime)
        values (#{contentID,jdbcType=BIGINT},#{wltServiceID,jdbcType=BIGINT},#{reply,jdbcType=VARCHAR}, 
            #{address,jdbcType=VARCHAR}, #{receiveNum,jdbcType=VARCHAR}, #{userNum,jdbcType=VARCHAR},
            #{trackingNum,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{planTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltUssdCalloutWrapper" >
        insert into ecpm_t_wlt_ussd_callout
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="contentID != null" >
                contentID,
            </if>
            <if test="wltServiceID != null" >
                wltServiceID,
            </if>
            <if test="reply != null" >
                reply,
            </if>
            <if test="address != null" >
                address,
            </if>
            <if test="receiveNum != null" >
                receiveNum,
            </if>
            <if test="userNum != null" >
                userNum,
            </if>
            <if test="trackingNum != null" >
                trackingNum,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="planTime != null" >
                planTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="contentID != null" >
                #{contentID,jdbcType=BIGINT},
            </if>
            <if test="wltServiceID != null" >
                #{wltServiceID,jdbcType=BIGINT},
            </if>
            <if test="reply != null" >
                #{reply,jdbcType=VARCHAR},
            </if>
            <if test="address != null" >
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="receiveNum != null" >
                #{receiveNum,jdbcType=VARCHAR},
            </if>
            <if test="userNum != null" >
                #{userNum,jdbcType=VARCHAR},
            </if>
            <if test="trackingNum != null" >
                #{trackingNum,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=INTEGER},
            </if>
            <if test="planTime != null" >
                #{planTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltUssdCalloutWrapper" >
        update ecpm_t_wlt_ussd_callout
        <set >
            reply = #{reply,jdbcType=VARCHAR},
            <if test="address != null" >
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="receiveNum != null" >
                receiveNum = #{receiveNum,jdbcType=VARCHAR},
            </if>
            <if test="userNum != null" >
                userNum = #{userNum,jdbcType=VARCHAR},
            </if>
            <if test="trackingNum != null" >
                trackingNum = #{trackingNum,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="planTime != null" >
                planTime = #{planTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where contentID = #{contentID,jdbcType=BIGINT}
    </update>
    
    <update id="updateByPrimaryKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltUssdCalloutWrapper" >
        update ecpm_t_wlt_ussd_callout
        set wltServiceID = #{enterpriseID,jdbcType=BIGINT},
            reply = #{reply,jdbcType=VARCHAR},
            address = #{address,jdbcType=VARCHAR},
            receiveNum = #{receiveNum,jdbcType=VARCHAR},
            userNum = #{userNum,jdbcType=VARCHAR},
            trackingNum = #{trackingNum,jdbcType=VARCHAR},
            status = #{status,jdbcType=INTEGER},
            planTime = #{planTime,jdbcType=TIMESTAMP}
        where contentID = #{ID,jdbcType=BIGINT}
    </update>
    
    <select id="queryWaitDealDelivery" resultMap="WltUssdCalloutMap" >
        SELECT 
        t.ID AS contentID,t.approveStatus AS approveStatus,t.status AS status,
		c.receiveNum AS receiveNum,c.userNum AS userNum,
		c.reply AS reply,
		DATE_FORMAT(c.planTime,'%Y%m%d%k%i%s') AS planTime
		FROM 
		ecpm_t_wlt_ussd_callout c 
		LEFT JOIN 
		ecpm_t_content t 
		ON t.ID=c.contentID
		WHERE c.planTime <![CDATA[ < ]]> NOW() AND c.status=0
    </select>
</mapper>