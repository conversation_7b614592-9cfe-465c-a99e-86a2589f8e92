<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title>直客管理－基本信息</title>
		<link href="../../../../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
		<link href="../../../../css/datepicker3.css" rel="stylesheet" type="text/css" />
		<link href="../../../../css/reset.css" rel="stylesheet" />
		<link href="../../../../css/searchList.css" rel="stylesheet" />
		<link href="../../../../css/merchantDetail.css" rel="stylesheet" />
		<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
		<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
		<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
		<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
		<script type="text/javascript"
            src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
		<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
		<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
		<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
		
		<!-- 引入分页组件 -->
		<script type="text/javascript" src="../../../../directives/page/page.js"></script>
		<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css" />
		<link rel="stylesheet" type="text/css" href="../../../../css/webuploader.css">
		<link rel="stylesheet" type="text/css" href="../../../../css/font-awesome.min.css">
		<!--引入JS-->
		<script type="text/javascript" src="../../../../frameworkJs/webuploader.js"></script>
		<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
		<script src="../../../../directives/cy-uploadify/cy-uploadify.js"></script>
		<link href="../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
		<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
		<script type="text/javascript" src="merchantCtrl.js"></script>
	</head>

	<body ng-app="myApp" ng-controller='merchantController' class="body-min-width">
		<div class="enterPrise">
			<div class="cooperation-head">
				<span class="frist-tab" ng-bind="'COMMON_MERCHANT'|translate"></span>&nbsp;&gt;&nbsp;
				<span class="second-tab" ng-bind="'COMMON_BASEINFO'|translate"></span>
			</div>
			<form  class="form-horizontal"name="myForm" novalidate ng-init="init()">
			<div class="cooper-messsage">
				<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/merchantInfoManage/merchant" 
					list-index="13" ng-if="operate =='detail'"></top:menu>
				<div class="cooper-tab">

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'MERCHANT_MERCHANTNAME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-if="operate !='detail'">
								<input class="form-control"
									type="text" id="enterpriseName" name="enterpriseName" ng-model="merchant.enterpriseName"
									placeholder="{{'MERCHANT_PLEASEINPUTMERCHANTNAME'|translate}}"
									ng-blur="checkMerchantName(merchant.enterpriseName,'')" ng-disabled="operate =='detail'"
									ng-if="!merchant.merchantNameDesc">
								<input class="form-control"
									type="text" name="merchantName" ng-model="merchant.enterpriseName"
									placeholder="{{'MERCHANT_PLEASEINPUTMERCHANTNAME'|translate}}"
									style="border-color:red;"
									ng-blur="checkMerchantName(merchant.enterpriseName,'')" ng-disabled="operate =='detail'"
									ng-if="merchant.merchantNameDesc">
								<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle" 
									ng-show="merchant.merchantNameDesc">
								<span style="color:red" ng-show="merchant.merchantNameDesc">{{merchant.merchantNameDesc|translate}}</span>
							</div>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-if="operate =='detail'">
								  <span>{{merchant.enterpriseName}}</span>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span ng-bind="'MERCHANT_CONTRACT'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<span>{{merchant.contract}}</span>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<icon>*</icon>
							<span ng-bind="'MERCHANT_CONTRACTNUM'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-if="operate !='detail'">
								<input type="text" class="form-control" name="msisdn" ng-model="merchant.msisdn" id="msisdn"
									placeholder="{{'MERCHANT_PLEASEINPUTCONTRACTNUM'|translate}}" 
									ng-show="msisdnValidate"
									ng-blur="checkMsisdn(merchant.msisdn)" ng-disabled="operate =='detail'">
								<input type="text" class="form-control" name="msisdn" ng-model="merchant.msisdn"
									placeholder="{{'MERCHANT_PLEASEINPUTCONTRACTNUM'|translate}}" 
									ng-show="!msisdnValidate" style="border-color:red;"
									ng-blur="checkMsisdn(merchant.msisdn)" ng-disabled="operate =='detail'">
							    <span style="color:red" ng-show="!msisdnValidate">
								  <img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'MERCHANT_CONTRACTNUMDESC'|translate"></span>
								</span>
							</div>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-if="operate =='detail'">
								  <span>{{merchant.msisdn}}</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<icon>*</icon>
								<span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-show="operate !='detail'">
								<cy:uploadify filelistid="fileList1" filepickerid="filePicker1" accepttype="accepttype"
										uploadifyid="uploadifyid1" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
										formdata="licenseUploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
										urllist="businessLicenseUrlList" createthumbnail="isCreateThumbnail" namelistid="nameList">
								</cy:uploadify>
							</div>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-3" ng-if="operate =='detail'">
								<img ng-src="{{businessLicenseUrl.review}}" width="100" height="100" align="absmiddle">
								<button ng-show="merchant.businessLicenseURL" type="submit" 
									class="btn download-btn" ng-click="exportFile(merchant.businessLicenseURL)">
	            					<span ng-bind="'COMMON_DOWNLOAD'|translate"></span>
	            				</button>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<icon>*</icon>
								<span ng-bind="'MERCHANT_CARDPOSITIVE'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-if="operate !='detail'">
								<cy:uploadify filelistid="fileList2" filepickerid="filePicker2" accepttype="accepttype"
										uploadifyid="uploadifyid2" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
										formdata="cardPositiveUploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
										urllist="cardPositiveUrlList" createthumbnail="isCreateThumbnail" namelistid="nameList">
								</cy:uploadify>
							</div>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-3" ng-if="operate =='detail'">
								<img ng-src="{{idCardPositiveUrl.review}}" width="100" height="100" align="absmiddle">
								
								<button ng-show="merchant.idCardPositiveURL" type="submit" 
									class="btn download-btn" ng-click="exportFile(merchant.idCardPositiveURL)">
	            					<span ng-bind="'COMMON_DOWNLOAD'|translate"></span>
	            				</button>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<icon>*</icon>
								<span ng-bind="'MERCHANT_CARDOPPOSITE'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-if="operate !='detail'">
								<cy:uploadify filelistid="fileList3" filepickerid="filePicker3" accepttype="accepttype"
										uploadifyid="uploadifyid3" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
										formdata="cardOppositeUploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
										urllist="cardOppositeUrlList" createthumbnail="isCreateThumbnail" namelistid="nameList">
								</cy:uploadify>
							</div>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-3" ng-if="operate =='detail'">
								<img ng-src="{{idCardOppositeUrl.review}}" width="100" height="100" align="absmiddle">
								<button ng-show="merchant.idCardOppositeURL" type="submit" 
									class="btn download-btn" ng-click="exportFile(merchant.idCardOppositeURL)">
	            					<span ng-bind="'COMMON_DOWNLOAD'|translate"></span>
	            				</button>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="enterprise-btn">
				<button type="submit" class="btn btn-primary search-btn"
				ng-disabled="!merchantNameValidate || !merchant.enterpriseName || 
				!msisdnValidate || !merchant.msisdn || 
				!businessLicenseUrl || !idCardPositiveUrl || 
				!idCardOppositeUrl || merchantNameExist"
				ng-click="beforeSave()" ng-show="operate !='detail'" ng-bind="'COMMON_SUBMIT'|translate"></button>
				<button type="submit" class="btn btn-back" ng-click="cancelToMerchantList()" ng-bind="'COMMON_BACK'|translate"></button>
			</div>
			</form>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p style='font-size: 16px;color:#383838'>
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_OK'|translate"></button>
						</div>
					</div>
				</div>
			</div>
			
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="cancelToList" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p style='font-size: 16px;color:#383838'>
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-click="editToMerchantList()" ng-bind="'COMMON_OK'|translate"></button>
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_CANCLE'|translate"></button>
						</div>
					</div>
				</div>
			</div>

	</body>
</html>