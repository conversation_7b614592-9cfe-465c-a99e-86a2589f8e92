;(function (root, factory) {
	if (typeof exports === "object") {
		// CommonJS
		module.exports = exports = factory(require("./core"));
	}
	else if (typeof define === "function" && define.amd) {
		// AMD
		define(["./core"], factory);
	}
	else {
		// Global (browser)
		factory(root.CryptoJS);
	}
}(this, function (CryptoJS) {
	CryptoJS.ecpmpEncrypt = function(cryptKey,word,cryptiv){
		var key = CryptoJS.enc.Utf8.parse(cryptKey);
		var srcs = CryptoJS.enc.Utf8.parse(word);
		var iv = CryptoJS.enc.Utf8.parse(cryptiv);
		var options = {
				iv:iv,
				mode:CryptoJS.mode.CBC,
				padding:CryptoJS.pad.Pkcs7
			};
		var encrypted = CryptoJS.AES.encrypt(srcs,key,options);
		return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
	}
	CryptoJS.ecpmpDecrypt = function(cryptKey,word,cryptiv){
		var key = CryptoJS.enc.Utf8.parse(cryptKey);
		var iv = CryptoJS.enc.Utf8.parse(cryptiv);
		//var vals = word.replace(/\-/g, '+').replace(/_/g, '/');
		var options = {
				iv:iv,
				mode:CryptoJS.mode.CBC,
				padding:CryptoJS.pad.Pkcs7
			};
		var decryptedData = CryptoJS.AES.decrypt(word, key, options);
		var decryptedStr = CryptoJS.enc.Utf8.stringify(decryptedData);
		return decryptedStr
//		var key = CryptoJS.enc.Utf8.parse(cryptKey);
//		var iv = CryptoJS.enc.Utf8.parse(cryptiv);
//		var base64 = CryptoJS.enc.Base64.parse(word);
//		var srcs = CryptoJS.enc.Base64.stringify(word);
//		var options = {
//				iv:iv,
//				mode:CryptoJS.mode.CBC,
//				padding:CryptoJS.pad.Pkcs7
//			};
//		var decrypted = CryptoJS.AES.decrypt(srcs,key,options);
//		return decrypted.toString();
	}
	return CryptoJS;

}));