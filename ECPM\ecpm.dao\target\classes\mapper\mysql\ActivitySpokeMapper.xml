<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivitySpokeMapper">
	<resultMap id="activitySpokeWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivitySpokeWrapper">
		<result property="activityID" column="activityID" javaType="java.lang.Integer" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="cityCode" column="cityCode" javaType="java.lang.String" />
		<result property="areaCodeUpdateTime" column="areaCodeUpdateTime"
			javaType="java.util.Date" />
		<result property="screenCount" column="screenCount" javaType="java.lang.Integer" />
		<result property="endPhoneCount" column="endPhoneCount"
			javaType="java.lang.Integer" />
		<result property="isCannel" column="isCannel" javaType="java.lang.Integer" />
		<result property="isUnsubscribed" column="isUnsubscribed"
			javaType="java.lang.Integer" />
		<result property="isReward" column="isReward" javaType="java.lang.Integer" />
		<result property="isSendNotify" column="isSendNotify" javaType="java.lang.Integer" />
		<result property="startTime" column="createTime" javaType="java.util.Date" />
		<result property="endTime" column="endTime" javaType="java.util.Date" />
		<result property="dayCount" column="dayCount" javaType="java.lang.Integer" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="spokeStatus" column="spokeStatus" javaType="java.lang.Integer" />
	</resultMap>

	<!-- 查询正在进行的代言 -->
	<select id="queryActivitySpoke" resultMap="activitySpokeWrapper">
		select
		activityID,
		msisdn,
		cityCode,
		areaCodeUpdateTime,
		screenCount,
		endPhoneCount,
		isCannel,
		isUnsubscribed,
		isReward,
		isSendNotify,
		spokeStatus,
		createTime,
		endTime,
		dayCount,
		updateTime,
		operatorID
		from
		ecpm_t_activity_spoke
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				and activityID=#{activityID}
			</if>
			<if test="msisdn != null and msisdn != ''">
				and msisdn=#{msisdn}
			</if>
			<if test="isReward != null">
				and isReward=#{isReward}
			</if>
			<if test="isCannel != null">
				and isCannel=#{isCannel}
			</if>
			<if test="isUnsubscribed != null">
				and isUnsubscribed=#{isUnsubscribed}
			</if>
			<if test="endTime != null">
				and endTime>#{endTime}
			</if>
			<if test="isSendNotify != null">
			    and isSendNotify=#{isSendNotify}
			</if>
			<if test="spokeStatus != null">
			    and spokeStatus=#{spokeStatus}
			</if>
		</trim>	
		<if test="pageNum!=null">
			order by activityID desc,msisdn
			limit #{pageNum},#{pageSize}
		</if>
	</select>


	<update id="updateActivitySpoke">
		update ecpm_t_activity_spoke set
		<trim suffixOverrides=","
			suffix="where msisdn = #{msisdn} and activityID = #{activityID} and isCannel=0 and isUnsubscribed =0 and  endTime>#{endTime}">
			<if test="endTime!=null">
				endTime= #{endTime},
			</if>
			<if test="dayCount!=null">
				dayCount= #{dayCount},
			</if>
			<if test="isUnsubscribed != null">
				isUnsubscribed = #{isUnsubscribed},
			</if>
			<if test="isCannel!=null">
				isCannel= #{isCannel},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime},
			</if>
			<if test="cityCode!=null">
				cityCode= #{cityCode},
			</if>
			<if test="screenCount!=null">
				screenCount= #{screenCount},
			</if>
			<if test="endPhoneCount!=null">
				endPhoneCount= #{endPhoneCount},
			</if>
			<if test="spokeStatus!=null">
				spokeStatus= #{spokeStatus},
			</if>
		</trim>
	</update>

	<insert id="insertActivitySpoke">
		INSERT INTO ecpm_t_activity_spoke
		(
		activityID,
		msisdn,
		cityCode,
		areaCodeUpdateTime,
		screenCount,
		endPhoneCount,
		isCannel,
		isUnsubscribed,
		isReward,
		isSendNotify,
		spokeStatus,
		createTime,
		endTime,
		dayCount,
		updateTime,
		operatorID
		)
		VALUES
		(
		#{activityID},
		#{msisdn},
		#{cityCode},
		#{areaCodeUpdateTime},
		#{screenCount},
		#{endPhoneCount},
		#{isCannel},
		#{isUnsubscribed},
		#{isReward},
		#{isSendNotify},
		#{spokeStatus},
		#{startTime},
		#{endTime},
		#{dayCount},
		#{updateTime},
		#{operatorID}
		)
	</insert>

	<select id="countActivitySpoke" resultType="java.lang.Integer">
		select
		count(*)
		from ecpm_t_activity_spoke
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				and activityID=#{activityID}
			</if>
			<if test="msisdn != null and msisdn != ''">
				and msisdn=#{msisdn}
			</if>
			<if test="isReward != null">
				and isReward=#{isReward}
			</if>
			<if test="isCannel != null">
				and isCannel=#{isCannel}
			</if>
			<if test="isUnsubscribed != null">
				and isUnsubscribed=#{isUnsubscribed}
			</if>
			<if test="endTime != null">
				and endTime>#{endTime}
			</if>
		</trim>
	</select>

	<!--查询活动代言人列表 -->
	<select id="querySpokesmanList" resultMap="activitySpokeWrapper">
		select
		t.activityID,
		t.msisdn,
		t1.cityCode,
		t1.cityName,
		t1.provinceCode,
		t1.provinceName,
		t.updateTime,
		t.screenCount,
		t.endPhoneCount,
		t.isCannel,
		t.isUnsubscribed,
		t.isReward,
		t.isSendNotify,
		t.createTime,
		t.endTime,
		t.dayCount,
		t.updateTime,
		t.operatorID,
		t.spokeStatus
		from
		ecpm_t_activity_spoke t,ecpm_t_city t1
		<trim prefix="where" prefixOverrides="and|or">
			t.cityCode = t1.cityCode
			and t.spokeStatus = 5
			<if test="activityID != null">
				and t.activityID=#{activityID}
			</if>
			<if test="msisdn != null and msisdn != ''">
				and t.msisdn like #{msisdn}
			</if>
			<if test="isReward != null">
				and t.isReward=#{isReward}
			</if>
			<choose>
				<!--代言中1 endTime结束时间未到 && isCannel为未取消 && isUnsubscribed为未退订 -->
				<when test="status!=null and status eq 1">
					and t.isCannel = 0 and t.isUnsubscribed = 0 and t.endTime <![CDATA[ > ]]>
					#{nowTime}
				</when>
				<!--取消代言2->是否取消为isCannel为1.取消 -->
				<when test="status!=null and status eq 2">
					and t.isCannel = 1
				</when>
				<!--结束代言3->退订为1 || (结束时间已到 && 未取消) -->
				<when test="status!=null and status eq 3">
					and (t.isUnsubscribed = 1 or t.endTime <![CDATA[ <= ]]>
					#{nowTime} and t.isCannel = 0 )
				</when>
			</choose>
		</trim>
		order by t.isReward desc,t.screenCount desc,t.dayCount
		desc,t.createTime desc
		limit
		#{pageNo},#{pageSize}
	</select>

	<!--查询活动代言人数量 -->
	<select id="countSpokesman" resultType="java.lang.Integer">
		select
		count(0)
		from ecpm_t_activity_spoke t,ecpm_t_city t1
		<trim prefix="where" prefixOverrides="and|or">
			t.cityCode = t1.cityCode
			and t.spokeStatus = 5
			<if test="activityID != null">
				and activityID=#{activityID}
			</if>
			<if test="msisdn != null and msisdn != ''">
				and msisdn like #{msisdn}
			</if>
			<if test="isReward != null">
				and isReward=#{isReward}
			</if>
			<choose>
				<!--代言中1 endTime结束时间未到 && isCannel为未取消 && isUnsubscribed为未退订 -->
				<when test="status!=null and status eq 1">
					and isCannel = 0 and isUnsubscribed = 0 and endTime <![CDATA[ > ]]>
					#{nowTime}
				</when>
				<!--取消代言2->是否取消为isCannel为1.取消 -->
				<when test="status!=null and status eq 2">
					and isCannel = 1
				</when>
				<!--结束代言3->退订为1 || (结束时间已到 && 未取消) -->
				<when test="status!=null and status eq 3">
					and (isUnsubscribed = 1 or endTime <![CDATA[ <= ]]>
					#{nowTime} and isCannel = 0 )
				</when>
			</choose>
		</trim>
	</select>

	<!-- 联合查询地域和代言记录表满足条件的代言 -->
	<select id="queryActivityIDWithIsNowSpokeArea" resultType="java.lang.Integer">
		select a.activityID
		from
		(select
		activityID
		from
		ecpm_t_activity_spoke
		<trim prefix="where" prefixOverrides="and|or">
			<if test="msisdn != null and msisdn != ''">
				and msisdn=#{msisdn}
			</if>
			<choose>
				<when test="isNowSpoke !=null and isNowSpoke == 1">
					and isCannel=0
					and isUnsubscribed=0
					and endTime <![CDATA[ >= ]]>
					#{now}
				</when>
				<when test="isNowSpoke !=null and isNowSpoke == 0">
					and (isCannel=1
					or isUnsubscribed=1
					or endTime <![CDATA[ < ]]>
					#{now})
				</when>
			</choose>
		</trim>
		)a
		<if test="area != null and area != ''">
			inner JOIN
			(select
			activityID
			from ecpm_t_activity_area
			where
			cityID =#{area} or provinceID=#{area})b
			on a.activityID = b.activityID
		</if>
	</select>


	<update id="batchUpdateActivitySpoke" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="activitySpokeWrapper" open="" separator=";">
			update
			ecpm_t_activity_spoke
			set
			isSendNotify = 1,
			updateTime=#{activitySpokeWrapper.updateTime}
			where
			activityID=#{activitySpokeWrapper.activityID} and
			msisdn=#{activitySpokeWrapper.msisdn}
		</foreach>
	</update>
	
	<update id="batchUpdateActivitySpokeReword" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="activitySpokeWrapper" open="" separator=";">
			update
			ecpm_t_activity_spoke
			set
			isReward = 1,
			updateTime=#{activitySpokeWrapper.updateTime}
			where
			activityID=#{activitySpokeWrapper.activityID} and
			msisdn=#{activitySpokeWrapper.msisdn}
		</foreach>
	</update>
	
		<update id="updateActivitySpokeToIsUnsubscribed">
		update ecpm_t_activity_spoke set
		<trim suffixOverrides=","
			suffix="where msisdn = #{msisdn} and activityID = #{activityID} and isCannel=0 and isUnsubscribed =0  and endTime>#{endTime}">
			<if test="endTime!=null">
				endTime= #{endTime},
			</if>
			<if test="dayCount!=null">
				dayCount= #{dayCount},
			</if>
			<if test="isUnsubscribed != null">
				isUnsubscribed = #{isUnsubscribed},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime},
			</if>
			<if test="cityCode!=null">
				cityCode= #{cityCode},
			</if>
			<if test="screenCount!=null">
				screenCount= #{screenCount},
			</if>
			<if test="endPhoneCount!=null">
				endPhoneCount= #{endPhoneCount},
			</if>
		</trim>
	</update>	
			
	<!-- 查询正在进行的代言活动的代言人列表总数-->
	<select id="countDealActivitySpokeing" resultType="java.lang.Integer">
		select
		count(1)
		from ecpm_t_activity_spoke
		<trim prefix="where" prefixOverrides="and|or">
			<if test="isCannel != null">
				and isCannel=#{isCannel}
			</if>
			<if test="isUnsubscribed != null">
				and isUnsubscribed=#{isUnsubscribed}
			</if>
			<if test="endTime != null">
				and endTime>#{endTime}
			</if>
		</trim>
	</select>
	
    <!-- 批量累计代言记录表的屏显和挂机数量-->
	<update id="batchUpdateActivitySpokeCount" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="activitySpokeWrapper" open="" separator=";">
			update
			ecpm_t_activity_spoke
			set
            <if test="activitySpokeWrapper.screenCount!=null">
				screenCount= screenCount + #{activitySpokeWrapper.screenCount},
			</if>
			<if test="activitySpokeWrapper.endPhoneCount!=null">
				endPhoneCount= endPhoneCount + #{activitySpokeWrapper.endPhoneCount},
			</if>
			updateTime=#{activitySpokeWrapper.updateTime}
			where
			activityID=#{activitySpokeWrapper.activityID} and
			msisdn=#{activitySpokeWrapper.msisdn}
			and spokeStatus = 5
			limit 1
		</foreach>
	</update>
	
	<update id="updateActivitySpokeStatus">
		update ecpm_t_activity_spoke
		set
		<if test="startTime!=null">
			createTime= #{startTime},
		</if>
		<if test="updateTime!=null">
			updateTime= #{updateTime},
		</if>
		spokeStatus= #{spokeStatus},
		dayCount = #{dayCount}
		where msisdn = #{msisdn} 
		and activityID = #{activityID}
		and isCannel=0 
		and isUnsubscribed =0
	</update>

	<delete id="deleteSpokeFailRecord" parameterType="java.util.Map">
		delete from
		ecpm_t_activity_spoke
		where msisdn = #{msisdn} 
		and	spokeStatus != #{spokeStatus}  
		and activityID = (
		select distinct(activityID)
		from ecpm_t_content_org t2,ecpm_t_activity_contentrel t3
		where
		t2.cyContID = t3.contentID
		and t2.ownerID = #{ownerID}
		)
	</delete>	
	
</mapper>