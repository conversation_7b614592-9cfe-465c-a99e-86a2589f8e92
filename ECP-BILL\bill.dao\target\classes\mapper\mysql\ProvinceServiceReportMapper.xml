<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper.ProvinceServiceReportMapper">
	<resultMap id="provinceServiceReport" type="com.huawei.jaguar.dsdp.bill.dao.domain.ProvinceServiceReportWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="corpID" column="corpID" javaType="java.lang.String" />
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="operationType" column="operationType" javaType="java.lang.String" />
		<result property="reqMsg" column="reqMsg" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
	</resultMap>

	<select id="queryMsgCount" resultType="java.lang.Integer">
		SELECT
			count(1)
		FROM
			ecpm_t_enterprise_simple es
		    INNER JOIN ecpm_t_province_service_report psr ON es.id = psr.enterpriseID
		WHERE
			es.enterpriseType = 5
		  AND es.reserved10 = "113"
		  and psr.createTime like concat(#{statDay},"%")
	</select>

	<select id="queryMsg" resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EcServiceCdrWrapper">
		SELECT
			es.id as enterpriseId,
			es.enterpriseCode,
			es.enterpriseName,
			es.createDate createTime,
			psr.servType,
		    psr.updateTime as syncTime,
			(CASE WHEN psr.operationType = '0' THEN '1'
				  WHEN psr.operationType = '2' THEN '2'
				  WHEN psr.operationType = '1' THEN '5'
				  WHEN psr.operationType = '3' THEN '3'
				  WHEN psr.operationType = '4' THEN '4'
				end) as operationSubType,
			psr.reqMsg,
			35 as enterpriseType
		FROM
			ecpm_t_enterprise_simple es
				INNER JOIN ecpm_t_province_service_report psr ON es.id = psr.enterpriseID
		WHERE
			es.enterpriseType = 5
		  AND es.reserved10 = "113"
		  and psr.createTime like concat(#{statDay},"%")
	</select>

</mapper>