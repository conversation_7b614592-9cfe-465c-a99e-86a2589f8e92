<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.GroupSendTaskResultMapper">
	<resultMap id="groupSendResultTask"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.GroupSendTaskResultWrapper">
		<result property="taskID" column="taskID" javaType="java.lang.Long" />
		<result property="taskCode" column="taskCode" javaType="java.lang.String" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="downloadStatus" column="downloadStatus"
			javaType="java.lang.Integer" />
		<result property="noticeTime" column="noticeTime" javaType="Date" />
		<result property="errdesc" column="errdesc" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
	</resultMap>

	<insert id="batchInsertGroupSendTaskResult">
		insert into ecpm_t_group_send_task_result
		(
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		)
		values
		<foreach collection="list" item="groupSendResultTask"
			separator=",">
			(
			#{groupSendResultTask.taskID},
			#{groupSendResultTask.taskCode},
			#{groupSendResultTask.msisdn},
			#{groupSendResultTask.status},
			#{groupSendResultTask.downloadStatus},
			#{groupSendResultTask.noticeTime},
			#{groupSendResultTask.errdesc},
			#{groupSendResultTask.reserved1},
			#{groupSendResultTask.reserved2},
			#{groupSendResultTask.reserved3},
			#{groupSendResultTask.reserved4}
			)
		</foreach>
	</insert>

	<select id="queryGroupSendTaskResultList" resultMap="groupSendResultTask">
		select
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpm_t_group_send_task_result
		<trim prefix="where" prefixOverrides="and|or">
			<if test="null != list">
				status in
				<foreach item="status" index="index" collection="list" open="("
					separator="," close=")">
					#{status}
				</foreach>
			</if>
			<if test="taskID != null and taskID != ''">
				and taskID = #{taskID}
			</if>
		</trim>
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryGroupSendTaskTotalCount" resultType="java.lang.Long">
		select count(1) from ecpm_t_group_send_task_result
		<trim prefix="where" prefixOverrides="and|or">
			<if test="null != list">
				status in
				<foreach item="status" index="index" collection="list" open="("
					separator="," close=")">
					#{status}
				</foreach>
			</if>
			<if test="taskID != null and taskID != ''">
				and taskID = #{taskID}
			</if>
		</trim>
	</select>

	<delete id="deleteGroupSendResultByTaskID">
		delete from ecpm_t_group_send_task_result where
		taskID = #{taskID}
	</delete>

	<update id="updateGroupSendResult">
		<foreach collection="list" item="groupSendResultTask" open=""
			close="" separator=";">
			update ecpm_t_group_send_task_result
			<set>
				<if
					test="null != groupSendResultTask.taskCode and '' != groupSendResultTask.taskCode">
					taskCode = #{groupSendResultTask.taskCode}
				</if>
				<if
					test="null != groupSendResultTask.downloadStatus and '' != groupSendResultTask.downloadStatus">
					,downloadStatus = #{groupSendResultTask.downloadStatus}
				</if>
				<if test="null != groupSendResultTask.status">
					,status =#{groupSendResultTask.status}
				</if>
				<if
					test="null != groupSendResultTask.errdesc and '' != groupSendResultTask.errdesc">
					,errdesc = #{groupSendResultTask.errdesc}
				</if>
				<if test="null != groupSendResultTask.noticeTime">
					,noticeTime = #{groupSendResultTask.noticeTime}
				</if>
			</set>
			where taskCode = #{groupSendResultTask.taskCode} and
			msisdn =
			#{groupSendResultTask.msisdn}
		</foreach>
	</update>

	<select id="queryMsisdnByTaskID" resultType="java.lang.String">
		select msisdn from
		ecpm_t_group_send_task_result
		where taskID = #{taskID}
		limit
		#{startIndex},#{pageSize}
	</select>

	<select id="queryCountByTaskID" resultType="java.lang.Long">
		select count(1) from
		ecpm_t_group_send_task_result
		where taskID = #{taskID}
	</select>

	<update id="updateTaskCodeByTaskIDAndMsisdn">
		<foreach collection="list" item="groupSendResultTask" open=""
			close="" separator=";">
			update ecpm_t_group_send_task_result
			<set>
				<if
					test="null != groupSendResultTask.taskCode and '' != groupSendResultTask.taskCode">
					taskCode = #{groupSendResultTask.taskCode}
				</if>
			</set>
			where taskID = #{groupSendResultTask.taskID} and
			msisdn =
			#{groupSendResultTask.msisdn}
		</foreach>
	</update>

	<select id="queryTaskResultByTaskCode" resultMap="groupSendResultTask">
		select
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpm_t_group_send_task_result
		where
		taskCode =
		#{taskCode}
	</select>

</mapper>