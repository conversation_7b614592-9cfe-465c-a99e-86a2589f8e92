<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>接入设置</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <!--tab页切换-->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>

    <script type="text/javascript" src="accessSettings.js"></script>
    <style>
        .switch-info {
            color: #aaa;
            margin-left: 30px;
            display: inline-block;
            vertical-align: top;
        }

        .switch {
            width: 40px;
            height: 20px;
            margin: 7px 0;
            vertical-align: top;
        }

        .switch .switch-icon {
            width: 18px;
            height: 18px;
        }

        .offtip {
            height: 20px;
            display: inline-block;
            vertical-align: top;
            color: #999999;
            padding-left: 20px;
        }
        .auto-width{
            width: -moz-max-content;
            width: max-content;
        }



    </style>
</head>

<body ng-app="myApp" ng-controller="settingController" ng-init="init()">
<div class="cooperation-manage">
    <!-- 直客企业自己登陆 -->
    <!-- 管理员登陆查看直客 -->
    <div  class="cooperation-head">
        <span class="frist-tab" >代理商管理 > 子企业管理 > 企业通知</span>
    </div>

    <top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/secondEnterpriseManage/task"
              list-index="78"></top:menu>

    <!--test-end-->

    <div class="cooper-tab">
        <div>
            <form class="form-horizontal ng-pristine ng-invalid ng-invalid-required ng-valid-maxlength"
                  name="myForm" novalidate="">
                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <span ng-bind="'闪信端口号:'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <span >{{groupSendFlashPort}}</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <span ng-bind="'短信端口号:'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <span >{{groupSendSmsPort}}</span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <span ng-bind="'彩信、增彩端口号:'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <span >{{groupSendMmsPort}}</span>
                    </div>
                </div>
                <!--访问密码-->
                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'流量控制fluid:'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input  type="number" class="form-control" ng-model="groupSendFluid"
                                ng-change="changeFluid()"
                               name="accessPassword">
                        <span style="color:red"
                              ng-show="invalid.groupSendFluid_max">
                                <span ng-show="invalid.groupSendFluid_max"
                                      ng-bind="'最大值不能超过50'|translate"></span>

                            </span>
                    </div>
                </div>

            </form>
        </div>
        <div class="modal-footer">
            <button type="submit"
                    class="btn btn-primary search-btn  col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 "
                    ng-disabled=""
                    ng-bind="'COMMON_SAVE'|translate" ng-click="saveFluid()"></button>
            <!--<button type="submit" style="margin-left: 50px" class="btn " data-dismiss="modal" aria-label="Close" id="addMemCancel" ng-bind="'COMMON_BACK'|translate"></button>-->
        </div>
    </div>
	
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog"
         aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838'>
                            {{tip|translate}}
                        </p>
                    </div>
                </div>
                <div class="modal-footer" style="text-align:center; margin-left:0px;">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
<style>
    body {
        background: #f2f2f2;
    }

    .modal-footer {
        text-align: left;
    }

    .fontGreen {
        color: rgb(48, 147, 25)
    }

    .fontRed {
        color: rgb(252, 70, 93);
    }

    .cooperation-manage {
        min-width: 1024px;
    }

    .cooperation-head {
        padding: 20px;
    }

    .cooperation-head .frist-tab {
        font-size: 16px;
    }

    .cooperation-head .second-tab {
        font-size: 14px;
    }

    .cooper-tab {
        margin: 0 20px;
        background: #fff;
        border-radius: 2px;
        padding: 36px 10px 16px;
    }

    .form-group .control-label icon {
        color: #ff254c;
        vertical-align: sub;
        margin-right: 2px;
    }

    .form-group div {
        line-height: 34px;
    }

    .form-group {
        margin-bottom: 35px;
    }

</style>

</html>