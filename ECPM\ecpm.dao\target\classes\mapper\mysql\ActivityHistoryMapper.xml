<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityHistoryMapper">
    <resultMap id="activityWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivityWrapper">       
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="activityName" column="activityName" javaType="java.lang.String" />
        <result property="activityRuleDesc" column="activityRuleDesc" javaType="java.lang.String" />
        <result property="rewardExplain" column="rewardExplain" javaType="java.lang.String" />
        <result property="rewardNotify" column="rewardNotify" javaType="java.lang.String" />
        <result property="maxPushNum" column="maxPushNum" javaType="java.lang.Integer" />
        <result property="totalNum" column="totalNum" javaType="java.lang.Integer" />
        <result property="memo" column="memo" javaType="java.lang.String" />
        <result property="templateID" column="templateID" javaType="java.lang.Integer" />
        <result property="backLogoURL" column="backLogoURL" javaType="java.lang.String" />
        <result property="activityUrl" column="activityUrl" javaType="java.lang.String" />
        <result property="activityQrCode" column="activityQrCode" javaType="java.lang.String" />
        <result property="effectiveTime" column="effectivetime" javaType="Date" />
        <result property="expireTime" column="expiretime" javaType="Date" />
        <result property="auditStatus" column="auditStatus" javaType="java.lang.Integer" />
        <result property="approveIdea" column="approveIdea" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
        <result property="isPrematureTerminated" column="isPrematureTerminated" javaType="java.lang.Integer" />
        <result property="terminatedReason" column="terminatedReason" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="Date" />
        <result property="updateTime" column="updateTime" javaType="Date" />
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
        <result property="extInfo" column="extInfo" javaType="java.lang.String" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="reserved5" column="reserved5" javaType="java.lang.String" />
        <result property="reserved6" column="reserved6" javaType="java.lang.String" />
        <result property="reserved7" column="reserved7" javaType="java.lang.String" />
        <result property="reserved8" column="reserved8" javaType="java.lang.String" />
        <result property="reserved9" column="reserved9" javaType="java.lang.String" />
        <result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>
    
    <insert id="createActivityHistory">
		insert into ecpm_t_activity_history
		(
			ID,
			activityName,
			activityRuleDesc,
			rewardExplain,
			rewardNotify,
			maxPushNum,
			totalNum,
			memo,
			templateID,
			backLogoURL,
			activityUrl,
			activityQrCode,
			effectivetime,
			expiretime,
			auditStatus,
			approveIdea,
			enterpriseID,
			enterpriseName,
			isPrematureTerminated,
			terminatedReason,
			createTime,
			updateTime,
			operatorID,
			extInfo,
			reserved1,
			reserved2,
			reserved3,
			reserved4,
			reserved5,
			reserved6,
			reserved7,
			reserved8,
			reserved9,
			reserved10
		)
		values
		(
			#{id},
			#{activityName},
			#{activityRuleDesc},
			#{rewardExplain},
			#{rewardNotify},
			#{maxPushNum},
			#{totalNum},
			#{memo},
			#{templateID},
			#{backLogoURL},
			#{activityUrl},
			#{activityQrCode},
			#{effectiveTime},
			#{expireTime},
			#{auditStatus},
			#{approveIdea},
			#{enterpriseID},
			#{enterpriseName},
			#{isPrematureTerminated},
			#{terminatedReason},
			#{createTime},
			#{updateTime},
			#{operatorID},
			#{extInfo},
			#{reserved1},
			#{reserved2},
			#{reserved3},
			#{reserved4},
			#{reserved5},
			#{reserved6},
			#{reserved7},
			#{reserved8},
			#{reserved9},
			#{reserved10})
	</insert>
    
    <update id="updateEnterpriseName">
		update ecpm_t_activity_history t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>
	
	<update id="updateParentEnterpriseName">
		update ecpm_t_activity_history t
		set t.updateTime = #{now},
		t.reserved1 = #{enterpriseName}
		where
		t.reserved3 = #{enterpriseID}
	</update>
    </mapper>