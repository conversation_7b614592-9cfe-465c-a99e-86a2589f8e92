<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.TemplateHotlineRelMapper">

    <insert id="batchInsetTemplateHotlineRel">
        INSERT INTO ecpm_t_content_org 
        (ID,
        cyContID,
        ownerType,
        ownerID,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        createTime,
        updateTime,
        operatorID,
        orgCode,
        orgName,
        hotlineNo
        )
        VALUES 
        <foreach collection="list" item="contentOrgWrapper"
            separator=",">
        (
        #{contentOrgWrapper.id},
        #{contentOrgWrapper.cyContID},
        #{contentOrgWrapper.ownerType},
        #{contentOrgWrapper.ownerID},
        #{contentOrgWrapper.extInfo},
        #{contentOrgWrapper.reserved1},
        #{contentOrgWrapper.reserved2},
        #{contentOrgWrapper.reserved3},
        #{contentOrgWrapper.reserved4},
        #{contentOrgWrapper.createTime},
        #{contentOrgWrapper.updateTime},
        #{contentOrgWrapper.operatorID},
        #{contentOrgWrapper.orgCode},
        #{contentOrgWrapper.orgName},
        #{contentOrgWrapper.hotlineNo}
        )
        </foreach>
    </insert>
    
	<delete id="batchDeleteEcpmTemplateHotlineRel">
		delete from ecpm_t_content_org where 
		hotlineNo in
		<foreach item="hotlineNo" index="index" collection="hotlineList"
			open="(" separator="," close=")">
			#{hotlineNo}
		</foreach>
		and 
		cyContID = #{cyContID}
	</delete>

    <delete id="deleteEcpmTemplateHotlineRel">
        delete from ecpm_t_content_org where
        hotlineNo = #{hotlineNo}
        and
        ID = #{ID}
    </delete>
	
	<select id="selectNextval" resultType="java.lang.Long">
		SELECT nextval('ecpm_seq_contentorg')
	</select>
	
</mapper>