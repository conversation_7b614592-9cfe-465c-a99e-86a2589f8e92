<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>业务设置</title>
    <link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
    <link href="../../../../css/reset.css" rel="stylesheet"/>
    <link rel="stylesheet" href="../../../../css/businessSetting.css">
    <script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
    <!-- 引入导航组件 -->
    <link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="businessSetting.js"></script>

    <style>
        .no-optional {
            padding: 20px 25px;
            background-color: #ffffff;
        }

        .no-optional p {
            margin: 0 auto;
            width: 145px;
        }

        .center {
            text-align: center;
        }

        .min-width-330 {
            min-width: 330px;
        }

        .service-config {
            color: #705DE1;
        }

        .noPadding {
            padding-right: 0px;
            padding-left: 5px;
        }

        .redBorder {
            border: #ff0000 1px solid;
        }
    </style>
</head>

<body ng-app='myApp' ng-controller='businessSettingController' ng-init="init();" class="body-min-width">
<div class="cooperation-manage">
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType=='1'">
        <span class="frist-tab">直客管理</span>&nbsp;&gt;&nbsp;<span class="second-tab">业务配置</span>
        &gt;&nbsp;<span class="second-tab" ng-if="menuType==0">频控设置</span>
        <span class="second-tab" ng-if="menuType==1">其他设置</span>
    </div>
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType=='2'">
        <span class="frist-tab">代理商信息管理</span>&nbsp;&gt;&nbsp;<span class="second-tab">业务设置</span>
        &gt;&nbsp;<span class="second-tab" ng-if="menuType==0">频控设置</span>
        <span class="second-tab" ng-if="menuType==1">其他设置</span>
    </div>
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType=='3'">
        <span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'BUSINESSSETTING'|translate"></span> &gt;&nbsp;
        &gt;&nbsp;<span class="second-tab" ng-if="menuType==0">频控设置</span>
        <span class="second-tab" ng-if="menuType==1">其他设置</span>
    </div>
    <!--二级企业管理-->
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType=='5'">
        <span class="frist-tab" ng-bind="'COMMON_PROENTERPRISE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'BUSINESSSETTING'|translate"></span>

        &gt;&nbsp;<span class="second-tab" ng-if="menuType==0">频控设置</span>
        <span class="second-tab" ng-if="menuType==1">其他设置</span>
    </div>
    <div class="cooperation-head" ng-if="isProvincial2 || (isAgent&&!isSecondEnter) || isZhike">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'BUSINESSSETTING'|translate"></span>
        &gt;&nbsp;<span class="second-tab" ng-if="menuType==0">频控设置</span>
        <span class="second-tab" ng-if="menuType==1">其他设置</span>
    </div>

    <div class="cooperation-head" ng-if="isAgent && isSecondEnter">
        <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>
        <span>&nbsp;&gt;&nbsp;</span>
        <span class="second-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>
        <span>&nbsp;&gt;&nbsp;</span>
        <span class="second-tab" ng-bind="'GROUP_BUSSINESSCONF'|translate"></span>
    </div>

    <top:menu ng-if="isSuperManager && enterpriseType=='1'" chose-index="1"
              page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/businessSetting" list-index="20"></top:menu>
    <top:menu ng-if="isSuperManager && enterpriseType=='5'" chose-index="1"
              page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/businessSetting" list-index="31"
              apply-val="{{proSupServerType}}"></top:menu>

    <top:menu ng-if="isSuperManager && enterpriseType=='2'" chose-index="1"
              page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/businessSetting" list-index="70"></top:menu>
    <top:menu ng-if="isAgent && isSecondEnter" chose-index="1"
              page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/businessSetting/businessSetting"
              list-index="52"></top:menu>


    <!--20191216 管理员登录时，子企业管理新增配置，内含业务配置、接入设置、黑白名单-->
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/businessSetting"
              list-index="68" ng-if="isSuperManager && enterpriseType=='3'"></top:menu>

    <div class="cooperation-nav ng-scope ng-isolate-scope second-topmenu">
        <a class="tabtn-menu ng-binding ng-scope menu-type-table cur-tabtn-menu" ng-click="changeTable(0)">
            频控设置
        </a>
        <a ng-show="isSuperManager" class="tabtn-menu ng-binding ng-scope menu-type-table"  ng-click="changeTable(1)">
            其他设置
        </a>
    </div>

    <div ng-show="businessStatus ==0">
        <!-- 提示 -->
        <div ng-show="menuType == 0" class="cooperation-tit col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <span style="color:red;" ng-bind="'FREQUENCY_CONTROL_COMMON_TIPS'|translate"></span>
        </div>
        <!--企业名称-->
        <div class="cooperation-tit col-lg-12 col-md-12 col-sm-12 col-xs-12"
             ng-if="!isSecondEnter && enterpriseType != '3'">
            <span ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></span>：<span>{{enterpriseName}}</span>
        </div>
        <div class="cooperation-tit col-lg-12 col-md-12 col-sm-12 col-xs-12"
             ng-if="isSecondEnter || enterpriseType == '3'">
            <span ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></span>：<span>{{subEnterpriseName}}</span>
        </div>

        <!-- 名片业务频控配置-->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="menuType == 0">
            <p class="form-horizontal-tit service-config" ng-bind="'MP_RATECONTROL_CONFIG'|translate"></p>
            <div class="bgWhite">
                <!--屏显配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase1">
                    <p class="form-horizontal-tit" ng-bind="'PINGXIANSETTING'|translate"></p>
                    <!--名片、屏显，同一模板单人日上限-->
                    <div style="width:320px" class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMITS'|translate"></label>
                        </div>
                        <div style="width:66px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <select class="form-control" ng-model="initAllInfo.mp.px.periodType"
                                    ng-change="moreThen('mp_px_limit',initDefaultInfo.mp.defaultOneTemplateDayLimit,initAllInfo.mp.px.oneTemplateDayLimit,initAllInfo.mp.px.periodType)"
                                    ng-options="x.id as x.name for x in periodTypeList">
                                <!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
                            </select>
                        </div>
                        <div style="width:70px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('mp_px_limit',initDefaultInfo.mp.defaultOneTemplateDayLimit,initAllInfo.mp.px.oneTemplateDayLimit,initAllInfo.mp.px.periodType)"
                                   placeholder="{{initDefaultInfo.mp.defaultOneTemplateDayLimitDesc}}" name="amount1"
                                   ng-class="{'redBorder':!!mp_px_limit_error}"
                                   ng-model="initAllInfo.mp.px.oneTemplateDayLimit">
                        </div>
                    </div>
                    <!--名片、屏显，最小投递间隔（分）-->
                    <div class="form-inline form-fst" style="width:320px;">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_MINUTE'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 noPadding">
                            <input type="text" class="form-control" autocomplete="off" style="width:130px;"
                                   ng-keyup="lessThen('mp_px_interval',initDefaultInfo.mp.defaultOneDeliveryInterval,initAllInfo.mp.px.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.mp.defaultOneDeliveryIntervalDesc}}" name="amount2"
                                   ng-class="{'redBorder':!!mp_px_interval_error}"
                                   ng-model="initAllInfo.mp.px.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
                <!--挂机短信配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase2">
                    <p class="form-horizontal-tit" ng-bind="'GUAJIDUANXINSETTING'|translate"></p>
                    <!--名片、挂短，同一模板单人日上限-->
                    <div style="width:320px" class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMITS'|translate"></label>
                        </div>
                        <div style="width:66px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <select class="form-control" ng-model="initAllInfo.mp.gjdx.periodType"
                                    ng-change="moreThen('mp_gjdx_limit',initDefaultInfo.mp.defaultOneTemplateDayLimit,initAllInfo.mp.gjdx.oneTemplateDayLimit,initAllInfo.mp.gjdx.periodType)"
                                    ng-options="x.id as x.name for x in periodTypeList">
                                <!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
                            </select>
                        </div>
                        <div style="width:70px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('mp_gjdx_limit',initDefaultInfo.mp.defaultOneTemplateDayLimit,initAllInfo.mp.gjdx.oneTemplateDayLimit,initAllInfo.mp.gjdx.periodType)"
                                   placeholder="{{initDefaultInfo.mp.defaultOneTemplateDayLimitDesc}}" name="amount3"
                                   ng-class="{'redBorder':!!mp_gjdx_limit_error}"
                                   ng-model="initAllInfo.mp.gjdx.oneTemplateDayLimit">
                        </div>

                    </div>
                    <!--名片、挂短，最小投递间隔（分）-->
                    <div class="form-inline form-fst" style="width:320px;">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_MINUTE'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off" style="width:130px;"
                                   ng-keyup="lessThen('mp_gjdx_interval',initDefaultInfo.mp.defaultOneDeliveryInterval,initAllInfo.mp.gjdx.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.mp.defaultOneDeliveryIntervalDesc}}" name="amount4"
                                   ng-class="{'redBorder':!!mp_gjdx_interval_error}"
                                   ng-model="initAllInfo.mp.gjdx.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
                <!--挂机彩信配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">
                    <p class="form-horizontal-tit" ng-bind="'GUAJICAIXINSETTING'|translate"></p>
                    <!--名片、挂彩，同一模板单人日上限-->
                    <div style="width:320px" class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMITS'|translate"></label>
                        </div>
                        <div style="width:66px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <select class="form-control" ng-model="initAllInfo.mp.gjcx.periodType"
                                    ng-change="moreThen('mp_gjcx_limit',initDefaultInfo.mp.defaultOneTemplateDayLimit,initAllInfo.mp.gjcx.oneTemplateDayLimit,initAllInfo.mp.gjcx.periodType)"
                                    ng-options="x.id as x.name for x in periodTypeList">
                                <!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
                            </select>
                        </div>
                        <div style="width:70px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('mp_gjcx_limit',initDefaultInfo.mp.defaultOneTemplateDayLimit,initAllInfo.mp.gjcx.oneTemplateDayLimit,initAllInfo.mp.gjcx.periodType)"
                                   placeholder="{{initDefaultInfo.mp.defaultOneTemplateDayLimitDesc}}" name="amount5"
                                   ng-class="{'redBorder':!!mp_gjcx_limit_error}"
                                   ng-model="initAllInfo.mp.gjcx.oneTemplateDayLimit">
                        </div>
                    </div>
                    <!--名片、挂彩，最小投递间隔（分）-->
                    <div class="form-inline form-fst" style="width:320px;">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_MINUTE'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off" style="width:130px;"
                                   ng-keyup="lessThen('mp_gjcx_interval',initDefaultInfo.mp.defaultOneDeliveryInterval,initAllInfo.mp.gjcx.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.mp.defaultOneDeliveryIntervalDesc}}" name="amount6"
                                   ng-class="{'redBorder':!!mp_gjcx_interval_error}"
                                   ng-model="initAllInfo.mp.gjcx.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- 热线业务频控配置 -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="menuType == 0">
            <p class="form-horizontal-tit service-config" ng-bind="'RX_RATECONTROL_CONFIG'|translate"></p>
            <div class="bgWhite">
                <!--屏显配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase1">
                    <p class="form-horizontal-tit" ng-bind="'PINGXIANSETTING'|translate"></p>
                    <!--热线、屏显，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('rx_px_limit',initDefaultInfo.rx.defaultOneTemplateDayLimit,initAllInfo.rx.px.oneTemplateDayLimit)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount1"
                                   ng-class="{'redBorder':!!rx_px_limit_error}"
                                   ng-model="initAllInfo.rx.px.oneTemplateDayLimit">
                        </div>


                    </div>
                    <!--热线、屏显，最小投递间隔（秒）-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_SECOND'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="lessThen('rx_px_interval',initDefaultInfo.rx.defaultOneDeliveryInterval,initAllInfo.rx.px.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneDeliveryIntervalDesc}}" name="amount2"
                                   ng-class="{'redBorder':!!rx_px_interval_error}"
                                   ng-model="initAllInfo.rx.px.oneDeliveryInterval">
                        </div>
                    </div>

                </form>
                <!--挂机短信配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase2">
                    <p class="form-horizontal-tit" ng-bind="'GUAJIDUANXINSETTING'|translate"></p>
                    <!--热线、挂短，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('rx_gjdx_limit',initDefaultInfo.rx.defaultOneTemplateDayLimit,initAllInfo.rx.gjdx.oneTemplateDayLimit)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount3"
                                   ng-class="{'redBorder':!!rx_gjdx_limit_error}"
                                   ng-model="initAllInfo.rx.gjdx.oneTemplateDayLimit">
                        </div>

                    </div>
                    <!--热线、挂短，最小投递间隔（秒）-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_SECOND'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="lessThen('rx_gjdx_interval',initDefaultInfo.rx.defaultOneDeliveryInterval,initAllInfo.rx.gjdx.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneDeliveryIntervalDesc}}" name="amount4"
                                   ng-class="{'redBorder':!!rx_gjdx_interval_error}"
                                   ng-model="initAllInfo.rx.gjdx.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
                <!--挂机彩信配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">
                    <p class="form-horizontal-tit" ng-bind="'GUAJICAIXINSETTING'|translate"></p>
                    <!--热线、挂彩，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('rx_gjcx_limit',initDefaultInfo.rx.defaultOneTemplateDayLimit,initAllInfo.rx.gjcx.oneTemplateDayLimit)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount5"
                                   ng-class="{'redBorder':!!rx_gjcx_limit_error}"
                                   ng-model="initAllInfo.rx.gjcx.oneTemplateDayLimit">
                        </div>

                    </div>
                    <!--热线、挂彩，最小投递间隔（秒）-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_SECOND'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="lessThen('rx_gjcx_interval',initDefaultInfo.rx.defaultOneDeliveryInterval,initAllInfo.rx.gjcx.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneDeliveryIntervalDesc}}" name="amount6"
                                   ng-class="{'redBorder':!!rx_gjcx_interval_error}"
                                   ng-model="initAllInfo.rx.gjcx.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
                <!--挂机增彩配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">
                    <p class="form-horizontal-tit" ng-bind="'ZGUAJIENGCAINSETTING'|translate"></p>
                    <!--热线、增彩，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('rx_gjzc_limit',initDefaultInfo.rx.defaultOneTemplateDayLimit,initAllInfo.rx.gjzc.oneTemplateDayLimit)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount5"
                                   ng-class="{'redBorder':!!rx_gjzc_limit_error}"
                                   ng-model="initAllInfo.rx.gjzc.oneTemplateDayLimit">
                        </div>

                    </div>
                    <!--热线、增彩，最小投递间隔（秒）-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_SECOND'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="lessThen('rx_gjzc_interval',initDefaultInfo.rx.defaultOneDeliveryInterval,initAllInfo.rx.gjzc.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.rx.defaultOneDeliveryIntervalDesc}}" name="amount6"
                                   ng-class="{'redBorder':!!rx_gjzc_interval_error}"
                                   ng-model="initAllInfo.rx.gjzc.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- 广告业务频控配置 -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="menuType == 0">
            <p class="form-horizontal-tit service-config" ng-bind="'GG_RATECONTROL_CONFIG'|translate"></p>
            <div class="bgWhite">
                <!--屏显配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase1">
                    <p class="form-horizontal-tit" ng-bind="'PINGXIANSETTING'|translate"></p>
                    <!--广告、屏显，同一模板单人日上限-->
                    <div style="width:320px" class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMITS'|translate"></label>
                        </div>
                        <div style="width:66px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <select class="form-control" ng-model="initAllInfo.gg.px.periodType"
                                    ng-change="moreThen('gg_px_limit',initDefaultInfo.gg.defaultOneTemplateDayLimit,initAllInfo.gg.px.oneTemplateDayLimit,initAllInfo.gg.px.periodType)"
                                    ng-options="x.id as x.name for x in periodTypeList">
                                <!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
                            </select>
                        </div>
                        <div style="width:70px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('gg_px_limit',initDefaultInfo.gg.defaultOneTemplateDayLimit,initAllInfo.gg.px.oneTemplateDayLimit,initAllInfo.gg.px.periodType)"
                                   placeholder="{{initDefaultInfo.gg.defaultOneTemplateDayLimitDesc}}" name="amount1"
                                   ng-class="{'redBorder':!!gg_px_limit_error}"
                                   ng-model="initAllInfo.gg.px.oneTemplateDayLimit">
                        </div>
                    </div>
                    <!--广告、屏显，最小投递间隔（分）-->
                    <div class="form-inline form-fst" style="width:320px;">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_MINUTE'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 noPadding">
                            <input type="text" class="form-control" autocomplete="off" style="width:130px;"
                                   ng-keyup="lessThen('gg_px_interval',initDefaultInfo.gg.defaultOneDeliveryInterval,initAllInfo.gg.px.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.gg.defaultOneDeliveryIntervalDesc}}" name="amount2"
                                   ng-class="{'redBorder':!!gg_px_interval_error}"
                                   ng-model="initAllInfo.gg.px.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
                <!--挂机彩信配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">
                    <p class="form-horizontal-tit" ng-bind="'GUAJICAIXINSETTING_2'|translate"></p>
                    <!--广告、挂彩，同一模板单人日上限-->
                    <div style="width:320px" class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMITS'|translate"></label>
                        </div>
                        <div style="width:66px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <select class="form-control" ng-model="initAllInfo.gg.gjcx.periodType"
                                    ng-change="moreThen('gg_gjcx_limit',initDefaultInfo.gg.defaultOneTemplateDayLimit,initAllInfo.gg.gjcx.oneTemplateDayLimit,initAllInfo.gg.gjcx.periodType)"
                                    ng-options="x.id as x.name for x in periodTypeList">
                                <!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
                            </select>
                        </div>
                        <div style="width:70px" class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('gg_gjcx_limit',initDefaultInfo.gg.defaultOneTemplateDayLimit,initAllInfo.gg.gjcx.oneTemplateDayLimit,initAllInfo.gg.gjcx.periodType)"
                                   placeholder="{{initDefaultInfo.gg.defaultOneTemplateDayLimitDesc}}" name="amount5"
                                   ng-class="{'redBorder':!!gg_gjcx_limit_error}"
                                   ng-model="initAllInfo.gg.gjcx.oneTemplateDayLimit">
                        </div>

                    </div>
                    <!--名片、挂彩，最小投递间隔（分）-->
                    <div class="form-inline form-fst" style="width:320px;">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'MINIUMDELIVERYINTERVAL_MINUTE'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off" style="width:130px;"
                                   ng-keyup="lessThen('gg_gjcx_interval',initDefaultInfo.gg.defaultOneDeliveryInterval,initAllInfo.gg.gjcx.oneDeliveryInterval)"
                                   placeholder="{{initDefaultInfo.gg.defaultOneDeliveryIntervalDesc}}" name="amount6"
                                   ng-class="{'redBorder':!!gg_gjcx_interval_error}"
                                   ng-model="initAllInfo.gg.gjcx.oneDeliveryInterval">
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!-- 通知业务频控配置 -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="menuType == 0">
            <p class="form-horizontal-tit service-config" ng-bind="'TZ_RATECONTROL_CONFIG'|translate"></p>
            <div class="bgWhite">
                <!--屏显配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase1">
                    <p class="form-horizontal-tit" ng-bind="'SMS_SETTING'|translate"></p>
                    <!--热线、屏显，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('tz_sms_limit',initAllInfo.TZSms.defaultOneTemplateDayLimit,initAllInfo.TZSms.oneTemplateDayLimit)"

                                   placeholder="{{initAllInfo.TZSms.defaultOneTemplateDayLimit}}" name="amount1"
                                   pattern="(^[0-9]{1,9}$)|(^[1-9]$)"
                                   ng-class="{'redBorder':!!limitError.tz_sms_limit}"
                                   ng-model="initAllInfo.TZSms.oneTemplateDayLimit">
                        </div>

                        <div style="width: 50%" class="col-lg-3 col-xs-3  col-sm-3 col-md-3" ng-show="!!limitError.tz_sms_limit">
                            <span class="redFont" style="color:red;">
                                        超过默认频控
                            </span>
                        </div>
                    </div>

                </form>
                <!--挂机短信配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase2">
                    <p class="form-horizontal-tit" ng-bind="'FLASH_SETTING'|translate"></p>
                    <!--热线、挂短，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('tz_flash_limit',initAllInfo.TZFlash.defaultOneTemplateDayLimit,initAllInfo.TZFlash.oneTemplateDayLimit)"

                                   placeholder="{{initAllInfo.TZFlash.defaultOneTemplateDayLimit}}" name="amount3"
                                   ng-class="{'redBorder':!!limitError.tz_flash_limit}"
                                   ng-model="initAllInfo.TZFlash.oneTemplateDayLimit">
                        </div>
                        <div style="width: 50%" class="col-lg-3 col-xs-3  col-sm-3 col-md-3" ng-show="!!limitError.tz_flash_limit">
                            <span class="redFont" style="color:red;">
                                        超过默认频控
                            </span>
                        </div>
                    </div>

                </form>
                <!--挂机彩信配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">
                    <p class="form-horizontal-tit" ng-bind="'CX_SETTING'|translate"></p>
                    <!--热线、挂彩，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('tz_cx_limit',initAllInfo.TZCX.defaultOneTemplateDayLimit,initAllInfo.TZCX.oneTemplateDayLimit)"
                                   placeholder="{{initAllInfo.TZCX.defaultOneTemplateDayLimit}}" name="amount5"
                                   ng-class="{'redBorder':!!limitError.tz_cx_limit}"
                                   ng-model="initAllInfo.TZCX.oneTemplateDayLimit">
                        </div>
                        <div style="width: 50%" class="col-lg-3 col-xs-3  col-sm-3 col-md-3" ng-show="!!limitError.tz_cx_limit">
                            <span class="redFont" style="color:red;">
                                        超过默认频控
                            </span>
                        </div>
                    </div>

                </form>
                <!--挂机增彩配置-->
                <!--<form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">
                    <p class="form-horizontal-tit" ng-bind="'USSD_SETTING'|translate"></p>
                    &lt;!&ndash;热线、增彩，同一模板单人日上限&ndash;&gt;
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('tz_ussd_limit',initAllInfo.TZUSSD.defaultOneTemplateDayLimit,initAllInfo.TZUSSD.oneTemplateDayLimit)"
                                   placeholder="{{initAllInfo.TZUSSD.defaultOneTemplateDayLimit}}" name="amount5"
                                   ng-class="{'redBorder':!!limitError.tz_ussd_limit}"
                                   ng-model="initAllInfo.TZUSSD.oneTemplateDayLimit">
                        </div>
                        <div style="width: 50%" class="col-lg-3 col-xs-3  col-sm-3 col-md-3" ng-show="!!limitError.tz_ussd_limit">
                            <span class="redFont" style="color:red;">
                                        超过默认频控
                            </span>
                        </div>
                    </div>

                </form>-->
                <!--挂机增彩配置-->
                <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">
                    <p class="form-horizontal-tit" ng-bind="'MMS_SETTING'|translate"></p>
                    <!--热线、增彩，同一模板单人日上限-->
                    <div class="form-inline form-fst">
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">
                            <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                        </div>
                        <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                            <input type="text" class="form-control" autocomplete="off"
                                   ng-keyup="moreThen('tz_mms_limit',initAllInfo.TZMms.defaultOneTemplateDayLimit,initAllInfo.TZMms.oneTemplateDayLimit)"
                                   placeholder="{{initAllInfo.TZMms.defaultOneTemplateDayLimit}}" name="amount5"
                                   ng-class="{'redBorder':!!limitError.tz_mms_limit}"
                                   ng-model="initAllInfo.TZMms.oneTemplateDayLimit">
                        </div>
                        <div style="width: 50%" class="col-lg-3 col-xs-3  col-sm-3 col-md-3" ng-show="!!limitError.tz_mms_limit">
                            <span class="redFont" style="color:red;">
                                        超过默认频控
                            </span>
                        </div>
                    </div>

                </form>
            </div>
        </div>
         <!--业务投递配置-->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="isSuperManager && menuType == 1">
            <p class="form-horizontal-tit service-config" ng-bind="'BUSINESS_CONFIG'|translate"></p>
            <!--名片彩印-->
            <form class="form-horizontal form-group bgWhite">
                <!--屏显-->
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'CONTENTAUDIT_PX'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch4"
                              ng-class={'off':initAllInfo.cardPrintStatusWrapper.screenStatus=='0'}
                              ng-click="changeStatus(11)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initparentInfo.mp.screenStatus=='0'" class="switch-info"
                              ng-bind="'CARDPRINT_SCREEN_NOTPERMIT'|translate"></span>
                    </div>
                </div>
                <!--挂短-->
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                        <p class="form-horizontal-tit" style="color:red;"
                           ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></p>
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label>挂机短信：</label>
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':initAllInfo.cardPrintStatusWrapper.smsstatus=='0'}
                              ng-click="changeStatus(12)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initparentInfo.mp.smsstatus=='0'" class="switch-info"
                              ng-bind="'CARDPRINT_SMSS_NOTPERMIT'|translate"></span>
                    </div>
                </div>
                <!--挂彩-->
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label>挂机彩信：</label>
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch6" ng-class={'off':initAllInfo.cardPrintStatusWrapper.mmsstatus=='0'}
                              ng-click="changeStatus(13)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initparentInfo.mp.mmsstatus=='0'" class="switch-info"
                              ng-bind="'CARDPRINT_MMSS_NOTPERMIT'|translate"></span>
                    </div>
                </div>
            </form>

            <!-- 热线彩印 -->
            <form class="form-horizontal form-group bgWhite">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                        <p class="form-horizontal-tit" style="color:red;"
                           ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></p>
                    </div>
                    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 control-label" style="text-align: left;">
                        <p ng-bind="'HOTLINE_CONTROL_MSG'|translate"></p>
                    </div>
                </div>
            </form>

            <!-- 广告彩印 -->
            <form class="form-horizontal form-group bgWhite" ng-show="isSuperManager">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                        <p class="form-horizontal-tit" style="color:red;"
                           ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></p>
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'CONTENTAUDIT_PX'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
					<span class="switch switch5" ng-class={'off':initAllInfo.adPrintStatus.screenStatus=='0'}
                          ng-click="changeStatus(31)">
						<icon class="switch-icon"></icon>
					</span>
                        <span class='offtip' ng-show="initparentInfo.gg.screenStatus=='0'" class="switch-info"
                              ng-bind="'ADPRINT_SCREEN_NOTPERMIT'|translate"></span>
                    </div>
                </div>
            </form>
        </div>
        <!-- 退订订购关系配置 -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
             ng-show="isSuperManager && ((enterpriseType == 5 && !isZYZQ) || (enterpriseType == 2)) && menuType == 1">
            <p class="form-horizontal-tit service-config" ng-bind="'UNSUBSWITCH_CONFIG'|translate"
               ng-show="isSuperManager"></p>
            <form class="form-horizontal form-group col-lg-12 bgWhite" ng-show="isSuperManager">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'UNSUBSWITCH_MSG'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
					<span class="switch switch5" ng-class={'off':initAllInfo.unSubscribeStatus=='0'}
                          ng-click="changeStatus(10)">
						<icon class="switch-icon"></icon>
					</span>
                        <span style="color:red;" class="switch-info"
                              ng-bind="'UNSUBSWITCH_ON_MSG'|translate"></span>
                    </div>
                </div>
            </form>
        </div>

        <!-- 企业通知配置 -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="(enterpriseType=='2' || enterpriseType=='3')&& menuType == 1">
            <p class="form-horizontal-tit service-config" ng-bind="'ZENGCAIQUNFA_CONFIG'|translate"
               ng-show="isSuperManager"></p>
            <form class="form-horizontal form-group col-lg-12 bgWhite" ng-show="isSuperManager">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'CONTENTAUDIT_PX'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
					<span class="switch switch5" ng-class={'off':initAllInfo.groupSendStatus.screenStatus=='0'}
                          ng-click="changeStatus(83)">
						<icon class="switch-icon"></icon>
					</span>
                        <span class='offtip' ng-show="initparentInfo.groupSendStatus.screenStatus=='0'"
                              class="switch-info"
                              ng-bind="'SENGGROUP_SCREEN_NOTPERMIT'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'ZENGCAISERVICE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':initAllInfo.groupSendStatus.enhancedMMSStatus=='0'}
                              ng-click="changeStatus(81)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initparentInfo.groupSendStatus.enhancedMMSStatus=='0'"
                              class="switch-info"
                              ng-bind="'SENGGROUP_EN_NOTPERMIT'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                        <!--<p  class="form-horizontal-tit" style="color:red;" ng-bind="'GROUP_SEND'|translate"></p>-->
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'CAIXINSERVICE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':initAllInfo.groupSendStatus.mmsstatus=='0'}
                              ng-click="changeStatus(84)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initparentInfo.groupSendStatus.mmsstatus=='0'" class="switch-info"
                              ng-bind="'SENGGROUP_MMS_NOTPERMIT'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                        <!--<p  class="form-horizontal-tit" style="color:red;" ng-bind="'GROUP_SEND'|translate"></p>-->
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'DUANXINSERVICE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':initAllInfo.groupSendStatus.smsstatus=='0'}
                              ng-click="changeStatus(82)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initparentInfo.groupSendStatus.smsstatus=='0'" class="switch-info"
                              ng-bind="'SENGGROUP_SM_NOTPERMIT'|translate"></span>
                    </div>
                </div>
            </form>
        </div>

        <!--异网投递配置-->
        <div ng-if="isSuperManager && !isZYZQ" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="menuType == 1">
            <p class="form-horizontal-tit service-config" ng-bind="'PLATFORM_CONFIG'|translate"></p>
            <form class="form-horizontal form-group bgWhite">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'MOBILE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch4" ng-class={'off':initAllInfo.platformStatus.mobileStatus=='0'}>
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initAllInfo.platformStatus.mobileStatus=='1'" class="switch-info"
                              ng-bind="'PLATFORM_MOBILE_MSG'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'UNICOM'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':initAllInfo.platformStatus.unicomStatus=='0'}
                              ng-click="changeStatus(4)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip'
                              ng-show="initAllInfo.platformStatus.unicomStatus=='1'||parentUnicomStatus=='0'"
                              class="switch-info"
                              ng-bind="(parentUnicomStatus=='0'?'PLATFORM_UNICOM_NOTPERMIT':'PLATFORM_UNICOM_MSG')|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'TELECOM'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch6" ng-class={'off':initAllInfo.platformStatus.telecomStatus=='0'}
                              ng-click="changeStatus(5)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip'
                              ng-show="initAllInfo.platformStatus.telecomStatus=='1'||parentTelecomStatus=='0'"
                              class="switch-info"
                              ng-bind="(parentTelecomStatus=='0'?'PLATFORM_TELECOM_NOTPERMIT':'PLATFORM_TELECOM_MSG')|translate"></span>
                    </div>
                </div>
            </form>
        </div>

        <!--名片异网投递配置-->
        <div ng-if="isSuperManager && enterpriseType == 5 && isZYZQ && showMpPlatformConfig && menuType == 1"
             class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p class="form-horizontal-tit service-config" ng-bind="'MP_PLATFORM_CONFIG'|translate"></p>
            <form class="form-horizontal form-group bgWhite">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'MOBILE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch4">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initAllInfo.platformStatus.mobileStatus=='1'" class="switch-info"
                              ng-bind="'PLATFORM_MOBILE_MSG'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'UNICOM'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':initAllInfo.platformStatus.unicomStatus=='0'}
                              ng-click="changeStatus(4)">
							<icon class="switch-icon"></icon>
						</span>
<!--                        <span class='offtip' class="switch-info" ng-bind="'PLATFORM_MOBILE_MSG_CLOSE'|translate"></span>-->
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'TELECOM'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch6" ng-class={'off':initAllInfo.platformStatus.telecomStatus=='0'}
                              ng-click="changeStatus(5)">
							<icon class="switch-icon"></icon>
						</span>
<!--                        <span class='offtip' class="switch-info" ng-bind="'PLATFORM_MOBILE_MSG_CLOSE'|translate"></span>-->
                    </div>
                </div>
            </form>
        </div>

        <!--热线彩印异网投递配置-->
        <div ng-if="isSuperManager && enterpriseType == 5 && isZYZQ  && menuType == 1"
             class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p class="form-horizontal-tit service-config" ng-bind="'RX_PLATFORM_CONFIG'|translate"></p>
            <form class="form-horizontal form-group bgWhite">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'MOBILE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch4" ng-class={'off':initAllInfo.rxPlatformStatus.mobileStatus=='0'}>
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="initAllInfo.rxPlatformStatus.mobileStatus=='1'"
                              class="switch-info"
                              ng-bind="'PLATFORM_MOBILE_MSG'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'UNICOM'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':initAllInfo.rxPlatformStatus.unicomStatus=='0'}
                              ng-click="changeStatus(6)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip'
                              ng-show="initAllInfo.rxPlatformStatus.unicomStatus=='1'||parentUnicomStatus=='0'"
                              class="switch-info"
                              ng-bind="(parentUnicomStatus=='0'?'PLATFORM_UNICOM_NOTPERMIT':'PLATFORM_UNICOM_MSG')|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'TELECOM'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch6" ng-class={'off':initAllInfo.rxPlatformStatus.telecomStatus=='0'}
                              ng-click="changeStatus(7)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip'
                              ng-show="initAllInfo.rxPlatformStatus.telecomStatus=='1'||parentTelecomStatus=='0'"
                              class="switch-info"
                              ng-bind="(parentTelecomStatus=='0'?'PLATFORM_TELECOM_NOTPERMIT':'PLATFORM_TELECOM_MSG')|translate"></span>
                    </div>
                </div>
            </form>
        </div>
		<!-- 异网审核结果回调设置 -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
             ng-show="isSuperManager && (enterpriseType == 2 || enterpriseType == 1 || (enterpriseType == 5 && !isZYZQ)) && menuType == 1">
            <p class="form-horizontal-tit service-config">异网审核结果回调设置</p>
            <form class="form-horizontal form-group col-lg-12 bgWhite" ng-show="isSuperManager">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label>回调设置：</label>
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
					<span class="switch switch5" ng-class={'off':initAllInfo.ywApproveCallback=='0'}
                          ng-click="changeStatus(14)">
						<icon class="switch-icon"></icon>
					</span>
                        <span class="switch-info">默认为关闭，开启后接口支持异网审核结果回调</span>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- 主叫挂机短信业务配置 -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
             ng-show="isSuperManager && (enterpriseType == 2 || enterpriseType == 3 || (enterpriseType == 5 && !isZYZQ && !isYDY)) && menuType == 1">
            <p class="form-horizontal-tit service-config">主叫挂机短信业务配置</p>
            <form class="form-horizontal form-group col-lg-12 bgWhite" ng-show="isSuperManager">
                                
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'CALLED_CONTENTAUDIT_GJDX2'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
					<span class="switch switch5">
						<icon class="switch-icon"></icon>
					</span>
                        <span class='offtip' 
                              class="switch-info"
                              ng-bind="'CALLED_CONTENTAUDIT_GJDX2_MSG'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'CALLER_CONTENTAUDIT_GJDX2'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class={'off':hangupTypeStatusSwitch}
                              ng-click="changeStatus(51)">
							<icon class="switch-icon"></icon>
						</span>
						<span class="switch-info" ng-show="hangupTypeStatus=='-1'">代理商未开启主叫挂机短信开关，子企业无权开启</span>
                    </div>
                </div>
            </form>
        </div>
        <!-- 行业挂机短信业务配置  -->
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"
             ng-show="isSuperManager && (enterpriseType == 2 || enterpriseType == 3 || (enterpriseType == 5 && !isZYZQ && !isYDY)) && menuType == 1">
            <p class="form-horizontal-tit service-config">行业挂机短信业务配置</p>
            <form class="form-horizontal form-group col-lg-12 bgWhite" ng-show="isSuperManager">

                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label ng-bind="'HY_CALLED_CONTENTAUDIT_GJDX2'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
                       	<span class="switch switch5" ng-class={'off':hyHangupSmsTypeStatusSwitch}
                              ng-click="changeStatus(52)">
							<icon class="switch-icon"></icon>
						</span>
                    </div>
                </div>
         </form>
        </div>

        <!-- 数智反诈通知方式设置-->
        <div ng-show="initAllInfo.SZFZStatus && menuType == 1" class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p class="form-horizontal-tit service-config" >数智反诈通知方式设置</p>
            <form class="form-horizontal form-group bgWhite">
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label>短信</label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
                        <input type="checkbox" ng-if="initAllInfo.FZDXStatus" ng-checked="1" ng-click="changeDXStatus()">
                        <input type="checkbox" ng-if="!initAllInfo.FZDXStatus" ng-click="changeDXStatus()">
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top: 11px;">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label>闪信</label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
                        <input type="checkbox" ng-if="initAllInfo.FZSXStatus"  ng-checked="1" ng-click="changeSXStatus()">
                        <input type="checkbox" ng-if="!initAllInfo.FZSXStatus" ng-click="changeSXStatus()">
                    </div>
                </div>
            </form>
        </div>

        <!--热线彩印业务开关设置-->
        <div ng-if="isSuperManager && enterpriseType == 5 && isZYZQ && showRxPlatformConfig && menuType == 1 && getFunctionDauth('gn000')"
             class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p class="form-horizontal-tit service-config" ng-bind="'RX_BUSSINESS_CONFIG'|translate"></p>
            <form class="form-horizontal form-group bgWhite">
                <div class="form-inline">
                    <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="text-align:right">
                        <label ng-bind="'CONTENTAUDIT_HOTLINE_PROVINCE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch4" ng-class={'off':false}>
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-show="true"
                              class="switch-info"
                              ng-bind="'RX_BUSSINESS_MSG'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="text-align:right">
                        <label ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class="{'off':initAllInfo.RXSwitchRXStatus=='0'}"
                              ng-click="changeStatus(15)">
							<icon class="switch-icon"></icon>
						</span>
                    </div>
                </div>
            </form>
        </div>

        <!--热线彩印超套控制开关设置-->
        <div ng-if="isSuperManager && enterpriseType == 5 && isZYZQ && menuType == 1 && getFunctionDauth('gn001')"
             class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <p class="form-horizontal-tit service-config" ng-bind="'RX_BEYOND_CONTROL_CONFIG'|translate"></p>
            <form class="form-horizontal form-group bgWhite">
                <div class="form-inline">
                    <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="text-align:right">
                        <label ng-bind="'CONTENTAUDIT_HOTLINE_PROVINCE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class="{'off':initAllInfo.RXBeyondSwitchRXSFStatus=='1'}"
                              ng-click="changeStatus(16)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-bind="'SWITCH_MSG_CLOSE'|translate"></span>
                    </div>
                </div>
                <div class="form-inline">
                    <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="text-align:right">
                        <label ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></label>：
                    </div>
                    <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" style="margin-top: 11px;">
						<span class="switch switch5" ng-class="{'off':initAllInfo.RXBeyondSwitchRXStatus=='1'}"
                              ng-click="changeStatus(17)">
							<icon class="switch-icon"></icon>
						</span>
                        <span class='offtip' ng-bind="'SWITCH_MSG_CLOSE'|translate"></span>
                    </div>
                </div>
            </form>
        </div>

        <!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog"
             aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button ng-click="refresh()" type="button" class="close" data-dismiss="modal"
                                aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <p style='font-size: 16px;color:#383838'>
                                <span ng-show="tip=='1030100000'" ng-bind="'COMMON_SAVE'|translate"></span>{{tip|translate}}
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button ng-click="refresh()" type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                                ng-bind="'COMMON_OK'|translate"></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group col-lg-12 col-md-12 col-sm-12 col-xs-12">
            <div class="order-btn" style="margin: 20px 50px; margin-left: 28%;">
                <button style="margin-right: 30px;" type="submit" class="btn btn-primary search-btn"
                        ng-disabled="(orderBase1.$invalid&&(isSuperManager|| isProvincial))||(orderBase2.$invalid&&(isSuperManager|| isProvincial))||
											(orderBase3.$invalid&&(isSuperManager|| isProvincial))
											||limitError.tz_sms_limit
											||limitError.tz_flash_limit
											||limitError.tz_cx_limit
											||limitError.tz_ussd_limit
											||limitError.tz_mms_limit
                       "ng-click="UpdateRuleList()"
                        ng-bind="'COMMON_SAVE'|translate">
                </button>
                <!-- <button style="margin:40px 20px;" type="submit" class="btn" ng-click="returnUp()">返回</button> -->
            </div>
        </div>
    </div>
    <!-- 业务未开通 -->
    <div ng-show="businessStatus ==1" class="modal fade bs-example-modal-sm" id="Modalisaengt" tabindex="-1"
         role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center" style="text-align: center;">
                        <p style='font-size: 18px;color:#383838'>
                            业务未开通
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>


</div>
</body>

</html>