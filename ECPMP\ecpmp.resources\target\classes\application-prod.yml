spring:
  http:
    encoding:
      force: true
      charset: UTF-8
    multipart:
      enabled: true
      max-file-size: 50MB
  freemarker:
    allow-request-override: false
    allow-session-override: false
    cache: false
    check-template-location: true
    charset: UTF-8
    content-type: text/html; charset=utf-8
    prefer-file-system-access: false
    expose-request-attributes: false
    expose-session-attributes: false
    expose-spring-macro-helpers: false
    suffix: .html
    template-loader-path: classpath:/templates
    request-context-attribute: request
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 100000
        readTimeout: 100000
hystrix:
 command:
  default:
   execution:
    isolation:
     thread:
      timeoutInMilliseconds: 20000
     strategy: SEMAPHORE
ribbon:
  ReadTimeout: 20000
  ConnectTimeout: 5000
server:
  tomcat:
    max-connections: 10000
  connection-timeout: 20000


    
      
 