<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DealEnContractOrdMapper">
    <resultMap id="dealEnContractOrdWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DealEnContractOrdWrapper">
        <result property="contentID" column="contentID" javaType="java.lang.Long" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
        <result property="execMonth" column="execMonth" javaType="java.lang.String" />
        <result property="execStatus" column="execStatus" javaType="java.lang.Integer" />
        <result property="failCount" column="failCount" javaType="java.lang.Integer" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="contRuleID" column="contRuleID" javaType="java.lang.String" />
        <result property="parentID" column="parentID" javaType="java.lang.Long" />
        <result property="servType" column="servType" javaType="java.lang.Integer" />
    </resultMap>

    <select id="queryDealEnContractOrd" resultMap="dealEnContractOrdWrapper">
		select
		temp.*,t.subServType,t.contRuleID,t.enterpriseName,t.servType from
		ecpm_t_dealEnContractOrdQuotaTemp temp,ecpm_t_content t where
		(temp.execStatus = 0 or temp.execStatus = 2) and temp.failCount <![CDATA[ <= ]]>
		#{failCount} and temp.execMonth = #{execMonth} and temp.contentID =
		t.id
	</select>

    <insert id="insertContentToTemp">
		insert into
		ecpm_t_dealEnContractOrdQuotaTemp(contentID,enterpriseID,execMonth,execStatus,failCount,createTime,parentID)
		select t.ID,t.enterpriseID, #{execMonth},
		#{execStatus},#{failCount},#{createTime}, t.parentID from
		ecpm_t_content t where
		t.thirdpartyType = 0 and (t.servType = 1 or t.servType = 5) and t.chargeType = 2 and
		t.approveStatus = 3 and t.status != 2 and t.status != 3 and t.parentID IS NULL and t.deductTime <![CDATA[ < ]]> #{deductTime}
		on duplicate key
		update updatetime = now()
	</insert>

    <update id="updateContentToTemp">
		update ecpm_t_dealEnContractOrdQuotaTemp set execMonth = #{execMonth},execStatus = #{execStatus},failCount =
		#{failCount},createTime = #{createTime},updateTime = null where execMonth != #{execMonth} and contentID in
		(select t.ID from
		ecpm_t_content t where
		t.thirdpartyType = 0 and t.servType = 1 and t.chargeType = 2 and
		t.approveStatus = 3 and t.status != 2 and t.status != 3 and t.parentID IS NULL and t.deductTime <![CDATA[ < ]]> #{deductTime})
	</update>

    <update id="updateDealEnContractOrdStatus">
        update ecpm_t_dealEnContractOrdQuotaTemp set updateTime = now(),
        <if test="failCount != null">
            failCount=failcount+1,
        </if>
        execStatus = #{execStatus} where contentId in
        <foreach collection="contentIDList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

</mapper>