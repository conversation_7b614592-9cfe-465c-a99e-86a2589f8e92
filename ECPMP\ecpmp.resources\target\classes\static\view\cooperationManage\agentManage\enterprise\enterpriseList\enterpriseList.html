<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>代理商信息管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript" 
		src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="enterpriseListCtrl.js"></script>

</head>

<body ng-app="myApp" class="body-min-width">
	<div class="cooperation-manage container-fluid" ng-controller="EnterpriselistCtrl" ng-init="init();" ng-cloak>
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'COMMON_COOPERATION'|translate"> </span> > 
			<span class="second-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span></div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap">企业名称</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<input type="text" id="enterpriseName" class="form-control" 
							placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}" 
							ng-model="enterpriseName">
					</div>

					<label for="organizationID" class="col-lg-2 col-md-2 col-sm-2 col-xs-2 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_ORGANIZATIONID2'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="organizationID" class="form-control" 
							placeholder="{{'ENTERPRISE_PLEASEINPUTORGANIZATIONID2'|translate}}" 
							ng-model="organizationID">
					</div>
					
					<label for="province" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'SPOKES_LOCATION'|translate"></label>
								 
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.provinceName for x in provinceList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					
					<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
						<button type="submit" class="btn search-btn" ng-click="enterpriseList()">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<!-- <button type="submit" class="btn add-btn" ng-click="gotoAdd()">
				<icon class="add-iocn"></icon>
				<span ng-bind="'ENTERPRISE_ADDAGENT'|translate"></span>
			</button> -->
		</div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:8%" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></th>
					<th style="width:15%" >企业名称</th>
					<th style="width:10%" ng-bind="'ENTERPRISE_ORGANIZATIONID2'|translate"></th>
					<th style="width:12%" ng-bind="'ENTERPRISE_CUSTID2'|translate"></th>
					<th style="width:15%" ng-bind="'COMMON_ACCOUNTNAME'|translate"></th>
					<th style="width:13%" ng-bind="'SPOKES_LOCATION'|translate"></th>
					<th style="width:12%" ng-bind="'COMMON_CREATETIME'|translate"></th>
					<th style="width:10%" ng-bind="'COMMON_OPERATE'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in queryEnterpriseList">
					<td><span title="{{item.id}}">{{item.id}}</span></td>
					<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
					<td style="min-width: 100px"><span title="{{item.organizationID}}">{{item.organizationID}}</span></td>
					<td><span title="{{item.custID}}">{{item.custID}}</span></td>
					<td><span title="{{item.accountInfo.accountName}}">{{item.accountInfo.accountName||""}}</span></td>
					<td title="{{provinceList2[item.provinceID]?provinceList2[item.provinceID]:''}}">
                        	{{provinceList2[item.provinceID]?provinceList2[item.provinceID]:''}}</td>
					<td title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</td>
					<td>
						<div class="handle">
							<ul>
								<!-- <li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'COMMON_WATCH'|translate"></span>
								</li> -->
								<li class="edit" ng-click="toEdit(item)">
									<icon class="edit-icon"></icon>
									<span ng-bind="'SETTINGS'|translate"></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="queryEnterpriseList.length<=0">
					<td style="text-align:center" colspan="8" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="enterpriseList('justPage')"></ptl-page>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate">
						</button>
					</div>
				</div>
			</div>
		</div>


	</div>
</body>

</html>