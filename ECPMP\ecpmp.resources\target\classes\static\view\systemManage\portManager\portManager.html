<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>端口拓展码管理</title>
    <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
    <link href="../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../css/searchList.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
    <link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
     <script type="text/javascript" src="../../../service/utils/service-common.js"></script>
    <link href="../../../css/bootstrap-datetimepicker.css" rel="stylesheet" type="text/css" />
    <script type="text/javascript" src="../../../frameworkJs/bootstrap-datetimepicker.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap-datetimepicker.zh-CN.js"></script>
    <script type="text/javascript" src="portManagerCtrl.js"></script>
    <style>
        .modal-footer {
            text-align: center;
        }

        .cond-div {
            min-width: 240px;
        }

        .addMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../assets/images/add.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
        }

        .deleteMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../assets/images/delete.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
        }
    </style>

</head>

<body ng-app="myApp" ng-controller="administratorController" ng-init="init()" class="body-min-width">
<div class="cooperation-manage">
    <div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate">
			</span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'PORTMANAGER'|translate"></span>
    </div>
    <div class="cooperation-search">
        <form class="form-horizontal">
            <div class="form-group">
                <label for="contentName" class="col-xs-1 control-label" ng-bind="'代理商名称'|translate"></label>

                <div class="cond-div col-xs-2">
                    <input type="text" autocomplete="off" class="form-control" id="contentName"
                           placeholder="{{'代理商名称'|translate}}" ng-model="parentEnterpriseName">
                </div>

                <label for="characterName" class="col-xs-1 control-label" ng-bind="'子企业名称'|translate"></label>
                <div class="cond-div col-xs-2">
                    <input type="text" autocomplete="off" class="form-control" id="characterName"
                           placeholder="{{'子企业名称'|translate}}" ng-model="enterpriseName">
                </div>

                <label for="portName" class="col-xs-1 control-label" ng-bind="'端口号'|translate"></label>
                <div class="cond-div col-xs-2">
                    <input type="number" autocomplete="off" class="form-control" id="portName"
                           placeholder="{{'端口号'|translate}}" ng-model="portName">
                </div>
                <div class="cond-div col-xs-2" style="float: right">
                    <button type="submit" class="btn search-btn" ng-click="queryPortList()" style="margin-left: 20px">
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
            </div>

        </form>
    </div>
    <div class="add-table">
        <button class="btn add-btn" ng-click="export()">
            <icon class="add-iocn"></icon>
            <span ng-bind="'DETAIL_EXPORT'|translate"></span>
        </button>
    </div>
</div>
<div class="coorPeration-table" style="width:100%;">
    <table class="table table-striped table-hover">
        <thead>
        <tr>
            <th style="width: 85px;" ng-bind="'ENTERPRISE_AGENTNAME'|translate"></th>
            <th style="width: 85px;" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></th>
            <th style="width: 100px;">热线短信端口号</th>
            <th style="width: 100px;">通知短信端口号</th>
            <th style="width: 100px;">通知闪信端口号</th>
            <th style="width: 100px;">通知彩信、增彩端口号</th>
            <th style="width: 138px;" ng-bind="'COMMON_OPERATE'|translate"></th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="item in enterprisePortList">
            <td><span title="{{item.parentEnterpriseName}}">{{item.parentEnterpriseName}}</span></td>
            <td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
            <td><span title="{{item.rxport}}">{{item.rxport}}</span></td>
            <td><span title="{{item.tzsmsport}}">{{item.tzsmsport}}</span></td>
            <td><span title="{{item.tzflashPort}}">{{item.tzflashPort}}</span></td>
            <td><span title="{{item.tzmmsport}}">{{item.tzmmsport}}</span></td>
            <td class="coorPeration-table-a">
                <div class="handle">
                    <ul>
                        <li style="color: #705de1;" class="" ng-click="updatePort(item,'rxPortList')"
                            ng-show="item.rxport != null && item.rxport != ''">
                            <!--<icon class="delete-icon"></icon>-->
                            <span>编辑热线端口</span>
                        </li>
                        <!--<li class="" ng-click="detailPort(item,'rxPortList')"-->
                            <!--ng-show="item.rxport != null && item.rxport != ''">-->
                            <!--&lt;!&ndash;<icon class="delete-icon"></icon>&ndash;&gt;-->
                            <!--<span>查看热线端口</span>-->
                        <!--</li>-->
                        <li style="color: #705de1;" class="" ng-click="addPort(item,'rxPortList')"
                            ng-show="item.rxport == null || item.rxport == ''">
                            <!--<icon class="delete-icon"></icon>-->
                            <span>新增热线端口</span>
                        </li>
                        <li style="color: #705de1;" class="" ng-click="updatePort(item,'tzsmsportList')"
                            ng-show="!((item.tzsmsport == null || item.tzsmsport == '')
                            &&(item.tzflashPort == null || item.tzflashPort == '')
                            &&(item.tzmmsport == null || item.tzmmsport == ''))">
                            <!--<icon class="delete-icon"></icon>-->
                            <span>编辑通知端口</span>
                        </li>
                        <!--<li class="" ng-click="detailPort(item,'tzsmsportList')"-->
                            <!--ng-show="item.tzsmsport != null && item.tzsmsport != ''">-->
                            <!--&lt;!&ndash;<icon class="delete-icon"></icon>&ndash;&gt;-->
                            <!--<span>查看通知端口</span>-->
                        <!--</li>-->
                        <li style="color: #705de1;" class="" ng-click="addPort(item,'tzsmsportList')"
                            ng-show="(item.tzsmsport == null || item.tzsmsport == '')
                            &&(item.tzflashPort == null || item.tzflashPort == '')
                            &&(item.tzmmsport == null || item.tzmmsport == '')">
                            <!--<icon class="delete-icon"></icon>-->
                            <span>新增通知端口</span>
                        </li>
                    </ul>
                </div>
            </td>
        </tr>
        <tr ng-show="enterprisePortList===null||enterprisePortList.length===0">
            <td style="text-align:center" colspan="7">暂无数据</td>
        </tr>
        </tbody>
    </table>
</div>
<div>
    <ptl-page tableId="0" change="queryPortList('justPage')"></ptl-page>
</div>

<!--新增编辑弹窗-->
<div class="modal fade" id="savePort" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                        ng-click="closeAdd()"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" ng-bind="optionType"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" name="addForm">
                    <div class="form-group">
                        <div class="row" ng-show="detailType == 1" style="height: 34px;">
                            <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                   style="width: 102px;padding-top:8px;text-align:left;padding-right: 1px">
                                <span>短信端口号:</span>
                            </label>
                            <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8" ng-repeat="item in saveObject.rxPortList"
                                 style="margin-top: 5px;padding-left: 1px;width:77%; height: 52px;">
                                <div ng-show="$index != 0" style="width: 102px;float: left;height: 40px;"></div>

                                <select style="width:116px;float: left;margin-right: 5px;"
                                        ng-change="checkMsisdnList('rxPortList')"
                                        class="form-control ng-pristine ng-valid ng-touched" name="province"
                                        ng-model="item.basePort">
                                    <option ng-repeat="item in basePortList" ng-value="item" class="">{{item}}</option>

                                </select>
                                <input style="float: left;width: 41%;margin-right: 10px" type="text"
                                       class="form-control"
                                       ng-model="item.port" name="addHotlineNo"
                                       ng-blur="checkMsisdnList('rxPortList')"
                                       placeholder="请输入0-12位数字"
                                       />
                                <span style="color:red;line-height: 34px;display: block;float: left"
                                      ng-show="item.$invalid || item.$repeat">
                                        <span ng-show="item.$invalid"
                                              ng-bind="'HOTLINE_PORTDESC012'|translate"></span>
                                        <span ng-show="item.$repeat"
                                              ng-bind="'HOTLINE_REPEAT'|translate"></span>
                                    </span>
                                <icon ng-show="$index == 0" ng-click="addMsisdn('rxPortList')" class="addMsinsdn">
                                </icon>
                                <icon ng-show="$index != 0" ng-click="deleteMsinsdn('rxPortList',$index)"
                                      class="deleteMsinsdn">
                                </icon>
                            </div>

                        </div>


                        <div class="row" ng-show="detailType == 2" style="height: 34px;">
                            <div style="float: left">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                       style="width: 102px;padding-top:8px;text-align:left;padding-right: 1px">
                                    <span>短信端口号:</span>
                                </label>
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8"
                                     ng-repeat="item in saveObject.tzsmsportList"
                                     style="margin-top: 5px;padding-left: 1px;width:77%; height: 52px;">
                                    <div ng-show="$index != 0" style="width: 102px;float: left;height: 40px;"></div>
                                    <select style="width:115px;float: left;margin-right: 5px;"
                                            ng-change="checkMsisdnList('tzsmsportList')"
                                            class="form-control ng-pristine ng-valid ng-touched" name="province"
                                            ng-model="item.basePort">
                                        <option ng-repeat="item in basePortList" ng-value="item" class="">{{item}}</option>
                                    </select>
                                    <input style="float: left;width: 41%;margin-right: 10px" type="text"
                                           class="form-control"
                                           ng-model="item.port" name="addHotlineNo"
                                           ng-blur="checkMsisdnList('tzsmsportList')"
                                           placeholder="请输入1-11位数字"
                                           />
                                    <span style="color:red;line-height: 34px;display: block;float: left"
                                          ng-show="item.$invalid || item.$repeat">
                                        <span ng-show="item.$invalid"
                                              ng-bind="'HOTLINE_PORTDESC'|translate"></span>
                                        <span ng-show="item.$repeat"
                                              ng-bind="'HOTLINE_REPEAT'|translate"></span>
                                    </span>
                                    <icon ng-show="$index == 0" ng-click="addMsisdn('tzsmsportList')"
                                          class="addMsinsdn">
                                    </icon>
                                    <icon ng-show="$index != 0" ng-click="deleteMsinsdn('tzsmsportList',$index)"
                                          class="deleteMsinsdn">
                                    </icon>
                                </div>
                            </div>
                            <div style="float: left">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                       style="width: 102px;padding-top:8px;text-align:left;padding-right: 1px">
                                    <span>闪信端口号:</span>
                                </label>
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8"
                                     ng-repeat="item in saveObject.tzflashPortList"
                                     style="margin-top: 5px;padding-left: 1px;width:77%; height: 52px;">
                                    <div ng-show="$index != 0" style="width: 102px;float: left;height: 40px;"></div>
                                    <select style="width:115px;float: left;margin-right: 5px;"
                                            ng-change="checkMsisdnList('tzflashPortList')"
                                            class="form-control ng-pristine ng-valid ng-touched" name="province"
                                            ng-model="item.basePort">
                                        <option ng-repeat="item in basePortList" ng-value="item" class="">{{item}}</option>
                                    </select>
                                    <input style="float: left;width: 41%;margin-right: 10px" type="text"
                                           class="form-control"
                                           ng-model="item.port" name="addHotlineNo"
                                           ng-blur="checkMsisdnList('tzflashPortList')"
                                           placeholder="请输入1-11位数字" />
                                    <span style="color:red;line-height: 34px;display: block;float: left"
                                          ng-show="item.$invalid || item.$repeat">
                                        <span ng-show="item.$invalid"
                                              ng-bind="'HOTLINE_PORTDESC'|translate"></span>
                                        <span ng-show="item.$repeat"
                                              ng-bind="'HOTLINE_REPEAT'|translate"></span>
                                    </span>
                                    <icon ng-show="$index == 0" ng-click="addMsisdn('tzflashPortList')"
                                          class="addMsinsdn">
                                    </icon>
                                    <icon ng-show="$index != 0" ng-click="deleteMsinsdn('tzflashPortList',$index)"
                                          class="deleteMsinsdn">
                                    </icon>
                                </div>
                            </div>
                            <div style="float: left">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                       style="width: 102px;padding-top:8px;text-align:left;padding-right: 1px">
                                    <span>彩信、增彩端口号:</span>
                                </label>
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8"
                                     ng-repeat="item in saveObject.tzmmsportList"
                                     style="margin-top: 5px;padding-left: 1px;width:77%; height: 52px;">
                                    <div ng-show="$index != 0" style="width: 102px;float: left;height: 40px;"></div>
                                    <select style="width:115px;float: left;margin-right: 5px;"
                                            ng-change="checkMsisdnList('tzmmsportList')"
                                            class="form-control ng-pristine ng-valid ng-touched" name="province"
                                            ng-model="item.basePort">
                                        <option ng-repeat="item in basePortList" ng-value="item" class="">{{item}}</option>
                                    </select>
                                    <input style="float: left;width: 41%;margin-right: 10px" type="text"
                                           class="form-control"
                                           ng-model="item.port" name="addHotlineNo"
                                           ng-blur="checkMsisdnList('tzmmsportList')"
                                           placeholder="请输入1-11位数字" />
                                    <span style="color:red;line-height: 34px;display: block;float: left"
                                          ng-show="item.$invalid || item.$repeat">
                                        <span ng-show="item.$invalid"
                                              ng-bind="'HOTLINE_PORTDESC'|translate"></span>
                                        <span ng-show="item.$repeat"
                                              ng-bind="'HOTLINE_REPEAT'|translate"></span>
                                    </span>
                                    <icon ng-show="$index == 0" ng-click="addMsisdn('tzmmsportList')"
                                          class="addMsinsdn">
                                    </icon>
                                    <icon ng-show="$index != 0" ng-click="deleteMsinsdn('tzmmsportList',$index)"
                                          class="deleteMsinsdn">
                                    </icon>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                                <!--<icon style="color:red">*</icon>-->
                                <span ></span>
                            </label>
                            <div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
                                <label style="	width: 200px;margin-right: 10px;font-size: 15px; text-align: left;">
                                    <input class="" style="width: 20px;height:15px;margin-right: 5px;"
                                           type="checkbox" ng-value="item" value="item" name="contentId" ng-model="saveObject.isSynAll"
                                           autocomplete="off">代理商与子企业同号</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-bind="'COMMON_SAVE'|translate"
                        ng-click="savePort()"
                        ng-disabled="(detailType == 1&&invalid.rxPortList) ||
                                     (detailType == 2&&(invalid.tzmmsportList||invalid.tzflashPortList||invalid.tzsmsportList))


                    "></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-click="closeAdd()" id="addHotlineCancel" style="margin-left: 20px"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!--查看弹窗-->
<div class="modal fade" id="operLogDetail" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:450px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">查看端口号</h4>
            </div>
            <div class="modal-body" style="max-height:300px; overflow: auto;">
                <div class="row" ng-show="detailType == 1 ">
                    <label style="margin: 10px;" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <span>短信端口号</span>：
                    </label>
                    <div style="margin: 10px;" class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
                        <div ng-repeat="item in detailItem.rxPortList">
                            <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                <span ng-bind="item.port"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div ng-show="detailType == 2 ">
                    <div class="row">
                        <label style="margin: 10px;" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>短信端口号</span>：
                        </label>
                        <div style="margin: 10px;" class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
                            <div ng-repeat="item in detailItem.tzsmsportList">
                                <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                    <span ng-bind="item.port"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <label style="margin: 10px;" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>闪信端口号</span>：
                        </label>
                        <div style="margin: 10px;" class="col-lg-6 col-xs-6 col-sm-6 col-md-6">

                            <div ng-repeat="item in detailItem.tzflashPortList">
                                <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                    <span ng-bind="item.port"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <label style="margin: 10px;" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>彩信、增彩端口号</span>：
                        </label>
                        <div style="margin: 10px;" class="col-lg-6 col-xs-6 col-sm-6 col-md-6">

                            <div ng-repeat="item in detailItem.tzmmsportList">
                                <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                    <span ng-bind="item.port"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="modal-footer" style="text-align: center">
                <button type="submit" class="btn btn-primary search-btn" data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center"><p style='font-size: 16px;color:#383838'>
                    {{tip|translate}}
                </p></div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>
</body>
</html>