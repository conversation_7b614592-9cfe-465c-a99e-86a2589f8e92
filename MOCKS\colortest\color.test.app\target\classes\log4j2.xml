<?xml version="1.0" encoding="UTF-8"?>
<configuration status="OFF" packages="com.huawei.jaguar.color.test.commons.log">
    <properties>
        <!-- 文件输出格式 -->
        <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>
    </properties>

    <appenders>
        <RollingFile name="STD_OUT_REDIRECTOR" filePattern="${sys:app.home}/log/server/server.out.%i"
                     fileName="${sys:app.home}/log/server/server.out">
            <PatternLayout pattern="%m %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="DEBUG_LOG" filePattern="${sys:app.home}/log/debug/debug.log.%i" fileName="${sys:app.home}/log/debug/debug.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%traceid|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="TASK_LOG" filePattern="${sys:app.home}/log/debug/task.log.%i" fileName="${sys:app.home}/log/debug/task.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%traceid|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="Intface_LOG" filePattern="${sys:app.home}/log/debug/interface.log.%i" fileName="${sys:app.home}/log/debug/interface.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%traceid|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="SERVER" filePattern="${sys:app.home}/log/server/server.log.%i" fileName="${sys:app.home}/log/server/server.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <Console name="CONSOLE" target="system_out">
            <PatternLayout pattern="${PATTERN}"/>
        </Console>
    </appenders>

    <loggers>

        <logger name="com.huawei.jaguar.color.test" additivity="false" level="info">
            <appender-ref ref="DEBUG_LOG"/>
            <Filters>
                <LogNameFliter regex=".*Mapper.*" level="debug,info" onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
        </logger>

        <logger name="Runtime@COLORTEST_TASK" additivity="false" level="INFO">
            <appender-ref ref="TASK_LOG"/>
        </logger>

        <logger name="Runtime@interface" additivity="false" level="INFO">
            <appender-ref ref="Intface_LOG"/>
        </logger>

        <logger name="com.huawei" additivity="false" level="info">
            <appender-ref ref="STD_OUT_REDIRECTOR"/>
        </logger>

        <root level="info">
            <appenderref ref="CONSOLE"/>
            <appender-ref ref="SERVER"/>
        </root>

    </loggers>

</configuration>


