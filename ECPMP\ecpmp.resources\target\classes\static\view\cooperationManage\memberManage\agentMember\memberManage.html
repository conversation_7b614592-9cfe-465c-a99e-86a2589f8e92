<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>代理商成员管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
	<link href="../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>

	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="memberManage.js"></script>
	<style>
	/* media for adjustable search-table width  */
	@media (max-width: 1366px){
  	.control-label{
		width: 86px;
		padding-left: 0;
		padding-right: 0;
	}
}
	</style>
</head>

<body ng-app="myApp" class="body-min-width-new">
	<div class="cooperation-manage container-fluid"  ng-controller="EnterpriselistCtrl" ng-init="init();" ng-cloak>
		<div ng-show="isSuperManager&&enterpriseType =='3'" class="cooperation-head"><span class="frist-tab" > </span> 成员管理 &gt; <span class="second-tab">代理商成员管理</span></div>
		<div ng-show="isSuperManager&&enterpriseType !='3'" class="cooperation-head"><span class="frist-tab" > </span> 成员管理 &gt; <span class="second-tab">分省成员管理</span></div>
		<div ng-show="!isSuperManager&&enterpriseType =='3'" class="cooperation-head"><span class="frist-tab" > </span> 业务管理 &gt; <span class="second-tab">代理商成员管理</span></div>
		<div ng-show="!isSuperManager&&enterpriseType !='3'" class="cooperation-head"><span class="frist-tab" > </span> 成员管理 &gt; <span class="second-tab">分省成员管理</span></div>

		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group" style="min-width: 1000px;">
					<label ng-show="isSuperManager" for="enterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate">企业编号</label>

					<div ng-show="isSuperManager" class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<input type="text" id="enterpriseID" class="form-control" placeholder="请输入企业编号" ng-model="enterpriseID">
					</div>

					<label ng-show="isSuperManager" for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap;width:9%;">企业名称</label>

					<div ng-show="isSuperManager"  class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<input type="text" id="enterpriseName" class="form-control" placeholder="请输入企业名称" ng-model="enterpriseName">
					</div>

					<label ng-show="enterpriseType =='3'" for="secondEnterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;" ng-bind="'ENTERPRISE_ENTERPRISEID1'|translate">子企业编号</label>

					<div ng-show="enterpriseType =='3'" class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width: 24%;">
						<input type="text" id="secondEnterpriseID" class="form-control" placeholder="请输入子企业编号" ng-model="secondEnterpriseID">
					</div>

					<label ng-show="enterpriseType =='3'" for="secondEnterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;">子企业名称</label>

					<div ng-show="enterpriseType =='3'" class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<input type="text" id="secondEnterpriseName" class="form-control" placeholder="子企业名称" ng-model="secondEnterpriseName">
					</div>

					<label for="organizationName" class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap;width:9%;">分组名称</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<input type="text" id="organizationName" class="form-control" placeholder="请输入分组名称"
									 ng-model="organizationName">
					</div>

					<label ng-show="enterpriseType !='3'" for="serviceType" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;">业务类型</label>

					<div ng-show="enterpriseType !='3'" class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<select class="form-control" name="serviceType" ng-model="serviceTypeObject"
								ng-options="x.value for x in serviceSelectMap">
							<option value="">不限</option>
						</select>
					</div>

					<label for="subServiceType" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap;width:9%;">子业务类型</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width: 24%;">
						<select class="form-control" name="subServiceType" ng-model="subServiceTypeObject"
										ng-options="x.value for x in subserviceSelectMap">
							<option value="">不限</option>
						</select>
					</div>
					<label for="memberNum" class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;">成员号码</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<input style="min-width:160px;" type="text" id="memberNum" class="form-control" placeholder="请输入成员号码"
							   ng-model="memberNum">
					</div>
					
					<label for="subscribeStatus" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;" ng-show="enterpriseType =='5'">渠道</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="enterpriseType =='5'">
						<select class="form-control" name="channel" ng-model="channelObject"
								ng-options="x.value for x in channelSelectMap">
							<option value="" ng-show="!isProvincial">不限</option>
						</select>
					</div>
					
					<label for="province" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;" ng-bind="'COMMON_PROVINCE'|translate">省份</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="(isProvincial || loginRoleType=='superrManager') && enterpriseType =='5'">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.provinceName for x in provinceList"
										ng-change="changeSelectedProvince(selectedProvince)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-show="provincialIsNull"></option>
						</select>
					</div>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="loginRoleType=='normalMangager' && enterpriseType =='5'">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.authName for x in provinceList"
										ng-change="changeSelectedProvince(selectedProvince)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="enterpriseType =='3'">
						<select class="form-control" name="province" ng-model="provinceID"
								ng-options="x.provinceID as x.provinceName for x in provinceList" 
								ng-change="changeSelectedProvinceID(provinceID)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					
					<label for="province" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;" ng-bind="'COMMON_CITY'|translate">地市</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="(isProvincial || loginRoleType=='superrManager') && enterpriseType =='5'">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity"
										ng-options="x.cityName for x in subCityList"
										ng-change="changeSelectedCity2(selectedCity)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-show="cityIsNull"></option>
						</select>
					</div>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="loginRoleType=='normalMangager' && enterpriseType =='5'">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity"
										ng-options="x.authName for x in subCityList"
										ng-change="changeSelectedCity2(selectedCity)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="enterpriseType =='3'">
						<select class="form-control"
							name="city" ng-model="selectedCity"
							ng-options="x as x.cityName for x in subCityList" 
							ng-if="!provinceID || provinceID =='000'">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
						<select class="form-control"
							name="city" ng-model="selectedCity"
							ng-change="changeSelectedCity(selectedCity.cityID)"
							ng-options="x as x.cityName for x in subCityList" 
							ng-if="provinceID && provinceID !='000'">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>

					<label for="selectedCounty" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;" ng-bind="'COMMON_COUNTY'|translate" ng-show="showCountyFlag">区县</label>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="(isProvincial || loginRoleType=='superrManager') && enterpriseType =='5'&&showCountyFlag">
						<select class="form-control" name="county" id="selectedCounty" ng-model="selectedCounty"
								ng-options="x.countyName for x in subCountyList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;" ng-show="loginRoleType=='normalMangager' && enterpriseType =='5'&&showCountyFlag">
						<select class="form-control" name="county" id="selectedCounty" ng-model="selectedCounty"
								ng-options="x.countyName for x in subCountyList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>

					<label for="subscribeStatus" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;">订购状态</label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<select class="form-control" name="subscribeStatus" ng-model="subscribeStatusObject"
								ng-options="x.value for x in statusSelectMap">
							<option value="">不限</option>
						</select>
					</div>
					<label ng-show="enterpriseType !='3'" for="msisdnType" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap;width:9%;">号码类型</label>

					<div ng-show="enterpriseType !='3'" class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width:24%;">
						<select class="form-control" name="msisdnType" ng-model="msisdnTypeObject"
								ng-options="x.value for x in msisSelectMap">
							<option value="">不限</option>
						</select>
					</div>
					
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-left: 40px;">
						<button type="submit" class="btn search-btn" ng-click="enterpriseList()">
							<icon class="search-iocn"></icon>
							搜索
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="deleteMember()" ng-disabled="selectedList.length==0">
				<icon class="add-iocn"></icon>
				批量删除成员
			</button>
			<button type="submit" class="btn add-btn" ng-click="exportMember()">
				<icon class="add-iocn"></icon>
				成员导出
			</button>
		</div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="padding-left:10px;width: 2%;"><input type="checkbox" ng-model="allChoose" ng-click="ifSelected()" id="allChoose"></th>
					<th ng-show="isSuperManager" style="width:6%">企业编号</th>
					<th ng-show="isSuperManager" style="width:6%">企业名称</th>
					<th ng-show="enterpriseType =='3'" style="width:8%">子企业编号</th>
					<th ng-show="enterpriseType =='3'" style="width:8%">子企业名称</th>
					<th ng-show="enterpriseType =='3'" style="width:4%">省份</th>
					<th ng-show="enterpriseType =='3'" style="width:4%">地市</th>
					<th ng-show="enterpriseType =='3'" style="width:10%">分组名称</th>
					<th ng-show="enterpriseType =='5'" style="width:4%">省份</th>
					<th ng-show="enterpriseType =='5'" style="width:4%">地市</th>
					<th ng-show="enterpriseType =='5'&&isSuperManager" style="width:4%">区县</th>
					<th ng-show="enterpriseType =='5'" style="width:6%">渠道</th>
					<th ng-show="enterpriseType =='5'" style="width:4%">业务类型</th>
					<th style="width:8%">子业务类型</th>
					<th ng-show="enterpriseType =='5'" style="width:4%">分组名称</th>
					<th style="width:8%">创建时间</th>
					<th style="width:8%">成员名称</th>
					<th style="width:6%">成员号码</th>
					<th ng-show="enterpriseType =='5'" style="width:8%">号码类型</th>
					<th style="width:6%">订购状态</th>
					<th style="display:none;">组织唯一标识</th>
					<th style="width:16%">操作</th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in collectMemberInfoList">
					<td  style="padding-left:10px;width: 2%;"><input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked" ng-disabled="item.status == 0 || item.status == 1 || item.status == 9 || item.status == 11 || item.status == 12 || item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4' ||item.delDisable"></td>
					<td ng-show="isSuperManager"><span title="{{item.parentEnterpriseID==null?item.enterpriseID:item.parentEnterpriseID}}">{{item.parentEnterpriseID==null?item.enterpriseID:item.parentEnterpriseID}}</span></td>
					<td ng-show="isSuperManager"><span title="{{item.parentEnterpriseName==null?item.enterpriseName:item.parentEnterpriseName}}">{{item.parentEnterpriseName==null?item.enterpriseName:item.parentEnterpriseName}}</span></td>
					<td ng-show="enterpriseType =='3'"><span title="{{item.parentEnterpriseID==null?'':item.enterpriseID}}">{{item.parentEnterpriseID==null?'':item.enterpriseID}}</span></td>
					<td ng-show="enterpriseType =='3'"><span title="{{item.parentEnterpriseName==null?'':item.enterpriseName}}">{{item.parentEnterpriseName==null?"":item.enterpriseName}}</span></td>
					<td ng-show="enterpriseType =='3'"><span title="{{item.provinceName}}">{{item.provinceName}}</span></td>
					<td ng-show="enterpriseType =='3'"><span title="{{item.cityName}}">{{item.cityName}}</span></td>
					<td ng-show="enterpriseType =='3'"><span title="{{item.orgName}}">{{item.orgName}}</span></td>
					<td ng-show="enterpriseType =='5'"><span title="{{item.provinceName}}">{{item.provinceName}}</span></td>
					<td ng-show="enterpriseType =='5'"><span title="{{item.cityName}}">{{item.cityName}}</span></td>
					<td ng-show="enterpriseType =='5'&&isSuperManager"><span title="{{countyList2[item.countyID]}}">{{countyList2[item.countyID]}}</span></td>
					<td ng-show="enterpriseType =='5'"><span title="{{changeChannel(item.channel)}}">{{changeChannel(item.channel)}}</span></td>
					<td ng-show="enterpriseType =='5'"><span title="{{changeServiceType(item.servTypes)}}">{{changeServiceType(item.servTypes)}}</span></td>
					<td><span title="{{changeSubServiceType(item.subServiceTypes, item.hangupType)}}">{{changeSubServiceType(item.subServiceTypes, item.hangupType)}}</span></td>
					<td ng-show="enterpriseType =='5'"><span title="{{item.orgName}}">{{item.orgName}}</span></td>
					<td><span>{{item.createTime|newDate|limitTo:10}}</span></td>
					<td><span title="{{item.memberName}}">{{item.memberName}}</span></td>
					<td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
					<td ng-show="enterpriseType =='5'"><span title="{{msisMap[item.msisdnType]}}">{{msisMap[item.msisdnType]}}</span></td>
					<td ng-show="(item.status == 3  || item.status == 13)  && (item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')"><span title="{{unSubscribeStatusMap[item.reserved4]}}">{{unSubscribeStatusMap[item.reserved4]}}</span></td>
					<td ng-hide="(item.status == 3  || item.status == 13)  && (item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')"><span title="{{statusMap[item.status]}}">{{statusMap[item.status]}}</span></td>
					<td style="display:none;"><span title="{{item.orgId}}">{{item.orgId}}</span></td>
					<td>
						<div class="handle">
							<ul>
								<!-- <li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									查看
								</li> -->
								<li
										ng-style="{ color:item.delDisable? '#a297df' : '#7360e2' }"
										ng-show="(enterpriseType =='3'|| item.isAllowMangementMember == '0')
										&&(item.status != 1&&item.status != 0&& item.status != 9 && item.status != 11 && item.status != 12
										&&item.reserved4 != '1' && item.reserved4 != '2' && item.reserved4 != '4')"
										class="edit" ng-click="item.delDisable?'':deleteMember(item)">
									<icon class="edit-icon"></icon>
									删除
								</li>
								<li
										ng-show="(enterpriseType == '5' && (item.status == 4 || item.status == 2 || item.status == 8))"
										class="sync" ng-click="sync(item)">
									<icon class="edit-icon"></icon>
									重新订购
								</li>

								<!-- <li class="delete">
										<icon class="delete-icon"></icon>删除</li> -->
								<!-- <li class="set">
										<icon class="set-icon"></icon>设置</li> -->
								<li class="delete"
									ng-show="((isSuperManager && enterpriseType== '3')
									&& (item.status == 0 || item.status == 1
									 || item.status == 2 || item.status == 4 || item.status == 5 || item.status == 8
									 || item.status == 131 || item.status == 13
									 || ((item.status == 3  || item.status == 13)&&(item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4'))
									 ))"
									 ng-click="deleteOrder(item)">
									企管订购关系删除
								</li>
								<li class="delete"
									ng-show="((isSuperManager && enterpriseType== '5')
									&& (item.status == 0 || item.status == 1
									|| item.status == 2 || item.status == 4 || item.status == 5 || item.status == 8
									|| item.status == 131 || item.status == 13
									|| item.status == 11 || item.status == 12 || item.status == 9
									|| ((item.status == 3  || item.status == 13)&&(item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4'))
									))"
									ng-click="deleteOrder(item)"
								>
									企管订购关系删除
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="queryEnterpriseList.length<=0">
					<td style="text-align:center" colspan="9">暂无数据</td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="enterpriseList('justPage')"></ptl-page>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel">提示</h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close">确定</button>
					</div>
				</div>
			</div>
		</div>
		<!--成员删除确认框弹出框-->
		<div class="modal fade" id="deleteMemberPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document" style="width: 360px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel2" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group">
								<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
									<div class="text-center" style="font-size: 16px;color:#383838">
										<span >请确认是否删除成员</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="oneSelect?remove('one'):remove('all')"
								ng-bind="'COMMON_OK'|translate"></button>
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="delMemCancel"
								ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		<!--重新同步-->
		<div class="modal fade" id="syncMemberPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document" style="width: 360px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel3" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group">
								<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
									<div class="text-center" style="font-size: 16px;color:#383838">
										<span >请确认是否重新同步成员</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="reSyncMember()"
								ng-bind="'COMMON_OK'|translate"></button>
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="cancelSyncMemCancel"
								ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		<!-- 企管订购关系删除确认-->
		<div class="modal fade" id="deleteOrderPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document" style="width: 360px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel4" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group">
								<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
									<div class="text-center" style="font-size: 16px;color:#383838">
										<span >请确认是否删除当前成员的企管订购关系</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="deleteOrderConfirm('one')"
								ng-bind="'COMMON_OK'|translate"></button>
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="delOrderCancel"
								ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>

</html>