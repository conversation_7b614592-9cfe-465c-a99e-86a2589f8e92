spring:
  http:
    encoding:
      force: true
      charset: UTF-8
  freemarker:
    allow-request-override: false
    cache: false
    check-template-location: true
    charset: UTF-8
    content-type: text/html; charset=utf-8
    prefer-file-system-access: false
    expose-request-attributes: false
    expose-session-attributes: false
    expose-spring-macro-helpers: false
    suffix: .ftl
    template-loader-path: classpath:/templates
  cloud:
    stream:
      kafka:
        binder:
          zkNodes: **************:2181,**************:2181,**************:2181
          brokers: **************:9092,**************:9092,**************:9092
      default-binder: kafka

    config:
      name: dsum
      enabled: true
      label: master
      profile: resource,serviceConfig
      #      uri: http://localhost:19004
      discovery:
        enabled: true
        service-id: CY-CONFIG-SERVER

eureka:
  client:
    serviceUrl:
      defaultZone: http://127.0.0.1:19000/eureka/
  instance:
    preferIpAddress: true
    instanceId: ${spring.cloud.client.ip-address}:${server.port}



ribbon:
  eureka:
    enabled: true
  ReadTimeout: 20000
  ConnectTimeout: 5000


logging:
  config:
    classpath: log4j2.xml