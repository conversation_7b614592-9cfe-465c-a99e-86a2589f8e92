/**
 * Created by cWX322788 on 2018/12/10.
 */
angular.module("service.common", ["util.ajax"
]).service("CommonUtils", function ($rootScope, RestClientUtil) {
  var fn = {
    /*日期时间格式化*/
    dateTimeFormate: function (date) {
      if (!date) {
        return
      } else {
        var obj = {};
        var d = new Date(date);
        obj.year = d.getFullYear();
        obj.month = ('0' + (d.getMonth() + 1)).slice(-2);
        obj.day = ('0' + (d.getDate())).slice(-2);
        obj.hour = ('0' + (d.getHours())).slice(-2);
        obj.minutes = ('0' + (d.getMinutes())).slice(-2);
        obj.seconds = ('0' + (d.getSeconds())).slice(-2);
        return obj
      }
    },
    /*获取服务器时间*/
    getServerTime: function () {
      var gmt_time = $.ajax({async: false, cache: false}).getResponseHeader("Date");
      var local_time = new Date(gmt_time);
      return this.dateTimeFormate(local_time);
    },

    /*对图片名称进行转码*/
    formatPic: function (url) {
      var obj = {
        "download":"",
        "review":"",
        "picName":""
      };
      if (url) {
        var index = url.lastIndexOf("\/");
        var strR = url.substring(index + 1, url.length);
        var strL = url.substring(0, index + 1);
        // if(strL.indexOf('/sftp/data')!=-1){
        //   obj.review = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=' + strL + encodeURIComponent(strR);
        // }else{
        //   obj.review = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=/sftp/data' + strL + encodeURIComponent(strR);
        // }
        obj.download = '/qycy/ecpmp/ecpmpServices/fileService/downloadFile?path=' + strL + encodeURIComponent(strR);
        obj.review = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=' + strL + encodeURIComponent(strR);
        obj.watch = '/qycy/ecpmp/ecpmpServices/fileService/reviewVideo?path=' + strL + encodeURIComponent(strR);
        obj.picName = strR;
      }
      return obj;
    },

    /**
     * 获取后台ecpmp.resourceLocal.properties文件
     * @param keys 要获取的数据key值数组  如 ["ecpmp.uploadFile.path","upload.pic.allow.types"]
     * @param success
     * @param error
     */
    getProperties: function (keys, success, error) {
      var req = {
        "keys": keys
      };
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/commonService/getProperties",
        data: JSON.stringify(req),
        success: function (result) {
          $rootScope.$apply(function () {
            var data = result.result;
            if (data.resultCode == '1030100000') {
              success(result.props)
            } else {
              error();
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
            error();
          })
        }
      });
    },
    exportFile: function (req) {
      var form = $("<form>");//定义一个form表单
      form.attr("style", "display:none");
      form.attr("target", "");
      form.attr("method", req.method);
      form.attr("action", req.url);
      $.each(req.param, function (k, v) {
        form.append($('<input type="hidden" ' +
            'name="' + k +
            '" value=\'' + v + '\'>'));
      });
      $("body").append(form);//将表单放置在web中
      form.submit();//表单提交
      form.remove();//移除该临时元素
    },
    isOpenAgent: function (id,fn){
     var keys=new Array("ecpmp.ThirdpartyAccess.enterprises");
      this.getProperties(keys,function (data){
        if(data){
          var enterprisesids=data["ecpmp.ThirdpartyAccess.enterprises"];
          if(fn) fn(enterprisesids);
            if(enterprisesids&&enterprisesids.indexOf(id)>=0){
              return true;
            }else {
              return false;
            }
        }else{
          return false;
        }

      },function (error){
        return false;
      })
    },
    // 获取当前locationUrl中的指定参数
    getQueryVariable : function (variable) {
      const query = window.location.search.substring(1);
      const vars = query.split("&");
      for (let i = 0; i < vars.length; i++) {
          const pair = vars[i].split("=");
          if (pair[0] == variable) {
              return pair[1];
          }
      }
      return (false);
    },
    // 分割字符串内容并展示 ,type 1 主叫 2 被叫 3 主叫和被叫
    splitStringAndShow: function (str,servType, subServType) {
      if (!str) { // 如果字符串为空，则返回""
        return {
          htmlVersion: "",
          titleVersion: ""
        };
      }
      if (servType != 2 && servType != 4 && subServType == 3) { // 如果serType不为2和4且subServType为3，则将字符串分割成数组，并展示成 主叫：arr[0] 被叫：arr[1]
        const arr = str.split("||||");
        // 用于HTML内容显示（使用<br>标签）
        const htmlDisplay = (arr[0] ? arr[0] : "--") + "<br>" + (arr[1] ? arr[1] : "--");
        // 用于title属性显示（使用&#10;实体）
        const titleDisplay = (arr[0] ? arr[0] : "--") + "\n" + (arr[1] ? arr[1] : "--");

        // 将两种格式都附加到返回结果上
        const result = {
          htmlVersion: htmlDisplay,
          titleVersion: titleDisplay
        };
        return result;
      } else { // 如果subServType其他，则直接返回字符串
        return {
          htmlVersion: str,
          titleVersion: str
        };
      }
    }
  };
  return fn;
});
