<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>能力开放测试</title>
    <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css" />
    <link href="../../../css/reset.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
        src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../directives/topMenu/topMenu.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/page/page.css" />
    <script type="text/javascript" src="../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
    <script type="text/javascript" src="capabilityOpenTest.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../css/hotlineContentManage.css" />
    <style>     
        .border-red {
            border: 1px solid red;
        }

        .dialog-690 {
            width: 696px;
        }

        .dialog-800 {
            width: 800px;
        }

        .dialog-900 {
            width: 900px;
        }

        .dialog-1000 {
            width: 1000px;
        }

        .handle ul li icon.manage-icon {
            background-position: -126px 0;
        }

        .handle ul li icon.add-icon {
            background-position: -55px 0;
        }

        .handle ul li icon.import-icon {
            background-position: -90px 0;
        }

        .table th.adjustable-width {
            width: 25%;
        }
        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        /* media for adjustable search-table width  */
        @media (max-width: 1850px) {
            .table th.adjustable-width {
                width: 28%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }
        @media (max-width: 1600px) {
            .table th.adjustable-width {
                width: 30%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }
        @media (max-width: 1300px) {
            .table th.adjustable-width {
                width: 33%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1100px) {
            .table th.adjustable-width {
                width: 42%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        #addHotlinePop li.notificationMethodStyle {
            position: relative;
            height: 25px;
            width: 369px;
            margin-top: 6px;
        }

        #addHotlinePop span.notificationMethodBtn {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            cursor: pointer;
        }

        #addHotlinePop span.notificationMethodText {
            position: absolute;
            top: 2px;
            bottom: 0;
            left: 31px;
            right: 0;
        }

        #addHotlinePop .create-number-modal-footer {
            padding-bottom: 15px;
            text-align: center;
            border:none;           
        }

        #deleteHotLinePop .deleteHOtlineContent{
            width: 300px;
            height:174px;
            margin-left: 142px;
        }

        #deleteHotLinePop .deleteHotLineFormGroup{
            margin-left: 0;
            margin-right: 0;
        }
		.addMsinsdn {
		    display: inline-block;
		    width: 23px;
		    height: 23px;
		    background: url(../../../assets/images/add.png) no-repeat;
		    vertical-align: middle;
		    background-position: 0 0;
		    position: absolute;
    		bottom: 0;
    		right: 132px;
    		z-index:99;
			}
		.col-ussd-3 {
		    width: 27%;
		}
    </style>
</head>

<body ng-app='myApp' ng-controller='capabilityOpenTest' ng-init="init()" class="">
    <div class="cooperation-manage">
            <div ng-if="isSuperManager" class="cooperation-head">
                    <span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate">
                    </span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'CAPABILITY_OPEN_TESTING'|translate"></span>
            </div>
        <div class="cooperation-search">
            <form class="form-inline">
                <div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
                        <label style="padding-right:30px;" for="contentNo"
                        ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></label>
                        <input ng-model='contentNo' type="text" autocomplete="off" class="form-control" id="contentNo"
                            placeholder="{{'CONTENTAUDIT_INPUTCONTENTNUMBERS'|translate}}">
                </div>                
                <div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
                    <label style="padding-right:30px;" for="contentName"
                        ng-bind="'CONTENTAUDIT_HOTCYCONTENT'|translate"></label>
                    <input ng-model='content' type="text" autocomplete="off" class="form-control" id="contentName"
                        placeholder="{{'HOTLINECONTENT_PLEASEINPUTCONTENTKEYWORDS'|translate}}">
                </div>
                <div class="form-group col-lg-1 col-xs-1  col-sm-1 col-md-1">
                    <button ng-click="queryContentInfoList('',enterpriseID)" type="submit" class="btn search-btn">
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
            </form>
        </div>
        <div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="addDeliveryTestContent()">
                <icon class="add-iocn"></icon>
                <span style="color:#705de1" ng-bind="'CONTENTAUDIT_ADDCTN'|translate"></span></span>
            </button>
        </div>

        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th style="width:10%" ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></th>
                        <th style="width:10%" ng-bind="'SUBSERVTYPE'|translate"></th>
                        <th style="width:15%" ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></th>
                        <th style="width:10%" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>                       
                        <th class="adjustable-width" ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in capabilityOpenTestListData"> 
                        <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>

                        <td><span title="{{typeMap[item.subServType]}}">{{typeMap[item.subServType]}}</span></td>
                         <td><span title="{{item.content}}">{{item.content}}</span></td>                         
                         <td style="min-width: 100px"><span
                                title="{{hotlineStatusMap[item.approveStatus]}}">{{hotlineStatusMap[item.approveStatus]}}</span>
                        </td>                      

                        <td>
                            <div class="handle">
                                <ul>
                                    <!-- 删除 -->
                                    <li class="delete" ng-click="deleteHotlineContent(item)">
                                        <icon class="delete-icon"></icon>
                                        <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
                                    </li>
                                    <!-- 编辑 -->
                                    <li class="edit" ng-click="updateHotlineContent(item)">
                                        <icon class="edit-icon"></icon>
                                        <span style="color:#705de1" ng-bind="'GROUP_EDIT'|translate"></span>
                                    </li>
                                    <!-- 号码新增 -->
                                    <li class="edit" ng-click="addHotLinePop(item)">
                                        <icon class="add-icon"></icon>
                                        <span style="color:#705de1" ng-bind="'HOTLINE_ADD'|translate"></span>
                                    </li>
                                    <!-- 投递模拟 -->
                                    <li class="edit" ng-click="managerDelivery(item)">
                                            <icon class="manage-icon"></icon>
                                            <span style="color:#705de1" ng-bind="'DELIVERY_TEST'|translate"></span>
                                    </li>                                   
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="capabilityOpenTestListData.length<=0">
                        <td style="text-align:center" colspan="5" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div>
            <ptl-page tableId="0" change="queryContentInfoList('justPage',enterpriseID)"></ptl-page>
        </div>

    </div>

     <!-- 新增(编辑)热线内容弹窗 -->
     <div class="modal fade" id="addDeliveryTestContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'CONTENT_ADD'|translate"></h4>
                    </div>
                    <div class="cooper-tab">
                        <form class="form-horizontal" name="myForm" novalidate>
                            <div class="form-group" style="padding-top:34px">
                                <label for="inputEmail3" class="col-ussd-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                    <icon>*</icon>
                                    <span ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></span>
                                </label>
                                <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                    <select class="form-control" ng-model="addHotlineContentInfo.subServType"
                                        ng-options="x.id as x.name|translate for x in subServTypeChoise"
                                        ng-disabled="operate =='update'"></select>
                                </div>
                            </div>
                              <div class="form-group" style="height: 34px;">
                                    <label class="col-ussd-3 col-xs-3 col-sm-3 col-md-3 control-label">
                                        <icon>*</icon><span ng-bind="'HOTLINE_NUMBER'|translate"></span>
                                    </label>
                                    <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
                                        <input type="text" class="form-control" style="display: inline-block;width:267px;"
                                            ng-model="addHotlineContentInfo.hotlineNo"
                                            placeholder="{{'HOTLINE_PLEASEINPUTHOTLINENUMBER'|translate}}"
                                             name="editHotlineNo" required pattern="^[0-9]{7,32}$" ng-disabled="operate =='update'"/>
                                        <span style="color:red;line-height: 34px;display: block;"
                                            ng-show="myForm.editHotlineNo.$dirty && myForm.editHotlineNo.$invalid">                                           
                                            <img src="../../../assets/images/reject-icon.png" width="20" height="20"
                                                align="absmiddle">
                                            <span ng-show="myForm.editHotlineNo.$error.required"
                                                ng-bind="'HOTLINE_NUMBERS_REQUIRED'|translate"></span>
                                            <span ng-show="myForm.editHotlineNo.$error.pattern"
                                                ng-bind="'HOTLINE_NUMBERS_REQUIRED'|translate"></span>                                          
                                        </span>
                                    </div>
                                </div>
                            <div class="form-group" style="padding-top:34px" >
                                <label for="inputPassword3" class="col-ussd-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                    <icon>*</icon><span ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></span>
                                </label>
                                <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                    <textarea class="form-control" rows="6" style="margin: 0px; width: 267px; height: 98px;"
                                        name="colorContent" ng-class="{'border-red':!contentVali||isSensitive}"
                                        placeholder="{{'PLEASEINPUTCYCONTENT_ONETOFIFTHWORDS'|translate}}"
                                        ng-model="addHotlineContentInfo.content" ng-blur="sensitiveCheck(1)">
                                      </textarea>
    
                                    <span style="color:red" ng-show="!contentVali||isSensitive">
                                        <img src="../../../assets/images/reject-icon.png" width="20" height="20"
                                            align="absmiddle">
                                        <span ng-show='contentDesc'>{{contentDesc|translate}}</span>
                                        <span
                                            ng-show='isSensitive'>{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
                                    </span>
                                </div>
                            </div>
                            <!-- 新增交互彩印USSD -->
                            <div style="position: relative;" ng-show = "addHotlineContentInfo.subServType == 7">
                            <span class="addMsinsdn" ng-click="addMsisdn()" ></span>
                            <!-- 指令框-->
                             <div  ng-repeat="reply in list">
                            	<div class="form-group" >
                                    <label class="col-ussd-3 col-xs-3 col-sm-3 col-md-3 control-label">
                                        <icon>*</icon><span  >回复指令{{$index+1}}</span>
                                    </label>
                                    <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
                                        <input type="text" class="form-control" style="display: inline-block;width:267px;"
                                            ng-model="reply.instruct" 
                                            placeholder="{{'INSTRUCTION_TIPS'|translate}}"
                                             id="instruction" name="{{'instruction'+$index}}" required pattern="^[a-zA-Z0-9]{1,10}$"/>
                                         <span style="color:red;line-height: 34px;display: block;" ng-show="myForm.{{'instruction'+$index}}.$dirty && myForm.{{'instruction'+$index}}.$invalid">
                                            <img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                            <span ng-show="myForm.{{'instruction'+$index}}.$error.required" ng-bind="'INSTRUCTION_TIPS'|translate"></span>
							                <span ng-show="myForm.{{'instruction'+$index}}.$error.pattern" ng-bind="'INSTRUCTION_TIPS'|translate"></span>
                                        </span>
                                    </div>
                                </div>
                                <!-- 指令回复内容框-->
                                <div class="form-group"  ng-show = "addHotlineContentInfo.subServType == 7">
                                    <label class="col-ussd-3 col-xs-3 col-sm-3 col-md-3 control-label">
                                        <icon>*</icon><span>回复指令交互内容{{$index+1}}</span>
                                    </label>
                                    <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                             
                                        <textarea class="form-control" rows="6" style="margin: 0px; width: 267px; height: 98px;"
                                         ng-class="{'border-red':reply.replyvalid || reply.replyisensitive}"
                                        placeholder="{{'PLEASEINPUTCYCONTENT_ONETOFIFTHWORDS'|translate}}"
                                        ng-model="reply.content" ng-blur="replyCheckissen($index)" >
                                      	</textarea>       
                                        <span ng-model="reply.replyvalid" style="color:red" ng-show="reply.replyvalid || reply.replyisensitive">
                                        <img src="../../../assets/images/reject-icon.png" width="20" height="20"
                                            align="absmiddle">
                                        <span ng-show='reply.replyvalid'>{{replycontentDesc|translate}}</span>
                                        <span ng-model="reply.replyisensitive"
                                            ng-show="reply.replyisensitive != ''">{{'CONTENT_DETECTION'|translate}}{{reply.replyisensitive}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
                                    </span>
                                    </div>
                                </div>
							</div>
                            </div>
                            <!-- 错误指令回复框-->
                            <div class="form-group" ng-show = "addHotlineContentInfo.subServType == 7">
                                <label class="col-ussd-3 col-xs-3 col-sm-3 col-md-3 control-label">
                                   <icon>*</icon><span ng-bind="'ERROR_INTERACTION_CONTENT'|translate"></span>
                                    </label>
                                    <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
                                        
                                        <textarea class="form-control" rows="6" style="margin: 0px; width: 267px; height: 98px;"
                                        ng-class="{'border-red':!errorcontentVali||errorisSensitive}"
                                        placeholder="{{'ERRORCONTENT_TIPS'|translate}}"
                                        ng-model="addHotlineContentInfo.errorInstructionRely" ng-blur="sensitiveCheck(99)">
                                      	</textarea>
                                        <span style="color:red" ng-show="!errorcontentVali ||errorisSensitive">
                                        <img src="../../../assets/images/reject-icon.png" width="20" height="20"
                                            align="absmiddle">
                                        <span ng-show='errorcontentDesc'>{{errorcontentDesc|translate}}</span>
                                        <span
                                            ng-show='errorisSensitive'>{{'CONTENT_DETECTION'|translate}}{{errorsensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span> 
                                    	</span>
                                  </div>
                             </div>    
                            
                        </form>
                    </div>
                    <div class="modal-footer" ng-show = "addHotlineContentInfo.subServType != 7">
                        <button type="submit" ng-disabled="!addHotlineContentInfo.content||myForm.editHotlineNo.$invalid"
                            class="btn btn-primary search-btn" ng-bind="'COMMON_SUBMIT'|translate"
                            ng-click="beforeCommit(itemContentID,enterpriseID,1)"></button>
                        <button ng-hide="true" type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                            ng-click="goback()" id="addHotlineContentCancel" ng-bind="'COMMON_BACK'|translate"></button>
                    </div>
                    <div class="modal-footer" ng-show = "addHotlineContentInfo.subServType == 7">
                        <button type="submit" ng-disabled="!addHotlineContentInfo.content ||myForm.editHotlineNo.$invalid || !addHotlineContentInfo.errorInstructionRely"
                            class="btn btn-primary search-btn" ng-bind="'COMMON_SUBMIT'|translate"
                            ng-click="beforeCommit(itemContentID,enterpriseID,2)"></button>
                        <button ng-hide="true" type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                            ng-click="goback()" id="addHotlineContentCancel" ng-bind="'COMMON_BACK'|translate"></button>
                    </div>
                </div>
            </div>
    </div>

    <!-- 删除热线内容弹窗 -->
    <div class="modal fade bs-example-modal-sm" id="deleteHotlineContent" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content" style="width:390px">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838' ng-bind="'HOTLINE_SUREDELETEHOTLINE'|translate"></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                        ng-click="delHotlineContent()"></button>
                    <button id="deleteHotlineContentCancel" type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" ng-bind="'NO'|translate"></button>
                </div>
            </div>
        </div>
    </div>

    <!--号码新增弹出框-->
    <div class="modal fade bs-example-modal-sm" id="addHotlinePop" tabindex="-1" role="dialog" 
        aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                        ng-click="closeAdd()"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'CREATE_NUMBER'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" name="addForm">
                        <div class="form-group">
                            <div class="row" style="height: 34px;">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label" style="width: 157px">
                                    <icon>*</icon><span ng-bind="'DELIVERY_PHONE_NUMBER'|translate"></span>
                                </label>
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
                                    <input type="text" class="form-control" style="display: inline-block"
                                        ng-model="addHotlineNo" name="addHotlineNo"
                                        placeholder="{{'DELIVERYLINE_PLEASEINPUTDELIVERYNUMBER'|translate}}" required
                                        pattern="^[0-9]{11}$" />
                                    <span style="color:red;line-height: 34px;display: block;" class="hotloneNoTips"
                                        ng-show="addForm.addHotlineNo.$dirty && addForm.addHotlineNo.$invalid">
                                        <img src="../../../assets/images/reject-icon.png" width="20" height="20"
                                            align="absmiddle">
                                        <span ng-show="addForm.addHotlineNo.$error.required"
                                            ng-bind="'GROUP_MEMBMSISDNDESC'|translate"></span>
                                        <span ng-show="addForm.addHotlineNo.$error.pattern"
                                            ng-bind="'GROUP_MEMBMSISDNDESC'|translate"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row" style="height:120px;">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label" style="width: 157px;height:160px;">
                                    <icon>*</icon><span ng-bind="'NOTICEMETHOD'|translate"></span>
                                </label>                               
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8" ng-repeat="x in notificationMethods" style="width: 390px;" ng-show = "showType != 7">
                                    <li class="redio-li notificationMethodStyle" ng-click="selectNotification(x.id)">
                                        <span  class="check-btn redio-btn notificationMethodBtn" ng-class="{true:'checked',false:''}[selectedMethod===x.id]"> </span>
                                        <span ng-bind="x.name" class="notificationMethodText"></span>
                                    </li>
                                </div>
                                <!-- 交互USSD -->
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8" ng-repeat="y in ussdnotifyMethods" style="width: 390px;" ng-show = "showType == 7">
                                    <li class="redio-li notificationMethodStyle" ng-click="selectNotification(y.id)">
                                        <span class="check-btn redio-btn notificationMethodBtn" ng-class="{true:'checked',false:''}[selectedMethod===y.id]"> </span>
                                        <span ng-bind="y.name" class="notificationMethodText"></span>
                                    </li>
                                </div>
                            </div>
                                
                        </div>
                    </form>
                </div>
                <div class="create-number-modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_SAVE'|translate"
                        ng-click="createHotline(itemContentID,selectedMethod)"
                        ng-disabled="addForm.addHotlineNo.$invalid||!selectedMethod"></button>
                    <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-click="goback()" id="addHotlineCancel" style="margin-left: 20px"
                        ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>

    <!--投递模拟弹出框-->
    <div class="modal fade" id="managerDelivery" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
        <div role="document" ng-class="{'modal-dialog':1==1,'dialog-690':pageInfo[2].totalPage<=3,
                    'dialog-800':pageInfo[2].totalPage>3 && pageInfo[2].totalPage<7,
                    'dialog-900':pageInfo[2].totalPage>=7 && pageInfo[2].totalPage<10,
                    'dialog-1000':pageInfo[2].totalPage>=10}">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'DELIVERY_TEST'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="form-horizontal">
                        <div cla ss="form-group">
                            <div class="row">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                    ng-bind="'DELIVERY_PHONE_NUMBER'|translate"></label>
                                <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5">
                                    <input type="text" class="form-control" id=""
                                        placeholder="{{'DELIVERYLINE_PLEASEINPUTDELIVERYNUMBER'|translate}}"
                                        ng-model="hotlineMsisdnDel">
                                </div>
                                <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                    <button class="btn bg_purple search-btn btn1"
                                        ng-click="queryContentRelObjectList(selectedItem,'search')">
                                        <span class="icon btnIcon search"></span>
                                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="add-table" style="margin-left:12px;margin-top:12px;">
                        <button class="btn" ng-disabled="!hasChoseDelHotLine"
                            style="width:105px; margin-left:10px;color:#7360e1" type="button"
                            ng-click="sureDelHotLine('batch')" ng-bind="'GROUP_BATCHDELETE'|translate"></button>
                    </div>

                    <div class="" style="max-height: 530px;overflow: auto">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th style="padding-left:30px;width: 10%;"><input type="checkbox"
                                            ng-model="chooseAllDelHotLine" ng-click="selectAllDelHotLine()"></th>
                                    <th style="padding-left:30px"  ng-bind="'DELIVERY_PHONE_NUMBER'|translate"></th>
                                    <th style="padding-left:30px"  ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></th>
                                    <th style="padding-left:30px;" ng-bind="'COMMON_OPERATE'|translate"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in contentBelongOrgList">
                                    <td><input type="checkbox" ng-model="item.checked"></td>
                                    <td><span title="{{item.target}}">{{item.target}}</span></td>
                                    <td><span title="{{notificationMethod[item.msgtype]}}">{{notificationMethod[item.msgtype]}}</span></td>
                                    <td style="font-size: small;">
                                        <a ng-click="deliveryTrigger(item)" ng-bind="'DELIVERY_TRIGGER'|translate"></a>                                       
                                        <a ng-click="sureDelHotLine('single',item)"
                                            ng-bind="'COMMON_DELETE'|translate"></a>
                                    </td>
                                </tr>
                                <tr ng-show="contentBelongOrgList.length<=0">
                                    <td style="text-align:center" colspan="4" ng-bind="'COMMON_NODATA'|translate"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ptl-page tableId="2" change="queryContentRelObjectList(selectedItem,'justPage')"></ptl-page>
                    </div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

    <!--热线号码管理确认删除框弹出框-->
    <div class="modal fade bs-example-modal-sm" id="deleteHotLinePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content deleteHOtlineContent">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="deleteHotLineFormGroup ">
                            <div class="row"
                                style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
                                <div class="text-center">
                                    <span ng-bind="'COMMON_DEL_SURE'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn"
                        ng-click="singleOrBatchDelHotLine(singleOrBatch)" ng-bind="'COMMON_OK'|translate"></button>
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="delMemCancel"
                        ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>

    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838'>
                            {{tip|translate}}
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
</body>

</html>