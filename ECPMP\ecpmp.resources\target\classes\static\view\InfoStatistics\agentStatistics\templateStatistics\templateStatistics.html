<!DOCTYPE html>
<html>

<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11" />
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
	<link href="../../../../css/reset.css" rel="stylesheet" />
	<link href="../../../../css/mian.css" rel="stylesheet" />
	<link href="../../../../css/datepicker3.css" rel="stylesheet" />
	<link href="../../../../css/layout.css" rel="stylesheet" />
	<link href="../../../../css/searchList.css" rel="stylesheet" />
	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
		src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
	<link rel="stylesheet" href="../../../../css/font-awesome.min.css" />
	<link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css" />
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
	<link href="../../../../css/statistics.css" rel="stylesheet" />
	<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
	<script type="text/javascript" src="templateStatistics.js"></script>

	<style>
		.form-horizontal .control-label {
			padding-top: 14px !important;
			padding-bottom: 6px;
			font-weight: normal;
			white-space: nowrap;
		}
		body,html {
			overflow: auto;
		}
		.input-daterange {
			padding-top: 0px !important;
		}
		.table > thead > tr > th,td{
			padding: 12px 3px!important;
		}
	</style>

</head>

<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" style="min-width: 1400px">
	<div class="cooperation-manage">
		<div class="cooperation-head" ng-show="isSuperManager"><span class="frist-tab"
				ng-bind="'AGENTSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab"
				ng-bind="'TEMPLATE_STATISTICS'|translate"></span></div>
		<div class="cooperation-head" ng-show="isAgent"><span class="frist-tab"
				ng-bind="'DATA_STATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab"
				ng-bind="'TEMPLATE_STATISTICS'|translate"></span></div>
		<top:menu chose-index="6" page-url="/qycy/ecpmp/view/InfoStatistics/agentStatistics" list-index="42"
			ng-show="isSuperManager"></top:menu>
		<top:menu chose-index="3" page-url="/qycy/ecpmp/view/InfoStatistics/agentStatistics" list-index="48"
			ng-show="isAgent"></top:menu>
		<form class="form-horizontal">
			<div class="form-group">
				<label for="contentID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
					ng-bind="'CYID'|translate"></label>
				<div class="col-lg-5 col-md-5 col-sm-5 col-xs-5 cond-div">
					<input type="text" autocomplete="off" class="form-control" id="contentID"
						placeholder="{{'PLEASE_ENTER_TEMPLATE_ID'|translate}}" ng-model="contentID">
				</div>

				<label for="serviceType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
					style="white-space: nowrap;" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
				<div class="col-lg-5 col-md-5 col-sm-5 col-xs-5 cond-div">
					<select class="form-control" name="serviceType" ng-model="serviceType"
						ng-options="x.id as x.name for x in serviceTypeChoise" style="max-width:200px">
					</select>
				</div>

				<label class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
					ng-bind="'QUERYORDERDETAIL_TIME'|translate"></label>
				<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 time">
					<div class="input-daterange input-group" id="datepicker">
						<input type="text" class="input-md form-control" autocomplete="off" id="start"
							ng-keyup="searchOn()" />
						<span class="input-group-addon" ng-bind="'TO'|translate"></span>
						<input type="text" class="input-md form-control" autocomplete="off" id="end"
							ng-keyup="searchOn()" />
					</div>
				</div>

				<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
					<button ng-click="queryDeliveryStatByContent()" type="submit" ng-disabled="initSel.search"
						class="btn search-btn">
						<icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span>
					</button>
				</div>
			</div>
		</form>

		<div class="add-table">
			<button id="exportSpokesList" class="btn add-btn" ng-click="exportFile()">
				<icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span>
			</button>
		</div>

		<div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-show="isSuperManager"
			ng-bind="'TEMPLATE_ID_STATISTICS_QUERY_LIST'|translate"></div>
		<div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-show="isAgent"
			ng-bind="'TEMPLATE_ID_STATISTICS_QUERY_LIST'|translate"></div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
					<tr>
						<th style="width:10%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
						<th style="width:10%" ng-bind="'CYID'|translate"></th>
						<th style="width:40%" ng-bind="'TEMPLATE_CONTENT'|translate"></th>
						<th style="width:10%" ng-bind="'DATE_CREATED'|translate"></th>
						<th style="width:10%" ng-bind="'AMOUNT'|translate"></th>
						<th style="width:10%" ng-bind="'CMCC_USE'|translate"></th>
						<th style="width:10%" ng-bind="'CUCC_USE'|translate"></th>
						<th style="width:10%" ng-bind="'CTCC_USE'|translate"></th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="item in contentDeliveryStatList">
						<td><span title="{{getServiceType(item.servType)}}">{{getServiceType(item.servType)}}</span></td>
						<td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
						<td><pre title="{{item.content}}">{{item.content}}</pre></td>
						<td><span title="{{item.createTime}}">{{item.createTime}}</span></td>
						<td><span title="{{item.totalCount}}">{{item.totalCount}}</span></td>
						<td><span title="{{item.mobileCount}}">{{item.mobileCount}}</span></td>
						<td><span title="{{item.unicomCount}}">{{item.unicomCount}}</span></td>
						<td><span title="{{item.telcomCount}}">{{item.telcomCount}}</span></td>
					</tr>
					<tr ng-show="contentDeliveryStatList.length<=0">
						<td style="text-align:center" colspan="17" ng-bind="'COMMON_NODATA'|translate"></td>
					</tr>
				</tbody>
			</table>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
						ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</body>

</html>