<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper2.AccountMapper">
	<resultMap id="accountMap"
		type="com.huawei.jaguar.dsdp.bill.dao.domain2.AccountWrapper">
		<result property="id" column="id" />
		<result property="accountName" column="accountName" />
		<result property="accountType" column="accountType" />
		<result property="fullName" column="fullName" />
		<result property="status" column="status" />
		<result property="cityID" column="cityID" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="orgID" column="orgID" />
		<result property="msisdn" column="msisdn" />
		<result property="email" column="email" />
		<result property="createTime" column="createTime" />
		<result property="operatorID" column="operatorID" />
		<result property="lastUpdateTime" column="lastUpdateTime" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="reserved5" column="reserved5" />
		<result property="reserved6" column="reserved6" />
		<result property="reserved7" column="reserved7" />
		<result property="reserved8" column="reserved8" />
		<result property="reserved9" column="reserved9" />
		<result property="reserved10" column="reserved10"/>
		<result property="roleID" column="roleID" />
		<result property="roleName" column="roleName"/>
		<result property="roleDesc" column="roleDesc"/>
		<result property="roleCode" column="roleCode"/>
		<result property="managerRightType" column="managerRightType"/>
		<result property="oaAccount" column="oaAccount"/>
		<result property="isMiguStaff" column="isMiguStaff"/>
		<result property="lockStatus" column="lockStatus"/>
		<result property="enterpriseType" column="enterpriseType"/>


	</resultMap>

	<insert id="insertAccount">
		INSERT INTO dsum_t_account
		(id,
		accountName,
		accountType,
		fullName,
		status,
		cityID,
		enterpriseID,
		orgID,
		msisdn,
		email,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		managerRightType,
		oaAccount,
		isMiguStaff
		)
		VALUES
		(
		#{id},
		#{accountName},
		#{accountType},
		#{fullName},
		#{status},
		#{cityID},
		#{enterpriseID},
		#{orgID},
		#{msisdn},
		#{email},
		#{createTime},
		#{operatorID},
		#{lastUpdateTime},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10},
		#{managerRightType},
		#{oaAccount},
		#{isMiguStaff}
		)
	</insert>

	<select id="getAccountID" resultType="java.lang.Integer">
		select
		nextval('dsum_sequence_account')
	</select>

	<select id="queryByAccountNameAndType" resultMap="accountMap">
		SELECT
		t.id,
		t.accountName,
		t.accountType,
		t.fullName,
		t.status,
		t.cityID,
		t.enterpriseID,
		t.orgID,
		t.msisdn,
		t.email,
		t.createTime,
		t.operatorID,
		t.lastUpdateTime,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		e.reserved10,
		t.managerRightType,
		e.enterpriseType
		from dsum_t_account t
		left JOIN dsum_t_enterprise e on (e.id = t.enterpriseID or (e.enterpriseCode = t.accountName and e.enterpriseType = 6))
		where
		t.accountName = #{accountName}
		and t.accountType=#{accountType}
		<if test="reserved1!=null and reserved1!=''">
			and t.reserved1 = #{reserved1}
	    </if>
	    <if test="reserved1==null or reserved1==''">
			and t.reserved1 is null
	    </if>
	</select>

	<select id="queryAccountByAccountID" resultMap="accountMap">
		SELECT
		t.*,
		if(al.id is not null,1,0) lockStatus
		from dsum_t_account t
		left JOIN dsum_t_account_lock al on al.accountID = t.id
		where t.id = #{accountID}
	</select>
	<select id="queryAccountByAccountName" resultMap="accountMap">
		SELECT
			t.*,
			if(al.id is not null,1,0) lockStatus
		from dsum_t_account t
		left JOIN dsum_t_account_lock al on al.accountID = t.id
		where t.accountName = #{accountName}
	</select>
	<select id="queryAccountByOAAccount" resultMap="accountMap">
		SELECT
		t.*,
		if(al.id is not null,1,0) lockStatus
		from dsum_t_account t
		left JOIN dsum_t_account_lock al on al.accountID = t.id
		where t.oaAccount= #{oaAccount}
    </select>
	<update id="updateAccountByAccountID">
		update dsum_t_account set
		<trim suffixOverrides="," suffix="where id = #{id}">
            <if test="fullName!=null and fullName!=''">fullName= #{fullName},</if>
            <if test="status!=null">status= #{status},</if>
            <if test="cityID!=null and cityID!=''">cityID= #{cityID},</if>
            <if test="enterpriseID!=null">enterpriseID= #{enterpriseID},</if>
            <if test="orgID!=null">orgID= #{orgID},</if>
            <if test="msisdn!=null and msisdn!=''">msisdn= #{msisdn},</if>
            <if test="email!=null and email=='-1'">email= null,</if>
            <if test="email!=null and email!='' and email!='-1'">email= #{email},</if>
            <if test="operatorID!=null">operatorID= #{operatorID},</if>
			<if test="managerRightType!=null">managerRightType= #{managerRightType},</if>
			<if test="lastUpdateTime!=null">lastUpdateTime= #{lastUpdateTime},</if>
            <if test="extInfo!=null and extInfo!=''">extInfo= #{extInfo},</if>
            <if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
            <if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
            <if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
            <if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
            <if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
            <if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
            <if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
            <if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
            <if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
            <if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
			<if test="isMiguStaff!=null and isMiguStaff!=''">
				isMiguStaff=#{isMiguStaff}, oaAccount=#{oaAccount},
			</if>
			<if test="isMiguStaff==null or isMiguStaff ==''">
				isMiguStaff=null, oaAccount=null,
			</if>
			<if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
			<if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>


        </trim>
	</update>

	<select id="batchQueryAccountID" resultType="java.lang.Integer"
		parameterType="java.util.List">
		SELECT t.id from dsum_t_account t where t.id in
		<foreach item="accountID" index="index" collection="list"
			open="(" separator="," close=")">
			#{accountID}
		</foreach>
	</select>

	<delete id="batchDeleteAccountByIDs" parameterType="java.util.List">
		delete from dsum_t_account where id in
		<foreach item="accountID" index="index" collection="list"
			open="(" separator="," close=")">
			#{accountID}
		</foreach>
	</delete>


	<select id="batchQueryAccount" resultMap="accountMap">
		SELECT
		<include refid="accountColumn"/>
		,if(al.id is not null,1,0) lockStatus
		,IF (e.enterpriseType is null,IF(t.reserved1 is null,4,6),e.enterpriseType) enterpriseType
		from dsum_t_account t
		<if test="sortField != null and sortField != ''">
			FORCE INDEX (`idx_order_by_2`)
		</if>
		left JOIN dsum_t_account_lock al on al.accountID = t.id
		left JOIN dsum_t_enterprise e on e.id = t.enterpriseID
        <trim prefix="where" prefixOverrides="and|or">
			<if test="accountIDList != null and accountIDList.size()>0">
				and t.id in
				<foreach item="accountID" index="index" collection="accountIDList" open="(" separator="," close=")">
					#{accountID}
				</foreach>
			</if>
			<if test="accountName != null and accountName != ''">
				and t.accountName like concat("%", #{accountName}, "%")
			</if>
			<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
				and t.enterpriseID in
				<foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="(" separator="," close=")">
					#{enterpriseID}
				</foreach>
			</if>
			<if test="orgIDList != null and orgIDList.size()>0">
				and t.orgID in
				<foreach item="orgID" index="index" collection="orgIDList" open="(" separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			<if test="accountQueryType == null or accountQueryType == 1">
				and t.id in (
					select t1.accountID from  dsum_t_account_role t1
					join dsum_t_role t2 on t1.roleID = t2.id
					where t2.roleType = 1
					<if test="roleName != null and roleName != ''">
						and t2.roleName like concat("%", #{roleName}, "%")
					</if>
						
				) 
			</if>
			<if test="accountQueryType != null and accountQueryType == 2">
				and t.id in ( 
					select t1.accountID from  dsum_t_account_role t1
					join dsum_t_role t2 on t1.roleID = t2.id
					where t2.roleType = 2
					<if test="roleName != null and roleName != ''">
						and t2.roleName like concat("%", #{roleName}, "%")
					</if>
				)
				<if test="enterpriseType != null and enterpriseType != 6">
					   and ( t.enterpriseID in
						(
							select t4.ID from dsum_t_enterprise t4
							where t4.enterpriseType = #{enterpriseType}
						)
					<if test="enterpriseType != null and enterpriseType == 4">
		    			or (t.enterpriseID is null and t.reserved1 is null)
					</if>
		    		)
				</if>
				<if test="enterpriseType != null and enterpriseType == 6">
					and t.accountName in
					(
					select t4.enterpriseCode from dsum_t_enterprise t4
					where t4.enterpriseType = #{enterpriseType}
					)
				</if>
			</if>
		</trim>
		<if test="sortType != null and sortType != ''">
			order by ${sortField} ${sortType} ,id ${sortType}
		</if>
		limit #{pageNo},#{pageSize}
	</select>


	<select id="batchQueryAccountByEnterpriseIds" resultMap="accountMap">
		SELECT
		<include refid="accountColumn"/>
		from dsum_t_account t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
				and t.enterpriseID in
				<foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="(" separator="," close=")">
					#{enterpriseID}
				</foreach>
			</if>
		</trim>

	</select>

	<select id="countAccount" resultType="java.lang.Integer">
		SELECT
		count(*)
		from dsum_t_account t 
        <trim prefix="where" prefixOverrides="and|or">
			<if test="accountIDList != null and accountIDList.size()>0">
				and t.id in
				<foreach item="accountID" index="index" collection="accountIDList" open="(" separator="," close=")">
					#{accountID}
				</foreach>
			</if>
			<if test="accountName != null and accountName != ''">
				and t.accountName like concat("%", #{accountName}, "%")
			</if>
			<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
				and t.enterpriseID in
				<foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="(" separator="," close=")">
					#{enterpriseID}
				</foreach>
			</if>
			<if test="orgIDList != null and orgIDList.size()>0">
				and t.orgID in
				<foreach item="orgID" index="index" collection="orgIDList" open="(" separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			<if test="accountQueryType == null or accountQueryType == 1">
				and t.id in (
					select t1.accountID from  dsum_t_account_role t1
					join dsum_t_role t2 on t1.roleID = t2.id
					where t2.roleType = 1
					<if test="roleName != null and roleName != ''">
						and t2.roleName like concat("%", #{roleName}, "%")
					</if>
				) 
			</if>
			<if test="accountQueryType != null and accountQueryType == 2">
				and t.id in ( 
					select t1.accountID from  dsum_t_account_role t1
					join dsum_t_role t2 on t1.roleID = t2.id
					where t2.roleType = 2
					<if test="roleName != null and roleName != ''">
						and t2.roleName like concat("%", #{roleName}, "%")
					</if>
				)
				<if test="enterpriseType != null and enterpriseType != 6">
					and t.enterpriseID in
					(
					select t4.ID from dsum_t_enterprise t4
					where t4.enterpriseType = #{enterpriseType}
					)
				</if>
				<if test="enterpriseType != null and enterpriseType == 6">
					and t.accountName in
					(
					select t4.enterpriseCode from dsum_t_enterprise t4
					where t4.enterpriseType = #{enterpriseType}
					)
				</if>
			</if>
		</trim>
	</select>

	<select id="memberMsisdnCount" resultType="java.lang.Integer">
		SELECT
		count(*) from
		dsum_t_account t where t.msisdn =
		#{msisdn}
	</select>

	<select id="memberAccountCount" resultType="java.lang.Integer">
		SELECT
		count(*) from
		dsum_t_account t where t.accountName =
		#{accountName} and t.accountType =#{accountType}
		and reserved1 is null
	</select>

    <delete id="deleteAccount">
      DELETE dsum_t_account,dsum_t_accountpwd,dsum_t_account_role 
      FROM dsum_t_account INNER JOIN dsum_t_accountpwd ON dsum_t_account.ID =dsum_t_accountpwd.accountID 
      INNER JOIN dsum_t_account_role ON dsum_t_accountpwd.accountID = dsum_t_account_role.accountID 
      WHERE dsum_t_account.ID=#{id}
    </delete>
    
    <update id="updateAccountByID">
		UPDATE dsum_t_enterprise p SET p.msisdn =null, p.contract=null WHERE p.ID=#{id};
	</update>
	
	<sql id="accountColumn">
        t.id,
		t.accountName,
		t.accountType,
        t.fullName,
        t.status,
        t.cityID,
        t.enterpriseID,
        t.orgID,
        t.msisdn,
        t.email,
        t.createTime,
        t.operatorID,
        t.lastUpdateTime,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10,
		t.managerRightType,
		t.oaAccount,
		t.isMiguStaff
    </sql>
    
    <select id="queryAccountID" resultType="java.lang.Integer">
		SELECT
		distinct t.id
		from dsum_t_account t 
        <trim prefix="where" prefixOverrides="and|or">
			<if test="accountIDList != null and accountIDList.size()>0">
				and t.id in
				<foreach item="accountID" index="index" collection="accountIDList" open="(" separator="," close=")">
					#{accountID}
				</foreach>
			</if>
			<if test="accountName != null and accountName != ''">
				and t.accountName like concat("%", #{accountName}, "%")
			</if>
			<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
				and t.enterpriseID in
				<foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="(" separator="," close=")">
					#{enterpriseID}
				</foreach>
			</if>
			<if test="orgIDList != null and orgIDList.size()>0">
				and t.orgID in
				<foreach item="orgID" index="index" collection="orgIDList" open="(" separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			<if test="accountQueryType == null or accountQueryType == 1">
				and t.id in (
					select t1.accountID from  dsum_t_account_role t1
					join dsum_t_role t2 on t1.roleID = t2.id
					where t2.roleType = 1
						<if test="roleName != null and roleName != ''">
							and t2.roleName like concat("%", #{roleName}, "%")
						</if>
				) 
			</if>
			<if test="accountQueryType != null and accountQueryType == 2">
				and t.id in ( 
					select t1.accountID from  dsum_t_account_role t1
					join dsum_t_role t2 on t1.roleID = t2.id
					where t2.roleType = 2
					<if test="roleName != null and roleName != ''">
						and t2.roleName like concat("%", #{roleName}, "%")
					</if>
				)
				<if test="enterpriseType != null and enterpriseType != 6">
					and t.enterpriseID in
					(
					select t4.ID from dsum_t_enterprise t4
					where t4.enterpriseType = #{enterpriseType}
					)
				</if>
				<if test="enterpriseType != null and enterpriseType == 6">
					and t.accountName in
					(
					select t4.enterpriseCode from dsum_t_enterprise t4
					where t4.enterpriseType = #{enterpriseType}
					)
				</if>
			</if>
		</trim>
	</select>
	
	
	<select id="queryAccountByIDs" resultMap="accountMap">
		SELECT
		<include refid="accountColumn"/>
		from dsum_t_account t 
		where  t.id in
		<foreach item="accountID" index="index" collection="list"
			open="(" separator="," close=")">
			#{accountID}
		</foreach>
	</select>

	<select id="queryAccountByEnterpriseCode" resultMap="accountMap">
		SELECT
			t.*,
			if(al.id is not null,1,0) lockStatus
		from dsum_t_account t
		left JOIN dsum_t_account_lock al on al.accountID = t.id
		left JOIN dsum_t_enterprise e on t.enterpriseID = e.id
		where e.enterpriseCode = #{enterpriseCode}
	</select>
</mapper>