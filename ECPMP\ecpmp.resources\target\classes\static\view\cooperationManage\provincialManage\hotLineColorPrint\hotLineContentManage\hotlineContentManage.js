var app = angular.module("myApp", ["util.ajax", "page", "angularI18n","cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common","ngSanitize"])
app.controller('hotlineContentController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
  $scope.CommonUtils = CommonUtils;
  $scope.init = function () {
    $scope.noSign = '0';
    $scope.isSuperManager = false;
    $scope.loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
    $scope.enterpriseType = $.cookie('enterpriseType') || '';
    $scope.enterpriseID = $.cookie('enterpriseID') || '';
    $scope.parentEnterpriseID = $scope.enterpriseID ;
    $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
    $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
    //获取enterpriseName
    $scope.enterpriseName = $.cookie('enterpriseName');
    //判断最终调接口的enterpriseID,enterpriseName
    if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
      $scope.enterpriseID = $scope.subEnterpriseID;
      $scope.enterpriseName = $scope.subEnterpriseName;
    }
    $scope.hotContentInfoListData = [];
    $scope.selectedList = [];
    $scope.hotMsisdnVali = true;
    $scope.contentVali = true;
    $scope.operatorID = $.cookie('accountID');
    $scope.hotlineMsisdnAdd = '';
    $scope.hotlineMsisdnDel = '';
    $scope.enterpriseReserved10 = '';
    $scope.hotlineList = [];
    $scope.contentBelongOrgList = [];
    $scope.chooseAllAddHotLine = false;
    //新增热线号码是否可以点击批量接口
    $scope.hasChoseAddHotLine = false;
    $scope.hasChoseDelHotLine = false;

    $scope.accepttype2 = "jpeg,jpg,png,bmp";
    $scope.isValidate2 = false;
    $scope.filesize2 = 100;
    $scope.mimetypes2 = ".jpeg,.jpg,.png,.bmp";
    $scope.isCreateThumbnail2 = false;
    $scope.uploadurl2 = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
    $scope.numlimit2 = 1000;
    $scope.uploadCertiDesc = "最多支持6张图片，仅支持jpg，bmp，png，jpeg格式";
    $scope.uploadParam2 = {
      enterpriseId: $scope.enterpriseID,
      fileUse: 'certiFile'
    };
    $scope.colorContentAndFileList = [];
    $scope.certificateUrlList = [];
    //文件数
    $scope.fileLength = 0;
    $scope.isZYZQ = $.cookie('reserved10') == "111";

    if ($scope.operate != 'detail') {
      $scope.$watch('colorContentAndFileList', function (newVal, oldVal) {
        $scope.fileLength = 0;
        for (var i in newVal) {
          if (newVal[i].frameTxt == undefined) {
            newVal[i].frameTxt = "";
          }
          if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
            newVal[i].filesize = 0;
          }
          if (newVal[i].frameFileUrl) {
            $scope.fileLength += 1;
          }
        }

      }, true)
    }

    $scope.$watch('hotlineList', function (newVal, oldVal) {
      $scope.hasChoseAddHotLine = false;
      if (newVal.length > 0) {
        //判断是否至少已经勾选了一项
        for (var i in newVal) {
          if (newVal[i].checked) {
            $scope.hasChoseAddHotLine = true;
            break;
          }
        }
        var str = JSON.stringify(newVal);
        var index = str.indexOf('"checked":false');
        //同步全选按钮
        $scope.chooseAllAddHotLine = index === -1 ? true : false;
      }
    }, true)
    $scope.$watch('contentBelongOrgList', function (newVal, oldVal) {
      $scope.hasChoseDelHotLine = false;
      if (newVal.length > 0) {
        for (var i in newVal) {
          if (newVal[i].checked) {
            $scope.hasChoseDelHotLine = true;
            break;
          }
        }
        var str = JSON.stringify(newVal);
        var index = str.indexOf('"checked":false');
        //同步全选按钮
        $scope.chooseAllDelHotLine = index === -1 ? true : false;
      }
    }, true)
    // 上传excel
    $scope.accepttype = "xlsx";
    $scope.isValidate = true;
    $scope.filesize = 20;
    $scope.mimetypes = ".xlsx,.xls";
    $scope.auto = true;
    $scope.isCreateThumbnail = false;
    $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
    $scope.uploadDesc = "仅支持xlsx格式的文件";
    $scope.numlimit = 1;
    $scope.urlList = [];
    $scope.uploadParam = {
      enterpriseId: $scope.enterpriseID,
      fileUse: 'importContentTemplate'
    };
    $scope.errorInfo = '';
    $scope.fileUrl = '';
    // 上传excel  END
    $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
      if (broadData.file !== "") {
        $scope.fileName = broadData.file.name;
      } else {
        $scope.fileName = "";
      }
      $scope.uploader = broadData.uploader;
      $scope.errorInfo = broadData.errorInfo;
      $scope.fileUrl = fileUrl;
    });

    //上传文件
    $scope.$on("uploadifyid2", function (event, fileUrl, fileData) {
      if (fileUrl != '') {
        if($scope.colorContentAndFileList.length >= 6){
          return;
        }
        $scope.colorContentAndFileList.push({
          frameFileUrl: fileUrl,
          formatFrameFileUrl: CommonUtils.formatPic(fileUrl).download,
          filesize: fileData.file.size,
          filename: fileData.file.name
        })
      } else if (fileData != '' || fileData != undefined) {
        // $scope.contentPicUrlList.splice(index, 1);
        // if($scope.urlList){
        //     $scope.urlList.splice(index,1);
        // }
      }
      console.log(fileUrl);
      console.log($scope.colorContentAndFileList);
      console.log($scope.fileLength);
    });

    $scope.$on("uploadifyid_1", function (event, fileUrl, index, broadData) {
          $scope.uploader_1 = broadData.uploader;
        });
    //彩印内容类型
    $scope.typeMap = {
      "1": "主叫彩印",
      "2": "被叫彩印",
      "3": "主被叫彩印",
      "4": "被叫挂机短信",
      "8": "挂机彩信",
      "7": "交互彩印"
    };
    //REQ-113 REQ-122
    //查询企业服务开关
    $scope.platformStatus  = "100";
    $scope.queryPlatformStatus();

    //查询所属行业列表
    $scope.queryIndustry($scope);

    $scope.initBusinessURLContainer($scope);

    $('#addHotlineContent').on('hidden.bs.modal', function (e) {
        $rootScope.$apply(function () {
          if ($scope.uploader) {
            $scope.uploader.reset();
          }
          $scope.showUpload = false;
          $scope.businessLicenseURL_ = "";
          $scope.fileUrl_ = "";
          $scope.urlList_ = [];
          //清空表单验证
          $scope.myForm.$setPristine();
        })


    })
    $('#addHotlineContent').on('show.bs.modal', function (e) {
          $scope.showUpload = true;

    })


    //状态信息
    $scope.hotlineStatusMap = {
      "1": "审核失败",
      "2": "待审核",
      "3": "审核通过",
      "4": "审核驳回"
    };

    $scope.unicomApproveStatusMap = {
    		"1": "审核失败",
            "-1": "--",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
    }

    $scope.telecomApproveStatusMap = {
      "null": "待审核",
      "-1": "--",
      "0": "审核通过",
      "1": "审核失败"
    }
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      },
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      },
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    $scope.auditStatusChoise = [
      {
        id: "",
        name: "不限"
      },
      {
        id: 2,
        name: "待审核"
      },
      {
        id: 3,
        name: "审核通过"
      },
      {
        id: 4,
        name: "审核驳回"
      },
      {
        id: 1,
        name: "审核失败"
      },
    ];
    $scope.subServChoise = [
      {
        id: "",
        name: "不限"
      }
    ];
    //初始化搜索条件
    $scope.initSel = {
      contentNo:"",
      subServType:"",
      auditStatus:""
    };
  //配置的企业id
    $scope.ids=["分省企业id","分省企业id","分省企业id"];
    $scope.addHotlineContentInfo = {};
    $scope.queryHotLineList('', 'search');
    $scope.querySyncServiceRule();
    $scope.queryHotlineContentInfoList();
    // $scope.checkenterpriseid();
    $scope.isProvincial = ($scope.loginRoleType=='provincial');

    $scope.choseIndex = 5;
    if ($scope.enterpriseType =='5' && $scope.isSuperManager)
    {
    	var proSupServerType = $.cookie('proSupServerType');
        $scope.proSupServerType = $.cookie('proSupServerType');
        if (proSupServerType)
        {
            var value = JSON.parse(proSupServerType);
            for (var i = 0; i < value.length; i++) {
	            var index = value[i];
	            if (!$scope.isZYZQ &&index == 30)
	            {
	            	$scope.choseIndex = i;
	            }
                if ($scope.isZYZQ && index == 76)
                {
                  $scope.choseIndex = i;
                }
            }
        }
    }
  };


  //其他资质文件下载
  $scope.exportFile = function (downloadUrl) {
    var req = {
      "param":{
        "path": downloadUrl,
        "token": $.cookie("token"),
        "isExport": 0
      },
      "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
      "method":"get"
    }
    CommonUtils.exportFile(req);
  };

  //导出文件
  $scope.exportContentFile = function () {

    var req = {
      "param":{"req":JSON.stringify($scope.queryHotlineContentInfoListTemp),"type":4},
      "url":"/qycy/ecpmp/ecpmpServices/contentService/downContentInfoCsvFileService",
      "method":"get"
    }

    if($scope.queryHotlineContentInfoListTemp != undefined)
    {
      CommonUtils.exportFile(req);
    }
  }


  $scope.changeSubServerType = function () {
	  var subServTypeChoise = $("#select").val();
	  //判断是否为被叫挂机短信
	  if("number:4" == subServTypeChoise || "number:4.1" == subServTypeChoise || "number:4.2" == subServTypeChoise)
	  {
		  $scope.addHotlineContentInfo.sceneDesc = "";
		  $scope.number = 750;
		  $scope.dsc = "SIGNATURE_HOTLINE_CONTENTDESC1";
		  $scope.msg = "请输入彩印内容1~750字";
	  }else
	  {
		  $scope.number = 70;
		  $scope.dsc = "SIGNATURE_HOTLINE_CONTENTDESC";
		  $scope.msg = "请输入彩印内容1~70字";
	  }
	  // $scope.checkenterpriseid();
  }

  $scope.deleteCtnOrFile = function (index) {
    $scope.colorContentAndFileList.splice(index, 1);
  }


  //导入号码
  $scope.importHotLinePop = function (item) {
    $scope.selectedItem = item;
    $scope.importContentID = item.contentID;
    $scope.contentVal = item.content;
    $('#impotHotLineNoPop').modal();
    $('#impotHotLineNoPop').on('hidden.bs.modal', function () {
      $rootScope.$apply(function () {
        $("#filePicker").find("span").text("导入文件");
        if ($scope.uploader) {
          $scope.uploader.reset();
        }
        $scope.errorInfo = "";
        $scope.fileName = "";
        $scope.fileUrl = '';
      })
    });
  };
  $scope.commitImportHotLineNo = function () {
    var req = {
      "templateID": $scope.selectedItem.contentID,
      "enterpriseID": $scope.enterpriseID,
      "operatorID": $scope.operatorID,
      "path": $scope.fileUrl,
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/batchAddTemplateHotlineRel",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $('#impotHotLineNoPop').modal("hide");
            $scope.tip = "导入成功";
            $('#myModal').modal();
          } else if (data.url) {
            $('#impotHotLineNoPop').modal("hide");
            $scope.tip = data.failNum + "条导入失败，请查看失败文件";
            $('#myModal').modal();
            var req1 = {
              "param": {
                "path": data.url,
                "token": $scope.token,
                "isExport": 0
              },
              "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
              "method": "get"
            }
            CommonUtils.exportFile(req1);
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
          $scope.queryHotLineList('', 'search');
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }
  $scope.goback = function () {
    $('.close').click();
  }
  // 新增热线内容弹窗
  $scope.addHotlineContent = function () {
    $scope.operate = 'add';
    $scope.addHotlineContentInfo.content = '';
    $scope.isSensitive = false;
    $scope.hotMsisdnVali = true;
    $scope.hotMsisdnExist = false;
    $scope.contentVali = true;
    $scope.changeSubServerType();

    //新增对于弹框的判断
    // if ($scope.hotlineList == null)
    // {
    //   $scope.tip = "1030120800";
    //   $('#myModal').modal();
    //   return;
    // }

    // $scope.addHotlineContentInfo.hotlineNo = $scope.hotlineList[0].hotlineNo;

    //初始化 运营商勾选转态
    var platformStatus = $scope.platformStatus;
    // var platformStatus = $scope.platformStatus;
    $scope.checkEnterpriseWithoutSignAdd(platformStatus);
    // $scope.initAddHotlineContentPlatforms();
    $scope.addHotlineContentInfo.signature = "";
    $scope.addHotlineContentInfo.sceneDesc = "";

    
    //初始化所属行业
    $scope.selectedIndustryID = "";
    $scope.selectedIndustry = "";
    //初始化营业执照
    $scope.businessLicenseURL_ = "";
    //初始化其他资质
    $scope.colorContentAndFileList = [];
    $scope.fileUrl_ = "";
    $scope.urlList_ = [];

    $scope.hotMsisdnDesc = '';
    $scope.contentDesc = '';
    $('#addHotlineContent').modal();
  };
  // 编辑热线内容弹窗
  $scope.updateHotlineContent = function (item) {
    $scope.addHotlineContentInfo.signature = "";
    $scope.businessLicenseURL_ = "";
    $scope.colorContentAndFileList = [];

    $scope.changeSubServerType();

    $scope.checkEnterpriseWithoutSignEdit(item);
    $scope.fileUrl_ = "";
    $scope.urlList_ = [];
    $scope.urlList_2 = [];
    //待审核状态不可修改
    if (item.approveStatus == 2) {
      $scope.tip = "**********";
      $('#myModal').modal();
      return;
    }
    $scope.operate = 'update';
    $scope.contentVali = true;
    $scope.addHotlineContentInfo = {};
    $scope.hotMsisdnVali = true;
    $scope.hotMsisdnExist = false;
    $scope.addHotlineContentInfo.subServType = item.subServType;
    $scope.addHotlineContentInfo.contentID = item.contentID;
    $scope.addHotlineContentInfo.content = item.content || '';
    $scope.hotMsisdnDesc = '';
    $scope.contentDesc = '';

    
    //初始化signatrue;
    $scope.addHotlineContentInfo.signature = item.signature;
    $scope.addHotlineContentInfo.sceneDesc = item.sceneDesc;

    //初始化所属行业
    $scope.selectedIndustryID = item.industryType;
    if($scope.industryList){
        jQuery.each($scope.industryList, function (i, e) {
            if (e.industryID == item.industryType) {
                $scope.selectedIndustry = e;
            }
          });
          $scope.changeIsSensitive($scope.selectedIndustry)

    }
    //初始化营业执照
    if(item.businessLicenseURL){
        $scope.businessLicenseURL_ = item.businessLicenseURL;
        $scope.fileUrl_ = CommonUtils.formatPic(item.businessLicenseURL).review;
        $scope.urlList_ = [$scope.fileUrl_];
	$scope.urlList_2 = [$scope.fileUrl_];
    }
    //初始化其他资质
    if (item.certificateUrlList && item.certificateUrlList.length > 0) {
      for (var j in item.certificateUrlList) {
        var certiparam = item.certificateUrlList[j];
        var name = certiparam.substring(certiparam.lastIndexOf("/")+1);
        $scope.colorContentAndFileList.push({
          frameFileUrl: certiparam,
          formatFrameFileUrl: CommonUtils.formatPic(certiparam).download,
          filename:name
        })
      }
    }


    $('#addHotlineContent').modal();
  };
  // 删除热线内容弹窗
  $scope.deleteHotlineContent = function (item) {
    $scope.selectedItemDel = item;
    $('#deleteHotlineContent').modal();
  };

   //暂停热线内容
    $scope.suspendHotlineContent = function (item, operaterType) {
      console.log(item);
      var removeReq = {
        "operaterType": operaterType,   //操作类型：0启动，1暂停
        "servType": "2",
        "contentID": item.contentID
      };

      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/contentService/suspendContent",
        data: JSON.stringify(removeReq),
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '**********') {
              $scope.queryHotlineContentInfoList();
            } else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
            $scope.tip = '1030120500';
            $('#myModal').modal();
          })
        }
      });
    }
  // 新增号码弹窗
  $scope.addHotLinePop = function (item) {
    $scope.hotlineMsisdnAdd = "";
    if ($scope.hotlineList.length == 0) {
      $scope.tip = "1030120800";
      $('#myModal').modal();
      return;
    }
    $scope.selectedItem = item;
    $scope.pageInfo[1].pageSize = '10';
    $scope.queryHotLineList(item, 'search');
    $('#addHotlinePop').modal();
  };
  // 号码管理弹窗
  $scope.manageHotLinePop = function (item) {
    $scope.hotlineMsisdnDel = "";
    $scope.selectedItem = item;
    $scope.pageInfo[2].pageSize = '10';
    $scope.queryContentRelObjectList(item, 'search');
    $('#manageHotLinePop').modal();
  };
  $scope.queryContentRelObjectList = function (item, condition) {
    $scope.hasChoseDelHotLine = false;
    if (condition != 'justPage') {
      var req = {
        "enterpriseID": parseInt($scope.enterpriseID),
        "contentID": item.contentID,
        "ownerType": 2,
        "hotlineNo": $scope.hotlineMsisdnDel,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[2].pageSize),
          "isReturnTotal": "1",
        }
      };
      $scope.pageInfo[2].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.reqTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[2].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[2].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/queryContentRelObjectList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.contentBelongOrgList = result.contentBelongOrgList || [];
            if ($scope.contentBelongOrgList.length > 0) {
              for (var i in $scope.contentBelongOrgList) {
                $scope.contentBelongOrgList[i].checked = false;
              }
            }
            $scope.pageInfo[2].totalCount = result.totalAmount || 0;
            $scope.pageInfo[2].totalPage = $scope.pageInfo[2].totalCount !== 0 ? Math.ceil($scope.pageInfo[2].totalCount / parseInt($scope.pageInfo[2].pageSize)) : 1;
          } else {
            $scope.contentBelongOrgList = [];
            $scope.pageInfo[2].currentPage = 1;
            $scope.pageInfo[2].totalCount = 0;
            $scope.pageInfo[2].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  };
  $scope.queryHotLineList = function (item, condition) {
    $scope.hasChoseAddHotLine = false;
    if (condition != 'justPage') {
      var req = {
        "enterpriseID": parseInt($scope.enterpriseID),
        "servTypeList": [2],
        "hotlineNo": $scope.hotlineMsisdnAdd,
        "page": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[1].pageSize),
          "isReturnTotal": "1",
        }
      };

      $scope.pageInfo[1].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.reqTemp;
      req.page.pageNum = parseInt($scope.pageInfo[1].currentPage);
      req.page.pageSize = parseInt($scope.pageInfo[1].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/queryHotlineList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.hotlineList = result.hotlineList || [];
            for (var i in $scope.hotlineList) {
              $scope.hotlineList[i].checked = false;
            }
            $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;
          } else {
            $scope.hotlineList = [];
            $scope.pageInfo[1].currentPage = 1;
            $scope.pageInfo[1].totalCount = 0;
            $scope.pageInfo[1].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  };
  $scope.selectAllHotLine = function () {
    $scope.chooseAllAddHotLine = !$scope.chooseAllAddHotLine;
    if ($scope.chooseAllAddHotLine) {
      for (var i in $scope.hotlineList) {
        $scope.hotlineList[i].checked = true;
      }
    } else {
      for (var i in $scope.hotlineList) {
        $scope.hotlineList[i].checked = false;
      }
    }
  }
  $scope.selectAllDelHotLine = function () {
    $scope.chooseAllDelHotLine = !$scope.chooseAllDelHotLine;
    if ($scope.chooseAllDelHotLine) {
      for (var i in $scope.contentBelongOrgList) {
        $scope.contentBelongOrgList[i].checked = true;
      }
    } else {
      for (var i in $scope.contentBelongOrgList) {
        $scope.contentBelongOrgList[i].checked = false;
      }
    }
  }
  $scope.singleOrBatchAddHotLine = function (condition, item) {
    //单个添加热线号码
    var req = {
      "templateHotlineRel": {
        "templateID": $scope.selectedItem.contentID,
        "enterpriseID": parseInt($scope.enterpriseID)
      }
    };
    if (condition == 'single') {
      req.templateHotlineRel.hotlineList = [item.hotlineNo.toString()]
    } else if (condition == 'batch') {//批量添加
      req.templateHotlineRel.hotlineList = [];
      for (var i in $scope.hotlineList) {
        if ($scope.hotlineList[i].checked) {
          req.templateHotlineRel.hotlineList.push($scope.hotlineList[i].hotlineNo.toString())
        }
      }
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/addTemplateHotlineRel",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          $scope.tip = data.resultCode;
          if (data.resultCode == '**********') {
            $scope.tip = '添加成功';
            $('#myModal').modal();
            $scope.queryHotLineList($scope.selectedItem, 'search');
          } else {
            $scope.tip = data.resultCode;
            if ($scope.tip == '1030120051') {
              $scope.tip = '热线内容中热线号码已关联';
            }
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });

  }
  $scope.sureDelHotLine = function (condition, item) {
    $scope.singleOrBatch = condition;
    $scope.delItem = item;
    $('#deleteHotLinePop').modal();
  }
  $scope.singleOrBatchDelHotLine = function (condition) {
    $('#deleteHotLinePop').modal('hide');
    //单个删除热线号码
    var req = {
      "templateHotlineRel": {
        "templateID": $scope.selectedItem.contentID,
        "enterpriseID": parseInt($scope.enterpriseID)
      }
    };
    if (condition == 'single') {
      req.templateHotlineRel.hotlineList = [$scope.delItem.hotlineNo.toString()]
    } else if (condition == 'batch') {//批量添加
      req.templateHotlineRel.hotlineList = [];
      for (var i in $scope.contentBelongOrgList) {
        if ($scope.contentBelongOrgList[i].checked) {
          req.templateHotlineRel.hotlineList.push($scope.contentBelongOrgList[i].hotlineNo.toString())
        }
      }
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/deleteTemplateHotlineRel ",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.tip = '删除成功';
            $('#myModal').modal();
            $scope.queryContentRelObjectList($scope.selectedItem, 'search');
            $scope.chooseAllDelHotLine = false;
          } else {
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });

  }
  /*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
  $scope.$watch('addHotlineContentInfo.signature',function(){
      $scope.checkHotlineContent();
  },true)
  //212企管平台彩印签名长度优化需求
  $scope.checkHotlineContent = function () {
    $scope.contentVali = true;
    var subServTypeChoise = $("#select").val();
    if ($scope.addHotlineContentInfo.content != null && $scope.addHotlineContentInfo.content != '') {
        var sign = 0;
        if ($scope.addHotlineContentInfo.signature != null && $scope.addHotlineContentInfo.signature != '') {
            sign = $scope.addHotlineContentInfo.signature.length + 2;
        }
        var content = $scope.addHotlineContentInfo.content.length;
        var str_len = sign + content;
        if (str_len > $scope.number) {
        	$scope.contentVali = false;
        } else {
            $scope.contentVali = true;
        }
  	    if("number:4" == subServTypeChoise || "number:4.1" == subServTypeChoise || "number:4.2" == subServTypeChoise)
  	    {
  		    $scope.dsc = "SIGNATURE_HOTLINE_CONTENTDESC1";
  	    }else{
  		  $scope.dsc = "SIGNATURE_HOTLINE_CONTENTDESC";
  	    }
    } else {
        $scope.sensitiveWords = [];
        $scope.isSensitive = false;
        $scope.sensitiveWordsStr = "";
        $scope.contentVali = false;
        if("number:4" == subServTypeChoise || "number:4.1" == subServTypeChoise || "number:4.2" == subServTypeChoise)
  	    {
  		    $scope.dsc = "HOTLINE_CONTENTDESC1";
  	    }else{
  		  $scope.dsc = "HOTLINE_CONTENTDESC";
  	    }
    }
    
    if (!$scope.contentVali) {
      $scope.contentDesc = $scope.dsc;
    } else {
      $scope.contentDesc = "";
    }
  };
  //敏感词校验
  $scope.sensitiveCheck = function () {
    $scope.checkHotlineContent();
    if (!$scope.contentVali || !$scope.hotMsisdnVali) {
      // return;
    } else {
      $scope.sensitiveWords = [];
      $scope.isSensitive = false;
      $scope.sensitiveWordsStr = "";
      var req = {
        "content": $scope.addHotlineContentInfo.content || '',
      };
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
        data: JSON.stringify(req),
        success: function (result) {
          $rootScope.$apply(function () {
            var data = result.result;
            if (data.resultCode == '1030120017') {
              $scope.sensitiveWords = result.sensitiveWords || [];
              if ($scope.sensitiveWords.length > 0) {
                // 110迭代：
                $scope.isSensitive = true;
                $scope.sensitiveWordsStr = $scope.sensitiveWords.join('、');
              } else {
                $scope.isSensitive = false;
              }
            } else if (data.resultCode == '**********') {
              $scope.sensitiveWords = [];
              $scope.isSensitive = false;
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
                $scope.tip = '1030120500';
                $('#myModal').modal();
              }
          )
        }
      });
    }

  };
  $scope.sensitiveChecknew = function (content, index) {
      if (!content) {
          return;
      }
      content = content.replace(/\s/g, '');
      var req = {
          "content": content || '',
      };
      $scope.sensitiveWords[index] = [];
      $scope.sensitiveWordsStr[index] = '';
      $scope.isSensitive[index] = false;
      RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
          async: false,
          data: JSON.stringify(req),
          success: function (result) {
              // $rootScope.$apply(function () {
              var data = result.result;
              if (data.resultCode == '1030120017') {
                  $scope.sensitiveWords[index] = result.sensitiveWords || [];
                  if ($scope.sensitiveWords[index].length > 0) {
                      $scope.isSensitive[index] = true;
                      $scope.sensitiveWordsStr[index] = $scope.sensitiveWords[index].join('、');
                  } else {
                      $scope.isSensitive[index] = false;
                  }
              } else if (data.resultCode == '**********') {
                  $scope.sensitiveWords[index] = [];
                  $scope.isSensitive[index] = false;
              } else {
                  $scope.isfeedbackType = false;
                  $scope.tip = data.resultCode;
                  $('#myModal').modal();
              }
              // })
          },
          error: function () {
              // $rootScope.$apply(function () {
              $scope.isfeedbackType = false;
              $scope.tip = '1030120500';
              $('#myModal').modal();
              // })
          }
      });
  };
  
  // 2109：提交时，若运营商选中异网（联通、电信），则二次确认
  $scope.diffNetAuthMaterialsConfirm = function () {
  	if ($scope.signatureRequired == '1') {
  		$('#diffNetAuthMaterials').modal();
      } else {
      	$scope.beforeCommit();
      }
  }
  $scope.diffNetAuthMaterialsUploaded = function () {
  	$('#diffNetAuthMaterialsCancel').click();
  	$scope.beforeCommit();
  }
  
  //确定提交前敏感词校验
  $scope.beforeCommit = function () {
      if ($scope.addHotlineContentInfo.content != null && $scope.addHotlineContentInfo.content != '') {
          var sign = 0;
          if ($scope.addHotlineContentInfo.signature != null && $scope.addHotlineContentInfo.signature != '') {
              sign = $scope.addHotlineContentInfo.signature.length + 2;
          }
          var content = $scope.addHotlineContentInfo.content.length;
          var str_len = sign + content;
          var totalNum = $scope.addHotlineContentInfo.subServType == '4'?750:70;
          if ($scope.flag) {
              totalNum = 748;
          }
          if (str_len > totalNum) {
              $scope.tip = totalNum != 70 ? '1030120085' : '1030120084';
              $('#myModal').modal();
              return;
          }
      }
    $scope.checkHotlineContent();
    if (!$scope.contentVali || !$scope.hotMsisdnVali) {
      return;
    }
    var req = {
      "content": $scope.addHotlineContentInfo.content || '',
    };
    // 110迭代：提交前取消敏感词校验，直接进行提交
    $scope.createHotlineContent();
    /*RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030120017') {
            $scope.sensitiveWords = result.sensitiveWords || [];
            if ($scope.sensitiveWords.length > 0) {
              $scope.isSensitive = true;
              $scope.sensitiveWordsStr = $scope.sensitiveWords.join('、');
            } else {
              $scope.isSensitive = false;
              $scope.createHotlineContent();
            }
          } else if (data.resultCode == '**********') {
            $scope.sensitiveWords = [];
            $scope.isSensitive = false;
            $scope.createHotlineContent();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = '1030120500';
              $('#myModal').modal();
              $scope.createHotlineContent();
            }
        )
      }
    });*/
  }
  //新增(编辑)热线内容
  $scope.createHotlineContent = function () {
    $scope.addHotlineContentInfo.operatorID = parseInt($scope.operatorID);
    $scope.addHotlineContentInfo.enterpriseID = parseInt($scope.enterpriseID);
    $scope.addHotlineContentInfo.enterpriseName = $scope.enterpriseName;
//    $scope.addHotlineContentInfo.subServType = parseInt($scope.addHotlineContentInfo.subServType);
    $scope.addHotlineContentInfo.subServType = $scope.addHotlineContentInfo.subServType;


    $scope.addHotlineContentInfo.platforms = $scope.platforms;
    $scope.addHotlineContentInfo.signature = $scope.addHotlineContentInfo.signature;
    $scope.addHotlineContentInfo.industryType = $scope.selectedIndustryID;
    $scope.addHotlineContentInfo.businessLicenseURL = $scope.businessLicenseURL_;
    //提交前清空之前的其他资质list，防止重复
    $scope.certificateUrlList = [];
    jQuery.each($scope.colorContentAndFileList, function (i, e) {
      $scope.certificateUrlList.push(e.frameFileUrl)
    });
    $scope.addHotlineContentInfo.certificateUrlList = $scope.certificateUrlList;
    $scope.addHotlineContentInfo.sceneDesc = $scope.addHotlineContentInfo.sceneDesc;

    // if($scope.hotlineList){
    //   jQuery.each($scope.hotlineList, function (i, e) {
    //         if (e.hotlineNo == $scope.addHotlineContentInfo.hotlineNo) {
    //           $scope.addHotlineContentInfo.hotlineID = e.id;
    //         }
    //       });
    // }
    var req = {
      "hotlineContent": $scope.addHotlineContentInfo
    };
    var serviceUrl = '/ecpmp/ecpmpServices/hotlineService/addHotlineContent';
    if ($scope.operate == 'update') {
      serviceUrl = '/ecpmp/ecpmpServices/hotlineService/updateHotlineContent';
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: serviceUrl,
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.queryHotlineContentInfoList();
            $('#addHotlineContentCancel').click();
          } else {
            $('#addHotlineContentCancel').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#addHotlineContentCancel').click();
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }

  //删除热线内容
  $scope.delHotlineContent = function () {
    var item = $scope.selectedItemDel;
    console.log(item);
    var removeReq = {
      "operaterType":"2",    //1名片，2热线，3广告
      "id": item.contentID
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
      data: JSON.stringify(removeReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $('#deleteHotlineContentCancel').click();
            //$scope.selectedList.splice($.inArray(item.contentID, $scope.selectedList), 1);
            $scope.queryHotlineContentInfoList();
          } else {
            $('#deleteHotlineContentCancel').click();
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#deleteHotlineContentCancel').click();
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }
  //数组去重
  $scope.uniq = function (array) {
    var temp = [];
    var l = array.length;
    if (l > 0) {
      for (var i = 0; i < l; i++) {
        for (var j = i + 1; j < l; j++) {
          if (array[i].id === array[j].id) {
            i++;
            j = i;
          }
        }
        temp.push(array[i]);
      }
    }
    return temp;
  };
  //查询子业务类型可选项
  $scope.querySyncServiceRule = function () {
    var serverTime = CommonUtils.getServerTime();
    $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
    //下拉框(投递方式)
    $scope.subServTypeChoise = [];
    var req = {
      "enterpriseID": parseInt($scope.enterpriseID),
      "servTypes": [2]
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.stopSubServerTypeList = [];
            $scope.subServerTypeList = result.syncServiceRuleList || [];
            for (var i in $scope.subServerTypeList) {
              var item = $scope.subServerTypeList[i];
              if (item.subServType == 7) {
                continue;
              }
              //开通状态才能投递，同时筛掉subServerType为4的子业务类型
              if ($scope.nowTime <= item.expiryTime) {
                $scope.subServTypeChoise.push({
                  id: item.subServType,
                  name: $scope.typeMap[item.subServType],
                  status: item.status
                })
                if ($scope.nowTime >= item.effectiveTime && (item.status == 1 || item.status == 3)) {
                  $scope.subServChoise.push({
                    id: item.subServType,
                    name: $scope.typeMap[item.subServType]
                  })
                }
              }
            }
            for (var j = 0; j < $scope.subServTypeChoise.length; j++) {
              var item2 = $scope.subServTypeChoise[j];
              if (item2.status === 3) {
                // 记录暂停状态的子业务类型
                $scope.stopSubServerTypeList.push(item2.id)
              }
            }
            $scope.subServTypeChoise_temp = [];
            // 同一子业务类型中,有一个为暂停状态,则该类型的所有对象从数组中移除
            if ($scope.stopSubServerTypeList.length > 0) {
              for (var m = 0; m < $scope.subServTypeChoise.length; m++) {
                var subServTypeChoiseItem = $scope.subServTypeChoise[m];
                var isStop = false;
                for (var n = 0; n < $scope.stopSubServerTypeList.length; n++) {
                  var stopSubServerTypeItem = $scope.stopSubServerTypeList[n];
                  if (stopSubServerTypeItem === subServTypeChoiseItem.id) {
                    isStop = true;
                    break;
                  }
                }
                if (!isStop) {
                  $scope.subServTypeChoise_temp.push(subServTypeChoiseItem);
                }
              }
              $scope.subServTypeChoise = $scope.uniq($scope.subServTypeChoise_temp);
            } else {
              $scope.subServTypeChoise = $scope.uniq($scope.subServTypeChoise);
            }

            if ($scope.subServTypeChoise.length > 0) {
              $scope.addHotlineContentInfo.subServType = parseInt($scope.subServTypeChoise[0].id);
            }
          } else {
            $scope.subServerTypeList = [];
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.subServerTypeList = [];
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }

  //查询热线内容列表
  $scope.queryHotlineContentInfoList = function (condition) {
    if (condition != 'justPage') {
      var req = {
        "contentName": $scope.content || '',
        "enterpriseID": $scope.enterpriseID,
        "servTypeList": [2],
        "contentTypeList": [1, 2],
        "approveStatus":$scope.initSel.auditStatus,
        "getBelongOrg": 0,
        "getFrame": 0,
        "getPushTime": 0,
        "getSwitchState": 0,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1",
        }
      };
      if($scope.initSel.subServType!=''){
        req.subServTypeList=[parseInt($scope.initSel.subServType)];
      }
      if($scope.initSel.contentNo!=''){
        req.contentIDList=[parseInt($scope.initSel.contentNo)];
      }
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryHotlineContentInfoListTemp = angular.copy(req);
    } else {
      //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.queryHotlineContentInfoListTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    req.getBelongOrg = 0;
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.hotContentInfoListData = result.contentInfoList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.hotContentInfoListData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.hotContentInfoListData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }

  //REQ-113 REQ-122
    //初始化营业执照上传容器
    $scope.initBusinessURLContainer = function($scope){
        $scope.showUpload = false;
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];
        $scope.urlList_2 = [];
        //初始化营业执照上传容器
        $scope.filePicker_ = "filePicker_";
        $scope.accepttype_ = "jpg,jpeg,png";
        $scope.isValidate_ = false;
        $scope.filesize_ = 20;
        $scope.mimetypes_ = ".jpg,.jpeg,.png";
        $scope.isCreateThumbnail_ = true;
        $scope.uploadurl_ ='/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.uploadDesc_ = "仅支持一张图片，仅支持jpg，jpeg，png格式";
        $scope.numlimit_ = 1;
        $scope.uploadParam_ = {
          enterpriseId: $scope.enterpriseID ||'',
          fileUse: 'businessLicense'
        };

        $scope.$on("uploadifyid_2",function(event,fileUrl_){
            if(fileUrl_){
                $scope.urlList_ = [fileUrl_];
                $scope.urlList_2 = [CommonUtils.formatPic(fileUrl_).review];
            }else{
                $scope.urlList_ = [];
		$scope.urlList_2 = [];
            }
            $scope.businessLicenseURL_ = fileUrl_;
        });


    }


    //所属行业是否为敏感行业
    $scope.changeIsSensitive = function(selectedIndustry){
        if(selectedIndustry){
            $scope.selectedIndustryID = selectedIndustry.industryID;
            $scope.isSensitiveIndustry = selectedIndustry.isSensitiveIndustry;
        }else{
            $scope.selectedIndustryID = '';
            $scope.selectedIndustryName = '';
        }
    }

    //查询所属行业
    $scope.queryIndustry = function ($scope) {
      //默认非敏感行业:0-非敏感；1-敏感
      $scope.selectedIndustryID = '';
      $scope.isSensitiveIndustry = '';
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '**********') {
                $scope.industryList = data.industryList;
            }else {
                $scope.tip=data.result.resultCode;
                $('#myModal').modal();
              }
          })
        },
        error:function(){
            $rootScope.$apply(function(){
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                }
            )
        }
      });
    };

  //查询企业服务开关
  $scope.queryPlatformStatus = function(){
	  var req = {};
      req.id = $scope.enterpriseID;
      var pageParameter = {};
      pageParameter.pageNum = 1;
      pageParameter.pageSize = 1000;
      pageParameter.isReturnTotal = 1;
      req.pageParameter = pageParameter;
      /*查询企业列表*/
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
        data: JSON.stringify(req),
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '**********') {
            	
              $scope.enterpriseReserved10 = data.enterprise.reservedsEcpmp.reserved10;
              
              var queryServiceControlReq = {
            	        "enterpriseID": $scope.enterpriseID
            	    }
            	    $scope.signatureRequired = '0';
            	    RestClientUtil.ajaxRequest({
            	      type: 'POST',
            	      url: "/ecpmp/ecpmpServices/contentService/queryServiceControl",
            	      data: JSON.stringify(queryServiceControlReq),
            	      success: function (result) {
//            	          $rootScope.$apply(function () {
            	    	  if ($scope.enterpriseReserved10 === '111')
            	          {
            	              $scope.platformStatus = result.rxPlatformStatus;
            	          }
            	    	  else
            	          {
            	              $scope.platformStatus = result.platformStatus;
            	          }
//            	          })
            	      },
            	      error: function () {
//            	        $rootScope.$apply(function () {
            	              $scope.tip = "1030120500";
            	              $('#myModal').modal();
//            	        })
            	      }
            	    });
            } else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
            $scope.tip = '1030120500';
            $('#myModal').modal();
          })
        }
      });
  }


  $scope.checkEnterpriseWithoutSignAdd = function (platformStatus){
    //判断父企业id是否为空
    var req;
    if( $scope.parentEnterpriseID === $scope.enterpriseID){
      req = {
        "enterpriseID": $scope.enterpriseID
      };
    }else{
      req = {
        "enterpriseID": $scope.parentEnterpriseID
      };
    }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          if(result.result.resultCode == '**********'){
            $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
            if($scope.enterpriseWithoutSignListData.length === 0){
              //沒有配置过免签名,按原来的流程走
              $scope.initAddHotlineContentPlatforms(platformStatus);
            }else{
              //签名不用必填
              $scope.initAddHotlineContentPlatformsNoSign(platformStatus);
            }
          }else{
            $scope.tip=result.result.resultCode;
            $('#myModal').modal();
          }
        })

      },
      error:function(){
        $rootScope.$apply(function(data){
              $scope.tip="1030120500";
              $('#myModal').modal();
            }
        )
      }
    })
  }


  $scope.checkEnterpriseWithoutSignEdit = function (item){
    //判断父企业id是否等于子企业id
    var req;
    if( $scope.parentEnterpriseID === $scope.enterpriseID){
      req = {
        "enterpriseID": $scope.enterpriseID
      };
    }else{
      req = {
        "enterpriseID": $scope.parentEnterpriseID
      };
    }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          if(result.result.resultCode == '**********'){
            $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
            if($scope.enterpriseWithoutSignListData.length === 0){
              //沒有配置过免签名,按原来的流程走
              $scope.platformInitEdit(item);
            }else{
              //签名不用必填
              $scope.platformInitEditNoSign(item);
            }
          }else{
            $scope.tip=result.result.resultCode;
            $('#myModal').modal();
          }
        })

      },
      error:function(){
        $rootScope.$apply(function(data){
              $scope.tip="1030120500";
              $('#myModal').modal();
            }
        )
      }
    })
  }


  $scope.platformInitEdit = function (item) {
    // var platforms = $scope.platforms;
    $scope.noSign = '1';
    //初始化运营商勾选状态
    var platforms = item.platforms;
    var platformStatus = $scope.platformStatus;
    var newPlatforms = '';
    //初始化signature必填状态
    if(platforms.charAt(1)=='1'||platforms.charAt(2)=='1'){
      $scope.signatureRequired='1';
    }
    for(var i = 0;i<3;i++){
      $(".platforms .check-li").eq(i).removeAttr("style","");
      $(".platforms .check-li").find('span').eq(i).removeClass('checked');
      $('.platforms .check-li').eq(i).unbind("click");
      if(platformStatus.charAt(i)=='0'){
        $(".platforms .check-li").eq(i).css({cursor:'not-allowed',color:'gray'});
      }else{
        //初始化勾选状态
        if(platforms.charAt(i)=='1'){
          $(".platforms .check-li").find('span').eq(i).addClass('checked')
        }
        //绑定点击事件
        $('.platforms .check-li').eq(i).on('click', function () {
          if ($(this).find('span').hasClass('checked')&&
              ($(this).siblings().eq(0).find('span').hasClass('checked')||$(this).siblings().eq(1).find('span').hasClass('checked'))) {

            $(this).find('span').removeClass('checked');
          } else {
            $(this).find('span').addClass('checked')
          }
          var _platforms = '';
          for(var i = 0;i<3;i++){
            if($(".platforms .check-li").find('span').eq(i).hasClass('checked')){
              _platforms+='1';
            }else{
              _platforms+='0';
            }
          }
          if(_platforms.charAt(1)=='1'||_platforms.charAt(2)=='1'){
            $scope.signatureRequired='1';
          }else{
            $scope.signatureRequired='0';
            $scope.addHotlineContentInfo.sceneDesc = "";
          }
          $scope.platforms = _platforms;
        });
      }

      if (platforms.charAt(i)=='1' && platformStatus.charAt(i)=='1') {
        newPlatforms+='1';
      } else {
        newPlatforms+='0';
      }
    }
    $scope.platforms = newPlatforms;
  }


  $scope.platformInitEditNoSign = function (item) {
    //初始化运营商勾选状态
    var platforms = item.platforms;
    var platformStatus = $scope.platformStatus;
    var newPlatforms = '';
    if(platforms.charAt(1)=='1'||platforms.charAt(2)=='1'){
      $scope.signatureRequired='1';
    }
    for(var i = 0;i<3;i++){
      $(".platforms .check-li").eq(i).removeAttr("style","");
      $(".platforms .check-li").find('span').eq(i).removeClass('checked');
      $('.platforms .check-li').eq(i).unbind("click");
      if(platformStatus.charAt(i)=='0'){
        $(".platforms .check-li").eq(i).css({cursor:'not-allowed',color:'gray'});
      }else{
        //初始化勾选状态
        if(platforms.charAt(i)=='1'){
          $(".platforms .check-li").find('span').eq(i).addClass('checked')
        }
        //绑定点击事件
        $('.platforms .check-li').eq(i).on('click', function () {
          if ($(this).find('span').hasClass('checked')&&
              ($(this).siblings().eq(0).find('span').hasClass('checked')||$(this).siblings().eq(1).find('span').hasClass('checked'))) {

            $(this).find('span').removeClass('checked');
          } else {
            $(this).find('span').addClass('checked')
          }
          var _platforms = '';
          for(var i = 0;i<3;i++){
            if($(".platforms .check-li").find('span').eq(i).hasClass('checked')){
              _platforms+='1';
            }else{
              _platforms+='0';
            }
          }
          if(_platforms.charAt(1)=='1'||_platforms.charAt(2)=='1'){
            $scope.signatureRequired='1';
          }else{
            $scope.signatureRequired='0';
            $scope.addHotlineContentInfo.sceneDesc = "";
          }
          $scope.platforms = _platforms;
        });
      }

      if (platforms.charAt(i)=='1' && platformStatus.charAt(i)=='1') {
        newPlatforms+='1';
      } else {
        newPlatforms+='0';
      }
    }
    $scope.platforms = newPlatforms;
  }


  $scope.initAddHotlineContentPlatforms = function(platformStatus){
    $scope.platforms = platformStatus;
    $scope.noSign = '1';
    //初始化signature必填状态
    $scope.signatureRequired='0';
    if(platformStatus.charAt(1)=='1'||platformStatus.charAt(2)=='1'){
        $scope.signatureRequired='1';
    }
    for(var i = 0;i<3;i++){
        $(".platforms .check-li").eq(i).removeAttr("style","");
        $(".platforms .check-li").find('span').eq(i).removeClass('checked');
        $('.platforms .check-li').eq(i).unbind("click");
        if(platformStatus.charAt(i)=='0'){
            $(".platforms .check-li").eq(i).css({cursor:'not-allowed',color:'gray'});
        }else{
            //初始化勾选状态
            $(".platforms .check-li").find('span').eq(i).addClass('checked')
          //绑定点击事件
          $('.platforms .check-li').eq(i).on('click', function () {
              if ($(this).find('span').hasClass('checked')&&
                  ($(this).siblings().eq(0).find('span').hasClass('checked')||$(this).siblings().eq(1).find('span').hasClass('checked'))) {

                  $(this).find('span').removeClass('checked');
              } else {
                  $(this).find('span').addClass('checked')
              }
              var _platforms = '';
              for(var i = 0;i<3;i++){
                  if($(".platforms .check-li").find('span').eq(i).hasClass('checked')){
                      _platforms+='1';
                  }else{
                      _platforms+='0';
                  }
              }
              if(_platforms.charAt(1)=='1'||_platforms.charAt(2)=='1'){
                  $scope.signatureRequired='1';
              }else{
                  $scope.signatureRequired='0';
                  $scope.addHotlineContentInfo.sceneDesc = "";
              }
              $scope.platforms = _platforms;
          });
        }

    }

  }

  $scope.initAddHotlineContentPlatformsNoSign = function(platformStatus){
    $scope.platforms = platformStatus;
    //初始化signature必填状态
    $scope.signatureRequired='0';
    if(platformStatus.charAt(1)=='1'||platformStatus.charAt(2)=='1'){
      $scope.signatureRequired='1';
    }
    for(var i = 0;i<3;i++){
      $(".platforms .check-li").eq(i).removeAttr("style","");
      $(".platforms .check-li").find('span').eq(i).removeClass('checked');
      $('.platforms .check-li').eq(i).unbind("click");
      if(platformStatus.charAt(i)=='0'){
        $(".platforms .check-li").eq(i).css({cursor:'not-allowed',color:'gray'});
      }else{
        //初始化勾选状态
        $(".platforms .check-li").find('span').eq(i).addClass('checked')
        //绑定点击事件
        $('.platforms .check-li').eq(i).on('click', function () {
          if ($(this).find('span').hasClass('checked')&&
              ($(this).siblings().eq(0).find('span').hasClass('checked')||$(this).siblings().eq(1).find('span').hasClass('checked'))) {

            $(this).find('span').removeClass('checked');
          } else {
            $(this).find('span').addClass('checked')
          }
          var _platforms = '';
          for(var i = 0;i<3;i++){
            if($(".platforms .check-li").find('span').eq(i).hasClass('checked')){
              _platforms+='1';
            }else{
              _platforms+='0';
            }
          }
          if(_platforms.charAt(1)=='1'||_platforms.charAt(2)=='1'){
            $scope.signatureRequired='1';
          }else{
            $scope.signatureRequired='0';
            $scope.addHotlineContentInfo.sceneDesc = "";
          }
          $scope.platforms = _platforms;
        });
      }

    }

  }

    $scope.showBusinessURL = function(){

      $("#showBusinessURL").modal('show');

    }

      $scope.hideBusinessURL = function(){

        $("#showBusinessURL").modal('hide');

      }

  $scope.checkSignAndContent = function () {
    if ($scope.operate !== 'add') {
      $scope.checkHotlineContent();
    }else{
      if ($scope.addHotlineContentInfo.content !=='') {
        $scope.checkHotlineContent();
      }
    }
  }

}])
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])
