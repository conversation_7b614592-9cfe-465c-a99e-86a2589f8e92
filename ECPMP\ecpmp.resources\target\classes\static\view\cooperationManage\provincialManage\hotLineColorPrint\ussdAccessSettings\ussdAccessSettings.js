var app = angular.module("myApp",["util.ajax","top.menu","angularI18n", "cy.uploadify"])
app.controller("ussdSettingController", ['$scope','$rootScope','$location','RestClientUtil',
    function ($scope,$rootScope,$location,RestClientUtil) {
    $scope.init = function () {
        $scope.isSuperManager = false;
        $scope.thirdpartyListData=[];
        $scope.id = null;
        $scope.accessAccount = "";
        $scope.accessPassword = "";
        $scope.thirdAccessAccount = "";
        $scope.thirdAccessPassword = "";
        $scope.checkedEdit = false;
        $scope.callbackUrl = "";
        $scope.approveCallbackUrl = "";
        $scope.fluid = "";
        $scope.operatorID = $.cookie('accountID');
	$scope.loginRoleType=$.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType=='superrManager'||$scope.loginRoleType=='normalMangager');
        $scope.enterpriseType = $.cookie('enterpriseType')||'';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.subEnterpriseID=$.cookie('subEnterpriseID')||'';
        $scope.subEnterpriseName=$.cookie('subEnterpriseName')||'';
        //��ȡenterpriseName
        $scope.enterpriseName = $.cookie('enterpriseName');
        //�ж����յ��ӿڵ�enterpriseID,enterpriseName
        if($scope.subEnterpriseID&&$scope.enterpriseType==3){
            $scope.enterpriseID=$scope.subEnterpriseID;
            $scope.enterpriseName=$scope.subEnterpriseName;
        }
        $scope.queryThirdpartyList();
        
        $scope.choseIndex = 5;
        if ($scope.enterpriseType =='5' && $scope.isSuperManager)
        {
        	var proSupServerType = $.cookie('proSupServerType');
            $scope.proSupServerType = $.cookie('proSupServerType');
            if (proSupServerType)
            {
                var value = JSON.parse(proSupServerType);
                for (var i = 0; i < value.length; i++) {
    	            var index = value[i];
    	            if (index == 30)
    	            {
    	            	$scope.choseIndex = i;
    	            }
                }
            }
        }
    };
    $scope.hotMsisdnVali = true;
    $scope.hotMsisdnDesc = "";
    $scope.fluidSwitch=1;
    $scope.status=0;
    $scope.diffnetUssdSwitch=0;
    $scope.msgtype="0000000000";
    $scope.msgtypeArr=[0,0,0,0,0,0,0,0,0,0];
    $scope.goBack=function(){
        location.href="../hotLineManage/HotlineManagement.html";
    }
    $scope.queryThirdpartyList = function () {
        var req={
            "enterpriseID":parseInt($scope.enterpriseID),
            "pageParameter":{
                "pageNum":1,
                "pageSize":10,
                "isReturnTotal":"1",
            }
        };

        RestClientUtil.ajaxRequest({
			type: 'POST',
			url: "/ecpmp/ecpmpServices/hotlineService/queryThirdparty",
			data: JSON.stringify(req),
			success: function (result) {
				$rootScope.$apply(function () {
                    var data = result.result;
                    if(data.resultCode=='1030100000'){
                        if(result.thirdpartyList&&result.thirdpartyList.length>0){
                            $scope.thirdpartyListData=result.thirdpartyList[0];
                            $scope.id = $scope.thirdpartyListData.id;                                           //唯一标识
                            $scope.fluid = $scope.thirdpartyListData.fluid;                                     //流量控制fluid
			                $scope.status=$scope.thirdpartyListData.status||0;                                  //第三方接入状态，缺省0
                            $scope.fluidSwitch=$scope.thirdpartyListData.fluidSwitch||0;                        //流控开关，默认0
                            $scope.msgtype=$scope.thirdpartyListData.msgtype||'';                               //消息类型开关
                            $scope.msgtypeArr= $scope.msgtype? $scope.msgtype.split(''):[0,0,0,0,0,0,0,0,0,0];      //消息类型开关
                            $scope.diffnetUssdSwitch=$scope.thirdpartyListData.diffnetUssdSwitch||0;
                            if($scope.msgtypeArr.length == 8)
                            {
                            	$scope.msgtypeArr.push(0);
                            	$scope.msgtypeArr.push(0);
                            }
                            //REQ-REQ-378_企管、中央平台USSD下线业务改造开发需求，msgtype去除的枚举值对应位默认置0（第1、3、5、7、8位默认置0）
                            $scope.msgtypeArr[0] = '0';
                            $scope.msgtypeArr[2] = '0';
                            $scope.msgtypeArr[4] = '0';
                            $scope.msgtypeArr[6] = '0';
                            $scope.msgtypeArr[7] = '0';
                        }
                    }else{
                        $scope.tip=data.resultCode;
                        $('#myModal').modal();
                    }
				})
            },
            error:function(){
                $rootScope.$apply(function () {
                    $scope.tip="**********";
                    $('#myModal').modal();
                })
            }
		});
    }
    $scope.changeStatus=function(params){
        if(!$scope.isSuperManager){
            return;
        }
        if(params=='fluidSwitch'){
            $scope.fluidSwitch= $scope.fluidSwitch==1? 0: 1;
        }else if(params=='status'){
            $scope.status= $scope.status==2? 0: 2;
        }else if(params=='diffnetUssdSwitch'){
            $scope.diffnetUssdSwitch= $scope.diffnetUssdSwitch==1? 0: 1;
        }
    }
    $scope.changemsgType=function(index){
        if(!$scope.isSuperManager){
            return;
        }
        $scope.msgtypeArr[index]= $scope.msgtypeArr[index]==1? 0:1;
    }
    $scope.saveUssd = function () {
        $scope.saveThirdpartyInfo = {
            "id":parseInt($scope.id),
            "platformID":parseInt($scope.enterpriseID),
            "fluid":$scope.fluid,
            "fluidSwitch":$scope.fluidSwitch,
            "status":$scope.status,
            "msgtype": $scope.msgtypeArr.join(''),
            "operatorID":parseInt($scope.operatorID),
            "diffnetUssdSwitch":0, //REQ-REQ-378_企管、中央平台USSD下线业务改造开发需求,diffnetUssdSwitch 默认置为0
        };
        console.log($scope.saveThirdpartyInfo);
        var req = {
            "thirdparty":$scope.saveThirdpartyInfo,
            "operateType":'2',
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/saveThirdparty",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.tip = 'COMMON_SAVESUCCESS';
					    $('#myModal').modal();
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }

                })
            },
            error: function (err) {
                $rootScope.$apply(function () {
                    $scope.tip='**********';
                    $('#myModal').modal();
                })
            }
        });
    }
}]);
app.config(['$locationProvider', function($locationProvider) {
    $locationProvider.html5Mode({
        enabled:true,
        requireBase:false
    });
}]);