<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MonquotaDeliverDayStatMapper">
    <resultMap id="MonquotaDeliveryDayStatWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MonquotaDeliveryDayStatWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="num" column="num" javaType="java.lang.Integer" />        
        <result property="statDate" column="statDate" javaType="java.lang.String" />
        <result property="ydpxDeliveryCount" column="ydpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltpxDeliveryCount" column="ltpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxpxDeliveryCount" column="dxpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="ydgdDeliveryCount" column="ydgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltgdDeliveryCount" column="ltgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxgdDeliveryCount" column="dxgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="ydgcDeliveryCount" column="ydgcDeliveryCount" javaType="java.lang.Integer" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
	</resultMap>


	
	<select id="queryAll" resultMap="MonquotaDeliveryDayStatWrapper">
        SELECT COUNT(*) num, MIN(statDate) statDate, SUM(ydpxDeliveryCount) ydpxDeliveryCount, SUM(ltpxDeliveryCount) ltpxDeliveryCount, SUM(dxpxDeliveryCount) dxpxDeliveryCount, SUM(ydgdDeliveryCount) ydgdDeliveryCount, SUM(ltgdDeliveryCount) ltgdDeliveryCount, SUM(dxgdDeliveryCount) dxgdDeliveryCount, SUM(ydgcDeliveryCount) ydgcDeliveryCount, enterpriseID, msisdn FROM ecpm_t_monquota_delivery_day_stat GROUP BY msisdn, enterpriseID;
	</select>
	
    <update id="deleteAll">
        delete from ecpm_t_monquota_delivery_day_stat where status = 1;
    </update>

	<update id="deleteLastMonth">
		TRUNCATE TABLE ecpm_t_monquota_delivery_day_stat
    </update>
	<update id="updateAll">
        update ecpm_t_monquota_delivery_day_stat set status = 1 where statDate = #{statDate};
    </update>

    <insert id="insertAll">
        insert into
		ecpm_t_monquota_delivery_day_stat
		(
		statDate,
		ydpxDeliveryCount,
		ltpxDeliveryCount,
		dxpxDeliveryCount,
		ydgdDeliveryCount,
		ltgdDeliveryCount,
		dxgdDeliveryCount,
		ydgcDeliveryCount,
		createTime,
		updateTime,
		msisdn,
		enterpriseID
		)
		values
		<foreach collection="list" item="wrapper" separator=",">
			(
			#{wrapper.statDate},
			#{wrapper.ydpxDeliveryCount},
			#{wrapper.ltpxDeliveryCount},
			#{wrapper.dxpxDeliveryCount},
			#{wrapper.ydgdDeliveryCount},
			#{wrapper.ltgdDeliveryCount},
			#{wrapper.dxgdDeliveryCount},
			#{wrapper.ydgcDeliveryCount},
			now(),
			now(),
			#{wrapper.msisdn},
			#{wrapper.enterpriseID}
			)
		</foreach>
    </insert>
</mapper>