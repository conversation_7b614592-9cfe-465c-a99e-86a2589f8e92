<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.SubscribeMapper">

	<resultMap id="subscribeWrapper"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.SubscribeWrapper">
		<result property="id" column="id" javaType="java.lang.Long" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="productID" column="productID" javaType="java.lang.Integer" />
		<result property="orderItemID" column="orderItemID" javaType="java.lang.String" />
		<result property="orderID" column="orderID" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="productType" column="productType" javaType="java.lang.Integer" />
		<result property="isExperience" column="isExperience" javaType="java.lang.Integer" />
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="isLimit" column="isLimit" javaType="java.lang.Integer" />
		<result property="chargeType" column="chargeType" javaType="java.lang.Integer" />
		<result property="amount" column="amount" javaType="java.lang.Long" />
		<result property="actualUseAmount" column="actualUseAmount" javaType="java.lang.Long" />
		<result property="memberCount" column="memberCount" javaType="java.lang.Long" />
		<result property="actualUseMemberCount" column="actualUseMemberCount" javaType="java.lang.Long" />
		<result property="maxAmountPerPerson" column="maxAmountPerPerson" javaType="java.lang.Long" />
		<result property="effictiveTime" column="effictiveTime"
			javaType="java.util.Date" />
		<result property="expireTime" column="expireTime" javaType="java.util.Date" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="lastUpdateTime" column="lastUpdateTime"
			javaType="java.util.Date" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>


	<!--查询自增序列 -->
	<!-- <select id="getSubscribeID" resultType="java.lang.Integer">
		select
		nextval('ecpe_sequence_subscribe');
	</select> -->

	<!--新增订购关系 -->
	<insert id="createSubscribe">
		INSERT INTO
		ecpe_t_subscribe
		(
		id,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		<if test="reserved4!=null  and reserved4!=''">
			reserved4,
		</if>
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10)
		VALUES
		(
		next value for MYCATSEQ_ECPE_T_SUBSCRIBE,
		#{enterpriseID},
		#{productID},
		#{orderItemID},
		#{orderID},
		#{status},
		#{productType},
		#{isExperience},
		#{servType},
		#{subServType},
		#{isLimit},
		#{chargeType},
		#{amount},
		#{actualUseAmount},
		#{memberCount},
		#{actualUseMemberCount},
		#{maxAmountPerPerson},
		#{effictiveTime},
		#{expireTime},
		#{createTime},
		#{operatorID},
		#{lastUpdateTime},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		<if test="reserved4!=null  and reserved4!=''">
			#{reserved4},
		</if>
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10}
		)
	</insert>
	
	<!-- 失效时间小于当前时间，配额总量大于已使用配额数量,按条非不限 -->
	<select id="queryExpirationQuota" resultMap="subscribeWrapper">
		SELECT 
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		FROM ecpe_t_subscribe t WHERE
		t.enterpriseID IN (
		<foreach collection="orderIDList" item="orderID" separator="," index="index">
			#{orderID}
		</foreach>
		) AND t.expireTime <![CDATA[ < ]]> NOW() AND ((chargeType = 1
		AND amount <![CDATA[ > ]]> actualUseAmount AND amount !=9223372036854775807) OR (chargeType = 2
		AND memberCount <![CDATA[ > ]]> actualUseMemberCount)) AND (reserved3 = '' OR  reserved3 IS NULL) 
	</select>
	
	<!-- 过期配额 -->
	<select id="queryExpirationQuotaTotal" resultMap="subscribeWrapper">
		SELECT 
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		FROM ecpe_t_subscribe t 
		WHERE 
		t.orderID IN (
		<foreach collection="orderIDList" item="orderID" separator="," index="index">
			#{orderID}
		</foreach>
		) 
	</select>

	<!--查询订购关系列表 -->
	<select id="querySubscribeList" resultMap="subscribeWrapper">
		select
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_subscribe
		<trim prefix="where" prefixOverrides="and|or">
		<if test="id!=null">
			and id = #{id}
		</if>
		<if test="enterpriseID!=null">
			and enterpriseID = #{enterpriseID}
		</if>
		<if test="productID!=null">
			and productID = #{productID}
		</if>
		<if test="orderItemID!=null and orderItemID!=''">
			and orderItemID = #{orderItemID}
		</if>
		<if test="orderID!=null and orderID!='' ">
			and orderID = #{orderID}
		</if>
		<if test="createTime!=null">
			and createTime = #{createTime}
		</if>
		<if test="effictiveTime!=null and effictiveTime!='' and expireTime!=null and expireTime!=''">
		    and ((effictiveTime <![CDATA[ >= ]]>
			#{effictiveTime} and effictiveTime <![CDATA[ <= ]]>
			#{expireTime})
			or (expireTime <![CDATA[ >= ]]>
			#{effictiveTime} and expireTime <![CDATA[ <= ]]>
			#{expireTime})
			or ((effictiveTime <![CDATA[ <= ]]>
			#{effictiveTime} and expireTime <![CDATA[ >= ]]>
				#{expireTime}))
			)
		</if>
        <if test="effictiveTime!=null and effictiveTime!='' and (expireTime ==null or expireTime =='')">
			 and expireTime <![CDATA[ >= ]]> #{effictiveTime}
	    </if>
		<if test="(effictiveTime==null or effictiveTime =='') and expireTime!=null and expireTime!=''">
			and effictiveTime <![CDATA[ <= ]]> #{expireTime}
		</if>
		<if test="lastUpdateTime!=null and lastUpdateTime!='' ">
			and lastUpdateTime = #{lastUpdateTime}
		</if>
		<if test="status!=null">
			and status = #{status}
		</if>
		<if test="operatorID!=null">
			and operatorID = #{operatorID}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
		<if test="servType!=null and subServType!=null and servType!=3">
			<if test="chargeType == 1">
				<if test="subServType==1 or subServType==2">
					and (subServType = #{subServType} or subServType = 3)
				</if>
				<if test="subServType!=1 and subServType!=2">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="chargeType == 2 or chargeType == null">
				and subServType = #{subServType}
			</if>
		</if>
		<if test="servType!=null and subServType!=null and servType==3">
			<if test="subServType==2 or subServType==10">
				and (subServType = 2 or  subServType =10)
			</if>
			<if test="subServType==8">
				and (subServType = 8 or  subServType =10)
			</if>
		</if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="productType!=null">
			and productType = #{productType}
		</if>
		<if test="isExperience!=null">
			and isExperience = #{isExperience}
		</if>
		<if test="isLimit!=null">
			and isLimit = #{isLimit}
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		<if test="reserved2!=null  and reserved2!=''">
			and reserved2 = #{reserved2}
		</if>
		<if test="reserved3!=null  and reserved3!=''">
			and reserved3 = #{reserved3}
		</if>
		<if test="reserved4!=null  and reserved4!=''">
			and reserved4 = #{reserved4}
		</if>
		<if test="reserved5!=null  and reserved5!=''">
			and reserved5 = #{reserved5}
		</if>
		<if test="reserved6!=null  and reserved6!=''">
			and reserved6 = #{reserved6}
		</if>
		<if test="reserved7!=null  and reserved7!=''">
			and reserved7 = #{reserved7}
		</if>
		<if test="reserved8!=null  and reserved8!=''">
			and reserved8 = #{reserved8}
		</if>
		<if test="reserved9!=null  and reserved9!=''">
			and reserved9 = #{reserved9}
		</if>
		<if test="reserved10!=null  and reserved10!=''">
			and reserved10 = #{reserved10}
		</if>
		</trim>
		order by lastUpdateTime desc,id desc
		limit #{pageNo},#{pageSize}
	</select>


<!--查询订购关系列表 -->
	<select id="querySubscribeListByTime" resultMap="subscribeWrapper">
		select
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_subscribe
		<trim prefix="where" prefixOverrides="and|or">
		<if test="enterpriseID!=null">
			and enterpriseID = #{enterpriseID}
		</if>
        <if test="effictiveTime!=null">
       		 and effictiveTime <![CDATA[ <= ]]> #{effictiveTime}
	    </if>
		<if test="expireTime!=null">
			 and expireTime <![CDATA[ >= ]]> #{expireTime}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
		<if test="servType!=null and subServType!=null and servType!=3">
			<if test="chargeType == 1">
				<if test="subServType==1 or subServType==2">
					and (subServType = #{subServType} or subServType = 3)
				</if>
<!--				<if test="subServType!=1 and subServType!=2 and subServType==3">-->
<!--					and subServType in (1,2,3)-->
<!--				</if>-->
				<if test="subServType!=1 and subServType!=2">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="chargeType == 2 or chargeType == null">
				and subServType = #{subServType}
			</if>
		</if>
		<if test="servType!=null and subServType!=null and servType==3">
			<if test="subServType==2 or subServType==10">
				and (subServType = 2 or  subServType =10)
			</if>
			<if test="subServType==8">
				and (subServType = 8 or  subServType =10)
			</if>
		</if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="productType!=null">
			and productType = #{productType}
		</if>
		<if test="isExperience!=null">
			and isExperience = #{isExperience}
		</if>
		<if test="isLimit!=null">
			and isLimit = #{isLimit}
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		<if test="reserved2!=null  and reserved2!=''">
			and reserved2 = #{reserved2}
		</if>
		<if test="reserved3!=null  and reserved3!=''">
			and reserved3 = #{reserved3}
		</if>
		<if test="reserved4!=null  and reserved4!=''">
			and reserved4 = #{reserved4}
		</if>
		<if test="reserved5!=null  and reserved5!=''">
			and reserved5 = #{reserved5}
		</if>
		<if test="reserved6!=null  and reserved6!=''">
			and reserved6 = #{reserved6}
		</if>
		<if test="reserved7!=null  and reserved7!=''">
			and reserved7 = #{reserved7}
		</if>
		<if test="reserved8!=null  and reserved8!=''">
			and reserved8 = #{reserved8}
		</if>
		<if test="reserved9!=null  and reserved9!=''">
			and reserved9 = #{reserved9}
		</if>
		<if test="reserved10!=null  and reserved10!=''">
			and reserved10 = #{reserved10}
		</if>
		</trim>
		order by lastUpdateTime desc,id desc
		limit #{pageNo},#{pageSize}
	</select>

	<!--查询订购关系数量 -->
	<select id="countSubscribe" resultType="java.lang.Integer">
		SELECT
		count(0)
		from ecpe_t_subscribe
		<trim prefix="where" prefixOverrides="and|or">
		<if test="id!=null">
			and id = #{id}
		</if>
		<if test="enterpriseID!=null">
			and enterpriseID = #{enterpriseID}
		</if>
		<if test="productID!=null">
			and productID = #{productID}
		</if>
		<if test="orderItemID!=null and orderItemID!=''">
			and orderItemID = #{orderItemID}
		</if>
		<if test="orderID!=null and orderID!=''">
			and orderID = #{orderID}
		</if>
		<if test="createTime!=null">
			and createTime = #{createTime}
		</if>
		<if test="effictiveTime!=null and effictiveTime!='' and expireTime!=null and expireTime!=''">
		    and ((effictiveTime <![CDATA[ >= ]]>
			#{effictiveTime} and effictiveTime <![CDATA[ <= ]]>
			#{expireTime})
			or (expireTime <![CDATA[ >= ]]>
			#{effictiveTime} and expireTime <![CDATA[ <= ]]>
			#{expireTime})
			or ((effictiveTime <![CDATA[ <= ]]>
			#{effictiveTime} and expireTime <![CDATA[ >= ]]>
				#{expireTime}))
			)
		</if>
       <if test="effictiveTime!=null and effictiveTime!='' and (expireTime ==null or expireTime =='')">
			 and expireTime <![CDATA[ >= ]]> #{effictiveTime}
	    </if>
		<if test="(effictiveTime==null or effictiveTime =='') and expireTime!=null and expireTime!=''">
			and effictiveTime <![CDATA[ <= ]]> #{expireTime}
		</if>
		<if test="lastUpdateTime!=null and lastUpdateTime!=''">
			and lastUpdateTime = #{lastUpdateTime}
		</if>
		<if test="status!=null">
			and status = #{status}
		</if>
		<if test="operatorID!=null">
			and operatorID = #{operatorID}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
        <if test="servType!=null and subServType!=null and servType!=3">
            <if test="chargeType == 1">
				<if test="subServType==1 or subServType==2">
					and (subServType = #{subServType} or subServType = 3)
				</if>

				<if test="subServType!=1 and subServType!=2">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="chargeType == 2 or chargeType == null">
				and subServType = #{subServType}
			</if>
        </if>
        <if test="servType!=null and subServType!=null and servType==3">
            <if test="subServType==2 or subServType==10">
                and (subServType = 2 or  subServType =10)
            </if>
            <if test="subServType==8">
                and (subServType = 8 or  subServType =10)
            </if>
        </if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="productType!=null">
			and productType = #{productType}
		</if>
		<if test="isExperience!=null">
			and isExperience = #{isExperience}
		</if>
		<if test="isLimit!=null">
			and isLimit = #{isLimit}
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		<if test="reserved2!=null  and reserved2!=''">
			and reserved2 = #{reserved2}
		</if>
		<if test="reserved3!=null  and reserved3!=''">
			and reserved3 = #{reserved3}
		</if>
		<if test="reserved4!=null  and reserved4!=''">
			and reserved4 = #{reserved4}
		</if>
		<if test="reserved5!=null  and reserved5!=''">
			and reserved5 = #{reserved5}
		</if>
		<if test="reserved6!=null  and reserved6!=''">
			and reserved6 = #{reserved6}
		</if>
		<if test="reserved7!=null  and reserved7!=''">
			and reserved7 = #{reserved7}
		</if>
		<if test="reserved8!=null  and reserved8!=''">
			and reserved8 = #{reserved8}
		</if>
		<if test="reserved9!=null  and reserved9!=''">
			and reserved9 = #{reserved9}
		</if>
		<if test="reserved10!=null  and reserved10!=''">
			and reserved10 = #{reserved10}
		</if>
		</trim>
	</select>

	<!--查询订购关系列表 -->
	<select id="querySubscribeListByDebutQuota" resultMap="subscribeWrapper">
		select
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_subscribe
		<trim prefix="where" prefixOverrides="and|or">
		<if test="id!=null">
			and id = #{id}
		</if>
		<if test="enterpriseID!=null">
			and enterpriseID = #{enterpriseID}
		</if>
		<if test="deductingDate!=null">
			and effictiveTime <![CDATA[ <= ]]> #{deductingDate}
		</if>
		<if test="deductingDate!=null">
			and expireTime <![CDATA[ >= ]]>  #{deductingDate}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
		<if test="servType!=null and subServType!=null and servType!=3">
			<if test="chargeType == 1">
				<if test="subServType==1 || subServType==2">
					and (subServType = #{subServType} or  subServType =3)
				</if>
				<if test="subServType!=1 and subServType!=2">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="chargeType == 2">
				and subServType = #{subServType}
			</if>
		</if>
		<if test="servType!=null and subServType!=null and servType==3">
			<if test="subServType==2 or subServType==10">
				and (subServType = 2 or  subServType =10)
			</if>
			<if test="subServType==8">
				and (subServType = 8 or  subServType =10)
			</if>
		</if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="orderByNoEffective!=null">
	        and expireTime <![CDATA[<]]> #{expireTime}
	    </if>
	    <if test="orderIDList != null and orderIDList.size() > 0" >
				and orderID in (
				<foreach collection="orderIDList" item="orderID" separator="," index="index">
					#{orderID}
				</foreach>
				)
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		</trim>
		<if test="orderByDebut!=null">
		   order by isExperience desc,expireTime asc,id asc
		</if>
	    <if test="orderByRecover!=null">
	        order by isExperience asc,expireTime desc,id desc
	    </if>

	    <if test="orderByNoEffective!=null">
	        order by expireTime desc,id desc
	        limit 1
	    </if>
	</select>

	<!-- 更新账本信息 -->
	<update id="updateSubscribeInfo">
		update ecpe_t_subscribe
		set
		<if test="actualUseAmount != null">
			actualUseAmount = actualUseAmount + #{actualUseAmount},
		</if>
		<if test="actualUseMemberCount != null">
			actualUseMemberCount = actualUseMemberCount + #{actualUseMemberCount},
		</if>
		<if test="lastUpdateTime != null">
			lastUpdateTime=#{lastUpdateTime}
		</if>
		where id=#{id}
	</update>

	<!-- 批量更新增加订购关系配额信息 -->
	<update id="updateSuscribeInfosYDY" parameterType="java.util.List">
		update ecpe_t_subscribe
		set
		<if test="reserved7 != null">
			reserved7 = if(reserved7 is null or reserved7 &lt; 0,0,reserved7) +  #{actualUseAmount},
		</if>
		<if test="actualUseAmount != null">
			actualUseAmount = actualUseAmount + #{actualUseAmount},
		</if>
		<if test="lastUpdateTime != null">
			lastUpdateTime=#{lastUpdateTime}
		</if>
		where enterpriseID = #{enterpriseID} and reserved8 = #{reserved8}
	</update>

    <!-- 批量更新增加订购关系配额信息 -->
	<update id="batchUpdateSuscribeInfos" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="subscribeWrapper" open="" separator=";">
			update ecpe_t_subscribe set
			<if test="subscribeWrapper.actualUseAmount != null">
			   actualUseAmount= actualUseAmount + #{subscribeWrapper.actualUseAmount},
			</if>
			<if test="subscribeWrapper.actualUseMemberCount != null">
			   actualUseMemberCount= actualUseMemberCount + #{subscribeWrapper.actualUseMemberCount},
			</if>
			lastUpdateTime= #{subscribeWrapper.lastUpdateTime}
			where id = #{subscribeWrapper.id}
		</foreach>
	</update>

		<update id="updateSubScribe">
		UPDATE ecpe_t_subscribe SET
		<if test="enterpriseID!=null">enterpriseID=#{enterpriseID},</if>
		<if test="amount!=null">amount=#{amount},</if>
		<if test="memberCount!=null">memberCount=#{memberCount},</if>
		<if test="actualUseAmount!=null">actualUseAmount=#{actualUseAmount},</if>
		<if test="actualUseMemberCount!=null">actualUseMemberCount=#{actualUseMemberCount},</if>
		<if test="maxAmountPerPerson!=null">maxAmountPerPerson=#{maxAmountPerPerson},</if>
		<if test="effictiveTime!=null">effictiveTime=#{effictiveTime},</if>
		<if test="expireTime!=null">expireTime=#{expireTime},</if>
		<if test="reserved3!=null">reserved3 = #{reserved3},</if>
			<if test="reserved5!=null">reserved5 = #{reserved5},</if>
			<if test="status!=null">status = #{status},</if>
		<if test="lastUpdateTime!=null">lastUpdateTime = #{lastUpdateTime}</if>

			<trim prefix="where" prefixOverrides="and|or">
			<if test="id!=null">
				and id = #{id}
			</if>
			<if test="enterpriseID!=null">
				and enterpriseID = #{enterpriseID}
			</if>
			<if test="servType!=null">
				and servType = #{servType}
			</if>
			<if test="subServType!=null">
				and subServType = #{subServType}
			</if>
			<if test="isExperience!=null">
				and isExperience = #{isExperience}
			</if>
			<if test="productID!=null">
				and productID = #{productID}
			</if>
			<if test="orderItemID!=null and orderItemID!=''">
				and orderItemID = #{orderItemID}
			</if>
			<if test="orderID!=null and orderID!=''">
				and orderID = #{orderID}
			</if>

			<if test="status!=null and status == 2">
				and (status IS NULL or status != 2)
			</if>

		</trim>
	</update>

	<select id="queryQuotaByOrder" resultMap="subscribeWrapper">
		select
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_subscribe
		<trim prefix="where" prefixOverrides="and|or">
			<if test="orderIDList!=null and orderIDList.size() > 0" >
				and orderID in (
				<foreach collection="orderIDList" item="orderID" separator="," index="index">
					#{orderID}
				</foreach>
				)
			</if>
			<if test="servType!=null">
				and servType = #{servType}
			</if>
			<if test="subServType!=null">
			       <if test="subServType==1 || subServType==2">
				         and (subServType = #{subServType} or  subServType =3)
				   </if>
				   <if test="subServType!=1 and subServType!=2">
				   	     and subServType = #{subServType}
				   </if>
			</if>
			<if test="chargeType!=null">
				and chargeType = #{chargeType}
			</if>
			<if test="enterpriseID!=null">
                and enterpriseID = #{enterpriseID}
            </if>
			<if test="effictiveTime!=null and effictiveTime!='' and expireTime!=null and expireTime!=''">
			    and ((effictiveTime <![CDATA[ >= ]]>
				#{effictiveTime} and effictiveTime <![CDATA[ <= ]]>
				#{expireTime})
				or (expireTime <![CDATA[ >= ]]>
				#{effictiveTime} and expireTime <![CDATA[ <= ]]>
				#{expireTime})
				or ((effictiveTime <![CDATA[ <= ]]>
				#{effictiveTime} and expireTime <![CDATA[ >= ]]>
					#{expireTime}))
				)
			</if>
			<if test="reserved1!=null and reserved1!=''">
				and reserved1 = #{reserved1}
			</if>
		</trim>
	</select>
	<!--查询订购关系列表 -->
	<select id="querySubscribes" resultMap="subscribeWrapper">
		select
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_subscribe
		<trim prefix="where" prefixOverrides="and|or">
		<if test="id!=null">
			and id = #{id}
		</if>
		<if test="enterpriseID!=null">
			and enterpriseID = #{enterpriseID}
		</if>
		<if test="productID!=null">
			and productID = #{productID}
		</if>
		<if test="orderItemID!=null and orderItemID!=''">
			and orderItemID = #{orderItemID}
		</if>
		<if test="orderID!=null and orderID!='' ">
			and orderID = #{orderID}
		</if>
		<if test="createTime!=null">
			and createTime = #{createTime}
		</if>
		<if test="effictiveTime!=null and effictiveTime!='' and expireTime!=null and expireTime!=''">
		    and ((effictiveTime <![CDATA[ >= ]]>
			#{effictiveTime} and effictiveTime <![CDATA[ <= ]]>
			#{expireTime})
			or (expireTime <![CDATA[ >= ]]>
			#{effictiveTime} and expireTime <![CDATA[ <= ]]>
			#{expireTime})
			or ((effictiveTime <![CDATA[ <= ]]>
			#{effictiveTime} and expireTime <![CDATA[ >= ]]>
				#{expireTime}))
			)
		</if>
        <if test="effictiveTime!=null and effictiveTime!='' and (expireTime ==null or expireTime =='')">
			 and expireTime <![CDATA[ >= ]]> #{effictiveTime}
	    </if>
		<if test="(effictiveTime==null or effictiveTime =='') and expireTime!=null and expireTime!=''">
			and effictiveTime <![CDATA[ <= ]]> #{expireTime}
		</if>
		<if test="lastUpdateTime!=null and lastUpdateTime!='' ">
			and lastUpdateTime = #{lastUpdateTime}
		</if>
		<if test="status!=null and status!=1">
			and status = #{status}
		</if>
			<if test="status!=null and status == 1">
				and (status = #{status} or status is null)
			</if>
		<if test="operatorID!=null">
			and operatorID = #{operatorID}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
		<if test="servType!=null and subServType!=null and servType!=3">
			<if test="chargeType == 1">
				<if test="subServType==1 || subServType==2">
					and (subServType = #{subServType} or  subServType =3)
				</if>
				<if test="subServType!=1 and subServType!=2">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="chargeType == 2">
				and subServType = #{subServType}
			</if>
		</if>
		<if test="servType!=null and subServType!=null and servType==3">
			<if test="subServType==2 or subServType==10">
				and (subServType = 2 or  subServType =10)
			</if>
			<if test="subServType==8">
				and (subServType = 8 or  subServType =10)
			</if>
		</if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="productType!=null">
			and productType = #{productType}
		</if>
		<if test="isExperience!=null">
			and isExperience = #{isExperience}
		</if>
		<if test="isLimit!=null">
			and isLimit = #{isLimit}
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		<if test="reserved2!=null  and reserved2!=''">
			and reserved2 = #{reserved2}
		</if>
		<if test="reserved3!=null  and reserved3!=''">
			and reserved3 = #{reserved3}
		</if>
		<if test="reserved4!=null  and reserved4!=''">
			and reserved4 = #{reserved4}
		</if>
		<if test="reserved5!=null  and reserved5!=''">
			and reserved5 = #{reserved5}
		</if>
		<if test="reserved6!=null  and reserved6!=''">
			and reserved6 = #{reserved6}
		</if>
		<if test="reserved7!=null  and reserved7!=''">
			and reserved7 = #{reserved7}
		</if>
		<if test="reserved8!=null  and reserved8!=''">
			and reserved8 = #{reserved8}
		</if>
		<if test="reserved9!=null  and reserved9!=''">
			and reserved9 = #{reserved9}
		</if>
		<if test="reserved10!=null  and reserved10!=''">
			and reserved10 = #{reserved10}
		</if>
		</trim>
		order by lastUpdateTime desc,id desc
	</select>
	<update id="updateMaxAmountPerPerson">
		UPDATE ecpe_t_subscribe t
		SET t.maxAmountPerPerson = (SELECT s.maxAmountPerPerson FROM ecpe_t_product s WHERE s.ID = #{pkgID})
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID!=null">
				and t.enterpriseID = #{enterpriseID}
			</if>
			<if test="servType!=null">
				and t.servType = #{servType}
			</if>
			<if test="subServType!=null">
				and t.subServType = #{subServType}
			</if>
			<if test="chargeType!=null">
				and t.chargeType = #{chargeType}
			</if>
		</trim>
	</update>

	<!--查询订购关系列表 -->
	<select id="queryByEnterpriseType" resultMap="subscribeWrapper">
		select
			a.ID,
			a.enterpriseID,
			a.productID,
			a.orderItemID,
			a.orderID,
			a.status,
			a.productType,
			a.isExperience,
			a.servType,
			a.subServType,
			a.isLimit,
			a.chargeType,
			a.amount,
			a.actualUseAmount,
			a.memberCount,
			a.actualUseMemberCount,
			a.maxAmountPerPerson,
			a.effictiveTime,
			a.expireTime,
			a.createTime,
			a.operatorID,
			a.lastUpdateTime,
			a.extInfo,
			a.reserved1,
			a.reserved2,
			a.reserved3,
			a.reserved4,
			a.reserved5,
			a.reserved6,
			a.reserved7,
			a.reserved8,
			a.reserved9,
			a.reserved10
		from ecpe_t_subscribe a force index(idx_subscribe_lastUpdateTime)
		STRAIGHT_JOIN ecpe_t_order b on a.enterpriseID = b.enterpriseID
		where
			b.reserved1 = #{enterpriseType}
			<if test="servType!=null">
				and a.servType = #{servType}
			</if>
			<if test="servType!=null and subServType!=null and servType!=3">
				<if test="chargeType == 1">
					<if test="subServType==1 or subServType==2">
						and (subServType = #{subServType} or subServType = 3)
					</if>
					<if test="subServType!=1 and subServType!=2">
						and subServType = #{subServType}
					</if>
				</if>
				<if test="chargeType == 2 or chargeType == null">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="servType!=null and subServType!=null and servType==3">
				<if test="subServType==2 or subServType==10">
					and (subServType = 2 or  subServType =10)
				</if>
				<if test="subServType==8">
					and (subServType = 8 or  subServType =10)
				</if>
			</if>
			<if test="chargeType!=null">
				and chargeType = #{chargeType}
			</if>
		order by a.lastUpdateTime desc, a.id desc
		limit #{pageNo}, #{pageSize}
	</select>

	<!--查询订购关系列表 -->
	<select id="queryByEnterpriseTypeForCount" resultType="java.lang.Integer">
		select
			count(0)
		from ecpe_t_subscribe a
		left join ecpe_t_order b on a.enterpriseID = b.enterpriseID
		where
			b.reserved1 = #{enterpriseType}
			<if test="servType!=null">
				and a.servType = #{servType}
			</if>
			<if test="servType!=null and subServType!=null and servType!=3">
				<if test="chargeType == 1">
					<if test="subServType==1 or subServType==2">
						and (subServType = #{subServType} or subServType = 3)
					</if>
					<if test="subServType!=1 and subServType!=2">
						and subServType = #{subServType}
					</if>
				</if>
				<if test="chargeType == 2 or chargeType == null">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="servType!=null and subServType!=null and servType==3">
				<if test="subServType==2 or subServType==10">
					and (subServType = 2 or  subServType =10)
				</if>
				<if test="subServType==8">
					and (subServType = 8 or  subServType =10)
				</if>
			</if>
			<if test="chargeType!=null">
				and chargeType = #{chargeType}
			</if>
	</select>

	<update id="updateSubScribeResetUse">
		UPDATE ecpe_t_subscribe SET
		<if test="actualUseAmount!=null">actualUseAmount=#{actualUseAmount},</if>
		<if test="reserved7!=null">reserved7 = #{reserved7}</if>
		<trim prefix="where" prefixOverrides="and|or">
			<if test="servType!=null">
				and servType = #{servType}
			</if>
			<if test="reserved8!=null and reserved8!=''">
				and reserved8 = #{reserved8}
			</if>
		</trim>
	</update>
	<update id="updateSubScribeResetUseZYZQ">
		UPDATE ecpe_t_subscribe SET
		<if test="actualUseAmount!=null">actualUseAmount=#{actualUseAmount},</if>
		<if test="reserved7!=null">reserved7 = if(reserved6!=null,#{reserved7},reserved7)</if>
		<trim prefix="where" prefixOverrides="and|or">
			<if test="reserved9!=null and reserved9!=''">
				and reserved9 = #{reserved9}
			</if>
		</trim>
	</update>

	<delete id="deleteSubscribeByOrderID">
		delete from ecpe_t_subscribe where orderID = #{orderID}
	</delete>

</mapper>