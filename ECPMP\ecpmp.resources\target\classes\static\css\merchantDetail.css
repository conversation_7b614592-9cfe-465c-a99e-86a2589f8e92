@charset "UTF-8";
[ng-block] {
	display:none !important;
}
body {
	background: #F8F8F8;
}
.modal-body .center {
	text-align: center;
}
.modal-footer{
       text-align: center;
   }
.form-horizontal .control-label {
	padding-top: 3px;
	padding-bottom: 15px;
	margin-bottom: 0;
	text-align: right;
}
.orderManage-nav {
	margin: 0 20px;
}

.enterPrise{
	overflow-x:hidden;
}

.enterprise-title {
	margin: 20px;
	color: #383838;
	font-size: 16px;
}

.cooper-tab {
	margin: 0 0px;
	background: #fff;
	border-radius: 2px;
	padding: 36px 10px 16px;
}

.cooper-tab .form-control {
	/* width: 512px; */
}

.cooperation-head {
      padding: 20px;
   }
   
   .cooperation-head .frist-tab {
       font-size: 16px;
   }
   
   .cooperation-head .second-tab {
       font-size: 14px;
   }
.downLoadHref{
	  position:absolute;
	  left:130px;
	  top:75px;
	  white-space:nowrap;
}
/*交互不合法样式*/
form .ng-invalid.ng-dirty {
    border-color: red;
}

.form-group .control-label icon {
	color: #ff254c;
	vertical-align: sub;
	margin-right: 2px;
}

.form-group div {
	line-height: 34px;
}

.form-group div li {
	/* display: inline-block; */
	margin-right: 10px;
	padding-right: 10px;
	cursor: pointer;
}
.form-group div button.btn.download-btn {
	position:absolute;
	left:130px;
	top:75px;
	white-space:nowrap;
	background:#fff;
	color:#337ab7;
}
.form-group div li span {
	vertical-align: middle;
	margin-right: 4px;
}

.form-group .data-time {
	display: flex;
}
.form-group .data-time .to{
	padding: 0 20px;
}
.form-group .date .form-control {
	width: 200px;
}
.enterprise-btn{
	margin: 40px 20px;
	margin-left:17%
}
.enterprise-btn .btn{
	margin-right: 20px;
}
.upload-img{
	padding: 10px 0;
}
.upload-img .images{
	position: relative;
	width: 10px;
	height: 10px;
	display: inline-block;
}
.upload-img .images .delete-btn{
	position: absolute;
	width: 20px;
	height: 20px;
	border-radius: 20px;
	top: -150px;
	right: -110px;
	background: url(../assets/images/otherIcons20.png)no-repeat;
	background-position: -20px 0;
	cursor: pointer;
}
.upload-img img{
	width: 100px;
	height: 100px;
	object-fit: cover;
	cursor: pointer;
	margin-right: 20px;
}
.upload-img .btn{
	vertical-align: bottom;
}
.panel-body{
	display: inline-block;
	padding: 0 15px 0 0;
	vertical-align: bottom;
}
.show-img{
	display: inline-block;
}