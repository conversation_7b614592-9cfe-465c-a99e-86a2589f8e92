<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <link rel="stylesheet" type="text/css" href="../../../../../../css/bootstrap.min.css" />
    <link href="../../../../../../css/reset.css" rel="stylesheet" />
    <link href="../../../../../../css/searchList.css" rel="stylesheet" />
    <link rel="stylesheet" type="text/css" href="../../../../../../css/groupList.css" />
    <script type="text/javascript" src="../../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../../frameworkJs/angular-sanitize.js"></script>
    <script type="text/javascript" src="../../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript" src="../../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../../../service/utils/service-common.js"></script>
    <!-- 引入分页组件 -->
    <link rel="stylesheet" type="text/css" href="../../../../../../directives/page/page.css" />
    <link href="../../../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../../../directives/page/page.js"></script>
    <script type="text/javascript" src="../../../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="../../../../../../frameworkJs/webuploader.js"></script>
	<script src="../../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<link href="../../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
	<link rel="stylesheet" type="text/css" href="../../../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="queryTemplateList.js"></script>
    <style>
        .cooperation-manage .form-inline {
		padding: 20px 0px 20px 10px;
		overflow:hidden;
    }

	.form-horizontal .control-label {
		padding-top: 7px;
		white-space: nowrap;
    }
     body .cooperation-manage .form-group select{
        width: 100%;
        margin-left: 0
    }
    .form-inline input.form-control{
        width: 100%;
    }
	.handle {
		overflow: hidden;
    }
    .handle ul li.impoMebr {
        color: #7360e2;
    }
    #filePicker div:nth-child(2) {
			width: 100% !important;
			height: 100% !important;
		}
</style>
</head>

<body ng-app='myApp' ng-controller='templateListController' ng-init="init();" class="body-min-width">
    <div class="cooperation-manage">
        <div class="cooperation-head">
            <span ng-show="!isSuperManager" class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>
            <span ng-show="isSuperManager" class="frist-tab" ng-bind="'COMMON_PROVINCEENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'CONTENT_TEMP_MANAGE'|translate"></span>
        </div>

        <top:menu ng-if="isSuperManager" apply-val="{{proSupServerType}}" chose-index="{{choseIndex}}" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/cardColorPrint/templateManage/templateListManage"
                list-index="37"></top:menu>
                <top:menu ng-class="{true:'',false:'second-topmenu'}[isProvincial]" chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/cardColorPrint/templateManage/templateListManage"
                list-index="38"></top:menu>
        <div class="cooperation-search">
            <form class="form-horizontal">
                <div class="form-group form-inline">
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label for="contentName" ng-bind="'TEMPLATE_CONTENT'|translate"></label>
                    </div>
                    <div style="min-width:180px;" class="col-lg-2 col-md-2 col-sm-3 col-xs-3">
                        <input style="min-width:165px;" type="text" autocomplete="off" class="form-control" id="contentName" placeholder="{{'TEMPLATE_PLH_KEYWORDS'|translate}}"
                            ng-model="initSel.contentInfo">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label for="contentstatus" ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></label>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                        <select class="form-control" ng-model="initSel.subServType" ng-options="x.id as x.name for x in subServChoise"></select>
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                        <label for="contentstatus" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></label>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                        <select class="form-control" ng-model="initSel.auditStatus" ng-options="x.id as x.name for x in auditStatusChoise"></select>
                    </div>
                    <div style="max-width:120px;" class="col-lg-2 col-xs-2  col-sm-2 col-md-2" style="margin-left: 20px;">
                        <button ng-click="queryContentInfoList()" type="submit" class="btn search-btn">
                            <icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="gotoAdd()">
                <icon class="add-iocn"></icon><span ng-bind="'COMMON_ADD'|translate"></span><span>模板</span>
            </button>
        </div>

        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'TEMPLATE_ALL'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th style="width:9%" ng-bind="'COMMON_ID'|translate"></th>
                        <th style="width:10%" ng-bind="'TEMPLATE_CONTENT'|translate"></th>
                        <th style="width:10%" ng-bind="'CONTENT_POSTOBJ'|translate"></th>
                        <th style="width:7%" ng-bind="'CONTENT_POSTDAY'|translate"></th>
                        <th style="width:11%" ng-bind="'CONTENT_POSTTIME'|translate"></th>
                        <th style="width:10%" ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></th>
                        <!--<th style="width:10%" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>-->
                        <th style="width:33%" ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in contentInfoData">
                        <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
                        <td>
                            <span ng-if="item.signature&&item.signature!=null&&item.signature!=''" title="{{item.content.length>512? item.content.slice(0,509)+'...' : item.content }}">【{{item.signature}}】{{item.content}}</span>
                            <span ng-if="item.signature==null||item.signature==''" title="{{item.content.length>512? item.content.slice(0,509)+'...' : item.content }}">{{item.content}}</span>

                        </td>
                        <td><span title="{{orgNameList(item.contentBelongOrgList)}}">{{orgNameList(item.contentBelongOrgList)}}</span></td>
                        <td><span title="{{deliveryDateMap(item.deliveryDate)}}">{{deliveryDateMap(item.deliveryDate)}}</span></td>
                        <td><span title="{{pushTime(item.contentPushTime.startTime,item.contentPushTime.endTime)}}">{{pushTime(item.contentPushTime.startTime,item.contentPushTime.endTime)}}</span></td>
                        <td><span title="{{subServTypeMap[item.subServType]}}">{{subServTypeMap[item.subServType]}}</span></td>
                        <!--<td><span title="{{approveStatusMap[item.approveStatus]}}">{{approveStatusMap[item.approveStatus]}}</span></td>-->
                        <td>
                            <div class="handle">
                                <ul>
                                    <!-- <li class="delete" ng-click="close(item)">
                                        <icon class="delete-icon"></icon><span ng-bind="'COMMON_CLOSE'|translate"></span>
                                    </li> -->
                                    <li class="query" ng-click="gotoDetail(item)">
                                        <icon class="query-icon"></icon><span ng-bind="'COMMON_WATCH'|translate"></span>
                                    </li>
                                    <li class="query" ng-click="gotoModify(item)">
                                        <icon class="edit-icon"></icon><span ng-bind="'COMMON_EDIT'|translate"></span>
                                    </li>
                                    <li class="query" ng-click="queryVars(item)">
                                        <icon class="query-icon"></icon><span ng-bind="'COMMON_WATCHVARS'|translate"></span>
                                    </li>
                                    <!--  <li class="impoMebr" ng-click="importTem(item)">
                                        <icon class="import-icon"></icon><span ng-bind="'TEMPLATE_IMPORT_V'|translate"></span>
                                    </li>  -->
                                    <li  ng-show="!(item.templateNum > 0)" class="delete" ng-click="deleteTempContent(item)">
                                        <icon class="delete-icon"></icon><span ng-bind="'COMMON_DELETE'|translate"></span></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="contentInfoData.length<=0">
                        <td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div>
            <ptl-page tableId="0" change="queryContentInfoList('justPage')"></ptl-page>
        </div>
    </div>

    <!--关闭确认弹出框-->
    <div class="modal fade" id="closeTemplatePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_CLOSE'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal">
                        <div class="form-group">
                            <div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
                                <div class="text-center">
                                    <span ng-bind="'TEMPLATE_CLOSE_TIP'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-click="closeTemplate()" ng-bind="'COMMON_OK'|translate"></button>
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="closeCancel" ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>

    <!--导入模板变量弹出框-->
    <div class="modal fade" id="impoTemplatePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'TEMPLATE_IMPORTV'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal form-inline">
                        <div class="form-group" style="width: 596px;">
                            <div class="form-group" style="width: 596px;">
                                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;"
                                    ng-bind="'COMMON_FILENAME'|translate"></label>
                                <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
                                    <input type="text" class="form-control" ng-model="fileName" id="addGroupName"
                                        placeholder="请导入.xlsx表格格式文件" style="width: 100%;" ng-disabled="true" />
                                    <!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
                                </div>
                                <cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
                                    uploadifyid="uploadifyid" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
                                    formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
                                    urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
                                </cy:uploadifyfile>
                            </div>
                            <div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 92px;" ng-show="errorInfo==''"></div>
                            <div style="color:#ff0000;margin: 10px 0 0 50px;" ng-show="errorInfo!=''">
                                <span class="uplodify-error-img"></span>
                                <span ng-bind="errorInfo|translate"></span>
                            </div>
                            <div class="downloadRow col-sm-10" style="margin: 20px 0 0 29px;">
                                <a target="_blank" href="/qycy/ecpmp/assets/importTemplateVar.xlsx" class="downMod" style="margin-right: 40px;"
                                    ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
                                <span style="color: #705de1 !important; font-size: 12px;">提示：</span><span style="color: #705de1 !important; font-size: 12px;" ng-bind="'TEMPLATE_IMPORTTIP'|translate"></span>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-click="importTemplate()" ng-disabled="errorInfo!==''||fileUrl==''">确认导入</button>
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="addOrgCancel">返回</button>
                </div>
            </div>
        </div>
    </div>
    
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer" style="text-align:center">
                        <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                    </div>
                </div>
            </div>
        </div>
	<!--名片内容确认删除弹窗-->
    <div class="modal fade bs-example-modal-sm" id="deleteTempContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content" style="width:390px">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838' ng-bind="'BUSINESSCARD_SUREDELETEHOTLINE'|translate"></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                            ng-click="delTempCardContent()"></button>
                    <button id="deleteTempCardCancel" type="submit" class="btn " data-dismiss="modal"
                            aria-label="Close" id="addTempCancel" ng-bind="'NO'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    
    <!--变量查询弹出框-->
	<div class="modal fade" id="varListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="z-index: 1039;">
		<div role="document" class="modal-dialog dialog-1000">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'TEMPLATE_QUERY'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="form-horizontal">
						<div class="form-group">
							<div class="row">
								<label style="width: 133px;" class="col-lg-3 col-xs-3 control-label"
											 ng-bind="'GROUP_MEMBMSISDN'|translate"></label>
								<div class="col-lg-5 col-xs-6 col-sm-6 col-md-5">
									<input type="text" class="form-control" id=""
												 placeholder="{{'GROUP_PLEASEINPUTMEMBMSISDN'|translate}}"
												 ng-model="msisdn">
								</div>
                                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                                    <label for="contentstatus" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></label>
                                </div>
                                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                                    <select class="form-control" ng-model="varListAuditStatus" ng-options="x.id as x.name for x in auditStatusChoise"></select>
                                </div>

								<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
									<button class="btn bg_purple search-btn btn1" ng-click="queryTemplateVarContent(selectedItem)">
										<span class="icon btnIcon search"></span>
										<span ng-bind="'COMMON_SEARCH'|translate"></span>
									</button>
								</div>
							</div>
						</div>
					</div>

					<div class="add-table" style="margin-left:12px;margin-top:12px;">
						<button class="btn" ng-disabled="selectedListTemp.length==0"
										style="width:105px; margin-left:10px;color:#7360e1"
										type="button" ng-click="removeContentPop('all')" ng-bind="'GROUP_BATCHDELETE'|translate"></button>
					</div>

					<div class="coorPeration-table" style="max-height: 530px;overflow: auto">
						<table class="table table-striped table-hover">
							<thead>
							<tr>
								<th style="padding-left:30px;width: 10%;"><input type="checkbox" ng-model="allChoose" ng-click="ifSelected()"></th>
								<th ng-bind="'GROUP_MEMBMSISDN'|translate"></th>
                                <th ng-bind="'GROUP_PROVINCE'|translate"></th>
                                <th ng-bind="'DELIVERYTYPE'|translate"></th>
								<th ng-bind="'TEMPLATE_CONTENT'|translate"></th>
								<th ng-bind="'DELIVERY_CONTENT'|translate"></th>
								<th ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate"></th>
                        		<th ng-bind="'CONTENTAUDIT_AUDITSTATE_UNICOM'|translate"></th>
                        		<th ng-bind="'CONTENTAUDIT_AUDITSTATE_TELECOM'|translate"></th>
                        		<th ng-bind="'CONTENTAUDIT_AUDITADVICE_MOVE'|translate"></th>
                        		<th ng-bind="'CONTENTAUDIT_AUDITSTATE_UNICOM'|translate"></th>
                        		<th ng-bind="'CONTENTAUDIT_AUDITADVICE_TELECOM'|translate"></th>
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="item in varListData">
								<td><input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked"  ng-disabled="item.approveStatus == 2"></td>
								<td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
                                <td><span title="{{item.provinceName}}-{{item.cityName}}">{{item.provinceName}}-{{item.cityName}}</span></td>
                                <td><span title="{{subServTypeMap[item.subServType]}}">{{subServTypeMap[item.subServType]}}</span></td>
								<td><span title="{{item.templateText}}">{{item.templateText}}</span></td>
								<td><span title="{{item.valText}}">{{item.valText}}</span></td>
								<td><span title="{{unicomApproveStatusMap[item.mobileApproveStatus]}}">{{unicomApproveStatusMap[item.mobileApproveStatus]}}</span></td>
								<td><span title="{{unicomApproveStatusMap[item.unicomApproveStatus]}}">{{unicomApproveStatusMap[item.unicomApproveStatus]}}</span></td>
                        		<td><span title="{{unicomApproveStatusMap[item.telecomApproveStatus]}}">{{unicomApproveStatusMap[item.telecomApproveStatus]}}</span></td>
                                <td ng-show = "item.servType != '1' && item.servType != '5'"><span title="{{item.approveIdea}}">{{item.approveIdea}}</span></td>
                                <td ng-show = "(item.servType == '1' || item.servType == '5')&&item.subServType!='3'"><span title="{{item.mobileApproveIdea}}">{{item.mobileApproveIdea}}</span></td>
                                <td ng-show = "item.subServType == '3' ">
                                    <span ng-show=" item.mobileApproveIdea != null && item.mobileApproveIdea != ''" title="主叫：{{item.mobileApproveIdea.split('||||')[0]}}&#10;被叫：{{item.mobileApproveIdea.split('||||')[1]}}">
                                        主叫：{{item.mobileApproveIdea.split('||||')[0]}}<br/>被叫：{{item.mobileApproveIdea.split('||||')[1]}}
                                    </span>
                                </td>
                                <td>
                                      <span
                                              ng-bind-html="CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType, item.subServType).htmlVersion"
                                              title="{{ CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType,item.subServType).titleVersion}}">
                                      </span>
                                </td>
                                <td>
                                      <span
                                              ng-bind-html="CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType, item.subServType).htmlVersion"
                                              title="{{ CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType,item.subServType).titleVersion}}">
                                      </span>
                                </td>
                            </tr>
							<tr ng-show="varListData==null || varListData.length==0">
								<td style="text-align:center" colspan="5" ng-bind="'COMMON_NODATA'|translate"></td>
							</tr>
							</tbody>
						</table>
					</div>
					<div>
						<ptl-page tableId="1" change="queryTemplateVarContent(selectedItem,'justPage')"></ptl-page>
					</div>
				</div>
				<div class="modal-footer">
				</div>
			</div>
		</div>
	</div>
	
	<!--变量删除确认框弹出框-->
	<div class="modal fade" id="deleteContentPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
								<div class="text-center">
									<span >请确认是否删除模板变量内容</span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn" ng-click="oneSelect?remove('one'):remove('all')"
									ng-bind="'COMMON_OK'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="delVarCancel"
									ng-bind="'COMMON_BACK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
</body>

</html>