<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.GlobalBlackMapper">
	<resultMap type="com.huawei.jaguar.dsdp.ecpm.dao.domain.GlobalBlackWrapper"
			   id="globalBlackWrapper">
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="operateType" column="operatetype" javaType="java.lang.String" />
	</resultMap>

	<insert id="insertGlobalBlack" >
		insert into ecpm_t_global_black
		(msisdn, createTime, updateTime, operatorID, operatetype )
		values
		( #{msisdn},#{createTime},#{updateTime},#{operatorID},#{operateType} )
	</insert>
	<update id="updateGlobalBlackByMsisdn">
		update ecpm_t_global_black set
		<trim suffixOverrides="," suffix="where msisdn = #{msisdn}">
			<if test="updateTime !=null">updateTime= #{updateTime},</if>
			operatetype= #{operateType},
			<if test="operatorID !=null">operatorID= #{operatorID},</if>
		</trim>
	</update>

	<select id="queryGlobalBlackByMsisdn" parameterType="java.lang.String" resultMap="globalBlackWrapper">
		select msisdn, createTime, updateTime, operatorID, operatetype
		from ecpm_t_global_black where msisdn = #{msisdn}
	</select>
</mapper>