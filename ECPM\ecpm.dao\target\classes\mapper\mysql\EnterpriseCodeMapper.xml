<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EnterpriseCodeMapper">
	<resultMap id="enterpriseCode"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseCodeWrapper">
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
		<result property="provinceCode" column="provinceCode" javaType="java.lang.String" />
		<result property="provinceName" column="provinceName" javaType="java.lang.String" />
		<result property="bossID" column="bossID" javaType="java.lang.String" />
		<result property="productCode" column="productCode" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

	<select id="queryEnterpriseCode" resultMap="enterpriseCode">
		select
		enterpriseID,
		enterpriseName,
		provinceCode,
		provinceName,
		bossID,
		productCode,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from ecpm_t_enterprise_code
		where provinceCode = #{provinceCode} and enterpriseID = #{enterpriseID}
		
	</select>
	

</mapper>