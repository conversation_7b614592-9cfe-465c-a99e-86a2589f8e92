<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.RemindGroupOrgRelMapper">
	    <resultMap id="remindGroupOrgRelWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.RemindGroupOrgRelWrapper">
        <result property="ID" column="ID" javaType="java.lang.Integer" />
        <result property="remindGroupID" column="remindGroupID" javaType="java.lang.String" />
        <result property="orgID" column="orgID" javaType="java.lang.Integer" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
    </resultMap>
	
	
	<insert id="batchSave">
        insert into
		ecpm_t_remind_group_org_rel
		(
		remindGroupID,
		orgID,
		createTime,
		updateTime
		)
		values
		<foreach collection="list" item="wrapper" separator=",">
			(
			#{wrapper.remindGroupID},
			#{wrapper.orgID},
			now(),
			now()
			)
		</foreach>
    </insert>
    
    <delete id="batchDelete">
    	delete from ecpm_t_remind_group_org_rel
    	where 
    	orgID = #{orgId}
    	and 
		remindGroupID in
		<foreach item="remindGroupID" index="index" collection="remindGroupIDList" open="(" separator="," close=")">
					#{remindGroupID}
		</foreach>
    </delete>
</mapper>