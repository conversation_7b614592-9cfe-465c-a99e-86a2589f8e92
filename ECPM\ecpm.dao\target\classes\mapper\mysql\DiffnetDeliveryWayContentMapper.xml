<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DiffnetDeliveryWayContentMapper">
	<resultMap id="diffnetDeliveryWayWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DiffnetDeliveryWayContentWrapper">
		<result property="ID" column="ID" javaType="java.lang.Integer" />
		<result property="contentID" column="contentID" javaType="java.lang.Long" />
		<result property="unicomWayType" column="unicomWayType" javaType="java.lang.Integer" />
		<result property="telcomWayType" column="telcomWayType" javaType="java.lang.Integer" />
		<result property="operType" column="operType" javaType="java.lang.Integer" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseCode" column="enterpriseCode" javaType="java.lang.String" />
		<result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
	</resultMap>

	<select id="queryList" resultMap="diffnetDeliveryWayWrapper">
		SELECT d.ID,
		d.contentID,
		d.unicomWayType,
		d.telcomWayType,
		d.operType,
		d.status,
		d.createTime,
		d.updateTime
		FROM ecpm_t_diffnet_delivery_way_content d
		<where>
	        <foreach collection="list" item="contentID" separator="or" index="index">
	            contentID=#{contentID}
	        </foreach>
	    </where>
	</select>
	
	<select id="queryListByCondition" resultMap="diffnetDeliveryWayWrapper">
		SELECT d.ID,
		d.contentID,
		d.unicomWayType,
		d.telcomWayType,
		d.operType,
		d.status,
		d.createTime,
		d.updateTime
		FROM ecpm_t_diffnet_delivery_way_content d
		where 1=1
		<if test="contentID !=null">
			and contentID like "%"#{contentID}"%"
		</if>
		<if test="status !=null">
			and status = #{status}
		</if>
		ORDER BY d.createTime DESC
		limit #{pageNum},#{pageSize} 
	</select>
	
	<select id="queryListFromContent" resultMap="diffnetDeliveryWayWrapper">
		SELECT d.ID,
		d.contentID,
		d.unicomWayType,
		d.telcomWayType,
		d.operType,
		d.status,
		d.createTime,
		d.updateTime,
		c.enterpriseID,
		c.enterpriseCode,
		c.enterpriseName
		FROM ecpm_t_diffnet_delivery_way_content d left join ecpm_t_content c on d.contentID = c.id
		where 1=1
		<if test="contentID !=null and contentID != ''">
			and d.contentID like "%"#{contentID}"%"
		</if>
		<if test="status !=null and status != ''">
			and d.status = #{status}
		</if>
		<if test="enterpriseID !=null and enterpriseID != ''">
			and c.enterpriseID = #{enterpriseID}
		</if>
		<if test="enterpriseName !=null and enterpriseName != ''">
			and c.enterpriseName like "%"#{enterpriseName}"%"
		</if>
		<if test="createBeginTime !=null and createBeginTime != ''">
			and d.createTime &gt;= #{createBeginTime}
		</if>
		<if test="createEndTime !=null and createEndTime != ''">
			and d.createTime &lt;= #{createEndTime}
		</if>
		ORDER BY d.createTime DESC
		limit #{pageNum},#{pageSize} 
	</select>
	
	<select id="queryTotalNumByCondition" resultType="java.lang.Integer">
		SELECT count(*)
		FROM ecpm_t_diffnet_delivery_way_content d left join ecpm_t_content c on d.contentID = c.id
		where 1=1
		<if test="contentID !=null and contentID != ''">
			and d.contentID like "%"#{contentID}"%"
		</if>
		<if test="status !=null and status != ''">
			and d.status = #{status}
		</if>
		<if test="enterpriseID !=null and enterpriseID != ''">
			and c.enterpriseID = #{enterpriseID}
		</if>
		<if test="enterpriseName !=null and enterpriseName != ''">
			and c.enterpriseName like "%"#{enterpriseName}"%"
		</if>
		<if test="createBeginTime !=null and createBeginTime != ''">
			and d.createTime &gt;= #{createBeginTime}
		</if>
		<if test="createEndTime !=null and createEndTime != ''">
			and d.createTime &lt;= #{createEndTime}
		</if>
	</select>

	<select id="queryListByList" resultMap="diffnetDeliveryWayWrapper">
		SELECT d.ID,
		d.contentID,
		d.unicomWayType,
		d.telcomWayType,
		d.operType,
		d.status,
		d.createTime,
		d.updateTime
		FROM ecpm_t_diffnet_delivery_way_content d
		<where>
	        <foreach collection="list" item="item" separator="or" index="index">
	            contentID=#{item.contentID}
	        </foreach>
	    </where>
	</select>
	
	<insert id="batchInsert" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="item" open="" separator=";">
			insert into ecpm_t_diffnet_delivery_way_content 
			(
				contentID,
				unicomWayType,
				telcomWayType,
				operType,
				status,
				createTime,
				updateTime
			) 
			VALUES
			(
				#{item.contentID},
		        #{item.unicomWayType},
		        #{item.telcomWayType},
		        #{item.operType},
		        #{item.status},
		        now(),
		        now()
			)
		</foreach>
	</insert>
	
	<update id="batchUpdate" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="item" open="" separator=";">
			UPDATE ecpm_t_diffnet_delivery_way_content 
			SET
			<trim suffixOverrides="," suffix="where ID = #{item.ID}">
			<if test="item.unicomWayType !=null">
				unicomWayType= #{item.unicomWayType},
			</if>
			<if test="item.telcomWayType !=null">
				telcomWayType= #{item.telcomWayType},
			</if>
			<if test="item.operType !=null">
				operType =#{item.operType},
			</if>
			<if test="item.status !=null">
				status =#{item.status},
			</if>
			updateTime = now()
		</trim>
		</foreach>
	</update>

	<update id="batchUpdateAndNotJudgeEmpty" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
				 item="item" open="" separator=";">
			UPDATE ecpm_t_diffnet_delivery_way_content
			SET
			<trim suffixOverrides="," suffix="where ID = #{item.ID}">
				unicomWayType= #{item.unicomWayType},
				telcomWayType= #{item.telcomWayType},
				<if test="item.operType !=null">
					operType =#{item.operType},
				</if>
				<if test="item.status !=null">
					status =#{item.status},
				</if>
				updateTime = now()
			</trim>
		</foreach>
	</update>
	
	<update id="batchUpdateByCondition">
		<foreach close=";" collection="diffnetWayContentList" index="index"
			item="item" open="" separator=";">
			UPDATE ecpm_t_diffnet_delivery_way_content 
			SET
			<trim suffixOverrides="," suffix="where ID = #{item.ID}">
			<if test="item.status !=null">
				status =#{status},
			</if>
			updateTime = now()
		</trim>
		</foreach>
	</update>
	<delete id="batchDelete" parameterType="java.util.List">
		delete from ecpm_t_diffnet_delivery_way_content
		where 
		ID in
		<foreach item="item" index="index" collection="list" open="(" separator="," close=")">
					#{item.ID}
		</foreach>
	</delete>
	
</mapper>