var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n", "service.common"])
app.controller('quotaController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {
        $scope.isSuperManager = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isAgent = loginRoleType == 'agent';
        $scope.orderCode = $location.search().orderCode || '';
        $scope.objectID = $location.search().objectID || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.anci = "";
        $scope.baoyue = "";
        $scope.gdbaoyue = "";
        $scope.gcbaoyue = "";
        $scope.guaduanAmount = "";
        $scope.guaduanAmountCUCC = "";
        $scope.guaduanAmountCTCC = "";
        $scope.guacaiAmount = "";
        $scope.hasPX_cmcc = false;
        $scope.hasPX_cucc = false;
        $scope.hasPX_ctcc = false;
        $scope.hasGD = false;
        $scope.hasGDCUCC = false;
        $scope.hasGDCTCC = false;
        $scope.hasGC = false;
        $scope.hasZC = false;

        $scope.isPXanci = false;
        $scope.isGDanci = false;
        $scope.isGDanciCUCC = false;
        $scope.isGDanciCTCC = false;
        $scope.isGCanci = false;
        $scope.isZCanci = false;

        $scope.isPXbaoyue = false;
        $scope.isGDbaoyue = false;
        $scope.isGCbaoyue = false;

        $scope.isPXLimit = false;
        $scope.isGDLimit = false;
        $scope.isGDLimitCUCC = false;
        $scope.isGDLimitCTCC = false;
        $scope.isGCLimit = false;

        $scope.cmcc = false;
        $scope.cucc = false;
        $scope.ctcc = false;
        $scope.statusMap = {
            "1": "生效",
            "2": "失效",
            "3": "预生效"
        };
        $scope.businessTypeMap = {
            "1": "名片彩印",
            "2": "热线彩印",
            "3": "广告彩印",
            "4": "群发业务"
        };

        $scope.queryOrderDetail();
    };
    $scope.goBack = function () {
        location.href = '../quotaList/quotaList.html';
    }


    $scope.showPeiE = function (selectedOrder) {
        $scope.orderItemList = selectedOrder.orderItemList;
        angular.forEach($scope.orderItemList, function (item) {
            var product = item.product;
            switch (product.subServType) {
                //主订单屏显配额为包月主叫
                case 1:
                    $scope.cmcc = true;
                    $scope.hasPX_cmcc = true;
                    $scope.isPXbaoyue = true;
                    $scope.pingxianisLimit = 1;
                    $scope.pxType = 2;
                    $scope.productName = product.productName;
                    $scope.baoyue = product.memberCount * item.quantity;
                    break;
                //主订单屏显配额为包月被叫
                case 2:
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        if (product.isLimit === 1) {
                            if (product.chargeType == 2) {
                                $scope.cmcc = true;
                                $scope.hasPX_cmcc = true;
                                $scope.isPXbaoyue = true;
                                $scope.pingxianisLimit = 1;
                                $scope.pxType = 2;
                                $scope.productName = product.productName;
                                $scope.baoyue = product.memberCount * item.quantity;
                            }
                        }
                    }
                    //联通广告屏显按次
                    if (product.reservedsEcpmp.reserved1 === "2") {
                        $scope.cucc = true;
                        $scope.hasPX_cucc = true;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_cucc = true;
                            $scope.pxType_cucc = 0;
                            $scope.pingxianisLimit_cucc = 0;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            if (product.chargeType == 1) {
                                $scope.pinxian_anci_cucc = product.amount * item.quantity;
                                $scope.isPXanci_cucc = true;
                                $scope.pxType_cucc = 1;
                                $scope.pingxianisLimit_cucc = 1;
                            }

                        }
                    }
                    //电信广告屏显按次
                    if (product.reservedsEcpmp.reserved1 === "3") {
                        $scope.ctcc = true;
                        $scope.hasPX_ctcc = true;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_ctcc = true;
                            $scope.pxType_ctcc = 0;
                            $scope.pingxianisLimit_ctcc = 0;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            if (product.chargeType == 1) {
                                $scope.pinxian_anci_ctcc = product.amount * item.quantity;
                                $scope.isPXanci_ctcc = true;
                                $scope.pxType_ctcc = 1;
                                $scope.pingxianisLimit_ctcc = 1;
                            }
                        }
                    }

                    break;
                case 3:
                    if (product.chargeType == 2)
                    {
                        //包月剩余量
                        $scope.cmcc = true;
                        $scope.hasPX_cmcc = true;
                        $scope.isPXbaoyue = true;
                        $scope.pingxianisLimit = 1;
                        $scope.pxType = 2;
                        $scope.productName = product.productName;
                        $scope.baoyue = product.memberCount * item.quantity;
                        break;
                    }
                    else
                    {
                        if (product.reservedsEcpmp.reserved1 === "1") {
                            $scope.cmcc = true;
                            $scope.hasPX_cmcc = true;
                            //主订单屏显配额为不限
                            if (product.isLimit === 0) {
                                $scope.isPXLimit = true;
                                $scope.pxType = 0;
                                $scope.pingxianisLimit = 0;
                            }
                            //主订单屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.pinxian_anci = product.amount * item.quantity;
                                $scope.isPXanci = true;
                                $scope.pxType = 1;
                                $scope.pingxianisLimit = 1;
                            }
                        }
                        if (product.reservedsEcpmp.reserved1 === "2") {
                            $scope.cucc = true;
                            $scope.hasPX_cucc = true;
                            //主订单屏显配额为不限
                            if (product.isLimit === 0) {
                                $scope.isPXLimit_cucc = true;
                                $scope.pxType_cucc = 0;
                                $scope.pingxianisLimit_cucc = 0;
                            }
                            //主订单屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.pinxian_anci_cucc = product.amount * item.quantity;
                                $scope.isPXanci_cucc = true;
                                $scope.pxType_cucc = 1;
                                $scope.pingxianisLimit_cucc = 1;
                            }
                        }
                        if (product.reservedsEcpmp.reserved1 === "3") {
                            $scope.ctcc = true;
                            $scope.hasPX_ctcc = true;
                            //主订单屏显配额为不限
                            if (product.isLimit === 0) {
                                $scope.isPXLimit_ctcc = true;
                                $scope.pxType_ctcc = 0;
                                $scope.pingxianisLimit_ctcc = 0;
                            }
                            //主订单屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.pinxian_anci_ctcc = product.amount * item.quantity;
                                $scope.isPXanci_ctcc = true;
                                $scope.pxType_ctcc = 1;
                                $scope.pingxianisLimit_ctcc = 1;
                            }
                        }
                    }
                    break;
                //主订单包含挂机短信
                case 4:
                    if (product.reservedsEcpmp.reserved1 === "1") {             //加if判断  20191107
                        $scope.cmcc = true;
                        $scope.hasGD = true;
                        //主订单挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimit = true;
                            $scope.guaduanType = 0;
                            $scope.guaduanisLimit = 0;
                        }
                        //主订单挂机短信为按次
                        if (product.isLimit === 1) {
                            if (product.chargeType == 2) {
                                $scope.isGDbaoyue = true;
                                $scope.guaduanType = 2;
                                $scope.guaduanisLimit = 1;
                                $scope.gdproductName = product.productName;
                                $scope.gdbaoyue = product.memberCount * item.quantity;
                            } else {
                                $scope.isGDanci = true;
                                $scope.guaduanType = 1;
                                $scope.guaduanisLimit = 1;
                                $scope.guaduanAmount = product.amount * item.quantity;
                            }
                        }
                    }

                    if (product.reservedsEcpmp.reserved1 === "2") {             //联通挂短
                        $scope.cucc = true;
                        $scope.hasGDCUCC = true;

                        //主订单挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimitCUCC = true;
                            $scope.guaduanTypeCUCC = 0;
                            $scope.guaduanisLimitCUCC = 0;
                        }
                        //主订单挂机短信为按次
                        if (product.isLimit === 1) {
                            $scope.isGDanciCUCC = true;
                            $scope.guaduanTypeCUCC = 1;
                            $scope.guaduanisLimitCUCC = 1;
                            $scope.guaduanAmountCUCC = product.amount * item.quantity;

                        }
                    }

                     if (product.reservedsEcpmp.reserved1 === "3") {             //电信挂短
                        $scope.ctcc = true;
                        $scope.hasGDCTCC = true;

                        //主订单挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimitCTCC = true;
                            $scope.guaduanTypeCTCC = 0;
                            $scope.guaduanisLimitCTCC = 0;
                        }
                        //主订单挂机短信为按次
                        if (product.isLimit === 1) {
                            $scope.isGDanciCTCC = true;
                            $scope.guaduanTypeCTCC = 1;
                            $scope.guaduanisLimitCTCC = 1;
                            $scope.guaduanAmountCTCC = product.amount * item.quantity;

                        }
                    }

                    break;
                case 8:
                    $scope.cmcc = true;
                    //主订单挂机彩信为不限
                    $scope.hasGC = true;
                    if (product.isLimit === 0) {
                        $scope.isGCLimit = true;
                        $scope.guacaiType = 0;
                        $scope.guacaiisLimit = 0;
                    }
                    //主订单挂机彩信为按次
                    if (product.isLimit === 1) {
                        if (product.chargeType == 2) {
                            $scope.isGCbaoyue = true;
                            $scope.guacaiType = 2;
                            $scope.guacaiisLimit = 1;
                            $scope.gcproductName = product.productName;
                            $scope.gcbaoyue = product.memberCount * item.quantity;
                        } else {
                            $scope.isGCanci = true;
                            $scope.guacaiType = 1;
                            $scope.guacaiisLimit = 1;
                            $scope.guacaiAmount = product.amount * item.quantity;
                        }
                    }
                    break;
                case 10:
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        $scope.cmcc = true;
                        $scope.hasPX_cmcc = true;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit = true;
                            $scope.pxType = 0;
                            $scope.pingxianisLimit = 0;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            $scope.pinxian_anci = product.amount * item.quantity;
                            $scope.isPXanci = true;
                            $scope.pxType = 1;
                            $scope.pingxianisLimit = 1;
                        }
                    }
                    break;
                case 16:
                   $scope.cmcc = true;
                   $scope.hasZC = true;
                   //主订单挂机短信为不限
                   if (product.isLimit === 0) {
                       $scope.isZCLimit = true;

                   }
                   //主订单挂机短信为按次
                   if (product.isLimit === 1) {
                       $scope.isZCanci = true;
                       $scope.ZCAmount = product.amount * item.quantity;

                   }
                   break;
                case 17:
                    //移动
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        $scope.cmcc = true;
                        $scope.hasGroupSendSMSCMCC = true;
                        if (product.isLimit == 1) {
                            $scope.groupSendSMSCMCCAmount = product.amount * item.quantity;
                            $scope.groupSendSMSCMCCNoLimit = false;
                        } else {
                            $scope.groupSendSMSCMCCNoLimit = true;
                        }

                    }else if(product.reservedsEcpmp.reserved1 === "2"){
                        $scope.cucc = true;
                        $scope.hasGroupSendSMSCUCC = true;
                        if (product.isLimit == 1) {
                            $scope.groupSendSMSCUCCAmount = product.amount * item.quantity;
                            $scope.groupSendSMSCUCCNoLimit = false;
                        } else {
                            $scope.groupSendSMSCUCCNoLimit = true;
                        }
                    }else if(product.reservedsEcpmp.reserved1 === "3"){
                        $scope.ctcc = true;
                        $scope.hasGroupSendSMSCTCC = true;
                        if (product.isLimit == 1) {
                            $scope.groupSendSMSCTCCAmount = product.amount * item.quantity;
                            $scope.groupSendSMSCTCCNoLimit = false;
                        } else {
                            $scope.groupSendSMSCTCCNoLimit = true;
                        }
                    }

                    break;
                default:
                    break;
            }
        })
    };

    $scope.queryOrderDetail = function () {
        var req = {
            "orderCode": $scope.orderCode,
            "isReturnOrderItem": 1,
            "objectID": $scope.objectID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                        $scope.orderInfo = data.orderList[0];
                        $scope.showPeiE($scope.orderInfo)
                    } else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();

                })
            }
        });
    }
})
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])
app.filter("newdate", function () {
    return function (date, type) {
        if (!date) {
            return '';
        }
        if (type === "ymd") {
            return date.substr(0, 4) + '-' + date.substr(4, 2) + '-' + date.substr(6, 2);
        }
        if (type === "ymdhm") {
            return date.substr(0, 4) + '-' + date.substr(4, 2) + '-' + date.substr(6, 2) + " " + date.substr(8, 2) + ':' + date.substr(10, 2)
        }

    }
});