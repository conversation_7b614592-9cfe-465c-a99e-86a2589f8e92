var app = angular.module("myApp", ["util.ajax", "service.common", "top.menu", "angularI18n", "cy.uploadifyfile"])
app.controller('replenishOrderController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {

    $scope.accepttype = "jpg,jpeg,png,xlsx,doc,pdf";
    $scope.isValidate = true;
    $scope.filesize = 20;
    $scope.mimetypes = ".jpg,.jpeg,.png,.xlsx,.doc,.pdf";
    $scope.auto = true;
    $scope.isCreateThumbnail = true;
    $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadAllTypeFile';
    $scope.uploadDesc = "必填，仅支持一张图片，仅支持jpg，jpeg，png格式";
    $scope.numlimit = 1;
    $scope.urlList1 = [];
    $scope.urlList2 = [];

    $scope.$on("uploadifyid1", function (event, fileUrl, index, broadData) {
        if (broadData) {
            if (broadData.file !== "") {
                $scope.orderDetailURL.fileName = broadData.file.name;
            } else {
                $scope.orderDetailURL.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.orderDetailURL.errorInfo = broadData.errorInfo;
        }
        $scope.orderDetailURL.upload = fileUrl;
    });
    $scope.$on("uploadifyid2", function (event, fileUrl, index, broadData) {
        if (broadData) {
            if (broadData.file !== "") {
                $scope.transferAttachURL.fileName = broadData.file.name;
            } else {
                $scope.transferAttachURL.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.transferAttachURL.errorInfo = broadData.errorInfo;
        }
        $scope.transferAttachURL.upload = fileUrl;
    });

    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        //获取enterpriseID
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.objectId = $location.search().objectId || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.operatorID = $.cookie('accountID') || '';

        //订单的生成日期获取
        var time = CommonUtils.getServerTime();
        var year = time.year;
        var month = time.month;
        var day = time.day;
        //名片彩印增补订单********
        $scope.servertime = year + month + day;

        //生效日期
        $scope.effictiveTime = "";
        //失效日期
        $scope.expireTime = "";

        $scope.anci = "";
        $scope.gdanci = "";
        $scope.gdanciCUCC = "";
        $scope.gdanciCTCC = "";
        $scope.gcanci = "";
        $scope.zcanci = "";
        $scope.gjzcanci = "";
        $scope.cxanci = "";
        //查询主订单是否存在屏显配额、挂机短信、挂机彩信配置。
        $scope.hasPX = false;
        $scope.hasPXCUCC = false;         //联通屏显
        $scope.hasPXCTCC = false;         //电信屏显
        $scope.hasGD = false;
        $scope.hasGDCUCC = false;       //add 联通挂短
        $scope.hasGDCTCC = false;       //add 电信挂短
        $scope.hasGC = false;
        $scope.hasZC = false;

        //默认屏显配额是配置的
        $scope.postPingXianCMCC = false;
        $scope.postPingXianCUCC = false;
        $scope.postPingXianCTCC = false;
        //默认挂机短信是配置的
        $scope.postGuaDuan = false;
        $scope.postGuaDuanCUCC = false;
        $scope.postGuaDuanCTCC = false;
        //默认挂机彩信是配置的
        $scope.postGuaCai = false;

        $scope.businessTypeMap = {
            "1": "名片彩印",
            "2": "热线彩印",
            "3": "广告彩印",
            "4": "企业通知"
        }

        $scope.exportFile = function (downloadUrl) {
            var req = {
                "param": {
                    "path": downloadUrl,
                    "token": $.cookie("token"),
                    "isExport": 0
                },
                "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
                "method": "get"
            }
            CommonUtils.exportFile(req);
        };


        //转账附件上传配置
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'transferAttach'
        };
        //订单明细上传配置
        $scope.uploadParam2 = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'orderDetail'
        };

        //上传的配置
        $scope.transferAttachURL = {};
        $scope.orderDetailURL = {};
//    $scope.transferAttachURL.upload = "/home/<USER>/uploadFile/10000017/orderDetail/20190509173214/down_arrow_16px_1205405_easyicon.net.png";
//    $scope.orderDetailURL.upload = "/home/<USER>/uploadFile/10000017/orderDetail/20190509173214/down_arrow_16px_1205405_easyicon.net.png"

        //初始化显示的信息
        $scope.initOrderInfo = {
            enterpriseName: $scope.enterpriseName,
            orderCode: "",//订单编号
            isExperienceVersion: 1,
            effictiveTime: "",
            orderName: "",
            expireTime: "",
            caller: {
                memberCount: '',
                maxAmountPerPerson: ''
            },
            called: {
                memberCount: '',
                maxAmountPerPerson: ''
            },
            guaduanAmount: "",
            guaduanAmountCUCC: "",
            guaduanAmountCTCC: "",
            guacaiAmount: "",
            pxByTimes: "",//屏显按次
        };
        $scope.cmcc = false;
        $scope.cucc = false;
        $scope.ctcc = false;
        $scope.hasCMCC = false;
        $scope.hasCUCC = false;
        $scope.hasCTCC = false;
        $scope.queryOrderDetail();
    };

    $scope.formatDate = function (str) {
        if (!str) {
            return 'format error';
        }
        var newDateStr = "";
        newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
        return newDateStr;
    }

    //是否展示屏显的配置
    $scope.showPingXianCMCC = function () {
        $scope.postPingXianCMCC = !$scope.postPingXianCMCC;
    }
    $scope.showPingXianCUCC = function () {
        $scope.postPingXianCUCC = !$scope.postPingXianCUCC;
    }

    $scope.showPingXianCTCC = function () {
        $scope.postPingXianCTCC = !$scope.postPingXianCTCC;
    }
    //是否展示挂短的配置
    $scope.showGuaDuan = function () {
        $scope.postGuaDuan = !$scope.postGuaDuan;
    }

    //联通是否展示挂短的配置
    $scope.showGuaDuanCUCC = function () {
        $scope.postGuaDuanCUCC = !$scope.postGuaDuanCUCC;
    }

    //电信是否展示挂短的配置
    $scope.showGuaDuanCTCC = function () {
        $scope.postGuaDuanCTCC = !$scope.postGuaDuanCTCC;
    }

    //是否展示挂彩的配置
    $scope.showGuaCai = function () {
        $scope.postGuaCai = !$scope.postGuaCai;
    }

    //相反值 val名字
    $scope.showEnable = function (val) {
        $scope[val] = !$scope[val];
    }

    $scope.accMul = function (arg1, arg2) {
        if (!arg1) {
            arg1 = 0;
        }

        if (!arg2) {
            arg2 = 0;
        }

        var m = 0;
        var s1 = arg1.toString();
        var s2 = arg2.toString();
        try {
            m += s1.split(".")[1].length
        } catch (e) {
        }
        try {
            m += s2.split(".")[1].length
        } catch (e) {
        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
    }

    $scope.accSum = function (count) {
        var monPrice = count;
        var monuUnitPrice = $scope.unitPrice;
        var memberCount = $scope.memberCount;
        return $scope.accMul($scope.accMul(monPrice, monuUnitPrice), memberCount);
    };
    $scope.accGDSum = function (count) {
        var monPrice = count;
        var monuUnitPrice = $scope.gdunitPrice;
        var memberCount = $scope.gdmemberCount;
        return $scope.accMul($scope.accMul(monPrice, monuUnitPrice), memberCount);
    };
    $scope.accGCSum = function (count) {
        var monPrice = count;
        var monuUnitPrice = $scope.gcunitPrice;
        var memberCount = $scope.gcmemberCount;
        return $scope.accMul($scope.accMul(monPrice, monuUnitPrice), memberCount);
    };


    $scope.chooseCMCC = function () {
        $scope.cmcc = !$scope.cmcc;
    }

    $scope.chooseCUCC = function () {
        $scope.cucc = !$scope.cucc;
    }
    $scope.chooseCTCC = function () {
        $scope.ctcc = !$scope.ctcc;
    }

    $scope.goBack = function () {
        location.href = '../orderList/orderList.html?enterpriseID=' + $scope.enterpriseID;
    }

    $scope.queryOrderDetail = function () {
        var req = {
            "objectID": $scope.objectId,
            "isReturnOrderItem": 1,
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.orderListInfo = result.orderList || [];
                        for (var i = 0, len = $scope.orderListInfo.length; i < len; i++) {
                            var item = $scope.orderListInfo[i];
                            //主订单详情
                            if (item.orderType == 1) {
                                $scope.initOrderInfo.orderCode = item.orderCode;
                                $scope.servType = item.servType;
                                $scope.initOrderInfo.orderName = $scope.businessTypeMap[item.servType] + "增补订单" + $scope.servertime;
                                $scope.initOrderInfo.isExperienceVersion = item.isExperience;
                                $scope.isExperience = item.isExperience;

                                if ("0" == item.isExperience) {
                                    $("#TYDD").css("display", "none");
                                } else {
                                    $("#ZZMX").css("display", "none");
                                    $("#DDMX").css("display", "none");
                                }

                                $scope.effictiveTime = item.effictiveTime;
                                $scope.initOrderInfo.effictiveTime = $scope.formatDate(item.effictiveTime);
                                $scope.expireTime = item.expireTime;
                                $scope.initOrderInfo.expireTime = $scope.formatDate(item.expireTime);
                                $scope.productList = new Array();
                                $scope.guaDuanBaoyueProductList = new Array();
                                $scope.guaCaiBaoyueProductList = new Array();
                                //主订单的子项遍历
                                for (i = 0; i < item.orderItemList.length; i++) {
                                    var innerItem = item.orderItemList[i].product;
                                    switch (innerItem.subServType) {
                                        case 1:// 包月主叫
                                            $scope.hasPX = true;
                                            $scope.hasCMCC = true;
                                            $scope.cmcc = true;
                                            $scope.postPingXianCMCC = true;
                                            $scope.initOrderInfo.caller.memberCount = innerItem.memberCount;
                                            $scope.xiancizhu = innerItem.maxAmountPerPerson;
                                            $scope.productType = innerItem.productType;
                                            $scope.productList.push(innerItem);
                                            $scope.unitPrice = innerItem.unitPrice;
                                            $scope.productID = innerItem.objectID;
                                            $scope.memberCount = innerItem.memberCount;
                                            break;
                                        case 2:
                                            // 移动包月被叫
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                if (innerItem.isLimit == 1) {
                                                    if (innerItem.chargeType == 2) {
                                                        $scope.hasPX = true;
                                                        $scope.hasCMCC = true;
                                                        $scope.cmcc = true;
                                                        $scope.postPingXianCMCC = true;
                                                        $scope.initOrderInfo.called.memberCount = innerItem.memberCount;
                                                        $scope.xiancibei = innerItem.maxAmountPerPerson;
                                                        $scope.productType = innerItem.productType;
                                                        $scope.productList.push(innerItem);
                                                        $scope.unitPrice = innerItem.unitPrice;
                                                        $scope.productID = innerItem.objectID;
                                                        $scope.memberCount = innerItem.memberCount;
                                                    }
                                                }
                                            }

                                            //联通广告屏显按次
                                            if (innerItem.reservedsEcpmp.reserved1 === "2") {
                                                if (innerItem.isLimit == 1) {
                                                    if (innerItem.chargeType == 1) {
                                                        $scope.cucc = true;
                                                        $scope.hasCUCC = true;
                                                        $scope.hasPXCUCC = true;
                                                        $scope.postPingXianCUCC = true;
                                                        $scope.initOrderInfo.pxByTimes_cucc = innerItem.amount;
                                                        $scope.pxBySeqproductID_cucc = innerItem.objectID;
                                                        $scope.PXPrice_cucc = innerItem.unitPrice;
                                                    }
                                                }
                                            }

                                            //电信广告屏显按次
                                            if (innerItem.reservedsEcpmp.reserved1 === "3") {
                                                if (innerItem.isLimit == 1) {
                                                    if (innerItem.chargeType == 1) {
                                                        $scope.ctcc = true;
                                                        $scope.hasCTCC = true;
                                                        $scope.hasPXCTCC = true;
                                                        $scope.postPingXianCTCC = true;
                                                        $scope.initOrderInfo.pxByTimes_ctcc = innerItem.amount;
                                                        $scope.pxBySeqproductID_ctcc = innerItem.objectID;
                                                        $scope.PXPrice_ctcc = innerItem.unitPrice;
                                                    }
                                                }
                                            }

                                            break;
                                        case 3:// 屏显配额
                                        	if(innerItem.servType === 4){
                                        		if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                    if (innerItem.isLimit == 1) {
                                                        $scope.hasCMCC = true;
                                                        $scope.cmcc = true;
                                                        $scope.groupScreenCMCCPrice = innerItem.unitPrice;
                                                        $scope.groupScreenCMCCproductID = innerItem.objectID;
                                                        $scope.isgroupScreenCMCCNoLimit = false;
                                                        $scope.showGroupScreen = true;
                                                    } else {
                                                        $scope.isgroupScreenCMCCNoLimit = true;
                                                    }
                                                } else if (innerItem.reservedsEcpmp.reserved1 === "2") {
                                                    if (innerItem.isLimit == 1) {
                                                        $scope.hasCUCC = true;
                                                        $scope.cucc = true;
                                                        $scope.groupScreenCUCCPrice = innerItem.unitPrice;
                                                        $scope.groupScreenCUCCproductID = innerItem.objectID;
                                                        $scope.isgroupScreenCUCCNoLimit = false;
                                                        $scope.showGroupScreenCUCC = true;
                                                    } else {
                                                        $scope.isgroupScreenCUCCNoLimit = true;
                                                    }
                                                } else if (innerItem.reservedsEcpmp.reserved1 === "3") {
                                                    if (innerItem.isLimit == 1) {
                                                        $scope.hasCTCC = true;
                                                        $scope.ctcc = true;
                                                        $scope.groupScreenCTCCPrice = innerItem.unitPrice;
                                                        $scope.groupScreenCTCCproductID = innerItem.objectID;
                                                        $scope.isgroupScreenCTCCNoLimit = false;
                                                        $scope.showGroupScreenCTCC = true;
                                                    } else {
                                                        $scope.isgroupScreenCTCCNoLimit = true;
                                                    }
                                                }
                                        	}
                                        	else{
	                                            //修改 20190904
	                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {
	                                                if (innerItem.isLimit == 1) {
	                                                    if (innerItem.chargeType == 2) {            //按人包月
	                                                        $scope.hasPX = true;
	                                                        $scope.hasCMCC = true;
	                                                        $scope.cmcc = true;
	                                                        $scope.postPingXianCMCC = true;
	                                                        $scope.productType = innerItem.productType;
	                                                        $scope.productList.push(innerItem);
	                                                        $scope.unitPrice = innerItem.unitPrice;
	                                                        $scope.productID = innerItem.objectID;
	                                                        $scope.memberCount = innerItem.memberCount;
	                                                    } else if (innerItem.chargeType == 1) {        //按次
	                                                        $scope.cmcc = true;
	                                                        $scope.hasCMCC = true;
	                                                        $scope.hasPX = true;
	                                                        $scope.postPingXianCMCC = true;
	                                                        $scope.initOrderInfo.pxByTimes = innerItem.amount;
	                                                        $scope.pxBySeqproductID = innerItem.objectID;
	                                                        $scope.PXPrice = innerItem.unitPrice;
	                                                    }
	                                                }
	                                            }
	                                            if (innerItem.isLimit == 1) {
	                                                if (innerItem.chargeType == 1) {
	//                                                    // 移动
	//                                                    if (innerItem.reservedsEcpmp.reserved1 === "1") {
	//                                                        $scope.cmcc = true;
	//                                                        $scope.hasCMCC = true;
	//                                                        $scope.hasPX = true;
	//                                                        $scope.postPingXianCMCC = true;
	//                                                        $scope.initOrderInfo.pxByTimes = innerItem.amount;
	//                                                        $scope.pxBySeqproductID = innerItem.objectID;
	//                                                        $scope.PXPrice = innerItem.unitPrice;
	//                                                    }
	                                                    // 联通
	                                                    if (innerItem.reservedsEcpmp.reserved1 === "2") {
	                                                        $scope.cucc = true;
	                                                        $scope.hasCUCC = true;
	                                                        $scope.hasPXCUCC = true;
	                                                        $scope.postPingXianCUCC = true;
	                                                        $scope.initOrderInfo.pxByTimes_cucc = innerItem.amount;
	                                                        $scope.pxBySeqproductID_cucc = innerItem.objectID;
	                                                        $scope.PXPrice_cucc = innerItem.unitPrice;
	                                                    }
	                                                    // 电信
	                                                    if (innerItem.reservedsEcpmp.reserved1 === "3") {
	                                                        $scope.ctcc = true;
	                                                        $scope.hasCTCC = true;
	                                                        $scope.hasPXCTCC = true;
	                                                        $scope.postPingXianCTCC = true;
	                                                        $scope.initOrderInfo.pxByTimes_ctcc = innerItem.amount;
	                                                        $scope.pxBySeqproductID_ctcc = innerItem.objectID;
	                                                        $scope.PXPrice_ctcc = innerItem.unitPrice;
	                                                    }
	                                                }
	                                            }
                                        	}
                                            break;
                                        case 4:// 挂机短信
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {           //加if判断 20191107
                                                if (innerItem.isLimit == 1) {
                                                    $scope.hasGD = true;
                                                    $scope.cmcc = true;
                                                    $scope.hasCMCC = true;
                                                    $scope.postGuaDuan = true;
                                                    if (innerItem.chargeType == 1) {
                                                        $scope.initOrderInfo.guaduanAmount = innerItem.amount;
                                                        $scope.smsProductID = innerItem.objectID;
                                                        $scope.GDPrice = innerItem.unitPrice;
                                                    } else {

                                                        $scope.gdxiancibei = innerItem.maxAmountPerPerson;
                                                        $scope.gdproductType = innerItem.productType;

                                                        $scope.gdunitPrice = innerItem.unitPrice;
                                                        $scope.gdproductID = innerItem.objectID;
                                                        $scope.gdmemberCount = innerItem.memberCount;

                                                        $scope.guaDuanBaoyueProductList.push(innerItem);

                                                    }
                                                }
                                            }

                                            if (innerItem.reservedsEcpmp.reserved1 === "2") {           //联通挂断按次
                                                if (innerItem.isLimit == 1) {
                                                    $scope.hasGDCUCC = true;
                                                    $scope.cucc = true;
                                                    $scope.hasCUCC = true;
                                                    $scope.postGuaDuanCUCC = true;
                                                    if (innerItem.chargeType == 1) {
                                                        $scope.initOrderInfo.guaduanAmountCUCC = innerItem.amount;
                                                        $scope.smsProductIDCUCC = innerItem.objectID;
                                                        $scope.GDPriceCUCC = innerItem.unitPrice;
                                                    }
                                                }
                                            }

                                            if (innerItem.reservedsEcpmp.reserved1 === "3") {           //电信挂断按次
                                                if (innerItem.isLimit == 1) {
                                                    $scope.hasGDCTCC = true;
                                                    $scope.ctcc = true;
                                                    $scope.hasCTCC = true;
                                                    $scope.postGuaDuanCTCC = true;
                                                    if (innerItem.chargeType == 1) {
                                                        $scope.initOrderInfo.guaduanAmountCTCC = innerItem.amount;
                                                        $scope.smsProductIDCTCC = innerItem.objectID;
                                                        $scope.GDPriceCTCC = innerItem.unitPrice;
                                                    }
                                                }
                                            }

                                            break;
                                        case 8:// 挂机彩信
                                        	if (innerItem.servType == 4) {
                                        		if (innerItem.isLimit == 1) {
                                                    $scope.cmcc = true;
                                                    $scope.hasCMCC = true;
                                                    $scope.cxPrice = innerItem.unitPrice;
                                                    $scope.cxproductID = innerItem.objectID;
                                                    $scope.iscxNoLimit = false;
                                                    $scope.showGroupcx = true;
                                                }
                                                else {
                                                    $scope.iscxNoLimit = true;
                                                }
											}
                                        	else{
                                        		if (innerItem.isLimit == 1) {
                                        			$scope.hasGC = true;
                                        			$scope.cmcc = true;
                                        			$scope.hasCMCC = true;
                                        			$scope.postGuaCai = true;
                                        			if (innerItem.chargeType == 1) {
                                        				$scope.initOrderInfo.guacaiAmount = innerItem.amount;
                                        				$scope.mmsProductID = innerItem.objectID;
                                        				$scope.GCPrice = innerItem.unitPrice;
                                        				
                                        			} else {
                                        				$scope.gcxiancibei = innerItem.maxAmountPerPerson;
                                        				$scope.gcproductType = innerItem.productType;
                                        				
                                        				$scope.gcunitPrice = innerItem.unitPrice;
                                        				$scope.gcproductID = innerItem.objectID;
                                        				$scope.gcmemberCount = innerItem.memberCount;
                                        				
                                        				$scope.guaCaiBaoyueProductList.push(innerItem);
                                        				
                                        			}
                                        		}
                                        	}
                                            break;
                                        case 10:// 广告配额
                                            if (innerItem.isLimit == 1) {
                                                // 移动
                                                if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                    $scope.cmcc = true;
                                                    $scope.hasCMCC = true;
                                                    $scope.hasPX = true;
                                                    $scope.postPingXianCMCC = true;
                                                    $scope.initOrderInfo.pxByTimes = innerItem.amount;
                                                    $scope.pxBySeqproductID = innerItem.objectID;
                                                    $scope.PXPrice = innerItem.unitPrice;
                                                }
                                            }
                                            break;
                                        case 16:// 增彩群发
                                            if (innerItem.isLimit == 1) {
                                            	$scope.hasZC = true;
                                                $scope.cmcc = true;
                                                $scope.hasCMCC = true;
                                                $scope.ZCPrice = innerItem.unitPrice;
                                                $scope.zcproductID = innerItem.objectID;
                                                $scope.isZcNoLimit = false;
                                                $scope.showGroupzc = true;
                                            }
                                            else {
                                                $scope.isZcNoLimit = true;
                                            }
                                            break;
                                        case 17:// 短信群发
                                            if (innerItem.reservedsEcpmp.reserved1 === "1") {
                                                if (innerItem.isLimit == 1) {
                                                    $scope.hasCMCC = true;
                                                    $scope.cmcc = true;
                                                    $scope.groupSMSCMCCPrice = innerItem.unitPrice;
                                                    $scope.groupSMSCMCCproductID = innerItem.objectID;
                                                    $scope.isgroupSMSCMCCNoLimit = false;
                                                    $scope.showGroupSMS = true;
                                                } else {
                                                    $scope.isgroupSMSCMCCNoLimit = true;
                                                }
                                            } else if (innerItem.reservedsEcpmp.reserved1 === "2") {
                                                if (innerItem.isLimit == 1) {
                                                    $scope.hasCUCC = true;
                                                    $scope.cucc = true;
                                                    $scope.groupSMSCUCCPrice = innerItem.unitPrice;
                                                    $scope.groupSMSCUCCproductID = innerItem.objectID;
                                                    $scope.isgroupSMSCUCCNoLimit = false;
                                                    $scope.showGroupSMSCUCC = true;
                                                } else {
                                                    $scope.isgroupSMSCMCCNoLimit = true;
                                                }
                                            } else if (innerItem.reservedsEcpmp.reserved1 === "3") {
                                                if (innerItem.isLimit == 1) {
                                                    $scope.hasCTCC = true;
                                                    $scope.ctcc = true;
                                                    $scope.groupSMSCTCCPrice = innerItem.unitPrice;
                                                    $scope.groupSMSCTCCproductID = innerItem.objectID;
                                                    $scope.isgroupSMSCTCCNoLimit = false;
                                                    $scope.showGroupSMSCTCC = true;
                                                } else {
                                                    $scope.isgroupSMSCTCCNoLimit = true;
                                                }
                                            }


                                            break;
                                        default :
                                            break;
                                    }
                                }
                            }
                        }

                        if (!$scope.hasCMCC && !$scope.hasCUCC && !$scope.hasCTCC) {
                            $scope.tip = "1030120900";
                            $('#myModal').modal();
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
                })
            }
        });
    }

    //后续post的函数
    $scope.createOrder = function () {
        $('#formSub').attr('disabled', 'true');
        var req = {
            "order": {
                "orderType": 2,
                "ownerOrderID": $scope.initOrderInfo.orderCode,
                "transferAttachURL": $scope.transferAttachURL.upload,
                "orderDetailURL": $scope.orderDetailURL.upload,
                "payStatus": 3,
                "orderName": $scope.initOrderInfo.orderName,
                "amount": $scope.amount,
                "enterpriseID": $scope.enterpriseID,
                "enterpriseName": $scope.enterpriseName,
                "servType": $scope.servType,
                "effictiveTime": $scope.effictiveTime,
                "expireTime": $scope.expireTime,
                "isExperience": $scope.initOrderInfo.isExperienceVersion,
                "operatorID": $scope.operatorID,
                "orderItemList": []
            }
        };
        //群发
        if ($scope.servType == '4' && $scope.operatorID) {
            //移动
            if($scope.cmcc){
            	//群发增彩
                if($scope.cmcc && $scope.showGroupzc && $scope.zcanci != ''){
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.isExperience == 1 ? 0 : $scope.ZCPrice,
                        "quantity": $scope.zcanci,
                        "totalAmount": $scope.accMul($scope.ZCPrice, $scope.zcanci),
                        "productID": $scope.zcproductID
                    });
                }
                //群发彩信
                if($scope.showGroupcx && $scope.cxanci != ''){
                	req.order.orderItemList.push({
                		"operatorID": $scope.operatorID,
                		"unitPrice": $scope.isExperience == 1 ? 0 : $scope.cxPrice,
        				"quantity": $scope.cxanci,
        				"totalAmount": $scope.accMul($scope.cxPrice, $scope.cxanci),
        				"productID": $scope.cxproductID
                	});
                }
                //群发短信
                if($scope.showGroupSMS && $scope.groupSendSMSCMCCanci != ''){
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.isExperience == 1 ? 0 : $scope.groupSMSCMCCPrice,
                        "quantity": $scope.groupSendSMSCMCCanci,
                        "totalAmount": $scope.accMul($scope.groupSMSCMCCPrice, $scope.groupSendSMSCMCCanci),
                        "productID": $scope.groupSMSCMCCproductID
                    });
                }
                //群发屏显
                if($scope.showGroupScreen && $scope.groupSendScreenCMCCanci != ''){
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.isExperience == 1 ? 0 : $scope.groupScreenCMCCPrice,
                        "quantity": $scope.groupSendScreenCMCCanci,
                        "totalAmount": $scope.accMul($scope.groupScreenCMCCPrice, $scope.groupSendScreenCMCCanci),
                        "productID": $scope.groupScreenCMCCproductID
                    });
                }
            }
            //联通
            if( $scope.cucc){
                //群发短信
                if($scope.showGroupSMSCUCC && $scope.groupSendSMSCUCCanci != ''){
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.isExperience == 1 ? 0 : $scope.groupSMSCUCCPrice,
                        "quantity": $scope.groupSendSMSCUCCanci,
                        "totalAmount": $scope.accMul($scope.groupSMSCUCCPrice, $scope.groupSendSMSCUCCanci),
                        "productID": $scope.groupSMSCUCCproductID
                    });
                }
                //群发屏显
                if($scope.showGroupScreenCUCC && $scope.groupSendScreenCUCCanci != ''){
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.isExperience == 1 ? 0 : $scope.groupScreenCUCCPrice,
                        "quantity": $scope.groupSendScreenCUCCanci,
                        "totalAmount": $scope.accMul($scope.groupScreenCUCCPrice, $scope.groupSendScreenCUCCanci),
                        "productID": $scope.groupScreenCUCCproductID
                    });
                }
            }
            //电信
            if($scope.ctcc){
                //群发短信
                if($scope.showGroupSMSCTCC && $scope.groupSendSMSCTCCanci != ''){
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.isExperience == 1 ? 0 : $scope.groupSMSCTCCPrice,
                        "quantity": $scope.groupSendSMSCTCCanci,
                        "totalAmount": $scope.accMul($scope.groupSMSCTCCPrice, $scope.groupSendSMSCTCCanci),
                        "productID": $scope.groupSMSCTCCproductID
                    });
                }
                //群发屏显
                if($scope.showGroupScreenCTCC && $scope.groupSendScreenCTCCanci != ''){
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.isExperience == 1 ? 0 : $scope.groupScreenCTCCPrice,
                        "quantity": $scope.groupSendScreenCTCCanci,
                        "totalAmount": $scope.accMul($scope.groupScreenCTCCPrice, $scope.groupSendScreenCTCCanci),
                        "productID": $scope.groupScreenCTCCproductID
                    });
                }
            }






        }

        //移动屏显按次
        if ($scope.cmcc && $scope.hasPX && ($scope.initOrderInfo.pxByTimes !== '') && $scope.postPingXianCMCC) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.PXPrice,
                "quantity": $scope.anci,
                "totalAmount": $scope.accMul($scope.PXPrice, $scope.anci),
                "productID": $scope.pxBySeqproductID
            });
        }
        //屏显包月
        if ($scope.cmcc && $scope.hasPX && $scope.productType == '2' && $scope.postPingXianCMCC) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.unitPrice,
                "quantity": $scope.peirenyue,
                "totalAmount": $scope.accMul($scope.accMul(parseInt($scope.peirenyue), $scope.unitPrice), $scope.memberCount),
                "productID": $scope.productID
            });
        }
        //挂机短信按次
        if ($scope.cmcc && $scope.initOrderInfo.guaduanAmount !== '' && $scope.postGuaDuan) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.GDPrice,
                "quantity": $scope.gdanci,
                "totalAmount": $scope.accMul($scope.GDPrice, $scope.gdanci),
                "productID": $scope.smsProductID
            });
        }
        //挂机短信包月
        if ($scope.cmcc && $scope.hasGD && $scope.gdproductType == '2' && $scope.postGuaDuan) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.gdunitPrice,
                "quantity": $scope.guaDuanPrice,
                "totalAmount": $scope.accMul($scope.accMul(parseInt($scope.guaDuanPrice), $scope.gdunitPrice), $scope.gdmemberCount),
                "productID": $scope.gdproductID
            });
        }


        //挂机彩信
        if ($scope.cmcc && $scope.initOrderInfo.guacaiAmount !== '' && $scope.postGuaCai) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.GCPrice,
                "quantity": $scope.gcanci,
                "totalAmount": $scope.accMul($scope.GCPrice, $scope.gcanci),
                "productID": $scope.mmsProductID
            });
        }

        //挂机彩信包月
        if ($scope.cmcc && $scope.hasGC && $scope.gcproductType == '2' && $scope.postGuaCai) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.gcunitPrice,
                "quantity": $scope.guaCaiPrice,
                "totalAmount": $scope.accMul($scope.accMul(parseInt($scope.guaCaiPrice), $scope.gcunitPrice), $scope.gcmemberCount),
                "productID": $scope.gcproductID
            });
        }


        //联通屏显按次
        if ($scope.cucc && $scope.hasPXCUCC && $scope.postPingXianCUCC && ($scope.initOrderInfo.pxByTimes_cucc !== '')) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.PXPrice_cucc,
                "quantity": $scope.anci_cucc,
                "totalAmount": $scope.accMul($scope.PXPrice_cucc, $scope.anci_cucc),
                "productID": $scope.pxBySeqproductID_cucc
            });
        }

        //联通挂机短信按次     add 20191107
        if ($scope.cucc && $scope.initOrderInfo.guaduanAmountCUCC !== '' && $scope.postGuaDuanCUCC) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.GDPriceCUCC,
                "quantity": $scope.gdanciCUCC,
                "totalAmount": $scope.accMul($scope.GDPriceCUCC, $scope.gdanciCUCC),
                "productID": $scope.smsProductIDCUCC
            });
        }
        //电信屏显按次
        if ($scope.ctcc && $scope.hasPXCTCC && $scope.postPingXianCTCC && ($scope.initOrderInfo.pxByTimes_ctcc !== '')) {

            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.PXPrice_ctcc,
                "quantity": $scope.anci_ctcc,
                "totalAmount": $scope.accMul($scope.PXPrice_ctcc, $scope.anci_ctcc),
                "productID": $scope.pxBySeqproductID_ctcc
            });
        }

        //电信挂机短信按次     add 20191107
        if ($scope.ctcc && $scope.initOrderInfo.guaduanAmountCTCC !== '' && $scope.postGuaDuanCTCC) {
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.GDPriceCTCC,
                "quantity": $scope.gdanciCTCC,
                "totalAmount": $scope.accMul($scope.GDPriceCTCC, $scope.gdanciCTCC),
                "productID": $scope.smsProductIDCTCC
            });
        }
        //群发增彩
        if($scope.cmcc && $scope.showGroupzc && $scope.gjzcanci != ''){
            req.order.orderItemList.push({
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.isExperience == 1 ? 0 : $scope.ZCPrice,
                "quantity": $scope.gjzcanci,
                "totalAmount": $scope.accMul($scope.ZCPrice, $scope.gjzcanci),
                "productID": $scope.zcproductID
            });
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/createOrder",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {

                        $scope.tip = "UPDATE_ORDER_SUCCESS_MSG";
                        $('#myModal').modal();

                        setTimeout(function () {
                            location.href = '../orderList/orderList.html';
                        }, 1000);

                    }
                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
                })
            }
        });
    };


});

app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])
