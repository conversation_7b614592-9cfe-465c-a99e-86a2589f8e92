var app = angular.module("myApp",["util.ajax",'page',"top.menu","angularI18n","service.common"])
app.controller('actStatListController', ['$scope','$rootScope','$location','RestClientUtil','CommonUtils',function ($scope,$rootScope,$location,RestClientUtil,CommonUtils) {
    $scope.init=function(){
      $scope.token = $.cookie("token");
        //初始化分页信息
        $scope.pageInfo =[
            {
              "totalPage": 1,
              "totalCount": 0,
              "pageSize": '10',
              "currentPage": 1
            }
          ];
        $scope.queryactStatList();
    };
    $scope.gotoDetail=function(item){
        //传入objectID，作为唯一标识
        location.href='../activityDetailList/activityDetailList.html?enterpriseID='+item.enterpriseID;
    };
    $scope.exportFile=function(){
      var req = {
        "param":{
          "enterpriseName":$scope.reqTemp.queryEnterpriseStatCond.enterpriseName,
          "token":$scope.token,
          "isExport":1
        },
        "url":"/qycy/ecpmp/ecpmpServices/activityService/downEnterpriseStatCsvFile",
        "method":"get"
      }
      CommonUtils.exportFile(req);
    }
	$scope.queryactStatList = function (condition) {
        if(condition!='justPage'){
            var req={
                "queryEnterpriseStatCond":{
                    "enterpriseName":$scope.enterpriseName||"",
                    "servType":3
                },
                "page":{
                    "pageNum":1,
                    "pageSize":parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal":"1",
                }
            };
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
            $scope.exportUrl="/qycy/ecpmp/ecpmpServices/activityService/downEnterpriseStatCsvFile?enterpriseName="+$scope.reqTemp.queryEnterpriseStatCond.enterpriseName+"&token="+$scope.token+"&isExport=1";
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.page.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
			type: 'post',
			url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseStat",
			data: JSON.stringify(req),
			success: function (result) {
				$rootScope.$apply(function () {
                    var data = result.result;
                    if(data.resultCode=='1030100000'){
                        $scope.enterpriseStatList=result.enterpriseStatList||[];
                        $scope.pageInfo[0].totalCount=parseInt(result.totalNumber)||0;
                        $scope.pageInfo[0].totalPage = result.totalNumber!==0 ? Math.ceil(result.totalNumber / parseInt($scope.pageInfo[0].pageSize)):1;
                    }else{
                        $scope.enterpriseStatList=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
				})
				
            },
            error:function(){
                $rootScope.$apply(function(){
                    $scope.enterpriseStatList=[];
                    $scope.pageInfo[0].currentPage=1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage=1;
                    $scope.tip="**********";
                    $('#myModal').modal();
                    }
                )
            }
		});
          
	}
}])
app.config(['$locationProvider', function($locationProvider) {
    $locationProvider.html5Mode({
        enabled:true,
        requireBase:false
    });
  }])