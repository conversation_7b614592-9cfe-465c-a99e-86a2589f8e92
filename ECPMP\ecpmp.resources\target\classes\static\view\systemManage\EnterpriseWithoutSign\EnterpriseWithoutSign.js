var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common"])
app.controller('enterpriseWithoutSignListController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.queryEnterpriseWithoutSignList();
    };

    $scope.generateReq = function () {
    	var req = {
            "enterpriseID": $scope.enterpriseID || '',
            "enterpriseName": $scope.enterpriseName || '',
            "pageParameter": {
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[0].pageSize),
                "isReturnTotal": "1",
            }
        };
    	return req;
    };
    //查询列表
    $scope.queryEnterpriseWithoutSignList = function (condition) {
        if (condition != 'justPage') {
            if(( !($scope.enterpriseID != null && $scope.enterpriseID != ''
                && Number.isNaN(+$scope.enterpriseID)) && parseInt($scope.enterpriseID) > 2147483647) ||
                ($scope.enterpriseID != null && $scope.enterpriseID != ''
                && Number.isNaN(+$scope.enterpriseID))){
                $scope.tip = '企业编号输入值非法';
                $('#myModal').modal();
                return;
            }
            var req = $scope.generateReq();
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryEnterpriseWithoutSignListTemp = angular.copy(req);
        } else {
            //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.queryEnterpriseWithoutSignListTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        req.getBelongOrg = 0;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                        if($scope.enterpriseWithoutSignListData.length === 0&&$scope.pageInfo[0].totalCount>0&&$scope.pageInfo[0].currentPage>1){
                            $scope.pageInfo[0].currentPage = $scope.pageInfo[0].currentPage - 1;
                            $scope.queryEnterpriseWithoutSignList('justPage');
                        }
                    } else {
                        $scope.enterpriseWithoutSignListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.enterpriseWithoutSignListData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.lastID = "";
    $scope.lastName = "";
    // 新增弹窗
    $scope.addEnterpriseWithoutSign = function () {
        if($scope.addEnterpriseWithoutSign.enterpriseID || $scope.lastID === "" || $scope.lastName === "" || $scope.addEnterpriseWithoutSign.enterpriseName){
            $scope.addEnterpriseWithoutSign.enterpriseID = "";
            $scope.addEnterpriseWithoutSign.enterpriseName = "";
            $scope.lastID = "";
            $scope.lastName = "";
        }
        $('#addEnterpriseWithoutSign').modal();
    };

    $scope.beforeCommit = function () {
        //校验
        if(($scope.lastID === $scope.addEnterpriseWithoutSign.enterpriseID)&&($scope.lastName = $scope.addEnterpriseWithoutSign.enterpriseName)){
            var addReq = {
                "operType": 0,
                "enterpriseID": $scope.addEnterpriseWithoutSign.enterpriseID,
                "enterpriseName": $scope.addEnterpriseWithoutSign.enterpriseName
            };

            $scope.deliveryOrSaveEnterpriseWithoutSign(addReq);

            // setTimeout(function () {
            //     $('#addDeliveryNoBlackListCancel').click();
            //     $scope.queryDeliveryNoBlackList();
            // },1200);
        }else{
            $scope.tip="企业编号和企业名称不一致，请点击搜索！";
            $('#myModal').modal();
        }
    };

    $scope.getEnterpriseName = function () {
        if( !$scope.addEnterpriseWithoutSign.enterpriseID){
            return;
        }
        if(Number($scope.addEnterpriseWithoutSign.enterpriseID)>2147483647){
            $scope.tip="该企业不存在";
            $('#myModal').modal();
            return;
        }

        var req = {
            "id": $scope.addEnterpriseWithoutSign.enterpriseID? Number($scope.addEnterpriseWithoutSign.enterpriseID):null
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseInfo",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.queryEnterprise=result.enterprise;
                        if($scope.queryEnterprise.enterpriseName){
                            $scope.addEnterpriseWithoutSign.enterpriseName = $scope.queryEnterprise.enterpriseName;
                            $scope.lastID = $scope.addEnterpriseWithoutSign.enterpriseID;
                            $scope.lastName = $scope.addEnterpriseWithoutSign.enterpriseName;
                        }else{
                            $scope.tip="该企业不存在";
                            $('#myModal').modal();
                        }
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }

    // 删除显示弹窗
    $scope.deleteEnterpriseWithoutSign = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteEnterpriseWithoutSign').modal();
    };
    // 执行操作中的请求
    $scope.delEnterpriseWithoutSign = function () {
        var item = $scope.selectedItemDel;
        var removeReq = {
            "operType": 1
        };
        if (item) {
            removeReq.id = item.id;
        }
        $scope.deliveryOrSaveEnterpriseWithoutSign(removeReq);
    };
    $scope.deliveryOrSaveEnterpriseWithoutSign = function (req) {
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/submitEnterpriseWithoutSign",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1010100000') {
                        if (req.operType == 0) {
                            $('#addEnterpriseWithoutSignCancel').click();
                        } else {
                            $('#delEnterpriseWithoutSign').click();
                            $scope.tip = "删除成功";
                            $('#myModal').modal();
                        }
                        $scope.queryEnterpriseWithoutSignList('justPage');
                    } else if(result.resultCode == '1010120098') {
                        $scope.tip = "该企业已存在签名配置";
                        $('#myModal').modal();
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    if (req.operType == 0) {
                        $('#addEnterpriseWithoutSignCancel').click();
                    } else {
                        $('#delEnterpriseWithoutSign').click();
                    }
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };

}]);
app.filter("formatDate", function () {
    return function (date) {
        if (date) {
            var date = new Date(date);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
            Y = date.getFullYear() + '-';
            M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
            D = (date.getDate() < 10 ? '0'+(date.getDate()) : date.getDate()) + ' ';
            h = (date.getHours() < 10 ? '0'+(date.getHours()) : date.getHours()) + ':';
            m = (date.getMinutes() < 10 ? '0'+(date.getMinutes()) : date.getMinutes()) + ':';
            s = date.getSeconds() < 10 ? '0'+(date.getSeconds()) : date.getSeconds();
            return Y+M+D+h+m+s;
        }
        return "";
    }
});