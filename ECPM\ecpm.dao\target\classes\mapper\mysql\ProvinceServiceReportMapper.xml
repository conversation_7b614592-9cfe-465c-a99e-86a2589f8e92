<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ProvinceServiceReportMapper">
    <resultMap id="provinceServiceReport" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceReportWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="corpID" column="corpID" javaType="java.lang.String" />
        <result property="servType" column="servType" javaType="java.lang.Integer" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="operationType" column="operationType" javaType="java.lang.String" />
        <result property="reqMsg" column="reqMsg" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
    </resultMap>

    <insert id="insertProvinceServiceReport">
        INSERT INTO ecpm_t_province_service_report
        (
        <if test="enterpriseID != null">
            enterpriseID,
        </if>
        <if test="corpID != null">
            corpID,
        </if>
        servType,
        <if test="subServType != null">
            subServType,
        </if>
        operationType,
        <if test="reqMsg != null">
            reqMsg,
        </if>
        createTime,
        updateTime
        )
		VALUES
		(
        <if test="enterpriseID != null">
            #{enterpriseID},
        </if>
        <if test="corpID != null">
            #{corpID},
        </if>
        #{servType},
        <if test="subServType != null">
            #{subServType},
        </if>
		#{operationType},
        <if test="reqMsg != null">
            #{reqMsg},
        </if>
		#{createTime},
		#{updateTime}
		)
	</insert>

    <select id="queryMsg" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceReportWrapper">
        select a.updateTime,a.operationType,a.reqMsg  from ecpm_t_province_service_report a
            inner join ecpm_t_enterprise_simple b on a.enterpriseID = b.ID
        where b.enterpriseType=5 and b.reserved10='113' and a.operationType  in ('0','1','2') and a.updateTime = #{updateTime}
    </select>

</mapper>