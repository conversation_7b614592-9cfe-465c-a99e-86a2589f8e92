<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>金库申请</title>
    <link rel="stylesheet" type="text/css" href="../../css/bootstrap.min.css"/>
    <link href="../../css/reset.css" rel="stylesheet"/>
    <link rel="stylesheet" href="../../css/businessSetting.css">
    <script type="text/javascript" src="../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../service/utils/service-ajax.js"></script>
    <!-- 引入导航组件 -->
    <link href="../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="apply.js"></script>

    <style>
		.no-optional {
			padding: 20px 25px;
			background-color: #ffffff;
		}

		.no-optional p {
			margin: 0 auto;
			width: 145px;
		}

		.center {
			text-align: center;
		}

		.min-width-330 {
			min-width: 330px;
		}
		.service-config{
   			color: #705DE1;
		}


    </style>
</head>

<body ng-app='myApp' ng-controller='defaultFrequencyController' ng-init="init();" class="body-min-width">
<div class="cooperation-manage">

    <div class="cooperation-head" ng-if="isSuperManager">
    <span class="frist-tab">金库申请</span>
</div>
    <!--名片彩印-->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <form class="form-horizontal form-group bgWhite" name="orderBase1" style="padding-bottom: 15px;">

            <div class="form-inline">
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label>申请次数：</label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="text-align: left;">
                    <label>1次</label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:220px;">
					<input type="hidden" class="form-control" autocomplete="off"
                           placeholder="{{'DEFAULTFREQUENCY_INPUT'|translate}}" name="userTimes"
                           ng-model="req.userTimes" pattern="(^[0-9]{1,9}$)|(^[1-9]$)">
                </div>

                <span style="color:red;line-height: 34px"
                      ng-show="orderBase1.amount1.$dirty && orderBase1.amount1.$invalid">
						<img src="../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase1.amount1.$error.required">必填</span> -->
						<span ng-show="orderBase1.amount1.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>

            </div>
            <div class="form-inline">
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label">
                    <icon style="color:red">*</icon>
                    <label>申请时长：</label>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3">
                    <select style="width:100px" class="form-control" ng-model="req.duration"
                            ng-options="x.id as x.name for x in periodTypeList" ng-change="changMPOption()">
                        <!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
                    </select>
                    <label style="margin-left: 5px;">小时</label>
                </div>
                <span style="color:red;line-height: 34px"
                      ng-show="orderBase1.amount2.$dirty && orderBase1.amount2.$invalid">
						<img src="../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase1.amount2.$error.required">必填</span> -->
						<span ng-show="orderBase1.amount2.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>
            </div>
            <div class="form-inline" style="height: 100%;">
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label>审批人：</label>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3">
                    <select class="form-control" ng-model="req.selectedApprover"
                            ng-options="x.id as x.name for x in approverList">
                        <!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
                    </select>
                </div>
                <span style="color:red;line-height: 34px"
                      ng-show="orderBase1.amount2.$dirty && orderBase1.amount2.$invalid">
						<img src="../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase1.amount2.$error.required">必填</span> -->
						<span ng-show="orderBase1.amount2.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>
            </div>
            <div class="form-inline" style="height: 100%;">
                <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3 control-label">
                    <icon style="color:red">*</icon>
                    <label>申请说明：</label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 min-width-330">
                    <textarea type="text" class="form-control" autocomplete="off"
                           placeholder="最多150个字" ng-maxlength="150"
                           name="applyReason" ng-model="req.applyReason"></textarea>
                </div>

                <span style="color:red;line-height: 34px"
                      ng-show="orderBase1.amount2.$dirty && orderBase1.amount2.$invalid">
						<img src="../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase1.amount2.$error.required">必填</span> -->
						<span ng-show="orderBase1.amount2.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>
            </div>
        </form>
    </div>
    <div class="form-group col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <div class="order-btn" style="margin: 20px 50px; margin-left: 28%;">
            <button style="margin-right: 30px;" type="submit" class="btn btn-primary search-btn"
                    ng-disabled="!req.applyReason" ng-click="save()"
                    ng-bind="'PUBLIC_NUMBER_SUBMIT'|translate">
            </button>
            <!-- <button style="margin:40px 20px;" type="submit" class="btn" ng-click="returnUp()">返回</button> -->
        </div>
    </div>
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center"><p style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p></div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

</html>