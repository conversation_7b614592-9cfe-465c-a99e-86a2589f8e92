package com.huawei.jaguar.dsum.dao.config;

import com.huawei.jaguar.dsum.commons.utils.AESCodec;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.io.IOException;

@Configuration
@EnableTransactionManagement
@EnableConfigurationProperties(DBConfig.class)
@MapperScan({"com.huawei.jaguar.dsum.dao.mapper"})
@Slf4j
public class MyBatisConfig {

    @Value("${mybatis.mapper-locations}")
    private String mapperLocations;

    @Autowired
    private DBConfig dBConfig;

    @Autowired
    private DBEncryptConfig dBEncryptConfig;

    private HikariDataSource dataSource;

    @Bean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();

        config.setDriverClassName(dBConfig.getDriverClassName());
        config.setJdbcUrl(dBConfig.getUrl());
        config.setUsername(dBConfig.getUsername());

        try {
            String decryptedPassword = AESCodec.decrypt(dBConfig.getPassword(), dBEncryptConfig.getKey(), dBEncryptConfig.getFactor());
            config.setPassword(decryptedPassword);
        } catch (Exception e) {
            log.error("密码解密失败", e);
            throw new RuntimeException("数据库密码解密失败", e);
        }

        config.setMaximumPoolSize(dBConfig.getMaxActive());
        config.setMinimumIdle(dBConfig.getMinIdle());
        config.setIdleTimeout(600000); // 默认10分钟
        config.setConnectionTestQuery(dBConfig.getValidationQuery());
        config.setPoolName("HikariCP");

        this.dataSource = new HikariDataSource(config);
        return this.dataSource;
    }

    @PreDestroy
    public void close() {
        if (this.dataSource != null) {
            this.dataSource.close();
        }
    }

    @Bean
    public SqlSessionFactory sqlSessionFactoryBean() {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource());
        try {
            PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            sqlSessionFactoryBean.setMapperLocations(resolver.getResources(mapperLocations));
            return sqlSessionFactoryBean.getObject();
        } catch (IOException | RuntimeException e) {
            log.error("sqlSessionFactoryBean 初始化失败", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Bean
    public PlatformTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }
}
