<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>商户活动审核</title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../css/searchList.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-common.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<script type="text/javascript" src="merchantActivityAuditCtrl.js"></script>
	<style>
		/*.cooperation-manage .coorPeration-table th:not(.ng-hide):first-child{*/
			/*padding-left: 30px!important;*/
		/*}*/
	</style>
</head>

<body ng-app="myApp" class="body-min-width">

	<div class="cooperation-manage container-fluid" ng-controller="merchantActivityAuditCtrl" ng-init="init();" ng-cloak>
		<div ng-show="businessStatus !=1">
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'MENU_INFOAUDIT'|translate" ng-show="isSuperManager"> </span>
			<span class="frist-tab" ng-bind="'AUDIT_MANAGE'|translate" ng-show="isZhike"> </span>
			<span class="frist-tab" ng-bind="'AUDIT_SEARCH'|translate" ng-show="isAgent"> </span>>
			<span class="second-tab" ng-bind="'ACTIVITY_AUDIT3'|translate"></span>
		</div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<!--企业名称，只有超管展示-->
					<label for="enterpriseName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label" ng-show="isSuperManager"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></label>

					<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" ng-show="isSuperManager">
						<input type="text" id="enterpriseName" class="form-control"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"
									 ng-model="enterpriseName">
					</div>
					<!--子企业名称，只有代理商管理员展示-->
					<label for="enterpriseName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label" ng-show="isAgent"
					style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></label>

					<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" ng-show="isAgent">
						<input type="text" id="enterpriseName" class="form-control"
									placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME'|translate}}"
									ng-model="enterpriseName">
					</div>

					<!--活动名称-->
					<label for="activityName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"
								 style="white-space:nowrap" ng-bind="'COMMON_ACTIVITYNAME'|translate"></label>

					<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4">
						<input type="text" id="activityName" class="form-control"
									 placeholder="{{'COMMON_PLEASEINPUTACTIVITYMANAGE'|translate}}"
									 ng-model="activityName">
					</div>

					<!--活动ID-->
					<label for="" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label" ng-show="!isSuperManager"
					style="white-space:nowrap" ng-bind="'ACTIVITY_ID2'|translate"></label>

					<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" ng-show="!isSuperManager">
						<input type="text" id="activityID" class="form-control"
									placeholder="{{'ACTIVITY_PLEASEINPUTACTIVITYID'|translate}}"
									ng-model="activityID">
					</div>

					<label for="" class="province col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label" ng-show="isSuperManager"
								 style="white-space:nowrap" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" ng-show="isSuperManager">
						<select class="form-control" ng-model="auditStatus"
										ng-options="x.value for x in auditStatusList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
						<button type="submit" class="btn search-btn" ng-click="getActivityList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>

		<div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="isSuperManager"></th>
					<th style="width:10%" ng-bind="'SECOND_ID'|translate" ng-show="isAgent"></th>
					<th style="width:10%" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate" ng-show="isAgent"></th>
					<th style="width:10%" ng-bind="'ACTIVITY_ID2'|translate"></th>
					<th style="width:10%" ng-bind="'COMMON_ACTIVITYNAME'|translate"></th>
					<th style="width:10%" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>
					<th style="width:10%" ng-bind="'COMMON_EXPIRYDATE'|translate"></th>
					<th style="width:10%" ng-bind="'ACTIVITY_AREASET'|translate"></th>
					<th style="width:10%" ng-bind="'SPOKES_SPOKESDAYS'|translate"></th>
					<th style="width:10%" ng-bind="'ACTIVITY_SPOKENUM'|translate"></th>
					<th style="width:10%" ng-bind="'ITEM_EFFICTIVETIME'|translate"></th>
					<th style="width:10%" ng-bind="'ITEM_PAYSTATUS'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in activityInfoListData">
					<td ng-show="isAgent"><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
					<td ng-show="!isZhike"><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
					<td><span title="{{item.activityID}}">{{item.activityID}}</span></td>
					<td><span title="{{item.activityName}}">{{item.activityName}}</span></td>
						<td>
							<div ng-attr-title="{{'ACTIVITY_AUDIT_REJECTED'|translate}}" ng-show="item.auditStatus==0"
							ng-bind="'ACTIVITY_AUDIT_REJECTED'|translate"></div>
							<div ng-attr-title="{{'CONTENTAUDIT_TODO_AUDIT'|translate}}" ng-show="item.auditStatus==1"
										ng-bind="'CONTENTAUDIT_TODO_AUDIT'|translate"></div>
							<div ng-attr-title="{{'ACTIVITY_AUDIT_PASS'|translate}}" ng-show="item.auditStatus==2"
										ng-bind="'ACTIVITY_AUDIT_PASS'|translate"></div>
							<div ng-attr-title="{{'ACTIVITY_AUDIT_REJECTED'|translate}}" ng-show="item.auditStatus==3"
										ng-bind="'ACTIVITY_AUDIT_REJECTED'|translate"></div>
						</td>
						<td title="{{item.effectivetime}}~{{item.expiretime}}">{{item.effectivetime}}~{{item.expiretime}}</td>
						<td><span title="{{getArea(item.cityList,item.proviceList)}}">{{getArea(item.cityList,item.proviceList)}}</span></td>
						<td><span title="{{item.spokeDayNum}}">{{item.spokeDayNum}}</span></td>
						<td><span title="{{item.totalNum}}">{{item.totalNum}}</span></td>
						<td><span title="{{item.createTime}}">{{item.createTime}}</span></td>
					<td>
						<div class="handle">
							<ul>
								<li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'COMMON_WATCH'|translate"></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="activityInfoListData.length<=0">
					<td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate" ng-show="!isAgent"></td>
					<td style="text-align:center" colspan="11" ng-bind="'COMMON_NODATA'|translate" ng-show="isAgent"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="getActivityList('justPage')"></ptl-page>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="activityModal" tabindex="-1" role="dialog"
				 aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
										ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 业务未开通 -->
	<div ng-show="businessStatus ==1" class="modal fade bs-example-modal-sm" id="Modalisaengt" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" >
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 18px;color:#383838'>
							业务未开通
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	</div>
	
	
	
</body>

</html>