<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
<!--	<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">-->
	<title>忘记密码</title>
	<link rel="stylesheet" type="text/css" href="../../css/bootstrap.min.css"/>
	<link href="../../css/reset.css" rel="stylesheet"/>
	<link href="../../css/login.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="forgetPwd.js"></script>
	<style>
		.v_pic{height:117px !important}

		/*与图形验证码同宽*/
		.login-cont{padding-right: 80px}
		.login .login-cont .form-horizontal{
			width: 380px;
		}
		.login .login-cont .form-horizontal .search-btn, .btn-outer button{
			width: 115px;
		}
		body .form-control.passwordInput{
			width: 110px;
		}
		#getSmsCode.btn{
			padding: 6px !important;
		}
		@media (min-width: 768px) {
			.login-cont .col-sm-11 {
				width: 87.666667%;
			}
		}
		.form-control {
			padding: 6px 5px;
		}
	</style>
</head>
<body ng-app='myApp' ng-controller='loginController' ng-init="init();">
	<div class="login">
		<div class="login-cont">

			<form class="form-horizontal" style="padding: 100px 80px 0 40px;" name='myForm' novalidate ng-show="sendSms">

				<div style="text-align:left;margin-bottom:20px;color: #6759a4;">
					<p style="font-weight:bold;font-size:20px">短信验证</p>

					<p style="opacity:0.2;font-weight:bold;font-size:25px">SMS verification</p>
				</div>
				<div class="form-group">
					<label for="account" class="col-sm-1 control-label" style="padding-top: 10px;">
						<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/account.png') no-repeat scroll 0 0;display: inline-block;height: 16px;width: 16px;"></icon>
					</label>

					<div class="col-sm-11">
						<input style="width:210px" required name='account' type="account" class="form-control"
									 ng-model="forgetAccount" placeholder="请输入账号">
					</div>
					<div>
						<span style="color:red;padding-left: 55px" ng-show="myForm.account.$dirty&& myForm.account.$invalid">
									<span ng-show="myForm.account.$error.required">账号必填</span>
						</span>
					</div>
				</div>
				<div class="form-group">
					<label for="sms" class="col-sm-1 control-label">
						<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/sms.png') no-repeat scroll 0 0;display: inline-block;height: 22px;width: 22px;"></icon>
					</label>

					<div class="col-sm-11" style="padding-right:0px">
						<input type="code" placeholder="请输入短信验证" ng-model="smsVerify" class="passwordInput form-control"
									 name="smsVerify" required autocomplete="off">
						<button ng-disabled="!forgetAccount.trim()" type="button" ng-click="getSmsVerifyCode()" style="color: #6759a4;"
										class="btn" id="getSmsCode">获取短信验证
						</button>
					</div>
					<div>
						<span style="color:red;padding-left: 55px"
									ng-show="myForm.smsVerify.$dirty&&myForm.smsVerify.$invalid">
								<span ng-show="myForm.smsVerify.$error.required">请输入验证码</span>
						</span>
					</div>
				</div>
				<div class="form-group">
					<label for="pwd" class="col-sm-1 control-label" style="padding-top: 10px;">
						<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/password.png') no-repeat scroll 0 0;display: inline-block;height: 16px;width: 16px;"></icon>
					</label>

					<div class="col-sm-11">
						<input style="width:210px" required type="password" class="form-control" ng-model="forgetPwd"
									 placeholder="请输入新密码" ng-blur="checkPwd()" name="forgetPwd">
					</div>
					<label class="col-sm-1 control-label" ng-show="passwordValidateDesc"></label>

					<div class="col-sm-11" ng-show="passwordValidateDesc" style="color:#ff0000;padding-left: 16px">
						{{passwordValidateDesc}}
					</div>

				</div>
				<div class="form-group">
					<label for="sms" class="col-sm-1 control-label">
						<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/sms.png') no-repeat scroll 0 0;display: inline-block;height: 22px;width: 22px;"></icon>
					</label>

					<div class="col-sm-11">
						<input type="password" class="form-control passwordInput" ng-model="confirmForgetPwd"
									 id="password" placeholder="请确认新密码" ng-blur="checkRePassword()">
					</div>
            <span style="color:red;padding-left:16px" ng-show="!rePasswordValidate">
									<span>确认密码必填，与新密码保持一致</span>
						</span>
				</div>

				<div class="">
					<div id="imgCode" class="" ></div>
					<label id="rtnFalse-error" class="error" for="vCode" style="display:none;text-align: center;font-weight: 400;color: red;margin-top: 10px;" ></label>
				</div>

				<!--<div>
					<p style="color:#6759a4;text-align:left">验证码在1分钟内有效，若未收到短信，请稍后再试</p>
				</div>-->
				<div class="btn-outer">
					<button class="btn btn-primary search-btn" ng-click="modifyPwd()"
									ng-disabled="!(smsVerify.trim()&&forgetAccount.trim())||!(passwordValidate&&rePasswordValidate&&forgetPwd&&confirmForgetPwd)">
						保存修改
					</button>
					<button class="btn btn-default" style="margin-left: 25px" ng-click="goBack()"
									ng-bind="'COMMON_BACK'|translate"></button>
				</div>

			</form>

		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
			 data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{tip|translate}}
					</p></div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>




</body>
</html>