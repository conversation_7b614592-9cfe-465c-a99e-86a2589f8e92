.cooperation-nav{
  margin-bottom: 15px;
  margin-left: 20px;
}
.tabtn-menu {
  /*width: 145px;*/
  padding: 0 10px;
  display: inline-block;
  border-radius: 0.5rem;
  background-color: #FFFFFF;
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  color: #c3c3c3;
  margin: 10px 0 0 3px;
  text-align: center;
  border: 1px solid #e5e5e5;
}
.cur-tabtn-menu {
  color: #705de1;
  box-shadow: #bababa 0 0 2px;
}
a:hover{
  text-decoration: none;
}
/* media for adjustable top_menu width  */
@media (max-width: 1450px){
  .tabtn-menu{
        /*width: 140px;*/
        font-size: 16px;
    }
}
@media (max-width: 1360px){
  .tabtn-menu{
        /*width: 125px;*/
        font-size: 16px;
    }
}
@media (max-width: 1200px){
  .tabtn-menu{
        /*width: 113px;*/
        font-size: 15px;
    }
}
@media (max-width: 1080px){
  .tabtn-menu{
        /*width: 97px;*/
        font-size: 13px;
    }
}

.second-topmenu .tabtn-menu{
  width: 113px;
  height: 25px;
  line-height: 25px;
  border: none;
  background: none;
  box-shadow: none;
  border-right: 1px solid #c3c3c3;
  border-radius: unset;
}