/*
 * 文 件 名:  DeliveryNotifyResultTask.java
 * 版    权:  Huawei Technologies Co., Ltd. Copyright YYYY-YYYY,  All rights reserved
 * 描    述:  <描述>
 * 修 改 人:  wWX570570
 * 修改时间:  2019年1月9日
 * 跟踪单号:  <跟踪单号>
 * 修改单号:  <修改单号>
 * 修改内容:  <修改内容>
 */
package com.huawei.jaguar.dsdp.ecpe.schedule.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.huawei.jaguar.dsdp.ecpe.commons.log.TracePatternConverter;
import com.huawei.jaguar.dsdp.ecpe.commons.utils.DateUtil;
import com.huawei.jaguar.dsdp.ecpe.commons.utils.MD5Util;
import com.huawei.jaguar.dsdp.ecpe.commons.utils.UserCountUtil;
import com.huawei.jaguar.dsdp.ecpe.constant.DeliveryConstant;
import com.huawei.jaguar.dsdp.ecpe.core.FrameSpringBeanUtil;
import com.huawei.jaguar.dsdp.ecpe.core.elasticsearch.ElasticSearchCluster;
import com.huawei.jaguar.dsdp.ecpe.core.redis.impl.ShardedJedisCluster;
import com.huawei.jaguar.dsdp.ecpe.dao.domain.*;
import com.huawei.jaguar.dsdp.ecpe.dao.mapper.ContentMapper;
import com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdPartyMapper;
import com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryMapper;
import com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryTaskMapper;
import com.huawei.jaguar.dsdp.ecpe.exception.EcpeException;
import com.huawei.jaguar.dsdp.ecpe.model.WltUssdCallout;
import com.huawei.jaguar.dsdp.ecpe.request.DeductingQuotaReq;
import com.huawei.jaguar.dsdp.ecpe.request.QueryWltServiceReq;
import com.huawei.jaguar.dsdp.ecpe.response.QueryWltServiceRsp;
import com.huawei.jaguar.dsdp.ecpe.result.Constants;
import com.huawei.jaguar.dsdp.ecpe.schedule.TaskTemplate;
import com.huawei.jaguar.dsdp.ecpe.schedule.domain.DeliverySyncInfo;
import com.huawei.jaguar.dsdp.ecpe.schedule.task.exectetask.DeliveryNotifyResultExecuteScheduleTask;
import com.huawei.jaguar.dsdp.ecpe.service.impl.DeliveryRuleDataService;
import com.huawei.jaguar.dsdp.ecpe.service.impl.DeliveryToEsService;
import com.huawei.jaguar.dsdp.ecpe.service.impl.HotLineManagementService;
import com.huawei.jaguar.dsdp.ecpe.service.impl.SubscribeServices;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.DsumService;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.EcpfepService;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.EcpmService;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.IocService;
import com.huawei.jaguar.dsdp.ecpe.spi.model.DeliveryResult;
import com.huawei.jaguar.dsdp.ecpe.spi.model.NamedParameter;
import com.huawei.jaguar.dsdp.ecpe.spi.request.DeliveryResultNotifyReq;
import com.huawei.jaguar.dsdp.ecpe.spi.request.IntelligentCallReq;
import com.huawei.jaguar.dsdp.ecpe.task.ComparableFutureTask;
import com.huawei.jaguar.dsdp.ecpe.task.ComparableThreadPoolExecutor;
import com.huawei.jaguar.dsdp.ecpe.task.DeliveryTaskDomain;
import com.huawei.jaguar.dsdp.ecpe.task.DeliveryToEsRunnable;
import com.huawei.jaguar.dsdp.ecpe.util.ContentUtil;
import com.huawei.jaguar.dsdp.ecpe.util.DeliveryAccessInitUtil;
import com.huawei.jaguar.dsdp.ecpe.util.ErrorlLogUtils;
import com.huawei.jaguar.dsum.request.QueryEnterpriseInfoReq;
import com.huawei.jaguar.dsum.response.QueryEnterpriseInfoRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;

/**
 * <一句话功能简述>
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [MDSP V300R005C10LAA035, 2019年1月9日]
 * @see  [相关类/方法]
 * @since  [ecpe]
 */
@Component
@EnableScheduling
@Slf4j(topic = "Runtime@ECPE_TASK")
@Configuration
@PropertySource(value = {"classpath:ecpe-serviceConfig.properties"}, encoding = "UTF-8")
@RefreshScope
public class DeliveryNotifyResultTask extends TaskTemplate
{
    /**
     * 定时任务开关
     */
    @Value("${task.deliveryNotifyResultTask.taskSwitch}")
    private Integer taskSwitch;
    
    /**
     * 定时任务名称
     */
    @Value("${task.deliveryNotifyResultTask.taskName}")
    private String taskName;
    
    /**
     * 定时任务共享锁key
     */
    @Value("${task.deliveryNotifyResultTask.taskKey}")
    private String taskKey;
    
    /**
     * 数据自动释放间隔：单位ms
     */
    @Value("${task.deliveryNotifyResultTask.expireMillis}")
    private Long expireMillis;
    
    /**
     * 数据失效时间间隔：单位：分钟
     */
    @Value("${task.deliveryNotifyResultTask.minutes}")
    private Long minutes;
    
    /**
     * 单次处理条数，默认1000
     */
    @Value("${task.deliveryNotifyResultTask.limitNum:1000}")
    private Integer limitQueryNum;
    
    /**
     * ecpe_t_thirdparty_delivery_task 表捞取数据的时间间隔,默认7200秒即2小时
     */
    @Value("${task.deliveryNotifyResultTask.querySecond:15}")
    private Integer queryxSecond;
    
    /**
     * ecpe_t_thirdparty_delivery 表捞取数据的时间间隔,默认5秒
     */
    @Value("${task.deliveryNotifyResultTask.querySecondSecond:5}")
    private Integer query5Second;
    
    /**
     * ecpe_t_thirdparty_delivery 表捞取数据的时间间隔,默认120秒即2分钟
     */
    @Value("${task.deliveryNotifyResultTask.querySecondNext:120}")
    private Integer query120Second;
    /**
     * ecpe_t_thirdparty_delivery 表捞取联通数据的时间间隔,默认600秒即10分钟
     */
    @Value("${task.deliveryNotifyResultTask.querySecondNext.unicom:120}")
    private Integer queryUnicomSecond;
    /**
     * ecpe_t_thirdparty_delivery 表捞取电信数据的时间间隔,默认600秒即10分钟
     */
    @Value("${task.deliveryNotifyResultTask.querySecondNext.telecom:120}")
    private Integer queryTelecomSecond;

    /**
     * 通知次数控制阀值配置项，默认3次
     */
    @Value("${task.deliveryNotifyResultTask.notifyCount:3}")
    private Integer notifyCount;
    
    /**
     * 线程池处理超时时间
     */
    @Value("${task.deliveryNotifyResultTask.dealDeliveryNotifyTimeOut:50000000}")
    private Integer dealDeliveryNotifyTimeOut;
    
    /**
     * 短信超时时间配置项
     */
    @Value("${task.deliveryNotifyResultTask.smsTimeout}")
    private Integer smsTimeout;

    private ArrayList<String> cmppTemplateID;
    /**
     * 控制异网超时是否进行配额扣减的开关
     */
    @Value("${ecpe.reduceSubscribeSwitch:1}")
    private Integer reduceSubscribeSwitch;

    @Value("${ecpe.deliveryNotifyResultTask.timeOutSeconds:60}")
    private Integer timeOutSeconds;

    @Value("${ecpe.deliveryNotifyResultTask.partitionTotal:4}")
    private Integer partitionTotal;

    private final String execKey = "ecpe:delivery:notify:lock";

    private static final String LOCK_SUCCESS = "OK";

    private final static Integer MSGTYPE_THREE = 3;
    
    private final static Integer MSGTYPE_SX = 4;
    
    private final static Integer MSGTYPE_USSD = 5;
    
    private final static Integer MSGTYPE_ONE = 1;
    
    private final static Integer MSGTYPE_EIGHT = 8;
    
    private final static Integer MSGTYPE_SMS = 6;

    private final static Integer MSGTYPE_NINE = 9;

    private final static Integer MSGTYPE_MMS = 10;

    private final static Integer INTERACTION_USSD = 7;


    /**
     * 处理中
     */
    private final static Integer PROCESSSTATUS_PROCESSING = 1;
    
    /**
     * 处理成功
     */
    private final static Integer PROCESSSTATUS_SUCCESS = 2;
    
    /**
     * 处理失败
     */
    private final static Integer PROCESSSTATUS_FAILED = 3;
    
    /**
     * 已异步通知
     */
    private final static Integer NOTIFYSTATUS = 1;
    
    /**
     * 服务类型：voiceQuery：语音询问（快件派送前直接AI语音询问用户派送时间，存放地点等）
     */
    private static final String SERVICETYPE_VOICEQUERY = "voiceQuery";
    
    /**
     * 接入场景ID:1：对话性交互
     */
    private static final String SCENEID = "1";
    
    @Autowired
    private DeliveryAccessInitUtil deliveryAccessInitUtil;
    
    /**
     * 通知ES的Service层
     */
    @Autowired
    private DeliveryToEsService deliveryToEsService;
    
    /**
     * 订购关系服务
     */
    @Autowired
    private SubscribeServices subscribeServices;
    
    /**
     * 通知接口机的线程池
     */
    @Autowired
    @Qualifier("deliveryNotifyResultTaskExecuter")
    private ComparableThreadPoolExecutor deliveryNotifyResultTaskExecuter;
    
    /**
     * 投递信息dao
     */
    @Autowired
    private ThirdpartyDeliveryMapper thirdpartyDeliveryMapper;
    
    /**
     * 投递任务dao
     */
    @Autowired
    private ThirdpartyDeliveryTaskMapper thirdpartyDeliveryTaskMapper;
    
    @Autowired
    private ThirdPartyMapper thirdPartyMapper;
    
    /**
     * deliveryRule數據服務
     */
    @Autowired
    private DeliveryRuleDataService deliveryRuleDataService;
    
    /**
     * 内容dao
     */
    @Autowired
    private ContentMapper contentMapper;
    
    /**
     * 接口机服务spi
     */
    @Autowired
    private EcpfepService ecpfepService;
    
    /**
     * 构造单个的线程池
     */
    private final ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
    
    /**
     * ES服务
     */
    @Autowired
    private ElasticSearchCluster elasticSearchCluster;
    
    /**
     * dsum的service
     */
    @Autowired
    private DsumService dsumService;
    
    @Autowired
    private EcpmService ecpmService;
    
    @Autowired
    private IocService iocService;
    
    /**
     * 投递结果通知优先级基础知识
     */
    @Value("${ecpe.delivery.notify.base.priority}")
    private int deliveryNotifyBasePriority;
    
    /**
     * ioc接口鉴权platformId
     */
    @Value("${ioc.interface.platformId}")
    private String iocPlatformId;
    
    /**
     * ioc接口鉴权accountPasswd
     */
    @Value("${ioc.interface.accountPasswd}")
    private String iocAccountPasswd;
    
    /**
     * 热线管理服务
     */
    @Autowired
    private HotLineManagementService hotLineManagementService;
    
    private static final Map<String, String> MSG_TYPE_MAP = new HashMap<String, String>(){{
        put("11", "1");
        put("12", "2");
        put("13", "3");
        put("14", "4");
        put("15", "5");
        put("16", "6");
        put("17", "7");
        put("18", "8");
        put("19", "9");
        put("20", "10");}};

    @Autowired
    private ShardedJedisCluster jedisService;
    /**
     * |分隔符
     */
    private static final String REGEX = "\\|";
    /**
     * 201 指定企业配置项
     */
    @Value("${ecpe.deliveryNotify.needPlatformEnterprise}")
    private String needPlatformEnterprise;

    private String parentEnterpriseID;
    /**
     * 设置定时任务执行时间
     * <功能详细描述>
     * @see [类、类#方法、类#成员]
     */
    @Scheduled(cron = "${task.deliveryNotifyResultTask.fixedDelay}")
    public void executeTask()
    {
        if (1 == taskSwitch)
        {
            if (running.compareAndSet(false, true))
            {
                TaskInfo taskInfo = new TaskInfo();
                taskInfo.setTaskName(taskName);
                taskInfo.setTaskKey(taskKey);
                taskInfo.setExpireTime(DateUtil.calculateTimeAfterParam(minutes));
                taskInfo.setExpireMillis(expireMillis);
                Object rtn = super.executeTask(taskInfo, running);
                
                // 执行处理任务
                if (null == rtn)
                {
                    // 最后更新线程ID，更新为false
                    TracePatternConverter.clearTraceid(null);
                    running.set(false);
                    return;
                }
                
                try
                {
                    @SuppressWarnings("unchecked")
                    List<ThirdpartyDeliveryWrapper> targetList = (List<ThirdpartyDeliveryWrapper>)rtn;
                    dealDelivery(targetList);
                }
                catch (Exception e)
                {
                    log.error(" deal delivery error , e is {} ", e);
                    ErrorlLogUtils.errorLog(" deal delivery error , e is {} ", e);
                }
                finally
                {
                    // 最后更新线程ID，更新为false
                    TracePatternConverter.clearTraceid(null);
                    running.set(false);
                }
            }
        }
    }
    
    /**
     * 查询需要处理的数据
     */
    @Override
    public List<ThirdpartyDeliveryWrapper> execute()
    {
        log.info("deliveryNotifyResultTask fetch data start. ");

        //检查redis是否有数据
        String isExec = jedisService.get(execKey,String.class);
        //如果有数据，且值为OK，表示上次任务还未结束，则退出任务
        if(isExec != null && StringUtils.equals(LOCK_SUCCESS, isExec))
        {
            log.info("Last time task is not finished. exit deliveryNotifyResultTask");
            return null;
        }

        //占坑，标记正在处理
        jedisService.setex(execKey, timeOutSeconds,LOCK_SUCCESS);

        List<ThirdpartyDeliveryWrapper> deliveryList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        Date now = new Date();

        calendar.setTime(now);
        calendar.add(Calendar.SECOND, -queryxSecond);
        Date queryxSecond = calendar.getTime();

        Map<String, Object> map = new HashMap<String, Object>();
        for (int i = 0; i < partitionTotal; i++)
        {
            map.clear();
            map.put("queryxSecond", queryxSecond);

            // 通知次数小于3
            map.put("notifyCount", notifyCount);
            map.put("limitQueryNum", limitQueryNum);
            int dayNum = 0;
            if (i > 0 )
            {
                dayNum = -1;
            }
            calendar.add(Calendar.DAY_OF_MONTH, dayNum);
            map.put("partition",calendar.get(Calendar.DAY_OF_MONTH));

            List<ThirdpartyDeliveryWrapper> localDeliveryList = thirdpartyDeliveryMapper.queryDeliveryForSchedule(map);


            // 修改执行状态
            if (CollectionUtils.isNotEmpty(localDeliveryList))
            {
                List<Long> ids = new ArrayList<Long>();
                for (ThirdpartyDeliveryWrapper delivery : localDeliveryList)
                {
                    ids.add(delivery.getTaskID());
                }
                map.put("processStatus", PROCESSSTATUS_PROCESSING);
                // 通知次数notifyCount+1
                map.put("notifyCount", 1);
                map.put("updateTime", new Date());
                map.put("list", ids);
                thirdpartyDeliveryTaskMapper.updateNotifyStatusForSchedule(map);
            }
            deliveryList.addAll(localDeliveryList);
        }

        //释放坑，标记处理结束
        jedisService.del(execKey);

        log.info("deliveryNotifyResultTask fetch data end. ");
        return deliveryList;

    }

    private void dealDelivery(List<ThirdpartyDeliveryWrapper> deliveryList)
    {
        log.info("deliveryNotifyResultTask dealDelivery start. ");
        // 如果没有数据要处理,直接返回
        if (CollectionUtils.isEmpty(deliveryList))
        {
            log.info("no data need deal deliveryNotifyResultTask");
            return;
        }
        
        DeliveryResultNotifyReq req = null;
        
        List<DeliveryResult> deliveryResults = null;
        
        DeliveryResult deliveryResult = null;
        
        // 处理成功的任务
        List<Long> list = new ArrayList<Long>();
        // 处理失败的集合
        List<Long> faillist = new ArrayList<Long>();
        
        // 构造内容列表
        List<ContentWrapper> contentWrapperList = buildContentMapperList(deliveryList);
        
        // 构造第三方投递map
        Map<Long, ThirdpartyDeliveryWrapper> deliveryMap = buildDeliveryMap(deliveryList);


        Map<Long, DeliverySyncInfo> syncMap = new HashMap<Long, DeliverySyncInfo>();
        
        // 构造内容ID和内容对象的map
        Map<Long, ContentWrapper> contentWrapperMap = buildContentMap(contentWrapperList);
        // 构造taskID和内容对象ID
        Map<Long, Long> taskContentMap = new HashMap<Long, Long>();
        
        // 构造企业ID和第三方接入信息映射的Map
        Map<Integer, ThirdPartyWrapper> thirdPartyWrapperMap = buildThirdPartyMap(deliveryList);
        
        /**
         * 定义通知接口机的任务列表
         */
        List<Future<Long>> tasks = new ArrayList<Future<Long>>();
        
        // 遍历第三方投递内容列表
        for (ThirdpartyDeliveryWrapper delivery : deliveryList)
        {
            deliveryResults = new ArrayList<DeliveryResult>();
            deliveryResult = new DeliveryResult();
            
            Long taskID = delivery.getTaskID();


            
            if (StringUtils.isNotEmpty(MSG_TYPE_MAP.get(delivery.getMsgType().toString())))
            {
                delivery.setMsgType(Integer.valueOf(MSG_TYPE_MAP.get(delivery.getMsgType().toString())));
            }
            
            
            faillist.add(taskID);
            String templateID = delivery.getTemplateID();
            taskContentMap.put(taskID, Long.valueOf(templateID));
            String notifyURL = delivery.getNotifyUrl();
            
            String deliveryResultString = delivery.getDeliveryResult();
            String ussdDeliveryResult = delivery.getUssdDeliveryResult();
            String sentResult = delivery.getSentResult();
            String ussdSentResult = delivery.getUssdSentResult();
            String msg=delivery.getMsg();
            // 定义彩印发送类型
            String colorSendType = null;
            
            // 定义未发送彩印原因
            String noSendColorReason = null;

            
            //查询归属运营商
            //投递记录入库ES热线清单时，运营商（owner）不再通过调用中央查询运营商接口获取，改取获取到的投递记录（数据源：待投递请求消息表（ecpe_t_thirdparty_delivery））的platform
            String owner = delivery.getPlatform();
//            String owner = deliveryAccessInitUtil.queryMobilePlatform(delivery.getTarget());
            if (StringUtils.isBlank(owner))
            {
                log.error("queryMobilePlatform failed. query from large_segment");
                ErrorlLogUtils.errorLog("queryMobilePlatform failed. query from large_segment");
                owner = Constants.Delivery.TELECOMS_OPERATOR_NUM_MAP
                    .get(deliveryAccessInitUtil.getOwnerByPhone(delivery.getTarget()));
            }
            
            //获取投递类型
            checkMsgTypeGetDeliveryAction(delivery);
            
            // 能力平台触发投递时间d         
            Date colorPushTime = new Date();

            // 接收新媒结果通知时间
            Long colorSendDelayTime = chargeColorSendDelayTime(delivery);


            // 根据企业ID查询外部thirdAccount
            ThirdPartyWrapper accessWrapper = thirdPartyWrapperMap.get(delivery.getEnterpriseID());
            String diffnetUssdSwitch = accessWrapper.getDiffnetUssdSwitch();

            //一次投递：只有发送结果，没有投递结果
            if (StringUtils.isEmpty(deliveryResultString) && StringUtils.isEmpty(ussdDeliveryResult))
            {
                if (StringUtils.isNotEmpty(sentResult))
                {
                    if (Constants.Delivery.RESULT_SUCCESS.equals(sentResult))
                    {
                        //sentResult为0则返回码：212  闪信回执报告超时
                        deliveryResult.setStatus(Constants.Delivery.RECEIPT_REPORT_TIMEOUT);
                    }
                    else
                    {
                        if (Constants.Delivery.SMS.equals(delivery.getMsgType()))
                        {
                            deliveryResult.setStatus(Constants.Delivery.SM_DELIVERY_FAILED);
                        }
                        else
                        {
                            //sentResult不为0则返回码：110  闪信投递失败
                            deliveryResult.setStatus(Constants.Delivery.FLASHSM_FAIL);
                        }
                    }
                    
                    if (DeliveryConstant.DeliveryAction.USSD.equals(delivery.getDeliveryFirstAction())
                        && DeliveryConstant.DeliveryAction.SMS_FLASH.equals(delivery.getDeliveryFailedAction()))
                    {

                        colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH_AFTER_FAIL;


                        //010 运营商为异网（非移动），msgtype=3/5 入库ES时，置sendType=1
                        if((delivery.getMsgType().equals(3)||delivery.getMsgType().equals(5))
                                &&!"1".equals(delivery.getPlatform())
                                &&DeliveryConstant.diffnetUssdSwitch.switchClose.equals(diffnetUssdSwitch)){
                            colorSendType =DeliveryConstant.CY_SEND_TYPE_FLASH;
                        }
                    }
                    else
                    {
                        colorSendType = chargeColorSendType(delivery.getDeliveryFirstAction());
                    }
                }
                else if (StringUtils.isNotEmpty(ussdSentResult))
                {
                    
                    if (Constants.Delivery.RESULT_SUCCESS.equals(ussdSentResult))
                    {
                        //ussdSentResult为0则返回码：214 回执超时，投递失败
                        deliveryResult.setStatus(Constants.Delivery.DELIVERY_NOTIFY_TIMEOUT);
                        // 第一次定时任务 ussd超时才语音外呼，后面的定时任务重试不外呼
                        if(delivery.getParentTaskID() == null && (delivery.getProcessStatus().equals(0)
                        		|| delivery.getProcessStatus().equals(1)))
                        {
                        	callIntelligentCall(delivery);
                        }
                    }
                    else
                    {
                        //ussdSentResult不为0则返回码：100  USSD投递失败
                        deliveryResult.setStatus(Constants.Delivery.USSD_FAIL);
                    }
                    
                    colorSendType = DeliveryConstant.CY_SEND_TYPE_USSD;
                }
            }
            
            //两次投递
            else if (StringUtils.isNotEmpty(deliveryResultString) && StringUtils.isNotEmpty(ussdDeliveryResult))
            {
                if (Constants.Delivery.RESULT_SUCCESS.equals(deliveryResultString))
                {
                    deliveryResult.setStatus(Constants.Delivery.USSD_FAIL_FLASHSM_SUBMIT);
                    
                }
                else
                {
                    deliveryResult.setStatus(Constants.Delivery.USSD_FLASHSM_FAIL);
                    
                }
                
                noSendColorReason = deliveryResultString;

                colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH_AFTER_FAIL;
                //010 运营商为异网（非移动），msgtype=3/5 入库ES时，置sendType=1
                if((delivery.getMsgType().equals(3)||delivery.getMsgType().equals(5))
                        &&!"1".equals(delivery.getPlatform())
                        &&DeliveryConstant.diffnetUssdSwitch.switchClose.equals(diffnetUssdSwitch)){
                    colorSendType =DeliveryConstant.CY_SEND_TYPE_FLASH;
                }
                colorSendDelayTime = secondSetSendDelayTime(delivery, colorSendDelayTime);
            }
            
            //一次投递：短信或闪信:存在投递结果 或者ussd调用失败转二次调用
            else if (StringUtils.isNotEmpty(deliveryResultString) && StringUtils.isEmpty(ussdDeliveryResult))
            {
                //新增判断：是否存在优先ussd调用
                if (StringUtils.isNotEmpty(ussdSentResult))
                {
                    //ussd调用失败转二次调用
                    if (Constants.Delivery.RESULT_SUCCESS.equals(deliveryResultString))
                    {
                        deliveryResult.setStatus(Constants.Delivery.USSD_FAIL_FLASHSM_SUBMIT);
                        
                    }
                    else
                    {
                        deliveryResult.setStatus(Constants.Delivery.USSD_FLASHSM_FAIL);
                        
                    }
                    colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH_AFTER_FAIL;
                    //010 运营商为异网（非移动），msgtype=3/5 入库ES时，置sendType=1
                    if((delivery.getMsgType().equals(3)||delivery.getMsgType().equals(5))
                            &&!"1".equals(delivery.getPlatform())
                            &&DeliveryConstant.diffnetUssdSwitch.switchClose.equals(diffnetUssdSwitch)){
                        colorSendType =DeliveryConstant.CY_SEND_TYPE_FLASH;
                    }
                    colorSendDelayTime = secondSetSendDelayTime(delivery, colorSendDelayTime);
                }
                else
                {
                    //一次投递：短信或闪信
                    if (Constants.Delivery.RESULT_SUCCESS.equals(deliveryResultString))
                    {
                        if (Constants.Delivery.SMS.equals(delivery.getMsgType()))
                        {
                            deliveryResult.setStatus(Constants.Delivery.SM_DELIVERY_SUCCEED);
                        }
                        else
                        {
                            deliveryResult.setStatus(Constants.Delivery.FLASHSM_SUCCEED);
                        }
                    }
                    else
                    {
                        if (Constants.Delivery.SMS.equals(delivery.getMsgType()))
                        {
                            deliveryResult.setStatus(Constants.Delivery.SM_DELIVERY_FAILED);
                        }
                        else
                        {
                            //sentResult不为0则返回码：110  闪信投递失败
                            deliveryResult.setStatus(Constants.Delivery.FLASHSM_FAIL);
                        }
                    }
                    
                    colorSendType = chargeColorSendType(delivery.getDeliveryFirstAction());
                }
                
                noSendColorReason = deliveryResultString;
            }
            
            //一次投递：ussd  或者  二次投递：ussd发送失败，再次短信或闪信发送
            else if (StringUtils.isEmpty(deliveryResultString) && StringUtils.isNotEmpty(ussdDeliveryResult))
            {
                if (Constants.Delivery.RESULT_SUCCESS.equals(ussdDeliveryResult))
                {
                    deliveryResult.setStatus(Constants.Delivery.USSD_SUCCEED);
                    
                    colorSendType = DeliveryConstant.CY_SEND_TYPE_USSD;
                    noSendColorReason = ussdDeliveryResult;
                }
                else
                {
                    if (Constants.Delivery.USSD_MSGTYPE.equals(delivery.getMsgType())
                        || Constants.Delivery.USSD_MSGTYPE_NO_RSP.equals(delivery.getMsgType()))
                    {
                        if (Constants.Delivery.RESULT_SUCCESS.equals(sentResult))
                        {
                            //sentResult为0则返回码：212  闪信回执报告超时
                            deliveryResult.setStatus(Constants.Delivery.RECEIPT_REPORT_TIMEOUT);
                        }
                        else
                        {
                            if (Constants.Delivery.SMS.equals(delivery.getMsgType()))
                            {
                                deliveryResult.setStatus(Constants.Delivery.SM_DELIVERY_FAILED);
                            }
                            else
                            {
                                //sentResult不为0则返回码：110  闪信投递失败
                                deliveryResult.setStatus(Constants.Delivery.FLASHSM_FAIL);
                            }
                        }

                        colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH_AFTER_FAIL;
                        //010 运营商为异网（非移动），msgtype=3/5 入库ES时，置sendType=1
                        if((delivery.getMsgType().equals(3)||delivery.getMsgType().equals(5))
                                &&!"1".equals(delivery.getPlatform())
                                &&DeliveryConstant.diffnetUssdSwitch.switchClose.equals(diffnetUssdSwitch)){
                            colorSendType =DeliveryConstant.CY_SEND_TYPE_FLASH;
                        }
                    }
                    else
                    {
                        deliveryResult.setStatus(Constants.Delivery.USSD_FAIL);
                        colorSendType = DeliveryConstant.CY_SEND_TYPE_USSD;
                        noSendColorReason = ussdDeliveryResult;
                    }
                    
                    colorSendDelayTime = secondSetSendDelayTime(delivery, colorSendDelayTime);
                }
            }

            if(Constants.MsgType.INTERACTION_USSD.equals(delivery.getMsgType())&&delivery.getParentTaskID()==null){
                //存在闪信投递且没有投递成功
                if(!StringUtils.isEmpty(sentResult)&&!Constants.Delivery.RESULT_SUCCESS.equals(deliveryResultString)){
                    deliveryResult.setStatus(Constants.Delivery.USSD_FLASHSM_FAIL);
                }
                //交互ussd 类型为uusd为 colorSendType改为7 交互ussd
                if(colorSendType.equals(DeliveryConstant.CY_SEND_TYPE_USSD)){
                    colorSendType = DeliveryConstant.CY_SEND_TYPE_INTERACTION_USSD;
                }
            }

            // 需要回执的调用ecpfep接口
            if (MSGTYPE_NINE.equals(delivery.getMsgType()) ||MSGTYPE_MMS.equals(delivery.getMsgType())
                    ||MSGTYPE_SX.equals(delivery.getMsgType())||INTERACTION_USSD.equals(delivery.getMsgType())
                    || MSGTYPE_USSD.equals(delivery.getMsgType())
                || MSGTYPE_ONE.equals(delivery.getMsgType()) || MSGTYPE_EIGHT.equals(delivery.getMsgType())
                || MSGTYPE_SMS.equals(delivery.getMsgType()))
            {
                // 获取每个内容信息
                if (CollectionUtils.isEmpty(cmppTemplateID))
                {
                    cmppTemplateID = FrameSpringBeanUtil.cmppTemplateID;
                }

                ContentWrapper contentWrapper = contentWrapperMap.get(Long.valueOf(templateID));
                //CMPP内容没有企业id
                if (cmppTemplateID.contains(templateID))
                {
                    contentWrapper.setEnterpriseID(delivery.getEnterpriseID());
                }
                if(contentWrapper.getEnterpriseID()==null){
                    contentWrapper.setEnterpriseID(delivery.getEnterpriseID());
                }

                // 获取内部企业ID
                Integer platformID = contentWrapper.getEnterpriseID();


                if (StringUtils.isEmpty(notifyURL) && null != accessWrapper
                    && StringUtils.isNotEmpty(accessWrapper.getCallbackUrl()))
                {
                    notifyURL = accessWrapper.getCallbackUrl();
                }
                
                deliveryResult.setTarget(delivery.getTarget());
                deliveryResult.setMsg(null==msg?Constants.Delivery.DELIVERY_RESULTMSG_MAP.get(deliveryResult.getStatus()):msg);
                deliveryResult.setChargeNum(delivery.getChargeNum());
                deliveryResult.setReply(delivery.getReply());
                deliveryResults.add(deliveryResult);

                // 构造请求
                req = new DeliveryResultNotifyReq();
                req.setTaskID(taskID.toString());
                //004如存在父ID取父ID作为通知ID；
                if(delivery.getParentTaskID()!=null){
                    req.setTaskID(String.valueOf(delivery.getParentTaskID()));
                }
                req.setDeliveryResults(deliveryResults);
                req.setPlatformID(accessWrapper.getThirdAccount());
                req.setNotifyURL(notifyURL);
                req.setThirdPassword(accessWrapper.getThirdPassword());
                
                // 添加到线程池进行处理
                buildDeliveryNotifyResultTask(req,
                    tasks,
                    deliveryToEsService,
                    buildSynchroToEs(delivery,
                        contentWrapper,
                        DeliveryConstant.FLAG_ES_DELIVERY_TASK,
                        deliveryResult.getStatus(),
                        colorSendType,
                        noSendColorReason,
                        owner,
                        colorPushTime,
                        colorSendDelayTime),
                    delivery);
            }
            // 不需要回执的接口直接同步ES
            else
            {
                //超时的还原配额
                if (Constants.Delivery.DELIVERY_NOTIFY_TIMEOUT.equals(deliveryResult.getStatus())
                    || Constants.Delivery.RECEIPT_REPORT_TIMEOUT.equals(deliveryResult.getStatus()))
                {
                    // 实时失败的不需要还原配额
                    if (checkSendFailed(delivery))
                    {
                        log.info(
                            "deliveryNotifyResultTask.SentResult/UssdSentResult not success and smsNotify/ussdNotify timeout");
                    }
                    else
                    {
                        //迭代8改造,挂机长短信算做多个配额
                        Long useCount = countLongContent(delivery);
                        backQuota(contentWrapperMap.get(Long.valueOf(templateID)), owner,useCount);
                        
                        //如果需要还原配额，那就把这两个字段置空 
                        delivery.setSubscribeID(null);
                        delivery.setOrderID(null);
                    }
                }
                
                // 异步调用ES
                asynchroToEs(delivery,
                    DeliveryConstant.FLAG_ES_DELIVERY_TASK,
                    deliveryResult.getStatus(),
                    colorSendType,
                    noSendColorReason,
                    owner,
                    colorPushTime,
                    colorSendDelayTime);
                
                list.add(taskID);
            }
            
            syncMap.put(taskID,
                new DeliverySyncInfo(deliveryResult.getStatus(), colorSendType, noSendColorReason, owner, colorPushTime,
                    colorSendDelayTime));
        }
        
        // 构造需要更新的taskID
        buildUpdateSuccessTaskID(list, tasks);
        
        // 处理已经执行结束的投递任务
        dealFinishedTaskList(list, faillist, deliveryMap, syncMap, contentWrapperMap, taskContentMap);
        
        log.info("deliveryNotifyResultTask end. ");
    }
    
    /**
     * 调用ioc外呼接口
     * @param delivery
     */
    private void callIntelligentCall(ThirdpartyDeliveryWrapper delivery)
    {
    	//根据企业ID调用查询物流通业务接口（queryWltService），获取物流通业务信息
    	QueryWltServiceReq queryWltServiceReq = new QueryWltServiceReq();
    	queryWltServiceReq.setContentID(delivery.getTemplateID());
    	QueryWltServiceRsp queryWltServiceRsp = ecpmService.queryWltService(queryWltServiceReq);
    	if(queryWltServiceRsp != null && queryWltServiceRsp.getWltServiceInfo() != null)
    	{
    		if(CollectionUtils.isNotEmpty(queryWltServiceRsp.getWltServiceInfo().getWltUssdCalloutList()))
    		{
    			List<WltUssdCallout> wltUssdCalloutList = queryWltServiceRsp.getWltServiceInfo().getWltUssdCalloutList();
		    	//调用ioc的智能外呼接口（intelligentCall），caller 、callee 分别取物流通业务信息的用户号码、呼叫号码、serviceType=voiceQuery、sceneID=1（对话性交互）
    			IntelligentCallReq intelligentCallReq = new IntelligentCallReq();
    			intelligentCallReq.setRequestId(UUID.randomUUID().toString().substring(0,32));
    			intelligentCallReq.setCaller(wltUssdCalloutList.get(0).getUserNum());
    			intelligentCallReq.setCallee(wltUssdCalloutList.get(0).getReceiveNum());
    			intelligentCallReq.setServiceType(SERVICETYPE_VOICEQUERY);
    			intelligentCallReq.setSceneID(SCENEID);
    			List<NamedParameter> extensionInfoList = new ArrayList<NamedParameter>();
    			if(StringUtils.isNotEmpty(wltUssdCalloutList.get(0).getAddress()))
    			{
    				NamedParameter namedParameter = new NamedParameter();
    				namedParameter.setKey("address");
    				namedParameter.setValue(wltUssdCalloutList.get(0).getAddress());
    				extensionInfoList.add(namedParameter);
    			}
    			if(StringUtils.isNotEmpty(wltUssdCalloutList.get(0).getContentID()))
    			{
    				NamedParameter namedParameter = new NamedParameter();
    				namedParameter.setKey("contentID");
    				namedParameter.setValue(wltUssdCalloutList.get(0).getContentID());
    				extensionInfoList.add(namedParameter);
    			}
    			if(StringUtils.isNotEmpty(wltUssdCalloutList.get(0).getTrackingNum()))
    			{
    				NamedParameter namedParameter = new NamedParameter();
    				namedParameter.setKey("trackingNum");
    				namedParameter.setValue(wltUssdCalloutList.get(0).getTrackingNum());
    				extensionInfoList.add(namedParameter);
    			}
    			if(StringUtils.isNotEmpty(wltUssdCalloutList.get(0).getReply()))
    			{
    				NamedParameter namedParameter = new NamedParameter();
    				namedParameter.setKey("reply");
    				namedParameter.setValue(wltUssdCalloutList.get(0).getReply());
    				extensionInfoList.add(namedParameter);
    			}
				NamedParameter namedParameter1 = new NamedParameter();
				namedParameter1.setKey("businessType");
				namedParameter1.setValue("0");
				extensionInfoList.add(namedParameter1);
				NamedParameter namedParameter2 = new NamedParameter();
				namedParameter2.setKey("deliveryResult");
				namedParameter2.setValue("1");
				extensionInfoList.add(namedParameter2);
				List<ThirdpartyDeliveryTaskWrapper> taskList = thirdpartyDeliveryTaskMapper.queryDeliveryTaskByID(delivery.getTaskID());
				if(CollectionUtils.isNotEmpty(taskList))
				{
					NamedParameter namedParameter3 = new NamedParameter();
					namedParameter3.setKey("deliveryTime");
					namedParameter3.setValue(DateUtil.format(taskList.get(0).getInsertTime()));
					extensionInfoList.add(namedParameter3);
				}
				NamedParameter [] extensionInfo = new NamedParameter[extensionInfoList.size()];
				for(int i=0;i<extensionInfoList.size();i++)
				{
					extensionInfo[i] = extensionInfoList.get(i);
				}

    			intelligentCallReq.setExtensionInfo(extensionInfo);
    			StringBuffer sb = new StringBuffer();
    	        String timeStamp = DateUtil.format(new Date());
    			sb.append(iocPlatformId).append(timeStamp)
    					.append(iocAccountPasswd);
    			String md5Str = null;
    	    	try 
    	    	{
    				md5Str = MD5Util.encode(sb.toString());
    			} 
    	    	catch (Exception e) 
    	    	{
    	    		log.error("intelligentCall head encode error", e);
    			}
    			try {
					iocService.intelligentCall(iocPlatformId,timeStamp,md5Str,intelligentCallReq);
				} catch (Exception e) {
					log.error("intelligentCall error", e);
				}
    		}
    	}
    }
    
    /** 
     * 处理已经执行结束的投递任务
     * @param list 成功集合
     * @param faillist 失败集合
     * @param deliveryMap 投递
     * @param syncMap 同步
     * @param contentWrapperMap 内容
     * @see [类、类#方法、类#成员]
     */
    private void dealFinishedTaskList(List<Long> list, List<Long> faillist,
        Map<Long, ThirdpartyDeliveryWrapper> deliveryMap, Map<Long, DeliverySyncInfo> syncMap,
        Map<Long, ContentWrapper> contentWrapperMap, Map<Long, Long> taskContentMap)
    {
        // 移除处理成功的任务
        faillist.removeAll(list);
        // 更新处理成功的
        if (CollectionUtils.isNotEmpty(list))
        {
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("notifyTime", new Date());
            map.put("notifyStatus", NOTIFYSTATUS);
            map.put("list", list);
            map.put("processStatus", PROCESSSTATUS_SUCCESS);
            map.put("updateTime", new Date());
            thirdpartyDeliveryTaskMapper.updateNotifyStatusForSchedule(map);
        }
        // 更新处理失败的
        if (CollectionUtils.isNotEmpty(faillist))
        {
            // 更新处理失败的处理状态和更新时间
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("processStatus", PROCESSSTATUS_FAILED);
            map.put("updateTime", new Date());
            map.put("list", faillist);
            thirdpartyDeliveryTaskMapper.updateNotifyStatusForSchedule(map);
            
            ThirdpartyDeliveryWrapper delivery = null;
            DeliverySyncInfo info = null;
            // 处理失败的集合
            List<Long> fail3List = new ArrayList<Long>();
            for (Long task : faillist)
            {

                delivery = deliveryMap.get(task);
                // 失败三次，需要回退配额同步ES
                if (null != delivery)
                {
                    // 失败次数为3次的
                    fail3List.add(task);
                    info = syncMap.get(task);
                    // 投递成功的不需要还原配额
                    if (checkSendFailed(delivery))
                    {
                        log.info(
                            "deliveryNotifyResultTask.SentResult/UssdSentResult not success and smsNotify/ussdNotify timeout");
                    }
                    else
                    {
                        log.debug(" deal failed more times delivery , backquota and asynchroToEs , task is {} ", task);
                        //迭代8改造,挂机长短信算做多个配额
                        Long useCount = countLongContent(delivery);
                        backQuota(contentWrapperMap.get(taskContentMap.get(task)), info.getOwner(),useCount);
                        //如果需要还原配额，那就把这两个字段置空 
                        delivery.setSubscribeID(null);
                        delivery.setOrderID(null);
                    }
                    // 异步调用ES
                    asynchroToEs(delivery,
                        DeliveryConstant.FLAG_ES_DELIVERY_TASK,
                        info.getStatus(),
                        info.getColorSendType(),
                        info.getNoSendColorReason(),
                        info.getOwner(),
                        info.getColorPushTime(),
                        info.getColorSendDelayTime(),
                            DateUtil.format(new Date(), DateUtil.DatePattern.yyyyMMddHHmmssSSS1),
                            DateUtil.format(new Date(), DateUtil.DatePattern.yyyyMMddHHmmssSSS1));
                }
            }
            
            // notifyCount为3，processStatus更新为3，notifyStatus更新为1
            if (CollectionUtils.isNotEmpty(fail3List))
            {
                map.put("processStatus", PROCESSSTATUS_FAILED);
                map.put("notifyStatus", NOTIFYSTATUS);
                map.put("updateTime", new Date());
                map.put("list", faillist);
                thirdpartyDeliveryTaskMapper.updateNotifyStatusForSchedule(map);
            }
            
        }
    }
    
    /**
     * 构造投递任务map
     * <功能详细描述>
     * @param deliveryList 当前需要执行投递任务列表
     * @return 投递任务map，key为taskID
     * @see [类、类#方法、类#成员]
     */
    private Map<Long, ThirdpartyDeliveryWrapper> buildDeliveryMap(List<ThirdpartyDeliveryWrapper> deliveryList)
    {
        Map<Long, ThirdpartyDeliveryWrapper> rtnMap = new HashMap<Long, ThirdpartyDeliveryWrapper>();
        for (ThirdpartyDeliveryWrapper delivery : deliveryList)
        {
            // 存放最后一次重试的任务，用于失败时回退配额
            if (notifyCount.equals(delivery.getNotifyCount() + 1))
            {
                rtnMap.put(delivery.getTaskID(), delivery);
            }
        }
        return rtnMap;
    }
    
    /** 
     * 投递成功不需要扣减配额
     * @return
     * @see [类、类#方法、类#成员]
     */
    private boolean checkSendFailed(ThirdpartyDeliveryWrapper delivery)
    {
        boolean isSendFailed = false;
        if (Constants.Delivery.RESULT_SUCCESS.equals(delivery.getDeliveryResult())
            || Constants.Delivery.RESULT_SUCCESS.equals(delivery.getUssdDeliveryResult()))
        {
            isSendFailed = true;
        }
        return isSendFailed;
    }
    
    /** 
     * ussd投递失败转短信重新计算时间
     * @param delivery
     * @return
     * @see [类、类#方法、类#成员]
     */
    private Long secondSetSendDelayTime(ThirdpartyDeliveryWrapper delivery, Long colorSendDelayTime)
    {
        if (MSGTYPE_THREE.equals(delivery.getMsgType()) || MSGTYPE_USSD.equals(delivery.getMsgType()))
        {
            //ussd失败转闪信：reviceNotifyTime - ussdPushTime
            if (null != delivery.getReviceNotifyTime() && null != delivery.getUssdPushTime())
            {
                colorSendDelayTime = delivery.getReviceNotifyTime().getTime() - delivery.getUssdPushTime().getTime();
            }
            else
            {
                colorSendDelayTime = null;
            }
        }
        return colorSendDelayTime;
    }
    
    /**
     * 还原配额
     * 
     * @see [类、类#方法、类#成员]
     */
    private void backQuota(ContentWrapper contentWrapper, String mobilePlatform,Long useCount)
    {
        if (null == contentWrapper)
        {
            return;
        }
        DeductingQuotaReq deductingQuotaReq =
            new DeductingQuotaReq(contentWrapper.getEnterpriseID(), new Date(), contentWrapper.getServType(),
                contentWrapper.getSubServType(), contentWrapper.getChargeType(), mobilePlatform, -useCount);
        
        try
        {
            subscribeServices.deductingQuota(deductingQuotaReq);
        }
        catch (EcpeException e)
        {
            log.error("back quota exception, e=" + e + " ,req=" + deductingQuotaReq);
            ErrorlLogUtils.errorLog("back quota exception, e=" + e + " ,req=" + deductingQuotaReq);
        }
        
    }
    
    /**
     * 默认赋值：ussd:ussdReviceNotifyTime-ussdPushTime;非ussd:reviceNotifyTime–pushTime 
     * 后续重新判断：ussd失败转闪信的状况
     * 
     * @param delivery
     * @return
     * @see [类、类#方法、类#成员]
     */
    private Long chargeColorSendDelayTime(ThirdpartyDeliveryWrapper delivery)
    {
        if (DeliveryConstant.DeliveryAction.SMS_NORMAL.equals(delivery.getDeliveryFirstAction())
            || DeliveryConstant.DeliveryAction.SMS_FLASH.equals(delivery.getDeliveryFirstAction()))
        {
            if (null != delivery.getReviceNotifyTime() && null != delivery.getPushTime())
            {
                return delivery.getReviceNotifyTime().getTime() - delivery.getPushTime().getTime();
            }
        }
        else
        {
            if (null != delivery.getUssdReviceNotifyTime() && null != delivery.getUssdPushTime())
            {
                return delivery.getUssdReviceNotifyTime().getTime() - delivery.getUssdPushTime().getTime();
            }
        }
        return null;
    }
    

    
    private String chargeColorSendType(String deliveryFirstAction)
    {
        if (DeliveryConstant.DeliveryAction.SMS_NORMAL.equals(deliveryFirstAction))
        {
            return DeliveryConstant.CY_SEND_TYPE_NORMAL;
        }
        else if (DeliveryConstant.DeliveryAction.SMS_FLASH.equals(deliveryFirstAction))
        {
            return DeliveryConstant.CY_SEND_TYPE_FLASH;
        }else if (DeliveryConstant.DeliveryAction.MMS.equals(deliveryFirstAction))
        {
            return DeliveryConstant.CY_SEND_TYPE_MMS;
        }
        else
        {
            return DeliveryConstant.CY_SEND_TYPE_USSD;
        }
    }
    
    private void checkMsgTypeGetDeliveryAction(ThirdpartyDeliveryWrapper delivery)
    {
        if (null == delivery.getRuleID())
        {
            log.error("ruleID is null!");
            ErrorlLogUtils.errorLog("ruleID is null!");
            return;
        }
        ThirdpartyDeliveryRuleWrapper deliveryRule =
            deliveryRuleDataService.queryDeliveryRuleByID(delivery.getRuleID());
        
        if (null == deliveryRule)
        {
            log.error("cannot find delivery rules. ruleID={}", delivery.getRuleID());
            ErrorlLogUtils.errorLog("cannot find delivery rules. ruleID={}", delivery.getRuleID());
            return;
        }
        
        //设置投递方式
        delivery.setDeliveryFirstAction(deliveryRule.getDeliveryFirstAction());
        delivery.setDeliveryFailedAction(deliveryRule.getDeliveryFailedAction());
        
        return;
    }
    
    /**
     * 
     * 构造投递需要的同步数据
     *
     * <AUTHOR>
     * @param thirdpartyDeliveryWrapper 第三方投递结果
     * @param contentWrapper 内容信息
     * @param flag 投递标识
     * @param deliveryResultES 投递结果
     * @param colorSendType 发送结果类型
     * @param noSendColorReason 未发送结果
     * @param owner 
     * @param colorSendDelayTime 
     * @param colorPushTime 
     * @return 投递同步ES需要的数据
     */
    private DeliveryTaskDomain buildSynchroToEs(ThirdpartyDeliveryWrapper thirdpartyDeliveryWrapper,
        ContentWrapper contentWrapper, String flag, String deliveryResultES, String colorSendType,
        String noSendColorReason, String owner, Date colorPushTime, Long colorSendDelayTime)
    {
        DeliveryTaskDomain taskParam = new DeliveryTaskDomain();
        taskParam.setFlag(flag);
        taskParam.setDeliveryResult(deliveryResultES);
        taskParam.setContentWrapper(contentWrapper);
        
        taskParam.setDeliveryContent(thirdpartyDeliveryWrapper);
        taskParam.setColorSendType(colorSendType);
        taskParam.setNoSendColorReason(noSendColorReason);
        taskParam.setOwner(owner);
        taskParam.setColorPushTime(colorPushTime);
        taskParam.setColorSendDelayTime(colorSendDelayTime);
        
        return taskParam;
    }
    
    /**
     * 
     * 构造第三方通知的线程池
     *
     * <AUTHOR>
     * @param deliveryResultNotifyReq 调用通知接口机的请求
     * @param taskList 线程池
     * @param deliveryToEsService 调用ES的service层
     * @param deliveryTaskDomain 同步ES需要的数据
     */
    public void buildDeliveryNotifyResultTask(DeliveryResultNotifyReq deliveryResultNotifyReq,
        List<Future<Long>> taskList, DeliveryToEsService deliveryToEsService, DeliveryTaskDomain deliveryTaskDomain,
        ThirdpartyDeliveryWrapper delivery)
    {
        // 201
        QueryEnterpriseInfoReq queryEnterpriseInfoReq = new QueryEnterpriseInfoReq();
        queryEnterpriseInfoReq.setId(deliveryTaskDomain.getContentWrapper().getEnterpriseID());
        QueryEnterpriseInfoRsp queryEnterpriseInfoRsp = dsumService.queryEnterpriseInfo(queryEnterpriseInfoReq);
        String parentEnterpriseID = deliveryTaskDomain.getContentWrapper().getEnterpriseID().toString();
        if (null != queryEnterpriseInfoRsp
                && null != queryEnterpriseInfoRsp.getEnterprise()
                && null != queryEnterpriseInfoRsp.getEnterprise().getParentEnterpriseID()
        ){
            parentEnterpriseID = queryEnterpriseInfoRsp.getEnterprise().getParentEnterpriseID().toString();
        }
        ComparableFutureTask<Long> task = new ComparableFutureTask<Long>(
            new DeliveryNotifyResultExecuteScheduleTask(deliveryResultNotifyReq, ecpfepService, deliveryToEsService,
                deliveryTaskDomain, subscribeServices, thirdpartyDeliveryTaskMapper, getPriority(delivery),contentMapper,reduceSubscribeSwitch,needPlatformEnterprise,parentEnterpriseID));
        
        taskList.add(task);
        
        try
        {
            deliveryNotifyResultTaskExecuter.submit(task);
        }
        catch (Exception e)
        {
            log.error("buildDeliveryNotifyResultTask is fail " + e);
            ErrorlLogUtils.errorLog("buildDeliveryNotifyResultTask is fail " + e);
        }
    }
    
    /** 获取优先级
     * 优先级属性为优先级基础值5 + notifyCount通知次数（为空则取0）
     * @param delivery
     * @return
     * @see [类、类#方法、类#成员]
     */
    private int getPriority(ThirdpartyDeliveryWrapper delivery)
    {
        int notifyCount = null != delivery.getNotifyCount() ? delivery.getNotifyCount() : 0;
        return notifyCount + deliveryNotifyBasePriority;
    }
    
    /**
     *    
     * 构造成功和是的taskID
     *
     * <AUTHOR>
     * @param list 需要更新数据库的taskID
     * @param tasks 执行线程的里面的信息
     */
    private void buildUpdateSuccessTaskID(List<Long> list, List<Future<Long>> tasks)
    {
        try
        {
            // 只有处理完了，才更新数据库
            for (Future<Long> task : tasks)
            {
                Long taskID = task.get(dealDeliveryNotifyTimeOut, TimeUnit.SECONDS);
                
                // 如果线程处理成功，才更新数据库
                if (!DeliveryConstant.FAILDEFAULTTASKID.equals(taskID))
                {
                    list.add(taskID);
                }
            }
        }
        catch (InterruptedException e)
        {
            log.error("DeliveryNotifyResultTask is del dDeliveryNotifyResultTask", e);
            ErrorlLogUtils.errorLog("DeliveryNotifyResultTask is del dDeliveryNotifyResultTask", e);
        }
        catch (ExecutionException e)
        {
            log.error("DeliveryNotifyResultTask is del dDeliveryNotifyResultTask", e);
            ErrorlLogUtils.errorLog("DeliveryNotifyResultTask is del dDeliveryNotifyResultTask", e);
        }
        catch (TimeoutException e)
        {
            log.error("DeliveryNotifyResultTask is del dDeliveryNotifyResultTask", e);
            ErrorlLogUtils.errorLog("DeliveryNotifyResultTask is del dDeliveryNotifyResultTask", e);
        }
    }


    private void asynchroToEs(ThirdpartyDeliveryWrapper deliveryWrapper, String flag, String deliveryResultES,
                              String colorSendType, String noSendColorReason, String owner, Date colorPushTime, Long colorSendDelayTime){
        asynchroToEs( deliveryWrapper,  flag,  deliveryResultES,
                 colorSendType,  noSendColorReason,  owner,  colorPushTime,  colorSendDelayTime,null,null);

    }
    /**
     * 
     * 异步调用
     *
     * <AUTHOR>
     * @param deliveryWrapper 第三方投递wrapper
     * @param flag 投递标识 
     * @param deliveryResultES 投递结果
     * @param colorSendType 投递结果类型
     * @param noSendColorReason 未发送原因
     * @param owner 
     * @param colorSendDelayTime 
     * @param colorPushTime 
     */
    private void asynchroToEs(ThirdpartyDeliveryWrapper deliveryWrapper, String flag, String deliveryResultES,
        String colorSendType, String noSendColorReason, String owner, Date colorPushTime, Long colorSendDelayTime,String enterMethodTime,String existMethodTime)
    {
        DeliveryTaskDomain taskParam = new DeliveryTaskDomain();
        
        taskParam.setDelivery(deliveryWrapper);
        taskParam.setContentMapper(contentMapper);
        taskParam.setThirdpartyDeliveryTaskMapper(thirdpartyDeliveryTaskMapper);
        taskParam.setDsumService(dsumService);
        taskParam.setElasticSearchCluster(elasticSearchCluster);
        taskParam.setFlag(flag);
        taskParam.setDeliveryResult(deliveryResultES);
        taskParam.setOwner(owner);
        
        taskParam.setColorSendType(colorSendType);
        taskParam.setNoSendColorReason(noSendColorReason);
        taskParam.setColorPushTime(colorPushTime);
        taskParam.setColorSendDelayTime(colorSendDelayTime);
        taskParam.setHotLineManagementService(hotLineManagementService);
        taskParam.setEnterMethodTime(enterMethodTime);
        taskParam.setExistMethodTime(existMethodTime);
        Runnable task = new DeliveryToEsRunnable(taskParam);
        try
        {
            this.singleThreadExecutor.execute(task);
        }
        catch (Exception e)
        {
            log.error("singleThreadExecutor.execute error", e);
            ErrorlLogUtils.errorLog("singleThreadExecutor.execute error", e);
        }
    }
    
    /**
     * 
     * 构造内容ID和内容的映射关系
     *
     * <AUTHOR>
     * @param deliveryList 第三方投递内容列表
     * @return 内容列表对象
     */

    private List<ContentWrapper> buildContentMapperList(List<ThirdpartyDeliveryWrapper> deliveryList)
    {
        if (CollectionUtils.isEmpty(cmppTemplateID))
        {
            cmppTemplateID = FrameSpringBeanUtil.cmppTemplateID;
        }
        List<Long> contentIDs = new ArrayList<Long>();
        ArrayList<ContentWrapper> contentWrappers = new ArrayList<>();

        HashMap<Long, String> idToContJsonMap = new HashMap<>();

        for (ThirdpartyDeliveryWrapper delivery : deliveryList)
        {
            Long templateID = Long.valueOf(delivery.getTemplateID());

            if (!cmppTemplateID.contains(templateID.toString()))
            {
                contentIDs.add(templateID);
            }
            else
            {
                //如果是cmpp投递,内容没有企业id,查出公共模板内容,然后给enterpriseID赋值
                String cloneJSon = idToContJsonMap.get(templateID);
                if (StringUtils.isEmpty(cloneJSon))
                {
                    ContentWrapper content = contentMapper.queryContentByID(templateID);
                    cloneJSon = JSON.toJSONString(content);
                    idToContJsonMap.put(templateID,cloneJSon);
                }

                ContentWrapper contentWrapperCopy = JSON.parseObject(cloneJSon,ContentWrapper.class);
                contentWrapperCopy.setEnterpriseID(delivery.getEnterpriseID());
                contentWrappers.add(contentWrapperCopy);
            }
        }
        if (CollectionUtils.isNotEmpty(contentIDs))
        {
            List<ContentWrapper> wrapperList = contentMapper.batchQueryContentByID(contentIDs);
            contentWrappers.addAll(wrapperList);
        }
        return contentWrappers;
    }

    /**
     * 
     * 构造每个
     *
     * <AUTHOR>
     * @param contentMapperList 内容列表对象
     * @return 内容map信息
     */
    private Map<Long, ContentWrapper> buildContentMap(List<ContentWrapper> contentMapperList)
    {
        Map<Long, ContentWrapper> contentMap = new HashMap<Long, ContentWrapper>();
        
        for (ContentWrapper contentWrapper : contentMapperList)
        {
            contentMap.put(contentWrapper.getId(), contentWrapper);
        }
        
        return contentMap;
    }
    
    /**
     * 
     * 构造第三方接入的表
     *
     * <AUTHOR>
     * @param deliveryList 内容列表
     * @return 企业ID和第三方接入信息
     */
    private Map<Integer, ThirdPartyWrapper> buildThirdPartyMap(List<ThirdpartyDeliveryWrapper> deliveryList)
    {
        //需要返回的 企业ID和第三方接入信息Map 
        Map<Integer, ThirdPartyWrapper> thirdPartyMap = new HashMap<Integer, ThirdPartyWrapper>();
        
        List<Integer> platformIDs = new ArrayList<Integer>();
        
        // 遍历内容
        for (ThirdpartyDeliveryWrapper thirdpartyDeliveryWrapper : deliveryList)
        {
            platformIDs.add(thirdpartyDeliveryWrapper.getEnterpriseID());
        }
        
        List<ThirdPartyWrapper> thirdPartyMapperList = thirdPartyMapper.batchQueryThirdPartyByPlatformID(platformIDs);
        
        for (ThirdPartyWrapper thirdPartyWrapper : thirdPartyMapperList)
        {
            thirdPartyMap.put(thirdPartyWrapper.getPlatformID(), thirdPartyWrapper);
        }
        
        return thirdPartyMap;
    }
    /**
     * 如果是挂机长短信的话配额算多条,其他算1条
     * @param
     * @return
     * @see [类、类#方法、类#成员]
     */
    private Long countLongContent(ThirdpartyDeliveryWrapper delivery){
        //迭代8改造,挂机长短信投递按照多条配额来算
        Long useCount = 1L;
        try
        {
            //迭代8改造,挂机长短信算多个配额
            ThirdpartyDeliveryTaskWrapper thirdpartyDeliveryTask = queryDeliveryTaskInfo(delivery);
            //拿到投递类型,需判断是不是6--挂短
            Integer msgType = thirdpartyDeliveryTask.getMsgType();
            //拿到原始内容,没替换模板变量
            ContentWrapper content = contentMapper.queryContentByID(Long.valueOf(delivery.getTemplateID()));
            // 获取投递内容,模板+变量参数替换后的投递内容
            String contentDelivery = getContentDelivery(delivery, content);
            int contLength = contentDelivery.length();
            useCount=UserCountUtil.getCountByLength(msgType.toString(),contLength);
            // 投递为挂机短信投递（msgtype=6），计费条数据计算获得，计算方式为：内容长度<=70按1条计算，大于70按照:长度/67+(长度%67==0?0:1)；
           /* if (DeliveryConstant.DELIVERY_REQ_SMS_SUBMIT_MSGTYPE.equals(msgType.toString()) && contLength > 70)
            {
                Integer i = Integer.valueOf((contLength / 67) + (contLength % 67 == 0 ? 0 : 1));
                useCount = i.longValue();
            }
            else if(("2".equals(msgType.toString())||"4".equals(msgType.toString())||
    				"12".equals(msgType.toString())||"14".equals(msgType.toString()))&& contLength > 70){
            	Integer i = Integer.valueOf((contLength / 67) + (contLength % 67 == 0 ? 0 : 1));
                useCount = i.longValue();
            }*/
        }
        catch (Exception e)
        {
            log.error("count useCount error",e);
            ErrorlLogUtils.errorLog("count useCount error",e);
            return useCount;
        }
        return useCount;
    }

    private String getContentDelivery(ThirdpartyDeliveryWrapper deliveryCont, ContentWrapper contentWrapper) {
        List<String> args = null;
        if (StringUtils.isNotBlank(deliveryCont.getArgv())) {
            JSONArray array = JSON.parseArray(deliveryCont.getArgv());
            if (null != array) {
                args = array.toJavaList(String.class);
            }
        }
        String cont = ContentUtil.replaceParams(contentWrapper.getContent(), args);
        return cont;
    }

    private ThirdpartyDeliveryTaskWrapper queryDeliveryTaskInfo(ThirdpartyDeliveryWrapper delivery)
    {
        if (null == delivery.getTaskID())
        {
            return null;
        }
        Long taskID = delivery.getTaskID();
        ThirdpartyDeliveryTaskWrapper deliveryTask = null;
        //查询投递任务
        List<ThirdpartyDeliveryTaskWrapper> deliveryTaskList =
            thirdpartyDeliveryTaskMapper.queryDeliveryTaskByID(taskID);

        if (CollectionUtils.isNotEmpty(deliveryTaskList))
        {
            deliveryTask = deliveryTaskList.get(0);

        }
        return deliveryTask;
    }
}
