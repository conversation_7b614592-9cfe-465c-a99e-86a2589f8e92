########################################################
### notifytemplate_service_url
########################################################
notifyService.application.url=127.0.0.1:8888

########################################################
### template_header_auth
########################################################
ecpfep.header.secret=sd7sf8ht93k57l23nhg56j3bvw8f7g6w


########################################################
### delivery_service_url
########################################################
deliveryService.application.url=127.0.0.1:8888

########################################################
### \u63A5\u53E3\u6D41\u63A7\u9608\u503C\u914D\u7F6Emap\uFF1Akey\u503C\u4E3A\u65B9\u6CD5\u540D
########################################################
ecpfep.flow-control.defaultFlowControl=50
ecpfep.flow-control.thresholdMap[delivery]=700

# spi interface application name
spi.interface.application.names=com.huawei.jaguar.ecpfep.spi.feign.DeliverySpiService:DeliverySpiService|com.huawei.jaguar.ecpfep.spi.feign.DsumService:dsum|com.huawei.jaguar.ecpfep.spi.feign.EcpeService:ecpe|com.huawei.jaguar.ecpfep.spi.feign.EcpmService:ecpm|com.huawei.jaguar.ecpfep.spi.feign.ManagerService:managerService

#\u6302\u673A\u77ED\u4FE1\u5185\u5BB9\u957F\u5EA6\u9650\u5236
ecpfep.hangingSms.length=750

ecpfep.deliverySpiService.connectTimeout=500
ecpfep.deliverySpiService.readTimeout=2000

ecpfep.managerService.connectTimeout=500
ecpfep.managerService.readTimeout=2000

ecpfep.cpmmGuajiTemplateID=500000
ecpfep.cmppflashTemplateID=499999

ecpfep.server.ip=************
ecpfep.http.port=28080
ecpfep.cpmmFrameExpire=259200

ecpfep.ebossNotify.url=http://ip:port/ecpfep/ebossEnterprises/backEboss

#\u662F\u5426\u6821\u9A8C\u547C\u53EB\u72B6\u6001\u4E8B\u4EF6\u4F01\u4E1A
ecpfep.callEvent.Enterprise=30000379|30000564|********|10000001|30000711|20000122|30000850
ecpfep.is.toprovice=1

ecpfep.submitTemplate.scene=|1|2|3|4|5|6|7|8|9|10|11|12|13|14|
ecpfep.bboss.getTokenUrl=http://***********:2357/api/token/authinternal
ecpfep.bbssprodordrspserv.url=http://***********:2357/api/v2/sync/BBOSS/BBSSProdordRspServ
###\u6210\u5458\u540C\u6B65###
ecpfep.bboss.syncMemUrl=http://***********:2357/api/v2/sync/BBOSS/ABSMembServ
ecpfep.bboss.syncMemRspUrl=http://***********:2357/api/v2/sync/BBOSS/ABSMembRspServ
bboss.common.request.params.domain=BBSS
bboss.common.request.params.routeType=00
bboss.common.request.params.routeValue=998
bboss.common.request.params.busType=BBSS
bboss.common.request.params.AppId=QYCY5910
bboss.common.request.params.AppSecret=creBv9jTDT
bboss.common.request.params.envFlag=1
bboss.common.request.params.version=1.0.0
bboss.common.request.params.signMethod=md5
bboss.common.request.params.3DESKEY=A906F2CABB76E9162EA74E811C825EF69F15EEA487AEB537

ecpfep.createOrderTheard.coolSize=5
ecpfep.createOrderTheard.maxSize=10

##\u4EA7\u54C1\u8BA2\u5355\u540C\u6B65\u786E\u8BA4url
ecpfep.queryBalanceUrl.url=http://10.124.59.11:8080/web/dm-account/v1/expense/colorprint-account-balance
ecpfep.queryBalanceUrl.secretKey=cp#2020
ecpfep.businessOrderAccept.productDesc={"1,11,1,1": "1007","1,11,2,1": "1040","1,11,3,1": "1044","1,12,1,1": "1063","1,12,1,2": "1069","1,12,1,3": "1070","1,12,2,1": "1075","1,12,2,2": "1071","1,12,2,3": "1073","1,12,3,1": "1076","1,12,3,2": "1072","1,12,3,3": "1074","1,13,1,1": "1092","1,13,1,2": "1082","1,13,1,3": "1085","1,13,2,1": "1093","1,13,1,3": "1085","1,13,2,1": "1093","1,13,2,2": "1083","1,13,2,3": "1086","1,13,3,1": "1081","1,13,3,2": "1084","1,13,3,3": "1087","1,21,1,1": "1009","1,21,2,1": "1079","1,21,3,1": "1080","1,22,1,1": "1064","1,22,2,1": "1077","1,22,3,1": "1078","1,23,1,1": "1088","1,23,2,1": "1089","1,23,3,1": "1090","1,31,1,1": "1011","1,32,1,1": "1065","1,33,1,1": "1091","2,11,1,1": "1020","2,11,2,1": "1032","2,11,3,1": "1036","2,21,1,1": "1022","2,21,2,1": "201310","2,21,3,1": "201314","2,31,1,1": "1024","2,41,1,1": "201524","3,11,1,1": "1066","3,11,2,1": "1067","3,11,3,1": "1068","4,11,1,1": "201508","4,11,2,1": "201512","4,11,3,1": "201516","4,21,1,1": "201324","4,21,2,1": "201503","4,21,3,1": "201505","4,31,1,1": "201520","4,41,1,1": "201318"}
ecpfep.ospSyncQuotaOvernotify.ospGetTokenUrl=http://**************:20200/aopoauth/oauth/token
ecpfep.ospSyncQuotaOvernotify.ospSyncQuotaOvernotifyUrl=http://**************:20110/oppf
ecpfep.ospSyncQuotaOvernotify.appId=1077848
ecpfep.ospSyncQuotaOvernotify.appKey=8f8ce9bf8b6223d553043ef8117d8c63
ecpfep.ospSyncQuotaOvernotify.method=OSP_SYNC_QUOTA_OVERNOTIFY
ecpfep.ospSyncQuotaOvernotify.version=1.0
ecpfep.ospSyncQuotaOvernotify.busiSerial=1
bboss.createECEnterpriseInfo.FilterLimitProvinceId=10

BBSSProdordServ.produce.description=体验套餐0元，移动本网不限量
ecpfep.subprovince.servType={"01": "|1|","09": "|1|2|5|", "11": "|1|5|", "15": "|1|", "16": "|1|", "04": "|9|", "10": "|9|", "12": "|9|", "13": "|9|", "14": "|9|", "27": "|9|"}
ecpfep.mobileCloud.productId=****************|****************|****************|****************|****************|****************|2111251530152236B|2111251536162239B|2111251524522233A|2111251524522233C

ecpfep.submitTemplate.enterprise=0c5ee706b1184125a4a1bdd9aa64ade4|37fc73805c3943c0a96297baefaf99be
migu.email.senderAddress=cyxinxiang@migu.cn1
migu.email.senderAccount=cyxinxiang@migu.cn1
migu.email.senderPassword=QAZwsx@20188
migu.email.smtpServer=************
migu.email.smtpPort=465
delivery.ussd.trans.switch=1

BBSSProdordServ.produce.productCode=*********|*********|*********
BBSSProdordServ.produce.productCode.parameterNameX=*********
BBSSProdordServ.produce.productCode.parameterNameY=*********


ecpfep.ThirdpartyAccess.enterprises=********,********

ecpfep.createOrganization.quotaPkgIDInfo=1001:::1:::10:::2000:::-1:::0,1002:::1:::10:::2000:::150:::0,1101:::2:::00:::150:::-1:::0,1102:::2:::00:::300:::-1:::0,1201:::3:::00:::60:::-1:::0,1202:::3:::00:::120:::-1:::0,1003:::1:::00:::100:::-1:::1,1004:::1:::10:::2000:::50:::0,1005:::1:::10:::2000:::300:::0,1006:::1:::10:::2000:::600:::0,2001:::2:::00:::100:::-1:::0,2002:::2:::00:::200:::-1:::0,2003:::2:::00:::500:::-1:::0,2004:::2:::00:::800:::-1:::0



#\u6587\u4EF6\u4E0A\u4F20\u5730\u5740
ecpfep.uploadFile.path=/sftp/data/uploadFile
#\u6587\u4EF6\u4E0A\u4F20\u7684fileUse
ecpfep.uploadFile.fileUse=ebanhanceMms
ecpfep.uploadFile.businessLicense.fileUse=businessLicense
ecpfep.uploadFile.certificate.fileUse=certiFile
ecpfep.MGMusic.productId={"**********":{"productName":"双彩融合网内版（彩印5元套餐）","unitPrice":5},"**********":{"productName":"双彩融合三网基础版（彩印10元套餐）","unitPrice":10},"**********":{"productName":"双彩融合三网升级版（彩印20元套餐）","unitPrice":20}}
ecpfep.updateCorpInfo.forbiddenIp=127.0.0.1
ecpfep.updateCorpInfo.forbiddenTime=20231213