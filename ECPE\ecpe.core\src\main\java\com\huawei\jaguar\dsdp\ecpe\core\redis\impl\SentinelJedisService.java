
package com.huawei.jaguar.dsdp.ecpe.core.redis.impl;

import com.huawei.jaguar.dsdp.color.common.config.JedisPoolConfig;
import com.huawei.jaguar.dsdp.color.common.redis.common.SentinelClusterImpl;
import com.huawei.jaguar.dsdp.color.common.redis.compress.impl.ProtostuffTranscoder;
import com.huawei.jaguar.dsdp.color.common.utils.JedisClientClusterUtil;
import com.huawei.jaguar.dsdp.ecpe.core.redis.JedisCluster;
import com.huawei.jaguar.dsdp.ecpe.exception.EcpeException;
import com.huawei.jaguar.dsdp.ecpe.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.ShardedJedis;
import redis.clients.jedis.params.SetParams;
import redis.clients.jedis.util.Pool;
import redis.clients.jedis.util.SafeEncoder;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 * Sentinel模式初始化
 *
 * <AUTHOR>
 * @version C10 2018年11月26日
 * @since SDP V300R003C10
 */
@Slf4j
@Service
public class SentinelJedisService implements JedisCluster {
    /**
     * comons的初始化redis方法
     */
    private SentinelClusterImpl sentinelClusterImpl;

    /**
     * 序列化
     */
    private final ProtostuffTranscoder protostuffTranscoder = new ProtostuffTranscoder();

    @Override
    public SentinelJedisService initialize(JedisPoolConfig jedisPoolConfig, String connectUrls) {

        int mode = JedisClientClusterUtil.getRedisMode(connectUrls);
        if (mode == JedisClientClusterUtil.SENTINEL_CLUSTER) {
            sentinelClusterImpl = new SentinelClusterImpl();
        } else {
            throw new EcpeException(ResultCode.OTHER_EXCEPTION,
                "sentinel mode is chose, but redis server doesn't support." + connectUrls);
        }

        sentinelClusterImpl.initCluster(connectUrls, jedisPoolConfig);

        return this;

    }

    @Override
    public void init() {

    }

    @Override
    public void set(String key, Object object) {
        byte[] data = protostuffTranscoder.encode(object);

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.set(SafeEncoder.encode(key), data);
        } finally {
            returnJedis(jedis);

        }
    }

    @Override
    public void set(byte[] key, byte[] value) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.set(key, value);
        } finally {
            returnJedis(jedis);

        }
    }

    @Override
    public void setex(byte[] key, int seconds, byte[] value) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.setex(key, seconds, value);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when set cache.", e);
        } finally {
            returnJedis(jedis);

        }
    }

    /**
     * 向缓存池返回缓存连接。
     * 
     * @param jedis jedis
     * @since V300R003C20B301
     */
    protected void returnJedis(ShardedJedis jedis) {
        if (null == jedis) {
            return;
        }

        try {
            // 由于调用returnResource()也可能抛异常，而缓存里即使里抛异常也不应影响主体业务逻辑，所以捕获所有异常
            sentinelClusterImpl.returnShardedJedis(jedis);
        } catch (Throwable e) {
            log.warn("error when return cache connection.");
        }
    }

    @Override
    public <T> T get(String key, Class<T> targetClass) {

        byte[] ret = null;
        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            ret = jedis.get(SafeEncoder.encode(key));
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when get(String key, Class<T> clazz) from cache.", e);
        } finally {
            returnJedis(jedis);

        }

        if (null == ret) {
            return null;
        } else {
            try {
                log.debug("found in cache, key=" + key + ",object=" + protostuffTranscoder.decode(ret, targetClass));
                return protostuffTranscoder.decode(ret, targetClass);
            } catch (Exception e) {
                log.warn("error when get({}, Class<T> clazz) from cache. byte{}",key, Arrays.toString(ret));
                return null;
            }
        }
    }

    // 用于特殊对象脱敏处理，如手机号
    @Override
    public <T> T get(String key, Class<T> targetClass, Boolean isNotLog) {
        // 打印日志
        if (!isNotLog) {
            return get(key, targetClass);
        }

        // 不打印日志
        byte[] ret = null;
        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            ret = jedis.get(SafeEncoder.encode(key));
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when get(String key, Class<T> clazz) from cache.", e);
        } finally {
            returnJedis(jedis);
        }

        return null == ret ? null : protostuffTranscoder.decode(ret, targetClass);
    }

    @Override
    public void del(String key) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.del(SafeEncoder.encode(key));
        } catch (Throwable e) {

            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when delete(String key) from cache.", e);
        } finally {
            returnJedis(jedis);

        }
    }

    @Override
    public void del(byte[] key) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.del(key);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when delete(byte[] key) from cache.", e);
        } finally {
            returnJedis(jedis);

        }
    }

    @Override
    public long setnx(String key, String value) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.setnx(key, value);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when setnx cache.", e);
        } finally {
            returnJedis(jedis);

        }

        return 0;
    }

    @Override
    public String getSetBytes(String key, String value) {

        byte[] date = null;
        ShardedJedis jedis = null;

        try {
            jedis = sentinelClusterImpl.getShardedJedis();

            date = jedis.getSet(SafeEncoder.encode(key), SafeEncoder.encode(value));
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when getSetBytes from cache.", e);
        } finally {
            returnJedis(jedis);

        }

        if (null == date) {
            return null;
        } else {
            return SafeEncoder.encode(date);
        }
    }

    @Override
    public List<String> lrange(String key, long start, long end) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.lrange(key, start, end);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when lrange cache.", e);
            return null;
        } finally {
            returnJedis(jedis);

        }
    }

    @Override
    public Long lrem(String key, long count, String value) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.lrem(key, count, value);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when lrem cache.", e);
            return null;
        } finally {
            returnJedis(jedis);

        }
    }

    @Override
    public Long lpush(String key, String... strings) {

        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.lpush(key, strings);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when lpush cache.", e);
            return null;
        } finally {
            returnJedis(jedis);

        }
    }

    @Override
    public List<HostAndPort> getAllRedisInfo() {
        return sentinelClusterImpl.getAllRedis();
    }

    @Override
    public void refresh(String connectUrls) {

    }

    @Override
    public Set<String> hkeys(String pattern) {
        ShardedJedis shardedJedis = null;
        Set<String> rtn = new HashSet<String>();
        try {
            shardedJedis = sentinelClusterImpl.getShardedJedis();
            rtn = shardedJedis.hkeys(pattern);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when hkeys cache.", e);
        } finally {
            returnJedis(shardedJedis);
        }
        return rtn;
    }

    @Override
    public Map<String, String> hgetAll(String pattern) {
        ShardedJedis shardedJedis = null;
        Map<String, String> rtn = new HashMap<String, String>();
        try {
            shardedJedis = sentinelClusterImpl.getShardedJedis();
            rtn = shardedJedis.hgetAll(pattern);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when hgetAll cache.", e);
        } finally {
            returnJedis(shardedJedis);
        }
        return rtn;
    }

    @Override
    public String hget(String key, String failed) {
        ShardedJedis jedis = null;
        String rtn = "";
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            rtn = jedis.hget(key, failed);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when hget cache.", e);
        } finally {
            returnJedis(jedis);
        }
        return rtn;
    }

    @Override
    public Long hdel(String key, String... fields) {
        ShardedJedis shardedJedis = null;
        Long rtn = null;
        try {
            shardedJedis = sentinelClusterImpl.getShardedJedis();
            rtn = shardedJedis.hdel(key, fields);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when hdel cache.", e);
        } finally {
            returnJedis(shardedJedis);
        }
        return rtn;
    }

    @Override
    public Long hset(String key, String field, String value) {
        ShardedJedis shardedJedis = null;
        Long rtn = null;
        try {
            shardedJedis = sentinelClusterImpl.getShardedJedis();
            rtn = shardedJedis.hset(key, field, value);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when hset cache.", e);
        } finally {
            returnJedis(shardedJedis);
        }
        return rtn;
    }

    @Override
    public String hmset(String key, Map<String, String> hash) {
        ShardedJedis shardedJedis = null;
        String rtn = null;
        try {
            shardedJedis = sentinelClusterImpl.getShardedJedis();
            rtn = shardedJedis.hmset(key, hash);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when hmset cache.", e);
        } finally {
            returnJedis(shardedJedis);
        }
        return rtn;
    }

    @Override
    public void destory() {
        try {
            sentinelClusterImpl.destroyCluster();
            sentinelClusterImpl = null;
        } catch (Exception e) {
            sentinelClusterImpl = null;
        }

    }

    @Override
    public Pool<? extends ShardedJedis> getRedisPool() {
        return sentinelClusterImpl.getShardedJedisPool();
    }

    @Override
    public String set(String key, String value, String nxxx, String expx, long time) {
        ShardedJedis jedis = null;

        String rtn = "";
        try {
            jedis = sentinelClusterImpl.getShardedJedis();

            SetParams setParams = SetParams.setParams(); // 创建默认参数

            // 处理 NX/XX 条件（nxxx可能为null）
            if (nxxx != null) {
                if ("NX".equalsIgnoreCase(nxxx)) {
                    setParams.nx();
                } else if ("XX".equalsIgnoreCase(nxxx)) {
                    setParams.xx();
                }
                // 其他值忽略，保持无条件设置
            }

            // 处理 EX/PX 过期时间（expx可能为null，time需要验证）
            if (expx != null && time > 0) {
                if ("EX".equalsIgnoreCase(expx)) {
                    setParams.ex((int) time);  // 转换为秒
                } else if ("PX".equalsIgnoreCase(expx)) {
                    setParams.px(time);  // 毫秒
                }
                // 其他值忽略，不设置过期时间
            }

            rtn = jedis.set(key, value, setParams);
        } finally {
            returnJedis(jedis);
        }
        return rtn;
    }
    /**
     * {@inheritDoc}
     */
    @Override
    public void setex(String key, int seconds, Object value) {
        byte[] data = protostuffTranscoder.encode(value);
        ShardedJedis jedis = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.setex(SafeEncoder.encode(key), seconds, data);
        } catch (Throwable e) {
            log.warn("error when set cache.", e);
        } finally {
            returnJedis(jedis);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long incr(String key) {
        ShardedJedis jedis = null;
        Long rtn = 0L;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            rtn = jedis.incr(key);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when incr .", e);
        } finally {
            returnJedis(jedis);
        }
        return rtn;
    }

    /** {@inheritDoc} */

    @Override
    public Long expire(String key, int seconds) {
        ShardedJedis jedis = null;
        Long rtn = 0L;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            rtn = jedis.expire(key, seconds);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when expire .", e);
        } finally {
            returnJedis(jedis);
        }
        return rtn;
    }

    public Long incrBy(String key, Long num)
    {
    	ShardedJedis jedis = null;
    	Long result = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            result = jedis.incrBy(key, num);
        } catch (Throwable e) {
        	log.error("RedisService operate error: " + key, e);
        } finally {
            returnJedis(jedis);
        }
        return result;
    }

    @Override
    public Jedis getShard(String key){
        ShardedJedis jedis = null;
        Jedis result = null;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();

            result = jedis.getShard(key);
        } catch (Throwable e) {
            log.error("RedisService operate error: " + key, e);
        } finally {
            returnJedis(jedis);
        }
        return result;
    }
}
