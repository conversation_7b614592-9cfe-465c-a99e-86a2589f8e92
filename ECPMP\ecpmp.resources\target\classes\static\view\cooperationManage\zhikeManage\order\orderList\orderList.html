
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>订单管理</title>
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/searchList.css" rel="stylesheet" />
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="orderListCtrl.js"></script>
</head>
<body ng-app='myApp' class="body-min-width" ng-controller='orderListController' ng-init="init();">
    <div class="cooperation-manage container-fluid ">
        <div ng-if="isSuperManager && enterpriseType == '1'" class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
        </div>
        <div ng-if="isSuperManager && enterpriseType == '2'" class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
        </div>
        <div ng-if="loginRoleType == 'zhike'" class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
        </div>
        <div ng-if="loginRoleType == 'agent'" class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
        </div>
        <!-- 管理员登录查看直客 -->
        <top:menu chose-index="2" 
            page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/orderList" 
            list-index="1" ng-if="isSuperManager && enterpriseType == '1'"></top:menu>
        <top:menu chose-index="2" 
            page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/orderList/orderList" 
            list-index="71" ng-if="isSuperManager && enterpriseType == '2'"></top:menu>
        <div class="cooperation-search">
            <form class="form-horizontal">
                <div class="form-group form-inline">
                    <label for="orderName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap" ng-bind="'ORDER_NAME'|translate"></label>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                            <input autocomplete="off" type="text" class="form-control" id="orderName" placeholder="{{'ORDER_PLACEHODERNAME'|translate}}" ng-model="initSel.orderName">
                    </div>
                    <label for="orderStatus" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap" ng-bind="'ORDER_STATUS'|translate" ></label>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                            <select id="orderStatus" class="form-control" ng-model="initSel.orderStatus" ng-options="x.id as x.name for x in orderStatusChoise" >
                            </select>
                    </div>
                    <label for="businessType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                            <select id="businessType" class="form-control" ng-model="initSel.businessType" ng-options="x.id as x.name for x in businessTypeChoise" >
                                </select>
                    </div>

                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1">
                        <button type="submit" class="btn search-btn" ng-click="queryOrderList()" >
                            <icon class="search-iocn"></icon>
                            <span ng-bind="'COMMON_SEARCH'|translate"></span>
                        </button>
                    </div>
                    
                </div>
            </form>
        </div>
        <div class="add-table">
            <button ng-if="isSuperManager || (loginRoleType != 'agent' && loginRoleType != 'zhike')" 
                type="submit" class="btn add-btn" 
                ng-click="gotoAdd()">
            <icon class="add-iocn"></icon>
            <span ng-bind="'ORDER_ADDORDER'|translate"></span>
        </button>
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'ORDER_MANAGE'|translate"></p>
        </div>
        <div class="coorPeration-table" style="width: fit-content;" >
            <table class="table table-striped table-hover" >
                <thead>
                    <tr>
                        <th style="width:150px;padding-left: 30px!important" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>
                        <th style="width:200px" ng-bind="'ORDER_CODE'|translate"></th>
                        <th style="width:75px" ng-bind="'ORDER_ISEXPERIENCE'|translate" style="width: 100px;"></th>
                        <th style="width:130px" ng-bind="'ORDER_NAME'|translate"></th>
                        <th style="width:76px" ng-bind="'ORDER_tcType'|translate"></th>
                        <th style="width:100px" ng-bind="'ORDER_AMOUNT'|translate" style="width: 140px;"></th>
                        <th style="width:420px"  style="width: 140px;">配额</th>
                        <th style="width: 92px" ng-bind="'COMMON_CREATETIME'|translate"></th>
                        <th style="width:48px" ng-bind="'COMMON_STATUS'|translate"></th>
                        <th style="width:180px" ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in orderListData">
                        <td style="vertical-align: middle;padding-left:30px!important" title="{{enterpriseName}}">{{enterpriseName}}</td>
                        <td style="vertical-align: middle" title="{{item.orderCode}}">{{item.orderCode}}</td>
                        <td style="vertical-align: middle">{{item.isExperience=="1"? ('YES'|translate) : ('NO'|translate)}}</td>
                        <td style="vertical-align: middle" title="{{item.orderName}}">{{item.orderName}}</td>
                        <td style="vertical-align: middle" title="{{item.tcType==1?'包月':'按条计费'}}">{{item.tcType==1?'包月':'按条计费'}}</td>
                        <td style="vertical-align: middle" title="{{item.amount}}">{{item.amount}}</td>
                        <!--新增配额列-->
                        <td >
                                <div ng-repeat="quotauseinfo in (item.operatorQuota)" class="peTable">
                                    <!--1-移动，2-联通，3-电信  -->
                                    <div class="peL fontRd">
                                        <span title="{{quotauseinfo.mobilePlatform}}" ng-if="quotauseinfo.mobilePlatform == 1">移动
                                        </span>
                                        <span title="{{quotauseinfo.mobilePlatform}}" ng-if="quotauseinfo.mobilePlatform == 2">联通
                                        </span>
                                        <span title="{{quotauseinfo.mobilePlatform}}" ng-if="quotauseinfo.mobilePlatform == 3">电信
                                        </span>
                                    </div>
                                    <!-- -->
                                    <div class="peR">
                                        <p>
                                            <span ng-if="quotauseinfo.subServType == 1">主叫屏显
                                            </span>
                                            <span ng-if="quotauseinfo.subServType == 2">被叫屏显
                                            </span>
                                            <span ng-if="quotauseinfo.subServType == 4">挂机短信
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==8 && quotauseinfo.servType != 4">挂机彩信
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==16 && quotauseinfo.servType == 2">挂机增彩
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==3 && quotauseinfo.servType != 4">屏显(主被叫)
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==10">屏显+挂彩
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==16 && quotauseinfo.servType == 4">增彩
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==17">短信
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==3 && quotauseinfo.servType == 4">屏显
                                            </span>
                                            <span ng-if="quotauseinfo.subServType ==8 && quotauseinfo.servType == 4">彩信
                                            </span>
                                            <span ng-if="quotauseinfo.chargeType == 1">(
                                                <span ng-if="quotauseinfo.isLimit == 0">不限量</span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.amount}}">配额:<span class="fontRd">{{quotauseinfo.amount}}</span></span>
                                                <span title="{{quotauseinfo.actualUseAmount}}">已使用:<span class="fontRd">{{quotauseinfo.actualUseAmount}}</span></span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.remainUseAmount}}">剩余:<span class="fontRd">{{quotauseinfo.remainUseAmount}}</span></span>)
                                            </span>
                                            <span ng-if="quotauseinfo.chargeType == 2">(
                                                <span ng-if="quotauseinfo.isLimit == 0">不限量</span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.memberCount}}">配额:<span class="fontRd">{{quotauseinfo.memberCount}}</span></span>
                                                <span title="{{quotauseinfo.actualUseMemberCount}}">已使用:<span class="fontRd">{{quotauseinfo.actualUseMemberCount}}</span></span>
                                                <span ng-if="quotauseinfo.isLimit == 1" title="{{quotauseinfo.remainUseMemberCount}}">剩余:<span class="fontRd">{{quotauseinfo.remainUseMemberCount}}</span></span>)
                                            </span>
                                        </p>
                                    </div>
                                </div>
                        </td>

                        <td style="vertical-align: middle" title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</td>
                        <td style="vertical-align: middle" title="{{statusMap[item.status]}}" ng-class="{'fontGreen':item.status=='1','fontRed':item.status=='2'}">{{statusMap[item.status]}}</td>
                        <td style="vertical-align: middle">
                            <div class="handle">
                                <ul>
                                    <li ng-if = "!isSuperManager" class="query" ng-click="gotoDetail(item)">
                                        <icon class="query-icon"></icon>
                                        <span ng-bind="'COMMON_WATCH'|translate"></span>
                                    </li>
                                    <li ng-if = "isSuperManager" class="query" ng-click="gotoupdate(item)">
                                        <icon class="edit-icon"></icon>
                                        <span ng-bind="'COMMON_UPDATE'|translate"></span>
                                    </li>
                                    <li class="edit" ng-if="isSuperManager || loginRoleType != 'agent'"
                                        ng-click="gotoAddtion(item)">
                                        <icon class="edit-icon"></icon>
                                        <span ng-bind="'ORDER_ADDTIONORDER'|translate"></span>
                                    </li>
                                    <!-- <li class="delete">
                                        <icon class="delete-icon"></icon>删除</li> -->
                                    <!-- <li class="set">
                                        <icon class="set-icon"></icon>设置</li> -->
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="orderListData.length<=0">
                        <td style="text-align:center;vertical-align:middle" colspan="9"  ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    <div>
        <ptl-page tableId="0" change="queryOrderList('justPage')"></ptl-page>
      </div>
    </div>

<!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838'>
                                {{tip|translate}}
                            </p></div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>
    
</body>
<style type="text/css">
    .table th{
        width:12.5%;
        min-width: 100px;
    }
    table{
        table-layout:fixed;
    }
    table td{
        width:100%;
        word-break:keep-all;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
    }
    .form-horizontal .control-label {
			padding-top: 7px;
			margin-bottom: 0;
			text-align: right;
		}
		.container-fluid{
			padding-left: 0;padding-right: 0;
		}
    .modal-footer{
        text-align: center;
    }
    .fontGreen{
        color: rgb(48, 147,25)
    }
    .fontRed{
        color:rgb(252,70,93);
    }
    .cooperation-nav{
        margin-bottom: 15px;
        margin-left: 20px;
    }
    .cooperation-manage{
        min-width: 1024px;
    }
    body .cooperation-manage .form-inline {
        margin: 0 20px;
        background: #fff;
        border-radius: 4px;
        padding: 20px 0px 20px 10px;
        overflow: hidden;
    }
    
    .cooperation-manage .form-group {
        min-width: 255px;
    }
    .form-control:focus {
        border-color: #7360e1;
    }
    .form-group label{
        height: 34px;
        margin-bottom: 0
    }
    .coorPeration-table {
        margin: 0px 20px;
        background: #fff;
        min-width: 850px;
    }
    .cooperation-manage .coorPeration-table th,td{
        padding-left: 10px !important;        
    }
    
    body .handle {
        overflow: hidden;
    }

    .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
        border: none;
        padding: 12px 8px;
    }
    /* @media (max-width: 1280px){
        .form-inline .form-control{
            width: 140px;
        }
        .cooperation-manage .form-group select{
            width: 140px;
        }
    } */
    .form-inline .form-control{
        width: 100%!important;
    }
    /*配额*/
.peTable{
    display: table;
}
.peL{
    display: table-cell;
    padding-right: 10px;
    vertical-align: middle;
}
.peR{
    display: table-cell;
}
.fontRd{
    color: red;
}
.peld{
    vertical-align: middle;
}
</style>
</html>