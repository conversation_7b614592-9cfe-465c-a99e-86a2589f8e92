angular.module('preview', []).directive('ptlPreview', function () {
  return {
    restrict: 'AE',
    templateUrl: '/qycy/ecpmp/directives/preview/preview.html',
    replace: true,
    transclude: true,
    scope: {
      urllist: "=",
      nofiledesc: "@"
    },
    controller: function ($scope, $element, $attrs, $rootScope) {
//      document.onkeydown = function (e) {
//        var ev = (typeof event != 'undefined') ? window.event : e;
//        if (ev.keyCode == 13) {
//          return false;
//        }
//      }

      $scope.nofiledesc = $scope.nofiledesc || "暂无图片，请先上传";
      $scope.picUrlList = $scope.urllist || [];
      $scope.pdf_type_value = false;
      $scope.img_type_value = false;
      $scope.$watch("urllist", function (newValue) {
        $scope.picUrlList = angular.copy(newValue);
        var index, strL, strR;
        for (var i in $scope.picUrlList) {
          index = $scope.picUrlList[i].lastIndexOf("\/");
          strR = $scope.picUrlList[i].substring(index + 1, $scope.picUrlList[i].length);
          strL = $scope.picUrlList[i].substring(0, index + 1);
          // if(strL.indexOf('/sftp/data')!=-1){
          //     $scope.picUrlList[i] = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=' +strL+ encodeURIComponent(strR);
          // }else{
          //     $scope.picUrlList[i] = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=/sftp/data' +strL+ encodeURIComponent(strR);
          // }
          // console.log(strR)
          //   console.log(strL + strR)
          //   console.log(encodeURIComponent(strR))

         // if (encodeURIComponent(strR).substring(0,4) == "http") {
            //     // $scope.picUrlList[i] =encodeURIComponent(strR);
            //
            // }
            // else{
            //
            //   $scope.picUrlList[i] = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=' + strL + encodeURIComponent(strR);
            // }
            if (strL.substring(0,4) == "http") {
                $scope.picUrlList[i] =strL + strR;

            }
            else{

                $scope.picUrlList[i] = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=' + strL + encodeURIComponent(strR);
            }

         // $scope.picUrlList[i] = '/qycy/ecpmp/ecpmpServices/fileService/reviewPic?path=' + strL + encodeURIComponent(strR);
          //测试桩
          //$scope.picUrlList[i] = "/qycy/ecpmp/directives/preview/SSDD.pdf";
          
          //判断格式是否PDF，特殊显示
          var type = strR.substring(strR.lastIndexOf('.')+1)
          if(type == "pdf" || type == "PDF" )
          {
        	  $scope.pdf_type_value = true; 
          }
          else
          {
        	  $scope.img_type_value = true; 
          }
        }
        console.log($scope.picUrlList);
      }, true)
      $scope.showPreview = function () {
        $('#previewModal').modal();
      }

    },
    compile: function (tElement, tAttr, linker) {
    }

  };
});