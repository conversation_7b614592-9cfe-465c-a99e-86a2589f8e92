var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n"])
/**/
app.controller('businessSettingController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    $scope.menuType = 0;
    $scope.accountID = $.cookie('accountID');
    var loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
    $scope.isProvincial = (loginRoleType == 'provincial');
    $scope.isProvincial2 = (loginRoleType == 'provincial');

    $scope.isAgent = (loginRoleType == 'agent');
    $scope.isZhike = (loginRoleType == 'zhike');
    if ($scope.isAgent) {
      $.cookie("enterpriseType", 2, {path: '/'});
    }
    $scope.enterpriseType = $.cookie('enterpriseType');
    $scope.isSecondEnter = $location.$$search.isSecondEnter;//地址栏获取变量，判断是不是二级企业

    var enterpriseTypeParam = $location.$$search.enterpriseType;
    if (!$scope.isProvincial) {
      $scope.isProvincial = enterpriseTypeParam === 'provincial';
    }
    //获取enterpriseID
    $scope.enterpriseID = parseInt($.cookie('enterpriseID')) || '';
    $scope.enterpriseName = $.cookie('enterpriseName') || '';
    $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
    $scope.custIDValidate = true;
    $scope.subEnterpriseID = parseInt($.cookie('subEnterpriseID')) || '';

    $scope.showMpPlatformConfig = $.cookie('showMpPlatformConfig') == "true" || false;
    $scope.showRxPlatformConfig = $.cookie('showRxPlatformConfig') == "true" || false;
    
    if($scope.getQueryVariable('enterpriseID')){
        $scope.enterpriseID = $scope.getQueryVariable('enterpriseID');
    }
      if($scope.getQueryVariable('enterpriseName')){
          $scope.enterpriseName = $scope.getQueryVariable('enterpriseName');
      }
      if(window.localStorage.getItem("menuType")){
          $scope.menuType = window.localStorage.getItem("menuType");
          $(".menu-type-table").removeClass("cur-tabtn-menu");
          $(".menu-type-table").eq($scope.menuType).addClass("cur-tabtn-menu");
          window.localStorage.setItem('menuType', 0);
      }
    $scope.isZYZQ = $.cookie('reserved10') == "111";

    //获取企业名称
    if (true) {
      var req = {
        "id": $scope.enterpriseID,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": 100,
          "isReturnTotal": "1"
        }
      }
      /*查询企业列表*/
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
        data: JSON.stringify(req),
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '1030100000') {
            	console.log(data.enterprise);
              $scope.enterpriseName = data.enterprise.enterpriseName;
              $scope.businessStatus = data.enterprise.businessStatus;
              $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
              console.log($scope.businessStatus);
              if ($scope.businessStatus == 1) {
            	  $('#Modalisaengt').modal();
              }

              $scope.isZYZQ = data.enterprise.reservedsEcpmp.reserved10 == "111"?true:false;
              $scope.isYDY = data.enterprise.reservedsEcpmp.reserved10 == "112"?true:false;
            }
          })
        }
      });
    }
	
	//下拉框
	$scope.periodTypeList = [
	   {
	     id: 0,
	     name: "日"
	   },
	  {
	    id: 1,
	    name: "周"
	  },
        {
            id: 2,
            name: "月"
        }
	];
    
    //初始化代理商开关
    $scope.initparentInfo = {
      mp: {
          screenStatus: "1",              //屏显
          smsstatus: "1",                 //挂短
          mmsstatus: "1"                  //挂彩
      },
      gg: {
        screenStatus: "1"               //屏显
      },
      groupSendStatus: {        //群发业务
        enhancedMMSStatus: "1",              //增彩
        smsstatus: "1",                       //短信
        screenStatus: "1",               //屏显
        mmsstatus: "1"               	//彩信
        },
      hangupTypeStatus: "0",
      hyHangupSmsTypeStatus:"0" // 行业挂机短信
    }
    //记录系统原始开关
    $scope.initsysInfo = {
      mp: {
          screenStatus: "0",              //屏显
          smsstatus: "0",                 //挂短
          mmsstatus: "0"                  //挂彩
      },
      gg: {
         screenStatus: "0",              //屏显
      },
      groupSendStatus: {        //群发业务
          enhancedMMSStatus: "0",              //增彩
          smsstatus: "0",                      //短信
          screenStatus: "0",               //屏显
          mmsstatus: "0"               	//彩信
      }
    }

    //初始化系统默认值
    $scope.initDefaultInfo = {
        mp:{
			defaultPeriodType: 0,
            defaultOneTemplateDayLimit: null,
            defaultOneTemplateDayLimitDesc: "默认值",
            defaultOneDeliveryInterval: null,
            defaultOneDeliveryIntervalDesc: "默认值"
        },
        rx:{
            defaultOneTemplateDayLimit: null,
            defaultOneTemplateDayLimitDesc: "默认值",
            defaultOneDeliveryInterval: null,
            defaultOneDeliveryIntervalDesc: "默认值"
        },
        gg:{
			defaultPeriodType: 0,
            defaultOneTemplateDayLimit: null,
            defaultOneTemplateDayLimitDesc: "默认值",
            defaultOneDeliveryInterval: null,
            defaultOneDeliveryIntervalDesc: "默认值"
        }
    }

    //初始化所有参数
    $scope.initAllInfo = {
      mp: {
          px: {
			periodType: 0,  
            oneTemplateDayLimit: null,
            oneDeliveryInterval: null,
            id: null,
            servRuleCode: null,
            operatorID: null
          },
          gjdx: {
			periodType: 0,  
            oneTemplateDayLimit: null,
            oneDeliveryInterval: null,
            id: null,
            servRuleCode: null,
            operatorID: null
          },
          gjcx: {
			periodType: 0,  
            oneTemplateDayLimit: null,
            oneDeliveryInterval: null,
            id: null,
            servRuleCode: null,
            operatorID: null
          }
      },
      rx: {
        px: {
          oneTemplateDayLimit: null,
          oneDeliveryInterval: null,
          id: null,
          servRuleCode: null,
          operatorID: null
        },
        gjdx: {
          oneTemplateDayLimit: null,
          oneDeliveryInterval: null,
          id: null,
          servRuleCode: null,
          operatorID: null
        },
        gjcx: {
          oneTemplateDayLimit: null,
          oneDeliveryInterval: null,
          id: null,
          servRuleCode: null,
          operatorID: null
        },
        gjzc: {
            oneTemplateDayLimit: null,
            oneDeliveryInterval: null,
            id: null,
            servRuleCode: null,
            operatorID: null
         }
      },
      gg: {
          px: {
			periodType: 0,  
            oneTemplateDayLimit: null,
            oneDeliveryInterval: null,
            id: null,
            servRuleCode: null,
            operatorID: null
          },
          gjcx: {
			periodType: 0,  
            oneTemplateDayLimit: null,
            oneDeliveryInterval: null,
            id: null,
            servRuleCode: null,
            operatorID: null
          }
      },
        TZSms: {
            periodType: 0, //默认0
            oneTemplateDayLimit: 10,
            oneDeliveryInterval: null,
            status: "0",      //默认0
            id: null,
            servRuleCode: null,
            operatorID: null,
            subServType: 0, //默认0
            isUse: 1, //默认1
            servType: 4,
            reserved1: 11
        },
        TZFlash: {
            periodType: 0, //默认0
            oneTemplateDayLimit: 10,
            oneDeliveryInterval: null,
            status: "0",      //默认0
            id: null,
            servRuleCode: null,
            operatorID: null,
            subServType: 0, //默认0
            isUse: 1, //默认1
            servType: 4,
            reserved1: 10
        },
        TZMms: {
            periodType: 0, //默认0
            oneTemplateDayLimit: 10,
            oneDeliveryInterval: null,
            status: "0",      //默认0
            id: null,
            servRuleCode: null,
            operatorID: null,
            subServType: 0, //默认0
            isUse: 1, //默认1
            servType: 4,
            reserved1: 8
        },
        TZUSSD: {
            periodType: 0, //默认0
            oneTemplateDayLimit: 10,
            oneDeliveryInterval: null,
            status: "0",      //默认0
            id: null,
            servRuleCode: null,
            operatorID: null,
            subServType: 0, //默认0
            isUse: 1, //默认1
            servType: 4,
            reserved1: 9
        },
        TZCX: {
            periodType: 0, //默认0
            oneTemplateDayLimit: 10,
            oneDeliveryInterval: null,
            status: "0",      //默认0
            id: null,
            servRuleCode: null,
            operatorID: null,
            subServType: 0, //默认0
            isUse: 1, //默认1
            servType: 4,
            reserved1: 13
        },


      platformStatus: {
        mobileStatus: "1",
        unicomStatus: "0",
        telecomStatus: "0"
      },
      rxPlatformStatus: {
	    mobileStatus: "1",
	    unicomStatus: "1",
	    telecomStatus: "1"
	  },
      cardPrintStatusWrapper: {     //名片
        screenStatus: "0",              //屏显
        smsstatus: "0",                 //挂短
        mmsstatus: "0"                  //挂彩
      },
      adPrintStatus: {              //广告
        screenStatus: "0",              //屏显
      },
      groupSendStatus: {     //群发业务
          enhancedMMSStatus: "0",              //增彩
          smsstatus: "0",                       //短信
          screenStatus: "0",               //屏显
          mmsstatus: "0"               	//彩信
      },
      unSubscribeStatus: "1",
      ywApproveCallback: "0",
      SZFZStatus: "0",
      FZDXStatus: "",
      hangupTypeStatus: "0",
      hyHangupSmsTypeStatus:"0", // 行业挂机短信
      RXSwitchRXStatus:"0",
      RXBeyondSwitchRXStatus:"0",
      RXBeyondSwitchRXSFStatus:"0"




        /*SZFZStatus: {
          FZDXStatus: "0",  //数智反诈短信
          FZSXStatus: "0"   //数智反诈闪信
      }*/
    }


    var req1 = {};
    // 二级企业
    if ($scope.enterpriseType == 3 || $scope.isSecondEnter) {
      req1 = {
        enterpriseID: $scope.enterpriseID           //代理商企业id
      };
      $scope.getFatherConfig(req1);                     //查询父级企业开关
    } else {
      req1 = {
        enterpriseID: $scope.enterpriseID
      };
      $scope.getShowConfig(req1, "one");
    }
    //获取默认值
    $scope.getDefaultServiceRuleList();


      $scope.getIsFrequencyWhite();
    if ($scope.enterpriseType =='5' && $scope.isSuperManager)
    {
        $scope.proSupServerType = $.cookie('proSupServerType');
    }
  };
    $scope.getQueryVariable = function (variable)
    {
        const query = window.location.search.substring(1);
        const vars = query.split("&");
        for (let i = 0; i < vars.length; i++) {
            const pair = vars[i].split("=");
            if (pair[0] == variable) {
                return pair[1];
            }
        }
        return (false);
    };
  $scope.getShowConfig = function (req1, type) {
      req1.enterpriseType =  $scope.enterpriseType;
    /*查询业务设置规则*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryServiceRuleList",
      data: JSON.stringify(req1),
      success: function (ruleData) {
        $rootScope.$apply(function () {
            //导航标签判断
            $scope.serviceControl = ruleData.serviceControl || {};
            $.removeCookie('reserved4', { path: '/'});
            $.cookie('reserved4', $scope.serviceControl.reserved4,{ path: '/'});
            setTimeout(function (){
                $scope.querySyncServiceRule();

            },500)

            var result = ruleData.result;
            if (result.resultCode == '1030100000') {
            if (ruleData.serviceRuleList == null || ruleData.serviceRuleList == []) {
              $scope.operateType = "1";
            }
            else {
              $scope.operateType = "1";
              $scope.serviceRuleListInfo = ruleData.serviceRuleList;
              for (var i in $scope.serviceRuleListInfo) {
                var item = $scope.serviceRuleListInfo[i];
                //名片业务频控
                if(item.servType == 1){
                    if (item.subServType == 3) {
                      $scope.initAllInfo.mp.px.periodType = item.periodType;
                      $scope.initAllInfo.mp.px.oneTemplateDayLimit = item.oneTemplateDayLimit;          //单人单模板日上限
                      $scope.initAllInfo.mp.px.oneDeliveryInterval = item.oneDeliveryInterval;          //单人投递间隔
                      $scope.initAllInfo.mp.px.id = item.id;                                            //规则配置表主键id
                      $scope.initAllInfo.mp.px.idservRuleCode = item.servRuleCode;                      //规则外部编码
                      $scope.initAllInfo.mp.px.operatorID = item.operatorID;                            //操作人ID
                    } else if (item.subServType == 4) {
                      $scope.initAllInfo.mp.gjdx.periodType = item.periodType;
                      $scope.initAllInfo.mp.gjdx.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.mp.gjdx.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.mp.gjdx.id = item.id;
                      $scope.initAllInfo.mp.gjdx.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.mp.gjdx.operatorID = item.operatorID;
                    } else if (item.subServType == 8) {
                      $scope.initAllInfo.mp.gjcx.periodType = item.periodType;
                      $scope.initAllInfo.mp.gjcx.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.mp.gjcx.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.mp.gjcx.id = item.id;
                      $scope.initAllInfo.mp.gjcx.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.mp.gjcx.operatorID = item.operatorID;
                    }
                }
                else if(item.servType == 2){
                    if (item.subServType == 3) {
                      $scope.initAllInfo.rx.px.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.rx.px.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.rx.px.id = item.id;
                      $scope.initAllInfo.rx.px.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.rx.px.operatorID = item.operatorID;
                    } else if (item.subServType == 4) {
                      $scope.initAllInfo.rx.gjdx.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.rx.gjdx.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.rx.gjdx.id = item.id;
                      $scope.initAllInfo.rx.gjdx.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.rx.gjdx.operatorID = item.operatorID;
                    } else if (item.subServType == 8) {
                      $scope.initAllInfo.rx.gjcx.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.rx.gjcx.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.rx.gjcx.id = item.id;
                      $scope.initAllInfo.rx.gjcx.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.rx.gjcx.operatorID = item.operatorID;
                    } else if (item.subServType == 16) {
                      $scope.initAllInfo.rx.gjzc.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.rx.gjzc.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.rx.gjzc.id = item.id;
                      $scope.initAllInfo.rx.gjzc.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.rx.gjzc.operatorID = item.operatorID;
                    }
                }
                else if(item.servType == 3){
                    if (item.subServType == 3) {
                      $scope.initAllInfo.gg.px.periodType = item.periodType;
                      $scope.initAllInfo.gg.px.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.gg.px.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.gg.px.id = item.id;
                      $scope.initAllInfo.gg.px.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.gg.px.operatorID = item.operatorID;
                    } else if (item.subServType == 8) {
                      $scope.initAllInfo.gg.gjcx.periodType = item.periodType;
                      $scope.initAllInfo.gg.gjcx.oneTemplateDayLimit = item.oneTemplateDayLimit;
                      $scope.initAllInfo.gg.gjcx.oneDeliveryInterval = item.oneDeliveryInterval;
                      $scope.initAllInfo.gg.gjcx.id = item.id;
                      $scope.initAllInfo.gg.gjcx.idservRuleCode = item.servRuleCode;
                      $scope.initAllInfo.gg.gjcx.operatorID = item.operatorID;
                    }
                }
                else if (item.servType === 4) {
                      if(item.reservedsEcpmp.reserved1 == 11){
                          $scope.initAllInfo.TZSms.periodType = item.periodType;
                          $scope.initAllInfo.TZSms.oneTemplateDayLimit = item.oneTemplateDayLimit;
                          $scope.initAllInfo.TZSms.oneDeliveryInterval = item.oneDeliveryInterval;
                          $scope.initAllInfo.TZSms.id = item.id;
                      }
                      if(item.reservedsEcpmp.reserved1 == 10){
                          $scope.initAllInfo.TZFlash.periodType = item.periodType;
                          $scope.initAllInfo.TZFlash.oneTemplateDayLimit = item.oneTemplateDayLimit;
                          $scope.initAllInfo.TZFlash.oneDeliveryInterval = item.oneDeliveryInterval;
                          $scope.initAllInfo.TZFlash.id = item.id;
                      }
                      if(item.reservedsEcpmp.reserved1 == 8){
                          $scope.initAllInfo.TZMms.periodType = item.periodType;
                          $scope.initAllInfo.TZMms.oneTemplateDayLimit = item.oneTemplateDayLimit;
                          $scope.initAllInfo.TZMms.oneDeliveryInterval = item.oneDeliveryInterval;
                          $scope.initAllInfo.TZMms.id = item.id;
                      }
                      if(item.reservedsEcpmp.reserved1 == 9){
                          $scope.initAllInfo.TZUSSD.periodType = item.periodType;
                          $scope.initAllInfo.TZUSSD.oneTemplateDayLimit = item.oneTemplateDayLimit;
                          $scope.initAllInfo.TZUSSD.oneDeliveryInterval = item.oneDeliveryInterval;
                          $scope.initAllInfo.TZUSSD.id = item.id;
                      }
                      if(item.reservedsEcpmp.reserved1 == 13){
                          $scope.initAllInfo.TZCX.periodType = item.periodType;
                          $scope.initAllInfo.TZCX.oneTemplateDayLimit = item.oneTemplateDayLimit;
                          $scope.initAllInfo.TZCX.oneDeliveryInterval = item.oneDeliveryInterval;
                          $scope.initAllInfo.TZCX.id = item.id;
                      }
                  }
                console.log("a123a"+$scope.initAllInfo.mp.px.periodType)
              }
            }

            $scope.serviceControlInfo = ruleData.serviceControl;
            if($scope.serviceControlInfo){
                $scope.initAllInfo.platformStatus = ruleData.serviceControl.platformStatus;
                if($scope.initAllInfo.platformStatus){
                   //移动
                  $scope.initAllInfo.platformStatus.mobileStatus = ruleData.serviceControl.platformStatus.mobileStatus;
                  //联通
                  if($scope.parentUnicomStatus == '0'){
                     $scope.initAllInfo.platformStatus.unicomStatus = '0';
                     $scope.switch5Value=0;
                  }else{
                     $scope.initAllInfo.platformStatus.unicomStatus = ruleData.serviceControl.platformStatus.unicomStatus;
                     $scope.switch5Value=4;
                  }
                  //电信
                  if($scope.parentTelecomStatus == '0'){
                     $scope.initAllInfo.platformStatus.telecomStatus = '0';
                     $scope.switch6Value=0;
                  }else{
                     $scope.initAllInfo.platformStatus.telecomStatus = ruleData.serviceControl.platformStatus.telecomStatus;
                     $scope.switch6Value=5;
                  }
                }
                // 热线彩印省份版异网能力开关
                if(ruleData.serviceControl.rxPlatformStatus){
                  $scope.initAllInfo.rxPlatformStatus = ruleData.serviceControl.rxPlatformStatus;
                  //移动
                  $scope.initAllInfo.rxPlatformStatus.mobileStatus = ruleData.serviceControl.rxPlatformStatus.mobileStatus;
                  //联通
                  if($scope.parentUnicomStatus == '0'){
                     $scope.initAllInfo.rxPlatformStatus.unicomStatus = '0';
                     $scope.switch7Value=0;
                  }else{
                     $scope.initAllInfo.rxPlatformStatus.unicomStatus = ruleData.serviceControl.rxPlatformStatus.unicomStatus;
                     $scope.switch7Value=6;
                  }
                  //电信
                  if($scope.parentTelecomStatus == '0'){
                     $scope.initAllInfo.rxPlatformStatus.telecomStatus = '0';
                     $scope.switch8Value=0;
                  }else{
                     $scope.initAllInfo.rxPlatformStatus.telecomStatus = ruleData.serviceControl.rxPlatformStatus.telecomStatus;
                     $scope.switch8Value=7;
                  }
                }
                  //名片开关
                $scope.initAllInfo.cardPrintStatusWrapper = ruleData.serviceControl.cardPrintStatusWrapper;
                if($scope.initAllInfo.cardPrintStatusWrapper){
                    $scope.initsysInfo.mp.screenStatus = ruleData.serviceControl.cardPrintStatusWrapper.screenStatus;    //名片屏显
                    $scope.initsysInfo.mp.smsstatus = ruleData.serviceControl.cardPrintStatusWrapper.smsstatus;          //名片挂短
                    $scope.initsysInfo.mp.mmsstatus = ruleData.serviceControl.cardPrintStatusWrapper.mmsstatus;          //名片挂彩
                      //屏显
                      if($scope.initparentInfo.mp.screenStatus == '0'){
                         $scope.initAllInfo.cardPrintStatusWrapper.screenStatus = '0';
                         $scope.switch11Value=0;
                      }else{
                         $scope.initAllInfo.cardPrintStatusWrapper.screenStatus = ruleData.serviceControl.cardPrintStatusWrapper.screenStatus;
                         $scope.switch11Value=11;
                      }
                      //挂短
                      if($scope.initparentInfo.mp.smsstatus == '0'){
                         $scope.initAllInfo.cardPrintStatusWrapper.smsstatus = '0';
                         $scope.switch12Value=0;
                      }else{
                         $scope.initAllInfo.cardPrintStatusWrapper.smsstatus = ruleData.serviceControl.cardPrintStatusWrapper.sMSStatus;
                         $scope.switch12Value=12;
                      }
                      //挂彩
                      if($scope.initparentInfo.mp.mmsstatus == '0'){
                         $scope.initAllInfo.cardPrintStatusWrapper.mmsstatus = '0';
                         $scope.switch13Value=0;
                      }else{
                         $scope.initAllInfo.cardPrintStatusWrapper.mmsstatus = ruleData.serviceControl.cardPrintStatusWrapper.mMSStatus;
                         $scope.switch13Value=13;
                      }
                }
                //广告开关
                $scope.initAllInfo.adPrintStatus = ruleData.serviceControl.adPrintStatus;
                if($scope.initAllInfo.adPrintStatus){
                    $scope.initsysInfo.gg.screenStatus = ruleData.serviceControl.adPrintStatus.screenStatus;             //广告屏显
                     //屏显
                     if($scope.initparentInfo.gg.screenStatus == '0'){
                        $scope.initAllInfo.adPrintStatus.screenStatus = '0';
                        $scope.switch31Value=0;
                     }else{
                        $scope.initAllInfo.adPrintStatus.screenStatus = ruleData.serviceControl.adPrintStatus.screenStatus;
                        $scope.switch31Value=31;
                     }
                }
              //群发业务
              $scope.initAllInfo.groupSendStatus = ruleData.serviceControl.groupSendStatus;
              if($scope.initAllInfo.groupSendStatus){
                $scope.initsysInfo.groupSendStatus.enhancedMMSStatus = ruleData.serviceControl.groupSendStatus.enhancedMMSStatus;             //群发增彩
                $scope.initsysInfo.groupSendStatus.smsstatus = ruleData.serviceControl.groupSendStatus.sMSStatus;             //群发短信
                $scope.initsysInfo.groupSendStatus.screenStatus = ruleData.serviceControl.groupSendStatus.screenStatus;             //群发短信
                $scope.initsysInfo.groupSendStatus.mmsstatus = ruleData.serviceControl.groupSendStatus.mMSStatus;             //群发短信
                 //增彩
                 if($scope.initparentInfo.groupSendStatus.enhancedMMSStatus == '0'){
                    $scope.initAllInfo.groupSendStatus.enhancedMMSStatus = '0';
                    $scope.switch81Value=0;
                 }else{
                    $scope.initAllInfo.groupSendStatus.enhancedMMSStatus = ruleData.serviceControl.groupSendStatus.enhancedMMSStatus;
                    $scope.switch81Value=81;
                 }
                 //短信
	              if($scope.initparentInfo.groupSendStatus.smsstatus == '0'){
	                 $scope.initAllInfo.groupSendStatus.smsstatus = '0';
	                 $scope.switch82Value=0;
	              }else{
	                 $scope.initAllInfo.groupSendStatus.smsstatus = ruleData.serviceControl.groupSendStatus.sMSStatus;
	                 $scope.switch82Value=82;
	              }
	              //屏显
	              if($scope.initparentInfo.groupSendStatus.screenStatus == '0'){
	            	  $scope.initAllInfo.groupSendStatus.screenStatus = '0';
	            	  $scope.switch83Value=0;
	              }else{
	            	  $scope.initAllInfo.groupSendStatus.screenStatus = ruleData.serviceControl.groupSendStatus.screenStatus;
	            	  $scope.switch83Value=83;
	              }
	              //彩信
	              if($scope.initparentInfo.groupSendStatus.mmsstatus == '0'){
	            	  $scope.initAllInfo.groupSendStatus.mmsstatus = '0';
	            	  $scope.switch84Value=0;
	              }else{
	            	  $scope.initAllInfo.groupSendStatus.mmsstatus = ruleData.serviceControl.groupSendStatus.mMSStatus;
	            	  $scope.switch84Value=84;
	              }

            }
              $scope.initAllInfo.unSubscribeStatus = ruleData.serviceControl.unSubscribeStatus;
              if (ruleData.serviceControl.ywApproveCallback)
              {
                $scope.initAllInfo.ywApproveCallback = ruleData.serviceControl.ywApproveCallback;
              }
            //数智反诈业务
             $scope.initAllInfo.SZFZStatus = ruleData.serviceControl.reservedsEcpmp.reserved3;
             if($scope.initAllInfo.SZFZStatus){
                var reserved3=$scope.initAllInfo.SZFZStatus.split(",");
                if(reserved3&&reserved3.length>1){
                    for (var i = 0; i <reserved3.length ; i++) {
                        if(reserved3[i]=='1'){
                            $scope.initAllInfo.FZSXStatus='1';
                        }
                        if(reserved3[i]=='2'){
                            $scope.initAllInfo.FZDXStatus='2';
                        }
                    }
                }
                else{
                    if(reserved3[0] =='1'){
                        $scope.initAllInfo.FZSXStatus='1';
                    }
                    if(reserved3[0] =='2'){
                        $scope.initAllInfo.FZDXStatus='2';
                    }
                }
            }
            //主叫挂机短信业务配置,新增行业挂机短信
//            if(ruleData.serviceControl.reservedsEcpmp.reserved5 && ruleData.serviceControl.reservedsEcpmp.reserved5 == "1,2")
//            {
//            	$scope.initAllInfo.hangupTypeStatus = '1';
//            } else {
//            	$scope.initAllInfo.hangupTypeStatus = '0';
//            }
            if(ruleData.serviceControl.reservedsEcpmp.reserved5){
                // 主叫挂机短信是否开启
                var zjFlag = ruleData.serviceControl.reservedsEcpmp.reserved5.indexOf("1")!=-1;
                // 行业挂机短信是否开启
                var hyFlag = ruleData.serviceControl.reservedsEcpmp.reserved5.indexOf("3")!=-1;
                $scope.initAllInfo.hangupTypeStatus = zjFlag ? '1':'0';
                $scope.initAllInfo.hyHangupSmsTypeStatus = hyFlag ? '1':'0';

            }
            if($scope.enterpriseType == 3 || $scope.isSecondEnter){
            	if($scope.initparentInfo.hangupTypeStatus == '1') {
            		$scope.hangupTypeStatus = $scope.initAllInfo.hangupTypeStatus;
            	} else {
            		$scope.hangupTypeStatus = '-1';
            	}
            	// 新增行业挂机短信
                if($scope.initparentInfo.hyHangupSmsTypeStatus == '1') {
                    $scope.hyHangupSmsTypeStatus = $scope.initAllInfo.hyHangupSmsTypeStatus;
                } else {
                    $scope.hyHangupSmsTypeStatus = '-1';
                }
            } else {
            	$scope.hangupTypeStatus = $scope.initAllInfo.hangupTypeStatus;
            	// 新增行业挂机短信
            	$scope.hyHangupSmsTypeStatus = $scope.initAllInfo.hyHangupSmsTypeStatus;
            }
            $scope.hangupTypeStatusSwitch = ($scope.hangupTypeStatus == '0' || $scope.hangupTypeStatus == '-1') ? true:false;
            // 新增行业挂机短信
            $scope.hyHangupSmsTypeStatusSwitch = ($scope.hyHangupSmsTypeStatus  == '0' || $scope.hyHangupSmsTypeStatus == '-1') ? true:false;
            //中移政企热线开关
            $scope.initAllInfo.RXSwitch = ruleData.serviceControl.reserved4;
            $scope.initAllInfo.RXBeyondSwitch = ruleData.serviceControl.reserved6;

            $scope.initAllInfo.RXSwitchRXStatus = '0';
            $scope.initAllInfo.RXSwitchRXSFStatus = '0';
            $scope.initAllInfo.RXBeyondSwitchRXStatus = '0';
            $scope.initAllInfo.RXBeyondSwitchRXSFStatus = '0';
            if($scope.initAllInfo.RXSwitch){
                var reserved4=$scope.initAllInfo.RXSwitch.split(",");
                for (let i = 0; i <reserved4.length ; i++) {
                    if(reserved4[i] ==='2'){
                        $scope.initAllInfo.RXSwitchRXStatus='1';
                    }
                    if(reserved4[i] ==='5'){
                        $scope.initAllInfo.RXSwitchRXSFStatus='1';
                    }
                }
            }

            if ($scope.initAllInfo.RXBeyondSwitch) {
                var reserved6 = $scope.initAllInfo.RXBeyondSwitch.split("");
                for (let i = 0; i <reserved6.length ; i++) {
                    if(reserved6[i] ==='1' && i === 0 ){
                        $scope.initAllInfo.RXBeyondSwitchRXStatus='1';
                    }
                    if(reserved6[i] ==='1'&& i === 1 ){
                        $scope.initAllInfo.RXBeyondSwitchRXSFStatus='1';
                    }
                }
            }

      	  	$scope.switch10Value=10;
      	  	$scope.switch14Value=14;

            }
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }
    $scope.changeDXStatus=function (data) {
      if($scope.initAllInfo.FZDXStatus==null ||$scope.initAllInfo.FZDXStatus==''){
          $scope.initAllInfo.FZDXStatus='2';
      }else{
          $scope.initAllInfo.FZDXStatus='';
      }

    }
    $scope.changeSXStatus=function (data) {
        if($scope.initAllInfo.FZSXStatus==null || $scope.initAllInfo.FZSXStatus==''){
            $scope.initAllInfo.FZSXStatus='1';
        }else{
            $scope.initAllInfo.FZSXStatus='';
        }
    }
  $scope.getFatherConfig = function (req1) {
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryServiceRuleList",
      data: JSON.stringify(req1),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '1030100000') {
            //父企业开关关闭
            if(data.serviceControl){
                if(data.serviceControl.platformStatus){
                    if (data.serviceControl.platformStatus.unicomStatus == '0') {               //联通
                        $scope.parentUnicomStatus = '0';
                    }
                    if (data.serviceControl.platformStatus.telecomStatus == '0') {              //电信
                        $scope.parentTelecomStatus = '0';
                    }
                }
                //名片开关
                if(data.serviceControl.cardPrintStatusWrapper){
                    if(data.serviceControl.cardPrintStatusWrapper.screenStatus == '0'){     //屏显
                        $scope.initparentInfo.mp.screenStatus = '0';
                    }
                    if(data.serviceControl.cardPrintStatusWrapper.smsstatus == '0'){        //挂短
                        $scope.initparentInfo.mp.smsstatus = '0';
                    }
                    if(data.serviceControl.cardPrintStatusWrapper.mmsstatus == '0'){        //挂彩
                        $scope.initparentInfo.mp.mmsstatus = '0';
                    }
                }
                //广告开关
                if(data.serviceControl.adPrintStatus){
                    if(data.serviceControl.adPrintStatus.screenStatus == '0'){     //屏显
                        $scope.initparentInfo.gg.screenStatus = '0';
                    }
                }
                //群发业务开关
                if(data.serviceControl.groupSendStatus){
                    if(data.serviceControl.groupSendStatus.enhancedMMSStatus == '0'){     //增彩
                        $scope.initparentInfo.groupSendStatus.enhancedMMSStatus = '0';
                    }
                    if(data.serviceControl.groupSendStatus.smsstatus == '0'){     //短信
                        $scope.initparentInfo.groupSendStatus.smsstatus = '0';
                    }
                    if(data.serviceControl.groupSendStatus.screenStatus == '0'){     //屏显
                    	$scope.initparentInfo.groupSendStatus.screenStatus = '0';
                    }
                    if(data.serviceControl.groupSendStatus.mmsstatus == '0'){     //彩信
                    	$scope.initparentInfo.groupSendStatus.mmsstatus = '0';
                    }
                }
                //主叫挂机短信业务配置,新增行业挂机短信
//                if(data.serviceControl.reservedsEcpmp.reserved5 && data.serviceControl.reservedsEcpmp.reserved5 == "1,2")
//                {
//                	$scope.initparentInfo.hangupTypeStatus = '1';
//                } else {
//                	$scope.initparentInfo.hangupTypeStatus = '0';
//                }
                if(data.serviceControl.reservedsEcpmp.reserved5){
                    // 主叫挂机短信是否开启
                    var zjFlag = data.serviceControl.reservedsEcpmp.reserved5.indexOf("1") != -1;
                    // 行业挂机短信是否开启
                    var hyFlag = data.serviceControl.reservedsEcpmp.reserved5.indexOf("3") != -1;
                    $scope.initparentInfo.hangupTypeStatus = zjFlag;
                    $scope.initparentInfo.hyHangupSmsTypeStatus = hyFlag;
                }
                if($scope.enterpriseType == 2){
                	$scope.hangupTypeStatus = $scope.initparentInfo.hangupTypeStatus;
                	$scope.hangupTypeStatusSwitch = ($scope.hangupTypeStatus == '0' || $scope.hangupTypeStatus == '-1') ? true:false;
                	// 新增行业挂机短信
                    $scope.hyHangupSmsTypeStatusSwitch = ($scope.hyHangupSmsTypeStatus == '0' || $scope.hyHangupSmsTypeStatus == '-1') ? true:false;
                }

            }
              var req = {
                enterpriseID: $scope.subEnterpriseID
              }
              $scope.getShowConfig(req, "second");
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }

    //获取默认值
    $scope.getDefaultServiceRuleList = function () {
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/defaultFrequencyService/queryDefaultServiceRuleList",
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '1030100000') {
                $scope.serviceRuleList = data.serviceRuleList;
                if(!($scope.serviceRuleList == null || $scope.serviceRuleList == [])){
                    for (var i in $scope.serviceRuleList) {
                        var ruleItem = $scope.serviceRuleList[i];
                        if(ruleItem.servType == 1){
							$scope.initDefaultInfo.mp.defaultPeriodType = ruleItem.periodType;
                            $scope.initDefaultInfo.mp.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;
                            if(!(ruleItem.oneTemplateDayLimit == null || ruleItem.oneTemplateDayLimit == '')){
                                $scope.initDefaultInfo.mp.defaultOneTemplateDayLimitDesc = ruleItem.oneTemplateDayLimit;
                            }
                            $scope.initDefaultInfo.mp.defaultOneDeliveryInterval = ruleItem.oneDeliveryInterval;
                            if(!(ruleItem.oneDeliveryInterval == null || ruleItem.oneDeliveryInterval == '')){
                                $scope.initDefaultInfo.mp.defaultOneDeliveryIntervalDesc = ruleItem.oneDeliveryInterval;
                            }
                        }else if(ruleItem.servType == 2){
                            $scope.initDefaultInfo.rx.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;
                            if(!(ruleItem.oneTemplateDayLimit == null || ruleItem.oneTemplateDayLimit == '')){
                                $scope.initDefaultInfo.rx.defaultOneTemplateDayLimitDesc = ruleItem.oneTemplateDayLimit;

                            }
                            $scope.initDefaultInfo.rx.defaultOneDeliveryInterval = ruleItem.oneDeliveryInterval;
                            if(!(ruleItem.oneDeliveryInterval == null || ruleItem.oneDeliveryInterval == '')){
                                $scope.initDefaultInfo.rx.defaultOneDeliveryIntervalDesc =  ruleItem.oneDeliveryInterval;

                            }

                        }else if(ruleItem.servType == 3){
							$scope.initDefaultInfo.gg.defaultPeriodType = ruleItem.periodType;
                            $scope.initDefaultInfo.gg.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;
                            if(!(ruleItem.oneTemplateDayLimit == null || ruleItem.oneTemplateDayLimit == '')){
                               $scope.initDefaultInfo.gg.defaultOneTemplateDayLimitDesc = ruleItem.oneTemplateDayLimit;

                            }
                            $scope.initDefaultInfo.gg.defaultOneDeliveryInterval = ruleItem.oneDeliveryInterval;
                            if(!(ruleItem.oneDeliveryInterval == null || ruleItem.oneDeliveryInterval == '')){
                               $scope.initDefaultInfo.gg.defaultOneDeliveryIntervalDesc = ruleItem.oneDeliveryInterval;

                            }
                        }else if (ruleItem.servType === 4) {
                                $scope.initAllInfo.TZSms.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;
                                $scope.initAllInfo.TZFlash.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;
                                $scope.initAllInfo.TZMms.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;
                                $scope.initAllInfo.TZUSSD.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;
                                $scope.initAllInfo.TZCX.defaultOneTemplateDayLimit = ruleItem.oneTemplateDayLimit;

                        }
                    }
                }

            } else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
            $scope.tip = '1030120500';
            $('#myModal').modal();
          })
        }
      });
    }
    $scope.limitError = {}

  //判断输入值是否大于默认值：字符串、默认值、输入值、周期类型(填了才会校验)
  $scope.moreThen = function (arg1, arg2, arg3,arg4) {
    var reg1=/^[1-9][0-9]{1,8}$/;
    var reg2=/^[1-9]$/;
    if(arg1 === "mp_px_limit"){                 //名片屏显、上限
        $scope.mp_px_limit_error = false;
    }else if(arg1 === "mp_gjdx_limit"){         //名片挂短、上限
        $scope.mp_gjdx_limit_error = false;
    }else if(arg1 === "mp_gjcx_limit"){         //名片挂彩、上限
        $scope.mp_gjcx_limit_error = false;
    }else if(arg1 === "rx_px_limit"){           //热线屏显、上限
        $scope.rx_px_limit_error = false;
    }else if(arg1 === "rx_gjdx_limit"){         //热线挂短、上限
        $scope.rx_gjdx_limit_error = false;
    }else if(arg1 === "rx_gjcx_limit"){         //热线挂彩、上限
        $scope.rx_gjcx_limit_error = false;
    }else if(arg1 === "rx_gjzc_limit"){         //热线增彩、上限
        $scope.rx_gjzc_limit_error = false;
    }else if(arg1 === "gg_px_limit"){           //广告屏显、上限
        $scope.gg_px_limit_error = false;
    }else if(arg1 === "gg_gjcx_limit"){         //广告挂彩、上限
        $scope.gg_gjcx_limit_error = false;
    }
    $scope.limitError[arg1] = false;
    if (!(arg3 == null || arg3 == '')){
        //传了周期类型
        if(arg4!=null){
            //设置的周期类型和系统保存的周期类型不一致 使用默认值
            if(arg1.indexOf('mp_')>=0&&arg4!==$scope.initDefaultInfo.mp.defaultPeriodType){
                arg2 = arg4 === 0?50:2000
            }
            if(arg1.indexOf('gg_')>=0&&arg4!==$scope.initDefaultInfo.gg.defaultPeriodType){
                arg2 = arg4 === 0?50:2000
            }
        }
        if((!(arg2 == null || arg2 == '') && Number(arg2) < Number(arg3)) || !(reg1.test(arg3) || reg2.test(arg3))){
            if(arg1 === "mp_px_limit" && !$scope.isFrequencyWhiteCard){                 //名片屏显、上限
                $scope.mp_px_limit_error = true;
            }else if(arg1 === "mp_gjdx_limit" && !$scope.isFrequencyWhiteCard){         //名片挂短、上限
                $scope.mp_gjdx_limit_error = true;
            }else if(arg1 === "mp_gjcx_limit" && !$scope.isFrequencyWhiteCard){         //名片挂彩、上限
                $scope.mp_gjcx_limit_error = true;
            }else if(arg1 === "rx_px_limit"&&!$scope.isFrequencyWhiteHotline){           //热线屏显、上限
                $scope.rx_px_limit_error = true;
            }else if(arg1 === "rx_gjdx_limit"&&!$scope.isFrequencyWhiteHotline){         //热线挂短、上限
                $scope.rx_gjdx_limit_error = true;
            }else if(arg1 === "rx_gjcx_limit"&&!$scope.isFrequencyWhiteHotline){         //热线挂彩、上限
                $scope.rx_gjcx_limit_error = true;
            }else if(arg1 === "rx_gjzc_limit"&&!$scope.isFrequencyWhiteHotline){         //热线增彩、上限
                $scope.rx_gjzc_limit_error = false;
            }else if(arg1 === "gg_px_limit"){           //广告屏显、上限
                $scope.gg_px_limit_error = true;
            }else if(arg1 === "gg_gjcx_limit"){         //广告挂彩、上限
                $scope.gg_gjcx_limit_error = true;
            }
            $scope.limitError[arg1] = true;
        }
    }
  }
    //判断输入值是否小于默认值：字符串、默认值、输入值
    $scope.lessThen = function (arg4, arg5, arg6) {
      var reg3=/^[1-9][0-9]{1,8}$/;
      var reg4=/^[1-9]$/;
      if(arg4 === "mp_px_interval"){        //名片屏显、间隔
          $scope.mp_px_interval_error = false;
      }else if(arg4 === "mp_gjdx_interval"){      //名片挂短、间隔
          $scope.mp_gjdx_interval_error = false;
      }else if(arg4 === "mp_gjcx_interval"){      //名片挂彩、间隔
          $scope.mp_gjcx_interval_error = false;
      }else if(arg4 === "rx_px_interval"){        //热线屏显、间隔
          $scope.rx_px_interval_error = false;
      }else if(arg4 === "rx_gjdx_interval"){      //热线挂短、间隔
          $scope.rx_gjdx_interval_error = false;
      }else if(arg4 === "rx_gjcx_interval"){      //热线挂彩、间隔
          $scope.rx_gjcx_interval_error = false;
      }else if(arg4 === "rx_gjzc_interval"){      //热线增彩、间隔
          $scope.rx_gjzc_interval_error = false;
      }else if(arg4 === "gg_px_interval"){        //广告屏显、间隔
          $scope.gg_px_interval_error = false;
      }else if(arg4 === "gg_gjcx_interval"){      //广告挂彩、间隔
          $scope.gg_gjcx_interval_error = false;
      }
      if (!(arg6 == null || arg6 == '')){
          if((!(arg5 == null || arg5 == '') && Number(arg5) > Number(arg6)) || !(reg3.test(arg6) || reg4.test(arg6))){
              if(arg4 === "mp_px_interval" && !$scope.isFrequencyWhiteCard){        //名片屏显、间隔
                  $scope.mp_px_interval_error = true;
              }else if(arg4 === "mp_gjdx_interval" && !$scope.isFrequencyWhiteCard){      //名片挂短、间隔
                  $scope.mp_gjdx_interval_error = true;
              }else if(arg4 === "mp_gjcx_interval" && !$scope.isFrequencyWhiteCard){      //名片挂彩、间隔
                  $scope.mp_gjcx_interval_error = true;
              }else if(arg4 === "rx_px_interval"&&!$scope.isFrequencyWhiteHotline){        //热线屏显、间隔
                  $scope.rx_px_interval_error = true;
              }else if(arg4 === "rx_gjdx_interval"&&!$scope.isFrequencyWhiteHotline){      //热线挂短、间隔
                  $scope.rx_gjdx_interval_error = true;
              }else if(arg4 === "rx_gjcx_interval"&&!$scope.isFrequencyWhiteHotline){      //热线挂彩、间隔
                  $scope.rx_gjcx_interval_error = true;
              }else if(arg4 === "rx_gjzc_interval"&&!$scope.isFrequencyWhiteHotline){      //热线增彩、间隔
                  $scope.rx_gjzc_interval_error = false;
              }else if(arg4 === "gg_px_interval"){        //广告屏显、间隔
                  $scope.gg_px_interval_error = true;
              }else if(arg4 === "gg_gjcx_interval"){      //广告挂彩、间隔
                  $scope.gg_gjcx_interval_error = true;
              }
          }
      }
    }
  $scope.refresh = function () {
    if ($scope.tip == '1030100000') {
      setTimeout(function () {
          window.localStorage.setItem('menuType',  $scope.menuType);
        location.reload();
      }, 500)
    }
  }
  $scope.changeStatus = function (index) {
    if (index == 11 && $scope.switch11Value == 11) {           //名片屏显
      $scope.initAllInfo.cardPrintStatusWrapper.screenStatus = $scope.initAllInfo.cardPrintStatusWrapper.screenStatus == '1' ? '0' : '1';
    } else if (index == 12 && $scope.switch12Value == 12) {    //名片挂短
      $scope.initAllInfo.cardPrintStatusWrapper.smsstatus = $scope.initAllInfo.cardPrintStatusWrapper.smsstatus == '1' ? '0' : '1';
    } else if (index == 13 && $scope.switch13Value == 13) {    //名片挂彩
      $scope.initAllInfo.cardPrintStatusWrapper.mmsstatus = $scope.initAllInfo.cardPrintStatusWrapper.mmsstatus == '1' ? '0' : '1';
    } else if (index == 31 && $scope.switch31Value == 31) {    //广告屏显
      $scope.initAllInfo.adPrintStatus.screenStatus = $scope.initAllInfo.adPrintStatus.screenStatus == '1' ? '0' : '1';
    } else if (index == 81 && $scope.switch81Value == 81) {    //群发增彩
    	console.log($scope.initAllInfo.groupSendStatus.enhancedMMSStatus)
      $scope.initAllInfo.groupSendStatus.enhancedMMSStatus = $scope.initAllInfo.groupSendStatus.enhancedMMSStatus == '1' ? '0' : '1';
    } else if (index == 82 && $scope.switch82Value == 82) {    //群发短信
      $scope.initAllInfo.groupSendStatus.smsstatus = $scope.initAllInfo.groupSendStatus.smsstatus == '1' ? '0' : '1';
    } else if (index == 83 && $scope.switch83Value == 83) {    //群发屏显
    	$scope.initAllInfo.groupSendStatus.screenStatus = $scope.initAllInfo.groupSendStatus.screenStatus == '1' ? '0' : '1';
    } else if (index == 84 && $scope.switch84Value == 84) {    //群发屏显
    	$scope.initAllInfo.groupSendStatus.mmsstatus = $scope.initAllInfo.groupSendStatus.mmsstatus == '1' ? '0' : '1';
    } else if (index ==4 && $scope.switch5Value == 4) {     //异网投递-联通
      $scope.initAllInfo.platformStatus.unicomStatus = $scope.initAllInfo.platformStatus.unicomStatus == '1' ? '0' : '1';
    } else if (index ==5 && $scope.switch6Value == 5) {     //异网投递-电信
      $scope.initAllInfo.platformStatus.telecomStatus = $scope.initAllInfo.platformStatus.telecomStatus == '1' ? '0' : '1';
    } else if (index ==10 && $scope.switch10Value == 10) {     //异网投递-电信
      $scope.initAllInfo.unSubscribeStatus = $scope.initAllInfo.unSubscribeStatus == '1' ? '0' : '1';
    } else if (index ==6 && $scope.switch7Value == 6) {     //异网投递-联通
      $scope.initAllInfo.rxPlatformStatus.unicomStatus = $scope.initAllInfo.rxPlatformStatus.unicomStatus == '1' ? '0' : '1';
    } else if (index ==7 && $scope.switch8Value == 7) {     //异网投递-电信
      $scope.initAllInfo.rxPlatformStatus.telecomStatus = $scope.initAllInfo.rxPlatformStatus.telecomStatus == '1' ? '0' : '1';
    } else if (index ==14 && $scope.switch14Value == 14) {     //异网投递-电信 结果审核回调
      $scope.initAllInfo.ywApproveCallback = $scope.initAllInfo.ywApproveCallback == '1' ? '0' : '1';
    } else if (index == 49){  //数智反诈-短信
      $scope.initAllInfo.FZDXStatus = $scope.initAllInfo.FZDXStatus == '1' ? '0' : '1';
    } else if (index == 50){  //数智反诈-闪信
      $scope.initAllInfo.FZSXStatus = $scope.initAllInfo.FZSXStatus == '2' ? '0' : '2';
    } else if (index == 51){  //主叫挂机短信业务配置
    	if ($scope.enterpriseType == 3) {
        	if($scope.initparentInfo.hangupTypeStatus == '1') {
        		$scope.hangupTypeStatus = $scope.hangupTypeStatus == '1' ? '0' : '1';
        	} else {
        		$scope.hangupTypeStatus = '-1';
        	}
        } else {
        	$scope.hangupTypeStatus = $scope.hangupTypeStatus == '1' ? '0' : '1';
        }
    	$scope.hangupTypeStatusSwitch = ($scope.hangupTypeStatus == '0' || $scope.hangupTypeStatus == '-1') ? true:false;
    }else if (index == 15){  //中移政企-热线
        $scope.initAllInfo.RXSwitchRXStatus = $scope.initAllInfo.RXSwitchRXStatus == '1' ? '0' : '1';
    }else if (index == 16){  //中移政企-热线省份超套
        $scope.initAllInfo.RXBeyondSwitchRXSFStatus = $scope.initAllInfo.RXBeyondSwitchRXSFStatus == '1' ? '0' : '1';
    }else if (index == 17){  //中移政企-热线超套
        $scope.initAllInfo.RXBeyondSwitchRXStatus = $scope.initAllInfo.RXBeyondSwitchRXStatus == '1' ? '0' : '1';
    }else if (index == 52) { // 行业挂机短信
        if ($scope.enterpriseType == 3) { // 子企业
            if($scope.initparentInfo.hyHangupSmsTypeStatus == '1') {
                $scope.hyHangupSmsTypeStatus = $scope.hyHangupSmsTypeStatus == '1' ? '0' : '1';
            } else {
                $scope.hyHangupSmsTypeStatus = '-1';
            }
        } else {
            $scope.hyHangupSmsTypeStatus = $scope.hyHangupSmsTypeStatus == '1' ? '0' : '1';
        }
        $scope.hyHangupSmsTypeStatusSwitch = ($scope.hyHangupSmsTypeStatus == '0' || $scope.hyHangupSmsTypeStatus == '-1') ? true:false;
    }

  }
$scope.UpdateRuleListChecked = false;
    //新增或修改业务规则
$scope.UpdateRuleList = function () {

    if($scope.mp_px_limit_error
    || $scope.mp_px_interval_error
    || $scope.mp_gjdx_limit_error
    || $scope.mp_gjdx_interval_error
    || $scope.mp_gjcx_limit_error
    || $scope.mp_gjcx_interval_error
    || $scope.rx_px_limit_error
    || $scope.rx_px_interval_error
    || $scope.rx_gjdx_limit_error
    || $scope.rx_gjdx_interval_error
    || $scope.rx_gjcx_limit_error
    || $scope.rx_gjcx_interval_error
    || $scope.rx_gjzc_limit_error
    || $scope.rx_gjzc_interval_error
    || $scope.gg_px_limit_error
    || $scope.gg_px_interval_error
    || $scope.gg_gjcx_limit_error
    || $scope.gg_gjcx_interval_error){

        $scope.tip = "不能超过系统默认频控值";
        $('#myModal').modal();
        return;
    }

      if ($scope.initAllInfo.SZFZStatus != '' && $scope.initAllInfo.SZFZStatus != null && $scope.initAllInfo.FZSXStatus == '' && $scope.initAllInfo.FZDXStatus == '') {
          $scope.tip = "数智反诈通知方式必选其一";
          $('#myModal').modal();
          return;
      }



    var req = {
    "serviceRuleList": [],
    "serviceControl": {},
    "operateType": $scope.operateType
    };
    //名片
    if (!$scope.initAllInfo.mp.px.oneTemplateDayLimit) {
    $scope.initAllInfo.mp.px.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.mp.px.oneDeliveryInterval) {
    $scope.initAllInfo.mp.px.oneDeliveryInterval = null;
    }
    if (!$scope.initAllInfo.mp.gjdx.oneTemplateDayLimit) {
    $scope.initAllInfo.mp.gjdx.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.mp.gjdx.oneDeliveryInterval) {
    $scope.initAllInfo.mp.gjdx.oneDeliveryInterval = null;
    }
    if (!$scope.initAllInfo.mp.gjcx.oneTemplateDayLimit) {
    $scope.initAllInfo.mp.gjcx.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.mp.gjcx.oneDeliveryInterval) {
    $scope.initAllInfo.mp.gjcx.oneDeliveryInterval = null;
    }
    //热线
    if (!$scope.initAllInfo.rx.px.oneTemplateDayLimit) {
    $scope.initAllInfo.rx.px.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.rx.px.oneDeliveryInterval) {
    $scope.initAllInfo.rx.px.oneDeliveryInterval = null;
    }
    if (!$scope.initAllInfo.rx.gjdx.oneTemplateDayLimit) {
    $scope.initAllInfo.rx.gjdx.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.rx.gjdx.oneDeliveryInterval) {
    $scope.initAllInfo.rx.gjdx.oneDeliveryInterval = null;
    }
    if (!$scope.initAllInfo.rx.gjcx.oneTemplateDayLimit) {
    $scope.initAllInfo.rx.gjcx.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.rx.gjcx.oneDeliveryInterval) {
    $scope.initAllInfo.rx.gjcx.oneDeliveryInterval = null;
    }
    if (!$scope.initAllInfo.rx.gjzc.oneTemplateDayLimit) {
    $scope.initAllInfo.rx.gjzc.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.rx.gjzc.oneDeliveryInterval) {
    $scope.initAllInfo.rx.gjzc.oneDeliveryInterval = null;
    }
    //广告
    if (!$scope.initAllInfo.gg.px.oneTemplateDayLimit) {
    $scope.initAllInfo.gg.px.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.gg.px.oneDeliveryInterval) {
    $scope.initAllInfo.gg.px.oneDeliveryInterval = null;
    }
    if (!$scope.initAllInfo.gg.gjcx.oneTemplateDayLimit) {
    $scope.initAllInfo.gg.gjcx.oneTemplateDayLimit = null;
    }
    if (!$scope.initAllInfo.gg.gjcx.oneDeliveryInterval) {
    $scope.initAllInfo.gg.gjcx.oneDeliveryInterval = null;
    }

    if ($scope.enterpriseType == 3 || $scope.isSecondEnter) {       //二级企业
    $scope.enterpriseIDParam = $scope.subEnterpriseID
    } else {                                                        //非二级企业
    $scope.enterpriseIDParam = $scope.enterpriseID
    }

    //名片屏显
    req.serviceRuleList.push({
        id: $scope.initAllInfo.mp.px.id,
        servRuleCode: $scope.initAllInfo.mp.px.servRuleCode,
        servType:"1",
        subServType: "3",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.mp.px.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.mp.px.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID,
        // 周期类型
        periodType: $scope.initAllInfo.mp.px.periodType,
      })
    //热线屏显
    req.serviceRuleList.push({
        id: $scope.initAllInfo.rx.px.id,
        servRuleCode: $scope.initAllInfo.rx.px.servRuleCode,
        servType:"2",
        subServType: "3",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.rx.px.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.rx.px.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID
      })
    //广告屏显
    req.serviceRuleList.push({
        id: $scope.initAllInfo.gg.px.id,
        servRuleCode: $scope.initAllInfo.gg.px.servRuleCode,
        servType:"3",
        subServType: "3",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.gg.px.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.gg.px.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID,
        // 周期类型
        periodType: $scope.initAllInfo.gg.px.periodType,
      })
    //名片挂短
    req.serviceRuleList.push({
        id: $scope.initAllInfo.mp.gjdx.id,
        servRuleCode: $scope.initAllInfo.mp.gjdx.servRuleCode,
        servType:"1",
        subServType: "4",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.mp.gjdx.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.mp.gjdx.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID,
        // 周期类型
        periodType: $scope.initAllInfo.mp.gjdx.periodType,
      })
    //热线挂短
    req.serviceRuleList.push({
        id: $scope.initAllInfo.rx.gjdx.id,
        servRuleCode: $scope.initAllInfo.rx.gjdx.servRuleCode,
        servType:"2",
        subServType: "4",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.rx.gjdx.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.rx.gjdx.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID
      })
    //名片挂彩
    req.serviceRuleList.push({
        id: $scope.initAllInfo.mp.gjcx.id,
        servRuleCode: $scope.initAllInfo.mp.gjcx.servRuleCode,
        servType:"1",
        subServType: "8",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.mp.gjcx.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.mp.gjcx.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID,
        // 周期类型
        periodType: $scope.initAllInfo.mp.gjcx.periodType,
      })
    //热线挂彩
    req.serviceRuleList.push({
        id: $scope.initAllInfo.rx.gjcx.id,
        servRuleCode: $scope.initAllInfo.rx.gjcx.servRuleCode,
        servType:"2",
        subServType: "8",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.rx.gjcx.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.rx.gjcx.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID
      })
    //广告挂彩
    req.serviceRuleList.push({
        id: $scope.initAllInfo.gg.gjcx.id,
        servRuleCode: $scope.initAllInfo.gg.gjcx.servRuleCode,
        servType:"3",
        subServType: "8",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.gg.gjcx.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.gg.gjcx.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID,
        // 周期类型
        periodType: $scope.initAllInfo.gg.gjcx.periodType,
      })
    //热线挂彩
    req.serviceRuleList.push({
        id: $scope.initAllInfo.rx.gjzc.id,
        servRuleCode: $scope.initAllInfo.rx.gjzc.servRuleCode,
        servType:"2",
        subServType: "16",
        enterpriseID: $scope.enterpriseIDParam,
        oneTemplateDayLimit: $scope.initAllInfo.rx.gjzc.oneTemplateDayLimit,
        oneDeliveryInterval: $scope.initAllInfo.rx.gjzc.oneDeliveryInterval,
        creatorID: $scope.accountID,
        operatorID: $scope.accountID
      })


    req.serviceRuleList.push({
      id: $scope.initAllInfo.TZSms.id,
      servRuleCode: $scope.initAllInfo.TZSms.servRuleCode,
      servType: "4",
      enterpriseID: $scope.enterpriseIDParam,
      // 周期类型
      periodType: $scope.initAllInfo.TZSms.periodType,
      oneTemplateDayLimit: $scope.initAllInfo.TZSms.oneTemplateDayLimit,
      oneDeliveryInterval: $scope.initAllInfo.TZSms.oneDeliveryInterval,
      status: $scope.initAllInfo.TZSms.status,
      creatorID: $scope.accountID,
      operatorID: $scope.accountID,
      subServType: "17",
      isUse: $scope.initAllInfo.TZSms.isUse,
      extInfo: {"periodType": $scope.initAllInfo.TZSms.periodType},
      reservedsEcpmp:{reserved1:$scope.initAllInfo.TZSms.reserved1,}

    });
    req.serviceRuleList.push({
      id: $scope.initAllInfo.TZFlash.id,
      servRuleCode: $scope.initAllInfo.TZFlash.servRuleCode,
      servType: "4",
      enterpriseID: $scope.enterpriseIDParam,
      // 周期类型
      periodType: $scope.initAllInfo.TZFlash.periodType,
      oneTemplateDayLimit: $scope.initAllInfo.TZFlash.oneTemplateDayLimit,
      oneDeliveryInterval: $scope.initAllInfo.TZFlash.oneDeliveryInterval,
      status: $scope.initAllInfo.TZFlash.status,
      creatorID: $scope.accountID,
      operatorID: $scope.accountID,
      subServType: "3",
      isUse: $scope.initAllInfo.TZFlash.isUse,
      extInfo: {"periodType": $scope.initAllInfo.TZFlash.periodType},
      reservedsEcpmp:{reserved1:$scope.initAllInfo.TZFlash.reserved1,}

    });
    req.serviceRuleList.push({
      id: $scope.initAllInfo.TZMms.id,
      servRuleCode: $scope.initAllInfo.TZMms.servRuleCode,
      servType: "4",
      enterpriseID: $scope.enterpriseIDParam,
      // 周期类型
      periodType: $scope.initAllInfo.TZMms.periodType,
      oneTemplateDayLimit: $scope.initAllInfo.TZMms.oneTemplateDayLimit,
      oneDeliveryInterval: $scope.initAllInfo.TZMms.oneDeliveryInterval,
      status: $scope.initAllInfo.TZMms.status,
      creatorID: $scope.accountID,
      operatorID: $scope.accountID,
      subServType: "16",
      isUse: $scope.initAllInfo.TZMms.isUse,
      extInfo: {"periodType": $scope.initAllInfo.TZMms.periodType},
      reservedsEcpmp:{reserved1:$scope.initAllInfo.TZMms.reserved1,}


    });
    req.serviceRuleList.push({
      id: $scope.initAllInfo.TZUSSD.id,
      servRuleCode: $scope.initAllInfo.TZUSSD.servRuleCode,
      servType: "4",
      enterpriseID: $scope.enterpriseIDParam,
      // 周期类型
      periodType: $scope.initAllInfo.TZUSSD.periodType,
      oneTemplateDayLimit: $scope.initAllInfo.TZUSSD.oneTemplateDayLimit,
      oneDeliveryInterval: $scope.initAllInfo.TZUSSD.oneDeliveryInterval,
      status: $scope.initAllInfo.TZUSSD.status,
      creatorID: $scope.accountID,
      operatorID: $scope.accountID,
      subServType: "3",
      isUse: $scope.initAllInfo.TZUSSD.isUse,
      extInfo: {"periodType": $scope.initAllInfo.TZUSSD.periodType},
      reservedsEcpmp:{reserved1:$scope.initAllInfo.TZUSSD.reserved1,}


    });
    req.serviceRuleList.push({
      id: $scope.initAllInfo.TZCX.id,
      servRuleCode: $scope.initAllInfo.TZCX.servRuleCode,
      servType: "4",
      enterpriseID: $scope.enterpriseIDParam,
      // 周期类型
      periodType: $scope.initAllInfo.TZCX.periodType,
      oneTemplateDayLimit: $scope.initAllInfo.TZCX.oneTemplateDayLimit,
      oneDeliveryInterval: $scope.initAllInfo.TZCX.oneDeliveryInterval,
      status: $scope.initAllInfo.TZCX.status,
      creatorID: $scope.accountID,
      operatorID: $scope.accountID,
      subServType: "8",
      isUse: $scope.initAllInfo.TZCX.isUse,
      extInfo: {"periodType": $scope.initAllInfo.TZCX.periodType},
      reservedsEcpmp:{reserved1:$scope.initAllInfo.TZCX.reserved1,}
    });

    if($scope.initparentInfo.mp.screenStatus == '0'){
    $scope.mp_px = $scope.initsysInfo.mp.screenStatus;
    }else{
    $scope.mp_px = $scope.initAllInfo.cardPrintStatusWrapper.screenStatus;
    }
    if($scope.initparentInfo.mp.smsstatus == '0'){
    $scope.mp_gd = $scope.initsysInfo.mp.smsstatus;
    }else{
    $scope.mp_gd = $scope.initAllInfo.cardPrintStatusWrapper.smsstatus;
    }
    if($scope.initparentInfo.mp.mmsstatus == '0'){
    $scope.mp_gc = $scope.initsysInfo.mp.mmsstatus;
    }else{
    $scope.mp_gc = $scope.initAllInfo.cardPrintStatusWrapper.mmsstatus;
    }
    if($scope.initparentInfo.gg.screenStatus == '0'){
    $scope.gg_px = $scope.initsysInfo.gg.screenStatus;
    }else{
    $scope.gg_px = $scope.initAllInfo.adPrintStatus.screenStatus;
    }

    if($scope.initparentInfo.groupSendStatus.enhancedMMSStatus == '0'){
    $scope.group_en = $scope.initsysInfo.groupSendStatus.enhancedMMSStatus;
    }else{
    $scope.group_en = $scope.initAllInfo.groupSendStatus.enhancedMMSStatus;
    }
    if($scope.initparentInfo.groupSendStatus.smsstatus == '0'){
    $scope.group_sm = $scope.initsysInfo.groupSendStatus.smsstatus;
    }else{
    $scope.group_sm = $scope.initAllInfo.groupSendStatus.smsstatus;
    }
    if($scope.initparentInfo.groupSendStatus.screenStatus == '0'){
    $scope.group_screen = $scope.initsysInfo.groupSendStatus.screenStatus;
    }else{
    $scope.group_screen = $scope.initAllInfo.groupSendStatus.screenStatus;
    }
    if($scope.initparentInfo.groupSendStatus.mmsstatus == '0'){
    $scope.group_mms = $scope.initsysInfo.groupSendStatus.mmsstatus;
    }else{
    $scope.group_mms = $scope.initAllInfo.groupSendStatus.mmsstatus;
    }
    if(($scope.initAllInfo.FZDXStatus && $scope.initAllInfo.FZSXStatus)&&($scope.initAllInfo.FZSXStatus!=''&&$scope.initAllInfo.FZDXStatus!='')){
    $scope.SZFZStatusUpdate=$scope.initAllInfo.FZSXStatus+","+$scope.initAllInfo.FZDXStatus;
    }else{
    if ($scope.initAllInfo.FZSXStatus == '1'){
        $scope.SZFZStatusUpdate = $scope.initAllInfo.FZSXStatus;
    }
    if ($scope.initAllInfo.FZDXStatus == '2'){
        $scope.SZFZStatusUpdate = $scope.initAllInfo.FZDXStatus;
    }
    }

    req.serviceControl = {
    enterpriseID: $scope.enterpriseIDParam,
    platformStatus:{
        mobileStatus:$scope.initAllInfo.platformStatus.mobileStatus,
        unicomStatus:$scope.initAllInfo.platformStatus.unicomStatus,
        telecomStatus:$scope.initAllInfo.platformStatus.telecomStatus
    },
    rxPlatformStatus:{
        mobileStatus:$scope.initAllInfo.rxPlatformStatus.mobileStatus,
        unicomStatus:$scope.initAllInfo.rxPlatformStatus.unicomStatus,
        telecomStatus:$scope.initAllInfo.rxPlatformStatus.telecomStatus
    },
    cardPrintStatusWrapper:{
        screenStatus:$scope.mp_px,        //屏显
        smsstatus:$scope.mp_gd,              //挂短
        mmsstatus:$scope.mp_gc               //挂彩
    },
    adPrintStatus:{
        screenStatus:$scope.gg_px        //屏显
    },
    groupSendStatus:{
        enhancedMMSStatus:$scope.group_en,        //增彩
        smsstatus:$scope.group_sm,                //短信
        screenStatus:$scope.group_screen,          //屏显
        mmsstatus:$scope.group_mms             //彩信
    },
    reservedsEcpmp:{
        reserved3:$scope.SZFZStatusUpdate,
        reserved4:$scope.initAllInfo.RXSwitchRXStatus === '1'?"5,2":"5",
    },
    creatorID: $scope.accountID,
    operatorID: $scope.accountID,
    unSubscribeStatus: $scope.initAllInfo.unSubscribeStatus,
    ywApproveCallback: $scope.initAllInfo.ywApproveCallback
    }
    let reserved6 = "";
    if($scope.initAllInfo.RXBeyondSwitchRXStatus === "1"){
    reserved6 = reserved6 + "1";
    }else {
    reserved6 = reserved6 + "0";
    }
    if($scope.initAllInfo.RXBeyondSwitchRXSFStatus === "1"){
    reserved6 = reserved6 + "1";
    }else {
    reserved6 = reserved6 + "0";
    }

    req.serviceControl.reservedsEcpmp.reserved6 = reserved6;
    if($scope.hangupTypeStatus == '1'){
        req.serviceControl.reservedsEcpmp.reserved5 = '1,2';
    }else{
        req.serviceControl.reservedsEcpmp.reserved5 = '2';
    }
    // 行业挂机短信
    if($scope.hyHangupSmsTypeStatus == '1'){
        req.serviceControl.reservedsEcpmp.reserved5 =  req.serviceControl.reservedsEcpmp.reserved5+",3";
    }else{
       req.serviceControl.reservedsEcpmp.reserved5 = req.serviceControl.reservedsEcpmp.reserved5.replace(",3","");
    }
    if($scope.UpdateRuleListChecked){
      console.log("点击过快...")
      return;
    }
    $scope.UpdateRuleListChecked = true;
    setTimeout(function (){
      $scope.UpdateRuleListChecked = false;
    },2000);

    RestClientUtil.ajaxRequest({
    type: 'POST',
    url: "/ecpmp/ecpmpServices/contentService/batchUpdateServiceRuleList",
    data: JSON.stringify(req),
    success: function (result) {
    $rootScope.$apply(function () {
      var data = result.result;
      if (data.resultCode == '1030100000') {
        $scope.tip = data.resultCode;
        $('#myModal').modal({
            backdrop: "static",//点击空白处不关闭对话框
            show: true
        });
      } else {
        $scope.tip = data.resultCode;
        $('#myModal').modal();
      }
    })
    },
    error: function () {
    $rootScope.$apply(function () {
      $scope.tip = '1030120500';
      $('#myModal').modal();
      $scope.UpdateRuleListChecked = false;
    })
    }
    });
    };
$scope.generateReq = function (id) {
    var enterpriseId = "";
    if(id) {
        enterpriseId = id;
    }else{
        enterpriseId  =  parseInt($.cookie('enterpriseType')) == 3 ? parseInt($.cookie('subEnterpriseID')) : parseInt($.cookie('enterpriseID'));
    }
    var req = {
        "enterpriseId": enterpriseId || $scope.enterpriseID
    };
    return req;
};
$scope.isFrequencyWhiteHotline = false;
$scope.isFrequencyWhiteCard = false;
$scope.flag = false;
$scope.getIsFrequencyWhite = function (enterpriseId){
    let req = $scope.generateReq (enterpriseId) ;

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/dayLimitServices/queryList",
      data: JSON.stringify(req),
      success: function (result) {
          $rootScope.$apply(function () {
              var data = result.result;
              if (data.resultCode == '1030100000') {
                  $scope.contentInfoListData = result.dayLimitEnterpriseList || [];
                  if($scope.contentInfoListData.length > 0){
                      for(let i = 0;i<$scope.contentInfoListData.length;i++){
                          if($scope.contentInfoListData[i].servType.indexOf("1") !== -1){
                              $scope.isFrequencyWhiteCard = true;
                          }
                          if($scope.contentInfoListData[i].servType.indexOf("2") !== -1){
                              $scope.isFrequencyWhiteHotline = true;
                          }
                      }
                  }else {
                     if(parseInt($.cookie('enterpriseType')) == 3 && !$scope.flag ) {  // 子企业没有特殊频控，查父企业的
                        $scope.flag = true;
                        var enterpriseId = parseInt($.cookie('enterpriseID'));
                        $scope.getIsFrequencyWhite(enterpriseId);
                     }
                  }
              }
          })
      },
      error: function () {
          $rootScope.$apply(function () {
              $scope.tip = '1030120500';
              $('#myModal').modal();
          })
      }
    });
    }
    $scope.changeTable = function (num) {
    window.localStorage.setItem('menuType', num);
    window.location.href = window.location.href;
  };

  $scope.returnUp = function () {
    location.href = '../enterprise/enterpriseList/enterpriseList.html';
  };
    $scope.getServerTime = function () {
        var gmt_time = $.ajax({async: false, cache: false}).getResponseHeader("Date");
        var local_time = new Date(gmt_time)
        if (!local_time) {
            return
        } else {
            var obj = {};
            var d = new Date(local_time);
            obj.year = d.getFullYear();
            obj.month = ('0' + (d.getMonth() + 1)).slice(-2);
            obj.day = ('0' + (d.getDate())).slice(-2);
            obj.hour = ('0' + (d.getHours())).slice(-2);
            obj.minutes = ('0' + (d.getMinutes())).slice(-2);
            obj.seconds = ('0' + (d.getSeconds())).slice(-2);
            return obj
        }
    };
    $scope.querySyncServiceRule = function () {



        var serverTime = $scope.getServerTime();
        $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
        //下拉框(投递方式)
        $scope.subServTypeChoise = [];
        var req = {
            "enterpriseID": parseInt($scope.enterpriseID)
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    console.log(result)
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.subServerTypeList = result.syncServiceRuleList || [];
                        var serverTypeMap = new Map();
                        // 业务设置页面是否显示名片异网投递配置
                        var showMpPlatformConfig = false;
                        // 业务设置页面是否显示热线异网投递配置
                        var showRxPlatformConfig = false;

                        // //查询业务开关
                        // $scope.querySyncServiceRuleList($scope)

                        for (var i in $scope.subServerTypeList) {
                            var item = $scope.subServerTypeList[i];
                            //开通或暂停状态才能投递
                            if ((item.status == 1 || item.status == 3) && $scope.nowTime <= item.expiryTime && $scope.nowTime >= item.effectiveTime) {
                                if (item.servType == 1)
                                {
                                    serverTypeMap.set("mp", "23");
                                    showMpPlatformConfig = true;
                                }
                                else if (item.servType == 5)
                                {
                                    serverTypeMap.set("rxsf", "40");
                                    showRxPlatformConfig = true;
                                }
                                else if (item.servType == 2)
                                {
                                    //中移政企额外判断开关reserve4
                                    if($scope.isZYZQ == true
                                        &&　($scope.serviceControl ==null || $scope.serviceControl.reserved4 == null || $scope.serviceControl.reserved4.indexOf("2") == -1)){
                                        continue;
                                    }

                                    serverTypeMap.set("rx", "30,49")
                                }
                            }
                        }

                        $.cookie("showMpPlatformConfig", showMpPlatformConfig, {path:'/'});
                        $.cookie("showRxPlatformConfig", showRxPlatformConfig, {path:'/'});

                        // if (serverTypeMap.size < 3)
                        // {
                            var serverTypeString = "[22, 24, 7";
                            if (serverTypeMap.get("mp")
                                && (!$scope.isYDY || ($scope.isYDY&&$scope.ydyMp)))
                            {
                                serverTypeString = serverTypeString + "," + serverTypeMap.get("mp");
                            }
                            if (serverTypeMap.get("rxsf"))
                            {
                                serverTypeString = serverTypeString + "," + serverTypeMap.get("rxsf");
                            }
                            if (serverTypeMap.get("rx")
                                && (!$scope.isYDY|| ($scope.isYDY&&$scope.ydyRx)))
                            {
                                serverTypeString = serverTypeString + "," + serverTypeMap.get("rx");
                            }
                            serverTypeString = serverTypeString + "]";
                            if($scope.isZYZQ ){
                                serverTypeString = serverTypeString
                                    .replaceAll("40","76")
                                    .replaceAll(",30","")
                                    .replaceAll(",49","");
                            }
                            $scope.proSupServerType = serverTypeString;
                            console.log(serverTypeString);
                            $.cookie("proSupServerType",serverTypeString,{path:'/'});
                        // }

                    } else {
                        $scope.subServerTypeList = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.getFunctionDauth = function (code) {
        let re = false;
        $scope.dataAuthList = window.localStorage.getItem("dataAuthList");
        if( $scope.dataAuthList){
            angular.forEach(JSON.parse($scope.dataAuthList), function (row) {
                if(row.authCode == code){
                    re  = true;
                    return re;
                }
            });
        }
        return re;
    }

}])
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])