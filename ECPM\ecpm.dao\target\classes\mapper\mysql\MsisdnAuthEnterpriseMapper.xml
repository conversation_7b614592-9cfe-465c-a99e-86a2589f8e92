<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MsisdnAuthEnterpriseMapper">

    <select id="countMsisdnAuthEnterprise" parameterType="com.huawei.jaguar.dsdp.ecpm.request.QueryMsisdnAuthReq" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ecpm_t_msisdn_auth_enterprise
        <where>
            <if test="req.enterpriseName != null and req.enterpriseName != ''">
                AND enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')
            </if>
            <if test="req.auditStatus != null">
                AND audit_status = #{req.auditStatus}
            </if>
            <if test="req.startAuditTime != null">
                AND first_audit_time <![CDATA[ >= ]]> #{req.startAuditTime}
            </if>
            <if test="req.endAuditTime != null">
                AND first_audit_time <![CDATA[ <= ]]> #{req.endAuditTime}
            </if>
        </where>
    </select>
    
    <select id="queryMsisdnAuthEnterprise" parameterType="com.huawei.jaguar.dsdp.ecpm.request.QueryMsisdnAuthReq" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MsisdnAuthEnterpriseWrapper">
        SELECT 
            id,
            enterprise_name as enterpriseName,
            brand_fee_flag as brandFeeFlag,
            create_time as createTime,
            first_audit_time as firstAuditTime,
            audit_status as auditStatus,
            msisdn_quantity as msisdnQuantity
        FROM ecpm_t_msisdn_auth_enterprise
        <where>
            <if test="req.enterpriseName != null and req.enterpriseName != ''">
                AND enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')
            </if>
            <if test="req.auditStatus != null">
                AND audit_status = #{req.auditStatus}
            </if>
            <if test="req.startAuditTime != null">
                AND first_audit_time <![CDATA[ >= ]]> #{req.startAuditTime}
            </if>
            <if test="req.endAuditTime != null">
                AND first_audit_time <![CDATA[ <= ]]> #{req.endAuditTime}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{pageStart},#{pageSize}
    </select>



</mapper> 