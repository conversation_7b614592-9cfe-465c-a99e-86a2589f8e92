<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
<link href="../../../../../css/reset.css" rel="stylesheet"/>
<link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript"
        src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
<!-- 引入菜单组件 -->
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<!--分页-->
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css"/>
<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<script type="text/javascript" src="intelligentCallList.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../css/hotlineContentManage.css"/>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
	font-weight: normal;
	white-space: nowrap;
}
.daterangepicker td, .daterangepicker th {
	width:auto;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
</style>

</head>
<body ng-app='myApp' ng-controller='intelligentCallListController' ng-init="init()" class="body-min-width-new">
	
	<div class="cooperation-manage" style="overflow-x: scroll;">
		<div ng-if="loginRoleType=='agent'" class="cooperation-head">
	        <span class="frist-tab" ng-bind="'COMMON_SUBENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;
	        <span class="second-tab" ng-bind="'WLT_MANAGER'|translate"></span>
	    </div>
		<top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/expressageManage/hotline/HotlineManagement"
            list-index="76" ng-if="wltServiceType==1"></top:menu>
        <top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/expressageManage/hotline/intelligentCall"
            list-index="77" ng-if="wltServiceType==2"></top:menu>
            <div class="cooperation-search">
				<form class="form-inline">
				<div class="form-group">
					<div class="form-group col-lg-2 col-xs-2  col-sm-2 col-md-3">
						<label for="businessType" style="padding-right:30px;" style="white-space: nowrap;" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
						<select class="form-control" name="businessType" ng-model="businessType" ng-options="x.id as x.name for x in businessTypeChoise">
						</select>
					</div>
					<div class="form-group col-lg-2 col-xs-2  col-sm-2 col-md-3">
						<label for="phoneNum" style="padding-right:30px;" ng-bind="'COMMON_NUMBER'|translate"></label>
						<input type="text" autocomplete="off" class="form-control" id="phoneNum" placeholder="{{'USER_PHONE_PLACEHOLDER'|translate}}"   ng-model="phoneNum">
					</div>
					
					<div class="form-group col-lg-2 col-xs-2  col-sm-2 col-md-3">
						<label for="trackingNum" style="padding-right:30px;" ng-bind="'TRACKING_NUMBER'|translate"></label>
						<input type="text" autocomplete="off" class="form-control" id="trackingNum" placeholder="{{'TRACKING_PLACEHOLDER'|translate}}"   ng-model="trackingNum">
					</div>
					
					<div class="form-group col-lg-2 col-xs-2  col-sm-2 col-md-3">
						<label for="contentID" style="padding-right:30px;" ng-bind="'COMMON_ID1'|translate"></label>
						<input type="text" autocomplete="off" class="form-control" id="contentID" placeholder="{{'CONTENTAUDIT_INPUTCONTENTNUMBERS'|translate}}"   ng-model="contentID">
					</div>
					
					<div class="form-group col-lg-2 col-xs-2  col-sm-2 col-md-2">
						<label for="result" style="padding-right:30px;" style="white-space: nowrap;" ng-bind="'CALL_RESULT'|translate"></label>
						<select class="form-control" name="result" ng-model="result" ng-options="x.id as x.name for x in resultChoise">
						</select>
					</div>

					<div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
						<label style="padding-right:30px;" ng-bind="'CONTENT_POSTTIME'|translate"></label>
						<div class="input-daterange input-group" id="datepicker">
							<input type="text" class="input-md form-control" autocomplete="off" id="deliveryStartTime" ng-keyup="searchOn()"/>
							<span class="input-group-addon" ng-bind="'TO'|translate"></span>
							<input type="text" class="input-md form-control" autocomplete="off" id="deliveryEndTime" ng-keyup="searchOn()"/>
						</div>
					</div>
					
					<div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
						<label style="padding-right:30px;" ng-bind="'CALL_TIME'|translate"></label>
						<div class="input-daterange input-group" id="datepicker">
							<input type="text" class="input-md form-control" autocomplete="off" id="startTime" ng-keyup="searchOn2()"/>
							<span class="input-group-addon" ng-bind="'TO'|translate"></span>
							<input type="text" class="input-md form-control" autocomplete="off" id="endTime" ng-keyup="searchOn2()"/>
						</div>
					</div>

            		<div class="form-group col-lg-1 col-xs-1  col-sm-1 col-md-1">
							<button ng-click="queryIntelligentCall()" type="submit" class="btn search-btn" ng-disabled="initSel[0].search && initSel[1].search" ><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
				</div>
				</form>
			</div>
			
			<div style="margin-left: 20px;margin-bottom: 20px;">
		        <p style="font-size: 16px;font-weight: 500;"></p>
		    </div>

			<div class="coorPeration-table">
					<table class="table table-striped table-hover">
						<thead>
							<tr>
								<th style="width:10%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
								<th style="width:10%" ng-bind="'USER_PHONE_NUMBER'|translate"></th>
								<th style="width:10%" ng-bind="'DELIVERY_PHONE_NUMBER'|translate"></th>
								<th style="width:10%" ng-bind="'TRACKING_NUMBER'|translate"></th>
								<th style="width:10%" ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></th>
								<th style="width:10%" ng-bind="'DELIVERYRESULT'|translate"></th>
								<th style="width:10%" ng-bind="'CONTENT_POSTTIME'|translate"></th>
								<th style="width:10%" ng-bind="'REPLY_INSTRUCTION'|translate"></th>
								<th style="width:10%" ng-bind="'CALL_RESULT'|translate"></th>
								<th style="width:9%" ng-bind="'CALL_TIME'|translate"></th>
								<th style="width:9%" ng-bind="'REPLY_CONTENT'|translate"></th>
								<th class="adjustable-width" ng-bind="'COMMON_OPERATE'|translate"></th>
							</tr>
						</thead>
						<tbody>
								<tr ng-repeat="item in IntelligentCallListData">
									<td><span title="{{getBusinessType(item.businessType)}}">{{getBusinessType(item.businessType)}}</span></td>
									<td><span title="{{getNumber(item.caller)}}">{{getNumber(item.caller)}}</span></td>
									<td><span title="{{getNumber(item.callee)}}">{{getNumber(item.callee)}}</span></td> 
									<td><span title="{{item.trackingNum}}">{{item.trackingNum}}</span></td>
									<td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
									<td><span title="{{getDeliveryResult(item.deliveryResult)}}">{{getDeliveryResult(item.deliveryResult)}}</span></td>
									<td><span title="{{getTime(item.deliveryTime)}}">{{getTime(item.deliveryTime)}}</span></td>
									<td><span title="{{item.reply}}">{{item.reply}}</span></td>
									<td><span title="{{getResult(item.result)}}">{{getResult(item.result)}}</span></td>
									<td><span title="{{getTime(item.createTime)}}">{{getTime(item.createTime)}}</span></td>
									<td><span title="{{item.collectInfo}}">{{item.collectInfo}}</span></td>
									<td>
					                    <div class="handle">
					                       <ul>
												<li class="query" ng-click="showDetail(item.requestId)">
																<icon class="query-icon"></icon>
																<span style="color:#705de1" ng-bind="'COMMON_WATCH'|translate"></span>
														   </li>
										   </ul>
										</div>
									</td>
								</tr>
								<tr ng-show="IntelligentCallListData.length<=0">
									<td style="text-align:center" colspan="10" ng-bind="'COMMON_NODATA'|translate"></td>
								</tr>
							</tbody>
					</table>
				</div>

				<div>
					<ptl-page tableId="0" change="queryIntelligentCall('justPage')"></ptl-page>
				</div>
		</div>
		
		<div class="modal fade" id="addIntelligentCall" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
		     style="overflow: auto">
		    <div class="modal-dialog" role="document">
		        <div class="modal-content">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
		                        aria-hidden="true">&times;</span></button>
		                <h4 class="modal-title" id="myModalLabel" ng-bind="'CALL_RECORD'|translate"></h4>
		            </div>
		            <div class="cooper-tab" style="padding:10px 20px;line-height:24px;max-height: 400px;overflow: auto;">
		                <p>
		                	<label  ng-bind="'DELIVERY_PHONE_NUMBER'|translate"></label>
		                	<span style="padding-right:20px;">{{getNumber(detailDeliveryNum)}}</span>
		                	<label  ng-bind="'TRACKING_NUMBER'|translate"></label>
		                	<span style="padding-right:20px;">{{detailTrackingNum}}</span>
		                	<label  ng-bind="'CALL_TIME'|translate"></label>
		                	<span>{{getTime(detailCallTime)}}</span>
		                </p>
		                <h4 style="font-weight: bold;margin: 10px 0;" ng-bind="'CALL_RECORD'|translate"></h4>
		                <div ng-if="detailList.length>0">
		                	<p ng-repeat="item in detailList track by $index">{{item}}</p>
		                </div>
		            </div>
		            <div class="modal-footer">
		            	<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-click="goback()" id="addHotlineContentCancel" ng-bind="'COMMON_BACK'|translate"></button>
		            </div>
		        </div>
		    </div>
		</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>