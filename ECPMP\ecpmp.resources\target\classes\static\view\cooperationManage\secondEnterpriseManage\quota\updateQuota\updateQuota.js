var app = angular.module("myApp", ["util.ajax", "service.common", "angularI18n", "top.menu"]);
app.controller('updateQuotaCtrl', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {

    //初始化页面
    $scope.init = function () {
        //默认企业名称
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.isSuperManager = false;
        $scope.operatorID = $.cookie('accountID') || '';
        var loginRoleType = $.cookie('loginRoleType');
        $scope.accountName = $.cookie('accountName') || '';
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isAgent = loginRoleType == 'agent';
        $scope.orderCode = $location.search().orderCode || '';
        $scope.objectID = $location.search().objectID || '';

        //默认屏显配额是不限，字段值为
        $scope.pingxianisLimit = 0;
        $scope.pingxianisLimit_cucc = 0;
        $scope.pingxianisLimit_ctcc = 0;
        //默认挂机短信是不限，字段值为
        $scope.guaduanisLimit = 0;
        //默认挂机彩信是不限，字段值为
        $scope.guacaiisLimit = 0;
        $scope.expireTime = "";
        $scope.effictiveTime = "";


        //下拉框(业务类别)
        $scope.servTypeChoise = [
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            }
        ];
        $scope.pxType = 0;
        $scope.gdType = 0;
        $scope.gdTypeCUCC = 0;
        $scope.gdTypeCTCC = 0;
        $scope.gcType = 0;

        $scope.pxType_cucc = 0;
        $scope.pxType_ctcc = 0;

        $scope.pxanci_price = 0;
        $scope.baoyue_price = 0;
        $scope.gdbaoyue_price = 0;
        $scope.gcbaoyue_price = 0;

        $scope.guaduan_price = 0;
        $scope.guaduan_priceCUCC = 0;
        $scope.guaduan_priceCTCC = 0;
        $scope.guacai_price = 0;

        $scope.queryOrderDetail()
    };

    //查询代理商下的订单
    $scope.queryOrderDetail = function () {
        var req = {
            "orderCode": $scope.orderCode,
            "isReturnOrderItem": 1,
            "objectID": $scope.objectID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                        $scope.orderInfo = data.orderList[0];
                        $scope.startDate = $scope.formateDate($scope.orderInfo.effictiveTime);
                        $scope.endDate = $scope.formateDate($scope.orderInfo.expireTime);
                        $scope.servType = $scope.orderInfo.servType;
                        //生效时间和失效时间
                        $('.input-daterange').datepicker({
                            format: "yyyy-mm-dd",
                            weekStart: 0,
                            language: "zh-CN",
                            autoclose: true,
                            startDate: $scope.startDate,
                            endDate: $scope.endDate
                        });
                        $scope.effictiveTime = $scope.orderInfo.effictiveTime;
                        $scope.expireTime = $scope.orderInfo.expireTime;
                        $scope.showPeiE($scope.orderInfo)
                    } else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();

                })
            }
        });
    }
    $scope.formateDate = function (date) {
        return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8)
    };

    $scope.querySubscribeList = function (selectedOrder, isCreate) {
        var subreq = {
            "orderID": selectedOrder.extInfo.quotaOrderID
        };
        $scope.baoyueRest = Infinity;
        $scope.gdbaoyueRest = Infinity;
        $scope.gcbaoyueRest = Infinity;

        $scope.px_anciRest_cmcc = Infinity;
        $scope.px_anciRest_cucc = Infinity;
        $scope.px_anciRest_ctcc = Infinity;
        $scope.guaduan_anciRest = Infinity;
        $scope.guaduan_anciRestCUCC = Infinity;
        $scope.guaduan_anciRestCTCC = Infinity;
        $scope.guacai_anciRest = Infinity;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryQuotaByOrder",
            data: JSON.stringify(subreq),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                    	
                    	$scope.quotaUsedList = data.quotaUsedList;
                        angular.forEach($scope.quotaUsedList, function (item) {
                            switch (item.subServType) {
                                case 1:
                                    //包月剩余量
                                    $scope.baoyueRest = item.remainUseMemberCount / $scope.memberCount;
                                    break;
                                case 2:
                                    if (item.mobilePlatform === "1") {
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 2) {

                                                //包月剩余量
                                                $scope.baoyueRest = item.remainUseMemberCount / $scope.memberCount;
                                            }
                                        }
                                    }
                                    if (item.mobilePlatform === "2") {// 广告联通屏显按次剩余量
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 1) {

                                                $scope.px_anciRest_cucc = item.remainUseAmount / $scope.pinxian_amount_cucc;
                                            }
                                        }
                                    }
                                    if (item.mobilePlatform === "3") {// 广告电信屏显按次剩余量
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 1) {

                                                $scope.px_anciRest_ctcc = item.remainUseAmount / $scope.pinxian_amount_ctcc;
                                            }
                                        }
                                    }

                                    break;
                                case 3:
                                    if (item.isLimit === 1) {
                                        if (item.chargeType == 1)
                                        {
                                            if (item.mobilePlatform === "1") {// 移动屏显按次剩余量
                                                $scope.px_anciRest_cmcc = item.remainUseAmount / $scope.pinxian_amount;
                                            }
                                            if (item.mobilePlatform === "2") {// 联通屏显按次剩余量
                                                $scope.px_anciRest_cucc = item.remainUseAmount / $scope.pinxian_amount_cucc;
                                            }
                                            if (item.mobilePlatform === "3") {// 电信屏显按次剩余量
                                                $scope.px_anciRest_ctcc = item.remainUseAmount / $scope.pinxian_amount_ctcc;
                                            }
                                        }
                                        else
                                        {
                                            //包月剩余量
                                            $scope.baoyueRest = item.remainUseMemberCount / $scope.memberCount;
                                            break;
                                        }
                                    }
                                    break;
                                case 10:
                                    if (item.isLimit === 1) {
                                        if (item.mobilePlatform === "1") {
                                            $scope.px_anciRest_cmcc = item.remainUseAmount / $scope.pinxian_amount;
                                        }
                                    }
                                    break;
                                case 4:
                                    if (item.mobilePlatform === "1") {                  //加if判断 20191107
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 2) {
                                                //包月剩余量
                                                $scope.gdbaoyueRest = item.remainUseMemberCount / $scope.gdmemberCount;
                                            } else {
                                                //挂机短信剩余量
                                                $scope.guaduan_anciRest = item.remainUseAmount / $scope.guaduan_amount;
                                            }
                                        }
                                    }

                                    if (item.mobilePlatform === "2") {                  //联通挂短
                                        if (item.isLimit === 1) {
                                            //挂机短信剩余量
                                            $scope.guaduan_anciRestCUCC = item.remainUseAmount / $scope.guaduan_amountCUCC;
                                        }
                                    }

                                     if (item.mobilePlatform === "3") {                  //电信挂短
                                        if (item.isLimit === 1) {
                                            //挂机短信剩余量
                                            $scope.guaduan_anciRestCTCC = item.remainUseAmount / $scope.guaduan_amountCTCC;
                                        }
                                    }

                                    break;
                                case 8:
                                    if (item.isLimit === 1) {
                                        if (item.chargeType == 2) {
                                            //包月剩余量
                                            $scope.gcbaoyueRest = item.remainUseMemberCount / $scope.gcmemberCount;

                                        } else {
                                            //挂机彩信剩余量
                                            $scope.guacai_anciRest = item.remainUseAmount / $scope.guacai_amount;
                                        }
                                    }
                                    break;
                                case 16:
                                     if (item.isLimit === 1) {
                                         //剩余量
                                         $scope.zengcai_anciRest = item.remainUseAmount / $scope.zengcai_amount;
                                     }
                                     break;
                                case 17:
                                    if (item.mobilePlatform === "1") {                  //加if判断 20191107
                                        if (item.isLimit === 1) {
                                            //剩余量
                                            $scope.groupSendSMSCMCC_anciRest = item.remainUseAmount / $scope.groupSendSMSCMCCAmount;
                                        }
                                    }

                                    if (item.mobilePlatform === "2") {                  //联通群发短信
                                        if (item.isLimit === 1) {
                                            //剩余量
                                            $scope.groupSendSMSCUCC_anciRest = item.remainUseAmount / $scope.groupSendSMSCUCCAmount;
                                        }
                                    }

                                    if (item.mobilePlatform === "3") {                  //联通群发短信
                                        if (item.isLimit === 1) {
                                            //剩余量
                                            $scope.groupSendSMSCTCC_anciRest = item.remainUseAmount / $scope.groupSendSMSCTCCAmount;
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        })
                        if ($scope.groupSendSMSCMCC_anciRest !== Infinity) {
                            $scope.groupSendSMSCMCC_tip = "剩余配额：" + $scope.groupSendSMSCMCC_anciRest;
                        } else {
                            $scope.groupSendSMSCMCC_tip = "剩余配额：不限";
                        }
                        if ($scope.groupSendSMSCUCC_anciRest !== Infinity) {
                            $scope.groupSendSMSCUCC_tip = "剩余配额：" + $scope.groupSendSMSCUCC_anciRest;
                        } else {
                            $scope.groupSendSMSCUCC_tip = "剩余配额：不限";
                        }
                        if ($scope.groupSendSMSCTCC_anciRest !== Infinity) {
                            $scope.groupSendSMSCTCC_tip = "剩余配额：" + $scope.groupSendSMSCTCC_anciRest;
                        } else {
                            $scope.groupSendSMSCTCC_tip = "剩余配额：不限";
                        }

                        if ($scope.zengcai_anciRest !== Infinity) {
                            $scope.zengcai_anciRest_tip = "剩余配额：" + $scope.zengcai_anciRest;
                        } else {
                            $scope.zengcai_anciRest_tip = "剩余配额：不限";
                        }
                        if ($scope.px_anciRest_cmcc !== Infinity) {
                            $scope.px_anciRest_tip = "剩余配额：" + $scope.px_anciRest_cmcc;
                        } else {
                            $scope.px_anciRest_tip = "剩余配额：不限";
                        }
                        if ($scope.px_anciRest_cucc !== Infinity) {
                            $scope.px_anciRest_tip_cucc = "剩余配额：" + $scope.px_anciRest_cucc;
                        } else {
                            $scope.px_anciRest_tip_cucc = "剩余配额：不限";
                        }
                        if ($scope.px_anciRest_ctcc !== Infinity) {
                            $scope.px_anciRest_tip_ctcc = "剩余配额：" + $scope.px_anciRest_ctcc;
                        } else {
                            $scope.px_anciRest_tip_ctcc = "剩余配额：不限";
                        }
                        if ($scope.baoyueRest !== Infinity) {
                            $scope.baoyueRest_tip = "剩余配额：" + $scope.baoyueRest;
                        } else {
                            $scope.baoyueRest_tip = "剩余配额：不限";
                        }
                        if ($scope.gdbaoyueRest !== Infinity) {
                            $scope.gdbaoyueRest_tip = "剩余配额：" + $scope.gdbaoyueRest;
                        } else {
                            $scope.gdbaoyueRest_tip = "剩余配额：不限";
                        }
                        if ($scope.gcbaoyueRest !== Infinity) {
                            $scope.gcbaoyueRest_tip = "剩余配额：" + $scope.gcbaoyueRest;
                        } else {
                            $scope.gcbaoyueRest_tip = "剩余配额：不限";
                        }
                        if ($scope.guaduan_anciRest !== Infinity) {
                            $scope.guaduan_anciRest_tip = "剩余配额：" + $scope.guaduan_anciRest;
                        } else {
                            $scope.guaduan_anciRest_tip = "剩余配额：不限";
                        }
                        if ($scope.guaduan_anciRestCUCC !== Infinity) {             //联通挂短
                            $scope.guaduan_anciRest_tipCUCC = "剩余配额：" + $scope.guaduan_anciRestCUCC;
                        } else {
                            $scope.guaduan_anciRest_tipCUCC = "剩余配额：不限";
                        }
                        if ($scope.guaduan_anciRestCTCC !== Infinity) {             //电信挂短
                            $scope.guaduan_anciRest_tipCTCC = "剩余配额：" + $scope.guaduan_anciRestCTCC;
                        } else {
                            $scope.guaduan_anciRest_tipCTCC = "剩余配额：不限";
                        }
                        if ($scope.guacai_anciRest !== Infinity) {
                            $scope.guacai_anciRest_tip = "剩余配额：" + $scope.guacai_anciRest;
                        } else {
                            $scope.guacai_anciRest_tip = "剩余配额：不限";
                        }
                        if (isCreate === "updateOrder") {
                            $scope.updateOrder();
                        }

                        $scope.queryQuotaByOrderToMin(selectedOrder);
                    }
                    else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
                });
            }
        });
        
    };
    
    $scope.queryQuotaByOrderToMin = function (selectedOrder) {
    	var subreq = {
            "orderID": selectedOrder.orderCode,
            "enterpriseID": selectedOrder.enterpriseID
        };
    	$scope.baoyueMin = Infinity;
        $scope.gdbaoyueMin = Infinity;
        $scope.gcbaoyueMin = Infinity;

        $scope.px_anciMin_cmcc = Infinity;
        $scope.px_anciMin_cucc = Infinity;
        $scope.px_anciMin_ctcc = Infinity;
        $scope.guaduan_anciMin = Infinity;
        $scope.guaduan_anciMinCUCC = Infinity;
        $scope.guaduan_anciMinCTCC = Infinity;
        $scope.guacai_anciMin = Infinity;
        $scope.zengcai_anciMin = Infinity;
    	RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryQuotaByOrder",
            data: JSON.stringify(subreq),
            success: function (data) {
                $rootScope.$apply(function () {
                	if (data.result.resultCode == '1030100000') {
                    	$scope.quotaUsedList = data.quotaUsedList;
                        angular.forEach($scope.quotaUsedList, function (item) {
                            switch (item.subServType) {
                                case 1:
                                    //包月最小值
                                    $scope.baoyueMin = item.deductionMemberCount;
                                    break;
                                case 2:
                                    if (item.mobilePlatform === "1") {
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 2) {

                                                //包月最小值
                                                $scope.baoyueMin = item.deductionMemberCount;
                                            }
                                        }
                                    }
                                    if (item.mobilePlatform === "2") {// 广告联通屏显按次最小值
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 1) {

                                                $scope.px_anciMin_cucc = item.oneActualUseAmount;
                                            }
                                        }
                                    }
                                    if (item.mobilePlatform === "3") {// 广告电信屏显按次最小值
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 1) {

                                                $scope.px_anciMin_ctcc = item.oneActualUseAmount;
                                            }
                                        }
                                    }

                                    break;
                                case 3:
                                    if (item.isLimit === 1) {
                                        if (item.chargeType == 1)
                                        {
                                            if (item.mobilePlatform === "1") {// 移动屏显按次最小值
                                                $scope.px_anciMin_cmcc = item.oneActualUseAmount;
                                            }
                                            if (item.mobilePlatform === "2") {// 联通屏显按次最小值
                                                $scope.px_anciMin_cucc = item.oneActualUseAmount;
                                            }
                                            if (item.mobilePlatform === "3") {// 电信屏显按次最小值
                                                $scope.px_anciMin_ctcc = item.oneActualUseAmount;
                                            }
                                        }
                                        else
                                        {
                                        	//包月最小值
                                            $scope.baoyueMin = item.deductionMemberCount;
                                            break;
                                        }
                                    }
                                    break;
                                case 10:
                                    if (item.isLimit === 1) {
                                        if (item.mobilePlatform === "1") {
                                            $scope.px_anciMin_cmcc = item.oneActualUseAmount;
                                        }
                                    }
                                    break;
                                case 4:
                                    if (item.mobilePlatform === "1") {                  //加if判断 20191107
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 2) {
                                                //包月最小值
                                                $scope.gdbaoyueMin = item.deductionMemberCount;
                                            } else {
                                                //挂机短信最小值
                                                $scope.guaduan_anciMin = item.oneActualUseAmount;
                                            }
                                        }
                                    }

                                    if (item.mobilePlatform === "2") {                  //联通挂短
                                        if (item.isLimit === 1) {
                                            //挂机短信最小值
                                            $scope.guaduan_anciMinCUCC = item.oneActualUseAmount;
                                        }
                                    }

                                     if (item.mobilePlatform === "3") {                  //电信挂短
                                        if (item.isLimit === 1) {
                                            //挂机短信最小值
                                            $scope.guaduan_anciMinCTCC = item.oneActualUseAmount;
                                        }
                                    }

                                    break;
                                case 8:
                                    if (item.isLimit === 1) {
                                        if (item.chargeType == 2) {
                                            //包月最小值
                                            $scope.gcbaoyueMin = item.deductionMemberCount;
                                        } else {
                                            //挂机彩信最小值
                                            $scope.guacai_anciMin = item.oneActualUseAmount;
                                        }
                                    }
                                    break;
                                case 16:
                                     if (item.isLimit === 1) {
                                         //最小值
                                         $scope.zengcai_anciMin = item.oneActualUseAmount;
                                     }
                                     break;
                                case 17:
                                    if (item.mobilePlatform === "1") {                  //加if判断 20191107
                                        if (item.isLimit === 1) {
                                            $scope.groupSendSMSCMCC_anciMin = item.oneActualUseAmount;
                                        }
                                    }

                                    if (item.mobilePlatform === "2") {                  //联通群发短信
                                        if (item.isLimit === 1) {
                                            $scope.groupSendSMSCUCC_anciMin = item.oneActualUseAmount;
                                        }
                                    }

                                    if (item.mobilePlatform === "3") {                  //电信群发短信
                                        if (item.isLimit === 1) {

                                            $scope.groupSendSMSCTCC_anciMin = item.oneActualUseAmount;
                                        }
                                    }

                                    break;
                                default:
                                    break;
                            }
                        })                       

                    }
                    else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
                });
            }
    	})
    };

    $scope.showPeiE = function (selectedOrder) {
        $scope.anci = "";
        $scope.baoyue = "";
        $scope.gdbaoyue = "";
        $scope.gcbaoyue = "";
        $scope.gdanci = "";
        $scope.gdanciCUCC = "";
        $scope.gdanciCTCC = "";
        $scope.gcanci = "";
        $scope.zcanci = "";
        $scope.px_noLimit_orderItem = {};
        $scope.px_noLimit_orderItem_cucc = {};
        $scope.px_noLimit_orderItem_ctcc = {};

        $scope.px_baoyue_orderItem = {};
        $scope.gd_baoyue_orderItem = {};
        $scope.gc_baoyue_orderItem = {};

        $scope.px_anci_orderItem = {};
        $scope.guaduan_noLimit_orderItem = {};
        $scope.guaduan_noLimit_orderItemCUCC = {};
        $scope.guaduan_noLimit_orderItemCTCC = {};
        $scope.guaduan_anci_orderItem = {};
        $scope.guaduan_anci_orderItemCUCC = {};
        $scope.guaduan_anci_orderItemCTCC = {};
        $scope.guacai_noLimit_orderItem = {};
        $scope.guacai_anci_orderItem = {};
        $scope.zengcai_orderItem = {};
        //重置表单校验
        $scope.orderItemDomain.$setPristine();
        $scope.orderItemDomain.$setUntouched();
        $scope.hasPX_cmcc = false;
        $scope.hasGD_cmcc = false;
        $scope.hasGD_cmccCUCC = false;
        $scope.hasGD_cmccCTCC = false;
        $scope.hasGC_cmcc = false;
        $scope.hasGD = false;
        $scope.hasGDCUCC = false;
        $scope.hasGDCTCC = false;
        $scope.hasGC = false;

        $scope.postPingXianCMCC = false;
        $scope.postPingXianCUCC = false;
        $scope.postPingXianCTCC = false;
        $scope.postGuaDuan = false;
        $scope.postGuaDuanCUCC = false;
        $scope.postGuaDuanCTCC = false;
        $scope.postGuaCai = false;
        $scope.isPXLimit = false;
        $scope.isPXLimit_cucc = false;
        $scope.isPXLimit_ctcc = false;
        $scope.isPXanci = false;
        $scope.isPXbaoyue = false;
        $scope.isGDLimit = false;
        $scope.isGDLimitCUCC = false;
        $scope.isGDLimitCTCC = false;
        $scope.isGDanci = false;
        $scope.isGDanciCUCC = false;
        $scope.isGDanciCTCC = false;
        $scope.isGDbaoyue = false;
        $scope.isGCbaoyue = false;
        $scope.isGCLimit = false;
        $scope.isGCanci = false;
        $scope.hasCMCC = false;
        $scope.hasCUCC = false;
        $scope.hasCTCC = false;

        $scope.hasZC_cmcc =false;
        $scope.hasZC = false;

        $scope.orderItemList = selectedOrder.orderItemList;
        $scope.isExperience = selectedOrder.isExperience;
        $scope.orderItemList_copy = angular.copy($scope.orderItemList);
        angular.forEach($scope.orderItemList, function (item) {
            var product = item.product;
            switch (product.subServType) {
                //屏显配额为包月主叫
                case 1:
                    $scope.hasCMCC = true;
                    $scope.hasPX_cmcc = true;
                    $scope.postPingXianCMCC = true;
                    $scope.isPXbaoyue = true;
                    $scope.pingxianisLimit = 1;
                    $scope.pxType = 2;
                    $scope.productName = product.productName;
                    $scope.baoyue_unitPrice = product.unitPrice || 0;
                    $scope.memberCount = product.memberCount;
                    $scope.baoyue_price = 0;
                    $scope.baoyue = item.quantity;
                    $scope.baoyue_temp = item.quantity;
                    $scope.subServType = 1;
                    $scope.px_baoyue_orderItem = item;
                    break;
                //屏显配额为包月被叫
                case 2:
                    //移动跑屏显包月
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        if (product.isLimit === 1) {
                            if (product.chargeType == 2) {
                                $scope.hasCMCC = true;
                                $scope.hasPX_cmcc = true;
                                $scope.postPingXianCMCC = true;
                                $scope.isPXbaoyue = true;
                                $scope.pingxianisLimit = 1;
                                $scope.pxType = 2;
                                $scope.productName = product.productName;
                                $scope.baoyue_unitPrice = product.unitPrice || 0;
                                $scope.memberCount = product.memberCount;
                                $scope.baoyue_price = 0;
                                $scope.baoyue = item.quantity;
                                $scope.baoyue_temp = item.quantity;
                                $scope.subServType = 2;
                                $scope.px_baoyue_orderItem = item;
                            }
                        }
                    }

                    // 广告联通屏显
                    if (product.reservedsEcpmp.reserved1 === "2") {
                        $scope.hasCUCC = true;
                        $scope.hasPX_cucc = true;
                        $scope.postPingXianCUCC = true;
                        $scope.pinxian_amount_cucc = item.product.amount;
                        //屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_cucc = true;
                            $scope.pxType_cucc = 0;
                            $scope.pingxianisLimit_cucc = 0;
                            $scope.px_noLimit_orderItem_cucc = item;
                        }
                        //屏显配额为按次
                        if (product.isLimit === 1) {
                            if (product.chargeType == 1) {
                                $scope.isPXanci_cucc = true;
                                $scope.pxType_cucc = 1;
                                $scope.pingxianisLimit_cucc = 1;
                                $scope.anci_cucc = item.quantity;
                                $scope.anci_temp_cucc = item.quantity;
                                $scope.px_anci_orderItem_cucc = item;
                            }
                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice_cucc = 0;
                        } else {
                            $scope.PXPrice_cucc = product.unitPrice;
                        }
                    }
                    // 广告电信屏显
                    if (product.reservedsEcpmp.reserved1 === "3") {
                        $scope.hasCTCC = true;
                        $scope.hasPX_ctcc = true;
                        $scope.postPingXianCTCC = true;
                        $scope.pinxian_amount_ctcc = item.product.amount;
                        //屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_ctcc = true;
                            $scope.pxType_ctcc = 0;
                            $scope.pingxianisLimit_ctcc = 0;
                            $scope.px_noLimit_orderItem_ctcc = item;
                        }
                        //屏显配额为按次
                        if (product.isLimit === 1) {
                            if (product.chargeType == 1) {
                                $scope.isPXanci_ctcc = true;
                                $scope.pxType_ctcc = 1;
                                $scope.pingxianisLimit_ctcc = 1;
                                $scope.anci_ctcc = item.quantity;
                                $scope.anci_temp_ctcc = item.quantity;
                                $scope.px_anci_orderItem_ctcc = item;
                            }

                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice_ctcc = 0;
                        } else {
                            $scope.PXPrice_ctcc = product.unitPrice;
                        }
                    }

                    break;
                case 3:
                    // 移动
                    if (product.chargeType == 2) {
                        $scope.hasCMCC = true;
                        $scope.hasPX_cmcc = true;
                        $scope.postPingXianCMCC = true;
                        $scope.isPXbaoyue = true;
                        $scope.pingxianisLimit = 1;
                        $scope.pxType = 2;
                        $scope.productName = product.productName;
                        $scope.baoyue_unitPrice = product.unitPrice || 0;
                        $scope.memberCount = product.memberCount;
                        $scope.baoyue_price = 0;
                        $scope.baoyue = item.quantity;
                        $scope.baoyue_temp = item.quantity;
                        $scope.subServType = 2;
                        $scope.px_baoyue_orderItem = item;
                    }
                    else {
                        if (product.reservedsEcpmp.reserved1 === "1") {
                            $scope.hasCMCC = true;
                            $scope.hasPX_cmcc = true;
                            $scope.postPingXianCMCC = true;
                            $scope.pinxian_amount = item.product.amount;
                            //屏显配额为不限
                            if (product.isLimit === 0) {
                                $scope.isPXLimit = true;
                                $scope.pxType = 0;
                                $scope.pingxianisLimit = 0;
                                $scope.px_noLimit_orderItem = item;
                            }
                            //屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.isPXanci = true;
                                $scope.pxType = 1;
                                $scope.pingxianisLimit = 1;
                                $scope.anci = item.quantity;
                                $scope.anci_temp = item.quantity;
                                $scope.px_anci_orderItem = item;
                            }
                            if ($scope.isExperience === 1) {
                                $scope.PXPrice = 0;
                            } else {
                                $scope.PXPrice = product.unitPrice;
                            }
                        }
                        // 联通
                        if (product.reservedsEcpmp.reserved1 === "2") {
                            $scope.hasCUCC = true;
                            $scope.hasPX_cucc = true;
                            $scope.postPingXianCUCC = true;
                            $scope.pinxian_amount_cucc = item.product.amount;
                            //屏显配额为不限
                            if (product.isLimit === 0) {
                                $scope.isPXLimit_cucc = true;
                                $scope.pxType_cucc = 0;
                                $scope.pingxianisLimit_cucc = 0;
                                $scope.px_noLimit_orderItem_cucc = item;
                            }
                            //屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.isPXanci_cucc = true;
                                $scope.pxType_cucc = 1;
                                $scope.pingxianisLimit_cucc = 1;
                                $scope.anci_cucc = item.quantity;
                                $scope.anci_temp_cucc = item.quantity;
                                $scope.px_anci_orderItem_cucc = item;
                            }
                            if ($scope.isExperience === 1) {
                                $scope.PXPrice_cucc = 0;
                            } else {
                                $scope.PXPrice_cucc = product.unitPrice;
                            }
                        }
                        // 电信
                        if (product.reservedsEcpmp.reserved1 === "3") {
                            $scope.hasCTCC = true;
                            $scope.hasPX_ctcc = true;
                            $scope.postPingXianCTCC = true;
                            $scope.pinxian_amount_ctcc = item.product.amount;
                            //屏显配额为不限
                            if (product.isLimit === 0) {
                                $scope.isPXLimit_ctcc = true;
                                $scope.pxType_ctcc = 0;
                                $scope.pingxianisLimit_ctcc = 0;
                                $scope.px_noLimit_orderItem_ctcc = item;
                            }
                            //屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.isPXanci_ctcc = true;
                                $scope.pxType_ctcc = 1;
                                $scope.pingxianisLimit_ctcc = 1;
                                $scope.anci_ctcc = item.quantity;
                                $scope.anci_temp_ctcc = item.quantity;
                                $scope.px_anci_orderItem_ctcc = item;
                            }
                            if ($scope.isExperience === 1) {
                                $scope.PXPrice_ctcc = 0;
                            } else {
                                $scope.PXPrice_ctcc = product.unitPrice;
                            }
                        }
                    }
                    break;
                //挂机短信
                case 4:
                    if (product.reservedsEcpmp.reserved1 === "1") {                 //加if判断 20191107
                        $scope.hasCMCC = true;
                        $scope.hasGD_cmcc = true;
                        $scope.hasGD = true;
                        $scope.postGuaDuan = true;
                        //挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimit = true;
                            $scope.gdType = 0;
                            $scope.guaduanisLimit = 0;
                            $scope.guaduan_noLimit_orderItem = item;
                        }
                        //挂机短信为按次
                        if (product.isLimit === 1) {
                            if (product.chargeType == 2) {
                                $scope.isGDbaoyue = true;
                                $scope.guaduanisLimit = 1;
                                $scope.gdType = 2;
                                $scope.gdproductName = product.productName;
                                $scope.gdbaoyue_unitPrice = product.unitPrice || 0;
                                $scope.gdmemberCount = product.memberCount;
                                $scope.gdbaoyue_price = 0;
                                $scope.gdbaoyue = item.quantity;
                                $scope.gdbaoyue_temp = item.quantity;
                                $scope.gdsubServType = 2;
                                $scope.gd_baoyue_orderItem = item;
                            } else {
                                $scope.guaduan_amount = item.product.amount;
                                $scope.isGDanci = true;
                                $scope.gdType = 1;
                                $scope.guaduanisLimit = 1;
                                $scope.gdanci = item.quantity;
                                $scope.guaduanInput_temp = item.quantity;
                                $scope.guaduan_anci_orderItem = item;
                            }
                        }
                        if ($scope.isExperience === 1) {
                            $scope.GDPrice = 0;
                        } else {
                            $scope.GDPrice = product.unitPrice;
                        }
                    }

                    if (product.reservedsEcpmp.reserved1 === "2") {                 //联通挂短
                        $scope.hasCUCC = true;
                        $scope.hasGD_cmccCUCC = true;
                        $scope.hasGDCUCC = true;
                        $scope.postGuaDuanCUCC = true;
                        //挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimitCUCC = true;
                            $scope.gdTypeCUCC = 0;
                            $scope.guaduanisLimitCUCC = 0;
                            $scope.guaduan_noLimit_orderItemCUCC = item;
                        }
                        //挂机短信为按次
                        if (product.isLimit === 1) {
                            $scope.guaduan_amountCUCC = item.product.amount;
                            $scope.isGDanciCUCC = true;
                            $scope.gdTypeCUCC = 1;
                            $scope.guaduanisLimitCUCC = 1;
                            $scope.gdanciCUCC = item.quantity;
                            $scope.guaduanInput_tempCUCC = item.quantity;
                            $scope.guaduan_anci_orderItemCUCC = item;

                        }
                        if ($scope.isExperience === 1) {
                            $scope.GDPriceCUCC = 0;
                        } else {
                            $scope.GDPriceCUCC = product.unitPrice;
                        }
                    }

                      if (product.reservedsEcpmp.reserved1 === "3") {                 //电信挂短
                        $scope.hasCTCC = true;
                        $scope.hasGD_cmccCTCC = true;
                        $scope.hasGDCTCC = true;
                        $scope.postGuaDuanCTCC = true;
                        //挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimitCTCC = true;
                            $scope.gdTypeCTCC = 0;
                            $scope.guaduanisLimitCTCC = 0;
                            $scope.guaduan_noLimit_orderItemCTCC = item;
                        }
                        //挂机短信为按次
                        if (product.isLimit === 1) {
                            $scope.guaduan_amountCTCC = item.product.amount;
                            $scope.isGDanciCTCC = true;
                            $scope.gdTypeCTCC = 1;
                            $scope.guaduanisLimitCTCC = 1;
                            $scope.gdanciCTCC = item.quantity;
                            $scope.guaduanInput_tempCTCC = item.quantity;
                            $scope.guaduan_anci_orderItemCTCC = item;

                        }
                        if ($scope.isExperience === 1) {
                            $scope.GDPriceCTCC = 0;
                        } else {
                            $scope.GDPriceCTCC = product.unitPrice;
                        }
                    }

                    break;
                case 8:
                    $scope.hasCMCC = true;
                    $scope.hasGC_cmcc = true;
                    $scope.hasGC = true;
                    $scope.postGuaCai = true;
                    //挂机彩信为不限
                    if (product.isLimit === 0) {
                        $scope.isGCLimit = true;
                        $scope.gcType = 0;
                        $scope.guacaiisLimit = 0;
                        $scope.guacai_noLimit_orderItem = item;
                    }
                    //挂机彩信为按次
                    if (product.isLimit === 1) {
                        if (product.chargeType == 2) {
                            $scope.isGCbaoyue = true;
                            $scope.guacaiisLimit = 1;
                            $scope.gcType = 2;
                            $scope.gcproductName = product.productName;
                            $scope.gcbaoyue_unitPrice = product.unitPrice || 0;
                            $scope.gcmemberCount = product.memberCount;
                            $scope.gcbaoyue_price = 0;
                            $scope.gcbaoyue = item.quantity;
                            $scope.gcbaoyue_temp = item.quantity;
                            $scope.gcsubServType = 2;
                            $scope.gc_baoyue_orderItem = item;

                        } else {
                            $scope.guacai_amount = item.product.amount;
                            $scope.isGCanci = true;
                            $scope.guacaiisLimit = 1;
                            $scope.gcType = 1;
                            $scope.gcanci = item.quantity;
                            $scope.guacaiInput_temp = item.quantity;
                            $scope.guacai_anci_orderItem = item;
                        }
                    }
                    if ($scope.isExperience === 1) {
                        $scope.GCPrice = 0;
                    } else {
                        $scope.GCPrice = product.unitPrice;
                    }
                    break;
                case 10:
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        // 移动
                        $scope.hasCMCC = true;
                        $scope.hasPX_cmcc = true;
                        $scope.postPingXianCMCC = true;
                        $scope.pinxian_amount = item.product.amount;
                        //屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit = true;
                            $scope.pxType = 0;
                            $scope.pingxianisLimit = 0;
                            $scope.px_noLimit_orderItem = item;
                        }
                        //屏显配额为按次
                        if (product.isLimit === 1) {
                            $scope.isPXanci = true;
                            $scope.pxType = 1;
                            $scope.pingxianisLimit = 1;
                            $scope.anci = item.quantity;
                            $scope.anci_temp = item.quantity;
                            $scope.px_anci_orderItem = item;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice = 0;
                        } else {
                            $scope.PXPrice = product.unitPrice;
                        }
                    }

                    break;
                //增彩群发
                case 16:
                    $scope.hasCMCC = true;
                    $scope.hasZC_cmcc = true;
                    $scope.hasZC = true;
                    $scope.postZC = true;
                    $scope.zengcai_orderItem = item;
                    //不限
                    if (product.isLimit === 0) {
                        $scope.isZCLimit = true;
                        $scope.zcType = 0;
                    }
                    //按次
                    if (product.isLimit === 1) {
                            $scope.zengcai_amount = item.product.amount;
                            $scope.isZCanci = true;
                            $scope.zcType = 1;
                            $scope.zengcaiIsLimit = 1;
                            $scope.zcanci = item.quantity;
                           $scope.zengcaiInput_temp = item.quantity;
                    }
                    if ($scope.isExperience === 1) {
                        $scope.ZCPrice = 0;
                    } else {
                        $scope.ZCPrice = product.unitPrice;
                    }
                    break;
                //增彩群发
                case 17:
                    //移动
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        $scope.GroupSendSMSCMCC_orderItem = item;
                        $scope.hasCMCC = true;
                        $scope.hasGroupSendSMSCMCC = true;
                        if (product.isLimit == 1) {
                            $scope.groupSendSMSCMCCAmount = product.amount;
                            $scope.groupSendSMSCMCCNoLimit = false;
                            $scope.groupSendSMSCMCCanci = item.quantity;
                            $scope.groupSendSMSCMCC_temp = item.quantity;

                        } else {
                            $scope.groupSendSMSCMCCNoLimit = true;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.groupSendSMSCMCCPrice = 0;
                        } else {
                            $scope.groupSendSMSCMCCPrice = product.unitPrice;
                        }

                    }
                    else if(product.reservedsEcpmp.reserved1 === "2"){
                        $scope.GroupSendSMSCUCC_orderItem = item;
                        $scope.hasCUCC = true;
                        $scope.hasGroupSendSMSCUCC = true;
                         if (product.isLimit == 1) {
                        $scope.groupSendSMSCUCCAmount = product.amount;
                        $scope.groupSendSMSCUCCNoLimit = false;
                        $scope.groupSendSMSCUCCanci = item.quantity;
                        $scope.groupSendSMSCUCC_temp = item.quantity;
                         } else {
                             $scope.groupSendSMSCUCCNoLimit = true;
                         }
                        if ($scope.isExperience === 1) {
                            $scope.groupSendSMSCUCCPrice = 0;
                        } else {
                            $scope.groupSendSMSCUCCPrice = product.unitPrice;
                        }
                    }else if(product.reservedsEcpmp.reserved1 === "3"){
                        $scope.GroupSendSMSCTCC_orderItem = item;
                        $scope.hasCTCC = true;
                        $scope.hasGroupSendSMSCTCC = true;
                         if (product.isLimit == 1) {
                        $scope.groupSendSMSCTCCAmount = product.amount;
                        $scope.groupSendSMSCTCCNoLimit = false;
                        $scope.groupSendSMSCTCCanci = item.quantity;
                        $scope.groupSendSMSCTCC_temp = item.quantity;
                         } else {
                             $scope.groupSendSMSCTCCNoLimit = true;
                         }
                        if ($scope.isExperience === 1) {
                            $scope.groupSendSMSCTCCPrice = 0;
                        } else {
                            $scope.groupSendSMSCTCCPrice = product.unitPrice;
                        }
                    }
                    break;
                default:
                    break;
            }
        })
        $scope.querySubscribeList(selectedOrder);
    };

    //给弹出框确认按钮绑定不同事件
    $scope.sure = function (eventType) {
        if (eventType === 1) {
            window.location.href = "../quotaList/quotaList.html"
        }
    };

    //解决小数相乘时  丢失精度的问题
    $scope.accMul = function (arg1, arg2, arg3, arg4, arg5) {
        var arg2_temp = angular.copy(arg2);
        if ("undefined" == typeof arg1) {
            arg1 = 0;
        }
        if ("undefined" == typeof arg2) {
            arg2_temp = 0;
        }
        if ("undefined" == typeof arg3) {
            arg3 = 0;
        }
        if ("undefined" == typeof arg4) {
            arg4 = 0;
        }
        var m = 0;
        var s1 = arg1.toString();
        var s2 = (arg2_temp - arg4).toString();
        var s3 = arg3.toString();
        try {
            m += s1.split(".")[1].length
        } catch (e) {
        }
        try {
            m += s2.split(".")[1].length
        } catch (e) {
        }
        try {
            m += s3.split(".")[1].length
        } catch (e) {
        }

        if (arg5 === "pxanci") {
            if (s2 < 0) {
                $scope.pxanci_price = 0;
            } else {
                $scope.pxanci_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }
            if ("undefined" == typeof arg2) {
                $scope.px_anci_over_error_cmcc = false;
                $scope.px_anci_lower_error_cmcc = false;
            } else {
                //判断剩余配额
                $scope.px_anci_over_error_cmcc = s2 > $scope.px_anciRest_cmcc;
                $scope.px_anci_lower_error_cmcc = arg2_temp < Number($scope.px_anciMin_cmcc);
            }
        }
        if (arg5 === "pxanci_cucc") {
            if (s2 < 0) {
                $scope.pxanci_price_cucc = 0;
            } else {
                $scope.pxanci_price_cucc = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }
            if ("undefined" == typeof arg2) {
                $scope.px_anci_over_error_cucc = false;
                $scope.px_anci_lower_error_cucc = false;
            } else {
                //判断剩余配额
                $scope.px_anci_over_error_cucc = s2 > $scope.px_anciRest_cucc;
                $scope.px_anci_lower_error_cucc = arg2_temp < Number($scope.px_anciMin_cucc);
            }
        }
        if (arg5 === "pxanci_ctcc") {
            if (s2 < 0) {
                $scope.pxanci_price_ctcc = 0;
            } else {
                $scope.pxanci_price_ctcc = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }
            if ("undefined" == typeof arg2) {
                $scope.px_anci_over_error_ctcc = false;
                $scope.px_anci_lower_error_ctcc = false;
            } else {
                //判断剩余配额
                $scope.px_anci_over_error_ctcc = s2 > $scope.px_anciRest_ctcc;
                $scope.px_anci_lower_error_ctcc = arg2_temp < Number($scope.px_anciMin_ctcc);
            }
        }


        if (arg5 === "baoyue") {
            if (s2 < 0) {
                $scope.baoyue_price = 0;
            } else {
                $scope.baoyue_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }
            if ("undefined" == typeof arg2) {
                $scope.baoyue_over_error = false;
                $scope.baoyue_lower_error = false;
            } else {
                //判断剩余配额
                $scope.baoyue_over_error = s2 > $scope.baoyueRest;
                $scope.baoyue_lower_error = arg2_temp < Number($scope.baoyueMin);
            }
        }


        if (arg5 === "gdanci") {
            if ("undefined" == typeof arg2) {
                $scope.guaduan_anci_over_error = false;
                $scope.guaduan_anci_lower_error = false;
            } else {
                //判断剩余配额
                $scope.guaduan_anci_over_error = s2 > $scope.guaduan_anciRest;
                $scope.guaduan_anci_lower_error = arg2_temp < Number($scope.guaduan_anciMin);
            }
            if (s2 < 0) {
                $scope.guaduan_price = 0;
            } else {
                $scope.guaduan_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }

        }

        //联通挂短按次
        if (arg5 === "gdanciCUCC") {
            if ("undefined" == typeof arg2) {
                $scope.guaduan_anci_over_errorCUCC = false;
                $scope.guaduan_anci_lower_errorCUCC = false;
            } else {
                //判断剩余配额
                $scope.guaduan_anci_over_errorCUCC = s2 > $scope.guaduan_anciRestCUCC;
                $scope.guaduan_anci_lower_errorCUCC = arg2_temp < Number($scope.guaduan_anciMinCUCC);
            }
            if (s2 < 0) {
                $scope.guaduan_priceCUCC = 0;
            } else {
                $scope.guaduan_priceCUCC = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }

        }
        if (arg5 === "zcanci") {
            if ("undefined" == typeof arg2) {
                $scope.zengcai_anci_over_error = false;
                $scope.zengcai_anci_lower_error = false;
            } else {
                //判断剩余配额
                $scope.zengcai_anci_over_error = s2 > $scope.zengcai_anciRest;
                $scope.zengcai_anci_lower_error = arg2_temp < Number($scope.zengcai_anciMin);
            }
            if (s2 < 0) {
                $scope.zengcai_price = 0;
            } else {
                $scope.zengcai_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }

        }
        if (arg5 === "groupSendSMSCMCC") {
            if ("undefined" == typeof arg2) {
                $scope.groupSendSMSCMCC_anci_over_error = false;
                $scope.groupSendSMSCMCC_lower_error = false;
            } else {
                //判断剩余配额
                $scope.groupSendSMSCMCC_anci_over_error = s2 > $scope.groupSendSMSCMCC_anciRest;
                $scope.groupSendSMSCMCC_lower_error = arg2_temp < Number($scope.groupSendSMSCMCC_anciMin);
            }
            if (s2 < 0) {
                $scope.groupSendSMSCMCC_Price = 0;
            } else {
                $scope.groupSendSMSCMCC_Price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }

        }
        if (arg5 === "groupSendSMSCUCC") {
            if ("undefined" == typeof arg2) {
                $scope.groupSendSMSCUCC_anci_over_error = false;
                $scope.groupSendSMSCUCC_lower_error = false;
            } else {
                //判断剩余配额
                $scope.groupSendSMSCUCC_anci_over_error = s2 > $scope.groupSendSMSCUCC_anciRest;
                $scope.groupSendSMSCUCC_lower_error = arg2_temp < Number($scope.groupSendSMSCUCC_anciMin);
            }
            if (s2 < 0) {
                $scope.groupSendSMSCUCC_Price = 0;
            } else {
                $scope.groupSendSMSCUCC_Price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }

        }
        if (arg5 === "groupSendSMSCTCC") {
            if ("undefined" == typeof arg2) {
                $scope.groupSendSMSCTCC_anci_over_error = false;
                $scope.groupSendSMSCTCC_lower_error = false;
            } else {
                //判断剩余配额
                $scope.groupSendSMSCTCC_anci_over_error = s2 > $scope.groupSendSMSCTCC_anciRest;
                $scope.groupSendSMSCTCC_lower_error = arg2_temp < Number($scope.groupSendSMSCTCC_anciMin);
            }
            if (s2 < 0) {
                $scope.groupSendSMSCTCC_Price = 0;
            } else {
                $scope.groupSendSMSCTCC_Price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }

        }

        //电信挂短按次
        if (arg5 === "gdanciCTCC") {
            if ("undefined" == typeof arg2) {
                $scope.guaduan_anci_over_errorCTCC = false;
                $scope.guaduan_anci_lower_errorCTCC = false;
            } else {
                //判断剩余配额
                $scope.guaduan_anci_over_errorCTCC = s2 > $scope.guaduan_anciRestCTCC;
                $scope.guaduan_anci_lower_errorCTCC = arg2_temp < Number($scope.guaduan_anciMinCTCC);
            }
            if (s2 < 0) {
                $scope.guaduan_priceCTCC = 0;
            } else {
                $scope.guaduan_priceCTCC = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }

        }

        if (arg5 === "gdbaoyue") {
            if (s2 < 0) {
                $scope.gdbaoyue_price = 0;
            } else {
                $scope.gdbaoyue_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }
            if ("undefined" == typeof arg2) {
                $scope.gdbaoyue_over_error = false;
                $scope.gdbaoyue_lower_error = false;
            } else {
                //判断剩余配额
                $scope.gdbaoyue_over_error = s2 > $scope.gdbaoyueRest;
                $scope.gdbaoyue_lower_error = arg2_temp < Number($scope.gdbaoyueMin);
            }
        }

        if (arg5 === "gcanci") {
            if ("undefined" == typeof arg2) {
                $scope.guacai_anci_over_error = false;
                $scope.guacai_anci_lower_error = false;
            } else {
                //判断剩余配额
                $scope.guacai_anci_over_error = s2 > $scope.guacai_anciRest;
                $scope.guacai_anci_lower_error = arg2_temp < Number($scope.guacai_anciMin);
            }
            if (s2 < 0) {
                $scope.guacai_price = 0;
            } else {
                $scope.guacai_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }
        }

        if (arg5 === "gcbaoyue") {
            if (s2 < 0) {
                $scope.gcbaoyue_price = 0;
            } else {
                $scope.gcbaoyue_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            }
            if ("undefined" == typeof arg2) {
                $scope.gcbaoyue_over_error = false;
                $scope.gcbaoyue_lower_error = false;
            } else {
                //判断剩余配额
                $scope.gcbaoyue_over_error = s2 > $scope.gcbaoyueRest;
                $scope.gcbaoyue_lower_error = arg2_temp < Number($scope.gcbaoyueMin);
            }
        }

    };

    //提交配额
    $scope.updateOrder = function () {
        $('#formSub').attr('disabled', 'true');
        var req = {
            "order": {
                "orderCode": $scope.orderCode,
                "objectID": $scope.objectID,
                "orderType": 1,
                "amount": $scope.orderInfo.amount,
                "enterpriseID": $scope.subEnterpriseID,
                "enterpriseName": $scope.subEnterpriseName,
                "servType": $scope.servType,
                "payStatus": 3,
                "effictiveTime": $scope.effictiveTime,
                "isExperience": $scope.orderInfo.isExperience,
                "expireTime": $scope.expireTime,
                "operatorID": $scope.operatorID,
                "orderItemList": [],
                "extInfo": {
                    "quotaOrderID": $scope.orderInfo.extInfo.quotaOrderID,
                    "quotaOrderName": $scope.orderInfo.extInfo.quotaOrderName,
                    "creatorID": $scope.orderInfo.extInfo.creatorID,
                    "editorID": $scope.accountName
                }
            }
        };
        angular.forEach($scope.orderItemList_copy, function (item) {
            var subServType = item.product.subServType;
            var isLimit = item.product.isLimit;
            var telecomsOperator = item.product.reservedsEcpmp.reserved1;


            if (subServType === 1 || subServType === 2) {
                if (telecomsOperator === "1") {
                    if (isLimit === 1) {
                        //判断剩余配额
                        $scope.baoyue_over_error = $scope.baoyue - $scope.baoyue_temp > $scope.baoyueRest;
                        req.order.orderItemList.push({
                            "objectID": $scope.px_baoyue_orderItem.objectID,
                            "orderItemCode": $scope.px_baoyue_orderItem.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.px_baoyue_orderItem.productID,
                            "product": $scope.px_baoyue_orderItem.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.baoyue_unitPrice,
                            "quantity": $scope.baoyue,
                            "totalAmount": $scope.baoyue_price
                        });
                    }
                }
                if (telecomsOperator === "2") {
                    if (isLimit === 1) {
                        $scope.px_anci_over_error_cucc = $scope.anci_cucc - $scope.anci_temp_cucc > $scope.px_anciRest_cucc;
                        req.order.orderItemList.push({
                            "objectID": $scope.px_anci_orderItem_cucc.objectID,
                            "orderItemCode": $scope.px_anci_orderItem_cucc.orderItemCode,
                            "orderID": $scope.px_anci_orderItem_cucc.orderID,
                            "productID": $scope.px_anci_orderItem_cucc.productID,
                            "product": $scope.px_anci_orderItem_cucc.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.PXPrice_cucc,
                            "quantity": $scope.anci_cucc,
                            "totalAmount": $scope.pxanci_price_cucc
                        });
                    }
                }
                if (telecomsOperator === "3") {
                    if (isLimit === 1) {
                        $scope.px_anci_over_error_ctcc = $scope.anci_ctcc - $scope.anci_temp_ctcc > $scope.px_anciRest_ctcc;
                        req.order.orderItemList.push({
                            "objectID": $scope.px_anci_orderItem_ctcc.objectID,
                            "orderItemCode": $scope.px_anci_orderItem_ctcc.orderItemCode,
                            "orderID": $scope.px_anci_orderItem_ctcc.orderID,
                            "productID": $scope.px_anci_orderItem_ctcc.productID,
                            "product": $scope.px_anci_orderItem_ctcc.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.PXPrice_ctcc,
                            "quantity": $scope.anci_ctcc,
                            "totalAmount": $scope.pxanci_price_ctcc
                        });
                    }
                }
            }
            if (subServType === 3) {
                if (telecomsOperator === "1") {
                    if (isLimit === 1) {
                        //判断剩余配额
                        if ($scope.pxType === 2)
                        {
                            req.order.orderItemList.push({
                                "objectID": $scope.px_baoyue_orderItem.objectID,
                                "orderItemCode": $scope.px_baoyue_orderItem.orderItemCode,
                                "orderID": $scope.orderInfo.orderCode,
                                "productID": $scope.px_baoyue_orderItem.productID,
                                "product": $scope.px_baoyue_orderItem.product,
                                "operatorID": $scope.operatorID,
                                "unitPrice": $scope.baoyue_unitPrice,
                                "quantity": $scope.baoyue,
                                "totalAmount": $scope.baoyue_price
                            });
                        }
                        else {
                            $scope.px_anci_over_error_cmcc = $scope.anci - $scope.anci_temp > $scope.px_anciRest_cmcc;
                                req.order.orderItemList.push({
                                    "objectID": $scope.px_anci_orderItem.objectID,
                                    "orderItemCode": $scope.px_anci_orderItem.orderItemCode,
                                    "orderID": $scope.px_anci_orderItem.orderID,
                                    "productID": $scope.px_anci_orderItem.productID,
                                    "product": $scope.px_anci_orderItem.product,
                                    "operatorID": $scope.operatorID,
                                    "unitPrice": $scope.PXPrice,
                                    "quantity": $scope.anci,
                                    "totalAmount": $scope.pxanci_price
                                });
                        }



                    } else {
                        req.order.orderItemList.push($scope.px_noLimit_orderItem);

                    }
                }
                if (telecomsOperator === "2") {
                    if (isLimit === 1) {
                        $scope.px_anci_over_error_cucc = $scope.anci_cucc - $scope.anci_temp_cucc > $scope.px_anciRest_cucc;
                        req.order.orderItemList.push({
                            "objectID": $scope.px_anci_orderItem_cucc.objectID,
                            "orderItemCode": $scope.px_anci_orderItem_cucc.orderItemCode,
                            "orderID": $scope.px_anci_orderItem_cucc.orderID,
                            "productID": $scope.px_anci_orderItem_cucc.productID,
                            "product": $scope.px_anci_orderItem_cucc.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.PXPrice_cucc,
                            "quantity": $scope.anci_cucc,
                            "totalAmount": $scope.pxanci_price_cucc
                        });
                    } else {
                        req.order.orderItemList.push($scope.px_noLimit_orderItem_cucc);
                    }
                }
                if (telecomsOperator === "3") {
                    if (isLimit === 1) {
                        $scope.px_anci_over_error_ctcc = $scope.anci_ctcc - $scope.anci_temp_ctcc > $scope.px_anciRest_ctcc;
                        req.order.orderItemList.push({
                            "objectID": $scope.px_anci_orderItem_ctcc.objectID,
                            "orderItemCode": $scope.px_anci_orderItem_ctcc.orderItemCode,
                            "orderID": $scope.px_anci_orderItem_ctcc.orderID,
                            "productID": $scope.px_anci_orderItem_ctcc.productID,
                            "product": $scope.px_anci_orderItem_ctcc.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.PXPrice_ctcc,
                            "quantity": $scope.anci_ctcc,
                            "totalAmount": $scope.pxanci_price_ctcc
                        });
                    } else {
                        req.order.orderItemList.push($scope.px_noLimit_orderItem_ctcc);
                    }
                }
            }

            if (subServType === 10) {
                if (telecomsOperator === "1") {
                    if (isLimit === 1) {
                        //判断剩余配额
                        //判断剩余配额
                        $scope.px_anci_over_error_cmcc = $scope.anci - $scope.anci_temp > $scope.px_anciRest_cmcc;
                        req.order.orderItemList.push({
                            "objectID": $scope.px_anci_orderItem.objectID,
                            "orderItemCode": $scope.px_anci_orderItem.orderItemCode,
                            "orderID": $scope.px_anci_orderItem.orderID,
                            "productID": $scope.px_anci_orderItem.productID,
                            "product": $scope.px_anci_orderItem.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.PXPrice,
                            "quantity": $scope.anci,
                            "totalAmount": $scope.pxanci_price
                        });
                    }
                }

            }

            if (subServType === 4) {
                if (telecomsOperator === "1") {
                    //移动挂短
                    $scope.guaduan_anci_over_error = $scope.gdanci - $scope.guaduanInput_temp > $scope.guaduan_anciRest;
                    if ($scope.gdType === 1) {
                        //按次
                        req.order.orderItemList.push({
                            "objectID": $scope.guaduan_anci_orderItem.objectID,
                            "orderItemCode": $scope.guaduan_anci_orderItem.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.guaduan_anci_orderItem.productID,
                            "product": $scope.guaduan_anci_orderItem.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.GDPrice,
                            "quantity": $scope.gdanci,
                            "totalAmount": $scope.guaduan_price
                        });
                    }
                    //挂短包月
                    if ($scope.gdType === 2) {

                    $scope.gdbaoyue_over_error = $scope.gdbaoyue - $scope.gdbaoyue_temp > $scope.gdbaoyueRest;
                    req.order.orderItemList.push({
                        "objectID": $scope.gd_baoyue_orderItem.objectID,
                        "orderItemCode": $scope.gd_baoyue_orderItem.orderItemCode,
                        "orderID": $scope.orderInfo.orderCode,
                        "productID": $scope.gd_baoyue_orderItem.productID,
                        "product": $scope.gd_baoyue_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.gdbaoyue_unitPrice,
                        "quantity": $scope.gdbaoyue,
                        "totalAmount": $scope.gdbaoyue_price
                    });
                }
                if ($scope.gdType === 0) {
                    //不限
                    req.order.orderItemList.push({
                        "objectID": $scope.guaduan_noLimit_orderItem.objectID,
                        "orderItemCode": $scope.guaduan_noLimit_orderItem.orderItemCode,
                        "orderID": $scope.orderInfo.orderCode,
                        "productID": $scope.guaduan_noLimit_orderItem.productID,
                        "product": $scope.guaduan_noLimit_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0
                    });
                }
            }

                if (telecomsOperator === "2") {
                    //联通挂短
                    $scope.guaduan_anci_over_errorCUCC = $scope.gdanciCUCC - $scope.guaduanInput_tempCUCC > $scope.guaduan_anciRestCUCC;
                    if ($scope.gdTypeCUCC === 1) {
                        //按次
                        req.order.orderItemList.push({
                            "objectID": $scope.guaduan_anci_orderItemCUCC.objectID,
                            "orderItemCode": $scope.guaduan_anci_orderItemCUCC.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.guaduan_anci_orderItemCUCC.productID,
                            "product": $scope.guaduan_anci_orderItemCUCC.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.GDPriceCUCC,
                            "quantity": $scope.gdanciCUCC,
                            "totalAmount": $scope.guaduan_priceCUCC
                        });
                    }
                    if ($scope.gdTypeCUCC === 0) {
                        //不限
                        req.order.orderItemList.push({
                            "objectID": $scope.guaduan_noLimit_orderItemCUCC.objectID,
                            "orderItemCode": $scope.guaduan_noLimit_orderItemCUCC.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.guaduan_noLimit_orderItemCUCC.productID,
                            "product": $scope.guaduan_noLimit_orderItemCUCC.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": 1,
                            "totalAmount": 0
                        });
                    }
                }

                if (telecomsOperator === "3") {
                    //电信挂短
                    $scope.guaduan_anci_over_errorCTCC = $scope.gdanciCTCC - $scope.guaduanInput_tempCTCC > $scope.guaduan_anciRestCTCC;
                    if ($scope.gdTypeCTCC === 1) {
                        //按次
                        req.order.orderItemList.push({
                            "objectID": $scope.guaduan_anci_orderItemCTCC.objectID,
                            "orderItemCode": $scope.guaduan_anci_orderItemCTCC.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.guaduan_anci_orderItemCTCC.productID,
                            "product": $scope.guaduan_anci_orderItemCTCC.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.GDPriceCTCC,
                            "quantity": $scope.gdanciCTCC,
                            "totalAmount": $scope.guaduan_priceCTCC
                        });
                    }
                    if ($scope.gdTypeCTCC === 0) {
                        //不限
                        req.order.orderItemList.push({
                            "objectID": $scope.guaduan_noLimit_orderItemCTCC.objectID,
                            "orderItemCode": $scope.guaduan_noLimit_orderItemCTCC.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.guaduan_noLimit_orderItemCTCC.productID,
                            "product": $scope.guaduan_noLimit_orderItemCTCC.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": 1,
                            "totalAmount": 0
                        });
                    }
                }


            }

            if (subServType === 8) {
                $scope.guacai_anci_over_error = $scope.gcanci - $scope.guacaiInput_temp > $scope.guacai_anciRest;
                if ($scope.gcType == 1) {

                    //按次
                    req.order.orderItemList.push({
                        "objectID": $scope.guacai_anci_orderItem.objectID,
                        "orderItemCode": $scope.guacai_anci_orderItem.orderItemCode,
                        "orderID": $scope.orderInfo.orderCode,
                        "productID": $scope.guacai_anci_orderItem.productID,
                        "product": $scope.guacai_anci_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.GCPrice,
                        "quantity": $scope.gcanci,
                        "totalAmount": $scope.guacai_price
                    });
                } //挂短包月
                if ($scope.gcType === 2) {
                    $scope.gcbaoyue_over_error = $scope.gcbaoyue - $scope.gcbaoyue_temp > $scope.gcbaoyueRest;
                    req.order.orderItemList.push({
                        "objectID": $scope.gc_baoyue_orderItem.objectID,
                        "orderItemCode": $scope.gc_baoyue_orderItem.orderItemCode,
                        "orderID": $scope.orderInfo.orderCode,
                        "productID": $scope.gc_baoyue_orderItem.productID,
                        "product": $scope.gc_baoyue_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.gcbaoyue_unitPrice,
                        "quantity": $scope.gcbaoyue,
                        "totalAmount": $scope.gcbaoyue_price
                    });
                }
                if ($scope.gcType === 0) {
                    req.order.orderItemList.push({
                        "objectID": $scope.guacai_noLimit_orderItem.objectID,
                        "orderItemCode": $scope.guacai_noLimit_orderItem.orderItemCode,
                        "orderID": $scope.orderInfo.orderCode,
                        "productID": $scope.guacai_noLimit_orderItem.productID,
                        "product": $scope.guacai_noLimit_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0
                    });
                }
            }
          //增彩
          if (subServType === 16) {
             $scope.zengcai_anci_over_error = $scope.zcanci - $scope.zengcaiInput_temp > $scope.zengcai_anciRest;
             if ($scope.zcType === 1) {
                 //按次
                 req.order.orderItemList.push({
                     "objectID": $scope.zengcai_orderItem.objectID,
                     "orderItemCode": $scope.zengcai_orderItem.orderItemCode,
                     "orderID": $scope.orderInfo.orderCode,
                     "productID": $scope.zengcai_orderItem.productID,
                     "product": $scope.zengcai_orderItem.product,
                     "operatorID": $scope.operatorID,
                     "unitPrice": $scope.ZCPrice,
                     "quantity": $scope.zcanci,
                     "totalAmount": $scope.zengcai_price
                 });
             }
             if ($scope.zcType === 0) {
                 //不限
                 req.order.orderItemList.push({
                      "objectID": $scope.zengcai_orderItem.objectID,
                      "orderItemCode": $scope.zengcai_orderItem.orderItemCode,
                      "orderID": $scope.orderInfo.orderCode,
                      "productID": $scope.zengcai_orderItem.productID,
                      "product": $scope.zengcai_orderItem.product,
                      "operatorID": $scope.operatorID,
                      "unitPrice": 0,
                      "quantity": 1,
                      "totalAmount": 0
                  });
              }
          }

            //群发短信
            if (subServType === 17) {
                if (telecomsOperator === "1") {
                $scope.groupSendSMSCMCC_anci_over_error = $scope.groupSendSMSCMCCanci - $scope.groupSendSMSCMCC_temp > $scope.groupSendSMSCMCC_anciRest;
                if (!$scope.groupSendSMSCMCCNoLimit) {
                    //按次
                    req.order.orderItemList.push({
                        "objectID": $scope.GroupSendSMSCMCC_orderItem.objectID,
                        "orderItemCode": $scope.GroupSendSMSCMCC_orderItem.orderItemCode,
                        "orderID": $scope.orderInfo.orderCode,
                        "productID": $scope.GroupSendSMSCMCC_orderItem.productID,
                        "product": $scope.GroupSendSMSCMCC_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.groupSendSMSCMCCPrice,
                        "quantity": $scope.groupSendSMSCMCCanci,
                        "totalAmount": $scope.groupSendSMSCMCC_Price
                    });
                }
                if ($scope.groupSendSMSCMCCNoLimit) {
                    //不限
                    req.order.orderItemList.push({
                        "objectID": $scope.GroupSendSMSCMCC_orderItem.objectID,
                        "orderItemCode": $scope.GroupSendSMSCMCC_orderItem.orderItemCode,
                        "orderID": $scope.orderInfo.orderCode,
                        "productID": $scope.GroupSendSMSCMCC_orderItem.productID,
                        "product": $scope.GroupSendSMSCMCC_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0
                    });
                }
                }else if (telecomsOperator === "2") {
                    $scope.groupSendSMSCUCC_anci_over_error = $scope.groupSendSMSCUCCanci - $scope.groupSendSMSCUCC_temp > $scope.groupSendSMSCUCC_anciRest;
                    if (!$scope.groupSendSMSCUCCNoLimit) {
                        //按次
                        req.order.orderItemList.push({
                            "objectID": $scope.GroupSendSMSCUCC_orderItem.objectID,
                            "orderItemCode": $scope.GroupSendSMSCUCC_orderItem.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.GroupSendSMSCUCC_orderItem.productID,
                            "product": $scope.GroupSendSMSCUCC_orderItem.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.groupSendSMSCUCCPrice,
                            "quantity": $scope.groupSendSMSCUCCanci,
                            "totalAmount": $scope.groupSendSMSCUCC_Price
                        });
                    }
                    if ($scope.groupSendSMSCUCCNoLimit) {
                        //不限
                        req.order.orderItemList.push({
                            "objectID": $scope.GroupSendSMSCUCC_orderItem.objectID,
                            "orderItemCode": $scope.GroupSendSMSCUCC_orderItem.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.GroupSendSMSCUCC_orderItem.productID,
                            "product": $scope.GroupSendSMSCUCC_orderItem.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": 1,
                            "totalAmount": 0
                        });
                    }
                }else if (telecomsOperator === "3") {
                    $scope.groupSendSMSCTCC_anci_over_error = $scope.groupSendSMSCTCCanci - $scope.groupSendSMSCTCC_temp > $scope.groupSendSMSCTCC_anciRest;
                    if (!$scope.groupSendSMSCTCCNoLimit) {
                        //按次
                        req.order.orderItemList.push({
                            "objectID": $scope.GroupSendSMSCTCC_orderItem.objectID,
                            "orderItemCode": $scope.GroupSendSMSCTCC_orderItem.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.GroupSendSMSCTCC_orderItem.productID,
                            "product": $scope.GroupSendSMSCTCC_orderItem.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.groupSendSMSCTCCPrice,
                            "quantity": $scope.groupSendSMSCTCCanci,
                            "totalAmount": $scope.groupSendSMSCTCC_Price
                        });
                    }
                    if ($scope.groupSendSMSCTCCNoLimit) {
                        //不限
                        req.order.orderItemList.push({
                            "objectID": $scope.GroupSendSMSCTCC_orderItem.objectID,
                            "orderItemCode": $scope.GroupSendSMSCTCC_orderItem.orderItemCode,
                            "orderID": $scope.orderInfo.orderCode,
                            "productID": $scope.GroupSendSMSCTCC_orderItem.productID,
                            "product": $scope.GroupSendSMSCTCC_orderItem.product,
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": 1,
                            "totalAmount": 0
                        });
                    }
                }
            }
        });

        if ($scope.px_anci_over_error_cmcc || $scope.px_anci_over_error_cucc || $scope.px_anci_over_error_ctcc || $scope.baoyue_over_error || $scope.gdbaoyue_over_error || $scope.gcbaoyue_over_error || $scope.guaduan_anci_over_error || $scope.guacai_anci_over_error || $scope.guaduan_anci_over_errorCUCC || $scope.zengcai_anci_over_error) {
            $scope.tip = "剩余配额有更新，请重新填写";
            $('#myModal').modal();
            return;
        }
        if ($scope.px_anci_lower_error_cmcc || $scope.px_anci_lower_error_cucc || $scope.px_anci_lower_error_ctcc || $scope.baoyue_lower_error || $scope.gdbaoyue_lower_error || $scope.gcbaoyue_lower_error || $scope.guacai_anci_lower_error || $scope.zengcai_anci_lower_error) {
            $scope.tip = "配额不能低于当前已使用数量，请重新填写";
            $('#myModal').modal();
            return;
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/updateOrder",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {

                        $scope.tip = "UPDATE_ORDER_SUCCESS_MSG";
                        $('#myModal').modal();

                        setTimeout(function () {
                            location.href = '../quotaList/quotaList.html';
                        }, 1000);
                    }
                    else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });

    };

    //返回按钮的处理
    $scope.goBack = function () {
        //跳转到上一层
        location.href = '../quotaList/quotaList.html';
    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

});

app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])
app.filter("newdate", function () {
    return function (date, type) {
        if (!date) {
            return '';
        }
        if (type === "ymd") {
            return date.substr(0, 4) + '-' + date.substr(4, 2) + '-' + date.substr(6, 2);
        }
        if (type === "ymdhm") {
            return date.substr(0, 4) + '-' + date.substr(4, 2) + '-' + date.substr(6, 2) + " " + date.substr(8, 2) + ':' + date.substr(10, 2)
        }
    }
});