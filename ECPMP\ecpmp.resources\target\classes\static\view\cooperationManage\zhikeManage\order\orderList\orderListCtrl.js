var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n"])
app.controller('orderListController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        if (!$scope.isSuperManager) {
            var req = {
                "id": $scope.enterpriseID,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": 100,
                    "isReturnTotal": "1",
                }
            }
            /*查询企业列表*/
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
                data: JSON.stringify(req),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '1030100000') {
                            $scope.enterpriseName = data.enterprise.enterpriseName;
                            $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
                        }
                    })
                }
            });
        }
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.statusMap = {
            "1": "生效",
            "2": "失效",
            "3": "预生效"
        }
        $scope.orderListData = [];
        //下拉框(订单状态)
        $scope.orderStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "生效"
            },
            {
                id: "2",
                name: "失效"
            }, {
                id: "3",
                name: "预生效"
            }
        ];
        //下拉框(业务类型)
        $scope.businessTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "名片彩印"
            },
            {
                id: "2",
                name: "热线彩印"
            }, {
                id: "3",
                name: "广告彩印"
            }
        ];
        //直客和代理商共用同一个页面,企业通知只有代理商有
        if($scope.enterpriseType == '2')
        {
            var s = {
                id: 4,
                name: "企业通知"
            };
            $scope.businessTypeChoise.push(s);
        }

        //初始化搜索条件
        $scope.initSel = {
            orderStatus: "",
            businessType: "",
            orderName: "",
        };
        $scope.queryOrderList();
    };
    $scope.formatDate = function (str) {
        if (!str) {
            return 'format error';
        }
        var newDateStr = "";
        newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
        return newDateStr;
    }
    $scope.gotoAdd = function () {
        location.href = '../createOrder/createOrder.html';
    }
    $scope.gotoDetail = function (item) {
        //传入objectID，作为唯一标识
        location.href = '../queryOrderDetail/queryOrderDetail.html?orderCode=' + item.orderCode + '&objectID=' + item.objectID;
    }
    $scope.gotoupdate = function (item) {
        //传入objectID，作为唯一标识
        location.href = '../updateOrder/updateOrder.html?orderCode=' + item.orderCode + '&objectID=' + item.objectID;
    }
    $scope.gotoAddtion = function (item) {
        location.href = '../replenishOrder/replenishOrder.html?objectId=' + item.objectID;
    }
    $scope.queryOrderList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "enterpriseID": $scope.enterpriseID || "",
                "orderName": $scope.initSel.orderName || '',
                "status": $scope.initSel.orderStatus || '',
                "servType": $scope.initSel.businessType || '',
                "orderType": 1,
                "isReturnOrderItem": 1,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.orderListData = result.orderList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil($scope.pageInfo[0].totalCount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                        var hasPX = true;
                        for (var k in $scope.orderListData) {
                            var item = $scope.orderListData[k];
                            for (var m in item.orderItemList) {
                                var innerItem = item.orderItemList[m].product;
                                if (innerItem.chargeType === 1) {
                                    hasPX = false;
                                }
                            }
                            if (hasPX) {
                                item.tcType = 1;
                            } else {
                                item.tcType = 2;
                            }
                        }
                    } else {
                        $scope.orderListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.orderListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });

    }
}])
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])