<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ProvinceServiceTaskMapper">

    <resultMap id="ProvinceServiceTaskResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceTask">
        <id column="ID" property="ID"/>
        <result column="enterpriseID" property="enterpriseID"/>
        <result column="corpID" property="corpID"/>
        <result column="servType" property="servType"/>
        <result column="subServType" property="subServType"/>
        <result column="operationType" property="operationType"/>
        <result column="reqMsg" property="reqMsg"/>
        <result column="effStatus" property="effStatus"/>
        <result column="retryCount" property="retryCount"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
    </resultMap>

    <insert id="insertProvinceServiceTask" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceTask">
        INSERT INTO `ecpm_t_province_service_task`
        ( `enterpriseID`, `corpID`, `servType`, `subServType`, `operationType`, reqMsg, effStatus, retryCount,
         createTime, updateTime)
        VALUES ( #{enterpriseID}, #{corpID}, #{servType}, #{subServType}, #{operationType}, #{reqMsg},
                #{effStatus}, #{retryCount}, #{createTime}, #{updateTime})
    </insert>

    <!-- 更新记录 -->
    <update id="updateEffStatusProvinceServiceTask" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceTask">
        UPDATE `ecpm_t_province_service_task`
        SET `effStatus` = 5
        WHERE `enterpriseID` = #{enterpriseID}
          and `servType` = #{servType}
        <if test="subServType != null">
            and `subServType` = #{subServType}
        </if>
          and `effStatus` = 2
    </update>

    <update id="updateProvinceServiceTaskById" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceTask">
        UPDATE `ecpm_t_province_service_task`
        SET `effStatus` = #{effStatus}, retryCount = #{retryCount}
        WHERE id = #{id}
    </update>

    <!-- 删除记录 -->
<!--    <delete id="deleteProvinceServiceTask" parameterType="int">-->
<!--        DELETE-->
<!--        FROM `ecpm_t_province_service_task`-->
<!--        WHERE `ID` = #{ID}-->
<!--    </delete>-->

    <!-- 查询记录 -->
<!--    <select id="selectProvinceServiceTaskById" parameterType="int" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceTask">-->
<!--        SELECT * FROM `ecpm_t_province_service_task`-->
<!--        WHERE `ID` = #{ID}-->
<!--    </select>-->

    <select id="queryProvinceServiceTaskForTask"  resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceServiceTask">
        SELECT * FROM `ecpm_t_province_service_task`
       where  effStatus = 2 and createTime &lt; #{createTime}
    </select>

</mapper>