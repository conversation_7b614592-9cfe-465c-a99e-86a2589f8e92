<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper2.GetSysCfgMapper">
	<resultMap id="getCfgValueModel" type="com.huawei.jaguar.dsdp.bill.dao.domain2.GetSysCfgWrapper">
	
        <result property="cfgID" column="cfgID" javaType="java.lang.Integer"/>
        <result property="cfgKey" column="cfgKey" javaType="java.lang.String"/>
        <result property="cfgValueType" column="cfgValueType" javaType="java.lang.Integer"/>
        <result property="cfgValue" column="cfgValue" javaType="java.lang.String"/>
        <result property="cfgDesc" column="cfgDesc" javaType="java.lang.String"/>
        <result property="createTime" column="createTime" javaType="java.util.Date"/>
        <result property="lastUpdateTime" column="lastUpdateTime" javaType="java.util.Date"/>
    </resultMap>

	<select id="getSysCfg" resultMap="getCfgValueModel">
				select 
				cfgID,
				cfgKey,
				cfgValueType,
				cfgValue,
				cfgDesc,
				createTime,
				lastUpdateTime from
				dsum_t_syscfg where cfgKey = #{key}
    </select>
    
    <select id="getAllSysCfg" resultMap="getCfgValueModel">
				select 
				cfgID,
				cfgKey,
				cfgValueType,
				cfgValue,
				cfgDesc,
				createTime,
				lastUpdateTime from
				dsum_t_syscfg
    </select>
</mapper>