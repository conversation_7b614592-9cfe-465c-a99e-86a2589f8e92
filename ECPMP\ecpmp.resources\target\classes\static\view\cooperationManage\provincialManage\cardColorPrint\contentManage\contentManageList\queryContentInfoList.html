
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../../../css/bootstrap.min.css" />
<link href="../../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../../css/searchList.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="../../../../../../css/groupList.css" />
<script type="text/javascript" src="../../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../../frameworkJs/angular-sanitize.js"></script>
<script type="text/javascript" src="../../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../../../service/utils/service-common.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../../directives/page/page.css" />
<link href="../../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="queryContentInfoList.js"></script>
<style>
	.cooperation-manage .form-inline {
		padding: 20px 0px 20px 10px;
		overflow:hidden;
    }

	.form-horizontal .control-label {
		padding-top: 7px;
		white-space: nowrap;
	}

	.handle {
		overflow: hidden;
    }
    .cooperation-manage .form-group select{
        width:100%;
    }
    @media (max-width: 1366px){
            .cooperation-manage .coorPeration-table th,td{
                padding-left: 15px!important;
            }
    }

    .li-disabled{
        opacity: 0.5;
        cursor: not-allowed !important;
    }
    .label-supply {
        display: inline-block;
        float: left;
        padding-right: 15px;
        padding-left: 15px;
    }
    .clearf:after{
        content:'';
        clear:both;
        height:0;
        display:block;
    }
</style>
</head>
<!--
<body ng-app='myApp' ng-controller='orderListController' ng-init="init();" class="body-min-width">
-->
<body ng-app='myApp' ng-controller='orderListController' ng-init="init();" class="body-min-width-new">
    <div class="cooperation-manage" style="overflow-x: scroll;" ng-show="isAllow">
        <div class="cooperation-head">
            <span ng-show="!isSuperManager" class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>
            <span ng-show="isSuperManager" class="frist-tab" ng-bind="'COMMON_PROVINCEENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENT_MANAGE'|translate"></span></div>

    <top:menu ng-if="isSuperManager" apply-val="{{proSupServerType}}" chose-index="{{choseIndex}}" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/cardColorPrint/contentManage/contentManageList" list-index="35"></top:menu>
		<top:menu ng-if="enterpriseType =='5' &&  isEcProvince !=112 && !isMiguMusic" ng-class="{true:'',false:'second-topmenu'}[isProvincial]"
                  chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/cardColorPrint/contentManage/contentManageList" list-index="36"></top:menu>
        <top:menu ng-if="enterpriseType =='5' &&  (isEcProvince ==112 || isMiguMusic)" ng-class="{true:'',false:'second-topmenu'}[isProvincial]"
                  chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/cardColorPrint/contentManage/contentManageList" list-index="82"></top:menu>
        <div class="cooperation-search">
            <form class="form-horizontal">
				<div class="form-group form-inline">
                    <div class="control-label label-supply">
                        <label for="contentName" ng-bind="'CONTENTAUDIT_HOTCYCONTENT'|translate"></label>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                        <input type="text" autocomplete="off" class="form-control" id="contentName" placeholder="{{'CONTENT_PLH_COLORBOX'|translate}}" ng-model="initSel.contentInfo">
                    </div>
                    <!--内容编号查询-->
                    <div class="control-label label-supply">
                        <label for="contentName" ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></label>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                        <input type="text" autocomplete="off" class="form-control" id="contentNo"
                               placeholder="{{'CONTENTAUDIT_INPUTCONTENTNUMBERS'|translate}}" ng-model="initSel.contentNo">
                    </div>
                    <div class="control-label label-supply">
                            <label for="contentstatus" ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate"></label>
                        </div>
                        <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3" style="padding-left:0">
                            <select style="max-width:200px" class="form-control" ng-model="initSel.auditStatus" ng-options="x.id as x.name for x in auditStatusChoise"></select>
                        </div>
                    <div class="clearf"> </div>
                    <!--投递方式-->
                    <div class="control-label label-supply">
                        <label for="contentSubServType" ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></label>
                    </div>
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                        <select class="form-control" id="contentSubServType" ng-change="changeSelectedSubServType(initSel.subServType)" style="width: 100%;max-width: 200px;margin-left: 0;"
                                ng-model="initSel.subServType"
                                ng-options="x.id as x.name for x in subServTypeChoise"></select>
                    </div>
                    <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
                        <button ng-click="queryContentInfoList()" type="submit" class="btn search-btn" style="margin-left: 86px"><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
                    </div>

                </div>
			</form>
        </div>
        <div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="gotoAdd()" ng-disabled="isMiguMusic"><icon class="add-iocn"></icon><span ng-bind="'COMMON_ADD'|translate"></span><span>彩印</span></button>
            <button id="exportContentInfoList" class="btn add-btn" ng-click="exportContentFile()">
                <icon class="export-icon"></icon><span ng-bind="'DETAIL_EXPORT'|translate" style="margin-left: 8px"></span>
            </button>
        </div>

        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'CONTENTAUDIT_ALLCOLOR'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th style="width:8%;" ng-bind="'COMMON_ID'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENT_POSTOBJ'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENT_POSTDAY'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENT_POSTTIME'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_UNICOM'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_TELECOM'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_MOVE'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_UNICOM'|translate"></th>
                        <th style="width:8%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_TELECOM'|translate"></th>
                        <!-- <th ng-bind="'CONTENT_BUSINESSTYPE'|translate"></th> -->
                        <th style="width:22%" ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in orderListData">
                        <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
                        <!--<td><span title="{{item.content}}">{{item.content}}</span></td>-->
                        <td ng-show = "item.subServType != '3'">
                            <span title="{{item.content}}" ng-if="item.signature&&item.signature!=null&&item.signature!=''">【{{item.signature}}】{{item.content}}</span>
                            <span title="{{item.content}}" ng-if="item.signature==null||item.signature==''">{{item.content}}</span>
                        </td>
                        <td ng-show = "item.subServType == '3'">
                            <span title="主叫：{{item.content.split('||||')[0]}}&#10;被叫：{{item.content.split('||||')[1]}}" ng-if="item.signature&&item.signature!=null&&item.signature!=''">【{{item.signature}}】<br/>主叫：{{item.content.split('||||')[0]}}<br/>被叫：{{item.content.split('||||')[1]}}</span>
                            <span title="主叫：{{item.content.split('||||')[0]}}&#10;被叫：{{item.content.split('||||')[1]}}" ng-if="item.signature==null||item.signature==''">主叫：{{item.content.split('||||')[0]}}<br/>被叫：{{item.content.split('||||')[1]}}</span>
                        </td>
                      	<td><span title="{{orgNameList(item.contentBelongOrgList)}}">{{orgNameList(item.contentBelongOrgList)}}</span></td>
                        <td><span title="{{deliveryDateMap(item.deliveryDate)}}">{{deliveryDateMap(item.deliveryDate)}}</span></td>
                        <td><span title="{{pushTime(item.contentPushTime.startTime,item.contentPushTime.endTime)}}">{{pushTime(item.contentPushTime.startTime,item.contentPushTime.endTime)}}</span></td>
                        <td><span title="{{pushSubServTypeMap(item.subServType, item.hangupType)}}">{{pushSubServTypeMap(item.subServType, item.hangupType)}}</span></td>
                        <td ng-show = "item.servType != '1' && item.servType != '5'"><span title="{{approveStatusMap[item.approveStatus]}}">{{approveStatusMap[item.approveStatus]}}</span></td>
                        <td ng-show = "item.servType == '1' || item.servType == '5'"><span title="{{unicomApproveStatusMap[item.mobileApproveStatus]}}">{{unicomApproveStatusMap[item.mobileApproveStatus]}}</span></td>
                        <td><span
                                title="{{unicomApproveStatusMap[item.unicomApproveStatus]}}">{{unicomApproveStatusMap[item.unicomApproveStatus]}}</span>
                        </td>
                        <td><span
                                title="{{telecomApproveStatusMap[item.telecomApproveStatus]}}">{{telecomApproveStatusMap[item.telecomApproveStatus]}}</span>
                        </td>
                        <td ng-show = "item.servType != '1' && item.servType != '5'"><span title="{{item.approveIdea}}">{{item.approveIdea}}</span></td>
                        <!--113 修改主被叫审核意见展现方式-->
                        <td ng-show = "(item.servType == '1' || item.servType == '5')&&item.subServType!='3'"><span title="{{item.mobileApproveIdea}}">{{item.mobileApproveIdea}}</span></td>
                        <td ng-show = "item.subServType == '3'"><span ng-show=" item.mobileApproveIdea != null && item.mobileApproveIdea != ''" title="主叫：{{item.mobileApproveIdea.split('||||')[0]}}&#10;被叫：{{item.mobileApproveIdea.split('||||')[1]}}">主叫：{{item.mobileApproveIdea.split('||||')[0]}}<br/>被叫：{{item.mobileApproveIdea.split('||||')[1]}}</span></td>
                        <td>
                          <span
                                  ng-bind-html="CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType, item.subServType).htmlVersion"
                                  title="{{ CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType,item.subServType).titleVersion}}">
                          </span>
                        </td>
                        <td>
                          <span
                                  ng-bind-html="CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType, item.subServType).htmlVersion"
                                  title="{{ CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType,item.subServType).titleVersion}}">
                          </span>
                        </td>
                        <!-- <td><span title="{{servTypeMap[item.servType]}}">{{servTypeMap[item.servType]}}</span></td> -->
                        <td>
                            <div class="handle">
                                <ul>
                                    <!--删除-->
                                    <li ng-show = "item.approveStatus != '2'" class="delete" ng-click="isMiguMusic?'':deleteBusinessCardContent(item)" ng-class="{true:'',false:'li-disabled'}[!isMiguMusic]">
                                        <icon class="delete-icon"></icon><span ng-bind="'COMMON_DELETE'|translate"></span></li>
                                    <!--暂停/恢复-->
                                    <li ng-show = "(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.approveStatus == '3' && (item.status == '0' || item.status == '1')" class="suspend" ng-click="item.switchState===1?suspendContent(item,'1'):''"  ng-class="{true:'',false:'li-disabled'}[item.switchState===1]">
                                        <span style="color:#7360e2"
                                            ng-bind="'COMMON_SUSPEND'|translate"></span>
                                    </li>
                                    <li ng-show = "(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.approveStatus == '3' && item.status == '3'" class="recovery" ng-click="item.switchState===1?suspendContent(item,'0'):''"  ng-class="{true:'',false:'li-disabled'}[item.switchState===1]">
                                        <span style="color:#ff2549"
                                            ng-bind="'COMMON_RECOVERY'|translate"></span>
                                    </li>
                                    <li class="query" ng-click="gotoDetail(item)">
                                                <icon class="query-icon"></icon><span ng-bind="'COMMON_WATCH'|translate"></span></li>
                                    <li class="query" ng-click="isMiguMusic?'':gotoModify(item)"  ng-class="{true:'',false:'li-disabled'}[!isMiguMusic]">
                                                <icon class="edit-icon"></icon><span ng-bind="'COMMON_EDIT'|translate"></span></li>
                                	<li ng-if ="item.approveStatus == '3'" class="query" ng-click="gotoSynchronize(item)">
                                        <!-- <icon class="edit-icon"></icon> --><span ng-bind="'COMMON_SYNCHRONIZE'|translate"></span>
                                    </li>
                                    <li ng-if ="item.approveStatus != '3'" style="color:#C0C0C0;" class="query">
                                        <!-- <icon style="color:#C0C0C0;"></icon> --><span ng-bind="'COMMON_SYNCHRONIZE'|translate"></span>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="orderListData.length<=0">
                        <td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    <div>
        <ptl-page tableId="0" change="queryContentInfoList('justPage')"></ptl-page>
      </div>
    </div>
	<div class="modal fade" id="bussinessNo" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    	<div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p class="tip ng-binding" ng-bind="'BUSSINESS_NOT_ALLOW'|translate">
                            </p></div>
                        </div>
                        <div class="modal-footer" style="text-align:center">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>
<!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="z-index: 9999">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                            </p></div>
                        </div>
                        <div class="modal-footer" style="text-align:center">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>

	<!--生效状态弹出框-->
	<div class="modal fade" id="detailListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div role="document" class="modal-dialog dialog-1000">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" ng-click="ztclose()"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SYNCHRONIZE'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="form-horizontal">
						<div class="form-group">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'COMMON_SYNCHRONIZE'|translate"></span>：</label>
								<div class="col-lg-6 col-xs-9 col-sm-9 col-md-4">
									<select id="selectType" class="form-control">
										<option value="error" ng-show="operate !='edit'">同步失败</option>
										<option value="success" ng-show="operate !='edit'">同步成功</option>
									</select>
								</div>
								<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
									<button class="btn bg_purple search-btn btn1" ng-click="queryByMemberName()">
										<span class="icon btnIcon search"></span>
										<span ng-bind="'COMMON_SEARCH'|translate"></span>
									</button>
								</div>
							</div>
						</div>
					</div>


					<div class="add-table" style="margin-left:12px;margin-top:12px;">
						<button class="btn bg_purple search-btn btn1"
										style="width:105px; margin-left:10px;color:white"
										type="button" ng-click="batchSynchronize()" ng-bind="'RESTATE_STATUS'|translate"></button>
						<span style="color:red;">未选择成员时点击对同步失败号码重新同步</span>
					</div>

					<div class="coorPeration-table" style="max-height: 530px;overflow: auto">
						<table class="table table-striped table-hover">
							<thead>
							<tr>
								<th style="padding-left:30px;width: 10%;"><input type="checkbox" ng-model="allChoose" ng-click="ifSelected()"></th>
								<th>成员名称</th>
								<th>成员号码</th>
								<th>同步状态</th>
								<th>操作</th>
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="item in memberListData">
								<td><input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked" ng-if ="(item.syncGroupMemStatus == 2 && !item.noQuota)"></td>
								<td><span title="{{item.memberName}}">{{item.memberName}}</span></td>
								<td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
								<td>
									<span ng-if="item.syncGroupMemStatus == 2">同步失败</span>
									<span ng-if="item.syncGroupMemStatus == 1">同步成功</span>
								</td>
								<td style="font-size: small;cursor: pointer;">
									<span ng-if="item.syncGroupMemStatus == 2 && !item.noQuota" ng-click="retrySynchronize(item)"  style="cursor: pointer;color: #7360e2">重新同步</span>
									<!--<span ng-click="deleteSynchronize(item)"  style="cursor: pointer;color: #7360e2">删除</span>-->
								</td>
							</tr>
							<tr ng-show="memberListData.length<=0">
								<td style="text-align:center" colspan="5" ng-bind="'COMMON_NODATA'|translate"></td>
							</tr>
							</tbody>
						</table>
					</div>
					<div>
						<ptl-page tableId="1" change="queryMemberList2(selectedItem,'justPage','error')"></ptl-page>
					</div>
				</div>
				<div class="modal-footer">
				</div>
			</div>
		</div>
	</div>


    <!--名片内容确认删除弹窗-->
    <div class="modal fade bs-example-modal-sm" id="deleteBusinessCardContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content" style="width:390px">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838' ng-bind="'BUSINESSCARD_SUREDELETEHOTLINE'|translate"></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                            ng-click="delBusinessCardContent()"></button>
                    <button id="deleteBusinessCardCancel" type="submit" class="btn btn-back" data-dismiss="modal"
                            aria-label="Close" id="addBusinessCardCancel" ng-bind="'NO'|translate"></button>
                </div>

            </div>
        </div>
    </div>

</body>
</html>