elasticsearch.server.databaseName=cy-detail
elasticsearch.server.hotlineDeliveryTableName=delivery_detail
elasticsearch.server.cardAndAdvertisingTableName=delivery_record
task.merchantStatTask.tableName=detail


#BbossBillExportTask
task.bbossBillExportTask.fixedDelay=0 0/30 * * * ?
task.bbossBillExportTask.resetIndex=0 0 0 * * ?
task.bbossBillExportTask.taskName=bbossBillExportTask
task.bbossBillExportTask.taskKey=bill:enterprise:bbossBillExportTask
task.bbossBillExportTask.expireMillis=1800000
task.bbossBillExportTask.minutes=5
task.bbossBillExportTask.taskSwitch=1
bboss.bill.task.filepath=/home/<USER>/cdr/temp/businessanalysis/bboss2
bboss.bill.task.backUpPath=/home/<USER>/cdr/billBak/zyzq/bboss2
bboss.bill.task.limitDoSize=1000
bboss.bill.task.limitQuerySize=500
#filesize M/40*100000
bboss.bill.task.fileSize=10319
bbossBillExportTask.executor.thread.core_pool_size=5
bbossBillExportTask.executor.thread.max_pool_size=5
subProvincialDeliveryCdr.executor.thread.core_pool_size=8
subProvincialDeliveryCdr.executor.thread.max_pool_size=10
subProvincialDeliveryCdr.executor.thread.keepAliveSeconds=3000




elasticsearch.server.memberDatabaseName=provice_member
elasticsearch.server.memberTableName=member_record


task.bbossWrongBillWarningTask.fixedDelay=0 0 8 * * ?
task.bbossWrongBillWarningTask.taskName=bbossWrongBillWarningTask
task.bbossWrongBillWarningTask.taskKey=bill:enterprise:bbossWrongBillWarningTask2
task.bbossWrongBillWarningTask.expireMillis=3600000
task.bbossWrongBillWarningTask.minutes=5
task.bbossWrongBillWarningTask.taskSwitch=1
task.bbossWrongBillWarningTask.localFilePath=/home/<USER>/cdr/billBak/zyzq/bboss2
task.bbossWrongBillWarningTask.contactEmail=<EMAIL>,<EMAIL>
task.bbossWrongBillWarningTask.codeMap=F000:\u6587\u4EF6\u5927\u5C0F\u4E0D\u662F508\u7684\u500D\u6570|F001:\u6587\u4EF6\u91CD\u590D\u4E0A\u4F20\uFF0C\u4E3B\u8981\u6307\u5DF2\u7ECF\u6B63\u786E\u5904\u7406\u8FC7\u8BE5\u8BDD\u5355\u6587\u4EF6\uFF08\u62D2\u6536\u6587\u4EF6\u7684\u91CD\u590D\u4E0A\u4F20\u662F\u5141\u8BB8\u7684\uFF09|F002:\u6587\u4EF6\u540D\u4E2D\u7684\u5E8F\u53F7\u975E\u6CD5\uFF0C\u5E94\u8BE5\u4E3A0000\u20149998|F003:\u6587\u4EF6\u540D\u4E2D\u7684\u65E5\u671F\u975E\u6CD5|F004:\u6587\u4EF6\u540D\u65E5\u671F\u65E9\u4E8E\u89C4\u5B9A\u7684\u65F6\u95F4\u8303\u56F4|F005:\u6587\u4EF6\u540D\u65F6\u95F4\u8D85\u524D(\u6587\u4EF6\u540D\u65F6\u95F4\u8D85\u8FC7\u7CFB\u7EDF\u5F53\u524D\u65F6\u95F41\u5929)|F110:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u8BB0\u5F55\u7C7B\u578B\u4E0D\u662F\u201C90\u201D|F120:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u4FDD\u7559\u5B57\u6BB5\u4E0D\u4E3A\u7A7A|F130:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u6587\u4EF6\u5E8F\u53F7\u4E0E\u6587\u4EF6\u540D\u4E2D\u7684\u6587\u4EF6\u5E8F\u53F7\u4E0D\u4E00\u81F4|F140:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u6587\u4EF6\u4EA7\u751F\u65E5\u671F\u4E0E\u6587\u4EF6\u540D\u4E2D\u7684\u65E5\u671F\u4E0D\u4E00\u81F4|F150:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u8BDD\u5355\u603B\u6570\u5408\u8BA1\u4E0E\u7D2F\u8BA1\u503C\u4E0D\u7B26|F170:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u7ED3\u675F\u7B26\u4E0D\u662F\u56DE\u8F66\u6362\u884C
task.bbossWrongBillWarningTask.sftpip=***********
task.bbossWrongBillWarningTask.sftpusername=sftpqycy
task.bbossWrongBillWarningTask.sftppassword=dH0wo9L6oLaHNKKhN8bnVw==
task.bbossWrongBillWarningTask.sftpipport=2302
task.bbossWrongBillWarningTask.sftpremotepath=/outgoing/CMP/



### retryStatTask
########################################################
task.retryStatTask.taskName=retryStatTask
task.retryStatTask.taskKey=bill:stat:retryStatTask
task.retryStatTask.expireMillis=10000
task.retryStatTask.minutes=5
task.retryStatTask.fixedDelay=0 0/1 * * * ?
task.retryStatTask.taskSwitch=1
task.retryStatTask.produceCodes=09
task.retryStatTask.dealcdrnumber=50000
task.retryStatTask.core_pool_size=8
task.retryStatTask.max_pool_size=10
task.retryStatTask.keepAliveSeconds=3000

tentBill.maxPageSize=100000
task.subsideDeliveryCdrUploadTask.subsidecdrnumber=100000
task.subProvincialDeliveryCdrUploadTask.dealcdrnumber=50000
task.subProvincialDeliveryHotlineUploadTask.dealcdrnumber=100000
esSearch.switch=0


#BusinessAnaliysisBillTask
task.businessAnaliysisBillTask.fixedDelay=0 0 1 * * ?
task.businessAnaliysisBillTask.taskName=businessAnaliysisBillTask
task.businessAnaliysisBillTask.taskKey=bill:enterprise:businessAnaliysisBillTask
task.businessAnaliysisBillTask.expireMillis=300000
task.businessAnaliysisBillTask.minutes=5
task.businessAnaliysisBillTask.taskSwitch=1
business.analysis.bill.task.filepath=/home/<USER>/cdr/temp/businessanalysis2/hotlineTemp
business.analysis.bill.task.limitDoSize=5000
#filesize M/40*100000
business.analysis.bill.task.fileSize=1247500

#ProviceMemberExportTask
task.ProviceMemberExportTask.fixedDelay=0 0 4 1 * ?
task.ProviceMemberExportTask.taskName=ProviceMemberExportTask
task.ProviceMemberExportTask.taskKey=bill:ProviceMember:ProviceMemberExportTask
task.ProviceMemberExportTask.expireMillis=3600000
task.ProviceMemberExportTask.minutes=5
task.ProviceMemberExportTask.taskSwitch=1
ProviceMemberExportTask.executor.thread.core_pool_size=8
ProviceMemberExportTask.executor.thread.max_pool_size=10
ProviceMemberExportTask.executor.thread.keepAliveSeconds=3000
task.ProviceMemberExportTask.redisKeyExpireTime=86400
#MemberBillExportTask
task.memberBillExportTask.fixedDelay=0 0 4 1 * ?
task.memberBillExportTask.taskName=memberBillExportTask
task.memberBillExportTask.taskKey=bill:enterprise:memberBillExportTask
task.memberBillExportTask.expireMillis=1800000
task.memberBillExportTask.minutes=5
task.memberBillExportTask.taskSwitch=1
member.bill.task.filepath=/home/<USER>/cdr/temp/businessanalysis2/member
member.bill.task.backUpPath=/home/<USER>/cdr/billBak/businessanalysis2/member
member.bill.task.limitDoSize=1000
#filesize M/40*100000
member.bill.task.fileSize=1247500


task.subProvincialDeliveryHotlineUploadTask.fixedDelay=0 0 3 * * ?
#task.subProvincialDeliveryHotlineUploadTask.fixedDelay=0 0/1 * * * ?
task.subProvincialDeliveryHotlineUploadTask.taskName=subProvincialDeliveryHotlineUploadTask
task.subProvincialDeliveryHotlineUploadTask.taskKey=bill:enterprise:subProvincialDeliveryHotlineUploadTask
task.subProvincialDeliveryHotlineUploadTask.expireMillis=300000
task.subProvincialDeliveryHotlineUploadTask.minutes=5
task.subProvincialDeliveryHotlineUploadTask.taskSwitch=1
subProvincialDeliveryHotline.executor.thread.core_pool_size=8
subProvincialDeliveryHotline.executor.thread.max_pool_size=10
subProvincialDeliveryHotline.executor.thread.keepAliveSeconds=3000

task.subProvincialDeliveryCdrUploadTask.taskName=subProvincialDeliveryCdrUploadTask
task.subProvincialDeliveryCdrUploadTask.taskKey=bill:enterprise:subProvincialDeliveryCdrUploadTask
task.subProvincialDeliveryCdrUploadTask.expireMillis=300000
task.subProvincialDeliveryCdrUploadTask.minutes=5
task.subProvincialDeliveryCdrUploadTask.fixedDelay=0 5/30 * * * ?
task.subProvincialDeliveryCdrUploadTask.taskSwitch=1
task.subProvincialDeliveryCdrUploadTask.produceCodes=09|12|01|11|16
task.subProvincialDeliveryCdrUploadTask.produceNotTarCodes=09|12|01|16


tentBill.memberbill.fixedDelay=0 2/60 * * * ?
#tentBill.memberbill.fixedDelay=0 0/1 * * * ?
tentBill.memberbill.taskSwitch=1
tentBill.memberbill.taskName=memberbillTask
tentBill.memberbill.taskKey=bill:stat:memberbillTask
tentBill.memberbill.expireMillis=10000
tentBill.memberbill.minutes=5
tentBill.memberbill.sftpremotepath=/colorprint/membill
tentBill.memberbill.fileSize=100000
tentBill.memberbill.limitDoSize=100000

tentBill.allmemberbill.fixedDelay=0 0 4 1 * ?
tentBill.allmemberbill.taskSwitch=1
tentBill.allmemberbill.taskName=allmemberbillTask
tentBill.allmemberbill.taskKey=bill:stat:allmemberbillTask
tentBill.allmemberbill.expireMillis=10000
tentBill.allmemberbill.minutes=5
tentBill.allmemberbill.limitDoSize=1000
tentBill.allmemberbill.sftpremotepath=/colorprint/membill

task.designatedAgentBillTask.fixedDelay=0 5/60 * * * ?
task.designatedAgentBillTask.taskName=designatedAgentBillTask
task.designatedAgentBillTask.taskKey=bill:enterprise:designatedAgentBillTask
task.designatedAgentBillTask.taskSwitch=1
task.designatedAgentBillTask.expireMillis=300000
task.designatedAgentBillTask.minutes=5
task.designatedAgentBillTask.configValue={"cfgList":[{"id":"weilan","savePath":"/home/<USER>/cdr/designAgent2/weilansftp","enterpriseIDList":["********"],"sftpCfg":{"sftpIP":"*************","sftpPort":"22","sfptPath":"/weilansftp/weilan","sftpAccount":"agentsftp","sftpPwd":"zeX#Gk5645"}}]}
task.designatedAgentBillTask.aesEncryptKey=em05N3dPU1VvblFUdFZpcw==
task.designatedAgentBillTask.aesEncryptIv=RWQzSWMvemRvN2YxdVBHag==

task.subsidiaryAdvertDeliveryTask.taskName=subsidiaryAdvertDeliveryTask
task.subsidiaryAdvertDeliveryTask.fixedDelay=0 5/60 * * * ?
task.subsidiaryAdvertDeliveryTask.taskKey=bill:stat:subsidiaryAdvertDeliveryTask
task.subsidiaryAdvertDeliveryTask.taskSwitch=1
task.subsidiaryAdvertDeliveryTask.expireMillis=10000
task.subsidiaryAdvertDeliveryTask.minutes=5
task.subsidiaryAdvertDeliveryTask.task.fileSize=100000

#subsidiaryCardDeliveryTask
task.subsidiaryCardDeliveryTask.fixedDelay=0 5/60 * * * ?
task.subsidiaryCardDeliveryTask.taskName=subsidiaryCardDeliveryTask
task.subsidiaryCardDeliveryTask.taskKey=bill:enterprise:subsidiaryCardDeliveryTask
task.subsidiaryCardDeliveryTask.taskSwitch=1
task.subsidiaryCardDeliveryTask.expireMillis=300000
task.subsidiaryCardDeliveryTask.minutes=5

#SubsidiaryHotlineDeliveryTask\u5B50\u4F01\u4E1A
task.subsidiaryHotlineDeliveryTask.fixedDelay=0 10/60 * * * ?
#task.subsidiaryHotlineDeliveryTask.fixedDelay=0 0/1 * * * ?
task.subsidiaryHotlineDeliveryTask.taskName=subsidiaryHotlineDeliveryTask
task.subsidiaryHotlineDeliveryTask.taskKey=bill:enterprise:subsidiaryHotlineDeliveryTask
task.subsidiaryHotlineDeliveryTask.taskSwitch=1
task.subsidiaryHotlineDeliveryTask.expireMillis=300000
task.subsidiaryHotlineDeliveryTask.minutes=5

#subsidiaryNoticeDeliveryTask
task.subsidiaryNoticeDeliveryTask.fixedDelay=0 10/60 * * * ?
task.subsidiaryNoticeDeliveryTask.taskName=subsidiaryNoticeDeliveryTask
task.subsidiaryNoticeDeliveryTask.taskKey=bill:enterprise:subsidiaryNoticeDeliveryTask
task.subsidiaryNoticeDeliveryTask.taskSwitch=1
task.subsidiaryNoticeDeliveryTask.expireMillis=300000
task.subsidiaryNoticeDeliveryTask.minutes=5

#BusinessServiceRuleTask
task.BusinessServiceRuleTask.fixedDelay=0 0 2 * * ?
task.BusinessServiceRuleTask.taskName=businessServiceRuleTask
task.BusinessServiceRuleTask.taskKey=bill:enterprise:businessServiceRuleTask
task.BusinessServiceRuleTask.expireMillis=300000
task.BusinessServiceRuleTask.minutes=5
task.BusinessServiceRuleTask.taskSwitch=1
business.service.rule.task.filepath=/home/<USER>/cdr/temp/businessanalysis2/serviceRule
business.service.rule.task.limitDoSize=1000
#filesize M/40*100000
business.service.rule.task.fileSize=1247500



task.BusinessContentTempBillTask.fixedDelay=0 15 1 * * ?
task.BusinessContentTempBillTask.taskName=businessContentTempBillTask
task.BusinessContentTempBillTask.taskKey=bill:enterprise:businessContentTempBillTask
task.BusinessContentTempBillTask.expireMillis=300000
task.BusinessContentTempBillTask.minutes=5
task.BusinessContentTempBillTask.taskSwitch=1
business.contentTemp.bill.task.filepath=/home/<USER>/cdr/temp/businessanalysis2/ysmbDelivery
business.contentTemp.bill.task.limitDoSize=5000
business.contentTemp.bill.task.fileSize=1000000

#RemindDeliveryConfig
task.remindDeliveryTask.fixedDelay=0 33 3 * * ?
#task.remindDeliveryTask.fixedDelay=0 0/1 * * * ?
task.remindDeliveryTask.taskName=remindDeliveryTask
task.remindDeliveryTask.taskKey=bill:enterprise:remindDeliveryTask
task.remindDeliveryTask.taskSwitch=1
task.remindDeliveryTask.expireMillis=300000
task.remindDeliveryTask.minutes=5

#ExportMsisdnToRemindTask
task.ExportMsisdnToRemindTask.fixedDelay=0 0 3 * * ?
task.ExportMsisdnToRemindTask.taskName=ExportMsisdnToRemindTask
task.ExportMsisdnToRemindTask.taskKey=bill:enterprise:ExportMsisdnToRemindTask
task.ExportMsisdnToRemindTask.expireMillis=180000
task.ExportMsisdnToRemindTask.minutes=5
task.ExportMsisdnToRemindTask.taskSwitch=1
task.ExportMsisdnToRemindTask.msisdnToRemind.localpath = /home/<USER>/cdr/temp/businessanalysis2/msisdnToRemind/
task.ExportMsisdnToRemindTask.msisdnToRemind.locabaklpath = /home/<USER>/cdr/billBak2/msisdnToRemindBak/
task.ExportMsisdnToRemindTask.msisdnToRemind.limitDoSize=100000
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpipport=22
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpip=************
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpusername=huaweisftp
task.ExportMsisdnToRemindTask.msisdnToRemind.sftppassword=J3Ia\\$z0fw3
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpremotepath=/home/<USER>/qiyerenzheng/download


#businessServiceBillTask
task.ZYZQMemberBillExportTask.fixedDelay=0 0 4 * * ?
task.ZYZQMemberBillExportTask.taskName=businessServiceBillTask
task.ZYZQMemberBillExportTask.taskKey=bill:enterprise:businessServiceBillTask
task.ZYZQMemberBillExportTask.expireMillis=1800000
task.ZYZQMemberBillExportTask.minutes=5
task.ZYZQMemberBillExportTask.taskSwitch=1
ZYZQMemberBillExportTask.bill.task.filepath=/home/<USER>/cdr/temp/businessanalysis2/ecService
ZYZQMemberBillExportTask.bill.task.backUpPath=/home/<USER>/cdr/billBak/businessanalysis2/ecService
ZYZQMemberBillExportTask.bill.task.limitDoSize=100000
ZYZQMemberBillExportTask.bill.task.fileSize=100000
ZYZQMemberBillExportTask.bill.task.sftpremotepath=/cyqiye/ecService
task.ZYZQMemberBillExportTask.productMap={"1000202701":{"productname":"\u53cc\u5f69\u878d\u5408\u7f51\u5185\u7248\uff08\u5f69\u53705\u5143\u5957\u9910\uff09","unitprice":5},"1000375901":{"productname":"\u53cc\u5f69\u878d\u5408\u4e09\u7f51\u57fa\u7840\u7248\uff08\u5f69\u537010\u5143\u5957\u9910\uff09","unitprice":10},"1000376001":{"productname":"\u53cc\u5f69\u878d\u5408\u4e09\u7f51\u5347\u7ea7\u7248\uff08\u5f69\u537020\u5143\u5957\u9910\uff09","unitprice":20},"000366301":{"productname":"\u6b63\u5f0f8000\u5143\u5957\u9910","unitprice":8000},"000366201":{"productname":"\u6b63\u5f0f5000\u5143\u5957\u9910","unitprice":5000},"000366501":{"productname":"\u6b63\u5f0f15000\u5143\u5957\u9910","unitprice":15000},"000366401":{"productname":"\u6b63\u5f0f10000\u5143\u5957\u9910","unitprice":10000},"000375901":{"productname":"\u6b63\u5f0f10\u5143\u5957\u9910","unitprice":10},"000365801":{"productname":"\u6b63\u5f0f500\u5143\u5957\u9910","unitprice":500},"000375801":{"productname":"\u6b63\u5f0f6\u5143\u5957\u9910","unitprice":6},"000365701":{"productname":"\u6b63\u5f0f100\u5143\u5957\u9910","unitprice":100},"000365901":{"productname":"\u6b63\u5f0f800\u5143\u5957\u9910","unitprice":800},"000376401":{"productname":"\u6b63\u5f0f50\u5143\u5957\u9910","unitprice":50},"000376301":{"productname":"\u6b63\u5f0f20\u5143\u5957\u9910","unitprice":20},"000376201":{"productname":"\u6b63\u5f0f10\u5143\u5957\u9910","unitprice":10},"000376101":{"productname":"\u6b63\u5f0f30\u5143\u5957\u9910","unitprice":30},"000376001":{"productname":"\u6b63\u5f0f20\u5143\u5957\u9910","unitprice":20},"000202701":{"productname":"\u6b63\u5f0f5\u5143\u5957\u9910","unitprice":5},"000202801":{"productname":"\u4f53\u9a8c\u5957\u9910","unitprice":0},"000202601":{"productname":"\u4f53\u9a8c\u5957\u9910\uff08\u5305\u542b\u672c\u5f02\u7f51\uff09","unitprice":0},"000313401":{"productname":"\u4f53\u9a8c\u5957\u9910\uff08\u79fb\u52a8\u672c\u7f51\uff09","unitprice":0},"000366101":{"productname":"\u6b63\u5f0f3000\u5143\u5957\u9910","unitprice":3000},"000366001":{"productname":"\u6b63\u5f0f1000\u5143\u5957\u9910","unitprice":1000}}

task.dealMhWhiteTask.enterprise.groupId=226231


task.businessMhWhiteTask.fixedDelay=0 0 4 * * ?
businessMhWhiteTask.bill.task.weekDay=1
businessMhWhiteTask.bill.task.monthDay=1
task.businessMhWhiteTask.taskName=businessMhWhiteTask
task.businessMhWhiteTask.taskKey=bill:enterprise:businessMhWhiteTask
task.businessMhWhiteTask.expireMillis=1800000
task.businessMhWhiteTask.minutes=5
task.businessMhWhiteTask.taskSwitch=1
task.businessMhWhiteTask.sleepMillis=1000
task.businessMhWhiteTask.maxDealNumber=300
businessMhWhiteTask.bill.task.filepath=/home/<USER>/cdr/temp/businessanalysis2/dealMhWhiteTask
businessMhWhiteTask.bill.task.backUpPath=/home/<USER>/cdr/billBak/businessanalysis2/mhwhite
businessMhWhiteTask.bill.task.limitDoSize=1000
businessMhWhiteTask.bill.task.fileSize=100000
businessMhWhiteTask.bill.task.sftpremotepath=/cyqiye/mhwhite
businessMhWhiteTask.bill.task.expiredSeconds=7776000

task.ProviceMemberByTimeTask.fixedDelay = 0 0/1 * * * ?
task.ProviceMemberByTimeTask.taskName = ProviceMemberByTimeTask
task.ProviceMemberByTimeTask.taskKey = bill:ProviceMember:ProviceMemberByTimeTask
task.ProviceMemberByTimeTask.minutes = 5
task.ProviceMemberByTimeTask.expireMillis = 3600000
task.ProviceMemberByTimeTask.taskSwitch=1
task.ProviceMemberByTimeTask.synBusinessProviceCode=12|01
task.ProviceMemberByTimeTask.filterProviceCode=01

task.BusinessEnterpriseBillTask.fixedDelay=0 50 3 * * ?
task.BusinessEnterpriseBillTask.taskName=BusinessEnterpriseBillTask
task.BusinessEnterpriseBillTask.taskKey=bill:enterprise:BusinessEnterpriseBillTask
task.BusinessEnterpriseBillTask.expireMillis=180000
task.BusinessEnterpriseBillTask.minutes=5
task.BusinessEnterpriseBillTask.taskSwitch=1
BusinessEnterpriseBillTask.bill.task.filepath=/home/<USER>/cdr/temp/businessanalysis2/enterprise
BusinessEnterpriseBillTask.bill.task.backUpPath=/home/<USER>/cdr/billBak/businessanalysis2/enterprise
BusinessEnterpriseBillTask.bill.task.sftpremotepath=/cyqiye/enterprise
BusinessEnterpriseBillTask.bill.task.fileSize=100000


task.HaoBaiCardDeliveryTask.taskName=HaoBaiCardDeliveryTask
task.HaoBaiCardDeliveryTask.taskKey=HaoBaiCardDeliveryTask
task.HaoBaiCardDeliveryTask.taskSwitch=1
task.HaoBaiCardDeliveryTask.expireMillis=3600000
task.HaoBaiCardDeliveryTask.minutes=5
task.HaoBaiCardDeliveryTask.fixedDelay=0 0 0/1 * * ?
task.HaoBaiCardDeliveryTask.limitDoSize=1000
task.HaoBaiCardDeliveryTask.limitQuerySize=500
task.HaoBaiCardDeliveryTask.filepath=/home/<USER>/cdr/agent/haobai/card
task.HaoBaiCardDeliveryTask.backUpPath=/home/<USER>/cdr/billBak/agent/haobai/card
task.HaoBaiCardDeliveryTask.fileSize=10319
task.HaoBaiCardDeliveryTask.enterpriseIds=20000217
task.HaoBaiCardDeliveryTask.sftpremotepath=/data/qghbcdr
task.HaoBaiCardDeliveryTask.sftpip=*************
task.HaoBaiCardDeliveryTask.sftpusername=qghb1
task.HaoBaiCardDeliveryTask.sftppassword=30hm8kh^ib0m
task.HaoBaiCardDeliveryTask.sftpipport=22



task.BusinessContentTaskConfig.fixedDelay=0 0 3 * * ?
task.BusinessContentTaskConfig.taskName=businessContentTask
task.BusinessContentTaskConfig.taskKey=ecpm:enterprise:businessContentTask
task.BusinessContentTaskConfig.expireMillis=300000
task.BusinessContentTaskConfig.minutes=5
task.BusinessContentTaskConfig.taskSwitch=0
task.BusinessContentTaskConfig.filepath=/home/<USER>/cdr/temp/businessanalysis2/content
task.BusinessContentTaskConfig.fileSize=100000
task.BusinessContentTaskConfig.sftpip=*************
task.BusinessContentTaskConfig.sftpusername=mgcyqiye_sftp
task.BusinessContentTaskConfig.sftppassword=0ACcJLZD/ul56mAOEiUaUw==
task.BusinessContentTaskConfig.sftpipport=22
task.BusinessContentTaskConfig.sftpconnecttimeount=5000
task.BusinessContentTaskConfig.sftpremotepath=/cyqiye/content
task.BusinessContentTaskConfig.backUpPath=/home/<USER>/cdr/billBak/businessanalysis2/content