var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n", "service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {
        $scope.token = $.cookie("token");
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否分省管理员
        $scope.isProvincial = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isProvincial = (loginRoleType == 'provincial');
        $scope.isSuperManager = (loginRoleType == 'superrManager');
        $scope.isNormalMangager = (loginRoleType == 'normalMangager');

        $scope.enterpriseID = "";

        if ($scope.isProvincial) {
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
        }

        $scope.selectedProvince = null;

        //默认为1：名片彩印
        $scope.serviceType = "";

        $scope.enterpriseName = "";
        $scope.subProvinceType = "";
        $scope.subProvinceTypeMap = {"1": "分省", "2": "集客", "3": "移动云PAAS","4": "咪咕音乐"};



        $scope.subProvinceTypeSelect = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "分省"
            },
            {
                id: "2",
                name:
                    "集客"
            }
            ,
            {
                id: "3",
                name:
                    "移动云PAAS"
            }
            ,
            {
                id: "4",
                name:
                    "咪咕音乐"
            }
        ];

        $scope.channelSelectMap = [{
            id: "",
            name: "不限"
        }];
        $scope.enterpriseTypeList = localStorage.getItem("enterpriseTypeList")
        if($scope.enterpriseTypeList!=null){
            angular.forEach(JSON.parse($scope.enterpriseTypeList),function (item){
                if("112" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"3","name": "移动云PAAS"} );
                }
                if("111" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"2","name": "集客"});
                }
                if("113" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"4","name": "咪咕音乐"});
                }
                if("0" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"1","name": "省份"});
                }
            });
        }
        $scope.subProvinceTypeSelect = $scope.channelSelectMap;


        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            }
            ,
            {
                id: 5,
                name: "热线彩印省份版"
            }
        ];

        if ($scope.isSuperManager || $scope.isProvincial) {
            $scope.queryProvinceAndCitybychaoguan();
        }
        else {
            $scope.queryProvinceAndCity();
        }
        //初始化搜索条件
        $scope.initSel = {
            provinceName: "0",//归属地
            startTime: "",
            endTime: "",
            search: false,
        };

        //$scope.queryEnterpriseStatInfo();
    }

    $scope.getServiceType = function (serviceType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 3) {
            return "广告彩印";
        }
        else if (serviceType == 5) {
            return "热线彩印省份版";
        }
    };

    $scope.getSubServType = function (subServType, hangupType) {
        if (subServType == 1) {
            return "主叫屏显";
        }
        else if (subServType == 2) {
            return "被叫屏显";
        }
        else if (subServType == 4) {
        	if(hangupType != undefined && hangupType != null && hangupType == 1) {
        		return "主叫挂机短信";
        	}
        	if(hangupType != undefined && hangupType != null && hangupType == 2) {
        		return "被叫挂机短信";
        	}
            return "被叫挂机短信";
        }
        else if (subServType == 8) {
            return "挂机彩信";
        }
        else if (subServType == 3) {
            return "屏显";
        }
        else if (subServType == 16) {
            return "挂机增彩";
        }
    };

    $scope.setTime = function (time) {
        if (time == "") {
            return "";
        }
        var year = time.slice(0, 4);
        var month = time.slice(5, 7);
        return year + month + "01000000";
    }

    $('.input-daterange').datepicker({
        format: "yyyy-mm",
        startView: 1,
        minViewMode: 1,
        clearBtn: true,
        language: "zh-CN",
        orientation: "bottom left",
        autoclose: true
    });

    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
                $scope.searchOn();
            }
        )
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
                $scope.searchOn();
            }
        )
    });

    $scope.getTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        return year + "-" + month;
    }

    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        $scope.initSel.startTime = document.getElementById("start").value;
        $scope.initSel.endTime = document.getElementById("end").value;

        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }
    }
    $scope.exportFile = function () {
        let provinceID = '';
        if (!$scope.isProvincial && $scope.selectedProvince != null && $scope.selectedProvince != undefined) {
            if ($scope.isSuperManager) {
                provinceID = $scope.selectedProvince.provinceID;
            }
            if ($scope.isProvincial || $scope.isNormalMangager) {
                provinceID = $scope.selectedProvince.fieldVal;
            }
        }
        let cityID = '';

        if (!$scope.isProvincial && $scope.selectedCity != null && $scope.selectedCity != undefined) {
            if ($scope.isSuperManager) {
                cityID = $scope.selectedCity.cityID;
            }
            if ($scope.isProvincial || $scope.isNormalMangager) {
                cityID = $scope.selectedCity.fieldVal;
            }
        }
        var req = {
            "param": {
                "provinceID": provinceID,
                "cityID": cityID,
                "serviceType": $scope.serviceType,
                "startDate": $scope.setTime($scope.initSel.startTime),
                "endDate": $scope.setTime($scope.initSel.endTime),
                "enterpriseName": $scope.enterpriseName,
                "areaDimension": 3,
                "timeDimension": 2,
                "enterpriseID": $scope.enterpriseID,
                "parentEnterpriseID": "",
                "enterpriseType": 5,
                "type":$scope.isProvincial?51:5,
                "token": $scope.token,
                "isExport": 1,
                "subProvinceType": $scope.subProvinceType || null,
            },
            "url": "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile",
            "method": "get"
        }
        CommonUtils.exportFile(req);
    };
    //后续post的函数
    $scope.queryEnterpriseStatInfo = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "areaDimension": 3,
                "timeDimension": 2,
                "enterpriseType": 5,
                "enterpriseName": $scope.enterpriseName || '',
                "enterpriseID": $scope.enterpriseID || '',
                "subProvinceType": $scope.subProvinceType || null,
                "cityID": $scope.cityID,
                "serviceType": $scope.serviceType || '',
                "startDate": $scope.setTime($scope.initSel.startTime) || '',
                "endDate": $scope.setTime($scope.initSel.endTime) || '',
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };

            if ($scope.selectedProvince != null && $scope.selectedProvince != undefined) {
                if ($scope.isSuperManager) {
                    req.provinceID = $scope.selectedProvince.provinceID;
                }
                if ($scope.isProvincial || $scope.isNormalMangager) {
                    req.provinceID = $scope.selectedProvince.fieldVal;
                }
            }
            if ($scope.selectedCity != null && $scope.selectedCity != undefined) {
                if ($scope.isSuperManager) {
                    req.cityID = $scope.selectedCity.cityID;
                }
                if ($scope.isProvincial || $scope.isNormalMangager) {
                    req.cityID = $scope.selectedCity.fieldVal;
                }
            }
            if ($scope.initSel.startTime != null && $scope.initSel.startTime != ""
                && $scope.initSel.startTime == $scope.initSel.endTime) {
                req.getCollectData = 1;
                $scope.showStat = true;
            } else {
                $scope.showStat = false;
            }

            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile?enterpriseName=" + $scope.enterpriseName +
                "&serviceType=" + $scope.serviceType + "&startDate=" + $scope.setTime($scope.initSel.startTime) + "&endDate=" + $scope.setTime($scope.initSel.endTime) +
                "&areaDimension=3" + "&timeDimension=2" + "&provinceID=" + "&enterpriseID=" + $scope.enterpriseID + "&parentEnterpriseID=" + "&cityID=" + "&enterpriseType=5" + "&type=5";
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.getCollectData = 0;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseStatInfo",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.StatInfoListData = result.enterpriseStatInfoList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[0].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
                        if (condition != 'justPage') {
                            $scope.enterpriseStatCollect = result.enterpriseStatCollect;
                        }
                        if ($scope.enterpriseStatCollect) {
                            $scope.enterpriseStatCollect.time = $scope.initSel.startTime;
                        }
                    }
                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });

    };

    //搜索省份改变时，找到对应的市(超管)
    $scope.changeSelectedProvincebychaoguan = function (selectedProvince) {
        $scope.subCityList = null;
        if (selectedProvince) {

            jQuery.each($scope.cityList, function (i, e) {
                if (e.key == selectedProvince.provinceID) {
                    $scope.subCityList = e;
                }
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
    }

    //从Local Storage中查询超管省的方法
    $scope.queryProvinceAndCitybychaoguan = function () {
        if ($scope.isProvincial) {
            if ($.cookie('provinceID') != null
                && $.cookie('provinceID') != undefined
                && $.cookie('cityID') != null
                && $.cookie('cityID') != undefined) {
                var queryProvinceListReq = {};
                /*查询省份*/
                RestClientUtil.ajaxRequest({
                    type: 'POST',
                    url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
                    data: JSON.stringify(queryProvinceListReq),
                    success: function (data) {
                        $rootScope.$apply(function () {
                            var result = data.result;
                            var provinceList = data.provinceList;
                            if (result.resultCode == '1030100000') {
                                jQuery.each(provinceList, function (i, e) {
                                    if ($.cookie('provinceID') == e.provinceID) {
                                        $scope.provinceList = {};
                                        $scope.provinceList2 = {};

                                        $scope.provinceList[0] = e;
                                        $scope.selectedProvince = e;
                                        $scope.provinceList2[e.provinceID] = e.provinceName;
                                        return;
                                    }
                                });
                                var provinceIds = [];
                                provinceIds[0] = $.cookie('provinceID');
                                var queryCityListReq = {};
                                queryCityListReq.provinceIDs = provinceIds;
                                /*查询地市*/
                                RestClientUtil.ajaxRequest({
                                    type: 'POST',
                                    url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
                                    data: JSON.stringify(queryCityListReq),
                                    success: function (data) {
                                        $rootScope.$apply(function () {
                                            var result = data.result;
                                            if (result.resultCode == '1030100000') {
                                                $scope.subCityList = {};
                                                jQuery.each(data.cityList, function (i, e) {
                                                    jQuery.each(e, function (i, e) {
                                                        if (e.cityID == $.cookie('cityID')) {
                                                            $scope.subCityList = {};
                                                            $scope.selectedCity = e;
                                                            $scope.subCityList[0] = e;

                                                            $scope.cityList2 = {};
                                                            $scope.cityList2[e.cityID] = e.cityName;

                                                            return;
                                                        }
                                                    });
                                                });
                                            } else {
                                                $scope.tip = result.resultCode;
                                                $('#myModal').modal();
                                            }
                                        })
                                    },
                                    error: function () {
                                        $rootScope.$apply(function () {
                                                $scope.tip = '1030120500';
                                                $('#myModal').modal();
                                            }
                                        )
                                    }
                                });
                            }
                            else {
                                $scope.tip = data.result.resultCode;
                                $('#myModal').modal();
                            }
                        })
                    },
                    error: function () {
                        $rootScope.$apply(function () {
                                $scope.tip = '1030120500';
                                $('#myModal').modal();
                            }
                        )
                    }
                });
            }
        }
        else {
            $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
            $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
            $scope.provinceList2 = {};
            $scope.cityList2 = {};

            //删除全国选项
            var length = $scope.provinceList.length;
            for (var i = 0; i < length; i++) {
                if ($scope.provinceList[i].provinceID == "000") {
                    $scope.provinceList.splice(i, 1); //删除下标为i的元素
                    break;
                }
            }

            jQuery.each($scope.provinceList, function (i, e) {
                $scope.provinceList2[e.provinceID] = e.provinceName;
            });
            $scope.cityList = $scope.mapToList($scope.cityList);
            for (var a = 0; a < $scope.cityList.length; a++) {
                jQuery.each($scope.cityList[a], function (i, e) {
                    $scope.cityList2[e.cityID] = e.cityName;
                });
            }
            console.log($scope.cityList2);
        }


    };

    //搜索省份改变时，找到对应的市
    $scope.changeSelectedProvince = function (selectedProvince) {
        $scope.subCityList = null;
        if (selectedProvince) {
            $scope.subCityList = $scope.cityList.filter(function (a) {
                return a.parentAuthID == selectedProvince.id;
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
    }
    //省市联动方法
    $scope.queryProvinceAndCity = function () {
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
        $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
        $scope.provinceList2 = {};
        jQuery.each($scope.provinceList, function (i, e) {
            $scope.provinceList2[e.fieldVal] = e.authName;
        });
        console.log($scope.provinceList2);
        $scope.cityList2 = {};
        jQuery.each($scope.cityList, function (i, e) {
            $scope.cityList2[e.fieldVal] = e.authName;
        });
        console.log($scope.cityList2);
    };

    //省市联动时调用，用于转化格式
    $scope.mapToList = function (map) {
        var result = [];
        jQuery.each(map, function (_, o) {
            if (o) {
                o.key = o[0].provinceID;
                result.push(o);
            }
        });
        return result;
    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])