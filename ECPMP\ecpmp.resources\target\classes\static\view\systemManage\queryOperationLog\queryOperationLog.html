<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>日志管理</title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap-datepicker.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-common.js"></script>
	<!--分页-->
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<link rel="stylesheet" type="text/css" href="../../../css/searchList.css"/>
	<link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
	<link href="../../../css/bootstrap-datepicker.css" rel="stylesheet">
	
	<script type="text/javascript" src="queryOperationLogCtrl.js"></script>

	<style>
		label {
			min-width: 120px;
		}

		.cond-div {
			min-width: 240px;
		}
	</style>

</head>
<body ng-app="myApp" ng-controller="OperLogManagementController" ng-init="init()" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'OPERATION_LOG'|translate">日志管理</span>
		</div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="operationName" class="col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'OPERATION_NAME'|translate"></label>

					<div class="cond-div col-xs-2">
						<input type="text" autocomplete="off" class="form-control" id="operationName"
									 placeholder="{{'COMMON_INPUT_OPERATION'|translate}}" ng-model="operationName">
					</div>

					<label for="operTime" class="col-xs-1 control-label" ng-bind="'OPER_TIME'|translate"></label>
					<div class="cond-div col-xs-4 time">
						<div class="input-daterange input-group" id="datepicker">
						<input type="text" class="input-md form-control" autocomplete="off" id="start"
							ng-keyup="searchOn()" />
						<span class="input-group-addon" ng-bind="'TO'|translate"></span>
						<input type="text" class="input-md form-control" autocomplete="off" id="end"
							ng-keyup="searchOn()" />
					</div>
					</div>
					<div class="cond-div">
						<button type="submit" class="btn search-btn" ng-click="queryOperationLogList()" style="margin-left: 20px">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>

			</form>
		</div>
		<div class="add-table">
					<button ng-click="exportFile()" id="exportSpokesList" type="submit" class="btn add-btn" ><icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span></button>
		</div>
		<div style="margin: 20px">
			<p style="font-size: 16px" ng-bind="'OPERLOG_LIST'|translate"></p>
		</div>
		
		<div class="coorPeration-table">
		<table class="table table-striped table-hover">
			<thead>
			<tr>
				<th style="width:15%" ng-bind="'OPERATION_NAME'|translate"></th>
				<th style="width:15%" ng-bind="'OPER_NAME'|translate"></th>
				<th style="width:15%" ng-bind="'OPER_TIME'|translate"></th>
				<th style="width:30%" ng-bind="'OPER_DESC'|translate"></th>
				<th style="width:15%" ng-bind="'OPER_SOURCEIP'|translate"></th>
				<th style="width:15%" ng-bind="'OPER'|translate"></th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="item in operationLogList">
				<td><span title="{{item.operatorAccount}}" ng-bind="item.operatorAccount"></span></td>
				<td><span title="{{item.operName}}" ng-bind="item.operName"></span></td>
				<td><span title="{{item.operTime|formatDate}}" ng-bind="item.operTime|formatDate"></span></td>
				<td><span title="{{item.operDesc}}" ng-bind="item.operDesc"></span></td>
				<td><span title="{{item.sourceIP}}" ng-bind="item.sourceIP"></span></td>
				<td class="coorPeration-table-a">
					<div class="handle">
							<ul>
								<li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'OPER_DETAIL'|translate"></span>
								</li>
							</ul>
					</div>
					
				</td>
			</tr>
			<tr ng-show="operationLogList===null||operationLogList.length===0">
				<td style="text-align:center" colspan="7">暂无数据</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div>
		<ptl-page tableId="0" change="queryOperationLogList('justPage')"></ptl-page>
	</div>
	</div>
	

	<div class="modal fade" id="operLogDetail" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width:450px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'OPER_DETAIL'|translate"></h4>
				</div>
				<div class="modal-body" style="max-height:300px; overflow: auto;">
					<div class="row">
						<label  class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
							<span ng-bind="'OPERATION_NAME'|translate"></span>：
						</label>
						<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
							<span ng-bind="operationLog.operatorAccount"></span>
						</div>
					</div>
					<div class="row">
						<label  class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
							<span ng-bind="'OPER_NAME'|translate"></span>：
						</label>
						<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
							<span ng-bind="operationLog.operName"></span>
						</div>
					</div>
					<div class="row">
						<label  class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
							<span ng-bind="'OPER_TIME'|translate"></span>：
						</label>
						<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
							<span ng-bind="operationLog.operTime|formatDate"></span>
						</div>
					</div>
					<div class="row">
						<label  class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
							<span ng-bind="'OPER_DESC'|translate"></span>：
						</label>
						<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
							<span ng-bind="operationLog.operDesc"></span>
						</div>
					</div>
					<div class="row">
						<label  class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
							<span ng-bind="'OPER_SOURCEIP'|translate"></span>：
						</label>
						<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
							<span ng-bind="operationLog.sourceIP"></span>
						</div>
					</div>
				</div>
				
				<div class="modal-footer" style="text-align: center">
					<button type="submit" class="btn btn-primary search-btn" data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
			 style="z-index:555555;">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838;text-align: center'>
							{{tip|translate}}
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align: center">
					<button type="submit" class="btn btn-primary" data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
</body>
</html>