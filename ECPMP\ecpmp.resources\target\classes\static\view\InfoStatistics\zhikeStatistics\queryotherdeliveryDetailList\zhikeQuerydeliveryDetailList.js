var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        $scope.token=$.cookie('token')||'';
        $scope.accountID = $.cookie('accountID') || '1000';
        $scope.isFileNamePass = false;
        // $(function () {
        //     $('#exportSpokesList').bind("click", function () {
        //         window.open($scope.exportUrl);
        //     });
        // });
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            enterpriseName: "",
            receiveNum: "",
            startTime: "",
            endTime: "",
            enterpriseID: "",
            search:false,
        };
        // $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downDeliveryDetailListCsvFile?enterpriseName=" + $scope.initSel.enterpriseName +
        //     "&receiveNum=" + $scope.initSel.receiveNum + "&startDate=" + $scope.initSel.startTime + "&endDate=" + $scope.initSel.endTime + "&enterpriseID="  +
        //     "&serviceType=" + "&enterpriseType=1";
        /*
        	* 导出文件弹窗
        	*/
          $scope.exportFile = function () {
            $scope.remarks = "";
            $scope.isGreaterFileName = false;
            $scope.isEmptyFileName = false;
            $scope.isSpecialCharacters = false;
            $scope.isFileNamePass = true;
            $("#exportFile").modal("show");
          };
           /*
          	* 文件名校验
          	*/
        $scope.checkEmpty = function(){
            $scope.isFileNamePass = true;
            $scope.isGreaterFileName = false;


            var reg = /^[a-z0-9\u4e00-\u9fa5]+$/i
            // if(!$scope.remarks){
            //     $scope.isGreaterFileName = false;
            //     $scope.isEmptyFileName = true;
            //     $scope.isFileNamePass = false;
            //     $scope.isSpecialCharacters = false
            // }else if(!reg.test($scope.remarks)){
            //     $scope.isGreaterFileName = false;
            //     $scope.isEmptyFileName = false;
            //     $scope.isFileNamePass = false;
            //     $scope.isSpecialCharacters = true
            // }else

            if($scope.remarks.length > 255){
                $scope.isEmptyFileName = false;
                $scope.isGreaterFileName = true;
                $scope.isFileNamePass = false;
                $scope.isSpecialCharacters = false
            }
            //     else {
            //     $scope.isEmptyFileName = false;
            //     $scope.isGreaterFileName = false;
            //     $scope.isSpecialCharacters = false
            //     $scope.isFileNamePass = true;
            // }
        };
           /*
          	* 提交导出文件信息
          	*/
          $scope.submitExportTask = function(){
                $("#exportFile").modal("hide");
                if((!($scope.initSel.enterpriseID != null && $scope.initSel.enterpriseID != ''
                        && Number.isNaN(+$scope.initSel.enterpriseID)) && parseInt($scope.initSel.enterpriseID) > 2147483647) ||
                    ($scope.initSel.enterpriseID != null && $scope.initSel.enterpriseID != ''
                        && Number.isNaN(+$scope.initSel.enterpriseID))){
                    $scope.tip = '企业编号输入值非法';
                    $('#myModal').modal();
                    return;
                }
                var createExportTaskInfoRequest = {
                    "exportTaskInfo":{
                        "fileName":null,
                        "remarks":$scope.remarks,
                        "taskType":4,
                        "taskStatus":0,
                        "operatorID":$scope.accountID,
                        "params":{
                            "enterpriseName":$scope.initSel.enterpriseName,
                            "receiveNum":$scope.initSel.receiveNum,
                            "serviceType":"",
                            "startDate":getTime($scope.initSel.startTime),
                            "endDate":getTime($scope.initSel.endTime),
                            "enterpriseID":$scope.initSel.enterpriseID,
                            "enterpriseType":1,
                            "token":$scope.token,
                            "isExport":1
                        }
                    }
                }
                RestClientUtil.ajaxRequest({
                  type: 'POST',
                  url: "/ecpmp/ecpmpServices/exportTaskService/createExportTask",
                  data: JSON.stringify(createExportTaskInfoRequest),
                  success: function (data) {
                    $rootScope.$apply(function () {
                      var result = data.result;
                      if (result.resultCode == '1030100000') {
                          if(data.desc){
                              $scope.tip = data.desc;
                              $('#myModal').modal();
                          }
                      }else {
                          $scope.tip = result.resultCode;
                          $('#myModal').modal();
                        }
                    })
                  },
                  error:function(){
                      $rootScope.$apply(function(){
                          $scope.tip='1030120500';
                          $('#myModal').modal();
                          }
                      )
                  }
                });
          };
          $scope.uniqueTip = "";
          $scope.checkUnique = true;
//            $scope.querydeliveryDetailList();
    }

    $scope.clearmsg = function () {

    	$scope.uniqueTip = "";
        $scope.checkUnique = true;
    }
    
    $scope.getResult = function (deliveryResult) {

        if (deliveryResult == 0) {
            return "成功";
        }
        else {
            return "失败";
        }
    }

    $scope.getDeliveryType = function (deliveryType) {

    	if (deliveryType == 1) {
            return "USSD";
        }
        else if (deliveryType == 2) {
            return "闪信";
        }
        else if (deliveryType == 3) {
            return "USSD失败转闪信";
        }
        else if (deliveryType == 4) {
          return "短信";
        }
        else if (deliveryType == 5) {
            return "彩信";
        }
        else if (deliveryType == 6) {
            return "增彩";
        }

    }

    $scope.getTime = function (time) {
        if (time == null || time == "") {
            return "";
        }

        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        var hour = time.slice(8, 10);
        var minutes = time.slice(10, 12);
        return year + "-" + month + "-" + day + " " + hour + ":" + minutes;
    }

    $('.input-daterange').datepicker({
		format: "yyyy-mm-dd",
		weekStart: 0,
		language: "zh-CN",
		clearBtn: true,
		autoclose: true
	});

	$('#start').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});

	$('#end').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});

	//判断搜索按钮是否置灰
	$scope.searchOn = function () {
    	$scope.uniqueTip = "";
        $scope.checkUnique = true;
		var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;
        $scope.initSel.startTime = "";
        $scope.initSel.endTime = "";

		if (startTime !== '')
		{
			$scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
		}
		
		if (endTime !== '')
		{
			$scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
		}
		
		if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
			$scope.initSel.search = false;
		}
		else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
			$scope.initSel.search = false;
		}
		else {
			$scope.initSel.search = true;
		}
	}
    $scope.showTime = function (time) {
    	if(time)
		{
    		var year = time.slice(0, 4);
            var month = time.slice(4, 6);
            var day = time.slice(6, 8);
            var hour = time.slice(8, 10);
            var minute = time.slice(10, 12);
            var second = time.slice(12, 14);
            return year + month + day + hour + minute + second;
		}
    	else
		{
    		return ''
		}
        
    }
    
    //后续post的函数
    $scope.querydeliveryDetailList = function (condition) {
    	$scope.uniqueTip = "";
        $scope.checkUnique = true;
        //若日期清掉，starttime和endtime都设置为空
        if ($scope.time == "") {
            $scope.initSel.startTime = "";
            $scope.initSel.endTime = "";
        }

        if (condition != 'justPage') {
        	if(!$scope.initSel.enterpriseID && !$scope.initSel.enterpriseName)
        	{
        		$scope.uniqueTip = "企业编号、企业名称必填一个";
        		$scope.checkUnique = false;
        		return;
        	}
            var req = {
                "enterpriseName": $scope.initSel.enterpriseName || '',
                "enterpriseType":1,
                "deliverflag":"deliverflag",
                "receiveNum": $scope.initSel.receiveNum || '',
                "startDate": $scope.initSel.startTime || '',
                "endDate": $scope.initSel.endTime || '',
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            if($scope.initSel.enterpriseID)
        	{
                if(Number.isNaN(+$scope.initSel.enterpriseID)){
                    $scope.tip = '企业编号输入值非法';
                    $('#myModal').modal();
                    return;
                }else {
                    if(parseInt($scope.initSel.enterpriseID) > 2147483647){
                        $scope.tip = '企业编号输入值非法';
                        $('#myModal').modal();
                        return;
                    }
                    req.enterpriseID = parseInt($scope.initSel.enterpriseID);
                }
        	}
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downDeliveryDetailListCsvFile?enterpriseName=" + $scope.initSel.enterpriseName +
            "&receiveNum=" + $scope.initSel.receiveNum + "&startDate=" + $scope.initSel.startTime + "&endDate=" + $scope.initSel.endTime + "&enterpriseID="  +
            "&serviceType=" + "&enterpriseType=1";
            
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            if(!$scope.reqTemp.enterpriseID && !$scope.reqTemp.enterpriseName)
        	{
        		$scope.uniqueTip = "企业编号、企业名称必填一个";
        		$scope.checkUnique = false;
        		return;
        	}
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryDeliveryDetailList",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if(data.resultCode=='1030100000'){
                $scope.deliveryDetailListData=result.deliveryDetailList||[];
                $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                $scope.pageInfo[0].totalPage=result.totalNum!=="0" ?Math.ceil(result.totalNum/parseInt($scope.pageInfo[0].pageSize)):1;
              }  
              else 
              {
                $scope.tip = data.resultCode;
                $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
            )
          }
        });
        
    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])


/**
 * 获取时间戳 
 * @param timeStr 时间，是为yyyyMMddHHmmss
 * @returns
 */
function getTime(timeStr){
	var startTime = timeStr.substring(0,4) + "/" + timeStr.substring(4,6) + "/" + timeStr.substring(6,8) + " " + timeStr.substring(8,10) + ":" + timeStr.substring(10,12) + ":" + timeStr.substring(12,14)
	return new Date(startTime).getTime();
}
