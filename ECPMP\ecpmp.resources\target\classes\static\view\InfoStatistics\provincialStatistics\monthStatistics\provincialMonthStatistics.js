var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        // $(function () {
        //     $('#exportSpokesList').bind("click", function () {
        //         window.open($scope.exportUrl);
        //     });
        // });
        $scope.token=$.cookie('token')||'';
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否超管
        $scope.isSuperManager = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager');

        $scope.selectedProvince = null;

        //默认为1：名片彩印
        $scope.serviceType = "";
        $scope.subProvinceType = "";
        $scope.subProvinceTypeMap = {"1":"分省","2":"集客", "3": "移动云PAAS","4": "咪咕音乐"};
        $scope.subProvinceTypeSelect = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "分省"
            },
            {
                id: "2",
                name:
                    "集客"
            } ,
            {
                id: "3",
                name:
                    "移动云PAAS"
            },
            {
                id: "4",
                name:
                    "咪咕音乐"
            }
        ];
        $scope.channelSelectMap = [{
            id: "",
            name: "不限"
        }];
        $scope.enterpriseTypeList = localStorage.getItem("enterpriseTypeList")
        if($scope.enterpriseTypeList!=null){
            angular.forEach(JSON.parse($scope.enterpriseTypeList),function (item){
                if("112" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"3","name": "移动云PAAS"} );
                }
                if("111" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"2","name": "集客"});
                }
                if("113" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"4","name": "咪咕音乐"});
                }
                if("0" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"1","name": "省份"});
                }
            });
        }
        $scope.subProvinceTypeSelect = $scope.channelSelectMap;
        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            },
            {
                id: 5,
                name: "热线彩印省份版"
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            provinceName: "0",//归属地
            startTime: "",
            endTime: "",
            search: false,
        };
        // $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile?provinceID=" + $scope.provinceID + "&serviceType=" + $scope.serviceType + "&startDate=" +
        //     $scope.setTime($scope.initSel.startTime) + "&endDate=" + $scope.setTime($scope.initSel.endTime) + "&enterpriseName=" + "&areaDimension=1" + "&timeDimension=2" +"&enterpriseID=" +
        //     "&cityID=" +"&enterpriseType=5" + "&type=1";
        $scope.exportFile=function(){
            var req = {
                "param":{
                    "provinceID":$scope.provinceID,
                    "serviceType":$scope.serviceType,
                    "startDate":$scope.setTime($scope.initSel.startTime),
                    "endDate":$scope.setTime($scope.initSel.endTime),
                    "enterpriseName":"",
                    "areaDimension":1,
                    "timeDimension":2,
                    "enterpriseID":"",
                    "parentEnterpriseID":"",
                    "cityID":"",
                    "enterpriseType":5,
                    "type":1,
                    "token":$scope.token,
                    "isExport":1,
                    "subProvinceType": $scope.subProvinceType || null,

                },
              "url":"/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile",
              "method":"get"
            }
            CommonUtils.exportFile(req);
          }

        //根据是否是超管，来获取省份的权限信息
        if ($scope.isSuperManager) {
            $scope.queryProvinceAndCity();
        }
        else {
            $scope.queryProvinceAndCityFromStorage();
        }

        //$scope.queryEnterpriseStatInfo();
    }

    $scope.getServiceType = function (serviceType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 3) {
            return "广告彩印";
        }
        else if (serviceType == 5) {
            return "热线彩印省份版";
        }
        else if (serviceType == 5) {
            return "热线彩印省份版";
        }
        else if (serviceType == 7) {
            return "交互彩印";
        }
    };

    $scope.getSubServType = function (subServType, hangupType) {
        if (subServType == 1) {
            return "主叫屏显";
        }
        else if (subServType == 2) {
            return "被叫屏显";
        }
        else if (subServType == 4) {
        	if(hangupType != undefined && hangupType != null && hangupType == 1) {
        		return "主叫挂机短信";
        	}
        	if(hangupType != undefined && hangupType != null && hangupType == 2) {
        		return "被叫挂机短信";
        	}
            return "被叫挂机短信";
        }
        else if (subServType == 8) {
            return "挂机彩信";
        }
        else if (subServType == 3) {
            return "屏显";
        }
    };

    $('.input-daterange').datepicker({
        format: "yyyy-mm",
        startView: 1,
        minViewMode: 1,
        clearBtn: true,
        language: "zh-CN",
        orientation: "bottom left",
        autoclose: true
    });

    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $scope.getTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        return year + "-" + month;
    }

    $scope.setTime = function (time) {
        if (time == "")
        {
            return "";
        }
        var year = time.slice(0, 4);
        var month = time.slice(5, 7);
        return year + month + "01000000";
    }

    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        $scope.initSel.startTime = document.getElementById("start").value;
        $scope.initSel.endTime = document.getElementById("end").value;

        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }
    }

    //后续post的函数
    $scope.queryEnterpriseStatInfo = function (condition) {
        if (condition != 'justPage') {
            if ($scope.selectedProvince == null) {
                $scope.provinceID = '';
            }
            else {
                $scope.provinceID = $scope.selectedProvince.provinceID || $scope.selectedProvince.fieldVal;
            }

            var req = {
                "areaDimension": 1,
                "timeDimension": 2,
                "enterpriseType":5,
                "provinceID": $scope.provinceID,
                "subProvinceType": $scope.subProvinceType || null,
                "serviceType": $scope.serviceType || '',
                "startDate": $scope.setTime($scope.initSel.startTime) || '',
                "endDate": $scope.setTime($scope.initSel.endTime) || '',
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };

            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile?provinceID=" + $scope.provinceID + "&serviceType=" + $scope.serviceType + "&startDate=" +
            $scope.setTime($scope.initSel.startTime) + "&endDate=" + $scope.setTime($scope.initSel.endTime) + "&enterpriseName=" + "&areaDimension=1" + "&timeDimension=2" +"&enterpriseID=" + "&parentEnterpriseID=" +
            "&cityID=" +"&enterpriseType=5" + "&type=1";
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseStatInfo",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if(data.resultCode=='1030100000'){
                $scope.StatInfoListData=result.enterpriseStatInfoList||[];
                $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                $scope.pageInfo[0].totalPage=result.totalNum!==0 ?Math.ceil(result.totalNum/parseInt($scope.pageInfo[0].pageSize)):1;
              }  
              else 
              {
                $scope.tip = data.resultCode;
                $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
            )
          }
        });
    };

    //从Local Storage中查询超管省的方法
    $scope.queryProvinceAndCity = function () {
        /*查询省份*/
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));

        //删除全国选项
        var length = $scope.provinceList.length;
        for (var i = 0; i < length; i++) {
            if ($scope.provinceList[i].provinceID == "000") {
                $scope.provinceList.splice(i, 1); //删除下标为i的元素
                break;
            }
        }
        
        console.log($scope.provinceList);
        if (!!$scope.provinceList) {
            var provinceIDs = [];
            $scope.provinceList2 = {};
            jQuery.each($scope.provinceList, function (i, e) {
                provinceIDs[i] = e.provinceID;
                $scope.provinceList2[e.provinceID] = e.provinceName;
            });
            console.log($scope.provinceList2);
            var queryCityListReq = {};
            queryCityListReq.provinceIDs = provinceIDs;
        }
    };

    //从Local Storage中查询非超管省的方法
    $scope.queryProvinceAndCityFromStorage = function () {
        /*查询省份*/
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
        console.log($scope.provinceList);
        if (!!$scope.provinceList) {
            var provinceIDs = [];
            $scope.provinceList2 = {};
            jQuery.each($scope.provinceList, function (i, e) {
                provinceIDs[i] = e.fieldVal;
                $scope.provinceList2[e.fieldVal] = e.authName;
            });
            console.log($scope.provinceList2);
            var queryCityListReq = {};
            queryCityListReq.provinceIDs = provinceIDs;
        }
    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])