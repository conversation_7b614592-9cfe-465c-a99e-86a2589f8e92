var app = angular.module("myApp", ["util.ajax", "angularI18n", "service.common"]);
app.controller("roleManageEditCtrl", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.openFirst = true;
  $scope.openSecond = true;
  $scope.openThird = true;
  $scope.openFourth = true;
  $scope.provinceList = [];
  $scope.provinceList2 = [];
  $scope.cityList = [];
  $scope.showCountyProIds = ["10"];
  $scope.countyList = [];
  $scope.cityIDs = [];
  $scope.rebuildFirstList = [];
  $scope.rebuildSecondList = [];
  $scope.roleName = "";
  $scope.roleDesc = "";
  $scope.role_dAuthInfoList = [];
  $scope.error = "";
  $scope.errorType = 0;
  $scope.isSubmit = false;
  $scope.allChecked =false;
  $scope.allCityChecked = false;
  $scope.allChecked2 =false;
  $scope.enterpriseTypeList = [];
  $scope.init = function () {
    $scope.roleID = $location.$$search.roleID;
    $scope.operatorID = $.cookie('accountID');
    $scope.queryRole($scope.roleID);
    


  };

  //判断是否选了全国
  $scope.isAllChecked =function(){
    var allChecked =true ;
     //判断是否已经选中全国
     angular.forEach($scope.provinceList, function (province) {
        if(!province.checked){
         allChecked=false;
       }
     })
       $scope.allChecked = allChecked;
    }
  //判断企业数据权限是否选了全国
  $scope.isAllChecked2 =function(){
    var allChecked =true ;
     //判断是否已经选中全国
     angular.forEach($scope.provinceList2, function (province) {
        if(!province.checked){
         allChecked=false;
       }
     })
       $scope.allChecked2 = allChecked;
    }
 //判断是否已经选中全地市
  $scope.isAllCityChecked =function(){
  var allCityChecked =true ;
  angular.forEach($scope.cityList, function (city) {
       if(!city.checked){
          allCityChecked=false;
        }
          })
      $scope.allCityChecked = allCityChecked;
  }
  //判断是否已经选中全区县
  $scope.isAllCountyChecked =function(){
      var allCountyChecked =true ;
      angular.forEach($scope.countyList, function (county) {
          if(!county.checked){
              allCountyChecked=false;
          }
      })
      $scope.allCountyChecked = allCountyChecked;
  }

  $scope.rebuildList = function (oldList) {
    var newlist = [];
    angular.forEach(oldList, function (item) {
      if (item.parentAuthID == null) {
        newlist.push(item)
      }
    });
    angular.forEach(newlist, function (rebuildData) {
      rebuildData.childList = [];
      angular.forEach(oldList, function (oldData) {
        if (oldData.parentAuthID === rebuildData.id) {
          oldData.checked = false;
          angular.forEach($scope.role_fAuthInfoList, function (roleAuth) {
            if (roleAuth.id === oldData.id) {
              oldData.checked = true;
            }
          });
          rebuildData.childList.push(oldData);
        }
      })
    });
    return newlist;
  };

  //查询角色信息
  $scope.queryRole = function (id) {
    var req = {
      roleIDList: [id]
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryRoleList",
      async: false,
      data: JSON.stringify(req),
      success: function (data) {
        if (data && data.result && data.result.resultCode === "1030100000") {
          $scope.role_fAuthInfoList = data.roleList[0].functionAuthList;
          $scope.role_dAuthInfoList = data.roleList[0].dateAuthList;
          $scope.role_enterpriseDAuthInfoList = data.roleList[0].enterpriseDateAuthList;
          $scope.roleName = data.roleList[0].roleName;
          $scope.roleDesc = data.roleList[0].roleDesc;
          
        //获取社会合作管理权限
          $scope.getAuthInfoList({
            authCategory: 1,
            authBelongType: 1
          }, function (data) {
            if (data && data.result && data.result.resultCode === "1030100000") {
              $scope.social_fAuthInfoList = data.functionAuthList;
              //重新构建社会合作管理权限
              $scope.rebuildFirstList = $scope.rebuildList($scope.social_fAuthInfoList);
            } else {
              $scope.tip = data.result.resultCode;
              $("#roleModal").modal();
            }
          }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          });

          //获取分省合作管理权限
          $scope.getAuthInfoList({
            authCategory: 1,
            authBelongType: 2
          }, function (data) {
            if (data && data.result && data.result.resultCode === "1030100000") {
              $scope.province_fAuthInfoList = data.functionAuthList;
              //重新构建社会合作管理权限
              $scope.rebuildSecondList = $scope.rebuildList($scope.province_fAuthInfoList);
            } else {
              $scope.tip = data.result.resultCode;
              $("#roleModal").modal();
            }
          }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          });

          //获取分省合作数据权限
          $scope.getAuthInfoList({
            authCategory: 2
          }, function (data) {
            if (data && data.result && data.result.resultCode === "1030100000") {
              $scope.dAuthInfoList = data.dateAuthList;
              angular.forEach($scope.dAuthInfoList, function (item, index) {
                if (item.fieldName == "provinceID") {
                  item.checked = false;
                  $scope.provinceList.push(item)
                }
                if(item.fieldName == "reserved10" && item.tableName == "dsum_t_enterprise"){
                  $scope.enterpriseTypeList.push(item);
                }
              });

              //获取企业数据权限
              $scope.dAuthInfoList2 = angular.copy(data.dateAuthList);
              angular.forEach($scope.dAuthInfoList2, function (item, index) {
                if (item.fieldName == "provinceID") {
                  item.checked = false;
                  $scope.provinceList2.push(item)
                }
              });
              //勾选企业数据权限
              if ($scope.role_enterpriseDAuthInfoList!=null && $scope.role_enterpriseDAuthInfoList.length > 0) {
                    angular.forEach($scope.role_enterpriseDAuthInfoList, function (row) {
                      angular.forEach($scope.provinceList2, function (item) {
                        if (row.fieldName === "provinceID" && row.id === item.id) {
                          item.checked = true;
                        }
                      });
                    });
              }

              if ($scope.role_dAuthInfoList != null && $scope.role_dAuthInfoList.length > 0) {
                $scope.role_cityList = [];
                $scope.role_countyList = [];
                angular.forEach($scope.role_dAuthInfoList, function (row) {
                  if (row.fieldName === "cityID") {
                    //角色所拥有的市级列表
                    $scope.role_cityList.push(row);
                  }
                  if (row.fieldName === "countyID") {
                      //角色所拥有的区县级列表
                      $scope.role_countyList.push(row);
                  }
                  //勾选角色所拥有的省级区域
                  angular.forEach($scope.provinceList, function (item) {
                    if (row.fieldName === "provinceID" && row.id === item.id) {
                      item.checked = true;
                      //配置有区县信息的省份，如果选中了，则展示数据权限（区县）
                      if($scope.showCountyProIds.includes(item.fieldVal)){
                          $scope.showCountyFlag = true;
                      }
                    }
                  });
                    //勾选角色所拥有的企业类型权限
                    angular.forEach($scope.enterpriseTypeList, function (item) {
                        if (item.fieldName == "reserved10" && item.tableName == "dsum_t_enterprise" && row.id === item.id) {
                            item.checked = true;
                        }
                    });

                  //已勾选的省级区域下的所有市级区域
                  angular.forEach($scope.dAuthInfoList, function (item2) {
                    if (row.fieldName == "provinceID" && item2.fieldName == "cityID" && item2.parentAuthID === row.id) {
                      item2.checked = false;
                      $scope.cityList.push(item2)
                    }
                  });
                });
              }
              //勾选角色所拥有的市级区域
              angular.forEach($scope.role_cityList, function (item) {
                angular.forEach($scope.cityList, function (row) {
                  if (row.id === item.id) {
                    row.checked = true;
                    //汇总已勾选的市级区域有区县信息的地市编码
                    /*if ($scope.showCountyProIds.includes(row.parentAuthID.toString())) {
                        $scope.cityIDs.push(row.fieldVal)
                    }*/
                    angular.forEach($scope.showCountyProIds, function (proId) {
                        if(proId==row.parentAuthID){
                            $scope.cityIDs.push(row.fieldVal)
                        }
                    })
                  }
                });
              });
              //如果地市含有区县，查出已勾选的地市区域下含有的区县数据,查到后进行勾选（isCheckCounty为1进行勾选）
              if($scope.cityIDs.length>0){
                  $scope.queryCounty(1);
              }

            } else {
              $scope.tip = data.result.resultCode;
              $("#roleModal").modal();
            }
             $scope.isAllChecked();
             $scope.isAllChecked2();
             $scope.isAllCityChecked();
          }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          });
        } else {
          $scope.tip = data.result.resultCode;
          $("#roleModal").modal();
        }
      },
      error: function (data) {
        $rootScope.$apply(function () {
        })
      }
    })
  };

  $scope.getAuthInfoList = function (req, success, error) {
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryAuths",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          success(data);
        })
      },
      error: function (data) {
        $rootScope.$apply(function () {
          error();
        })
      }
    })
  };
    //查询区县信息
    $scope.queryCounty = function (isCheckCounty) {
        $scope.getAuthInfoList({
            authCategory: 2,
            dauthType: 2,
            cityIDs: $scope.cityIDs
        }, function (data) {
            if (data && data.result && data.result.resultCode === "1030100000") {
                $scope.dAuthInfoList3 = data.dateAuthList;
                angular.forEach($scope.dAuthInfoList3, function (item, index) {
                    $scope.countyList.push(item)

                });
                //查询出新的区县，则肯定为非全选状态
                $scope.allCountyChecked = false;
                //此项为尽在进入编辑页面初始化时执行
                if(isCheckCounty == 1){
                    //勾选角色所拥有的区县级区域
                    angular.forEach($scope.role_countyList, function (item) {
                        angular.forEach($scope.countyList, function (row) {
                            if (row.id === item.id) {
                                row.checked = true;
                            }
                        });
                    });
                    $scope.isAllCountyChecked();
                }


            } else {
                $scope.tip = data.result.resultCode;
                $("#roleModal").modal();
            }
        }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
        });
    };

  $scope.chooseProvince = function (item) {
    if ($scope.errorType === 1) {
      $scope.errorType = 0;
      $scope.error = "";
    }
    if ($scope.errorType===2) {
        $scope.errorType = 0;
        $scope.error = "";
      }
     var flag = true;
    item.checked = !item.checked;
    if (item.checked) {
      angular.forEach($scope.dAuthInfoList, function (rowData) {
        if (rowData.fieldName == "cityID" && rowData.parentAuthID === item.id) {
          rowData.checked = false; //是否被选中的标识位
          $scope.cityList.push(rowData)
        }
        //判断是否所有省全选
        if(rowData.fieldName == "provinceID" && !rowData.checked){
        flag = false;
         }
          //控制是否展示区县
          if($scope.showCountyProIds.includes(item.fieldVal)){
              $scope.showCountyFlag = true;
          }
      })
    }
    if (!item.checked) {
      for (var i = 0; i < $scope.cityList.length; i++) {
        if ($scope.cityList[i].parentAuthID === item.id) {
          //清除对应的区县
          for (var j = 0; j < $scope.countyList.length; j++) {
              if ($scope.countyList[j].parentAuthID == $scope.cityList[i].fieldVal) {
                  $scope.countyList[j].checked = false;
                  $scope.countyList.splice(j--, 1)
              }
          }
          $scope.cityList[i].checked = false;
          $scope.cityList.splice(i--, 1)
        }
      }
      //判断是否所有省全选
      angular.forEach($scope.dAuthInfoList, function (rowData) {
         if(rowData.fieldName == "provinceID" && !rowData.checked){
             flag = false;
           }
      })
    }
    $scope.allChecked = flag;
    if ($scope.cityList.length===0 && $scope.errorType===2) {
      $scope.errorType = 0;
      $scope.error = "";
    }
    $scope.isAllCityChecked();
  };
  //全国全选功能
  $scope.chooseAllProvince = function () {
    if ($scope.errorType === 1) {
          $scope.errorType = 0;
          $scope.error = "";
        }

        //判断是否已经选中全国

        //已经选中，现在点击表明想取消
        if ($scope.allChecked) {
         angular.forEach($scope.dAuthInfoList, function (rowData) {

             if (rowData.fieldName == "provinceID" || rowData.fieldName == "cityID"){
                 rowData.checked = false;
             }
         })
          $scope.cityList = [];
         $scope.allChecked = false;
          $scope.showCountyFlag = false;
          $scope.countyList = [];
        }
        //选中全国
        else {
         angular.forEach($scope.dAuthInfoList, function (rowData) {
         if (rowData.fieldName == "provinceID"){
            rowData.checked = true;

         } //所有市显示出来

          if (rowData.fieldName == "cityID" && !$scope.cityList.includes(rowData) ){
                      $scope.cityList.push(rowData);
          }

          })
              $scope.allChecked = true;
            $scope.showCountyFlag = true;
        }

        //这里的errortype是什么意思
        if ($scope.cityList.length===0 && $scope.errorType===2) {
          $scope.errorType = 0;
          $scope.error = "";
        }
    $scope.allCityChecked = false;
  };
  //全地市全选功能
   $scope.chooseAllCity = function () {
       if (2 === $scope.errorType) {
           $scope.error = "";
       }
        //取消全选
      if ($scope.allCityChecked) {
       angular.forEach($scope.cityList, function (city) {
                     city.checked = false;
              })
            $scope.allCityChecked = false;
          $scope.allCountyChecked = false;
          $scope.countyList = [];
      }
      //全选
      else{
          $scope.cityIDs=[];
      angular.forEach($scope.cityList, function (city) {
              city.checked = true;
          //汇总有区县信息的地市编码
          /*if ($scope.showCountyProIds.includes(city.parentAuthID.toString())) {
              $scope.cityIDs.push(city.fieldVal)

          }*/
          angular.forEach($scope.showCountyProIds, function (proId) {
              if(proId==city.parentAuthID){
                  $scope.cityIDs.push(city.fieldVal)
              }
          })
       })
          //全选地市时，若选中的地市有区县信息，则查询
          if($scope.cityIDs.length!=0){
              $scope.countyList = [];
              $scope.queryCounty();
              $scope.showCountyFlag = true;
          }
       $scope.allCityChecked = true;
      }
    };
    //全区县全选功能
    $scope.chooseAllCounty = function () {
        if ($scope.errorType === 2) {
            $scope.errorType = 0;
            $scope.error = "";
        }
        //取消全选
        if ($scope.allCountyChecked) {
            angular.forEach($scope.countyList, function (county) {
                county.checked = false;
            })
            $scope.allCountyChecked = false;
            $scope.checkedCountys = [];
        }
        //全选
        else{
            angular.forEach($scope.countyList, function (county) {
                county.checked = true;
            })
            $scope.allCountyChecked = true;
            $scope.checkedCountys = $scope.countyList;
        }
    };
    $scope.showAllCityChecked=function(){
    if(!$scope.allChecked && $scope.cityList.length==0){
    return false;
    }
    return true;
    }
    $scope.showAllCountyChecked=function(){
        if($scope.countyList.length==0){
            return false;
        }
        return true;
    }


  $scope.chooseChild = function (item, errorType) {
    if (errorType === $scope.errorType) {
      $scope.error = "";
    }
    item.checked = !item.checked;
     var allCityChecked =true ;
     angular.forEach($scope.cityList, function (city) {
        if(!city.checked){
            allCityChecked=false;
       }
      })
       $scope.allCityChecked = allCityChecked;
      if (item.checked) {
          /*if ($scope.showCountyProIds.includes(item.parentAuthID.toString())) {
              //rowData.checked = false; //是否被选中的标识位
              $scope.cityIDs=[];
              $scope.cityIDs.push(item.fieldVal)
              $scope.queryCounty();
          }*/
          angular.forEach($scope.showCountyProIds, function (proId) {
              if(proId==item.parentAuthID){
                  $scope.cityIDs=[];
                  $scope.cityIDs.push(item.fieldVal)
                  $scope.queryCounty();
              }
          })
      }
      if(!item.checked){
          var sid = item.fieldVal
          $scope.cityIDs =$scope.cityIDs.filter(param => param != sid)
          for (var i = 0; i < $scope.countyList.length; i++) {
              if ($scope.countyList[i].parentAuthID == item.fieldVal) {
                  $scope.countyList[i].checked = false;
                  $scope.countyList.splice(i--, 1)
              }
          }
          $scope.isAllCountyChecked();
      }
  };
    //点击区县勾选框
    $scope.chooseCounty = function (item,errorType) {
        if (errorType === $scope.errorType) {
            $scope.error = "";
        }
        item.checked = !item.checked;
        var allCountyChecked =true ;
        angular.forEach($scope.countyList, function (county) {
            if(!county.checked){
                allCountyChecked=false;
            }
        })
        $scope.allCountyChecked = allCountyChecked;
    }


  $scope.modifyRole = function () {
    $scope.selected_fAuthInfoList = [];
    $scope.selected_dAuthInfoList = [];
    $scope.checkedProvinceList = [];
    $scope.checkedProvinceList2 = [];
    $scope.checkedCityList = [];
    $scope.checkedCountyList=[];
    var eAuth = [];
    var pAuth = [];
    var hAuth = [];
    var sAuth = [];
    var cAuth = [];
    angular.forEach($scope.rebuildFirstList, function (rebuildData) {
      angular.forEach(rebuildData.childList, function (childData) {
        if (childData.checked) {
          childData.operatorID = $scope.operatorID;
          $scope.selected_fAuthInfoList.push(childData);
          if (childData.authBelongType == 1)
          {
        	  eAuth.push(childData);
        	  if (childData.parentAuthID == 5 || childData.parentAuthID == 11)
              {
        		  sAuth.push(childData);
              }
        	  else
              {
        		  if (childData.id == 2003)
        	      {
            		  cAuth.push(childData);
        	      }
        		  else
        	      {
            		  hAuth.push(childData);
        	      }
              }
          }  
          else if (childData.authBelongType == 2)
          {
        	  pAuth.push(childData);
          }
        }
      })
    });
    angular.forEach($scope.rebuildSecondList, function (rebuildData) {
      angular.forEach(rebuildData.childList, function (childData) {
        if (childData.checked) {
          childData.operatorID = $scope.operatorID;
          $scope.selected_fAuthInfoList.push(childData);
          if (childData.authBelongType == 1)
          {
        	  eAuth.push(childData);
          }  
          else if (childData.authBelongType == 2)
          {
        	  pAuth.push(childData);
          }
        }
      })
    });

    angular.forEach($scope.provinceList2, function (item) {
      if (item.checked) {
        item.operatorID = $scope.operatorID;
        $scope.checkedProvinceList2.push(item);
      }
    });
    var provinceMap = new Map();
    var cityMap = new Map();
    angular.forEach($scope.provinceList, function (item) {
      if (item.checked) {
        item.operatorID = $scope.operatorID;
        $scope.checkedProvinceList.push(item);
        $scope.selected_dAuthInfoList.push(item);
        provinceMap.set(item.id, "1");

      }
    });
    angular.forEach($scope.cityList, function (item) {
      if (item.checked) {
        item.operatorID = $scope.operatorID;
        $scope.checkedCityList.push(item);
        $scope.selected_dAuthInfoList.push(item);
        provinceMap.delete(item.parentAuthID);
        cityMap.set(item.fieldVal, "1");
      }
    });
    angular.forEach($scope.countyList, function (item) {
        if (item.checked) {
            item.operatorID = $scope.operatorID;
            $scope.checkedCountyList.push(item);
            $scope.selected_dAuthInfoList.push(item);
            cityMap.delete(item.parentAuthID.toString());
        }
    });
      $scope.selected_enterpriseTypeList = [];
      //企业类型权限
      //是否选择移动云
      $scope.selected_enterpriseType_ydy = false;
      //是否选择其他企业类型
      $scope.selected_enterpriseType_other = false;
      angular.forEach($scope.enterpriseTypeList, function (item) {
          if (item.checked) {
              if(item.fieldVal == "112" || item.fieldVal == "113"){
                  $scope.selected_enterpriseType_ydy = true;
              }else {
                  $scope.selected_enterpriseType_other = true;
              }
              $scope.selected_enterpriseTypeList.push(item)
          }
      });

    //选择了省  但是未选择市
    if ($scope.checkedProvinceList.length > 0 && $scope.checkedCityList.length === 0) {
      $scope.errorType = 2;
      $scope.error = "您已选择数据权限（省），请选择至少一个数据权限（市）";
      return;
    } else {
      $scope.error = "";
      $scope.errorType = 0;
    }

    if ($scope.selected_fAuthInfoList.length === 0 && $scope.selected_dAuthInfoList.length === 0 && $scope.checkedProvinceList2.length === 0) {
        $scope.errorType = 1;
        $scope.error = "请选择至少一个权限";
        return;
      } else {
        $scope.error = "";
        $scope.errorType = 0;
      }
      
    //i.	“社会合作管理”下，若只选中“系统管理”栏下权限,不校验
    if (sAuth.length != 0 && hAuth.length == 0 && cAuth.length == 0)
    {
    	$scope.error = "";
        $scope.errorType = 0;
    }
    //ii.	“社会合作管理”下，若选中“彩印内容审核”权限，且未选中“系统管理”栏下权限以外的权限，则限制企业数据权限、分省合作数据权限必选一个
    else if (cAuth.length != 0 && hAuth.length == 0 && $scope.checkedProvinceList2.length === 0 && $scope.selected_dAuthInfoList.length === 0 && !$scope.selected_enterpriseType_ydy)
    {
    	$scope.errorType = 1;
        $scope.error = "请选择至少一个数据权限";
        return;
    }
    else
    {
    	if (eAuth.length != 0 && $scope.checkedProvinceList2.length === 0 && hAuth.length != 0 && !$scope.selected_enterpriseType_ydy) {
            $scope.errorType = 1;
            $scope.error = "请选择至少一个企业数据权限";
            return;
        } else {
            $scope.error = "";
            $scope.errorType = 0;
        }
    }

      if (pAuth.length != 0 && $scope.selected_enterpriseTypeList.length === 0) {
          $scope.errorType = 1;
          $scope.error = "请选择企业类型";
          return;
      } else {
          $scope.error = "";
          $scope.errorType = 0;
      }
      //勾选了移动云以外的企业类型
      if (pAuth.length != 0 && $scope.selected_dAuthInfoList.length === 0 && $scope.selected_enterpriseType_other) {
          $scope.errorType = 1;
          $scope.error = "请选择至少一个分省数据权限";
          return;
      } else {
          $scope.error = "";
          $scope.errorType = 0;
      }
      if (provinceMap.size != 0)
      {
      	 $scope.errorType = 2;
           $scope.error = "您已选择数据权限（省），请选择至少一个该省下的数据权限（市）";
           return;
         } else {
           $scope.error = "";
           $scope.errorType = 0;
      }
    //企业类型权限
      $scope.selected_dAuthInfoList = $scope.selected_dAuthInfoList.concat($scope.selected_enterpriseTypeList);

    var req = {
      role: {
        roleID: $scope.roleID,
        roleName: $scope.roleName,
        roleDesc: $scope.roleDesc,
        functionAuthList: $scope.selected_fAuthInfoList,
        dateAuthList: $scope.selected_dAuthInfoList,
        enterpriseDateAuthList:$scope.checkedProvinceList2,
        operatorID: $scope.operatorID
      }
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/updateRole",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          if (data && data.result && data.result.resultCode === "1030100000") {
            $scope.tip = "提交成功";
            $scope.isSubmit = true;
            $("#roleModal").modal();
          } else {
            $scope.isSubmit = false;
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          }
        })
      },
      error: function (data) {
        $rootScope.$apply(function () {
          $scope.isSubmit = false;
          $scope.tip = data.result.resultCode;
          $("#roleModal").modal();
        })
      }
    })
  };
  //企业数据权限全国全选功能
  $scope.chooseAllProvince2 = function () {
    if ($scope.errorType === 1) {
          $scope.errorType = 0;
          $scope.error = "";
    }
    //已经选中，现在点击表明想取消
    if ($scope.allChecked2) {
     angular.forEach($scope.dAuthInfoList2, function (rowData) {
             rowData.checked = false;//选中的省取消，但要显示所有省
      })
     $scope.allChecked2 = false;
      $scope.checkedProvinces2 = [];
    }
    else {
     angular.forEach($scope.dAuthInfoList2, function (rowData) {
         if (rowData.fieldName == "provinceID"){
            rowData.checked = true;

         }
     })
     $scope.allChecked2 = true;
    }
  };
  //企业数据权限选择省份
  $scope.chooseProvince2 = function (item) {
    if ($scope.errorType===1) {
      $scope.errorType = 0;
      $scope.error = "";
    }
    var flag = true;
    item.checked = !item.checked;
    //如果想选中
    if (item.checked) {
      angular.forEach($scope.dAuthInfoList2, function (rowData) {
        //判断是否所有省全选
        if(rowData.fieldName == "provinceID" && !rowData.checked){
             flag = false;
        }
      })
    }
    //取消省份勾选
    if (!item.checked) {
      //判断是否所有省全选
      angular.forEach($scope.dAuthInfoList2, function (rowData) {
         if(rowData.fieldName == "provinceID" && !rowData.checked){
                flag = false;
          }
       })
    }
    $scope.allChecked2 = flag;
  };

  $scope.goBack = function () {
    window.location.href = "../list/roleManage_list.html";
  }

});
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}]);