var app = angular.module("myApp", ["util.ajax", "top.menu","angularI18n","cy.uploadifyfile","service.common"])
app.controller('enterpriseController', function ($scope, $rootScope, $http, $location, RestClientUtil,CommonUtils) {
	  	// 上传
	    $scope.accepttype = "jpg,jpeg,png,xlsx,doc,pdf";
	    $scope.isValidate = true;
	    $scope.filesize = 20;
	    $scope.mimetypes = ".jpg,.jpeg,.png,.xlsx,.doc,.pdf";
	    $scope.auto = true;
	    $scope.isCreateThumbnail = false;
	    $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadAllTypeFile';
	    $scope.uploadDesc = "必填，仅支持jpg，jpeg，png，xlsx，doc，pdf";
	    $scope.numlimit = 1;
	    $scope.urlList = [];
	    //默认非敏感行业:0-非敏感；1-敏感
	    $scope.selectedIndustryID = '';
	    $scope.isSensitiveIndustry = '';

	    // 上传
	    $scope.$on("uploadifyid1", function (event, fileUrl, index, broadData) {
	      if(broadData){
	    	  if (broadData.file !== "") {
		    	  $scope.businessLicenseURL.fileName = broadData.file.name;
		      } else {
		    	  $scope.businessLicenseURL.fileName = "";
		      }
	    	  $scope.uploader = broadData.uploader;
		      $scope.businessLicenseURL.errorInfo = broadData.errorInfo;
	      }
            $scope.businessLicenseURL.upload = fileUrl;
	    });
	    $scope.$on("uploadifyid2", function (event, fileUrl, index, broadData) {
	      if(broadData){
	    	  if (broadData.file !== "") {
		    	  $scope.guaranteeLetterUrl.fileName = broadData.file.name;
		      } else {
		    	  $scope.guaranteeLetterUrl.fileName = "";
		      }
	    	  $scope.uploader = broadData.uploader;
		      $scope.guaranteeLetterUrl.errorInfo = broadData.errorInfo;
	      }
	      $scope.guaranteeLetterUrl.upload = fileUrl;
	    });

    $scope.enterType = $location.$$search.enterType;

  /* 查询省市 */
  $scope.queryProvinceAndCity = function ($scope) {
    var queryProvinceListReq = {};
    /*查询省份*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
      data: JSON.stringify(queryProvinceListReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.provinceList = data.provinceList;
            if (!!$scope.provinceList) {
              var provinceIds = [];
              jQuery.each($scope.provinceList, function (i, e) {
                provinceIds[i] = e.provinceID;
              });
              var queryCityListReq = {};
              queryCityListReq.provinceIDs = provinceIds;
              
              /*查询地市*/
              RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
                data: JSON.stringify(queryCityListReq),
                success: function (data) {
                  $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                      $scope.cityList = $scope.mapToList(data.cityList);
                      if ($scope.operate == 'detail' || $scope.operate == 'edit' || $scope.operate == 'audit') {
                        $scope.changeSelectedProvince($scope.enterpriseInfo.provinceID);
                      }
                    }else {
    	                $scope.tip =result.resultCode;
    	                $('#myModal').modal();
    	              }
                  })
                },
                error:function(){
                    $rootScope.$apply(function(){
                        $scope.tip='**********';
                        $('#myModal').modal();
                        }
                    )
                }
              });
            }
          }else {
              $scope.tip=data.result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
        	  	  $scope.tip = '**********';
                  $('#myModal').modal();
              }
          )
      }
    });
  };

  /* 查询所属行业 added by wwx470949 2019-4-15 */
  $scope.queryIndustry = function ($scope) {
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
              $scope.industryList = data.industryList;
          }else {
              $scope.tip=data.result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
        	  	  $scope.tip = '**********';
                  $('#myModal').modal();
              }
          )
      }
    });
  };

  /* 所属行业是否为敏感行业 */
  $scope.changeIsSensitive = function(selectedIndustry){
      if(selectedIndustry){
          $scope.selectedIndustryID = selectedIndustry.industryID;
          $scope.isSensitiveIndustry = selectedIndustry.isSensitiveIndustry;
      }else{
          $scope.selectedIndustryID = '';
          $scope.selectedIndustryName = '';
      }

      if($scope.selectedIndustryID){
         $scope.selectedIndustryErrorInfo = '';
         if($scope.isSensitiveIndustry==0||$scope.isSensitiveIndustry==2){
            $scope.businessLicenseURL.errorInfo = '';
         }
      }else{
         $scope.selectedIndustryErrorInfo = 'ENTERPRISE_INDUSTRY_CHECK';
      }
  }
  $scope.changeIsSensitive2 = function(){
        var industryTemp = jQuery("select[name='industry2']").val();
        var index = industryTemp.lastIndexOf(":");
        industryTemp = industryTemp.substring(index+1,industryTemp.length);
        jQuery.each($scope.industryList, function (i, e) {
            if (e.industryID == industryTemp) {
                //所属行业赋值
                $scope.selectedIndustryID = industryTemp;
                $scope.selectedIndustryName = e.industryName;
                $scope.isSensitiveIndustry = e.isSensitiveIndustry;
            }
        });

        if($scope.selectedIndustryID){
           $scope.selectedIndustryErrorInfo = '';
           if($scope.isSensitiveIndustry==0||$scope.isSensitiveIndustry==2){
              $scope.businessLicenseURL.errorInfo = '';
           }
        }else{
           $scope.selectedIndustryErrorInfo = 'ENTERPRISE_INDUSTRY_CHECK';
        }
  }

  /* 初始化新增企业 */
  $scope.initEnterprise = function ($scope) {
      $scope.businessLicenseShow = true;
      $scope.guaranteeLetterShow = true;
	$scope.id = JSON.parse($.cookie("subEnterpriseID"));
    $scope.enterpriseInfo = {};
    $scope.fileUrl ='';
    $scope.businessLicenseURL ={};
    $scope.guaranteeLetterUrl ={};
    $scope.urlList =[];
    $scope.urlList2 =[];
    $scope.preUrlList =[];
    $scope.preUrlList2 =[];
    var loginRoleType=$.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
    $scope.isZhike = (loginRoleType=='zhike');
    $scope.isAgent = (loginRoleType=='agent');
    $scope.isProvincial = (loginRoleType=='provincial');
    $scope.enterpriseID = $.cookie("enterpriseID");
    //获取operationType和enterpriseType
    var operationType = $location.search().operationType;
    if (operationType) {
      $scope.operate = operationType;
    }else{
      $scope.operate = $.cookie("operationType");
      if(!$scope.isSuperManager){
    	  $scope.operate = 'edit';
      }
    }
    if ($scope.operate == 'add') {
        $scope.id = '';
      }
  	$scope.uploadParam = {
  		enterpriseId:$scope.id||'',
  	    fileUse: 'businessLicense'
  	  };
  	  $scope.uploadParam2 = {
  		enterpriseId:$scope.id||'',
  	    fileUse: 'guaranteeLetter'
  	  };
    $scope.enterpriseType = 3;

    $scope.enterpriseNameValidate = true;
    $scope.custIDValidate = true;
    $scope.organizationIDValidate = true;
    $scope.contractValidate = true;
    $scope.msisdnValidate = true;
    $scope.passwordValidate = true;
    $scope.rePasswordValidate = true;
    $scope.businessLicenseVali ='true';
    $scope.passwordValidateDesc = '';
    $scope.queryProvinceAndCity($scope);
    $scope.queryIndustry($scope);//获取所属行业信息
    $scope.parentEnterpriseName = $.cookie("enterpriseName");
    if (!$scope.parentEnterpriseName) {
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
        data: JSON.stringify({"id":$scope.enterpriseID}),
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '**********') {
              $scope.parentEnterpriseName = data.enterprise.enterpriseName;
            }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
          })
        },
        error:function(){
          $rootScope.$apply(function(){
                $scope.tip='**********';
                $('#myModal').modal();
              }
          )
        }
      });

    }
    if ($scope.operate == 'add') {
      $scope.enterpriseInfo.showUpload = true;
    }
    if ($scope.operate == 'edit') {
	    $scope.enterpriseInfo.showUpload = true;
	    $scope.queryEnterpriseDetails($scope);


	  }
    if ($scope.operate == 'detail' || $scope.operate == 'audit') {
      $scope.queryEnterpriseDetails($scope);
    }

  };

  /*查询企业详情 */
  $scope.queryEnterpriseDetails = function ($scope) {
    var req = {};
    req.id = $scope.id;
    var pageParameter = {};
    pageParameter.pageNum = 1;
    pageParameter.pageSize = 1000;
    pageParameter.isReturnTotal = 1;
    req.pageParameter = angular.copy(pageParameter);
    /*查询企业列表*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.enterpriseInfo = data.enterprise;
            if($scope.enterpriseInfo.enterpriseName===$scope.parentEnterpriseName){
                $scope.businessLicenseShow = false;
                // $scope.guaranteeLetterShow = false; //名字相同时，不再隐藏企业担保函了
            }

            $scope.setEnterpriseDetails($scope);
              $scope.ShowServiceProduct = true
              $scope.showMemberQuota = true
            $scope.queryServiceProduct($scope);
            $scope.queryMemberQuota($scope);

          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };

  /* 新增时修改省份 */
  $scope.changeSelectedProvince = function (selectedProvinceID) {
	  angular.forEach($scope.cityList, function (e, i) {
			if (e.key == selectedProvinceID) {
		        $scope.subCityList = angular.copy(e);
		      }
	    });
	    if (!selectedProvinceID || selectedProvinceID =='000') {
	      $scope.subCityList = null;
	      $scope.selectedCityID = null;
	    }else{
	    	if($scope.subCityList){
	    		$scope.selectedCity =$scope.subCityList[0];
	    		$scope.selectedCityID =$scope.selectedCity.cityID;
	    		$scope.selectedCityName =$scope.selectedCity.cityName;
	        	delete($scope.subCityList.key);
	    	}
	    }
	    if ($scope.subCityList) {
	        if ($scope.operate == 'detail' || $scope.operate == 'edit' || $scope.operate == 'audit') {
	  	    	$scope.provinceID = selectedProvinceID;
  	    		jQuery.each($scope.subCityList, function (i, e) {
  	    	        if (e.cityID == $scope.enterpriseInfo.cityID) {
  	    	        	$scope.selectedCity = e;
  	    	        }
  	    	      });
  	    		$scope.selectedCityID = $scope.selectedCity.cityID;
	  	    }
	      }
  }
  $scope.changeSelectedCity=function(){
    $scope.selectedCityID =$scope.selectedCity.cityID;
  }
  
  /* 非新增时企业详情 赋值*/
  $scope.setEnterpriseDetails = function ($scope) {
    $scope.passwordValidate = true;
    $scope.parentEnterpriseName = $scope.enterpriseInfo.parentEnterpriseName;
    $scope.enterpriseNameTemp = $scope.enterpriseInfo.enterpriseName;
    $scope.organizationIDTemp = $scope.enterpriseInfo.organizationID||"";
    $scope.cityIDTemp = $scope.enterpriseInfo.cityID;
    $scope.provinceIDTemp = $scope.enterpriseInfo.provinceID;

    //所属行业
    if($scope.enterpriseInfo.reservedsEcpmp && $scope.enterpriseInfo.reservedsEcpmp.reserved5){
        var industryID = $scope.enterpriseInfo.reservedsEcpmp.reserved5;
        //查询所属行业列表
        $scope.queryIndustry($scope);
        //用id匹配出符合条件的名称
        if ($scope.industryList) {
            if ($scope.operate == 'detail' || $scope.operate == 'edit' || $scope.operate == 'audit') {
                jQuery.each($scope.industryList, function (i, e) {
                    if (e.industryID == industryID) {
                        //所属行业赋值
                        $scope.industry = e;
                        $scope.selectedIndustryName = e.industryName;
                        $scope.selectedIndustryID = e.industryID;
                        $scope.isSensitiveIndustry = e.isSensitiveIndustry;
                    }
                });
            }
          }
    }
    //是否同名
      if($scope.enterpriseInfo.enterpriseName!==$scope.parentEnterpriseName){
          if($scope.enterpriseInfo.businessLicenseURL){
              $scope.businessLicenseURL = CommonUtils.formatPic($scope.enterpriseInfo.businessLicenseURL);
              $scope.businessLicenseURL.upload =$scope.enterpriseInfo.businessLicenseURL;
              $scope.businessLicenseURL.fileName =$scope.businessLicenseURL.picName;
              $scope.businessLicenseURLTemp = $scope.enterpriseInfo.businessLicenseURL;
              $scope.urlList=[$scope.businessLicenseURL.review];
          }

          if($scope.enterpriseInfo.extInfo){
              if($scope.enterpriseInfo.extInfo.guaranteeLetterUrl){
                  $scope.guaranteeLetterUrl = CommonUtils.formatPic($scope.enterpriseInfo.extInfo.guaranteeLetterUrl);
                  $scope.guaranteeLetterUrl.upload =$scope.enterpriseInfo.extInfo.guaranteeLetterUrl;
                  $scope.guaranteeLetterUrl.fileName =$scope.guaranteeLetterUrl.picName;
              }
              $scope.urlList2=[$scope.guaranteeLetterUrl.review];
          }
      }else {
          if($scope.enterpriseInfo.extInfo){
              if($scope.enterpriseInfo.extInfo.guaranteeLetterUrl){
                  $scope.guaranteeLetterUrl = CommonUtils.formatPic($scope.enterpriseInfo.extInfo.guaranteeLetterUrl);
                  $scope.guaranteeLetterUrl.upload =$scope.enterpriseInfo.extInfo.guaranteeLetterUrl;
                  $scope.guaranteeLetterUrl.fileName =$scope.guaranteeLetterUrl.picName;
              }
              $scope.urlList2=[$scope.guaranteeLetterUrl.review];
          }
          // if($scope.enterpriseInfo.extInfo){
          //     if($scope.enterpriseInfo.extInfo.guaranteeLetterUrl){
          //         $scope.guaranteeLetterUrl = CommonUtils.formatPic($scope.enterpriseInfo.extInfo.guaranteeLetterUrl);
                 // $scope.guaranteeLetterUrl.upload =true;
                  // $scope.guaranteeLetterUrl.fileName =$scope.guaranteeLetterUrl.picName;
              // }
              // $scope.urlList2=[$scope.guaranteeLetterUrl.review];
          // }
      }

    $scope.extInfoTemp = $scope.enterpriseInfo.extInfo;
    
    if ($scope.cityList) {
      $scope.changeSelectedProvince($scope.enterpriseInfo.provinceID);
      if ($scope.operate == 'detail' || $scope.operate == 'edit' || $scope.operate == 'audit') {
	    	$scope.provinceID = $scope.enterpriseInfo.provinceID;
	    	if($scope.subCityList){
	    		jQuery.each($scope.subCityList, function (i, e) {
	    	        if (e.cityID == $scope.enterpriseInfo.cityID) {
	    	        	$scope.selectedCity = e;
	    	        }
	    	      });
	    		$scope.selectedCityID = $scope.selectedCity.cityID;
	    	}
	    }
    }
  };


  /* 验证企业名称 */
  $scope.checkEnterpriseName = function (enterpriseName,condition,first) {
      //如果名字一样 隐藏 营业执照和企业担保函并清空值
      if($scope.enterpriseInfo.enterpriseName===$scope.parentEnterpriseName){
          $scope.businessLicenseShow = false;
          //$scope.guaranteeLetterShow = false;//名字相同时，不再隐藏企业担保函了
          $scope.businessLicenseURL.upload = true;
          // $scope.guaranteeLetterUrl.upload = true;//名字相同时，不再清空企业担保函了
          // $scope.businessLicenseURL.errorInfo = '';
          // $scope.businessLicenseURL.fileName = '';
          // $scope.guaranteeLetterUrl.fileName = '';
          // $scope.guaranteeLetterUrl.errorInfo = '';
          // $scope.guaranteeLetterUrl.upload = fileUrl;
      }else {
          if(!$scope.businessLicenseShow){  //原先处于隐藏状态,再显示时清空上传内容
              $scope.businessLicenseURL = {};
              // $scope.guaranteeLetterUrl = {};//名字相同时，不再清空企业担保函上传的内容
              $("div[uploadifyid='uploadifyid1']").find("span").text("上传文件");
              // $("div[uploadifyid='uploadifyid2']").find("span").text("上传文件");//名字相同时，不再清空企业担保函上传的内容

          }
          $scope.businessLicenseShow = true;
          $scope.guaranteeLetterShow = true;
      }

	  $scope.condition =condition;
	  $scope.enterpriseNameValidate =true;
	  $scope.enterpriseInfo.enterpriseNameDesc ='';
	  if($scope.enterpriseNameTemp == enterpriseName && $scope.operate=='edit'){
		  $scope.enterpriseNameValidate =true;
		  $scope.enterpriseInfo.enterpriseNameDesc = '';
		  $scope.enterpriseNameExist =false;
		  if($scope.condition =='save'){
			  $scope.update();
		  }else{
			  return;
		  }
	  }else{
		  
		  //判断子企业是否为空
		  if(undefined == enterpriseName)
		  {
			  return;
		  }
		  
		  $scope.enterpriseNameExist = false;
	      var QueryAccountListReq = {};
	      QueryAccountListReq.content = angular.copy(enterpriseName);
	      
	      QueryAccountListReq.serviceType = 6;
	      /*模糊匹配企业名称，匹配到则飘红*/
	      RestClientUtil.ajaxRequest({
	        type: 'POST',
	        url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
	        data: JSON.stringify(QueryAccountListReq),
	        success: function (data) {
	          $rootScope.$apply(function () {
	            var result = data.result;
	            $scope.enterpriseInfo.enterpriseNameDesc = '';
	            //是否存在重复的用户名称
	            if (result.resultCode =='**********') {
	              $scope.enterpriseNameExist = true;
	              $scope.enterpriseInfo.enterpriseNameDesc = 'ENTERPRISE_SUBENTERPRISENAMEEXIST';
	            }
	            if(result.resultCode !='**********' && result.resultCode !='**********'){
	            	$scope.tip = result.resultCode;
	                $('#myModal').modal();
	            }
                if(first){
                    if($scope.condition =='save' && result.resultCode =='**********'){
                        if($scope.operate == 'add'){
                            $scope.save();
                        }
                        if($scope.operate == 'edit'){
                            $scope.update();
                        }
                    }
                }
	          })
	        },
	        error:function(){
	            $rootScope.$apply(function(){
	                $scope.tip='**********';
	                $('#myModal').modal();
	                }
	            )
	        }
	      });
		  
		  /*$scope.enterpriseNameValidate = $scope.validate(enterpriseName, 256, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
		    if (!$scope.enterpriseNameValidate) {
		      $scope.enterpriseInfo.enterpriseNameDesc = 'ENTERPRISE_SUBENTERPRISENAMEDESC';
		    } else {
		      $scope.enterpriseNameExist = false;
		      var QueryAccountListReq = {};
		      QueryAccountListReq.content = angular.copy(enterpriseName);
		      QueryAccountListReq.serviceType = 1;
		      模糊匹配企业名称，匹配到则飘红
		      RestClientUtil.ajaxRequest({
		        type: 'POST',
		        url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
		        data: JSON.stringify(QueryAccountListReq),
		        success: function (data) {
		          $rootScope.$apply(function () {
		            var result = data.result;
		            $scope.enterpriseInfo.enterpriseNameDesc = '';
		            //是否存在重复的用户名称
		            if (result.resultCode =='**********') {
		              $scope.enterpriseNameExist = true;
		              $scope.enterpriseInfo.enterpriseNameDesc = 'ENTERPRISE_SUBENTERPRISENAMEEXIST';
		            }
		            if(result.resultCode !='**********' && result.resultCode !='**********'){
		            	$scope.tip = result.resultCode;
		                $('#myModal').modal();
		            }
		            if($scope.condition =='save' && result.resultCode =='**********'){
		            	if($scope.operate == 'add'){
		            		$scope.save();
		            	}
		            	if($scope.operate == 'edit'){
		            		$scope.update();
		            	}
		            }
		          })
		        },
		        error:function(){
		            $rootScope.$apply(function(){
		                $scope.tip='**********';
		                $('#myModal').modal();
		                }
		            )
		        }
		      });
		    }*/
	  }
  };

  $scope.mapToList = function (map) {
    var result = [];
    jQuery.each(map, function (_, o) {
    	if(_=='000'){
    		return;
    	}
        if (o) {
            o.key =o[0].provinceID;
            result.push(o);
        }
    });
    return result;
  };

  /* 验证organizationID */
  $scope.checkOrganizationID = function (organizationID) {
    $scope.organizationIDValidate = $scope.validate(organizationID, 32, /^[0-9a-zA-Z]+$/, false);
  };

  /*保存前验证与赋值 */
  $scope.beforeSave = function () {
	$scope.condition ='save';
    $scope.enterpriseInfo.enterpriseType = $scope.enterpriseType;
    $scope.checkOrganizationID($scope.enterpriseInfo.organizationID);
    if($scope.organizationIDValidate){
    	//保存前再校验一遍唯一性与密码规则
        $scope.checkEnterpriseName($scope.enterpriseInfo.enterpriseName,$scope.condition,true);
    }

  };

  /* 所属行业为敏感行业，营业执照必填 added by wwx470949 2019-4-16*/
  $scope.checkSensitive = function (isSensitive,businessLicenseURL) {
      //所属行业必填
      if (!$scope.selectedIndustryID) {
         $scope.selectedIndustryErrorInfo = 'ENTERPRISE_INDUSTRY_CHECK';
         return true;
      }
      //敏感行业营业执照必填
      if ((isSensitive == 1 || isSensitive == 3) && !businessLicenseURL) {
         $scope.businessLicenseURL.errorInfo = 'ENTERPRISE_BUSINESSLICENSE_CHECK';
         return true;
      }
      return false;
  }

  /*保存*/
  $scope.save = function () {
	if ($scope.enterpriseNameExist == true) {
		return;
    }

    if($scope.checkSensitive($scope.isSensitiveIndustry,$scope.businessLicenseURL.upload)){
        return;
    }
    var createEnterpriseReq = {};
    createEnterpriseReq.enterprise = angular.copy($scope.enterpriseInfo);
    createEnterpriseReq.enterprise.operatorID = $.cookie("accountID");
    createEnterpriseReq.enterprise.provinceID =$scope.provinceID;
    createEnterpriseReq.enterprise.cityID =$scope.selectedCityID;
    createEnterpriseReq.enterprise.businessLicenseURL = $scope.businessLicenseURL.upload;
    createEnterpriseReq.enterprise.parentEnterpriseID = $.cookie("enterpriseID");
    createEnterpriseReq.enterprise.parentEnterpriseName = $scope.parentEnterpriseName;
    var ecpmReserveds = {};
    createEnterpriseReq.enterprise.ecpmReserveds = ecpmReserveds;
    createEnterpriseReq.enterprise.ecpmReserveds.reserved5 = $scope.selectedIndustryID;    //所属行业
    createEnterpriseReq.enterprise.ecpmReserveds.reserved6 = $scope.isSensitiveIndustry;   //所属行业是否为敏感行业
    createEnterpriseReq.enterprise.extInfo={};
    createEnterpriseReq.enterprise.extInfo.guaranteeLetterUrl = $scope.guaranteeLetterUrl.upload;
    delete(createEnterpriseReq.enterprise.enterpriseNameDesc);
    delete(createEnterpriseReq.enterprise.passwordValidate);
    delete(createEnterpriseReq.enterprise.showUpload);
    //代理商和子企业名字一样 清空上传文件
      if($scope.enterpriseInfo.enterpriseName===$scope.parentEnterpriseName) {
          delete(createEnterpriseReq.enterprise.businessLicenseURL);
         // delete(createEnterpriseReq.enterprise.extInfo.guaranteeLetterUrl);
      }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/createEnterprise",
      data: JSON.stringify(createEnterpriseReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
              if($scope.isSuperManager){
            	  location.href = '../enterpriseList/enterpriseList.html';
	      	  }
	      	  if($scope.isAgent && $scope.operate =='add'){
	      		  location.href = '../../../secondEnterpriseManage/enterpriseList/enterpriseList.html';
	      	  }
	      	  if($scope.isAgent && $scope.operate =='edit'){
	      		  location.href = '../../../secondEnterpriseManage/enterpriseList/enterpriseList.html';
	      	  }
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };

  /*更新*/
  $scope.update = function () {
    if ($scope.enterpriseNameExist == true) {
			return;
    }
    if($scope.checkSensitive($scope.isSensitiveIndustry,$scope.businessLicenseURL.upload)){
        return;
    }
    var updateEnterpriseReq = {
    		"enterprise":{
    			"id":$scope.enterpriseInfo.id,
    			"enterpriseName":$scope.enterpriseInfo.enterpriseName,
    			"enterpriseType":$scope.enterpriseType,
    			"cityID":$scope.selectedCityID,
    			"provinceID":$scope.provinceID,
    			"businessLicenseURL":$scope.businessLicenseURL.upload,
    			"reservedsEcpmp":{
    			    "reserved5":$scope.selectedIndustryID,
    			    "reserved6":$scope.isSensitiveIndustry
    			},
    			"extInfo":{}
    		}
    };
    if($scope.extInfoTemp){
    	updateEnterpriseReq.enterprise.extInfo.submitTime =$scope.extInfoTemp.submitTime;
        updateEnterpriseReq.enterprise.extInfo.auditTime =$scope.extInfoTemp.auditTime;
        updateEnterpriseReq.enterprise.extInfo.auditor =$scope.extInfoTemp.auditor;
    }
    updateEnterpriseReq.enterprise.extInfo.guaranteeLetterUrl =$scope.guaranteeLetterUrl.upload;
    
    if($scope.enterpriseInfo.auditStatus ==2 || $scope.enterpriseInfo.auditStatus ==5){
    	updateEnterpriseReq.enterprise.auditStatus =4;
    }
    if($scope.enterpriseNameTemp ==updateEnterpriseReq.enterprise.enterpriseName){
    	delete(updateEnterpriseReq.enterprise.enterpriseName);
    }
    if($scope.provinceIDTemp ==updateEnterpriseReq.enterprise.provinceID){
    	delete(updateEnterpriseReq.enterprise.provinceID);
    }
    if($scope.cityIDTemp ==updateEnterpriseReq.enterprise.cityID){
    	delete(updateEnterpriseReq.enterprise.cityID);
    }

    if($scope.extInfoTemp ==updateEnterpriseReq.enterprise.extInfo){
    	delete(updateEnterpriseReq.enterprise.extInfo);
    }
    updateEnterpriseReq.enterprise.operatorID = $.cookie("accountID");
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/updateEnterprise",
      data: JSON.stringify(updateEnterpriseReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
        	  // if($scope.isSuperManager){
        		//   location.href = '../enterpriseList/enterpriseList.html';
              //
        	  // }else{
        		//   $scope.tip = 'COMMON_SAVESUCCESS';
              //     $('#myModal').modal();
              //     if($scope.enterType == 1){
              //         location.href = '../../../../InfoAudit/secondEnterpriseInfoAudit/secondEnterpriseInfoAudit.html';
              //         return;
              //     }
        	  // }
              $scope.ensureToList();
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };
  
  
  /*点击取消返回列表页面*/
  $scope.cancelToEnterpriseList = function ($scope) {
	if($scope.operate == 'audit'){
		location.href = '../../../../InfoAudit/secondEnterpriseInfoAudit/secondEnterpriseInfoAudit.html';
	}
	else if ($scope.operate == 'detail') {
		if($scope.isSuperManager){
			location.href = '../enterpriseList/enterpriseList.html';
		}
		if($scope.isAgent){
			location.href = '../../../secondEnterpriseManage/enterpriseList/enterpriseList.html';
		}
    }
    else {
    	$scope.tip='COMMON_RETURNTOLIST';
        $('#ensureToList').modal();
    }
  };
  
  /*确认返回列表页面*/
  $scope.ensureToList = function () {


      if($scope.enterType == 1){
          location.href = '../../../../InfoAudit/secondEnterpriseInfoAudit/secondEnterpriseInfoAudit.html';
          return;
      }
	  if($scope.isSuperManager){
		  location.href = '../enterpriseList/enterpriseList.html';
	  }
	  if($scope.isAgent && $scope.operate =='add'){
		  location.href = '../../../secondEnterpriseManage/enterpriseList/enterpriseList.html';
	  }
	  if($scope.isAgent && $scope.operate =='edit'){
		  location.href = '../../../secondEnterpriseManage/enterpriseList/enterpriseList.html';
	  }
  };
  $scope.exportFile = function (downloadUrl) {
	  var req = {
			   "param":{
				   "path": downloadUrl,
	                "token": $.cookie("token"),
	                "isExport": 0
			   },
		       "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
		       "method":"get"
		     }
		     CommonUtils.exportFile(req);
  };
  /*校验各个必填字段*/
  $scope.validate = function (context, maxlength, reg, required) {
    if (!context && required) {
      return false;
    }
    if(context){
    	if (context.length > maxlength) {
            return false;
          } else {
            if (!reg.test(context)) {
              return false;
            } else {
              return true;
            }
          }
    }
    return true;
  };
    /**
     * 调用ecpmp的获取配置项接口（getProperties），获取企业业务套餐配置项
     * 解析为企业业务套餐列表
     */
    $scope.getProperties = function() {
        var keys=new Array("ecpmp.enterprise.service.product");
        var req = {
            "keys": keys
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/getProperties",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '**********') {

                        for(var key in result.props){
                            var productStr = result.props[ key ];
                        }
                        var products = productStr.substring(1,productStr.length-1);
                        var productList = products.split("|");
                        var productTypes = new Array();
                        for (let i = 0; i < productList.length; i++) {
                            productTypes.push($.parseJSON(productList[i]))
                        }
                        $scope.productTypeList = productTypes
                        // 根据企业业务套餐产品ID，从企业业务套餐列表获取对应产品名称、配额描述
                        for (let i = 0; i < $scope.productTypeList.length; i++) {
                            var productType = $scope.productTypeList[i];
                            if (productType.productId == $scope.serviceProduct.productID){
                                //展示套餐名称、套餐描述，“套餐名称”取产品名称，“套餐描述”取配额描述
                                $scope.packagesName = productType.productName
                                $scope.packagesDesc = productType.quotaDescription
                            }
                        }
                    }
                })
            }
        });
    }
    /**
     *根据当前子企业ID，调用ecpm查询企业业务套餐接口（queryServiceProduct），获取企业业务套餐产品ID
     * @param item
     *
     */
    $scope.queryServiceProduct=function($scope){

        var req = {
            "enterpriseID":$scope.enterpriseInfo.id,
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryServiceProduct",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        if (undefined == result.serviceProduct){
                            $scope.ShowServiceProduct = false
                        }
                        $scope.serviceProduct=result.serviceProduct
                        //若企业业务套餐产品ID非空
                        if (undefined != $scope.serviceProduct.productID){
                            $scope.getProperties()
                        }
                    }
                })
            },
        })
    }

    /**
     *  根据当前子企业企业ID，调用ecpm查询企业成员配额接口（queryMemberQuota），获取企业成员配额信息
     */
    $scope.queryMemberQuota=function($scope){

        var req = {
            "enterpriseID": $scope.enterpriseInfo.id,
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryMemberQuota",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        if (undefined != result.memberQuota){
                            $scope.UsedMemberQuota = result.memberQuota
                            $scope.RemainingMemberCount = result.memberQuota - result.memberCount

                        }else {
                            $scope.showMemberQuota = false

                        }


                    }
                })
            }
        })
    }
});
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])



