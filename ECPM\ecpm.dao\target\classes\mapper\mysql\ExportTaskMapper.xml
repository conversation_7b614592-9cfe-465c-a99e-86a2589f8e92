<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ExportTaskMapper">


	<resultMap id="exportTaskMapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ExportTaskWrapper">
		<result property="taskID" column="taskID" javaType="java.lang.Integer" />
		<result property="fileName" column="fileName" javaType="java.lang.String" />
		<result property="taskType" column="taskType" javaType="java.lang.Integer" />
		<result property="taskStatus" column="taskStatus" javaType="java.lang.Integer" />
		<result property="params" column="params" javaType="java.lang.String" />
		<result property="filePath" column="filePath" javaType="java.lang.String" />
		<result property="exportTime" column="exportTime" javaType="java.util.Date" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

	<select id="getExportTaskInfo" resultMap="exportTaskMapper">
		select
		t.taskID,
		t.fileName,
		t.taskType,
		t.taskStatus,
		t.params,
		t.filePath,
		t.exportTime,
		t.createTime,
		t.updateTime,
		t.extInfo,
		t.operatorID,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10
		from ecpm_t_export_task t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="fileName != null  and fileName !=''  ">
				and t.fileName like concat("%", #{fileName}, "%")
			</if>
			<if test="operatorID != null">
				and t.operatorID = #{operatorID}
			</if>
			<if test="taskStatus != null">
				and t.taskStatus = #{taskStatus}
			</if>
			<if test="taskType != null">
				and t.taskType = #{taskType}
			</if>
			<if test="startTime != null">
				and t.exportTime <![CDATA[ > ]]> #{startTime}
			</if>
			<if test="endTime != null">
				and t.exportTime <![CDATA[ < ]]> #{endTime}
			</if>
		</trim>

		<if test="sequenceType != null and sequenceType == 0">
			order by t.exportTime asc
		</if>
		<if test="sequenceType != null and sequenceType == 1">
			order by t.exportTime desc
		</if>

		<if test="page != null and page.pageNum != null and page.pageSize != null">
			limit #{page.pageNum},#{page.pageSize}
		</if>

	</select>

	<select id="getExportTaskInfoCount" resultType="java.lang.Integer">
		select distinct count(1)
		from ecpm_t_export_task t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="fileName != null  and fileName !=''  ">
				and t.fileName like concat("%", #{fileName}, "%")
			</if>
			<if test="operatorID != null">
				and t.operatorID = #{operatorID}
			</if>
			<if test="taskStatus != null">
				and t.taskStatus = #{taskStatus}
			</if>
			<if test="taskType != null">
				and t.taskType = #{taskType}
			</if>
			<if test="startTime != null">
				and t.exportTime <![CDATA[ > ]]> #{startTime}
			</if>
			<if test="endTime != null">
				and t.exportTime <![CDATA[ < ]]> #{endTime}
			</if>
		</trim>
	</select>

	<select id="getExportTaskInfoByTaskID" resultMap="exportTaskMapper">
		select
		t.taskID,
		t.fileName,
		t.taskType,
		t.taskStatus,
		t.params,
		t.filePath,
		t.exportTime,
		t.createTime,
		t.updateTime,
		t.extInfo,
		t.operatorID,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10
		from ecpm_t_export_task t
		where t.taskID = #{taskID}
	</select>

    <insert id="createExportTask" useGeneratedKeys="true" keyProperty="taskID"
            parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ExportTaskWrapper">
    insert into ecpm_t_export_task
		(
		fileName,
		taskType,
		taskStatus,
		params,
		filePath,
		exportTime,
		createTime,
		extInfo,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		values
		(
		#{fileName},
		#{taskType},
		#{taskStatus},
		#{params},
		#{filePath},
		now(),
		now(),
		#{extInfo},
		#{operatorID},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10})
	</insert>


	<update id="updateExportTaskStatus">
		update ecpm_t_export_task
		set
		taskStatus = #{status},
		updateTime = now()
		where taskID = #{taskID}
	</update>

	<update id="updateExportTask">
		update ecpm_t_export_task
		set
		<if test="null != taskStatus">
			taskStatus = #{taskStatus},
		</if>
		<if test="null != filePath and '' != filePath">
			filePath = #{filePath},
		</if>
		<if test="null != operatorID">
			operatorID = #{operatorID},
		</if>
		<if test="null != reserved1 and '' != reserved1">
			reserved1 = #{reserved1},
		</if>
		<if test="null != reserved2 and '' != reserved2">
			reserved2 = #{reserved2},
		</if>
		<if test="null != reserved3 and '' != reserved3">
			reserved3 = #{reserved3},
		</if>
		<if test="null != reserved4 and '' != reserved4">
			reserved4 = #{reserved4},
		</if>
		<if test="null != reserved5 and '' != reserved5">
			reserved5 = #{reserved5},
		</if>
		<if test="null != reserved6 and '' != reserved6">
			reserved6 = #{reserved6},
		</if>
		<if test="null != reserved7 and '' != reserved7">
			reserved7 = #{reserved7},
		</if>
		<if test="null != reserved8 and '' != reserved8">
			reserved8 = #{reserved8},
		</if>
		<if test="null != reserved9 and '' != reserved9">
			reserved9 = #{reserved9},
		</if>
		<if test="null != reserved10 and '' != reserved10">
			reserved10 = #{reserved10},
		</if>
		<if test="null != extInfo and '' != extInfo">
			extInfo = #{extInfo},
		</if>
		updateTime = now()
		where taskID = #{taskID}
	</update>

</mapper>