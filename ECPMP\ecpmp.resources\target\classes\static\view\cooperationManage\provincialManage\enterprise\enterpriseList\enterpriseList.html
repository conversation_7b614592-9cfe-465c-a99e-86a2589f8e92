<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>分省企业管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript" 
		src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="enterpriseListCtrl.js"></script>

</head>

<body ng-app="myApp" class="body-min-width body-min-width-new">
	<div class="cooperation-manage container-fluid" ng-controller="EnterpriselistCtrl" ng-init="init();" ng-cloak>
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'COMMON_COOPERATION'|translate"> </span> > 
			<span class="second-tab" ng-bind="'COMMON_PROENTERPRISE'|translate"></span></div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group" style="min-width: 1000px;">
					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'CREATEORDER_ENTERPRISENAME'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="enterpriseName" class="form-control" 
							placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}" 
							ng-model="enterpriseName">
					</div>

					<label for="enterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap" ng-bind="'ENTERPRISEID'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="enterpriseID" class="form-control"
							   placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISEID'|translate}}"
							   ng-model="enterpriseID">
					</div>

					<label for="enterpriseCode" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap" ng-bind="'PROVINCIAL_ENTERPRISEID'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="enterpriseCode" class="form-control"
							   placeholder="{{'ENTERPRISE_PROVINCIAL_PLEASEINPUTENTERPRISEID'|translate}}"
							   ng-model="enterpriseCodeLike">
					</div>
					<div ng-show="isSuperManager || isNormalMangager">
						<label for="isZyzq" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
							   style="white-space:nowrap" ng-bind="'ENTERPRISE_TYPE'|translate" ></label>
						<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" >
							<select class="form-control" ng-model="isZyzq"
									ng-options="x.id as x.name for x in isZyzqChoise"
									ng-change="changeSelectedIsZyzq()">
								<option  value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
							</select>
						</div>
					</div>



					<label for="servType" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<select class="form-control" ng-model="servType"
								ng-options="x.id as x.name for x in servTypeChoise">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>


				<div style="float: left;width: 100%">
					<label for="province" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'SPOKES_LOCATION'|translate"></label>
								 
								 
					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" ng-show="isSuperManager">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.provinceName for x in provinceList"
										ng-change="changeSelectedProvince(selectedProvince)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" ng-show="isSuperManager">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity"
										ng-options="x.cityName for x in subCityList"
										ng-change="changeSelectedCity(selectedCity)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" style="margin-left: 8.33333333%;" ng-show="isSuperManager && showCountyFlag">
						<select class="form-control" name="county" id="selectedCounty" ng-model="selectedCounty"
								ng-options="x.countyName for x in subCountyList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" ng-show="isProvincial || isNormalMangager">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.authName for x in provinceList"
										ng-change="changeSelectedProvince(selectedProvince)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" ng-show="isProvincial || isNormalMangager">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity"
										ng-options="x.authName for x in subCityList"
										ng-change="changeSelectedCity(selectedCity)">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" style="margin-left: 8.33333333%;" ng-show="(isProvincial || isNormalMangager)&&showCountyFlag">
						<select class="form-control" name="county" id="selectedCounty" ng-model="selectedCounty"
								ng-options="x.countyName for x in subCountyList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
						<button type="submit" class="btn search-btn" ng-click="enterpriseList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>

					
				</div>
			</form>
		</div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:65px" ng-bind="''|translate"></th>
					<th style="width:100px" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></th>
					<th style="width:150px">企业名称</th>
					<th style="width:130px" ng-bind="'PROVINCIAL_ENTERPRISEID'|translate"></th>
					<th style="width:130px" ng-bind="'COMMON_ACCOUNTNAME'|translate"></th>
					<th style="width:130px" ng-bind="'SPOKES_LOCATION'|translate"></th>
					<th style="width:130px" ng-bind="'COMMON_CREATETIME'|translate"></th>
					<th style="width:560px" ng-bind="'COMMON_OPERATE'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<style>
					.tag_cy,.tag_hl{color:#fff;border-radius: 4px;}
					.tag_cy{background: #fd6f9a;}
					.tag_hl{background: #5190e8;}
					.li-disabled{
						opacity: 0.5;
						cursor: not-allowed!important;
					}
				</style>
				<tr ng-repeat="item in queryEnterpriseList">
					<td><span ng-if="item.cardStatus" class="tag_cy">名</span>
						<span ng-if="item.hotlineStatus" class="tag_hl">热</span>
					</td>

					<td><span title="{{item.id}}">{{item.id}}</span></td>
					<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
					<td><span title="{{item.enterpriseCode}}">{{item.enterpriseCode}}</span></td>

					<td><span title="{{item.accountInfo.accountName}}">{{item.accountInfo.accountName||""}}</span></td>
					<td title="{{provinceList2[item.provinceID]?provinceList2[item.provinceID]:''}}{{cityList2[item.cityID]?'-'+cityList2[item.cityID]:''}}
								{{countyList2[item.countyID]?'-'+countyList2[item.countyID]:''}}">
                        	{{provinceList2[item.provinceID]?provinceList2[item.provinceID]:''}}{{cityList2[item.cityID]?'-'+cityList2[item.cityID]:''}}
							{{countyList2[item.countyID]?'-'+countyList2[item.countyID]:''}}</td>
					<td title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</td>
					<td>
						<div class="handle">
							<ul>
								<!--名片彩印-->
								<li class="edit" ng-click="(item.cardType===1)?toMingpian(item):''" ng-class="{true:'',false:'li-disabled'}[item.cardType===1]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>
								</li>
								<!--热线彩印-->
								<li class="edit" ng-click="(item.hotlineType===1)?toHotLine(item):''" ng-class="{true:'',false:'li-disabled'}[item.hotlineType===1]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'COMMON_ENTERPRISEHOTLINE1'|translate"></span>
								</li>
								<!--热线彩印省份版-->
								<li class="edit" ng-if="!item.provinceHotlineMenu" ng-click="(item.provinceHotlineType===1)?toProvinceHotline(item):''" ng-class="{true:'',false:'li-disabled'}[item.provinceHotlineType===1]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'CONTENTAUDIT_HOTLINE_PROVINCE'|translate"></span>
								</li>
								<!--设置-->
								<li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'SETTINGS'|translate"></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="queryEnterpriseList.length<=0">
					<td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="enterpriseList('justPage')"></ptl-page>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate">
						</button>
					</div>
				</div>
			</div>
		</div>


	</div>
</body>

</html>