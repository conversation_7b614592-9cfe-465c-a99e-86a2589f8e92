<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ContentMapper">
	<resultMap id="contentMap"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentWrapper">
		<result property="id" column="ID" />
		<result property="contCode" column="contCode" />
		<result property="contRuleID" column="contRuleID" />
		<result property="thirdpartyType" column="thirdpartyType" />
		<result property="servType" column="servType" />
		<result property="subServType" column="subServType" />
		<result property="blackWhiteListType" column="blackwhiteListType" />
		<result property="content" column="content" />
		<result property="contentTitle" column="contentTitle" />
		<result property="chargeType" column="chargeType" />
		<result property="deliveryDate" column="deliveryDate" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="scenedesc" column="scenedesc" />
		<result property="deliveryType" column="deliveryType" />
		<result property="industryType" column="industryType" />
		<result property="applyproperty" column="applyproperty" />
		<result property="unicomdelivery" column="unicomdelivery" />
		<result property="scene" column="scene" />
		<result property="createTime" column="createTime" />
		<result property="updateTime" column="updateTime" />
		<result property="operatorID" column="operatorID" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="frequencyID" column="frequencyID" />
		<result property="statID" column="statID" />
		<result property="maxPushPerDay" column="maxPushPerDay" />
		<result property="pushInterval" column="pushInterval" />
		<result property="instruct" column="instruct" javaType="java.lang.String" />
        <result property="replyParentID" column="replyParentID" javaType="java.lang.Long" />
        <result property="replyApproveStatus" column="replyApproveStatus" javaType="java.lang.Integer" />
        <result property="approveStatus" column="approveStatus" javaType="java.lang.Integer" />
	</resultMap>

	<sql id="content_wrapper">
		ID,contCode,contRuleID,thirdpartyType,servType,subServType,
		blackWhiteListType,content,contentTitle,chargeType,deliveryDate,enterpriseID,
		scenedesc,deliveryType,industryType,applyproperty,unicomdelivery,scene,status,createTime,updateTime,operatorID,
		extInfo,reserved1,reserved2,reserved3,reserved4,reserved5,reserved6,reserved7,reserved8,reserved9,reserved10,
		frequencyID,statID,maxPushPerDay,pushInterval,instruct,replyParentID,replyApproveStatus,approveStatus,supportEnterpriseType,contentType,srcPlatform
	</sql>

	<insert id="insertContent"
		parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentWrapper">
		INSERT INTO ecpe_t_content
		(
		<include refid="content_wrapper"></include>
		)
		VALUES
		(
		#{id},
		#{contCode},
		#{contRuleID},
		#{thirdpartyType},
		#{servType},
		#{subServType},
		#{blackWhiteListType},
		#{content},
		#{contentTitle},
		#{chargeType},
		#{deliveryDate},
		#{enterpriseID},
		#{scenedesc},
		#{deliveryType},
		#{industryType},
		#{applyproperty},
		#{unicomdelivery},
		#{scene},
		#{status},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10},
		#{frequencyID},
		#{statID},
		#{maxPushPerDay},
		#{pushInterval},
		#{instruct},
		#{replyParentID},
		#{replyApproveStatus},
		#{approveStatus},
		#{supportEnterpriseType},
		#{contentType},
		#{srcPlatform}
		)
	</insert>

	<select id="queryContentByID" parameterType="java.lang.Long"
		resultMap="contentMap">
		select
		<include refid="content_wrapper"></include>
		from ecpe_t_content t where t.ID = #{id}
	</select>
	
	<select id="queryByContentAndEnterprise"  resultMap="contentMap">
		select
		<include refid="content_wrapper"></include>
		from ecpe_t_content t 
		<trim prefix="where" suffixOverrides="AND">
			<if test="templateId != null">
				t.ID = #{templateId} AND 
			</if>
			<if test="servType != null">
				t.servType=#{servType} AND
			</if>
			<if test="subServType != null">
				t.subServType=#{subServType} AND
			</if>
			<if test="enterpriseID != null">
				t.enterpriseID=#{enterpriseID} AND
			</if>
		</trim>
	</select>
	
	<select id="batchQueryContentByID" resultMap="contentMap">
		select <include refid="content_wrapper"/>
		  from ecpe_t_content t where t.ID in
		<foreach collection="list" item="ID" index="index" open="(" separator="," close=")">  
			#{ID}
		</foreach>
	</select>

	<update id="updateContentByContentID"
		parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentWrapper">
		update ecpe_t_content set
		contCode=#{contCode},contRuleID=#{contRuleID},thirdpartyType=#{thirdpartyType},servType=#{servType},blackwhiteListType=#{blackWhiteListType},content=#{content},contentTitle=#{contentTitle},chargeType=#{chargeType},
		deliveryDate=#{deliveryDate},enterpriseID=#{enterpriseID},scenedesc=#{scenedesc},deliveryType=#{deliveryType},industryType=#{industryType},applyproperty=#{applyproperty},unicomdelivery=#{unicomdelivery},scene=#{scene},
		status=#{status},updateTime=#{updateTime},operatorID=#{operatorID},extInfo=#{extInfo},reserved1=#{reserved1},reserved2=#{reserved2},reserved3=#{reserved3},
		reserved4=#{reserved4},reserved5=#{reserved5},frequencyID=#{frequencyID},statID=#{statID},maxPushPerDay=#{maxPushPerDay},pushInterval=#{pushInterval},
		reserved6=#{reserved6},reserved7=#{reserved7},reserved8=#{reserved8},reserved9=#{reserved9},reserved10=#{reserved10},
		instruct = #{instruct},replyParentID = #{replyParentID},replyApproveStatus = #{replyApproveStatus},approveStatus = #{approveStatus},srcPlatform =#{srcPlatform}
		where ID = #{id}
	</update>

	<delete id="deleteContentByID" parameterType="java.lang.Long">
		delete from ecpe_t_content where ID=#{id}
	</delete>

	<update id="updateContentStatus" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index" item="contentMap"
			open="" separator=";">
			update ecpe_t_content
			<trim prefix="set" suffixOverrides=",">
				<if test="contentMap.status != null">
					status=#{contentMap.status},
				</if>
				<if test="contentMap.reserved7 != null">
					reserved7=#{contentMap.reserved7},
				</if>
				<if test="contentMap.updateTime != null">
					updateTime=#{contentMap.updateTime}
				</if>
			</trim>
			where ID=#{contentMap.id}
		</foreach>
	</update>


    <update id="updateContentControlStatus" >
        update ecpe_t_content
		set reserved7=#{reserved7},
            updateTime=now()
        where
            id=#{id}
    </update>
    
    <select id="getByreplyParentID" resultType="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentTitle,
		chargeType,
		deliveryDate,
		enterpriseID,
		STATUS,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		createTime,
		updateTime,
		operatorID,
		instruct,
		replyParentID,
		replyApproveStatus
		FROM ecpe_t_content
		where replyParentID = #{contentId}
	</select>


	<update id="updateContentStatusByEnterprise">
        update ecpe_t_content
		set STATUS=#{status},updateTime = NOW()
		where enterpriseId = #{enterpriseID}
		and status not in (2,3)
		and servType = #{servType}
		and reserved7 != "000"
		<if test="chargeType != null">
			and chargeType = #{chargeType}
		</if>
		<if test="subServType != null and subServType == 3"  >
			and subServType in (1,2,3,7)
		</if>
		<if test="subServType != null and subServType != 3"  >
			and subServType  = #{subServType}
		</if>
    </update>
	<update id="updateContentPlatformByEnterprise">
		UPDATE ecpe_t_content set reserved7 = CONCAT(
		if(substring(srcPlatform,1, 1) = substring(#{reserved7},1, 1),substring(#{reserved7},1, 1),0),
		if(substring(srcPlatform,2, 1) = substring(#{reserved7},2, 1),substring(#{reserved7},2, 1),0),
		if(substring(srcPlatform,3, 1) = substring(#{reserved7},3, 1),substring(#{reserved7},3, 1),0))
		where enterpriseId = #{enterpriseID}
		and status not in (2,3)
		and servType = #{servType}
		<if test="chargeType != null">
			and chargeType = #{chargeType}
		</if>
		<if test="subServType != null and subServType == 3"  >
			and subServType in (1,2,3,7)
		</if>
		<if test="subServType != null and subServType != 3"  >
			and subServType  = #{subServType}
		</if>
	</update>

	<update id="updateContentstatusByreserved7">
		update ecpe_t_content
		set STATUS=if(reserved7 = "000",1,0),updateTime = NOW()
		where enterpriseId = #{enterpriseID}
		and status not in (2,3)
		and servType = #{servType}
		<if test="chargeType != null">
			and chargeType = #{chargeType}
		</if>
		<if test="subServType != null and subServType == 3"  >
			and subServType in (1,2,3,7)
		</if>
		<if test="subServType != null and subServType != 3"  >
			and subServType  = #{subServType}
		</if>
	</update>
	<select id="queryIdByEnterprise" resultType="java.lang.Integer">
		select id from  ecpe_t_content where enterpriseId = #{enterpriseID}
	</select>


</mapper>