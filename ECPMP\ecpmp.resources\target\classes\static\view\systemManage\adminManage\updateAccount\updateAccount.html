<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>编辑账号</title>
    <link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
    <link href="../../../../css/reset.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css" />
    <link rel="stylesheet" type="text/css" href="../../../../css/webuploader.css">
    <link rel="stylesheet" type="text/css" href="../../../../css/font-awesome.min.css">

    <script type="text/javascript" src="updateAccountCtrl.js"></script>
</head>
<body  ng-app="myApp" ng-controller="updateAccountController" ng-init="init()" >
    <div class="cooperation-manage" >
        <div class="cooperation-head">
            <span class="frist-tab" ng-bind="'ACCOUNTMANAGER'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'EDIT_ACCOUNT'|translate"></span>
        </div>
        <div class="cooper-tab">
            <div>
                <form class="form-horizontal ng-pristine ng-invalid ng-invalid-required ng-valid-maxlength" name="myForm"
                      novalidate="">
                    <div class="form-group">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ACCOUNT_NAME'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input type="text" class="form-control" ng-model="accountName" name="accountName" readonly>
                        </div>
                    </div>
                    <div class="form-group black-white">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span>账号类型</span>
                        </label>
                        <div class="col-lg-4 col-xs-4 col-sm-4">
                            <li    class="redio-li" ng-click="changeBlackwhiteListType('0')" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn" style="vertical-align: middle;"></span>4A账号
                            </li>
                            <li    class="redio-li" ng-click="changeBlackwhiteListType('1')" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn checked"  style="vertical-align: middle;"> </span>应用账号
                            </li>
                        </div>
                    </div>
                    <div class="form-group OA-account">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span>是否是咪咕员工</span>
                        </label>
                        <div class="col-lg-4 col-xs-4 col-sm-4">
                            <li class="redio-li" ng-click="changeIsOAAccount(1)" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn" style="vertical-align: middle;"></span>是
                            </li>

                            <li class="redio-li" ng-click="changeIsOAAccount(0)" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn checked" style="vertical-align: middle;"> </span>否
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMiguStaff==1">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'OA_ACCOUNT'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input disabled type="text" class="form-control" ng-show="oaAccountValidate"
                                   ng-model="oaAccount" name="oaAccount" ng-blur="checkOaAccount(oaAccount)">
                        </div>
                        <div class="col-lg-1 col-xs-1  col-sm-1 col-md-1 selBtn" ng-click="selOAAccount()">选择员工</div>
                        <div class="col-lg-2 col-xs-3  col-sm-3 col-md-3">

                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CHARACTERNAME'|translate"></span>
                        </label>
                        <div class="col-lg-1 col-xs-1  col-sm-1 col-md-1 selBtn" ng-click="selAccount()">选择角色</div>
                        <div class="col-lg-2 col-xs-3  col-sm-3 col-md-3">
                            <ul>
                                <li ng-repeat="x in selectedRole" ng-bind="x.roleName"></li>
                            </ul>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CONTACT'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input type="text" class="form-control"
                                ng-model="fullName" name="fullName" ng-show="fullNameValidate"
                                ng-blur="checkFullName(fullName)">
                            <input type="text" class="form-control"
                                ng-model="fullName" name="fullName" ng-show="!fullNameValidate"
                                ng-blur="checkFullName(fullName)"
                                style="border-color:red">
                            <span style="color:red"
                                  ng-show="!fullNameValidate">
                                <img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                <span>必填,联系人只能输入中文和字母,长度不能超过32位</span>
                            </span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'PHONENUMBER'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input type="text" class="form-control"  ng-show="msisdnValidate"
                                ng-blur="checkMsisdn(msisdn)"
                                ng-model="msisdn" name="msisdn">
                            <input type="text" class="form-control"  ng-show="!msisdnValidate"
                                ng-blur="checkMsisdn(msisdn)"
                                ng-model="msisdn" name="msisdn"
                                style="border-color:red">
                            <span style="color:red" ng-show="!msisdnValidate">
                                <img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                <span>必填,联系人手机号必填,仅支持11位数字输入</span>
                            </span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                            ng-bind="'ACCOUNT_EMAIL'|translate"></label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input type="text" class="form-control" ng-show="emailValidate"
                                ng-model="email" name="email" ng-blur="checkEmail(email)">
                            <input type="text" class="form-control" ng-show="!emailValidate"
                                ng-model="email" name="email" ng-blur="checkEmail(email)"
                                style="border-color:red">
                            <span style="color:red"
                                   ng-show="!emailValidate">
                                <img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                <span>请输入正确的邮箱格式,长度不能超过64位</span>
                            </span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ACCOUNT_PASSWORD'|translate">用户密码</span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input type="password" class="form-control" name="password"
									autocomplete="off" ng-model="password"
									ng-blur="checkPassword()"
									ng-show="!passwordValidateDesc">
							<input type="password" class="form-control redBorder" name="password"
									autocomplete="off" ng-model="password"
									ng-blur="checkPassword()" style="border-color:red"
									ng-show="passwordValidateDesc">
							<span class="redFont" ng-show="passwordValidateDesc" style="color:red">
								<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								<span>{{passwordValidateDesc|translate}}</span>
							</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ACCOUNTRPASSWORD'|translate">确认密码</span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <input type="password" class="form-control" name="rePassword"
							    autocomplete="off" ng-model="rePassword"
							    ng-blur="checkRePassword(password,rePassword,'')"
							    ng-show="rePasswordValidate">
						    <input type="password" class="form-control redBorder" name="rePassword"
							    autocomplete="off" ng-model="rePassword"  style="border-color:red"
							    ng-blur="checkRePassword(password,rePassword,'')"
							    ng-show="!rePasswordValidate">
						    <span class="redFont" ng-show="!rePasswordValidate" style="color:red">
							    <img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
							    <span>两次输入密码不一致</span>
						    </span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ACCOUNT_STATUS'|translate">账号状态</span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4 align-center" style="min-width: 310px;">
                            <span class="mr-r-10" >{{ getAccountStatus(accountStatus) }}</span>
                            <span class="switch "
                                  ng-show="originAccountStatus == 2"
                                  ng-class={'off':accountStatus==2}
                                  ng-click="changeStatus()"
                                  >
							<icon class="switch-icon"></icon>
						</span>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn  col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 "
                    ng-disabled="!fullNameValidate || !fullName || !msisdnValidate || !msisdn ||
                    !emailValidate || !rePasswordValidate ||
                    passwordValidateDesc || (password && !rePassword)||(isMiguStaff==1&&!oaAccount)"
                    ng-bind="'COMMON_SAVE'|translate" ng-click="updateAccount()"></button>
                <button type="submit" class="btn btn-back"ng-click="goBack()"
                    ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>


    <!--角色查询弹框-->
    <div class="modal fade" id="selectAccount"  tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document" style="width:690px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'SELECTROLE'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="form-horizontal">
                        <div cla ss="form-group">
                            <div class="row">
                                <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                                    style="font-weight:400;text-align:left;"
                                    ng-bind="'ROLENAME'|translate"></label>
                                <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5" style="margin-left:-35px">
                                    <input type="text" class="form-control" id=""  ng-model="roleName"
                                        placeholder="{{'PLEASEINPUTCHARACTERNAME'|translate}}">
                                </div>
                                <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                    <button class="btn bg_purple search-btn btn1"  ng-click="queryRoleList()">
                                        <span class="icon btnIcon search"></span>
                                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="coorPeration-table">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <td style="width:20%" ng-bind="'ROLENAME'|translate"></td>
                                    <td style="width:60%" ng-bind="'MENUACCESS'|translate"></td>
                                    <td style="width:20%" ng-bind="'COMMON_OPERATE'|translate"></td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in roleList">
                                    <td><span title="{{item.roleName}}">{{item.roleName}}</span></td>
                                    <td><span title="{{item.authNameString}}">{{item.authNameString}}</span></td>
                                    <td class="coorPerationTr">
                                        <span ng-click="roleAdd(item)">添加</span>
                                    </td>
                                </tr>
                                <tr ng-show="roleContentInfoData.length<=0">
                                    <td style="text-align:center" colspan="4">暂无数据</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ptl-page tableId="0" change="queryRoleList('justPage')"></ptl-page>
                    </div>
                </div>
                <div class="modal-footer"></div>
            </div>
        </div>
    </div>


        <!--角色查询弹框-->
        <div style="z-index: 1" class="modal fade" id="selectOAAccount"  tabindex="-2" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog" role="document" style="width:690px">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title"  ng-bind="'SELECTROLE'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                            <div cla ss="form-group">
                                <div class="row">
                                    <label  class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                                            style="font-weight:400;text-align:left;"
                                            ng-bind="'OA_ACCOUNT_NAME'|translate"></label>
                                    <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5" style="margin-left:-35px;width: 25%">
                                        <input  type="text" class="form-control"
                                                placeholder="{{''|translate}}"
                                                ng-model="realName">
                                    </div>

                                    <label  class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                                            style="font-weight:400;text-align:left;"
                                            ng-bind="'OA_ACCOUNT_DEPT_NAME'|translate"></label>
                                    <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5" style="margin-left:-35px;width: 25%">
                                        <select class="form-control"
                                                name="deptId" ng-model="deptId"
                                                ng-options="x.deptID as x.deptName for x in oaDeptInfoList">
                                            <option value="" ng-show="operate !='edit'">不限</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                        <button class="btn bg_purple search-btn btn1" ng-click="queryOAAccount()">
                                            <span class="icon btnIcon search"></span>
                                            <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="coorPeration-table">
                            <table class="table table-striped table-hover">
                                <thead>
                                <tr>
                                    <th style="width:30%" ng-bind="'OA_ACCOUNT_NAME'|translate"></th>
                                    <th style="width:30%" ng-bind="'OA_ACCOUNT'|translate"></th>
                                    <th style="width:40%" ng-bind="'OA_ACCOUNT_DEPT_NAME'|translate"></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="item in accountInfoList" ng-click="clickOAAccount(item)">
                                    <td><span title="{{item.realName}}">{{item.realName}}</span></td>
                                    <td><span title="{{item.oaAccount}}">{{item.oaAccount}}</span></td>
                                    <td><span title="{{item.deptName}}">{{item.deptName}}</span></td>
                                </tr>
                                <tr ng-show="accountInfoList.length<=0">
                                    <td style="text-align:center" colspan="4">暂无数据</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ptl-page tableId="1" change="queryOAAccount()"></ptl-page>
                        </div>
                    </div>
                    <div class="modal-footer"></div>
                </div>
            </div>
        </div>
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" role="dialog" aria-labelledby="myModalLabel" style="z-index:555555">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838' ng-bind="tip|translate"></p>
                    </div>
                </div>
                <div class="modal-footer" style="text-align:center; margin-left:0px;">
                    <button type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" ng-bind="'COMMON_OK'|translate"
                        ng-click="closeTip()"></button>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
<style>
    body {
        background: #f2f2f2;
    }
    table{
        table-layout: fixed;
    }
    table td{
        width: 100%;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    ul{
        table-layout: fixed;
    }
    ul li{
        word-break: break-all;
        text-overflow: ellipsis;
    }
    .modal-footer{
        text-align: left;
    }
    .cooperation-manage{
        min-width: 1024px;
    }
    .cooperation-head {
        padding: 20px;
    }
    .cooperation-head .frist-tab {
        font-size: 16px;
    }
    .cooperation-head .second-tab {
        font-size: 14px;
    }
    .cooper-tab {
        margin: 0 20px;
        background: #fff;
        border-radius: 2px;
        padding: 36px 10px 16px;
    }
    .form-group .control-label icon {
        color: #ff254c;
        vertical-align: sub;
        margin-right: 2px;
    }
    .form-group div {
        line-height: 34px;
    }
    .form-group {
        margin-bottom: 35px;
    }
    .selBtn {
        width:100px;
        height: 34px;
        border-radius: 5px;
        background: #7360e1;
        color: white;
        float: left;
        margin-left: 15px;
        text-align: center;
        cursor: pointer;
        margin-right: 30px;
    }
    .coorPerationTr span {
        color: #7360e1;
        cursor: pointer;
    }
    .mr-r-10 {
     margin-right:10px;
    }
     .switch{
            width: 40px;
            height: 20px;
        }
        .switch .switch-icon{
            width: 18px;
            height: 18px;
        }
        .align-center {
            display: flex;
            align-items: center;
        }
</style>
</html>
