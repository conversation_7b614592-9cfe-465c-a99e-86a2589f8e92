<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityWinSendMapper">
	<resultMap id="activityWinSendWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivityWinSendWrapper">
		<result property="activityID" column="activityID" javaType="java.lang.Integer" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="lastUpdateTime" column="lastUpdateTime"
			javaType="java.util.Date" />
	</resultMap>

	<select id="queryActivityWinSendRecord" resultMap="activityWinSendWrapper">
		select
		activityID,
		status,
		createTime,
		lastUpdateTime
		from
		ecpm_activity_winSendTask
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				activityID=#{activityID}
			</if>
		</trim>
	</select>

	<update id="mergeActivityWinSendRecord">
		INSERT INTO
		ecpm_activity_winSendTask(activityID,status,createTime,lastUpdateTime)
		VALUES (#{activityID},#{status},#{createTime},#{lastUpdateTime}) ON
		DUPLICATE KEY
		UPDATE status=#{status},lastUpdateTime=#{lastUpdateTime};
	</update>
	
	<update id="updateActivityWinSendRecord">
		update ecpm_activity_winSendTask set
		<trim suffixOverrides=","
			suffix="where activityID = #{activityID} ">
			<if test="status!=null">
				status=#{status},
			</if>
			<if test="lastUpdateTime!=null">
				lastUpdateTime=#{lastUpdateTime}
			</if>
		</trim>
	</update>
</mapper>