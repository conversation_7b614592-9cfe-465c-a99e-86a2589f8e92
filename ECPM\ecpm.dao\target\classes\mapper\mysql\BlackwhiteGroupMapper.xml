<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.BlackwhiteGroupMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.BlackwhiteGroupWrapper" >
        <id column="ID" property="id" jdbcType="BIGINT" />
        <result column="servType" property="servType" jdbcType="TINYINT" />
        <result column="subServType" property="subServType" jdbcType="TINYINT" />
        <result column="blackWhiteListType" property="blackWhiteListType" jdbcType="TINYINT" />
        <result column="enterpriseID" property="enterpriseID" jdbcType="INTEGER" />
        <result column="orgCode" property="orgCode" jdbcType="VARCHAR" />
        <result column="reserved1" property="reserved1" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, servType, subServType, blackWhiteListType, enterpriseID, orgCode, reserved1
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_blackwhite_group
        where ID = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
        delete from ecpm_t_blackwhite_group
        where ID = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.BlackwhiteGroupWrapper" >
        insert into ecpm_t_blackwhite_group (ID, servType, subServType, 
            blackWhiteListType, enterpriseID, orgCode, 
            reserved1)
        values (#{id,jdbcType=BIGINT}, #{servType,jdbcType=TINYINT}, #{subServType,jdbcType=TINYINT}, 
            #{blackWhiteListType,jdbcType=TINYINT}, #{enterpriseID,jdbcType=INTEGER}, #{orgCode,jdbcType=VARCHAR}, 
            #{reserved1,jdbcType=VARCHAR})
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.BlackwhiteGroupWrapper" >
        insert into ecpm_t_blackwhite_group
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                ID,
            </if>
            <if test="servType != null" >
                servType,
            </if>
            <if test="subServType != null" >
                subServType,
            </if>
            <if test="blackWhiteListType != null" >
                blackWhiteListType,
            </if>
            <if test="enterpriseID != null" >
                enterpriseID,
            </if>
            <if test="orgCode != null" >
                orgCode,
            </if>
            <if test="reserved1 != null" >
                reserved1,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="servType != null" >
                #{servType,jdbcType=TINYINT},
            </if>
            <if test="subServType != null" >
                #{subServType,jdbcType=TINYINT},
            </if>
            <if test="blackWhiteListType != null" >
                #{blackWhiteListType,jdbcType=TINYINT},
            </if>
            <if test="enterpriseID != null" >
                #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="orgCode != null" >
                #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="reserved1 != null" >
                #{reserved1,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.BlackwhiteGroupWrapper" >
        update ecpm_t_blackwhite_group
        <set >
            <if test="servType != null" >
                servType = #{servType,jdbcType=TINYINT},
            </if>
            <if test="subServType != null" >
                subServType = #{subServType,jdbcType=TINYINT},
            </if>
            <if test="blackWhiteListType != null" >
                blackWhiteListType = #{blackWhiteListType,jdbcType=TINYINT},
            </if>
            <if test="enterpriseID != null" >
                enterpriseID = #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="orgCode != null" >
                orgCode = #{orgCode,jdbcType=VARCHAR},
            </if>
            <if test="reserved1 != null" >
                reserved1 = #{reserved1,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.BlackwhiteGroupWrapper" >
        update ecpm_t_blackwhite_group
        set servType = #{servType,jdbcType=TINYINT},
            subServType = #{subServType,jdbcType=TINYINT},
            blackWhiteListType = #{blackWhiteListType,jdbcType=TINYINT},
            enterpriseID = #{enterpriseID,jdbcType=INTEGER},
            orgCode = #{orgCode,jdbcType=VARCHAR},
            reserved1 = #{reserved1,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=BIGINT}
    </update>
    
	<select id="queryBlackWhiteGroup" resultMap="BaseResultMap">
		SELECT
			t.enterpriseID,
			t.blackWhiteListType,
			t.servType,
			t.subServType,
			t.orgCode,
			t.reserved1 
		FROM
			ecpm_t_blackwhite_group t 
		WHERE
			t.orgCode IS NOT NULL OR t.reserved1 IS NOT NULL
		GROUP BY
			t.enterpriseID,
			t.blackWhiteListType,
			t.servType,
			t.subServType,
			t.orgCode,
			t.reserved1
	</select>
	
	<select id="queryBlackWhiteGroupList" resultMap="BaseResultMap">
		select 
        	<include refid="Base_Column_List" />
		from ecpm_t_blackwhite_group t
		where 
			t.enterpriseID = #{enterpriseID} 
			and t.servType = #{servType}
			and t.subServType = #{subServType} 
			and t.blackWhiteListType = #{blackWhiteListType}
	</select>

	<update id="updateBlackWhiteGroupOrgCode">
		update
			ecpm_t_blackwhite_group 
		<set>
			<if test="orgCode != null">
				orgCode=#{orgCode},
			</if>
			<if test="reserved1 != null">
				reserved1=#{reserved1},
			</if>
		</set>
		where
			enterpriseID = #{enterpriseID} 
			and servType = #{servType}
			and subServType = #{subServType} 
			and blackWhiteListType = #{blackWhiteListType}
	</update>
</mapper>