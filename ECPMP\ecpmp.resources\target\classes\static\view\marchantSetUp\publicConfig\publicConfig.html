<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>公众号配置</title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../css/publicConfig.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/webuploader.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-common.js"></script>
	<script src="../../../directives/cy-uploadify/cy-uploadify.js"></script>
	<link href="../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
	<link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
	<script type="text/javascript" src="publicConfigCtrl.js"></script>
</head>
<body ng-app='myApp' ng-controller='publicConfigCtrl' ng-init="init();" class="body-min-width" ng-cloak>
	<div>
		<div class="order-manage">
			<div class="head-nav">
				<span class="frist-tab" ng-bind="'MENU_MARCHANT_SETUP'|translate"></span> >
				<span class="second-tab" ng-bind="'PUBLIC_NUMBER_CONFIGURATION'|translate"></span>
			</div>
		</div>
		<div class="cooper-tab">
			<form class="form-horizontal ng-pristine ng-invalid ng-invalid-required ng-valid-maxlength" name="myForm"
						novalidate="">
				<div class="form-group">
					<!--公众号名称-->
					<label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<icon>*</icon>
						<span ng-bind="'PUBLIC_NUMBER_NAME'|translate"></span>：</label>

					<div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
						<input type="text" id="publicName" class="form-control" placeholder="请输入公众号名称"
									 ng-model="publicName" name="publicName" ng-maxlength="32" required
									 ng-disabled="(isExist&&!isEdit)||isError"
									 ng-class="{true:'error',false:''}[myForm.publicName.$dirty && myForm.publicName.$invalid]">
						<span style="color:red" ng-show="myForm.publicName.$dirty && myForm.publicName.$invalid">
							<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
							<span ng-show="myForm.publicName.$error.required" ng-bind="'PUBLIC_NUMBER_NAME_MUST'|translate"></span>
							<span ng-show="myForm.publicName.$error.maxlength" ng-bind="'PUBLIC_NUMBER_NAME_LENGTH'|translate"></span>
						</span>
					</div>
				</div>
				<div class="form-group">
					<!--二维码-->
					<label for="" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<icon>*</icon>
						<span ng-bind="'PUBLIC_NUMBER_QRCODE'|translate"></span>：
					</label>
					<div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" ng-if="(isExist&&!isEdit)||isError">
						<img ng-src="{{imgUrl}}" width="250"/>
					</div>
					<cy:uploadify ng-if="!isExist||isEdit" filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
												uploadifyid="uploadifyid" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
												formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
												urllist="urlList" createthumbnail="isCreateThumbnail" style="margin-left: 15px;float: left;">
					</cy:uploadify>
				</div>
				<div class="form-group">
					<!--说明-->
					<label for="explain" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<icon>*</icon>
						<span ng-bind="'COMMON_EXPLAIN'|translate"></span>：</label>

					<div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
						<textarea class="form-control" rows="3" ng-model="explain" ng-maxlength="1024" id="explain" name="explain"
											ng-class="{true:'error',false:''}[myForm.explain.$dirty && myForm.explain.$invalid]"
											ng-disabled="(isExist&&!isEdit)||isError" required></textarea>
						<span style="color:red" ng-show="myForm.explain.$dirty && myForm.explain.$invalid">
							<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
							<span ng-show="myForm.explain.$error.required" ng-bind="'COMMON_EXPLAIN_MUST'|translate"></span>
							<span ng-show="myForm.explain.$error.maxlength" ng-bind="'COMMON_EXPLAIN_LENGTH'|translate"></span>
						</span>
					</div>
				</div>
			</form>
		</div>
		<div style="margin: 40px 20px;margin-left: 18%;">
			<button type="submit" class="btn btn-primary search-btn" ng-click="save()"
							ng-disabled="qrCode===''||myForm.explain.$invalid||myForm.publicName.$invalid||isError"
							ng-bind="'PUBLIC_NUMBER_SUBMIT'|translate" ng-show="!isExist||isEdit||isError">
			</button>
			<button class="btn btn-primary search-btn" ng-click="isEdit=true;urlList= imgUrl? [imgUrl]:[];"
							ng-show="isExist && !isEdit && !isError" ng-bind="'GROUP_EDIT'|translate">
			</button>
			<button class="btn" ng-click="isEdit=false" style=" margin-left: 10px"
							ng-show="isEdit && !isError" ng-bind="'COMMON_CANCLE'|translate">
			</button>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="publicModal" tabindex="-1" role="dialog"
				 aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
										ng-bind="'COMMON_OK'|translate">
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>

</body>

</html>