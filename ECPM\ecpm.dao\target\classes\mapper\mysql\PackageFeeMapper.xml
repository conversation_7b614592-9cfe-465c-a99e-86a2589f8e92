<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.PackageFeeMapper">
    <resultMap id="PackageFeeWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.PackageFeeWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="packageOrderID" column="packageOrderID" javaType="java.lang.Integer" />
        <result property="operateCode" column="operateCode" javaType="java.lang.String" />
        <result property="feeID" column="feeID" javaType="java.lang.String" />
		<result property="feeName" column="feeName" javaType="java.lang.String" />
		<result property="feeValue" column="feeValue" javaType="java.lang.String" />
		<result property="insertTime" column="insertTime" javaType="java.util.Date" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
    </resultMap>
    


	<insert id="createPackageFee">
		insert into  ecpm_t_package_fee
		(
		packageOrderID,
		operateCode,
		feeID,
		feeName,
		feeValue,
		insertTime,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5
		)
		values
		<foreach collection="list" item="packageFeeWrapper"
				 separator=",">
			(
			#{packageFeeWrapper.packageOrderID},
			#{packageFeeWrapper.operateCode},
			#{packageFeeWrapper.feeID},
			#{packageFeeWrapper.feeName},
			#{packageFeeWrapper.feeValue},
			#{packageFeeWrapper.insertTime},
			#{packageFeeWrapper.reserved1},
			#{packageFeeWrapper.reserved2},
			#{packageFeeWrapper.reserved3},
			#{packageFeeWrapper.reserved4},
			#{packageFeeWrapper.reserved5}
			)
		</foreach>

	</insert>


</mapper>