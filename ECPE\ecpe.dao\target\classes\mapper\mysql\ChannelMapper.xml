<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ChannelMapper">
	<resultMap id="channelWrapper"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ChannelWrapper">
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="smurl" column="smurl" javaType="java.lang.String" />
		<result property="src" column="src" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
	</resultMap>

	<select id="getChannels" resultMap="channelWrapper">
		select  enterpriseID , src , status , smurl  from ecpe_t_channel where status = 1
	</select>

</mapper>
