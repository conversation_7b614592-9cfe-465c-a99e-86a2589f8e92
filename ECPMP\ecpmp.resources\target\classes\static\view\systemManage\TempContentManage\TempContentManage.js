var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common"])
app.controller('hotlineContentController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {

        $scope.number = 70;
        $scope.dsc = "HOTLINE_CONTENTDESC";
        $scope.msg = "请输入模板内容,不超过70字,支持变量内容,如:我是#*#";
        $scope.msg2 = "变量名1,变量名2....";
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        //获取enterpriseName
        $scope.enterpriseName = $.cookie('enterpriseName');
        //判断最终调接口的enterpriseID,enterpriseName
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            $scope.enterpriseID = $scope.subEnterpriseID;
            $scope.enterpriseName = $scope.subEnterpriseName;
        }
        //标题
        $scope.contentTitle = '';
        //标题校验
        $scope.contentTitleValidate = true;

        $scope.contentValidate = true;
        //文本框的个数
        $scope.ctnTextMount = 0;
        $scope.contentInfoListData = [];
        $scope.selectedList = [];
        $scope.hotMsisdnVali = true;
        $scope.contentVali = true;
        $scope.operatorID = $.cookie('accountID');
        $scope.oldMap = new Map();
        //彩印内容类型
        $scope.serviceTypeMap = {
            "2": "热线彩印",
            "1": "名片彩印",
            "5": "热线彩印省份版"
        }
        //彩印内容类型
        $scope.typeMap = {
            "1": "主叫彩印",
            "2": "被叫彩印",
            "3": "主被叫彩印",
            "4": "挂机短信",
            "8": "挂机彩信",
            "16": "挂机增彩",
            "7": "交互彩印",
        };
        $scope.enterpriseTypeMap = {
            "1": "直客",
            "5": "分省",
            "3": "代理商"
        }
        //状态信息
        $scope.hotlineStatusMap = {
            "1": "审核失败",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        };
        $scope.approveStatusMap = {
            "1": "审核失败",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        }

        $scope.unicomApproveStatusMap = {
            "1": "审核失败",
            "-1": "--",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        }

        $scope.telecomApproveStatusMap = {
            "1": "审核失败",
            "-1": "--",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        }
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        $scope.enterpriseTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "直客"
            },
            {
                id: 3,
                name: "代理商"
            },
            {
                id: 5,
                name: "分省"
            }
        ];
        $scope.auditStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 2,
                name: "待审核"
            },
            {
                id: 3,
                name: "审核通过"
            },
            {
                id: 4,
                name: "审核驳回"
            },
            {
                id: 1,
                name: "审核失败"
            },
        ];
        $scope.subServChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "主叫彩印"
            },
            {
                id: 2,
                name: "被叫彩印"
            },
            {
                id: 3,
                name: "主被叫彩印"
            },
            {
                id: 4,
                name: "挂机短信"
            }
            // ,
            // {
            //     id: 8,
            //     name: "挂机彩信"
            // },
            // {
            //     id: "16",
            //     name: "挂机增彩"
            // },
            // {
            //     id: "7",
            //     name: "交互彩印"
            // }
        ];
        $scope.servChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 5,
                name: "热线彩印省份版"
            }
        ];
        //初始化搜索条件
        $scope.initSel = {
            contentNo:"",
            auditStatus:"",
            enterpriseType:"",
            supportEnterpriseType:"",
            subServType:"",
            servType:""
        };

        $scope.queryHotlineContentInfoList();

    };
    // 新增热线内容弹窗
    $scope.addHotlineContent = function () {
        $scope.operateTitle = "CONTENT_ADD";
        $scope.addHotlineContentInfo = {};
        $('#addHotlineContent').modal();
        $scope.operate = 'add';

        $scope.sensitiveWords = [];
        $scope.isSensitive = false;
        $scope.sensitiveWordsStr = "";
        $scope.contentVali = true;
        $scope.addHotlineContentInfo.supportEnterpriseType3 = "3";
        $scope.addHotlineContentInfo.serviceType2 = "2";
        $scope.addHotlineContentInfo.operator1 = true;
        $scope.addHotlineContentInfo.operator2 = false;
        $scope.addHotlineContentInfo.operator3 = false;
        $scope.addHotlineContentInfo.ysmbType1 = false;
        $scope.addHotlineContentInfo.ysmbType2 = false;


    };
    // 编辑热线内容弹窗
    $scope.editHotlineContent = function (item) {
        $scope.operate = 'edit';
        $scope.operateTitle ="COMMON_UPDATE";

        $scope.editBatchNo = item.batchNo;
        var req = {
            "batchNo": item.batchNo,
            "contentTypeList": [6],
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 20,
                "isReturnTotal": "0"
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.ysmbListData = result.contentInfoList || [];
                        var editFlag = true;
                        for (let ysmb of $scope.ysmbListData) {
                        	if (ysmb.approveStatus == 2) {
                        		editFlag = false;
                        		break;
                        	}
                        }
                        if (!editFlag) {
                        	$scope.tip = "预设固定模板存在待审";
                        	$('#myModal').modal();
                        } else {
                        	$scope.addHotlineContentInfo = {};
                        	$scope.oldMap = new Map();
                        	for (let ysmb of $scope.ysmbListData) {
                        		// 企业类型
                        		if (ysmb.supportEnterpriseType == 3) {
                        			$scope.addHotlineContentInfo.supportEnterpriseType3 = "3";
                        		} else if (ysmb.supportEnterpriseType == 1) {
                        			$scope.addHotlineContentInfo.supportEnterpriseType1 = "1";
                        		} else if (ysmb.supportEnterpriseType == 5) {
                        			$scope.addHotlineContentInfo.supportEnterpriseType5 = "5";
                        		}
                        		// 业务类型
                        		if (ysmb.servType == 2) {
                        			$scope.addHotlineContentInfo.serviceType2 = "2";
                        		} else if (ysmb.servType == 1) {
                        			$scope.addHotlineContentInfo.serviceType1 = "1";
                        		} else if (ysmb.servType == 5) {
                        			$scope.addHotlineContentInfo.serviceType5 = "5";
                        		}
                        		if  (ysmb.subServType == 3) {
                        			var contents = ysmb.content.split('||||');
                        			ysmb.content = contents[0];
                        			ysmb.calledContent = contents[1];
                        		}
                                // 添加运营商回显逻辑
                                if (ysmb.platforms?.length) {
                                    $scope.addHotlineContentInfo.operator1 = (ysmb.platforms.charAt(0) == '1' ? true : false)
                                    $scope.addHotlineContentInfo.operator2 = (ysmb.platforms.charAt(1) == '1' ? true : false)
                                    $scope.addHotlineContentInfo.operator3 = (ysmb.platforms.charAt(2) == '1' ? true : false)
                                }
                                // 添加内容类型回显逻辑
                                if(ysmb.ysmbType){
                                    let ysmbType = ysmb.ysmbType + ""
                                    if (ysmbType?.length) {
                                        $scope.addHotlineContentInfo.ysmbType1 = (ysmbType.charAt(0) == '1' ? true : false)
                                        $scope.addHotlineContentInfo.ysmbType2 = (ysmbType.charAt(1) == '1' ? true : false)
                                    }
                                }

                                $scope.addHotlineContentInfo["subServiceType" +ysmb.subServType ] = true;
                        		// 将编辑前的预设模板保存在map里
                        		let key = ysmb.supportEnterpriseType + "_" + ysmb.servType + "_" + ysmb.subServType;
                        		var value = $scope.oldMap.get(key);
                        		if (!value) {
                        			value = [];
                        			$scope.oldMap.set(key, value);
                        		}
                        		value.push(ysmb);
                            }
                        	$scope.addHotlineContentInfo.content = $scope.ysmbListData[0].content;
                            $scope.addHotlineContentInfo.variableNames = $scope.ysmbListData[0].variableNames;

                            $('#addHotlineContent').modal();
                        	
                        	$scope.sensitiveWords = [];
                        	$scope.isSensitive = false;
                        	$scope.sensitiveWordsStr = "";
                        	$scope.contentVali = true;
                        }
                        
                    } else {
                        $scope.ysmbListData = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.ysmbListData = [];
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.beforeCommit = function () {
        let operatorParams = ($scope.addHotlineContentInfo.operator1 ? "1" : "0") + ($scope.addHotlineContentInfo.operator2 ? "1" : "0") + ($scope.addHotlineContentInfo.operator3 ? "1" : "0")
        $scope.addHotlineContentInfo.platforms= operatorParams
        let ysmbTypeParams = ($scope.addHotlineContentInfo.ysmbType1 ? "1" : "0") + ($scope.addHotlineContentInfo.ysmbType2 ? "1" : "0")
        $scope.addHotlineContentInfo.ysmbType= ysmbTypeParams

        console.log("operatorParams",operatorParams,'---',ysmbTypeParams)
        console.log($scope.addHotlineContentInfo);

        if ($scope.addHotlineContentInfo.serviceType5
        		&& !$scope.addHotlineContentInfo.supportEnterpriseType5) {
        	$scope.tip = "热线彩印省份版，未选择分省企业类型";
        	$('#myModal').modal();
        	return;
        }
        
        var enterpriseTypes = [1, 3, 5];
        var serviceTypes = [1, 2, 5];
        var subServiceTypes = [1, 2, 4];
        var subServiceTypes2 = [3];

        var batchNo;
        if ($scope.operate == 'edit') {
        	batchNo = $scope.editBatchNo;
        } else {
        	batchNo = $scope.getUUID();
        }
        for (let i of enterpriseTypes) {
            if ($scope.addHotlineContentInfo["supportEnterpriseType" + i]) {
                for (let j of serviceTypes) {
                	// 只有分省能创建热线彩印省份版
                	if ($scope.addHotlineContentInfo["serviceType" + j] && (j != 5 || i == 5)) {
                        // 继续走新增逻辑
                        var req = {
                            "contentInfo": {
                                "batchNo": batchNo,
                                "supportEnterpriseType": i,
                                "servType": j,
                                "blackwhiteListType": 0,
                                "content": $scope.addHotlineContentInfo.content,
                                "contentType": 6,
                                "thirdpartyType": 0,
                                "operatorID": $scope.operatorID,
                                "chargeType": 1,
                                "industryType": "101010",
                                "platforms": $scope.addHotlineContentInfo.platforms,
                                "ysmbType":$scope.addHotlineContentInfo.ysmbType
                            }
                        };
                        req.contentInfo.variableNames = $scope.addHotlineContentInfo.variableNames;
                        let key;
                        let subServiceTypesTemp = null;
                        if(j===2){
                            subServiceTypesTemp = subServiceTypes2;
                            req.contentInfo.subServType = 3;
                            req.contentInfo.deliveryType = "111000";
                            req.contentInfo.approveStatus = 1;
                        }else {
                            subServiceTypesTemp = subServiceTypes
                            req.contentInfo.deliveryDate = "1111111";

                        }
                        for(let f of subServiceTypesTemp){
                            if($scope.addHotlineContentInfo["subServiceType" + f]){
                                key = i + "_" + j + "_" + f;
                                // 在旧模板map中存在则走变更逻辑
                                if ($scope.oldMap.has(key)) {
                                    $scope.updateContent(key);
                                }else{
                                    req.contentInfo.subServType = f;
                                    $scope.createContent(req);
                                }
                            }
                        }
                    }
                }
            }

        }
        
        // 取消勾选的内容需要删除
        if ($scope.operate == 'edit') {
        	for (let deleteList of $scope.oldMap.values()) {
        		for (let deleteItem of deleteList) {
        			var removeReq = {
    					"operaterType": deleteItem.servType,
    					"id": deleteItem.contentID
        			};
        			$scope.deleteContent(removeReq);
        		}
        	}
        }
        
        
        setTimeout(function () {
        	$('#addHotlineContentCancel').click();
            $scope.queryHotlineContentInfoList();
        },1200);

    };

    $scope.getUUID = function () {
    	var uuid = ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
            (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
        );
        return uuid.replace(/-/g, '');
    };
    
    $scope.createContent = function (req) {
    	RestClientUtil.ajaxRequest({
    		type: 'POST',
    		url: '/ecpmp/ecpmpServices/contentService/createContent',
    		data: JSON.stringify(req),
    		success: function (result) {
    			$rootScope.$apply(function () {
    				var data = result.result;
    				if (data.resultCode == '1030100000') {
    					$('#addHotlineContentCancel').click();
    				}else if(data.resultCode == '1030120090'){
    					$('#addHotlineContentCancel').click();
    				}else {
    					$('#addHotlineContentCancel').click();
    					$scope.tip = data.resultCode;
    					$('#myModal').modal();
    				}
    			})
    		},
    		error: function () {
    			$rootScope.$apply(function () {
    				$('#addHotlineContentCancel').click();
    				$scope.tip = '1030120500';
    				$('#myModal').modal();
    			})
    		}
    	});
    };
    
    $scope.updateContent = function (key) {
    	var value = $scope.oldMap.get(key);
		// 将map中需要调编辑接口的内容删除，剩下的就是需要调删除接口的
		$scope.oldMap.delete(key);
		// 模板内容有变化才需要走编辑接口，否则跳过
		if ($scope.addHotlineContentInfo.content == value[0].content && $scope.addHotlineContentInfo.variableNames == value[0].variableNames) {
			return;
		}
		for (let item of value) {
			var updateReq = {};
			updateReq.contentInfo = item;
			updateReq.contentInfo.content = $scope.addHotlineContentInfo.content;
            updateReq.contentInfo.variableNames = $scope.addHotlineContentInfo.variableNames;

            updateReq.contentInfo.extInfo = null;
    		if (updateReq.contentInfo.subServType == 3) {
    			updateReq.contentInfo.calledContent = updateReq.contentInfo.content;
    		}
			RestClientUtil.ajaxRequest({
				type: 'POST',
				url: '/ecpmp/ecpmpServices/contentService/updateContent',
				data: JSON.stringify(updateReq),
				success: function (result) {
					$rootScope.$apply(function () {
						var data = result.result;
						if (data.resultCode == '1030100000') {
							$('#addHotlineContentCancel').click();
						} else if (data.resultCode == '1030120091') {
							$('#addHotlineContentCancel').click();
                            $scope.tip = "内容正在待审核，请稍后再试。";
                            $('#myModal').modal();
                        } else {
							$('#addHotlineContentCancel').click();
							$scope.tip = data.resultCode;
							$('#myModal').modal();
						}
					})
				},
				error: function () {
					$rootScope.$apply(function () {
						$('#addHotlineContentCancel').click();
						$scope.tip = '1030120500';
						$('#myModal').modal();
					})
				}
			});
		}
    };

    // 删除热线内容弹窗
    $scope.deleteHotlineContent = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteHotlineContent').modal();
    };
    //删除热线内容
    $scope.delHotlineContent = function () {
        var item = $scope.selectedItemDel;
        console.log(item);
        var removeReq = {
            "operaterType": item.servType,
            "id": item.contentID
        };
        $scope.deleteContent(removeReq);
    };
    $scope.deleteContent = function (req) {
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $('#deleteHotlineContentCancel').click();
                        $scope.queryHotlineContentInfoList('justPage');
                    } else {
                        $('#deleteHotlineContentCancel').click();
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteHotlineContentCancel').click();
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };
    //彩印内容必填，最长62个字
    $scope.checkHotlineContent = function () {
        $scope.contentVali = true;
        if ($scope.addHotlineContentInfo.content) {
            if ($scope.addHotlineContentInfo.content.length > $scope.number||$scope.addHotlineContentInfo.content.indexOf("€")>=0) {
                $scope.contentVali = false;
            } else {
                $scope.contentVali = true;
            }
            $scope.sensitiveCheck($scope.addHotlineContentInfo.content);
        } else {
            $scope.sensitiveWords = [];
            $scope.isSensitive = false;
            $scope.sensitiveWordsStr = "";
            $scope.contentVali = false;
        }
        if (!$scope.contentVali) {
            $scope.contentDesc = $scope.dsc;
            if ($scope.addHotlineContentInfo.serviceType2
            		&& !$scope.addHotlineContentInfo.serviceType1
            		&& !$scope.addHotlineContentInfo.serviceType5) {
            	$scope.contentDescText = "模板内容必填，最长70个字且不能存在€符号";
            } else {
            	$scope.contentDescText = "请输入模板内容，不超过70字";
            }
        } else {
            $scope.contentDesc = "";
        }


    };
    $scope.sensitiveCheck = function (content) {
        if (!content) {
            return;
        }
        content = content.replace(/\s/g, '');
        var req = {
            "content": content || '',
        };

        $scope.sensitiveWordsStr = '';
        $scope.isSensitive = false;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030120017') {
                    $scope.sensitiveWordsStr = result.sensitiveWords || [];
                    if ($scope.sensitiveWordsStr.length > 0) {
                        $scope.isSensitive = true;
                        $scope.sensitiveWordsStr = $scope.sensitiveWordsStr.join('、');
                    } else {
                        $scope.isSensitive = false;
                    }
                } else if (data.resultCode == '1030100000') {
                    $scope.sensitiveWordsStr = "";
                    $scope.isSensitive = false;
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
                // })
            },
            error: function () {
                // $rootScope.$apply(function () {
                $scope.tip = '1030120500';
                $('#myModal').modal();
                // })
            }
        });
    };
    $scope.generateReq = function () {
    	var req = {
            "contentName": $scope.content || '',
            "servTypeList": $scope.initSel.servType?[$scope.initSel.servType]:[1,2,5],
            "subServTypeList": $scope.initSel.subServType?[$scope.initSel.subServType]:null,
            "contentTypeList": [6],
            "supportEnterpriseType":$scope.initSel.supportEnterpriseType,
            "approveStatus":$scope.initSel.auditStatus,
            "pageParameter": {
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[0].pageSize),
                "isReturnTotal": "1",
            }
        };
    	return req;
    };
    //查询热线内容列表
    $scope.queryHotlineContentInfoList = function (condition) {
        if (condition != 'justPage') {
            var req = $scope.generateReq();
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryHotlineContentInfoListTemp = angular.copy(req);
        } else {
            //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.queryHotlineContentInfoListTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        req.getBelongOrg = 0;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.contentInfoListData = result.contentInfoList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                        if($scope.contentInfoListData.length === 0&&$scope.pageInfo[0].totalCount>0&&$scope.pageInfo[0].currentPage>1){
                            $scope.pageInfo[0].currentPage = $scope.pageInfo[0].currentPage - 1;
                            $scope.queryHotlineContentInfoList('justPage');
                        }


                    } else {
                        $scope.contentInfoListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.contentInfoListData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };
    // 导出
    $scope.exportYsmbFile = function () {
        var req = {
            "param":{"req":JSON.stringify($scope.generateReq())},
            "url":"/qycy/ecpmp/ecpmpServices/contentService/downYsmbInfoCsvFileService",
            "method":"get"
        }
        CommonUtils.exportFile(req);
    }
    $("[name='addSupportEnterpriseType']").click(function (e){
        var enterpriseTypes = ["1", "3", "5"];
        let val = e.target.value;
        if(val !== "5"&& $scope.addHotlineContentInfo.serviceType5){
            $scope.addHotlineContentInfo.serviceType5 = false;
        }
        for(let t of enterpriseTypes){
            if(t!==val){
                $scope.addHotlineContentInfo["supportEnterpriseType" + t] = false;
            }
        }

    });

    $("[name='serviceType']").click(function (e){
        var serviceTypes = [1, 2, 5];
        let val = e.target.value;
        for(let t of serviceTypes){
            if(t!==val){
                $scope.addHotlineContentInfo["serviceType" + t] = false;
            }
        }

    });
//    $("#colorContent").keydown( function (e) {
//        var ev = (typeof event != 'undefined') ? window.event : e;
//        if (ev.keyCode == 13) {
//            return false;
//        }
//    } );
}]);
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}]);
app.filter("formatDate", function () {
    return function (date) {
        if (date) {
            return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
        }
        return "";
    }
})