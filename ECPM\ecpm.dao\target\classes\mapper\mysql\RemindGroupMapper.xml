<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.RemindGroupMapper">
	    <resultMap id="remindGroupWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.RemindGroupWrapper">
        <result property="ID" column="ID" javaType="java.lang.Integer" />
        <result property="remindGroupID" column="remindGroupID" javaType="java.lang.String" />
        <result property="smsCalledGroupID" column="smsCalledGroupID" javaType="java.lang.String" />
        <result property="flashCallerGroupID" column="flashCallerGroupID" javaType="java.lang.String" />
        <result property="flashCalledGroupID" column="flashCalledGroupID" javaType="java.lang.String" />        
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
    </resultMap>
	
	
	<select id="queryRemindGroupId" resultType="java.lang.String">
		select remindGroupID
		from ecpm_t_remind_group
	</select>
	
	<select id="queryByID" resultMap="remindGroupWrapper">
		select ID, remindGroupID, smsCalledGroupID, flashCallerGroupID, flashCalledGroupID
		from ecpm_t_remind_group
		where remindGroupID = #{remindGroupID}
	</select>
	
	<select id="query" resultMap="remindGroupWrapper">
		select ID, remindGroupID, smsCalledGroupID, flashCallerGroupID, flashCalledGroupID
		from ecpm_t_remind_group
	</select>
	
	<insert id="batchSave">
        insert into
		ecpm_t_remind_group
		(
		remindGroupID, 
		smsCalledGroupID, 
		flashCallerGroupID, 
		flashCalledGroupID,
		createTime,
		updateTime
		)
		values
		<foreach collection="list" item="wrapper" separator=",">
			(
			#{wrapper.remindGroupID},
			#{wrapper.smsCalledGroupID},
			#{wrapper.flashCallerGroupID},
			#{wrapper.flashCalledGroupID},
			now(),
			now()
			)
		</foreach>
    </insert>
    
    <delete id="batchDelete">
    	delete from ecpm_t_remind_group
    	where 
		remindGroupID in
		<foreach item="remindGroupID" index="index" collection="list" open="(" separator="," close=")">
					#{remindGroupID}
		</foreach>
    </delete>
</mapper>