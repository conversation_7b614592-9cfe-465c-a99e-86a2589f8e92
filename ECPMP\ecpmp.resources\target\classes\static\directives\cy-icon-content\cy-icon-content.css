.custom-tooltip-wrapper {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.img-wrap {
  display: inline-block;
  cursor: pointer;
}

.img-wrap img {
  width: 16px;
  height: 16px;
  vertical-align: middle;
}

.tooltip-content {
  position: absolute;
  z-index: 1000;
  padding: 10px;
  font-size: 12px;
  line-height: 1.4;
  width: 300px;
  background: #f0f0f0;
  color: #333;
  border-radius: 4px;
  box-sizing: border-box;
  transition: opacity .3s, transform .3s;
  transform: scale(0.95);
  /* 添加padding确保鼠标可以移动到tooltip上 */
  padding: 10px;
  /* 确保内容区域可以交互 */
  pointer-events: auto;
}

/* 确保tooltip和图标之间有一定的间隔，方便鼠标移动 */
.tooltip-content:before {
  content: '';
  position: absolute;
  width: 100%;
  height: 10px;
}

/* 根据不同位置添加不同的间隔区域 */
.top-left:before, .top:before, .top-right:before {
  bottom: -10px;
  left: 0;
}

.bottom-left:before, .bottom:before, .bottom-right:before {
  top: -10px;
  left: 0;
}

.left-top:before, .left:before, .left-bottom:before {
  right: -10px;
  top: 0;
  width: 10px;
  height: 100%;
}

.right-top:before, .right:before, .right-bottom:before {
  left: -10px;
  top: 0;
  width: 10px;
  height: 100%;
}

.tooltip-content.ng-show {
  opacity: 1;
  transform: scale(1);
}

.tooltip-inner {
  max-width: 300px!important;
  max-height: 300px!important;
  overflow-y: auto;
  word-wrap: break-word;
  background-color: #f0f0f0;
  color: #333;
  text-align: left;
}

.tooltip-content .ng-hide {
  opacity: 0;
  transform: scale(0.95);
}




/* 箭头基础样式 */
[class*="tooltip-content"]:after {
  content: '';
  position: absolute;
  border-width: 5px;
  border-style: solid;
}

/* 上方位置 */
.top-left, .top, .top-right {
  bottom: 100%;
  margin-bottom: 12px;
}

.top-left:after, .top:after, .top-right:after {
  bottom: -10px;
  border-color: #f0f0f0 transparent transparent transparent;
}

.top-left {
  left: 0;
}
.top-left:after {
  left: 10%;
}

.top {
  left: 50%;
  transform: translateX(-50%);
}
.top:after {
  left: 50%;
  transform: translateX(-50%);
}

.top-right {
  right: 0;
}
.top-right:after {
  right: 10%;
}

/* 右侧位置 */
.right-top, .right, .right-bottom {
  left: 100%;
  margin-left: 12px;
}

.right-top:after, .right:after, .right-bottom:after {
  left: -10px;
  border-color: transparent #f0f0f0 transparent transparent;
}

.right-top {
  top: -10px;
}
.right-top:after {
  top: 10%;
}

.right {
  top: 50%;
  transform: translateY(-50%);
}
.right:after {
  top: 50%;
  transform: translateY(-50%);
}

.right-bottom {
  bottom: 0;
}
.right-bottom:after {
  bottom: 10%;
}

/* 下方位置 */
.bottom-left, .bottom, .bottom-right {
  top: 100%;
  margin-top: 12px;
}

.bottom-left:after, .bottom:after, .bottom-right:after {
  top: -10px;
  border-color: transparent transparent #f0f0f0 transparent;
}

.bottom-left {
  left: 0;
}
.bottom-left:after {
  left: 10%;
}

.bottom {
  left: 50%;
  transform: translateX(-50%);
}
.bottom:after {
  left: 50%;
  transform: translateX(-50%);
}

.bottom-right {
  right: 0;
}
.bottom-right:after {
  right: 10%;
}

/* 左侧位置 */
.left-top, .left, .left-bottom {
  right: 100%;
  margin-right: 12px;
}

.left-top:after, .left:after, .left-bottom:after {
  right: -10px;
  border-color: transparent transparent transparent #f0f0f0;
}

.left-top {
  top: 0;
}
.left-top:after {
  top: 10%;
}

.left {
  top: 50%;
  transform: translateY(-50%);
}
.left:after {
  top: 50%;
  transform: translateY(-50%);
}

.left-bottom {
  bottom: 0;
}
.left-bottom:after {
  bottom: 10%;
}

