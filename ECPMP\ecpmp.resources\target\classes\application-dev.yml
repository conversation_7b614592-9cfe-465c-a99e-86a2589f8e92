spring:
  http:
    encoding:
      force: true
      charset: UTF-8
    multipart:
      enabled: true
      max-file-size: 50MB
  freemarker:
    allow-request-override: false
    allow-session-override: false
    cache: false
    check-template-location: true
    charset: UTF-8
    content-type: text/html; charset=utf-8
    prefer-file-system-access: false
    expose-request-attributes: false
    expose-session-attributes: false
    expose-spring-macro-helpers: false
    suffix: .html
    template-loader-path: classpath:/templates
    request-context-attribute: request


feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 100000
        readTimeout: 100000
hystrix:
 command:
  default:
   execution:
    isolation:
     thread:
      timeoutInMilliseconds: 20000
     strategy: SEMAPHORE
ribbon:
  ReadTimeout: 20000
  ConnectTimeout: 5000
server:
  tomcat:
    max-connections: 1000
  connection-timeout: 20000    
      
cas:
  #移动云开发环境
  server-url-prefix: https://ecloud.10086.cn/op-user-sso
  server-login-url: https://ecloud.10086.cn/op-user-sso/login
  #本地调试，https可以改为http,并配置好本地的DNS
  client-host-url: cy.migudm.cn
  use-session: true
  validation-type: cas
  #需要cas认证的路径，/*匹配所有路径，多个路径用逗号隔开
  authentication-url-patterns: /ecpmpServices/mobileCould/*
  validation-url-patterns: /ecpmpServices/mobileCould/*
config:
  logoutUrl: https://ecloud.10086.cn/op-user-sso/logout?service=https://ecloud.10086.cn:31008/user/logout 
