<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ExternalProductMapper">

	<select id="queryExternalProduct" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ExternalProductWrapper">
		select ID,
		productID,
		productPortal,
		productName,
		subServType,
		limitSubscribeNum,
		limitQuotaNum,
		productType
		from ecpm_t_external_product
		<trim prefix="where" prefixOverrides="and|or">
			<if test="productID != null">
				productID=#{productID}
			</if>
		</trim>
	</select>

	<insert id="createExternalProduct">
		insert into ecpm_t_external_product
		(
		productID,
		productPortal,
		productName,
		subServType,
		limitSubscribeNum,
		limitQuotaNum,
		productType
		)
		values
		(
		#{productID},
		#{productPortal},
		#{productName},
		#{subServType},
		#{limitSubscribeNum},
		#{limitQuotaNum}
		#{productType}
		)
	</insert>

	<delete id="deleteExternalProduct">
		delete from ecpm_t_external_product where ID =
		#{ID}
	</delete>


</mapper>