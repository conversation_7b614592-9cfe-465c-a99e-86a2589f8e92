/*
 * 文 件 名:  EsTest.java
 * 版    权:  Huawei Technologies Co., Ltd. Copyright YYYY-YYYY,  All rights reserved
 * 描    述:  <描述>
 * 修 改 人:  lWX595992
 * 修改时间:  2019年1月3日
 * 跟踪单号:  <跟踪单号>
 * 修改单号:  <修改单号>
 * 修改内容:  <修改内容>
 */

package com.huawei.jaguar.dsdp.bill.core.elasticsearch;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.huawei.jaguar.dsdp.bill.commons.utils.DateUtil;
import com.huawei.jaguar.dsdp.bill.core.elasticsearch.Utils.EsUtils;
import com.huawei.jaguar.dsdp.bill.core.elasticsearch.model.*;
import com.huawei.jaguar.dsdp.bill.core.elasticsearch.service.DealExportDeliveryService;
import com.huawei.jaguar.dsdp.bill.core.elasticsearch.service.DealQuerySearchService;
import com.huawei.jaguar.dsum.model.Enterprise;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.transport.TransportClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.transport.InetSocketTransportAddress;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.metrics.cardinality.Cardinality;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.elasticsearch.transport.client.PreBuiltTransportClient;
import org.hibernate.validator.constraints.ModCheck;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.math.BigInteger;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <ES客户端> <功能详细描述>
 *
 * <AUTHOR>
 * @version [版本号, 2019年1月3日]
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Service
@Slf4j
@Configuration
//@PropertySource(value = "classpath:bill-serviceConfig.properties", encoding = "UTF-8")
@RefreshScope
public class ElasticSearchCluster {

    /**
     * es客户端
     */
    private static TransportClient client;

    /**
     * es连接url格式为ip:port|ip1:port1
     */
    @Value("${elasticsearch.server.connect}")
    private String esConnectUrls;

    @Autowired
    private EsUtils esUtils;

    /**
     * 服务名称
     */
    @Value("${elasticsearch.server.clustername}")
    private String clusterName;

    /**
     * 处理es的数据，超过多少数据处理业务，默认50000
     */
    @Value("${task.subProvincialDeliveryCdrUploadTask.dealcdrnumber}")
    private Integer dealCdrNumber;

    @Value("${task.subsideDeliveryCdrUploadTask.subsidecdrnumber}")
    private Integer subsideCdrNumber;

    /**
     * 处理es的数据，超过多少数据处理业务，默认100000
     */
    @Value("${tentBill.maxPageSize}")
    private Integer dealadvertCdrNumber;

    @Autowired
    @Qualifier("ESSearchExecutor")
    private ThreadPoolExecutor ESSearchExecutor;

    /**
     * 保存ES查询快照时间
     */
    private static final int DEFAULT_QUERY_TIME = 18000000;

    /**
     * 热线
     */
    private static Integer HOT_LINE = 2;

    /**
     * 热线主叫
     */
    private static Integer HOT_LINE_SUBTYPE_CALLER = 1;

    /**
     * 热线被叫
     */
    private static Integer HOT_LINE_SUBTYPE_CALLED = 2;

    /**
     * 增彩
     */
    private static Integer ENHANCED_COLOR_PRINT = 4;

    /**
     * 处理es的数据，超过多少数据处理业务，默认100000
     */
    @Value("${task.subProvincialDeliveryHotlineUploadTask.dealcdrnumber}")
    private Integer dealCdrNumberHotline;


    @PostConstruct
    private void init() throws UnknownHostException {
        if (StringUtils.isEmpty(esConnectUrls) || StringUtils.equals(esConnectUrls, "elasticsearchURL")) {
            return;
        }
        List<String> urlList = Arrays.asList(esConnectUrls.split("\\|"));
        Settings settings =
                Settings.builder().put("cluster.name", clusterName).put("client.transport.sniff", false).build();
        client = new PreBuiltTransportClient(settings);
        for (String string : urlList) {
            String[] url = string.split("\\:");
            client.addTransportAddress(
                    new InetSocketTransportAddress(InetAddress.getByName(url[0]), Integer.parseInt(url[1])));
        }
        log.info(">>>>>connect to elasticsearch is success . url is {} ", esConnectUrls);
    }

    /**
     * 查询ES里面的所有数据
     *
     * @param databseName ES数据库名字
     * @param tableName   ES表名
     * @param clazz       需要转换成的类名
     * @return 满足要求的数据
     * <AUTHOR>
     */
    public <T> List<T> queryAllMatchQuery(String databseName, String tableName, Class<T> clazz) {
        log.debug(">>>>>begin ElasticSearchCluster queryAllMatchQuery tableName is {} ", tableName);

        // 定义信息返回
        List<T> objectList = null;

        // 构造查询条件
        // QueryBuilder queryBuilder = QueryBuilders.matchAllQuery();
        try {
            SearchResponse response = client.prepareSearch(esUtils.getDatabaseName(databseName, null, true)).setTypes(tableName).get();
            SearchHits resultHits = response.getHits();

            SearchHit[] searchHits = resultHits.getHits();

            // 如果有数据
            if (null != searchHits && searchHits.length > 0) {
                // 初始化List
                objectList = new ArrayList<T>();

                // 遍历数据
                for (SearchHit searchHit : searchHits) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));
                }

            }

            log.debug(">>>>>Exit ElasticSearchCluster queryAllMatchQuery {} ", objectList);
        } catch (Exception e) {
            log.error("queryAllMatchQuery Fail to query data from ES.The error is {}", e);
        }

        return objectList;
    }

    /**
     * 分页按照指定时间段查询
     *
     * @param elasticSearchQueryCondition
     * @param clazz                       需要转换的类
     * @return 满足要求的数据
     * <AUTHOR>
     */
    public <T> List<T> searchByTimePeriod(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriod queryConditon is {} ",
                elasticSearchQueryCondition);

        // 定义需要返回的List
        List<T> objectList = null;

        // 如果查询条件不为空
        try {
            if (null != elasticSearchQueryCondition) {
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                // 添加时间范围过滤
                String pushTimeFieldName =
                        (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                        .format(elasticSearchQueryCondition.getDateFomat())
                        .from(elasticSearchQueryCondition.getBeginTime())
                        .to(elasticSearchQueryCondition.getEndTime()));

                // 构造“或”的查询条件
                BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();

                // 设置ussd过滤
                shouldQueryBuilder.should(QueryBuilders.matchQuery("noUssdSendResult", 0));
                shouldQueryBuilder.should(QueryBuilders.matchQuery("ussdSendResult", 0));
                boolQueryBuilder.must(shouldQueryBuilder);

                // 获取分页参数
                Integer pageNum = elasticSearchQueryCondition.getPageNum();
                Integer pageSize = elasticSearchQueryCondition.getPageSize();
                String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);
                // 调用ES查询数据
                SearchResponse scrollResp = client.prepareSearch(databaseName)
                        .setTypes(elasticSearchQueryCondition.getTableName())
                        .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .setQuery(boolQueryBuilder)
                        .setFrom((pageNum - 1) * pageSize)
                        .setSize(pageSize)
                        .get();

                Long totalAmount = 0L;

                // 如果查询不到数据，直接return
                if (null != scrollResp && null != scrollResp.getHits()) {
                    totalAmount = scrollResp.getHits().getTotalHits();
                }

                if (0 == totalAmount) {
                    log.info("no query need to deal data from ES");

                    return objectList;
                }

                // 初始化es数据
                objectList = new ArrayList<T>();

                // 循环分页处理
                do {
                    for (SearchHit searchHit : scrollResp.getHits()) {
                        objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    }

                    scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                            .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                            .execute()
                            .actionGet();
                } while (scrollResp.getHits().getHits().length != 0);
            }
        } catch (Exception e) {
            log.error("searchByTimePeriod Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriod queryConditon is {} ", objectList);

        return objectList;
    }

    /**
     * 查询指定时间段的总数
     *
     * @param elasticSearchQueryCondition 查询条件
     * @return 满足要求的总数
     * <AUTHOR>
     */
    public Long searchByTimePeriodCount(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriodCount queryConditon is {}",
                elasticSearchQueryCondition);

        // 默认没有数据
        Long totalAmount = 0L;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                // 添加时间范围过滤
                String pushTimeFieldName =
                        (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                        .format(elasticSearchQueryCondition.getDateFomat())
                        .from(elasticSearchQueryCondition.getBeginTime())
                        .to(elasticSearchQueryCondition.getEndTime()));

                // 构造“或”的查询条件
                BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();

                // 设置ussd过滤
                shouldQueryBuilder.should(QueryBuilders.matchQuery("noUssdSendResult", 0));
                shouldQueryBuilder.should(QueryBuilders.matchQuery("ussdSendResult", 0));
                boolQueryBuilder.must(shouldQueryBuilder);

                // 调用ES分页查询数据
                String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);
                SearchRequestBuilder searchRequestBuilder =
                        client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);

                // 获取ES的响应
                SearchResponse response = searchRequestBuilder.execute().actionGet();

                // 如果响应不为空
                if (null != response && null != response.getHits()) {
                    totalAmount = response.getHits().getTotalHits();
                }
            }
        } catch (Exception e) {
            log.error("searchByTimePeriodCount Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriodCount queryConditon is totalAmount {}",
                totalAmount);

        return totalAmount;
    }

    /**
     * 查询指定条件和时间段的使用CMPP模板的使用量
     *
     * @param elasticSearchQueryCondition 查询条件
     * @return 满足要求的总数
     * <AUTHOR>
     */
    public Long searchByTimePeriodAndCmppTemplate(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriodAndCmppTemplate queryConditon is {}",
                elasticSearchQueryCondition);

        // 默认没有数据
        Long totalAmount = 0L;
        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                SearchRequestBuilder searchRequestBuilder = null;
                //设置ruleID等条件
                if (null != elasticSearchQueryCondition.getQueryCondition()) {
                    String ruleIDFieldName = (String) elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName");
                    boolQueryBuilder.must(QueryBuilders.matchQuery(ruleIDFieldName,
                            elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)));

                    String ownerFieldName = (String) elasticSearchQueryCondition.getQueryCondition().get("ownerFieldName");
                    if (StringUtils.isNotEmpty(ownerFieldName)) {
                        boolQueryBuilder.must(QueryBuilders.matchQuery(ownerFieldName, elasticSearchQueryCondition.getQueryCondition().get(ownerFieldName)));
                    }
                    String enterpriseIDFieldName = (String) elasticSearchQueryCondition.getQueryCondition().get("enterpriseIDFieldName");
                    boolQueryBuilder.must(QueryBuilders.matchQuery(enterpriseIDFieldName, elasticSearchQueryCondition.getQueryCondition().get(enterpriseIDFieldName)));

                    // 添加时间范围过滤
                    String pushTimeFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
                    List<QueryBuilder> shoulds = new ArrayList<QueryBuilder>();
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "0"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "1"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "2"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "8"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "999"));
                    BoolQueryBuilder boolQueryBuildershould = QueryBuilders.boolQuery();
                    for (QueryBuilder queryBuild : shoulds) {
                        boolQueryBuildershould.should(queryBuild);
                    }
                    boolQueryBuilder.must(boolQueryBuildershould);

                    SumAggregationBuilder sumBuilder = AggregationBuilders.sum("sum_chargeCount").field("chargeCount");

                    // 调用ES查询数据
                    String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                    String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);
                    searchRequestBuilder =
                            client.prepareSearch(databaseName)
                                    .setTypes(elasticSearchQueryCondition.getTableName())
                                    .setQuery(boolQueryBuilder)
                                    .addAggregation(sumBuilder);

                    // 获取ES的响应
                    SearchResponse response = searchRequestBuilder.execute().actionGet();

                    // 如果响应不为空
                    if (null != response && null != response.getAggregations()
                            && null != response.getAggregations().get("sum_chargeCount")) {
                        Sum sum = response.getAggregations().get("sum_chargeCount");
                        totalAmount = Double.valueOf(sum.getValue()).longValue();
                    }
                }
            }
        } catch (Exception e) {
            log.error("searchByTimePeriodAndCmppTemplate Fail to query data from ES.The error is {}", e);
        }
        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriodAndCmppTemplate result is totalAmount {}", totalAmount);
        return totalAmount;
    }

    /**
     * 查询指定条件和时间段的总数
     *
     * @param elasticSearchQueryCondition 查询条件
     * @return 满足要求的总数
     * <AUTHOR>
     */
    public Long searchByTimePeriodAndRuleID(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriodAndRuleID queryConditon is {}",
                elasticSearchQueryCondition);

        // 默认没有数据
        Long totalAmount = 0L;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                SearchRequestBuilder searchRequestBuilder = null;

                // 设置ruleID等条件
                if (null != elasticSearchQueryCondition.getQueryCondition()) {
                    String ruleIDFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName");
                    boolQueryBuilder.must(QueryBuilders.matchQuery(ruleIDFieldName,
                            elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)));

                    // 设置ussd过滤
                    Integer servType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("servType");
                    // 添加时间范围过滤
                    String pushTimeFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
                    Integer subServType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("subServType");
                    String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                    String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

                    if (null != servType && servType.equals(HOT_LINE)) {
                        if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLER)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MO"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 1));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        } else if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLED)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MT"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 2));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        }
                        List<QueryBuilder> shoulds = new ArrayList<QueryBuilder>();
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "0"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "1"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "2"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "8"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "999"));
                        BoolQueryBuilder boolQueryBuildershould = QueryBuilders.boolQuery();
                        for (QueryBuilder queryBuild : shoulds) {
                            boolQueryBuildershould.should(queryBuild);
                        }
                        boolQueryBuilder.must(boolQueryBuildershould);

                        Integer enterpriseID = (Integer) elasticSearchQueryCondition.getQueryCondition().get("enterpriseID");
                        if (null != enterpriseID) {
                            boolQueryBuilder.must(QueryBuilders.matchQuery("enterpriseID", enterpriseID));
                        }

                        // 以前的chargeCount都是1,依据上面的条件查询出多少条数据就是多少使用量,迭代8改造,挂机长短信算多个使用量chargeCount,需要依据chargeCount累加
                        // 迭代8改造,构造聚合条件,依据chargeCount求和,name为sum_chargeCount(自定义)
                        SumAggregationBuilder sumBuilder = AggregationBuilders.sum("sum_chargeCount").field("chargeCount");

                        // 调用ES查询数据
                        searchRequestBuilder = client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder)
                                .addAggregation(sumBuilder);

                        // 获取ES的响应
                        SearchResponse response = searchRequestBuilder.execute().actionGet();

                        // 如果响应不为空
                        if (null != response && null != response.getAggregations()
                                && null != response.getAggregations().get("sum_chargeCount")) {
                            Sum sum = response.getAggregations().get("sum_chargeCount");
                            totalAmount = Double.valueOf(sum.getValue()).longValue();
                        }

                    } else if (null != servType && servType.equals(ENHANCED_COLOR_PRINT)) {
                        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("deliveryResult", "90"));
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("deliveryResult", "999"));
                        boolQueryBuilder.must(shouldQueryBuilder);
                        // 调用ES查询数据
                        boolQueryBuilder.must(shouldQueryBuilder);
                        // 调用ES查询数据
                        searchRequestBuilder = client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);
                        // 获取ES的响应
                        SearchResponse response = searchRequestBuilder.execute().actionGet();

                        // 如果响应不为空
                        if (null != response && null != response.getHits()) {
                            totalAmount = response.getHits().getTotalHits();
                        }
                    } else {
                        // 构造“或”的查询条件
                        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("noUssdSendResult", 0));
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("ussdSendResult", 0));
                        boolQueryBuilder.must(shouldQueryBuilder);
                        // 调用ES查询数据
                        searchRequestBuilder = client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);

                        // 获取ES的响应
                        SearchResponse response = searchRequestBuilder.execute().actionGet();

                        // 如果响应不为空
                        if (null != response && null != response.getHits()) {
                            totalAmount = response.getHits().getTotalHits();
                        }
                    }
                } else {
                    // 调用ES查询数据
                    String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), null, true);
                    searchRequestBuilder = client.prepareSearch(databaseName)
                            .setTypes(elasticSearchQueryCondition.getTableName())
                            .setQuery(boolQueryBuilder);

                    // 获取ES的响应
                    SearchResponse response = searchRequestBuilder.execute().actionGet();

                    // 如果响应不为空
                    if (null != response && null != response.getHits()) {
                        totalAmount = response.getHits().getTotalHits();
                    }
                }
            }
        } catch (Exception e) {
            log.error("searchByTimePeriodAndRuleID Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriodAndRuleID result is totalAmount {}", totalAmount);

        return totalAmount;
    }

    /**
     * 查询指定条件和时间段的总数 <功能详细描述>
     *
     * @param elasticSearchQueryCondition
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    public Long searchByTimePeriodAndRuleIDWithOwner(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriodAndRuleIDWithOwner queryConditon is {}",
                elasticSearchQueryCondition);

        // 默认没有数据
        Long totalAmount = 0L;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                SearchRequestBuilder searchRequestBuilder = null;

                if (null != elasticSearchQueryCondition.getQueryCondition()) {
                    // 设置ruleID条件
                    String ruleIDFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName");
                    boolQueryBuilder.must(QueryBuilders.matchQuery(ruleIDFieldName,
                            elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)));

                    // 设置于运营商条件
                    String ownerFieldName = (String) elasticSearchQueryCondition.getQueryCondition().get("ownerFieldName");
                    boolQueryBuilder.must(QueryBuilders.matchQuery(ownerFieldName,
                            elasticSearchQueryCondition.getQueryCondition().get(ownerFieldName)));
                    Integer servType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("servType");
                    Integer subServType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("subServType");

                    // 添加时间范围过滤
                    String pushTimeFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
                    String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                    String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

                    if (elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName2") != null) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery((String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName2"))
                                .from((String) elasticSearchQueryCondition.getQueryCondition().get("yesterdayBegin2"))
                                .to((String) elasticSearchQueryCondition.getQueryCondition().get("yesterdayEnd2")));
                    }

                    if (null != servType && servType.equals(HOT_LINE)) {
                        if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLER)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MO"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 1));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        } else if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLED)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MT"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 2));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        }
                        List<QueryBuilder> shoulds = new ArrayList<QueryBuilder>();
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "0"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "1"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "2"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "8"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "999"));
                        BoolQueryBuilder boolQueryBuildershould = QueryBuilders.boolQuery();
                        for (QueryBuilder queryBuild : shoulds) {
                            boolQueryBuildershould.should(queryBuild);
                        }
                        boolQueryBuilder.must(boolQueryBuildershould);

                        Integer enterpriseID = (Integer) elasticSearchQueryCondition.getQueryCondition().get("enterpriseID");
                        if (null != enterpriseID) {
                            boolQueryBuilder.must(QueryBuilders.matchQuery("enterpriseID", enterpriseID));
                        }

                        // 以前的chargeCount都是1,依据上面的条件查询出多少条数据就是多少使用量,迭代8改造,挂机长短信算多个使用量chargeCount,需要依据chargeCount累加
                        // 迭代8改造,构造聚合条件,依据chargeCount求和,name为sum_chargeCount(自定义)
                        SumAggregationBuilder sumBuilder = AggregationBuilders.sum("sum_chargeCount").field("chargeCount");

                        // 调用ES查询数据
                        searchRequestBuilder = client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder)
                                .addAggregation(sumBuilder);

                        // 获取ES的响应
                        SearchResponse response = searchRequestBuilder.execute().actionGet();

                        // 如果响应不为空
                        if (null != response && null != response.getAggregations()
                                && null != response.getAggregations().get("sum_chargeCount")) {
                            Sum sum = response.getAggregations().get("sum_chargeCount");
                            totalAmount = Double.valueOf(sum.getValue()).longValue();
                        }

                    } else if (null != servType && servType.equals(ENHANCED_COLOR_PRINT)) {
                        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("deliveryResult", 90));
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("deliveryResult", 999));
                        boolQueryBuilder.must(shouldQueryBuilder);
                        // 调用ES查询数据
                        searchRequestBuilder = client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);
                        // 获取ES的响应
                        SearchResponse response = searchRequestBuilder.execute().actionGet();

                        // 如果响应不为空
                        if (null != response && null != response.getHits()) {
                            totalAmount = response.getHits().getTotalHits();
                        }
                    } else {
                        // 构造“或”的查询条件
                        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("noUssdSendResult", 0));
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("ussdSendResult", 0));
                        boolQueryBuilder.must(shouldQueryBuilder);

                        // 调用ES查询数据
                        searchRequestBuilder = client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);

                        // 获取ES的响应
                        SearchResponse response = searchRequestBuilder.execute().actionGet();

                        // 如果响应不为空
                        if (null != response && null != response.getHits()) {
                            totalAmount = response.getHits().getTotalHits();
                        }
                    }
                } else {
                    // 调用ES查询数据
                    String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), null, true);
                    searchRequestBuilder = client.prepareSearch(databaseName)
                            .setTypes(elasticSearchQueryCondition.getTableName())
                            .setQuery(boolQueryBuilder);

                    // 获取ES的响应
                    SearchResponse response = searchRequestBuilder.execute().actionGet();

                    // 如果响应不为空
                    if (null != response && null != response.getHits()) {
                        totalAmount = response.getHits().getTotalHits();
                    }
                }
            }
        } catch (Exception e) {
            log.error("searchByTimePeriodAndRuleIDWithOwner Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriodAndRuleIDWithOwner result is totalAmount {}", totalAmount);

        return totalAmount;
    }

    /**
     * 查询指定条件和时间段的总数 <功能详细描述>
     *
     * @param elasticSearchQueryCondition
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    public Long searchByTimePeriodForHotline(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriodForHotline queryConditon is {}",
                elasticSearchQueryCondition);

        // 默认没有数据
        Long totalAmount = 0L;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                SearchRequestBuilder searchRequestBuilder = null;

                if (null != elasticSearchQueryCondition.getQueryCondition()) {

                    // 设置于运营商条件
                    String ownerFieldName = (String) elasticSearchQueryCondition.getQueryCondition().get("ownerFieldName");
                    if (StringUtils.isNotEmpty(ownerFieldName)) {
                        boolQueryBuilder.must(QueryBuilders.matchQuery(ownerFieldName,
                                elasticSearchQueryCondition.getQueryCondition().get(ownerFieldName)));
                    }

                    Integer servType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("servType");
                    if (null != servType) {
                        boolQueryBuilder.must(QueryBuilders.matchQuery("serviceType",
                                servType));
                    }
                    Integer subServType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("subServType");
                    if (null != subServType) {
                        boolQueryBuilder.must(QueryBuilders.matchQuery("subServiceType",
                                subServType));
                    }

                    // 添加时间范围过滤
                    String pushTimeFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                    String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                    String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

                    boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
                    if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLER)) {
                        List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                        mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MO"));
                        mOshoulds.add(QueryBuilders.termQuery("subServiceType", 1));
                        BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                        for (QueryBuilder queryBuild : mOshoulds) {
                            mOboolQueryBuildershould.should(queryBuild);
                        }
                        boolQueryBuilder.must(mOboolQueryBuildershould);
                    } else if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLED)) {
                        List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                        mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MT"));
                        mOshoulds.add(QueryBuilders.termQuery("subServiceType", 2));
                        BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                        for (QueryBuilder queryBuild : mOshoulds) {
                            mOboolQueryBuildershould.should(queryBuild);
                        }
                        boolQueryBuilder.must(mOboolQueryBuildershould);
                    }
                    List<QueryBuilder> shoulds = new ArrayList<QueryBuilder>();
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "0"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "1"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "2"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "8"));
                    shoulds.add(QueryBuilders.termQuery("deliveryResult", "999"));
                    BoolQueryBuilder boolQueryBuildershould = QueryBuilders.boolQuery();
                    for (QueryBuilder queryBuild : shoulds) {
                        boolQueryBuildershould.should(queryBuild);
                    }
                    boolQueryBuilder.must(boolQueryBuildershould);

                    Integer enterpriseID = (Integer) elasticSearchQueryCondition.getQueryCondition().get("enterpriseID");
                    if (null != enterpriseID) {
                        boolQueryBuilder.must(QueryBuilders.matchQuery("enterpriseID", enterpriseID));
                    }

                    // 或
                    String sendTypeFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("sendTypeFieldName");
                    String colorSendType = (String) elasticSearchQueryCondition.getQueryCondition().get(sendTypeFieldName);
                    if (StringUtils.isNotBlank(sendTypeFieldName)) {
                        // 构造“或”的查询条件
                        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                        List<String> colorSendTypeList = Arrays.asList(StringUtils.split(colorSendType, "|"));
                        for (String type : colorSendTypeList) {
                            shouldQuery.should(QueryBuilders.matchQuery(sendTypeFieldName, type));
                        }
                        boolQueryBuilder.must(shouldQuery);
                    }

                    // 以前的chargeCount都是1,依据上面的条件查询出多少条数据就是多少使用量,迭代8改造,挂机长短信算多个使用量chargeCount,需要依据chargeCount累加
                    // 迭代8改造,构造聚合条件,依据chargeCount求和,name为sum_chargeCount(自定义)
                    SumAggregationBuilder sumBuilder = AggregationBuilders.sum("sum_chargeCount").field("chargeCount");

                    // 调用ES查询数据
                    searchRequestBuilder = client.prepareSearch(databaseName)
                            .setTypes(elasticSearchQueryCondition.getTableName())
                            .setQuery(boolQueryBuilder)
                            .addAggregation(sumBuilder);

                    // 获取ES的响应
                    SearchResponse response = searchRequestBuilder.execute().actionGet();

                    // 如果响应不为空
                    if (null != response && null != response.getAggregations()
                            && null != response.getAggregations().get("sum_chargeCount")) {
                        Sum sum = response.getAggregations().get("sum_chargeCount");
                        totalAmount = Double.valueOf(sum.getValue()).longValue();
                    }

                } else {
                    // 调用ES查询数据
                    String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), null, true);
                    searchRequestBuilder = client.prepareSearch(databaseName)
                            .setTypes(elasticSearchQueryCondition.getTableName())
                            .setQuery(boolQueryBuilder);

                    // 获取ES的响应
                    SearchResponse response = searchRequestBuilder.execute().actionGet();

                    // 如果响应不为空
                    if (null != response && null != response.getHits()) {
                        totalAmount = response.getHits().getTotalHits();
                    }
                }
            }
        } catch (Exception e) {
            log.error("searchByTimePeriodForHotline Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriodForHotline result is totalAmount {}", totalAmount);

        return totalAmount;
    }

    /**
     * 查询指定条件和时间段的总数
     *
     * @param elasticSearchQueryCondition 查询条件
     * @return 满足要求的总数
     * <AUTHOR>
     */
    public Long searchByTimePeriodAndSendType(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriodAndSendType queryConditon is {}",
                elasticSearchQueryCondition);

        // 默认没有数据
        Long totalAmount = 0L;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                // 设置ruleID等条件
                if (null != elasticSearchQueryCondition.getQueryCondition()) {
                    // 设置ussd过滤
                    Integer servType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("servType");
                    Integer subServType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("subServType");
                    if (null != servType && servType.equals(HOT_LINE)) {
                        if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLER)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MO"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 1));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        } else if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLED)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MT"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 2));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        }
                        List<QueryBuilder> shoulds = new ArrayList<QueryBuilder>();
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "0"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "1"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "2"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "8"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "999"));

                        BoolQueryBuilder boolQueryBuildershould = QueryBuilders.boolQuery();
                        for (QueryBuilder queryBuild : shoulds) {
                            boolQueryBuildershould.should(queryBuild);
                        }
                        boolQueryBuilder.must(boolQueryBuildershould);
                        Integer enterpriseID = (Integer) elasticSearchQueryCondition.getQueryCondition().get("enterpriseID");

                        if (null != enterpriseID) {
                            boolQueryBuilder.must(QueryBuilders.matchQuery("enterpriseID", enterpriseID));
                        }
                    } else {
                        // 构造“或”的查询条件
                        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("noUssdSendResult", 0));
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("ussdSendResult", 0));
                        boolQueryBuilder.must(shouldQueryBuilder);
                    }

                    // 并且
                    String ruleIDFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName");
                    boolQueryBuilder.must(QueryBuilders.matchQuery(ruleIDFieldName,
                            elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)));

                    // 或
                    String sendTypeFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("sendTypeFieldName");
                    String colorSendType = (String) elasticSearchQueryCondition.getQueryCondition().get(sendTypeFieldName);
                    if (StringUtils.isNotBlank(sendTypeFieldName)) {
                        // 构造“或”的查询条件
                        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                        List<String> colorSendTypeList = Arrays.asList(StringUtils.split(colorSendType, "|"));
                        for (String type : colorSendTypeList) {
                            shouldQuery.should(QueryBuilders.matchQuery(sendTypeFieldName, type));
                        }
                        boolQueryBuilder.must(shouldQuery);
                    }

                }

                // 添加时间范围过滤
                String pushTimeFieldName =
                        (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                        .format(elasticSearchQueryCondition.getDateFomat())
                        .gte(elasticSearchQueryCondition.getBeginTime())
                        .lte(elasticSearchQueryCondition.getEndTime()));

                // 调用ES查询数据
                String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

                SearchRequestBuilder searchRequestBuilder =
                        client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);

                // 获取ES的响应
                SearchResponse response = searchRequestBuilder.execute().actionGet();

                // 如果响应不为空
                if (null != response && null != response.getHits()) {
                    totalAmount = response.getHits().getTotalHits();
                }
            }
        } catch (Exception e) {
            log.error("searchByTimePeriodAndSendType Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriodAndSendType queryConditon is totalAmount {}",
                totalAmount);

        return totalAmount;
    }

    /**
     * 查询指定条件和时间段的总数(按照字段去重)
     *
     * @param elasticSearchQueryCondition 查询条件
     * @return 满足要求的总数
     * <AUTHOR>
     */
    public Integer searchByTimePeriodAndRuleIDGroupByPhone(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster searchByTimePeriodAndRuleIDGroupByPhone queryConditon is {}",
                elasticSearchQueryCondition);

        // 默认没有数据
        Integer totalAmount = 0;
        Integer servType = null;
        Integer subServType = null;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                // 设置ruleID
                if (null != elasticSearchQueryCondition.getQueryCondition()) {
                    String ruleIDFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName");
                    boolQueryBuilder.must(QueryBuilders.matchQuery(ruleIDFieldName,
                            elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)));

                    // 设置ussd过滤
                    servType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("servType");
                    subServType = (Integer) elasticSearchQueryCondition.getQueryCondition().get("subServType");
                    if (null != servType && servType.equals(HOT_LINE)) {
                        if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLER)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MO"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 1));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        } else if (null != subServType && subServType.equals(HOT_LINE_SUBTYPE_CALLED)) {
                            List<QueryBuilder> mOshoulds = new ArrayList<QueryBuilder>();
                            mOshoulds.add(QueryBuilders.termQuery("callProcess.keyword", "MT"));
                            mOshoulds.add(QueryBuilders.termQuery("subServiceType", 2));
                            BoolQueryBuilder mOboolQueryBuildershould = QueryBuilders.boolQuery();
                            for (QueryBuilder queryBuild : mOshoulds) {
                                mOboolQueryBuildershould.should(queryBuild);
                            }
                            boolQueryBuilder.must(mOboolQueryBuildershould);
                        }
                        List<QueryBuilder> shoulds = new ArrayList<QueryBuilder>();
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "0"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "1"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "2"));
                        shoulds.add(QueryBuilders.termQuery("deliveryResult", "8"));

                        BoolQueryBuilder boolQueryBuildershould = QueryBuilders.boolQuery();
                        for (QueryBuilder queryBuild : shoulds) {
                            boolQueryBuildershould.should(queryBuild);
                        }
                        boolQueryBuilder.must(boolQueryBuildershould);
                    } else if (null == servType || !ENHANCED_COLOR_PRINT.equals(servType)) {
                        // 构造“或”的查询条件
                        BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("deliveryResult", 90));
                        shouldQueryBuilder.should(QueryBuilders.matchQuery("deliveryResult", 999));
                        boolQueryBuilder.must(shouldQueryBuilder);
                    }
                }
                // 添加时间范围过滤
                String pushTimeFieldName =
                        (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                        .from(elasticSearchQueryCondition.getBeginTime())
                        .to(elasticSearchQueryCondition.getEndTime()));
                String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

                if (null != servType && ENHANCED_COLOR_PRINT.equals(servType)) {
                    // 调用ES查询数据
                    SearchRequestBuilder searchRequestBuilder =
                            client.prepareSearch(databaseName)
                                    .setTypes(elasticSearchQueryCondition.getTableName())
                                    .setQuery(boolQueryBuilder);

                    // 获取ES的响应
                    SearchResponse response = searchRequestBuilder.execute().actionGet();

                    // 如果响应不为空
                    if (null != response && null != response.getHits()) {
                        totalAmount = (int) response.getHits().getTotalHits();
                    }
                } else {
                    // 调用ES查询数据
                    SearchRequestBuilder searchRequestBuilder =
                            client.prepareSearch(databaseName)
                                    .setTypes(elasticSearchQueryCondition.getTableName())
                                    .setQuery(boolQueryBuilder)
                                    .addAggregation(AggregationBuilders.cardinality("deliverCount")
                                            .field((String) elasticSearchQueryCondition.getQueryCondition().get("groupByFieldName")))
                                    .setSize(1);
                    // 获取ES的响应
                    SearchResponse response = searchRequestBuilder.execute().actionGet();

                    // 如果响应不为空
                    if (null != response && null != response.getHits()) {
                        Cardinality userAgg = response.getAggregations().get("deliverCount");
                        totalAmount = (int) userAgg.getValue();
                    }
                }
            }
        } catch (Exception e) {
            log.error("searchByTimePeriodAndRuleIDGroupByPhone Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster searchByTimePeriodAndRuleIDGroupByPhone queryConditon is totalAmount {}",
                totalAmount);

        return totalAmount;
    }

    /**
     * 查询指定时间段内满足ContRuleID与Phone条件的总数
     *
     * @param elasticSearchQueryCondition 查询条件
     * @return 符合条件的数量
     */
    public Integer queryCountByTimeContRuleIDAndPhone(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster queryCountByTimeContRuleIDAndPhone queryConditon is {}",
                elasticSearchQueryCondition);
        // 默认没有数据
        Integer totalAmount = 0;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                BoolQueryBuilder shouldQueryBuilder1 = QueryBuilders.boolQuery();

                // 设置ruleID和设置主叫或别叫号码
                if (null != elasticSearchQueryCondition.getQueryCondition()) {
                    String ruleIDFieldName =
                            (String) elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName");
                    if (elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName) != null
                            && elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName).toString().length() > 0) {
                        String[] str = ((String) elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)).split(",");
                        for (String s : str) {
                            shouldQueryBuilder1.should(QueryBuilders.matchQuery(ruleIDFieldName, s));
                        }
                        boolQueryBuilder.must(shouldQueryBuilder1);
                    } else {
                        boolQueryBuilder.must(QueryBuilders.matchQuery(ruleIDFieldName,
                                elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)));
                    }


                    if (null != elasticSearchQueryCondition.getQueryCondition().get("phoneFieldName")) {
                        String phoneFieldName =
                                (String) elasticSearchQueryCondition.getQueryCondition().get("phoneFieldName");
                        boolQueryBuilder.must(QueryBuilders.matchQuery(phoneFieldName,
                                elasticSearchQueryCondition.getQueryCondition().get(phoneFieldName)));
                    }

                    // 设置ussd过滤
                    shouldQueryBuilder.should(QueryBuilders.matchQuery("noUssdSendResult", 0));
                    shouldQueryBuilder.should(QueryBuilders.matchQuery("ussdSendResult", 0));
                    boolQueryBuilder.must(shouldQueryBuilder);
                }

                // 添加时间范围过滤
                String pushTimeFieldName =
                        (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                        .format(elasticSearchQueryCondition.getDateFomat())
                        .gte(elasticSearchQueryCondition.getBeginTime())
                        .lte(elasticSearchQueryCondition.getEndTime()));

                // 调用ES查询数据
                String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

                SearchRequestBuilder searchRequestBuilder =
                        client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);

                // 获取ES的响应
                SearchResponse response = searchRequestBuilder.execute().actionGet();

                // 如果响应不为空
                if (null != response && null != response.getHits()) {
                    totalAmount = (int) response.getHits().getTotalHits();
                }
            }
        } catch (Exception e) {
            log.error("queryCountByTimeContRuleIDAndPhone Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster queryCountByTimeContRuleIDAndPhone queryConditon is totalAmount {}",
                totalAmount);

        return totalAmount;
    }

    /**
     * 查询指定时间段内满足ContRuleID与Phone条件的总数
     *
     * @param elasticSearchQueryCondition 查询条件
     * @return 符合条件的数量
     */
    public Integer queryCountByTimeContRuleIDAndPhoneByMonth(ElasticSearchQueryCondition elasticSearchQueryCondition) {
        log.debug(">>>>>begin ElasticSearchCluster queryCountByTimeContRuleIDAndPhoneByMonth queryConditon is {}",
                elasticSearchQueryCondition);
        // 默认没有数据
        Integer totalAmount = 0;

        try {
            // 如果查询条件不为空
            if (null != elasticSearchQueryCondition) {
                // 构造查询条件
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

                BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();

                // 设置ruleID和设置主叫或别叫号码
                if (null != elasticSearchQueryCondition.getQueryCondition()) {
                    if (null != elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName")) {
                        String ruleIDFieldName =
                                (String) elasticSearchQueryCondition.getQueryCondition().get("ruleIDFieldName");
                        boolQueryBuilder.must(QueryBuilders.matchQuery(ruleIDFieldName,
                                elasticSearchQueryCondition.getQueryCondition().get(ruleIDFieldName)));
                    }


                    if (null != elasticSearchQueryCondition.getQueryCondition().get("enterpriseIdFieldName")) {
                        String enterpriseIdFieldName =
                                (String) elasticSearchQueryCondition.getQueryCondition().get("enterpriseIdFieldName");
                        boolQueryBuilder.must(QueryBuilders.matchQuery(enterpriseIdFieldName,
                                elasticSearchQueryCondition.getQueryCondition().get(enterpriseIdFieldName)));
                    }

                    if (null != elasticSearchQueryCondition.getQueryCondition().get("ownerFieldName")) {
                        String ownerFieldName =
                                (String) elasticSearchQueryCondition.getQueryCondition().get("ownerFieldName");
                        boolQueryBuilder.must(QueryBuilders.matchQuery(ownerFieldName,
                                elasticSearchQueryCondition.getQueryCondition().get(ownerFieldName)));
                    }

                    if (null != elasticSearchQueryCondition.getQueryCondition().get("serviceIdFieldName")) {
                        String serviceIdFieldName =
                                (String) elasticSearchQueryCondition.getQueryCondition().get("serviceIdFieldName");
                        BoolQueryBuilder shouldQueryBuilderServiceId = QueryBuilders.boolQuery();
                        List<String> serviceIdList = (List<String>) elasticSearchQueryCondition.getQueryCondition().get(serviceIdFieldName);
                        if (null != serviceIdList && serviceIdList.size() > 1) {
                            Object phone = null;
                            if (null != elasticSearchQueryCondition.getQueryCondition().get("phoneFieldName")) {
                                String phoneFieldName =
                                        (String) elasticSearchQueryCondition.getQueryCondition().get("phoneFieldName");
                                phone = elasticSearchQueryCondition.getQueryCondition().get(phoneFieldName);
                            }
                            for (int i = 0; i < serviceIdList.size(); i++) {
                                BoolQueryBuilder shouldQueryBuilderphone = QueryBuilders.boolQuery();
                                if (i == 0) {
                                    shouldQueryBuilderphone.must(QueryBuilders.matchQuery("callingPhone", phone));

                                } else {
                                    shouldQueryBuilderphone.must(QueryBuilders.matchQuery("calledPhone", phone));
                                }
                                shouldQueryBuilderphone.must(QueryBuilders.matchQuery(serviceIdFieldName, serviceIdList.get(i)));
                                shouldQueryBuilderServiceId.should(shouldQueryBuilderphone);
                            }
                            boolQueryBuilder.must(shouldQueryBuilderServiceId);

                        } else if (null != serviceIdList && serviceIdList.size() == 1) {
                            if (null != elasticSearchQueryCondition.getQueryCondition().get("phoneFieldName")) {
                                String phoneFieldName =
                                        (String) elasticSearchQueryCondition.getQueryCondition().get("phoneFieldName");
                                boolQueryBuilder.must(QueryBuilders.matchQuery(phoneFieldName,
                                        elasticSearchQueryCondition.getQueryCondition().get(phoneFieldName)));
                            }
                            boolQueryBuilder.must(QueryBuilders.matchQuery(serviceIdFieldName, serviceIdList.get(0)));
                        }
                    }
                    boolQueryBuilder.must(QueryBuilders.matchQuery("isMonthByQuota", 1));
                    // 设置ussd过滤
                    shouldQueryBuilder.should(QueryBuilders.matchQuery("noUssdSendResult", 0));
                    shouldQueryBuilder.should(QueryBuilders.matchQuery("ussdSendResult", 0));
                    boolQueryBuilder.must(shouldQueryBuilder);
                }

                // 添加时间范围过滤
                String pushTimeFieldName =
                        (String) elasticSearchQueryCondition.getQueryCondition().get("pushTimeFieldName");
                boolQueryBuilder.must(QueryBuilders.rangeQuery(pushTimeFieldName)
                        .format(elasticSearchQueryCondition.getDateFomat())
                        .gte(elasticSearchQueryCondition.getBeginTime())
                        .lte(elasticSearchQueryCondition.getEndTime()));

                // 调用ES查询数据
                String timeString = getTimeString(pushTimeFieldName, elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
                String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

                SearchRequestBuilder searchRequestBuilder =
                        client.prepareSearch(databaseName)
                                .setTypes(elasticSearchQueryCondition.getTableName())
                                .setQuery(boolQueryBuilder);

                // 获取ES的响应
                SearchResponse response = searchRequestBuilder.execute().actionGet();

                // 如果响应不为空
                if (null != response && null != response.getHits()) {
                    totalAmount = (int) response.getHits().getTotalHits();
                }
            }
        } catch (Exception e) {
            log.error("queryCountByTimeContRuleIDAndPhoneByMonth Fail to query data from ES.The error is {}", e);
        }

        log.debug(">>>>>Exit ElasticSearchCluster queryCountByTimeContRuleIDAndPhoneByMonth queryConditon is totalAmount {}",
                totalAmount);

        return totalAmount;
    }

    /**
     * 新的分页ES查询信息,查询一组数据，处理一组数据
     *
     * @param elasticSearchQueryCondition 查询条件
     * @param clazz                       需要转换成的类
     * @param dealQuerySearchService      对每页数据处理的函数
     * @param dealExtInfo                 处理函数需要的扩展字段
     * <AUTHOR>
     */
    public <T> void serarchPageInfoAndDeal(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz,
                                           DealQuerySearchService dealQuerySearchService, Map<String, Object> dealExtInfo) {
        log.info(">>>>>SubProvincialDeliveryCdrService begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);

        try {
            // 定义需要返回的List
            List<T> objectList = null;

            // 定义列名查询条件
            QueryBuilder queryBuilder = null;
            // 如果是模糊查询
            if (elasticSearchQueryCondition.isFuzzy()) {
                queryBuilder = QueryBuilders.wildcardQuery(elasticSearchQueryCondition.getColumnName(),
                        "*" + elasticSearchQueryCondition.getColumValue() + "*");
            } else {

                queryBuilder = QueryBuilders.termQuery(elasticSearchQueryCondition.getColumnName(),
                        elasticSearchQueryCondition.getColumValue());
            }

            // 构造查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

            // 添加时间范围和条件查询
            boolQueryBuilder
                    .must(QueryBuilders.rangeQuery(elasticSearchQueryCondition.getRangeColumnName())
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()))
                    .must(queryBuilder);
            String timeString = getTimeString(elasticSearchQueryCondition.getRangeColumnName(),
                    elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
            String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(elasticSearchQueryCondition.getTableName())
                    .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(elasticSearchQueryCondition.getPageSize())
                    .get();
            log.info(">>>>>SubProvincialDeliveryCdrService begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}, total {}",
                    elasticSearchQueryCondition, null != scrollResp ? scrollResp.getHits().getTotalHits() : null);
            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("SubProvincialDeliveryCdrService no query need to deal data from ES");

                return;
            }

            // 初始化es数据
            objectList = new ArrayList<T>();

            // 循环分页处理
            do {
                for (SearchHit searchHit : scrollResp.getHits()) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    // 如果查询出来的ES数量等于50000条，输出话单
                    if (dealCdrNumber == objectList.size()) {
                        // 处理分页查询的业务逻辑
                        dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);

                        // 处理完清空
                        objectList.clear();
                    }
                }

                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
            } while (scrollResp.getHits().getHits().length != 0);

            // 不足50000条也要生成话单
            if (CollectionUtils.isNotEmpty(objectList)) {
                dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
            }
        } catch (Exception e) {
            log.error("serarchPageInfoAndDeal Fail to query data from ES.The error is {}", e);
        }
        log.info(">>>>>SubProvincialDeliveryCdrService Exit ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);
    }

    /**
     * 查询热线生成话单
     *
     * @param elasticSearchQueryCondition 查询条件
     * @param clazz                       需要转换成的类
     * @param dealQuerySearchService      对每页数据处理的函数
     * @param dealExtInfo                 处理函数需要的扩展字段
     * <AUTHOR>
     */
    public <T> void serarhotlineInfoAndDeal(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz,
                                            DealQuerySearchService dealQuerySearchService, Map<String, Object> dealExtInfo) {
        log.info(">>>>>subsidiaryHotlineDeliveryService begin ElasticSearchCluster serarhotlineInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);

        try {
            // 定义需要返回的List
            List<T> objectList = null;

            // 定义列名查询条件
//        QueryBuilder queryBuilder = null;
            // 如果是模糊查询
//        if (elasticSearchQueryCondition.isFuzzy()) {
//            queryBuilder = QueryBuilders.wildcardQuery(elasticSearchQueryCondition.getColumnName(),
//                "*" + elasticSearchQueryCondition.getColumValue() + "*");
//        } else {
//            queryBuilder = QueryBuilders.termQuery(elasticSearchQueryCondition.getColumnName(),
//                elasticSearchQueryCondition.getColumValue());
//        }
            // 构造查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            QueryBuilder servitype = QueryBuilders.termQuery("serviceType", 2);
            QueryBuilder enterprise = QueryBuilders.termQuery("enterpriseType", 3);

            // 添加时间范围和条件查询
            boolQueryBuilder
                    .must(QueryBuilders.rangeQuery(elasticSearchQueryCondition.getRangeColumnName())
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()).includeLower(true))
                    .must(servitype).must(enterprise);
            //112版本查询ES热线投递清单时，增加过滤条件：业务子类型不为11（AXB主被叫），设置完过滤条件删除subServiceType字段
            if (dealExtInfo != null && dealExtInfo.get("subServiceType") != null
                    && dealExtInfo.get("subServiceType").equals("11")) {
                boolQueryBuilder.mustNot(QueryBuilders.termQuery("subServiceType",
                        11));
                dealExtInfo.remove("subServiceType");
            }
            String timeString = getTimeString(elasticSearchQueryCondition.getRangeColumnName(),
                    elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
            String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(elasticSearchQueryCondition.getTableName())
                    .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(elasticSearchQueryCondition.getPageSize())
                    .get();
            log.info(">>>>>subsidiaryHotlineDeliveryService begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}, total {}",
                    elasticSearchQueryCondition, null != scrollResp ? scrollResp.getHits().getTotalHits() : null);
            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("subsidiaryHotlineDeliveryService no query need to deal data from ES");

                return;
            }

            // 初始化es数据
            objectList = new ArrayList<T>();

            dealExtInfo.put("subsideCdrNumber", subsideCdrNumber);
            // 循环分页处理
            do {
                for (SearchHit searchHit : scrollResp.getHits()) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    // 如果查询出来的ES数量等于50000条，输出话单
                    if (subsideCdrNumber == objectList.size()) {
                        // 处理分页查询的业务逻辑
                        dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);

                        // 处理完清空
                        objectList.clear();
                    }
                }

                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
            } while (scrollResp.getHits().getHits().length != 0);

            // 不足50000条也要生成话单
            if (CollectionUtils.isNotEmpty(objectList)) {
                dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
            }
        } catch (Exception e) {
            log.error("serarhotlineInfoAndDeal Fail to query data from ES.The error is {}", e);
        }

        log.info(">>>>>subsidiaryHotlineDeliveryService Exit ElasticSearchCluster serarhotlineInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);
    }


    /**
     * 查询广告生成话单
     *
     * @param elasticSearchQueryCondition 查询条件
     * @param clazz                       需要转换成的类
     * @param dealQuerySearchService      对每页数据处理的函数
     * @param dealExtInfo                 处理函数需要的扩展字段
     * <AUTHOR>
     */
    public <T> void serarCardInfoAndDeal(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz,
                                         DealQuerySearchService dealQuerySearchService, Map<String, Object> dealExtInfo) {
        log.info(">>>>>cardandadvert DeliveryTask begin ElasticSearchCluster serarCardInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);

        try {
            // 定义需要返回的List
            List<T> objectList = null;

            // 构造查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

            // 添加时间范围和条件查询
            boolQueryBuilder
                    .must(QueryBuilders.rangeQuery(elasticSearchQueryCondition.getRangeColumnName())
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
            String timeString = getTimeString(elasticSearchQueryCondition.getRangeColumnName(),
                    elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
            String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(elasticSearchQueryCondition.getTableName())
                    .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(elasticSearchQueryCondition.getPageSize())
                    .get();
            log.info(">>>>>cardandadvert DeliveryTask begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}, total {}",
                    elasticSearchQueryCondition, null != scrollResp ? scrollResp.getHits().getTotalHits() : null);
            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("cardandadvert DeliveryTask no query need to deal data from ES");

                return;
            }

            // 初始化es数据
            objectList = new ArrayList<T>();
            int count = 0;
            dealExtInfo.put("isFinish", Boolean.valueOf(false));
            dealExtInfo.put("dealNumber", dealadvertCdrNumber);
            dealExtInfo.put("isBegin", Boolean.valueOf(false));

            // 循环分页处理
            do {
                for (SearchHit searchHit : scrollResp.getHits()) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    // 如果查询出来的ES数量等于50000条，输出话单
                    if (dealadvertCdrNumber == objectList.size()) {
                        if (0 == count) {
                            dealExtInfo.put("isBegin", Boolean.valueOf(true));
                        }
                        count++;
                        if (count == (totalAmount / dealadvertCdrNumber.intValue() + 1)) {
                            dealExtInfo.put("isFinish", Boolean.valueOf(true));
                        }
                        // 处理分页查询的业务逻辑
                        dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);

                        // 处理完清空
                        objectList.clear();
                    }
                }

                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
            } while (scrollResp.getHits().getHits().length != 0);

            // 不足50000条也要生成话单
            if (CollectionUtils.isNotEmpty(objectList)) {
                if (0 == count) {
                    dealExtInfo.put("isBegin", Boolean.valueOf(true));
                }
                count++;
                if (count == (totalAmount / dealadvertCdrNumber.intValue() + 1)) {
                    dealExtInfo.put("isFinish", Boolean.valueOf(true));
                }
                dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
            }
        } catch (Exception e) {
            log.error("serarCardInfoAndDeal Fail to query data from ES.The error is {}", e);
        }

        log.info(">>>>>cardandadvert DeliveryTask Exit ElasticSearchCluster serarCardInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);
    }


    /**
     * 查询名片生成话单
     *
     * @param elasticSearchQueryCondition 查询条件
     * @param clazz                       需要转换成的类
     * @param dealQuerySearchService      对每页数据处理的函数
     * @param dealExtInfo                 处理函数需要的扩展字段
     * <AUTHOR>
     */
    public <T> void serarCardInfoDeal(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz,
                                      DealQuerySearchService dealQuerySearchService, Map<String, Object> dealExtInfo) {
        log.info(">>>>>cardandadvert DeliveryTask begin ElasticSearchCluster serarCardInfoDeal queryConditon is {}",
                elasticSearchQueryCondition);

        // 定义需要返回的List
        List<T> objectList = null;

        try {
            // 构造查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

            if (dealExtInfo.get("subsidiaryMap") != null) {
                Map<Integer, Enterprise> subsidiaryMap = (Map<Integer, Enterprise>) dealExtInfo.get("subsidiaryMap");
                BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                for (Integer enterpriseID : subsidiaryMap.keySet()) {
                    shouldQueryBuilder.should((QueryBuilders.matchQuery("enterpriseID", String.valueOf(enterpriseID))));
                }
                boolQueryBuilder.must(shouldQueryBuilder);
            }

            // 添加时间范围和条件查询
            boolQueryBuilder
                    .must(QueryBuilders.rangeQuery(elasticSearchQueryCondition.getRangeColumnName())
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
            String timeString = getTimeString(elasticSearchQueryCondition.getRangeColumnName(),
                    elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
            String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);
            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(elasticSearchQueryCondition.getTableName())
                    .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(elasticSearchQueryCondition.getPageSize())
                    .get();
            log.info(">>>>>cardandadvert DeliveryTask begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}, total {}",
                    elasticSearchQueryCondition, null != scrollResp ? scrollResp.getHits().getTotalHits() : null);
            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("cardandadvert DeliveryTask no query need to deal data from ES");

                return;
            }

            // 初始化es数据
            objectList = new ArrayList<T>();
            int count = 0;
            dealExtInfo.put("isFinish", Boolean.valueOf(false));
            dealExtInfo.put("isBegin", Boolean.valueOf(false));

            // 循环分页处理
            do {
                for (SearchHit searchHit : scrollResp.getHits()) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    // 如果查询出来的ES数量等于50000条，输出话单
                    if (dealadvertCdrNumber == objectList.size()) {
                        if (0 == count) {
                            dealExtInfo.put("isBegin", Boolean.valueOf(true));
                        }
                        count++;
                        if (count == (totalAmount / dealadvertCdrNumber.intValue() + 1)) {
                            dealExtInfo.put("isFinish", Boolean.valueOf(true));
                        }
                        // 处理分页查询的业务逻辑
                        dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);

                        // 处理完清空
                        objectList.clear();
                    }
                }

                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
            } while (scrollResp.getHits().getHits().length != 0);

            // 不足50000条也要生成话单
            if (CollectionUtils.isNotEmpty(objectList)) {
                if (0 == count) {
                    dealExtInfo.put("isBegin", Boolean.valueOf(true));
                }
                count++;
                if (count == (totalAmount / dealadvertCdrNumber.intValue() + 1)) {
                    dealExtInfo.put("isFinish", Boolean.valueOf(true));
                }
                dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
            }
        } catch (Exception e) {
            log.error("serarCardInfoDeal Fail to query data from ES.The error is {}", e);
        }

        log.info(">>>>>cardandadvert DeliveryTask Exit ElasticSearchCluster serarCardInfoDeal queryConditon is {}",
                elasticSearchQueryCondition);
    }


    /**
     * 查询名片生成话单
     *
     * @param elasticSearchQueryCondition 查询条件
     * @param clazz                       需要转换成的类
     * @param dealQuerySearchService      对每页数据处理的函数
     * @param dealExtInfo                 处理函数需要的扩展字段
     * <AUTHOR>
     */
    public <T> void serarRemindDeal(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz,
                                    DealQuerySearchService dealQuerySearchService, Map<String, Object> dealExtInfo) {
        log.info(">>>>>cardandadvert DeliveryTask begin ElasticSearchCluster serarRemindDeal queryConditon is {}",
                elasticSearchQueryCondition);

        // 定义需要返回的List
        List<T> objectList = null;

        try {
            // 构造查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

            if (dealExtInfo.get("subsidiaryMap") != null) {
                Map<Integer, Enterprise> subsidiaryMap = (Map<Integer, Enterprise>) dealExtInfo.get("subsidiaryMap");
                BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                for (Integer enterpriseID : subsidiaryMap.keySet()) {
                    shouldQueryBuilder.should((QueryBuilders.matchQuery("enterpriseID", String.valueOf(enterpriseID))));
                }
                boolQueryBuilder.must(shouldQueryBuilder);
            }

            BoolQueryBuilder shouldQueryBuilderServiceId = QueryBuilders.boolQuery();
            shouldQueryBuilderServiceId.should((QueryBuilders.matchQuery("serviceID", "01160")));
            shouldQueryBuilderServiceId.should((QueryBuilders.matchQuery("serviceID", "01161")));
            shouldQueryBuilderServiceId.should((QueryBuilders.matchQuery("serviceID", "01162")));

            boolQueryBuilder.must(shouldQueryBuilderServiceId);

            // 添加时间范围和条件查询
            boolQueryBuilder
                    .must(QueryBuilders.rangeQuery(elasticSearchQueryCondition.getRangeColumnName())
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
            String timeString = getTimeString(elasticSearchQueryCondition.getRangeColumnName(),
                    elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
            String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(elasticSearchQueryCondition.getTableName())
                    .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(elasticSearchQueryCondition.getPageSize())
                    .get();
            log.info(">>>>>cardandadvert DeliveryTask begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}, total {}",
                    elasticSearchQueryCondition, null != scrollResp ? scrollResp.getHits().getTotalHits() : null);
            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("cardandadvert DeliveryTask no query need to deal data from ES");

                return;
            }

            // 初始化es数据
            objectList = new ArrayList<T>();
            int count = 0;
            dealExtInfo.put("isFinish", Boolean.valueOf(false));
            dealExtInfo.put("isBegin", Boolean.valueOf(false));

            // 循环分页处理
            do {
                for (SearchHit searchHit : scrollResp.getHits()) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    // 如果查询出来的ES数量等于50000条，输出话单
                    if (dealadvertCdrNumber == objectList.size()) {
                        if (0 == count) {
                            dealExtInfo.put("isBegin", Boolean.valueOf(true));
                        }
                        count++;
                        if (count == (totalAmount / dealadvertCdrNumber.intValue() + 1)) {
                            dealExtInfo.put("isFinish", Boolean.valueOf(true));
                        }
                        // 处理分页查询的业务逻辑
                        dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);

                        // 处理完清空
                        objectList.clear();
                    }
                }

                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
            } while (scrollResp.getHits().getHits().length != 0);

            // 不足50000条也要生成话单
            if (CollectionUtils.isNotEmpty(objectList)) {
                if (0 == count) {
                    dealExtInfo.put("isBegin", Boolean.valueOf(true));
                }
                count++;
                if (count == (totalAmount / dealadvertCdrNumber.intValue() + 1)) {
                    dealExtInfo.put("isFinish", Boolean.valueOf(true));
                }
                dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
            }
        } catch (Exception e) {
            log.error("serarRemindDeal Fail to query data from ES.The error is {}", e);
        }

        log.info(">>>>>cardandadvert DeliveryTask Exit ElasticSearchCluster serarRemindDeal queryConditon is {}",
                elasticSearchQueryCondition);
    }

    /**
     * 查询企业通知生成话单
     *
     * @param elasticSearchQueryCondition 查询条件
     * @param clazz                       需要转换成的类
     * @param dealQuerySearchService      对每页数据处理的函数
     * @param dealExtInfo                 处理函数需要的扩展字段
     * <AUTHOR>
     */
    public <T> void serarNoticeInfoAndDeal(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz,
                                           DealQuerySearchService dealQuerySearchService, Map<String, Object> dealExtInfo) {
        log.info(">>>>>subsidiaryNoticeDeliveryService begin ElasticSearchCluster serarNoticeInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);

        // 定义需要返回的List
        List<T> objectList = null;

        try {
            // 构造查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            QueryBuilder servitype = QueryBuilders.termQuery("serviceType", 4);
            QueryBuilder enterprise = QueryBuilders.termQuery("enterpriseType", 3);

            // 添加时间范围和条件查询
            boolQueryBuilder
                    .must(QueryBuilders.rangeQuery(elasticSearchQueryCondition.getRangeColumnName())
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()))
                    .must(servitype).must(enterprise);
            String timeString = getTimeString(elasticSearchQueryCondition.getRangeColumnName(),
                    elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
            String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(elasticSearchQueryCondition.getTableName())
                    .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(elasticSearchQueryCondition.getPageSize())
                    .get();
            log.info(">>>>>subsidiaryNoticeDeliveryService begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}, total {}",
                    elasticSearchQueryCondition, null != scrollResp ? scrollResp.getHits().getTotalHits() : null);
            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("subsidiaryNoticeDeliveryService no query need to deal data from ES");

                return;
            }

            // 初始化es数据
            objectList = new ArrayList<T>();
            dealExtInfo.put("subsideCdrNumber", subsideCdrNumber);

            // 循环分页处理
            do {
                for (SearchHit searchHit : scrollResp.getHits()) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    // 如果查询出来的ES数量等于50000条，输出话单
                    if (subsideCdrNumber == objectList.size()) {
                        // 处理分页查询的业务逻辑
                        dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);

                        // 处理完清空
                        objectList.clear();
                    }
                }

                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
            } while (scrollResp.getHits().getHits().length != 0);

            // 不足50000条也要生成话单
            if (CollectionUtils.isNotEmpty(objectList)) {
                dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
            }
        } catch (Exception e) {
            log.error("serarNoticeInfoAndDeal Fail to query data from ES.The error is {}", e);
        }

        log.info(">>>>>subsidiaryNoticeDeliveryService Exit ElasticSearchCluster serarNoticeInfoAndDeal queryConditon is {}",
                elasticSearchQueryCondition);
    }

    /**
     * 新的分页ES查询信息,查询一组数据，处理一组数据
     *
     * @param elasticSearchQueryCondition 查询条件
     * @param clazz                       需要转换成的类
     * @param dealQuerySearchService      对每页数据处理的函数
     * @param dealExtInfo                 处理函数需要的扩展字段
     * <AUTHOR>
     */
    public <T> void serarchPageInfoAndDealHotline(ElasticSearchQueryCondition elasticSearchQueryCondition, Class<T> clazz,
                                                  DealQuerySearchService dealQuerySearchService, Map<String, Object> dealExtInfo, List<QueryBuilder> shoulds) {
        log.info(">>>>>SubProvincialDeliveryCdrService begin ElasticSearchCluster serarchPageInfoAndDealHotline queryConditon is {}",
                elasticSearchQueryCondition);

        // 定义需要返回的List
        List<T> objectList = null;

        try {
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(null, shoulds);
            // 添加时间范围和条件查询
            boolQueryBuilder
                    .must(QueryBuilders.rangeQuery(elasticSearchQueryCondition.getRangeColumnName())
                            .from(elasticSearchQueryCondition.getBeginTime())
                            .to(elasticSearchQueryCondition.getEndTime()));
            String timeString = getTimeString(elasticSearchQueryCondition.getRangeColumnName(),
                    elasticSearchQueryCondition.getBeginTime(), elasticSearchQueryCondition.getEndTime());
            String databaseName = esUtils.getDatabaseName(elasticSearchQueryCondition.getDatabaseName(), timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(elasticSearchQueryCondition.getTableName())
                    .addSort(FieldSortBuilder.DOC_FIELD_NAME, SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(elasticSearchQueryCondition.getPageSize())
                    .get();
            log.info(">>>>>SubProvincialDeliveryCdrService begin ElasticSearchCluster serarchPageInfoAndDeal queryConditon is {}, total {}",
                    elasticSearchQueryCondition, null != scrollResp ? scrollResp.getHits().getTotalHits() : null);
            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("SubProvincialDeliveryCdrService no query need to deal data from ES");

                return;
            }

            // 初始化es数据
            objectList = new ArrayList<T>();
            int count = 0;
            dealExtInfo.put("isFinish", Boolean.valueOf(false));
            // 循环分页处理
            do {
                for (SearchHit searchHit : scrollResp.getHits()) {
                    objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));

                    // 如果查询出来的ES数量等于100000条，输出话单
                    if (dealCdrNumberHotline == objectList.size()) {
                        count++;
                        // 处理分页查询的业务逻辑
                        long currentTimeMillis = System.currentTimeMillis();
                        if (count == (totalAmount / dealCdrNumberHotline.intValue() + 1)) {
                            dealExtInfo.put("isFinish", Boolean.valueOf(true));
                        }
                        dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
                        log.info("SubProvincialDeliveryhotlineUploadTask dealQuerySearch totalTime =" + (System.currentTimeMillis() - currentTimeMillis));
                        // 处理完清空
                        objectList.clear();
                    }
                }

                long currentTimeMillis2 = System.currentTimeMillis();
                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
                log.info("SubProvincialDeliveryhotlineUploadTask getESData totalTime =" + (System.currentTimeMillis() - currentTimeMillis2));
            } while (scrollResp.getHits().getHits().length != 0);

            // 不足100000条也要生成话单
            if (CollectionUtils.isNotEmpty(objectList)) {
                count++;
                long currentTimeMillis = System.currentTimeMillis();
                if (count == (totalAmount / dealCdrNumberHotline.intValue() + 1)) {
                    dealExtInfo.put("isFinish", Boolean.valueOf(true));
                }
                dealQuerySearchService.executeQuerySearchByInfo(objectList, dealExtInfo);
            }
        } catch (Exception e) {
            log.error("serarchPageInfoAndDealHotline Fail to query data from ES.The error is {}", e);
        }

        log.info(">>>>>SubProvincialDeliveryCdrService Exit ElasticSearchCluster serarchPageInfoAndDealHotline queryConditon is {}",
                elasticSearchQueryCondition);
    }


    /**
     * 排序条件查询，不分页. <功能详细描述>
     *
     * @param esInfo
     * @param sortCond
     * @param clazz
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    public <T> List<T> queryFromES(ElasticSearchInfo esInfo, List<QueryBuilder> musts, List<QueryBuilder> shoulds,
                                   OrderCondES sortCond, Class<T> clazz) {
        if (log.isDebugEnabled()) {
            log.debug(">>>>>Begin to query from ES with order.The sort condition is {}", sortCond);
        }
        BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(musts, shoulds);
        SearchRequestBuilder searchRequestBuilder = null;
        String timeString = getTimeString(musts);
        String databaseName = esUtils.getDatabaseName(esInfo.getDatabaseName(), timeString, true);

        // 不分页但是按照某个字段排序
        searchRequestBuilder = client.prepareSearch(databaseName)
                .setTypes(esInfo.getTableName())
                .setQuery(boolQueryBuilder)
                .addSort(sortCond.getSortField(), sortCond.getOrder());

        return buildResult(searchRequestBuilder, clazz);
    }

    private String getTimeString(List<QueryBuilder> musts) {
        String timeString = null;
        for (QueryBuilder must : musts) {
            if (StringUtils.isNotEmpty(must.toString())) {
                if (must.toString().contains("colorPushTime")) {
                    break;
                }
                if (must.toString().contains("@timestamp")) {
                    String fromString = (String) ((RangeQueryBuilder) must).from();
                    String toString = (String) ((RangeQueryBuilder) must).to();
                    Date from = DateUtil.convertUTCtoLocalDate(fromString, DateUtil.DatePattern.yyyyMMddHHmmssSSSZ);
                    Date to = DateUtil.convertUTCtoLocalDate(toString, DateUtil.DatePattern.yyyyMMddHHmmssSSSZ);
                    if (DateUtil.format(from, DateUtil.DatePattern.YYYYMM_01).equals(
                            DateUtil.format(to, DateUtil.DatePattern.YYYYMM_01))) {
                        timeString = DateUtil.format(from, DateUtil.DatePattern.YYYYMM_01);
                    }
                    break;
                }

                if (must.toString().contains("effictiveTime")) {
                    timeString = ((WildcardQueryBuilder) must).value();
                }
            }
        }
        return timeString;
    }

    private String getTimeString(String timeQuery, String fromString, String toString) {
        String timeString = null;
        if (timeQuery.contains("@timestamp")) {

            Date from = DateUtil.convertUTCtoLocalDate(fromString, DateUtil.DatePattern.yyyyMMddHHmmssSSSZ);
            Date to = DateUtil.convertUTCtoLocalDate(toString, DateUtil.DatePattern.yyyyMMddHHmmssSSSZ);
            if (DateUtil.format(from, DateUtil.DatePattern.YYYYMM_01).equals(
                    DateUtil.format(to, DateUtil.DatePattern.YYYYMM_01))) {
                timeString = DateUtil.format(from, DateUtil.DatePattern.YYYYMM_01);
            }
        }
        return timeString;
    }

    public List<ProviceMemberElasticsearchContent> queryProviceMemberElasticsearch(String databseName,
                                                                                   String tableName,
                                                                                   Integer enterpriseID,
                                                                                   List<QueryBuilder> shoulds,
                                                                                   String effictiveTime) {
        List<ProviceMemberElasticsearchContent> objectList = new ArrayList<>();

        try {
            BoolQueryBuilder countryQuery = new BoolQueryBuilder();
            countryQuery.must(QueryBuilders.matchQuery("enterpriseID", enterpriseID));
            if (shoulds.size() > 0) {
                countryQuery.must(QueryBuilders.matchQuery("isRefContent", 1));
            }
            countryQuery.must(QueryBuilders.wildcardQuery("effictiveTime", effictiveTime));

            BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
            if (shoulds.size() > 0) {
                for (QueryBuilder q : shoulds) {
                    queryBuilder.should(q);
                }
                countryQuery.must(queryBuilder);
            }
            String databaseName = esUtils.getDatabaseName(databseName, null, true);
            SearchResponse searchResponse = client
                    .prepareSearch(databaseName)
                    .setTypes(tableName)
                    .setQuery(countryQuery)//查询用户ID，获取到该用户的对应文档
                    .setSearchType(SearchType.QUERY_THEN_FETCH)
                    .setSize(100000)
                    .execute()
                    .actionGet();

            SearchHits hits = searchResponse.getHits();
            if (hits != null && hits.getHits() != null && hits.getHits().length > 0) {
                SearchHit hit = hits.getHits()[0];
                String indexId = hit.getId();
                objectList = esToClass(searchResponse, ProviceMemberElasticsearchContent.class);

            }
        } catch (Exception e) {
            log.error("queryProviceMemberElasticsearch Fail to query data from ES.The error is {}", e);
        }
        return objectList;
    }


    public List<ProviceMemberElasticsearchContent> queryProviceMemberElasticsearchByBusinessID(String databseName,
                                                                                               String tableName,
                                                                                               String businessID,
                                                                                               String effictiveTime) {
        List<ProviceMemberElasticsearchContent> objectList = new ArrayList<>();

        try {
            String databaseName = esUtils.getDatabaseName(databseName, null, true);
            BoolQueryBuilder countryQuery = new BoolQueryBuilder();
            countryQuery.must(QueryBuilders.matchQuery("businessID", businessID));
            countryQuery.must(QueryBuilders.wildcardQuery("effictiveTime", effictiveTime));
            SearchResponse searchResponse = client
                    .prepareSearch(databaseName)
                    .setTypes(tableName)
                    .setQuery(countryQuery)//查询用户ID，获取到该用户的对应文档
                    .setSearchType(SearchType.QUERY_THEN_FETCH)
                    .execute()
                    .actionGet();
            SearchHits hits = searchResponse.getHits();
            if (hits != null && hits.getHits() != null && hits.getHits().length > 0) {
                objectList = esToClass(searchResponse, ProviceMemberElasticsearchContent.class);
            }
        } catch (Exception e) {
            log.error("queryProviceMemberElasticsearchByBusinessID Fail to query data from ES.The error is {}", e);
        }
        return objectList;
    }


    public Integer updateES(String databseName, String tableName, String businessID, Map<String, Object> map, String date) throws Exception {

        String databaseName = esUtils.getDatabaseName(databseName, null, true);
        BoolQueryBuilder countryQuery = new BoolQueryBuilder();
        countryQuery.must(QueryBuilders.matchQuery("businessID", businessID));
        countryQuery.must(QueryBuilders.wildcardQuery("effictiveTime", date));
        SearchResponse searchResponse = client
                .prepareSearch(databaseName)
                .setTypes(tableName)
                .setQuery(countryQuery)//查询用户ID，获取到该用户的对应文档
                .setSearchType(SearchType.QUERY_THEN_FETCH)
                .execute()
                .actionGet();

        SearchHits hits = searchResponse.getHits();
        if (hits != null && hits.getHits() != null && hits.getHits().length > 0) {
            SearchHit hit = hits.getHits()[0];
            String indexId = hit.getId();
            String index = hit.getIndex();

            List<ProviceMemberElasticsearchContent> objectList = esToClass(searchResponse, ProviceMemberElasticsearchContent.class);

            //212版本，REQ-522_企管平台大数据成员清单新增模板信息同步需求增加contentIDs
//            //原内容审核状态为审核通过则不更新
//            for(ProviceMemberElasticsearchContent p:objectList){
//                if("1".equals(p.getContentApproveStatus())){
//                    return 1;
//                }
//            }
            //更新es
            UpdateRequest updateRequest = new UpdateRequest(index, tableName, indexId);
            XContentBuilder xContentBuilder = XContentFactory.jsonBuilder();
            xContentBuilder.startObject();
            for (String key : map.keySet()) {
                xContentBuilder.field(key, map.get(key));
            }
            xContentBuilder.endObject();
            updateRequest.doc(xContentBuilder);
            client.update(updateRequest).get();
            return hits.getHits().length;
        }
        return 0;
    }


    /**
     * 分页，排序条件查询. <功能详细描述>
     *
     * @param esInfo
     * @param pageParamES
     * @param sortCond
     * @param clazz
     * @return
     * <AUTHOR>
     * @see [类、类#方法、类#成员]
     */
    public <T> List<T> queryFromES(ElasticSearchInfo esInfo, List<QueryBuilder> musts, List<QueryBuilder> shoulds,
                                   PageParamES pageParamES, OrderCondES sortCond, Class<T> clazz) {
        BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(musts, shoulds);
        SearchRequestBuilder searchRequestBuilder = null;
        // 分页并且按某字段排序
        if (null != pageParamES && null != sortCond) {
            String timeString = getTimeString(musts);
            String databaseName = esUtils.getDatabaseName(esInfo.getDatabaseName(), timeString, true);
            searchRequestBuilder = client.prepareSearch(databaseName)
                    .setTypes(esInfo.getTableName())
                    .setQuery(boolQueryBuilder)
                    .setFrom(pageParamES.getStartNum())
                    .setSize(pageParamES.getPageSize())
                    .addSort(SortBuilders.fieldSort(sortCond.getSortField()).order(sortCond.getOrder()));

        }

        return buildResult(searchRequestBuilder, clazz);
    }

    /**
     * 不分页，按单一字段排序查询全部 <功能详细描述>
     *
     * @param <T>
     * @return
     * @see [类、类#方法、类#成员]
     */
    public <T> void queryFromESBABillNotReturn(QueryCondition queryCond, Class<T> clazz,
                                               DealQuerySearchService dealQuerySearchService) {
        try {
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
            String timeString = getTimeString(queryCond.getMusts());
            String databaseName = esUtils.getDatabaseName("delivery_detail", timeString, true);
            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes("delivery_record")
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(queryCond.getPageSize())
                    .get();


            Long totalAmount = 0L;
            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }
            List<T> objectList;
            if (0 == totalAmount) {
                log.info("no query need to deal data from ES");
                objectList = new ArrayList<>();
                try {
                    objectList.add(clazz.newInstance());
                } catch (InstantiationException | IllegalAccessException e) {
                    e.printStackTrace();
                }
            } else {
                objectList = esToClass(scrollResp, clazz);
            }

            do {

                // 查出来一波，写一波话单
                dealQuerySearchService.executeQuerySearchByInfo(objectList, queryCond.getExtMap());
                long startTime = System.currentTimeMillis();
                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
                startTime = System.currentTimeMillis();
                objectList = esToClass(scrollResp, clazz);
            } while (scrollResp.getHits().getHits().length != 0);

            // 把结束开关置true，关闭最后一个文件
            Map<String, Object> extMap = queryCond.getExtMap();
            extMap.put("isFinish", Boolean.TRUE);

            // 最后数据没了之后，把最后一个文件流关闭.
            dealQuerySearchService.executeQuerySearchByInfo(null, extMap);
        } catch (Exception e) {
            log.error("queryFromESBABillNotReturn Fail to query data from ES.The error is {}", e);
        }
    }

    /**
     * 不分页，按单一字段排序查询全部 <功能详细描述>
     *
     * @param <T>
     * @return
     * @see [类、类#方法、类#成员]
     */
    public <T> void queryFromESBABill(QueryCondition queryCond, Class<T> clazz,
                                      DealQuerySearchService dealQuerySearchService) {
        try {
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
            String timeString = getTimeString(queryCond.getMusts());
            String databaseName = esUtils.getDatabaseName("delivery_detail", timeString, true);
            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes("delivery_record")
                    .setQuery(boolQueryBuilder)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    // 本地测试时会查询ES报错,需要注释排序
                    .addSort(queryCond.getSortCond().getSortField(), queryCond.getSortCond().getOrder())
                    .setSize(queryCond.getPageSize())
                    .get();

            Long totalAmount = 0L;
            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("no query need to deal data from ES");
                Map<String, Object> extMap = queryCond.getExtMap();
                extMap.put("isFinish", Boolean.TRUE);
                dealQuerySearchService.executeQuerySearchByInfo(null, extMap);
                return;
            }

            List<T> objectList = esToClass(scrollResp, clazz);
            do {

                // 查出来一波，写一波话单
                dealQuerySearchService.executeQuerySearchByInfo(objectList, queryCond.getExtMap());

                // 然后接着滚动查询
                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();

                objectList = esToClass(scrollResp, clazz);
            } while (scrollResp.getHits().getHits().length != 0);

            // 把结束开关置true，关闭最后一个文件
            Map<String, Object> extMap = queryCond.getExtMap();
            extMap.put("isFinish", Boolean.TRUE);

            // 最后数据没了之后，把最后一个文件流关闭.
            dealQuerySearchService.executeQuerySearchByInfo(null, extMap);
        } catch (Exception e) {
            log.error("queryFromESBABill Fail to query data from ES.The error is {}", e);
        }
    }

    /**
     * 滚动查询投递明细并写入文件 <功能详细描述>
     *
     * @param <T>
     * @param clazz
     * @return
     * @see [类、类#方法、类#成员]
     */
    public <T> Boolean scollQueryFromESAndWrite(List<QueryCondition> queryCondList, Class<T> clazz,
                                                DealExportDeliveryService exportDeliveryHelper) {
        boolean result = true;

        if (null != queryCondList && queryCondList.size() > 0) {
            for (QueryCondition queryCond : queryCondList) {
                log.debug("start scrolling query delivery details...");
                try {
                    BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
                    String timeString = getTimeString(queryCond.getMusts());
                    String databaseName = esUtils.getDatabaseName(queryCond.getEsInfo().getDatabaseName(), timeString, true);
                    SearchResponse scrollResp = client.prepareSearch(databaseName)
                            .setTypes(queryCond.getEsInfo().getTableName())
                            .setQuery(boolQueryBuilder)
                            .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                            .addSort(queryCond.getSortCond().getSortField(), queryCond.getSortCond().getOrder())
                            .setSize(queryCond.getPageSize())
                            .get();
                    Long totalAmount = 0L;

                    // 如果查询不到数据，直接人return
                    if (null != scrollResp && null != scrollResp.getHits()) {
                        totalAmount = scrollResp.getHits().getTotalHits();
                    }

                    if (0 == totalAmount) {
                        log.info("no need to deal data from ES");
                        return true;
                    }

                    List<T> objectList = esToClass(scrollResp, clazz);
                    do {
                        // 查出来一波，写一波
                        result = exportDeliveryHelper.exportDeliveryDetail(objectList, queryCond.getExtMap());
                        // 写的时候出现异常直接返回
                        if (!result) {
                            return false;
                        }
                        // 然后接着滚动查询
                        scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                                .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                                .execute()
                                .actionGet();

                        objectList = esToClass(scrollResp, clazz);
                    } while (scrollResp.getHits().getHits().length != 0);
                } catch (Exception e) {
                    log.error("scollQueryFromESAndWrite Fail to query data from ES.The error is {}", e);
                }
            }
        }


        // 把结束开关置true，关闭最后一个文件
        Map<String, Object> extMap = new HashMap<String, Object>();
        extMap.put("isFinish", Boolean.valueOf(true));

        // 最后数据没了之后，把最后一个文件流关闭.
        result = exportDeliveryHelper.exportDeliveryDetail(null, extMap);
        if (!result) {
            return false;
        }
        log.debug("end of scrolling query delivery details.");
        return true;
    }

    /**
     * 构造一个响应结果 <功能详细描述>
     *
     * @param searchRequestBuilder
     * @param clazz
     * @return
     * @see [类、类#方法、类#成员]
     */
    private <T> List<T> buildResult(SearchRequestBuilder searchRequestBuilder, Class<T> clazz) {
        SearchResponse response = null;
        if (null != searchRequestBuilder) {

            try {
                // 执行查询动作.
                response = searchRequestBuilder.execute().actionGet();
            } catch (Exception e) {
                log.error("Fail to query data from ES.The error is {}", e);
            }

        }

        return esToClass(response, clazz);
    }

    /**
     * 把查出来的东西整成java 类 <功能详细描述>
     *
     * @param response
     * @param clazz
     * @return
     * @see [类、类#方法、类#成员]
     */
    private <T> List<T> esToClass(SearchResponse response, Class<T> clazz) {
        List<T> objectList = null;
        // 如果响应不为空，转成响应类
        if (null != response && null != response.getHits()) {
            objectList = new ArrayList<T>();

            for (SearchHit searchHit : response.getHits()) {
                objectList.add(JSONObject.parseObject(searchHit.getSourceAsString(), clazz));
            }
        }

        return objectList;
    }


    /**
     * 条件查询总数 <功能详细描述>
     *
     * @param esInfo
     * @param
     * @return
     * @see [类、类#方法、类#成员]
     */
    public Long queryTotalNum(ElasticSearchInfo esInfo, List<QueryBuilder> musts, List<QueryBuilder> shoulds) {
        try {
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(musts, shoulds);
            String timeString = getTimeString(musts);
            String databaseName = esUtils.getDatabaseName(esInfo.getDatabaseName(), timeString, true);
            SearchRequestBuilder searchRequestBuilder =
                    client.prepareSearch(databaseName).setTypes(esInfo.getTableName()).setQuery(boolQueryBuilder);

            SearchResponse response = searchRequestBuilder.execute().actionGet();

            // 如果响应不为空
            if (null != response && null != response.getHits()) {
                return response.getHits().getTotalHits() > 10000 ? 10000 : response.getHits().getTotalHits();
            }
        } catch (Exception e) {
            log.error("queryTotalNum Fail to query data from ES.The error is {}", e);
        }

        // 如果超过10000 那就返回10000条数据
        return 0L;
    }

    public Long queryTotalNumMax(ElasticSearchInfo esInfo, List<QueryBuilder> musts, List<QueryBuilder> shoulds) {
        try {
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(musts, shoulds);
            String timeString = getTimeString(musts);
            String databaseName = esUtils.getDatabaseName(esInfo.getDatabaseName(), timeString, true);
            SearchRequestBuilder searchRequestBuilder =
                    client.prepareSearch(databaseName).setTypes(esInfo.getTableName()).setQuery(boolQueryBuilder);

            SearchResponse response = searchRequestBuilder.execute().actionGet();

            // 如果响应不为空
            if (null != response && null != response.getHits()) {
                return response.getHits().getTotalHits();
            }
        } catch (Exception e) {
            log.error("queryTotalNumMax Fail to query data from ES.The error is {}", e);
        }

        // 如果超过10000 那就返回10000条数据
        return 0L;
    }

    /**
     * 构造查询条件. <功能详细描述>
     *
     * @param
     * @return
     * @see [类、类#方法、类#成员]
     */
    public BoolQueryBuilder buildBoolQueryBuilder(List<QueryBuilder> musts, List<QueryBuilder> shoulds) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        if (CollectionUtils.isNotEmpty(musts)) {
            // and关系
            for (QueryBuilder queryBuild : musts) {
                boolQueryBuilder.must(queryBuild);
            }
        }

        if (CollectionUtils.isNotEmpty(shoulds)) {
            BoolQueryBuilder boolQueryBuildershould = QueryBuilders.boolQuery();
            BoolQueryBuilder boolQueryBuildershouldPhone = QueryBuilders.boolQuery();

            boolean hasPhone = false;
            // or关系
            for (QueryBuilder queryBuild : shoulds) {
                if ("multi_match".equals(queryBuild.getName())) {
                    hasPhone = true;
                    boolQueryBuildershouldPhone.should(queryBuild);
                } else {
                    boolQueryBuildershould.should(queryBuild);
                }
            }
            boolQueryBuilder.must(boolQueryBuildershould);
            if (hasPhone) {
                boolQueryBuilder.must(boolQueryBuildershouldPhone);
            }
        }

        return boolQueryBuilder;
    }


    /**
     * 创建索引并添加文档
     *
     * @param databseName ES数据库名字
     * @param tableName   ES表名
     * @param content     增加的内容
     * @return
     * @throws Exception
     * <AUTHOR>
     */
    public void addIndexAndDocument(String databseName, String tableName, ProviceMemberElasticsearchContent content)
            throws Exception {
        log.debug("enter addIndexAndDocument to ES databseName is {}, tableName is {} ,content is {} ", databseName,
                tableName, content);
        String databaseName = esUtils.getDatabaseName(databseName, null, false);
        IndexResponse response = client.prepareIndex(databaseName, tableName)
                .setSource(XContentFactory.jsonBuilder()
                        .startObject()
                        .field("enterpriseID", content.getEnterpriseID())
                        .field("enterpriseCode", content.getEnterpriseCode())
                        .field("enterpriseName", content.getEnterpriseName())
                        .field("enterpriseType", content.getEnterpriseType())
                        .field("parentEnterpriseID", content.getParentEnterpriseID())
                        .field("parentEnterpriseCode", content.getParentEnterpriseCode())
                        .field("parentEnterpriseName", content.getParentEnterpriseName())
                        .field("serviceType", content.getServiceType())
                        .field("subServiceType", content.getSubServiceType())
                        .field("msisdn", content.getMsisdn())
                        .field("msisdnType", content.getMsisdnType())
                        .field("memberGroup", content.getMemberGroup())
                        .field("effictiveTime", content.getEffictiveTime())
                        .field("chargeNumber", content.getChargeNumber())
                        .field("orderId", content.getOrderId())
                        .field("subscribeId", content.getSubscribeId())
                        .field("businessID", content.getBusinessID())
                        .field("provinceID", content.getProvinceID())
                        .field("cityID", content.getCityID())

                        /***** 迭代十新增 start ****/

                        .field("packageCode", content.getPackageCode())
                        .field("productCode", content.getProductCode())
                        .field("ratePlanID", content.getRatePlanID())
                        .field("chargeType", content.getChargeType())

                        /***** 迭代十新增 end ****/
                        //010新增
                        .field("channelSrc", content.getChannelSrc())
                        .field("parentProvinceID", content.getParentProvinceID())
                        .field("parentCityID", content.getParentCityID())
                        .field("isRefContent", content.getIsRefContent())
                        .field("contentApproveStatus", content.getContentApproveStatus())

                        .field("isMonthByQuota", content.getIsMonthByQuota())


                        // 添加创建时间戳
                        .field("@timestamp", new Date())
                        //添加内容ID
                        .field("contentIDs", content.getContentIDs())
                        .endObject())
                .get();

        log.debug("exit addIndexAndDocument to ES  rsp= " + response);
    }

    /**
     * 获取成员信息（已去重） 该接口无法返回准确的总记录数 <功能详细描述>
     *
     * @param
     * @return
     * @see [类、类#方法、类#成员]
     */
    public Map<String, ProviceMemberElasticsearchContent> getProviceMemberList(ElasticSearchQueryCondition condition) {

        Map<String, ProviceMemberElasticsearchContent> map = new HashMap<>();

        try {
            // 获取库和表
            String indexName = condition.getDatabaseName();
            String typeName = condition.getTableName();

            // 设置过滤条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            Map<String, Object> queryCondition = condition.getQueryCondition();
            String timeString = null;
            for (Map.Entry<String, Object> entry : queryCondition.entrySet()) {
                if ("enterpriseIDs".equals(entry.getKey()) && entry.getValue() != null) {
                    List<String> enterpriseIDs = (List<String>) queryCondition.get("enterpriseIDs");

                    BoolQueryBuilder shouldQueryBuilder = QueryBuilders.boolQuery();
                    for (String enterpriseID : enterpriseIDs) {
                        shouldQueryBuilder.should((QueryBuilders.matchQuery("enterpriseID", enterpriseID)));
                    }

                    boolQueryBuilder.must(shouldQueryBuilder);

                } else {
                    boolQueryBuilder.must(QueryBuilders.termQuery(entry.getKey(), entry.getValue()));
                }
                if ("effictiveTime".equals(entry.getKey()) && entry.getValue() != null) {
                    timeString = (String) entry.getValue();
                }
            }
            String databaseName = esUtils.getDatabaseName(indexName, timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes(typeName)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setQuery(boolQueryBuilder)
                    .setSize(condition.getPageSize())
                    .execute()
                    .actionGet();

            Map<String, List<ProviceMemberElasticsearchContent>> stringListMap = new HashMap<>();
            // 循环分页处理
            do {

                List<ProviceMemberElasticsearchContent> objectList = esToClass(scrollResp, ProviceMemberElasticsearchContent.class);

                stringListMap = objectList.stream()
                        .filter(t -> t.getBusinessID().contains("__"))
                        .collect(Collectors.groupingBy(ProviceMemberElasticsearchContent::getMsisdn));
                for (String key : stringListMap.keySet()) {
                    List<ProviceMemberElasticsearchContent> proviceMemberElasticsearchContents = stringListMap.get(key);
                    //一个成员存在多条成员数据
                    if (proviceMemberElasticsearchContents.size() > 1) {
                        List<ProviceMemberElasticsearchContent> exitsContentData = proviceMemberElasticsearchContents.stream()
                                .filter(t -> !t.getBusinessID().contains("__")).collect(Collectors.toList());
                        if (exitsContentData.size() > 0) {
                            //存在多条数据 且有存在内容的数据 把没有内容的数据去除
                            for (ProviceMemberElasticsearchContent memberDetailCdr : proviceMemberElasticsearchContents) {
                                if (memberDetailCdr.getBusinessID().contains("__")) {
                                    objectList.remove(memberDetailCdr);
                                }
                            }
                        }

                    }
                }
                for (ProviceMemberElasticsearchContent content : objectList) {
                    map.put(content.getBusinessID(), content);
                }

                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();
            } while (scrollResp.getHits().getHits().length != 0);
        } catch (Exception e) {
            log.error("getProviceMemberList Fail to query data from ES.The error is {}", e);
        }

        return map;

    }

    public <T> void queryAddMemberFromESBABill(QueryCondition queryCond, Class<T> clazz,
                                            DealQuerySearchService dealQuerySearchService) {
        try {
            Map<String, MemberDetailCdr> result = new HashMap<>();
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
            String timeString = getTimeString(queryCond.getMusts());
            String databaseName = esUtils.getDatabaseName("provice_member", timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes("member_record")
                    .setQuery(boolQueryBuilder)
                    .addSort("msisdn", SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setSize(queryCond.getPageSize())
                    .get();

            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("no query need to deal data from ES");
                return;
            }
            List<MemberDetailCdr> objectList = esToClass(scrollResp, MemberDetailCdr.class);

            do {
                for (MemberDetailCdr memberDetailCdr : objectList) {
                    String subSuccessTime = memberDetailCdr.getSubSuccessTime();
                    if (StringUtils.isEmpty(subSuccessTime)) {
                        String timestamp = memberDetailCdr.getTimestamp();
                        Date from = DateUtil.convertUTCtoLocalDate(timestamp, DateUtil.DatePattern.yyyyMMddHHmmssSSSZ);
                        memberDetailCdr.setEffictiveTime(DateUtil.format(from, DateUtil.DatePattern.yyyyMMddHHmmss));
                    } else {
                        memberDetailCdr.setEffictiveTime(subSuccessTime);
                    }

                    result.put(memberDetailCdr.getBusinessID(), memberDetailCdr);
                }


                // 查出来一波，写一波话单
                // dealQuerySearchService.executeQuerySearchByInfo(objectList, queryCond.getExtMap());

                // 然后接着滚动查询
                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();

                objectList = esToClass(scrollResp, MemberDetailCdr.class);
            } while (scrollResp.getHits().getHits().length != 0);

            //map转list
            List<T> list = new ArrayList<>();
            for (Map.Entry<String, MemberDetailCdr> map : result.entrySet()) {
                list.add(JSONObject.parseObject(JSONObject.toJSONString(map.getValue()), clazz));
            }
            //list切割
            List<List<T>> resultLists =
                    Lists.partition(list, queryCond.getPageSize());
            for (List<T> memberDetails : resultLists) {
                dealQuerySearchService.executeQuerySearchByInfo(memberDetails, queryCond.getExtMap());
            }

            // 把结束开关置true，关闭最后一个文件
            Map<String, Object> extMap = queryCond.getExtMap();
            extMap.put("isFinish", Boolean.valueOf(true));

            // 最后数据没了之后，把最后一个文件流关闭.
            dealQuerySearchService.executeQuerySearchByInfo(null, extMap);
        } catch (Exception e) {
            log.error("queryMemberFromESBABill Fail to query data from ES.The error is {}", e);
        }
    }

    public <T> void queryMemberFromESBABill(QueryCondition queryCond, Class<T> clazz,
                                            DealQuerySearchService dealQuerySearchService) {
        try {
            Map<String, MemberDetailCdr> result = new HashMap<>();
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
            String timeString = getTimeString(queryCond.getMusts());
            String databaseName = esUtils.getDatabaseName("provice_member", timeString, true);

            SearchResponse scrollResp = client.prepareSearch(databaseName)
                    .setTypes("member_record")
                    .setQuery(boolQueryBuilder)
                    .addSort("msisdn", SortOrder.ASC)
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                    .setSize(queryCond.getPageSize())
                    .get();

            Long totalAmount = 0L;

            // 如果查询不到数据，直接人return
            if (null != scrollResp && null != scrollResp.getHits()) {
                totalAmount = scrollResp.getHits().getTotalHits();
            }

            if (0 == totalAmount) {
                log.info("no query need to deal data from ES");
                // 把结束开关置true，关闭最后一个文件
                Map<String, Object> extMap = queryCond.getExtMap();
                extMap.put("isFinish", Boolean.valueOf(true));

                // 最后数据没了之后，把最后一个文件流关闭.
                dealQuerySearchService.executeQuerySearchByInfo(null, extMap);
                return;
            }
            List<MemberDetailCdr> objectList = esToClass(scrollResp, MemberDetailCdr.class);

            do {
                for (MemberDetailCdr memberDetailCdr : objectList) {
                    String subSuccessTime = memberDetailCdr.getSubSuccessTime();
                    if (StringUtils.isEmpty(subSuccessTime)) {
                        String timestamp = memberDetailCdr.getTimestamp();
                        Date from = DateUtil.convertUTCtoLocalDate(timestamp, DateUtil.DatePattern.yyyyMMddHHmmssSSSZ);
                        memberDetailCdr.setEffictiveTime(DateUtil.format(from, DateUtil.DatePattern.yyyyMMddHHmmss));
                    } else {
                        memberDetailCdr.setEffictiveTime(subSuccessTime);
                    }

                    result.put(memberDetailCdr.getBusinessID(), memberDetailCdr);
                }


                // 查出来一波，写一波话单
                // dealQuerySearchService.executeQuerySearchByInfo(objectList, queryCond.getExtMap());

                // 然后接着滚动查询
                scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                        .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                        .execute()
                        .actionGet();

                objectList = esToClass(scrollResp, MemberDetailCdr.class);
            } while (scrollResp.getHits().getHits().length != 0);

            //map转list
            List<T> list = new ArrayList<>();
            for (Map.Entry<String, MemberDetailCdr> map : result.entrySet()) {
                list.add(JSONObject.parseObject(JSONObject.toJSONString(map.getValue()), clazz));
            }
            //list切割
            List<List<T>> resultLists =
                    Lists.partition(list, queryCond.getPageSize());
            for (List<T> memberDetails : resultLists) {
                dealQuerySearchService.executeQuerySearchByInfo(memberDetails, queryCond.getExtMap());
            }

            // 把结束开关置true，关闭最后一个文件
            Map<String, Object> extMap = queryCond.getExtMap();
            extMap.put("isFinish", Boolean.valueOf(true));

            // 最后数据没了之后，把最后一个文件流关闭.
            dealQuerySearchService.executeQuerySearchByInfo(null, extMap);
        } catch (Exception e) {
            log.error("queryMemberFromESBABill Fail to query data from ES.The error is {}", e);
        }
    }
    public <T> boolean queryFromESHaoBaiBill(QueryCondition queryCond, Class<T> clazz,
                                            DealQuerySearchService dealQuerySearchService, boolean hasData) {

        try {

            String timeString = getTimeString(queryCond.getMusts());
            String databaseName = esUtils.getDatabaseName(queryCond.getEsInfo().getDatabaseName(), timeString, true);
            queryCond.getEsInfo().setDatabaseName(databaseName);

            //构建查询条件
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
            //查询ES
            queryFromES(queryCond, clazz, dealQuerySearchService, boolQueryBuilder);

            Map<String, Object> extMap = queryCond.getExtMap();
            extMap.put("isFinish", Boolean.valueOf(true));
            // 关闭最后一个文件流
            dealQuerySearchService.executeQuerySearchByInfo(null, extMap);



        } catch (Exception e) {
            log.error("queryFromESBbossBill Fail to query data from ES.The error is {}", e);
        }
        return hasData;
    }
    /**
     * 不分页，按单一字段排序查询全部 <功能详细描述>
     *
     * @param <T>
     * @return
     * @see [类、类#方法、类#成员]
     */
    public <T> boolean queryFromESBbossBill(QueryCondition queryCond, Class<T> clazz,
                                            DealQuerySearchService dealQuerySearchService,
                                            boolean isHotline, boolean hasData) {
//        log.info("queryFromESBbossBill   queryCond  ：" + queryCond.toString());
        try {
            Integer limitQuerySize = null;
            if (null != queryCond && null != queryCond.getExtMap()) {
                limitQuerySize = (Integer) queryCond.getExtMap().get("limitQuerySize");
            }
            String timeString = getTimeString(queryCond.getMusts());

            String databaseName = esUtils.getDatabaseName(queryCond.getEsInfo().getDatabaseName(), timeString, true);
            queryCond.getEsInfo().setDatabaseName(databaseName);

            //分批异步查询
            List<List<QueryBuilder>> listPs = Lists.partition(queryCond.getShoulds(), limitQuerySize);
            List<FutureTask<Object>> taskList = new ArrayList<>();


            for (List<QueryBuilder> queryBuilders : listPs) {
                BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryBuilders);

                FutureTask<Object> task =
                        new FutureTask<Object>(new Callable<Object>() {
                            @Override
                            public Object call() throws Exception {
                                return queryFromES(queryCond, clazz, dealQuerySearchService, boolQueryBuilder);
                            }
                        });
                ESSearchExecutor.execute(task);
                taskList.add(task);
            }


//            if (null == limitQuerySize || queryCond.getShoulds().size() == 0 || queryCond.getShoulds().size() <= limitQuerySize) {
//                //ES查数据
//                BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
//                if (queryFromES(queryCond, clazz, dealQuerySearchService, boolQueryBuilder)) {
//                }
//                else
//                {
//                    hasData = true;
//                }
//            } else {
//                //需要处理的es总条件数
//                BigInteger maxSize=new BigInteger(String.valueOf(queryCond.getShoulds().size()));
//                //一次最多可设置的es条件数
//                BigInteger oneSize=new BigInteger(String.valueOf(limitQuerySize));
//                //求余，决定查询几次es
//                BigInteger[] counts=maxSize.divideAndRemainder(oneSize);
//                //需要循环的次数
//                BigInteger pageSize=counts[0];
//                //pageNo若大于1则pageSize加1
//                BigInteger yushu=counts[1];
//                if(yushu.intValue()>0){
//                    pageSize=pageSize.add(new BigInteger("1"));
//                }
//                int fromIndex=0;
//                int toIndex=0;
//                boolean flag=false;
//                //查询为空的次数
//                int totalAmount = 0;
//                log.info("queryFromES 本次循环 ：共"+pageSize.intValue()+"次");
//                for (int i = 0; i < pageSize.intValue(); i++) {
//                    if(i!=0){
//                        fromIndex=fromIndex+limitQuerySize;
//                    }
//                    //最后一次循环时结束标签为queryCond.getShoulds()的原始总长度（i=pageSize.intValue()-1时即为最后一次）
//                    if(i==pageSize.intValue()-1){
//                        toIndex=queryCond.getShoulds().size();
//                    }else{
//                        toIndex=toIndex+limitQuerySize;
//                    }
//                    log.debug("queryFromES 第几次 ："+(i+1)+"，起始标签："+fromIndex+"，结束标签："+toIndex);
//                    BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds().subList(fromIndex,toIndex));
//                    log.debug("queryFromESBbossBill   boolQueryBuilder  ："+boolQueryBuilder.toString());
//
//                    //ES查数据返回true代表本次查询没数据
//                    if (queryFromES(queryCond, clazz, dealQuerySearchService, boolQueryBuilder)) {
//                        //查询一次空数据则totalAmount+1
//                        totalAmount = totalAmount + 1;
//                    }
//                }
//                //若为totalAmount等于数据循环次数说明全部循环都没查询出数据，则直接结束流程;
//                if(totalAmount==pageSize.intValue()){
//                }
//                else
//                {
//                    hasData = true;
//                }
//            }
            //多线程异步执行

            //等待所有线程结束
            for(FutureTask<Object> futureTask:taskList){
                if(!(Boolean) futureTask.get()){
                    hasData = true;
                }
            }
            if (isHotline && hasData) {
                // 把结束开关置true，关闭最后一个文件
                Map<String, Object> extMap = queryCond.getExtMap();
                extMap.put("isFinish", Boolean.valueOf(true));

                // 关闭最后一个文件流
                dealQuerySearchService.executeQuerySearchByInfo(null, extMap);
            }


        } catch (Exception e) {
            log.error("queryFromESBbossBill Fail to query data from ES.The error is {}", e);
        }
        return hasData;

    }

    /**
     * ES查询数据
     *
     * @return boolean
     */
    public <T> boolean queryFromES(QueryCondition queryCond, Class<T> clazz, DealQuerySearchService dealQuerySearchService, BoolQueryBuilder boolQueryBuilder) {
        // 建立查询
        SearchResponse scrollResp = client
                .prepareSearch(queryCond.getEsInfo().getDatabaseName())
                .setTypes(queryCond.getEsInfo().getTableName())
                .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                .setQuery(boolQueryBuilder)
                .setSize(queryCond.getPageSize())
                // 本地测试时会查询ES报错,需要注释排序
                .addSort(queryCond.getSortCond().getSortField(),
                        queryCond.getSortCond().getOrder())
                .get();

        Long totalAmount = 0L;
        if (null != scrollResp && null != scrollResp.getHits()) {
            totalAmount = scrollResp.getHits().getTotalHits();
        }
//        log.info("queryFromESBbossBill   scrollResp  ：" + scrollResp.toString());
        // 如果查询不到数据，直接return
        if (0 == totalAmount) {
            log.info("no query need to deal data from ES");
            return true;
        }

        // 转换结果
        List<T> objectList = esToClass(scrollResp, clazz);
        do {
            // 查出来一波，写一波话单
            dealQuerySearchService.executeQuerySearchByInfo(objectList,
                    queryCond.getExtMap());

            // 然后接着滚动查询
            scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                    .setScroll(new TimeValue(DEFAULT_QUERY_TIME)).execute()
                    .actionGet();
            objectList = esToClass(scrollResp, clazz);
        } while (scrollResp.getHits().getHits().length != 0);
        return false;
    }

    /**
     * 滚动查询投递明细并写入文件
     * 变量模板清单导出独立出来，因为需要额外查询内容表，展示未被替换的content
     *
     * @return
     */
    public Boolean scollQueryFromESAndWriteTemp(List<QueryCondition> queryCondList,
                                                Class<DeliveryElasticsearchContent> clazz,
                                                DealExportDeliveryService exportDeliveryHelper,
                                                Function<List<String>, Map<String, String>> queryContentMap) {
        boolean result = true;

        try {
            if (null != queryCondList && queryCondList.size() > 0) {
                for (QueryCondition queryCond : queryCondList) {
                    log.debug("start scrolling query delivery details...");
                    BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryCond.getMusts(), queryCond.getShoulds());
                    String timeString = getTimeString(queryCond.getMusts());

                    String databaseName = esUtils.getDatabaseName(queryCond.getEsInfo().getDatabaseName(), timeString, true);
                    SearchResponse scrollResp = client.prepareSearch(databaseName)
                            .setTypes(queryCond.getEsInfo().getTableName())
                            .setQuery(boolQueryBuilder)
                            .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                            .addSort(queryCond.getSortCond().getSortField(), queryCond.getSortCond().getOrder())
                            .setSize(queryCond.getPageSize())
                            .get();
                    Long totalAmount = 0L;

                    // 如果查询不到数据，直接人return
                    if (null != scrollResp && null != scrollResp.getHits()) {
                        totalAmount = scrollResp.getHits().getTotalHits();
                    }

                    if (0 == totalAmount) {
                        log.info("no need to deal data from ES");
                        return true;
                    }

                    List<DeliveryElasticsearchContent> objectList = esToClass(scrollResp, clazz);
                    do {

                        // 2109：查询ES获取投递记录后，额外查询未替换变量的彩印内容
                        List<String> ids = objectList.stream().map(
                                DeliveryElasticsearchContent::getPushColorID).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(ids)) {
                            Map<String, String> contentMap = queryContentMap.apply(ids);
                            objectList.forEach(detail ->
                                    detail.setPushColorContent(contentMap.get(detail.getPushColorID())));
                        }

                        // 查出来一波，写一波
                        result = exportDeliveryHelper.exportDeliveryDetail(objectList, queryCond.getExtMap());
                        // 写的时候出现异常直接返回
                        if (!result) {
                            return false;
                        }
                        // 然后接着滚动查询
                        scrollResp = client.prepareSearchScroll(scrollResp.getScrollId())
                                .setScroll(new TimeValue(DEFAULT_QUERY_TIME))
                                .execute()
                                .actionGet();

                        objectList = esToClass(scrollResp, clazz);
                    } while (scrollResp.getHits().getHits().length != 0);
                }
            }


            // 把结束开关置true，关闭最后一个文件
            Map<String, Object> extMap = new HashMap<String, Object>();
            extMap.put("isFinish", Boolean.valueOf(true));

            // 最后数据没了之后，把最后一个文件流关闭.
            result = exportDeliveryHelper.exportDeliveryDetail(null, extMap);
            if (!result) {
                return false;
            }
        } catch (Exception e) {
            log.error("scollQueryFromESAndWriteTemp Fail to query data from ES.The error is {}", e);
        }
        log.debug("end of scrolling query delivery details.");
        return true;
    }

    public long queryDeliveryFromES(ElasticSearchInfo esInfo, List<QueryBuilder> musts, List<QueryBuilder> shoulds,
                                    PageParamES pageParamES, OrderCondES sortCond) {

        Long total = 0L;
        try {
            BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(musts, shoulds);
            SearchRequestBuilder searchRequestBuilder = null;
            String timeString = getTimeString(musts);

            String databaseName = esUtils.getDatabaseName(esInfo.getDatabaseName(), timeString, true);
            // 分页并且按某字段排序
            if (null != pageParamES) {
                searchRequestBuilder = client.prepareSearch(databaseName)
                        .setTypes(esInfo.getTableName())
                        .setQuery(boolQueryBuilder)
                        .addSort(SortBuilders.fieldSort(sortCond.getSortField()).order(sortCond.getOrder()))
                        .setFrom(pageParamES.getStartNum())
                        .setSize(pageParamES.getPageSize());
            } else {
                searchRequestBuilder = client.prepareSearch(databaseName)
                        .setTypes(esInfo.getTableName())
                        .setQuery(boolQueryBuilder);
            }
            SearchResponse response = null;

            if (null != searchRequestBuilder) {

                try {
                    // 执行查询动作.
                    response = searchRequestBuilder.execute().actionGet();
                    total = response.getHits().getTotalHits();
                } catch (Exception e) {
                    log.error("Fail to query data from ES.The error is {}", e);
                }

            }
        } catch (Exception e) {
            log.error("queryDeliveryFromES Fail to query data from ES.The error is {}", e);
        }
        return total;
    }

}
