<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SimpleEnterpriseMapper">

	<select id="getParentByid"  resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		Select f.*
		From ecpm_t_enterprise_simple
		s,ecpm_t_enterprise_simple f
		Where f.id=s.parentEnterpriseID and s.id =
		#{id}
	</select>
	
	<select id="queryAll"  resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		Select *
		From ecpm_t_enterprise_simple
	</select>

	<select id="queryListByID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		select
		id,
		enterpriseCode,
		enterpriseName,
		enterpriseType,
		organizationID,
		custID,
		parentEnterpriseID,
		provinceID,
		cityID,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpm_t_enterprise_simple where ID in
		<foreach collection="ids" item="id" open="(" separator=","
			close=")">
			#{id}
		</foreach>
	</select>
	<select id="queryZYZQList" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		select
		id,
		enterpriseCode,
		enterpriseName,
		enterpriseType,
		organizationID,
		custID,
		parentEnterpriseID,
		provinceID,
		cityID,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpm_t_enterprise_simple where reserved10 = "111"
	</select>
	<select id="queryYDYQList" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		select
			id,
			enterpriseCode,
			enterpriseName,
			enterpriseType,
			organizationID,
			custID,
			parentEnterpriseID,
			provinceID,
			cityID,
			reserved1,
			reserved2,
			reserved3,
			reserved4,
			reserved5,
			reserved6,
			reserved7,
			reserved8,
			reserved9,
			reserved10
		from
			ecpm_t_enterprise_simple where reserved10 = "112"
	</select>
	<delete id="deleteEnterprise">
		UPDATE ecpm_t_enterprise_simple set status = 2 WHERE id = #{id}
	</delete>

	<update id="updateEnterpriseStatus">
		UPDATE ecpm_t_enterprise_simple set status = #{status} WHERE id = #{id}
	</update>
	<select id="getByid"  resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		Select *
		From ecpm_t_enterprise_simple
		Where id = #{id}
	</select>

	<insert id="insertEnterprise">
		INSERT INTO ecpm_t_enterprise_simple
		(id,
		enterpriseCode,
		enterpriseName,
		enterpriseType,
		organizationID,
		custID,
		parentEnterpriseID,
		provinceID,
		cityID,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		needServiceProduct
		<if test="countyID != null and countyID!=''">,countyID</if>

		)
		VALUES
		(
		#{id},
		#{enterpriseCode},
		#{enterpriseName},
		#{enterpriseType},
		#{organizationID},
		#{custID},
		#{parentEnterpriseID},
		#{provinceID},
		#{cityID},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10},
		#{needServiceProduct}
		<if test="countyID != null and countyID!=''">,#{countyID}</if>

		)

	</insert>


	<update id="updateEnterprise">
		update ecpm_t_enterprise_simple set
		<trim suffixOverrides="," suffix="where id = #{id}">
			<if test="enterpriseCode!=null and enterpriseCode!=''">enterpriseCode= #{enterpriseCode},</if>
			<if test="enterpriseName!=null and enterpriseName!=''">enterpriseName= #{enterpriseName},</if>
			<if test="enterpriseType!=null">enterpriseType= #{enterpriseType},</if>
			<if test="organizationID!=null">organizationID= #{organizationID},</if>
			<if test="custID!=null">custID= #{custID},</if>
			<if test="parentEnterpriseID!=null and parentEnterpriseID!=''">parentEnterpriseID= #{parentEnterpriseID},</if>
			<if test="provinceID!=null and provinceID!=''">provinceID= #{provinceID},</if>
			<if test="cityID!=null and cityID!=''">cityID= #{cityID},</if>
			<if test="countyID!=null and countyID!=''">countyID= #{countyID},</if>
			<if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
			<if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
			<if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
			<if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
			<if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
			<if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
			<if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
			<if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
			<if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
			<if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
			<!--<if test="businessStatus!=null and businessStatus!=''">businessStatus= #{businessStatus}</if>-->
		</trim>
	</update>

	<select id="queryByOrgID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		select 
			b.* 
		from 
			ecpm_t_org_simple a left join ecpm_t_enterprise_simple b on a.enterpriseID = b.ID 
		where 
			a.ID = #{orgID}
	</select>

	<select id="queryEnterpriseByMsisdn" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SimpleEnterpriseWrapper">
		SELECT
		b.*
		FROM ecpm_t_member t, ecpm_t_org_rel t1, ecpm_t_enterprise_simple b
		WHERE t.msisdn = #{msisdn} AND t.ID = t1.ID AND t1.enterpriseID = b.ID;
	</select>
	
	
	<select id="queryEnterpriseIDsForSub"  resultType="java.lang.Integer">
    
    	SELECT s.id
		FROM ecpm_t_enterprise_simple s
        left join ecpm_t_enterprise_simple e on e.id=s.parentEnterpriseID
        <trim prefix="where" prefixOverrides="and|or">
        	<if test="enterpriseType != null">
            	s.enterpriseType = #{enterpriseType}
        	</if>
            <if test="cityValList != null and cityValList.size()>0">
                and (e.cityID in
                <foreach item="city" index="index" collection="cityValList"
                         open="(" separator="," close=")">
                    #{city}
                </foreach>
                or e.cityID is null)
            </if>
            <if test="districtsAndCountiesList != null and districtsAndCountiesList.size()>0">
                and (e.countyID in
                <foreach item="districtsAndCounty" index="index" collection="districtsAndCountiesList"
                         open="(" separator="," close=")">
                    #{districtsAndCounty}
                </foreach>
                or e.countyID is null)
            </if>
            <if test="enterpriseID != null">
                and (e.id = #{enterpriseID} or s.id = #{enterpriseID})
            </if>
            <if test="fieldValList !=null and fieldValList.size > 0">
                and (e.provinceID in
                <foreach collection="fieldValList" item="fieldVal" open="(" separator="," close=")">
                    #{fieldVal}
                </foreach>
                )
            </if>
        </trim>
    	
        
        
    </select>
    
    <select id="queryEnterpriseIDsForPro"  resultType="java.lang.Integer">
    
    	SELECT e.id
		FROM ecpm_t_enterprise_simple e
        <trim prefix="where" prefixOverrides="and|or">
        	<if test="enterpriseType != null">
            	e.enterpriseType = #{enterpriseType}
        	</if>
            <if test="cityValList != null and cityValList.size()>0">
                and (e.cityID in
                <foreach item="city" index="index" collection="cityValList"
                         open="(" separator="," close=")">
                    #{city}
                </foreach>
                or e.cityID is null)
            </if>
            <if test="districtsAndCountiesList != null and districtsAndCountiesList.size()>0">
                and (e.countyID in
                <foreach item="districtsAndCounty" index="index" collection="districtsAndCountiesList"
                         open="(" separator="," close=")">
                    #{districtsAndCounty}
                </foreach>
                or e.countyID is null)
            </if>
            <if test="enterpriseID != null">
                and e.id = #{enterpriseID}
            </if>
            <if test="fieldValList !=null and fieldValList.size > 0">
                and (e.provinceID in
                <foreach collection="fieldValList" item="fieldVal" open="(" separator="," close=")">
                    #{fieldVal}
                </foreach>
                )
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and (e.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
                )
            </if>
            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and (e.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach> )
            </if>
        </trim>
    	
        
        
    </select>
</mapper>