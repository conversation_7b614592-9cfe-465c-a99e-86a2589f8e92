var app = angular.module("myApp", ["util.ajax", "angularI18n"])
app.controller('accountManageController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    // var accountInfo = JSON.parse($.cookie('accountInfo'));
    $scope.accountID = $.cookie('accountID');
    $scope.loginRoleType = $.cookie('loginRoleType');
    $scope.accountInfo = {};
    $scope.accountName = "";
    $scope.roleName = "";
    $scope.fullName = "";
    $scope.msisdn = "";
    $scope.pwd = "";
    $scope.repwd = "";
    $scope.confirmPwd = "";
    $scope.rePasswordValidate = true;
    $scope.newPasswordValidate = true;
    $scope.newPwdRuleValidate = "";
    $scope.pwdValidate = true;
    $scope.originPwdValidate = true;

    $scope.queryAccoountInfo();
  };

  // 修改密码弹窗
  $scope.updatePwd = function () {
    $scope.pwd = "";
    $scope.repwd = "";
    $scope.confirmPwd = "";
    $scope.pwdValidate = true;
    $scope.rePasswordValidate = true;
    $scope.newPasswordValidate = true;
    $scope.originPwdValidate = true;
    $('#accountManage').modal();
  };

    /*校验各个字段*/
    $scope.validate = function (context, maxlength, reg) {
      if (!context) {
        return false;
      } else {
        if (context.length > maxlength) {
          return false;
        } else {
          if (!reg.test(context)) {
            return false;
          } else {
            return true;
          }
        }
      }
    };

  
  //查询账户
  $scope.queryAccoountInfo = function () {
    var req = {
      "accountID":parseInt($scope.accountID)
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/queryAccount",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          var accountInfo  = data.accountInfo;
          if (result.resultCode == '**********') {
            $scope.accountName = accountInfo.accountName
            angular.forEach(accountInfo.roleList, function (role) {
                $scope.roleName = $scope.roleName + role.roleName + "/";
            });
            $scope.roleName = $scope.roleName.substr(0, $scope.roleName.length - 1)
            $scope.fullName = accountInfo.fullName
            $scope.msisdn = accountInfo.msisdn
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }

  // 联系人
  $scope.fullNameValidate = true;
  $scope.checkFullName = function (fullName) {
    $scope.fullNameValidate = $scope.validate(fullName, 32, /[A-Za-z\u4e00-\u9fa5]{1,32}$/);
  }

  // 手机号
  $scope.msisdnValidate = true;
  $scope.checkMsisdn = function (msisdn) {
    $scope.msisdnValidate = $scope.validate(msisdn, 11, /[0-9]{11}$/);
  }

  //修改账号
  $scope.pwdSave = function () {
    var req = {
      "accountInfo":{
        "fullName":$scope.fullName,
        "msisdn":$scope.msisdn,
        "accountID":parseInt($scope.accountID)
      }
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/updateAccount",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.tip = "COMMON_SAVESUCCESS";
            $('#myModal').modal();
            // window.history.back();
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }

  //新密码与确认密码校验
  $scope.checkRePassword = function () {
    var repwd = $scope.repwd;
    var confirmPwd = $scope.confirmPwd;
    $scope.rePasswordValidate = true;
     if (!confirmPwd) {
       $scope.rePasswordValidate = false;
     }
     if (repwd != confirmPwd) {
       $scope.rePasswordValidate = false;
     }
   };

   //原密码校验
  $scope.checkOriginPwd = function () {
    var pwd = $scope.pwd;
    if (!pwd) {
      $scope.originPwdValidate = false;
    }else {
      $scope.originPwdValidate = true;
    }
  }

   //原密码与新密码校验
   $scope.checkNewPwd = function () {
     var pwd = $scope.pwd;
     var repwd = $scope.repwd;
     $scope.newPasswordValidate = true;
     $scope.rePasswordValidate = true;
      if (pwd == repwd && pwd!="") {
        $scope.tip = "NEWPWDEQUALSORIGINPWD";
        $scope.newPasswordValidate = false;
        $scope.rePasswordValidate = true;
        $('#myModal').modal();
    }
   };

    //检查密码
    $scope.checkNewPwdRule=function(){
      $scope.pwdValidate = true;
     $scope.newPwdRuleValidate = "";
     var rePwd = $scope.repwd;
     var confirmPwd = $scope.confirmPwd;
     var checkPwdRuleReq = {};
     checkPwdRuleReq.password = rePwd;
    RestClientUtil.ajaxRequest({
       type: 'POST',
       url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
       data: JSON.stringify(checkPwdRuleReq),
       success: function (data) {
         $rootScope.$apply(function () {
             var result = data.result;
             if (result.resultCode != '**********') {
               $scope.pwdValidate = false;
                 var passwordRuleList = data.passwordRuleList;
                 if(result.resultCode == '1030120000'){
                   $scope.newPwdRuleValidate ="密码必填";
                 }else{
                   if(!passwordRuleList){
                     $scope.newPwdRuleValidate = result.resultDesc;
                   }else{
                     for (var i = 0; i < passwordRuleList.length; i++) {
                             $scope.newPwdRuleValidate  = $scope.newPwdRuleValidate + passwordRuleList[i].ruleName;
                       // $scope.passwordValidateDesc =  passwordRuleList[i].ruleName;
                             // $scope.newPwdRuleValidate  = $scope.newPwdRuleValidate + ";";
                             }
                             $scope.newPwdRuleValidate.substring(0, $scope.newPwdRuleValidate.length - 1);
                   }
                 }
             } else {
             if(confirmPwd){
               $scope.checkRePassword();
             }
             }
           })
       },
       error:function(){
           $rootScope.$apply(function(){
               $scope.tip='**********';
               $('#myModal').modal();
               }
           )
       }
     });
  };

   //弹窗关闭
   $scope.closeTip = function () {
    if ($scope.tip == "UPDATEPWDSUCCESS") {
      $('#updatePwdCancel').click();
    } else if ($scope.tip == "COMMON_SAVESUCCESS") {
      window.history.back();
    }
   }

  //修改密码
  $scope.confirmUpdate = function () {
    const encrypt = new JSEncrypt();
    const key = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANW45vowD1WF7WGJO///YmziLY/wROrIsuKy9YVoi2k8F64o7a0yEw9YmvqOBIN4eTBXMBgxNRK7vAcMbVnh74sCAwEAAQ==";
    encrypt.setPublicKey(key);

    var req = {
      "accountID":parseInt($scope.accountID),
      "oldPwd":encrypt.encrypt($scope.pwd),
      "newPwd":encrypt.encrypt($scope.repwd)
    };
    // var pwd = $scope.pwd;
    // var rePwd = $scope.repwd;
    // if( pwd != rePwd){
    //   return
    // }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/changePwd",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          console.log(result + "123")
          if (result.resultCode == '**********') {
            if($scope.pwd == $scope.repwd){
              $scope.checkNewPwd();
            }else{
            $scope.tip = "UPDATEPWDSUCCESS";
            $('#myModal').modal();}
          } else {
            if (result.resultCode == '**********') {
              $scope.tip = "ORIGINPWDISWRONG";
            }
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
}])

