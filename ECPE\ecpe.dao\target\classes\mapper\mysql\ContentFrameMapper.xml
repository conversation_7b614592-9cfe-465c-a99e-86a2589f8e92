<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ContentFrameMapper">
	<resultMap id="contentFrameMap"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentFrameWrapper">
		<result property="id" column="ID" />
		<result property="cyContID" column="cyContID" />
		<result property="frameNo" column="frameNo" />
		<result property="frameType" column="frameType" />
		<result property="frameTxt" column="frameTxt" />
		<result property="framePicName" column="framePicName" />
		<result property="framePicUrl" column="framePicUrl" />
		<result property="framePicSize" column="framePicSize" />
		<result property="createTime" column="createTime" />
		<result property="updateTime" column="updateTime" />
		<result property="operatorID" column="operatorID" />
	</resultMap>
	
	<sql id="content_frame_wrapper">
		ID,cyContID,frameNo,frameType,frameTxt,framePicName,framePicUrl,framePicSize,createTime,updateTime,operatorID
	</sql>
	
	<insert id="insertContentFrame" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentWrapper">
		INSERT INTO ecpe_t_content_frame
		(
			<include refid="content_frame_wrapper"></include>
		)
		VALUES
		(
		#{id},
		#{cyContID},
		#{frameNo},
		#{frameType},
		#{frameTxt},
		#{framePicName},
		#{framePicUrl},
		#{framePicSize},
		#{createTime},
		#{updateTime},
		#{operatorID}
		)
	</insert>
	
	<delete id="deleteContentFrameByContentID" parameterType="java.lang.Long">
	    delete from ecpe_t_content_frame where cyContID=#{cyContID}
	</delete>


	<select id="queryContentFrameByConID" resultMap="contentFrameMap">
		select * from ecpe_t_content_frame where cyContID=#{id}
	</select>
</mapper>