<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper.SeqMapper">
	
    <select id="nextValue" parameterType="java.lang.String" resultType="java.lang.Integer">
		select nextval(#{name});
	</select>

	<update id="resetValue" parameterType="java.lang.String">
		update seq_ecpm 
		set currentvalue = 0 
		where name = #{name};
	</update>

</mapper>