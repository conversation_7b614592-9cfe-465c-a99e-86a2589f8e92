var app = angular.module("myApp",["util.ajax",'page',"top.menu","angularI18n"])
//自定义filter,格式化日期
app.filter('newDate',function(){
    return function(date){
        var new_date =  date.substr(0,4)+"-"+date.substr(4,2)+"-"+date.substr(6.2);
        return new_date;
    }
});
app.controller('merchantListController', function ($scope,$rootScope,$filter,$location,RestClientUtil) {
	/*$(window).resize(function () {          //当浏览器大小变化时
		$('#merchantDetailPop').modal('hide');
	});
	$(function(){
	    $(document).bind("click", function () {
    		var evt = event.srcElement ? event.srcElement : event.target;
    		if(evt.id){
    			if(evt.id == 'merchantDetailPop' || "queryButton_" ==evt.id.substring(0,evt.id.length-1)){　
    				$('#merchantDetailPop').modal('show');
              	} else{
              		$('#merchantDetailPop').modal('hide');
              	}
    		}else{
    			$('#merchantDetailPop').modal('hide');
          	}
	    });    
	});*/
    //初始化参数
    $scope.init = function () {
    	//$scope.merchantName ="";
    	//$scope.auditStatus ="";

    	//搜索时初始化参数
                $scope.initSel = {
                    merchantName: "",//企业名称
                    auditStatus: "",//企业机构代码
                };

    	$scope.statusMap={
            "1":"审核中",
            "2":"已认证",
            "3":"驳回",
            "4":"变更审核中",
            "5":"变更驳回"
        }
    	//下拉框(企业状态)
        $scope.auditStatusChoise = [
  	    {
              id:"",
              name: "不限"
          },
          {
              id:"1",
              name: "审核中"
          },
          {
              id:"2",
              name: "已认证"
          },{
              id:"3",
              name:"驳回"
          }
      ];
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        $scope.merchantListData =[];
        $scope.queryMerchantList();
        $.cookie("enterpriseType",4,{path:'/'});
    };
    
    $scope.formatDate=function(str){
        if(!str){
            return 'format error';
        }
        var newDateStr="";
        newDateStr=str.substr(0,4)+'-'+str.substr(4,2)+'-'+str.substr(6,2);
        return newDateStr;
    }
    
    //跳转至修改商户页面
    $scope.gotoEdit=function(item){
    	//审核中状态不可修改
        if(item.auditStatus==1){
            $scope.tip= "1030121000";
            $('#myModal').modal();
            return;
        }
    	$.cookie("enterpriseID",item.id,{path:'/'});
    	$.cookie("operationType","edit",{path:'/'});
    	location.href='../merchant/merchantDetail.html';
    }
    
    //弹出商户操作弹出框
    /*$scope.gotoDetailPop=function(item,index){
    	$scope.itemTemp =angular.copy(item);
    	var a ="queryButton_"+index;
    	var d = document.getElementById(a);
        var posL = d.offsetLeft;   
        var posT = d.offsetTop;
        while(d.offsetParent!=null) { 
	    	var oParent = d.offsetParent;
	    	posL += oParent.offsetLeft;
	    	posT += oParent.offsetTop;
	    	d = oParent;
    	}
        if(posL <1000){
        	a ="editButton_"+index;
        	d = document.getElementById(a);
            posL = d.offsetLeft;   
            posT = d.offsetTop;
            while(d.offsetParent!=null) { 
    	    	var oParent = d.offsetParent;
    	    	posL += oParent.offsetLeft;
    	    	posT += oParent.offsetTop;
    	    	d = oParent;
        	}
        }
        $("#merchantDetailPop").css({ "top": posT+'px', "left": posL+'px',position:'absolute'}).modal('show');
    }*/
    //跳转至商户详情页面
    $scope.gotoDetail=function(item){
    	$.cookie("enterpriseID",item.id,{path:'/'});
    	$.cookie("operationType","detail",{path:'/'});
    	location.href='../merchant/merchantDetail.html';
    }
    
    //跳转至商户管理页面
    $scope.gotoManage=function(item){
    	$.cookie("enterpriseID",item.id,{path:'/'});
        location.href='../activityManage/activityManageList/activityManageList.html';
    }
    
    //获取queryEnterpriseList接口的数据
    $scope.queryMerchantList = function (condition) {
    	var auditStatusTemp =null;
    	if($scope.auditStatus){
    		auditStatusTemp =parseInt($scope.auditStatus);
    	}
        if(condition!='justPage'){
            var req = {
                "enterpriseType":4,
                "auditStatus":auditStatusTemp,
                "sortType":2,
                "sortField":1,
                "enterpriseName":$scope.merchantName,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize":$scope.pageInfo[0].pageSize,
                    "isReturnTotal": "1"
                }
            };
            
            if($scope.merchantName===undefined&&req.auditStatus===null){
                    $scope.merchantName ="";
                	$scope.auditStatus ="";
            } else {
                    sessionStorage.setItem("cacheMerchantName",$scope.merchantName);
                    sessionStorage.setItem("cacheAuditStatus",$scope.auditStatus);
            }
            if(sessionStorage.getItem("cacheMerchantName")){
                    $scope.merchantName = sessionStorage.getItem("cacheMerchantName");
                    req.enterpriseName = sessionStorage.getItem("cacheMerchantName");
            } else {
                    $scope.merchantName = "";
            }
            if(sessionStorage.getItem("cacheAuditStatus")){
                    $scope.auditStatus = sessionStorage.getItem("cacheAuditStatus");
                    req.auditStatus = parseInt(sessionStorage.getItem("cacheAuditStatus"));
            } else {
                    $scope.auditStatus = "";
            }
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.pageParameter.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.merchantListData =result.enterpriseList||[];
                        //获取页面的总条数与总页面
                        $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                        if($scope.pageInfo[0].totalCount==0){
                            $scope.pageInfo[0].currentPage=1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage=1;
                        }else{
                            $scope.pageInfo[0].totalPage=Math.ceil(parseInt(result.totalNum)/parseInt($scope.pageInfo[0].pageSize));
                        }
                    }else{
                        $scope.merchantListData=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }
})
