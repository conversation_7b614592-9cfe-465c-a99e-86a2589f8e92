var myApp = angular.module("myApp", [
  "util.ajax",
  "page",
  "top.menu",
  "angularI18n",
  "service.common",
]);

// 定义号码认证控制器
myApp.controller("numberAuthenticationController", [
  "$scope",
  "$rootScope",
  "$location",
  "RestClientUtil",
  "CommonUtils",
  function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    // 初始化函数
    $scope.init = function () {
      // 初始化列表数据
      $scope.numberAuthenticationListData = [];
      // 初始化查询参数
      $scope.searchParams = {
        enterpriseName: "",
        auditStatus: "",
        startAuditTime: "",
        endAuditTime: "",
        pageParameter: {
          pageSize: 10,
          pageNum: 1,
          isReturnTotal: "1",
        },
      };

      //初始化分页信息
      $scope.pageInfo = [
        {
          totalPage: 1,
          totalCount: 0,
          pageSize: "10",
          currentPage: 1,
        },
      ];

      // 初始化审核状态选项
      $scope.auditStatusOptions = [
        { id: "", name: "全部" },
        { id: "1", name: "待审核" },
        { id: "2", name: "通过" },
        { id: "3", name: "驳回" },
      ];

      // 初始化审核状态映射表
      $scope.auditStatusMap = {
        1: "待审核",
        2: "通过",
        3: "驳回",
      };
      // 获取审核状态文本
      $scope.getAuditStatusText = function (status) {
        return $scope.auditStatusMap[status] || "-";
      };

      // 初始化日期选择器
      $(".input-daterange").datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
      });
      // 加载初始数据
      $scope.queryNumberAuthenticationList();
    };

    // 查询号码认证列表
    $scope.queryNumberAuthenticationList = function (isJustPage) {
      var queryParams = angular.copy($scope.searchParams);

      // 处理分页信息
      if (isJustPage != "justPage") {
        $scope.pageInfo[0].currentPage = 1;
      }

      // 构建查询参数
      var params = {
        enterpriseName: queryParams.enterpriseName,
        auditStatus: queryParams.auditStatus,
        startAuditTime: $scope.searchParams.startAuditTime,
        endAuditTime: $scope.searchParams.endAuditTime,
        pageParameter: {
          pageSize: $scope.pageInfo[0].pageSize,
          pageNum: $scope.pageInfo[0].currentPage,
          isReturnTotal: "1",
        },
      };

      // 发送请求获取数据
      RestClientUtil.ajaxRequest({
        type: "POST",
        url: "/ecpmp/ecpmpServices/msisdnAuthService/queryEnterprise",
        data: JSON.stringify(params),
        success: function (result) {
          $rootScope.$apply(function () {
            var data = result.result;
            if (data.resultCode == "1030100000") {
              $scope.numberAuthenticationListData =
                result.msisdnAuthEnterpriseInfos || [];

              // 设置分页信息
              $scope.pageInfo[0].totalCount = result.totalNum || 0;
              $scope.pageInfo[0].totalPage = Math.ceil(
                $scope.pageInfo[0].totalCount / $scope.pageInfo[0].pageSize
              );

              // 设置分页组件参数
              $("#0").attr("totalpage", $scope.pageInfo[0].totalPage);
              $("#0").attr("currentpage", $scope.pageInfo[0].currentPage);
              $("#0").attr("pagesize", $scope.pageInfo[0].pageSize);
            } else {
              $scope.tip = data.resultDesc || "查询号码认证数据失败";
              $("#myModal").modal("show");
            }
          });
        },
        error: function () {
          $rootScope.$apply(function () {
            $scope.tip = "系统错误，请稍后重试";
            $("#myModal").modal("show");
          });
        },
      });
    };

    // 导出号码认证数据
    $scope.exportNumberAuthentication = function () {
      var req = {
        param: { req: JSON.stringify($scope.searchParams) },
        url: "/qycy/ecpmp/ecpmpServices/msisdnAuthService/downEnterprise",
        method: "get",
      };
      if ($scope.searchParams != undefined) {
        CommonUtils.exportFile(req);
      }
    };

    // 查看号码认证详情
    $scope.viewNumberAuthentication = function (item) {
      // 将选中的记录ID存入sessionStorage，以便详情页面获取
      sessionStorage.setItem("numberAuthenticationId", item.id);

      // 跳转到详情页面
      window.location.href = "./check/check.html?id=" + item.id;
    };

    // 监听分页事件
    $scope.$on("pageChange", function (event, page) {
      $scope.pageInfo[0].currentPage = parseInt(page.currentpage);
      $scope.queryNumberAuthenticationList("justPage");
    });

    $("#start").on("changeDate", function () {
      $rootScope.$apply(function () {
        // 时间为yyyy-mm-dd 00:00:00
        $scope.searchParams.startAuditTime = $("#start").val();
        if ($scope.searchParams.startAuditTime) {
          $scope.searchParams.startAuditTime =
            $scope.searchParams.startAuditTime.substring(0, 10) + " 00:00:00";
        }
      });
    });

    $("#end").on("changeDate", function () {
      $rootScope.$apply(function () {
        // 时间为yyyy-mm-dd 23:59:59
        $scope.searchParams.endAuditTime = $("#end").val();
        if ($scope.searchParams.endAuditTime) {
          $scope.searchParams.endAuditTime =
            $scope.searchParams.endAuditTime.substring(0, 10) + " 23:59:59";
        }
      });
    });
  },
]);
