<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ContentOrgMapper">
	<resultMap id="contentOrgMap"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentOrgWrapper">
		<result property="id" column="ID" javaType="java.lang.Long" />
		<result property="cyContID" column="cyContID" javaType="java.lang.Long" />
		<result property="ownerType" column="ownerType" javaType="java.lang.Integer" />
		<result property="ownerID" column="ownerID" javaType="java.lang.Integer" />
		<result property="orgCode" column="orgCode" javaType="java.lang.String" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="hotlineNo" column="hotlineNo" javaType="java.lang.String" />
		<result property="orgName" column="orgName" javaType="java.lang.String" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="branchType" column="branchType" javaType="java.lang.String" />
	</resultMap>
	<insert id="batchInsertContentOrg">
		INSERT INTO ecpm_t_content_org
		(ID,
		cyContID,
		ownerType,
		ownerID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID,
		orgCode,
		orgName,
		hotlineNo,
		msisdn
		)
		VALUES
		<foreach collection="list" item="contentOrgWrapper" separator=",">
			(
			nextval('ecpm_seq_contentorg'),
			#{contentOrgWrapper.cyContID},
			#{contentOrgWrapper.ownerType},
			#{contentOrgWrapper.ownerID},
			#{contentOrgWrapper.extInfo},
			#{contentOrgWrapper.reserved1},
			#{contentOrgWrapper.reserved2},
			#{contentOrgWrapper.reserved3},
			#{contentOrgWrapper.reserved4},
			#{contentOrgWrapper.createTime},
			#{contentOrgWrapper.updateTime},
			#{contentOrgWrapper.operatorID},
			#{contentOrgWrapper.orgCode},
			#{contentOrgWrapper.orgName},
			#{contentOrgWrapper.hotlineNo},
			#{contentOrgWrapper.msisdn}
			)
		</foreach>
	</insert>
	<select id="queryContentOrgListByCyContID" resultMap="contentOrgMap">
		SELECT c.ID,
		c.cyContID,
		c.ownerType,
		c.ownerID,
		c.orgCode,
		c.orgName,
		c.hotlineNo,
		c.extInfo,
		c.reserved1,
		c.reserved2,
		c.reserved3,
		c.reserved4,
		c.createTime,
		c.updateTime,
		c.msisdn,
		c.operatorID FROM ecpm_t_content_org c WHERE cyContID=#{cyContID};
	</select>
	<select id="queryContentOrgListByCyContID2" resultMap="contentOrgMap">
		SELECT c.ID,
		c.cyContID,
		c.ownerType,
		c.ownerID,
		c.orgCode,
		c.orgName,
		c.hotlineNo,
		c.extInfo,
		c.reserved1,
		c.reserved2,
		c.reserved3,
		c.reserved4,
		c.createTime,
		c.updateTime,
		c.msisdn,
		c.operatorID,
		s.branchType FROM ecpm_t_content_org c, ecpm_t_org_simple s WHERE c.ownerID = s.id AND cyContID=#{cyContID};
	</select>
	<select id="queryContentOrgListByCyContIDX" resultMap="contentOrgMap">
		select co.ID,
		co.cyContID,
		co.ownerType,
		co.ownerID,
		co.orgCode,
		co.orgName,
		co.hotlineNo,
		co.extInfo,
		co.reserved1,
		co.reserved2,
		co.reserved3,
		co.reserved4,
		co.createTime,
		co.updateTime,
		co.msisdn,
		co.operatorID
		from ecpm_t_content_org co, ecpm_t_content c
		where c.id = co.cyContID and (co.cyContID=#{cyContID} OR c.parentID = #{cyContID} or oriContentID =  #{cyContID})
	</select>
	<update id="batchUpdateContentOrgCode">
		<foreach collection="list" item="contentOrgWrapper" separator=";">
			update ecpm_t_content_org set
			orgCode=#{contentOrgWrapper.orgCode},
			updateTime=#{contentOrgWrapper.updateTime}
			where ID =
			#{contentOrgWrapper.id}
		</foreach>
	</update>
	
	<update id="batchUpdateOrgCodeByCyContID">
		<foreach collection="list" item="contentOrgWrapper" separator=";">
			update ecpm_t_content_org set
			orgCode=#{contentOrgWrapper.orgCode},
			updateTime=#{contentOrgWrapper.updateTime}
			where cyContID =
			#{contentOrgWrapper.cyContID}
		</foreach>
	</update>
	<update id="updateOrgName">
		UPDATE  ecpm_t_content_org
		set orgName = #{newName}
		where orgName= #{oldName}
		and cyContID in
		<foreach item="id" index="index" collection="list" open="("
				 separator="," close=")">
			#{id}
		</foreach>
	</update>

	<select id="queryContentOrgListByOrgID" resultMap="contentOrgMap">
		select ID,
		cyContID,
		ownerType,
		ownerID,
		orgCode,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime
		from
		ecpm_t_content_org
		where
		ownerID=#{orgID}
	</select>

	<delete id="deleteContentOrgByCyContID">
		delete from ecpm_t_content_org where cyContID = #{cyContID}
	</delete>

	<select id="queryContentOrgListByActivityID" resultMap="contentOrgMap">
		select
		t2.ID,
		t2.cyContID,
		t2.ownerType,
		t2.ownerID,
		t2.orgCode,
		t2.extInfo,
		t2.reserved1,
		t2.reserved2,
		t2.reserved3,
		t2.reserved4,
		t2.createTime,
		t2.updateTime
		from
		ecpm_t_activity_contentrel t1, ecpm_t_content_org t2
		where
		t1.contentID=t2.cyContID
		and
		t1.activityID=#{activityID}
	</select>


	<!--根据组织获得内容列表 -->
	<select id="queryCyContIDListByOrgID" resultType="java.lang.Long">
		select cyContID
		from
		ecpm_t_content_org
		where
		ownerID=#{orgID}
	</select>

	<select id="queryOrgByContentID" resultMap="contentOrgMap">
		select
		ID,
		cyContID,
		ownerType,
		ownerID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID,
		orgCode,
		orgName,
		msisdn,
		hotlineNo
		from ecpm_t_content_org where cyContID = #{contentID}
	</select>

	<select id="queryOrgIDByEntIDAndServ" resultMap="contentOrgMap">
		select
		t2.ownerID ,t2.orgName ,t2.cyContID,t1.hangupType
		from ecpm_t_content t1,
		ecpm_t_content_org t2
		where t1.ID=t2.cyContID
		and
		t1.enterpriseID=#{enterpriseID}
		and
		t1.servType=#{servType}
		and
		t1.subServType=#{subServType}
-- 		and t1.thirdpartyType=0
	</select>
	<select id="queryOrgIDByProductId" resultMap="contentOrgMap">
		select
			t2.ownerID ,t2.orgName ,t2.cyContID
		from ecpm_t_content t1,
			 ecpm_t_content_org t2
		where t1.ID=t2.cyContID
		  and
			t1.enterpriseID=#{enterpriseID}

		and t1.pkgID in
		<foreach item="pkgID" index="index" collection="pkgIDs" open="("
				 separator="," close=")">
			#{pkgID}
		</foreach>
	</select>
	<delete id="deleteOrgContentByOrg">
		delete from ecpm_t_content_org where
		ownerID in
		<foreach item="orgID" index="index" collection="list" open="("
			separator="," close=")">
			#{orgID}
		</foreach>
	</delete>
	
	<select id="queryOrgCodeByOrgID" resultType="java.lang.String">
		select
		distinct(orgCode)
		from
		ecpm_t_content_org
		where ownerID = #{orgID}
	</select>
	
	<select id="queryMemberNumberByContId" resultType="java.lang.String">
		SELECT DISTINCT t1.msisdn
		  FROM ecpm_t_org_rel t, ecpm_t_member_subscribe t1, ecpm_t_content_org t2
		 WHERE t2.ownerID = t.orgID and t.ID = t1.memberID and t1.status = 3 and t2.cyContID = #{cyContID}
	</select>
	
	<select id="queryOrgByMsisdn" resultMap="contentOrgMap">
		select
		ID,
		cyContID,
		ownerType,
		ownerID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID,
		orgCode,
		orgName,
		hotlineNo,
		msisdn
		from ecpm_t_content_org where msisdn = #{msisdn}
	</select>
	
	<select id="queryOrgIDByContentID" resultMap="contentOrgMap">
		select
		ID,
		cyContID,
		ownerType,
		ownerID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID,
		orgCode,
		orgName,
		hotlineNo
		from ecpm_t_content_org where cyContID = #{contentID}
	</select>
	
	<select id="queryContentRelObjectList" resultMap="contentOrgMap">
		select
			a.ID,a.cyContID,a.ownerType,a.ownerID,a.extInfo,a.reserved1,
			a.reserved2,a.reserved3,a.reserved4,a.createTime,a.updateTime,
			a.operatorID,a.orgCode,a.orgName,a.hotlineNo,a.msisdn
		from ecpm_t_content_org a
		<if test="enterpriseID != null">
			left join ecpm_t_content b on a.cyContID = b.ID
		</if>
		<trim prefix="where" prefixOverrides="and|or">
			<if test="contentID != null">
				and a.cyContID = #{contentID}
			</if>
			<if test="ownerType !=null">
				and a.ownerType = #{ownerType}
			</if>
			<if test="hotlineNo !=null">
				and a.hotlineNo like "%"#{hotlineNo}"%"
			</if>
			<if test="enterpriseID != null">
				and b.enterpriseID = #{enterpriseID}
			</if>
		</trim>
		order by a.updateTime desc,a.ownerID desc
		limit #{pageNum},#{pageSize}
	</select>
	
	<select id="countContentRelObjectList" resultType="java.lang.Integer">
		select count(1) from ecpm_t_content_org a
		<if test="enterpriseID != null">
			left join ecpm_t_content b on a.cyContID = b.ID
		</if>
		<trim prefix="where" prefixOverrides="and|or">
			<if test="contentID != null">
				and a.cyContID = #{contentID}
			</if>
			<if test="ownerType !=null">
				and a.ownerType = #{ownerType}
			</if>
			<if test="hotlineNo !=null">
				and a.hotlineNo like "%"#{hotlineNo}"%"
			</if>
			<if test="enterpriseID != null">
				and b.enterpriseID = #{enterpriseID}
			</if>
		</trim>
	</select>
	
	<select id="queryContentOrgListByHotlineList" resultMap="contentOrgMap">
		select ID,
		cyContID,
		ownerType,
		ownerID,
		orgCode,
		orgName,
		hotlineNo,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		msisdn,
		operatorID from ecpm_t_content_org 
		<trim prefix="where" prefixOverrides="and|or">
			<if test="cyContID != null">
				and cyContID = #{cyContID}
			</if>
			<if test="hotlineNoList != null and hotlineNoList.size()>0">
				and hotlineNo in
				<foreach item="hotlineNo" index="index" collection="hotlineNoList"
					open="(" separator="," close=")">
					#{hotlineNo}
				</foreach>
			</if>
		</trim>
	</select>
	
	<select id="queryContentOrgListByCyIDList" resultMap="contentOrgMap">
		select ID,
		cyContID,
		ownerType,
		ownerID,
		orgCode,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		msisdn
		from
		ecpm_t_content_org
		where
		cyContID in
		<foreach collection="list" item="cyContID" open="(" separator=","
			close=")">
			#{cyContID}
		</foreach>
	</select>
	
	<select id="queryContentOrgByOrgIDAndStatus" resultMap="contentOrgMap">
		SELECT t1.ID,
		t1.cyContID,
		t1.ownerType,
		t1.ownerID,
		t1.orgCode,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.createTime,
		t1.updateTime,
		t2.enterpriseID,
		t2.subServType
		FROM
		ecpm_t_content_org t1,ecpm_t_content t2
		WHERE
		t1.cyContID = t2.ID AND t2.approveStatus =3
		and 
		t1.ownerID=#{orgID}
	</select>
	
	<select id="queryContentOrgListByHotlineIds" resultMap="contentOrgMap">
		select ID,
			cyContID,
			ownerType,
			ownerID,
			hotlineNo,
			extInfo,
			reserved1,
			reserved2,
			reserved3,
			reserved4,
			createTime,
			updateTime,
			operatorID 
		from 
			ecpm_t_content_org 
		where
			ownerType = 2
			and ownerID in
			<foreach item="hotlineId" index="index" collection="list"
				open="(" separator="," close=")">
				#{hotlineId}
			</foreach>
	</select>

	<select id="queryMsisdnTypeList" resultType="com.huawei.jaguar.dsdp.ecpm.model.MsisdnType">
		select id,msisdnType from ecpm_t_msisdntype order by id
	</select>

	<select id="queryContentAndOrg" resultMap="contentOrgMap">
		SELECT
			DISTINCT(t2.orgCode),
			t1.enterpriseID,
			t1.ServType,
			t1.subServType
		from
			(select * from ecpm_t_content where id = #{id} or parentID = #{id}) t1,ecpm_t_content_org t2
		where
			t1.ID = t2.cyContID
	</select>

</mapper>