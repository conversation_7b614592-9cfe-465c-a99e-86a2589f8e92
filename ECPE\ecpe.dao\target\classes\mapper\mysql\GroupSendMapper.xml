<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.GroupSendTaskMapper">
    <resultMap id="groupSendTask"
               type="com.huawei.jaguar.dsdp.ecpe.dao.domain.GroupSendTaskWrapper">
        <result property="id" column="id" javaType="java.lang.Long"/>
        <result property="code" column="code" javaType="java.lang.String"/>
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer"/>
        <result property="taskName" column="taskName" javaType="java.lang.String"/>
        <result property="serviceType" column="serviceType" javaType="java.lang.Integer"/>
        <result property="taskType" column="taskType" javaType="java.lang.Integer"/>
        <result property="timingTime" column="timingTime" javaType="Date"/>
        <result property="contentID" column="contentID" javaType="java.lang.Long"/>
        <result property="status" column="status" javaType="java.lang.Integer"/>
        <result property="auditOpinion" column="auditOpinion" javaType="java.lang.String"/>
        <result property="successNum" column="successNum" javaType="java.lang.Integer"/>
        <result property="total" column="total" javaType="java.lang.Integer"/>
        <result property="createTime" column="createTime" javaType="Date"/>
        <result property="updateTime" column="updateTime" javaType="Date"/>
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer"/>
        <result property="reserved1" column="reserved1" javaType="java.lang.String"/>
        <result property="reserved2" column="reserved2" javaType="java.lang.String"/>
        <result property="reserved3" column="reserved3" javaType="java.lang.String"/>
        <result property="reserved4" column="reserved4" javaType="java.lang.String"/>
        <result property="notifyThirdReqTime" column="notifyThirdReqTime" javaType="Date"/>
        <result property="notifyThirdRspTime" column="notifyThirdRspTime" javaType="Date"/>
        <result property="notifyStatus" column="notifyStatus" javaType="java.lang.Integer"/>
        <result property="notifyCount" column="notifyCount" javaType="java.lang.Integer"/>
        <result property="processStatus" column="processStatus" javaType="java.lang.Integer"/>
        <result property="src" column="src" javaType="java.lang.String"/>
        <result property="notifyUrl" column="notifyUrl" javaType="java.lang.String"/>
        <result property="triggerDeliveryTime" column="triggerDeliveryTime" javaType="Date"/>
        <result property="notifyTime" column="notifyTime" javaType="Date"/>
        <result property="flag" column="flag" javaType="java.lang.String"/>



    </resultMap>

    <select id="getTaskID" resultType="java.lang.Long">
        select next value for MYCATSEQ_ECPE_T_GROUP_SEND_TASK
    </select>

    <insert id="createGroupSendTask">
		insert into ecpe_t_group_send_task
		(
        id,
		code,
		enterpriseID,
		taskName,
		serviceType,
		taskType,
		timingTime,
		contentID,
		status,
		auditOpinion,
		successNum,
		total,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		notifyThirdReqTime,
		notifyThirdRspTime,
		notifyStatus,
		notifyCount,
		processStatus,
        src,
        notifyUrl,
        triggerDeliveryTime,
        notifyTime
		)
		values
		(
        #{id},
		#{code},
		#{enterpriseID},
		#{taskName},
		#{serviceType},
		#{taskType},
		#{timingTime},
		#{contentID},
		#{status},
		#{auditOpinion},
		#{successNum},
		#{total},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{notifyThirdReqTime},
		#{notifyThirdRspTime},
		#{notifyStatus},
		#{notifyCount},
		#{processStatus},
        #{src},
        #{notifyUrl},
        #{triggerDeliveryTime},
        #{notifyTime}
		)
	</insert>

    <select id="queryGroupSendTaskList" resultMap="groupSendTask">
        select
        ID,
        code,
        enterpriseID,
        taskName,
        serviceType,
        taskType,
        timingTime,
        contentID,
        status,
        auditOpinion,
        successNum,
        total,
        createTime,
        updateTime,
        operatorID,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        notifyThirdReqTime,
        notifyThirdRspTime,
        notifyStatus,
        notifyCount,
        processStatus,
        src,
        notifyUrl,
        triggerDeliveryTime,
        notifyTime
        from
        ecpe_t_group_send_task
        <trim prefix="where" prefixOverrides="and|or">
            <if test="taskName != null and taskName != ''">
                and taskName like "%"#{taskName}"%"
            </if>
            <if test="status != null">
                and status =#{status}
            </if>
            <if test="contentID != null">
                and contentID =#{contentID}
            </if>
            <if test="enterpriseID != null ">
                and enterpriseID = #{enterpriseID}
            </if>
            <if test="null != taskType">
                and taskType = #{taskType}
            </if>
            <if test="contentIDList != null and contentIDList.size > 0">
        		and contentID in 
        		<foreach collection="contentIDList" item="contentID" index="index" open="(" separator="," close=")">  
					#{contentID}
				</foreach>
        	</if>
        </trim>
        order by updateTime desc
        <if test="pageNum != null and pageSize != null">
            limit #{pageNum},#{pageSize}
        </if>

    </select>



    <select id="selectTaskListLimitTime" resultMap="groupSendTask">
        select
        ID,
        code,
        enterpriseID,
        taskName,
        serviceType,
        taskType,
        timingTime,
        contentID,
        status,
        auditOpinion,
        successNum,
        total,
        createTime,
        updateTime,
        operatorID,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        notifyThirdReqTime,
        notifyThirdRspTime,
        notifyStatus,
        notifyCount,
        processStatus,
        src,
        notifyUrl,
        triggerDeliveryTime,
        notifyTime
        from
        ecpe_t_group_send_task
        <trim prefix="where" prefixOverrides="and|or">
            <if test="taskName != null and taskName != ''">
                and taskName like "%"#{taskName}"%"
            </if>
            <if test="status != null">
                and status =#{status}
            </if>
            <if test="contentID != null">
                and contentID =#{contentID}
            </if>
            <if test="enterpriseID != null ">
                and enterpriseID = #{enterpriseID}
            </if>
            <if test="null != taskType">
                and taskType = #{taskType}
            </if>
            <if test="null != timingTime">
                and timingTime &lt;= #{timingTime}
            </if>
        </trim>
        order by id desc,updateTime desc
        <if test="pageNum != null and pageSize != null">
            limit #{pageNum},#{pageSize}
        </if>

    </select>

    <select id="queryGroupSendTaskTotal" resultType="java.lang.Integer">
        select count(1) from ecpe_t_group_send_task
        <trim prefix="where" prefixOverrides="and|or">
            <if test="taskName != null and taskName != ''">
                and taskName like "%"#{taskName}"%"
            </if>
            <if test="status != null and status != ''">
                and status =#{status}
            </if>
            <if test="enterpriseID != null ">
                and enterpriseID = #{enterpriseID}
            </if>
            <if test="null != taskType">
                and taskType = #{taskType}
            </if>
            <if test="contentIDList != null and contentIDList.size > 0">
        		and contentID in 
        		<foreach collection="contentIDList" item="contentID" index="index" open="(" separator="," close=")">  
					#{contentID}
				</foreach>
        	</if>
        </trim>
    </select>

    <delete id="deleteGroupSendByTaskID">
		delete from ecpe_t_group_send_task where ID =
		#{taskID}
	</delete>

    <update id="incryTaskSuccessByTaskId">
    update ecpe_t_group_send_task set  successNum = successNum + 1 WHERE ID = #{taskId}
    </update>

    <update id="updateGroupSendTask">
        update ecpe_t_group_send_task set
        <trim suffixOverrides="," suffix="where id=#{id}">
            <if test="null != code and '' != code">
                code = #{code},
            </if>
            <if test="null != taskName and '' != taskName">
                taskName = #{taskName},
            </if>
            <if test="null != serviceType">
                serviceType = #{serviceType},
            </if>
            <if test="null != taskType">
                taskType = #{taskType},
            </if>
            <if test="null != enterpriseID">
                enterpriseID = #{enterpriseID},
            </if>
            <if test="null != timingTime">
                timingTime = #{timingTime},
            </if>
            <if test="null != contentID">
                contentID = #{contentID},
            </if>
            <if test="null != status">
                status = #{status},
            </if>
            <if test="null != auditOpinion and '' != auditOpinion">
                auditOpinion = #{auditOpinion},
            </if>
            <if test="null != successNum">
                successNum =successNum + #{successNum},
            </if>
            <if test="null != total">
                total = #{total},
            </if>
            <if test="null != updateTime">
                updateTime = #{updateTime},
            </if>
            <if test="null != notifyThirdReqTime">
                notifyThirdReqTime = #{notifyThirdReqTime},
            </if>
            <if test="null != notifyThirdRspTime">
                notifyThirdRspTime = #{notifyThirdRspTime},
            </if>
            <if test="null != notifyStatus">
                notifyStatus = #{notifyStatus},
            </if>
            <if test="null != notifyCount">
                notifyCount = IFNULL(notifyCount,0)+1,
            </if>
            <if test="null != processStatus">
                processStatus = #{processStatus},
            </if>
            <if test="null != src and '' != src">
                src = #{src},
            </if>
            <if test="null != notifyUrl and '' != notifyUrl">
                notifyUrl = #{notifyUrl},
            </if>
            <if test="null != triggerDeliveryTime">
                triggerDeliveryTime = #{triggerDeliveryTime},
            </if>
            <if test="null != notifyTime">
                notifyTime = #{notifyTime}
            </if>
        </trim>
    </update>

    <select id="queryTaskByID" resultMap="groupSendTask">
		select
		ID,
		code,
		enterpriseID,
		taskName,
		serviceType,
		taskType,
		timingTime,
		contentID,
		status,
		auditOpinion,
		successNum,
		total,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		notifyThirdReqTime,
		notifyThirdRspTime,
		notifyStatus,
		notifyCount,
		processStatus,
		src,
		notifyUrl,
		triggerDeliveryTime,
        notifyTime
		from
		ecpe_t_group_send_task where id = #{taskID}
	</select>

    <select id="queryGroupSendTaskByContentID" resultMap="groupSendTask">
		select
		ID,
		code,
		enterpriseID,
		taskName,
		serviceType,
		taskType,
		timingTime,
		contentID,
		status,
		auditOpinion,
		successNum,
		total,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		notifyThirdReqTime,
		notifyThirdRspTime,
		notifyStatus,
		notifyCount,
		processStatus,
		src,
		notifyUrl,
		triggerDeliveryTime,
        notifyTime
		from
		ecpe_t_group_send_task where contentID = #{contentID}
	</select>

    <update id="updateTaskStatus">
		update ecpe_t_group_send_task set status = #{status}, updateTime=#{updateTime}
		where id = #{id}
	</update>

	<update id="updateTaskStatusAndContent">
		update ecpe_t_group_send_task set status = #{status}, contentID = null, updateTime=#{updateTime}
		where id = #{id}
	</update>

    <select id="selectGroupSendTaskList" resultMap="groupSendTask">
        (select
        1 flag,
        t1.ID,
        t1.code,
        t1.enterpriseID,
        t1.taskName,
        t1.serviceType,
        t1.taskType,
        t1.timingTime,
        t1.contentID,
        t1.status,
        t1.auditOpinion,
        t1.successNum,
        t1.total,
        t1.createTime,
        t1.updateTime,
        t1.operatorID,
        t1.reserved1,
        t1.reserved2,
        t1.reserved3,
        t1.reserved4,
        t1.notifyThirdReqTime,
        t1.notifyThirdRspTime,
        t1.notifyStatus,
        t1.notifyCount,
        t1.processStatus,
        t1.src,
        t1.notifyUrl,
        t1.triggerDeliveryTime,
        t1.notifyTime
        from
        ecpe_t_group_send_task t1
        where
        t1.processStatus in(0,3)
        and t1.status in(2,4,5)
        and (t1.notifyStatus = 0 or t1.notifyStatus is null)
        <if test="notifyCount != null ">
            and ( t1.notifyCount <![CDATA[  < ]]>#{notifyCount} or t1.notifyCount is null)
        </if>
        and not exists (select id from ecpe_t_group_send_task_result s where s.`taskID` = t1.ID and s.`status` = 5)
        )
        UNION
        (select
        1 flag,
        t2.ID,
        t2.code,
        t2.enterpriseID,
        t2.taskName,
        t2.serviceType,
        t2.taskType,
        t2.timingTime,
        t2.contentID,
        t2.status,
        t2.auditOpinion,
        t2.successNum,
        t2.total,
        t2.createTime,
        t2.updateTime,
        t2.operatorID,
        t2.reserved1,
        t2.reserved2,
        t2.reserved3,
        t2.reserved4,
        t2.notifyThirdReqTime,
        t2.notifyThirdRspTime,
        t2.notifyStatus,
        t2.notifyCount,
        t2.processStatus,
        t2.src,
        t2.notifyUrl,
        t2.triggerDeliveryTime,
        t2.notifyTime
        from
        ecpe_t_group_send_task t2
        where
        t2.processStatus is null
        and t2.status in(2,4,5)
        and (t2.notifyStatus = 0 or t2.notifyStatus is null)
        and not exists (select id from ecpe_t_group_send_task_result s where s.`taskID` = t2.ID and s.`status` = 5)
        )
        <if test="triggerDeliveryTime != null">
            UNION
            (select
            2 flag,
            t.ID,
            t.code,
            t.enterpriseID,
            t.taskName,
            t.serviceType,
            t.taskType,
            t.timingTime,
            t.contentID,
            t.status,
            t.auditOpinion,
            t.successNum,
            t.total,
            t.createTime,
            t.updateTime,
            t.operatorID,
            t.reserved1,
            t.reserved2,
            t.reserved3,
            t.reserved4,
            t.notifyThirdReqTime,
            t.notifyThirdRspTime,
            t.notifyStatus,
            t.notifyCount,
            t.processStatus,
            t.src,
            t.notifyUrl,
            t.triggerDeliveryTime,
            t.notifyTime
            from
            ecpe_t_group_send_task t
            where
            (t.processStatus is null or t.processStatus in(0,3))
            and (t.notifyStatus = 0 or t.notifyStatus is null)
            and t.status in(4,5,6)
            and t.notifyTime is null
            and t.timingTime <![CDATA[ < ]]>#{triggerDeliveryTime}
            )
        </if>
        limit #{limitQueryNum}
    </select>

    <update id="updateNotifyStatusByLIstId">
        UPDATE ecpe_t_group_send_task t
        SET
        <trim suffixOverrides=",">
            <if test="notifyStatus !=null">
                notifyStatus = #{notifyStatus},
            </if>
            <if test="processStatus !=null">
                processStatus = #{processStatus},
            </if>
            <if test="updateTime !=null">
                updateTime = #{updateTime},
            </if>
            <if test="notifyCount !=null">
                notifyCount = IFNULL(notifyCount,0)+1,
            </if>
            <if test="status !=null">
                status = #{status}
            </if>
        </trim>
        WHERE t.ID IN
        <foreach collection="taskIdList" item="taskID" separator="," open="(" close=")">
            #{taskID}
        </foreach>
    </update>

    <update id="updateNotifyStatusById">
        UPDATE ecpe_t_group_send_task t
        SET
        <trim suffixOverrides=",">
            <if test="notifyStatus !=null">
                notifyStatus = #{notifyStatus},
            </if>
            <if test="processStatus !=null">
                processStatus = #{processStatus},
            </if>
            <if test="updateTime !=null">
                updateTime = #{updateTime},
            </if>
            <if test="notifyCount !=null">
                notifyCount = IFNULL(notifyCount,0)+1,
            </if>
            <if test="status !=null">
                status = #{status}
            </if>
        </trim>
        WHERE t.ID = #{id}
    </update>

    <select id="getNotifyCountById" resultType="java.lang.Integer">
        select notifyCount from ecpe_t_group_send_task where ID=#{taskID}
    </select>

    <select id="queryTaskByTaskCode" resultMap="groupSendTask">
        select
        ID,
        code,
        enterpriseID,
        taskName,
        serviceType,
        taskType,
        timingTime,
        contentID,
        status,
        auditOpinion,
        successNum,
        total,
        createTime,
        updateTime,
        operatorID,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        notifyThirdReqTime,
        notifyThirdRspTime,
        notifyStatus,
        notifyCount,
        processStatus,
        src,
        notifyUrl,
        triggerDeliveryTime,
        notifyTime
        from
        ecpe_t_group_send_task where code = #{taskCode}
    </select>

</mapper>