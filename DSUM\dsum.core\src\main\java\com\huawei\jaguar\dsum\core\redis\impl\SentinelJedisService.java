package com.huawei.jaguar.dsum.core.redis.impl;

import com.huawei.jaguar.dsdp.color.common.config.JedisPoolConfig;
import com.huawei.jaguar.dsdp.color.common.redis.common.SentinelClusterImpl;
import com.huawei.jaguar.dsdp.color.common.redis.compress.impl.ProtostuffTranscoder;
import com.huawei.jaguar.dsdp.color.common.utils.JedisClientClusterUtil;
import com.huawei.jaguar.dsum.core.redis.JedisCluster;
import com.huawei.jaguar.dsum.exception.DsumException;
import com.huawei.jaguar.dsum.result.ResultCode;
import com.huawei.jaguar.dsum.result.ResultMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.ShardedJedis;
import redis.clients.jedis.util.Pool;
import redis.clients.jedis.util.SafeEncoder;

import java.util.List;

/**
 * 
 * Sentinel模式初始化
 *
 * <AUTHOR>
 * @version C10 2018年11月26日
 * @since SDP V300R003C10
 */
@Slf4j
@Service
public class SentinelJedisService implements JedisCluster
{
    
    /**
     * comons的初始化redis方法
     */
    private SentinelClusterImpl sentinelClusterImpl;
    
    /**
     * 序列化
     */
    private final ProtostuffTranscoder protostuffTranscoder = new ProtostuffTranscoder();
    
    @Override
    public SentinelJedisService initialize(JedisPoolConfig jedisPoolConfig, String connectUrls)
    {
        
        int mode = JedisClientClusterUtil.getRedisMode(connectUrls);
        if (mode == JedisClientClusterUtil.SENTINEL_CLUSTER)
        {
            sentinelClusterImpl = new SentinelClusterImpl();
        }
        else
        {
            throw new DsumException(ResultCode.SYSTEM_EXCEPTION,
                "sentinel mode is chose, but redis server doesn't support." + connectUrls);
        }
        
        sentinelClusterImpl.initCluster(connectUrls, jedisPoolConfig);
        
        return this;
        
    }
    
    @Override
    public void init()
    {
        
    }
    
    @Override
    public void set(String key, Object object)
    {
        byte[] data = protostuffTranscoder.encode(object);
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.set(SafeEncoder.encode(key), data);
        }
        finally
        {
            returnJedis(jedis);
        }
        
    }
    
    @Override
    public void set(byte[] key, byte[] value)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.set(key, value);
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    @Override
    public void setex(byte[] key, int seconds, byte[] value)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.setex(key, seconds, value);
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when set cache.", e);
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    /**
     * 向缓存池返回缓存连接。
     * 
     * @param jedis jedis
     * @since V300R003C20B301
     */
    protected void returnJedis(ShardedJedis jedis)
    {
        if (null == jedis)
        {
            return;
        }
        
        try
        {
            // 由于调用returnResource()也可能抛异常，而缓存里即使里抛异常也不应影响主体业务逻辑，所以捕获所有异常
            sentinelClusterImpl.returnShardedJedis(jedis);
        }
        catch (Throwable e)
        {
            log.warn("error when return cache connection.");
        }
    }
    
    @Override
    public <T> T get(String key, Class<T> targetClass)
    {
        
        byte[] ret = null;
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            ret = jedis.get(SafeEncoder.encode(key));
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when get(String key, Class<T> clazz) from cache.", e);
        }
        finally
        {
            returnJedis(jedis);
            
        }
        
        if (null == ret)
        {
            return null;
        }
        else
        {
            log.debug("found in cache, key=" + key + ",object=" + protostuffTranscoder.decode(ret, targetClass));
            return (T)protostuffTranscoder.decode(ret, targetClass);
        }
    }
    
    @Override
    public void del(String key)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.del(SafeEncoder.encode(key));
        }
        catch (Throwable e)
        {
            
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when delete(String key) from cache.", e);
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    @Override
    public void del(byte[] key)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.del(key);
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when delete(byte[] key) from cache.", e);
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    @Override
    public long setnx(String key, String value)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.setnx(key, value);
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when setnx cache.", e);
        }
        finally
        {
            returnJedis(jedis);
            
        }
        
        return 0;
    }
    
    @Override
    public String getSetBytes(String key, String value)
    {
        
        byte[] date = null;
        ShardedJedis jedis = null;
        
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            
            date = jedis.getSet(SafeEncoder.encode(key), SafeEncoder.encode(value));
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when getSetBytes from cache.", e);
        }
        finally
        {
            returnJedis(jedis);
            
        }
        
        if (null == date)
        {
            return null;
        }
        else
        {
            return SafeEncoder.encode(date);
        }
    }
    
    @Override
    public List<String> lrange(String key, long start, long end)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.lrange(key, start, end);
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when lrange cache.", e);
            return null;
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    @Override
    public Long lrem(String key, long count, String value)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.lrem(key, count, value);
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when lrem cache.", e);
            return null;
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    @Override
    public Long lpush(String key, String... strings)
    {
        
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            return jedis.lpush(key, strings);
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when lpush cache.", e);
            return null;
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    @Override
    public List<HostAndPort> getAllRedisInfo()
    {
        return sentinelClusterImpl.getAllRedis();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long incr(String key) {
        ShardedJedis jedis = null;
        Long rtn = 0L;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            rtn = jedis.incr(key);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when incr .", e);
        } finally {
            returnJedis(jedis);
        }
        return rtn;
    }

    /** {@inheritDoc} */

    @Override
    public Long expire(String key, int seconds) {
        ShardedJedis jedis = null;
        Long rtn = 0L;
        try {
            jedis = sentinelClusterImpl.getShardedJedis();
            rtn = jedis.expire(key, seconds);
        } catch (Throwable e) {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when expire .", e);
        } finally {
            returnJedis(jedis);
        }
        return rtn;
    }


    @Override
    public void refresh(String connectUrls)
    {
        
    }
    
    @Override
    public void destory()
    {
        try
        {
            sentinelClusterImpl.destroyCluster();
            sentinelClusterImpl = null;
        }
        catch (Exception e)
        {
            sentinelClusterImpl = null;
        }
        
    }
    
    @Override
    public Pool<? extends ShardedJedis> getRedisPool()
    {
        return sentinelClusterImpl.getShardedJedisPool();
    }
    
    @Override
    public void setex(String key, int seconds, Object value)
    {
        byte[] data = protostuffTranscoder.encode(value);
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            jedis.setex(SafeEncoder.encode(key), seconds, data);
        }
        catch (Throwable e)
        {
            log.warn("error when set cache.", e);
            throw new DsumException(ResultCode.REDIS_ERROR, ResultMessage.REDIS_ERROR_MESSAGE, e);
        }
        finally
        {
            returnJedis(jedis);
            
        }
    }
    
    @Override
    public <T> T getObject(String key, Class<T> targetClass)
        throws Exception
    {
        byte[] ret = null;
        ShardedJedis jedis = null;
        try
        {
            jedis = sentinelClusterImpl.getShardedJedis();
            ret = jedis.get(SafeEncoder.encode(key));
        }
        catch (Throwable e)
        {
            // 缓存异常不应影响主体业务逻辑，故只打日志
            log.warn("error when get(String key, Class<T> clazz) from cache.", e);
            throw new DsumException(ResultCode.REDIS_ERROR, ResultMessage.REDIS_ERROR_MESSAGE, e);
        }
        finally
        {
            returnJedis(jedis);
        }
        if (null == ret)
        {
            return null;
        }
        else
        {
            log.debug("found in cache, key=" + key + ",object=" + protostuffTranscoder.decode(ret, targetClass));
            return (T)protostuffTranscoder.decode(ret, targetClass);
        }
    }
    
}
