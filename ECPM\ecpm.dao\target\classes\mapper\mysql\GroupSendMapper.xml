<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.GroupSendTashMapper">
	<resultMap id="groupSendTask"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.GroupSendTaskWrapper">
		<result property="id" column="id" javaType="java.lang.Long" />
		<result property="code" column="code" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="taskName" column="taskName" javaType="java.lang.String" />
		<result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
		<result property="taskType" column="taskType" javaType="java.lang.Integer" />
		<result property="timingTime" column="timingTime" javaType="Date" />
		<result property="contentID" column="contentID" javaType="java.lang.Long" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="auditOpinion" column="auditOpinion" javaType="java.lang.String" />
		<result property="successNum" column="successNum" javaType="java.lang.Integer" />
		<result property="total" column="total" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
	</resultMap>

	<insert id="createGroupSendTask" keyProperty="id"
		useGeneratedKeys="true">
		insert into ecpm_t_group_send_task
		(
		code,
		enterpriseID,
		taskName,
		serviceType,
		taskType,
		timingTime,
		contentID,
		status,
		auditOpinion,
		successNum,
		total,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		)
		values
		(
		#{code},
		#{enterpriseID},
		#{taskName},
		#{serviceType},
		#{taskType},
		#{timingTime},
		#{contentID},
		#{status},
		#{auditOpinion},
		#{successNum},
		#{total},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4}
		)
	</insert>

	<select id="queryGroupSendTaskList" resultMap="groupSendTask">
		select
		ID,
		code,
		enterpriseID,
		taskName,
		serviceType,
		taskType,
		timingTime,
		contentID,
		status,
		auditOpinion,
		successNum,
		total,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpm_t_group_send_task
		<trim prefix="where" prefixOverrides="and|or">
			<if test="taskName != null and taskName != ''">
				and taskName like "%"#{taskName}"%"
			</if>
			<if test="status != null and status != ''">
				and status =#{status}
			</if>
			<if test="enterpriseID != null ">
				and enterpriseID = #{enterpriseID}
			</if>
		</trim>
		order by updateTime desc
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryGroupSendTaskTotal" resultType="java.lang.Integer">
		select count(1) from ecpm_t_group_send_task
		<trim prefix="where" prefixOverrides="and|or">
			<if test="taskName != null and taskName != ''">
				and taskName like "%"#{taskName}"%"
			</if>
			<if test="status != null and status != ''">
				and status =#{status}
			</if>
			<if test="enterpriseID != null ">
				and enterpriseID = #{enterpriseID}
			</if>
		</trim>
	</select>

	<delete id="deleteGroupSendByTaskID">
		delete from ecpm_t_group_send_task where ID =
		#{taskID}
	</delete>

	<update id="updateGroupSendTask">
		update ecpm_t_group_send_task set
		<trim suffixOverrides="," suffix="where id=#{id}">
			<if test="null != code and '' != code">
				code = #{code},
			</if>
			<if test="null != taskName and '' != taskName">
				taskName = #{taskName},
			</if>
			<if test="null != serviceType">
				serviceType = #{serviceType},
			</if>
			<if test="null != taskType">
				taskType = #{taskType},
			</if>
			<if test="null != enterpriseID">
				enterpriseID = #{enterpriseID},
			</if>
			<if test="null != timingTime">
				timingTime = #{timingTime},
			</if>
			<if test="null != contentID">
				contentID = #{contentID},
			</if>
			<if test="null != status">
				status = #{status},
			</if>
			<if test="null != auditOpinion and '' != auditOpinion">
				auditOpinion = #{auditOpinion},
			</if>
			<if test="null != successNum">
				successNum =successNum + #{successNum},
			</if>
			<if test="null != total">
				total = #{total},
			</if>
			<if test="null != updateTime">
				updateTime = #{updateTime}
			</if>
		</trim>
	</update>

	<select id="queryTaskByID" resultMap="groupSendTask">
		select
		ID,
		code,
		enterpriseID,
		taskName,
		serviceType,
		taskType,
		timingTime,
		contentID,
		status,
		auditOpinion,
		successNum,
		total,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpm_t_group_send_task where id = #{taskID}
	</select>

	<select id="queryGroupSendTaskByContentID" resultMap="groupSendTask">
		select
		ID,
		code,
		enterpriseID,
		taskName,
		serviceType,
		taskType,
		timingTime,
		contentID,
		status,
		auditOpinion,
		successNum,
		total,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpm_t_group_send_task where contentID = #{contentID}
	</select>

	<update id="updateTaskByContentID">
		update ecpm_t_group_send_task set status = #{status}
		where contentID = #{contentID}
	</update>
</mapper>