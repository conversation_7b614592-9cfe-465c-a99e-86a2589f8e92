########################################################
###server
########################################################
spring.profiles.active=dev
spring.application.name: ecpm
server.servlet.context-path=/ecpm
server.ip=127.0.0.1

########################################################
###datasource
########################################################
mybatis.mapper-locations: classpath*:mapper/mysql/*.xml

########################################################
###zk connector name
########################################################
zk.connector.name: ecpm.configerZKClient

########################################################
###redis
########################################################
#redis.server.connect=*************:17379|*************:17379|*************:17379
#redis.server.connect=**************:26750|**************:26751
#redis.server.password=
#redis.server.connectionTimeout=2000
#redis.server.maxIdle=80
#redis.server.maxWaitMillis=60000
#redis.server.maxTotal=100
#redis.server.soTimeout=2000
#redis.server.singleKeyTimeOut=100
#redis.server.mutiKeyTimeOut=200


########################################################
###\u5F69\u5370\u4E2D\u592E\u5E73\u53F0 url
########################################################
cybase.common.application.name=cy-base-common-params

cybase.sms.application.name=cy-base-sms

cybase.content.application.name=cy-core-content-enterprise

cybase.rule.application.name=cy-core-pushrule

cybase.order.application.name=cy-sop-user-order

cybase.content.audit.name=cy-core-content-enterprise

cybase.number.application.name=cy-core-remind

cybase.shumei.application.name=cy-core-shumei

content-contentBelongOrg-size=20

hotline-hotLinesList-length=50

ecpfep.application.url=10.124.73.14:18901

content-contentvar-size=50

enterprise.createContent.secretKey=0123456789ABHAEQ
server.port=21001
spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8

spring.freemarker.allow-request-override=false
spring.freemarker.cache=false
spring.freemarker.check-template-location=true
spring.freemarker.charset=UTF-8
spring.freemarker.content-type=text/html; charset=utf-8
spring.freemarker.prefer-file-system-access=false
spring.freemarker.expose-request-attributes=false
spring.freemarker.expose-session-attributes=false
spring.freemarker.expose-spring-macro-helpers=false
spring.freemarker.suffix=.ftl
spring.freemarker.template-loader-path=classpath:/templates

spring.cloud.stream.kafka.binder.zkNodes=**************:2181,**************:2181,**************:2181
spring.cloud.stream.kafka.binder.brokers=**************:9092,**************:9092,**************:9092
spring.cloud.stream.default-binder=kafka

spring.cloud.config.name=ecpm
spring.cloud.config.enabled=true
spring.cloud.config.label=master
spring.cloud.config.profile=resource,serviceConfig
spring.cloud.config.discovery.enabled=true
spring.cloud.config.discovery.service-id=CY-CONFIG-SERVER

eureka.client.serviceUrl.defaultZone=http://127.0.0.1:19000/eureka/
eureka.instance.preferIpAddress=true
eureka.instance.instanceId=${spring.cloud.client.ip-address}:${server.port}

ribbon.eureka.enabled=true
ribbon.ReadTimeout=20000
ribbon.ConnectTimeout=5000

logging.config.classpath=log4j2.xml

spring.main.allow-bean-definition-overriding=true
spring.config.import=optional:configserver:

feign.circuitbreaker.enabled=true


# 或者只开放特定端点
management.endpoints.web.exposure.include=refresh,health,info
