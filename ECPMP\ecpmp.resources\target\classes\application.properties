spring.application.name=ecpmp
spring.profiles.active=dev
server.servlet.context-path=/qycy/ecpmp
http.port=28080
server.ip=************
server.port=28443

server.ssl.key-store=classpath:ssl.keystorde/keystore.jks
server.ssl.key-alias=tomcat
server.ssl.enabled=true
server.ssl.key-store-password=Bb2rs6eXBXhLZMwFMBm/0A==
server.ssl.key-store-type=JKS
logging.config.classpath=log4j2.xml
dsum.application.name=dsum
ecpm.application.name=ecpm
ioc.application.name=ioc
ecpe.application.name=ecpe
dsum.application.url=127.0.0.1:21000/dsum
ecpm.application.url=127.0.0.1:21001/ecpm
ioc.application.url=127.0.0.1:21003/ioc
ecpe.application.url=127.0.0.1:21002/ecpe

server.ssl.enabled-protocols=TLSv1.0,TLSv1.1,TLSv1.2
server.ssl.ciphers=TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_RC4_128_SHA,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA,SSL_RSA_WITH_RC4_128_SHA
spring.http.multipart.maxFileSize=20Mb
spring.http.multipart.maxRequestSize=25Mb
management.security.enabled=false

endpoints.enabled = false

#\u65E5\u5FD7\u5F00\u5173
server.tomcat.access-log-enabled=true
#\u65E5\u5FD7\u683C\u5F0F
server.tomcat.access-log-pattern=%h %l %u %t "%r" %s %b %D
server.tomcat.basedir=../logs/
task.PropertyUtil.fixedDelay=0 0/30 * * * ?

spring.main.allow-bean-definition-overriding=true
spring.config.import=optional:configserver:

feign.circuitbreaker.enabled=true
eureka.client.enabled=false

#解决中文文件名访问问题的配置
server.tomcat.uri-encoding=UTF-8
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
server.tomcat.decode-uri-slash=true
server.tomcat.connection-timeout=10000
server.tomcat.keep-alive-timeout=20000
server.tomcat.max-connections=2000
server.tomcat.max-threads=2000
server.tomcat.accept-count=100
server.tomcat.max-http-header-size=32768
