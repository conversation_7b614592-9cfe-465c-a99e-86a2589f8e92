
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>查看代言人</title>
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/activityManageList.css" rel="stylesheet" />
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>

<script type="text/javascript" src="querySpokesperson.js"></script>
</head>
<body ng-app='myApp' style="min-width:1024px;" ng-controller='querySpokesController' ng-init="init();">
    <div class="cooperation-manage container-fluid" >
        <div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_MERCHANT'|translate">
        </span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'SPOKES_CHECK'|translate"></span></div>
        <!-- <top:menu chose-index="1" page-url="/qycy/ecpmp/view/orderListManage" list-index="1"></top:menu> -->
        <div class="cooperation-search">
            <form class="form-horizontal">
                    <div class="form-group form-inline">
                            <label for="msisdn" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap"  ng-bind="'SPOKES_MSISDN'|translate"></label>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                                    <input autocomplete="off" type="text" class="form-control" id="msisdn" placeholder="{{'SPOKES_PLEASEINPUTMSISDN'|translate}}" ng-model="initSel.msisdn">
                            </div>
                            <label for="spokeStatus" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap"  ng-bind="'SPOKES_STATUS'|translate"></label>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                                    <select id="spokeStatus" class="form-control" ng-model="initSel.spokeStatus" ng-options="x.id as x.name for x in spokeStatusChoise" >
                                    </select>
                            </div>
                            <label for="awardStatus" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap"  ng-bind="'SPOKES_REWARDSTATUS'|translate"></label>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                                    <select id="awardStatus" class="form-control" ng-model="initSel.awardStatus" ng-options="x.id as x.name for x in rewardStatusChoise" >
                                    </select>
                            </div>
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1">
                                <button type="submit" class="btn search-btn" ng-click="querySpokesmanList()">
                                    <icon class="search-iocn"></icon>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                            </div>
                        </div>
            </form>
        </div>
        <div class="add-table">
                <button id="exportSpokesList" type="submit" class="btn add-btn" ng-click="exportFile()"><icon class="add-iocn"></icon><span ng-bind="'COMMON_EXPORTRECORD'|translate"></span></button>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th ng-bind="'SPOKES_MSISDN'|translate"></th>
                        <th ng-bind="'SPOKES_LOCATION'|translate"></th>
                        <th ng-bind="'SPOKES_SPOKESTIME'|translate"></th>
                        <th ng-bind="'SPOKES_SPOKESDAYS'|translate"></th>
                        <th ng-bind="'SPOKES_SPOKESSCREENCOUNT'|translate"></th>
                        <th ng-bind="'SPOKES_SPOKESENDPHONECOUNT'|translate"></th>
                        <th ng-bind="'SPOKES_STATUS'|translate"></th>
                        <th ng-bind="'SPOKES_REWARDSTATUS'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in spokesmanList">
                        <td title="{{item.msisdn}}">{{item.msisdn}}</td>
                        <td title="{{formatRegion(item.province,item.city)}}">{{formatRegion(item.province,item.city)}}</td>
                        <td title="{{formatDate(item.startTime,item.endTime)}}">{{formatDate(item.startTime,item.endTime)}}</td>
                        <td title="{{item.dayCount}}">{{item.dayCount}}</td>
                        <td title="{{item.screenCount}}">{{item.screenCount}}</td>
                        <td title="{{item.endPhoneCount}}">{{item.endPhoneCount}}</td>
                        <td title="{{spokeStatusMap[item.isCannel]}}">{{spokeStatusMap[item.isCannel]}}</td>
                        <td title="{{item.isReward=='1'?'是':'否'}}">{{item.isReward=='1'?'是':'否'}}</td>
                    </tr>
                    <tr ng-show="spokesmanList.length<=0">
                        <td style="text-align:center" colspan="8" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    <div>
        <button type="submit" class="btn btn-back" ng-click="goBack()" style="margin: 20px"
                ng-bind="'COMMON_BACK'|translate"></button>
        <ptl-page tableId="0" change="querySpokesmanList('justPage')"></ptl-page>
      </div>
    </div>
<!--小弹出框-->
        <!-- <button type="button" class="btn btn-primary" id="open-model" data-toggle="modal" data-target=".bs-example-modal-sm">Large modal</button> -->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838'>
                                {{tip|translate}}
                            </p></div>
                        </div>
                        <div class="modal-footer">
                            <!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>
    
</body>
<style>
    body .cooperation-manage .add-table .add-btn .add-iocn{
        background: url(../../../../../assets/images/btnIcons18.png) no-repeat;
		background-position: -107px 0px;
    }
</style>
</html>