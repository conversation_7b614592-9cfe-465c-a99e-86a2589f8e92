server:
  port:  19006

spring:
  config:
    import: optional:configserver:${CONFIG_SERVER_URL:http://localhost:8888}
  cloud:
    config:
      enabled: true
      fail-fast: false
      label: master
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        - id: ecy-sop-yz
          uri: lb://ecy-sop-yz
          predicates:
            - Path=/cy/ecy/sop-yz/**
        - id: ecy-sop-province
          uri: lb://ecy-sop-province
          predicates:
            - Path=/cy/ecy/sop-province/**
        - id: ecpm
          uri: lb://ecpm
          predicates:
            - Path=/ecpm/**
        - id: dsum
          uri: lb://dsum
          predicates:
            - Path=/dsum/**
        - id: ecpe
          uri: lb://ecpe
          predicates:
            - Path=/ecpe/**
        - id: see
          uri: lb://see
          predicates:
            - Path=/see/**
        - id: uop
          uri: lb://uop
          predicates:
            - Path=/uop/**
        - id: ioc
          uri: lb://ioc
          predicates:
            - Path=/ioc/**
        - id: ecpaxb
          uri: lb://ecpaxb
          predicates:
            - Path=/ecpaxb/**
      httpclient:
        connect-timeout: 60000
        response-timeout: 60000
  http:
    encoding:
      enabled: true
      charset: UTF-8
      force: true
    multipart:
      max-file-size: 50MB

hystrix:
  threadpool:
    default:
      coreSize: 500
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 200000
feign:
  hystrix:
    enabled: true
ribbon:
  #  MaxTotalConnections: 5000
  #  MaxConnectionsPerHost: 5000
  ReadTimeout: 80000
  SocketTimeout: 60000
  ConnectTimeout: 60000
  eureka:
    enabled: true
  httpclient:
    enabled: false # 默认开启需要禁用
  okhttp:
    enabled: true

ecy:
  login:
    secret: ITISAVERYSTRONGSECRET

fileupload:
  uploadOperations: captcha, uploadAuditFile, downloadTempFile, uploadCorpUser, downLoadNameListPic, uploadMemberInfo, exportBusiUser, exportCorpAliveUser, importBlackWhiteListMember, import, export,


eureka:
  client:
    region: region1
    availability-zones:
      region1: cy_eureka
    service-url:
      cy_eureka: http://127.0.0.1:19000/eureka/
  instance:
    metadata-map:
      cluster: main
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}