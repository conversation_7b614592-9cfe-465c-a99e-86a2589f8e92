<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DeliveryTargetMapper">
	<resultMap id="deliveryTargetWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DeliveryTargetWrapper">
		<result property="id" column="id" javaType="java.lang.Long" />
		<result property="contentID" column="contentID" javaType="java.lang.Long" />
		<result property="target" column="target" javaType="java.lang.String" />
		<result property="msgType" column="msgType" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
	</resultMap>

    <insert id="createDeliveryTarget">
		insert into
		ecpm_t_delivery_target
		(
		contentID,
		target,
		msgType,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime
		)
		values
		(
		#{contentID},
		#{target},
		#{msgType},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		sysdate()
		)

	</insert>
	
	<select id="getDeliveryTargetByID" resultMap="deliveryTargetWrapper">
		select id,
		contentID,
		target,
		msgtype,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime
		from ecpm_t_delivery_target
		where id = #{id}
	</select>

	<select id="queryDeliveryTarget" resultMap="deliveryTargetWrapper">
		select id,contentID,target,msgtype
		from ecpm_t_delivery_target
		<trim prefix="where" prefixOverrides="and|or">
			<if test="contentID != null  and contentID !='' ">
				and contentID = #{contentID}
			</if>
			<if test="target != null  and target !=''  ">
				and target = #{target}
			</if>
		</trim>
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryDeliveryTargetCount" resultType="java.lang.Integer">
		select count(1)
		from ecpm_t_delivery_target
		<trim prefix="where" prefixOverrides="and|or">
			<if test="contentID != null  and contentID !='' ">
				and contentID = #{contentID}
			</if>
			<if test="target != null  and target !=''  ">
				and target = #{target}
			</if>
		</trim>
	</select>

	<delete id="deleteDeliveryTargets" parameterType="java.util.List">
		delete from ecpm_t_delivery_target
		where contentID = #{contentID}
		<if test="targetIDList != null">
			and id in
			<foreach item="id" index="index" collection="targetIDList"
					 open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	</delete>

</mapper>