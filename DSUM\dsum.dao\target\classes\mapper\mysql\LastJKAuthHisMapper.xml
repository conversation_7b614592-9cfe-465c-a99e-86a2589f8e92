<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.LastJKAuthHisMapper">


    <insert id="insert">
	INSERT INTO dsum_t_jk_auth_his (
		`id`,
	    `accountID`,
	    `duration`,
	    `approver`,
	    `applyReason`,
	    `reuquestID`,
		`createTime`)
	VALUES (
		nextval('dsum_t_jk_auth_his'),
		#{accountID},
	 	#{duration},
	 	#{approver},
	    #{applyReason},
	    #{reuquestID},
	   	#{createTime}
	    )
	</insert>

    <!-- 根据用户名称和用户类型查询账号锁定信息 -->
    <select id="getJKAuthHisByAccountId" resultType="com.huawei.jaguar.dsum.dao.domain.LastJKAuthHisWrapper" parameterType="integer">
		select * from dsum_t_jk_auth_his
		where accountID = #{accountID} order by createTime DESC
	</select>

</mapper>