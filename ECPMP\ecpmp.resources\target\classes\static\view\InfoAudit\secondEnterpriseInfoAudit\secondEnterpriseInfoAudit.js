
var app = angular.module("cyApp",["util.ajax","page","top.menu","angularI18n","service.common"])

app.controller('infoAudit', function ($scope,$rootScope,$filter,$location,RestClientUtil,CommonUtils) {
	
	$scope.checkEmpty=function(){
		if(!$scope.checkDesc){
			if($scope.auditStatus==3){
				$("input[name='checkDesc']").attr('style','border-color:red');
			}else{
				$("input[name='checkDesc']").removeAttr("style");
			}
			$scope.isEmpty = true ;
			$scope.isFirstTime =false;
			$scope.isTooLong = false;
		}else{
			if($scope.checkDesc.length>1024){
				$("input[name='checkDesc']").attr('style','border-color:red');
				$scope.isTooLong = true;
			}else{
				$("input[name='checkDesc']").removeAttr("style");
				$scope.isTooLong = false;
			}
			$scope.isEmpty = false ;
			$scope.isFirstTime =false;
			
		}
      };  
	//驳回按钮
	  $scope.cz = function(item){
		  $("input[name='checkDesc']").removeAttr("style");
		  $scope.isFirstTime =true;
		  $scope.isEmpty = true;
		  $scope.isTooLong = false;
		  $scope.auditStatus=3;
		  if(item.auditStatus =='4'){
			  $scope.auditStatus=5;
		  }
		  $scope.checkDesc="";
          $('#impoMebrPop').modal();
          $scope.id=item.id;
          $scope.czItem =item;
          
      };  
      //通过按钮
      $scope.czs = function(item){ 
    	  $("input[name='checkDesc']").removeAttr("style");
    	  $scope.isEmpty = true;
      	  $scope.isFirstTime =true;
      	  $scope.isTooLong = false;
    	  $scope.auditStatus=2;
    	  $scope.checkDesc="";
          $('#impoMebrPop').modal();
          $scope.id=item.id;
          $scope.czItem =item;
      };  
      //跳转至企业详情页面
      $scope.toDetail=function(item){
          $.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
          $.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
          $.cookie("subEnterpriseID",item.id,{path:'/'});
          $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
          location.href='../../cooperationManage/agentManage/subEnterprise/createEnterprise/createEnterprise.html?operationType=audit';
      }
      
      $scope.picturePop= function(item,con){ 
    	  $scope.pictureUrl = "";
    	  $("#picUrlListPop").modal();
    	 if(con == "businessLicense"){
    		 $scope.pictureUrl = CommonUtils.formatPic(item.businessLicenseURL).review;
    	 }else if(con=="idCardPositive"){
    		 $scope.pictureUrl = CommonUtils.formatPic(item.idCardPositiveURL).review;
    	 }else{
    		 $scope.pictureUrl = CommonUtils.formatPic(item.idCardOppositeURL).review;
    	 }
      };
      
      
    //初始化参数
    $scope.init = function () {
    	$scope.isEmpty = false ;
    	$scope.isTooLong = false;
    	$scope.operatorID = $.cookie('accountID');
    	//初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        
      //搜索时初始化参数
        $scope.initSel = {
        	parentEnterpriseName:"",
            enterpriseName: "",//企业名称
        };
        var loginRoleType=$.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
        $scope.isZhike = (loginRoleType=='zhike');
        $scope.isAgent = (loginRoleType=='agent');
        $scope.isProvincial = (loginRoleType=='provincial');
        $scope.parentEnterpriseID ='';
        if($scope.isAgent){
        	$scope.parentEnterpriseID =$.cookie('enterpriseID');
        }
        $scope.statusMap={
                "1":"待审核",
                "2":"审核通过",
                "3":"驳回",
                "4":"变更审核中",
                "5":"变更驳回"
            };
        $scope.statusList = [
        					 {"name":"待审核","value":1},
        					 {"name":"审核通过","value":2},
        					 {"name":"驳回","value":3},
        					 {"name":"变更审核中","value":4},
        					 {"name":"变更驳回","value":5}
        					];
        
        
       
        $scope.queryEnterpriseList();
        $scope.enterpriseinfo();
    };
    
  
    //获取queryEnterpriseList查询商户接口的数据
    $scope.queryEnterpriseList = function (condition) {
    	 $scope.style={"opacity":"0.5", "cursor":"default"};
    	if(condition!='justPage'){
            var req = {
            		"enterpriseType":3,
                    "sortType":2,
                    "sortField":1,
                    "parentEnterpriseIDs":$scope.parentEnterpriseID?[$scope.parentEnterpriseID]:[],
                    "parentEnterpriseName":$scope.initSel.parentEnterpriseName,
                    "enterpriseName":$scope.initSel.enterpriseName,
                    "pageParameter": {
                        "pageNum": 1,
                        "pageSize":$scope.pageInfo[0].pageSize,
                        "isReturnTotal": "1"
                    }
            };
            if (!$scope.selectedStatus) {
            	delete(req.auditStatus);
            }
             else{
                req.auditStatus = $scope.selectedStatus
            }
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.pageParameter.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }

    
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                    	 $scope.enterpriseList = result.enterpriseList||[];
                         $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                         $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !==0 ?Math.ceil($scope.pageInfo[0].totalCount / parseInt($scope.pageInfo[0].pageSize)) :1;
                         angular.forEach($scope.enterpriseList, function (enterpriseInfo) { 
                        	 if(!enterpriseInfo.businessLicenseURL){
                        		 enterpriseInfo.style1=$scope.style;
                        	 }else{
                        		 enterpriseInfo.businessLicenseName =  
                        			 CommonUtils.formatPic(enterpriseInfo.businessLicenseURL).picName;
                        	 };
                        	 if(enterpriseInfo.extInfo){
                        		 if(!enterpriseInfo.extInfo.guaranteeLetterUrl){
                            		 enterpriseInfo.style2=$scope.style;
                                 }else{
                                	 enterpriseInfo.guaranteeLetterName =  
                                		 CommonUtils.formatPic(enterpriseInfo.extInfo.guaranteeLetterUrl).picName;
                            	 }
                        	 }else{
                        		 enterpriseInfo.style2=$scope.style;
                        	 }
                         });
                         
                    }else{
                        $scope.enterpriseList=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    };
    $scope.enterpriseinfo = function () 
    {
    	if (null == $scope.parentEnterpriseID) 
    	{
			return;
		}
    	var req = {
    	        "id": $scope.parentEnterpriseID,
    	        "pageParameter": {
    	          "pageNum": 1,
    	          "pageSize": 100,
    	          "isReturnTotal": "1"
    	        }
    	      }
    	      /*查询企业列表*/
    	      RestClientUtil.ajaxRequest({
    	        type: 'POST',
    	        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
    	        data: JSON.stringify(req),
    	        success: function (data) {
    	          $rootScope.$apply(function () {
    	            var result = data.result;
    	            if (result.resultCode == '**********') {
    	            	console.log(data.enterprise);
    	              $scope.enterpriseName = data.enterprise.enterpriseName;
    	              $scope.businessStatus = data.enterprise.businessStatus;
    	              $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
    	              console.log($scope.businessStatus);
    	              if ($scope.businessStatus == 1) {
    	            	  $('#Modalisaengt').modal();
    	              }
    	            }
    	          })
    	        }
    	      });
    };
    
  //获取updateEnterprise审批接口的数据
    $scope.checkMerchantInfo = function () {
    	if(!$scope.checkDesc&&$scope.auditStatus==3){
    		return;
    	}
    	var checkReq = {};
    	checkReq.enterprise = {};
    	checkReq.enterprise.id = $scope.id;
    	checkReq.enterprise.auditDesc = $scope.checkDesc;
    	checkReq.enterprise.auditStatus = $scope.auditStatus;
    	checkReq.enterprise.enterpriseType = 3;
    	checkReq.enterprise.operatorID = $scope.operatorID;
    	checkReq.enterprise.extInfo =$scope.czItem.extInfo;
    	checkReq.enterprise.organizationID =$scope.czItem.organizationID||'';
    	RestClientUtil.ajaxRequest({
    	      type: 'POST',
    	      url: "/ecpmp/ecpmpServices/enterpriseManageService/updateEnterprise",
    	      data: JSON.stringify(checkReq),
    	      success: function (data) {
    	        $rootScope.$apply(function () {
    	          var result = data.result;
    	          if (result.resultCode == '**********') {
    	        	  window.location.href='secondEnterpriseInfoAudit.html';
    	          }else {
    	              $scope.tip = result.resultCode;
                      $('#myModal').modal();
    	            }
    	        })
    	      },
    	      error:function(){
    	          $rootScope.$apply(function(){
    	              $scope.tip='1030120500';
                      $('#myModal').modal();
    	              }
    	          )
    	      }
    	    });
    };
    $scope.exportFile = function (downloadUrl) {
  	  var req = {
  			   "param":{
  				   "path": downloadUrl,
  	                "token": $.cookie("token"),
  	                "isExport": 0
  			   },
  		       "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
  		       "method":"get"
  		     }
  		     CommonUtils.exportFile(req);
    };
	$scope.toBussiness=function(item,isBussiness){
		$.cookie("enterpriseID",item.parentEnterpriseID,{path:'/'});
		$.cookie("enterpriseName",item.parentEnterpriseName,{path:'/'});
		$.cookie("subEnterpriseID",item.id,{path:'/'});
		$.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
		if($scope.isAgent&&!isBussiness){
			if(item.auditStatus==1 || item.auditStatus==4){
				location.href='../../cooperationManage/agentManage/subEnterprise/createEnterprise/createEnterprise.html?operationType=detail';
			}else{
				location.href='../../cooperationManage/agentManage/subEnterprise/createEnterprise/createEnterprise.html?operationType=edit&enterType=1';
			}
		}else{
			// location.href='../secLevelBusinessSetting/businessSetting.html';
			location.href='../cooperationManage/zhikeManage/businessSetting/businessSetting.html';
		}
	}
})
