<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.CityMapper">
    <resultMap id="cityWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.CityWrapper">       
        <result property="sid" column="sid" javaType="java.lang.Integer" />
        <result property="cityCode" column="cityCode" javaType="java.lang.String" />
        <result property="cityName" column="cityName" javaType="java.lang.String" />
        <result property="provinceCode" column="provinceCode" javaType="java.lang.String" />
        <result property="provinceName" column="provinceName" javaType="java.lang.String" />
    </resultMap>
    
    <select id="countCity" resultType="java.lang.Integer">
        select 
        count(1)
        from ecpm_t_city
        <trim prefix="where" prefixOverrides="and|or">
			<if test="cityCode != null">
				cityCode=#{cityCode}
			</if>
		</trim>
    </select>
    
    <insert id="insertCity">
		INSERT INTO ecpm_t_city
		(
		sid,
        cityCode,
        cityName,
        provinceCode,
     	provinceName
		)
		VALUES
		(
		#{sid},
		#{cityCode},
		#{cityName},
		#{provinceCode},
		#{provinceName}
		)
	</insert>
    
    <select id="queryCityByCode" resultMap="cityWrapper">
		select 
		sid,
		cityCode,
		cityName,
		provinceCode,
		provinceName
		from ecpm_t_city
		where cityCode = #{cityCode}
	</select>
</mapper>