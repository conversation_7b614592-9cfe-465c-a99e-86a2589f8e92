<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.OrgServiceStatusMapper">

    <select id="getCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ecpm_t_org_service_status
        WHERE orgID = #{orgID}
        <if test="subServType!=null and subServType!='' and subServType != 1 and subServType != 2 ">
            AND subServType = #{subServType}
        </if>
        <if test="subServType!=null and subServType!='' and (subServType == 1 or subServType == 2 )">
            AND subServType in (#{subServType},3)
        </if>
        AND status = #{status}
    </select>

    <select id="getCountServiceStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ecpm_t_org_service_status
        WHERE orgID = #{orgID}
          AND servType = #{servType}
          AND subServType = #{subServType}
          AND status = #{status}
    </select>
    <insert id="insertOrgServiceStatus" useGeneratedKeys="true" keyProperty="ID">
        INSERT INTO ecpm_t_org_service_status (orgID, servType, subServType, status, createTime, updateTime)
        VALUES (#{orgID}, #{servType}, #{subServType}, #{status}, #{createTime}, #{updateTime})
    </insert>

    <select id="selectByOrgServTypeAndSubServType" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgServiceStatus">
        SELECT * FROM ecpm_t_org_service_status
        WHERE orgID = #{orgID}
          AND servType = #{servType}
          AND subServType = #{subServType}
    </select>
    <update id="updateStatusByOrgServTypeAndSubServType">
        UPDATE ecpm_t_org_service_status
        SET status = #{status}, updateTime = NOW()
        WHERE orgID = #{orgID}
          AND servType = #{servType}
          AND subServType = #{subServType}
    </update>
</mapper>