<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.EnterpriseReportMapper">
    <resultMap id="enterpriseReportWrapper" type="com.huawei.jaguar.dsum.dao.domain.EnterpriseReportWrapper">
        <result property="id" column="ID" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="operType" column="operType" />
		<result property="operTime" column="operTime" />
        <result property="enterpriseCode" column="enterpriseCode" />
        <result property="enterpriseName" column="enterpriseName" />
        <result property="enterpriseDesc" column="enterpriseDesc" />
        <result property="enterpriseType" column="enterpriseType" />
        <result property="organizationID" column="organizationID" />
        <result property="custID" column="custID" />
        <result property="businessLicenseID" column="businessLicenseID" />
        <result property="businessLicenseURL" column="businessLicenseURL" />
        <result property="idCardPositiveURL" column="IDCardPositiveURL" />
        <result property="idCardOppositeURL" column="IDCardOppositeURL" />
		<result property="auditStatus" column="auditStatus" />
		<result property="auditDesc" column="auditDesc" />
		<result property="msisdn" column="msisdn" />
		<result property="parentEnterpriseID" column="parentEnterpriseID" />
		<result property="provinceID" column="provinceID" />
		<result property="cityID" column="cityID" />
        <result property="contract" column="contract" />
		<result property="status" column="status" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="reserved5" column="reserved5" />
		<result property="reserved6" column="reserved6" />
		<result property="reserved7" column="reserved7" />
		<result property="reserved8" column="reserved8" />
		<result property="reserved9" column="reserved9" />
		<result property="reserved10" column="reserved10"/>
        <result property="parentEnterpriseName" column="parentEnterpriseName" />
		<result property="countyID" column="countyID"/>
    </resultMap>


    <insert id="insertEnterpriseReport">
		INSERT INTO dsum_t_enterprise_report
		(
		enterpriseID,
		operType,
		operTime,
		enterpriseCode,
		enterpriseName,
		enterpriseDesc,
		enterpriseType,
		organizationID,
		custID,
		businessLicenseID,
		businessLicenseURL,
		idCardPositiveURL,
		idCardOppositeURL,
		auditStatus,
		auditDesc,
		msisdn,
		parentEnterpriseID,
		provinceID,
		cityID,
		contract,
		status,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		parentEnterpriseName,
		countyID
		)
		VALUES
		<foreach collection="list" item="enterpriseWrapper"
				 index="index" separator="," >
		(
		#{enterpriseWrapper.id},
		3,
		now(),
		#{enterpriseWrapper.enterpriseCode},
		#{enterpriseWrapper.enterpriseName},
		#{enterpriseWrapper.enterpriseDesc},
		#{enterpriseWrapper.enterpriseType},
		#{enterpriseWrapper.organizationID},
		#{enterpriseWrapper.custID},
		#{enterpriseWrapper.businessLicenseID},
		#{enterpriseWrapper.businessLicenseURL},
		#{enterpriseWrapper.idCardPositiveURL},
		#{enterpriseWrapper.idCardOppositeURL},
		#{enterpriseWrapper.auditStatus},
		#{enterpriseWrapper.auditDesc},
		#{enterpriseWrapper.msisdn},
		#{enterpriseWrapper.parentEnterpriseID},
		#{enterpriseWrapper.provinceID},
		#{enterpriseWrapper.cityID},
		#{enterpriseWrapper.contract},
		#{enterpriseWrapper.status},
		#{enterpriseWrapper.extInfo},
		#{enterpriseWrapper.reserved1},
		#{enterpriseWrapper.reserved2},
		#{enterpriseWrapper.reserved3},
		#{enterpriseWrapper.reserved4},
		#{enterpriseWrapper.reserved5},
		#{enterpriseWrapper.reserved6},
		#{enterpriseWrapper.reserved7},
		#{enterpriseWrapper.reserved8},
		#{enterpriseWrapper.reserved9},
		#{enterpriseWrapper.reserved10},
		#{enterpriseWrapper.parentEnterpriseName},
        #{enterpriseWrapper.countyID}
		)
		</foreach>
	</insert>


	<insert id="batchInsertIntoEnterpriseReportByStatus">
		INSERT INTO dsum_t_enterprise_report
		( enterpriseID, operType, operTime, enterpriseCode, enterpriseName, enterpriseDesc, enterpriseType,
		organizationID, custID, businessLicenseID, businessLicenseURL, idCardPositiveURL, idCardOppositeURL,
		auditStatus, auditDesc, msisdn, parentEnterpriseID, provinceID, cityID, contract, status, extInfo,
		reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9,
		reserved10, parentEnterpriseName, countyID)
		SELECT
		    t.ID as enterpriseID,
			(CASE WHEN (auditStatus is null or auditStatus = 2) THEN 3 END) as operType,
			now() as operTime,
			t.enterpriseCode,
			t.enterpriseName,
			t.enterpriseDesc,
			t.enterpriseType,
			t.organizationID,
			t.custID,
			t.businessLicenseID,
			t.businessLicenseURL,
			t.idCardPositiveURL,
			t.idCardOppositeURL,
			t.auditStatus,
			t.auditDesc,
			t.msisdn,
			t.parentEnterpriseID,
			t.provinceID,
			t.cityID,
			t.contract,
			t.status,
			t.extInfo,
			t.reserved1,
			t.reserved2,
			t.reserved3,
			t.reserved4,
			t.reserved5,
			t.reserved6,
			t.reserved7,
			t.reserved8,
			t.reserved9,
			t.reserved10,
			t.parentEnterpriseName,
			t.countyID
		FROM dsum_t_enterprise t where t.ID = #{id};
	</insert>

	<insert id="batchInsertIntoEnterpriseReportByOldAuditStatus">
		INSERT INTO dsum_t_enterprise_report
		( enterpriseID, operType, operTime, enterpriseCode, enterpriseName, enterpriseDesc, enterpriseType,
		  organizationID, custID, businessLicenseID, businessLicenseURL, idCardPositiveURL, idCardOppositeURL,
		  auditStatus, auditDesc, msisdn, parentEnterpriseID, provinceID, cityID, contract, status, extInfo,
		  reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9,
		  reserved10, parentEnterpriseName, countyID)
		SELECT
			t.ID as enterpriseID,
			(CASE WHEN (#{oldAuditStatus} is null or #{oldAuditStatus} = 2) THEN 3 END) as operType,
			now() as operTime,
			t.enterpriseCode,
			t.enterpriseName,
			t.enterpriseDesc,
			t.enterpriseType,
			t.organizationID,
			t.custID,
			t.businessLicenseID,
			t.businessLicenseURL,
			t.idCardPositiveURL,
			t.idCardOppositeURL,
			t.auditStatus,
			t.auditDesc,
			t.msisdn,
			t.parentEnterpriseID,
			t.provinceID,
			t.cityID,
			t.contract,
			t.status,
			t.extInfo,
			t.reserved1,
			t.reserved2,
			t.reserved3,
			t.reserved4,
			t.reserved5,
			t.reserved6,
			t.reserved7,
			t.reserved8,
			t.reserved9,
			t.reserved10,
			t.parentEnterpriseName,
			t.countyID
		FROM dsum_t_enterprise t where t.ID = #{id};
	</insert>

	<insert id="batchInsertIntoEnterpriseReportByAuditStatus">
		INSERT INTO dsum_t_enterprise_report
		( enterpriseID, operType, operTime, enterpriseCode, enterpriseName, enterpriseDesc, enterpriseType,
		organizationID, custID, businessLicenseID, businessLicenseURL, idCardPositiveURL, idCardOppositeURL,
		auditStatus, auditDesc, msisdn, parentEnterpriseID, provinceID, cityID, contract, status, extInfo,
		reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9,
		reserved10, parentEnterpriseName, countyID)
		SELECT
		t.ID as enterpriseID,
		<if test="auditStatus == 2">
			(CASE WHEN (auditStatus = 1) THEN 1 ELSE 2 END) as operType,
		</if>
		<if test="auditStatus != 2">
			2 as operType,
		</if>
		now() as operTime,
		t.enterpriseCode,
		t.enterpriseName,
		t.enterpriseDesc,
		t.enterpriseType,
		t.organizationID,
		t.custID,
		t.businessLicenseID,
		t.businessLicenseURL,
		t.idCardPositiveURL,
		t.idCardOppositeURL,
		t.auditStatus,
		t.auditDesc,
		t.msisdn,
		t.parentEnterpriseID,
		t.provinceID,
		t.cityID,
		t.contract,
		t.status,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10,
		t.parentEnterpriseName,
		t.countyID
		FROM dsum_t_enterprise t where t.ID = #{id};
	</insert>

	<insert id="batchInsertIntoEnterpriseReportByOldAuditStatusAndAuditStatus">
		INSERT INTO dsum_t_enterprise_report
		( enterpriseID, operType, operTime, enterpriseCode, enterpriseName, enterpriseDesc, enterpriseType,
		organizationID, custID, businessLicenseID, businessLicenseURL, idCardPositiveURL, idCardOppositeURL,
		auditStatus, auditDesc, msisdn, parentEnterpriseID, provinceID, cityID, contract, status, extInfo,
		reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9,
		reserved10, parentEnterpriseName, countyID)
		SELECT
		t.ID as enterpriseID,
		<if test="auditStatus == 2">
			(CASE WHEN (#{oldAuditStatus} is not null and #{oldAuditStatus} = 1) THEN 1 ELSE 2 END) as operType,
		</if>
		<if test="auditStatus != 2">
			2 as operType,
		</if>
		now() as operTime,
		t.enterpriseCode,
		t.enterpriseName,
		t.enterpriseDesc,
		t.enterpriseType,
		t.organizationID,
		t.custID,
		t.businessLicenseID,
		t.businessLicenseURL,
		t.idCardPositiveURL,
		t.idCardOppositeURL,
		t.auditStatus,
		t.auditDesc,
		t.msisdn,
		t.parentEnterpriseID,
		t.provinceID,
		t.cityID,
		t.contract,
		t.status,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10,
		t.parentEnterpriseName,
		t.countyID
		FROM dsum_t_enterprise t where t.ID = #{id};
	</insert>

	<insert id="batchInsertIntoEnterpriseReport">
		INSERT INTO dsum_t_enterprise_report
		( enterpriseID, operType, operTime, enterpriseCode, enterpriseName, enterpriseDesc, enterpriseType,
		  organizationID, custID, businessLicenseID, businessLicenseURL, idCardPositiveURL, idCardOppositeURL,
		  auditStatus, auditDesc, msisdn, parentEnterpriseID, provinceID, cityID, contract, status, extInfo,
		  reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9,
		  reserved10, parentEnterpriseName, countyID)
		SELECT
		t.ID as enterpriseID,
		1 as operType,
		now() as operTime,
		t.enterpriseCode,
		t.enterpriseName,
		t.enterpriseDesc,
		t.enterpriseType,
		t.organizationID,
		t.custID,
		t.businessLicenseID,
		t.businessLicenseURL,
		t.idCardPositiveURL,
		t.idCardOppositeURL,
		t.auditStatus,
		t.auditDesc,
		t.msisdn,
		t.parentEnterpriseID,
		t.provinceID,
		t.cityID,
		t.contract,
		t.status,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10,
		t.parentEnterpriseName,
		t.countyID
		FROM dsum_t_enterprise t where t.ID = #{id} and (t.auditStatus is null or t.auditStatus = 2);
	</insert>


	<insert id="batchInsertIntoEnterpriseReportByParentEnterpriseID">
		INSERT INTO dsum_t_enterprise_report
		( enterpriseID, operType, operTime, enterpriseCode, enterpriseName, enterpriseDesc, enterpriseType,
		  organizationID, custID, businessLicenseID, businessLicenseURL, idCardPositiveURL, idCardOppositeURL,
		  auditStatus, auditDesc, msisdn, parentEnterpriseID, provinceID, cityID, contract, status, extInfo,
		  reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9,
		  reserved10, parentEnterpriseName, countyID)
		SELECT
			IFNULL(t.reserved3,t.ID) as enterpriseID,
			2 as operType,
			now() as operTime,
			t.enterpriseCode,
			t.enterpriseName,
			t.enterpriseDesc,
			t.enterpriseType,
			t.organizationID,
			t.custID,
			t.businessLicenseID,
			t.businessLicenseURL,
			t.idCardPositiveURL,
			t.idCardOppositeURL,
			t.auditStatus,
			t.auditDesc,
			t.msisdn,
			t.parentEnterpriseID,
			t.provinceID,
			t.cityID,
			t.contract,
			t.status,
			t.extInfo,
			t.reserved1,
			t.reserved2,
			t.reserved3,
			t.reserved4,
			t.reserved5,
			t.reserved6,
			t.reserved7,
			t.reserved8,
			t.reserved9,
			t.reserved10,
			t.parentEnterpriseName,
			t.countyID
		FROM dsum_t_enterprise t where t.parentEnterpriseID = #{id} and t.auditStatus = 2 and t.status != 2;
	</insert>

</mapper>