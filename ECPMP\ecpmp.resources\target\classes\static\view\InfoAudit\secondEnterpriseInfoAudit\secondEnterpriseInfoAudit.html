<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>查看活动</title>

	<link href="../../../css/bootstrap.min.css" rel="stylesheet"/>
	<link href="../../../css/mian.css" rel="stylesheet"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>

	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="secondEnterpriseInfoAudit.js"></script>
	
    <link rel="stylesheet" type="text/css" href="../../../directives/preview/preview.css" />

	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<link href="../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>

	<style>
		.li-disabled{
			opacity: 0.5;
			cursor: not-allowed!important;
		}
	</style>
</head>

<body ng-app="cyApp" ng-controller="infoAudit" class="body-min-width">
	<div ng-init="init();">
<div ng-show="businessStatus !=1">
		<div class="cooperation-manage">
			<div class="cooperation-head">
				<span class="frist-tab" ng-bind="'MENU_INFOAUDIT'|translate" ></span>
				&nbsp;&gt;&nbsp;
				<span class="second-tab" ng-bind="'MENU_SUBENTERPRISEAUDIT'|translate" ng-show="isSuperManager"></span>
				<span class="second-tab" ng-bind="'MENU_SUBENTERPRISEAUDIT'|translate" ng-show="isAgent"></span>
			</div>
			<div class="cooperation-search">
				<form class="form-horizontal">
					<div class="form-group">
						<label for="parentEnterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_AGENTNAME'|translate" ng-show="isSuperManager">
						</label>
						<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" ng-show="isSuperManager" style="min-width: 170px">
							<input type="text" id="parentEnterpriseName" class="form-control" 
								placeholder="{{'ENTERPRISE_PLEASEINPUTAGENTNAME'|translate}}" 
								ng-model="initSel.parentEnterpriseName">
						</div>
						
						<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
									 style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate">
						</label>
						<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" style="min-width: 170px">
							<input type="text" id="enterpriseName" class="form-control" 
								placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME'|translate}}" 
								ng-model="initSel.enterpriseName">
						</div>
						
						<label for="exampleInputName2" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
									 style="white-space:nowrap" ng-bind="'GROUP_STATUS'|translate"></label>
						<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
							<select class="form-control" name="status" ng-model="selectedStatus"
											ng-options="x.value as x.name for x in statusList">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
							</select>
						</div>
						
						<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
							<button type="submit" class="btn search-btn" ng-click="queryEnterpriseList()" style="float: right">
								<icon class="search-iocn"></icon>
								{{"COMMON_SEARCH"|translate}}
							</button>
						</div>
					</div>
				</form>
			</div>


			<div class="coorPeration-table">
				<table class="table table-striped table-hover" >
					<thead>
					<tr>
						<th style="width:9%" ng-bind="'ENTERPRISE_AGENTID2'|translate"></th>
						<th style="width:9%" ng-bind="'ENTERPRISE_AGENTNAME'|translate"></th>
						<th style="width:9%" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></th>
						<th style="width:9%" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></th>
						<th style="width:10%" ng-bind="'ENTERPRISE_ORGANIZATIONID'|translate"></th>
						<th style="width:8%" ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></th>
						<th style="width:9%" ng-bind="'ENTERPRISE_COMMITMENT'|translate"></th>
						<th style="width:8%" ng-bind="'GROUP_STATUS'|translate"></th>
						<th style="width:21%" ng-bind="'COMMON_OPERATE'|translate"></th>
					</tr>
					</thead>
					<tbody>
					<tr ng-repeat="item in enterpriseList">
						<td title="{{item.parentEnterpriseID}}">{{item.parentEnterpriseID}}</td>
						<td title="{{item.parentEnterpriseName}}">{{item.parentEnterpriseName}}</td>
						<td title="{{item.id}}">{{item.id}}</td>
						<td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
						<td title="{{item.organizationID}}">{{item.organizationID}}</td>
						<td>
							<a ng-click="item.businessLicenseURL?exportFile(item.businessLicenseURL):false" 
								title="{{item.businessLicenseName}}" ng-style="{{item.style1}}" 
								ng-bind="'COMMON_FILEDOWNLOAD'|translate">
							</a>
						</td>
						<td>
							<a ng-click="item.extInfo.guaranteeLetterUrl?exportFile(item.extInfo.guaranteeLetterUrl):false" 
								title="{{item.guaranteeLetterName}}" ng-style="{{item.style2}}" 
								ng-bind="'COMMON_FILEDOWNLOAD'|translate">
							</a>
						</td>
						<td title="{{statusMap[item.auditStatus]}}"><span class="ec_status">{{statusMap[item.auditStatus]}}</span></td>
						<td>
							<div class="handle">
								<ul>
									<li ng-show="!isSuperManager" ng-click="(item.auditStatus!==1 && item.auditStatus!==4 && !isSuperManager)?toBussiness(item):''" class="edit" style="margin-right:10px"
										ng-class="{true:'',false:'li-disabled'}[item.auditStatus!==1 && item.auditStatus!==4 && !isSuperManager]">
										<span ng-bind="'COMMON_UPDATE'|translate"></span>
									</li>

									<li ng-click="czs(item)" class="c_green" style="margin-right:10px" 
											ng-show="isSuperManager && (item.auditStatus==1 || item.auditStatus==4)">
										<icon class="past-icon"></icon>
										<span ng-bind="'COMMON_PASS'|translate"></span>
									</li>
									<li ng-click="cz(item)" class="c_red" style="margin-right:10px" 
											ng-show="isSuperManager && (item.auditStatus==1 || item.auditStatus==4)">
										<icon class="reject-icon"></icon>
										<span ng-bind="'COMMON_REJECT'|translate"></span>
									</li>
									<li class="query" style="margin-right:10px" ng-click="toDetail(item)">
										<icon class="query-icon"></icon>
										<span ng-bind="'COMMON_WATCH'|translate"></span>
									</li>
								</ul>
							</div>
						</td>
					</tr>

					<tr ng-show="enterpriseList.length<=0">
					    <td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
				    </tr>
					</tbody>
				</table>
			</div>
			
			<div>
				<ptl-page tableId="0" change="queryEnterpriseList('justPage')"></ptl-page>
			</div>
		</div>

		<!--查看图片弹出框-->
		<div class="modal fade bs-example-modal-sm" id="picUrlListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-sm model-lg model-md" role="document" style="width:40%">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_PREVIEW'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="img-wrap" >
                            <img  ng-src="{{pictureUrl}}" alt="加载失败">
                        </div>
                        <p ng-show="!pictureUrl" ng-bind="'COMMON_NOPICTURE'|translate"></p>
                    </div>
                    <div class="modal-footer" style="text-align:center">
                        <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_BACK'|translate"></button>
                    </div>
                </div>
            </div>
        </div>
		
		
		<!--审批弹出框-->
		<div class="modal fade" id="impoMebrPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_AUDIT'|translate"></h4>
					</div>

					<div class="modal-body">
						<form class="form-horizontal" name='myForm' novalidate>
							<div class="form-group">
								<label class="col-sm-3 control-label" ng-bind="'COMMON_AUDITDESC'|translate">:</label>

								<div class="cond-div col-sm-6">
									<input type="text" class="form-control" 
										placeholder="{{'COMMON_INPUTAUDITDESC'|translate}}"
										name="checkDesc" ng-model="checkDesc"
										ng-change="checkEmpty()" required/>
								</div>
								<div class="cond-div col-sm-3">
									<span id="info" style="color:red" ng-show="isEmpty&&auditStatus==3&&!isFirstTime">
										<span ng-bind="'COMMON_AUDITDESCDESC1'|translate"></span>
									</span>
									<span id="info" style="color:red" ng-show="isTooLong">
										<span ng-bind="'COMMON_AUDITDESCDESC2'|translate"></span>
									</span>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align: center!important;">
						<button class="btn bg_purple" ng-click="checkMerchantInfo()" type="submit"
										ng-disabled="((isFirstTime||isEmpty)&&(auditStatus==3||auditStatus==5))||isTooLong" ng-bind="'COMMON_OK'|translate">
						</button>
						<button class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
					</div>
				</div>
			</div>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
</div>
<!-- 业务未开通 -->
	<div ng-show="businessStatus ==1" class="modal fade bs-example-modal-sm" id="Modalisaengt" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" >
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 18px;color:#383838'>
							业务未开通
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	</div>

</body>
</html>