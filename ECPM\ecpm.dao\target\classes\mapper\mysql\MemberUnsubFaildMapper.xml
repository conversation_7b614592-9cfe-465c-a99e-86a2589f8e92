<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberUnsubFaildMapper">
    <insert id="insertMemberUnsubFaildListRecords">

    </insert>
    <insert id="insertMemberUnsubFaildRecords">
        INSERT INTO ecpm_t_member_unsub_faild
            (enterpriseID,
             msisdn,
             orgID,
             orgName,
             orgType,
             createTime,
             errCode,
             errDesc,
             batchNo)
        VALUES
            (#{memberUnsubFaild.enterpriseID},
             #{memberUnsubFaild.msisdn},
             #{memberUnsubFaild.orgID},
             #{memberUnsubFaild.orgName},
             #{memberUnsubFaild.orgType},
             #{memberUnsubFaild.createTime},
             #{memberUnsubFaild.errCode},
             #{memberUnsubFaild.errDesc},
             #{memberUnsubFaild.batchNo})
    </insert>
    <select id="queryUnsubFaildBatch" resultType="com.huawei.jaguar.dsdp.ecpm.model.UnsubFailBatch">
        SELECT orgID, orgName, batchNo, MIN(DATE_FORMAT(createTime,'%Y-%m-%d %T')) AS createTime, COUNT(*) AS faildCount
        FROM ecpm_t_member_unsub_faild
        WHERE enterpriseID = #{req.enterpriseID}
        AND orgType = #{req.orgType}
        <if test="req.orgName != null and req.orgName != ''">
            AND orgName LIKE CONCAT('%', #{req.orgName}, '%')
        </if>
        <if test="req.createBeginTime != null and req.createBeginTime != ''">
            AND createTime <![CDATA[>=]]> STR_TO_DATE(#{req.createBeginTime}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.createEndTime != null and req.createEndTime != ''">
            AND createTime <![CDATA[<=]]> STR_TO_DATE(#{req.createEndTime}, '%Y%m%d%H%i%s')
        </if>
        GROUP BY orgID, orgType, orgName, batchNo
        ORDER BY createTime
        LIMIT #{pageStart},#{pageSize}
    </select>

    <select id="queryUnsubFaildBatchTotal" resultType="java.lang.Long">
        SELECT COUNT(*) AS totalRows
        FROM (
        SELECT orgID, orgName, batchNo, MIN(createTime) AS createTime, COUNT(*) AS faildCount
        FROM ecpm_t_member_unsub_faild
        WHERE enterpriseID = #{enterpriseID}
        AND orgType = #{orgType}
        <if test="orgName != null and orgName != ''">
            AND orgName LIKE CONCAT('%', #{orgName}, '%')
        </if>
        <if test="createBeginTime != null and createBeginTime!=''">
            AND createTime <![CDATA[>=]]> STR_TO_DATE(#{createBeginTime}, '%Y%m%d%H%i%s')
        </if>
        <if test="createEndTime != null and createEndTime!=''">
            AND createTime <![CDATA[<=]]> STR_TO_DATE(#{createEndTime}, '%Y%m%d%H%i%s')
        </if>
        GROUP BY orgID, orgType, orgName, batchNo
        ORDER BY createTime DESC
        ) AS subquery;
    </select>

    <select id="queryMemberUnsubFaild" resultType="com.huawei.jaguar.dsdp.ecpm.model.MemberUnsubFaild">
        SELECT enterpriseID,
               msisdn,
               orgID,
               orgName,
               orgType,
               DATE_FORMAT(createTime,'%Y-%m-%d %T'),
               errCode,
               errDesc,
               batchNo
        FROM ecpm_t_member_unsub_faild
        WHERE batchNo = #{req.batchNo}
        ORDER BY createTime DESC
        LIMIT #{pageStart},#{pageSize}
    </select>
    <select id="queryMemberUnsubFaildTotal" resultType="java.lang.Long">
        SELECT COUNT(*) AS totalRows
        FROM (
                 SELECT *
                 FROM ecpm_t_member_unsub_faild
                 WHERE batchNo = #{batchNo}
             ) AS subquery;
    </select>


</mapper>