<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
<link href="../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../css/mian.css" rel="stylesheet"/>
<link href="../../../../css/datepicker3.css" rel="stylesheet"/>
<link href="../../../../css/layout.css" rel="stylesheet"/>
<link href="../../../../css/searchList.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
<link rel="stylesheet" href="../../../../css/font-awesome.min.css"/>
<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../directives/page/page.js"></script>
<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<link href="../../../../css/statistics.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="secondDayStatistics.js"></script>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
	font-weight: normal;
	white-space: nowrap;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
</style>

</head>
<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head"><span class="frist-tab" ng-bind="'DATA_STATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'SECOND_DAYSTATISTICS'|translate"></span></div>
		<top:menu ng-show="!isSubEnterpirse" chose-index="1" page-url="/qycy/ecpmp/view/InfoStatistics/secondStatistics" list-index="43"></top:menu>
			<form class="form-horizontal">
				<div class="form-group">
					<label ng-show="!isSubEnterpirse" for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></label>
					<div ng-show="!isSubEnterpirse" class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
						<input type="text" autocomplete="off" class="form-control" id="enterpriseName" placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"   ng-model="enterpriseName">
					</div>

					<label for="serviceType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
						<select class="form-control" name="serviceType" ng-model="serviceType" ng-options="x.id as x.name for x in serviceTypeChoise">
						</select>
					</div>

					<label class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></label>
					<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 time">
						<div class="input-daterange input-group" id="datepicker">
							<input type="text" class="input-md form-control" autocomplete="off" id="start" ng-keyup="searchOn()"/>
							<span class="input-group-addon" ng-bind="'TO'|translate"></span>
							<input type="text" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
						</div>
					</div>

					<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
							<button ng-click="queryEnterpriseStatInfo()" type="submit" class="btn search-btn" ng-disabled="initSel.search" ><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
				</div>
			</form>

			<div class="add-table">
				<button id="exportSpokesList" class="btn add-btn" ng-click="exportFile()">
					<icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span>
				</button>
			</div>

			<div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-bind="'DAYSTATISTICSREPORT'|translate"></div>
			<div class="coorPeration-table">
					<table class="table table-striped table-hover">
						<thead>
							<tr>
								<th style="width:6%" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></th>
								<th style="width:6%" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></th>
								<th style="width:6%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
								<th style="width:6%" ng-bind="'SUBSERVTYPE'|translate"></th>
								<th style="width:6%" ng-bind="'CHARGETYPE'|translate"></th>
								<th style="width:6%" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></th>
								<th style="width:8%" ng-bind="'MEMBER_COUNT'|translate"></th>								
								<th style="width:6%" ng-bind="'AMOUNT'|translate"></th>
								<th style="width:6%" ng-bind="'CMCC_USE'|translate"></th>
								<th style="width:6%" ng-bind="'CUCC_USE'|translate"></th>
								<th style="width:6%" ng-bind="'CTCC_USE'|translate"></th>

							</tr>
						</thead>
						<tbody>
								<tr ng-repeat="item in StatInfoListData">
									<td><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
									<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
									<td><span title="{{getServiceType(item.serviceType)}}">{{getServiceType(item.serviceType)}}</span></td> 
									<td><span title="{{getSubServType(item.subServType, item.hangupType)}}">{{getSubServType(item.subServType, item.hangupType)}}</span></td>
									<td><span title="{{getChargeType(item.chargeType)}}">{{getChargeType(item.chargeType)}}</span></td>
									<td><span title="{{getTime(item.statMonth)}}">{{getTime(item.statMonth)}}</span></td>
									<td><span title="{{item.memberCount}}">{{item.memberCount}}</span></td>																		
									<td><span title="{{item.useCount}}">{{item.useCount}}</span></td>
									<td><span title="{{item.useCountMobile}}">{{item.useCountMobile}}</span></td>
									<td><span title="{{item.useCountUnicom}}">{{item.useCountUnicom}}</span></td>
									<td><span title="{{item.useCountTelecom}}">{{item.useCountTelecom}}</span></td>
								</tr>
								<tr ng-show="StatInfoListData.length<=0">
									<td style="text-align:center" colspan="10" ng-bind="'COMMON_NODATA'|translate"></td>
								</tr>
							</tbody>
					</table>
				</div>

				<div>
					<ptl-page tableId="0" change="queryEnterpriseStatInfo('justPage')"></ptl-page>
				</div>
		</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>