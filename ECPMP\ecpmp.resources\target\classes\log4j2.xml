<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <properties>
        <!-- 文件输出格式 -->
        <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>
    </properties>

    <appenders>
        <RollingFile name="STD_OUT_REDIRECTOR" filePattern="${sys:app.home}/logs/server/server.out.%i" fileName="${sys:app.home}/logs/server/server.out">
            <PatternLayout pattern="%m %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="DEBUG_LOG" filePattern="${sys:app.home}/logs/debug/ecpmp_debug.log.%i" fileName="${sys:app.home}/logs/debug/ecpmp_debug.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%traceid|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <RollingFile name="SERVICE_LOG" filePattern="${sys:app.home}/logs/service/ecpmp_service.log.%i" fileName="${sys:app.home}/logs/service/ecpmp_service.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%traceid|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <Console name="CONSOLE" target="system_out">
            <PatternLayout pattern="${PATTERN}"/>
        </Console>
    </appenders>

    <loggers>

        <logger name="com.huawei.ecpmp" additivity="false" level="debug">
            <appender-ref ref="DEBUG_LOG"/>
            <appender-ref ref="CONSOLE"/>
            <appender-ref ref="STD_OUT_REDIRECTOR"/>
        </logger>
        
        <logger name="Runtime@service" additivity="false" level="info">
            <appender-ref ref="SERVICE_LOG"/>
        </logger>

        <root level="info">
            <appender-ref ref="STD_OUT_REDIRECTOR"/>
            <appenderref ref="CONSOLE"/>
        </root>

    </loggers>

</configuration>


