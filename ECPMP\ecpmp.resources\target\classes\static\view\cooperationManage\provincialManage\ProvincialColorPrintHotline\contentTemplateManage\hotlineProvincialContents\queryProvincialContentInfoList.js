var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n", "service.common","ngSanitize"])
app.controller('orderListController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.CommonUtils = CommonUtils;
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        $scope.memberListData = [];
        $scope.item = null;

        var loginRoleType = $.cookie('loginRoleType');
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isProvincial = (loginRoleType == 'provincial');
        $scope.isZYZQ = $.cookie('reserved10') == "111";
        $scope.enterpriseType=$.cookie('enterpriseType');
          //中移政企热线开关
        $scope.reserved4 = $.cookie('reserved4')!=null&&$.cookie('reserved4').indexOf("2")!=-1;
        $scope.choseIndex = 4;

        if ($scope.enterpriseType =='5' && $scope.isSuperManager)
        {
        	var proSupServerType = $.cookie('proSupServerType');
            $scope.proSupServerType = $.cookie('proSupServerType');
            if (proSupServerType)
            {
                var value = JSON.parse(proSupServerType);
                for (var i = 0; i < value.length; i++) {
    	            var index = value[i];
    	            if (index == 40)
    	            {
    	            	$scope.choseIndex = i;
    	            }
                }
            }
        }
        $scope.orgNameList = function (belongOrgList) {
            if (!belongOrgList) {
                return "";
            }
            var orgNameHtml = "";
            for (var i = 0; i < belongOrgList.length; i++) {
                if (belongOrgList[i].orgName) {
                    orgNameHtml = orgNameHtml + belongOrgList[i].orgName + "|";
                }
            }
            if (orgNameHtml == "") {
                return "";
            }
            orgNameHtml = orgNameHtml.slice(0, -1)
            orgNameHtml = orgNameHtml.length > 512 ? orgNameHtml.slice(0, 509) + '...' : orgNameHtml;
            //去掉最后的一位|
            return orgNameHtml;
        }
        $scope.pushTime = function (startTime, endTime) {

            var startTimeHtml = startTime.slice(0, -3);
            var endTimeHtml = endTime.slice(0, -3);

            return startTimeHtml + "~" + endTimeHtml;
        }

        $scope.deliveryDateMap = function (deliveryDate) {
            //先判断特殊场景
            if (deliveryDate == "1111111") {
                return "不限";
            }
            //返回给前台的初始字符串
            var deliveryDateHtml = "";
            for (var i = 0; i < 7; i++) {
                if (deliveryDate.charAt(i) == "1") {
                    switch (i) {
                        case 0:
                            deliveryDateHtml = deliveryDateHtml + "周一|";
                            break;
                        case 1:
                            deliveryDateHtml = deliveryDateHtml + "周二|";
                            break;
                        case 2:
                            deliveryDateHtml = deliveryDateHtml + "周三|";
                            break;
                        case 3:
                            deliveryDateHtml = deliveryDateHtml + "周四|";
                            break;
                        case 4:
                            deliveryDateHtml = deliveryDateHtml + "周五|";
                            break;
                        case 5:
                            deliveryDateHtml = deliveryDateHtml + "周六|";
                            break;
                        case 6:
                            deliveryDateHtml = deliveryDateHtml + "周日|";
                            break;
                    }
                }
            }
            //去掉最后的一位|
            return deliveryDateHtml.slice(0, -1);
        }
        $scope.approveStatusMap = {
            "1": "审核失败",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        }
        $scope.unicomApproveStatusMap = {
        		"1": "审核失败",
                "-1": "--",
                "2": "待审核",
                "3": "审核通过",
                "4": "审核驳回"
        }

        $scope.telecomApproveStatusMap = {
        		"1": "审核失败",
                "-1": "--",
                "2": "待审核",
                "3": "审核通过",
                "4": "审核驳回"
        }
        $scope.servTypeMap = {
            "1": "名片彩印",
            "2": "热线彩印",
            "3": "广告彩印"
        }
        $scope.subServTypeMap = {
            "1": "主叫彩印",
            "2": "被叫彩印",
            "3": "主被叫彩印",
            "4": "被叫挂机短信",
            "8": "挂机彩信"
        }
        $scope.auditStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 2,
                name: "待审核"
            },
            {
                id: 3,
                name: "审核通过"
            },
            {
                id: 4,
                name: "审核驳回"
            },
            {
                id: 1,
                name: "审核失败"
            },
        ];
        $scope.statusMap = {
            1: "主叫彩印",
            2: "被叫彩印",
            3: "主被叫彩印",
            4: "被叫挂机短信",
            8: "挂机彩信"
        };
        $scope.subServChoise = [
            {
                id: "",
                name: "不限"
            }
        ];
        $scope.orderListData = [];
        $scope.enterpriseID = $.cookie('enterpriseID') || '';

        //初始化搜索条件
        $scope.initSel = {
            contentInfo: "",
            auditStatus: "",
            contentNo:"",
            subServType:""
        };
        $scope.queryContentInfoList();
        $scope.querySyncServiceRule();
    };

    $scope.gotoAdd = function () {
        location.href = '../addProvincialColorPrint/addProvincialColorPrint.html?operateType=add';
    }
    $scope.gotoDetail = function (item) {
        location.href = '../addProvincialColorPrint/addProvincialColorPrint.html?contentID=' + item.contentID + '&operateType=detail';
    }
    $scope.gotoModify = function (item) {
        //审核中状态不可修改
    	//审核中状态不可修改
    	if (item.servType != 1 && item.servType != 5)
        {
    		if (item.approveStatus == 2) {
                $scope.tip = "1030121000";
                $('#myModal').modal();
                return;
            }
        }
    	else
        {
    		if (item.mobileApproveStatus == 2  || item.unicomApproveStatus == 2 || item.telecomApproveStatus == 2)
    	    {
    			$scope.tip = "1030121000";
                $('#myModal').modal();
                return;
    	    }
        }
        //预设固定模板关联内容等待处理不可修改
        if (item.dealStatus == 0) {
            $scope.tip = "1030120108";
            $('#myModal').modal();
            return;
        }
        location.href = '../addProvincialColorPrint/addProvincialColorPrint.html?contentID=' + item.contentID + '&operateType=modify';
    }

    //============================= 912 =============================
    $scope.ztclose = function () {
        $('#selectType option:first').prop("selected", "selected");
        $('#detailListPop').modal("hide");
        $scope.queryMemberList(item, "", "error");
    }

    //同步状态
    $scope.gotoSynchronize = function (item) {
        $scope.selectedItem = item;
        $scope.queryMemberList(item, "", "error");
        $('#detailListPop').modal();
    };

    //根据类型查询数据
    $scope.queryByMemberName = function () {
        var type = $("#selectType").val();
        $scope.queryMemberList($scope.item, "", type);
    }

    //单点同步成员
    $scope.retrySynchronize = function (item) {
        $scope.batchSynchronize(item);
    }

    //单点删除同步成员
    $scope.deleteSynchronize = function (item) {
        //console.log(item);
        //console.log($scope.item.contentBelongOrgList[0].ownerID);
        var arr = [];
        arr.push(item.id);
        var batchSyncMemberReq = {
            "memberIDList": arr,
            "orgID": $scope.item.contentBelongOrgList[0].ownerID
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/batchDeleteMember",
            data: JSON.stringify(batchSyncMemberReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        //重新请求查询数据
                        $scope.queryMemberList($scope.item, "", "error");
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteBusinessCardCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });

    }
    $scope.queryMemberList2 = function (item, condition, type) {
        var type = $("#selectType").val();
        $scope.queryMemberList(item,condition,type);
    }
    //查询成员信息
    $scope.queryMemberList = function (item, condition, type) {
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        $scope.item = item;
        $scope.allChoose = false;
        // var reserved1 = 1;
        // if ("error" == type) {
        //     reserved1 = 1;
        // }
        // else if ("success" == type) {
        //     reserved1 = 0;
        // }
        // else {
        //     reserved1 = 2;
        // }
        if (condition != 'justPage') {
            $scope.orgID = item.id;
            var req = {
                "member": {
                    "id": "",
                    "msisdn": "",
                    "enterpriseID": item.enterpriseID,
                    "status": item.approveStatus,
                    "ecpmReserveds": {
                        // "reserved1": 2
                    }
                },
                "orgID": item.contentBelongOrgList[0].ownerID,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": 10,
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTemp2 = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp2;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
        }
        const orgIDs = [];
        const orgCodes = [];
        if ("error" == type) {
            for (i = 0; i < item.contentBelongOrgList.length; i++) {
                if (item.contentBelongOrgList[i].orgCode) {
                    orgIDs.push(item.contentBelongOrgList[i].ownerID);
                    orgCodes.push(item.contentBelongOrgList[i].orgCode);
                }
            }
            req.notLikeOrgCode = orgCodes.join(",");
        }
        else if ("success" == type) {
            for (i = 0; i < item.contentBelongOrgList.length; i++) {
                if (item.contentBelongOrgList[i].orgCode) {
                    orgIDs.push(item.contentBelongOrgList[i].ownerID);
                    orgCodes.push(item.contentBelongOrgList[i].orgCode);
                }
            }
            req.orgCode = orgCodes.join(",");
        } else {
            for (i = 0; i < item.contentBelongOrgList.length; i++) {
                if (!item.contentBelongOrgList[i].orgCode) {
                    orgIDs.push(item.contentBelongOrgList[i].ownerID);
                }
            }

        }
        if (orgIDs.length === 0) {
            $scope.memberListData = [];
            return;
        }
        req.orgIDs = orgIDs;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryMember",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    //console.log(data);
                    $scope.memberListData = result.memberList;
                    ;
                    //console.log($scope.memberListData);
                    if (data.resultCode == '1030100000') {
                        $scope.memberListData = result.memberList;
                        $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;
                    } else {
                        $scope.memberListData = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    //更改全选框
    $scope.ifSelected = function () {

        angular.forEach($scope.selectedListTemp, function (itemTemp) {
            $scope.selectedList.splice($.inArray(itemTemp, $scope.selectedList), 1);

        });
        if ($scope.allChoose) {
            $scope.selectedListTemp = [];
            angular.forEach($scope.memberListData, function (item) {
                item.checked = true;
                $scope.selectedList.push(item);
                $scope.selectedListTemp.push(item);
            })
        } else {
            angular.forEach($scope.memberListData, function (item) {
                item.checked = false;
                $scope.selectedListTemp = [];
            })

        }
    };


    //改变选择框
    $scope.changeSelected = function (item) {
        if ($.inArray(item, $scope.selectedListTemp) == -1) {
            $scope.selectedListTemp.push(item);
            $scope.selectedList.push(item);
        } else {
            $scope.selectedListTemp.splice($.inArray(item, $scope.selectedListTemp), 1);
            $scope.selectedList.splice($.inArray(item, $scope.selectedList), 1);
        }
        if ($scope.selectedListTemp.length == $scope.memberListData.length) {
            $scope.allChoose = true;
        } else {
            $scope.allChoose = false;
        }
    };

    /**
     * 重新同步
     */
    $scope.batchSynchronize = function (item) {
        //console.log(item);
        //console.log($scope.selectedListTemp);
        //console.log($scope.item);
        var len = $scope.selectedListTemp.length;
        var arrMsisdn = [];
        var arrMemberID = [];
        var orgID = null;
        if (len > 0) {
            orgID = $scope.item.contentBelongOrgList[0].ownerID;
        }
        else {
            orgID = $scope.item.contentBelongOrgList[0].ownerID
        }

        var contentId = $scope.item.contentID;
        //alert(contentId);
        //alert(orgID);
        if (null == item) {
            for (var i = 0; i < len; i++) {
                arrMsisdn.push($scope.selectedListTemp[i].msisdn);
                arrMemberID.push($scope.selectedListTemp[i].id);
            }
        }
        else {
            arrMsisdn.push(item.msisdn);
            arrMemberID.push(item.id);
        }
        var batchSyncMemberReq = {
            "memberMsisdnList": arrMsisdn,
            "memberIDList": arrMemberID,
            "contentId": contentId,
            "orgID": orgID
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/batchSyncMember",
            data: JSON.stringify(batchSyncMemberReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        //重新请求查询数据
                        $scope.queryMemberList($scope.item, "", "error");
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                        alert("同步成员失败");
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteBusinessCardCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    //============================= 912 =============================


    //删除名片内容弹窗
    $scope.deleteBusinessCardContent = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteBusinessCardContent').modal();
    };

    //删除名片内容
    $scope.delBusinessCardContent = function () {
        var item = $scope.selectedItemDel;
        console.log(item);
        var removeReq = {
            "operaterType": "1",    //1名片，2热线，3广告
            "id": item.contentID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $('#deleteBusinessCardCancel').click();
                        $scope.queryContentInfoList();
                    } else {
                        $('#deleteBusinessCardCancel').click();
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteBusinessCardCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //暂停名片内容
    $scope.suspendContent = function (item, operaterType) {
        console.log(item);
        var removeReq = {
            "operaterType": operaterType,   //操作类型：0启动，1暂停
            "servType": "5",
            "contentID": item.contentID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/suspendContent",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $scope.queryContentInfoList();
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //导出文件
    $scope.exportContentFile = function () {

        var req = {
            "param":{"req":JSON.stringify($scope.reqTemp),"type":3},
            "url":"/qycy/ecpmp/ecpmpServices/contentService/downContentInfoCsvFileService",
            "method":"get"
        }

        if($scope.reqTemp != undefined)
        {
            CommonUtils.exportFile(req);
        }
    }

    $scope.queryContentInfoList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "contentName": $scope.initSel.contentInfo || '',
                "enterpriseID": parseInt($scope.enterpriseID),
                "servTypeList": [5],
                "contentTypeList": [1],
                "approveStatus": $scope.initSel.auditStatus,
    	        "getFrame": 0,
    	        "getSwitchState": 0,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            if($scope.initSel.subServType!=''){
                req.subServTypeList=[parseInt($scope.initSel.subServType)];
            }
            if($scope.initSel.contentNo!=''){
                req.contentIDList=[parseInt($scope.initSel.contentNo)];
            }
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.orderListData = result.contentInfoList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.orderListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });

    }
    //查询子业务类型可选项
    $scope.querySyncServiceRule = function () {
        var serverTime = CommonUtils.getServerTime();
        $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
        /*//下拉框(投递方式)
        $scope.subServTypeChoise = [];*/
        var req = {
            "enterpriseID": parseInt($scope.enterpriseID),
            "servTypes": [5]
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    console.log(result)
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.subServerTypeList = result.syncServiceRuleList || [];
                        for (var i in $scope.subServerTypeList) {
                            var item = $scope.subServerTypeList[i];
                            //开通状态才能投递
                            if ($scope.nowTime <= item.expiryTime && $scope.nowTime >= item.effectiveTime && (item.status == 1 || item.status == 3)) {
                                $scope.subServChoise.push({
                                    id: item.subServType,
                                    name: $scope.statusMap[item.subServType]
                                })
                            }
                        }
                    }else {

                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.isfeedbackType = false;
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
}])
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])
