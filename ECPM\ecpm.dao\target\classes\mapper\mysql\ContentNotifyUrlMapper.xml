<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ContentNotifyUrlMapper">
	<resultMap id="contentNotifyUrlWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentNotifyUrlWrapper">
		<result property="contentID" column="contentID" javaType="java.lang.Integer" />
		<result property="notifyUrl" column="notifyUrl" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
	</resultMap>

	<insert id="insertContentNotifyUrl">
		insert into ecpm_t_content_notify_url
		(
			contentID,
			notifyUrl,
			createTime,
			updateTime
		)
		values
			(
				#{contentID},
				#{notifyUrl},
				now(),
				now())
	</insert>

	<select id="selectContentNotifyUrl" resultMap="contentNotifyUrlWrapper">
        select * from ecpm_t_content_notify_url where contentID = #{contentID}
	</select>



</mapper>