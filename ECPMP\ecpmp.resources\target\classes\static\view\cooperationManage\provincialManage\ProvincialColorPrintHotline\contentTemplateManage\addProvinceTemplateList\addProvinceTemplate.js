var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n", "cy.uploadify", "cy.uploadifyfile","cy.icon.content", "preview", "service.common", "page"])
app.controller('addPrintController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    document.onkeydown = function (e) {
        var ev = (typeof event != 'undefined') ? window.event : e;
        if (ev.keyCode == 13) {
            return false;
        }
    }
    $scope.operateType = $location.search().operateType || 'add';
    $scope.init = function () {
        $scope.htmlflat = false;
        $scope.noSign = '0';
        //获取enterpriseID
        $scope.enterpriseID = $.cookie('enterpriseID');
        $scope.parentEnterpriseID = $scope.enterpriseID ;
        $scope.contentID = $location.search().contentID || '';
        $scope.operatorID = $.cookie('accountID');
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.parentID = $.cookie('parentID') || '';
        $scope.pushObjArrTemp = [];
        $scope.timeError = false;
        $scope.chosePushObj = true;

        //初始化分组分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '30',
                "currentPage": 1
            }, {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '30',
                "currentPage": 1
            }
        ];

        $scope.allOrgList = [];
        $scope.OrganizationListTotal = 0;
        $scope.checkOrganizationList = [];

        //彩印标题和彩印内容的敏感校验标识,0表示彩印标题，1表示非挂机彩印的内容，2表示挂机彩印的内容
        $scope.isSensitive = [false, false, false];
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        //非超管登录，获取企业名称
        // if (!$scope.isSuperManager && !$scope.enterpriseName) {
        var req = {
            "id": $scope.enterpriseID,
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 100,
                "isReturnTotal": "1",
            }
        }
        /*查询企业列表*/
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        if (data.enterprise.reservedsEcpmp && data.enterprise.reservedsEcpmp.reserved10 == "111") {
                            $scope.isZYZQ = true;
                        } else {
                            $scope.isZYZQ = false;
                        }
                        //查询企业服务开关,当为add时初始化运营商选项按钮和勾选状态
                        $scope.queryPlatformStatus();
                        $scope.enterpriseName = data.enterprise.enterpriseName;
                        $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
                    }
                })
            }
        });
        // }
        //初始化显示的信息
        $scope.sensitiveWords = {
            "0": [],
            "1": [],
            "2": []
        };
        $scope.sensitiveWordsStr = {
            "0": '',
            "1": '',
            "2": ''
        }
        $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];
        $scope.initPrintInfo = {
            thirdpartyType: 0,
            servType: 5,
            subServType: 1,
            deliveryDate: '0000000',
            colorContent: '',
            calledContent: '',
            templateName: '',
            contentPushTime: {
                startTime: '',
                endTime: '',
            },
            blackwhiteListType: 0,
            chargeType: 1,
        };
        $scope.statusMap = {
            1: "主叫彩印",
            2: "被叫彩印",
            3: "主被叫彩印",
            4: "被叫挂机短信",
            8: "挂机彩信"
        };
        $scope.contentType = $scope.initPrintInfo.subServType;
        //是否可选择黑名单
        $scope.showBlack = false;
        //是否可选择白名单
        $scope.showWhite = false;


        //查询所属行业列表
        $scope.queryIndustry($scope);

        //初始化营业执照容器
        $scope.initBusinessURLContainer($scope);

        $scope.queryOrg();
        //交集
        $scope.intersection = function (a, b) {
            if (a == null) {
                return b;
            }
            if (b == null) {
                return a;
            }
            let temp = "";
            for (let i = 0; i < a.length; i++) {
                if (a.charAt(i) === b.charAt(i) && a.charAt(i) === "1") {
                    temp += "1";
                } else {
                    temp += "0";
                }
            }
            return temp;
        };
        //并集
        $scope.union = function (a, b) {
            if (a == null) {
                return b;
            }
            if (b == null) {
                return a;
            }
            let temp = "";
            for (let i = 0; i < a.length; i++) {
                if (a.charAt(i) === b.charAt(i) && a.charAt(i) === "0") {
                    temp += "0";
                } else {
                    temp += "1";
                }
            }
            return temp;
        };
        $scope.syncServiceRulePlat = {};
        $scope.accepttype2 = "jpeg,jpg,png,bmp";
        $scope.isValidate2 = false;
        $scope.filesize2 = 100;
        $scope.mimetypes2 = ".jpeg,.jpg,.png,.bmp";
        $scope.isCreateThumbnail2 = false;
        $scope.uploadurl2 = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.numlimit2 = 1000;
        $scope.uploadCertiDesc = "最多支持6张图片，仅支持jpg，bmp，png，jpeg格式";
        $scope.uploadParam2 = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'certiFile'
        };
        $scope.colorContentAndFileList = [];
        $scope.certificateUrlList = [];
        //文件数
        $scope.fileLength = 0;

        if ($scope.operateType != 'detail') {
            $scope.$watch('colorContentAndFileList', function (newVal, oldVal) {
                $scope.fileLength = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameFileUrl) {
                        $scope.fileLength += 1;
                    }
                }

            }, true)
        }
        //上传文件
        $scope.$on("uploadifyid2", function (event, fileUrl, fileData) {
            if (fileUrl != '') {
                if ($scope.colorContentAndFileList.length >= 6) {
                    return;
                }
                $scope.colorContentAndFileList.push({
                    frameFileUrl: fileUrl,
                    formatFrameFileUrl: CommonUtils.formatPic(fileUrl).download,
                    filesize: fileData.file.size,
                    filename: fileData.file.name
                })
            } else if (fileData != '' || fileData != undefined) {
                // $scope.contentPicUrlList.splice(index, 1);
                // if($scope.urlList){
                //     $scope.urlList.splice(index,1);
                // }
            }
            console.log(fileUrl);
            console.log($scope.colorContentAndFileList);
            console.log($scope.fileLength);
        });
        //其他资质文件下载
        $scope.exportFile = function (downloadUrl) {
            var req = {
                "param": {
                    "path": downloadUrl,
                    "token": $.cookie("token"),
                    "isExport": 0
                },
                "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
                "method": "get"
            }
            CommonUtils.exportFile(req);
        };

        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'importContentTemplate'
        };
        $scope.errorInfoCaller = '';
        $scope.errorInfoCalled = '';
        $scope.fileUrlCaller = '';
        $scope.fileUrlCalled = '';
        // 上传excel  END
        $scope.$on("uploadifyidCaller", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileNameCaller = broadData.file.name;
            } else {
                $scope.fileNameCaller = "";
            }
            $scope.uploaderCaller = broadData.uploader;
            $scope.errorInfoCaller = broadData.errorInfo;
            $scope.fileUrlCaller = fileUrl;
        });

        $scope.$on("uploadifyidCalled", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileNameCalled = broadData.file.name;
            } else {
                $scope.fileNameCalled = "";
            }
            $scope.uploaderCalled = broadData.uploader;
            $scope.errorInfoCalled = broadData.errorInfo;
            $scope.fileUrlCalled = fileUrl;
        });
    };
    $scope.deleteTemplate = function (type) {
        if (1 == type) {
            $scope.fileUrlCaller = '';
            $scope.fileNameCaller = "";
        }
        if (2 == type) {
            $scope.fileUrlCalled = '';
            $scope.fileNameCalled = "";
        }


    }

    $scope.changePlatStatus = function (platformStatus) {
        // var platformStatus = $scope.platformStatus;
        $scope.noSign = '1';
        let platforms = $scope.syncServiceRulePlat[$scope.initPrintInfo.subServType] || "000";
        platformStatus = $scope.intersection(platforms, platformStatus);
        if ($scope.operateType != 'add') {
            platforms = $scope.intersection(platforms, $scope.platforms);
        }

        $scope.platforms = platforms;
        var newPlatforms = '';
        //初始化signature必填状态
        if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }else{
            $scope.signatureRequired = '0';

        }
        for (var i = 0; i < 3; i++) {
            $(".platforms .check-li").eq(i).removeAttr("style", "");
            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            $('.platforms .check-li').eq(i).unbind("click");
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
            } else {
                //初始化勾选状态
                if (platforms.charAt(i) == '1') {
                    $(".platforms .check-li").find('span').eq(i).addClass('checked')
                }

                if ($scope.operateType != 'detail') {
                    //绑定点击事件
                    $('.platforms .check-li').eq(i).on('click', function () {
                        if ($(this).find('span').hasClass('checked') &&
                            ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                            $(this).find('span').removeClass('checked');
                        } else {
                            $(this).find('span').addClass('checked')
                        }
                        var _platforms = '';
                        for (var i = 0; i < 3; i++) {
                            if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                                _platforms += '1';
                            } else {
                                _platforms += '0';
                            }
                        }
                        if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                            $scope.signatureRequired = '1';
                        } else {
                            $scope.signatureRequired = '0';
                            $scope.initPrintInfo.sceneDesc = "";
                        }
                        $scope.platforms = _platforms;
                    });
                }
            }

            if (platforms.charAt(i) == '1' && platformStatus.charAt(i) == '1') {
                newPlatforms += '1';
            } else {
                newPlatforms += '0';
            }
        }
        $scope.platforms = newPlatforms;
    }

    $scope.checkEnterpriseWithoutSignAdd = function (platformStatus){
        //判断父企业id是否为空
        var req;
        if( $scope.parentEnterpriseID === $scope.enterpriseID){
            req = {
                "enterpriseID": $scope.enterpriseID
            };
        }else{
            req = {
                "enterpriseID": $scope.parentEnterpriseID
            };
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                        if($scope.enterpriseWithoutSignListData.length === 0){
                            //沒有配置过免签名,按原来的流程走
                            $scope.changePlatStatus(platformStatus);
                        }else{
                            //签名不用必填
                            $scope.changePlatStatusNoSign(platformStatus);
                        }
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }


    $scope.changePlatStatusNoSign = function (platformStatus) {
        // var platformStatus = $scope.platformStatus;
        let platforms = $scope.syncServiceRulePlat[$scope.initPrintInfo.subServType] || "000";
        platformStatus = $scope.intersection(platforms, platformStatus);
        if ($scope.operateType != 'add') {
            platforms = $scope.intersection(platforms, $scope.platforms);
        }
        $scope.platforms = platforms;
        var newPlatforms = '';
        if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }else{
            $scope.signatureRequired = '0';

        }
        for (var i = 0; i < 3; i++) {
            $(".platforms .check-li").eq(i).removeAttr("style", "");
            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            $('.platforms .check-li').eq(i).unbind("click");
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
            } else {
                //初始化勾选状态
                if (platforms.charAt(i) == '1') {
                    $(".platforms .check-li").find('span').eq(i).addClass('checked')
                }

                if ($scope.operateType != 'detail') {
                    //绑定点击事件
                    $('.platforms .check-li').eq(i).on('click', function () {
                        if ($(this).find('span').hasClass('checked') &&
                            ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                            $(this).find('span').removeClass('checked');
                        } else {
                            $(this).find('span').addClass('checked')
                        }
                        var _platforms = '';
                        for (var i = 0; i < 3; i++) {
                            if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                                _platforms += '1';
                            } else {
                                _platforms += '0';
                            }
                        }
                        if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                            $scope.signatureRequired = '1';
                        } else {
                            $scope.signatureRequired = '0';
                            $scope.initPrintInfo.sceneDesc = "";
                        }
                        $scope.platforms = _platforms;
                    });
                }
            }
            if (platforms.charAt(i) == '1' && platformStatus.charAt(i) == '1') {
                newPlatforms += '1';
            } else {
                newPlatforms += '0';
            }
        }
        $scope.platforms = newPlatforms;
    }




    $scope.changeSubServerType = function () {
        // 处理?的展示内容
        if($scope.initPrintInfo.subServType == 1 || $scope.initPrintInfo.subServType == 2 || $scope.initPrintInfo.subServType == 3){
          $scope.contentType = $scope.initPrintInfo.subServType;
        }else {
          $scope.contentType = 0; // 不展示?
        }
        //黑名单查询
        $scope.queryBlackWhiteListFun(1);
        //白名单查询
        $scope.queryBlackWhiteListFun(2);


        //查询企业订购的产品计费类型
        $scope.queryProductSubscribe($scope);

        if ($scope.initPrintInfo.subServType != null && $scope.initPrintInfo.subServType != '4') {
            $scope.initPrintInfo.sceneDesc = "";
        }

        var platformStatus = $scope.syncServiceRulePlat[$scope.initPrintInfo.subServType];
        //初始化signature必填状态
        $scope.signatureRequired = '0';
        if (!platformStatus) {
            return;
        }
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }

        var platformStatus = $scope.platformStatus;
        $scope.checkEnterpriseWithoutSignAdd(platformStatus);
        // $scope.changePlatStatus();
    }

    //数组去重
    $scope.uniq = function (array) {
        var temp = [];
        var l = array.length;
        if (l > 0) {
            for (var i = 0; i < l; i++) {
                for (var j = i + 1; j < l; j++) {
                    if (array[i].id === array[j].id) {
                        i++;
                        j = i;
                    }
                }
                temp.push(array[i]);
            }
        }
        return temp;
    };

    //查询子业务类型可选项
    $scope.querySyncServiceRule = function () {
        var serverTime = CommonUtils.getServerTime();
        $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
        //下拉框(投递方式)
        $scope.subServTypeChoise = [];
        var req = {
            "enterpriseID": parseInt($scope.enterpriseID),
            "servTypes": [5]
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    console.log(result)
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.stopSubServerTypeList = [];
                        $scope.subServerTypeList = result.syncServiceRuleList || [];
                        for (var i in $scope.subServerTypeList) {
                            var item = $scope.subServerTypeList[i];
                            //开通状态才能投递
                            if ($scope.nowTime <= item.expiryTime) {
                                $scope.subServTypeChoise.push({
                                    id: item.subServType,
                                    name: $scope.statusMap[item.subServType],
                                    status: item.status,
                                    plat: item.platforms
                                })
                            }
                        }
                        for (var j = 0; j < $scope.subServTypeChoise.length; j++) {
                            var item2 = $scope.subServTypeChoise[j];
                            if (item2.status === 3) {
                                // 记录暂停状态的子业务类型
                                $scope.stopSubServerTypeList.push(item2.id)
                            }
                        }
                        $scope.subServTypeChoise_temp = [];
                        // 同一子业务类型中,有一个为暂停状态,则该类型的所有对象从数组中移除
                        if ($scope.stopSubServerTypeList.length > 0) {
                            for (var m = 0; m < $scope.subServTypeChoise.length; m++) {
                                var subServTypeChoiseItem = $scope.subServTypeChoise[m];
                                var isStop = false;
                                for (var n = 0; n < $scope.stopSubServerTypeList.length; n++) {
                                    var stopSubServerTypeItem = $scope.stopSubServerTypeList[n];
                                    if (stopSubServerTypeItem === subServTypeChoiseItem.id) {
                                        isStop = true;
                                        break;
                                    }
                                }
                                if (!isStop) {
                                    $scope.subServTypeChoise_temp.push(subServTypeChoiseItem);
                                }
                            }
                            $scope.subServTypeChoise = $scope.subServTypeChoise_temp;
                        }

                        //开关 多个取并集
                        for (let i = 0; i < $scope.subServTypeChoise.length; i++) {
                            let t = $scope.subServTypeChoise[i];
                            if ($scope.syncServiceRulePlat[t.id] == null) {
                                $scope.syncServiceRulePlat[t.id] = t.plat;
                            } else {
                                $scope.syncServiceRulePlat[t.id] = $scope.union($scope.syncServiceRulePlat[t.id], t.plat);
                            }
                            //取完再和开关比对
                            $scope.syncServiceRulePlat[t.id] = $scope.intersection($scope.syncServiceRulePlat[t.id], $scope.platformStatus);
                        }

                        $scope.subServTypeChoise = $scope.uniq($scope.subServTypeChoise);


                        if ($scope.subServTypeChoise.length > 0) {
                        	if ($scope.operateType == 'add')
                            {
                                $scope.initPrintInfo.subServType = $scope.subServTypeChoise[0].id;
                            }
                        	else
                            {
                                $scope.initPrintInfo.subServType = $scope.contentInfoDetail.subServType;

                            }
                            $scope.changeSubServerType();

                        }
                    } else {
                        $scope.subServerTypeList = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    }

    //黑白名单选择
    $scope.changeBlackwhiteListType = function () {
        //使用订单类型
        var _f0 = $('.black-white .redio-li').eq(0).find('span').hasClass('checked'),
            _f1 = $('.black-white .redio-li').eq(1).find('span').hasClass('checked'),
            _f2 = $('.black-white .redio-li').eq(2).find('span').hasClass('checked');

        if (_f1) {
            $scope.initPrintInfo.blackwhiteListType = 1;
        } else if (_f2) {
            $scope.initPrintInfo.blackwhiteListType = 2;
        } else if (_f0) {
            $scope.initPrintInfo.blackwhiteListType = 0;
        }
    }
    $scope.deleteCtnOrFile = function (index) {
        $scope.colorContentAndFileList.splice(index, 1);
    }
    $scope.mapDataToHtmlNum = 0;
    $scope.mapDataToHtml = function () {
        if ($scope.mapDataToHtmlNum > 0) {
            return;
        }
        $scope.mapDataToHtmlNum++;
        $scope.initPrintInfo.subServType = $scope.contentInfoDetail.subServType;
        $scope.initPrintInfo.deliveryDate = $scope.contentInfoDetail.deliveryDate;
        if ($scope.contentInfoDetail.subServType == 3) {
            $scope.initPrintInfo.colorContent = $scope.contentInfoDetail.content.split('||||')[0];
            $scope.initPrintInfo.calledContent = $scope.contentInfoDetail.content.split('||||')[1];
        } else {
            $scope.initPrintInfo.colorContent = $scope.contentInfoDetail.content;
        }
        $scope.initPrintInfo.colorTitle = $scope.contentInfoDetail.contentTitle;
        $scope.initPrintInfo.contentName = $scope.contentInfoDetail.contentName;
        $scope.initPrintInfo.blackwhiteListType = $scope.contentInfoDetail.blackwhiteListType;

        //REQ-113 REQ-122  运营商 签名 所属行业 营业执照
        $scope.platforms = $scope.contentInfoDetail.platforms;


        $scope.initPrintInfo.signature = $scope.contentInfoDetail.signature;
        $scope.initPrintInfo.sceneDesc = $scope.contentInfoDetail.sceneDesc;

        $scope.selectedIndustryID = $scope.contentInfoDetail.industryType;
        if ($scope.industryList) {
            jQuery.each($scope.industryList, function (i, e) {
                if (e.industryID == $scope.contentInfoDetail.industryType) {
                    $scope.selectedIndustry = e;
                }
            });
            $scope.changeIsSensitive($scope.selectedIndustry)

        }

        if ($scope.contentInfoDetail.businessLicenseURL) {
            $scope.businessLicenseURL_ = $scope.contentInfoDetail.businessLicenseURL;
            $scope.fileUrl_ = CommonUtils.formatPic($scope.contentInfoDetail.businessLicenseURL).review;
            console.log("$scope.fileUrl_", $scope.fileUrl_)
            console.log("$scope.operateType", $scope.operateType)
            $scope.urlList_ = [$scope.fileUrl_];
            $scope.urlList_2 = [$scope.contentInfoDetail.businessLicenseURL];
        }


        $scope.initPrintInfo.contentPushTime.startTime = $scope.contentInfoDetail.contentPushTime.startTime;
        $scope.initPrintInfo.contentPushTime.endTime = $scope.contentInfoDetail.contentPushTime.endTime;
        $scope.time = $scope.initPrintInfo.contentPushTime.startTime + ' ~ ' + $scope.initPrintInfo.contentPushTime.endTime;
        //黑白名单匹配
        $(".black-white .redio-li").find('span').removeClass('checked');
        if ($scope.initPrintInfo.blackwhiteListType == 1) {
            $(".black-white .redio-li").find('span').eq(1).addClass('checked');
        } else if ($scope.initPrintInfo.blackwhiteListType == 2) {
            $(".black-white .redio-li").find('span').eq(2).addClass('checked');
        } else {
            $(".black-white .redio-li").find('span').eq(0).addClass('checked');
        }
        $(".pushDate .check-li").find('span').removeClass('checked');
        if ($scope.initPrintInfo.deliveryDate == '1111111') {
            $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];
            $(".pushDate .check-li").find('span').first().addClass('checked');
        } else {
            $scope.deliveryDateArr = $scope.initPrintInfo.deliveryDate.split('');
            for (var i in $scope.deliveryDateArr) {
                if ($scope.deliveryDateArr[i] == '1') {
                    var _i = parseInt(i);
                    $(".pushDate .check-li").find('span').eq(_i + 1).addClass('checked');
                }
            }
        }
        if ($scope.contentInfoDetail.certificateUrlList && $scope.contentInfoDetail.certificateUrlList.length > 0) {
            for (var j in $scope.contentInfoDetail.certificateUrlList) {
                var item = $scope.contentInfoDetail.certificateUrlList[j];
                var name = item.substring(item.lastIndexOf("/") + 1);
                $scope.colorContentAndFileList.push({
                    frameFileUrl: item,
                    formatFrameFileUrl: CommonUtils.formatPic(item).download,
                    filename: name
                })
            }
        }
        //推送对象匹配
        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr0);
        var _span = $('.pushObj .check-li span'),
            ctnBelongOrg = $scope.contentInfoDetail.contentBelongOrgList;
        if (ctnBelongOrg && ctnBelongOrg.length > 0) {
            var orgMap = new Map();
            _span.first().removeClass('checked');
            for (var n in ctnBelongOrg) {
                var value = ctnBelongOrg[n].ownerID;
                orgMap.set(value, 1);
                for (var m in $scope.orgList) {
                    if (value == $scope.orgList[m].id) {
                        $scope.pushObjArrTemp[m] = '1';
                        var _m = parseInt(m);
                        _span.eq(_m + 1).addClass('checked');
                    }
                }
            }
            //判断推送对象是否全部
            // if ($scope.orgList.length == orgMap.size) {
            //     _span.removeClass('checked');
            //     _span.first().addClass('checked');
            // } else {
                _span.first().removeClass('checked');
            // }
        } else {    //如果修改查询时组织已经被清空
            _span.first().removeClass('checked');
            $scope.chosePushObj = false;
        }

        for (let i in $scope.contentInfoDetail.contentBelongOrgList) {
            let org = $scope.findFromGroupList($scope.contentInfoDetail.contentBelongOrgList[i].ownerID, $scope.allOrgList);
            if (org && !$scope.findFromGroupList($scope.contentInfoDetail.contentBelongOrgList[i].ownerID, $scope.checkOrganizationList)) {
                $scope.checkOrganizationList.push(org);
            }
        }
        if ($scope.checkOrganizationList.length > 50) {
            $scope.queryCheckedOrg();
        }
        $scope.htmlflat = true;
    }
    //修改和详情进来需要查询一遍
    $scope.subServTypeChoise = [];
    $scope.queryContentDetail = function () {
        var contentID = "";
        if ($scope.parentID != '') {
            contentID = parseInt($scope.parentID);
        } else {
            contentID = parseInt($scope.contentID);
        }
        var req = {
            "enterpriseID": $scope.enterpriseID,
            "contentIDList": [parseInt(contentID)],
            "contentTypeList": [2],
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 10,
                "isReturnTotal": "1"
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        if (result.contentInfoList && result.contentInfoList.length > 0) {
                            $scope.contentInfoDetail = result.contentInfoList[0];
                            $scope.subServTypeChoise.push({
                                id: $scope.contentInfoDetail.subServType,
                                name: $scope.statusMap[$scope.contentInfoDetail.subServType]
                            });
                            $scope.mapDataToHtmlNum = 0;
                            if ($scope.operateType != 'add') {
                                $scope.mapDataToHtml();

                            }

                            //黑名单查询
                            $scope.queryBlackWhiteListFun(1);
                            //白名单查询
                            $scope.queryBlackWhiteListFun(2);
                        }

                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                // })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        });
    };
    //获取queryEnterpriseList接口的数据
    $scope.queryBlackWhiteListFun = function (blackWhiteListType) {
        //查询条件 enterpriseID ， servType ， subServType ， blackWhiteListType
        var req = {
            "blackWhite": {
                "blackWhiteListType": blackWhiteListType,
                "servType": 5,
                "enterpriseID": $scope.enterpriseID,
                "subServType": $scope.initPrintInfo.subServType
            },
            "page": {
                "isReturnTotal": "1",
            }
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/blackWhiteService/queryBlackWhiteList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '**********') {
                        //获取页面的总条数与总页面
                        if ((parseInt(result.totalNum) || 0) != 0) {
                            //黑名单
                            if (blackWhiteListType === 1) {
                                $scope.showBlack = true;
                            }
                            else {
                                $scope.showWhite = true;
                            }
                        }
                        else {
                            //黑名单
                            if (blackWhiteListType === 1) {
                                $scope.showBlack = false;
                            }
                            else {
                                $scope.showWhite = false;
                            }
                        }
                    } else {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        })
    };
    $scope.queryCheckedOrg = function () {
        let page = $scope.pageInfo[1];
        let par = $scope.group($scope.checkOrganizationList, parseInt(page.pageSize));
        page.totalCount = $scope.checkOrganizationList.length;
        page.totalPage = page.totalCount !== 0 ? Math.ceil($scope.checkOrganizationList.length / parseInt(page.pageSize)) : 1;
        $scope.checkOrganizationListTemp = par[page.currentPage - 1];
    };
    $scope.groupSelectAll = function () {
        let checkedObject = $("#groupListPop .pushObj .checked");
        for (let i in checkedObject) {
            checkedObject.eq(i).click();
        }

        let gourpObject = $("#groupListPop .pushObj .group-info");
        for (let i in gourpObject) {
            gourpObject.eq(i).click();
        }
    };


    $scope.resetGroupSelect = function () {
        let checkedObject = $("#groupListPop .pushObj .checked");
        for (let i in checkedObject) {
            checkedObject.eq(i).click();
        }
        $scope.checkedOrgListTemp = [];

    };
    $scope.queryOrg = function (pageInfo, condition) {
        var req = {
            "enterpriseID": $scope.enterpriseID,
            "isReturnMemberCount": 1,
            "orgType": 3,
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 20000,
                "isReturnTotal": "1"
            }
        };
        if (pageInfo) {
        	if (condition != 'justPage') {
        		req = {
                        "enterpriseID": $scope.enterpriseID,
                        "orgType": 3,
                        "isReturnMemberCount": 1,
                        "pageParameter": {
                            "pageNum": 1,
                            "pageSize": pageInfo[0].pageSize,
                            "isReturnTotal": "1"
                        }
                    };
        	}
        	else
            {
        		req = {
                        "enterpriseID": $scope.enterpriseID,
                        "orgType": 3,
                        "isReturnMemberCount": 1,
                        "pageParameter": {
                            "pageNum": pageInfo[0].currentPage,
                            "pageSize": pageInfo[0].pageSize,
                            "isReturnTotal": "1"
                        }
                    };
            }
        	if ($scope.groupName) {
                req.orgName = $scope.groupName;
            }
        }
        
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryOrganizationList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {

                        $scope.orgList = result.organizationList || [];

                        //不存在分页为第一次查全量数据 //原流程
                        if (!pageInfo) {
                            $scope.allOrgList = result.organizationList || [];
                            $scope.OrganizationListTotal = result.totalNum || 0;
                            $scope.pushObjArr0 = [];
                            $scope.pushObjArr1 = [];
                            if ($scope.orgList.length == 0 && $scope.operateType != 'detail') {
                                $scope.tip = "1030120601";
                                $('#myModal').modal();
                            }
                            for (var i in $scope.orgList) {
                                $scope.pushObjArr0.push('0');
                                $scope.pushObjArr1.push('1');
                            }
                        } else {
                            $scope.pageInfo[0].totalCount = result.totalNum || 0;
                            $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;

                            //渲染勾选
                            if (!$scope.isNoLimitTemp) {
                                $('.pushObj.max .check-li span.isLimit').removeClass("checked");
                                setTimeout(function () {
                                    for (let i = 0; i < $scope.checkedOrgListTemp.length; i++) {
                                        $('.pushObj.max .check-li span[value="' + $scope.checkedOrgListTemp[i].id + '"]').addClass("checked")
                                    }
                                }, 300);
                            }
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                    if (!pageInfo) {
                        if ($scope.operateType != 'add') {
                            $scope.queryContentDetail();
                        }
                        else {
                            //新增的查询黑名单和白名单
                            //黑名单查询
                            $scope.queryBlackWhiteListFun(1);
                            //白名单查询
                            $scope.queryBlackWhiteListFun(2);
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                    if ($scope.operateType != 'add') {
                        $scope.queryContentDetail();
                    }
                    ;

                })
            }
        });
    }
    $('#time-config').daterangepicker({
        "timePicker": true,
        "timePicker24Hour": true,
        "linkedCalendars": false,
        "autoUpdateInput": true,
        "locale": {
            format: 'HH:mm:ss',
            separator: ' ~ ',
            applyLabel: "应用",
            cancelLabel: "取消",
            resetLabel: "重置",
            "daysOfWeek": ["日", "一", "二", "三", "四", "五", "六"],
            "monthNames": ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"],
        },
        drops: "up",

    }, function (start, end, label) {
        var _self = this,
            start1 = this.startDate.format(this.locale.format),
            end1 = this.endDate.format(this.locale.format);
        $scope.initPrintInfo.contentPushTime.startTime = start1;
        $scope.initPrintInfo.contentPushTime.endTime = end1;

        $rootScope.$apply(function () {
            var start2 = start1.replace(new RegExp(/(:)/g), ""),
                end2 = end1.replace(new RegExp(/(:)/g), "");
            if (parseInt(start2) >= parseInt(end2)) {
                $scope.timeError = true;
            } else {
                $scope.timeError = false;
            }
            $scope.time = start1 + _self.locale.separator + end1;
        })
        console.log(_self.startDate.format(_self.locale.format));
        if (!this.startDate) {
            this.element.val('');
        } else {
            this.element.val(start1 + this.locale.separator + end1);
        }
    });
    $scope.formatDate = function (str) {
        if (!str) {
            return 'format error';
        }
        var newDateStr = "";
        newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
        return newDateStr;

    }
    $scope.goBack = function () {
        window.location.href = '../provincialTemplate/queryProvinceTemplateList.html';
    }
    
    // 2109：提交时，若运营商选中异网（联通、电信），则二次确认
    $scope.diffNetAuthMaterialsConfirm = function () {
    	if ($scope.signatureRequired == '1') {
    		$('#diffNetAuthMaterials').modal();
        } else {
        	$scope.templateSubmitPop();
        }
    }
    $scope.diffNetAuthMaterialsUploaded = function () {
    	$('#diffNetAuthMaterialsCancel').click();
    	$scope.templateSubmitPop();
    }
    
    $scope.templateSubmitPop = function () {
        if ($scope.initPrintInfo.subServType == '3') {
            var colorContentLength = $scope.initPrintInfo.colorContent.length;
            var calledContentLength = $scope.initPrintInfo.calledContent.length;
            var contentLength = colorContentLength + calledContentLength;
            if (contentLength <= 0) {
                $scope.tip = '请填写彩印内容！';
                $('#myModal').modal();
                return;
            }
            if (colorContentLength <= 0 && $scope.fileUrlCaller) {
                $scope.tip = '请先填写模板内容，再导入变量！';
                $('#myModal').modal();
                return;
            }
            if (calledContentLength <= 0 && $scope.fileUrlCalled) {
                $scope.tip = '请先填写模板内容，再导入变量！';
                $('#myModal').modal();
                return;
            }
            if (colorContentLength > 0 && !$scope.fileUrlCaller) {
                $('#templateSubmitPop').modal();

                return;
            }
            if (calledContentLength > 0 && !$scope.fileUrlCalled) {
                $('#templateSubmitPop').modal();

                return;
            }
        }
        else {

            var colorContentLength = $scope.initPrintInfo.colorContent.length;
            if (colorContentLength <= 0 && $scope.fileUrlCaller) {
                $scope.tip = '请先填写模板内容，再导入变量！';
                $('#myModal').modal();
                return;
            }
            if (colorContentLength > 0 && !$scope.fileUrlCaller && $scope.operateType == 'add') {
                $('#templateSubmitPop').modal();

                return;
            }
        }
        $scope.queryMemberCntByChgType();
    }
    // 成员数校验
    $scope.queryMemberCntByChgType = function () {
        $scope.noRepeat = false;
        //110迭代：提交前取消敏感词校验
        // $scope.sensitiveCheck($scope.initPrintInfo.colorContent, 1);
        // if ($scope.isSensitive[0] || $scope.isSensitive[1] || $scope.isSensitive[2]) {
        //     $scope.tip = '1030120017';
        //     $('#myModal').modal();
        //     return;
        // }
        $scope.contentBelongOrgList = [];
        $scope.orgIds = [];
        $scope.initPrintInfo.chargeType = 2;
        if ($scope.OrganizationListTotal > 50) {
            for (let i in $scope.checkOrganizationList) {
                $scope.contentBelongOrgList.push({
                    ownerType: 1,
                    ownerID: $scope.checkOrganizationList[i].id,
                    orgName: $scope.checkOrganizationList[i].orgName
                })
                $scope.orgIds.push($scope.checkOrganizationList[i].id);
            }

        } else {
            for (var i in $scope.pushObjArrTemp) {
                if ($scope.pushObjArrTemp[i] == '1' && i != "-1") {
                    $scope.contentBelongOrgList.push({
                        ownerType: 1,
                        ownerID: $scope.orgList[i].id,
                        orgName: $scope.orgList[i].orgName
                    })
                    $scope.orgIds.push($scope.orgList[i].id);
                }
            }
        }
        var menberCheckReq = {
            "enterpriseID": $scope.enterpriseID,
            "orgIDList": $scope.orgIds,
            "servType": 5,
            "subServType": $scope.initPrintInfo.subServType,
            "chargeType": $scope.initPrintInfo.chargeType
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryMemberCntByChgType",
            data: JSON.stringify(menberCheckReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        //如果当前选择的分组下没有成员
                        if (result.memberNum === '0') {
                            $scope.tip = '1030120700';
                            $('#myModal').modal();
                            return;
                        } else {
                            $scope.submitReview();
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });

    };
    $scope.submitReview = function () {
        jQuery.each($scope.colorContentAndFileList, function (i, e) {
            $scope.certificateUrlList.push(e.frameFileUrl)
        });
        if ($scope.initPrintInfo.subServType == '3') {
            var colorContentLength = $scope.initPrintInfo.colorContent.length;
            var calledContentLength = $scope.initPrintInfo.calledContent.length;
            var contentLength = colorContentLength + calledContentLength;
            if (contentLength <= 0) {
                $scope.tip = '请填写彩印内容！';
                $('#myModal').modal();
                return;
            }
        }
        if ($scope.initPrintInfo.colorContent != null || $scope.initPrintInfo.calledContent != null) {
            var sign = 0;

            if ($scope.initPrintInfo.signature != null) {
                sign = $scope.initPrintInfo.signature.length;
            }

            var content = $scope.initPrintInfo.colorContent ? $scope.initPrintInfo.colorContent.length : 0;
            var callContent = $scope.initPrintInfo.calledContent ? $scope.initPrintInfo.calledContent.length : null;

            var str_len = sign + content;
            var callStr_len = sign + callContent;
            var total_len = 70;

            if (str_len > total_len || (callContent && callStr_len > total_len)) {
                $scope.tip = '1030120089';
                $('#myModal').modal();
                return;
            }
        }
        $scope.failOrgName = "";
        var req = {
            "contentInfo": {
                "enterpriseID": $scope.enterpriseID,
                "enterpriseName": $scope.enterpriseName,
                "subServType": $scope.initPrintInfo.subServType,
                "servType": 5,
                "chargeType": 1,
                "blackwhiteListType": "",
                "contentPushTime": {
                    "startTime": $scope.initPrintInfo.contentPushTime.startTime,
                    "endTime": $scope.initPrintInfo.contentPushTime.endTime,
                },
                "content": $scope.initPrintInfo.colorContent,
                "calledContent": $scope.initPrintInfo.calledContent,
                "contentType": 2,
                "thirdpartyType": 0,
                "deliveryDate": $scope.deliveryDateArr.join(''),
                "operatorID": $scope.operatorID,
                "contentTitle": $scope.initPrintInfo.colorTitle,
                "signature": $scope.initPrintInfo.signature,
                "platforms": $scope.platforms,
                "industryType": $scope.selectedIndustryID,
                "businessLicenseURL": $scope.businessLicenseURL_,
                "certificateUrlList": $scope.certificateUrlList,
                "sceneDesc": $scope.initPrintInfo.sceneDesc
            },
            "fileUrlCaller": $scope.fileUrlCaller,
            "fileUrlCalled": $scope.fileUrlCalled
        };
        if ($scope.contentBelongOrgList.length > 0) {
            req.contentInfo.contentBelongOrgList = $scope.contentBelongOrgList;
        }
        var _f0 = $('.black-white .redio-li').eq(0).find('span').hasClass('checked'),
            _f1 = $('.black-white .redio-li').eq(1).find('span').hasClass('checked'),
            _f2 = $('.black-white .redio-li').eq(2).find('span').hasClass('checked');

        if (_f1) {
            req.contentInfo.blackwhiteListType = 1;
        } else if (_f2) {
            req.contentInfo.blackwhiteListType = 2;
        } else if (_f0) {
            req.contentInfo.blackwhiteListType = 0;
        }

        if ($scope.operateType == 'add') {
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/createContent",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        // || data.resultCode == '1030120017'
                        if (data.resultCode == '**********') {
                            window.location.href = '../provincialTemplate/queryProvinceTemplateList.html';
                        } else if (data.resultCode == '1030120030') {
                            $scope.failOrgName = result.extInfo.failOrgName.replace(/\|/g, ',');
                            $scope.noRepeat = true;
                            $scope.tip = "";
                            $('#myModal').modal();
                        } else if (result.failedListUrl) {
                            $scope.tip = result.failedNum + "条导入失败，请查看失败文件";
                            $('#myModal').modal();
                            var failUrlList = result.failedListUrl;
                            for (var i = 0; i < failUrlList.length; i++) {
                                var req = {
                                    "param": {
                                        "path": failUrlList[i],
                                        "token": $scope.token,
                                        "isExport": 0
                                    },
                                    "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
                                    "method": "get"
                                }
                                CommonUtils.exportFile(req);
                            }

                        } else {
                            $scope.noRepeat = false;
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })

                },
                error: function () {
                    $rootScope.$apply(function () {
                            $scope.noRepeat = false;
                            $scope.tip = "1030120500";
                            $('#myModal').modal();
                        }
                    )
                }
            });
        } else if ($scope.operateType == 'modify') {
            req.contentInfo.contentID = $scope.contentID;
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/updateContent",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '**********') {
                            window.location.href = '../provincialTemplate/queryProvinceTemplateList.html';
                        } else if (data.resultCode == '1030120030') {
                            $scope.noRepeat = true;
                            $scope.tip = "";
                            $scope.failOrgName = result.extInfo.failOrgName;
                            $('#myModal').modal();
                        } else if (data.resultCode == '1030120091') {
                            $scope.noRepeat = false;
                            $scope.tip = "模板存在待审核的变量内容，无法修改";
                            $('#myModal').modal();
                        } else if (result.failedListUrl) {
                            $scope.tip = result.failedNum + "条导入失败，请查看失败文件";
                            $('#myModal').modal();
                            var failUrlList = result.failedListUrl;
                            for (var i = 0; i < failUrlList.length; i++) {
                                var req = {
                                    "param": {
                                        "path": failUrlList[i],
                                        "token": $scope.token,
                                        "isExport": 0
                                    },
                                    "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
                                    "method": "get"
                                }
                                CommonUtils.exportFile(req);
                            }

                        } else {
                            $scope.noRepeat = false;
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                            $scope.noRepeat = false;
                            $scope.tip = "1030120500";
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }

    };
    $scope.intoFocus = function () {
        $scope.isOnColorTitleFocus = true;
    }
    $scope.sensitiveCheck = function (content, index) {
        if (!content) {
            return;
        }
        var req = {
            "content": content || '',
        };

        $scope.sensitiveWords[index] = [];
        $scope.sensitiveWordsStr[index] = '';
        $scope.isSensitive[index] = false;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030120017') {
                    $scope.sensitiveWords[index] = result.sensitiveWords || [];
                    if ($scope.sensitiveWords[index].length > 0) {

                        $scope.isSensitive[index] = true;
                        $scope.sensitiveWordsStr[index] = $scope.sensitiveWords[index].join('、');
                    } else {
                        $scope.isSensitive[index] = false;
                    }
                } else if (data.resultCode == '**********') {
                    $scope.sensitiveWords[index] = [];
                    $scope.isSensitive[index] = false;
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
                // })
            },
            error: function () {
                // $rootScope.$apply(function () {
                $scope.tip = '1030120500';
                $('#myModal').modal();
                // })
            }
        });
    };
    $scope.$on("ngRepeatFinished", function (repeatFinishedEvent, element) {
        //默认全是1
        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr1);
        if ($scope.operateType !== 'detail') {
            $('.pushObj .check-li').unbind("click");
            $('.pushObj .check-li').on('click', function (e) {
                $(this).find('span').toggleClass('checked');
                var _index = $(this).index();
                if ($(this).find('span').hasClass('checked')) {
         $rootScope.$apply(function () {
                    $scope.chosePushObj = true;
                    if (_index == 0) {
                        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr1);
                        $('.pushObj .check-li span').not(":first").removeClass('checked')
                    } else {
                        $($('.pushObj .check-li span')[0]).removeClass('checked');
                        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr0);
                        $('.pushObj .check-li').not(":first").each(function () {
                            if ($(this).find('span').hasClass('checked')) {
                                var _index1 = $(this).index();
                                $scope.pushObjArrTemp[_index1 - 1] = '1';
                            }
                        })
                    }
         })
                } else {
         $rootScope.$apply(function () {
                    if (_index == 0) {
                        $scope.pushObjArrTemp = angular.copy($scope.pushObjArr0);
                    } else {
                        $scope.pushObjArrTemp[_index - 1] = '0';
                    }
         })
                }
//        console.log($scope.pushObjArrTemp)
                //50条时换成弹窗
                if ($scope.OrganizationListTotal > 50) {
                    let orgId = $(e.currentTarget).find("span").attr("value");
                    if ($(e.currentTarget).find("span").hasClass("checked")) {
                        //选中
                        let org = $scope.findFromGroupList(orgId, $scope.checkedOrgListTemp);
                        //原未选中才添加
                        if (!org) {
                            org = $scope.findFromGroupList(orgId, $scope.allOrgList);
                            if (org) {
                                $scope.checkedOrgListTemp.push(org);
                            }
                        }
                        if (orgId === "all") {
                            $scope.isNoLimitTemp = true;
                        } else {
                            $scope.isNoLimitTemp = false;
                        }
                    } else {
                        $scope.moveFromGroupList(orgId, $scope.checkedOrgListTemp);
                        if (orgId === "all") {
                            $scope.isNoLimitTemp = false;
                        }
                    }
                    if (!$scope.isNoLimitTemp) {
                        //重新渲染
                        setTimeout(function () {
                            for (let i = 0; i < $scope.checkedOrgListTemp.length; i++) {
                                $('.pushObj.max .check-li span[value="' + $scope.checkedOrgListTemp[i].id + '"]').addClass("checked")
                            }
                        }, 300);
                    }

                }
            });
        }
    });
    if ($scope.operateType !== 'detail') {
        $(function () {
            $('.pushDate .check-li').on('click', function () {
                $(this).find('span').toggleClass('checked');
                var _index = $(this).index();
                if ($(this).find('span').hasClass('checked')) {
                    if (_index == 0) {
                        $('.pushDate .check-li span').not(":first").removeClass('checked')
                        $rootScope.$apply(function () {
                            $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];
                        })
                    } else {
                        $($('.pushDate .check-li span')[0]).removeClass('checked');
                        $scope.deliveryDateArr = ['0', '0', '0', '0', '0', '0', '0'];
                        $('.pushDate .check-li').not(":first").each(function () {
                            if ($(this).find('span').hasClass('checked')) {
                                var _index1 = $(this).index();
                                $scope.deliveryDateArr[_index1 - 1] = '1';
                            }
                        })
                        // if($scope.deliveryDateArr.indexOf('1')==-1){
                        //     $($('.pushDate .check-li span')[0]).addClass('checked');
                        //     $scope.deliveryDateArr=['1','1','1','1','1','1','1'];
                        // }
                    }
                } else {
                    if (_index == 0) {
                        $rootScope.$apply(function () {
                            // $scope.deliveryDateArr=['0','0','0','0','0','0','0'];
                            $($('.pushDate .check-li span')[0]).addClass('checked');
                            $scope.deliveryDateArr = ['1', '1', '1', '1', '1', '1', '1'];

                        })
                    } else {
                        $scope.deliveryDateArr[_index - 1] = '0';
                        //判断当前是否是最后一个非不限选择，是的话不给置0
                        if ($scope.deliveryDateArr.indexOf('1') == -1) {
                            $scope.deliveryDateArr[_index - 1] = '1';
                            $(this).find('span').addClass('checked');
                        }
                    }
                }
                console.log($scope.deliveryDateArr)
            })
            $('.glyphicon-calendar').click(function () {
                $('#time-config').trigger('click');

            })
        })

    }
    if ($scope.operateType != 'detail') {
        $('.black-white .redio-li').on('click', function () {
            $(this).find('span').addClass('checked');
            $(this).siblings('.redio-li').find('span').removeClass('checked')
        });
    }

    //初始化营业执照上传容器
    $scope.initBusinessURLContainer = function () {
        //初始化营业执照上传容器
        $scope.filePicker_ = "filePicker2";
        $scope.accepttype_ = "jpg,jpeg,png";
        $scope.isValidate_ = false;
        $scope.filesize_ = 20;
        $scope.mimetypes_ = ".jpg,.jpeg,.png";
        $scope.isCreateThumbnail_ = true;
        $scope.uploadurl_ = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.uploadDesc_ = "仅支持一张图片，仅支持jpg，jpeg，png格式";
        $scope.numlimit_ = 1;
        $scope.urlList_ = [];
        $scope.uploadParam_ = {
            enterpriseId: $scope.enterpriseID || '',
            fileUse: 'businessLicense'
        };

        $scope.$on("uploadifyid_1", function (event, fileUrl_) {
            if (fileUrl_) {
                $scope.urlList_ = [fileUrl_];
                $scope.urlList_2 = [fileUrl_];
            } else {
                $scope.urlList_ = [];
                $scope.urlList_2 = [];
            }
            $scope.businessLicenseURL_ = fileUrl_;
        });
    }


    //所属行业是否为敏感行业
    $scope.changeIsSensitive = function (selectedIndustry) {
        if (selectedIndustry) {
            $scope.selectedIndustryID = selectedIndustry.industryID;
            $scope.isSensitiveIndustry = selectedIndustry.isSensitiveIndustry;
        } else {
            $scope.selectedIndustryID = '';
            $scope.selectedIndustryName = '';
        }
    }

    //查询所属行业
    $scope.queryIndustry = function ($scope) {
        //默认非敏感行业:0-非敏感；1-敏感
        $scope.selectedIndustryID = '';
        $scope.isSensitiveIndustry = '';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.industryList = data.industryList;
                    } else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '1030120500';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    //对象选择临时存放
    $scope.checkedOrgListTemp = [];
    $scope.isNoLimit = false;
    $scope.isNoLimitTemp = false;
    //分组配置弹出
    $scope.groupListPop = function (item) {
        $scope.pageInfo[0].currentPage = 1;
        $('#groupListPop').modal();
        $scope.queryOrg($scope.pageInfo);
        $scope.isNoLimitTemp = $scope.isNoLimit;

        if ($scope.isNoLimit) {
            $('.pushObj.max .check-li span.isLimit').addClass("checked");
            $scope.checkedOrgListTemp = [];
        } else {
            $scope.checkedOrgListTemp = $scope.checkOrganizationList.concat();
        }

    };

    //配置对象选择提交
    $scope.submitSelect = function (item) {
        //判断是否不限
        let oldIsNoLimit = $scope.isNoLimit;
        $scope.isNoLimit = $('.pushObj.max .check-li span.isLimit').hasClass("checked");
        if ($scope.isNoLimit) {
            $scope.checkOrganizationList = $scope.allOrgList;
            $('#groupListPop').modal("hide");
            return;
        } else {
            if (oldIsNoLimit) {
                $scope.checkOrganizationList = [];
            }
        }
        $scope.checkOrganizationList = $scope.checkedOrgListTemp.concat();
        $('#groupListPop').modal("hide");

        if ($scope.checkOrganizationList.length > 50) {
            $scope.queryCheckedOrg();
        }
    };
    //找分组
    $scope.findFromGroupList = function (orgId, groupList) {
        for (let i = 0; i < groupList.length; i++) {
            if (groupList[i].id == orgId) {
                return groupList[i];
            }
        }
    }
    //移除分组
    $scope.moveFromGroupList = function (orgId, groupList) {
        groupList.remove = function (v) {
            if (isNaN(v) || v > this.length) {
                return false
            }
            for (let i = 0, j = 0; i < this.length; i++) {
                if (this[i] != this[v]) {
                    this[j++] = this[i]
                }
            }
            this.length -= 1
        };
        for (let i = 0; i < groupList.length; i++) {
            if (groupList[i].id == orgId) {
                groupList.remove(i);
                return;
            }
        }
    };
    //数组分组
    $scope.group = function (array, subGroupLength) {
        let index = 0;
        let newArray = [];
        while (index < array.length) {
            newArray.push(array.slice(index, index += subGroupLength));
        }
        return newArray;
    };

    //查询企业服务开关
    $scope.queryPlatformStatus = function () {
        var queryServiceControlReq = {
            "enterpriseID": $scope.enterpriseID
        }
        $scope.signatureRequired = '0';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceControl",
            data: JSON.stringify(queryServiceControlReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.platformStatus = $scope.isZYZQ ? result.rxPlatformStatus : result.platformStatus;
                    if ($scope.operateType == 'add') {
                        var platformStatus = $scope.platformStatus;
                        $scope.platforms = platformStatus;
                        //初始化signature必填状态
                        $scope.signatureRequired = '0';
                        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
                            $scope.signatureRequired = '1';
                        }
                    }
                    $scope.querySyncServiceRule();
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.queryProductSubscribe = function ($scope) {
        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": 5,
            "subServType": $scope.initPrintInfo.subServType

        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryProductSubscribe",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        if (data.chargeType) {
                            $scope.initPrintInfo.chargeType = data.chargeType;
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.$watch('initPrintInfo.signature',function(){
            $scope.maximumLengthOfSignature();
            $scope.checkSignAndContent();
    },true)

    $scope.checkSignAndContent = function () {
        var total_len = 70;
        $scope.checkContentSign = 0;
        $scope.checkCallContentSign = 0;
        var signature = $scope.initPrintInfo.signature;
        if (signature) {
            //212企管平台彩印签名长度优化需求
            $scope.signAndContent = ($scope.initPrintInfo.colorContent) ? ($scope.calculateContentLength($scope.initPrintInfo.colorContent) + signature.length + 2) : signature.length + 2;
            $scope.signAndCallContent = ($scope.initPrintInfo.calledContent) ? ($scope.calculateContentLength($scope.initPrintInfo.calledContent) + signature.length + 2) : signature.length + 2;
        } else {
            $scope.signAndContent = ($scope.initPrintInfo.colorContent) ? ($scope.calculateContentLength($scope.initPrintInfo.colorContent)) : 0;
            $scope.signAndCallContent = ($scope.initPrintInfo.calledContent) ? ($scope.calculateContentLength($scope.initPrintInfo.calledContent)) : 0;
        }
        if(total_len  < $scope.signAndContent){
            $scope.checkContentSign = 1;
        }else {
            $scope.checkContentSign = 0;
        }
        if(total_len  < $scope.signAndCallContent){
            $scope.checkCallContentSign = 1;
        }else {
            $scope.checkCallContentSign = 0;
        }
    }
    
    $scope.calculateContentLength = function (content) {
    	  if(content) {
    		  for(var i=1;i<10;i++) {
    			  content = content.replace("{" + i + "}", i);
    		  }
    		  return content.length;
    	  } else {
    		  return 0;
    	  }
      }

    $scope.$watch('initPrintInfo.colorContent',function(){
        $scope.maximumLengthOfSignature();
        $scope.checkSignAndContent();
    },true)

    $scope.maximumLengthOfSignature = function () {
        if($scope.initPrintInfo.signature){
            $scope.maxSignLength = 70 - $scope.initPrintInfo.signature.length;
        }else {
            $scope.maxSignLength = 70;
        }
    }

    $scope.$watch('initPrintInfo.calledContent',function(){
        $scope.maximumLengthOfSignature();
        $scope.checkSignAndContent();
    },true)

    $scope.$watch('initPrintInfo.subServType', function(newVal, oldVal) {
                if (newVal !== oldVal) {
                    // 重新计算contentType的值
                    $scope.updateContentType();
                }
            });

    $scope.updateContentType = function() {
        // 根据subServType设置正确的contentType
        if ($scope.initPrintInfo.subServType == '3'
        || $scope.initPrintInfo.subServType == '1'
        || $scope.initPrintInfo.subServType == '2')  {
            $scope.contentType =  $scope.initPrintInfo.subServType;
        } else {
            $scope.contentType = 0
        }
    };
})
app.directive('onRepeatFinishedRender', function ($timeout) {
    return {
        restrict: 'A',
        link: function (scope, element, attr) {
            if (scope.$last === true) {
                $timeout(function () {
                    //这里element, 就是ng-repeat渲染的最后一个元素
                    scope.$emit('ngRepeatFinished', element);
                    if (scope.htmlflat) {
                        scope.mapDataToHtml();
                    }
                });
            }
        }
    };
});

app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])