var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils',function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否超管
        $scope.isSuperManager = false;
        $scope.isAgent = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isAgent = (loginRoleType == 'agent');

        $scope.enterpriseID = "";

        if ($scope.isAgent) {
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
        }

        //默认业务类别为：不限
        $scope.serviceType = "";

        $scope.enterpriseName = "";

        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            },
            {
                id: 4,
                name: "增彩"
            }

        ];

        //初始化搜索条件
        $scope.initSel = {
            startTime: "",
            endTime: "",
            search: false,
        };

        $scope.exportFile=function(){
            var req = {
              "param":{
                "enterpriseName":$scope.enterpriseName,
                "serviceType":'4',
                "startDate":$scope.setTime($scope.initSel.startTime),
                "endDate":$scope.setTime($scope.initSel.endTime),
                "areaDimension":3,
                "timeDimension":2,
                "enterpriseID":$scope.enterpriseID,
                "parentEnterpriseID":"",
                "cityID":"",
                "provinceID":"",
                "enterpriseType":2,
                "type":14,
                "token":$scope.token,
                "isExport":1
              },
              "url":"/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile",
              "method":"get"
            }
            CommonUtils.exportFile(req);
        }

        //$scope.queryEnterpriseStatInfo();
    }

    $scope.setTime = function (time) {
        if (time == "")
        {
            return "";
        }
        var year = time.slice(0, 4);
        var month = time.slice(5, 7);
        return year + month + "01000000";
    }
      
    $scope.getTime = function (experienceStartTime, experienceEndTime) {

        if (experienceStartTime == null || experienceEndTime == null)
        {
            return "";
        } 
        var startYear = experienceStartTime.slice(0, 4);
        var startMonth = experienceStartTime.slice(4, 6);
        var startDay = experienceStartTime.slice(6, 8);
        var endYear = experienceEndTime.slice(0, 4);
        var endMonth = experienceEndTime.slice(4, 6);
        var endDay = experienceEndTime.slice(6, 8);
        return startYear + "-" + startMonth + "-" + startDay + "-" + endYear + "-" + endMonth + "-" + endDay;
    }

    $scope.getServiceType = function (serviceType,subServType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 3) {
            return "广告彩印";
        }
        else if (serviceType == 4 && subServType == 16) {
            return "增彩";
        }
        else if (serviceType == 4 && subServType == 17) {
            return "短信";
        }
        else if (serviceType == 4 && subServType == 3) {
            return "屏显";
        }
        else if (serviceType == 4 && subServType == 8) {
            return "彩信";
        }
    };

    $scope.getSubServType = function (subServType) {
        if (subServType == 1) {
            return "主叫屏显";
        }
        else if (subServType == 2) {
            return "被叫屏显";
        }
        else if (subServType == 4) {
            return "挂机短信";
        }
        else if (subServType == 8) {
            return "挂机彩信";
        }
        else if (subServType == 3) {
            return "屏显";
        }
        else if (subServType == 16) {
            return "挂机增彩";
        }
    };

    $('.input-daterange').datepicker({
        format: "yyyy-mm",
        startView: 1,
        minViewMode: 1,
        clearBtn: true,
        language: "zh-CN",
        orientation: "bottom left",
        autoclose: true
    });

    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $scope.showTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        return year + "-" + month;
    }

    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        $scope.initSel.startTime = document.getElementById("start").value;
        $scope.initSel.endTime = document.getElementById("end").value;

        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }
    }

    //后续post的函数
    $scope.queryEnterpriseStatInfo = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "areaDimension": 3,
                "timeDimension": 2,
                "enterpriseType":2,
                "enterpriseName": $scope.enterpriseName,
                "enterpriseID": $scope.enterpriseID|| '',
                "serviceType": '4',
                "startDate": $scope.setTime($scope.initSel.startTime) || '',
                "endDate": $scope.setTime($scope.initSel.endTime) || '',
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };

            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile?enterpriseName=" + $scope.enterpriseName + "&serviceType=" + $scope.serviceType + "&startDate=" +
            $scope.setTime($scope.initSel.startTime) + "&endDate=" + $scope.setTime($scope.initSel.endTime) + "&areaDimension=3" + "&timeDimension=2" +"&enterpriseID=" + $scope.enterpriseID + "&parentEnterpriseID=" + "&cityID=" +
            "&provinceID=" + "&enterpriseType=2" + "&type=9";
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseStatInfo",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if(data.resultCode=='1030100000'){
                $scope.StatInfoListData=result.enterpriseStatInfoList||[];
                $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                $scope.pageInfo[0].totalPage=result.totalNum!==0 ?Math.ceil(result.totalNum/parseInt($scope.pageInfo[0].pageSize)):1;
              }  
              else 
              {
                $scope.tip = data.resultCode;
                $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
            )
          }
        });

    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])