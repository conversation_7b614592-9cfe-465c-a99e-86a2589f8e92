<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DiffnetDeliveryWayMapper">
	<resultMap id="diffnetDeliveryWayWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DiffnetDeliveryWayWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="isDefault" column="isDefault" javaType="java.lang.Integer" />
		<result property="wayType" column="wayType" javaType="java.lang.Integer" />
		<result property="platforms" column="platforms" javaType="java.lang.String" />
		<result property="enterpriseType" column="enterpriseType" javaType="java.lang.String" />
		<result property="servType" column="servType" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="dealCount" column="dealCount" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
	</resultMap>

	
	<select id="queryList" resultMap="diffnetDeliveryWayWrapper">
		SELECT id,
		isDefault,
		wayType,
		platforms,
		enterpriseType,
		servType,
		status,
		dealCount,
		createTime,
		updateTime
		FROM ecpm_t_diffnet_delivery_way
		where 1=1
		<if test="isDefault!=null">
			and isDefault=#{isDefault}
		</if>
		<if test="wayType!=null">
			and wayType=#{wayType}
		</if>
	</select>
	
	<update id="batchUpdate" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="item" open="" separator=";">
			UPDATE ecpm_t_diffnet_delivery_way 
			SET
			<trim suffixOverrides="," suffix="where id = #{item.id}">
			<if test="item.platforms !=null">
				platforms= #{item.platforms},
			</if>
			<if test="item.enterpriseType !=null">
				enterpriseType= #{item.enterpriseType},
			</if>
			<if test="item.servType !=null">
				servType =#{item.servType},
			</if>
			updateTime = SYSDATE()
		</trim>
		</foreach>
	</update>
</mapper>