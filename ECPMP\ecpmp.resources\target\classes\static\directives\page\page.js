angular.module('page',[]).directive('ptlPage', function () {
    return {
        restrict: 'AE',
        templateUrl: '/qycy/ecpmp/directives/page/page.html',
        
        replace: true,
        transclude: true,
        scope: true,
        controller: function ($scope, $element, $attrs, $rootScope) {

            //tableId见组件上方注释一个页面中需要用到多个table时用到)
            var tableId = $attrs.tableid ? $attrs.tableid : false;
            var method = $attrs.change;
            $scope.toPage="";
            if(tableId){
                $scope.pageInfoDirective = $scope.pageInfo[tableId];
            }else{
                $scope.pageInfoDirective = $scope.pageInfo;
            }

            $scope.pageToGo = $scope.pageInfoDirective.currentPage;

          $scope.$watch("pageInfoDirective",function(newValue,oldValue){
            if(newValue){
              $scope.pageToGo = newValue.currentPage;
            }
          },true);

            $scope.translateNum = function(){
                var pageToGoNum =parseInt($scope.pageToGo);
                //如果总页数为1或者跳转输入值等于当前页，则不执行调转操作
                if( $scope.pageInfoDirective.totalPage == 1 || $scope.pageInfoDirective.currentPage == pageToGoNum){
                    return;
                }else{
                    if(pageToGoNum>0 && pageToGoNum<=$scope.pageInfoDirective.totalPage){
                        $scope.pageInfoDirective.currentPage = pageToGoNum;
                        $scope.excuteMethod();
                    }
                }
            }

          //执行翻页方法
            $scope.excuteMethod = function(){
              if(tableId){
                $scope.pageInfo[tableId] = $scope.pageInfoDirective;
              }else{
                $scope.pageInfo = $scope.pageInfoDirective;
              }
              if ( typeof (method) != "undefine") {
                $scope.$eval(method);
              }
            }

            //选择pageSize时方法
            $scope.changeSizeDirectives = function(size){
              $scope.pageInfoDirective.currentPage = 1;
              $scope.pageToGo = 1;
              $scope.excuteMethod();
            };

            /**
             * 翻页事件
             */
            $scope.changePageDirectives = function(pageNum){
                if(pageNum>0 && pageNum<=$scope.pageInfoDirective.totalPage){
                    $scope.pageInfoDirective.currentPage = pageNum;
                }else if(pageNum==0){
                    return;
                }else if(pageNum>$scope.pageInfoDirective.totalPage){
                    return;
                }else{
                    return;
                }
                $scope.pageToGo = $scope.pageInfoDirective.currentPage;
                $scope.excuteMethod();
            }

            /**
             * 点击跳转到某一固定页
             */
            $scope.gotoPage=function(){
                if($scope.toPage){
                    if($scope.toPage>=$scope.pageInfoDirective.totalPage){
                        $scope.toPage=$scope.pageInfoDirective.totalPage;
                    }else if($scope.toPage<=1){
                        $scope.toPage=1;
                    }
                    $scope.changePageDirectives(parseInt($scope.toPage));
                }else if($scope.toPage==0){
                    $scope.toPage=1;
                    $scope.changePageDirectives($scope.toPage);
                }
               
            }
        },
        compile : function(tElement, tAttr, linker){
        }

    };
});