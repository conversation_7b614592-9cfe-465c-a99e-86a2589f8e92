<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>号码管理</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
    <link href="../../../../../css/reset.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
        src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <!--tab页切换-->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
    <link rel="stylesheet" type="text/css" href="../../../../../css/webuploader.css">
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <!--引入JS-->
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>

    <script type="text/javascript" src="HotlineManagement.js"></script>

</head>

<body ng-app="myApp" ng-controller="hotLineController" ng-init="init()" class="body-min-width">
    <div class="cooperation-manage">
        <!-- <span class="frist-tab" ng-bind="'COMMON_SUBENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;<span
            class="second-tab" ng-bind="'WLT_MANAGER'|translate"></span> -->
        <div ng-if="loginRoleType=='agent'" class="cooperation-head">
	        <span class="frist-tab" ng-bind="'COMMON_SUBENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;
	        <span class="second-tab" ng-bind="'WLT_MANAGER'|translate"></span>
	    </div>
        <!-- 管理员登陆查看代理商二级企业
        <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/hotline/ussdAccessSettings"
            list-index="50" ng-if="isSuperManager&&enterpriseType==3"></top:menu>
          -->
        <!-- 代理商用户登陆查看二级企业 -->
        <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/expressageManage/hotline/HotlineManagement"
            list-index="76" ng-if="loginRoleType=='agent'"></top:menu>
        <div class="cooperation-search">
            <form class="form-inline">
                <div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
                    <label style="padding-right:30px;" for="contentName" ng-bind="'HOTLINE_NUMBER'|translate"></label>
                    <input ng-model="hotlineNo" type="text" autocomplete="off" class="form-control" id="contentName"
                        placeholder="{{'NUMBER_PLEASEINPUTHOTLINENUMBER'|translate}}">
                </div>
                <div class="form-group col-lg-1 col-xs-1  col-sm-1 col-md-1">
                    <button type="submit" class="btn search-btn" ng-click="queryHotInfoList()">
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
            </form>
        </div>
        <div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="addHot()">
                <icon class="add-iocn"></icon>
                <span ng-bind="'NUMBER_ADDHOTLINE'|translate"></span>
            </button>
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px" ng-bind="'NUMBER_MANAGERMENT'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                    	<th ng-if="loginRoleType=='zhike'||enterpriseType==1" style="width:20%"
                            ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></th>
                            <th ng-if="loginRoleType=='agent'||enterpriseType==3" style="width:20%"
                            ng-bind="'ENTERPRISE_ENTERPRISEID1'|translate"></th>
                        <th ng-if="loginRoleType=='zhike'||enterpriseType==1" style="width:20%"
                            ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>
                        <th ng-if="loginRoleType=='agent'||enterpriseType==3" style="width:20%"
                            ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></th>
                        <th style="width:15%" ng-bind="'HOTLINE_PROVE'|translate"></th>
                        <th style="width:15%" ng-bind="'HOTLINE_NUMBER'|translate"></th>
                        <!--<th ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>-->
                        <th style="width:30%" ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in hotContentInfoListData">
                        <td><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
                        <td><span title="{{enterpriseName}}">{{enterpriseName}}</span></td>
                        <td>
                            <a ng-show='item.hotlinePicUrl' ng-click="picturePop(item,'hotlineLicense')"
                                title="{{item.hotlinePicUrl}}" ng-style="{{item.style1}}">{{'COMMON_CHECK_PIC'|translate}}</a>
                            <span ng-show='!item.hotlinePicUrl'>{{'COMMON_NOPIC'|translate}}</span>
                        </td>
                        <td><span title="{{item.hotlineNo}}">{{item.hotlineNo}}</span></td>
                        <!--<td><span title="item.auditStatus">{{item.auditStatus}}</span></td>-->
                        <td class="coorPeration-table-a">
                            <div class="handle">
                                <ul>
                                    <li class="edit" ng-click="updateHot(item)">
                                        <icon class="edit-icon"></icon>
                                        <span ng-bind="'GROUP_EDIT'|translate"></span>
                                    </li>
                                    <li class="delete" ng-click="delHot(item)">
                                        <icon class="delete-icon"></icon>
                                        <span ng-bind="'COMMON_DELETE'|translate"></span>
                                    </li>
                                    <li ng-if="isSuperManager&&enterpriseType==1" class="query"
                                        ng-click="selHot(item)">
                                        <icon class="query-icon" style="background-position: -72px;"></icon>
                                        <span style="color: #7360e1;" ng-bind="'HOTLINE_WATCH'|translate"></span>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="hotContentInfoListData.length==0">
                        <td style="text-align:center" colspan="5" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div>
            <ptl-page tableId="0" change="queryHotInfoList('justPage')"></ptl-page>
        </div>
    </div>

    <!--查看图片弹出框-->
    <div class="modal fade bs-example-modal-sm" id="picUrlListPop" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm model-lg model-md" role="document" style="width:40%">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel">{{'COMMON_PREVIEW'|translate}}</h4>
                </div>
                <div class="modal-body">
                    <div class="img-wrap">
                        <img style="width: 100%;height: 100%" ng-src="{{pictureUrl}}" alt="加载失败">
                    </div>
                    <p ng-show="!pictureUrl">{{'COMMON_NOPICTURE'|translate}}</p>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        style="z-index:555555;">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838;text-align: center'>
                            {{tip|translate}}
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <!--新增热线弹窗-->
    <div class="modal fade" id="addHotLine" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" ng-if="isShowAdd"
        data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                        ng-click="closeAdd()"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'HOTLINE_ADDHOTLINE'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" name="addForm">
                        <div class="form-group">
                            <div class="row" style="height: 34px;">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label" style="width: 157px">
                                    <icon>*</icon><span ng-bind="'HOTLINE_NUMBER'|translate"></span>
                                </label>
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
                                    <input type="text" class="form-control" style="display: inline-block"
                                        ng-model="addHotlineInfo.hotlineNo" name="addHotlineNo"
                                        placeholder="{{'HOTLINE_PLEASEINPUTHOTLINENUMBER'|translate}}" required
                                        pattern="^[0-9]{1,14}$" />
                                    <span style="color:red;line-height: 34px;display: block;"
                                        ng-show="addForm.addHotlineNo.$dirty && addForm.addHotlineNo.$invalid">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                            align="absmiddle">
                                        <span ng-show="addForm.addHotlineNo.$error.required"
                                            ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                                        <span ng-show="addForm.addHotlineNo.$error.pattern"
                                            ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                ng-bind="'HOTLINE_PROVE'|translate"></label>
                            <cy:uploadify filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
                                uploadifyid="uploadifyidHotLine" validate="isValidate" filesize="filesize"
                                mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc"
                                numlimit="numlimit" urllist="urlList" createthumbnail="isCreateThumbnail"
                                namelistid="nameList" class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
                            </cy:uploadify>
                            <img ng-src="{{fileUrl}}" width="100" height="100" align="absmiddle"
                                ng-show="!isShowHotPic">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_SAVE'|translate"
                        ng-click="createHotline(addHotlineInfo.hotlineNo)"
                        ng-disabled="addForm.addHotlineNo.$invalid"></button>
                    <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-click="closeAdd()" id="addHotlineCancel" style="margin-left: 20px"
                        ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <!--热线编辑-->
    <div class="modal fade" id="updateHotLine" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
        ng-if="isShowEdit" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                        ng-click="closeEdit()"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'HOTLINE_EDIT'|translate"></h4>
                </div>
                <div class="modal-body">
                    <form class="form-horizontal" name="myName">
                        <div class="form-group">
                            <div class="row" style="height: 34px;">
                                <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label" style="width: 157px">
                                    <icon>*</icon><span ng-bind="'HOTLINE_NUMBER'|translate"></span>
                                </label>
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
                                    <input type="text" class="form-control" style="display: inline-block"
                                        ng-model="addHotlineInfo.hotlineNo"
                                        placeholder="{{'HOTLINE_PLEASEINPUTHOTLINENUMBER'|translate}}"
                                        name="editHotlineNo" required pattern="^[0-9]{1,14}$" />
                                    <span style="color:red;line-height: 34px;display: block;"
                                        ng-show="myName.editHotlineNo.$dirty && myName.editHotlineNo.$invalid">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                            align="absmiddle">
                                        <span ng-show="myName.editHotlineNo.$error.required"
                                            ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                                        <span ng-show="myName.editHotlineNo.$error.pattern"
                                            ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                ng-bind="'HOTLINE_PROVE'|translate"></label>
                            <cy:uploadify filelistid="fileList1" filepickerid="filePicker1" accepttype="accepttype"
                                uploadifyid="uploadifyid1" validate="isValidate" filesize="filesize"
                                mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc"
                                numlimit="numlimit" urllist="urlList" createthumbnail="isCreateThumbnail"
                                namelistid="nameList1" class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
                            </cy:uploadify>
                            <img ng-src="{{fileUrl}}" width="100" height="100" align="absmiddle"
                                ng-show="!isShowHotPic">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_SAVE'|translate"
                        ng-click="updateHotline(updateHotlineInfo.hotlineNo)"
                        ng-disabled="myName.editHotlineNo.$invalid"></button>
                    <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-click="closeEdit()" id="updateHotlineCancel" style="margin-left: 20px"
                        ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <!--删除弹窗-->
    <div class="modal fade" id="deleteHotLine" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="width:450px">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'CONFIRM_THEDELETION'|translate"></h4>
                </div>
                <div class="modal-body">
                    <p style="text-align:center;margin-top: 30px;" ng-bind="'HOTLINE_SUREDELETEGROUP'|translate"></p>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary search-btn" ng-click="delHotline()"
                        ng-bind="'COMMON_DELETE'|translate"></button>
                    <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        id="delHotlineCancel" style="margin-left: 20px" ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <!--内容框-->
    <div class="modal fade" id="selectHotLine" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document" style="width:690px">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'CONTENT_QUERY'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="coorPeration-table">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <td ng-bind="'GROUP_MEMBMSISDN'|translate"></td>
                                    <td ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></td>
                                    <td ng-bind="'HOTLINE_CONTENT'|translate"></td>
                                    <td ng-bind="'HOTLINE_DELIVERYSTATUS'|translate"></td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr ng-repeat="item in contentInfoList">
                                    <td>{{content_msisdn}}</td>
                                    <td><span
                                            title="{{statusMap[item.subServType]}}">{{statusMap[item.subServType]}}</span>
                                    </td>
                                    <td><span title="{{item.content}}">{{item.content}}</span></td>
                                    <td><span
                                            title="{{approveStatusMap[item.approveStatus]}}">{{approveStatusMap[item.approveStatus]}}</span>
                                    </td>
                                </tr>
                                <tr ng-show="contentInfoList.length<=0">
                                    <td style="text-align:center" colspan="4">{{'COMMON_NODATA'|translate}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div>
                        <ptl-page tableId="1" change="queryHot(selectedItem,'justPage')"></ptl-page>
                    </div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

</body>
<style>
    #filePicker div:nth-child(2) {
        width: 100% !important;
        height: 100% !important;
    }

    #filePicker1 div:nth-child(2) {
        width: 100% !important;
        height: 100% !important;
    }

    body {
        background: #f2f2f2;
    }

    table {
        table-layout: fixed;
    }

    table td {
        width: 100%;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .btn-back {
        border: 1px solid #cccccc;
    }

    .modal-footer {
        text-align: center;
    }

    .fontGreen {
        color: rgb(48, 147, 25)
    }

    .fontRed {
        color: rgb(252, 70, 93);
    }

    .control-label icon {
        color: red;
    }

    .cooperation-head {
        padding: 20px;
    }

    .cooperation-head .frist-tab {
        font-size: 16px;
    }

    .cooperation-head .second-tab {
        font-size: 14px;
    }

    .cooperation-manage .form-inline {
        margin: 0 20px;
        background: #fff;
        border-radius: 4px;
        padding: 20px;
        overflow: hidden;
    }

    .cooperation-manage .form-group {
        min-width: 255px;
        margin-bottom: 0px;
    }

    .form-group label {
        height: 34px;
        margin-bottom: 0
    }

    .form-group input {
        width: 260px;
    }

    .cooperation-manage .add-table {
        margin: 20px;
    }

    .cooperation-manage .add-table .add-btn {
        background: #fff;
        color: #7360e1;
    }

    .cooperation-manage .add-table .add-btn .add-iocn {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url(../../../../../assets/images/btnIcons.png)no-repeat;
        vertical-align: middle;
        background-position: -20px 0;
    }

    .coorPeration-table {
        margin: 0px 20px;
        background: #fff;
    }

    .cooperation-manage .coorPeration-table th,
    td {
        padding-left: 30px !important;
    }
</style>

</html>