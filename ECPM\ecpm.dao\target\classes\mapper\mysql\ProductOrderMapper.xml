<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ProductOrderMapper">
    <resultMap id="ProductOrderWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProductOrderWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="packageOrderID" column="packageOrderID" javaType="java.lang.Integer" />
        <result property="orderID" column="orderID" javaType="java.lang.String" />
        <result property="operateType" column="operateType" javaType="java.lang.String" />
		<result property="productCode" column="productCode" javaType="java.lang.String" />
		<result property="productOrderID" column="productOrderID" javaType="java.lang.String" />
		<result property="productOrderCharacter" column="productOrderCharacter" javaType="java.lang.String" />
		<result property="insertTime" column="insertTime" javaType="java.util.Date" />
		<result property="effictiveTime" column="effictiveTime" javaType="java.util.Date" />
		<result property="expireTime" column="expireTime" javaType="java.util.Date" />
		<result property="feeStartTime" column="feeStartTime" javaType="java.util.Date" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
    </resultMap>
    


	<insert id="createProductOrder">
		<selectKey keyProperty="id" resultType="java.lang.Integer" order="AFTER">
			select last_insert_id()
		</selectKey>
		insert into  ecpm_t_product_order
		(
		packageOrderID,
		orderID,
		operateType,
		productCode,
		productOrderID,
		productOrderCharacter,
		insertTime,
		effictiveTime,
		expireTime,
		feeStartTime,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5
		)
		values
		(
		#{packageOrderID},
		#{orderID},
		#{operateType},
		#{productCode},
		#{productOrderID},
		#{productOrderCharacter},
		#{insertTime},
		#{effictiveTime},
		#{expireTime},
		#{feeStartTime},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5}
		)
	</insert>


</mapper>