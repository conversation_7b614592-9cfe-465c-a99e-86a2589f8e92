<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.PushRuleMapper">
    <resultMap id="pushRuleWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.PushRuleWrapper">
        <result property="contentID" column="contentID" javaType="java.lang.Long" />
        <result property="dealStatus" column="dealStatus" javaType="java.lang.Integer" />
    </resultMap>

    <insert id="insertCity">
		INSERT INTO ecpm_t_hs_push_rule
		(
		contentID,
        dealStatus
		)
		VALUES
		(
		#{contentID},
		#{dealStatus}
		)
	</insert>

	<update id="updatePushRuleByContentId">
		update ecpm_t_hs_push_rule set dealStatus=#{dealStatus}
		where contentID=#{contentID} and dealStatus = 0;
	</update>

	<select id="queryPushRuleList" resultType="java.lang.Long">
		select contentID from ecpm_t_hs_push_rule where dealStatus = 0;
	</select>

</mapper>