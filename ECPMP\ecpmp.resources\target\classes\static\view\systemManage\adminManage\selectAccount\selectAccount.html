<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>查看账号</title>
    <link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
    <link href="../../../../css/reset.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css" />
    <link rel="stylesheet" type="text/css" href="../../../../css/webuploader.css">
    <link rel="stylesheet" type="text/css" href="../../../../css/font-awesome.min.css">

    <script type="text/javascript" src="selectAccountCtrl.js"></script>

</head>
<body  ng-app="myApp" ng-controller="selAccountController" ng-init="init()" >
    <div class="cooperation-manage" >
        <div class="cooperation-head">
            <span class="frist-tab" ng-bind="'ACCOUNTMANAGER'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'QUERY_ACCOUNT'|translate">查看账号</span>
        </div>
        <div class="cooper-tab">
            <div>
                <form class="form-horizontal ng-pristine ng-invalid ng-invalid-required ng-valid-maxlength" name="myForm"
                      novalidate="">
                    <div class="form-group">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ACCOUNT_NAME'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="accountName"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span >账号类型</span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="managerRightType"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span >OA账号</span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="oaAccount"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CHARACTERNAME'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4">
                            <div class="roleName" ng-bind="roleName"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CONTACT'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="fullName"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'PHONENUMBER'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="msisdn"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
                            ng-bind="'ACCOUNT_EMAIL'|translate"></label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="email"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ACCOUNT_PASSWORD'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span>********</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEACCOUNT'|translate"></span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="operatorAccountName"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>创建时间</span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="createTime|formatDate"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>更新时间</span>
                        </label>
                        <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                            <span ng-bind="lastUpdateTime|formatDate"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ACCOUNT_STATUS'|translate">账号状态</span>
                        </label>
                        <div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" >
                            <span >{{getAccountStatus(accountStatus)}}</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn  col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 "
                    ng-click="goBack()" ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
<style>
    body {
        background: #f2f2f2;
    }
    .roleName{
        word-break: break-all;
        text-overflow: ellipsis;
    }
    .modal-footer{
        text-align: left;
    }
    .cooperation-manage{
        min-width: 1024px;
    }
    .cooperation-head {
        padding: 20px;
    }
    .cooperation-head .frist-tab {
        font-size: 16px;
    }
    .cooperation-head .second-tab {
        font-size: 14px;
    }
    .cooper-tab {
        margin: 0 20px;
        background: #fff;
        border-radius: 2px;
        padding: 36px 10px 16px;
    }
    .form-group .control-label icon {
        color: #ff254c;
        vertical-align: sub;
        margin-right: 2px;
    }
    .form-group div {
        line-height: 34px;
    }
    .form-group {
        margin-bottom: 35px;
    }
</style>
</html>