<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SharedPackageQuotaDetailMapper">

    <select id="getMsisdnUsedShareQuota" resultType="java.lang.Integer">
        SELECT
            IFNULL(sum(useCount),0)
        FROM
            `ecpm_t_sharedPackage_quota_detail`
        WHERE
            msisdn = #{msisdn}
            AND `month` = DATE_FORMAT( CURRENT_DATE, '%Y%m' )
            AND netWorkType = #{netWorkType}
            AND useType = #{useType}

    </select>

    <insert id="insertOrUpdateUseCount" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO ecpm_t_sharedPackage_quota_detail ( msisdn, useCount, `month`, createTime, updateTime, netWorkType, useType )
        VALUES(#{msisdn},${useCount},#{month},#{createTime},#{updateTime},#{netWorkType},#{useType})
            ON DUPLICATE KEY UPDATE useCount = useCount + ${useCount};
    </insert>

</mapper>