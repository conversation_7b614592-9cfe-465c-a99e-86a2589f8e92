var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common","ngSanitize"])
app.controller('hotlineContentController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.CommonUtils = CommonUtils;
    $scope.init = function () {
        $scope.noSign = '0';
        $scope.number = 70;
        $scope.dsc = "HOTLINE_CONTENTDESC";
        $scope.msg = "请输入彩印内容1~70字";
        $scope.tipMsg = "SMS_CONTENT_LENGTH_MAX_70";
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.parentEnterpriseID = $scope.enterpriseID ;
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        //获取enterpriseName
        $scope.enterpriseName = $.cookie('enterpriseName');
        //判断最终调接口的enterpriseID,enterpriseName
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            $scope.enterpriseID = $scope.subEnterpriseID;
            $scope.enterpriseName = $scope.subEnterpriseName;
        }
        $scope.groupSendTaskInfo = {};
        //状态信息
        $scope.hotlineStatusMap = {
            "1": "审核失败",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        };
        $scope.unicomApproveStatusMap = {
        		"1": "审核失败",
                "-1": "--",
                "2": "待审核",
                "3": "审核通过",
                "4": "审核驳回"
            }

        $scope.telecomApproveStatusMap = {
                "null": "待审核",
                "-1": "--",
                "0": "审核通过",
                "1": "审核失败"
            }    
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.auditStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 2,
                name: "待审核"
            },
            {
                id: 3,
                name: "审核通过"
            },
            {
                id: 4,
                name: "审核驳回"
            },
            {
                id: 1,
                name: "审核失败"
            },
        ];
        //彩印内容类型
        $scope.sceneMap = {
            "11": "短信",
            "13": "彩信",
            "9": "USSD",
            "10": "闪信",
            "8": "增彩"
        };
        //彩印内容类型
        $scope.sceneServiceType = {
            "11": "17",
            "13": "20",
            "9": "18",
            "10": "19",
            "8": "16"
        };
        $scope.sceneServiceType2 = {
                "11": 17,
                "13": 8,
                "9": 3,
                "10": 3,
                "8": 16
            };
        //彩印内容类型
        $scope.deliveryTypeMap = {
            "17": "0000100",
            "18": "1110000",
            "20": "0000001",
            "16": "0000010",
            "19": "1110000"
        };
        $scope.serviceTypeChoise = [];

        // 上传video
        $scope.accepttypeVideo = "mp4,3gp";
        $scope.isValidateVideo = true;
        $scope.filesizeVideo = 2;
        $scope.mimetypesVideo = ".mp4,.3gp";
        $scope.autoVideo = true;
        $scope.isCreateThumbnailVideo = false;
        $scope.uploadurlVideo = '/qycy/ecpmp/ecpmpServices/fileService/uploadVideo';
        $scope.uploadDescVideo = "仅支持mp4,3gp格式的文件";
        $scope.numlimitVideo = 3;
        $scope.urlListVideo = [];
        $scope.uploadParamVideo = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms',
            use: 'video'
        };
        // 上传Video  END
        $scope.$on("uploadifyidVideo", function (event, fileUrl, index, broadData) {
            if (fileUrl) {
                $scope.contentList.push({
                    frameType: 3,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).watch,
                    frameTxt: "",
                    filesize: broadData.file.size
                })
                $scope.checkContentList();

            }
            $scope.errorInfoVideo = broadData.errorInfo;
        });

        //上传图片
        $scope.accepttypeImg = "jpg,gif";
        $scope.isValidateImg = true;
        $scope.filesizeImg = 0.2;
        $scope.mimetypesImg = ".jpg,.gif";
        $scope.isCreateThumbnailImg = false;
        $scope.uploadurlImg = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.numlimitImg = 1000;
        $scope.uploadParamImg = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms'
        };

        $scope.$on("uploadifyidImg", function (event, fileUrl, fileData) {
            if (fileUrl) {
                $scope.contentList.push({
                    frameType: 1,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).review,
                    frameTxt: "",
                    filesize: fileData.file.size
                })
                $scope.checkContentList();
            } else if (fileData != '' || fileData != undefined) {
            }
        });
        //上传图片
        $scope.accepttypeImgCaixin = "jpg,jpeg,png";
        $scope.isValidateImgCaixin = true;
        $scope.filesizeImgCaixin = 0.28;
        $scope.mimetypesImgCaixin = ".jpg,.jpeg,.png,.JPG,.JPEG,.PNG";
        $scope.isCreateThumbnailImgCaixin = false;
        $scope.uploadurlImgCaixin = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.numlimitImgCaixin = 1000;
        $scope.uploadParamImgCaixin = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms_cx'
        };
        $scope.$on("uploadifyidImgCaixin", function (event, fileUrl, fileData) {
            if (fileUrl) {
                $scope.contentList.push({
                    frameType: 1,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).review,
                    frameTxt: "",
                    filesize: fileData.file.size
                })
                $scope.checkContentList();

            } else if (fileData != '' || fileData != undefined) {
            }
        });
        $scope.contentList = [];
        //总的文本区域长度
        $scope.ctnTextSumLength = 0;
        //总图片大小
        $scope.allPicSize = 0;
        //图片的张数
        $scope.picLength = 0;
        //视频数量
        $scope.videoLength = 0;
        //文本框的个数
        $scope.ctnTextMount = 0;
        $scope.ctnTextSum = "";
        //其他资质
        $scope.accepttype2 = "jpeg,jpg,png,bmp";
        $scope.isValidate2 = false;
        $scope.filesize2 = 100;
        $scope.mimetypes2 = ".jpeg,.jpg,.png,.bmp";
        $scope.isCreateThumbnail2 = false;
        $scope.uploadurl2 = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.numlimit2 = 1000;
        $scope.uploadCertiDesc = "最多支持6张图片，仅支持jpg，bmp，png，jpeg格式";
        $scope.uploadParam2 = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'certiFile'
        };
        $scope.colorContentAndFileList = [];
        //其他资质end
        if ($scope.operate != 'detail') {
            $scope.$watch('contentList', function (newVal, oldVal) {
                $scope.ctnTextSumLength = 0;
                $scope.allPicSize = 0;
                $scope.picLength = 0;
                $scope.videoLength = 0;
                $scope.ctnTextSum = "";
                $scope.ctnTextMount = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameType == 1) {
                        $scope.picLength += 1;
                    } else if (newVal[i].frameType == 3) {
                        $scope.videoLength += 1;
                    } else {
                        $scope.ctnTextMount += 1;
                    }
                    $scope.ctnTextSumLength += newVal[i].frameTxt.length;
                    $scope.ctnTextSum += newVal[i].frameTxt;
                    $scope.allPicSize += newVal[i].filesize;
                }
                //删除的话，就去再次校验敏感词
                if (newVal.length < oldVal.length) {
                    $scope.sensitiveCheck($scope.ctnTextSum, 2)
                }
            }, true)
            $scope.$watch('colorContentAndFileList', function (newVal, oldVal) {
                $scope.fileLength = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameFileUrl) {
                        $scope.fileLength += 1;
                    }
                }

            }, true)
        }
// 上传excel
        $scope.accepttypeExcel = "xlsx";
        $scope.isValidateExcel = true;
        $scope.filesizeExcel = 20;
        $scope.mimetypesExcel = ".xlsx,.xls";
        $scope.autoExcel = true;
        $scope.isCreateThumbnailExcel = false;
        $scope.uploadurlExcel = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDescExcel = "仅支持xlsx格式的文件";
        $scope.numlimitExcel = 1;
        $scope.urlListExcel = [];
        $scope.uploadParamExcel = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms',
            use: 'msidsnList'
        };
        // 上传excel  END
        $scope.$on("uploadifyidExcel", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileNameExcel = broadData.file.name;
            } else {
                $scope.fileNameExcel = "";
            }
            $scope.fileUrlExcel = fileUrl;
        });
       

        //上传文件
        $scope.$on("uploadifyid2", function (event, fileUrl, fileData) {
            if (fileUrl != '') {
                if($scope.colorContentAndFileList.length >= 6){
                    return;
                }
                $scope.colorContentAndFileList.push({
                    frameFileUrl: fileUrl,
                    formatFrameFileUrl: CommonUtils.formatPic(fileUrl).download,
                    filesize: fileData.file.size,
                    filename: fileData.file.name
                })
            } else if (fileData != '' || fileData != undefined) {
                // $scope.contentPicUrlList.splice(index, 1);
                // if($scope.urlList){
                //     $scope.urlList.splice(index,1);
                // }
            }
            console.log(fileUrl);
            console.log($scope.colorContentAndFileList);
            console.log($scope.fileLength);
        });
        //上传资质end
        //敏感校验标识,0\1预留，2表示增彩的文本内容
        $scope.isSensitive = [false, false, false];
        $scope.uploadMsisdnList = [];
        //初始化显示的信息
        $scope.sensitiveWords = {
            "0": [],
            "1": [],
            "2": []
        };
        $scope.sensitiveWordsStr = {
            "0": '',
            "1": '',
            "2": ''
        };

        $scope.initSel = {};
        $scope.initSel.auditStatus = "";

        $scope.taskNameValidate = true;
        $scope.msisdnListValidate = true;
        $scope.contentTitleValidate = true;
        $scope.signatureValidate = true;
        $scope.contentValidate = true;
        $scope.contentValidateScreen = true;
        $scope.queryPlatformStatus();
        $scope.querySubscribeList();

        $scope.queryHotlineContentInfoList();
        $scope.getStatus();
        let req = {
            "enterpriseID": $scope.enterpriseID
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryPortList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.enterprisePortList = result.enterprisePortList;
                    $scope.object = result.enterprisePortList[0];
                    $scope.groupSendMmsPort = $scope.object.groupSendMmsPort;
                    $scope.groupSendFlashPort = ['10658086'];
                    $scope.groupSendSmsPort = ['10658086'];
                    if ($scope.object.groupSendFluid) {
                        $scope.groupSendFluid = $scope.object.groupSendFluid;
                    } else {
                        $scope.groupSendFluid = 20;
                    }
                })
            }


        })
    };

    $scope.closeAdd = function () {
        $scope.isShowAdd = false;
        $("body").removeClass("modal-open");
    }

    $("[name='taskType']").click(function (e){
        $scope.groupSendTaskInfo.taskType = e.target.value;
        if ($scope.groupSendTaskInfo.taskType == 1){
            $scope.groupSendTaskInfo.taskType1 = true;
            $scope.groupSendTaskInfo.taskType2 = false;
        } else {
            $scope.groupSendTaskInfo.taskType1 = false;
            $scope.groupSendTaskInfo.taskType2 = true;
        }
        console.log($scope.groupSendTaskInfo.taskType)
    });

    //查询热线内容列表
    $scope.queryHotlineContentInfoList = function (condition) {
        let req = {};
        if (condition !== 'justPage') {
            req = {
                "contentName": $scope.initSel.content || '',
                "enterpriseID": $scope.enterpriseID,
                "servTypeList": [4],
                "approveStatus": $scope.initSel.auditStatus,
                "getBelongOrg": 0,
                //111版本 getFrame置0
                "getFrame" : 0,
    	        "getPushTime": 0,
    	        "getSwitchState": 0,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };


            $scope.pageInfo[0].currentPage = 1;
            $scope.queryHotlineContentInfoListTemp = angular.copy(req);
        } else {
            //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            req = $scope.queryHotlineContentInfoListTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        req.getBelongOrg = 0;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.hotContentInfoListData = result.contentInfoList || [];
                        for(let i = 0;i<$scope.hotContentInfoListData.length;i++){
                            $scope.hotContentInfoListData[i].createTask = true;
                            let groupTaskList = $scope.hotContentInfoListData[i].groupSendTaskInfoList;
                            if(groupTaskList!=null){
                                for(let f = 0;f < groupTaskList.length;f ++){
                                    if(groupTaskList[f].status!==4&&groupTaskList[f].status!==5){
                                        $scope.hotContentInfoListData[i].createTask = false;
                                    }
                                }
                            }
                        }

                        $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.hotContentInfoListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.hotContentInfoListData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };
    /*$scope.operaContent = function (item, type) {
        //110迭代：
        $scope.sensitive = false;
        if (!item) {
            $scope.operate = "add";
            $scope.operaName = "新增内容";
            $('#operaContent').modal();
            $scope.scene = $scope.serviceTypeChoise[0].id || 0;
            $scope.changeServiceType($scope.scene);
            $scope.colorContentAndFileList = [];
        } else {
            if(type === "modify"){
                $scope.operaName = "编辑内容";
            }else{
                $scope.operaName = "查看内容";
            }
            $scope.operate = type;

            $scope.itemId = item.contentID;
            $scope.scene = item.scene;
            $scope.platforms = item.platforms;
            $scope.changeServiceType($scope.scene);

            $scope.signature = item.signature;
            $scope.content = item.content;
            $scope.contentTitle = item.contentTitle;
            if (item.certificateUrlList) {
            	$scope.colorContentAndFileList = item.certificateUrlList;
            } else {
            	$scope.colorContentAndFileList = [];
            }
            if (item.contentFrameMappingList&&item.contentFrameMappingList.length > 0) {
                $scope.contentList = [];
                for (let i = 0; i < item.contentFrameMappingList.length; i++) {
                    var tepm = item.contentFrameMappingList[i];
                    if (tepm.frameType == 1) {
                        $scope.contentList.push({
                            frameType: 1,
                            framePicUrl: tepm.framePicUrl,
                            formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                            filesize: tepm.framePicSize,
                            Validate:true
                        });
                    } else if (tepm.frameType == 3) {
                        $scope.contentList.push({
                            frameType: 3,
                            framePicUrl: tepm.framePicUrl,
                            formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                            filesize: tepm.framePicSize,
                            Validate:true
                        });
                    } else {
                        $scope.contentList.push({
                            frameType: 2,
                            frameTxt: tepm.frameTxt,
                            frameTxtValidate: true,
                            Validate:true
                        });
                    }
                }
            }
            if (item.certificateUrlList&&item.certificateUrlList.length > 0)
            {
            	$scope.colorContentAndFileList = [];
                for (let i = 0; i < item.certificateUrlList.length; i++) 
                {
                    var tepm = item.certificateUrlList[i];
                    var name = tepm.substring(tepm.lastIndexOf("/")+1);

                    $scope.colorContentAndFileList.push({
                        frameFileUrl: tepm,
                        formatFrameFileUrl: CommonUtils.formatPic(tepm).download,
                        filename:name
                    })
                }
            	
            }
            $('#operaContent').modal();


        }


    };*/

    // 111版本
    $scope.operaContent = function (item, type) {
        //110迭代：
        $scope.sensitive = false;
        $scope.isShowAdd = true;
        $rootScope.$broadcast("event.resetErrTip");
        if (!item) {
            $scope.operate = "add";
            $scope.operaName = "新增内容";
            $('#operaContent').modal();
            $scope.scene = $scope.serviceTypeChoise[0].id || 0;
            $scope.changeServiceType($scope.scene);
            $scope.colorContentAndFileList = [];
        } else {
            let req = {
                "contentName": $scope.initSel.content || '',
                "enterpriseID": $scope.enterpriseID,
                "servTypeList": [4],
                "approveStatus": $scope.initSel.auditStatus,
                "getBelongOrg": 0,
                "getFrame" : 1,
                "getPushTime": 0,
                "getSwitchState": 0,
                "contentIDList":[item.contentID],
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            req.getBelongOrg = 0;
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '1030100000') {
                            // $scope.hotContentInfoListData = result.contentInfoList || [];
                            for(let i = 0;i<result.contentInfoList.length;i++){
                                $scope.hotContentInfoSingle = result.contentInfoList[i];
                                result.contentInfoList.createTask = true;
                                let groupTaskList = result.contentInfoList[i].groupSendTaskInfoList;
                                if(groupTaskList!=null){
                                    for(let f = 0;f < groupTaskList.length;f ++){
                                        if(groupTaskList[f].status!==4&&groupTaskList[f].status!==5){
                                            result.contentInfoList[i].createTask = false;
                                        }
                                    }
                                }
                            }

                            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                            $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                            if(type === "modify"){
                                $scope.operaName = "编辑内容";
                            }else{
                                $scope.operaName = "查看内容";
                            }
                            $scope.operate = type;

                            $scope.itemId = $scope.hotContentInfoSingle.contentID;
                            $scope.scene = $scope.hotContentInfoSingle.scene;
                            $scope.platforms = $scope.hotContentInfoSingle.platforms;
                            $scope.changeServiceType($scope.scene);

                            $scope.signature = $scope.hotContentInfoSingle.signature;
                            $scope.content = $scope.hotContentInfoSingle.content;
                            $scope.contentTitle = $scope.hotContentInfoSingle.contentTitle;
                            if ($scope.hotContentInfoSingle.certificateUrlList) {
                                $scope.colorContentAndFileList = $scope.hotContentInfoSingle.certificateUrlList;
                            } else {
                                $scope.colorContentAndFileList = [];
                            }
                            if ($scope.hotContentInfoSingle.contentFrameMappingList&&$scope.hotContentInfoSingle.contentFrameMappingList.length > 0) {
                                $scope.contentList = [];
                                for (let i = 0; i < $scope.hotContentInfoSingle.contentFrameMappingList.length; i++) {
                                    var tepm = $scope.hotContentInfoSingle.contentFrameMappingList[i];
                                    if (tepm.frameType == 1) {
                                        $scope.contentList.push({
                                            frameType: 1,
                                            framePicUrl: tepm.framePicUrl,
                                            formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                                            filesize: tepm.framePicSize,
                                            Validate:true
                                        });
                                    } else if (tepm.frameType == 3) {
                                        $scope.contentList.push({
                                            frameType: 3,
                                            framePicUrl: tepm.framePicUrl,
                                            formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                                            filesize: tepm.framePicSize,
                                            Validate:true
                                        });
                                    } else {
                                        $scope.contentList.push({
                                            frameType: 2,
                                            frameTxt: tepm.frameTxt,
                                            frameTxtValidate: true,
                                            Validate:true
                                        });
                                    }
                                }
                            }
                            if ($scope.hotContentInfoSingle.certificateUrlList&&$scope.hotContentInfoSingle.certificateUrlList.length > 0)
                            {
                                $scope.colorContentAndFileList = [];
                                for (let i = 0; i < $scope.hotContentInfoSingle.certificateUrlList.length; i++)
                                {
                                    var tepm = $scope.hotContentInfoSingle.certificateUrlList[i];
                                    var name = tepm.substring(tepm.lastIndexOf("/")+1);

                                    $scope.colorContentAndFileList.push({
                                        frameFileUrl: tepm,
                                        formatFrameFileUrl: CommonUtils.formatPic(tepm).download,
                                        filename:name
                                    })
                                }

                            }
                            $('#operaContent').modal();
                        } else {
                            $scope.hotContentInfoListData = [];
                            $scope.pageInfo[0].currentPage = 1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage = 1;
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                        $scope.hotContentInfoListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = '1030120500';
                        $('#myModal').modal();
                    })
                }
            });



        }


    };

    $scope.changeServiceType = function (val) {

        if (val)
        {
            $scope.platformInit(val);

        }
        $scope.serviceType = $scope.sceneServiceType[val];
        val = $scope.serviceType;
        if (val) {
            //选择短信时
            if (val === "17" || val === "18" || val === "19") {
                //110迭代：
                $scope.sensitive = false;

                $scope.contentList = [];
                //增彩内容插入假的值以过校验
                $scope.contentList.push({
                    framePicUrl: "",
                    formatFramePicUrl: "",
                    frameTxt: true,
                    filesize: ""
                });
                $scope.contentTitle = '';

                //清空短信坑的内容
                $scope.signature = ""; //签名
                $scope.timingTime = null; //发送时间
                $scope.content = ""; //内容


                $scope.contentTitleValidate = true;
                $scope.msg = "请输入彩印内容1~750字";
                if (val === "18") {
                    $scope.msg = "请输入彩印内容1~62字";
                    $scope.contentValidate = true;
                    return;
                }
                if (val === "19") {
                    $scope.msg = "请输入彩印内容1~70字";
                    $scope.contentValidate = true;
                    return;
                }
                $scope.contentValidateScreen = true;

            } else {
                //清空增彩内容
                $scope.contentList = [];
                $scope.contentTitle = "";
                //短信内容插入假的值以过校验
                $scope.signatureValidate = true;
                $scope.content = '';
                $scope.signature = '';

                $scope.contentValidate = true;
                $scope.contentTitleValidate = true;
                $scope.contentValidateScreen = true;

                //
            }
        }

    };
    
    $scope.checkSignatureRequired = function () {
        if ($scope.serviceType) {
            //选择闪信时
            if ($scope.serviceType === "19") {
            	$scope.contentValidateScreen = true
            	if($scope.platforms === "100") {
            		$scope.msg = "请输入彩印内容1~750字";
            	} else {
            		$scope.msg = "请输入彩印内容1~70字";
            	}
            	$scope.checkSmsContent($scope.content,3,'')
            }
        }
    };

    //校验增彩标题
    $scope.checkContentTitle = function (contentTitle) {
        $scope.contentTitleValidate = $scope.validateV(contentTitle, 9, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
    };
    //校验彩信标题
    $scope.checkCXContentTitle = function (contentTitle) {
        $scope.contentTitleValidate = $scope.validateV(contentTitle, 20, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
    };
    $scope.addTextCtn = function () {
        $scope.contentList.push({
            framePicUrl: "",
            formatFramePicUrl: "",
            frameTxt: "",
            filesize: "",
            Validate:true
        });
        $scope.contentListValidate = false;

    };
    $scope.deleteCtnOrPic = function (index) {
        $scope.contentList.splice(index, 1);
        $scope.checkContentList();
    };
    $scope.checkSmsContent = function (content, index, condition) {
        //110迭代：
        $scope.isSensitive[index] = false;

        $scope.sensitive = false;
        if (content) {
            if ($scope.signature) {
            	if($scope.serviceType === "17" || $scope.serviceType === "18" || $scope.serviceType === "19") {
            		content = "[" + $scope.signature + "]" + content;
            	} else {
            		content = $scope.signature + content;
            	}
            }
            if ($scope.serviceType === "18") {
                $scope.tipMsg = "SMS_CONTENT_LENGTH_MAX_62";
                $scope.contentValidateScreen = $scope.validateV(content, 62, null, true);
            } else if($scope.serviceType === "19"){
            	if($scope.platforms === "100") {
            		$scope.tipMsg = "SMS_CONTENT_LENGTH_MAX_750";
            		$scope.contentValidateScreen = $scope.validateV(content, 750, null, true);
            	} else {
            		$scope.tipMsg = "SMS_CONTENT_LENGTH_MAX_70";
            		$scope.contentValidateScreen = $scope.validateV(content, 70, null, true);
            	}
            }
            else {
                $scope.contentValidate = $scope.validateV(content, 750, null, true);
            }
        } else {
            if ($scope.serviceType  === "18") {
                $scope.tipMsg = "SMS_CONTENT_LENGTH_MAX_62";
                $scope.contentValidateScreen = true;
            }else if($scope.serviceType === "19"){
                $scope.tipMsg = "SMS_CONTENT_LENGTH_MAX_70";
                $scope.contentValidateScreen = true;
            }
            else {
                $scope.contentValidate = true;
            }
        }
        //110迭代:
        // $scope.sensitiveCheck(content, index, condition);
    };
    /*校验各个字段*/
    $scope.validateV = function (context, maxlength, reg) {
        if (!context) {
            return false;
        } else {
            if (context.length > maxlength) {
                return false;
            } else {
                if (reg) {
                    if (!reg.test(context)) {
                        return false;
                    } else {
                        return true;
                    }
                } else {
                    return true;
                }
            }
        }
    };
    //校验敏感词
        $scope.sensitiveCheck = function (content, index, condition) {
            // 110迭代：
            $scope.sensitive = false;

        if (!content) {
            //110迭代：
            // $scope.isSensitive[index] = false;
            //
            // $scope.sensitive = false;
            return;
        }
        content = content.replace(/\s/g, '');

        $scope.condition = condition;
        var req = {
            "content": content || '',
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030120017') {
                    $scope.sensitiveWords[index] = result.sensitiveWords || [];
                    if ($scope.sensitiveWords[index].length > 0) {

                        $scope.isSensitive[index] = true;

                        //110迭代：
                        $scope.sensitive = true;

                        $scope.sensitiveWordsStr[index] = $scope.sensitiveWords[index].join('、');
                    } else {
                        $scope.isSensitive[index] = false;

                    //110迭代：
                    $scope.sensitive = false;
                }
                } else if (data.resultCode == '1030100000') {
                    $scope.sensitiveWords[index] = [];
                    $scope.isSensitive[index] = false;

                    //110迭代：
                    $scope.sensitive = false;

                    if ($scope.condition == 'save') {
                        $scope.submitReview();
                    }
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
                // })
            },
            error: function () {
                // $rootScope.$apply(function () {
                $scope.tip = '1030120500';
                $('#myModal').modal();
                // })
            }
        });
    };

    //获取当前企业群发业务开关
    $scope.getStatus = function () {

        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": "4",
            "subservType": "16"
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
                        $scope.isStatusOpen = result.switchState;
                    }
                    if (result.switchState == 1) {
                        if ($scope.serviceTypeChoise == null) {
                            $scope.serviceTypeChoise = [];
                        }
                        //下拉框(投递方式)
                        $scope.serviceTypeChoise.push({
                            id: 8,
                            name: "增彩"
                        });
                        $scope.scene = 8;
                        $scope.changeServiceType($scope.scene);
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })

        //短信
        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": "4",
            "subservType": "17"
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
                        $scope.isStatusOpen = result.switchState;
                    }
                    if (result.switchState == 1) {
                        if ($scope.serviceTypeChoise == null) {
                            $scope.serviceTypeChoise = [];
                        }

                        //下拉框(投递方式)
                        $scope.serviceTypeChoise.push({
                            id: 11,
                            name: "短信"
                        });
                        if ($scope.scene == null) {
                            $scope.scene = 11;
                            $scope.changeServiceType($scope.scene);
                        }

                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
        //屏显
        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": "4",
            "subservType": "3"
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
                        $scope.isStatusOpen = result.switchState;
                    }
                    if (result.switchState == 1) {
                        if ($scope.serviceTypeChoise == null) {
                            $scope.serviceTypeChoise = [];
                        }
                        //下拉框(投递方式)
                        /*$scope.serviceTypeChoise.push({
                            id: 9,
                            name: "USSD"
                        });*/
                        $scope.serviceTypeChoise.push({
                            id: 10,
                            name: "闪信"
                        });

                        if ($scope.scene == null) {
                            $scope.scene = 10;
                            $scope.changeServiceType($scope.scene);
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
        //彩信
        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": "4",
            "subservType": "8"
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
                        $scope.isStatusOpen = result.switchState;
                    }
                    if (result.switchState == 1) {
                        if ($scope.serviceTypeChoise == null) {
                            $scope.serviceTypeChoise = [];
                        }
                        //下拉框(投递方式)
                        $scope.serviceTypeChoise.push({
                            id: 13,
                            name: "彩信"
                        });
                        if ($scope.scene == null) {
                            $scope.scene = 13;
                            $scope.changeServiceType($scope.scene);
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    };
    $scope.openDate = function(){
        laydate({
            elem: '#datepicker',
            istime: true,
            format: 'YYYY-MM-DD hh:mm',
            min:(new Date()).toLocaleString(),
            choose:function(value){
                console.log(value);
                $scope.timingTime = value;
                $scope.groupSendTaskInfo.timingTime = value;
            }
        });
    }
    $scope.dateFormat = function (fmt, date) {
        let ret;
        let opt = {
            "Y+": date.getFullYear().toString(),        // 年
            "m+": (date.getMonth() + 1).toString(),     // 月
            "d+": date.getDate().toString(),            // 日
            "H+": date.getHours().toString(),           // 时
            "M+": date.getMinutes().toString(),         // 分
            "S+": date.getSeconds().toString()          // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
            ret = new RegExp("(" + k + ")").exec(fmt);
            if (ret) {
                fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
            }
        }
        return fmt;
    };
    
    // 2109：提交时，若运营商选中异网（联通、电信），则二次确认
        $scope.diffNetAuthMaterialsConfirm = function () {
    	if ($scope.signatureRequired == '1') {
    		$('#diffNetAuthMaterials').modal();
        } else {
        	$scope.saveContent();
        }
    }
    $scope.diffNetAuthMaterialsUploaded = function () {
    	$('#diffNetAuthMaterialsCancel').click();
    	$scope.saveContent();
    }

    $scope.saveContent = function () {
        $scope.saveObject = {};
        $scope.saveObject.operatorID = parseInt($.cookie('accountID'));
        $scope.saveObject.enterpriseID = parseInt($scope.enterpriseID);
        $scope.saveObject.enterpriseName = $scope.enterpriseName;
        $scope.saveObject.thirdpartyType = 0;
        $scope.saveObject.industryType = "101010";
        $scope.saveObject.subServType = $scope.serviceType;
        $scope.saveObject.servType = 4;
        $scope.saveObject.signature = $scope.signature;
        $scope.saveObject.chargeType = 1;
        $scope.saveObject.contentType = 1;
        $scope.saveObject.contentTitle = $scope.contentTitle;
        $scope.saveObject.deliveryType = $scope.deliveryTypeMap[$scope.saveObject.subServType];

        if($scope.saveObject.subServType == 20){
            $scope.saveObject.subServType = "8";
        }else if($scope.saveObject.subServType == 18 || $scope.saveObject.subServType ==19){
            $scope.saveObject.subServType = "3";
        }

        $scope.saveObject.scene = $scope.scene;
        if ("3|17".indexOf($scope.saveObject.subServType) !== -1) {
            $scope.saveObject.content = $scope.content;
            $scope.saveObject.platforms = $scope.platforms;

        } else {
            let totalSize = 0;
            $scope.saveObject.contentFrameMappingList = [];
            $scope.saveObject.platforms = "100";
            for (var j in $scope.contentList) {
                var item = $scope.contentList[j];
                if (item.frameType == 1) {
                    $scope.saveObject.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 1,
                        framePicUrl: item.framePicUrl,
                        framePicSize: item.filesize
                    });
                } else if (item.frameType == 3) {
                    $scope.saveObject.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 3,
                        framePicUrl: item.framePicUrl,
                        framePicSize: item.filesize
                    });
                } else {
                    $scope.saveObject.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 2,
                        frameTxt: item.frameTxt,
                    });
                }
                totalSize += item.filesize;
            }
            if ($scope.groupSendTaskInfo.serviceType === $scope.tempServiceZC && totalSize > 2 * 1024 * 1024) {
                $scope.tip = "增彩包大小超过2M";
                $('#myModal').modal();
                return;
            }
        }

        //提交前清空之前的其他资质list，防止重复
        $scope.certificateUrlList = [];
        jQuery.each($scope.colorContentAndFileList, function (i, e) {
            $scope.certificateUrlList.push(e.frameFileUrl)
        });
        $scope.saveObject.certificateUrlList = $scope.certificateUrlList;

        var serviceUrl = '/ecpmp/ecpmpServices/contentService/createContent';
        if ($scope.operate === "modify") {
            $scope.saveObject.contentID = $scope.itemId;
            serviceUrl = '/ecpmp/ecpmpServices/contentService/updateContent';
        }
        var req = {
            "contentInfo": $scope.saveObject
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: serviceUrl,
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.queryHotlineContentInfoList();
                        $('#operaContent').click();
                    } else if (data.resultCode == '1030120090') {
                        $('#operaContent').click();
                        $scope.tip = "信控业务已关闭";
                        $('#myModal').modal();
                    }   else if (data.resultCode == '1030120091') {
                        $('#operaContent').click();
                        $scope.tip = "内容正在待审核，请稍后再试。";
                        $('#myModal').modal();
                    } else {
                        $('#operaContent').click();
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#operaContent').click();
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });

    };
    //跳转至新增页面
    $scope.toAdd = function (item) {
        $scope.fileNameExcel = '';
        $scope.groupSendTaskInfo = {
            enterpriseID: $scope.enterpriseID,
            taskName: '',
            serviceType: null,
            taskType: 1,
            content: {},
            groupSendTaskMsisdn: [],
            contentId:item.contentID,
            timingTime:null,
            timingTimeTemp:""
        };
        $('#datepicker').val("");
        $scope.checkTaskContentId(item.contentID,"add");
        $scope.validate = {};
        $scope.contentScene = item.scene;
        $scope.operate = "add";
        $scope.groupSendTaskInfo.taskType = 2;
    };


    $scope.uploadMsisdns = 0;
    $scope.validate = {};
    $scope.checkTaskContentId = function (contentId,type) {
        if (!contentId) {
            $scope.validate.contentId_null = true;
            return;
        } else {
            $scope.validate.contentId_null = false;
        }

        let req = {
            "contentIDList": [contentId],
            "approveStatus": 3,
            "enterpriseID": $scope.enterpriseID

        };
        $scope.contentScene = null;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    $scope.validate.contentId_statue = true;
                    if (data.resultCode == '1030100000') {
                        if (result.contentInfoList.length > 0) {
                            $scope.validate.contentId_statue = false;
                            if (!result.contentInfoList[0].scene) {
                                $scope.validate.contentId_scene = true;
                            } else {
                                $scope.validate.contentId_scene = false;
                                $scope.contentScene = result.contentInfoList[0].scene;
                                $scope.ports = [];
                                if ($scope.contentScene === 11) {
                                    $scope.ports = $scope.groupSendSmsPort;
                                } else if ($scope.contentScene === 10) {
                                    $scope.ports = $scope.groupSendFlashPort;
                                } else if ($scope.contentScene === 8 || $scope.contentScene === 13) {
                                    $scope.ports = $scope.groupSendMmsPort;
                                }
                                if ($scope.ports&&$scope.ports.length > 0) {
                                    $scope.groupSendTaskInfo.port = $scope.ports[0];
                                }
                            }
                            // let groupTaskList = result.contentInfoList[0].groupSendTaskInfoList;
                            // if(groupTaskList!=null){
                            //     for(let f = 0;f < groupTaskList.length;f ++){
                            //         if(groupTaskList[f].status!==4&&groupTaskList[f].status!==5){
                            //             $scope.hotContentInfoListData[i].createTask = false;
                            //             $scope.tip = "存在执行中任务，无法编辑";
                            //             $('#myModal').modal();
                            //             return;
                            //         }
                            //     }
                            // }
                            if("add" === type){
                                $('#createTask').modal();
                            }
                        }
                    }
                })
            }

        });
    };
    //审核意见合并
    $scope.getApproveIdea = function (item) {
        let approveIdea = item.approveIdea||"";
        if (item.subset && item.subset.length > 0) {
            for (let i = 0; i<item.subset.length; i++) {
                if(item.subset[i].approveIdea){
                    if(approveIdea){
                        approveIdea +="|"+item.subset[i].approveIdea;
                    }else {
                        approveIdea =item.subset[i].approveIdea;

                    }

                }
            }
        }
        return approveIdea;
    }
    $scope.addMsisdn = function () {
        $scope.groupSendTaskInfo.groupSendTaskMsisdn.push({
            msisdn: ''
        });
    };
    $scope.export = function () {
        var req = JSON.stringify($scope.queryHotlineContentInfoListTemp);
        req = {
            "param": {
                "req": req,
                "method": "contentManager"
            },
            "url": "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downCsvFile",
            "method": "get"
        };
        CommonUtils.exportFile(req);

    };
    $scope.deleteMsisdn = function (index) {
        $scope.groupSendTaskInfo.groupSendTaskMsisdn.splice(index, 1);
        $scope.checkMsisdnList($scope.groupSendTaskInfo.groupSendTaskMsisdn);
    };
    $scope.createTask = function () {
        if ($scope.groupSendTaskInfo.timingTime) {
            var date = new Date($scope.groupSendTaskInfo.timingTime);
            if (!date.getTime() || date <= new Date()) {
                $scope.tip = "任务时间不得小于等于当前时间";
                $('#myModal').modal();
                return;
            }
            $scope.groupSendTaskInfo.timingTime = date;
            $scope.groupSendTaskInfo.taskType = 2;
        }
        $scope.groupSendTaskInfo.serviceType = $scope.sceneServiceType[$scope.contentScene];
        $scope.groupSendTaskInfo.deliveryType = $scope.deliveryTypeMap[$scope.groupSendTaskInfo.serviceType];
        if ($scope.fileNameExcel == "") {
            $scope.fileUrlExcel = "";
        }
        let req = {
            "groupSendTaskInfo":$scope.groupSendTaskInfo,
            "fileUrl":$scope.fileUrlExcel
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/createGroupSendTask",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == "1030100000") {
                        $scope.tip = "保存成功";
                        $('#myModal').modal();
                    }else if ( result.result.resultCode =="1030120081"){
                        $scope.tip = "号码未输入";
                        $('#myModal').modal();
                    }else if ( result.result.resultCode =="1030199999"){
                        $scope.tip = result.result.resultDesc;
                        if(result.result.resultDesc == "使用了未配置的端口号"){
                            $scope.tip = "请联系管理员配置拓展码"
                        }
                        $('#myModal').modal();
                    }else{
                        $scope.tip = "其他错误";
                        $('#myModal').modal();
                    }
                    $scope.queryHotlineContentInfoList();

                })
            }

        });
    }
    //校验签名名称
    $scope.checkSignName = function (signName) {
        if (signName) {
            // $scope.signatureValidate = $scope.validateV(signName, 12, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
            //212企管平台彩印签名长度优化需求
            if(signName.length > 67){
                $scope.signatureValidate = false;
            }else{
                $scope.signatureValidate = true;
            }
        } else {
            $scope.signatureValidate = true;
        }
        $scope.checkSmsContent($scope.content,3,'');
    };

    $scope.isTempContent = function(content){
      let re = true;
      if(content && content.indexOf("#*#")>=0){
          re = false;
      }
      return re;
    };

// 删除热线内容弹窗
    $scope.deleteHotlineContent = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteHotlineContent').modal();
    };
    $scope.delHotlineContent = function () {
        var removeReq = {
            "id": $scope.selectedItemDel,
            "operaterType": 2
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
            data: JSON.stringify(removeReq),
            notLoad: true,
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $('#deleteHotlineContent').modal('hide');
                        $scope.queryHotlineContentInfoList();
                    }
                })
            }

        });
    };

    $scope.checkContentList = function(){
        $scope.contentListValidate = true;
        for(let i = 0;i<$scope.contentList.length;i++){
            let item = $scope.contentList[i];
            //为文本
            if(!item.framePicUrl && (item.frameType !=3 && item.frameType !=1)){
                if(!item.frameTxt){
                    item.Validate = false;
                }
                if(!item.Validate){
                    $scope.contentListValidate = false;
                }
            }
        }
    };
    $scope.sensitiveCheckX = function(item,content,index,length){
        item.Validate = !!item.frameTxt;
        if(!item.Validate){
            $scope.contentListValidate = false;
            return;
        }
        var req = {
            "content": content || '',
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030120017') {
                    let temp =  result.sensitiveWords || [];
                    if (temp.length > 0) {
                        item.sensitiveWordsStr = temp.join('、');
                        item.Validate = false;
                    } else {
                        item.Validate = true;
                    }
                } else if (data.resultCode == '1030100000') {
                    item.Validate = true;
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
                $scope.checkContentList();
                // })
            },
            error: function () {
                // $rootScope.$apply(function () {
                $scope.tip = '1030120500';
                $('#myModal').modal();
                // })
            }
        });

    };
    // 获取当前时间YYYYMMDDHHMMSS
    $scope.getCurrentDate = function () {
    	var today = new Date();
        var year = today.getFullYear()+'';
    	var month = today.getMonth() + 1;
    	month = month < 10 ? '0'+month : month;
    	var day = today.getDate() < 10 ? '0'+today.getDate() : today.getDate();
    	var hours = today.getHours() < 10 ? '0'+today.getHours() : today.getHours();
    	var mins = today.getMinutes() < 10 ? '0'+today.getMinutes() : today.getMinutes();
    	var secs = today.getSeconds() < 10 ? '0'+today.getSeconds() : today.getSeconds();
        
        return year + month + day + hours + mins + secs;
    };
    // 查询订购关系列表接口，获取有效订购关系（失效时间>当前时间）清单，转为订购关系map<业务子类型, map<计费方式, 运营商清单>>
    $scope.querySubscribeList = function () {
    	var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID": $scope.enterpriseID,
                "servType": 4,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                        $scope.subscribeList = data.subscribeInfoList || [];
                        if ($scope.subscribeList.length > 0) 
                        {
                        	// map<业务子类型, map<计费方式, 运营商清单>>
                        	$scope.subscribeMap = $scope.getSubscribeMap();
                            
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    };
  //根据订购关系生成map<业务子类型, map<计费方式, 运营商清单>>
    $scope.getSubscribeMap = function () {
    	var resultMap = new Map();
        for (let i in $scope.subscribeList)
    	{
    		var subscribe = $scope.subscribeList[i];

    		// 计费方式map
    		var chargeTypeMap = resultMap.get(subscribe.subServType);
    		if (!chargeTypeMap)
    		{
    			chargeTypeMap = new Map();
    			resultMap.set(subscribe.subServType, chargeTypeMap);
    		}
    		// 运营商清单
    		var chargeType = subscribe.chargeType;
    		var platformList = chargeTypeMap.get(chargeType);
    		if (!platformList)
    		{
    			platformList = [];
    			chargeTypeMap.set(chargeType, platformList);
    		}
    		var telecomsOperator = !subscribe.reservedsEcpmp ? "1"
                    : !subscribe.reservedsEcpmp.reserved1 ? "1"
                            : subscribe.reservedsEcpmp.reserved1;
    		platformList.push(telecomsOperator);
    	}
    	
    	// 屏显需要加上主叫，被叫
    	if (resultMap.get(3))
    	{
    		if (!resultMap.get(1))
    		{
    			resultMap.set(1, resultMap.get(3));
    		}
    		else
    		{
    			var chargeTypeMap1 = resultMap.get(1);
    			var chargeTypeMap3 = resultMap.get(3);
    			for(let key of chargeTypeMap3.keys()) {
            		if (!chargeTypeMap1.get(key))
            		{
            			chargeTypeMap1.set(key, chargeTypeMap3.get(key))
            		}
            	};
    		}
    		if (!resultMap.get(2))
    		{
    			resultMap.set(2, resultMap.get(3));
    		}
    		else
    		{
    			var chargeTypeMap2 = resultMap.get(2);
    			var chargeTypeMap3 = resultMap.get(3);
    			for(let key of chargeTypeMap3.keys()) {
            		if (!chargeTypeMap2.get(key))
            		{
            			chargeTypeMap2.set(key, chargeTypeMap3.get(key))
            		}
            	};
    		}
    	}
    	
    	return resultMap;
    };
  //查询企业服务开关
    $scope.queryPlatformStatus = function () {
        var queryServiceControlReq = {
            "enterpriseID": $scope.enterpriseID
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceControl",
            data: JSON.stringify(queryServiceControlReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.platformStatus = result.platformStatus;
                	
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                })
            }
        });
    }
    // 2102：计费方式选中后，根据选中的计费方式，控制运营商选项
    $scope.platformInit = function (val) {
        var subServType = $scope.sceneServiceType2[val];
    	// 以platformStatus作为初始值，运营商对应占位若在运营商清单中不存在，则置为0
    	var platformStatus = $scope.platformStatus;
    	var platformList = [];
    	if ($scope.subscribeMap.get(subServType)
    			&& $scope.subscribeMap.get(subServType).get(1)) 
    	{
    		var platformList = $scope.subscribeMap.get(subServType).get(1);
    	}
    	var yd = platformStatus.substring(0, 1);
    	var lt = platformStatus.substring(1, 2);
    	var dx = platformStatus.substring(2);
    	if (yd == "1" && platformList.indexOf("1") < 0) {
    		yd = "0";
    	}
    	if (lt == "1" && platformList.indexOf("2") < 0) {
    		lt = "0";
    	}
    	if (dx == "1" && platformList.indexOf("3") < 0) {
    		dx = "0";
    	}
    	platformStatus = yd + lt + dx;
    	
        //初始化勾选状态
        if ($scope.operate == 'add') {
            $scope.checkEnterpriseWithoutSignAdd(platformStatus);
        	// $scope.platformInitAdd(platformStatus);
        } else {
            $scope.checkEnterpriseWithoutSignEdit(platformStatus);
        	// $scope.platformInitEdit(platformStatus);
        }
    };







    $scope.checkEnterpriseWithoutSignAdd = function (platformStatus){
        //判断父企业id是否为空
        var req;
        if( $scope.parentEnterpriseID === $scope.enterpriseID){
            req = {
                "enterpriseID": $scope.enterpriseID
            };
        }else{
            req = {
                "enterpriseID": $scope.parentEnterpriseID
            };
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                        if($scope.enterpriseWithoutSignListData.length === 0){
                            //沒有配置过免签名,按原来的流程走
                            $scope.platformInitAdd(platformStatus);
                        }else{
                            //签名不用必填
                            $scope.platformInitAddNoSign(platformStatus);
                        }
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }

    $scope.checkEnterpriseWithoutSignEdit = function (platformStatus){
        //判断父企业id是否等于子企业id
        var req;
        if( $scope.parentEnterpriseID === $scope.enterpriseID){
            req = {
                "enterpriseID": $scope.enterpriseID
            };
        }else{
            req = {
                "enterpriseID": $scope.parentEnterpriseID
            };
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
                        if($scope.enterpriseWithoutSignListData.length === 0){
                            //沒有配置过免签名,按原来的流程走
                            $scope.platformInitEdit(platformStatus);
                        }else{
                            //签名不用必填
                            $scope.platformInitEditNoSign(platformStatus);
                        }
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }












    // 新增页初始化运营商勾选状态
    $scope.platformInitAdd = function (platformStatus) {
        $scope.platforms = platformStatus;
        //初始化signature必填状态
        $scope.signatureRequired = '0';
        $scope.noSign = '1';
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
	            $('.platforms .check-li').eq(i).unbind("click");
	            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                $(".platforms .check-li").find('span').eq(i).addClass('checked')
                //绑定点击事件
                $('.platforms .check-li').eq(i).unbind("click");
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';

                    } else {
                        $scope.signatureRequired = '0';
                    }
                    $scope.platforms = _platforms;
                });
            }
        }
    }


    $scope.platformInitAddNoSign = function (platformStatus) {
        $scope.platforms = platformStatus;
        //初始化signature必填状态
        $scope.signatureRequired = '0';
        $scope.noSign = '0';
        if (platformStatus.charAt(1) == '1' || platformStatus.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                $('.platforms .check-li').eq(i).unbind("click");
                $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                $(".platforms .check-li").find('span').eq(i).addClass('checked')
                //绑定点击事件
                $('.platforms .check-li').eq(i).unbind("click");
                $('.platforms .check-li').eq(i).on('click', function () {
                    if ($(this).find('span').hasClass('checked') &&
                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                        $(this).find('span').removeClass('checked');
                    } else {
                        $(this).find('span').addClass('checked')
                    }
                    var _platforms = '';
                    for (var i = 0; i < 3; i++) {
                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                            _platforms += '1';
                        } else {
                            _platforms += '0';
                        }
                    }
                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                        $scope.signatureRequired = '1';

                    } else {
                        $scope.signatureRequired = '0';
                    }
                    $scope.platforms = _platforms;
                });
            }
        }
    }

    
    // 编辑页初始化运营商勾选状态
    $scope.platformInitEdit = function (platformStatus) {
    	var platforms = $scope.platforms;
        $scope.noSign = '1';
    	//初始化signature必填状态
	    if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
	        $scope.signatureRequired = '1';
	    }
	    for (var i = 0; i < 3; i++) {
	        if (platformStatus.charAt(i) == '0' && platforms.charAt(i) == '0') {
	            $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
	            $('.platforms .check-li').eq(i).unbind("click");
	            $(".platforms .check-li").find('span').eq(i).removeClass('checked');
	        } else {
	            //初始化勾选状态
	        	$(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
	            if (platforms.charAt(i) == '1') {
	                $(".platforms .check-li").find('span').eq(i).addClass('checked')
	            }
	            else
	            {
	                $(".platforms .check-li").find('span').eq(i).removeClass('checked')
	            }
                $('.platforms .check-li').eq(i).unbind("click");

	            if ($scope.operate != 'detail') {
	                //绑定点击事件
	                $('.platforms .check-li').eq(i).on('click', function () {
	                    if ($(this).find('span').hasClass('checked') &&
	                        ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {
	
	                        $(this).find('span').removeClass('checked');
	                    } else {
	                        $(this).find('span').addClass('checked')
	                    }
	                    var _platforms = '';
	                    for (var i = 0; i < 3; i++) {
	                        if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
	                            _platforms += '1';
	                        } else {
	                            _platforms += '0';
	                        }
	                    }
	                    if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
	                        $scope.signatureRequired = '1';

	                    } else {
	                        $scope.signatureRequired = '0';
	                    }
	                    $scope.platforms = _platforms;
	                });
	            }
	        }
	    }
    }


    $scope.platformInitEditNoSign = function (platformStatus) {
        var platforms = $scope.platforms;
        $scope.noSign = '0';
        if (platforms.charAt(1) == '1' || platforms.charAt(2) == '1') {
            $scope.signatureRequired = '1';
        }
        for (var i = 0; i < 3; i++) {
            if (platformStatus.charAt(i) == '0' && platforms.charAt(i) == '0') {
                $(".platforms .check-li").eq(i).css({cursor: 'not-allowed', color: 'gray'});
                $('.platforms .check-li').eq(i).unbind("click");
                $(".platforms .check-li").find('span').eq(i).removeClass('checked');
            } else {
                //初始化勾选状态
                $(".platforms .check-li").eq(i).css({cursor: 'pointer', color: '#333'});
                if (platforms.charAt(i) == '1') {
                    $(".platforms .check-li").find('span').eq(i).addClass('checked')
                }
                else
                {
                    $(".platforms .check-li").find('span').eq(i).removeClass('checked')
                }
                $('.platforms .check-li').eq(i).unbind("click");

                if ($scope.operate != 'detail') {
                    //绑定点击事件
                    $('.platforms .check-li').eq(i).on('click', function () {
                        if ($(this).find('span').hasClass('checked') &&
                            ($(this).siblings().eq(0).find('span').hasClass('checked') || $(this).siblings().eq(1).find('span').hasClass('checked'))) {

                            $(this).find('span').removeClass('checked');
                        } else {
                            $(this).find('span').addClass('checked')
                        }
                        var _platforms = '';
                        for (var i = 0; i < 3; i++) {
                            if ($(".platforms .check-li").find('span').eq(i).hasClass('checked')) {
                                _platforms += '1';
                            } else {
                                _platforms += '0';
                            }
                        }
                        if (_platforms.charAt(1) == '1' || _platforms.charAt(2) == '1') {
                            $scope.signatureRequired = '1';

                        } else {
                            $scope.signatureRequired = '0';
                        }
                        $scope.platforms = _platforms;
                    });
                }
            }
        }
    }





    $scope.deleteCtnOrFile = function (index) {
        $scope.colorContentAndFileList.splice(index, 1);
    }
    //其他资质文件下载
    $scope.exportFile = function (downloadUrl) {
        var req = {
            "param":{
                "path": downloadUrl,
                "token": $.cookie("token"),
                "isExport": 0
            },
            "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
            "method":"get"
        }
        CommonUtils.exportFile(req);
    };
}]);
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}]);
