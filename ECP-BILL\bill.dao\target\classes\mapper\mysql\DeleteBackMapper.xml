<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper.DeleteBackMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.bill.dao.domain.DeleteBackWrapper" >
        <id column="ID" property="id" jdbcType="VARCHAR" />
        <result column="operType" property="operType" jdbcType="INTEGER" />
        <result column="enterpriseID" property="enterpriseID" jdbcType="INTEGER" />
        <result column="orgID" property="orgID" jdbcType="INTEGER" />
        <result column="orgName" property="orgName" jdbcType="VARCHAR" />
        <result column="memberName" property="memberName" jdbcType="VARCHAR" />
        <result column="msisdn" property="msisdn" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="operatorID" property="operatorID" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, operType, enterpriseID, orgID, orgName, memberName, msisdn, createTime, operatorID
    </sql>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.bill.dao.domain.DeleteBackWrapper" >
        insert into ecpm_t_delete_back (ID, operType, enterpriseID, 
            orgID, orgName, memberName, 
            msisdn, createTime, operatorID
            )
        values (#{id,jdbcType=VARCHAR}, #{operType,jdbcType=INTEGER}, #{enterpriseID,jdbcType=INTEGER}, 
            #{orgID,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, #{memberName,jdbcType=VARCHAR}, 
            #{msisdn,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{operatorID,jdbcType=INTEGER}
            )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List" >
        insert into ecpm_t_delete_back (ID, operType, enterpriseID, 
            orgID, orgName, memberName, 
            msisdn, createTime, operatorID,orgType,objCreateTime,subStatus,subSuccessTime
            )
        values 
        <foreach collection ="list" item="item" index="index" separator =",">
        	(
        	#{item.id,jdbcType=VARCHAR},
            #{item.operType,jdbcType=INTEGER},
            #{item.enterpriseID,jdbcType=INTEGER},
            #{item.orgID,jdbcType=INTEGER},
            #{item.orgName,jdbcType=VARCHAR},
            #{item.memberName,jdbcType=VARCHAR},
            #{item.msisdn,jdbcType=VARCHAR},
            #{item.createTime,jdbcType=TIMESTAMP},
            #{item.operatorID,jdbcType=INTEGER},

            #{item.orgType},
            #{item.objCreateTime},
            #{item.subStatus},
            #{item.subSuccessTime}
            )
        </foreach>
    </insert>

    <select id="queryDeleteBackByCreateTime" resultMap="BaseResultMap">
        SELECT
        db.msisdn,
        es.enterpriseName as memberName,
        es.reserved5 as operatorID,
        db.createTime
        FROM
        ecpm_t_delete_back db
        LEFT JOIN ecpm_t_enterprise_simple es ON db.enterpriseID = es.ID
        WHERE
        db.operType IN (2, 3, 4)
        AND (
        UNIX_TIMESTAMP(db.createTime) &gt;= UNIX_TIMESTAMP(#{startTime})
        AND UNIX_TIMESTAMP(db.createTime) &lt;= UNIX_TIMESTAMP(#{endTime})
        );
	</select>
    <select id="queryByMsisdn" resultMap="BaseResultMap">
        SELECT * FROM ecpm_t_delete_back WHERE msisdn = #{msisdn} ORDER BY createTime desc
    </select>


</mapper>