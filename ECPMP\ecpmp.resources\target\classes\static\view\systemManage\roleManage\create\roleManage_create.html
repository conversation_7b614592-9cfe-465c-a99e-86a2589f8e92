<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>创建角色</title>
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
	<link href="../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../css/mian.css" rel="stylesheet"/>
	<link href="../../../../css/role.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="roleManage_create.js"></script>
	<style>
		label {
			min-width: 110px;
		}
	</style>
</head>
<body ng-app="myApp" ng-controller='roleManageCreateCtrl' ng-init="init()" class="body-min-width">
	<div class="creat-roles">
		<div class="head-nav">
			<span class="frist-tab" ng-bind="'ROLE_MANAGE'|translate"></span> >
			<span class="second-tab" ng-bind="'CREATE_ROLE'|translate"></span>
		</div>
		<form class="form-horizontal" name="myForm">
			<div class="form-group">
				<label for="roleName" class="col-xs-1 control-label">
					<icon>*</icon>
					<span ng-bind="'CHARACTERNAME'|translate"></span>
				</label>
				<div class="col-xs-6">
					<input type="text" class="form-control" name="roleName" id="roleName" ng-model="roleName" ng-maxlength="100"
								 required ng-class="{true:'error-border',false:''}[myForm.roleName.$dirty && myForm.roleName.$invalid]">
					<span style="color:red" ng-show="myForm.roleName.$dirty && myForm.roleName.$invalid">
						<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
						<span ng-show="myForm.roleName.$error.required" ng-bind="'CHARACTERNAME_MUST'|translate"></span>
						<span ng-show="myForm.roleName.$error.maxlength" ng-bind="'CHARACTERNAME_LENGTH'|translate"></span>
					</span>
				</div>
			</div>
			<div class="form-group">
				<label class="col-xs-1 control-label">
					<icon>*</icon>
					<span ng-bind="'AUTH_NAME'|translate"></span>
				</label>
				<div class="col-xs-10">
					<!--社会合作管理-->
					<div class="table-head">
						<span ng-bind="'SOCIAL_COOPERATION_MANAGEMENT'|translate"></span>
						<span class="btn-open" ng-click="openFirst=!openFirst" ng-show="openFirst===true"
									ng-bind="'RETRACT'|translate"></span>
						<span class="btn-open" ng-click="openFirst=!openFirst" ng-show="openFirst===false"
									ng-bind="'OPEN'|translate"></span>
					</div>
					<table class="table table-striped table-hover">
						<tbody class="showtable" ng-show="openFirst===true">
						<tr ng-repeat="item in rebuildFirstList track by $index">
							<td ng-bind="item.authName" width="200"></td>
							<td>
								<li class="check-li" ng-repeat="child in item.childList track by $index" ng-click="chooseChild(child,1)"
										onselectstart="return false">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[child.checked]">&nbsp;</span>
									<span ng-bind="child.authName"></span>
								</li>
							</td>
						</tr>
						</tbody>
					</table>
					<!--企业数据权限-->
					<div class="table-head">
						<span ng-bind="'ENTERPRISE_DATA_RIGHTS'|translate"></span>
						<span class="btn-open" ng-click="openFourth=!openFourth" ng-show="openFourth===true"
							  ng-bind="'RETRACT'|translate"></span>
						<span class="btn-open" ng-click="openFourth=!openFourth" ng-show="openFourth===false"
							  ng-bind="'OPEN'|translate"></span>
					</div>
					<table class="table table-striped table-hover">
						<tbody class="showtable" ng-show="openFourth===true">
						<tr>
							<!--数据权限（省）-->
							<td width="200"><span ng-bind="'DATA_PERMISSIONS_PROVINCE'|translate"></span>
								<!--全国全选框-->
								<li class="check-li"  ng-click="chooseAllProvince2()"
									onselectstart="return false" style="width: 147px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[allChecked2]"></span>&nbsp;
									<span >全国</span>
								</li>
							</td>
							<td>
								<li class="check-li" ng-repeat="item in provinceList2 track by $index" ng-click="chooseProvince2(item)"
									onselectstart="return false" style="width: 147px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[item.checked]"></span>&nbsp;
									<span ng-bind="item.authName"></span>
								</li>
							</td>
						</tr>
						</tbody>
					</table>
					<!--分省合作管理-->
					<div class="table-head">
						<span ng-bind="'PROVINCIAL_COOPERATION_MANAGEMENT'|translate"></span>
						<span class="btn-open" ng-click="openSecond=!openSecond" ng-show="openSecond===true"
									ng-bind="'RETRACT'|translate"></span>
						<span class="btn-open" ng-click="openSecond=!openSecond" ng-show="openSecond===false"
									ng-bind="'OPEN'|translate"></span>
					</div>
					<table class="table table-striped table-hover">
						<tbody class="showtable" ng-show="openSecond===true">
						<tr ng-repeat="item in rebuildSecondList track by $index">
							<td ng-bind="item.authName" width="200"></td>
							<td>
								<li class="check-li" ng-repeat="child in item.childList track by $index" ng-click="chooseChild(child,1)"
										onselectstart="return false">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[child.checked]">&nbsp;</span>
									<span ng-bind="child.authName"></span>
								</li>
							</td>
						</tr>
						</tbody>
					</table>

					<!--分省类型权限-->
					<div class="table-head">
						<span ng-bind="'PROVINCIAL_ENTERPRISE_TYPE_MANAGEMENT'|translate"></span>
						<span class="btn-open" ng-click="openSecond=!openSecond" ng-show="openSecond===true"
							  ng-bind="'RETRACT'|translate"></span>
						<span class="btn-open" ng-click="openSecond=!openSecond" ng-show="openSecond===false"
							  ng-bind="'OPEN'|translate"></span>
					</div>
					<table class="table table-striped table-hover">
						<tbody class="showtable" ng-show="openSecond===true">
						<tr>
							<td  width="200">企业类型</td>
							<td>
								<li class="check-li" ng-repeat="child in enterpriseTypeList track by $index" ng-click="chooseChild(child,1)"
									onselectstart="return false">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[child.checked]">&nbsp;</span>
									<span ng-bind="child.authName"></span>
								</li>
							</td>
						</tr>
						</tbody>
					</table>

					<!--分省合作数据权限-->
					<div class="table-head">
						<span ng-bind="'PROVINCIAL_COOPERATIVE_DATA_RIGHTS'|translate"></span>
						<span class="btn-open" ng-click="openThird=!openThird" ng-show="openThird===true"
									ng-bind="'RETRACT'|translate"></span>
						<span class="btn-open" ng-click="openThird=!openThird" ng-show="openThird===false"
									ng-bind="'OPEN'|translate"></span>
					</div>
					<table class="table table-striped table-hover">
						<tbody class="showtable" ng-show="openThird===true">
						<tr>
							<!--数据权限（省）-->
							<td width="200"><span ng-bind="'DATA_PERMISSIONS_PROVINCE'|translate"></span>
								<!--全国全选框-->
								<li class="check-li"  ng-click="chooseAllProvince()"
									onselectstart="return false" style="width: 147px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[allChecked]"></span>&nbsp;
									<span >全国</span>
								</li>
							</td>
							<td>
								<li class="check-li" ng-repeat="item in provinceList track by $index" ng-click="chooseProvince(item)"
										onselectstart="return false" style="width: 147px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[item.checked]"></span>&nbsp;
									<span ng-bind="item.authName"></span>
								</li>
							</td>
						</tr>
						<tr>
							<!--数据权限（市）-->
							<td width="200"><span ng-bind="'DATA_PERMISSIONS_CITY'|translate"></span>
								<!--全地市选框-->
								<li class="check-li"  ng-click="chooseAllCity()" ng-if="showAllCityChecked()"
									onselectstart="return false" style="width: 147px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[allCityChecked]"></span>&nbsp;
									<span >全地市</span>
								</li>
							</td>
							<td>
								<li class="check-li" ng-repeat="item in cityList track by $index" ng-click="chooseChild(item,2)"
										onselectstart="return false" style="width: 100px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[item.checked]"></span>&nbsp;
									<span ng-bind="item.authName"></span>
								</li>
							</td>
						</tr>
						<tr ng-if="showCountyFlag==true">
							<!--数据权限（区县）-->
							<td width="200"><span ng-bind="'DATA_PERMISSIONS_COUNTY'|translate"></span>
								<!--全区县选框-->
								<li class="check-li"  ng-click="chooseAllCounty()" ng-if="showAllCountyChecked()"
									onselectstart="return false" style="width: 147px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[allCountyChecked]"></span>&nbsp;
									<span >全区县</span>
								</li>
							</td>
							<td>
								<li class="check-li" ng-repeat="item in countyList track by $index" ng-click="chooseCounty(item,2)"
									onselectstart="return false" style="width: 100px">
									<span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[item.checked]"></span>&nbsp;
									<span ng-bind="item.authName"></span>
								</li>
							</td>
						</tr>
						</tbody>
					</table>
				</div>
			</div>
			<div class="form-group">
				<label for="roleDesc" class="col-xs-1 control-label">
					<!--角色介绍-->
					<span ng-bind="'ROLE_DESC'|translate"></span>
				</label>
				<div class="col-xs-6">
					<textarea class="form-control" rows="3" id="roleDesc" ng-model="roleDesc" ng-maxlength="1024" name="roleDesc"
										ng-class="{true:'error-border',false:''}[myForm.roleDesc.$dirty && myForm.roleDesc.$invalid]"></textarea>
					<span style="color:red" ng-show="myForm.roleDesc.$dirty && myForm.roleDesc.$invalid">
						<img src="../../../../assets/images/reject-icon.png" width="20" height="20" style="vertical-align: middle">
						<span ng-show="myForm.roleDesc.$error.maxlength" ng-bind="'ROLEDESC_LENGTH'|translate"
									style="vertical-align: middle"></span>
					</span>
				</div>
			</div>
			<div class="form-group" ng-show="error!=''">
				<label class="col-xs-1 control-label"></label>
				<div class="col-xs-6">
					<img src="../../../../assets/images/reject-icon.png" width="20" height="20" style="vertical-align: middle">
					<span ng-bind="error" style="color: red;vertical-align: middle"></span>
				</div>
			</div>
		</form>
		<div style="margin: 40px 20px;margin-left: 18%;">
			<button type="submit" class="btn btn-primary search-btn" ng-click="createRole()"
							ng-disabled="myForm.roleName.$invalid || myForm.roleDesc.$invalid || error!==''"
							ng-bind="'PUBLIC_NUMBER_SUBMIT'|translate">
			</button>
			<button class="btn" ng-click="goBack()" style="margin-left: 10px" ng-bind="'COMMON_CANCLE'|translate">
			</button>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="roleModal" tabindex="-1" role="dialog" data-backdrop="static"
				 data-keyboard="false"
				 aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true" ng-click="isSubmit===true?goBack():''">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
										ng-bind="'COMMON_OK'|translate" ng-click="isSubmit===true?goBack():''">
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>
</html>