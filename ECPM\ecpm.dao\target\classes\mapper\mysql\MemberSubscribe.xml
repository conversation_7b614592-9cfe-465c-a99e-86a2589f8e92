<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberSubscribeMapper">
	<resultMap id="memberSubscribeWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubscribeWrapper">
		<result property="memberID" column="memberID" javaType="java.lang.Long" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="productCode" column="productCode" javaType="java.lang.String" />
		<result property="orderID" column="orderID" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="operateCode" column="operateCode" javaType="java.lang.String" />
		<result property="errCode" column="errCode" javaType="java.lang.String" />
		<result property="errDesc" column="errDesc" javaType="java.lang.String" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="memberName" column="memberName" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="orgID" column="orgID" javaType="java.lang.Integer" />
		<result property="orgCode" column="orgCode" javaType="java.lang.String" />
		<result property="batchNo" column="batchNo" javaType="java.lang.String" />
		<result property="deleteBatchNo" column="deleteBatchNo" javaType="java.lang.String" />
	</resultMap>

	<select id="queryMemberStatus" resultMap="memberSubscribeWrapper">
		select
		t.memberID,
		t.msisdn,
		t.productCode,
		t.orderID,
		t.status,
		t.errCode,
		t.errDesc,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.createTime,
		t.updateTime,
		t.subSuccessTime,
		t.operatorID,
		t.batchNo,
		t1.reserved2 AS msisdnType
		from
		ecpm_t_member_subscribe t,
		ecpm_t_member t1
		where
		t.memberID = t1.ID
		and 
		memberID in 
		<foreach collection="memberIDList" item="memberID" open="(" separator="," close=")">
		#{memberID}
		</foreach>
	</select>
	

	<select id="queryMsisdnList" resultMap="memberSubscribeWrapper">
		(
		SELECT t1.memberID, t1.msisdn, t3.reserved2,t.orgID
		FROM ecpm_t_org_rel t,
		ecpm_t_member_subscribe t1,
		ecpm_t_serv_product t2,
		ecpm_t_member t3
		WHERE t.ID = t1.memberID
		and t.ID = t3.ID
		and t1.productCode = t2.productCode
		and t1.status in (3, 5)
		and (orgCode not like concat("%", #{orgCode},"%") or orgCode is null)
		and t2.servType in (1,5)
		and t.orgID in
		<foreach item="orgID" index="index" collection="orgIDList"
				 open="(" separator="," close=")">
			#{orgID}
		</foreach>
		<if test="enterpriseID !=null">
			and t.enterpriseID = #{enterpriseID}
		</if>
		)
		union all
		(
		SELECT t1.memberID, t1.msisdn, t4.reserved2,t.orgID
		FROM ecpm_t_org_rel t,
		ecpm_t_member_subscribe t1,
		ecpm_t_enterprise_code t3,
		ecpm_t_member t4
		WHERE t.ID = t1.memberID
		and t.ID = t4.ID
		and t1.productCode = t3.productCode
		and t1.status in (3, 5)
		and (orgCode not like #{orgCode} or orgCode is null)
		and t.orgID in
		<foreach item="orgID" index="index" collection="orgIDList"
				 open="(" separator="," close=")">
			#{orgID}
		</foreach>
		<if test="enterpriseID !=null">
			and t.enterpriseID = #{enterpriseID}
		</if>
		<if test="parentEnterpriseID !=null">
			and t3.enterpriseID = #{parentEnterpriseID}
		</if>
		)


	</select>

	<select id="queryMemberSubscribeByMsisdn" resultMap="memberSubscribeWrapper">
		(
		select t1.memberID,
		t1.msisdn,
		t1.productCode,
		t1.orderID,
		t1.status,
		t1.operateCode,
		t1.errCode,
		t1.errDesc,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.reserved5,
		t1.createTime,
		t2.orgID,
		t1.updateTime,
		t1.operatorID
		from ecpm_t_member_subscribe t1, ecpm_t_org_rel t2, ecpm_t_serv_product t3
		where
		t1.msisdn=#{msisdn}
		and
		t1.status in (1,2, 3, 4)
		and
		t1.memberID = t2.ID
		and
		t1.productCode = t3.productCode
		and
		t3.servType = 3
		)
		union all
		(
		select t1.memberID,
		t1.msisdn,
		t1.productCode,
		t1.orderID,
		t1.status,
		t1.operateCode,
		t1.errCode,
		t1.errDesc,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.reserved5,
		t1.createTime,
		t2.orgID,
		t1.updateTime,
		t1.operatorID
		from ecpm_t_member_subscribe t1, ecpm_t_org_rel t2, ecpm_t_enterprise_code t4
		where
		t1.msisdn=#{msisdn}
		and
		t1.status in (1,2, 3, 4)
		and
		t1.memberID = t2.ID
		and
		t1.productCode = t4.productCode
		)
	</select>


	<select id="queryMemberSubscribeByMemId" resultMap="memberSubscribeWrapper">
		SELECT t1.memberID,
		t1.msisdn,
		t1.productCode,
		t1.orderID,
		t1.status,
		t1.operateCode,
		t1.errCode,
		t1.errDesc,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.reserved5,
		t1.createTime,
		t1.updateTime,
		t1.operatorID
		 from ecpm_t_member_subscribe  t1 where
		memberID in
		<foreach item="id"  collection="list" open="("
				 separator="," close=")">
			#{id}
		</foreach>
	</select>

	<select id="queryMemberSubscribeListByMsisdn" resultMap="memberSubscribeWrapper">
		select t1.memberID,
		t1.msisdn,
		t1.productCode,
		t1.orderID,
		t1.status,
		t1.operateCode,
		t1.errCode,
		t1.errDesc,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.reserved5,
		t1.createTime,
		t1.updateTime,
		t1.subSuccessTime,
		t1.operatorID,
		t1.reserved1,
		t1.deleteBatchNo
		from ecpm_t_member_subscribe t1
		where 
		t1.msisdn = #{msisdn} and 
		t1.productCode in (
		select t2.productCode from ecpm_t_serv_product t2 where t2.servType in (1,5)
		union all
		select t3.productCode from ecpm_t_enterprise_code t3
		);
		
	</select>
	<select id="querySubscribeListByMsisdn" resultMap="memberSubscribeWrapper">
		select t1.memberID,
		t1.msisdn,
		t1.productCode,
		t1.orderID,
		t1.status,
		t1.operateCode,
		t1.errCode,
		t1.errDesc,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.reserved5,
		t1.createTime,
		t1.updateTime,
		t1.operatorID,
		t1.reserved1
		from ecpm_t_member_subscribe t1
		where
		t1.msisdn = #{msisdn};

	</select>

	<select id="querySubscribeListBySubSuccessTime" resultMap="memberSubscribeWrapper">
		SELECT
		ms.msisdn,
		es.enterpriseName,
		es.reserved5,
		ms.subSuccessTime
		FROM
		ecpm_t_member_subscribe ms
		LEFT JOIN ecpm_t_org_rel ors ON ms.memberID = ors.ID
		LEFT JOIN ecpm_t_enterprise_simple es ON ors.enterpriseID = es.ID
		WHERE
		ms.`status` IN (3, 5, 13)
		AND (
		UNIX_TIMESTAMP(ms.subSuccessTime) &gt;= UNIX_TIMESTAMP(#{startTime})
		AND UNIX_TIMESTAMP(ms.subSuccessTime) &lt;= UNIX_TIMESTAMP(#{endTime})
		);
	</select>

	<select id="queryMemberSubscribeListByGroupId" resultMap="memberSubscribeWrapper">
		select t1.memberID,
		t1.msisdn,
		t1.productCode,
		t1.orderID,
		t1.status,
		t1.operateCode,
		t1.errCode,
		t1.errDesc,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.reserved5,
		t1.createTime,
		t1.updateTime,
		t1.operatorID,
		t1.reserved1
		from ecpm_t_member_subscribe t1
		LEFT JOIN ecpm_t_org_rel CO on CO.ID = t1.memberID
		where
		CO.orgId = #{orgId} and
		t1.productCode in (
		select t2.productCode from ecpm_t_serv_product t2 where t2.servType in (1,5)
		union all
		select t3.productCode from ecpm_t_enterprise_code t3
		);

	</select>

	<update id="updateSubStatusByOrderID">
		update ecpm_t_member_subscribe set
		<trim suffixOverrides="," suffix="where orderID = #{orderID}">
			<if test="status!=null">
				status= #{status},
			</if>
			<if test="errCode!=null">
				errCode= #{errCode},
			</if>
			<if test="errDesc!=null">
				errDesc= #{errDesc},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime}
			</if>
		</trim>
	</update>

	<select id="queryMemberByOrderId" resultMap="memberSubscribeWrapper">
		select
		t1.memberID,
		t1.msisdn,
		t1.productCode,
		t1.orderID,
		t1.status,
		t1.operateCode,
		t1.errCode,
		t1.errDesc,
		t1.extInfo,
		t1.reserved1,
		t1.reserved2,
		t1.reserved3,
		t1.reserved4,
		t1.reserved5,
		t1.createTime,
		t1.updateTime,
		t1.operatorID,
		t2.memberName,
		t3.orgID,
		t3.enterpriseID,
		t1.batchNo
		from ecpm_t_member_subscribe t1, ecpm_t_member
		t2,ecpm_t_org_rel t3
		where
		t1.memberID=t2.ID
		and t3.ID=t1.memberID
		and
		t1.orderID=#{orderID}
	</select>

	<select id="querySubscribeByMemberIDAndPro" resultMap="memberSubscribeWrapper">
		select
		memberID,
		msisdn,
		productCode,
		orderID,
		status,
		errCode,
		errDesc,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		createTime,
		updateTime,
		operatorID
		from
		ecpm_t_member_subscribe
		where
		memberID=#{id}
		<if test="productCode !=null and productCode != ''">
			and productCode=#{productCode}
		</if>
	</select>

	<select id="queryMemberSubscribe" resultMap="memberSubscribeWrapper">
		select
		t1.orgID,
		t1.orgCode,
		t.memberID,
		t.msisdn,
		t.productCode,
		t.orderID,
		t.status,
		t.errCode,
		t.errDesc,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.createTime,
		t1.enterpriseID,
		t.updateTime,
		t.operatorID,
		t.batchNo,
		t.deleteBatchNo
		from
		ecpm_t_member_subscribe t,ecpm_t_org_rel t1
		where
		t.memberID = t1.ID and
		t.productCode =#{productCode}
		<if test="msisdn!=null and msisdn!=''">
			and t.msisdn=#{msisdn}
		</if>
		<if test="memberID!=null">
			and t.memberID =#{memberID}
		</if>
	</select>


	<insert id="createMemberSubscribe">
		insert into
		ecpm_t_member_subscribe
		(
		memberID,
		msisdn,
		productCode,
		status,
		createTime,
		updateTime,
		operatorID,
		reserved1,
		reserved2,
		batchNo
		)
		values
		(
		#{memberID},
		#{msisdn},
		#{productCode},
		#{status},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{reserved1},
		#{reserved2},
		#{batchNo}
		)
	</insert>

	<!-- errCode为响应参数code的值为0，errDesc为响应参数text的值，orderID为响应参数data对象中orderId字段值> -->
	<update id="updateMemberSubscribe">
		update
		ecpm_t_member_subscribe
		set
		status=#{status},
		errCode=#{errCode},
		errDesc=#{errDesc},
		orderID=#{orderID},
		<if test="operateCode!=null and operateCode!=''">
			operateCode=#{operateCode},
		</if>
		<if test="reserved2!=null and reserved2!=''">
			reserved2=#{reserved2},
		</if>
		updateTime =#{updateTime}
		where
		productCode = #{productCode}
		and memberID
		= #{memberID}
		and msisdn = #{msisdn}
	</update>
	
	<update id="updateMemberSubscribeStatus">
		update
		ecpm_t_member_subscribe
		set
		status=#{status}
		where
		productCode = #{productCode}
		and memberID = #{memberID}
	</update>

	<update id="updateMemberUnSubscribeOrderId">
		update
		ecpm_t_member_subscribe
		set
		operatorID = #{operatorID},
		reserved4 = #{reserved4},
		reserved5 = #{reserved5}
		where
		memberID = #{memberID}
	</update>

	<delete id="deleteMemberSubscribe">
		delete from ecpm_t_member_subscribe where
		msisdn = #{msisdn}
		and productCode = #{productCode}
	</delete>
	
	<delete id="deleteMemberSubscribeByMemIDs">
		delete from ecpm_t_member_subscribe where
		memberID in
		<foreach item="id"  collection="list" open="("
			separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<update id="updateMemberSubscribeByMemID">
		update ecpm_t_member_subscribe set status = 13,
		reserved4 = 3,
		errCode = #{errCode},
		errDesc = #{errDesc}
		where memberID = #{memberID}
	</update>
	
	<update id="updateMemberSubscribeByMemIDs">
		update ecpm_t_member_subscribe set status = 13 
		where memberID in
		<foreach item="id"  collection="list" open="("
			separator="," close=")">
			#{id}
		</foreach>
	</update>
	
	<delete id="deleteMemberSubscribeByMemLongIDs">
		delete from ecpm_t_member_subscribe where
		memberID in
		<foreach item="id"  collection="list" open="("
				 separator="," close=")">
			#{id}
		</foreach>
	</delete>

	<update id="batchUpdateStatus" parameterType="java.util.List">
		<foreach collection="list" item="item" open="" close=""
			separator=";">
			update ecpm_t_member_subscribe s,ecpm_t_serv_product p
			set s.status=#{item.status},s.updateTime=#{item.updateTime}
			<if test="item.reserved3!=null and item.reserved3!=''">
				,s.reserved3= #{item.reserved3}
			</if>
			where s.memberID = #{item.memberID} and s.productCode = p.productCode and
			p.servType = 1
		</foreach>
	</update>
	<update id="batchUpdateStatusX" parameterType="java.util.List">
		<foreach collection="list" item="item" open="" close=""
				 separator=";">
			update ecpm_t_member_subscribe s,ecpm_t_serv_product p
			set s.status=#{item.status},s.updateTime=#{item.updateTime}
			<if test="item.reserved3!=null and item.reserved3!=''">
				,s.reserved3= #{item.reserved3}
			</if>
			<if test="item.operatorID!=null">
				,s.operatorID=#{item.operatorID}
			</if>
			where s.memberID = #{item.memberID} and s.productCode = p.productCode
		</foreach>
	</update>

	<update id="batchUpdateStatusXAndDeleteBatchNo">
		<foreach collection="list" item="item" open="" close=""
				 separator=";">
			update ecpm_t_member_subscribe s,ecpm_t_serv_product p
			set s.status=#{item.status},s.updateTime=#{item.updateTime},s.deleteBatchNo = null
			<if test="item.reserved3!=null and item.reserved3!=''">
				,s.reserved3= #{item.reserved3}
			</if>
			<if test="item.operatorID!=null">
				,s.operatorID=#{item.operatorID}
			</if>
			where s.memberID = #{item.memberID} and s.productCode = p.productCode
		</foreach>
	</update>

	<update id="batchUpdateBatchNo" parameterType="java.util.List">
		<foreach collection="list" item="item" open="" close=""
				 separator=";">
			update ecpm_t_member_subscribe s,ecpm_t_serv_product p
			set deleteBatchNo = #{item.batchNo}
			where s.memberID = #{item.memberID} and s.productCode = p.productCode
		</foreach>
	</update>


	<update id="updateSubStatusByMap">
		update ecpm_t_member_subscribe set
		<trim suffixOverrides="," suffix="where msisdn = #{msisdn} and productCode = #{productCode}">
			<if test="status!=null">
				status= #{status},
			</if>
			<if test="errCode!=null">
				errCode= #{errCode},
			</if>
			<if test="errDesc!=null">
				errDesc= #{errDesc},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime}
			</if>
		</trim>
	</update>
	<select id="queryProviceMemberSub" resultMap="memberSubscribeWrapper">
		select
			t.memberID,
			t.msisdn,
			t.productCode,
			t.orderID,
			t.status,
			t.errCode,
			t.errDesc,
			t.extInfo,
			t.reserved1,
			t.reserved2,
			t.reserved3,
			t.reserved4,
			t.reserved5,
			t.createTime,
			t.updateTime,
			t.subSuccessTime,
			t.operatorID,
			t1.memberName,
			t1.reserved2 AS msisdnType,
			t2.orgID
		from
			ecpm_t_member_subscribe t,
			ecpm_t_member t1,
			ecpm_t_org_rel t2
		where
			t.memberID = t1.ID
			and t.memberID = t2.ID
			and status in (3, 5, 13, 0, 2, 4, 8) 
			and t2.orgID in
				<foreach collection="orgIDList" item="orgID" open="(" separator="," close=")">
					#{orgID}
				</foreach>
	</select>
	<select id="queryProviceMemberUnsubscribe" resultMap="memberSubscribeWrapper">
		select
		t.memberID,
		t.msisdn,
		t.productCode,
		t.orderID,
		t.status,
		t.errCode,
		t.errDesc,
		t.reserved4,
		t.reserved5,
		t.createTime,
		t.updateTime,
		t.subSuccessTime,
		t.operatorID,
		t.batchNo,
		t.deleteBatchNo,
		t1.memberName,
		t2.orgID,
		t2.enterpriseID,
		t2.orgCode,
		t3.oriOrgID,
		t3.orgName as orgName,
		t3.orgType,
		t4.orgName as orgInitName
		from
			ecpm_t_member_subscribe t,
			ecpm_t_member t1,
			ecpm_t_org_rel t2,
			ecpm_t_org_simple t3
		LEFT JOIN ecpm_t_org_simple t4
		on t4.ID = t3.oriOrgID
		where
			t.memberID = t1.ID
			AND t.memberID = t2.ID
			AND t.reserved5 = #{orderId}
			AND t2.orgID = t3.ID
	</select>
	<select id="queryProviceSub" resultMap="memberSubscribeWrapper">
		select
		memberID,
		msisdn,
		productCode,
		orderID,
		status,
		errCode,
		errDesc,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		createTime,
		updateTime,
		operatorID,
		reserved1
		from
		ecpm_t_member_subscribe
		where
		productCode = #{productCode}
		and msisdn = #{msisdn}
	</select>
	
	<select id="queryProviceMemberEc" resultMap="memberSubscribeWrapper">
		SELECT
		  t.memberID,
		  t.msisdn,
		  t.productCode,
		  t.orderID,
		  t.status,
		  t.errCode,
		  t.errDesc,
		  t.extInfo,
		  t.reserved1,
		  t.reserved2,
		  t.reserved3,
		  t.reserved4,
		  t.reserved5,
		  t.createTime,
		  t.updateTime,
		  t.operatorID,
		  t1.reserved2  AS msisdnType,
		  t.subSuccessTime
		FROM ecpm_t_member_subscribe t,
		  ecpm_t_member t1
		where t.memberID = t1.ID and memberID in 
		 ( SELECT ID FROM ecpm_t_org_rel where orgID in 
		<foreach collection="orgIDList" item="orgID" open="(" separator="," close=")">
		#{orgID}
		</foreach>
		 )
	</select>
	
	<update id="updateErrInfoByMapMemberID">
		update ecpm_t_member_subscribe set
		<trim suffixOverrides="," suffix="where memberID = #{memberID}">
			<if test="errCode!=null">
				errCode= #{errCode},
			</if>
			<if test="errDesc!=null">
				errDesc= #{errDesc},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime}
			</if>
		</trim>
	</update>


	<select id="queryOrgRelMap" resultType="java.util.HashMap">
		select
		orgID orgID,
		enterpriseID enterpriseID,
		orgCode orgCode
		from
			ecpm_t_org_rel
		where ID in
		<foreach collection="list" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</select>

	<update id="updateMemberSubscribeByMemberNums">
		update ecpm_t_member_subscribe set status = #{status},updateTime = SYSDATE()
		where productCode = #{productCode}
		and msisdn in
		<foreach item="id"  collection="list" open="("
				 separator="," close=")">
			#{id}
		</foreach>
	</update>
	<update id="updateDeleteBatchNo">
		<foreach collection="list" item="item" open="" close=""
				 separator=";">
			update ecpm_t_member_subscribe s
			set s.deleteBatchNo = null
			where s.memberID = #{item}
		</foreach>
	</update>
	<update id="updateDelBatchNo">
		update
			ecpm_t_member_subscribe
		set
			deleteBatchNo = null
		where
			memberID = #{memberID}
	</update>
	<update id="updateBatchNoAndSubSuccessTime">
		update
			ecpm_t_member_subscribe
		set
			batchNo = #{batchNo},
			subSuccessTime = #{subSuccessTime}
		where
			msisdn = #{msisdn}
	</update>

	<select id="queryMemberSubscribeList"
			resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSubscribeWrapper">
		SELECT
		    t1.memberID,
			t1.msisdn,
			t1.status,
			t1.reserved4,
		    t1.subSuccessTime
		FROM
			ecpm_t_member_subscribe t1
			INNER JOIN ecpm_t_org_rel t2 ON t1.memberID = t2.ID
			INNER JOIN ecpm_t_org_simple t3 ON t2.orgID = t3.ID
		WHERE
			t1.msisdn
			IN
				<foreach item="item" index="index" collection="req.msisdnList"
						 open="(" separator="," close=")">
					#{item}
				</foreach>
			AND (t2.orgID = #{req.orgID} OR t3.oriOrgID = #{req.orgID})
	</select>
</mapper>