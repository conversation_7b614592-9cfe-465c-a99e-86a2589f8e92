var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n"])
app.controller('actManageListController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {
        $scope.enterpriseID = $.cookie('enterpriseID');
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            activityName: "",
            auditStatus: "",
        };
        $scope.statusMap={
            "1":"进行中",
            "2":"已结束",
            "3":"未开始",
            "4":"审核中",
            "5":"驳回"
        }
        //下拉框(活动状态)
        $scope.actStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "进行中"
            },
            {
                id: "2",
                name: "已结束"
            }, {
                id: "3",
                name: "未开始"
            }, 
            {
                id: "4",
                name: "审核中"
            }, {
                id: "5",
                name: "驳回"
            }
        ];
        $scope.queryActivityList();
    };
    $scope.formatDate=function(para1,para2){
        if(!para1||!para2){
            return 'format error';
        }
        var start=new Date(para1),
            end=new Date(para2),
            start_year=start.getFullYear(),
            end_year=end.getFullYear(),
            start_month=('0' + (start.getMonth() + 1)).slice(-2),
            end_month=('0' + (end.getMonth() + 1)).slice(-2),
            start_day=('0' + (start.getDate())).slice(-2),
            end_day=('0' + (end.getDate())).slice(-2);
        return start_year+'.'+start_month+'.'+start_day+'-'+end_year+'.'+end_month+'.'+end_day; 

    };
    $scope.querySpokesperson = function (item) {
        //传入objectID，作为唯一标识
        location.href = '../querySpokesperson/querySpokesperson.html?activityID=' + item;
    }
    $scope.queryActDetail=function(item){
        location.href = '../queryActivityDetail/queryActivityDetail.html?activityID=' + item;
    }
    $scope.queryActivityList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "activityInfoCond":{
                    "activityName":$scope.initSel.activityName,
                    "auditStatus":$scope.initSel.auditStatus,
                    "enterpriseID":$scope.enterpriseID
                },
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            if(req.activityInfoCond.auditStatus!=''){
                req.activityInfoCond.auditStatus=parseInt(req.activityInfoCond.auditStatus);
            }
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/activityService/queryActivityList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.activityInfoList = result.activityInfoList||[];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalcount) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount!==0 ? Math.ceil(result.totalcount / parseInt($scope.pageInfo[0].pageSize)):1;
                    } else {
                        $scope.activityInfoList = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.activityInfoList = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = "**********";
                    $('#myModal').modal();
                }
                )
            }
        });

    }
}]) 
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])