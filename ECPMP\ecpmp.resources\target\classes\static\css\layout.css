/***layout***/
body,html{height: 100%;font-size: 14px;overflow: hidden}
.transition{-webkit-transition: all .2s ease-in;-moz-transition: all .2s ease-in;-ms-transition: all .2s ease-in;transition: all .2s ease-in;}
.dib{display: inline-block;*display: inline;*zoom:1;vertical-align: middle;}
.container .main_left {
  width: 15%;
  background: #ffffff;
  min-width: 230px;
  overflow-y: auto;
}

.container .main_right {
  width: 85%;
  min-height: 100%;
  background: #f8f8f8;
}
.main_right .menu_Bar{
    background: linear-gradient(0deg, #8f83e7, #8171e4, #715de1);
    height: 58px;
    line-height: 58px;
    text-align: right;
    color:#ffffff;
    display:flex;
    justify-content: flex-end;
    align-items: center;
}
.menu_Bar .user{margin-right: 60px;}
.menu_Bar a{margin-right: 20px;}
.cont_main{box-sizing: border-box;height: calc(100% - 58px);}

/***sideNav***/
.sideNav{padding-right:25px;border-top:1px solid #f8f8f8 }
.sideNav .navcss .navItem{padding-left: 25px;border-bottom:1px solid #f8f8f8}
.sideNav .navItem,
.sideNav .subNav li{padding: 17px 0; cursor: pointer;border-bottom:1px solid #f8f8f8}
.sideNav .navName{
    width: 120px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-align: center;
    color:#c3c3c3;
    font-size: 16px;
}
.sideNav .subNav .navName{
    width: 100%;
    font-size: 14px;
}
.moreArr{
    width: 24px;
    height: 24px;
    background-image:url(../assets/images/arrow1.png);
    background-repeat: no-repeat;
}
.active .moreArr{
    transform:rotate(90deg);
	-ms-transform:rotate(90deg);
	-webkit-transform:rotate(90deg);
}
.navcss{min-width: 220px}
.navcss.active .navName{
    color:#383838
}
.navcss.subNav{display: none;
    height: 0;}
.navcss.active .subNav{display: block;height: auto;}
.subnavcss{border-left: 10px solid;border-left-color: transparent;}
.subnavcss.active{border-left-color:#715de1}

/***icon***/

.navIcon,.operateIcon{
    width: 24px;
    height: 24px;
    background-repeat: no-repeat;
}
.navIcon{background-image:url(../assets/images/sideNavIcons.png);}
.navIcon.icon_1000{background-position: 0 0;}
.active .navIcon.icon_1000{background-position: 0 -24px;}
.navIcon.icon_1001,.navIcon.icon_2000{background-position: -24px 0;}
.active .navIcon.icon_1001, .active .navIcon.icon_2000{background-position: -24px -24px;}
.navIcon.icon_1002,.navIcon.icon_2001{background-position: -48px 0;}
.active .navIcon.icon_1002,.active .navIcon.icon_2001{background-position: -48px -24px;}
.navIcon.icon_1004{background-position: -72px 0;}
.active .navIcon.icon_1004{background-position: -72px -24px;}
.navIcon.icon_1003{background-position: -96px 0;}
.active .navIcon.icon_1003{background-position: -96px -24px;}

.operateIcon{background-image:url(../assets/images/operateIcon.png);}
.operateIcon.close{background-position: 0 0}
.operateIcon.set{background-position: -24px 0}

.starIcon{
    padding: 8px 16px 10px;
    background-image: url(../assets/images/redStar.png);
    background-size: 20px;
    background-repeat: no-repeat;
    background-position: center;
}

.hoverName:hover {
  color: #23527c;
}
.menu_Bar ul{
  float: right;
  /*overflow: hidden;*/
}
.menu_Bar ul li{
  margin-right: 26px;
  display: inline-block;
  line-height: 58px;
  color: #fff;
  cursor: pointer;
}
.menu_Bar ul li .admin{
  display: inline-block;
  width: 22px;
  height: 22px;
  background: url(../assets/images/operateIcon.png)no-repeat;
  margin-right: 4px;
  vertical-align: sub;
}
.menu_Bar ul li .admin.set-icon{
  background-position: -24px 0;
}
.menu_Bar ul li .admin.back-icon{
  background-position: 0 0;
}