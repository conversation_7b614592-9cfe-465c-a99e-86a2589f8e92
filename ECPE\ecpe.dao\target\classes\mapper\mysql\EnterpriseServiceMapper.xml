<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.EnterpriseServiceMapper">
	<resultMap id="enterpriseService"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.EnterpriseServiceWrapper">
		<id column="ID" property="id"/>
		<result property="enterpriseID" column="enterpriseID" />
		<result property="serviceID" column="serviceID" />
		<result property="subservice" column="subservice" />
		<result property="chargeType" column="chargeType" />
		<result property="sendTimes" column="sendTimes" />
		<result property="updateTime" column="updateTime" />
	</resultMap>

	<select id="searchEnterpriseService" resultMap="enterpriseService">
		select 
		ID,
		enterpriseID,
		serviceID,
		subservice,
		chargeType,
		sendTimes,
		updateTime
		from ecpe_t_enterprise_service t
		<trim prefix="where" suffixOverrides="AND">
			<if test="id != null">
				t.ID = #{id} AND 
			</if>
			<if test="enterpriseID != null">
				t.enterpriseID = #{enterpriseID} AND 
			</if>
			<if test="serviceID != null">
				t.serviceID = #{serviceID} AND 
			</if>
			<if test="subservice != null">
				t.subservice = #{subservice} AND 
			</if>
			<if test="chargeType != null">
				t.chargeType = #{chargeType} AND 
			</if>
			<if test="sendTimes != null">
				t.sendTimes=#{sendTimes} AND
			</if>
		</trim>
	</select>

	<insert id="addEnterpriseService">
		insert into ecpe_t_enterprise_service
		(
			enterpriseID,
			serviceID,
			subservice,
			chargeType,
			sendTimes,
			updateTime
		)
		values
		(
			#{enterpriseID},
			#{serviceID},
			#{subservice},
			#{chargeType},
			#{sendTimes},
			#{updateTime}
		)
	</insert>


	<update id="setEnterpriseService">
		update ecpe_t_enterprise_service t
		set 
		<if test="enterpriseID != null">
			t.enterpriseID = #{enterpriseID},
		</if>
		<if test="serviceID != null">
			t.serviceID = #{serviceID},
		</if>
		<if test="subservice != null">
			t.subservice = #{subservice}, 
		</if>
		<if test="chargeType != null">
			t.chargeType = #{chargeType},
		</if>
		<if test="sendTimes != null">
			t.sendTimes=#{sendTimes}
		</if>
        where
            ID=#{id}
	</update>
	
</mapper>
