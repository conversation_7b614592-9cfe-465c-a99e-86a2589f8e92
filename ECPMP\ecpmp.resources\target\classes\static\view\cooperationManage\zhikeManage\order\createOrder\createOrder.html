<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css">
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/mian.css" rel="stylesheet"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
    <link href="../../../../../css/layout.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
    <link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">

    <!--引入JS-->
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
    <script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <link href="../../../../../css/createOrder.css" rel="stylesheet">
    <script type="text/javascript" src="createOrder.js"></script>

    <style>
        .upload div {
            padding-top: 0px;
        }

        .webuploader-pick {
            padding: 10px 12px !important;
        }

        .form-horizontal .control-label {
            padding-top: 7px;
            margin-bottom: 0;
            text-align: right;
        }

        .input-min-width {
            min-width: 190px;
        }

        .error-ver img, .error-ver > span {
            vertical-align: middle;
        }
        .control-left-label {
            padding-top: 7px;
            margin-bottom: 0;
            text-align: left;
        }
    </style>
</head>

<body>
<div ng-app="myApp" ng-init="init();" ng-controller="orderController" class="body-min-width">
    <div class="cooperation-manage">
        <div ng-if="isSuperManager && enterpriseType == '1'" class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
        </div>
        <div ng-if="loginRoleType=='agent' || enterpriseType == '2'" class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
        </div>
        <!-- 管理员登录查看直客 -->
        <top:menu chose-index="2"
                  page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/orderList"
                  list-index="1" ng-if="isSuperManager && enterpriseType == '1'"></top:menu>
        <top:menu chose-index="2"
                  page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/orderList/orderList"
                  list-index="51" ng-if="isSuperManager && enterpriseType == '2'"></top:menu>
        <div class="cooper-title" ng-bind="'CREATEORDER_ORDERINFO'|translate"></div>

        <div class="cooper-tab">
            <form class="form-horizontal" name="orderBase" novalidate>
                <!--企业名称-->
                <div class="form-group">
                    <div class="row">
                        <label class="col-xs-2 control-label"
                               ng-bind="'CREATEORDER_ENTERPRISENAME'|translate"></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3"><p>{{enterpriseName}}</p></div>

                        <label for="servType" class="col-xs-2 control-label"
                               ng-bind="'CREATEORDER_SERVTYPE'|translate"></label>

                        <div class="col-lg-3 col-xs-4 col-sm-3 col-md-3">
                            <select id="servType" class="form-control" ng-model="servType"
                                    ng-change="queryOrderList()"
                                    ng-options="x.id as x.name for x in servTypeChoise"></select>
                        </div>
                    </div>
                </div>

                <!--是否体验版-->
                <div class="form-group">
                    <div class="row">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CREATEORDER_EXPERIENCE'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">
                            <!--同一业务大类下已有体验订单 则不能再创建体验单-->
                            <li class="redio-li" ng-click="showOrderName(1)" ng-show="orderListData.length===0">
                                <span class="check-btn redio-btn"
                                      ng-class="{true:'checked',false:''}[isExperience===1]"> </span>
                                <span ng-bind="'YES'|translate"></span>
                            </li>
                            <li class="redio-li" ng-click="showOrderName(0)">
                                <span class="check-btn redio-btn"
                                      ng-class="{true:'checked',false:''}[isExperience===0]"></span>
                                <span ng-bind="'NO'|translate"></span>
                            </li>
                        </div>

                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CREATEORDER_ORDERNAME'|translate"></span>
                        </label>

                        <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                            <input type="text" class="form-control" disabled="disabled" ng-model="orderName">
                        </div>
                    </div>
                </div>

                <!--订单金额-->
                <div class="form-group">
                    <div class="row">
                        <label for="amount" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CREATEORDER_AMOUNT'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">
                            <input type="text" class="form-control" autocomplete="off"
                                   placeholder="{{'CREATEORDER_INPUTAMOUNT'|translate}}" name="amount" id="amount"
                                   ng-model="amount" required
                                   pattern="(^[0-9]{1,17}$)|(^[0-9]{1,17}[\.]{1}[0-9]{1,3}$)">
                            <span style="color:red" ng-show="orderBase.amount.$dirty && orderBase.amount.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderBase.amount.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderBase.amount.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXAMOUNT'|translate"></span>
									</span>
                        </div>
                    </div>
                </div>

                <!--转账附件-->
                <div id="ZZMX"  class="form-group upload">
                    <div class="row">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <sapn ng-bind="'CREATEORDER_TRANSFERATTACHURL'|translate"></sapn>
                        </label>

                        <div class="col-lg-3 col-xs-4 col-sm-4 col-md-3">
                            <input type="text" class="form-control" ng-model="transferAttachURL.fileName"
                                   id="transferAttachURLFileName"
                                   placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
                                   title={{transferAttachURL.fileName}}>
                        </div>
                        <cy:uploadifyfile filelistid="fileList2" filepickerid="filePicker2" accepttype="accepttype"
                                          uploadifyid="uploadifyid2" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList1" createthumbnail="isCreateThumbnail" auto="auto"
                                          class="col-xs-2">
                        </cy:uploadifyfile>
                        <div style="color:#ff0000;" ng-show="transferAttachURL.errorInfo">
                            <span class="uplodify-error-img"></span>
                            <span ng-bind="transferAttachURL.errorInfo|translate"></span>
                        </div>
                    </div>
                </div>


                <!--订单明细-->
                <div id="DDMX" class="form-group upload">
                    <div class="row">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <sapn ng-bind="'CREATEORDER_ORDERDETAILURL'|translate"></sapn>
                        </label>

                        <div class="col-lg-3 col-xs-4 col-sm-4 col-md-3">
                            <input type="text" class="form-control" ng-model="orderDetailURL.fileName"
                                   id="orderDetailURLFileName"
                                   placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
                                   title={{orderDetailURL.fileName}}>
                        </div>
                        <cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
                                          uploadifyid="uploadifyid1" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadParam2" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList2" createthumbnail="isCreateThumbnail" auto="auto"
                                          class="col-xs-2">
                        </cy:uploadifyfile>
                        <div style="color:#ff0000;" ng-show="orderDetailURL.errorInfo">
                            <span class="uplodify-error-img"></span>
                            <span ng-bind="orderDetailURL.errorInfo|translate"></span>
                        </div>
                    </div>
                </div>

                <!--体验申请单-->
                <div id="TYSQD" style="visibility:hidden;" class="form-group upload">
                    <div class="row">
                        <label class="col-xs-2 control-label">
                        <icon>*</icon>
                            <sapn ng-bind="'CREATEORDER_ORDEREXPERIENCE'|translate"></sapn>
                        </label>

                        <div class="col-lg-3 col-xs-4 col-sm-4 col-md-3">
                            <input type="text" class="form-control" ng-model="transferAttachURL.fileName"
                                   id="transferAttachURLFileName"
                                   placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
                                   title={{transferAttachURL.fileName}}>
                        </div>
                        <cy:uploadifyfile filelistid="fileList2" filepickerid="filePicker3" accepttype="accepttype"
                                          uploadifyid="uploadifyid3" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList1" createthumbnail="isCreateThumbnail" auto="auto"
                                          class="col-xs-2">
                        </cy:uploadifyfile>
                        <div style="color:#ff0000;" ng-show="transferAttachURL.errorInfo">
                            <span class="uplodify-error-img"></span>
                            <span ng-bind="transferAttachURL.errorInfo|translate"></span>
                        </div>
                    </div>
                </div>

            </form>
        </div>
        <!--||(servType =='3' && (!postPingXianCMCC || !postPingXianCUCC || !postPingXianCTCC)  && isExperience == 0)-->
        <!-- 业务配额代码 -->
        <div class="cooper-title">
            <span class="red">*</span>
            <span ng-bind="'CREATEORDER_ORDERITEMLIST'|translate"></span>
            <span style="color:red"
                  ng-show="((!(cmcc || cucc || ctcc) ||(!(cmcc && (postPingXianCMCC || (servType !='4'&& postGuaCai) || postGuaDuan || (servType =='4'&& postZengCaiCMCC)
                  			 || (servType =='2'&& postZengCaiCMCC))) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)) && servType !='3')
							|| (servType =='3'&& isExperience == 1 && !(cmcc && postPingXianCMCC))
							||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))
                              ">
					<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                         style="vertical-align: middle">
					<span ng-bind="'CREATEORDER_MININPUTDESC'|translate"
                          style="vertical-align: middle;font-size: 14px"></span>
				</span>
        </div>

        <div class="cooper-tab">
            <form class="form-horizontal" name="orderItemDomain" novalidate>
                <!--移动-->
                <div class="form-group" ng-show="servType !='4'">
                    <div class="row">
                        <li class="col-xs-2 check-li" ng-click="chooseCMCC()">
                            <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cmcc]"></span>
                            <span>移动</span>
                        </li>
                    </div>
                </div>
                <div style="margin-left: 65px" ng-show="cmcc===true && servType !='4'">
                    <!--屏显配额-->
                    <div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showPingXianCMCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postPingXianCMCC]"></span>
                                <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate" ng-show="servType !=3"></span>
                                <!--<span ng-bind="'CREATEORDER_GUANGGAOPEIE'|translate" ng-show="servType ==3"></span>-->
                                <span ng-bind="'CREATEORDER_GUANGGAOANDPEIE'|translate" ng-show="servType ==3"></span>
                            </li>
                        </div>
                    </div>

                    <div class="form-group" ng-show="postPingXianCMCC">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changePingXianType(1)" ng-show="servType !='3'">
									<span class="check-btn redio-btn" id="pingXianType1"
                                          ng-class="{true:'checked',false:''}[chargeType===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changePingXianType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="pingXianType2"
                                          ng-class="{true:'checked',false:''}[chargeType===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" style="white-space: nowrap;"
                                 ng-show="chargeType ==1 && postPingXianCMCC"
                                 ng-if="isExperience == '0' && isAcDesc">
                                <span ng-bind="PXPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>

                            <div class="col-xs-2 input-min-width error-ver"
                                 ng-show="chargeType ==1 && postPingXianCMCC && isAcDesc">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       ng-disabled="chargeType!=1" name="anci"
                                       ng-model="anci" required pattern="^[0-9]{1,9}$">
                                <span style="color:red"
                                      ng-show="orderItemDomain.anci.$dirty && orderItemDomain.anci.$invalid && chargeType==1 && postPingXianCMCC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.anci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.anci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                 ng-show="chargeType ==1 && postPingXianCMCC"
                                 ng-if="isExperience == '0' && isAcDesc"
                                 style="white-space: nowrap">
                                <span ng-bind="accMul(PXPrice,anci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2" style="padding-top:7px"
                                 ng-if="!isAcDesc" ng-bind="acDesc"></div>

                            <div class="form-group" ng-show="servType =='3'">
                                <div class="row">
                                    <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                                       ng-bind="'CREATEORDER_DESC3'|translate"></p>
                                </div>
                            </div>

                            <!--按人/包月-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <div ng-show="servType !='3'">
                                <li class="col-xs-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    id="pingXianType3"
                                    ng-click="changePingXianType(3)" style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[chargeType===2]"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </li>
                                <div class="col-xs-2 input-min-width error-ver" ng-show="isByDesc">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                           ng-disabled="chargeType!=2"
                                           name="peirenyue" ng-model="peirenyue" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.peirenyue.$dirty && orderItemDomain.peirenyue.$invalid && chargeType==2 && postPingXianCMCC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.peirenyue.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.peirenyue.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                </div>
                                <label class="col-xs-2 control-label" style="width: 115px"
                                       ng-bind="'TAOCAN_CHOOSE'|translate" ng-if="isByDesc"></label>

                                <div class="col-xs-1" ng-if="isByDesc" style="min-width: 210px">
                                    <select id="productName" class="form-control" ng-model="productID"
                                            ng-change="getPorductUnitPrice()"
                                            ng-disabled="chargeType!=2"
                                            ng-options="x.objectID as x.productName for x in baoyueProductList">
                                    </select>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeType ==2 && postPingXianCMCC"
                                     ng-if="isExperience == '0' && isByDesc"
                                     style="padding-top: 6px;padding-left: 0;white-space: nowrap">
                                    <span ng-bind="accSum(peirenyue)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2" style="padding-top:8px"
                                     ng-if="!isByDesc" ng-bind="byDesc"></div>
                            </div>

                        </div>
                    </div>

                    <div class="form-group" ng-show="servType !='3'">
                        <div class="row">
                            <!--挂机短信-->
                            <li class="col-xs-2 check-li" ng-click="showGuaDuan()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaDuan]"></span>
                                <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                            </li>
                        </div>
                    </div>

                    <div class="form-group" ng-show="postGuaDuan && servType !='3'">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGuaDuanType(1)" ng-show="servType !='3'">
									<span class="check-btn redio-btn" id="GuaDuanType1"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaDuan===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="gdesc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGuaDuanType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="GuaDuanType2"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaDuan===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                 ng-show="chargeTypeGuaDuan==1 && postGuaDuan"
                                 ng-if="isExperience == '0' && isGdDesc"
                                 style="white-space: nowrap;">
                                <span ng-bind="GDPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver"
                                 ng-show="chargeTypeGuaDuan ==1 && postGuaDuan && isGdDesc">
                                <input type="text" class="form-control" autocomplete="off" ng-model="guaduanInput"
                                       ng-disabled="chargeTypeGuaDuan==0"
                                       name="guaduanInput" placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" required
                                       pattern="^[0-9]{1,9}$" ng-show="postGuaDuan">
                                <span style="color:red"
                                      ng-show="orderItemDomain.guaduanInput.$dirty && orderItemDomain.guaduanInput.$invalid && chargeTypeGuaDuan==1 && postGuaDuan">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.guaduanInput.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.guaduanInput.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                 ng-show="chargeTypeGuaDuan==1 && postGuaDuan"
                                 ng-if="isExperience == '0' && isGdDesc"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(GDPrice,guaduanInput)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2" style="padding-top:2px"
                                 ng-if="!isGdDesc" ng-bind="gdDesc"></div>
                            <!--按人/包月-->

                            <div class="col-lg-10 col-xs-10"></div>
                            <div ng-show="servType =='1'">
                                <li class="col-xs-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    id="GuaDuanType3"
                                    ng-click="changeGuaDuanType(3)" style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[chargeTypeGuaDuan===2]"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </li>

                                <div class="col-xs-2 input-min-width error-ver" ng-show="isGDByDesc">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                           ng-disabled="chargeTypeGuaDuan!=2"
                                           name="guaDuanPrice" ng-model="guaDuanPrice" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.guaDuanPrice.$dirty && orderItemDomain.guaDuanPrice.$invalid && chargeTypeGuaDuan==2 && postGuaDuan">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.guaDuanPrice.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.guaDuanPrice.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                </div>
                                <label class="col-xs-2 control-label" style="width: 115px"
                                       ng-bind="'TAOCAN_CHOOSE'|translate" ng-if="isGDByDesc"></label>

                                <div class="col-xs-1" ng-if="isGDByDesc" style="min-width: 210px">
                                    <select id="gdProductName" class="form-control" ng-model="gdproductID"
                                            ng-change="getPorductPrice()"
                                            ng-disabled="chargeTypeGuaDuan!=2"
                                            ng-options="x.objectID as x.productName for x in guaDuanBaoyueProductList">
                                    </select>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeTypeGuaDuan ==2 && postGuaDuan"
                                     ng-if="isExperience == '0' && isGDByDesc"
                                     style="padding-top: 6px;padding-left: 0;white-space: nowrap">
                                    <span ng-bind="accGDSum(guaDuanPrice)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2" style="padding-top:8px"
                                     ng-if="!isGDByDesc" ng-bind="gdByDesc"></div>
                            </div>

                        </div>
                    </div>

                    <div class="form-group" ng-show="servType !='3'">
                        <div class="row">
                            <!--挂机彩信-->
                            <li class="col-xs-2 check-li" ng-click="showGuaCai()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaCai]"></span>
                                <span ng-bind="'CREATEORDER_GUACAI'|translate"></span>
                            </li>
                        </div>
                    </div>

                    <div class="form-group" ng-show="postGuaCai && servType !='3' ">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGuaCaiType(1)" ng-show="servType !='3'">
									<span class="check-btn redio-btn" id="GuaCaiType1"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaCai===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="gcesc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGuaCaiType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="GuaCaiType2"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaCai===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                 ng-show="chargeTypeGuaCai==1 && postGuaCai"
                                 ng-if="isExperience == '0' && isGcDesc"
                                 style="white-space: nowrap;">
                                <span ng-bind="GCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>

                            <div class="col-xs-2 input-min-width error-ver"
                                 ng-show="chargeTypeGuaCai ==1 && postGuaCai && isGcDesc">
                                <input type="text" class="form-control" autocomplete="off" ng-model="guacaiInput"
                                       ng-disabled="chargeTypeGuaCai==0"
                                       name="guacaiInput" placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" required
                                       pattern="^[0-9]{1,9}$" ng-show="postGuaCai">
                                <span style="color:red"
                                      ng-show="orderItemDomain.guacaiInput.$dirty && orderItemDomain.guacaiInput.$invalid && chargeTypeGuaCai==1 && postGuaCai">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.guacaiInput.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.guacaiInput.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                 ng-show="chargeTypeGuaCai==1 && postGuaCai"
                                 ng-if="isExperience == '0' && isGcDesc"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(GCPrice,guacaiInput)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2" style="padding-top:2px"
                                 ng-if="!isGcDesc" ng-bind="gcDesc"></div>
                            <!--按人/包月-->

                            <div class="col-lg-10 col-xs-10"></div>
                            <div ng-show="servType =='1'">
                                <li class="col-xs-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    id="GuaCaiType3"
                                    ng-click="changeGuaCaiType(3)" style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[chargeTypeGuaCai===2]"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </li>

                                <div class="col-xs-2 input-min-width error-ver" ng-show="isGCByDesc">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                           ng-disabled="chargeTypeGuaCai!=2"
                                           name="guaCaiPrice" ng-model="guaCaiPrice" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.guaCaiPrice.$dirty && orderItemDomain.guaCaiPrice.$invalid && chargeTypeGuaCai==2 && postGuaCai">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.guaCaiPrice.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.guaCaiPrice.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                </div>
                                <label class="col-xs-2 control-label" style="width: 115px"
                                       ng-bind="'TAOCAN_CHOOSE'|translate" ng-if="isGCByDesc"></label>

                                <div class="col-xs-1" ng-if="isGCByDesc" style="min-width: 210px">
                                    <select id="gcProductName" class="form-control" ng-model="gcproductID"
                                            ng-change="getPorductGCPrice()"
                                            ng-disabled="chargeTypeGuaCai!=2"
                                            ng-options="x.objectID as x.productName for x in guaCaiBaoyueProductList">
                                    </select>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeTypeGuaCai ==2 && postGuaCai"
                                     ng-if="isExperience == '0' && isGCByDesc"
                                     style="padding-top: 6px;padding-left: 0;white-space: nowrap">
                                    <span ng-bind="accGCSum(guaCaiPrice)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2" style="padding-top:8px"
                                     ng-if="!isGCByDesc" ng-bind="gcByDesc"></div>
                            </div>

                        </div>
                    </div>
                    <div class="form-group" ng-show="servType =='2'">
                        <div class="row">
                            <!--挂机增彩-->
                            <li class="col-xs-2 check-li" ng-click="showZengCaiCMCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postZengCaiCMCC]"></span>
                                <span ng-bind="'CREATEORDER_ZENGCAI'|translate"></span>
                            </li>
                        </div>
                    </div>
					<div class="form-group" ng-show="postZengCaiCMCC && servType =='2'">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="guajiZengCaiType(1)">
									<span class="check-btn redio-btn" id="zengCaiType4"
                                          ng-class="{true:'checked',false:''}[gjzcchargeTypeEx===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="guajiZengCaiType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="zengCaiType3"
                                          ng-class="{true:'checked',false:''}[gjzcchargeTypeEx===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>

                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="gjzcchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="gjzcPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="gjzcchargeTypeEx ==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="gjzcanci"
                                       ng-model="gjzcanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.gjzcanci.$dirty && orderItemDomain.gjzcanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.gjzcanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.gjzcanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="gjzcchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(gjzcPrice,gjzcanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div ng-show="isExperience===0 && servType !='4'">
                    <!--联通-->
                    <div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="chooseCUCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cucc]"></span>
                                <span>联通</span>
                            </li>
                        </div>
                    </div>

                    <div style="margin-left: 65px" ng-show="cucc===true">
                        <!--屏显配额-->
                        <div class="form-group">
                            <div class="row">
                                <li class="col-xs-2 check-li" ng-click="showPingXianCUCC()">
                                    <span class="check-btn checked-btn"
                                          ng-class="{true:'checked',false:''}[postPingXianCUCC]"></span>
                                    <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"></span>
                                </li>
                            </div>
                        </div>

                        <div class="form-group" ng-show="postPingXianCUCC">
                            <div class="row">
                                <!--不限-->
                                <li class="col-xs-2 col-xs-offset-2 redio-li"
                                    ng-click="changePingXianTypeCUCC(1)" ng-show="servType !='3'">
										<span class="check-btn redio-btn"
                                              ng-class="{true:'checked',false:''}[chargeType_cucc===0]">
										</span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc_cucc" style="margin-left:30px"></span>
                                </li>
                                <!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    ng-click="changePingXianTypeCUCC(2)" style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[chargeType_cucc===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" style="white-space: nowrap;"
                                     ng-show="chargeType_cucc ==1 && postPingXianCUCC"
                                     ng-if="isExperience == '0' && isAcDesc_cucc">
                                    <span ng-bind="PXPrice_cucc"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>

                                <div class="col-xs-2 input-min-width error-ver"
                                     ng-show="chargeType_cucc ==1 && postPingXianCUCC && isAcDesc_cucc">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                           ng-disabled="chargeType_cucc!=1" name="anci_cucc"
                                           ng-model="anci_cucc" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.anci_cucc.$dirty && orderItemDomain.anci_cucc.$invalid && chargeType_cucc==1 && postPingXianCUCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20" align="absmiddle">
											<span ng-show="orderItemDomain.anci_cucc.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
																<span ng-show="orderItemDomain.anci_cucc.$error.pattern"
                                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeType_cucc ==1 && postPingXianCUCC"
                                     ng-if="isExperience == '0' && isAcDesc_cucc"
                                     style="white-space: nowrap">
                                    <span ng-bind="accMul(PXPrice_cucc,anci_cucc)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2" style="padding-top:7px"
                                     ng-if="!isAcDesc_cucc" ng-bind="acDesc_cucc"></div>
                            </div>
                        </div>
                        <!--add by hyj start-->

                        <div class="form-group" ng-show="servType =='2'">
                            <div class="row">
                                <!--挂机短信-->
                                <li class="col-xs-2 check-li" ng-click="showGuaDuanCUCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaDuanCUCC]"></span>
                                    <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                </li>
                            </div>
                        </div>

                        <div class="form-group" ng-show="postGuaDuanCUCC && servType =='2'">
                            <div class="row">
                                <!--不限-->
                                <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    ng-click="changeGuaDuanTypeCUCC(1)" ng-show="servType =='2'">
									<span class="check-btn redio-btn" id="GuaDuanCUCCType1"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaDuanCUCC===0]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="gdescCUCC" style="margin-left:30px"></span>
                                </li>
                                <!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    ng-click="changeGuaDuanTypeCUCC(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="GuaDuanCUCCType2"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaDuanCUCC===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeTypeGuaDuanCUCC==1 && postGuaDuanCUCC"
                                     ng-if="isExperience == '0' && isGdDescCUCC"
                                     style="white-space: nowrap;">
                                    <span ng-bind="GDPriceCUCC"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2 input-min-width error-ver"
                                     ng-show="chargeTypeGuaDuanCUCC ==1 && postGuaDuanCUCC && isGdDescCUCC">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="guaduanInputCUCC"
                                           ng-disabled="chargeTypeGuaDuanCUCC==0"
                                           name="guaduanInputCUCC" placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" required
                                           pattern="^[0-9]{1,9}$" ng-show="postGuaDuanCUCC">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.guaduanInputCUCC.$dirty && orderItemDomain.guaduanInputCUCC.$invalid && chargeTypeGuaDuanCUCC==1 && postGuaDuanCUCC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.guaduanInputCUCC.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.guaduanInputCUCC.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeTypeGuaDuanCUCC==1 && postGuaDuanCUCC"
                                     ng-if="isExperience == '0' && isGdDescCUCC"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(GDPriceCUCC,guaduanInputCUCC)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2" style="padding-top:2px"
                                     ng-if="!isGdDescCUCC" ng-bind="gdDescCUCC"></div>
                            </div>
                        </div>

                        <!--add by hyj end-->
                    </div>
                </div>

                <div ng-show="isExperience===0 && servType !='4'">
                    <!--电信-->
                    <div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="chooseCTCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[ctcc]"></span>
                                <span>电信</span>
                            </li>
                        </div>
                    </div>

                    <div style="margin-left: 65px" ng-show="ctcc===true">
                        <!--屏显配额-->
                        <div class="form-group">
                            <div class="row">
                                <li class="col-xs-2 check-li" ng-click="showPingXianCTCC()">
                                    <span class="check-btn checked-btn"
                                          ng-class="{true:'checked',false:''}[postPingXianCTCC]"></span>
                                    <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"></span>
                                </li>
                            </div>
                        </div>

                        <div class="form-group" ng-show="postPingXianCTCC">
                            <div class="row">
                                <!--不限-->
                                <li class="col-xs-2 col-xs-offset-2 redio-li"
                                    ng-click="changePingXianTypeCTCC(1)" ng-show="servType !='3'">
										<span class="check-btn redio-btn"
                                              ng-class="{true:'checked',false:''}[chargeType_ctcc===0]">
										</span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc_ctcc" style="margin-left:30px"></span>
                                </li>
                                <!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    ng-click="changePingXianTypeCTCC(2)" style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[chargeType_ctcc===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" style="white-space: nowrap;"
                                     ng-show="chargeType_ctcc ==1 && postPingXianCTCC"
                                     ng-if="isExperience == '0' && isAcDesc_ctcc">
                                    <span ng-bind="PXPrice_ctcc"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>

                                <div class="col-xs-2 input-min-width error-ver"
                                     ng-show="chargeType_ctcc ==1 && postPingXianCTCC && isAcDesc_ctcc">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                           ng-disabled="chargeType_ctcc!=1" name="anci_ctcc"
                                           ng-model="anci_ctcc" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.anci_ctcc.$dirty && orderItemDomain.anci_ctcc.$invalid && chargeType_ctcc==1 && postPingXianCTCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20" align="absmiddle">
											<span ng-show="orderItemDomain.anci_ctcc.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
																<span ng-show="orderItemDomain.anci_ctcc.$error.pattern"
                                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeType_ctcc ==1 && postPingXianCTCC"
                                     ng-if="isExperience == '0' && isAcDesc_ctcc"
                                     style="white-space: nowrap">
                                    <span ng-bind="accMul(PXPrice_ctcc,anci_ctcc)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2" style="padding-top:7px"
                                     ng-if="!isAcDesc_ctcc" ng-bind="acDesc_ctcc"></div>
                            </div>
                        </div>
                        <!--add by hyj start-->
                        <div class="form-group" ng-show="servType =='2'">
                            <div class="row">
                                <!--挂机短信-->
                                <li class="col-xs-2 check-li" ng-click="showGuaDuanCTCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaDuanCTCC]"></span>
                                    <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                </li>
                            </div>
                        </div>

                        <div class="form-group" ng-show="postGuaDuanCTCC && servType =='2'">
                            <div class="row">
                                <!--不限-->
                                <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    ng-click="changeGuaDuanTypeCTCC(1)" ng-show="servType =='2'">
									<span class="check-btn redio-btn" id="GuaDuanCTCCType1"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaDuanCTCC===0]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="gdescCTCC" style="margin-left:30px"></span>
                                </li>
                                <!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    ng-click="changeGuaDuanTypeCTCC(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="GuaDuanCTCCType2"
                                          ng-class="{true:'checked',false:''}[chargeTypeGuaDuanCTCC===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeTypeGuaDuanCTCC==1 && postGuaDuanCTCC"
                                     ng-if="isExperience == '0' && isGdDescCTCC"
                                     style="white-space: nowrap;">
                                    <span ng-bind="GDPriceCTCC"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2 input-min-width error-ver"
                                     ng-show="chargeTypeGuaDuanCTCC ==1 && postGuaDuanCTCC && isGdDescCTCC">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="guaduanInputCTCC"
                                           ng-disabled="chargeTypeGuaDuanCTCC==0"
                                           name="guaduanInputCTCC" placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" required
                                           pattern="^[0-9]{1,9}$" ng-show="postGuaDuanCTCC">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.guaduanInputCTCC.$dirty && orderItemDomain.guaduanInputCTCC.$invalid && chargeTypeGuaDuanCTCC==1 && postGuaDuanCTCC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.guaduanInputCTCC.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.guaduanInputCTCC.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     ng-show="chargeTypeGuaDuanCTCC==1 && postGuaDuanCTCC"
                                     ng-if="isExperience == '0' && isGdDescCTCC"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(GDPriceCTCC,guaduanInputCTCC)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2" style="padding-top:2px"
                                     ng-if="!isGdDescCTCC" ng-bind="gdDescCTCC"></div>
                            </div>
                        </div>

                        <!--add by hyj end-->
                    </div>
                </div>
                <div class="form-group" ng-show="servType !='3' && servType !='4'">
                    <div class="row">
                        <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                           ng-bind="'CREATEORDER_DESC1'|translate"></p>
                    </div>
                </div>
                <!--移动-->
                <div class="form-group" ng-show="servType =='4'">
                    <div class="row">
                        <li class="col-xs-2 check-li" ng-click="chooseCMCC()">
                            <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cmcc]"></span>
                            <span>移动</span>
                        </li>
                    </div>
                </div>
                <!--增彩产品-->
                <!--ng-if="isExperience == '0' && chargeTypeGuaDuan==1"-->
                <div style="margin-left: 65px" ng-show=" servType =='4' && cmcc===true">
                	<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showPingXianCMCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postPingXianCMCC]"></span>
                                <span>屏显:</span>
                            </li>
                        </div>
                    </div>
                	<div class="form-group"  ng-show="postPingXianCMCC">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendScreenType(1)">
									<span class="check-btn redio-btn" id="screenType4"
                                          ng-class="{true:'checked',false:''}[screenchargeTypeEx===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendScreenType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="screenType3"
                                          ng-class="{true:'checked',false:''}[screenchargeTypeEx===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>

                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="screenchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupScreencmccPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="screenchargeTypeEx ==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupScreenCMCCanci"
                                       ng-model="groupSendScreenCMCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupScreenCMCCanci.$dirty && orderItemDomain.groupScreenCMCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupScreenCMCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupScreenCMCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="screenchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupScreencmccPrice,groupSendScreenCMCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                    </div>
                    
                </div>
                	<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showGuaCai()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaCai]"></span>
                                <span>彩信:</span>
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="postGuaCai">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeCaixinType(1)">
									<span class="check-btn redio-btn" id="zengCaiType4"
                                          ng-class="{true:'checked',false:''}[cxchargeTypeEx===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeCaixinType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="zengCaiType3"
                                          ng-class="{true:'checked',false:''}[cxchargeTypeEx===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>

                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="cxchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="cxPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="cxchargeTypeEx ==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="cxanci"
                                       ng-model="cxanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.cxanci.$dirty && orderItemDomain.cxanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.cxanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.cxanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="cxchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(cxPrice,cxanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                	<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showZengCaiCMCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postZengCaiCMCC]"></span>
                                <span>增彩:</span>
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="postZengCaiCMCC">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeZengCaiType(1)">
									<span class="check-btn redio-btn" id="zengCaiType4"
                                          ng-class="{true:'checked',false:''}[zcchargeTypeEx===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeZengCaiType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="zengCaiType3"
                                          ng-class="{true:'checked',false:''}[zcchargeTypeEx===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>

                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="zcchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="ZCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="zcchargeTypeEx ==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="zcanci"
                                       ng-model="zcanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.zcanci.$dirty && orderItemDomain.zcanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.zcanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.zcanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="zcchargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(ZCPrice,zcanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
					<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showGuaDuan()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaDuan]"></span>
                                <span>短信:</span>
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="postGuaDuan">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendSMSType(1)">
									<span class="check-btn redio-btn" id="smsType4"
                                          ng-class="{true:'checked',false:''}[smschargeTypeEx===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <!--按次-->
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendSMSType(2)" style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="smsType3"
                                          ng-class="{true:'checked',false:''}[smschargeTypeEx===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>

                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="smschargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupSmscmccPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="smschargeTypeEx ==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSMSCMCCanci"
                                       ng-model="groupSendSMSCMCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSMSCMCCanci.$dirty && orderItemDomain.groupSMSCMCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSMSCMCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSMSCMCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="smschargeTypeEx ==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupSmscmccPrice,groupSendSMSCMCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
				</div>
                
                <!--联通-->
                <div class="form-group" ng-show=" servType =='4'">
                    <div class="row">
                        <li class="col-xs-2 check-li" ng-click="chooseCUCC()">
                            <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cucc]"></span>
                            <span>联通</span>
                        </li>
                    </div>
                </div>
                <!--增彩产品-->
                <!--ng-if="isExperience == '0' && chargeTypeGuaDuan==1"-->
                <div style="margin-left: 65px" ng-show=" servType =='4' && cucc===true">
                	<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showPingXianCUCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postPingXianCUCC]"></span>
                                <span>屏显:</span>
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="postPingXianCUCC">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendScreenTypeCUCC(1)">
									<span class="check-btn redio-btn" id="screenType4"
                                          ng-class="{true:'checked',false:''}[screenchargeTypeExCUCC===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                               ng-click="changeGroupSendScreenTypeCUCC(2)"  style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[screenchargeTypeExCUCC===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="screenchargeTypeExCUCC==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupScreencuccPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="screenchargeTypeExCUCC==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendScreenCUCCanci"
                                       ng-model="groupSendScreenCUCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendScreenCUCCanci.$dirty && orderItemDomain.groupSendScreenCUCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendScreenCUCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendScreenCUCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="screenchargeTypeExCUCC==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupScreencuccPrice,groupSendScreenCUCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                	<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showGuaDuanCUCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaDuanCUCC]"></span>
                                <span>短信:</span>
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="postGuaDuanCUCC">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendSMSTypeCUCC(1)">
									<span class="check-btn redio-btn" id="smsType4"
                                          ng-class="{true:'checked',false:''}[smschargeTypeExCUCC===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                               ng-click="changeGroupSendSMSTypeCUCC(2)"  style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[smschargeTypeExCUCC===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="smschargeTypeExCUCC==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupSmscuccPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="smschargeTypeExCUCC==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendSMSCUCCanci"
                                       ng-model="groupSendSMSCUCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendSMSCUCCanci.$dirty && orderItemDomain.groupSendSMSCUCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="smschargeTypeExCUCC==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupSmscuccPrice,groupSendSMSCUCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                </div>
				<!--电信-->
                <div class="form-group" ng-show=" servType =='4'">
                    <div class="row">
                        <li class="col-xs-2 check-li" ng-click="chooseCTCC()">
                            <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[ctcc]"></span>
                            <span>电信</span>
                        </li>
                    </div>
                </div>
                <!--增彩产品-->
                <!--ng-if="isExperience == '0' && chargeTypeGuaDuan==1"-->
                <div style="margin-left: 65px" ng-show=" servType =='4' && ctcc===true">
                	<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showPingXianCTCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postPingXianCTCC]"></span>
                                <span>屏显:</span>
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="postPingXianCTCC">
                    	<div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendScreenTypeCTCC(1)">
									<span class="check-btn redio-btn" id="screenType4"
                                          ng-class="{true:'checked',false:''}[screenchargeTypeExCTCC===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendScreenTypeCTCC(2)" style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[screenchargeTypeExCTCC===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="screenchargeTypeExCTCC==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupScreenctccPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="screenchargeTypeExCTCC==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendScreenCTCCanci"
                                       ng-model="groupSendScreenCTCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendScreenCTCCanci.$dirty && orderItemDomain.groupSendScreenCTCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendScreenCTCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendScreenCTCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="screenchargeTypeExCTCC==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupScreenctccPrice,groupSendScreenCTCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                	<div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="showGuaDuanCTCC()">
                                <span class="check-btn checked-btn"
                                      ng-class="{true:'checked',false:''}[postGuaDuanCTCC]"></span>
                                <span>短信:</span>
                            </li>
                        </div>
                    </div>
                    <div class="form-group" ng-show="postGuaDuanCTCC">
                        <div class="row">
                            <!--不限-->
                            <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendSMSTypeCTCC(1)">
									<span class="check-btn redio-btn" id="smsType4"
                                          ng-class="{true:'checked',false:''}[smschargeTypeExCTCC===0]"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                <span ng-bind="desc" style="margin-left:30px"></span>
                            </li>
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                ng-click="changeGroupSendSMSTypeCTCC(2)" style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[smschargeTypeExCTCC===1]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="smschargeTypeExCTCC==1"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupSmsctccPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="smschargeTypeExCTCC==1">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendSMSCTCCanci"
                                       ng-model="groupSendSMSCTCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendSMSCTCCanci.$dirty && orderItemDomain.groupSendSMSCTCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="smschargeTypeExCTCC==1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupSmsctccPrice,groupSendSMSCTCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!--时间选择-->
                <div class="form-group">
                    <div class="row">
                        <label class="col-xs-2 control-label"
                               ng-bind="'CREATEORDER_TIME'|translate"></label>

                        <div class="col-lg-4 col-xs-6 col-sm-6 col-md-6 input-daterange input-group" id="datepicker"
                             style="padding-left:15px;padding-right:15px;">
                            <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off"
                                   id="start"/>
                            <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                            <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off"
                                   id="end"/>
                        </div>
                    </div>
                    <div class="row">
                        <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                           ng-bind="'CREATEORDER_DESC2'|translate"></p>
                    </div>
                </div>

            </form>
        </div>

        <div class="form-group">
            <div class="order-btn row">
                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                     style="padding-left: 30px;">
                    <button type="submit" class="btn btn-primary search-btn"
                            ng-disabled="orderBase.amount.$invalid || effictiveTime==='' ||expireTime ===''
										|| !transferAttachURL.upload || transferAttachURL.errorInfo
										||(servType != '4' && ((cmcc && postPingXianCMCC && chargeType==0 && desc)
										||(cmcc && postPingXianCMCC && chargeType==1 && (orderItemDomain.anci.$invalid || acDesc))
										||(cmcc && postPingXianCMCC && chargeType==2 && (orderItemDomain.peirenyue.$invalid || byDesc))
										||(cmcc && postGuaDuan && chargeTypeGuaDuan==0 && gdesc)
										||(cmcc && postGuaDuan && chargeTypeGuaDuan==1 && (orderItemDomain.guaduanInput.$invalid || gdDesc))
										||(cmcc && postGuaDuan && chargeTypeGuaDuan==2 && (orderItemDomain.guaDuanPrice.$invalid || gdByDesc))
										||(cmcc && postGuaCai && chargeTypeGuaCai==0 && gcesc)
										||(cmcc && postGuaCai && chargeTypeGuaCai==1 && (orderItemDomain.guacaiInput.$invalid || gcDesc))
										||(cmcc && postGuaCai && chargeTypeGuaCai==2 && (orderItemDomain.guaCaiPrice.$invalid || gcByDesc))
										||(cucc && postPingXianCUCC && chargeType_cucc==0  && desc_cucc)
										||(cucc && postPingXianCUCC && chargeType_cucc==1  && (orderItemDomain.anci_cucc.$invalid || acDesc_cucc))
										||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && desc_ctcc)
										||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && (orderItemDomain.anci_ctcc.$invalid || acDesc_ctcc))
										||!(cmcc || cucc || ctcc)
										||(!(cmcc && (postPingXianCMCC || postGuaCai || postGuaDuan || ((servType =='4' || servType =='2') && postZengCaiCMCC))) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)) && servType !='3')
										||(servType =='3' && !(cmcc && postPingXianCMCC) && isExperience == 1)
										||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))
										||(servType == '4' && cmcc && ((isExperience == 0 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid) || (isExperience == 1 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid)))
										||(servType == '4' && cmcc && smschargeTypeEx ==1 && ((isExperience == 0 && orderItemDomain.groupSMSCMCCanci.$invalid) ||(isExperience == 1 && orderItemDomain.groupSMSCMCCanciEx.$invalid)))
										||(servType == '4' && cucc && smschargeTypeExCUCC ==1 && ((isExperience == 0 && orderItemDomain.groupSMSCUCCanci.$invalid) ||(isExperience == 1 && orderItemDomain.groupSMSCUCCanciEx.$invalid)))
										||(servType == '4' && ctcc && smschargeTypeExCTCC ==1 && ((isExperience == 0 && orderItemDomain.groupSMSCTCCanci.$invalid) ||(isExperience == 1 && orderItemDomain.groupSMSCTCCanciEx.$invalid)))
										||(servType == '4' && cmcc && screenchargeTypeEx ==1 && ((isExperience == 0 && orderItemDomain.groupScreenCMCCanci.$invalid) ||(isExperience == 1 && orderItemDomain.groupScreenCMCCanciEx.$invalid)))
										||(servType == '4' && cucc && screenchargeTypeExCUCC ==1 && ((isExperience == 0 && orderItemDomain.groupScreenCUCCanci.$invalid) ||(isExperience == 1 && orderItemDomain.groupScreenCUCCanciEx.$invalid)))
										||(servType == '4' && ctcc && screenchargeTypeExCTCC ==1 && ((isExperience == 0 && orderItemDomain.groupScreenCTCCanci.$invalid) ||(isExperience == 1 && orderItemDomain.groupScreenCTCCanciEx.$invalid)))
										||(servType == '4' && !(cmcc || cucc || ctcc))
										||(servType == '2' && cmcc && (gjzcchargeTypeEx == 1 && orderItemDomain.gjzcanci.$invalid))
										||(servType == '4' && cmcc && (gjzcchargeTypeEx == 1 && orderItemDomain.cxanci.$invalid))
										"
                            ng-click="createOrder()" ng-bind="'COMMON_SAVE'|translate" id="formSub">
                    </button>
                    <button type="submit" class="btn btn-back" ng-click="goBack()"
                            ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                    </p></div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>

</div>

</body>
</html>
