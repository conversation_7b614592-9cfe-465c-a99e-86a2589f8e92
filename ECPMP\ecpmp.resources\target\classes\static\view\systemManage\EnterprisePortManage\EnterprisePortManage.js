var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common"])
app.controller('hotlineContentController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        //获取enterpriseName
        $scope.enterpriseName = $.cookie('enterpriseName');
        //判断最终调接口的enterpriseID,enterpriseName
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            $scope.enterpriseID = $scope.subEnterpriseID;
            $scope.enterpriseName = $scope.subEnterpriseName;
        }
        $scope.operatorID = $.cookie('accountID');
        $scope.isProvinceType = false;
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        //初始化搜索条件
        $scope.initSel = {
            enterpriseName:"",
            enterpriseType:"",
            enterpriseID:"",
        };
        $scope.servTypeArr = []
        $scope.enterpriseTypeChoise = [
            {
                id: "",
                name: "请选择"
            },
            {
                id: 2,
                name: "代理商"
            },
            {
                id: 5,
                name: "省份"
            },
            {
                id: 15,
                name: "集客"
            },
            {
                id:3,
                name:"子企业"
            }
        ];
        $scope.enterpriseTypeChoiseForInsert = [
            {
                id: 2,
                name: "代理商"
            },
            {
                id: 5,
                name: "省份"
            },
            {
                id: 15,
                name: "集客"
            },
            {
                id: 25,
                name: "移动云PAAS"
            },
            {
                id: 35,
                name: "咪咕音乐"
            },
            {
                id:3,
                name:"子企业"
            }
        ];
        $scope.queryList();

    };

    // 新增热线内容弹窗
    $scope.addObjectF = function () {
        $("[name='enterpriseNameInput']").css("z-index",1);
        $("[name='enterpriseNameSelect']").css("z-index",-1);
        $("#other").html("");
        $scope.operate = 'add';
        $scope.addObject = {};
        $scope.queryEnterpriseList = [];
        $scope.addObject.enterpriseType = 2;
        $('#addObject').modal();
        $scope.servTypeArr=[0,0,0];
        $('.servType .check-li').eq(0).find('span').removeClass('checked');
        $('.servType .check-li').eq(1).find('span').removeClass('checked');
        $('.servType .check-li').eq(2).find('span').removeClass('checked');
    };
    $scope.enterpriseNameInputBlur = function(){
        $("[name='enterpriseNameInput']").css("z-index",-1);
        $("[name='enterpriseNameSelect']").css("z-index",1);
        if($scope.queryEnterpriseList&&$scope.queryEnterpriseList.length>0){
            $("#other").html("请选择");

        }else{
            $("#other").html($scope.addObject.name);

        }
    }
    $scope.enterpriseNameClick = function(){
        $("[name='enterpriseNameInput']").css("z-index",1);
        $("[name='enterpriseNameSelect']").css("z-index",-1);
        $("[name='enterpriseNameSelect']").attr("size",1);


    };
    $scope.changeServType = function (val) {
        if ($scope.servTypeArr[val] == 1)
        {
            $('.servType .check-li').eq(val).find('span').removeClass('checked');
            $scope.servTypeArr[val] = 0;
        }
        else
        {
            $('.servType .check-li').eq(val).find('span').addClass('checked');
            $scope.servTypeArr[val] = 1;
        }
    };
    $scope.enterpriseNameSelectChange = function(){
        setTimeout(function (){
            $("[name='enterpriseNameInput']").css("z-index",-1);
            $("[name='enterpriseNameSelect']").css("z-index",1);
            console.log("1111");
        },100)
        for(let i = 0;i<$scope.queryEnterpriseList.length;i++){
            if($scope.addObject.id === $scope.queryEnterpriseList[i].id){
                $scope.addObject.name = $scope.queryEnterpriseList[i].enterpriseName;
                $scope.addObject.code = $scope.queryEnterpriseList[i].enterpriseCode;
                $scope.addObject.parentEnterpriseID =  $scope.queryEnterpriseList[i].parentEnterpriseID || "";
                $scope.addObject.parentEnterpriseName =  $scope.queryEnterpriseList[i].parentEnterpriseName || "";
                return;
            }
        }
        $scope.addObject.code = null;
        $scope.addObject.id = null;

    }
    $scope.generateReq = function () {
        var req = {
            "enterpriseName": $scope.initSel.enterpriseName || null,
            "enterpriseType": $scope.initSel.enterpriseType || null,
            "enterpriseId": $scope.initSel.enterpriseID || null, // 企业id
            "pageParameter": {
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[0].pageSize),
                "isReturnTotal": "1",
            }
        };
        return req;
    };
    //查询列表
    $scope.queryList = function (condition) {
        let req;
        if (condition !== 'justPage') {
            req = $scope.generateReq();
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryReqTemp = angular.copy(req);
        } else {
            //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            req = $scope.queryReqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/interfaceControlQuery",
            data: JSON.stringify(req),
            success: function (result) {
                debugger
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.contentInfoListData = result.interfaceControlQueryWrapperList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.total) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.total / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.contentInfoListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.contentInfoListData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.lastName = null
    $scope.enterpriseList = function () {
        // if($scope.addObject.enterpriseType == 2){
        //     $('.servType .check-li').eq(2).addClass('hide');
        // }else {
        //     $('.servType .check-li').eq(2).removeClass('hide');
        // }

        if( !$scope.addObject.name){
            return;
        }
        if($scope.lastName === $scope.addObject.name){
            $("[name='enterpriseNameInput']").css("z-index",-1);
            $("[name='enterpriseNameSelect']").css("z-index",1);
        }else{
            $scope.lastName = $scope.addObject.name
        }
        let reqEnterpriseType = $scope.addObject.enterpriseType
        let isZyzq = 0;
        if($scope.addObject.enterpriseType === 15){
            reqEnterpriseType = 5;
            isZyzq = 1;
        }

        if($scope.addObject.enterpriseType === 25){
            reqEnterpriseType = 5;
            isZyzq = 2;
        }
        if($scope.addObject.enterpriseType === 35){
            reqEnterpriseType = 5;
            isZyzq = 3;
        }
        var req = {
            "enterpriseType":reqEnterpriseType,
            "enterpriseName": $scope.addObject.name?$scope.addObject.name:null,
            "isZyzq":isZyzq,
            "pageParameter": {
                "pageNum": 0,
                "pageSize":10000
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseListFromDsum",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.queryEnterpriseList=result.enterpriseList;
                        if($scope.queryEnterpriseList&&$scope.queryEnterpriseList.length>0){
                            $("[name='enterpriseNameInput']").css("z-index",-1);
                            $("[name='enterpriseNameSelect']").css("z-index",1);
                            $("#other").html("请选择");
                            $("[name='enterpriseNameSelect']").attr("size",$scope.queryEnterpriseList.length<20?$scope.queryEnterpriseList.length+1:20);
                        }else{
                            $("#other").html("暂无数据");
                            $("[name='enterpriseNameSelect']").attr("size",5);
                        }
                    }else{

                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }


    $scope.beforeCommit = function (){

        var orgServType = [2, 1, 5, 4, 3];
        var servTypes = [];
        for (var i=0; i< $scope.servTypeArr.length; i++)
        {
            if ($scope.servTypeArr[i] == 1)
            {
                //分省名片保存时也保存类型为5的数据
                if(($scope.addObject.enterpriseType == 5 || $scope.addObject.enterpriseType == 15) && orgServType[i] == 1){
                    servTypes.push(orgServType[2]);
                }

                servTypes.push(orgServType[i]);
            }
        }

        if(servTypes.length === 0){
            $scope.tip="请勾选业务类型";
            $('#myModal').modal();
            return;
        }
        if(!$scope.addObject.id||!$scope.addObject.name){
            return;
        }
        let req = {
            "dayLimitEnterprise": {
                "id":$scope.addObject.objectId,
                "servType":servTypes.join(","),
                "enterpriseID":$scope.addObject.id,
                "enterpriseCode":$scope.addObject.code,
                "enterpriseName":$scope.addObject.name,
                "enterpriseType":$scope.addObject.enterpriseType,
                "parentEnterpriseID": $scope.addObject.parentEnterpriseID,
                "parentEnterpriseName": $scope.addObject.parentEnterpriseName
            }
        }

        let url = "/ecpmp/ecpmpServices/enterpriseManageService/dayLimitServices/insert";
        if($scope.operate === 'edit'){
            url = "/ecpmp/ecpmpServices/enterpriseManageService/dayLimitServices/update";
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: url,
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.tip="成功";
                        $('#myModal').modal();
                        $('#addObject').modal("hide");
                        $scope.queryList();
                    }else if(result.result.resultCode == '1030120093'){
                        $scope.tip="已存在记录";
                        $('#myModal').modal();
                    } else{

                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#myModal').modal();
                    }
                )
            }
        })

    }
    // 删除热线内容弹窗
    $scope.deleteObject = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteHotlineContent').modal();
    };

    $scope.enterpriseNameInputChange = function () {
        $scope.addObject.code = null;
        $scope.addObject.id=null;
        $scope.queryEnterpriseList=null;
    }
    // 关闭二次确认弹窗
    $scope.closeAccount = function (item,val,status){
        $scope.selectedItemLogout = item;
        $scope.interfaceType = val;
        $scope.status = status;
        $("#deleteAccount").modal();
    }
    $scope.delAccount =function (){
        $scope.changeServTypeFunction($scope.selectedItemLogout,$scope.interfaceType,$scope.status)
    }
    // 开启/关闭 能力接口
    $scope.changeServTypeFunction = function (item,val,status) {
        let req = {
            "enterpriseId": (item.subEnterpriseID && item.subEnterpriseID != '-') ? item.subEnterpriseID : item.enterpriseID,
            "interfaceType":val,
            "type": 1,
            "status":status,
        }
        if($scope.initSel.enterpriseType == 5) { // 如果是省份，增加provinceId参数
            req['provinceId'] = item.provinceId;
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/interfaceControlManage",
            data: JSON.stringify(req),
            success: function (result) {
                debugger
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.tip="成功";
                        $('#myModal').modal();
                        $('#delAccountCancel').click();

                        $scope.queryList();
                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#delAccountCancel').click();
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#delAccountCancel').click();
                        $('#myModal').modal();
                    }
                )
            }
        })
    };

    // 编辑热线内容弹窗
    $scope.updateObject = function (item) {
        $scope.selectedItemDel = item;


        $("#other").html("");


        $scope.operate = 'edit';
        $scope.queryEnterpriseList = [];
        $scope.addObject = {};
        $scope.addObject.name = item.enterpriseName;
        $scope.addObject.id = item.enterpriseID;
        $scope.addObject.code = item.enterpriseCode;
        $scope.addObject.enterpriseType = item.enterpriseType;
        $scope.addObject.objectId = item.id;
        // if($scope.addObject.enterpriseType == 2 ){
        //     $('.servType .check-li').eq(2).addClass('hide');
        // }else {
        //     $('.servType .check-li').eq(2).removeClass('hide');
        // }
        $scope.servTypeArr = [0,0,0];
        $('.servType .check-li').eq(0).find('span').removeClass('checked');
        $('.servType .check-li').eq(1).find('span').removeClass('checked');
        $('.servType .check-li').eq(2).find('span').removeClass('checked');
        let servTypes = item.servType.split(',');
        for(let i = 0; i< servTypes.length; i ++ ){
            if(servTypes[i] === "1"){
                $scope.changeServType('1');
            }
            if(servTypes[i] === "2"){
                $scope.changeServType('0');
            }
            // if(servTypes[i] === "5"){
            //     $scope.changeServType('2');
            // }
        }

        $('#addObject').modal();
        $("[name='enterpriseNameInput']").css("z-index",1);
        $("[name='enterpriseNameSelect']").css("z-index",-1);
    };

    $scope.getEnterpriseType = function (type) {
        if (type == 2) {
            return "代理商";
        }
        else if (type == 5) {
            return "省份";
        }
        else if (type == 15) {
            return "集客";
        }
       else if (type == 3) {
            return "子企业";
        }
    };
    //删除热线内容
    $scope.delHotlineContent = function () {
        var item = $scope.selectedItemDel;
        let req = {
            "dayLimitEnterprise": {
                "id":item.id,
                "enterpriseID":item.enterpriseID
            },
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/dayLimitServices/delete",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.tip="成功";
                        $('#myModal').modal();
                        $('#deleteHotlineContent').modal("hide");
                        $scope.queryList();
                    }else{

                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
    };

    $scope.$watch('initSel.enterpriseType', function(newVal) {
        if (newVal == 5) { // 5代表“省份”
            // 清空其他搜索条件
            $scope.initSel.enterpriseID = '';
            $scope.initSel.enterpriseName = '';
            // 设置禁用标志
            $scope.isProvinceType = true;
        } else {
            // 取消禁用
            $scope.isProvinceType = false;
        }
    });

}]);
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}]);
app.filter("formatDate", function () {
    return function (date) {
        date = new Date(date);
        if (date) {
            return date.getFullYear() + "-" + (date.getMonth()+1) + "-" + date.getDate() + " " + date.getHours() + ":" + date.getMinutes();
        }
        return "";
    }
})
