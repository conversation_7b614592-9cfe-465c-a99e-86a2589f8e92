<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper3.ProductOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductOrder">
        <id column="ID" property="id"/>
        <result column="enterpriseID" property="enterpriseID"/>
        <result column="productCode" property="productCode"/>
        <result column="status" property="status"/>
        <result column="channel" property="channel"/>
        <result column="insertTime" property="insertTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="effictiveTime" property="effictiveTime"/>
        <result column="expireTime" property="expireTime"/>
        <result column="reserved1" property="reserved1"/>
        <result column="reserved2" property="reserved2"/>
        <result column="reserved3" property="reserved3"/>
        <result column="reserved4" property="reserved4"/>
        <result column="reserved5" property="reserved5"/>
        <result column="reserved6" property="reserved6"/>
        <result column="reserved7" property="reserved7"/>
        <result column="reserved8" property="reserved8"/>
        <result column="reserved9" property="reserved9"/>
        <result column="reserved10" property="reserved10"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, enterpriseID, productCode, status, channel, insertTime, updateTime, effictiveTime, expireTime, reserved1,
        reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, reserved10
    </sql>

    <select id="queryEbossEsContent" parameterType="java.util.Map"
            resultType="com.huawei.jaguar.dsdp.bill.dao.domain3.EbossEsContent">
        select
        t1.enterpriseID,
        t1.productCode,
        t2.packageID as packageCode,
        t3.servType,
        t3.subServType
        from ecpe_t_product_order t1 left join  ecpe_t_product t3  on t1.productCode=t3.ID
        right join ecpe_t_product_package_rule t2 on t1.productCode=t2.productID
        where
        1=1
        <if test="enterpriseID !=null">
            and t1.enterpriseID=#{enterpriseID}
        </if>

        <if test="servType !=null">
            and t3.servType=#{servType}
        </if>

        <if test="subServType !=null">
            and t3.subServType=#{subServType}
        </if>
    </select>


    <insert id="batchInsertProductOrders">
        insert into ecpe_t_product_order
        (
        enterpriseID,
        productCode,
        status,
        channel,
        insertTime,
        updateTime,
        effictiveTime,
        expireTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10
        )
        values
        <foreach collection="list" item="productOrder"
                 separator=",">
            (
            #{productOrder.enterpriseID},
            #{productOrder.productCode},
            #{productOrder.status},
            #{productOrder.channel},
            #{productOrder.insertTime},
            #{productOrder.updateTime},
            #{productOrder.effictiveTime},
            #{productOrder.expireTime},
            #{productOrder.reserved1},
            #{productOrder.reserved2},
            #{productOrder.reserved3},
            #{productOrder.reserved4},
            #{productOrder.reserved5},
            #{productOrder.reserved6},
            #{productOrder.reserved7},
            #{productOrder.reserved8},
            #{productOrder.reserved9},
            #{productOrder.reserved10}
            )
        </foreach>
    </insert>

    <select id="queryProductByEnterprise" parameterType="java.lang.String"
            resultType="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductInfoWrapper">

        SELECT
        t2.ID,
        t2.productCode,
        t2.productName,
        t2.productDesc,
        t2.productType,
        t2.unitPrice,
        t2.currency,
        t2.productStatus,
        t2.isExperience,
        t2.servType,
        t2.subServType,
        t2.isLimit,
        t2.chargeType,
        t2.amount,
        t2.memberCount,
        t2.maxAmountPerPerson,
        t1.effictiveTime,
        t1.expireTime,
        t2.displayNo,
        t2.isUse,
        t2.createTime,
        t2.operatorID,
        t2.lastUpdateTime,
        t2.reserved1,
        t2.reserved2,
        t2.reserved3,
        t2.reserved4,
        t2.reserved5,
        t2.reserved6,
        t2.reserved7,
        t2.reserved8,
        t2.reserved9,
        t2.reserved10
        FROM
        ecpe_t_product_order t1
        LEFT JOIN ecpe_t_product t2 ON t1.productCode = t2.ID
        where t1.enterpriseID = #{enterpriseID} and t1.status = 0

    </select>

    <select id="queryProductByMap" parameterType="java.util.Map"
            resultType="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductInfoWrapper">

        SELECT
        t2.ID as productID,
        t2.productCode,
        t2.productName,
        t2.productDesc,
        t2.productType,
        t2.unitPrice,
        t2.currency,
        t2.productStatus,
        t2.isExperience,
        t2.servType,
        t2.subServType,
        t2.isLimit,
        t2.chargeType,
        t2.amount,
        t2.memberCount,
        t2.maxAmountPerPerson,
        t2.effictiveTime,
        t2.expireTime,
        t2.displayNo,
        t2.isUse,
        t2.createTime,
        t2.operatorID,
        t2.lastUpdateTime,
        t2.reserved1,
        t2.reserved2,
        t2.reserved3,
        t2.reserved4,
        t2.reserved5,
        t2.reserved6,
        t2.reserved7,
        t2.reserved8,
        t2.reserved9,
        t2.reserved10
        FROM
        ecpe_t_product_order t1
        LEFT JOIN ecpe_t_product t2 ON t1.productCode = t2.ID
        where t1.enterpriseID = #{enterpriseID} and t1.status = 0 and t1.expireTime <![CDATA[ > ]]> now()
        <if test="productCode!=null and productCode!=''">and t1.productCode = #{productCode}</if>
    </select>

    <select id="queryProductByPackageIDAndEnterpriseID" parameterType="java.util.Map"
            resultType="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductInfoWrapper">

        SELECT
        t3.*,t3.id productID
        FROM
        ecpe_t_product t3
        WHERE
        t3.ID IN (
        SELECT
        t1.productID
        FROM
        ecpe_t_product_package_rule t1
        LEFT JOIN ecpe_t_product_order t2 ON t1.productID = t2.productCode
        WHERE t2.status = 0  and t2.expireTime <![CDATA[ > ]]> now()
        <if test="packageID!=null and packageID!=''">and t1.packageID = #{packageID}</if>
        <if test="enterpriseID!=null and enterpriseID!=''">and t2.enterpriseID = #{enterpriseID}</if>
        )

    </select>

    <delete id="deleteOrder" parameterType="java.util.Map">
        DELETE
        FROM
        ecpe_t_product_order
        WHERE
        enterpriseID = #{enterpriseID}
        AND (
            productCode IN ( SELECT t1.productID FROM ecpe_t_product_package_rule t1 WHERE t1.packageID = #{packageCode})
           or productCode = #{packageCode}
            )
    </delete>
    <update id="updateOrderByPackage" parameterType="java.util.Map">
         UPDATE ecpe_t_product_order
        SET
        updateTime = now()
        <if test="status!=null and status!=''">,status = #{status}</if>
        <if test="effictiveTime!=null">,effictiveTime = #{effictiveTime}</if>
        <if test="expireTime!=null">,expireTime = #{expireTime}</if>
        WHERE
        enterpriseID = #{enterpriseID}
        AND productCode IN ( SELECT t1.productID FROM ecpe_t_product_package_rule t1 WHERE t1.packageID = #{packageCode})
    </update>

    <update id="updateOrderByProduct" parameterType="java.util.Map">
             UPDATE ecpe_t_product_order
        SET
        updateTime = now()
        <if test="status!=null and status!=''">,status = #{status}</if>
        <if test="effictiveTime!=null">,effictiveTime = #{effictiveTime}</if>
        <if test="expireTime!=null">,expireTime = #{expireTime}</if>
        WHERE
        enterpriseID = #{enterpriseID}
        and productCode = #{productCode}
    </update>
    <update id="insertOrUpdateProductOrders" parameterType="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductOrder">
        <selectKey keyProperty="count" resultType="int" order="BEFORE">
            select count(*) as count from ecpe_t_product_order where enterpriseID = #{enterpriseID} and productCode=#{productCode}
        </selectKey>
        <if test="count > 0">
            update ecpe_t_product_order
            set status=0
            <if test="effictiveTime!=null">,effictiveTime = #{effictiveTime}</if>
            <if test="expireTime!=null">,expireTime = #{expireTime}</if>
            where enterpriseID = #{enterpriseID} and productCode=#{productCode}
        </if>
        <if test="count==0">
            insert into ecpe_t_product_order
            (
            ID,
            enterpriseID,
            productCode,
            status,
            channel,
            insertTime,
            updateTime,
            effictiveTime,
            expireTime,
            reserved1,
            reserved2,
            reserved3,
            reserved4,
            reserved5,
            reserved6,
            reserved7,
            reserved8,
            reserved9,
            reserved10
            )
            values

            (
            next value for MYCATSEQ_ECPE_T_PRODUCT_ORDER,
            #{enterpriseID},
            #{productCode},
            #{status},
            #{channel},
            #{insertTime},
            #{updateTime},
            #{effictiveTime},
            #{expireTime},
            #{reserved1},
            #{reserved2},
            #{reserved3},
            #{reserved4},
            #{reserved5},
            #{reserved6},
            #{reserved7},
            #{reserved8},
            #{reserved9},
            #{reserved10}
            )
        </if>
    </update>


    <select id="queryProductSubscribe" parameterType="java.util.Map"
            resultType="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductInfoWrapper">

        SELECT
        t2.ID as productID,
        t2.productCode,
        t2.productName,
        t2.productDesc,
        t2.productType,
        t2.unitPrice,
        t2.currency,
        t2.productStatus,
        t2.isExperience,
        t2.servType,
        t2.subServType,
        t2.isLimit,
        t2.chargeType,
        t2.amount,
        t2.memberCount,
        t2.maxAmountPerPerson,
        t2.effictiveTime,
        t2.expireTime,
        t2.displayNo,
        t2.isUse,
        t2.createTime,
        t2.operatorID,
        t2.lastUpdateTime,
        t2.reserved1,
        t2.reserved2,
        t2.reserved3,
        t2.reserved4,
        t2.reserved5,
        t2.reserved6,
        t2.reserved7,
        t2.reserved8,
        t2.reserved9,
        t2.reserved10
        FROM
        ecpe_t_product_order t1
        LEFT JOIN ecpe_t_product t2 ON t1.productCode = t2.ID
        where t1.status = 0 and t1.expireTime <![CDATA[ > ]]> now()
        <if test="enterpriseId!=null and enterpriseId!=''">and t1.enterpriseId = #{enterpriseId}</if>
        <if test="servType!=null and servType!=''">and t2.servType = #{servType}</if>
        <if test="subServType!=null and subServType!=''">and t2.subServType = #{subServType}</if>
        <if test="chargeType!=null and chargeType!=''">and t2.chargeType = #{chargeType}</if>
        <if test="isExperience!=null and isExperience!=''">and t2.isExperience = #{isExperience}</if>
        <if test="isLimit!=null and isLimit!=''">and t2.isLimit = #{isLimit}</if>
    </select>
    <select id="queryProductByQuotaPkgIDAndProductType"
            resultType="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductWrapper">
        select t3.ID, t3.servType, t3.subServType from ecpe_t_product t3 WHERE t3.ID IN (SELECT t1.productID FROM ecpe_t_product_package_rule t1 WHERE t1.packageID = (select ID from ecpe_t_productpackage where packageCode = #{packageCode}))
    </select>

    <select id="queryAllProduct" resultType="com.huawei.jaguar.dsdp.bill.dao.domain3.ProductPackageWrapper">
        SELECT ID, packageCode, packageName, price FROM ecpe_t_productpackage
    </select>
</mapper>

