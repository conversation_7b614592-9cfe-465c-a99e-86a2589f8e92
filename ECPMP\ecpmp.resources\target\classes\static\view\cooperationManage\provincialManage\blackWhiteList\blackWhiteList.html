<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>黑白名单</title>
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
	<link href="../../../../css/reset.css" rel="stylesheet" />
	<link href="../../../../css/searchList.css" rel="stylesheet" />

	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css" />
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="blackWhiteListCtrl.js"></script>
	<!-- 引入菜单组件 -->
	<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
	<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
	<!-- 导入文件组件 -->
	<script type="text/javascript" src="../../../../frameworkJs/webuploader.js"></script>
	<script src="../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<link href="../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet" />
	<link rel="stylesheet" type="text/css" href="../../../../css/font-awesome.min.css">
	<style>
		.body-min-width{
			min-width: 1100px;
		}
	    /* media for adjustable search-table width  */
	    @media (max-width: 1366px){
  	    .control-label{
	    	width: 86px;
	    	padding-left: 0;
	    	padding-right: 0;
	    	}
	    }
	    #filePicker div:nth-child(2) {
	    	width: 100% !important;
	    	height: 100% !important;
		}
		.cooperation-manage .form-group{
			margin-right: 0;
		}
		.error {
  			border-color: red;
		}
		.ques{
			display: block;
			width: 16px;
			height: 16px;
			background:url(../../../../assets/images/ques.png) no-repeat;
			position: absolute;
			top: -8px;
			right: -4px;
			background-size: 100%;
		}
		.tipB{
			position: absolute;
			top: -22px;
			left: 120px;
			background-color: #e5e2fb;
			color: #7462e2;
			padding: 0 5px;
			border-radius: 4px;
		}
	</style>
</head>

<body ng-app="myApp" class="body-min-width">
	<div class="cooperation-manage container-fluid" ng-controller="blackWhiteListCtrl" ng-init="init();" ng-cloak>
		<div class="cooperation-head">
			<div ng-show="isSuperManager && enterpriseType=='5'" style="display:inline">
				<span class="frist-tab" ng-bind="'COMMON_PROVINCEENTERPRISEMANAGE'|translate"></span> >
			</div>
			<div ng-show="isSuperManager && enterpriseType=='1'" style="display:inline">
				<span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span> >
			</div>
			<div ng-show="isSuperManager && enterpriseType=='3'" style="display:inline">
				<span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span> >
			</div>
			<div ng-show="isSuperManager && enterpriseType=='2'" style="display:inline">
				<span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span> >
			</div>

			<div ng-show="isAgent || isProvincial || isZhike" style="display:inline">
				<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span> >
			</div>

			<span class="second-tab" ng-bind="'BLACKWHITE_LIST'|translate"></span>
		</div>
		<top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/blackWhiteList/blackWhiteList.html"
		 list-index="41" apply-val="{{proSupServerType}}" ng-show="isSuperManager && enterpriseType=='5'"></top:menu>
		 <top:menu chose-index="8" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/blackWhiteList/blackWhiteList.html"
		 list-index="53" ng-show="isSuperManager && enterpriseType=='1'"></top:menu>
		 <top:menu chose-index="4" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/blackWhiteList/blackWhiteList"
		 list-index="47" ng-show="isSuperManager && enterpriseType=='2'"></top:menu>

		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group" style="padding-left:25px;">
					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap;max-width: 70px;"
					 ng-bind="'BLACKWHITE_NUM'|translate"></label>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<input type="text" id="enterpriseName" class="form-control" placeholder="{{'BLACKWHITE_INPUTNUM'|translate}}"
						 ng-model="msisdn">
					</div>
					<label for="listType" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap;max-width: 70px;"
					 ng-bind="'BLACKWHITE_KIND'|translate"></label>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<select class="form-control" name="listType" ng-model="listType" ng-options="item.id as item.name for item in listTypeChoise">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<label for="businessType" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap;max-width: 70px;"
					 ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<select class="form-control" name="businessType" ng-model="businessType" ng-options="item.id as item.name for item in businessTypeChoise2">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>

					<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
						<button type="submit" class="btn search-btn" ng-click="queryBlackWhiteListFun()" style="float: right" ng-bind="'COMMON_SEARCH'|translate">
							<icon class="search-iocn"></icon>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="toAddMen('addMenBPop')" style="position: relative;">
				<icon class="add-iocn"></icon><span ng-bind="'BLACKWHITE_ADDBLACK'|translate"></span>
				<span class="ques" title="不会给黑名单用户进行业务投递"></span>
			</button>
			<button type="submit" class="btn add-btn" ng-click="toAddMen('addMenWPop')" style="position: relative;">
				<icon class="add-iocn"></icon><span ng-bind="'BLACKWHITE_ADDWHITE'|translate"></span>
				<span class="ques" title="只给白名单用户进行业务投递"></span>
			</button>
			<!--<button type="submit" class="btn add-btn" ng-click="toAddMen('addMenRPop')" style="position: relative;">-->
				<!--<icon class="add-iocn"></icon><span ng-bind="'BLACKWHITE_ADDRED'|translate"></span>-->
				<!--<span class="ques" title="号码不受热线频控限制，只作用于热线"></span>-->
			<!--</button>-->
			<button type="submit" class="btn add-btn" ng-click="impotMeb()" style="position: relative;">
				<icon class="add-iocn"></icon><span ng-bind="'BLACKWHITE_INPUTALL'|translate"></span>
			</button>
			<button type="submit" class="btn add-btn" ng-disabled="selectedListTemp.length==0"
					type="button" ng-click="removeMemberPop('all')" ng-bind="'GROUP_BATCHDELETE'|translate" style="position: relative;"></button>
		</div>
		<p style="font-size: 16px;margin: 12px 25px 12px;" ng-bind="'BLACKWHITE_ALLMISDN'|translate"></p>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
					<tr>
						<th style="padding-left:30px;width: 5%;"><input type="checkbox" ng-model="allChoose" ng-click="ifSelected()"></th>
						<th style="width:20%;padding-left: 25px" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
						<th style="width:20%" ng-bind="'SUBSERVTYPE'|translate"></th>
						<th style="width:20%" ng-bind="'BLACKWHITE_KIND'|translate"></th>
						<th style="width:20%" ng-bind="'COMMON_NUMBER'|translate"></th>
						<th style="width:20%" ng-bind="'COMMON_CREATETIME'|translate"></th>
						<th style="width:20%" ng-bind="'COMMON_OPERATE'|translate"></th>
					</tr>
				</thead>
				<tbody>
					<tr ng-repeat="item in queryBlackWhiteList">
						<td style="padding-left:30px;width: 5%;"><input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked"></td>
						<td style="width:20%;padding-left: 25px"><span title="{{servTypeMap[item.servType]}}" ng-bind="servTypeMap[item.servType]"></span></td>
						<td style="width:20%"><span title="{{subServTypeMap[item.subServType]}}" ng-bind="subServTypeMap[item.subServType]"></span></td>
						<td style="width:20%">
							<span ng-show="item.blackWhiteListType===1" ng-bind="'BLACKWHITE_BLACKLIST'|translate"></span>
							<span ng-show="item.blackWhiteListType===2" ng-bind="'BLACKWHITE_WHITELIST'|translate"></span>
						</td>
						<td style="width:20%"><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
						<td style="width:20%;min-width: 100px"><span title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</span></td>

						<td>
							<div class="handle">
								<ul>
									<li class="delete" ng-click="toDelete(item)" ng-bind="'COMMON_DELETE'|translate">
										<icon class="delete-icon"></icon>
									</li>
									<!-- <li class="edit" ng-click="toEdit(item)">
										<icon class="edit-icon"></icon>编辑
									</li> -->
								</ul>
							</div>
						</td>
					</tr>
					<tr ng-show="queryBlackWhiteList===null||queryBlackWhiteList.length===0">
						<td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
					</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="queryBlackWhiteListFun('justPage')"></ptl-page>
		</div>


		<!--删除弹出框-->
		<div class="modal fade" id="deleteGroupPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group">
								<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
									<div class="text-center">
										<span ng-bind="'GROUP_SUREDELETE'|translate"></span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="oneSelect?removeOrg('one'):removeOrg('all')" ng-bind="'COMMON_OK'|translate"></button>
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="delOrgCancel" ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		<!--添加黑名单弹出框-->
		<div class="modal fade addMenPop" id="addMenBPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-mousedown="canclePop($event)"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_ADDBLACK'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal" name="blackListForm">
							<div class="form-group">
								<div class="col-lg-12 col-xs-12 col-sm-12 col-md-12">
									<label for="businessType" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
									 ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
									<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
										<select class="form-control" name="businessType" ng-change="selectChange()" ng-click="checkDataUnique(1)" ng-model="addInfo.businessType" ng-options="item.id as item.name for item in selectBusList">
											<!-- 名片彩印 -->
											<option value="" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></option>
											<!-- <option value="" selected hidden></option> -->
										</select>
									</div>
									<!-- 业务形态 -->
									<label for="listType" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
									 ng-bind="'SUBSERVTYPE'|translate"></label>
									<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
										<select class="form-control" name="listType" ng-click="checkDataUnique(1)" ng-model="addInfo.listType" ng-options="item.id as item.name for item in subServTypeList">
											<option value="" ng-bind="'ZHUJIAO_COLORPRINT'|translate"></option>
											<!-- <option value="" selected hidden></option> -->
										</select>
									</div>
									<label for="enterpriseName" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
									 ng-bind="'BLACKWHITE_NUM'|translate"></label>
									<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
										<input type="text" id="blackList" class="form-control" placeholder="{{'BLACKWHITE_INPUTNUM'|translate}}"
										 ng-model="addInfo.msisdn" name="listIpt" required pattern="[0-9]{11}$" ng-class="{true:'error',false:''}[ !checkUnique]" 
										 ng-blur="checkDataUnique(1)" >
										<span style="color:red;line-height: 34px;display: block;" ng-show="!checkUnique">
											<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
											<span>{{uniqueTip}}</span>
										</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="addBlackWhiteList(1)" ng-disabled="!addInfo.msisdn || !checkUnique"
						 ng-bind="'COMMON_SAVE'|translate"></button>
						<button type="submit" class="btn btn-back addMemCancel" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_CANCLE'|translate" ng-mousedown="canclePop($event)"></button>
					</div>
				</div>
			</div>
		</div>
		<!--新增白名单弹出框-->
		<div class="modal fade addMenPop" id="addMenWPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-mousedown="canclePop($event)"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_ADDWHITE'|translate"></h4>
					</div>
					<div class="modal-body">
							<form class="form-horizontal" name="whiteListForm">
								<div class="form-group">
									<div class="col-lg-12 col-xs-12 col-sm-12 col-md-12">
										<label for="businessType" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
											   ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
										<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
											<!-- <select class="form-control" name="businessType" ng-click="checkDataUnique(2)" ng-model="addInfo.businessType" ng-options="item.id as item.name for item in businessTypeChoise">
												<option value="" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></option>
											</select> -->
											<select class="form-control" name="businessType" ng-change="selectChange()" ng-click="checkDataUnique(2)" ng-model="addInfo.businessType" ng-options="item.id as item.name for item in selectBusList">
												<!-- 名片彩印 -->
												<option value="" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></option>
												<!-- <option value="" selected hidden></option> -->
											</select>
										</div>
										<!--<label for="listType" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"-->
											   <!--ng-bind="'SUBSERVTYPE'|translate"></label>-->
										<!--<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">-->
											<!--<select class="form-control" name="listType" ng-click="checkDataUnique(2)" ng-model="addInfo.listType" ng-options="item.id as item.name for item in subServTypeChoise">-->
												<!--<option value="" ng-bind="'CONTENTAUDIT_CALLPX'|translate"></option>-->
											<!--</select>-->
										<!--</div>-->
										<!-- 业务形态 -->
										<label for="listType" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
										 ng-bind="'SUBSERVTYPE'|translate"></label>
										<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
											<select class="form-control" name="listType" ng-click="checkDataUnique(2)" ng-model="addInfo.listType" ng-options="item.id as item.name for item in subServTypeList">
												<!-- 挂机短信 -->
												<option value="" ng-bind="'ZHUJIAO_COLORPRINT'|translate"></option>
												<!-- <option value="" selected hidden></option> -->
											</select>
										</div>
										<label for="enterpriseName" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
											   ng-bind="'BLACKWHITE_NUM'|translate"></label>
										<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
											<input type="text" id="blackList" class="form-control" placeholder="{{'BLACKWHITE_INPUTNUM'|translate}}"
												   ng-model="addInfo.msisdn" name="listIpt" required pattern="[0-9]{11}$"
												   ng-class="{true:'error',false:''}[!checkUnique]"
												   ng-blur="checkDataUnique(2)">
											<span style="color:red;line-height: 34px;display: block;" ng-show="!checkUnique">
											<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
											<span>{{uniqueTip}}</span>
										</span>
										</div>
									</div>
								</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="addBlackWhiteList(2)" ng-disabled="!addInfo.msisdn || !checkUnique"
						 ng-bind="'COMMON_SAVE'|translate"></button>
						<button type="submit" class="btn btn-back addMemCancel" data-dismiss="modal" aria-label="Close" id="addMemCancel"
						 ng-bind="'COMMON_CANCLE'|translate" ng-mousedown="canclePop($event)"></button>
					</div>
				</div>
			</div>
		</div>
		<!--新增红名单弹出框-->
		<div class="modal fade addMenPop" id="addMenRPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-mousedown="canclePop($event)"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_ADDRED'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal" name="whiteListForm">
							<div class="form-group">
								<div class="col-lg-12 col-xs-12 col-sm-12 col-md-12">
									<label for="businessType" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
										   ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
									<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
										<select class="form-control" name="businessType" ng-click="checkDataUnique(2)" ng-model="addInfo.businessType" >
											<option value="" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></option>
										</select>
									</div>
									<label for="enterpriseName" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
										   ng-bind="'BLACKWHITE_NUM'|translate"></label>
									<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
										<input type="text" id="blackList" class="form-control" placeholder="{{'BLACKWHITE_INPUTNUM'|translate}}"
											   ng-model="addInfo.msisdn" name="listIpt" required pattern="[0-9]{11}$"
											   ng-class="{true:'error',false:''}[!checkUnique]"
											   ng-blur="checkDataUnique(2)">
										<span style="color:red;line-height: 34px;display: block;" ng-show="!checkUnique">
											<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
											<span>{{uniqueTip}}</span>
										</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="addBlackWhiteList(3)" ng-disabled="!addInfo.msisdn || !checkUnique"
								ng-bind="'COMMON_SAVE'|translate"></button>
						<button type="submit" class="btn btn-back addMemCancel" data-dismiss="modal" aria-label="Close" id="addMemCancel"
								ng-bind="'COMMON_CANCLE'|translate" ng-mousedown="canclePop($event)"></button>
					</div>
				</div>
			</div>
		</div>
		<!--导入名单弹出框-->
		<div class="modal fade" id="impoMebrPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_INPUTALL'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group" style="padding-bottom:0">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;" ng-bind="'COMMON_FILENAME'|translate"></label>
								<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
									<input type="text" class="form-control" ng-model="fileName" id="addGroupName" placeholder="{{'IMPORTXLSXTABLEFILE'|translate}}"
												 style="width: 100%;" ng-disabled="true" />
								</div>
								<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype" uploadifyid="uploadifyid"
																	validate="isValidate" filesize="filesize" mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl"
																	desc="uploadDesc" numlimit="numlimit" urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
								</cy:uploadifyfile>
							</div>
							<div style="color:#ff0000;margin: 0px 0 10px 100px;" ng-show="errorInfo!==''">
								<span class="uplodify-error-img"></span>
								<span ng-bind="errorInfo|translate"></span>
							</div>
							<div class="downloadRow col-sm-10" style="margin: 0 0 0 16px;">
								<a target="_blank" href="/qycy/ecpmp/assets/BlackWhiteTem2.xlsx" class="downMod" style="margin-right: 40px;"
									 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
								<span style="color: #705de1 !important; font-size: 12px;" ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center;padding: 30px">
						<button type="submit" class="btn btn-primary search-btn" ng-click="importMember()" ng-disabled="errorInfo!==''||fileUrl==''" ng-bind="'CONFIRMIMPORT'|translate"></button>
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addOrgCancel" ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center">
							<p style='font-size: 16px;color:#383838'>
								{{tip|translate}}
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

	</div>
</body>

</html>