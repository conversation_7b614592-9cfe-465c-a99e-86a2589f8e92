var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "service.common"])
app.controller("XwBatchsubDetailController", function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
  $scope.init = function () {

    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    //下拉框
    $scope.dealStatusList = [
      {
        id: "0",
        name: "处理中"
      },
      {
        id: "1",
        name: "成功"
      },
      {
          id: "2",
          name: "失败"
      }
    ];
    //下拉框
    $scope.subServTypeList = [
      {
        id: "1",
        name: "主叫"
      },
      {
          id: "2",
          name: "被叫"
      }
    ];
    //下拉框
    $scope.approveStatusList = [
      {
        id: "2",
        name: "待审核"
      },
      {
        id: "3",
        name: "审核通过"
      },
      {
          id: "4",
          name: "审核驳回"
      }
    ];
    $scope.queryH5BatchSubMember();
  };

  $scope.exportFile=function(){
	  	var dealStatus = [];
  		if ($scope.dealStatus && $scope.dealStatus == "0")
  		{
  			dealStatus = [0, 1, 2];
  		}
  		else if ($scope.dealStatus && $scope.dealStatus == "1")
  		{
  			dealStatus = [3];
  		}
  		else if ($scope.dealStatus && $scope.dealStatus == "2")
  		{
  			dealStatus = [4,5,6,7,8];
  		}
      var req = {
        "param":{
        	"taskID": $scope.taskId,
            "dealStatusList": dealStatus,
            "msisdn": $scope.msisdn || "",
            "subServType": $scope.subServType || "",
            "approveStatus": $scope.approveStatus || ""
        },
        "url":"/qycy/ecpmp/ecpmpServices/commonService/downH5BatchSubDetailFile",
        "method":"get"
      }
      CommonUtils.exportFile(req);
    }
  
  $scope.h5BatchSubMemberData = [];

  //查询
  $scope.queryH5BatchSubMember = function (condition) {
	  
	  $scope.taskId =  $.cookie("taskId");
    var req = {};
    if (condition != 'justPage') {
    	var dealStatus = [];
    	if ($scope.dealStatus && $scope.dealStatus == "0")
        {
    		dealStatus = [0, 1, 2];
        }
    	else if ($scope.dealStatus && $scope.dealStatus == "1")
        {
    		dealStatus = [3];
        }
    	else if ($scope.dealStatus && $scope.dealStatus == "2")
        {
    		dealStatus = [4,5,6,7,8];
        }
    	
      req = {
        "taskID": $scope.taskId,
        "dealStatusList": dealStatus,
        "msisdn": $scope.msisdn || "",
        "subServType": $scope.subServType || "",
        "approveStatus": $scope.approveStatus || "",
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryH5BatchSubMemberTemp = angular.copy(req);
    } else {
      req = $scope.queryH5BatchSubMemberTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryH5BatchSubMember",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(result);
          if (data.resultCode == '**********') {
            $scope.h5BatchSubMemberData = result.memberList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.accountContentInfoData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.accountContentInfoData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
});
app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 16);
    }
    return "";
  }
})
app.filter("formatApproveStatus", function () {
  return function (approveStatus) {
    if (approveStatus) {
    	var dealTxt = "";
    	if (approveStatus == 1 || approveStatus == 2)
        {
    		dealTxt = "审核中";
        }
    	else if (approveStatus == 3)
        {
    		dealTxt = "审核通过";
        }
    	else if (approveStatus == 4)
        {
    		dealTxt = "审核驳回";
        }
      return dealTxt;
    }
    return "";
  }
})
app.filter("formatSubServType", function () {
  return function (subServType) {
	  if (subServType) {
	    	var dealTxt = "";
	    	if (subServType == 1)
	        {
	    		dealTxt = "主叫彩印";
	        }
	    	else if (subServType == 2)
	        {
	    		dealTxt = "被叫彩印";
	        }
	    	
	      return dealTxt;
	    }
    return "";
  }
})
app.filter("dealStatus", function () {
  return function (dealStatus) {
    if (dealStatus) {
    	var dealTxt = "";
    	if (dealStatus == 0 || dealStatus == 1 || dealStatus == 2)
        {
    		dealTxt = "处理中";
        }
    	else if (dealStatus == 3)
        {
    		dealTxt = "成功";
        }
    	else
        {
    		dealTxt = "失败";
        }
      return dealTxt;
    }
    return "处理中";
  }
})