<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityTemplateMapper">
    <resultMap id="activityTemplate" type="com.huawei.jaguar.dsdp.ecpm.model.ActivityTemplate">       
        <result property="templateID" column="ID" javaType="java.lang.Integer" />
        <result property="templateName" column="templateName" javaType="java.lang.String" />
        <result property="templateFileURL" column="templateFileURL" javaType="java.lang.String" />
        <result property="templateIconURL" column="templateIconURL" javaType="java.lang.String" />
        <result property="backgroundURL" column="backgroundURL" javaType="java.lang.String" />
    </resultMap>
    
    <select id="getActivityTemplate" resultMap="activityTemplate">
       	select t.ID , t.templateName , t.templateFileURL, t.templateIconURL , t.backgroundURL
       		from ecpm_t_activity_template t 
        <trim prefix="where" prefixOverrides="and|or">
			<if test="templateIDList != null and templateIDList.size()>0">
				and t.ID in
				<foreach item="templateID" index="index" collection="templateIDList"
					open="(" separator="," close=")">
					#{templateID}
				</foreach>
			</if>
		</trim>
    </select>
    
</mapper>