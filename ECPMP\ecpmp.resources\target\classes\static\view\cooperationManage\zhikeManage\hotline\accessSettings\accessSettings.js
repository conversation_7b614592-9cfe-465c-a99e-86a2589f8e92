var app = angular.module("myApp", [
  "util.ajax",
  "top.menu",
  "angularI18n",
  "cy.uploadify",
]); /**/
app.controller("settingController", [
  "$scope",
  "$rootScope",
  "$location",
  "RestClientUtil",
  function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {
      $scope.isSuperManager = false;
      $scope.operatorID = $.cookie("accountID");
      $scope.loginRoleType = $.cookie("loginRoleType");
      $scope.isSuperManager =
        $scope.loginRoleType == "superrManager" ||
        $scope.loginRoleType == "normalMangager";
      $scope.enterpriseType = $.cookie("enterpriseType") || "";
      $scope.enterpriseID = $.cookie("enterpriseID") || "";
      $scope.subEnterpriseID = $.cookie("subEnterpriseID") || "";
      $scope.subEnterpriseName = $.cookie("subEnterpriseName") || "";
      $scope.thirdAccessAccount = $.cookie("subEnterpriseName") || "";
      $scope.thirdAccessPassword = $.cookie("subEnterpriseName") || "";
      $scope.checkedEdit = false;
      $scope.enterpriseApproveCallbackUrlPre = "";
      $scope.enterpriseApproveCallbackUrlEnd = "";


      $scope.isProvincial = $scope.loginRoleType == "provincial";

      $scope.isAllow = true;
      $scope.isZYZQ = $.cookie("reserved10") == "111";

      if ($scope.isProvincial && !$scope.isSuperManager) {
        var proSupType = $.cookie("proSupType");
        $scope.isAllow = false;
        if (proSupType) {
          var value = JSON.parse(proSupType);
          for (var i = 0; i < value.length; i++) {
            var index = value[i];
            if (index == 2) {
              $scope.isAllow = true;
            }
          }
        }
      }
      if ($scope.isAllow) {
        $scope.initThirdpartyData();
        $scope.choseIndex = 6;
        if ($scope.enterpriseType == "5" && $scope.isSuperManager) {
          var proSupServerType = $.cookie("proSupServerType");
          $scope.proSupServerType = $.cookie("proSupServerType");
          if (proSupServerType) {
            var value = JSON.parse(proSupServerType);
            for (var i = 0; i < value.length; i++) {
              var index = value[i];
              if (!$scope.isZYZQ && index == 49) {
                $scope.choseIndex = i;
              }
              if ($scope.isZYZQ && index == 76) {
                $scope.choseIndex = i;
              }
            }
          }
        }
      } else {
        $("#bussinessNo").modal();
      }
    };
    // 1. 抽取公共方法处理数据复制
    $scope.copyThirdpartyData = function (data) {
      // 使用对象解构简化赋值
      const {
        id = "",
        accessAccount = "",
        accessPassword = "",
        callbackUrl = "",
        approveCallbackUrl = "",
        groupSendNotifyUrl = "",
        feedbackUrl = "",
        thirdAccount = "",
        thirdPassword = "",
        mpApproveCallbackUrl = "",
        subcribeResultNotifyUrl = "",
      } = data || {};

      // 一次性赋值所有属性
      Object.assign($scope, {
        thirdpartyListData: data,
        id,
        accessAccount,
        accessPassword,
        callbackUrl,
        approveCallbackUrl,
        groupSendNotifyUrl,
        feedbackUrl,
        thirdAccessAccount: thirdAccount,
        thirdAccessPassword: thirdPassword,
        mpApproveCallbackUrl,
        subcribeResultNotifyUrl,
      });
      // 处理子企业审核回调地址
      if (data && data.enterpriseApproveCallbackUrl) {
        // 查找第一个分隔符的位置
        const separatorIndex = data.enterpriseApproveCallbackUrl.indexOf('|');

        if (separatorIndex !== -1) {
            // 有分隔符，拆分为前后两部分
            $scope.enterpriseApproveCallbackUrlPre = data.enterpriseApproveCallbackUrl.substring(0, separatorIndex);
            $scope.enterpriseApproveCallbackUrlEnd = data.enterpriseApproveCallbackUrl.substring(separatorIndex + 1);
        } else {
            // 没有分隔符，全部放在前缀部分
            $scope.enterpriseApproveCallbackUrlPre = data.enterpriseApproveCallbackUrl;
            $scope.enterpriseApproveCallbackUrlEnd = '';
        }
      } else {
          $scope.enterpriseApproveCallbackUrlPre = '';
          $scope.enterpriseApproveCallbackUrlEnd = '';
      }

      // 判断账号是否可以编辑
      $scope.isUnEdit = !($scope.accessAccount === "" && $scope.isSuperManager);
    };
    $scope.initThirdpartyData = async function () {
      try {
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
          // 获取代理商配置
          const agentConfigData = await $scope.queryThirdpartyList(
            $scope.enterpriseID
          );

          // 获取子企业配置
          const subConfigData = await $scope.queryThirdpartyList(
            $scope.subEnterpriseID
          );

          // 合并配置
          const mergedConfig = {
            id: subConfigData.id || "",
            accessAccount: subConfigData.accessAccount || agentConfigData.accessAccount || "",
            accessPassword: subConfigData.accessPassword || agentConfigData.accessPassword || "",
            callbackUrl: subConfigData.callbackUrl || agentConfigData.callbackUrl || "",
            approveCallbackUrl: subConfigData.approveCallbackUrl || agentConfigData.approveCallbackUrl || "",
            groupSendNotifyUrl: subConfigData.groupSendNotifyUrl || agentConfigData.groupSendNotifyUrl || "",
            feedbackUrl: subConfigData.feedbackUrl || agentConfigData.feedbackUrl || "",
            thirdAccount: subConfigData.thirdAccount || agentConfigData.thirdAccount || "",
            thirdPassword: subConfigData.thirdPassword || agentConfigData.thirdPassword || "",
            mpApproveCallbackUrl: subConfigData.mpApproveCallbackUrl || agentConfigData.mpApproveCallbackUrl || "",
            subcribeResultNotifyUrl: subConfigData.subcribeResultNotifyUrl || agentConfigData.subcribeResultNotifyUrl || ""
        };
          // 复制数据
         // 使用 $apply 确保数据更新
         $scope.$apply(function() {
            $scope.copyThirdpartyData(mergedConfig);
         });
        } else {
          // 获取普通配置
          const data = await $scope.queryThirdpartyList($scope.enterpriseID);
          console.log("data", data);
          $scope.$apply(function() {
            $scope.copyThirdpartyData(data);
          });
        }
      } catch (errorCode) {
        $scope.$apply(function() {
          $scope.tip = errorCode;
          $("#myModal").modal();
        });
      }
    };
    $scope.hotMsisdnVali = true;
    $scope.hotMsisdnDesc = "";
    $scope.queryThirdpartyList = function (enterpriseID) {
      return new Promise((resolve, reject) => {
        var req = {
          enterpriseID: enterpriseID,
          pageParameter: {
            pageNum: 1,
            pageSize: 10,
            isReturnTotal: "1",
          },
        };
        if( $scope.enterpriseType == 2) { // 只代理商接入设置那里查询传参新增
          req["defaultGenerationFlag"] = 1;
        }
        RestClientUtil.ajaxRequest({
          type: "POST",
          url: "/ecpmp/ecpmpServices/hotlineService/queryThirdparty",
          data: JSON.stringify(req),
          success: function (result) {
            if (result.result.resultCode == "**********") {
              resolve(result.thirdpartyList ? result.thirdpartyList[0] : {});
            } else {
              reject(result.result.resultCode);
            }
          },
          error: function (err) {
            reject("**********");
          },
        });
      });
    };
    $scope.saveUssd = function () {
     // 先进行校验
      if (!$scope.validateCallbackUrls()) {
        $scope.tip = "子企业审核回调地址的请求地址和接口名必须同时填写或同时为空";
        $("#myModal").modal();
        return; // 阻止继续执行保存操作
      }
      $scope.saveThirdpartyInfo = {
        id: parseInt($scope.id), //唯一标识、
        platformID: parseInt($scope.enterpriseID), //企业id、
        accessPassword: $scope.accessPassword, //访问密码、
        thirdAccount: $scope.thirdAccessAccount, //第三方分配的账号、
        thirdPassword: $scope.thirdAccessPassword, //第三方分配的密码、
        callbackUrl: $scope.callbackUrl, //回调地址、
        approveCallbackUrl: $scope.approveCallbackUrl, //审核回调地址、
        groupSendNotifyUrl: $scope.groupSendNotifyUrl, //群发回调地址、
        feedbackUrl: $scope.feedbackUrl, //信息反馈回调地址、
        operatorID: parseInt($scope.operatorID),
        mpApproveCallbackUrl: $scope.mpApproveCallbackUrl,
        subcribeResultNotifyUrl: $scope.subcribeResultNotifyUrl,
        enterpriseApproveCallbackUrl: $scope.getEnterpriseApproveCallbackUrl(), // 子企业审核回调地址
      };
      // 如果是子企业
      if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
        $scope.saveThirdpartyInfo["platformID"] = $scope.subEnterpriseID;
      }
      //如果是可编辑 isUnEdit为false，绑定数据发至后端
      if (!$scope.isUnEdit) {
        $scope.saveThirdpartyInfo.accessAccount = $scope.accessAccount; //账号
      }
      var req = {
        thirdparty: $scope.saveThirdpartyInfo,
        operateType: "1",
      };
      RestClientUtil.ajaxRequest({
        type: "POST",
        url: "/ecpmp/ecpmpServices/hotlineService/saveThirdparty",
        data: JSON.stringify(req),
        success: function (result) {
          $rootScope.$apply(function () {
            var data = result.result;
            if (data.resultCode == "**********") {
              $scope.tip = "COMMON_SAVESUCCESS";
              $("#myModal").modal();
              $scope.initThirdpartyData();
            } else {
              $scope.tip = data.resultCode;
              $("#myModal").modal();
            }
          });
        },
        error: function (err) {
          $rootScope.$apply(function () {
            $scope.tip = "**********";
            $("#myModal").modal();
          });
        },
      });
    };

    // 1. 在控制器中添加自定义验证函数
    $scope.validateCallbackUrls = function() {
      // 获取两个字段的值
      const preUrl = $scope.enterpriseApproveCallbackUrlPre;
      const endUrl = $scope.enterpriseApproveCallbackUrlEnd;

      // 检查依赖关系
      const preUrlFilled = preUrl && preUrl.trim().length > 0;
      const endUrlFilled = endUrl && endUrl.trim().length > 0;

      // 设置验证结果
      $scope.callbackUrlsValid = !(preUrlFilled && !endUrlFilled || !preUrlFilled && endUrlFilled);

      // 返回验证结果，可用于表单提交前检查
      return $scope.callbackUrlsValid;
    };
    // 2. 添加监听器监控两个字段的变化
    $scope.$watch('enterpriseApproveCallbackUrlPre + enterpriseApproveCallbackUrlEnd', function() {
      $scope.validateCallbackUrls();
    });
    // 获取子企业回调地址
    $scope.getEnterpriseApproveCallbackUrl = function() {
      var pre = $scope.enterpriseApproveCallbackUrlPre;
      var end = $scope.enterpriseApproveCallbackUrlEnd;
      if (pre && pre.trim() !== "" && end && end.trim() !== "") {
          return pre + "|" + end;
      }
      return "";
  };
  },
]);
app.config([
  "$locationProvider",
  function ($locationProvider) {
    $locationProvider.html5Mode({
      enabled: true,
      requireBase: false,
    });
  },
]);
