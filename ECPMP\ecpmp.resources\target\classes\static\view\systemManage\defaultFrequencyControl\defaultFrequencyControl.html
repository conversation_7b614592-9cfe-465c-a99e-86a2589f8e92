<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>默认频控设置</title>
    <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
    <link href="../../../css/reset.css" rel="stylesheet"/>
    <link rel="stylesheet" href="../../../css/businessSetting.css">
    <script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
    <!-- 引入导航组件 -->
    <link href="../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="defaultFrequencyControl.js"></script>

    <style>
		.no-optional {
			padding: 20px 25px;
			background-color: #ffffff;
		}

		.no-optional p {
			margin: 0 auto;
			width: 145px;
		}

		.center {
			text-align: center;
		}

		.min-width-330 {
			min-width: 330px;
		}
		.service-config{
   			color: #705DE1;
		}


    </style>
</head>

<body ng-app='myApp' ng-controller='defaultFrequencyController' ng-init="init();" class="body-min-width">
<div class="cooperation-manage">

    <div class="cooperation-head" ng-if="isSuperManager">
    <span class="frist-tab">系统管理</span>&nbsp;&gt;&nbsp;<span class="second-tab">默认频控设置</span>
</div>
	<!-- 页面提示 -->
	<div class="cooperation-tit col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <p style="color:red;" ng-bind="'FREQUENCY_CONTROL_COMMON_TIPS'|translate"></p>
    </div>
    
    <!--名片彩印-->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="switchOff[0]==1||(isSuperManager&&enterpriseType != 3&&!isSecondEnter)||(!isSuperManager&&enterpriseType != 3&&!isSecondEnter&&switchOff[0]==0&&initAllInfo.px.operatorID!==1000)">
        <p class="form-horizontal-tit service-config" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></p>
        <form class="form-horizontal form-group bgWhite" name="orderBase1">

            <div class="form-inline">
                <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label for="groupName" ng-bind="'SAMETEMPLATELIMITS'|translate"></label>
                </div>
				<div style="width:110px" class="col-lg-3 col-md-3 col-sm-3 col-xs-3">
					<select style="width:100px" class="form-control" ng-model="initAllInfo.mp.periodType"
					        ng-options="x.id as x.name for x in periodTypeList" ng-change="changMPOption()">
							<!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
					</select>
				</div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:220px;">
					<input type="text" class="form-control" autocomplete="off"
                           placeholder="{{'DEFAULTFREQUENCY_INPUT'|translate}}" name="amount1"
                           ng-model="initAllInfo.mp.oneTemplateDayLimit" pattern="(^[0-9]{1,9}$)|(^[1-9]$)">
                </div>
                <span style="color:red;line-height: 34px"
                      ng-show="orderBase1.amount1.$dirty && orderBase1.amount1.$invalid">
						<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase1.amount1.$error.required">必填</span> -->
						<span ng-show="orderBase1.amount1.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>

            </div>
            <div class="form-inline">
                <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label for="groupName" ng-bind="'MINIUMDELIVERYSECOND'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 min-width-330">
                    <input type="text" class="form-control" autocomplete="off"
                           placeholder="{{'DEFAULTFREQUENCY_INPUT_MINUTE'|translate}}"
                           name="amount2"
                           ng-model="initAllInfo.mp.oneDeliveryInterval" pattern="(^[0-9]{1,9}$)|(^[1-9]$)">
                </div>
                <span style="color:red;line-height: 34px"
                      ng-show="orderBase1.amount2.$dirty && orderBase1.amount2.$invalid">
						<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase1.amount2.$error.required">必填</span> -->
						<span ng-show="orderBase1.amount2.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>
            </div>
        </form>
    </div>
    <!--热线彩印 -->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="switchOff[1]==1||(isSuperManager&&enterpriseType != 3&&!isSecondEnter)||(!isSuperManager&&enterpriseType != 3&&!isSecondEnter&&switchOff[1]==0 && initAllInfo.gjdx.operatorID!==1000)">
        <p class="form-horizontal-tit service-config ng-binding" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></p>
        <form class="form-horizontal form-group bgWhite" name="orderBase2">
            <div class="form-inline">
                <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 min-width-330">
                    <input type="text" class="form-control" autocomplete="off"
                           placeholder="{{'DEFAULTFREQUENCY_INPUT'|translate}}" name="amount3"
                           ng-model="initAllInfo.rx.oneTemplateDayLimit" pattern="(^[0-9]{1,9}$)|(^[1-9]$)">
                </div>
                <span style="color:red;line-height: 34px"
                      ng-show="orderBase2.amount3.$dirty && orderBase2.amount3.$invalid">
						<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase2.amount3.$error.required">必填</span> -->
						<span ng-show="orderBase2.amount3.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>

            </div>
            <div class="form-inline">
                <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label for="groupName" ng-bind="'DEFAULTFREQUENCY_DELIVERY_SECOND'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 min-width-330">
                    <input type="text" class="form-control" autocomplete="off"
                           placeholder="{{'DEFAULTFREQUENCY_INPUT_SECOND'|translate}}"
                           name="amount4"
                           ng-model="initAllInfo.rx.oneDeliveryInterval" pattern="(^[0-9]{1,9}$)|(^[1-9]$)">
                </div>
                <span style="color:red;line-height: 34px"
                      ng-show="orderBase2.amount4.$dirty && orderBase2.amount4.$invalid">
						<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase2.amount4.$error.required">必填</span> -->
						<span ng-show="orderBase2.amount4.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>
            </div>

        </form>
    </div>
    <!--广告彩印-->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" ng-show="switchOff[2]==1||(isSuperManager&&enterpriseType != 3&&!isSecondEnter)||(!isSuperManager&&enterpriseType != 3&&!isSecondEnter&&switchOff[2]==0 && initAllInfo.gjcx.operatorID!==1000)">
        <p class="form-horizontal-tit service-config ng-binding" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></p>
        <form class="form-horizontal form-group bgWhite" name="orderBase3">
            <div class="form-inline">
                <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label for="groupName" ng-bind="'SAMETEMPLATELIMITS'|translate"></label>
                </div>
				<div style="width:110px" class="col-lg-3 col-md-3 col-sm-3 col-xs-3">
					<select style="width:100px" class="form-control" ng-model="initAllInfo.gg.periodType"
					        ng-options="x.id as x.name for x in periodTypeList"  ng-change="changGGOption()">
							<!-- <option value="" ng-bind="'PERIOD_TYPE'|translate"></option> -->
					</select>
				</div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width:220px;">
                    <input type="text" class="form-control" autocomplete="off"
                           placeholder="{{'DEFAULTFREQUENCY_INPUT'|translate}}" name="amount5"
                           ng-model="initAllInfo.gg.oneTemplateDayLimit" pattern="(^[0-9]{1,9}$)|(^[1-9]$)">
                </div>
                <span style="color:red;line-height: 34px;line-height: 34px"
                      ng-show="orderBase3.amount5.$dirty && orderBase3.amount5.$invalid">
						<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase3.amount5.$error.required">必填</span> -->
						<span ng-show="orderBase3.amount5.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>
            </div>
            <div class="form-inline">
                <div class="col-lg-2 col-md-3 col-sm-3 col-xs-3 control-label">
                    <label for="groupName" ng-bind="'MINIUMDELIVERYSECOND'|translate"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 min-width-330">
                    <input type="text" class="form-control" autocomplete="off"
                           placeholder="{{'DEFAULTFREQUENCY_INPUT_MINUTE'|translate}}"
                           name="amount6"
                           ng-model="initAllInfo.gg.oneDeliveryInterval" pattern="(^[0-9]{1,9}$)|(^[1-9]$)">
                </div>
                <span style="color:red;line-height: 34px"
                      ng-show="orderBase3.amount6.$dirty && orderBase3.amount6.$invalid">
						<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                    <!-- <span ng-show="orderBase3.amount6.$error.required">必填</span> -->
						<span ng-show="orderBase3.amount6.$error.pattern"
                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
					</span>
            </div>
        </form>
    </div>

    <!-- 通知业务频控配置 -->
    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
        <p class="form-horizontal-tit service-config" ng-bind="'ZENGCAIQUNFA'|translate"></p>
        <div class="bgWhite">
            <!--屏显配置-->
            <form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase1">
                <!--<p class="form-horizontal-tit" ng-bind="'SMS_SETTING'|translate"></p>-->
                <!--热线、屏显，同一模板单人日上限-->
                <div class="form-inline form-fst">
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-7 control-label noPadding">
                        <label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">
                        <input type="text" class="form-control" autocomplete="off"
                               placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount1"
                               pattern="(^[0-9]{1,9}$)|(^[1-9]$)"
                               ng-class="{'redBorder':!!rx_px_limit_error}"
                               ng-model="initAllInfo.TZSms.oneTemplateDayLimit">
                    </div>


                </div>

            </form>
            <!--挂机短信配置-->
            <!--<form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase2">-->
                <!--<p class="form-horizontal-tit" ng-bind="'FLASH_SETTING'|translate"></p>-->
                <!--&lt;!&ndash;热线、挂短，同一模板单人日上限&ndash;&gt;-->
                <!--<div class="form-inline form-fst">-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">-->
                        <!--<label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>-->
                    <!--</div>-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">-->
                        <!--<input type="text" class="form-control" autocomplete="off"-->
                               <!--placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount3"-->
                               <!--ng-class="{'redBorder':!!rx_gjdx_limit_error}"-->
                               <!--ng-model="initAllInfo.TZFlash.oneTemplateDayLimit">-->
                    <!--</div>-->

                <!--</div>-->

            <!--</form>-->
            <!--&lt;!&ndash;挂机彩信配置&ndash;&gt;-->
            <!--<form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">-->
                <!--<p class="form-horizontal-tit" ng-bind="'CX_SETTING'|translate"></p>-->
                <!--&lt;!&ndash;热线、挂彩，同一模板单人日上限&ndash;&gt;-->
                <!--<div class="form-inline form-fst">-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">-->
                        <!--<label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>-->
                    <!--</div>-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">-->
                        <!--<input type="text" class="form-control" autocomplete="off"-->
                               <!--placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount5"-->
                               <!--ng-class="{'redBorder':!!rx_gjcx_limit_error}"-->
                               <!--ng-model="initAllInfo.TZCX.oneTemplateDayLimit">-->
                    <!--</div>-->

                <!--</div>-->

            <!--</form>-->
            <!--&lt;!&ndash;挂机增彩配置&ndash;&gt;-->
            <!--<form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">-->
                <!--<p class="form-horizontal-tit" ng-bind="'USSD_SETTING'|translate"></p>-->
                <!--&lt;!&ndash;热线、增彩，同一模板单人日上限&ndash;&gt;-->
                <!--<div class="form-inline form-fst">-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">-->
                        <!--<label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>-->
                    <!--</div>-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">-->
                        <!--<input type="text" class="form-control" autocomplete="off"-->
                               <!--placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount5"-->
                               <!--ng-class="{'redBorder':!!rx_gjzc_limit_error}"-->
                               <!--ng-model="initAllInfo.TZUSSD.oneTemplateDayLimit">-->
                    <!--</div>-->

                <!--</div>-->

            <!--</form>-->
            <!--&lt;!&ndash;挂机增彩配置&ndash;&gt;-->
            <!--<form class="form-horizontal form-group col-lg-4 col-md-4 col-sm-4 col-xs-4" name="orderBase3">-->
                <!--<p class="form-horizontal-tit" ng-bind="'MMS_SETTING'|translate"></p>-->
                <!--&lt;!&ndash;热线、增彩，同一模板单人日上限&ndash;&gt;-->
                <!--<div class="form-inline form-fst">-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 control-label noPadding">-->
                        <!--<label for="groupName" ng-bind="'SAMETEMPLATELIMIT'|translate"></label>-->
                    <!--</div>-->
                    <!--<div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 noPadding">-->
                        <!--<input type="text" class="form-control" autocomplete="off"-->
                               <!--ng-keyup="moreThen('rx_gjzc_limit',initDefaultInfo.rx.defaultOneTemplateDayLimit,initAllInfo.rx.gjzc.oneTemplateDayLimit)"-->
                               <!--placeholder="{{initDefaultInfo.rx.defaultOneTemplateDayLimitDesc}}" name="amount5"-->
                               <!--ng-class="{'redBorder':!!rx_gjzc_limit_error}"-->
                               <!--ng-model="initAllInfo.TZMms.oneTemplateDayLimit">-->
                    <!--</div>-->

                <!--</div>-->

            <!--</form>-->
        </div>
    </div>
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button ng-click="refresh()" type="button" class="close" data-dismiss="modal"
                            aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838'>
                            <span ng-show="tip=='1030100000'" ng-bind="'COMMON_SAVE'|translate"></span>{{tip|translate}}
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button ng-click="refresh()" type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="order-btn" style="margin: 20px 50px; margin-left: 28%;">
            <button style="margin-right: 30px;" type="submit" class="btn btn-primary search-btn"
                    ng-disabled="(orderBase1.$invalid&&(switchOff[0]==1||isSuperManager|| isProvincial))||(orderBase2.$invalid&&(switchOff[1]==1||isSuperManager|| isProvincial))||
                (orderBase3.$invalid&&(switchOff[2]==1||isSuperManager|| isProvincial))" ng-click="UpdateRuleList()"
                    ng-bind="'COMMON_SAVE'|translate">
            </button>
            <!-- <button style="margin:40px 20px;" type="submit" class="btn" ng-click="returnUp()">返回</button> -->
        </div>
    </div>

</div>
</body>

</html>