<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.OrderMapper">

	<resultMap id="OrderWrapper"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.OrderWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="orderCode" column="orderCode" javaType="java.lang.String" />
		<result property="thirdPartyOrderID" column="thirdPartyOrderID" javaType="java.lang.String" />
		<result property="orderType" column="orderType" javaType="java.lang.Integer" />
		<result property="ownerOrderID" column="ownerOrderID" javaType="java.lang.String" />
		<result property="transferAttachURL" column="transferAttachURL" javaType="java.lang.String" />
		<result property="orderDetailURL" column="orderDetailURL" javaType="java.lang.String" />
		<result property="orderName" column="orderName"	javaType="java.lang.String" />
		<result property="amount" column="amount" javaType="java.math.BigDecimal" />
		<result property="currency" column="currency" javaType="java.lang.String" />
		<result property="payChannel" column="payChannel" javaType="java.lang.Integer" />
		<result property="payStatus" column="payStatus" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
		<result property="servType" column="servType"	javaType="java.lang.Integer" />
		<result property="effictiveTime" column="effictiveTime" javaType="java.util.Date" />
		<result property="expireTime" column="expireTime" javaType="java.util.Date" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="lastUpdateTime" column="lastUpdateTime" javaType="java.util.Date" />
		<result property="isExperience" column="isExperience" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>
	
	<select id="queryOrderItemListBySecond" resultMap="OrderWrapper">
		select
		ID,
		orderCode,
		thirdPartyOrderID,
		isExperience,
		orderType,
		ownerOrderID,
		transferAttachURL,
		orderDetailURL,
		orderName,
		amount,
		currency,
		payChannel,
		payStatus,
		enterpriseID,
		enterpriseName,
		servType,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_order
		WHERE expireTime <![CDATA[ >= ]]> NOW() AND orderCode=
		(SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(extInfo,'":"',-4),'","',1) AS orderItemCode  FROM ecpe_t_order r 
		WHERE r.orderCode = #{orderCode});
	</select>
	
	<select id="queryOrderByMainOrder" resultMap="OrderWrapper">
		SELECT 
		ID,
		orderCode ,
		thirdPartyOrderID,
		isExperience,
		orderType,
		ownerOrderID,
		transferAttachURL,
		orderDetailURL,
		orderName,
		amount,
		currency,
		payChannel,
		payStatus,
		enterpriseID,
		enterpriseName,
		servType,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		FROM ecpe_t_order  
		WHERE orderCode=
		(SELECT SUBSTRING_INDEX(SUBSTRING_INDEX(extInfo,'":"',-4),'","',1) AS orderItemCode  FROM ecpe_t_order r 
		WHERE r.orderCode = #{orderCode})
	</select>
	
	<select id="queryOrderCodeTotal" resultMap="OrderWrapper">
		select
		ID,
		orderCode,
		thirdPartyOrderID,
		isExperience,
		orderType,
		ownerOrderID,
		transferAttachURL,
		orderDetailURL,
		orderName,
		amount,
		currency,
		payChannel,
		payStatus,
		enterpriseID,
		enterpriseName,
		servType,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_order
		WHERE SUBSTRING_INDEX(SUBSTRING_INDEX(extInfo,'":"',-4),'","',1) IN (
			#{orderCode}
		); 

	</select>

	<!--查询订单主项列表 -->
	<select id="queryOrderList" resultMap="OrderWrapper">
		select
		ID,
		orderCode,
		thirdPartyOrderID,
		isExperience,
		orderType,
		ownerOrderID,
		transferAttachURL,
		orderDetailURL,
		orderName,
		amount,
		currency,
		payChannel,
		payStatus,
		enterpriseID,
		enterpriseName,
		servType,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_order
		<trim prefix="where" prefixOverrides="and|or">
			<if test="id!=null">
				and ID = #{id}
			</if>
			<if test="isExperience!=null">
				and isExperience = #{isExperience}
			</if>
			<if test="orderType!=null">
				and orderType = #{orderType}
			</if>
			<if test="orderCode!=null  and orderCode!=''">
				and orderCode = #{orderCode}
			</if>
			<if test="thirdPartyOrderID!=null and thirdPartyOrderID!=''">
				and thirdPartyOrderID = #{thirdPartyOrderID}
			</if>
			<if test="ownerOrderID!=null and ownerOrderID!=''">
				and ownerOrderID = #{ownerOrderID}
			</if>
			<if test="orderName!=null  and orderName!=''">
				and orderName like concat("%", #{orderName}, "%") 
			</if>
			<if test="enterpriseID!=null">
				and enterpriseID = #{enterpriseID}
			</if>
			<if test="enterpriseName!=null  and enterpriseName!=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="servType!=null">
				and servType = #{servType}
			</if>
			<if test="enterpriseType!=null">
				and reserved1 = #{enterpriseType}
			</if>
			<if test="status!=null and status == 1">
				and effictiveTime <![CDATA[ <= ]]>  #{nowTime}
				and expireTime <![CDATA[ >= ]]> #{nowTime}
			</if>
			<if test="status!=null and status == 2">
				and expireTime <![CDATA[ <= ]]> #{nowTime}
			</if>
			<if test="status!=null and status == 3">
				and effictiveTime <![CDATA[ >= ]]>  #{nowTime}
			</if>
			<if test="payStatus!=null">
				and payStatus = #{payStatus}
			</if>
			<if test="startTime!=null and endTime!=null">
				and ((effictiveTime <![CDATA[ >= ]]>
				#{startTime} and effictiveTime <![CDATA[ <= ]]>
				#{endTime})
				or (expireTime <![CDATA[ >= ]]>
				#{startTime} and expireTime <![CDATA[ <= ]]>
				#{endTime})
				or ((effictiveTime <![CDATA[ <= ]]>
				#{startTime} and expireTime <![CDATA[ >= ]]>
				#{endTime}))
				)
			</if>
			<if test="startTime!=null and endTime ==null">
				and expireTime <![CDATA[ >= ]]> #{startTime}
			</if>
			<if test="startTime==null and endTime!=null">
				and effictiveTime <![CDATA[ <= ]]> #{endTime}
			</if>
			<if test="createStartTime!=null">
				and createTime <![CDATA[ >= ]]> #{createStartTime}
			</if>
			<if test="createEndTime!=null">
				and createTime <![CDATA[ <= ]]> #{createEndTime}
			</if>
		</trim>
		order by createTime desc
		limit #{pageNo},#{pageSize}
	</select>

	<!--查询订单主项列表总数 -->
	<select id="countOrder" resultType="java.lang.Integer">
		SELECT
		count(0)
		from ecpe_t_order
		<trim prefix="where" prefixOverrides="and|or">
			<if test="id!=null">
				and ID = #{id}
			</if>
			<if test="isExperience!=null">
				and isExperience = #{isExperience}
			</if>
			<if test="orderType!=null">
				and orderType = #{orderType}
			</if>
			<if test="orderCode!=null  and orderCode!=''">
				and orderCode = #{orderCode}
			</if>
			<if test="thirdPartyOrderID!=null and thirdPartyOrderID!=''">
				and thirdPartyOrderID = #{thirdPartyOrderID}
			</if>
			<if test="ownerOrderID!=null and ownerOrderID!=''">
				and ownerOrderID = #{ownerOrderID}
			</if>
			<if test="orderName!=null  and orderName!=''">
				and orderName like concat("%", #{orderName}, "%") 
			</if>
			<if test="enterpriseID!=null">
				and enterpriseID = #{enterpriseID}
			</if>
			<if test="enterpriseName!=null  and enterpriseName!=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="servType!=null">
				and servType = #{servType}
			</if>
			<if test="enterpriseType!=null">
				and reserved1 = #{enterpriseType}
			</if>
			<if test="status!=null and status == 1">
				and effictiveTime <![CDATA[ <= ]]>  #{nowTime}
				and expireTime <![CDATA[ >= ]]> #{nowTime}
			</if>
			<if test="status!=null and status == 2">
				and expireTime <![CDATA[ <= ]]> #{nowTime}
			</if>
			<if test="status!=null and status == 3">
				and effictiveTime <![CDATA[ >= ]]>  #{nowTime}
			</if>
			<if test="payStatus!=null">
				and payStatus = #{payStatus}
			</if>
			<if test="startTime!=null and endTime!=null">
				and ((effictiveTime <![CDATA[ >= ]]>
				#{startTime} and effictiveTime <![CDATA[ <= ]]>
				#{endTime})
				or (expireTime <![CDATA[ >= ]]>
				#{startTime} and expireTime <![CDATA[ <= ]]>
				#{endTime})
				or ((effictiveTime <![CDATA[ <= ]]>
				#{startTime} and expireTime <![CDATA[ >= ]]>
				#{endTime}))
				)
			</if>
			<if test="startTime!=null and endTime ==null">
				and expireTime <![CDATA[ >= ]]> #{startTime}
			</if>
			<if test="startTime==null and endTime!=null">
				and effictiveTime <![CDATA[ <= ]]> #{endTime}
			</if>
			<if test="createStartTime!=null">
				and createTime <![CDATA[ >= ]]> #{createStartTime}
			</if>
			<if test="createEndTime!=null">
				and createTime <![CDATA[ <= ]]> #{createEndTime}
			</if>
		</trim>
	</select>

	<!--新增订单主表 -->
	<insert id="insertOrder">
		INSERT INTO
		ecpe_t_order
		(
		id,
		orderCode,
		thirdPartyOrderID,
		orderType,
		ownerOrderID,
		transferAttachURL,
		orderDetailURL,
		orderName,
		amount,
		currency,
		payChannel,
		payStatus,
		enterpriseID,
		enterpriseName,
		servType,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		isExperience,		
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10)
		VALUES
		(
		next value for MYCATSEQ_ECPE_T_ORDER,
		#{orderCode},
		#{thirdPartyOrderID},
		#{orderType},
		#{ownerOrderID},
		#{transferAttachURL},
		#{orderDetailURL},
		#{orderName},
		#{amount},
		#{currency},
		#{payChannel},
		#{payStatus},
		#{enterpriseID},
		#{enterpriseName},
		#{servType},
		#{effictiveTime},
		#{expireTime},
		#{createTime},
		#{operatorID},
		#{lastUpdateTime},
		#{isExperience},		
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10}
		)
	</insert>
	
	<update id="updateOrderByID">
		UPDATE ecpe_t_order SET
		<trim suffixOverrides="," suffix="where ID = #{id}">
			<if test="thirdPartyOrderID!=null and thirdPartyOrderID!=''">
				thirdPartyOrderID = #{thirdPartyOrderID},
			</if>
			<if test="isExperience!=null">
				isExperience= #{isExperience},
			</if>
			<if test="orderType!=null">
				orderType = #{orderType},
			</if>
			<if test="ownerOrderID!=null and ownerOrderID!=''">
				ownerOrderID = #{ownerOrderID},
			</if>
			<if test="transferAttachURL!=null  and transferAttachURL!=''">
				transferAttachURL = #{transferAttachURL}, 
			</if>
			<if test="orderDetailURL!=null  and orderDetailURL!=''">
				orderDetailURL = #{orderDetailURL}, 
			</if>
			<if test="orderName!=null  and orderName!=''">
				orderName = #{orderName}, 
			</if>
			<if test="amount!=null">
				amount = #{amount},
			</if>
			<if test="currency!=null  and currency!=''">
				currency = #{currency}, 
			</if>
			<if test="payChannel!=null">
				payChannel = #{payChannel},
			</if>
			<if test="payStatus!=null">
				payStatus = #{payStatus},
			</if>
			<if test="enterpriseID!=null">
				enterpriseID = #{enterpriseID},
			</if>
			<if test="enterpriseName!=null  and enterpriseName!=''">
				enterpriseName = #{enterpriseName},
			</if>
			<if test="servType!=null">
				servType = #{servType},
			</if>
			<if test="effictiveTime!=null">
            	effictiveTime = #{effictiveTime},
            </if>
            <if test="expireTime!=null">
            	expireTime = #{expireTime},
            </if>
            <if test="createTime!=null">
            	createTime = #{createTime},
            </if>
			<if test="operatorID!=null">
				operatorID = #{operatorID},
			</if>
            <if test="lastUpdateTime!=null">
            	lastUpdateTime = #{lastUpdateTime},
            </if>
            <if test="extInfo!=null and extInfo!=''">extInfo= #{extInfo},</if>
            <if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
            <if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
            <if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
            <if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
            <if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
            <if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
            <if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
            <if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
            <if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
            <if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
		</trim> 
	</update>
	
	<update id="updateOrderByCode">
		UPDATE ecpe_t_order SET
		<trim suffixOverrides="," suffix="where orderCode = #{orderCode}">
			<if test="thirdPartyOrderID!=null and thirdPartyOrderID!=''">
				thirdPartyOrderID = #{thirdPartyOrderID},
			</if>
			<if test="isExperience!=null">
				isExperience= #{isExperience},
			</if>
			<if test="orderType!=null">
				orderType = #{orderType},
			</if>
			<if test="ownerOrderID!=null and ownerOrderID!=''">
				ownerOrderID = #{ownerOrderID},
			</if>
			<if test="transferAttachURL!=null  and transferAttachURL!=''">
				transferAttachURL = #{transferAttachURL}, 
			</if>
			<if test="orderDetailURL!=null  and orderDetailURL!=''">
				orderDetailURL = #{orderDetailURL}, 
			</if>
			<if test="orderName!=null  and orderName!=''">
				orderName = #{orderName}, 
			</if>
			<if test="amount!=null">
				amount = #{amount},
			</if>
			<if test="currency!=null  and currency!=''">
				currency = #{currency}, 
			</if>
			<if test="payChannel!=null">
				payChannel = #{payChannel},
			</if>
			<if test="payStatus!=null">
				payStatus = #{payStatus},
			</if>
			<if test="enterpriseID!=null">
				enterpriseID = #{enterpriseID},
			</if>
			<if test="enterpriseName!=null  and enterpriseName!=''">
				enterpriseName = #{enterpriseName},
			</if>
			<if test="servType!=null">
				servType = #{servType},
			</if>
			<if test="effictiveTime!=null">
            	effictiveTime = #{effictiveTime},
            </if>
            <if test="expireTime!=null">
            	expireTime = #{expireTime},
            </if>
            <if test="createTime!=null">
            	createTime = #{createTime},
            </if>
			<if test="operatorID!=null">
				operatorID = #{operatorID},
			</if>
            <if test="lastUpdateTime!=null">
            	lastUpdateTime = #{lastUpdateTime},
            </if>
            <if test="extInfo!=null and extInfo!=''">extInfo= #{extInfo},</if>
            <if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
            <if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
            <if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
            <if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
            <if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
            <if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
            <if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
            <if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
            <if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
            <if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
		</trim> 
	</update>
	
	<select id="queryAdditionOrderByOwnerID" parameterType="java.lang.String" resultType="java.lang.String">
	     select	orderCode from ecpe_t_order where ownerOrderID=#{orderID}
    </select>

	<select id="queryOrderByOrderCode" resultType="java.lang.Integer">
		SELECT
			o.ID
		FROM
			ecpe_t_order o
		LEFT JOIN ecpe_t_orderitem oi ON o.id = oi.orderID
		WHERE
		o.orderCode IN (
		<foreach collection="orderIDList" item="orderID" separator="," index="index">
			#{orderID}
		</foreach>
		)
		AND oi.ID is NULL;
	</select>

	<!--查询订单主项列表 -->
	<select id="queryefftiveOrderList" resultMap="OrderWrapper">
		select
		ID,
		orderCode,
		thirdPartyOrderID,
		isExperience,
		orderType,
		ownerOrderID,
		transferAttachURL,
		orderDetailURL,
		orderName,
		amount,
		currency,
		payChannel,
		payStatus,
		enterpriseID,
		enterpriseName,
		servType,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_order
		<trim prefix="where" prefixOverrides="and|or">
			<if test="id!=null">
				and ID = #{id}
			</if>
			<if test="isExperience!=null">
				and isExperience = #{isExperience}
			</if>
			<if test="orderType!=null">
				and orderType = #{orderType}
			</if>
			<if test="orderCode!=null  and orderCode!=''">
				and orderCode = #{orderCode}
			</if>
			<if test="thirdPartyOrderID!=null and thirdPartyOrderID!=''">
				and thirdPartyOrderID = #{thirdPartyOrderID}
			</if>
			<if test="ownerOrderID!=null and ownerOrderID!=''">
				and ownerOrderID = #{ownerOrderID}
			</if>
			<if test="orderName!=null  and orderName!=''">
				and orderName like concat("%", #{orderName}, "%") 
			</if>
			<if test="enterpriseID!=null">
				and enterpriseID = #{enterpriseID}
			</if>
			<if test="enterpriseName!=null  and enterpriseName!=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="servType!=null">
				and servType = #{servType}
			</if>
			<if test="enterpriseType!=null">
				and reserved1 = #{enterpriseType}
			</if>
			<if test="status!=null and status == 3">
				and expireTime <![CDATA[ >= ]]> now()
			</if>
			<if test="payStatus!=null">
				and payStatus = #{payStatus}
			</if>
		</trim>
		order by createTime desc
	</select>
</mapper>