var app = angular.module("myApp", ["util.ajax", "angularI18n", "service.common","preview"]);
app.controller('activityDetailCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
  $scope.init = function () {
    $scope.signature = "";
    $scope.industryType = "";
    //判断用户角色
    $scope.isSuperManager = false;
    $scope.isZhike = false;
    $scope.isAgent = false;
    var loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
    $scope.isZhike = (loginRoleType == 'zhike');
    $scope.isAgent = (loginRoleType == 'agent');

    //超管进直客为1，超管进二级企业为3
    $scope.type = $location.search().type;


    $scope.activityID = $location.search().activityID;
    $scope.queryActivity();
    var isIE=false;
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("Opera") > -1; //判断是否Opera浏览器
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //判断是否IE浏览器
    var isEdge = userAgent.indexOf("Windows NT 6.1") > -1 && userAgent.indexOf("Trident/7.0") > -1 && !isIE; //判断是否IE的Edge浏览器
    if(isIE||isEdge){
       $(function(){
         $('.actInfo').addClass('actInfoIE');
       })
    }
    //查询企业服务开关,当为add时初始化运营商选项按钮和勾选状态
    //$scope.queryPlatformStatus();

  };

//查询所属行业
      $scope.queryIndustry = function ($scope) {
        //默认非敏感行业:0-非敏感；1-敏感
        $scope.selectedIndustryID = '';
        $scope.isSensitiveIndustry = '';
        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
          success: function (data) {
            $rootScope.$apply(function () {
              var result = data.result;
              if (result.resultCode == '1030100000') {
                  $scope.industryList = data.industryList;
                  let typeArr = $scope.industryList.filter((item)=> item.industryID == $scope.industryType)
                  $scope.selectedIndustry = typeArr[0]
                  console.log('88888', typeArr)
              }else {
                  $scope.tip=data.result.resultCode;
                  $('#myModal').modal();
                }
            })
          },
          error:function(){
              $rootScope.$apply(function(){
                      $scope.tip = '1030120500';
                      $('#myModal').modal();
                  }
              )
          }
        });
      };

  $scope.queryActivity = function () {
    var req = {
      "activityID": $scope.activityID
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/activityService/queryActivity",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            // 获取行业
            $scope.queryIndustry($scope)
            $scope.activityInfo = result.activityInfo;
            var effectivetime = CommonUtils.dateTimeFormate($scope.activityInfo.effectivetime);
            $scope.activityInfo.effectivetime = effectivetime.year + "." + effectivetime.month + "." + effectivetime.day;
            var expiretime = CommonUtils.dateTimeFormate($scope.activityInfo.expiretime);
            $scope.activityInfo.expiretime = expiretime.year + "." + expiretime.month + "." + expiretime.day;
            $scope.bannerList = [];
            angular.forEach($scope.activityInfo.bannerList, function (pic) {
              var picObj = CommonUtils.formatPic(pic.bannerURL);
              $scope.bannerList.push(picObj);
            });
            var url = $scope.activityInfo.backLogoURL;
            $scope.backgroundURL = CommonUtils.formatPic(url);
            $scope.contentList = $scope.activityInfo.contentList;
            var contentPlat = $scope.contentList[0].platforms;
            for(var i = 0;i<3;i++){
                if(contentPlat.charAt(i)=='1'){
                    //初始化勾选状态
                    $(".platforms .check-li").find('span').eq(i).addClass('checked')
                }
            }


            if (angular.isArray($scope.contentList) && $scope.contentList.length > 0) {
              angular.forEach($scope.contentList, function (item) {
              $scope.industryType = item.industryType;
              $scope.signature = item.signature;
              if(item.businessLicenseURL){
              $scope.urlList_ = [CommonUtils.formatPic(item.businessLicenseURL)];
              } else {
              $scope.urlList_ = [];
              }
              //$scope.urlList_ = [CommonUtils.formatPic(item.businessLicenseURL)];
              console.log("urlList_:"+$scope.urlList_);
                if (item.subServType === 2) {
                  $scope.pxcontent = item.content;
                  $scope.pxpushInterval = item.pushInterval;//每天对同一用户的发送间隔;
                  $scope.pxmaxPushPerDay = item.maxPushPerDay;//每天对同一用户的发送限制
                }
                if (item.subServType === 8) {
                  $scope.contentTitle = item.contentTitle;
                  $scope.gjpushInterval = item.pushInterval;//每天对同一用户的发送间隔;
                  $scope.gjmaxPushPerDay = item.maxPushPerDay;//每天对同一用户的发送限制
                  $scope.gjcontent = "";
                  $scope.gjcxList = [];
                  angular.forEach(item.contentFrameMappingList, function (pic) {
                    //图片
                    if (pic.frameType == 1)
                    {
                      var picObj = CommonUtils.formatPic(pic.framePicUrl);
                      $scope.gjcxList.push(picObj);
                    }
                    else (pic.frameType == 2)
                    {
                      $scope.gjcontent = pic.frameTxt;
                    }
                  })

                }
              })
            }

            if ($scope.activityInfo.createTime == null || $scope.activityInfo.createTime == "")
            {
              $scope.createTime = "";
            }
            else
            {
              $scope.createTime = $scope.activityInfo.createTime.slice(0, 4) + "-" + $scope.activityInfo.createTime.slice(4, 6) + "-" + $scope.activityInfo.createTime.slice(6, 8) + " " + $scope.activityInfo.createTime.slice(8, 10) + ":" + $scope.activityInfo.createTime.slice(10, 12);
            }

            $scope.cityList = $scope.activityInfo.cityList;
            $scope.provinceList=$scope.activityInfo.proviceList||[];
            $scope.citynameAll = "";
            $scope.provinceNameAll="";
            angular.forEach($scope.provinceList, function (item) {
              $scope.provinceNameAll = $scope.provinceNameAll + "，" + item.provinceName;
            });
            $scope.provinceNameAll = $scope.provinceNameAll.substr(1);
            angular.forEach($scope.cityList, function (item) {
              $scope.citynameAll = $scope.citynameAll + "，" + item.cityName;
            });
            if($scope.activityInfo.maxPushNum == "null")
            {
              $scope.activityInfo.maxPushNum = "";
            }
            if($scope.activityInfo.totalNum == "null")
            {
              $scope.activityInfo.totalNum = "";
            }
            if($scope.provinceNameAll){
              $scope.citynameAll = $scope.provinceNameAll+$scope.citynameAll;
            }else{
              $scope.citynameAll=$scope.citynameAll.substr(1);
            }
            if ($scope.activityInfo.template) {
              $scope.templateIconURL = [$scope.activityInfo.template.templateIconURL];
            }
            $scope.activityQrCode = CommonUtils.formatPic($scope.activityInfo.activityQrCode);
          } else {
            $scope.activityInfo = {};
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
        )
      }
    });
  }

$scope.checkSignatureRequired = function(){
     $scope.signatureRequired = $scope.signatureRequired;
  }

  $scope.checkSignature = function () {
        var platforms = $scope.platforms;
        //未勾选异网
        if(platforms.charAt(1)=='0'&&platforms.charAt(2)=='0'){
            return false;
        }
        //签名必填
        var obj = $scope.signature;
        if (obj == "undefined" || obj == null || obj == "") {
           return true;
        }else if(obj.length>10){
           $scope.signatureErrorInfo = "SIGNATURE_MAXLENTH_10";
           return true;
        }
        return false;
    }

  $scope.goBack = function () {
    //超管进直客为1，超管进二级企业为3
    if ($scope.type == 1)
    {
      window.location = "../../../cooperationManage/zhikeManage/advertisePrint/list/advertisePrint_list.html";
      return;
    }
    window.location = "../merchantActivityAudit.html"
  }

}])
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])