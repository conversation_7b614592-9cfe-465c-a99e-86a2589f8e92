.form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0;
    text-align: right;
}
.container-fluid{
    padding-left: 0;padding-right: 0;
}
body {
    background: #f2f2f2;
}
.table th{
    width:12.5%;
    min-width: 100px;
}
table{
    table-layout:fixed;
}
table td{
    width:100%;
    word-break:keep-all;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}
.modal-footer{
    text-align: center;
}
.fontGreen{
    color: rgb(48, 147,25)
}
.fontRed{
    color:rgb(252,70,93);
}
.cooperation-head {
    padding: 20px;
}

.cooperation-head .frist-tab {
    font-size: 16px;
}

.cooperation-head .second-tab {
    font-size: 14px;
}
.cooperation-nav{
    margin-bottom: 15px;
    margin-left: 20px;
}
.cooperation-manage{
    min-width: 1024px;
}
.cooperation-manage .form-inline {
    margin: 0 20px;
    background: #fff;
    border-radius: 4px;
    padding: 20px 0px 20px 10px;
    overflow: hidden;
}
.form-inline .form-control{
    width: 100%;
}

.cooperation-manage label {
    font-weight: normal;
}

.form-group input {
    /* margin-left: 20px; */
}
.form-control:focus {
    border-color: #7360e1;
}
.form-group label{
    height: 34px;
    margin-bottom: 0
}
.cooperation-manage .form-group select{
    /* width: 180px;
    margin-left: 20px; */
}
.cooperation-manage .search-btn {
    background-image: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
    color: #fff;
}

.cooperation-manage .btn .search-iocn {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(../assets/images/btnIcons.png)no-repeat;
    vertical-align: middle;
}

.cooperation-manage .add-table {
    margin: 20px;
}

.cooperation-manage .add-table .add-btn {
    background: #fff;
    color: #7360e1;
}

.cooperation-manage .add-table .add-btn .add-iocn {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(../../../../../assets/images/export.png)no-repeat;
    vertical-align: middle;
    background-position: 0 -2px;
}

.coorPeration-table {
    margin: 0px 20px;
    background: #fff;
    min-width: 850px;
}
.cooperation-manage .coorPeration-table th,td{
    padding-left: 30px !important;
}

.handle {
    overflow: hidden;
}

.handle ul li {
    display: inline-block;
    margin-right: 20px;
    cursor: pointer;
}

.handle ul li.query {
    color: #7360e2;
}
.handle ul li.edit {
    color: #7360e2;
}

.handle ul li.delete {
    color: #ff2549;
}

.handle ul li.set {
    color: #7360e2;
}

.handle ul li icon {
    width: 20px;
    height: 20px;
    display: inline-block;
    background: url(../../../../../assets/images/sideNavIcons.png)no-repeat;
    vertical-align: bottom;
}

.handle ul li icon.query-icon {
    background-position: -50px -25px;
}
.handle ul li icon.edit-icon {
    background-position: 0 0;
}

.handle ul li icon.delete-icon {
    background-position: -18px 0;
}

.handle ul li icon.set-icon {
    background-position: -38px 0;
}
.table-tab{
    float: right;
    margin: 0 20px;
}

.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
    border: none;
    padding: 12px 8px;
}
.table-striped>tbody>tr:nth-child(odd){
    background-color:#f2f2f2;
}