<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.PackageExclusiveProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.PackageExclusiveProduct">
        <id column="ID" property="id" />
        <result column="packageID" property="packageID" />
        <result column="exclusivePackageID" property="exclusivePackageID" />
        <result column="createTime" property="createTime" />
        <result column="reserved1" property="reserved1" />
        <result column="reserved2" property="reserved2" />
        <result column="reserved3" property="reserved3" />
        <result column="reserved4" property="reserved4" />
        <result column="reserved5" property="reserved5" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, packageID, exclusiveProductID, createTime, reserved1, reserved2, reserved3, reserved4, reserved5
    </sql>
    <select id="queryExclusiveByEnterpriseID" resultMap="BaseResultMap">
        ( SELECT
        t2.ID, t2.packageID, t2.exclusivePackageID, t2.createTime, t2.reserved1, t2.reserved2, t2.reserved3, t2.reserved4, t2.reserved5
        FROM
        ecpe_t_package_exclusive_product t2 left join  ecpe_t_package_order t1
        on t1.packageCode = t2.packageID
        where t1.enterpriseID = #{enterpriseID} and t1.status = 0 and t1.expireTime <![CDATA[ > ]]> now()
        )
        union
        (
        SELECT
        t2.ID, t2.packageID, t2.exclusivePackageID, t2.createTime, t2.reserved1, t2.reserved2, t2.reserved3, t2.reserved4, t2.reserved5
        FROM
        ecpe_t_package_exclusive_product t2 left join  ecpe_t_package_order t1
        on t1.packageCode = t2.exclusivePackageID
        where t1.enterpriseID = #{enterpriseID} and t1.status = 0 and t1.expireTime <![CDATA[ > ]]> now() )
    </select>
</mapper>