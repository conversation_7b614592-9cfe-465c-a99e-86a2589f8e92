var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils',function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否超管
        $scope.isSuperManager = false;
        $scope.isProvincial = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isProvincial = (loginRoleType == 'provincial');

        if ($scope.isProvincial)
        {
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
        }

        //默认业务类别为：不限
        $scope.serviceType = "";

        $scope.contentID = "";

        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "请选择"
            },
            {
                id: 1,
                name: "名片彩印/热线彩印省份版"
            },
            {
                id: 2,
                name: "热线彩印/企业通知"
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            startTime: "",
            endTime: "",
            search: false,
        };

        $scope.exportFile=function(){
            var req = {
              "param":{
                "contentID":$scope.contentID,
                "dataType":($scope.serviceType ===2) ? 1 :2,
                "startTime":$scope.setTime($scope.initSel.startTime),
                "endTime":$scope.setTime($scope.initSel.endTime),
                "enterpriseType": 5,
                "areaDimension":3,
                "timeDimension":2,
                "token":$scope.token,
                "isExport":1
              },
              "url":"/qycy/ecpmp/ecpmpServices/contentService/downtemplateStatistics",
              "method":"get"
            }
            CommonUtils.exportFile(req);
        }

    }


    $scope.getServiceType = function (serviceType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 4) {
            return "企业通知";
        }
        else if (serviceType == 5) {
            return "热线彩印省份版";
        }
    };


    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true
    });

    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $scope.showTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        return year + "-" + month + "-" + day;
    }

    $scope.setTime = function (time) {
        if (time === "")
        {
            return "";
        }
        return time;
    }

    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;
        if (startTime !== '')
        {
            $scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
        }
        else
        {
            $scope.initSel.startTime = "";
        }
        if (endTime !== '')
        {
            $scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
        }
        else
        {
            $scope.initSel.endTime = "";
        }
        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }
    }

    $scope.queryDeliveryStatByContent = function (condition) {
        if(!$scope.contentID){
            $scope.tip = "模板ID为空";
            $('#myModal').modal();
        }
        else if(!$scope.serviceType){
             $scope.tip = "业务类型为空";
             $('#myModal').modal();
        }
        else if ($scope.contentID && !$scope.contentID.match(/^[0-9,\,]*$/))
        {
        	$scope.tip = "模板ID输入格式错误";
            $('#myModal').modal();
        }
        else {
            if (condition != 'justPage') {
                var req = {
                    "areaDimension": 3,
                    "timeDimension": 2,
                    "contentID": $scope.contentID,
                    "dataType": ($scope.serviceType ===2) ? 1 :2,
                    "enterpriseType": 5,
                    "startTime": $scope.setTime($scope.initSel.startTime),
                    "endTime": $scope.setTime($scope.initSel.endTime)
                };
            }

            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/queryDeliveryStatByContent",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if(data.resultCode==='1010100000'){
                            $scope.contentDeliveryStatList = result.contentDeliveryStatList||[];
                        }
                        else
                        {
                            $scope.tip = data.resultDesc;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                            $scope.tip = data.resultDesc;
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }
    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])