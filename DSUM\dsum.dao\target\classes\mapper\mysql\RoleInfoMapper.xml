<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.RoleInfoMapper">
	<resultMap id="roleInfoModel" type="com.huawei.jaguar.dsum.dao.domain.RoleInfoWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="roleCode" column="roleCode" javaType="java.lang.String" />
		<result property="roleName" column="roleName" javaType="java.lang.String" />
		<result property="roleDesc" column="roleDesc" javaType="java.lang.String" />
		<result property="roleType" column="roleType" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="lastUpdateTime" column="lastUpdateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>

	<select id="getRoleInfoByRoleID" resultMap="roleInfoModel">
			select
			<include refid="roleInfoColumn"/>
			 from dsum_t_role t where t.id in
			<foreach collection="list" item="roleID" separator="," open="(" close=")">
				#{roleID}
			</foreach>
    </select>
    
    
    <select id="getRoleInfo" resultMap="roleInfoModel">
			select
				t.id,
				t.roleCode,
				t.roleName,
				t.roleDesc,
				t.roleType,
				t.createTime,
				t.lastUpdateTime,
				t.operatorID,
				t.extInfo,
				t.reserved1,
				t.reserved2,
				t.reserved3,
				t.reserved4,
				t.reserved5,
				t.reserved6,
				t.reserved7,
				t.reserved8,
				t.reserved9,
				t.reserved10 from
				dsum_t_role t
				where t.isDisplay = 1
		<if test="roleIDList != null and roleIDList.size()>0">
			and t.id in
			<foreach item="id" index="index" collection="roleIDList"
				open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="roleName !=null  and roleName !=''">
			and t.roleName like concat("%", #{roleName}, "%")
		</if>
			order by  t.createTime DESC
		<if test="pageNum !=null and pageSize !=null ">
			limit #{pageNum},#{pageSize}
		</if>
    </select>
    
    
    <select id="getRoleInfoNum" resultType="java.lang.Long">
		select count(0) from
			dsum_t_role t
			where t.isDisplay = 1
			<if test="roleIDList != null and roleIDList.size()>0">
				and t.id in
				<foreach item="id" index="index" collection="roleIDList"
					open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
			<if test="roleName !=null  and roleName !=''">
				and t.roleName like concat("%", #{roleName}, "%")
			</if>
    </select>
    
	
	<update id="updateRoleInfo">
		update dsum_t_role set
		<trim suffixOverrides="," suffix="where id = #{id}">
            <if test="roleCode!=null and roleCode!=''">roleCode= #{roleCode},</if>
            <if test="roleName!=null and roleName!=''">roleName= #{roleName},</if>
            <if test="roleDesc!=null">roleDesc= #{roleDesc},</if>
            <if test="lastUpdateTime!=null">lastUpdateTime= #{lastUpdateTime},</if>
            <if test="operatorID!=null">operatorID= #{operatorID},</if>
            isDisplay= 1,
            <if test="extInfo!=null">extInfo= #{extInfo},</if>
            <if test="reserved1!=null">reserved1= #{reserved1},</if>
            <if test="reserved2!=null">reserved2= #{reserved2},</if>
            <if test="reserved3!=null">reserved3= #{reserved3},</if>
            <if test="reserved4!=null">reserved4= #{reserved4},</if>
            <if test="reserved5!=null">reserved5= #{reserved5},</if>
            <if test="reserved6!=null">reserved6= #{reserved6},</if>
            <if test="reserved7!=null">reserved7= #{reserved7},</if>
            <if test="reserved8!=null">reserved8= #{reserved8},</if>
            <if test="reserved9!=null">reserved9= #{reserved9},</if>
            <if test="reserved10!=null">reserved10= #{reserved10},</if>
        </trim>
	</update>
	
	<delete id="deleteRoleInfoByRoleID" parameterType="java.util.List">
		delete from dsum_t_role where id in
		<foreach collection="list" item="roleIDs" index="no" open="("
			separator="," close=")">
			#{roleIDs}
		</foreach>
	</delete>
	
	<select id="getRoleInfoID" resultType="java.lang.Integer">
		select
		nextval('dsum_sequence_role');
	</select>
	
	<insert id="createRoleInfo">
    INSERT INTO dsum_t_role
		(id,
		roleCode,
		roleName,
		roleDesc,
		createtime,
		lastupdatetime,
		operatorID,
		isDisplay,
		roleType,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10)
		VALUES
		(
		#{id},
		#{roleCode},
		#{roleName},
		#{roleDesc},
		#{createTime},
		#{lastUpdateTime},
		#{operatorID},
		1,
		1,
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10}
		)
	</insert>
	
	
	<select id="queryRoleInfoByRoleName" resultMap="roleInfoModel">
			select
				t.id,
				t.roleCode,
				t.roleName,
				t.roleDesc,
				t.roleType,
				t.createTime,
				t.lastUpdateTime,
				t.operatorID,
				t.extInfo,
				t.reserved1,
				t.reserved2,
				t.reserved3,
				t.reserved4,
				t.reserved5,
				t.reserved6,
				t.reserved7,
				t.reserved8,
				t.reserved9,
				t.reserved10 from
				dsum_t_role t
			<trim prefix="where" prefixOverrides="and|or">
			<if test="id != null">
				and t.id != #{id}
			</if>
			<if test="roleName !=null  and roleName !=''">
				and t.roleName = #{roleName}
			</if>
			</trim>
    </select>
    
    <sql id="roleInfoColumn">
        t.id,
		t.roleCode,
		t.roleName,
		t.roleDesc,
		t.roleType,
		t.createTime,
		t.lastUpdateTime,
		t.operatorID,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10
    </sql>
    
    
    <select id="queryRoleInfo" resultMap="roleInfoModel">
		select
		<include refid="roleInfoColumn"/> 
		from dsum_t_role t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="roleIDList != null and roleIDList.size()>0">
				and t.id in
				<foreach item="id" index="index" collection="roleIDList"
					open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
			<if test="roleName !=null  and roleName !=''">
				and t.roleName like concat("%", #{roleName}, "%")
			</if>
		</trim>
		order by  t.createTime DESC
		<if test="pageNum !=null and pageSize !=null ">
			limit #{pageNum},#{pageSize}
		</if>
    </select>
    
</mapper>