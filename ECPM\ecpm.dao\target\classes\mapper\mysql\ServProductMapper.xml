<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ServProductMapper">

	<resultMap id="servProductWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ServProductWrapper">
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="productCode" column="productCode" javaType="java.lang.String" />
	</resultMap>


	<select id="queryProductCodeOrgID" resultType="java.lang.String">
		select t4.productCode from
		(
		SELECT t2.productCode,t2.servType
		FROM ecpm_t_content_org t, ecpm_t_content t1,ecpm_t_serv_product
		t2,ecpm_t_org_rel t3
		<trim prefix="where" prefixOverrides="and|or">
			t.cyContID = t1.ID AND t.ownerType = t1.servType AND t1.servType =
			t2.servType
			and t.ownerID = t3.orgID
			<if test="orgID != null">
				and t.ownerID = #{orgID}
			</if>
			<if test="orgCodeList != null and orgCodeList.size()>0">
				and t.orgCode in
				<foreach item="orgCode" index="index" collection="orgCodeList"
					open="(" separator="," close=")">
					#{orgCode}
				</foreach>
			</if>
		</trim>
		)t4
		group by t4.servType,t4.productCode
	</select>
	<select id="queryProductCode" resultType="java.lang.String">
	   select productCode from ecpm_t_serv_product
	</select>
	
	<select id="queryServTypeByProductCode" resultType="java.lang.Integer">
		SELECT servType
			FROM ecpm_t_serv_product
			where productCode = #{productCode}
	</select>
	
	<select id="queryProductCodeByServType" resultType="java.lang.String">
		SELECT productCode
			FROM ecpm_t_serv_product
			where servType = #{servType} and reserved2 is null
	</select>
	
	<select id="queryProductNameByServType" resultType="java.lang.String">
		SELECT reserved1
			FROM ecpm_t_serv_product
			where servType = #{servType} and reserved2 is null
	</select>
</mapper>