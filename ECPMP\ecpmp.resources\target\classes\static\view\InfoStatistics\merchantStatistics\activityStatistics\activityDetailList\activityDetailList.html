<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title></title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="activityDetailListCtrl.js"></script>

	<style>
		.cooperation-manage .coorPeration-table th, td {
			padding-left: 30px !important;
		}
	</style>
</head>
<body ng-app='myApp' style="min-width:1024px;" ng-controller='activityDetailListCtrl' ng-init="init();">
	<div class="cooperation-manage">
		<div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_MERCHANTSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span
						class="second-tab" ng-bind="'COMMON_WATCHDETAIL'|translate"></span></div>
		<top:menu chose-index="0" list-index="16"
							page-url="/qycy/ecpmp/view/InfoStatistics/merchantStatistics/activityStatistics/activityDetailList"></top:menu>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="activityName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"
								 style="white-space:nowrap" ng-bind="'COMMON_ACTIVITYNAME'|translate"></label>

					<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" style="min-width: 300px;max-width: 300px">
						<input type="text" id="activityName" class="form-control"
									 placeholder="{{'COMMON_PLEASEINPUTACTIVITYMANAGE'|translate}}" ng-model="activityName">
					</div>

					<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
						<button type="submit" class="btn search-btn" ng-click="queryAcitivityStatList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="exportList()">
				<icon class="export-icon"></icon>
				<span ng-bind="'COMMON_EXPORT'|translate"></span>
			</button>
		</div>
		<p style="font-size: 16px;padding: 0 0 20px 20px;"
			 ng-bind="'COMMON_ALL_ACVIVITIES'|translate"></p>

		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th width="20%" ng-bind="'COMMON_ACTIVITYNAME'|translate"></th>
					<th width="20%" ng-bind="'COMMON_EXPIRYDATE'|translate"></th>
					<th width="20%" ng-bind="'ACTIVITY_SPOKEMANNUM'|translate"></th>
					<th width="20%" ng-bind="'ACTIVITY_PXNUM'|translate"></th>
					<th width="20%" ng-bind="'ACTIVITY_GJNUM'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in activityStatList">
					<td title="{{item.activityName}}">{{item.activityName}}</td>
					<td title="{{item.startDate|newDate}} - {{item.endDate|newDate}}">{{item.startDate|newDate}} -
						{{item.endDate|newDate}}
					</td>
					<td title="{{item.screenCount}}">{{item.spokeCount}}</td>
					<td title="{{item.screenCount}}">{{item.screenCount}}</td>
					<td title="{{item.endPhoneCount}}">{{item.endPhoneCount}}</td>
				</tr>
				<tr ng-show="activityStatList===null||activityStatList.length<=0">
					<td style="text-align:center" colspan="7" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<button type="submit" class="btn btn-back" ng-click="goBack()" style="margin: 20px;border: 1px solid #cccccc"
							ng-bind="'COMMON_BACK'|translate"></button>
			<ptl-page tableId="0" change="queryAcitivityStatList('justPage')"></ptl-page>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{tip|translate}}
					</p></div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</body>
</html>