var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n", "service.common", "top.menu"])
app.controller('advertisePrintListCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {

    $scope.init = function () {
        $scope.enterpriseType = $.cookie("enterpriseType");
        $scope.enterpriseID = $.cookie("enterpriseID");
        $scope.subEnterpriseID = $.cookie("subEnterpriseID");
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.isZhike = ($scope.loginRoleType == 'zhike');
        $scope.isAgent = ($scope.loginRoleType == 'agent');
        //直客
        if ($scope.enterpriseType === "1" || $scope.isZhike) {
            $scope.eId = $scope.enterpriseID;
        }
        //二级企业
        if ($scope.enterpriseType === "3" || $scope.isAgent) {
            $scope.eId = $scope.subEnterpriseID;
        }
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        $scope.auditStatusList = [
            {"key": "", "value": "不限"},
            {"key": "1", "value": "审核中"},
            {"key": "2", "value": "审核通过"},
            {"key": "3", "value": "审核驳回"}
        ];
        $scope.processStatusList = [
            {"key": "", "value": "不限"},
            {"key": "1", "value": "未开始"},
            {"key": "2", "value": "进行中"},
            {"key": "3", "value": "已结束"}
        ];
        $scope.enterpriseName = "";
        $scope.activityName = "";
        $scope.auditStatus = "";
        $scope.processStatus = "";

        //默认业务开关是开启状态
        $scope.switchState = 1;
        $scope.getActivityList();
    };


    //计算两个日期的天数差
    $scope.datedifference = function (strDateStart, strDateEnd) {    //sDate1和sDate2是2006-12-18格式
        var strSeparator = "-"; //日期分隔符
        var oDate1;
        var oDate2;
        var iDays;
        oDate1 = strDateStart.split(strSeparator);
        oDate2 = strDateEnd.split(strSeparator);
        var strDateS = new Date(oDate1[0], oDate1[1] - 1, oDate1[2]);
        var strDateE = new Date(oDate2[0], oDate2[1] - 1, oDate2[2]);
        iDays = parseInt(Math.abs(strDateS - strDateE) / 1000 / 60 / 60 / 24);//把相差的毫秒数转换为天数
        return iDays + 1;
    };

    $scope.getActivityList = function (condition) {
        var req;
        if (condition != 'justPage') {
            req = {
                "isExamine": "1",
                "activityInfoCond": {
                    "activityName": $scope.activityName,
                    "auditStatus": $scope.auditStatus,
                    "processStatus": $scope.processStatus,
                    "enterpriseID": $scope.eId,
                    "sortType": "2",
                    "sortField": "2"
                },
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/activityService/queryActivityList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.activityInfoList = result.activityInfoList;

                        //如果有数据，就赋值开关状态
                        //广告所有内容默认由一个开关控制
                        if ($scope.activityInfoList){
                            $scope.switchState=$scope.activityInfoList[0].switchState;

                            console.log('switchState:'+$scope.switchState)
                        }

                        angular.forEach($scope.activityInfoList, function (item) {
                            item.allCity = "";
                            //代言天数的日期生成
                            var myDate = new Date();
                            var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
                            var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
                            var date = myDate.getDate();        //获取当前日(1-31)
                            var today = year + "-" + month + "-" + date;
                            //当前时间大于活动结束时间
                            if (item.expiretime && item.effectivetime && (myDate.getTime() > item.expiretime)) {
                                var effectivetime = CommonUtils.dateTimeFormate(item.effectivetime);
                                item.effectivetime = effectivetime.year + "-" + effectivetime.month + "-" + effectivetime.day;
                                var expiretime = CommonUtils.dateTimeFormate(item.expiretime);
                                item.expiretime = expiretime.year + "-" + expiretime.month + "-" + expiretime.day;

                                item.spokeDayNum = $scope.datedifference(item.effectivetime, item.expiretime);
                            }//当前时间小于活动开始时间
                            else if (item.effectivetime && (myDate.getTime() < item.effectivetime)) {
                                item.spokeDayNum = 0;
                            }
                            else if (item.expiretime && item.effectivetime) {
                                var effectivetime = CommonUtils.dateTimeFormate(item.effectivetime);
                                item.effectivetime = effectivetime.year + "-" + effectivetime.month + "-" + effectivetime.day;
                                item.spokeDayNum = $scope.datedifference(item.effectivetime, today);
                            }

                            if (item.effectivetime) {
                                var effectivetime = CommonUtils.dateTimeFormate(item.effectivetime);
                                item.effectivetime = effectivetime.year + "-" + effectivetime.month + "-" + effectivetime.day;
                            }
                            if (item.expiretime) {
                                var expiretime = CommonUtils.dateTimeFormate(item.expiretime);
                                item.expiretime = expiretime.year + "-" + expiretime.month + "-" + expiretime.day;
                            }

                            angular.forEach(item.cityList, function (city) {
                                item.allCity = item.allCity + city.cityName + "\\"
                            });
                            item.allCity = item.allCity.substr(0, item.allCity.length - 1)


                        });
                        $scope.pageInfo[0].totalCount = parseInt(result.totalcount) || 0;
                        $scope.pageInfo[0].totalPage = Math.ceil($scope.pageInfo[0].totalCount / parseInt($scope.pageInfo[0].pageSize));
                        if ($scope.pageInfo[0].totalPage === 0) {
                            $scope.pageInfo[0].totalPage = 1;
                        }
                    } else {
                        $scope.activityInfoList = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    $scope.gotoAdd = function () {
        if ($scope.switchState&&$scope.switchState===1) {

            window.location = "../create/advertisePrint_create.html";

        }else {
            $('#deleteAdvertiseCancel').click();
            $scope.tip = "1030120083";
            $('#myModal').modal();
        }
    }

    $scope.toDetail = function (item) {
        window.location = "/qycy/ecpmp/view/InfoAudit/merchantActivityAudit/activityDetail/activityDetail.html?activityID=" + item.activityID + "&type=1";
    }

    //删除广告弹窗
    $scope.deleteBusinessCardContent = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteAdvertise').modal();
    };

    //删除广告内容
    $scope.delAdvertise = function () {
        var item = $scope.selectedItemDel;
        console.log(item);
        var removeReq = {
            "operaterType": "3",       //1名片，2热线，3广告
            "id": "0",               //该值针对广告无效,任意值使其通过非空校验
            "activityID": item.activityID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $('#deleteAdvertiseCancel').click();
                        $scope.getActivityList();
                    } else {
                        $('#deleteAdvertiseCancel').click();
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteAdvertiseCancel').click();
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    }

    //暂停广告内容
    $scope.suspendAdvertise = function (item, operaterType) {
        console.log(item);
        var removeReq = {
            "operaterType": operaterType,   //操作类型：0启动，1暂停
            "servType": "3",
            "activityID": item.activityID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/suspendActivity",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1030100000') {
                        $scope.getActivityList();
                    } else {
                        $scope.tip = result.resultDesc;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    }


    $scope.toEdit = function (item) {
        if (item.auditStatus === 1) {
            $scope.tip = "活动正在审核中，不可修改";
            $('#myModal').modal();
        } else if (item.auditStatus === 0) {
            $scope.tip = "数据异常";
            $('#myModal').modal();
        } else {
            window.location = "../edit/advertisePrint_edit.html?activityID=" + item.activityID;
        }
    }

}]);
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}]);

app.filter("formatDate", function () {
    return function (date) {
        if (date) {
            return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
        }
        return "";
    }
})