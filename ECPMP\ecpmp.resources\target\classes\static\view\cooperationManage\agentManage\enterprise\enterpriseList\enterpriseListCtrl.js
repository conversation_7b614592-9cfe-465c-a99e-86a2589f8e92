var app = angular.module("myApp",["util.ajax",'page',"angularI18n"])
//自定义filter,格式化日期
app.filter('newDate',function(){
    return function(date){
        var new_date =  date.substr(0,4)+"-"+date.substr(4,2)+"-"+date.substr(6.2);
        return new_date;
    }
});
app.controller('EnterpriselistCtrl', function ($scope,$rootScope,$filter,$location,RestClientUtil) {
    //初始化参数
    $scope.init = function () {
        $scope.id = $location.search().id || '';
      $scope.selectedProvince = null;
      $scope.selectedCity = null;
        $scope.subCityList = null;
      $.cookie("enterpriseType",2,{path:'/'});
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        //搜索时初始化参数
        $scope.initSel = {
            enterpriseName: "",//企业名称
            organizationID: "",//企业机构代码
            provinceName: "0"//归属地
        };
        $scope.enterpriseList();
        $scope.queryProvinceAndCity();
    };
    $scope.formatDate=function(str){
        if(!str){
            return 'format error';
        }
        var newDateStr="";
        newDateStr=str.substring(0, 4) + "-" + str.substring(4, 6) + "-" + str.substring(6, 8) + " " 
        	+ str.substring(8, 10) + ":" + str.substring(10, 12);
        return newDateStr;
    }
    //跳转至新增直客页面
    $scope.gotoAdd=function(){
        location.href='../createEnterprise/createEnterprise.html?operationType=add';
    }
    //跳转至企业详情页面,传递organizationID
    $scope.toDetail=function(item){
        $.cookie("enterpriseID",item.id,{path:'/'});
        $.cookie("enterpriseName",item.enterpriseName,{path:'/'});
        location.href='../createEnterprise/createEnterprise.html?operationType=detail';
    }
    //跳转至企业修改页面,传递enterpriseID
    $scope.toEdit=function(item){
        $.cookie("enterpriseID",item.id,{path:'/'});
        $.cookie("enterpriseName",item.enterpriseName,{path:'/'});
        location.href='../createEnterprise/createEnterprise.html?operationType=edit';
    }
    //获取queryEnterpriseList接口的数据
    $scope.enterpriseList = function (condition) {
        if(condition!='justPage'){
            var req = {
                "enterpriseType":2,
                "sortType":2,
                "sortField":1,
                "enterpriseName":$scope.enterpriseName,
                "organizationID":$scope.organizationID,
                "provinceIDs":[],
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize":$scope.pageInfo[0].pageSize,
                    "isReturnTotal": "1"
                }
            };
            if ($scope.selectedProvince==undefined) {
                req.provinceIDs = []
            }
             else{
                req.provinceIDs = [$scope.selectedProvince.provinceID];
            }
            var provinceArr = req.provinceIDs;
            if ($scope.enterpriseName===undefined && $scope.organizationID===undefined && req.provinceIDs.length==0){
                        $scope.enterpriseName = "";
                        $scope.organizationID = "";
               }
                else {
                    sessionStorage.setItem("cacheenterpriseName",$scope.enterpriseName);
                    sessionStorage.setItem("cacheorganizationID",$scope.organizationID);
                    sessionStorage.setItem("cacheprovinceIDs",provinceArr);
                    req.enterpriseName = sessionStorage.getItem("cacheenterpriseName");
                    req.organizationID = sessionStorage.getItem("cacheorganizationID");
                    if(sessionStorage.getItem("cacheprovinceIDs")==null || sessionStorage.getItem("cacheprovinceIDs")==""){
                                        provinceArr = [];
                                    } else {
                                        provinceArr = [sessionStorage.getItem("cacheprovinceIDs")];
                                    }
                    req.provinceIDs = provinceArr;
                }
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.pageParameter.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize=parseInt($scope.pageInfo[0].pageSize);
//            sessionStorage.setItem("cacheenterpriseName","");
//            sessionStorage.setItem("cacheorganizationID","");
            $scope.enterpriseName == sessionStorage.getItem("cacheenterpriseName");
            $scope.cacheorganizationID == sessionStorage.getItem("cacheorganizationID");
        }
        if(sessionStorage.getItem("cacheenterpriseName")||sessionStorage.getItem("cacheorganizationID")||sessionStorage.getItem("cacheprovinceIDs")||sessionStorage.getItem("cacheprovinceIDs")){
                $scope.enterpriseName = sessionStorage.getItem("cacheenterpriseName");
                $scope.organizationID = sessionStorage.getItem("cacheorganizationID");
                req.enterpriseName = sessionStorage.getItem("cacheenterpriseName");
                req.organizationID = sessionStorage.getItem("cacheorganizationID");

                var provinceIDs = new Array();
                if(sessionStorage.getItem("cacheprovinceIDs")==null || sessionStorage.getItem("cacheprovinceIDs")==""){
                    provinceIDs = [];
                } else {
                    provinceIDs = [sessionStorage.getItem("cacheprovinceIDs")];
                }
                req.provinceIDs = provinceIDs;
        } else {
                req.enterpriseName = "";
                req.organizationID = "";
                req.provinceIDs = [];
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.queryEnterpriseList=result.enterpriseList;
                        //获取页面的总条数与总页面
                        $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                        if($scope.pageInfo[0].totalCount==0){
                            // $scope.pageInfo[0].totalPage=0;
                            $scope.pageInfo[0].currentPage=1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage=1;
                        }else{
                            $scope.pageInfo[0].totalPage=Math.ceil(parseInt(result.totalNum)/parseInt($scope.pageInfo[0].pageSize));
                        }
                    }else{
                        $scope.queryEnterpriseList=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }
    //搜索省份改变时，找到对应的市
    $scope.changeSelectedProvince = function(selectedProvince) {
        $scope.subCityList =null;
        if(selectedProvince)
        {
            jQuery.each($scope.cityList,function(i,e){
                if(e.key == selectedProvince.provinceID){
                    $scope.subCityList =e;
                    $scope.selectedCity=$scope.subCityList[0];
                    sessionStorage.setItem("cacheSubCityList",JSON.stringify($scope.subCityList));
                }
            });
        }
        if(!selectedProvince){
            $scope.subCityList =null;
        }
        console.log("subCityList:"+JSON.stringify($scope.subCityList));
        console.log("selectedCity:"+JSON.stringify($scope.selectedCity));
    }
    //省市联动方法
    $scope.queryProvinceAndCity = function() {
        var queryProvinceListReq  ={};
        /*查询省份*/
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
            data: JSON.stringify(queryProvinceListReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result =data.result;
                    if(result.resultCode=='1030100000'){
                         $scope.provinceList =data.provinceList;
                        if(!!$scope.provinceList)
                        {
                            var provinceIds =[];
                            $scope.provinceList2={};
                            jQuery.each($scope.provinceList,function(i,e){
                                provinceIds[i]=e.provinceID;
                                $scope.provinceList2[e.provinceID]=e.provinceName;
                                if (sessionStorage.getItem("cacheprovinceIDs")==e.provinceID)
                                 {
                                    $scope.selectedProvince = e;
                                 }
                            });
                            var queryCityListReq ={};
                        }
                    }else{
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                })
            },
            error:function(){
                $rootScope.$apply(function(data){
                        //配置了1030120500为接口异常情况下提示
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    };
})
