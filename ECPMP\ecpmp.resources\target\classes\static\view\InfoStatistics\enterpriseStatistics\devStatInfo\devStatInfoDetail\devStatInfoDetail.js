var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
      $scope.token = $.cookie("token");
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否分省管理员
        $scope.isProvincial = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isProvincial = (loginRoleType == 'provincial');
        $scope.isSuperManager = (loginRoleType=='superrManager');
        $scope.isNormalMangager = (loginRoleType=='normalMangager');

        $scope.enterpriseID = "";
        
        if ($scope.isProvincial)
        {
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
        }

        $scope.selectedProvince = null;

        //默认为1：名片彩印
        $scope.serviceType = "";

        $scope.enterpriseName = "";

        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            },
            {
                id: 5,
                name: "热线彩印省份版"
            }
        ];

        $scope.selectedProvince = null;
        if ($scope.isSuperManager || $scope.isProvincial) {
            $scope.queryProvinceAndCitybychaoguan();
        }
        else {
            $scope.queryProvinceAndCity();
        }

        //初始化搜索条件
        $scope.initSel = {
            provinceName: "0",//归属地
            startTime: "",
            endTime: "",
            search:false,
        };
        $scope.searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
        // console.log($scope.getTime($scope.searchParam.startDate));
        $scope.itemCache=JSON.parse($.cookie('item'));
        console.log("传过来item的值："+ $scope.itemCache)
        console.log("传过来searchParam的值："+ sessionStorage.getItem("searchParam"))
        sessionStorage.setItem("notRefresh",JSON.stringify(1))
        $scope.queryEnterpriseDevStatInfo();

    }

    $scope.getServiceType = function (serviceType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 3) {
            return "广告彩印";
        }
        else if (serviceType == 4) {
            return "企业通知";
        }
        else if (serviceType == 5) {
            return "热线彩印省份版";
        }
    };

    $scope.getSubProvinceType = function (subProvinceType) {
        if (subProvinceType == 1) {
            return "分省";
        }
        else if (subProvinceType == 2) {
            return "集客";
        }
        else if (subProvinceType == 3) {
            return "移动云PAAS";
        }
        else if (subProvinceType == 4) {
            return "咪咕音乐";
        }
    };

    $scope.getSubServType = function (subServType) {
        if (subServType == 1) {
            return "主叫屏显";
        }
        else if (subServType == 2) {
            return "被叫屏显";
        }
        else if (subServType == 4) {
            return "挂机短信";
        }
        else if (subServType == 8) {
            return "挂机彩信";
        }
        else if (subServType == 3) {
            return "屏显";
        }
    };

    $scope.getTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        return year + "-" + month + "-" + day;
    }

    $('.input-daterange').datepicker({
		format: "yyyy-mm-dd",
		weekStart: 0,
		language: "zh-CN",
		clearBtn: true,
		autoclose: true
	});

	$('#start').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});
  $scope.exportFile = function () {
      let provinceID ='';
      if (!$scope.isProvincial && $scope.selectedProvince != null && $scope.selectedProvince != undefined) {
          if ($scope.isSuperManager) {
              provinceID = $scope.selectedProvince.provinceID;
          }
          if ($scope.isProvincial || $scope.isNormalMangager) {
              provinceID = $scope.selectedProvince.fieldVal;
          }
      }
      let cityID ='';

      if (!$scope.isProvincial && $scope.selectedCity != null && $scope.selectedCity != undefined) {
          if ($scope.isSuperManager) {
        	  cityID = $scope.selectedCity.cityID;
          }
          if ($scope.isProvincial || $scope.isNormalMangager) {
        	  cityID = $scope.selectedCity.fieldVal;
          }
      }
      var req = {
          "param":{"req":JSON.stringify($scope.reqTemp),"type":2},
          "url":"/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseDevStatInfoCsvFile",
          "method":"get"
      }

      if($scope.reqTemp != undefined)
      {
          CommonUtils.exportFile(req);
      }
  };
	$('#end').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});

	//判断搜索按钮是否置灰
	$scope.searchOn = function () {
		var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;
        $scope.initSel.startTime = "";
        $scope.initSel.endTime = "";

		if (startTime !== '')
		{
			$scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
		}
		
		if (endTime !== '')
		{
			$scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
		}
		
		if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
			$scope.initSel.search = false;
		}
		else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
			$scope.initSel.search = false;
		}
		else {
			$scope.initSel.search = true;
		}
	}

    //后续post的函数
    $scope.queryEnterpriseDevStatInfo = function (condition) {
        //若日期清掉，starttime和endtime都设置为空
        if ($scope.time == "") {
            $scope.initSel.startTime = "";
            $scope.initSel.endTime = "";
        }

        if (condition != 'justPage') {
            var req = {
                "areaDimension": 3,
                "enterpriseType":5,
                "subProvinceType":$scope.itemCache.subProvinceType,
                "enterpriseName": $scope.enterpriseName || '',
                "provinceID": $scope.itemCache.provinceID,
                "cityID":$scope.itemCache.cityID,
                "serviceType": $scope.itemCache.serviceType || '',
                "startDate": $scope.searchParam.startDate || '',
                "endDate": $scope.searchParam.endDate || '',
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);

        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
            req.page.isReturnTotal = "0";
        }

        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseDevStatInfo",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if(data.resultCode=='1030100000'){
                $scope.StatInfoListData=result.enterpriseDevStatInfoList||[];
                if (condition != 'justPage') {
                	$scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                    $scope.pageInfo[0].totalPage=result.totalNum!==0 ?Math.ceil(result.totalNum/parseInt($scope.pageInfo[0].pageSize)):1;
                }
              }  
              else 
              {
                $scope.tip = data.resultCode;
                $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
            )
          }
        });

    };

    //跳转至发展量统计详情页面
    $scope.goBack=function(){
        location.href='../queryEnterpriseDevStatInfoList.html';
    }

  //搜索省份改变时，找到对应的市(超管)
    $scope.changeSelectedProvincebychaoguan = function (selectedProvince) {
        $scope.subCityList = null;
        if (selectedProvince) {

            jQuery.each($scope.cityList, function (i, e) {
                if (e.key == selectedProvince.provinceID) {
                    $scope.subCityList = e;
                }
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
    }

    //从Local Storage中查询超管省的方法
    $scope.queryProvinceAndCitybychaoguan = function () {

    	if ($scope.isProvincial)
        {
    		if($.cookie('provinceID') != null 
       			 &&　$.cookie('provinceID')　!= undefined
       			 && $.cookie('cityID') != null
       			 && $.cookie('cityID') != undefined)
       	 {
       		var queryProvinceListReq = {};
       	    /*查询省份*/
       	    RestClientUtil.ajaxRequest({
       	      type: 'POST',
       	      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
       	      data: JSON.stringify(queryProvinceListReq),
       	      success: function (data) {
       	        $rootScope.$apply(function () {
       	          var result = data.result;
       	          var provinceList = data.provinceList;
       	          if (result.resultCode == '1030100000') {
       	              jQuery.each(provinceList, function (i, e) {
       	            	if($.cookie('provinceID') == e.provinceID)
       	            	{
       	            		$scope.provinceList = {};
       	            		$scope.provinceList2 = {};

       	            		$scope.provinceList[0] = e;
       	            		$scope.selectedProvince = e;
       	            		$scope.provinceList2[e.provinceID] = e.provinceName;
       	            		return;
       	            	}
       	          		});
	              var provinceIds = [];
	              provinceIds[0] = $.cookie('provinceID');
	              var queryCityListReq = {};
	              queryCityListReq.provinceIDs = provinceIds;
	              /*查询地市*/
	              RestClientUtil.ajaxRequest({
	                type: 'POST',
	                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
	                data: JSON.stringify(queryCityListReq),
	                success: function (data) {
	                  $rootScope.$apply(function () {
	                    var result = data.result;
	                    if (result.resultCode == '1030100000') {
	                      $scope.subCityList = {};
	                      jQuery.each(data.cityList, function (i, e) {
	        	            		jQuery.each(e, function (i, e) {
	    	        	            	if(e.cityID == $.cookie('cityID'))
	    	        	            	{
	    	        	            		$scope.subCityList = {};
	    	        	            		$scope.selectedCity = e;
  	    	        	            		$scope.subCityList[0] = e;

	    	        	            		$scope.cityList2 = {};
	    	        	                    $scope.cityList2[e.cityID] = e.cityName;

	    	        	            		return;
	    	        	            	}
	    	        	              });
	        	              });
	                    }else {
	    	                $scope.tip =result.resultCode;
	    	                $('#myModal').modal();
	    	              }
	                  })
	                },
	                error:function(){
	                    $rootScope.$apply(function(){
	                        $scope.tip='1030120500';
	                        $('#myModal').modal();
	                        }
	                    )
	                }
	              });
       	       }
       	          else {
       	              $scope.tip=data.result.resultCode;
       	              $('#myModal').modal();
       	            }
       	        })
       	      },
       	      error:function(){
       	          $rootScope.$apply(function(){
       	        	  	  $scope.tip = '1030120500';
       	                  $('#myModal').modal();
       	              }
       	          )
       	      }
       	    });
       	 }
        }
    	else
        {
    		$scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
            $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
            $scope.provinceList2 = {};
            $scope.cityList2 = {};

            //删除全国选项
            var length = $scope.provinceList.length;
            for (var i = 0; i < length; i++) {
                if ($scope.provinceList[i].provinceID == "000") {
                    $scope.provinceList.splice(i, 1); //删除下标为i的元素
                    break;
                }
            }
     
            jQuery.each($scope.provinceList, function (i, e) {
                $scope.provinceList2[e.provinceID] = e.provinceName;
            });
            $scope.cityList = $scope.mapToList($scope.cityList);
            for (var a = 0; a < $scope.cityList.length; a++) {
                jQuery.each($scope.cityList[a], function (i, e) {
                    $scope.cityList2[e.cityID] = e.cityName;
                });
            }
            console.log($scope.cityList2);
        }
        
        

    };

    //搜索省份改变时，找到对应的市
    $scope.changeSelectedProvince = function (selectedProvince) {
        $scope.subCityList = null;
        if (selectedProvince) {
            $scope.subCityList = $scope.cityList.filter(function (a) {
                return a.parentAuthID == selectedProvince.id;
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
    }
    //省市联动方法
    $scope.queryProvinceAndCity = function () {
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
        $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
        $scope.provinceList2 = {};
        jQuery.each($scope.provinceList, function (i, e) {
            $scope.provinceList2[e.fieldVal] = e.authName;
        });
        console.log($scope.provinceList2);
        $scope.cityList2 = {};
        jQuery.each($scope.cityList, function (i, e) {
            $scope.cityList2[e.fieldVal] = e.authName;
        });
        console.log($scope.cityList2);
    };

    //省市联动时调用，用于转化格式
    $scope.mapToList = function (map) {
        var result = [];
        jQuery.each(map, function (_, o) {
            if (o) {
                o.key = o[0].provinceID;
                result.push(o);
            }
        });
        return result;
    };
    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])