<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>物流通业务信息</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
    <link href="../../../../../css/reset.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
    <link rel="stylesheet" type="text/css" href="../../../../../css/webuploader.css">
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">

    <script type="text/javascript" src="expressageBusinessCtrl.js"></script>

</head>
<body  ng-app="myApp" ng-controller="expressageBusinessController" ng-init="init()" >
    <div class="cooperation-manage" >
        <div class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'WLT_BUSSINESSMANAGE'|translate"></span>
        </div>
        <div class="cooper-tab">
            <div>
                <form class="form-horizontal ng-pristine ng-invalid ng-invalid-required ng-valid-maxlength" name="myForm"
                      novalidate="">
                    <div class="form-group black">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>业务类型：</span>
                        </label>
                        <div class="col-lg-8 col-xs-8 col-sm-8">
                            <!--<li class="redio-li" ng-click="changeServiceType('1')" style="width:200px;display: inline-block;"><span
                                    class="check-btn redio-btn checked" style="vertical-align: middle;"></span>交互彩印&语音外呼
                            </li>-->
                            <li class="redio-li" ng-click="changeServiceType('2')" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn checked" style="vertical-align: middle;"> </span>语音外呼
                            </li>
                        </div>
                    </div>
                    <div class="form-group">
                    	<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>套餐类型：</span>
                        </label>
                        <div class="col-lg-4 col-xs-4 col-sm-4">
	                        <select class="form-control" name="pkgType" ng-model="pkgType">
								<option value="0">语音外呼包月</option>
							</select>
						</div>
                    </div>
                    <div class="form-group white">
                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>套餐规格：</span>
                        </label>
                        <div class="col-lg-8 col-xs-8 col-sm-8">
                            <li class="redio-li" ng-click="changePkgQuota('1')" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn checked" style="vertical-align: middle;"></span>50分钟
                            </li>
                            <li class="redio-li" ng-click="changePkgQuota('2')" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn" style="vertical-align: middle;"> </span>500分钟
                            </li>
                            <li class="redio-li" ng-click="changePkgQuota('3')" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn" style="vertical-align: middle;"> </span>1000分钟
                            </li>
                            <li class="redio-li" ng-click="changePkgQuota('4')" style="width:100px;display: inline-block;"><span
                                    class="check-btn redio-btn" style="vertical-align: middle;"> </span>5000分钟
                            </li>
                        </div>
                    </div>
                    <div class="form-group">
                    	<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                            <span>套餐价格：</span>
                        </label>
                        <div class="col-lg-8 col-xs-8 col-sm-8">
                            <span style="color:red">{{pkgPrice | number:2}}元</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn  col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 "
                ng-disabled="!pkgType"
                ng-bind="'COMMON_SAVE'|translate" ng-click="saveWltService()"></button>
                <button type="submit" class="btn btn-back" style="margin-left:55px" ng-click="goBack()"
                ng-bind="'COMMON_BACK'|translate"></button>
            </div>
            <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="text-center">
                            <p style='font-size: 16px;color:#383838' ng-bind="tip|translate"></p>
                        </div>
                    </div>
                    <div class="modal-footer" style="text-align:center; margin-left:0px;">
                        <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
                            ng-bind="'COMMON_OK'|translate" ng-click="closeTip()"></button>
                    </div>
                </div>
            </div>
        	</div>
    </div>
</div>
</body>
<style>
    body {
        background: #f2f2f2;
    }
    table{
        table-layout: fixed;
    }
    table td{
        width: 100%;
        word-break: keep-all;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    ul{
        table-layout: fixed;
    }
    ul li{
        word-break: break-all;
        text-overflow: ellipsis;
    }
    .modal-footer{
        text-align: left;
    }
    .cooperation-manage{
        min-width: 1024px;
    }
    .cooperation-head {
        padding: 20px;
    }
    .cooperation-head .frist-tab {
        font-size: 16px;
    }
    .cooperation-head .second-tab {
        font-size: 14px;
    }
    .cooper-tab {
        margin: 0 20px;
        background: #fff;
        border-radius: 2px;
        padding: 36px 10px 16px;
    }
    .form-group .control-label icon {
        color: #ff254c;
        vertical-align: sub;
        margin-right: 2px;
    }
    .form-group div {
        line-height: 34px;
    }
    .form-group {
        margin-bottom: 35px;
    }
    .selBtn {
        width:100px;
        height: 34px;
        border-radius: 5px;
        background: #7360e1;
        color: white;
        float: left;
        margin-left: 15px;
        text-align: center;
        cursor: pointer;
        margin-right: 30px;
    }
    .coorPerationTr span {
        color: #7360e1;
        cursor: pointer;
    }
</style>
</html>