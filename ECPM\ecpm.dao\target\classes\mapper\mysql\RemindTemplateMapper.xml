<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.RemindTemplateMapper">
	 <resultMap id="remindTemplateWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.RemindTemplateWrapper">
        <result property="ID" column="ID" javaType="java.lang.Integer" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="remindGroupID" column="remindGroupID" javaType="java.lang.String" />
        <result property="signature" column="signature" javaType="java.lang.String" />
        <result property="contentText" column="contentText" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
    </resultMap>
	
	
	<select id="queryListByGroupId" resultMap="remindTemplateWrapper">
		select enterpriseID, remindGroupID, signature, contentText
		from ecpm_t_remind_template
		where 
		enterpriseID = #{enterpriseId}
		and remindGroupID in
		<foreach item="remindGroupID" index="index" collection="remindGroupIDList" open="(" separator="," close=")">
					#{remindGroupID}
		</foreach>
	</select>
</mapper>