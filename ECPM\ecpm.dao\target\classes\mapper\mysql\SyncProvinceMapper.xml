<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SyncProvinceMapper">
	<resultMap id="enterpriseWithoutSignMap"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.SyncProvinceWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer"/>
		<result property="syncType" column="syncType" javaType="java.lang.Integer"/>
		<result property="syncResult" column="syncResult" javaType="java.lang.Integer"/>
		<result property="subscribeResult" column="subscribeResult" javaType="java.lang.Integer"/>
		<result property="operType" column="operType" javaType="java.lang.Integer"/>
		<result property="errDesc" column="errDesc" javaType="java.lang.String"/>
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="enterpriseCode" column="enterpriseCode" javaType="java.lang.String" />
	</resultMap>

	<insert id="insertSyncProvince" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO ecpm_t_sync_province
		(
		syncType,
		syncResult,
		subscribeResult,
		operType,
		errDesc,
		createTime,
		msisdn,
		enterpriseCode
		)
		VALUES
		(
		#{syncType},
		#{syncResult},
		#{subscribeResult},
		#{operType},
		#{errDesc},
		#{createTime},
		#{msisdn},
		#{enterpriseCode}
		)
	</insert>

</mapper>