var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "cy.uploadifyfile", "angularI18n","service.common"])
app.controller('intelligentCallController', ['$scope', '$rootScope', '$location', '$timeout', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, $timeout, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否超管
        $scope.isSuperManager = false;
        $scope.isAgent = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isAgent = (loginRoleType == 'agent');
        $scope.wltServiceType = $.cookie("wltServiceType");
        $scope.enterpriseID = "";

        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
            $scope.enterpriseID = $scope.subEnterpriseID;
        }
        
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'importIntelligentCall'
        };
        $scope.errorInfo = '';
        $scope.fileUrl = '';
        
        $scope.loginRoleType = $.cookie('loginRoleType');
    	$scope.serviceType = "voiceQuery";
    	$scope.sceneID = 1;
    	$scope.language = "zh-CN";
    	$scope.gender = 'Male';
    	$scope.label = "地址信息";
    	$scope.msg = "请输入地址信息，地址请用“{}”分隔，如{北京路123号}";
    	$scope.number = 62;
        $scope.contentVali = true;
        $scope.isSensitive = false;
        $scope.operate = 'add'
        //初始化搜索条件
        $scope.initSel = [{
        		startTime: "",
        		endTime: "",
	            search: false,
        	}
        ];
        $scope.phoneNum = "";
        $scope.trackingNum = "";
        $scope.planTimeFlag = "";
        $scope.queryWltIntelligentCall();
    }
    
    $scope.impotIntellgent = function () {
	    $('#impotIntellgentPop').modal();
	    $('#impotIntellgentPop').on('hidden.bs.modal', function () {
	      $rootScope.$apply(function () {
	        $("#filePicker").find("span").text("导入文件");
	        if ($scope.uploader) {
	          $scope.uploader.reset();
	        }
	        $scope.errorInfo = "";
	        $scope.fileName = "";
	        $scope.fileUrl = "";
	      })
	    });
	  };
	  
	  $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
		    if (broadData.file !== "") {
		      $scope.fileName = broadData.file.name;
		    } else {
		      $scope.fileName = "";
		    }
		    $scope.uploader = broadData.uploader;
		    $scope.errorInfo = broadData.errorInfo;
		    $scope.fileUrl = fileUrl;
		  });
	  
      
    $scope.impotIntellgentCall = function () {
	    var req = {
	      "enterpriseID": $scope.enterpriseID,
	      "operatorID": $.cookie('accountID'),
	      "path": $scope.fileUrl
	      /*"path": "D://WltIntelligentCallTemplate.xlsx"*/
	    };
	    RestClientUtil.ajaxRequest({
	      type: 'POST',
	      url: "/ecpmp/ecpmpServices/intelligentCallService/batchAddIntellgentCall",
	      data: JSON.stringify(req),
	      success: function (data) {
	        $rootScope.$apply(function () {
	          var result = data.result;
	          if (result.resultCode == '**********') {
	            $scope.tip = "导入成功";
	            $('#myModal').modal();
	          } else {
	            $scope.tip = result.resultCode;
	            $('#myModal').modal();
	          }
	          $('#impotIntellgentPop').modal("hide");
	          $scope.queryWltIntelligentCall();
	        })
	      },
	      error: function () {
	        $rootScope.$apply(function () {
	          $scope.tip = '**********';
	          $('#myModal').modal();
	        })
	      }
	    });
	  }
    
    $scope.getServiceType = function (serviceType) {
        if (serviceType == 'voiceQuery') {
            return "语音询问";
        }
        else if (serviceType == 'voiceNotify') {
            return "语音通知";
        }
    }
    
    $scope.getSceneID = function (sceneID) {
        if (sceneID == '1') {
            return "对话性交互";
        }
        else if (sceneID == '2') {
            return "类似USSD选择交互";
        }
    }
    
    $scope.getLanguage = function (language) {
        if (language == 'en-US') {
            return "英语";
        }
        else if (language == 'zh-CN') {
            return "汉语";
        }
    }
    
    $scope.getGender = function (gender) {
        if (gender == 'Male') {
            return "男性";
        }
        else if (gender == 'Female') {
            return "女性";
        }
    }
    
//    $scope.getAddress = function (address) {
//    	address = address.substring(1,address.length()-1);
//		if(address.indexOf("}{") != -1)
//		{
//			var addressStr = address.replace("\\}\\{",",");
//			return addressStr;
//		}else {
//			return address;
//		}
//    }
    
    $scope.getTime = function (time) {
    	if(time != null && time != '' && time != undefined){
    		var year = time.slice(0, 4);
            var month = time.slice(4, 6);
            var day = time.slice(6, 8);
            var hour = time.slice(8, 10);
            var minute = time.slice(10, 12);
            var second = time.slice(12, 14);
            return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
    	} else {
    		return "";
    	}
    }
    
    $scope.getNumber = function(number) {
    	if(number != null && number != '' && number != undefined){
    		if(number.indexOf('86') == 0) {
    			number = number.substring(2,number.length);
  		  }
    	}
    	return number;
    }
    
    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true
    });

    $('#planTimeStart').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#planTimeEnd').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });
    
    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        var startTime = document.getElementById("planTimeStart").value;
        var endTime = document.getElementById("planTimeEnd").value;

        if (startTime !== '')
        {
            $scope.initSel[0].startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
        }
        else
        {
            $scope.initSel[0].startTime = "";
        }

        if (endTime !== '')
        {
            $scope.initSel[0].endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
        }
        else
        {
            $scope.initSel[0].endTime = "";
        }

        if ($scope.initSel[0].startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel[0].search = false;
        }
        else if ($scope.initSel[0].startTime !== '' && $scope.initSel[0].endTime !== '') {
            $scope.initSel[0].search = false;
        }
        else {
            $scope.initSel[0].search = true;
        }
    }
    
    //后续post的函数
    $scope.queryWltIntelligentCall = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "phoneNum": $scope.phoneNum || '',
                "trackingNum": $scope.trackingNumInput || '',
                "planTimeStart":$scope.initSel[0].startTime || '',
                "planTimeEnd":$scope.initSel[0].endTime || '',
                "enterpriseID":$scope.enterpriseID || '',
                "pageParameter":{
                	"pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize)
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/intelligentCallService/queryWltIntelligentCall",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.WltIntelligentCallListData = result.wltIntelligentCallList || [];
                        angular.forEach($scope.WltIntelligentCallListData, function (e, i) {
                        	var wltIntelligentCall = angular.copy(e);
                        	if (e.planTime != null && e.planTime != '' && e.planTime != undefined) {
                  				var year = e.planTime.slice(0, 4);
                  	            var month = e.planTime.slice(4, 6);
                  	            if(Number(month) >= 11)
              	            	{
                  	            	month = "" + Number(month)-1;
              	            	} else if (Number(month) <= 10)
          	            		{
              	            		month = "0" + (Number(month)-1);
          	            		}
                  	            var day = e.planTime.slice(6, 8);
                  	            var hour = e.planTime.slice(8, 10);
                  	            var minute = e.planTime.slice(10, 12);
                  	            var second = e.planTime.slice(12, 14);
                  	            var planDate = new Date(year, month, day, hour, minute, second);
                  	            var now = new Date();
                  	            if(planDate > now) {
                  	            	wltIntelligentCall.showEdit = true;
                  	            } else {
                  	            	wltIntelligentCall.showEdit = false;
                  	            }
                  		     } else {
                  		    	wltIntelligentCall.showEdit = false;
                  		     }
                        	 $scope.WltIntelligentCallListData[i] = wltIntelligentCall;
                  	    });
                        $scope.pageInfo[0].totalCount = parseInt(result.totalCount) || 0;
                        $scope.pageInfo[0].totalPage = result.totalCount !== 0 ? Math.ceil(result.totalCount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    }
                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
                }
                )
            }
        });

    };
    
    $scope.showAddressModal = function() {
      	$('#addressModal').modal();
      };
      
      $scope.goback = function () {
          $('.close').click();
      }
      
      $scope.reset = function () {
    	  $scope.serviceType = "voiceQuery";
    		$scope.sceneID = 1;
    		$scope.language = "zh-CN";
    		$scope.gender = 'Male';
    		$scope.label = "地址信息";
    		$scope.msg = "请输入地址信息，地址请用“{}”分隔，如{北京路123号}";
    	    $scope.contentVali = true;
    	    $scope.isSensitive = false;
    	    $scope.sensitiveWordsStr = '';
    	    $scope.contentDesc = '';
    	    $scope.caller = '';
    	    $scope.callee = '';
    	    $scope.context = '';
    	    $scope.trackingNum = '';
    	    $scope.planTime = '';
      };
      $scope.changeServiceType = function (val) {
    	  if($scope.operate == 'detail')
		  {
    		  return;
		  }
    	  var index = 0;
    	  $scope.serviceType = val;
    	  if(val == 'voiceQuery') {
    		  index = 0;
    	  } else if(val == 'voiceNotify'){
    		  index = 1;
	      }
          $('.serviceType .redio-li').find('span').removeClass('checked');
          $('.serviceType .redio-li').eq(index).find('span').addClass('checked');
          if(index == 0)
          {
        	  $scope.label = "地址信息";
        	  $scope.msg = "请输入地址信息，地址请用“{}”分隔，如{北京路123号}";
        	  $scope.sceneID = 1;
        	  $scope.changeSceneID($scope.sceneID);
          } else {
        	  $scope.label = "文本内容";
        	  $scope.msg = "请输入文本内容";
        	  $scope.sceneID = null;
          }
      }
        $scope.changeGender = function (val) {
        	if($scope.operate == 'detail')
  		  	{
      		  	return;
  		  	}
        	  var index = 0;
        	  if(val == 'Male')
        	  {
        		  index = 0;
        	  } else {
        		  index = 1;
        	  }
              $('.gender .redio-li').find('span').removeClass('checked');
              $('.gender .redio-li').eq(index).find('span').addClass('checked');
        }
        $scope.changeSceneID = function (val) {
        	if($scope.operate == 'detail')
  		  	{
      		  	return;
  		  	}
            $('.sceneID .redio-li').find('span').removeClass('checked');
            $('.sceneID .redio-li').eq(val-1).find('span').addClass('checked');
            $scope.sceneID = val;
        }
        
        //敏感词校验
        $scope.sensitiveCheck = function () {
            $scope.checkContent($scope.context);
            if ($scope.contentVali) {
                $scope.sensitiveWords = [];
                $scope.isSensitive = false;
                $scope.sensitiveWordsStr = "";
                var req = {
                    "content": $scope.context || '',
                };
                RestClientUtil.ajaxRequest({
                    type: 'POST',
                    url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
                    data: JSON.stringify(req),
                    success: function (result) {
                        $rootScope.$apply(function () {
                            var data = result.result;
                            if (data.resultCode == '1030120017') {
                                $scope.sensitiveWords = result.sensitiveWords || [];
                                if ($scope.sensitiveWords.length > 0) {
                                    $scope.isSensitive = true;
                                    $scope.sensitiveWordsStr = $scope.sensitiveWords.join('、');
                                } else {
                                    $scope.isSensitive = false;
                                }
                            } else if (data.resultCode == '**********') {
                                $scope.sensitiveWords = [];
                                $scope.isSensitive = false;
                            }
                        })
                    },
                    error: function () {
                        $rootScope.$apply(function () {
                                $scope.tip = '**********';
                                $('#myModal').modal();
                            }
                        )
                    }
                });
            }

        };
        
        $scope.checkContent = function (context) {
            $scope.contentVali = true;
            if (context) {
                if (context.length > $scope.number) {
                    $scope.contentVali = false;
                } else {
                    $scope.contentVali = true;
                }
            } else {
                $scope.sensitiveWords = [];
                $scope.isSensitive = false;
                $scope.sensitiveWordsStr = "";
                $scope.contentVali = false;
            }
            if (!$scope.contentVali) {
            	if($scope.serviceType == 'voiceQuery')
            	{
            		 $scope.contentDesc = "地址信息必填，最长" + $scope.number + "个字";
            	} else {
            		$scope.contentDesc = "文本内容必填，最长" + $scope.number + "个字";
            	}
            } else {
            	$scope.contentDesc = "";
            }
        };
        
        $scope.showCall = function(operate,item) {
        	if(operate == 'detail' || operate == 'update') {
        		$scope.operate = 'update;'
        		$scope.requestId = item.requestId;
        		$scope.serviceType = item.serviceType;
        		$scope.changeServiceType(item.serviceType);
        		$scope.sceneID = item.sceneID;
        		$scope.changeSceneID(item.sceneID);
        		if($scope.sceneID == '1' || $scope.sceneID == '2')
        		{
        			$scope.context = item.address;
        		} else {
        			$scope.context = item.text;
        		}
        		$scope.language = item.language;
        		$scope.gender = item.gender;
        		$scope.changeGender(item.gender);
        		$scope.caller = $scope.getNumber(item.caller);
            	$scope.callee = $scope.getNumber(item.callee);
            	$scope.trackingNum = item.trackingNum;
            	if(item.planTime != null && item.planTime != '' && item.planTime != undefined){
            		var year = item.planTime.slice(0, 4);
                    var month = item.planTime.slice(4, 6);
                    var day = item.planTime.slice(6, 8);
                    var hour = item.planTime.slice(8, 10);
                    var minute = item.planTime.slice(10, 12);
                    $scope.planTime = year + "/" + month + "/" + day + " " + hour + ":" + minute;
                    $scope.planTimeFlag = $scope.planTime;
            	} else {
            		$scope.planTime = "";
            		$scope.planTimeFlag = "";
            	}
        	} else if (operate == 'add'){
            	$scope.serviceType = "voiceQuery";
        		$scope.changeServiceType($scope.serviceType);
            	$scope.sceneID = 1;
            	$scope.changeSceneID($scope.sceneID);
            	$scope.language = "zh-CN";
            	$scope.gender = 'Male';
            	$scope.label = "地址信息";
            	$scope.msg = "请输入地址信息，地址请用“{}”分隔，如{北京路123号}";
            	$scope.caller = '';
            	$scope.callee = '';
            	$scope.context = '';
            	$scope.trackingNum = '';
            	$scope.planTime = '';
            	$scope.planTimeFlag = "";
            	$scope.number = 62;
                $scope.contentVali = true;
                $scope.isSensitive = false;
        	}
        	$scope.operate = operate;
        	$('#addIntelligentCall').modal();
        }

      $scope.intelligentCall = function () {
    	  if($scope.context == undefined || !$scope.contentVali || $scope.isSensitive)
          {
    		  if($scope.serviceType == 'voiceQuery')
    	      	{
    			  $scope.tip = '请按要求填写地址信息';
    	      	} else {
    	      		$scope.tip = '请按要求填写文本内容';
    	      	}
              $('#myModal').modal();
              return;
          }
    	  if($scope.serviceType == 'voiceQuery' && $scope.context != '')
    	  {
    		  var reg_num=/^(\{\S[^{}]*\})+$/;
    		  if(!reg_num.test($scope.context)){
    			  $scope.tip = '请按要求填写地址信息';
    			  $('#myModal').modal();
    	          return;
    		  }
    	  }
    	  if($scope.operate == 'update')
		  {
    		  	var planTimeTepm = $scope.planTime.replace("\/","").replace("\/","").replace(":","").replace(" ","");
    		  	planTimeTepm = planTimeTepm + "00";
    		  	var year = planTimeTepm.slice(0, 4);
	            var month = planTimeTepm.slice(4, 6);
	            var day = planTimeTepm.slice(6, 8);
	            if (Number(month) == 2)
	            {
	            	if(Number(year) % 4 == 0 && Number(year) % 100 != 0 || Number(year) % 400 == 0)
	            	{
	            		if(Number(day) > 29)
	            		{
	            			$scope.tip = '预计下发时间格式错误';
	  	    			  	$('#myModal').modal();
	  	    			  	return;
	            		}
	            	} else {
	            		if(Number(day) > 28)
	            		{
	            			$scope.tip = '预计下发时间格式错误';
	  	    			  	$('#myModal').modal();
	  	    			  	return;
	            		}
	            	}
	            }
	            if(Number(month) >= 11)
	        	{
	            	month = "" + Number(month)-1;
	        	} else if (Number(month) <= 10)
	    		{
	        		month = "0" + (Number(month)-1);
	    		}
	            
	            var hour = planTimeTepm.slice(8, 10);
	            var minute = planTimeTepm.slice(10, 12);
	            var second = planTimeTepm.slice(12, 14);
	            var planDate = new Date(year, month, day, hour, minute, second);
	            var now = new Date();
	            if(planDate < now) {
	            	$scope.tip = '预计下发时间不能小于当前时间';
	    			  $('#myModal').modal();
	    	          return;
	            }
    		  $scope.submitWltIntelligentCall(planTimeTepm);
		  } 
    	  else if ($scope.operate == 'add')
		  {
    		  var caller = $scope.caller;
        	  var callee = $scope.callee;
        	  if(caller.indexOf('86') != 0) {
        		  caller = '86' + caller;
        	  }
        	  if(callee.indexOf('86') != 0) {
        		  callee = '86' + callee;
        	  }
            var req = {
              "caller": caller,
              "callee": callee,
              "serviceType": $scope.serviceType,
              "sceneID":$scope.sceneID,
              "extensionInfo":[{
            	  	"key":"businessType",
          		  	"value":"1"
              	  },{
              		"key":"trackingNum",
              		"value":$scope.trackingNum
              	  }]
            };
            if($scope.serviceType == 'voiceQuery')
          	{
            	req.extensionInfo.push({
            		"key":"address",
            		"value":$scope.context
            	});
          	} else {
          		req.tts = {};
          		req.tts.language = $scope.language;
          		req.tts.gender = $scope.gender;
          		req.tts.text = $scope.context;
          	}
            RestClientUtil.ajaxRequest({
              type: 'POST',
              url: "/ecpmp/ecpmpServices/intelligentCallService/intelligentCall",
              data: JSON.stringify(req),
              success: function (result) {
                $rootScope.$apply(function () {
                  var data = result.result;
                  if (data.resultCode == '**********') {
                	$scope.submitWltIntelligentCall();
                  } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                  }
                })
              },
              error: function () {
                $rootScope.$apply(function () {
                  $scope.tip = '**********';
                  $('#myModal').modal();
                })
              }
            });
		  }
      }
      
      $scope.deleteIntelligentCallPop = function (item) {
    	    $scope.requestId = item.requestId;
    	    $scope.deleteTip = "确认删除 ?";
    	    $scope.operate = 'delete'
    	    $('#deleteModal').modal();
      }
      
      $scope.submitWltIntelligentCall = function (planTime) {
    	  var req = {};
    	  if($scope.operate == 'delete') {
    		  req = {
    				  	 "operType":2,
    		    	     "wltIntelligentCallList":[{
    		    	    	 "requestId":$scope.requestId
    		    	     }]
    		    	  };
    	  } else {
    		  var caller = $scope.caller;
        	  var callee = $scope.callee;
        	  if(caller.indexOf('86') != 0) {
        		  caller = '86' + caller;
        	  }
        	  if(callee.indexOf('86') != 0) {
        		  callee = '86' + callee;
        	  }
        	  req = {
        	     "wltIntelligentCallList":[{
        	    	 "requestId":$scope.requestId,
        	    	 "enterpriseID":$scope.enterpriseID,
        	    	 "serviceType":$scope.serviceType,
        	    	 "language":$scope.language,
        	    	 "gender":$scope.gender,
        	    	 "caller":caller,
        	    	 "callee":callee,
        	    	 "trackingNum":$scope.trackingNum
        	     }]
        	  };
        	  if($scope.serviceType == 'voiceQuery'){
        		  req.wltIntelligentCallList[0].address=$scope.context;
        		  req.wltIntelligentCallList[0].sceneID=$scope.sceneID;
        		  req.wltIntelligentCallList[0].text='';
        	  } else if ($scope.serviceType == 'voiceNotify') {
        		  req.wltIntelligentCallList[0].text=$scope.context;
        		  req.wltIntelligentCallList[0].sceneID='';
        		  req.wltIntelligentCallList[0].address='';
        	  }
        	  if($scope.operate == 'add'){
        		  req.operType = 0;
        	  } else if ($scope.operate == 'update'){
        		  req.operType = 1;
        		  req.wltIntelligentCallList[0].planTime = planTime;
        	  }
    	  }
    	  RestClientUtil.ajaxRequest({
              type: 'POST',
              url: "/ecpmp/ecpmpServices/intelligentCallService/submitWltIntelligentCall",
              data: JSON.stringify(req),
              success: function (result) {
                $rootScope.$apply(function () {
                  var data = result.result;
                  if (data.resultCode == '**********') {
                	$('#addIntelligentCall').click();
                    if($scope.operate == 'add'){
                    	$scope.queryWltIntelligentCall();
                    	$('#callTipModal').modal();
                    	$timeout(()=>{
                    		$('#callTipModal').modal('hide');
                    	},10000)
                    } else if ($scope.operate == 'delete'){
                    	$scope.queryWltIntelligentCall('justPage');
                    	$('#deleteModal').modal("hide");
                    	$scope.tip = data.resultCode;
                        $('#myModal').modal();
                    } else {
                    	$scope.queryWltIntelligentCall();
                    	$scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                  } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                  }
                })
              },
              error: function () {
                $rootScope.$apply(function () {
                  $scope.tip = '**********';
                  $('#myModal').modal();
                })
              }
            });
      }

      $scope.closeTip = function () {
        if($scope.tip == "COMMON_SAVESUCCESS"){
          window.history.back();
        }
      }
    /*校验各个字段*/
      $scope.validate = function (context, maxlength, reg) {
        if (!context) {
          return false;
        } else {
          if (context.length > maxlength) {
            return false;
          } else {
            if (!reg.test(context)) {
              return false;
            } else {
              return true;
            }
          }
        }
      };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })


}])