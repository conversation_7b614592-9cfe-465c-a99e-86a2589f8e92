<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.HotLineTimeOutMapper">
	<resultMap id="blackWhite"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.TimeOutConfigWrapper">
		<result property="id" column="ID" />
		<result property="enterpriseID" column="enterpriseId" />
		<result property="enterpriseName" column="enterpriseName" />
		<result property="servType" column="servType" />
		<result property="subServType" column="subServType" />
		<result property="timeoutConfig" column="timeoutConfig" />
		<result property="deliveryChannel" column="deliveryChannel" />
		<result property="platform" column="platform" />
		<result property="operatorID" column="operatorId" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="status" column="status" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="reserved5" column="reserved5" />
	</resultMap>


	<select id="queryAllTimeOutConfig" resultMap="blackWhite">
		SELECT
		ID,
		enterpriseId,
		enterpriseName,
		servType,
		subServType,
		timeoutConfig,
		deliveryChannel,
		platform,
		createTime,
		updateTime,
		operatorId,
		`status`,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5
		FROM
		ecpe_t_hotline_timeout_configuration
	</select>

	<select id="queryConfig" resultMap="blackWhite">
		SELECT
		ID,
		enterpriseId,
		enterpriseName,
		servType,
		subServType,
		timeoutConfig,
		deliveryChannel,
		platform,
		createTime,
		updateTime,
		operatorId,
		`status`,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5
		FROM
		ecpe_t_hotline_timeout_configuration
		where 1 = 1
		<if test="enterpriseId != null || enterpriseId != ''">
			and enterpriseId = #{enterpriseID}
		</if>
		<if test="deliveryChannel != null || deliveryChannel != ''">
			and deliveryChannel = #{deliveryChannel}
		</if>
		<if test="platform != null || platform != ''">
			and platform = #{platform}
		</if>
	</select>
</mapper>
