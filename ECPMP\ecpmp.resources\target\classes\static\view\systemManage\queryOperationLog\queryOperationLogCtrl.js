var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n","service.common"])
app.controller('OperLogManagementController', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
  $scope.init = function () {

    $scope.enterpriseType = "";
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    $scope.exportFile=function(){
        var req = {
          "param":{
        	"operatorAccount": $scope.operationName,
            "startDate":$scope.initSel.startTime,
            "endDate":$scope.initSel.endTime
          },
          "url":"/qycy/ecpmp/ecpmpServices/commonService/downOperLogFile",
          "method":"get"
        }
        CommonUtils.exportFile(req);
      }
    
    $scope.queryOperationLogList();
  };

//初始化搜索条件
  $scope.initSel = {
      startTime: "",
      endTime: "",
      search: false,
  };
  
  $scope.operationLogList = [];

  //查询
  $scope.queryOperationLogList = function (condition) {
    var req = {};
    if (condition != 'justPage') {
      req = {
        "operTimeBegin": $scope.initSel.startTime,
        "operTimeEnd": $scope.initSel.endTime,
        "operatorAccount": $scope.operationName,
          "filtOperatorIDs":["9999"],
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryOperationLogListTemp = angular.copy(req);
    } else {
      req = $scope.queryOperationLogListTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/queryOperationLogList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(result);
          if (data.resultCode == '**********') {
            $scope.operationLogList = result.operationLogList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.operationLogList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.operationLogList = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }
  
  
  $('.input-daterange').datepicker({
	  format: "yyyy-mm-dd",
      weekStart: 0,
      clearBtn: true,
      language: "zh-CN",
      autoclose: true,
      startDate: "-6m"
  });

  $('#start').on('changeDate', function () {
      $rootScope.$apply(function () {
          $scope.searchOn();
      }
      )
  });

  $('#end').on('changeDate', function () {
      $rootScope.$apply(function () {
          $scope.searchOn();
      }
      )
  });
  
  //判断搜索按钮是否置灰
  $scope.searchOn = function () {
      $scope.initSel.startTime = document.getElementById("start").value;
      $scope.initSel.endTime = document.getElementById("end").value;

      if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
          $scope.initSel.search = false;
      }
      else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
          $scope.initSel.search = false;
      }
      else {
          $scope.initSel.search = true;
      }
  }

  $scope.toDetail = function(item){
	  $scope.operationLog = item;
	  $("#operLogDetail").modal();
  }
});


app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return format(date);
    }
    return "";
  }
});

function add0(m){
	return m<10?'0'+m:m 
}

function format(timestamp)
{
	var time = new Date(timestamp);
	var y = time.getFullYear();
	var m = time.getMonth()+1;
	var d = time.getDate();
	var h = time.getHours();
	var mm = time.getMinutes();
	var s = time.getSeconds();
	return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}

