<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title></title>
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
	<link href="../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="roleManage_list.js"></script>

	<style>
		.cooperation-manage .coorPeration-table th, td {
			padding-left: 30px !important;
		}
	</style>
</head>
<body ng-app='myApp' style="min-width:1024px;" ng-controller='roleListCtrl' ng-init="init();">
	<div class="cooperation-manage">
		<div class="cooperation-head"><span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate"></span>&nbsp;&gt;&nbsp;<span
						class="second-tab" ng-bind="'ROLE_MANAGE'|translate"></span></div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="roleName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"
								 style="white-space:nowrap" ng-bind="'CHARACTERNAME'|translate"></label>

					<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" style="min-width: 300px;max-width: 300px">
						<input type="text" id="roleName" class="form-control"
									 placeholder="{{'PLEASEINPUTCHARACTERNAME'|translate}}" ng-model="roleName">
					</div>

					<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
						<button type="submit" class="btn search-btn" ng-click="queryRoleList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="toCreate()">
				<icon class="add-iocn"></icon>
				<span ng-bind="'CREATE_ROLE'|translate"></span>
			</button>
		</div>

		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th ng-bind="'CHARACTERNAME'|translate"></th>
					<th ng-bind="'ROLE_AUTH'|translate"></th>
					<th ng-bind="'ROLE_DESC'|translate"></th>
					<th ng-bind="'COMMON_CREATETIME'|translate"></th>
					<th ng-bind="'CREATEACCOUNT'|translate"></th>
					<th ng-bind="'COMMON_OPERATE'|translate" width="300"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in roleList">
					<td ng-bind="item.roleName" title="{{item.roleName}}"></td>
					<td ng-bind="item.authNameString" title="{{item.authNameString}}"></td>
					<td ng-bind="item.roleDesc" title="{{item.roleDesc}}"></td>
					<td ng-bind="item.createTime|formatDate" title="{{item.createTime|formatDate}}"></td>
					<td ng-bind="item.operatorAccountName" title="{{item.operatorAccountName}}"></td>
					<td>
						<div class="handle">
							<ul>
								<li class="delete" >
									<icon class="delete-icon"></icon>
									<span ng-bind="'COMMON_DELETE'|translate" ng-click="deleteRolePop(item)"></span>
								</li>
								<li class="edit" ng-click="toEdit(item)">
									<icon class="edit-icon"></icon>
									<span ng-bind="'GROUP_EDIT'|translate"></span>
								</li>
								<li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'COMMON_WATCH'|translate"></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="roleList===null||roleList.length==0">
					<td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="queryRoleList('justPage')"></ptl-page>
		</div>
	</div>
	<!--删除确认框-->
	<div class="modal fade bs-example-modal-sm" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{deleteTip|translate}}
					</p></div>
				</div>
				<div class="modal-footer" style="text-align: center">
					<button type="submit" class="btn btn-primary search-btn ng-binding" ng-click="deleteRole()"
									ng-bind="'COMMON_OK'|translate">
					</button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_CANCLE'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{tip|translate}}
					</p></div>
				</div>
				<div class="modal-footer" style="text-align: center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</body>
</html>