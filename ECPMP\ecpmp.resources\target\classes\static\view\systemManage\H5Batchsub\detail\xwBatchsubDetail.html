<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>小微商户批设管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
	<link href="../../../../css/reset.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
	
	<!--分页-->
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
	<link rel="stylesheet" type="text/css" href="../../../../css/searchList.css"/>
	<link rel="stylesheet" type="text/css" href="../../../../css/font-awesome.min.css">

	<script type="text/javascript" src="xwBatchsubDetail.js"></script>
	<style>
		label {
			min-width: 120px;
		}

		.cond-div {
			min-width: 240px;
		}
		.clearf:after{
    		content:'';
    		clear:both;
        	height:0;
        	display:block;
		}
	</style>

</head>
<body ng-app="myApp" ng-controller="XwBatchsubDetailController" ng-init="init()" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'XW_BATCHSUB_MANAGER' | translate">小微商户批设管理</span>
		</div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="msisdn" class="col-xs-1 control-label"
								 style="white-space:nowrap">号码</label>

					<div class="cond-div col-xs-2">
						<input type="text" autocomplete="off" class="form-control" id="msisdn"
									 placeholder="请输入号码" ng-model="msisdn">
					</div>

					<label for="dealStatus" class="col-xs-1 control-label" ng-bind="'XW_BATCHSUB_DEALSTATUS'|translate"></label>
					<div class="cond-div col-xs-2">
						<select class="form-control" ng-model="dealStatus" id="dealStatus"
										ng-options="x.id as x.name for x in dealStatusList">
							<option value="">不限</option>
						</select>
					</div>
					<div class="clearf"></div>					
					<label for="subServType" class="col-xs-1 control-label">子业务类型</label>
					<div class="cond-div col-xs-2">
						<select class="form-control" ng-model="subServType"" id="subServType"
										ng-options="x.id as x.name for x in subServTypeList">
							<option value="">不限</option>
						</select>
					</div>
					<label for="approveStatus" class="col-xs-1 control-label">审核状态</label>
					<div class="cond-div col-xs-2">
						<select class="form-control" ng-model="approveStatus" id="approveStatus"
										ng-options="x.id as x.name for x in approveStatusList">
							<option value="">不限</option>
						</select>
					</div>
					<div class="cond-div">
						<button type="submit" class="btn search-btn" ng-click="queryH5BatchSubMember()" style="margin-left: 20px">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
					<button ng-click="exportFile()" id="exportSpokesList" type="submit" class="btn add-btn" ><icon class="export-icon"></icon><span>导出</span></button>
		</div>
		<div style="margin: 20px">
			<p style="font-size: 16px">设置列表</p>
		</div>
	</div>
	<div class="coorPeration-table">
		<table class="table table-striped table-hover">
			<thead>
			<tr>
				<th style="width:10%" >号码</th>
				<th style="width:10%" >产品ID</th>
				<th style="width:10%" >省份</th>
				<th style="width:10%" >开通时间</th>
				<th style="width:10%" >子业务类型</th>
				<th style="width:10%" >批设内容</th>
				<th style="width:10%" >开通状态</th>
				<th style="width:10%" >审核状态</th>				
				<th style="width:20%">驳回意见</th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="item in h5BatchSubMemberData">
				<td><span title="{{item.msisdn}}" ng-bind="item.msisdn"></span></td>
				<td><span title="{{item.productCode}}" ng-bind="item.productCode"></span></td>
				<td><span title="{{item.provinceID}}" ng-bind="item.provinceID"></span></td>
				<td><span title="{{item.dealTime|formatDate}}" ng-bind="item.dealTime|formatDate"></span></td>
				<td><span title="{{item.subServType|formatSubServType}}" ng-bind="item.subServType|formatSubServType"></span></td>
				<td><span title="{{item.content}}" ng-bind="item.content"></span></td>
				<td><span title="{{item.dealStatus|dealStatus}}" ng-bind="item.dealStatus|dealStatus"></span></td>
				<td><span title="{{item.approveStatus|formatApproveStatus}}" ng-bind="item.approveStatus|formatApproveStatus"></span></td>
				<td><span title="{{item.approveIdea}}" ng-bind="item.approveIdea"></span></td>		
			</tr>
			<tr ng-show="h5BatchSubMemberData===null||h5BatchSubMemberData.length===0">
				<td style="text-align:center" colspan="7">暂无数据</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div>
		<ptl-page tableId="0" change="queryH5BatchSubMember('justPage')"></ptl-page>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
			 style="z-index:555555;">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838;text-align: center'>
							{{tip|translate}}
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align: center">
					<button type="submit" class="btn btn-primary" data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
</body>
</html>