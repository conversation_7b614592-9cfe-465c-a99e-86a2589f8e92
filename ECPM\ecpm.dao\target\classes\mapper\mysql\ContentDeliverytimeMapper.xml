<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ContentDeliverytimeMapper">
    <resultMap id="contentDeliverytimeMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentDeliverytimeWrapper">       
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="cyContID" column="cyContID" javaType="java.lang.Long" />
        <result property="startTime" column="startTime" javaType="java.lang.String" />
        <result property="endTime" column="endTime" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />        
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
    </resultMap>
    <insert id="insertContentDeliverytime">
        INSERT INTO ecpm_t_cont_deliverytime
        (ID,
        cyContID,
        startTime,
        endTime,
        enterpriseID,
        createTime,
        updateTime,
        operatorID
        )
        VALUES 
        (
        nextval('ecpm_seq_contentdeliverytime'),
        #{cyContID},
        #{startTime},
        #{endTime},
        #{enterpriseID},
        #{createTime},
        #{updateTime},
        #{operatorID}
        )
    </insert>
    
    <select id="queryContentDeliverytimeBycyContID" resultMap="contentDeliverytimeMap">
        select ID,
        cyContID,
        startTime,
        endTime,
        enterpriseID,
        createTime,
        updateTime,
        operatorID
        from ecpm_t_cont_deliverytime where cyContID=#{cyContID}
    </select>
    
    <delete id="deleteContentDeliverytimeBycyContID">
    	delete from ecpm_t_cont_deliverytime where cyContID = #{cyContID}
    </delete>
</mapper>