<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>热线内容管理</title>
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/searchList.css" rel="stylesheet" />
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-sanitize.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
<!-- 引入菜单组件 -->
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<!--分页-->
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css" />
<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet" />
<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
<script type="text/javascript" src="hotlineContentManage.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../css/hotlineContentManage.css"/>
<style>
    .border-red{
        border: 1px solid red;
    }
    .dialog-690{
        width: 696px;
    }
    .dialog-800{
        width: 800px;
    }
    .dialog-900{
        width: 900px;
    }
    .dialog-1000{
        width: 1000px;
    }
    .handle ul li icon.manage-icon {
        background-position: -126px 0;
    }
    .handle ul li icon.add-icon {
        background-position: -55px 0;
    }
    .handle ul li icon.import-icon {
        background-position: -90px 0;
    }
    .table th.adjustable-width{
        width: 25%;
    }
    #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }
    #filePicker_ div:nth-child(2) {
        width: 100% !important;
        height: 100% !important;
    }

    .form-group div li {
        display: inline-block;
        margin-right: 10px;
        padding-right: 10px;
        cursor: pointer;
    }

    .form-group div li span {
        vertical-align: middle;
        margin-right: 4px;
    }
        /* media for adjustable search-table width  */
    @media (max-width: 1850px) {
        .table th.adjustable-width {
            width: 28%;
        }
        .handle ul li {
            margin-right: 10px;
        }
    }
    @media (max-width: 1600px) {
        .table th.adjustable-width {
            width: 30%;
        }

        .handle ul li {
            margin-right: 10px;
        }
    }
	@media (max-width: 1300px){
        .table th.adjustable-width{
            width: 33%;
            }
        .handle ul li{
            margin-right: 10px;
        }
    }
    @media (max-width: 1100px){
        .table th.adjustable-width{
            width: 42%;
            }
        .handle ul li{
            margin-right: 10px;
        }
    }
	   .ng-dirty.ng-invalid {
            border-color: red;
        }
        .ng-dirty.invalid {
            border-color: red;
        }
    .label-supply {
        display: inline-block;
        float: left;
        padding-right: 15px;
        padding-left: 15px;
    }
    .clearf:after{
        content:'';
        clear:both;
        height:0;
        display:block;
    }
    .zyzq .tabtn-menu{

        BACKGROUND: #7361e2;
        padding: 4px 10px;
        line-height: 38px;
        height: 46px;
        margin: 0;
        color: white;
    }
    .zyzq .tabtn-menu:nth-child(-n+3){
        background: #fd6f9a;
    }
    .zyzq .tabtn-menu:nth-child(3){
        margin-right: 50px;
    }
    .zyzq .cur-tabtn-menu {
        opacity: 0.6;
    }
</style>
</head>
<!--
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()" class="">
-->
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()" class="body-min-width-new">
    <div class="cooperation-manage" style="overflow-x: scroll;">
      <div class="cooperation-head">&nbsp;
        <label ng-show="!isSuperManager" class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></label>
        <label ng-show="isSuperManager" class="frist-tab" ng-bind="'COMMON_PROVINCEENTERPRISEMANAGE'|translate"></label>&nbsp;<b>&gt;</b>&nbsp;
        <label class="second-tab" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></label>&nbsp;<b>&gt;</b>&nbsp;
          <label ng-if="enterpriseType =='5' && isZYZQ" class="second-tab" ng-bind="'API_HOTLINE_CONTENTMANAGE'|translate"> </label>
          <label ng-if="!(enterpriseType =='5' && isZYZQ)" class="second-tab" ng-bind="'CONTENT_HOTLINE'|translate"></label>
    </div>
      <top:menu ng-if="isSuperManager" chose-index="{{choseIndex}}" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/hotLineColorPrint/hotLineManage"
        list-index="40" apply-val="{{proSupServerType}}"></top:menu>

        <top:menu chose-index="5" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/ProvincialColorPrintHotline/provinceGroupList"
                  list-index="83" ng-if="enterpriseType =='5' && isZYZQ" ng-class="{true:'',false:'second-topmenu zyzq'}[isProvincial]"></top:menu><!-- 分省一级-->

        <div class="cooperation-search">
          <!--<form class="form-inline">
              <div class="form-group col-lg-3 col-xs-4  col-sm-4 col-md-4">
                  <label style="padding-right:30px;" for="contentName" ng-bind="'CONTENTAUDIT_HOTCYCONTENT'|translate"></label>
                  <input ng-model='content' type="text" autocomplete="off" class="form-control" id="contentName" placeholder="{{'HOTLINE_CYCONTENT_KEY'|translate}}" >
              </div>
              <div class="form-group col-lg-1 col-xs-1  col-sm-1 col-md-1">
                  <button ng-click="queryHotlineContentInfoList()" type="submit" class="btn search-btn">
                      <icon class="search-iocn"></icon>
                      <span ng-bind="'COMMON_SEARCH'|translate"></span>
                  </button>
              </div>
          </form>-->
              <form class="form-horizontal">
                  <div class="form-group form-inline">
                      <div class="control-label label-supply">
                          <label for="contentName" ng-bind="'CONTENTAUDIT_HOTCYCONTENT'|translate"></label>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                          <input ng-model='content' type="text" autocomplete="off" class="form-control" id="contentName" placeholder="{{'HOTLINE_CYCONTENT_KEY'|translate}}" >
                      </div>
                      <!--内容编号查询-->
                      <div class="control-label label-supply">
                          <label for="contentName" ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></label>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" >
                          <input type="text" autocomplete="off" class="form-control" id="contentNo"
                                 placeholder="{{'CONTENTAUDIT_INPUTCONTENTNUMBERS'|translate}}" ng-model="initSel.contentNo">
                      </div>
                      <!--移动审核状态-->
                      <div class="control-label label-supply">
                          <label for="contentStatus" ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate"></label>
                      </div>
                      <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" >
                          <select style="max-width:200px;width: 100%;" class="form-control"
                                  ng-model="initSel.auditStatus" id="contentStatus"
                                  ng-options="x.id as x.name for x in auditStatusChoise"></select>
                      </div>
                      <div class="clearf"> </div>
                      <!--业务类型-->
                      <div class="control-label label-supply">
                          <label for="contentSubServType" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
                      </div>
                      <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" >
                          <select class="form-control" id="contentSubServType" style="width: 100%;max-width: 200px;"
                                  ng-model="initSel.subServType"
                                  ng-options="x.id as x.name for x in subServChoise"></select>
                      </div>
                      <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
                          <button ng-click="queryHotlineContentInfoList()" type="submit" class="btn search-btn" style="margin-left: 86px">
                              <icon class="search-iocn"></icon>
                              <span ng-bind="'COMMON_SEARCH'|translate"></span>
                          </button>
                      </div>
                  </div>
              </form>
      </div>
      <div class="add-table">
          <button type="submit" class="btn add-btn" ng-click="addHotlineContent()">
              <icon class="add-iocn"></icon>
              <span style="color:#705de1" ng-bind="'CONTENTAUDIT_ADD1'|translate"></span>
          </button>
          <button id="exportContentInfoList" class="btn add-btn" ng-click="exportContentFile()">
              <icon class="export-icon"></icon><span style="color:#705de1;margin-left: 8px;" ng-bind="'DETAIL_EXPORT'|translate"></span>
          </button>
      </div>

      <div style="margin-left: 20px;margin-bottom: 20px;">
          <p style="font-size: 16px;font-weight: 500;" ng-bind="'CONTENTAUDIT_ALLCOLOR'|translate"></p>
      </div>
      <div class="coorPeration-table">
          <table class="table table-striped table-hover">
              <thead>
                  <tr>
                      <th style="width:6%" ng-bind="'COMMON_ID'|translate"></th>
                      <!-- <th style="width:20%" ng-bind="'HOTLINE_NUMBER'|translate"></th> -->
                      <th style="width:7%" ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate"></th>
                      <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_UNICOM'|translate"></th>
                      <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITSTATE_TELECOM'|translate"></th>
                      <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_MOVE'|translate"></th>
                      <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_UNICOM'|translate"></th>
                      <th style="width:7%;" ng-bind="'CONTENTAUDIT_AUDITADVICE_TELECOM'|translate"></th>
                      <th style="width:7%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
                      <th style="width:15%" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></th>
                      <th style="width:34%" class="adjustable-width" ng-bind="'COMMON_OPERATE'|translate"></th>
                  </tr>
              </thead>
              <tbody>
                  <tr ng-repeat="item in hotContentInfoListData">
                      <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
                      <!-- <td><span title="{{item.contentBelongOrgList[0].hotlineNo}}">{{item.contentBelongOrgList[0].hotlineNo}}</span></td> -->
                      <td style="min-width: 100px"><span title="{{hotlineStatusMap[item.approveStatus]}}">{{hotlineStatusMap[item.approveStatus]}}</span></td>
                      <td><span
                              title="{{unicomApproveStatusMap[item.unicomApproveStatus]}}">{{unicomApproveStatusMap[item.unicomApproveStatus]}}</span>
                      </td>
                      <td><span
                              title="{{unicomApproveStatusMap[item.telecomApproveStatus]}}">{{unicomApproveStatusMap[item.telecomApproveStatus]}}</span>
                      </td>
                      <td><span title="{{item.approveIdea}}">{{item.approveIdea}}</span></td>
                      <td>
                          <span
                                  ng-bind-html="CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType, item.subServType).htmlVersion"
                                  title="{{ CommonUtils.splitStringAndShow(item.unicomApproveIdea,item.servType,item.subServType).titleVersion}}">
                          </span>
                      </td>
                      <td>
                          <span
                                  ng-bind-html="CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType, item.subServType).htmlVersion"
                                  title="{{ CommonUtils.splitStringAndShow(item.telecomApproveIdea,item.servType,item.subServType).titleVersion}}">
                          </span>
                      </td>
                      <td><span title="{{typeMap[item.subServType]}}">{{typeMap[item.subServType]}}</span></td>
                      <td>
                          <!--<span title="{{item.content}}">{{item.content}}</span>-->
                          <span title="{{item.content}}" ng-if="item.signature&&item.signature!=null&&item.signature!=''">【{{item.signature}}】{{item.content}}</span>
                          <span title="{{item.content}}" ng-if="item.signature==null||item.signature==''">{{item.content}}</span>

                      </td>
                      <td>
                          <div class="handle">
                              <ul>
                                  <!-- 删除 -->
                                <li ng-show = "item.approveStatus != '2'" class="delete" ng-click="deleteHotlineContent(item)">
                                    <icon class="delete-icon"></icon>
                                    <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
                                </li>
                                <!-- 编辑 -->
                                <li ng-show="item.thirdpartyType!=1" class="edit" ng-click="updateHotlineContent(item)">
                                    <icon class="edit-icon"></icon>
                                    <span style="color:#705de1" ng-bind="'GROUP_EDIT'|translate"></span>
                                </li>
                                  <!--暂停-->
                                  <li ng-show = "(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.approveStatus == '3' && (item.status == '0' || item.status == '1')" class="suspend"
                                      ng-click="suspendHotlineContent(item,'1')">
                                      <span style="color:#7360e2" ng-bind="'COMMON_SUSPEND'|translate"></span>
                                  </li>
                                  <!--恢复-->
                                  <li ng-show = "(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.approveStatus == '3' && item.status == '3'" class="recovery"
                                      ng-click="suspendHotlineContent(item,'0')">
                                      <span style="color:#ff2549" ng-bind="'COMMON_RECOVERY'|translate"></span>
                                  </li>
                                <!-- 号码新增 -->
                                <li class="edit" ng-click="addHotLinePop(item)">
                                    <icon class="add-icon"></icon>
                                    <span style="color:#705de1" ng-bind="'号码新增'|translate"></span>
                                </li>
                                <!-- 号码导入 -->
                                <li  class="edit" ng-click="importHotLinePop(item)">
                                    <icon class="import-icon"></icon>
                                    <span style="color:#705de1" ng-bind="'号码导入'|translate"></span>
                                </li>
                                <!-- 号码管理 -->
                                <li  class="edit" ng-click="manageHotLinePop(item)">
                                    <icon class="manage-icon"></icon>
                                    <span style="color:#705de1" ng-bind="'号码管理'|translate"></span>
                                </li>
                              </ul>
                          </div>
                      </td>
                  </tr>
                  <tr ng-show="hotContentInfoListData.length<=0">
                    <td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
                  </tr>
              </tbody>
          </table>
      </div>

      <div>
          <ptl-page tableId="0" change="queryHotlineContentInfoList('justPage')"></ptl-page>
      </div>

    </div>

      <!-- 新增(编辑)热线内容弹窗 -->
      <div class="modal fade" id="addHotlineContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"  style="overflow: auto">
          <div class="modal-dialog" role="document">
              <div class="modal-content">
                  <div class="modal-header">
                      <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                      <h4 class="modal-title" ng-if="operate == 'add' " ng-bind="'CONTENTAUDIT_ADD2'|translate"></h4>
                      <h4 class="modal-title" ng-if="operate == 'update' " ng-bind="'CONTENTAUDIT_MODIFY'|translate"></h4>
                  </div>
                  <div class="cooper-tab">
                      <form class="form-horizontal" name="myForm" novalidate >
                          <div class="form-group" style="padding-top:34px">
                              <label for="inputEmail3" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                  <icon>*</icon>
                                  <span ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></span>
                              </label>
                              <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                  <select id="select" class="form-control" ng-model="addHotlineContentInfo.subServType"
                                  ng-options="x.id as x.name for x in subServTypeChoise" ng-disabled="operate =='update'" ng-blur="changeSubServerType()"></select>
                              </div>
                          </div>
                          <!-- <div class="form-group" style="padding-top:34px">
                              <label for="inputEmail3" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label" ng-bind="'HOTLINE_NUMBER'|translate"></label>
                              <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                  <select class="form-control" ng-model="addHotlineContentInfo.hotlineNo"
                                      ng-options="x.hotlineNo as x.hotlineNo for x in hotlineList" ng-disabled="operate =='update'"></select>
                              </div>
                          </div> -->
                          <div class="form-group" style="padding-top:5px">
                              <label  for="inputPassword3" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                  <icon>*</icon><span ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></span>
                              </label>
                              <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                  <textarea  class="form-control" rows="6" style="margin: 0px; width: 267px; height: 98px;"
                                      name="colorContent"
                                      ng-class="{'border-red':!contentVali||isSensitive}"
                                      placeholder="{{msg}}"
                                      ng-model="addHotlineContentInfo.content"
                                      ng-change="checkHotlineContent()"
                                      ng-blur="sensitiveCheck()"
                                  >
                                  </textarea>

                                  <span style="color:red" ng-show="!contentVali||isSensitive">
                                      <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                      <span ng-show='contentDesc'>{{contentDesc|translate}}</span>
                                      <span ng-show='isSensitive'>{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
                                  </span>
                              </div>
                              <div class="rest-count col-lg-6 col-xs-6  col-sm-6 col-md-8" style="
							    font-size: 9px;
							    margin-left: 150px;
							    color: #999;
							    margin-top: 5px;
							 ">
	                              <p>主、被叫屏显：均仅限投递身份信息，可适当附加祝福语。不能投递任何通知类、营销类、公益宣传类的内容。</br>“范例”：【咪咕新空】给您来电，请您接听。祝您生活愉快。</p>
	                          </div>
                          </div>

                          <!-- 运营商 -->
                          <div class="form-group platforms">
                              <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                  <icon>*</icon><span ng-bind="'PLATFORM'|translate"></span>
                              </label>
                              <div class="col-lg-8 col-xs-8  col-sm-8 col-md-8" ng-click="checkSignatureRequired()" >
                                  <li class="check-li">
                                      <span class="check-btn checked-btn"> </span>
                                      {{'MOBILE'|translate}}
                                  </li>
                                  <li class="check-li">
                                      <span class="check-btn checked-btn"> </span>
                                      {{'UNICOM'|translate}}
                                  </li>
                                  <li class="check-li" >
                                      <span class="check-btn checked-btn" > </span>
                                      {{'TELECOM'|translate}}
                                  </li>

                              </div>
                          </div>
                          <!-- 场景描述 -->
				            <div class="form-group platforms" ng-show="addHotlineContentInfo.subServType == '4' 
				            	&& signatureRequired =='1'">
				                <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
											<span style="color:red"
				                                  ng-bind="'*'|translate" >
											</span>
				                    <span ng-bind="'SENCE_DESC'|translate"></span></label>
				                 <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
											<textarea style="height:80px;"
					                                  placeholder="请输入场景描述1~128字"
					                                  class="form-control"
					                                  rows="3" name="sceneDesc" ng-model="addHotlineContentInfo.sceneDesc"
					                                  ng-maxlength="128"
					                                  ng-required="addHotlineContentInfo.subServType == '4' 
				            	&& signatureRequired =='1'"
					                                  ng-blur="sensitiveChecknew(addHotlineContentInfo.sceneDesc,1)"
					                                  ng-disabled="operateType=='detail'"></textarea>
					                    <span style="color:red"
					                          ng-show="myForm.sceneDesc.$dirty && myForm.sceneDesc.$invalid">
												<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
					                                 align="absmiddle">
												<span ng-show="myForm.sceneDesc.$error.required && addHotlineContentInfo.subServType=='4'"
					                                  ng-bind="'SCENEDESC_REQUIRE'|translate"></span>
												<span ng-show="myForm.sceneDesc.$error.maxlength"
					                                  ng-bind="'SCENEDESC_WORDSLIMIT_256'|translate"></span>
											</span>
					                </div>
				            </div>
                          <!--REQ-113 -->
                          <!-- 签名 -->
                          <div class="form-group platforms">
                              <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
							<span style="color:red"
                                  ng-bind="'*'|translate"
                                  ng-model="signatureRequired"
                                  ng-show="signatureRequired =='1'&&noSign == '1'">
							</span>
                                  <span ng-bind="'SIGNATURE'|translate"></span></label>
                              <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
                                  <input type="text" class="form-control" placeholder="请输入1~67字" rows="1" name="signature"
                                         ng-model="addHotlineContentInfo.signature"
                                         ng-required="signatureRequired==='1'&&noSign === '1'"
                                         maxlength="67"
                                         ng-disabled="operateType=='detail'"
                                         ng-change="checkSignAndContent()"/>
                                  <span style="color:red" ng-show="myForm.signature.$invalid&&myForm.signature.$dirty">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                        <span style="color:red" ng-show="myForm.signature.$error.required" ng-bind="'SIGNATURE_REQUIRED'|translate"></span>
                                        <span style="color:red" ng-show="myForm.signature.$error.pattern&&!myForm.signature.$error.maxlength" ng-bind="'SIGNATURE_PATTERN_ERROR'|translate"></span>
                                        <span style="color:red" ng-show="myForm.signature.$error.maxlength" ng-bind="'SIGNATURE_MAXLENTH_67'|translate"></span>
                                    </span>
                              </div>
                          </div>
                          <!-- 所属行业 -->
                          <div class="form-group industry">
                              <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label"><icon>*</icon>
                                  <span ng-bind="'ENTERPRISE_INDUSTRY'|translate"></span></label>
                              <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
                                  <select class="form-control"
                                          name="industry1"
                                          required
                                          ng-model="selectedIndustry"
                                          ng-options="x.industryName for x in industryList"
                                          ng-change="changeIsSensitive(selectedIndustry)"
                                  >
                                      <option value="" ng-bind="" ng-show="true"></option>
                                  </select>
                                  <span ng-if="isSensitiveIndustry=='1' && operateType !='detail' && selectedIndustry.industryName == '金融/银行/保险/会计'" style="color:#c3c3c3" class="redFont">
                        		        {{selectedIndustry.industryName}}必须上传营业执照，涉及银行业务金额、利率等的彩印内容，须提供银行授权
                                  </span>
                                  <span ng-if="isSensitiveIndustry=='1' && operateType !='detail' && selectedIndustry.industryName != '金融/银行/保险/会计'" style="color:#c3c3c3" class="redFont">
                        		        {{selectedIndustry.industryName}}必须上传营业执照
                                  </span>
                                  <span ng-if="isSensitiveIndustry=='2'&& operateType !='detail'" style="color:#c3c3c3" class="redFont">{{selectedIndustry.industryName}}必须上传授权函或业务协议</span>
                                  <span ng-if="isSensitiveIndustry=='3'&& operateType !='detail'" style="color:#c3c3c3" class="redFont">{{selectedIndustry.industryName}}必须上传营业执照和版号证明</span>
                                  <span style="color:red" class="uplodify-error-img" ng-show="selectedIndustryErrorInfo"></span>
                                  <span  style="color:red" class="redFont" ng-bind="selectedIndustryErrorInfo|translate" ng-show="selectedIndustryErrorInfo"></span>
                              </div>
                          </div>

                          <!-- 其他资质 -->
                          <div class="form-group industry" style="margin-bottom: 0px;">
                              <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                  <icon ng-show="isSensitiveIndustry =='2' || isSensitiveIndustry =='3'">*</icon><span ng-bind="'ENTERPRISE_CERTIFICATEURLLIST'|translate"></span>
                              </label>
                              <div style="display: table;" class="col-lg-6 col-xs-7 col-sm-7 col-md-7">
                                  <div class="ctn-pic-list" ng-repeat="item in colorContentAndFileList" style="height:50px">
                                      <div class="pic-wrapper" ng-if="item.frameFileUrl">
                                          <!--                                <img style="float:left;max-width: 250px;border: 1px solid darkgrey;"-->
                                          <!--                                     ng-src="{{item.formatFrameFileUrl}}" alt="">-->
                                          <a ng-click="item.frameFileUrl?exportFile(item.frameFileUrl):false"
                                             title="{{item.filename}}" style="display: inline-block;width: 250px;{{operateType!='detail' ? 'overflow: hidden;' :''}};white-space: nowrap;text-overflow: ellipsis;" ng-style="" class="ng-binding">
                                              {{item.filename}}</a>
                                          <button ng-hide="operateType=='detail'" ng-click="deleteCtnOrFile($index)"
                                                  type="submit" class="btn btn-primary search-btn "
                                                  style="position: absolute;left: 290px;">
                                              <span ng-bind="'COMMON_DELETE'|translate"></span></button>
                                      </div>
                                  </div>
                                  <!--                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"></label>-->
                                  <div ng-show="operateType!='detail'" style="margin-left: 0;padding-left: 0;width:100%;" ng-required="isSensitiveIndustry=='2' || isSensitiveIndustry =='3'"
                                       class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview">
                                      <cy:uploadify ng-show="fileLength<6" ng-if="operateType !='detail'"
                                                    filelistid="fileList2" filepickerid="filePicker_certi" accepttype="accepttype2"
                                                    uploadifyid="uploadifyid2" validate="isValidate2" filesize="filesize2"
                                                    mimetypes="mimetypes2" formdata="uploadParam2" uploadurl="uploadurl2" desc="uploadCertiDesc"
                                                    numlimit="numlimit2" createthumbnail="isCreateThumbnail2" style="float: left;">
                                      </cy:uploadify>
                                      <!-- 图片最大张数时展示的上传图片框(只用于展示，没有作用) -->
                                      <button ng-required="isSensitiveIndustry=='2' || isSensitiveIndustry =='3'" disabled style="float:left;margin-right: 15px;"
                                              ng-show="fileLength>5" type="submit"
                                              class="btn btn-primary search-btn">
                                          <span style="float:left" ng-bind="'上传文件'|translate"></span></button>
                                      <br>
                                      <div style="color: #c3c3c3;" ng-show="fileLength>5" class="ng-binding">
                                          最多支持6张图片，仅支持jpg，bmp，png，jpeg格式
                                      </div>
										<div class="downloadRow col-sm-12">
											<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】号码使用声明书.docx" style="margin-left: -15px;"
												 ng-bind="'NUMBER_USE_TEMP_DOWNLOD'|translate"></a>
										</div>
<!--										<div class="downloadRow col-sm-12">-->
<!--											<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】品牌归属声明书.docx" style="margin-left: -15px;"-->
<!--												 ng-bind="'BRAND_ATTR_TEMP_DOWNLOD'|translate"></a>-->
<!--										</div>-->
										<div class="downloadRow col-sm-12">
											<a target="_blank" href="/qycy/ecpmp/assets/签名授权书-移动模板.docx" style="margin-left: -15px;"
												 ng-bind="'SIGN_AUTH_TEMP_DOWNLOD'|translate"></a>
										</div>
                                  </div>
                              </div>



                          </div>

                          <!-- 营业执照 -->
                          <div class="form-group industry" style="margin-bottom: 0px;">
                              <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <span style="color:red"
                                  ng-bind="'*'|translate"
                                  ng-model="isSensitiveIndustry"
                                  ng-show="isSensitiveIndustry =='1' || isSensitiveIndustry =='3'">
                            </span>
                                  <span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span></label>
                              <div class="col-lg-9 col-xs-9  col-sm-9 col-md-9">
                                  <cy:uploadify filelistid="fileList_" filepickerid="filePicker_" accepttype="accepttype_"
                                                uploadifyid="uploadifyid_2" validate="isValidate_" filesize="filesize_" mimetypes_="mimetypes_"
                                                formdata="uploadParam_"  uploadurl="uploadurl_" desc="uploadDesc_" numlimit="numlimit_"
                                                urllist="urlList_" createthumbnail="isCreateThumbnail_" namelistid="nameList_"
                                                ng-if="showUpload" >
                                  </cy:uploadify>
                                  <input class="form-control" name="businessLicenseURL_" ng-model="businessLicenseURL_" ng-required="isSensitiveIndustry=='1' || isSensitiveIndustry =='3'" ng-hide="true">
                              </div>
                          </div>
                        <div class="form-group" style="margin-bottom: 0px;margin-left:50px;" ng-show="businessLicenseURL_!=''">
                            <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label"></label>
                            <button type="button" class="btn btn-primary search-btn" ng-click='showBusinessURL()'>预览</button>
                        </div>

                      </form>
                  </div>
                  <div class="modal-footer">
                      <button type="submit" ng-disabled="contentDesc||!addHotlineContentInfo.content||subServTypeChoise.length===0||myForm.$invalid||(colorContentAndFileList.length==0&&(isSensitiveIndustry=='2' || isSensitiveIndustry =='3'))" class="btn btn-primary search-btn" ng-bind="'COMMON_SUBMIT'|translate" ng-click="diffNetAuthMaterialsConfirm()"></button>
                      <button ng-hide="true" type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" ng-click="goback()" id="addHotlineContentCancel" ng-bind="'COMMON_BACK'|translate"></button>
                  </div>
              </div>
          </div>
      </div>

	  <div class="modal fade bs-example-modal-sm" id="showBusinessURL" tabindex="-1" style="overflow: auto;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" ng-click="hideBusinessURL()"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">预览</h4>
                </div>
                <div class="modal-body">
                    <div class="img-wrap" ng-repeat='item in urlList_2'>
                        <img ng-src="{{item}}" alt="">
                    </div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="button" class="btn "  ng-click="hideBusinessURL()" >确定</button>
                </div>
            </div>
        </div>
    </div>

      <!-- 删除热线内容弹窗 -->
      <div class="modal fade bs-example-modal-sm" id="deleteHotlineContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
          <div class="modal-dialog modal-sm" role="document">
              <div class="modal-content" style="width:390px">
                  <div class="modal-header">
                      <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                      <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
                  </div>
                  <div class="modal-body">
                      <div class="text-center">
                          <p style='font-size: 16px;color:#383838' ng-bind="'HOTLINE_SUREDELETEHOTLINE'|translate"></p>
                      </div>
                  </div>
                  <div class="modal-footer">
                      <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate" ng-click="delHotlineContent()"></button>
                      <button id="deleteHotlineContentCancel" type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addHotlineContentCancel" ng-bind="'NO'|translate"></button>
                  </div>
              </div>
          </div>
      </div>

          <!--号码导入弹出框-->
    <div class="modal fade" id="impotHotLineNoPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_INPUTALL'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal form-inline">
                            <div class="form-group" style="width: 596px;">
                                <div class="form-group" style="width: 596px;">
                                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;"
                                        ng-bind="'COMMON_FILENAME'|translate"></label>
                                    <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
                                        <input type="text" class="form-control" ng-model="fileName" id="addGroupName"
                                            placeholder="请导入.xlsx表格格式文件" style="width: 100%;" ng-disabled="true" />
                                        <!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
                                    </div>
                                    <cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
                                        uploadifyid="uploadifyid" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
                                        formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
                                        urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
                                    </cy:uploadifyfile>
                                </div>
                                <div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 92px;" ng-show="errorInfo==''"></div>
                                <div style="color:#ff0000;margin: 10px 0 0 50px;" ng-show="errorInfo!=''">
                                    <span class="uplodify-error-img"></span>
                                    <span ng-bind="errorInfo|translate"></span>
                                </div>
                                <div class="downloadRow col-sm-10" style="margin: 20px 0 0 29px;">
                                    <a target="_blank" href="/qycy/ecpmp/assets/importHotLineRel.xlsx" class="downMod" style="margin-right: 40px;"
                                        ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
                                    <span style="color: #705de1 !important; font-size: 12px;">提示：</span><span style="color: #705de1 !important; font-size: 12px;" ng-bind="'TEMPLATE_IMPORTTIP'|translate"></span>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary search-btn" ng-click="commitImportHotLineNo()" ng-disabled="errorInfo!==''||fileUrl==''">确认导入</button>
                        <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addOrgCancel">返回</button>
                    </div>
                </div>
            </div>
        </div>

    <!--号码新增弹出框-->
	<div class="modal fade bs-example-modal-sm" id="addHotlinePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div role="document"
                ng-class="{'modal-dialog':1==1,'dialog-690':pageInfo[1].totalPage<=3,
                    'dialog-800':pageInfo[1].totalPage>3 && pageInfo[1].totalPage<7,
                    'dialog-900':pageInfo[1].totalPage>=7 && pageInfo[1].totalPage<10,
                    'dialog-1000':pageInfo[1].totalPage>=10}">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'HOTLINE_ADD'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                            <div cla ss="form-group">
                                <div class="row">
                                    <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                                 ng-bind="'HOTLINE_NUMBER'|translate"></label>
                                    <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5">
                                        <input type="text" class="form-control"
                                                     placeholder="{{'HOTLINE_PLEASEINPUTHOTLINENUMBER'|translate}}"
                                                     ng-model="hotlineMsisdnAdd">
                                    </div>
                                    <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                        <button class="btn bg_purple search-btn btn1" ng-click="queryHotLineList(selectedItem,'search')">
                                            <span class="icon btnIcon search"></span>
                                            <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="add-table" style="margin-left:12px;margin-top:12px;">
                            <button class="btn" ng-disabled="!hasChoseAddHotLine"
                                            style="width:105px; margin-left:10px;color:#7360e1"
                                            type="button" ng-click="singleOrBatchAddHotLine('batch')" ng-bind="'CONTENT_BATCH_ADD'|translate"></button>
                            <!-- <button class="btn" style="width:105px;color:#7360e1" type="button" ng-click="exportMember()"
                                            ng-bind="'GROUP_MEMBEXPORT'|translate"></button> -->
                        </div>

                        <div class="" style="max-height: 530px;overflow: auto">
                            <table class="table table-striped table-hover">
                                <thead>
                                <tr>
                                    <th style="padding-left:30px;width: 10%;"><input type="checkbox" ng-model="chooseAllAddHotLine" ng-click="selectAllHotLine()"></th>
                                    <th style="padding-left:30px" ng-bind="'HOTLINE_NUMBER'|translate"></th>
                                    <th style="padding-left:30px;" ng-bind="'COMMON_OPERATE'|translate"></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="item in hotlineList">
                                    <td><input type="checkbox"  ng-model="item.checked"></td>
                                    <td><span title="{{item.hotlineNo}}">{{item.hotlineNo}}</span></td>
                                    <td style="font-size: small;">
                                        <a ng-click="singleOrBatchAddHotLine('single',item)" ng-bind="'COMMON_ADD'|translate"></a>
                                    </td>
                                </tr>
                                <tr ng-show="hotlineList.length<=0">
                                    <td style="text-align:center" colspan="3" ng-bind="'COMMON_NODATA'|translate"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ptl-page tableId="1" change="queryHotLineList(selectedItem,'justPage')"></ptl-page>
                        </div>
                    </div>
                    <div class="modal-footer">
                    </div>
                </div>
            </div>
        </div>

    <!--号码管理弹出框-->
	<div class="modal fade" id="manageHotLinePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div role="document"
                ng-class="{'modal-dialog':1==1,'dialog-690':pageInfo[2].totalPage<=3,
                    'dialog-800':pageInfo[2].totalPage>3 && pageInfo[2].totalPage<7,
                    'dialog-900':pageInfo[2].totalPage>=7 && pageInfo[2].totalPage<10,
                    'dialog-1000':pageInfo[2].totalPage>=10}">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'HOTLINE_MANAGEMENT'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <div class="form-horizontal">
                            <div cla ss="form-group">
                                <div class="row">
                                    <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                                 ng-bind="'HOTLINE_NUMBER'|translate"></label>
                                    <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5">
                                        <input type="text" class="form-control" id=""
                                                     placeholder="{{'HOTLINE_PLEASEINPUTHOTLINENUMBER'|translate}}"
                                                     ng-model="hotlineMsisdnDel">
                                    </div>
                                    <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                        <button class="btn bg_purple search-btn btn1" ng-click="queryContentRelObjectList(selectedItem,'search')">
                                            <span class="icon btnIcon search"></span>
                                            <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="add-table" style="margin-left:12px;margin-top:12px;">
                            <button class="btn" ng-disabled="!hasChoseDelHotLine"
                                            style="width:105px; margin-left:10px;color:#7360e1"
                                            type="button" ng-click="sureDelHotLine('batch')" ng-bind="'GROUP_BATCHDELETE'|translate"></button>
                            <!-- <button class="btn" style="width:105px;color:#7360e1" type="button" ng-click="exportMember()"
                                            ng-bind="'GROUP_MEMBEXPORT'|translate"></button> -->
                        </div>

                        <div class="" style="max-height: 530px;overflow: auto">
                            <table class="table table-striped table-hover">
                                <thead>
                                <tr>
                                    <th style="padding-left:30px;width: 10%;"><input type="checkbox" ng-model="chooseAllDelHotLine" ng-click="selectAllDelHotLine()"></th>
                                    <th style="padding-left:30px" ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></th>
                                    <th style="padding-left:30px" ng-bind="'HOTLINE_NUMBER'|translate"></th>
                                    <th style="padding-left:30px;" ng-bind="'COMMON_OPERATE'|translate"></th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr ng-repeat="item in contentBelongOrgList">
                                    <td><input type="checkbox" ng-model="item.checked"></td>
                                    <td><span title="{{selectedItem.content}}">{{selectedItem.content}}</span></td>
                                    <td><span title="{{item.hotlineNo}}">{{item.hotlineNo}}</span></td>
                                    <td style="font-size: small;">
                                        <a ng-click="sureDelHotLine('single',item)" ng-bind="'COMMON_DELETE'|translate"></a>
                                    </td>
                                </tr>
                                <tr ng-show="contentBelongOrgList.length<=0">
                                    <td style="text-align:center" colspan="4" ng-bind="'COMMON_NODATA'|translate"></td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div>
                            <ptl-page tableId="2" change="queryContentRelObjectList(selectedItem,'justPage')"></ptl-page>
                        </div>
                    </div>
                    <div class="modal-footer">
                    </div>
                </div>
            </div>
        </div>

    <!--热线号码管理确认删除框弹出框-->
	<div class="modal fade" id="deleteHotLinePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                    </div>
                    <div class="modal-body">
                        <form class="form-horizontal">
                            <div class="form-group">
                                <div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
                                    <div class="text-center">
                                        <span ng-bind="'COMMON_DEL_SURE'|translate"></span>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary search-btn" ng-click="singleOrBatchDelHotLine(singleOrBatch)"
                                        ng-bind="'COMMON_OK'|translate"></button>
                        <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="delMemCancel"
                                        ng-bind="'COMMON_BACK'|translate"></button>
                    </div>
                </div>
            </div>
        </div>


      <!--小弹出框-->
      <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
          <div class="modal-dialog modal-sm" role="document">
              <div class="modal-content">
                  <div class="modal-header">
                      <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                      <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                  </div>
                  <div class="modal-body">
                      <div class="text-center">
                          <p style='font-size: 16px;color:#383838'>
                              {{tip|translate}}
                          </p>
                      </div>
                  </div>
                  <div class="modal-footer">
                      <button type="submit" class="btn btn-primary" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                  </div>
              </div>
          </div>
      </div>

	<!--提交异网授权材料二次确认弹出框-->
	<div class="modal fade" id="diffNetAuthMaterials" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal">
						<div class="form-group">
							<div class="row" style="width: 400px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
								<div class="text-center">
									<span ng-bind="'DIFFNET_AUTH_MATERIALS'|translate"></span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer" style="text-align:center">
	                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_UPLOADED'|translate"
	                        ng-click="diffNetAuthMaterialsUploaded()"></button>
	                <button type="submit" id="diffNetAuthMaterialsCancel" class="btn btn-back" data-dismiss="modal"
	                        aria-label="Close" ng-bind="'COMMON_NOT_UPLOAD'|translate"></button>
				</div>
			</div>
		</div>
	</div>
  </body>
</html>
