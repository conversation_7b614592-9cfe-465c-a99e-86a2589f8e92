<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.BatchsubTaskMemberMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.BatchsubTaskMemberWrapper" >
        <result column="ID" property="id" jdbcType="INTEGER" />
        <result column="taskID" property="taskID" jdbcType="INTEGER" />
        <result column="provinceID" property="provinceID" jdbcType="VARCHAR" />
        <result column="msisdn" property="msisdn" jdbcType="VARCHAR" />
        <result column="productCode" property="productCode" jdbcType="VARCHAR" />
        <result column="subServType" property="subServType" jdbcType="INTEGER" />
        <result column="content" property="content" jdbcType="VARCHAR" />
        <result column="businessLicenseURL" property="businessLicenseURL" jdbcType="VARCHAR" />
        <result column="dealStatus" property="dealStatus" jdbcType="INTEGER" />
        <result column="dealTime" property="dealTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, taskID, provinceID, msisdn, productCode, subServType, content, businessLicenseURL, dealTime, dealStatus
    </sql>

	<insert id="batchInsert">
		insert into ecpm_t_h5_batchsub_task_member
		(
		taskID, provinceID, msisdn, productCode, subServType, content, businessLicenseURL, dealTime, dealStatus
		)
		values
		<foreach collection="list" item="batchsubTaskMemberWrapper"
			separator=",">
		(
		#{batchsubTaskMemberWrapper.taskID},
		#{batchsubTaskMemberWrapper.provinceID},
		#{batchsubTaskMemberWrapper.msisdn},
		#{batchsubTaskMemberWrapper.productCode},
		#{batchsubTaskMemberWrapper.subServType},
		#{batchsubTaskMemberWrapper.content},
		#{batchsubTaskMemberWrapper.businessLicenseURL},
		#{batchsubTaskMemberWrapper.dealTime},
		#{batchsubTaskMemberWrapper.dealStatus}
		)
		</foreach>
	</insert>
	 <delete id="delete" parameterType="java.lang.String">
		delete from
		ecpm_t_h5_batchsub_task_member where taskID=#{taskID}
	</delete>
	 <select id="query" resultMap="BaseResultMap">
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_h5_batchsub_task_member
         <trim prefix="where" prefixOverrides="and|or">
         	taskID = #{taskId}
			<if test="dealStatus != null and dealStatus.size()>0">
				and dealStatus in
				<foreach item="status" index="index" collection="dealStatus"
					open="(" separator="," close=")">
					#{status}
				</foreach>
			</if>
		</trim>
    </select>
    <update id="updateStatus" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.BatchsubTaskMemberWrapper">
    	update ecpm_t_h5_batchsub_task_member
    	set
    	dealStatus = #{dealStatus}
    	where
    	dealStatus = 1 and msisdn = #{msisdn}
    	
    </update>
    <update id="batchUpdateStatus">
	    <foreach collection="list" item="item" index="index" open=""
			close="" separator=";">
			update ecpm_t_h5_batchsub_task_member
			set
			<trim suffixOverrides=","
				suffix="where ID=#{item.id}">
				<if test="item.dealTime != null">
					dealTime=#{item.dealTime},
				</if>
				<if test="item.dealStatus != null">
					dealStatus=#{item.dealStatus},
				</if>
			</trim>
		</foreach>
    </update>
    
    <select id="queryCount" resultType="java.lang.Integer">
        select 
        count(1)
        from ecpm_t_h5_batchsub_task_member
        where
        taskID = #{taskId}
		<if test="dealStatus != null and dealStatus.size()>0">
			and dealStatus in
			<foreach item="status" index="index" collection="dealStatus"
				open="(" separator="," close=")">
				#{status}
			</foreach>
		</if>
    </select>
    
    <select id="queryAndContentCount" resultType="java.lang.Integer">
        select 
        count(1)
        from ecpm_t_h5_batchsub_task_member a LEFT JOIN ecpm_t_content b ON a.msisdn = b.enterpriseCode AND a.subServType = b.subServType AND (b.status != 2 or b.status is null)
        where
		a.taskID = #{taskId}
		<if test="dealStatus != null and dealStatus.size()>0">
			and a.dealStatus in
			<foreach item="status" index="index" collection="dealStatus"
				open="(" separator="," close=")">
				#{status}
			</foreach>
		</if>
		<if test="msisdn != null">
			and a.msisdn like "%"#{msisdn}"%"
		</if>
		<if test="approveStatus != null">
			and b.approveStatus=#{approveStatus}
		</if>
		<if test="subServType != null">
			and a.subServType=#{subServType}
		</if>
		order by msisdn desc
		<if test="pageNum != null and pageSize != null">
			limit #{pageNum},#{pageSize}
		</if>
    </select>
    
    <select id="queryAndContent" resultType="com.huawei.jaguar.dsdp.ecpm.model.H5BatchSubMember">
        select 
       	a.id, a.taskID, a.provinceID, a.msisdn, a.productCode, a.subServType, a.content, a.businessLicenseURL, a.dealTime, a.dealStatus, 
       	b.approveStatus, b.approveIdea
        from ecpm_t_h5_batchsub_task_member a LEFT JOIN ecpm_t_content b ON a.msisdn = b.enterpriseCode AND a.subServType = b.subServType AND (b.status != 2 or b.status is null)
        where
		a.taskID = #{taskId} 
		<if test="dealStatus != null and dealStatus.size()>0">
			and a.dealStatus in
			<foreach item="status" index="index" collection="dealStatus"
				open="(" separator="," close=")">
				#{status}
			</foreach>
		</if>
		<if test="msisdn != null">
			and a.msisdn like "%"#{msisdn}"%"
		</if>
		<if test="approveStatus != null">
			and b.approveStatus=#{approveStatus}
		</if>
		<if test="subServType != null">
			and a.subServType=#{subServType}
		</if>
		order by msisdn desc
		<if test="pageNum != null and pageSize != null">
			limit #{pageNum},#{pageSize}
		</if>
    </select>
</mapper>