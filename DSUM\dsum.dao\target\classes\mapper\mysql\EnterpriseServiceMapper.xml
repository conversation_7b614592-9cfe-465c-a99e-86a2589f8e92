<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.EnterpriseServiceMapper">
    <resultMap id="enterpriseWrapper" type="com.huawei.jaguar.dsum.dao.domain.EnterpriseServiceWrapper">
        <result property="id" column="id" />
        <result property="enterpriseID" column="enterpriseID" />
        <result property="servType" column="servType" />
        <result property="subServType" column="subServType" />
        <result property="subServType" column="subServType" />
        <result property="effectiveTime" column="effectiveTime" />
        <result property="expiryTime" column="expiryTime" />
        <result property="reserved1" column="reserved1" />
        <result property="reserved2" column="reserved2" />
        <result property="reserved3" column="reserved3" />
        <result property="reserved4" column="reserved4" />
        <result property="reserved5" column="reserved5" />
        <result property="reserved6" column="reserved6" />
        <result property="reserved7" column="reserved7" />
        <result property="reserved8" column="reserved8" />
        <result property="reserved9" column="reserved9" />
        <result property="reserved10" column="reserved10"/>
        
    </resultMap>



    <insert id="insertEnterpriseService">
        INSERT INTO `dsum_t_enterprise_service`( `enterpriseID`, `servType`, `subServType`, `effectiveTime`, `expiryTime`)
        VALUES (#{enterpriseID}, #{servType}, #{subServType}, #{effectiveTime}, #{expiryTime})
	</insert>
    <delete id="deleteEnterpriseService" parameterType="java.util.List">
        delete from dsum_t_enterprise_service
        where enterpriseID = #{enterpriseID}
          and servType =  #{servType}
          and subServType = #{subServType}
    </delete>
</mapper>