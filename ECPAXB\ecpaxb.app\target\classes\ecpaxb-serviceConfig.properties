axb.need.risktel.enterprise=
axb.need.delivery.enterprise=30000164
axb.model=102
axb.unbind.url=http://north.spn.caihcom.com:9101/spn/secure/v2/axb
axb.bind.url=http://north.spn.caihcom.com:9101/spn/secure/v2/axb
axb.bind.appkey=MGDMT
axb.bind.secret=HaQ6r52EFzEm4Ia6

axb.need.risktel.mch_id=213123
axb.need.risktel.url=http://10.124.129.192:8882/v3/risktel
axb.need.risktel.appkey=dawdawd



#AXB推送话单
task.AXBPushReportTask.taskSwitch=1
task.AXBPushReportTask.fixedDelay=0 4 3 * * ?
task.AXBPushReportTask.taskName=AXBPushReportTask
task.AXBPushReportTask.taskKey=ecpaxb:AXBPushReportTask
task.AXBPushReportTask.expireMillis=1000
task.AXBPushReportTask.filepath=/home/<USER>/ecpaxb_container/modules/ecpaxb/sftpDetail/billBak/axb/push/
task.AXBPushReportTask.fileSize=1000
#AXB投递话单
task.AXBDeliveryTask.taskSwitch=1
task.AXBDeliveryTask.fixedDelay=0 13 3 * * ?
task.AXBDeliveryTask.taskName=AXBDeliveryTask
task.AXBDeliveryTask.taskKey=ecpaxb:AXBDeliveryTask
task.AXBDeliveryTask.expireMillis=1000
task.AXBDeliveryTask.filepath=/home/<USER>/ecpaxb_container/modules/ecpaxb/sftpDetail/billBak/axb/delivery/
task.AXBDeliveryTask.fileSize=1000



elasticsearch.server.hotlineDeliveryTableName=delivery_detail
elasticsearch.server.cardAndAdvertisingTableName=delivery_record