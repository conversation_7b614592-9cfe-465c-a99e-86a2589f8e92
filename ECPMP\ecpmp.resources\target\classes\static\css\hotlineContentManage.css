    body {
        background: #f2f2f2;
    }
    .table th{
        width:12.5%;
        min-width: 100px;
    }
    table{
        table-layout:fixed;
    }
    table td{
        width:100%;
        word-break:keep-all;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
    }
    .form-horizontal .control-label {
			padding-top: 7px;
			margin-bottom: 0;
			text-align: right;
		}
		.container-fluid{
			padding-left: 0;padding-right: 0;
		}
    .modal-footer{
        text-align: center;
    }
    .fontGreen{
        color: rgb(48, 147,25)
    }
    .fontRed{
        color:rgb(252,70,93);
    }
    .cooperation-head {
        padding: 20px;
    }
    
    .cooperation-head .frist-tab {
        font-size: 16px;
    }
    
    .cooperation-head .second-tab {
        font-size: 14px;
    }
    .cooperation-nav{
        margin-bottom: 15px;
        margin-left: 20px;
    }
    .cooperation-manage{
        min-width: 1024px;
    }
    .cooperation-manage .form-inline {
        margin: 0 20px;
        background: #fff;
        border-radius: 4px;
        padding: 20px 0px 20px 10px;
        overflow: hidden;
    }
    
    .cooperation-manage label {
        font-weight: normal;
    }
    .btn-back {
        border: 1px solid #cccccc;
    }
    .cooperation-manage .form-group {
        /* width: 368px; */
        /* margin-right: 20px; */
        min-width: 255px;
    }
    .form-group input {
        /* width: 280px; */
        /* margin-left: 20px; */
    }
    .form-control:focus {
        border-color: #7360e1;
    }
    .form-group label{
        height: 34px;
        margin-bottom: 0
    }
    .cooperation-manage .form-group select{
        /* width: 180px; */
        /* margin-left: 20px; */
    }
    
    .cooperation-manage .search-btn {
        background-image: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
        color: #fff;
    }
    
    .cooperation-manage .btn .search-iocn {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url(../assets/images/btnIcons.png)no-repeat;
        vertical-align: middle;
    }
    
    .cooperation-manage .add-table {
        margin: 20px;
    }
    
    .cooperation-manage .add-table .add-btn {
        background: #fff;
        color: #000;
    }
    
    .cooperation-manage .add-table .add-btn .add-iocn {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url(../assets/images/btnIcons.png)no-repeat;
        vertical-align: middle;
        background-position: -20px 0;
    }
    .form-group .control-label icon {
        color: #ff254c;
        vertical-align: sub;
        margin-right: 2px;
    }
    .coorPeration-table {
        margin: 0px 20px;
        background: #fff;
        min-width: 850px;
    }
    .cooperation-manage .coorPeration-table th td{
        padding-left: 30px !important;        
    }
    .table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td{
        border: none;
        padding: 12px 8px;
    }
    .table-striped>tbody>tr:nth-child(odd){
        background-color:#f2f2f2;
    }
