<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.PasswordRuleMapper">
	<resultMap id="passwordRuleMap"
		type="com.huawei.jaguar.dsum.dao.domain.PasswordRuleWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="pwdRuleCode" column="pwdRuleCode" javaType="java.lang.String" />
		<result property="ruleName" column="ruleName" javaType="java.lang.String" />
		<result property="ruleValueType" column="ruleValueType" javaType="java.lang.Integer" />
		<result property="ruleValue" column="ruleValue" javaType="java.lang.String" />
		<result property="isUse" column="isUse" javaType="java.lang.Integer" />
		<result property="createTime" column="createtime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="lastUpdateTime" column="lastupdatetime" javaType="java.util.Date" />
		<result property="extinfo" column="extinfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

    <!-- 获取当前数据库中启用的密码规则 -->
	<select id="getEnableRules" resultMap="passwordRuleMap">
		select ID,
		pwdRuleCode,
		ruleName,
		ruleValueType,
		ruleValue,
		isUse,
		createtime,
		operatorID,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from dsum_t_pwd_rule t
		where t.isUse = 1
	</select>
</mapper>