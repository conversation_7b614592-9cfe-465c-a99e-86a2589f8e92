
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>直客管理</title>
<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
<link href="../../../../css/reset.css" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="../../../../css/searchList.css" />
<link rel="stylesheet" type="text/css" href="../../../../css/merchantList.css" />
<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" 
	src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css" />
<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="merchantListCtrl.js"></script>
</head>
<body ng-app='myApp' ng-controller='merchantListController' ng-init="init();" class="body-min-width">
    <div class="cooperation-manage" >
        <div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_COOPERATION'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'COMMON_MERCHANT'|translate"></span></div>
        <!-- <top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/merchantInfoManage/merchantList" list-index="1"></top:menu> -->
        <div class="cooperation-search">
            <form class="form-horizontal">
                <div class="form-group">
	                <label for="merchantName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" 
	                	style="white-space:nowrap;" ng-bind="'MERCHANT_MERCHANTNAME'|translate">
	                </label>
	                <div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
                            <input autocomplete="off" type="text" class="form-control" id="merchantName" 
                            placeholder="{{'MERCHANT_PLEASEINPUTMERCHANTNAME'|translate}}" 
                            ng-model="merchantName">
                    </div>
                    <label for="auditStatus" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" 
                    	style="white-space:nowrap;" ng-bind="'MERCHANT_AUDITSTATUS'|translate">
	                </label>
                    <!-- <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                        <select id="auditStatus" class="form-control" ng-model="auditStatus" 
                        	ng-options="x.id as x.name for x in auditStatusChoise" >
                        </select>
                    </div> -->
                    <div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<select id="auditStatus" class="form-control" name="province" 
								ng-model="auditStatus"
								ng-options="x.id as x.name for x in auditStatusChoise">
						</select>
					</div>
	                <div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
	                    <button ng-click="queryMerchantList()" type="submit" class="btn search-btn">
	                    <icon class="search-iocn"></icon>
	                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
	                    </button>
	                </div>
                </div>
            </form>
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;margin-top: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'COMMON_MERCHANT'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th ng-bind="'MERCHANT_MERCHANTID'|translate"></th>
                        <th ng-bind="'MERCHANT_MERCHANTNAME'|translate"></th>
                        <th ng-bind="'COMMON_ACCOUNTNAME'|translate"></th>
                        <th ng-bind="'COMMON_CREATETIME'|translate"></th><!-- style="width: 140px;" -->
                        <th ng-bind="'MERCHANT_AUDITSTATUS'|translate"></th>
                        <th ng-bind="'COMMON_OPERATE'|translate" style="width:15%"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in merchantListData track by $index">
                        <td title="{{item.id}}">{{item.id}}</td>
                        <td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
                        <td title="{{item.accountInfo.accountName}}">{{item.accountInfo.accountName}}</td>
                        <td title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</td>
                        <td title="{{statusMap[item.auditStatus]}}">{{statusMap[item.auditStatus]}}</td>
                        <td>
                            <div class="handle">
                                <ul>
                                	<li class="edit" id="editButton_{{$index}}" ng-click="gotoEdit(item)">
                                        <icon class="edit-icon"></icon>
                                        <span ng-bind="'COMMON_EDIT'|translate"></span></li>
                                    <li class="query"  id="queryButton_{{$index}}" ng-click="gotoDetail(item)"><!-- gotoDetailPop(item,$index) -->
                                        <icon class="query-icon"></icon>
                                        <span ng-bind="'COMMON_WATCH'|translate"></span></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="merchantListData.length<=0">
                        <td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    	<div>
        	<ptl-page tableId="0" change="queryMerchantList('justPage')"></ptl-page>
      	</div>
    </div>

	<!--小弹出框-->
      <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
             <div class="modal-dialog modal-sm" role="document">
                 <div class="modal-content">
                     <div class="modal-header">
                         <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                         	<span aria-hidden="true">&times;</span>
                         </button>
                         <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                     </div>
                     <div class="modal-body">
                         <div class="text-center"><p style='font-size: 16px;color:#383838'>
                             {{tip|translate}}
                         </p></div>
                     </div>
                     <div class="modal-footer">
                         <!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
                         <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                         	ng-bind="'COMMON_OK'|translate"></button>
                     </div>
                 </div>
             </div>
         </div>
         
         <!--弹出框-->
			<div class="modal fade bs-example-modal-sm" id="merchantDetailPop" tabindex="-1" role="dialog" 
				aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content" style="width: 115px;height:120px;">
						<div class="modal-body">
							<div class="form-group" style="height: 40px;">
								<button type="submit" 
									class="btn" 
									ng-click="gotoDetail(itemTemp)">
									<icon class="add-iocn"></icon>
									<span ng-bind="'COMMON_BASEINFO'|translate"></span>
								</button>
							</div>
							<div class="form-group" style="height: 40px;">
								<button type="submit" 
									class="btn"  
									ng-click="gotoManage(itemTemp)">
									<icon class="add-iocn"></icon>
									<span ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></span>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>	
    
</body>
</html>