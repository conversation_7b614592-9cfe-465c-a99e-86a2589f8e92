<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>接入设置</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <!--tab页切换-->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>

    <script type="text/javascript" src="accessSettings.js"></script>
    <style>
        .switch-info {
            color: #aaa;
            margin-left: 30px;
            display: inline-block;
            vertical-align: top;
        }

        .switch {
            width: 40px;
            height: 20px;
            margin: 7px 0;
            vertical-align: top;
        }

        .switch .switch-icon {
            width: 18px;
            height: 18px;
        }

        .offtip {
            height: 20px;
            display: inline-block;
            vertical-align: top;
            color: #999999;
            padding-left: 20px;
        }
        .auto-width{
            width: -moz-max-content;
            width: max-content;
        }

        .zyzq .tabtn-menu{

            BACKGROUND: #7361e2;
            padding: 4px 10px;
            line-height: 38px;
            height: 46px;
            margin: 0;
            color: white;
        }
        .zyzq .tabtn-menu:nth-child(-n+3){
            background: #fd6f9a;
        }
        .zyzq .tabtn-menu:nth-child(3){
            margin-right: 50px;
        }
        .zyzq .cur-tabtn-menu {
            opacity: 0.6;
        }
    </style>
</head>

<body ng-app="myApp" ng-controller="settingController" ng-init="init()">
<div class="cooperation-manage" ng-show="isAllow">
    <!-- 直客企业自己登陆 -->
    <div ng-if="loginRoleType=='zhike'" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ACCESSSETTINGS'|translate"></span>
    </div>
    <!-- 管理员登陆查看直客 -->
    <div ng-if="isSuperManager&&enterpriseType==1" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'HOTLINE_MANAGERMENT'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ACCESSSETTINGS'|translate"></span>
    </div>
    <!-- 代理商自己登陆 -->
    <div ng-if="loginRoleType=='agent'" class="cooperation-head">
        <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;
<!--        <span class="second-tab" ng-bind="'HOTLINE_MANAGERMENT'|translate"></span>&nbsp;&gt;&nbsp;-->
        <span class="second-tab" ng-bind="'ACCESSSETTINGS'|translate"></span>
    </div>
    <!-- 管理员登陆查看代理商二级企业 -->
    <div ng-if="isSuperManager&&enterpriseType==3" class="cooperation-head">
        <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;
<!--        <span class="second-tab" ng-bind="'HOTLINE_MANAGERMENT'|translate"></span>&nbsp;&gt;&nbsp;-->
        <span class="second-tab" ng-bind="'ACCESSSETTINGS'|translate"></span>
    </div>
    <!-- 管理员登陆查看分省企业 -->
    <div ng-if="isSuperManager&&enterpriseType==5" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_PROVINCEENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span ng-if="!(enterpriseType =='5' && isZYZQ)" class="second-tab" ng-bind="'ACCESSSETTINGS'|translate"></span>
        <span ng-if="enterpriseType =='5' && isZYZQ" class="second-tab" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"> </span>
        <span ng-if="enterpriseType =='5' && isZYZQ">&nbsp;&gt;&nbsp;</span>
        <span ng-if="enterpriseType =='5' && isZYZQ" class="second-tab" ng-bind="'API_ACCOUNTMANAGER'|translate"> </span>

    </div>
    <!-- 分省登陆查看 -->
    <div ng-if="loginRoleType=='provincial'" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span ng-if="!(isZYZQ)" class="second-tab" ng-bind="'ACCESSSETTINGS'|translate"></span>
        <span ng-if="isZYZQ" class="second-tab" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"> </span>
        <span ng-if="isZYZQ">&nbsp;&gt;&nbsp;</span>
        <span ng-if="isZYZQ" class="second-tab" ng-bind="'API_ACCOUNTMANAGER'|translate"> </span>
    </div>
    <!-- 管理员登录+查看代理商接入设置   -->
    <div ng-if="isSuperManager&&enterpriseType==2" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ACCESSSETTINGS'|translate"> </span>
    </div>
    <!-- 管理员登陆查看直客 -->
    <top:menu chose-index="10" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/hotline/accessSettings"
              list-index="62" ng-if="isSuperManager&&enterpriseType==1"></top:menu>
    <!-- 直客用户自己登陆 -->
    <!--
    <top:menu chose-index="3" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/hotline/accessSettings"
              list-index="63" ng-if="loginRoleType=='zhike'"></top:menu>
              -->
    <!-- 管理员登陆查看代理商二级企业 -->
    <!--201916 多余一个接入设置，先注释掉，因为list-index="68"的，已有接入设置-->
    <!--<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/hotline/accessSettings"-->
              <!--list-index="64" ng-if="isSuperManager&&enterpriseType==3"></top:menu>-->
    <!-- 代理商用户登陆查看二级企业 -->
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/hotline/accessSettings"
              list-index="64" ng-if="loginRoleType=='agent'"></top:menu>
    <!--管理员查看分省-->
    <top:menu chose-index="{{choseIndex}}" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/hotline/accessSettings"
              list-index="65"  apply-val="{{proSupServerType}}" ng-if="isSuperManager&&enterpriseType==5"></top:menu>

    <!--20191216 管理员登录时，子企业管理新增配置，内含业务配置、接入设置、黑白名单-->
    <top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/businessSetting"
              list-index="68" ng-if="isSuperManager&&enterpriseType==3"></top:menu>


    <top:menu chose-index="4" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/ProvincialColorPrintHotline/provinceGroupList"
              list-index="83" ng-if="enterpriseType =='5' && isZYZQ" ng-class="{true:'',false:'second-topmenu zyzq'}[isProvincial]"></top:menu><!-- 分省一级-->
    <!--管理员+ 查看代理商接入配置    -->
    <top:menu chose-index="3" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/hotline/accessSettings"
              list-index="84"  ng-if="isSuperManager&&enterpriseType==2"></top:menu>
    <!--test-end-->

    <div class="cooper-tab">
        <div>
            <form class="form-horizontal ng-pristine ng-invalid ng-invalid-required ng-valid-maxlength"
                  name="myForm" novalidate="">
                <!--账号-->
                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'COMMON_ACCOUNTNAME'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="isUnEdit" type="text" class="form-control" ng-model="accessAccount"
                               name="accessAccount"
                               required pattern="^[^\u4e00-\u9fa5]{1,32}$">
                        <span style="color:red"
                              ng-show="myForm.accessAccount.$dirty && myForm.accessAccount.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.accessAccount.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.accessAccount.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCA'|translate"></span>
                            </span>
                    </div>
                </div>
                <!--访问密码-->
                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ACCESSTHEPASSWORD'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control" ng-model="accessPassword"
                               name="accessPassword"
                               required pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <span style="color:red"
                              ng-show="myForm.accessPassword.$dirty && myForm.accessPassword.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.accessPassword.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.accessPassword.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCT'|translate"></span>
                            </span>
                    </div>
                </div>
                <!--第三方接入账号-->
                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">

                        <span ng-bind="'THIRDACCESSTHEACCOUNT'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               ng-model="thirdAccessAccount" name="thirdAccessAccount"
                               maxlength="64">

                    </div>
                </div>
                <!--第三方接入密码-->
                <div class="form-group">
                    <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">

                        <span ng-bind="'THIRDACCESSTHEPASSWORD'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               ng-model="thirdAccessPassword" name="thirdAccessPassword"
                               maxlength="1024">
                    </div>
                </div>
                <!--回调地址-->
                <div class="form-group">
                    <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'CALLBACKADDRESS'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="{{'PLEASEENTERCALLBACKADDRESS'|translate}}" ng-model="callbackUrl"
                               name="callbackUrl" required pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <span style="color:red" ng-show="myForm.callbackUrl.$dirty && myForm.callbackUrl.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.callbackUrl.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.callbackUrl.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCT'|translate"></span>
                            </span>
                    </div>
                </div>
                <!--审核回调地址-->
                <div class="form-group">
                    <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">

                        <span ng-bind="'REVIEW_CALLBACKADDRESS'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="{{'PLEASEENTERREVIEWCALLBACKADDRESS'|translate}}"
                               ng-model="approveCallbackUrl" name="approveCallbackUrl"
                               pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <span style="color:red"
                              ng-show="myForm.approveCallbackUrl.$dirty && myForm.approveCallbackUrl.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.approveCallbackUrl.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.approveCallbackUrl.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCT'|translate"></span>
                            </span>
                        <div style="float:left;color:#f30404;width: 370px">审核信息的接收地址，不填则无法接收审核情况回调信息</div>
                    </div>
                </div>
                <!--信息反馈回调地址-->
                <div class="form-group">
                    <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">

                        <span ng-bind="'MESSAGE_CALLBACKADDRESS'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="{{'PLEASEENTERREMESSAGECALLBACKADDRESS'|translate}}"
                               ng-model="feedbackUrl" name="feedbackUrl"
                               pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <span style="color:red"
                              ng-show="myForm.feedbackUrl.$dirty && myForm.feedbackUrl.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.feedbackUrl.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.feedbackUrl.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCT'|translate"></span>
                            </span>
                    </div>
                </div>
                <!--企业通知回调地址-->
                <div class="form-group" ng-show="enterpriseType==3 || enterpriseType==2 ">
                    <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">

                        <span ng-bind="'GROUPSEND_CALLBACKADDRESS'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="{{'PLEASEENTERREGROUPCALLBACKADDRESS'|translate}}"
                               ng-model="groupSendNotifyUrl" name="groupSendNotifyUrl"
                               pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <span style="color:red"
                              ng-show="myForm.groupSendNotifyUrl.$dirty && myForm.groupSendNotifyUrl.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.groupSendNotifyUrl.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.groupSendNotifyUrl.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCT'|translate"></span>
                            </span>
                    </div>
                </div>
                <!--名片成员订购回调地址-->
                <div class="form-group" ng-show="enterpriseType==3 ||enterpriseType==2 ">
                    <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">

                        <span ng-bind="'MP_SUBCRIBE_CALLBACKADDRESS'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="{{'PLEASE_MP_SUBCRIBE_CALLBACKADDRESS'|translate}}"
                               ng-model="subcribeResultNotifyUrl" name="subcribeResultNotifyUrl"
                               pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <span style="color:red"
                              ng-show="myForm.subcribeResultNotifyUrl.$dirty && myForm.subcribeResultNotifyUrl.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.subcribeResultNotifyUrl.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.subcribeResultNotifyUrl.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCT'|translate"></span>
                            </span>
                        <div style="float:left;color:#f30404;width: 370px">名片成员订购回调地址，不填则无法接收审核情况回调信息</div>
                    </div>
                </div>
                <!--名片模板审核回调地址-->
                <div class="form-group" ng-show="enterpriseType==3 || enterpriseType==2">
                    <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">

                        <span ng-bind="'MP_APPROVE_CALLBACKADDRESS'|translate"></span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="{{'PLEASE_MP_APPROVE_CALLBACKADDRESS'|translate}}"
                               ng-model="mpApproveCallbackUrl" name="mpApproveCallbackUrl"
                               pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <span style="color:red"
                              ng-show="myForm.mpApproveCallbackUrl.$dirty && myForm.mpApproveCallbackUrl.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
                                <span ng-show="myForm.mpApproveCallbackUrl.$error.required"
                                      ng-bind="'REQUIRED'|translate"></span>
                                <span ng-show="myForm.mpApproveCallbackUrl.$error.pattern"
                                      ng-bind="'HOTLINE_MAXINPUTDESCT'|translate"></span>
                            </span>
                        <div style="float:left;color:#f30404;width: 370px">名片模板审核回调地址，不填则无法接收审核情况回调信息</div>

                    </div>
                </div>
                <!--子企业审核结果回调地址配置-->
                <div class="form-group" ng-show="enterpriseType==2">
                    <label for="publicName" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <span>子企业审核结果回调地址</span>
                    </label>
                    <div class="col-lg-3 col-md-4 col-sm-4 col-xs-4" style="min-width: 310px">
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="请输入子企业审核结果回调请求地址"
                               ng-model="enterpriseApproveCallbackUrlPre" name="enterpriseApproveCallbackUrlPre"
                               pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        |
                        <input ng-disabled="!isSuperManager" type="text" class="form-control"
                               placeholder="请输入子企业审核结果回调接口名"
                               ng-model="enterpriseApproveCallbackUrlEnd" name="enterpriseApproveCallbackUrlEnd"
                               pattern="^[^\u4e00-\u9fa5]{1,1024}$">
                        <div class="error-message" ng-show="!callbackUrlsValid && (enterpriseApproveCallbackUrlPre || enterpriseApproveCallbackUrlEnd)">
                            <span style="color:red">请求地址和接口名必须同时填写或同时为空</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="submit"
                    class="btn btn-primary search-btn  col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 "
                    ng-disabled="myForm.accessAccount.$invalid || myForm.accessPassword.$invalid ||
                        myForm.callbackUrl.$invalid || myForm.approveCallbackUrl.$invalid
                        || (enterpriseType==3 && myForm.groupSendNotifyUrl.$invalid) || myForm.feedbackUrl.$invalid"
                    ng-bind="'COMMON_SAVE'|translate" ng-click="saveUssd()" ng-show="isSuperManager"></button>
            <!--<button type="submit" style="margin-left: 50px" class="btn " data-dismiss="modal" aria-label="Close" id="addMemCancel" ng-bind="'COMMON_BACK'|translate"></button>-->
        </div>
    </div>

    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog"
         aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                        </p>
                    </div>
                </div>
                <div class="modal-footer" style="text-align:center; margin-left:0px;">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="bussinessNo" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                    	<div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p class="tip ng-binding" ng-bind="'BUSSINESS_NOT_ALLOW'|translate">
                            </p></div>
                        </div>
                        <div class="modal-footer" style="text-align:center">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
     </div>
</body>
<style>
    body {
        background: #f2f2f2;
    }

    .modal-footer {
        text-align: left;
    }

    .fontGreen {
        color: rgb(48, 147, 25)
    }

    .fontRed {
        color: rgb(252, 70, 93);
    }

    .cooperation-manage {
        min-width: 1024px;
    }

    .cooperation-head {
        padding: 20px;
    }

    .cooperation-head .frist-tab {
        font-size: 16px;
    }

    .cooperation-head .second-tab {
        font-size: 14px;
    }

    .cooper-tab {
        margin: 0 20px;
        background: #fff;
        border-radius: 2px;
        padding: 36px 10px 16px;
    }

    .form-group .control-label icon {
        color: #ff254c;
        vertical-align: sub;
        margin-right: 2px;
    }

    .form-group div {
        line-height: 34px;
    }

    .form-group {
        margin-bottom: 35px;
    }

</style>

</html>
