<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title>直客管理－新增企业</title>
		<link href="../../../../../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
		<link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css" />
		<link href="../../../../../css/reset.css" rel="stylesheet" />
		<link href="../../../../../css/createEnterprise.css" rel="stylesheet" type="text/css"/>
		<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
		<script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
		<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
		<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
		<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
		<!-- 引入分页组件 -->
		<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
		<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />

		<link rel="stylesheet" type="text/css" href="../../../../../css/webuploader.css">
		<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
		<!--引入JS-->
		<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
		<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
		<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
		<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
		<script type="text/javascript" src="createEnterprise.js"></script>
	</head>

	<body ng-app="myApp" ng-controller='enterpriseController' class="body-min-width">
		<div class="enterPrise">
			<div class="cooperation-head" ng-if="isSuperManager">
				<span class="frist-tab" ng-bind="'COMMON_PROENTERPRISE'|translate">
				</span>
				<span>&nbsp;&gt;&nbsp;
				</span>
				<span class="second-tab" ng-bind="'COMMON_BASEINFO'|translate"></span>
			</div>
			<div class="cooperation-head" ng-if="!isSuperManager">
				<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate">

				</span>
				<span>&nbsp;&gt;&nbsp;
				</span>
				<span class="second-tab" ng-bind="'COMMON_BASEINFOMNG'|translate"></span>
			</div>
			<form  class="form-horizontal" name="myForm" novalidate ng-init="initEnterprise(this)">
			<div class="cooper-messsage">
				<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/provincialManage/enterprise/createEnterprise" 
					list-index="30" apply-val="{{proSupServerType}}" ng-if="isSuperManager"></top:menu>
				<div class="enterprise-title">
					1.<span ng-bind="'ENTERPRISE_ENTERPRISEINFO'|translate"></span>
				</div>
				<div class="cooper-tab">
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'PROENTERPRISE_ENTERPRISEID'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input class="form-control"
									type="text" id="id" name="id" ng-model="enterpriseInfo.id"
									ng-disabled="true"
									title={{enterpriseInfo.id}}>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'PROENTERPRISE_ENTERPRISENAME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input class="form-control"
									type="text" id="enterpriseName" name="enterpriseName" 
									ng-model="enterpriseInfo.enterpriseName"
									ng-disabled="true"
									title={{enterpriseInfo.enterpriseName}}>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'PROENTERPRISE_CONTRACTNUM'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="msisdn" ng-model="enterpriseInfo.msisdn" id="msisdn"
									placeholder="{{'PROENTERPRISE_PLEASEINPUTCONTRACTNUM'|translate}}"
									ng-class="{'redBorder':!msisdnValidate}"
									ng-blur="checkMsisdn(enterpriseInfo.msisdn)"
									title={{enterpriseInfo.msisdn}}>
							    <span class="redFont" ng-show="!msisdnValidate">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'PROENTERPRISE_CONTRACTNUMDESC'|translate"></span>
								</span>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span ng-bind="'PROENTERPRISE_EMAIL'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input class="form-control"
									type="text" id="email" name="email" ng-model="accountInfo.email"
									ng-disabled="true"
									title={{accountInfo.email}}>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'SPOKES_LOCATION'|translate"></span>：</label>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-2">
								<select class="form-control"
									name="province" ng-model="provinceID"
									ng-options="x.provinceID as x.provinceName for x in provinceList" 
									ng-change="changeSelectedProvince(provinceID)"
									ng-disabled="true">
									<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-show="operate !='edit'"></option>
								</select>
							</div>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-2">
								<select class="form-control"
									name="city" ng-model="selectedCity"
									ng-options="x as x.cityName for x in subCityList" 
									ng-disabled="true">
									<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
								</select>
							</div>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-2" ng-if="showCountyFlag">
								<select class="form-control"
										name="city" ng-model="selectedCounty"
										ng-options="x as x.countyName for x in subCountyList"
										ng-disabled="true">
									<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
								</select>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'PROENTERPRISE_DESC'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<textarea  class="form-control" rows="6" style="margin: 0px;resize:none;"
                                      name="desc" ng-class="{'redBorder':!descValidate}"
                                      placeholder="{{'PROENTERPRISE_PLEASEINPUTDESC'|translate}}" 
                                      ng-model="enterpriseInfo.enterpriseDesc"
                                      ng-blur="checkDesc(enterpriseInfo.enterpriseDesc)"
                                  	  title={{enterpriseInfo.enterpriseDesc}}></textarea>
							    <span class="redFont" ng-show="!descValidate">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'PROENTERPRISE_DESCDESC'|translate"></span>
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="cooper-messsage">
				<div class="enterprise-title">
					2.<span ng-bind="'ENTERPRISE_ACCOUNTINFO'|translate"></span>
				</div>
				<div class="cooper-tab">

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'COMMON_ACCOUNTNAME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="accountName" 
									ng-model="accountInfo.accountName"
									placeholder="{{'ENTERPRISE_PLEASEINPUTACCOUNTNAME'|translate}}" 
									title={{accountInfo.accountName}} ng-disabled="true" autocomplete="off">
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'ENTERPRISE_PASSWORD'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="password" class="form-control" name="password" 
									autocomplete="off" ng-model="accountInfo.password"
									ng-class="{'redBorder':passwordValidateDesc}"
									ng-blur="checkPassword('')" placeholder="{{'ENTERPRISE_PLEASEINPUTPASSWORD'|translate}}">
							    <span class="redFont" ng-show="passwordValidateDesc">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span>{{passwordValidateDesc|translate}}</span>
								</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'ENTERPRISE_REPASSWORD'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="password" class="form-control" name="rePassword" 
									 autocomplete="off" ng-model="accountInfo.rePassword"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTREPASSWORD'|translate}}" 
									 ng-blur="checkRePassword(accountInfo.password,accountInfo.rePassword,'')"
									 ng-class="{'redBorder':!rePasswordValidate}">
								</span>
								<span class="redFont" ng-show="!rePasswordValidate">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
									<span ng-bind="'ENTERPRISE_REPASSWORDDESCINEDIT'|translate"></span>
								</span>
							</div>
						</div>
					</div>
				</div>

			</div>
				<!--enterpriseType=5，且reserved10=111-->
				<div class="cooper-messsage" ng-show="enterpriseType==5 && (reserved10 == '111'||reserved10 == '113')">
					<div class="enterprise-title">
						3.<span ng-bind="'ENTERPRISE_PACKAGEINFO'|translate"></span>
					</div>
					<div class="cooper-tab">
						<div class="form-group" ng-if="mpPackageinfoName != ''">
							<div class="row">
								<label style="font-weight: bolder" class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label enterprise-packageinfo-title">
									<span ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>：</label>
								<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 control-label" style="text-align: left;">
									<p class="package-info package-info-first">
										<span ng-bind="'ENTERPRISE_PACKAGEINFO_NAME'|translate"></span>：{{mpPackageinfoName}}
									</p>
									<p class="package-info">
										<span ng-bind="'ENTERPRISE_PACKAGEINFO_QUOTA'|translate"></span>：{{mpPackageinfoQuota}}
									</p>
								</div>
							</div>
						</div>

						<div class="form-group" ng-if="rxPackageinfoName != ''">
							<div class="row">
								<label style="font-weight: bolder" class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label enterprise-packageinfo-title">
									<span ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></span>：</label>
								<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 control-label" style="text-align: left;">
									<p class="package-info package-info-first">
										<span ng-bind="'ENTERPRISE_PACKAGEINFO_NAME'|translate"></span>：{{rxPackageinfoName}}
									</p>
									<p class="package-info">
										<span ng-bind="'ENTERPRISE_PACKAGEINFO_QUOTA'|translate"></span>：{{rxPackageinfoQuota}}
									</p>
									<p class="package-info">
										<span ng-bind="'ENTERPRISE_PACKAGEINFO_USED_QUOTA'|translate"></span>：{{rxPackageinfoUsedQuota}}
									</p>
									<p class="package-info">
										<span ng-bind="'ENTERPRISE_PACKAGEINFO_USABLE_QUOTA'|translate"></span>：{{rxPackageinfoUsableQuota}}
									</p>
								</div>
							</div>
						</div>

					</div>
				</div>
			<div class="enterprise-btn">
				<button type="submit" class="btn btn-primary search-btn"
				ng-disabled="!msisdnValidate || !enterpriseInfo.msisdn || 
				!descValidate ||
				passwordValidateDesc ||!passwordValidate ||
				!rePasswordValidate ||(accountInfo.password && !accountInfo.rePassword)"
				ng-click="beforeSave()"
				ng-bind="'COMMON_SAVE'|translate"></button>
				<button type="submit" class="btn btn-back" ng-click="cancelToEnterpriseList()"
					ng-bind="'COMMON_BACK'|translate" ng-show="isSuperManager"></button>
			</div>
			</form>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p class="tip">
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_OK'|translate"></button>
						</div>
					</div>
				</div>
			</div>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="ensureToList" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p class="tip">
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-click="ensureToList()" 
								ng-bind="'COMMON_OK'|translate"></button>
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_CANCLE'|translate"></button>
						</div>
					</div>
				</div>
			</div>
		</div>

	</body>
</html>