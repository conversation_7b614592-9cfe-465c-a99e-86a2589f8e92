<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ProductPackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ProductPackage">
        <id column="ID" property="id" />
        <result column="packageCode" property="packageCode" />
        <result column="packageName" property="packageName" />
        <result column="packageDesc" property="packageDesc" />
        <result column="price" property="price" />
        <result column="status" property="status" />
        <result column="isExperience" property="isExperience" />
        <result column="chargeType" property="chargeType" />
        <result column="effictiveTime" property="effictiveTime" />
        <result column="expireTime" property="expireTime" />
        <result column="customer" property="customer" />
        <result column="customerPhone" property="customerPhone" />
        <result column="customerEmail" property="customerEmail" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
        <result column="reserved1" property="reserved1" />
        <result column="reserved2" property="reserved2" />
        <result column="reserved3" property="reserved3" />
        <result column="reserved4" property="reserved4" />
        <result column="reserved5" property="reserved5" />
        <result column="reserved6" property="reserved6" />
        <result column="reserved7" property="reserved7" />
        <result column="reserved8" property="reserved8" />
        <result column="reserved9" property="reserved9" />
        <result column="reserved10" property="reserved10" />
        <result column="extInfo" property="extInfo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, packageCode, packageName, packageDesc, price, status, isExperience, chargeType, effictiveTime, expireTime, customer, customerPhone, customerEmail, createTime, updateTime, reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, reserved9, reserved10, extInfo
    </sql>

    <select id="queryPackageByCode" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ecpe_t_productpackage
        where
        packageCode = #{packageCode}
    </select>
</mapper>