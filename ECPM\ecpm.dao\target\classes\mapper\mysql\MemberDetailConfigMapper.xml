<?xml version="1.0" encoding="UTF-8"?><!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberDetailConfigMapper">
    <resultMap id="memberDetailConfigWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberDetailConfigWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="provinceCode" column="provinceCode" javaType="java.lang.String" />
        <result property="exportTimeStr" column="exportTime" javaType="java.lang.String" />
        <result property="exportDay" column="exportDay" javaType="java.lang.Integer" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="reserved5" column="reserved5" javaType="java.lang.String" />
        <result property="reserved6" column="reserved6" javaType="java.lang.String" />
        <result property="reserved7" column="reserved7" javaType="java.lang.String" />
        <result property="reserved8" column="reserved8" javaType="java.lang.String" />
        <result property="reserved9" column="reserved9" javaType="java.lang.String" />
        <result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>

    <select id="queryMemDetailConfigList" resultMap="memberDetailConfigWrapper">
        SELECT
        ID,
        provinceCode,
        exportTime,
        exportDay,
        updateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10
        FROM
        ecpm_t_memberDetail_config
    </select>

    <update id="updateMemDetailConfig" parameterType="java.lang.Integer">
        UPDATE ecpm_t_memberDetail_config
        SET updateTime = now( )
        WHERE id = #{id}
    </update>
</mapper>