@charset "UTF-8";
[ng-cloak] {
  display: none !important;
}

body {
  background: #f2f2f2;
}

.cooperation-head {
  padding: 20px;
}

.cooperation-head .frist-tab {
  font-size: 16px;
}

.cooperation-head .second-tab {
  font-size: 14px;
}

.cooperation-manage .form-inline {
  margin: 0 20px;
  background: #fff;
  border-radius: 4px;
  padding: 20px;
}

.cooperation-manage label {
  font-weight: normal;
}

.cooperation-manage .form-group {
  margin-right: 20px;
  padding-top: 10px;
  padding-bottom: 20px;
}
.cooperation-manage .form-group2 {
  margin-right: 20px;
  padding-top: 0px;
  padding-bottom: 0px;
}
.form-control:focus {
  border-color: #7360e1;
}

.cooperation-manage .search-btn {
  background-image: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
  color: #fff;
}

.cooperation-manage .btn .search-iocn {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url("../assets/images/btnIcons.png") no-repeat;
  vertical-align: middle;
}

.cooperation-manage .add-table {
  margin: 20px;
}

.cooperation-manage .add-table .add-btn {
  background: #fff;
  color: #7360e1;
}

.cooperation-manage .add-table .add-btn .add-iocn {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../assets/images/btnIcons.png) no-repeat;
  vertical-align: middle;
  background-position: -20px 0;
}

.coorPeration-table {
  margin: 0px 20px;
  background: #fff;
}

.handle {
  overflow: hidden;
  /* min-width: 160px; */
}

.handle ul li {
  display: inline-block;
  margin-right: 20px;
  cursor: pointer;
}

.handle ul li.query {
  color: #7360e2;
}

.handle ul li.edit {
  color: #7360e2;
}
.handle ul li.removeMemP {
  color: #7360e2;
}
.handle ul li.moveMemP {
  color: #7360e2;
}
.handle ul li.sync {
  color: #7360e2;
}
.handle ul li.bsync {
  color: #7360e2;
}

.handle ul li.delete {
  color: #ff2549;
}

.handle ul li.set {
  color: #7360e2;
}

.handle ul li.suspend {
  color: #7360e2;
}

.handle ul li.recovery {
  color: #ff2549;
}

.handle ul li icon {
  width: 18px;
  height: 20px;
  display: inline-block;
  background: url(../assets/images/tableEditIcons18.png) no-repeat;
  vertical-align: bottom;
}

.handle ul li icon.query-icon {
  background-position: -72px 0px;
}

.handle ul li icon.edit-icon {
  background-position: 0 0;
}

.handle ul li icon.delete-icon {
  background-position: -18px 0;
}

.handle ul li icon.set-icon {
  background-position: -36px 0;
}
.handle ul li icon.set2-icon {
  background-position: -270px 0;
}
.handle ul li icon.package-icon {
  background-position: -288px 0;
}

.cooperation-manage .add-table .add-btn .export-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../assets/images/tableEditIcons18.png)no-repeat;
  vertical-align: middle;
  background-position: -108px 0;
}

.table-tab {
  float: right;
  margin: 0 20px;
}

.pagination > .active > a {
  background-image: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
  border: 1px solid #705de1;
}

.table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
  border: none;
  padding: 12px 8px;
}

.table-striped > tbody > tr:nth-child(odd) {
  background-color: #f2f2f2;
}

.pagination > li > a, .pagination > li > span {
  color: #777;
}

.province {
  float: left;
  padding-top: 6px;
}

table {
  table-layout: fixed;
}

td {
  width: 100%;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-horizontal .control-label {
  padding-top: 22px;
  margin-bottom: 0;
  text-align: right;
}
.form-horizontal .control-label2 {
  padding-top: 0px;
  margin-bottom: 0;
  text-align: right;
}

.container-fluid {
  padding-left: 0;
  padding-right: 0;
}

.cooperation-manage .form-horizontal {
  margin: 0 20px;
  background: #fff;
  border-radius: 4px;
}

.cond-div {
  padding-top: 15px;
}