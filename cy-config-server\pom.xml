<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.caiyin</groupId>
	<artifactId>cy-config-server</artifactId>
	<version>0.0.1</version>
	<packaging>jar</packaging>

	<name>cy-config-server</name>
	<description>config serverproject for Spring Boot</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>21</java.version>
		<spring-cloud.version>2024.0.0</spring-cloud.version>
	</properties>

	<profiles>
		<profile>
			<!-- 本地开发环境 -->
			<id>dev_local</id>
			<properties>
				<config.uri>http://*************:8080/zhengweidong/cyconfig.git</config.uri>
				<config.username><EMAIL></config.username>
				<config.password>a15710619223</config.password>
				<config.path>dev_configs</config.path>
				<config.label>master</config.label>
				<cy_eureka.url>http://127.0.0.1:19000/eureka/</cy_eureka.url>
				<kafka.zkNodes>**************:2181,**************:2181,**************:2181</kafka.zkNodes>
				<kafka.brokers>**************:9092,**************:9092,**************:9092</kafka.brokers>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<!-- 开发环境 -->
			<id>dev</id>
			<properties>
				<config.uri>http://*************:8080/zhengweidong/cyconfig.git</config.uri>
				<config.username><EMAIL></config.username>
				<config.password>a15710619223</config.password>
				<config.path>dev_configs</config.path>
				<cy_eureka.url>http://124.42.103.232:19000/eureka/</cy_eureka.url>
				<kafka.zkNodes>124.42.103.231:2181</kafka.zkNodes>
				<kafka.brokers>124.42.103.231:9092</kafka.brokers>
			</properties>
		</profile>
		<profile>
			<!-- 测试环境 -->
			<id>test</id>
			<properties>
				<config.uri>http://*************:8080/zhengweidong/cyconfig.git</config.uri>
				<config.username><EMAIL></config.username>
				<config.password>a15710619223</config.password>
				<config.path>test_configs</config.path>
				<config.label>master</config.label>
				<cy_eureka.url>http://10.124.130.248:31001/eureka/,http://10.124.130.249:31001/eureka/</cy_eureka.url>
				<kafka.zkNodes>**************:2181,**************:2181,**************:2181</kafka.zkNodes>
				<kafka.brokers>**************:9092,**************:9092,**************:9092</kafka.brokers>
			</properties>
		</profile>
		<profile>
			<!-- 生产环境 -->
			<id>production</id>
			<properties>
				<config.uri>http://10.124.70.48:8443/qgcy-git/qgcy.git </config.uri>
				<config.username>qgcy-git</config.username>
				<config.password>cyqg@965</config.password>
				<config.path>cyconfig/prod_configs</config.path>
				<config.label>main</config.label>
				<cy_eureka.url>http://10.124.70.218:19000/eureka/,http://10.124.70.219:19000/eureka/,http://10.124.70.220:19000/eureka/</cy_eureka.url>
				<kafka.brokers>10.124.70.246:9092,10.124.70.247:9092,10.124.70.248:9092</kafka.brokers>
				<kafka.zkNodes>10.124.70.246:2181,10.124.70.247:2181,10.124.70.248:2181</kafka.zkNodes>
			</properties>
		</profile>
	</profiles>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-config-server</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.apache.sshd</groupId>
					<artifactId>sshd-sftp</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.eclipse.jgit</groupId>
					<artifactId>org.eclipse.jgit</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.eclipse.jgit</groupId>
			<artifactId>org.eclipse.jgit</artifactId>
			<version>5.13.3.202401111512-r</version>
		</dependency>
		<dependency>
			<groupId>org.apache.sshd</groupId>
			<artifactId>sshd-sftp</artifactId>
			<version>2.15.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-jxpath</groupId>
					<artifactId>commons-jxpath</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--sleuth链路监控-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-netflix-eureka-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bus-kafka</artifactId>

		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-stream-kafka</artifactId>
		</dependency>
		<dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>

		<!--<dependency>-->
			<!--<groupId>org.springframework.boot</groupId>-->
			<!--<artifactId>spring-boot-starter-test</artifactId>-->
			<!--<scope>test</scope>-->
		<!--</dependency>-->
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>-->
				<!--spring-cloud-starter-hystrix-dashboard-->
			<!--</artifactId>-->
		<!--</dependency>-->
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>spring-cloud-starter-turbine</artifactId>-->
		<!--</dependency>-->
				<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>
		<dependency>
   			<groupId>net.logstash.logback</groupId>
  			<artifactId>logstash-logback-encoder</artifactId>
   			<version>5.1</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>

			<dependency>
				<artifactId>netty-codec-http</artifactId>
				<groupId>io.netty</groupId>
				<version>4.1.108.Final</version>
			</dependency>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>32.0.0-jre</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.yml</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>true</filtering>
			</resource>
		</resources>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-resources-plugin</artifactId>
					<configuration>
						<encoding>utf-8</encoding>
						<useDefaultDelimiters>true</useDefaultDelimiters>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>0.4.11</version>
				<configuration>
					<imageName>caiyin/${project.artifactId}</imageName>
					<imageTags>
						<imageTag>latest</imageTag>
					</imageTags>
					<dockerDirectory>src/main/docker</dockerDirectory>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
