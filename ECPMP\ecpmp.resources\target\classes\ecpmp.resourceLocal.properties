#\u6587\u4EF6\u4E0A\u4F20\u5730\u5740
ecpmp.uploadFile.path=D:/sftp/data/uploadFile
#\u4E0A\u4F20\u56FE\u7247\u652F\u6301\u683C\u5F0F\u5217\u8868
upload.pic.allow.types=png|jpeg|jpg|pdf
#\u4E0A\u4F20\u6587\u4EF6\u6700\u5927size
upload.file.max.size=52428800
#\u4E0A\u4F20\u6587\u4EF6\u652F\u6301\u683C\u5F0F\u5217\u8868
upload.file.allow.types=zip|rar|xlsx
#\u901A\u7528\u56FE\u7247\u4E0A\u4F20\u6700\u5927size
upload.pic.common.max.size=20971520
#\u5F69\u4FE1\u56FE\u7247\u4E0A\u4F20\u6700\u5927size
upload.pic.mms.max.size=307200
#\u767B\u5F55\u6821\u9A8C\u767D\u540D\u5355
login.status.check.white.list=/qycy/ecpmp/ecpmpServices/JKService/ticketToToken|/qycy/ecpmp/ecpmpServices/commonService/refresh|/qycy/ecpmp/view/login/login.html|/qycy/ecpmp/ecpmpServices/loginService/getVerificationCode|/qycy/ecpmp/ecpmpServices/loginService/login|/qycy/ecpmp/ecpmpServices/loginService/logOut|/qycy/ecpmp/ecpmpServices/loginService/tokenAuth|/qycy/ecpmp/view/forgetPwd/forgetPwd.html|/qycy/ecpmp/ecpmpServices/loginService/getSmsVerifyCode|/qycy/ecpmp/ecpmpServices/loginService/verifyCode|/qycy/ecpmp/ecpmpServices/loginService/resetPwd|/qycy/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule|/qycy/ecpmp/ecpmpServices/JKService/remoteTicket|/qycy/ecpmp/ecpmpServices/OAService/remoteTicket|/qycy/ecpmp/view/menu/errorMenu.html|/qycy/ecpmp/ecpmpServices/loginService/checkNetwork|/qycy/ecpmp/ecpmpServices/loginService/checkImgCodeToken|/qycy/ecpmp/ecpmpServices/accountManageService/changePwd|/qycy/ecpmp/ecpmpServices/accountManageService/queryLoginSmsEnable|/qycy/ecpmp/ecpmpServices/accountManageService/queryAccountMsisdn|/qycy/ecpmp/ecpmpServices/loginService/remoteTicket|/qycy/ecpmp/ecpmpServices/mobileCould/cas/login
#ecpm\u63A5\u53E3\u8C03\u7528\u524D\u7F00
ecpm.service.url=/ecpm
#dsum\u63A5\u53E3\u8C03\u7528\u524D\u7F00
dsum.service.url=/dsum
#keystorePass\u89E3\u5BC6iv
keystorePass.decrypt.iv=ZiApy4aslYAPqw5AI041ig==
#keystorePass\u89E3\u5BC6key
keystorePass.decrypt.key=sdx/IPhhs8+JIkeouzcAqKQ7xQLjtaYam3xnkOHZyFI=
#\u7A0B\u6C60\u7EF4\u62A4\u7EBF\u7A0B\u7684\u6700\u5C11\u6570\u91CF
ecpmp.batchCreateMemberPool.corePoolSize=10
#\u5141\u8BB8\u7684\u7A7A\u95F2\u65F6\u95F4
ecpmp.batchCreateMemberPool.keepAliveSeconds=200
#\u7EBF\u7A0B\u6C60\u7EF4\u62A4\u7EBF\u7A0B\u7684\u6700\u5927\u6570\u91CF
ecpmp.batchCreateMemberPool.maxPoolSize=20
#\u7F13\u5B58\u961F\u5217
ecpmp.batchCreateMemberPool.queueCapacity=100
#\u6279\u91CF\u521B\u5EFA\u6210\u5458\u6BCF\u6B21\u6700\u5927size
batchCreateMember.max.size=10
batchCreateMember.failedFile.head=成员名称|成员号码|错误码|失败原因
#\u5BFC\u51FA\u4EE3\u8A00\u4EBA\u5217\u8868\u5934\u90E8
downSpokesmanListCsvFile.head=代言人号码|归属地|代言时间|代言天数|屏显推送条数|挂机推送条数|代言状态|获奖状态
#\u5BFC\u51FA\u6D3B\u52A8\u7EDF\u8BA1\u5217\u8868\u5934\u90E8
downActivityStatCsvFile.head=活动名称|有效期|代言人数量|屏显推送条数|挂机推送条数
#\u5BFC\u51FA\u5546\u6237\u7EDF\u8BA1\u5934\u90E8
downEnterpriseStatCsvFile.head=商户编号|商户名称|活动数量|代言人数量|屏显推送条数|挂机推送条数
#\u5BFC\u51FA\u5957\u9910\u8BA2\u5355\u5934\u90E8
downOrderCsvFile.head=订单编号|创建时间|商户名称|充值条数|支付金额|支付方式|状态
#\u652F\u4ED8\u5B9D
alipay=支付宝
#\u5FAE\u4FE1
wechat=微信
#\u548C\u5305
andbag=和包
#\u672A\u652F\u4ED8
unpaid=未支付
#\u652F\u4ED8\u4E2D
paying=支付中
#\u5DF2\u652F\u4ED8
paid=已支付
EUSSD=交互ussd
EMMS=挂机增彩
SMS=挂机短信
#\u652F\u4ED8\u5931\u8D25
payFailed=支付失败
#\u5BFC\u51FA\u67E5\u770B\u4EE3\u8A00\u4EBA\u5934\u90E8
downSpokeStatCsvFile.head=代言人号码|归属地|第一次参与时间|参与数量|获奖数量|累计代言天数|累计屏显推送条数|累计挂机推送条数
#\u4EE3\u8A00\u4EBA\u7EDF\u8BA1\u5934\u90E8
downSpokesListCsvFile.head=商户名称|活动名称|代言时间|代言天数|屏显推送条数|挂机推送条数
#\u5BFC\u51FA\u6210\u5458\u5934\u90E8
downMemberCsvFile.head=成员名称|成员号码|归属地|状态
#\u5BFC\u51FA\u6210\u5458\u5934\u90E8\uFF08\u5206\u7701\uFF09
downMemberCsvFileProvince.head=成员名称|成员号码|归属地|号码类型|状态
#\u8BA2\u8D2D\u6210\u529F
member.order.status.success=订购成功
#\u8BA2\u8D2D\u5931\u8D25
member.order.status.fail=订购失败
#\u8BA2\u8D2D\u4E2D
member.order.status.ing=订购中
#\u5BFC\u5165\u6210\u5458\u6700\u5927\u6210\u5458\u6570\u91CF
batchCreateMember.size=500
#\u4EE3\u8A00\u4E2D
spoking=代言中
#\u53D6\u6D88\u4EE3\u8A00
cannelSpoke=取消代言
#\u7ED3\u675F\u4EE3\u8A00
endSpoke=结束代言
#\u662F\u5426\u83B7\u5956\uFF1A\u662F
yes=是
#\u662F\u5426\u83B7\u5956\uFF1A\u5426
no=否
#\u5C4F\u663E\u7684\u6309\u6B21\u5355\u4EF7
order.PX.unitPrice=0.016
#\u6302\u77ED\u7684\u6309\u6B21\u5355\u4EF7
order.GD.unitPrice=0.05
#\u6302\u5F69\u7684\u6309\u6B21\u5355\u4EF7
order.GC.unitPrice=0.3

#\u65B0\u589E\u9ED1\u767D\u540D\u5355\u5931\u8D25\u6587\u4EF6\u5934
batchAddBlackWhite.failedFile.head=号码|名单类型（1为黑名单，2为白名单）|业务类型（1为名片彩印，2为热线彩印，3为广告彩印）|业务形态（1为主叫，2为被叫，4为挂机短信，8为挂机彩信）|错误码|错误原因
#\u5BFC\u5165\u5185\u5BB9\u6A21\u677F\u5931\u8D25\u6587\u4EF6\u5934
importTemplateVar.failedFile.head=成员号码|内容|错误码|失败原因
#host\u767D\u540D\u5355
host.white.list=cy.migudm.cn|************:28080|************:28443|************:18901|************:18902|127.0.0.1:28443|127.0.0.1:28080|127.0.0.1|*********:28443
#\u5BFC\u51FA\u6295\u9012\u660E\u7EC6\u5934\u90E8
downDeliveryDetailCsvFile.head=企业编号|企业名称|taskid|模板ID|主叫号码|被叫号码|接收号码|第三方投递请求时间|能力开放平台下发请求时间|投递结果通知时间|投递时延（毫秒）|投递类型|投递结果
#\u5BFC\u51FA\u5206\u7701\u6295\u9012\u660E\u7EC6\u5934\u90E8
downProvinceDeliveryDetailCsvFile.head=企业编号|企业名称|taskid|模板ID|主叫号码|被叫号码|接收号码|第三方投递请求时间|能力开放平台下发请求时间|投递结果通知时间|投递时延（毫秒）|原投递类型|投递类型|投递结果
#\u6210\u529F
success=成功
#\u5931\u8D25
failed=失败
#\u5BFC\u51FA\u76F4\u5BA2\u6708\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downEnterpriseMonthStatCsvFile.head=企业编号|企业名称|业务类型|业务形态|计费方式|月份|企业成员数量|总使用量|移动使用量|联通使用量|电信使用量|赠送|体验总量|体验余额|体验周期|当月使用体验额|可结算额|USSD使用量|闪信使用量
#\u5BFC\u51FA\u76F4\u5BA2\u65E5\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downEnterpriseDayStatCsvFile.head=企业编号|企业名称|业务类型|业务形态|计费方式|日期|企业成员数量|使用量|移动使用量|联通使用量|电信使用量|USSD使用量|闪信使用量
#\u5BFC\u51FA\u5206\u7701\u6708\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downProvinceMonthStaCsvFile.head=企业类型|省份|年月|业务类型|业务形态|企业数量|企业成员数量|总使用量|移动投递量|联通投递量|电信投递量
#\u5BFC\u51FA\u5206\u7701\u65E5\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downProvinceDayStaCsvFile.head=企业类型|省份|日期|业务类型|业务形态|企业数量|企业成员数量|总使用量|移动投递量|联通投递量|电信投递量
#\u5BFC\u51FA\u5730\u5E02\u6708\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downCityMonthStaCsvFile.head=企业类型|省份|地市|年月|业务类型|业务形态|企业数量|企业成员数量|总使用量|移动投递量|联通投递量|电信投递量
#\u5BFC\u51FA\u5730\u5E02\u6708\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downCityDayStaCsvFile.head=企业类型|省份|地市|日期|业务类型|业务形态|企业数量|企业成员数量|总使用量|移动投递量|联通投递量|电信投递量
#\u5BFC\u51FA\u5206\u7701\u4F01\u4E1A\u6708\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downProvinceEnterpriseMonthStaCsvFile.head=省份|地市|企业编号|企业类型|企业名称|年月|业务类型|业务形态|企业成员数量|总使用量|移动投递量|联通投递量|电信投递量
#\u5BFC\u51FA\u5206\u7701\u4F01\u4E1A\u65E5\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downProvinceEnterpriseDayStaCsvFile.head=省份|地市|企业编号|企业类型|企业名称|日期|业务类型|业务形态|企业成员数量|总使用量|移动投递量|联通投递量|电信投递量
#\u5BFC\u51FA\u53D1\u5C55\u91CF\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downEnterpriseDevStatInfoCsvFile.head=省份|地市|企业类型|业务类型|新增企业|到达企业|新增成员|退订成员|到达成员
#\u5BFC\u51FA\u53D1\u5C55\u91CF\u7EDF\u8BA1\u8BE6\u7EC6\u5934\u90E8\u000d\u000a
downEnterpriseDevStatInfoDetailCsvFile.head=企业名称|企业编码|新增成员|退订成员|是否新企业
#\u6279\u91CF\u5BFC\u5165\u9ED1\u767D\u540D\u5355size
batchAddBlackWhite.size=500
#\u6279\u91CF\u65B0\u589E2\u9ED1\u767D\u540D\u5355\u6BCF\u6B21\u6700\u5927size
batchAddBlackWhite.max.size=10
#\u6279\u91CF\u5BFC\u5165\u53D8\u91CF\u6A21\u677F\u6700\u5927size
importTemplateVar.size=500
#\u5355\u6B21\u6700\u5927\u503C
importTemplateVar.max.size=10
#\u540D\u7247\u5F69\u5370
cardPrint=名片彩印
#\u5E7F\u544A\u5F69\u5370
adPrint=广告彩印
#\u70ED\u7EBF\u5F69\u5370\u7701\u4EFD\u7248
hotlineprintprovincial=热线彩印省份版
#\u70ED\u7EBF\u5F69\u5370
hotlinePrint=热线彩印
#\u4E3B\u53EB\u5C4F\u663E
callerScreen=主叫屏显
#\u88AB\u53EB\u5C4F\u663E
calledScreen=被叫屏显
#\u6302\u673A\u77ED\u4FE1
hangUpSms=挂机短信
#\u6302\u673A\u5F69\u4FE1
hangUpMms=挂机彩信
#\u95EA\u4FE1
flashLetter=闪信
#USSD\u5931\u8D25\u8F6C\u95EA\u4FE1
UssdFailedToFalshLetter=USSD失败转闪信
#\u6309\u6761\u8BA1\u8D39
stripCharge=按条计费
#\u6309\u6708\u8BA1\u8D39

#\u5C4F\u663E
screen=屏显
#\u6587\u4EF6\u4E0B\u8F7D\u8DEF\u5F84
ecpmp.downloadFile.path=/sftp/data
batchAddTemplateHotline.size=1000

batchAddTemplateHotline.max.size=10
batchAddTemplateHotline.failedFile.head=热线号码|异常码|失败原因
#\u4E0A\u4F20\u6587\u4EF6\u6216\u56FE\u7247\u6587\u4EF6\u683C\u5F0F
upload.allTypeFile.allow.types=png|jpeg|jpg|xlsx|doc|pdf
#\u5BFC\u51FA\u4EE3\u7406\u5546\u6295\u9012\u660E\u7EC6\u5934\u90E8
downAgentDeliveryDetailCsvFile.head=代理商编码|企业名称|taskid|模板ID|主叫号码|被叫号码|接收号码|第三方投递请求时间|能力开放平台下发请求时间|投递结果通知时间|投递时延（毫秒）|投递类型|投递结果
#\u5BFC\u51FA\u5B50\u4F01\u4E1A\u6708\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downSubEnterpriseMonthStatCsvFile.head=子企业编号|子企业名称|业务类型|业务形态|计费方式|月份|企业成员数量|总使用量|移动使用量|联通使用量|电信使用量|USSD使用量|闪信使用量
#\u5BFC\u51FA\u5B50\u4F01\u4E1A\u65E5\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downSubEnterpriseDayStatCsvFile.head=子企业编号|子企业名称|业务类型|业务形态|计费方式|日期|企业成员数量|使用量|移动使用量|联通使用量|电信使用量
monthCharge=包月计费
#\u5BFC\u51FA\u4EE3\u7406\u5546\u6708\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downAgentEnterpriseMonthStatCsvFile.head=企业编号|企业名称|业务类型|业务形态|计费方式|月份|企业成员数量|总使用量|移动使用量|联通使用量|电信使用量|赠送|体验总量|体验余额|体验周期|当月使用体验额|可结算额|USSD使用量|闪信使用量
#\u5BFC\u51FA\u4EE3\u7406\u5546\u65E5\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downAgentEnterpriseDayStatCsvFile.head=企业编号|企业名称|业务类型|业务形态|计费方式|日期|企业成员数量|使用量|移动使用量|联通使用量|电信使用量|USSD使用量|闪信使用量

#\u5BFC\u51FA\u4EE3\u7406\u5546\u589E\u5F69\u7FA4\u53D1\u65E5\u7EDF\u8BA1\u4FE1\u606F\u5934\u90E8
downAgentEnterpriseEnhancedDayStatCsvFile.head=企业编号|企业名称|业务类型|日期|发送总量|发送成功|发送失败
#\u5BFC\u51FA\u6587\u4EF6\u5B58\u50A8\u5728NAS\u7684\u76EE\u5F55,\u586B\u5199NAS\u6620\u5C04\u7684docker\u76EE\u5F55
enterpriseDetailExport.nasPath=/sftp/data/export

#\u80FD\u529B\u5F00\u653E\u6D4B\u8BD5\u9ED8\u8BA4\u67E5\u8BE2\u4F01\u4E1AID
deliveryTestEnterpriseID=99999
#\u80FD\u529B\u5F00\u653E\u6D4B\u8BD5\u9ED8\u8BA4\u67E5\u8BE2\u4F01\u4E1A\u540D\u79F0
deliveryTestEnterpriseName=能力开放测试专用企业20190420
# eg: H5.File.Upload.Path=1-/home/<USER>/uploadFile/H5|2-/home/<USER>/uploadFile/H6
H5.File.Upload.Paths=1-/sftp/data/image|2-/sftp/data/cyTemplate
#\u6D3B\u52A8\u672A\u5F00\u59CB
notBgain=未开始
#\u6D3B\u52A8\u8FDB\u884C\u4E2D
haveInHand=进行中
#\u6D3B\u52A8\u5DF2\u7ED3\u675F
ended=已结束
#\u5BFC\u51FA\u76F4\u5BA2\u6D3B\u52A8\u7EDF\u8BA1\u5934\u90E8
downEnterpriseActivityStatCsvFile.head=企业编号|企业名称|活动编号|活动名称|活动状态|活动有效期|代言人总数|屏显推送总次数|代言获奖人数|挂机推送条数
#\u5BFC\u51FA\u5B50\u4F01\u4E1A\u6D3B\u52A8\u7EDF\u8BA1\u5934\u90E8
downAgentEnterpriseActivityStatCsvFile.head=代理商编码|代理商名称|子企业编号|企业名称|活动编号|活动名称|活动状态|活动有效期|代言人总数|屏显推送总次数|代言获奖人数|挂机推送条数
#\u589E\u5F3A\u5F69\u5370\u56FE\u7247\u4E0A\u4F20\u5141\u8BB8\u7C7B\u578B
upload.pic.ebanhanceMms.allow.types=gif|jpg
#\u589E\u5F3A\u5F69\u5370\u4E0A\u4F20\u56FE\u7247size\u6700\u5927\u503C
upload.pic.ebanhanceMms.max.size=204800
#\u589E\u5F3A\u5F69\u5370\u4E0A\u4F20\u89C6\u9891\u5141\u8BB8\u7C7B\u578B
upload.video.ebanhanceMms.allow.types=3gp|mp4
#\u589E\u5F3A\u5F69\u5370\u4E0A\u4F20\u89C6\u9891size\u6700\u5927\u503C
upload.video.ebanhanceMms.max.size=2097152
#\u7FA4\u53D1\u53F7\u7801\u6700\u5927\u6570\u91CF
group.send.msisdn.max.size=10000
#\u7FA4\u53D1\u4EFB\u52A1\u53F7\u7801\u5BFC\u51FA\u6587\u4EF6\u5934
downGroupSendMsisdnCsvFile.head=导入号码

ecpm.application.name=ecpm
ecpm.application.httpurl=http://127.0.0.1:21001

group.send.msisdn.sms.max.size=10000
ecpmp.createTaskMaxMsisdn.num=2000


#png|jpeg|jpg|PNG|JPEG|JPG
upload.cx.ebanhanceMms.allow.types=png|jpeg|jpg|PNG|JPEG|JPG


#\u5BFC\u51FA\u6210\u5458\u805A\u5408\u5934\u90E8 \u4EE3\u7406\u5546 \u7BA1\u7406\u5458
downCollectMemberCsvFile.head.agent.mamager=企业编号|企业名称|子企业编号|子企业名称|省份|地市|分组名称|子业务类型|创建时间|成员名称|成员号码|订购状态
#\u5BFC\u51FA\u6210\u5458\u805A\u5408\u5934\u90E8 \u5206\u7701 \u7BA1\u7406\u5458
downCollectMemberCsvFile.head.prvince.mamager=企业编号|企业名称|省份|地市|区县|渠道|分组名称|业务类型|子业务类型|创建时间|成员名称|成员号码|号码类型|订购状态
#\u5BFC\u51FA\u6210\u5458\u805A\u5408\u5934\u90E8 \u5206\u7701 \u4F01\u4E1A
downCollectMemberCsvFile.head.prvince.enterprise=省份|地市|渠道|业务类型|子业务类型|分组名称|创建时间|成员名称|成员号码|号码类型|订购状态
#\u5BFC\u51FA\u6210\u5458\u805A\u5408\u5934\u90E8 \u4EE3\u7406\u5546 \u4F01\u4E1A
downCollectMemberCsvFile.head.agent.enterprise=子企业编号|子企业名称|省份|地市|分组名称|子业务类型|创建时间|成员名称|成员号码|订购状态


login.JK.info.JkUserName=QYGLPTJK
login.JK.info.JkUserpwd=p5DgJZrymXY=
login.JK.info.resNum=QYGLPT
login.status.check.JKAuthPage=/view/cooperationManage/zhikeManage/enterprise/createEnterprise/createEnterprise.html;/view/cooperationManage/agentManage/enterprise/createEnterprise/createEnterprise.html;/view/cooperationManage/provincialManage/enterprise/createEnterprise/createEnterprise.html;/view/cooperationManage/merchantInfoManage/merchant/merchantDetail.html;/view/cooperationManage/merchantInfoManage/merchant/merchantDetail.html;/view/InfoStatistics/agentStatistics/querydeliveryDetailList/agentQuerydeliveryDetailList.html;/view/InfoStatistics/agentStatistics/queryotherdeliveryDetailList/agentQuerydeliveryDetailList.html;/view/InfoStatistics/zhikeStatistics/querydeliveryDetailList/zhikeQuerydeliveryDetailList.html;/view/InfoStatistics/zhikeStatistics/queryotherdeliveryDetailList/zhikeQuerydeliveryDetailList.html;/view/InfoStatistics/enterpriseStatistics/querydeliveryDetailList/querydeliveryDetailList.html;/view/InfoStatistics/enterpriseStatistics/queryotherdeliveryDetailList/querydeliveryDetailList.html;/view/InfoStatistics/merchantStatistics/spokesStatistics/spokeStatListManage/spokeStatListManage.html
login.status.check.needJKAuth=1
is.allow.move.menber.province.list=test

ioc.interface.platformId=10001
ioc.interface.accountPasswd=qiguan@123
delivery.platformid=474d6550f1d146cab56d33b722c61845
delivery.scalling=***********
industryIDAndName=101001&计算机/通信/电子,101002&金融/银行/保险/会计,101003&广告/媒体,101004&物流/运输,101005&能源/矿产/原材料,101006&文化/体育,101007&政府/非盈利组织,101008&农/林/牧/渔,101009&商业服务,101010&其他,101011&教育/培训类,101012&电商类,101013&制药/医疗,101014&保健食品类,101015&农药/兽药/饲料/饲料添加剂类,101016&特种设备类,101017&投资类,101018&美容行业类,101019&开锁业务类,101020&移民类,101021&房地产类
downOperLog.head=操作者|操作名称|操作时间|操作描述|IP
downMemberSubFaildCsvFile.head=成员名称|成员号码|导入时间|错误码|失败原因
downFailList.head=号码|状态|失败原因
OA.remote.ticket.stValidation.url=https://oa.migu.cn/sysso/v2/stValidation
OA.remote.ticket.sts.url=https://oa.migu.cn/sysso/login
OA.remote.ticket.service=https://cy.migudm.cn/qycy/ecpmp/ecpmpServices/OAService/remoteTicket
ecpmp.certiFile.downloadFile.path=/certiFile/
downH5BatchSubDetail.head=号码|产品ID|省份|开通时间|子业务类型|批设内容|开通状态|审核状态|驳回意见
#\u5BFC\u51FA\u76F4\u5BA2\u540D\u7247\u5F69\u5370\u5185\u5BB9
downContentInfoCsvFile.head.zhike.content=内容编号|内容信息|配置对象|投递日期|投递时间|投递方式|计费类型|套餐包|移动审核状态|联通审核状态|电信审核状态|移动审核意见|联通审核意见|电信审核意见|业务类型（原）
#\u5BFC\u51FA\u76F4\u5BA2\u70ED\u7EBF\u5185\u5BB9
downContentInfoCsvFile.head.zhike.hotline=内容编号|移动审核状态|联通审核状态|电信审核状态|移动审核意见|联通审核意见|电信审核意见|业务类型|内容信息
#\u5BFC\u51FA\u5206\u7701\u70ED\u7EBF\u5185\u5BB9
downContentInfoCsvFile.head.province.hotline=编号|移动审核状态|联通审核状态|电信审核状态|移动审核意见|联通审核意见|电信审核意见|业务类型|内容信息
#\u5BFC\u51FA\u5206\u7701\u540D\u7247\u5F69\u5370\u5185\u5BB9
downContentInfoCsvFile.head.province.content=编号|内容信息|触发对象|投递日期|投递时间|投递方式|移动审核状态|联通审核状态|电信审核状态|移动审核意见|联通审核意见|电信审核意见
export.portManager.content={"parentEnterpriseName":"代理商名称","enterpriseName":"子企业名称","hotlineSmsPort":"热线短信端口号","groupSendSmsPort":"通知短信端口号","groupSendFlashPort":"通知闪信端口号","groupSendMmsPort":"通知彩信、增彩端口号"}
export.taskList.content={"objectID":"任务ID","taskName":"任务名称","contentID":"内容编号","serviceType":"业务类型","createTime":"提交时间","src":"端口号","status":"状态","successNum":"成功/总数"}
export.contentManager.content={"contentID":"内容编号","approveStatus":"移动审核状态","unicomApproveStatus":"联通审核状态","telecomApproveStatus":"电信审核状态","subset":"移动审核意见","unicomApproveIdea":"联通审核意见","telecomApproveIdea":"电信审核意见","scene":"业务类型","content":"内容信息"}
member.order.status.cancel=退订中
#\u5BFC\u51FA\u9884\u8BBE\u56FA\u5B9A\u6A21\u677F
downYsmbInfoCsvFile.head.content=企业类型|业务类型|投递类型|内容编号|内容信息|提交时间|审核时间|移动审核状态|联通审核状态|电信审核状态|移动审核意见
fzdxbj=数智反诈短信被叫
fzsxzj=数智反诈闪信主叫
fzsxbj=数智反诈闪信被叫
task.remindUpdateTask.enterpriseIDs=
login.sms.enable=false
downContentInfoCsvFile.head.diffnet=模板ID|企业编号|企业名称|创建时间|联通通道|电信通道|状态
decrypt.whiteUrl.list=uploadFile|uploadImg|uploadAllTypeFile|uploadVideo|commonRemoteTicket|downloadFile
login.service.source.101.url=
login.service.source.101.logoutRedirtUrl=
downTemplateStatistics.head=业务类型|模板|模板内容|创建日期|使用量|移动使用量|联通使用量|电信使用量
#\u4E3B\u53EB\u6302\u673A\u77ED\u4FE1
CallerHangUpSms=主叫挂机短信
#\u88AB\u53EB\u6302\u673A\u77ED\u4FE1
CalledHangUpSms=被叫挂机短信
#\u884C\u4E1A\u6302\u673A\u77ED\u4FE1
HangYeHangUpSms=行业挂机短信
ecpmp.ThirdpartyAccess.enterprises=30001224

#\u914D\u7F6Ehttp\u8BF7\u6C42\u8DF3\u8F6C\u5230https\u8BF7\u6C42\u7684ip,\u7AEF\u53E3\uFF0C\u5F53\u7AEF\u53E3\u4E0Ehttp\u7AEF\u53E3\u4E00\u81F4\u65F6\uFF0C\u4E0D\u505A\u8DF3\u8F6C\uFF0Chttp\u8BF7\u6C42\u5F00\u653E
rediret.port=443
rediret.ip=*********
login.service.source.102.url=http://**************:8883/source102/CheckTicket
login.service.source.102.logoutRedirtUrl=http://localhost:28081/ecpfep/logOut
ecpmp.enterprise.service.product=[{"productId":"2000202701","productName":"正式5元套餐","quotaDescription":"网内2000条"}|{"productId":"2000375801","productName":"正式6元套餐","quotaDescription":"网内2000条，异网50条"}|{"productId":"2000375901","productName":"正式10元套餐","quotaDescription":"网内2000条，异网150条"}|{"productId":"2000376001","productName":"正式20元套餐","quotaDescription":"网内2000条，异网300条"}|{"productId":"2000376101","productName":"正式30元套餐","quotaDescription":"网内2000条，异网600条"}]
ecpmp.enterprise.gd.service.product=[{"productId":"1002410","productName":"行业挂短三网10套餐","quotaDescription":"100条"}|{"productId":"2002418","productName":"行业挂短三网18套餐","quotaDescription":"200条"}|{"productId":"5002438","productName":"行业挂短三网38套餐","quotaDescription":"500条"}|{"productId":"8002458","productName":"行业挂短三网58套餐","quotaDescription":"800条"}]

downMemberUnSubFaildCsvFile.head=分组名称|成员号码|错误码|失败原因
batchDeleteMember.failedFile.head=分组名称|成员号码|错误码|失败原因
importBatchDeleteMember.size=500
importBatchDeleteMember.batchSize=10
downUnsubRecord.head=成员号码|分组名称|创建时间|退订时间|退订状态|退订渠道|失败原因|错误码

login.service.source.5.url=http://**************:8883/source5/CheckTicket
login.service.source.5.logoutRedirtUrl=https://www.baidu.com/

login.service.source.5GCard.url=http://**************:8883/source5GCard/CheckTicket
login.service.source.5GCard.logoutRedirtUrl=https://www.baidu.com/
ecpmp.5GCard.enterprises=20001036

login.service.source.MiguSso.url=@login.service.source.MiguSso.url@
login.service.source.MiguSso.logoutRedirtUrl=@login.service.source.MiguSso.logoutRedirtUrl@


page.content.hint=[{"type":1,"hintContent":" 1. 如果申请三网，必须要有“来电”、“接听”标识，比如：给您来电、给您致电、感谢您接听、请您放心接听等。<br/> 2. 内容里如果需要发布电话号码，只能写一个号码，同时要上传《号码使用声明书》。<br/> 3. 由于签名系统会自动加在前面，因此内容里可以不再重新写签名，可以直接写：给您来电等。<br/> 4. 内容禁止发布营销及通知内容，但可以发布一句不涉及营销广告内容的祝福语。<br/> 5. 如果使用营业执照作为签名，发布的内容要与营业执照的经营范围一致。如果使用商标注册证作为签名，发布的内容要与商标注册证的类别一致。<br/> 6. 内容里不能含有黄赌毒及其他违反法律法规的内容。"},{"type":2,"hintContent":" 1. 如果申请三网，必须要有“来电”、“接听”标识，比如：感谢您的来电、感谢您致电、欢迎来电等。<br/> 2. 内容里如果需要发布电话号码，只能写一个号码，同时要上传《号码使用声明书》。<br/> 3. 由于签名系统会自动加在前面，因此内容里可以不再重新写签名，可以直接写：感谢您的来电等。<br/> 4. 内容禁止发布营销及通知内容，但可以发布一句不涉及营销广告内容的祝福语。<br/> 5. 如果使用营业执照作为签名，发布的内容要与营业执照的经营范围一致。如果使用商标注册证作为签名，发布的内容要与商标注册证的类别一致。<br/> 6.内容里不能含有黄赌毒及其他违反法律法规的内容。"},{"type":4,"hintContent":" 1. 企业签名建议使用营业执照全称，如果需要缩写，要保障签名唯一性（不能与其它企业的工商注册名重复，比如企查查上搜索后只有一个企业出现），不能缩减关键字。<br/> 比如：惠州市公安局仲恺高新技术产业开发区分局，只能写仲恺高新区公安局，高新区不能缩；观致汽车销售有限公司，只能写观致汽车销售，销售不能缩。<br/> 注意<br/> <p style='text-indent:2em;'>① XX街道社区卫生服务中心、XX金融纠纷调解中心这些社区卫生服务中心、调解类别的企业，后面的所有关键字（街道社区卫生服务中心、金融纠纷调解中心等）都不能缩写。<br/></p> <p style='text-indent:2em;'>② 供电类企业的缩写，比如：国网浙江省电力有限公司杭州供电公司，可以缩写国网杭州供电公司，国网不能缩。广东电网有限责任公司广州供电局，可以缩写广东电网广州供电局，广东电网不能缩。<br/></p> <p style='text-indent:2em;'>③ 分公司的缩写，首先要写总公司名，后面接分公司名，不能直接缩写分公司。比如：中国人民财产保险股份有限公司北京市分公司，缩写为：中国人民财险北京分公司。<br/></p> 2. 企业签名不加【】，平台会自动在签名两边添加【】放在内容栏前面。<br/> 3. 企业签名要与授权书保持完全一致。<br/> 4. 企业签名如果使用商标证、软著、许可证、备案证书、红头文件等，要提供与签名完全一致的商标注册证、软著、许可证、备案证书、红头文件等证照的扫描件。<br/>"},{"type":5,"hintContent":"接收闪信号码归属运营商。申请本网，只勾选移动。申请三网，勾选移动、联通、电信。如果不能勾选联通电信（灰色，无法勾选），说明套餐不包含联通电信投递条数。"},{"type":6,"hintContent":" 申请三网内容，必须提交营业执照和签名授权书。<br/> 1. 内容里如果发布电话号码，必须提交号码使用声明书。<br/> 2. 申请本网内容，可以只提交营业执照，但如果是党、政、军、事业单位等，必须要提交授权书或者协议。<br/> 3. 签名如果使用商标证、软著、许可证、备案证书等，要提供与签名完全一致的商标注册证、软著、许可证、备案证书等证照的扫描件；政府单位如果需要申请与注册名称不同的签名，需要提供与签名一致的政府发文成立的红头文件，文件不能是其自己出具的，需要为其上级单位发文，并且文件中要可以看出主管部门或者负责部门为申请单位。要提供从证照所有企业一直授权到咪咕新空完整链条的所有授权书，授权链所经过的所有授权企业、被授权企业的营业执照也都要提交。<br/> 4. 营业执照可以上传1张图片，其它资质可以上传6张图片，一共可以提交7张图片。如果提交的图片超过了7张图片，请使用其它图片软件，将图片拼一下，保持在7张图片以内即可提交。<br/>"}]
downMsisdnAuthEnterprise.head=公司名称|品牌费|号码量|创建时间|首次审核时间|审核状态
downMsisdnAuthDetail.head=认证号码|公司名称|展示名称|LOGO|审核状态|上线状态|开始时间|结束时间