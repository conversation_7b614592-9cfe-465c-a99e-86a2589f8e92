<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ProductFeeMapper">
    <resultMap id="ProductFeeWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProductFeeWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="productOrderID" column="productOrderID" javaType="java.lang.Integer" />
        <result property="operateCode" column="operateCode" javaType="java.lang.String" />
        <result property="feeID" column="feeID" javaType="java.lang.String" />
		<result property="paramID" column="paramID" javaType="java.lang.String" />
		<result property="paramName" column="paramName" javaType="java.lang.String" />
		<result property="insertTime" column="insertTime" javaType="java.util.Date" />
		<result property="paramValue" column="paramValue" javaType="java.lang.String" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
    </resultMap>
    


	<insert id="createProductFee">
		insert into  ecpm_t_product_fee
		(
		productOrderID,
		operateCode,
		feeID,
		paramID,
		paramName,
		insertTime,
		paramValue,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5
		)
		values
		<foreach collection="list" item="productFeeWrapper"
				 separator=",">
			(
			#{productFeeWrapper.productOrderID},
			#{productFeeWrapper.operateCode},
			#{productFeeWrapper.feeID},
			#{productFeeWrapper.paramID},
			#{productFeeWrapper.paramName},
			#{productFeeWrapper.insertTime},
			#{productFeeWrapper.paramValue},
			#{productFeeWrapper.reserved1},
			#{productFeeWrapper.reserved2},
			#{productFeeWrapper.reserved3},
			#{productFeeWrapper.reserved4},
			#{productFeeWrapper.reserved5}
			)
		</foreach>

	</insert>


</mapper>