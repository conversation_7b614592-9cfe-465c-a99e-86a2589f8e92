########################################################
###server
########################################################
spring.profiles.active=dev
spring.application.name=ecpfep
server.servlet.context-path=
http.port=28081
server.ip=************
server.port=28445
server.ssl.key-store=classpath:ssl.keystorde/keystore.jks
server.ssl.key-alias=tomcat
server.ssl.enabled=true
server.ssl.key-store-password=Bb2rs6eXBXhLZMwFMBm/0A==
server.ssl.key-store-type=JKS

redis.server.connect=**************:16379|**************:16379|**************:16379
redis.server.password=MALL36911284e743Dx
redis.server.connectionTimeout=2000
redis.server.maxIdle=80
redis.server.maxWaitMillis=60000
redis.server.maxTotal=100
redis.server.soTimeout=2000
redis.server.singleKeyTimeOut=100
redis.server.mutiKeyTimeOut=200

hotline-hotLinesList-length=50

########################################################
###spi.url
########################################################
dsum.application.url=127.0.0.1:21000
ecpm.application.url=127.0.0.1:21001
ecpe.application.url=127.0.0.1:21002
ecpaxb.application.url= 127.0.0.1:21003
server.ssl.enabled-protocols=TLSv1,TLSv1.1,TLSv1.2
server.ssl.ciphers=TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256,TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,TLS_ECDHE_RSA_WITH_RC4_128_SHA,TLS_RSA_WITH_AES_128_CBC_SHA256,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA256,TLS_RSA_WITH_AES_256_CBC_SHA,SSL_RSA_WITH_RC4_128_SHA
task.PropertyUtil.fixedDelay=0 0/30 * * * ?

endpoints.enabled=false
endpoints.env.enabled=false
endpoints.refresh.enabled=false

spring.main.allow-bean-definition-overriding=true
spring.config.import=optional:configserver:

feign.circuitbreaker.enabled=true
eureka.client.enabled=false