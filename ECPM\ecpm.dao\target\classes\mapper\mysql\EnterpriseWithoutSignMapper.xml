<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EnterpriseWithoutSignMapper">
	<resultMap id="enterpriseWithoutSignMap"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseWithoutSignWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer"/>
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer"/>
		<result property="enterpriseName" column="enterpriseName" javaType="java.lang.String"/>
		<result property="createTime" column="createTime" javaType="java.util.Date" />
	</resultMap>

	<insert id="insertEnterpriseWithoutSign" keyProperty="id" useGeneratedKeys="true">
		INSERT INTO ecpm_t_enterprise_without_sign
		(
		enterpriseID,
		enterpriseName,
		createTime
		)
		VALUES
		(
		#{enterpriseID},
		#{enterpriseName},
		#{createTime}
		)
	</insert>

	<select id="queryEnterpriseWithoutSignByParameters" resultMap="enterpriseWithoutSignMap">
		SELECT
		t.ID,
		t.enterpriseID,
		t.enterpriseName,
		t.createTime
		from ecpm_t_enterprise_without_sign t
		<trim prefix="where" prefixOverrides="and">
		<if test="enterpriseID!=null and enterpriseID!=''">
			and t.enterpriseID = #{enterpriseID}
		</if>
		<if test="enterpriseName!=null and enterpriseName!=''">
			and t.enterpriseName like "%"#{enterpriseName}"%"
		</if>
		</trim>
		order by t.createTime desc
		limit #{pageNo},#{pageSize}
	</select>

	<select id="queryCount" resultType="java.lang.Integer">
		SELECT
		count(0)
		from ecpm_t_enterprise_without_sign t
		<trim prefix="where" prefixOverrides="and">
			<if test="enterpriseID!=null and enterpriseID!=''">
				and t.enterpriseID = #{enterpriseID}
			</if>
			<if test="enterpriseName!=null and enterpriseName!=''">
				and t.enterpriseName like "%"#{enterpriseName}"%"
			</if>
		</trim>
	</select>

	<delete id="deleteEnterpriseWithoutSign">
		delete from ecpm_t_enterprise_without_sign where ID = #{id}
	</delete>
</mapper>