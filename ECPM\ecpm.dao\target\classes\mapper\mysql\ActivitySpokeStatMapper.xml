<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivitySpokeStatMapper">
	<resultMap id="activitySpokeWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivitySpokeStatWrapper">
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="activityID" column="activityID" javaType="java.lang.Integer" />
		<result property="isReward" column="isReward" javaType="java.lang.Integer" />
		<result property="screenCount" column="screenCount" javaType="java.lang.Integer" />
		<result property="endPhoneCount" column="endPhoneCount"
			javaType="java.lang.Integer" />
		<result property="callingCount" column="callingCount" javaType="java.lang.Integer" />
		<result property="calledCount" column="calledCount" javaType="java.lang.Integer" />
		<result property="gjdxCount" column="gjdxCount" javaType="java.lang.Integer" />
		<result property="gjcxCount" column="gjcxCount" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="endTime" column="endTime" javaType="java.util.Date" />
		<result property="dayCount" column="dayCount" javaType="java.lang.Integer" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.String" />
	</resultMap>

	<update id="updateActivitySpokeStat">
		update ecpm_t_spoke_activity_stat set
		<trim suffixOverrides=","
			suffix="where msisdn = #{msisdn} and activityID = #{activityID}">
			<if test="endTime!=null">
				endTime= #{endTime},
			</if>
			<if test="dayCount!=null">
				dayCount= #{dayCount},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime},
			</if>
			<if test="createTime!=null">
				createTime= #{createTime},
			</if>
			<if test="screenCount!=null">
				screenCount= #{screenCount},
			</if>
			<if test="endPhoneCount!=null">
				endPhoneCount= #{endPhoneCount},
			</if>
		</trim>
	</update>

	<select id="queryActivitySpokeStat" resultMap="activitySpokeWrapper">
		select
		msisdn,
		activityID,
		isReward,
		screenCount,
		endPhoneCount,
		callingCount,
		calledCount,
		gjdxCount,
		gjcxCount,
		createTime,
		endTime,
		dayCount,
		updateTime,
		operatorID
		from ecpm_t_spoke_activity_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				and activityID=#{activityID}
			</if>
			<if test="msisdn != null and msisdn != ''">
				and msisdn=#{msisdn}
			</if>
		</trim>
	</select>

	<select id="countActivitySpokeStat" resultType="java.lang.Integer">
		select
		count(1)
		from ecpm_t_spoke_activity_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				and activityID=#{activityID}
			</if>
			<if test="msisdn != null and msisdn != ''">
				and msisdn=#{msisdn}
			</if>
		</trim>
	</select>

	<insert id="insertActivitySpokeStat">
		INSERT INTO ecpm_t_spoke_activity_stat
		(
		msisdn,
		activityID,
		isReward,
		screenCount,
		endPhoneCount,
		callingCount,
		calledCount,
		gjdxCount,
		gjcxCount,
		createTime,
		endTime,
		dayCount,
		updateTime,
		operatorID
		)
		VALUES
		(
		#{msisdn},
		#{activityID},
		#{isReward},
		#{screenCount},
		#{endPhoneCount},
		#{callingCount},
		#{calledCount},
		#{gjdxCount},
		#{gjcxCount},
		#{createTime},
		#{endTime},
		#{dayCount},
		#{updateTime},
		#{operatorID}
		)
	</insert>

	<select id="queryActivitySpokeStatByActids" resultMap="activitySpokeWrapper">
		select
		msisdn,
		activityID,
		isReward,
		screenCount,
		endPhoneCount,
		callingCount,
		calledCount,
		gjdxCount,
		gjcxCount,
		createTime,
		endTime,
		dayCount,
		updateTime,
		operatorID
		from ecpm_t_spoke_activity_stat
		<trim prefix="where" prefixOverrides="and|or">
			activityID in
			<foreach item="activityID" index="index" collection="activityIDs"
				open="(" separator="," close=")">
				#{activityID}
			</foreach>
			<if test="msisdn != null and msisdn != ''">
				and msisdn=#{msisdn}
			</if>
		</trim>
	</select>

	<update id="batchUpdateActivitySpokeStat" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="activitySpokeWrapper" open="" separator=";">
			update ecpm_t_spoke_activity_stat set
			isReward= 1,
			updateTime= #{activitySpokeWrapper.updateTime}
			where msisdn = #{activitySpokeWrapper.msisdn} and activityID = #{activitySpokeWrapper.activityID} 
		</foreach>
	</update>
	
	<update id="batchUpdateActivitySpokeStatAmount" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="activitySpokeWrapper" open="" separator=";">
			update ecpm_t_spoke_activity_stat set			
			<if test="activitySpokeWrapper.screenCount != null">
			   screenCount= screenCount + #{activitySpokeWrapper.screenCount},
			</if>
			<if test="activitySpokeWrapper.endPhoneCount != null">			
			   endPhoneCount= endPhoneCount + #{activitySpokeWrapper.endPhoneCount},
			</if>					
			updateTime= #{activitySpokeWrapper.updateTime}			
			where msisdn = #{activitySpokeWrapper.msisdn} and activityID = #{activitySpokeWrapper.activityID} 
		</foreach>
	</update>
</mapper>