.cooperation-head {
    padding: 20px;
}

.cooperation-head .frist-tab {
    font-size: 16px;
}

.cooperation-head .second-tab {
    font-size: 14px;
}

.cooper-tab form {
    padding: 0 38px;
}

.cooper-tab p {
    font-size: 15px;
}

.form-group p {
    max-width: 100%;
    word-break: break-all;
}

.form-group a {
    word-break: break-all;
    display: block;
}

body {
    background: #F8F8F8;
}

.cooper-title {
    padding: 20px 20px 20px 38px;
    margin-left: 20px;
    color: #383838;
    font-size: 16px;
    background-color: #fff;
}

.cooper-tab {
    margin: 0 20px;
    background: #fff;
    border-radius: 2px;
    padding: 36px 10px 16px;
}

.form-group .control-label icon {
    color: #ff254c;
    vertical-align: sub;
    margin-right: 2px;
}

.form-group div {
    line-height: 34px;
}

.form-group label {
    padding-top: 7px;
}

.form-group div li {
    display: inline-block;
    margin-right: 10px;
    padding-right: 10px;
    cursor: pointer;
}

.form-group div li span {
    vertical-align: middle;
    margin-right: 4px;
}

.form-group .data-time {
    display: flex;
}

.form-group .data-time .to {
    padding: 0 20px;
}

.form-group .date .form-control {
    max-width: 210px;
}

.order-btn {
    margin: 40px 195px;
}

.order-btn .btn {
    margin-right: 20px;
}

.upload-img {
    padding: 20px 0;
}

.upload-img .images {
    position: relative;
    width: 100px;
    height: 100px;
    display: inline-block;
}

.upload-img .images .delete-btn {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    top: -5px;
    right: -5px;
    background: url(../../../../../assets/images/otherIcons20.png) no-repeat;
    background-position: -20px 0;
    cursor: pointer;
}

.upload-img img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    cursor: pointer;
    margin-right: 20px;
}

.upload-img .btn {
    vertical-align: bottom;
}