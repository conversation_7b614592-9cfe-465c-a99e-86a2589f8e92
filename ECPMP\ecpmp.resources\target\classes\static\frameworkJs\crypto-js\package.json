{"_from": "crypto-js", "_id": "crypto-js@4.1.1", "_inBundle": false, "_integrity": "sha512-o2JlM7ydqd3Qk9CA0L4NL6mTzU2sdx96a+oOfPu8Mkl/PK51vSyoi8/rQ8NknZtk44vq15lmhAj9CIAGwgeWKw==", "_location": "/crypto-js", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "crypto-js", "name": "crypto-js", "escapedName": "crypto-js", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#DEV:/", "#USER"], "_resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-4.1.1.tgz", "_shasum": "9e485bcf03521041bd85844786b83fb7619736cf", "_spec": "crypto-js", "_where": "D:\\DM_IDE\\workspace\\ECPMP", "author": {"name": "<PERSON>", "url": "http://github.com/evanvosberg"}, "browser": {"crypto": false}, "bugs": {"url": "https://github.com/brix/crypto-js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "JavaScript library of crypto standards.", "homepage": "http://github.com/brix/crypto-js", "keywords": ["security", "crypto", "Hash", "MD5", "SHA1", "SHA-1", "SHA256", "SHA-256", "RC4", "Rabbit", "AES", "DES", "PBKDF2", "HMAC", "OFB", "CFB", "CTR", "CBC", "Base64", "Base64url"], "license": "MIT", "main": "index.js", "name": "crypto-js", "repository": {"type": "git", "url": "git+ssh://**************/brix/crypto-js.git"}, "version": "4.1.1"}