var app = angular.module("myApp",["util.ajax",'page',"angularI18n","service.common"])
//自定义filter,格式化日期
app.filter('newDate',function(){
    return function(date){
        var new_date =  date.substr(0,4)+"-"+date.substr(4,2)+"-"+date.substr(6.2);
        return new_date;
    }
});
app.controller('EnterpriselistCtrl', function ($scope,$rootScope,$filter,$location,RestClientUtil,CommonUtils) {
    //初始化参数
    $scope.init = function () {
        $scope.id = $location.search().id || '';
        $scope.selectedProvince = null;
        $scope.selectedCity = null;
        $scope.selectedCounty = null;
        $scope.subCityList = null;
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        $scope.showCountyProIds = ["10"];
        $scope.queryProvinceAndCity($scope);
        if($scope.isSuperManager && $scope.enterpriseType =='3') { // 代理商成员管理新增行业挂机短信
            $scope.subserviceSelectMap.push( {"key":"43", "value": "行业挂机短信"})
        }
    };
    $scope.provincialIsNull = true;
    $scope.cityIsNull = true;
    /* 查询省市 */
    $scope.queryProvinceAndCity = function ($scope) {
    	//分省
        if($scope.enterpriseType == 5) {
	    	$scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
	    	$scope.cityList = JSON.parse(localStorage.getItem("cityList"));
            $scope.cacheCountyList = JSON.parse(localStorage.getItem("countyList"));
	        		if($scope.loginRoleType=='normalMangager'){
	   	             	if (sessionStorage.getItem("cacheProvinceIDs") && sessionStorage.getItem("cacheProvinceIDs") !== "null" && sessionStorage.getItem("cacheProvinceIDs") !== "undefind")
	   	                     {
	   	                         jQuery.each($scope.provinceList,function(i,e){
	   	                         var provinceList = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
	   	                         if(provinceList.length==1){
	   	                         jQuery.each(provinceList,function(a,b){
	   	                          if(e.fieldVal==b){
	   	                          $rootScope.$applyAsync(function () {
	   	                            $scope.selectedProvince = e;
	   	                            $scope.changeSelectedProvince(e);
	   	                             });
	   	                            }
	   	                           });
	   	                         }
	   	
	   	                         });
	   	                     }
	   	
	   	                     if (sessionStorage.getItem("cacheCityIDs") && sessionStorage.getItem("cacheCityIDs") !== "null" && sessionStorage.getItem("cacheCityIDs") !== "undefind")
	   	                     {
	   	                         jQuery.each($scope.cityList,function(i,e){
	   	                         var cityList = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
	   	                         if(cityList.length==1){
	   	                         jQuery.each(cityList,function(a,b){
	   	                         if(e.fieldVal==b){
	   	                         $rootScope.$applyAsync(function () {
	   	                               $scope.selectedCity = e;
	   	                             });
	   	                            }
	   	                         });
	   	                         }
	   	                        });
	   	                     }
	   	                  $scope.provinceList2={};
	   	                  $scope.cityList2={};
	   	   	            jQuery.each($scope.provinceList,function(i,e){
	   	   	                $scope.provinceList2[e.fieldVal]=e.authName;
	   	   	            });
	   	   	            jQuery.each($scope.cityList,function(i,e){
	   	   	                $scope.cityList2[e.fieldVal]=e.authName;
	   	   	            });
                        $scope.countyList2={}
                        jQuery.each($scope.cacheCountyList,function(i,e){
                            $scope.countyList2[e.fieldVal]=e.authName;
                        });
	   	          } 
	        	  if($scope.loginRoleType=='superrManager'){
	   	        	if (sessionStorage.getItem("cacheProvinceIDs") && sessionStorage.getItem("cacheProvinceIDs") !== "null" && sessionStorage.getItem("cacheProvinceIDs") !== "undefind")
                    {
                        jQuery.each($scope.provinceList,function(i,e){
                        var provinceIDs = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
                            if([e.provinceID] == provinceIDs[0]){
                            $rootScope.$applyAsync(function () {
                                $scope.selectedProvince = e;
                                $scope.changeSelectedProvince(e);
                              });
                            }
                            });
                    }

                    if (sessionStorage.getItem("cacheCityIDs") && sessionStorage.getItem("cacheCityIDs") !== "null" && sessionStorage.getItem("cacheCityIDs") !== "undefind")
                    {
                        jQuery.each($scope.cityList,function(i,e){
                            jQuery.each(e,function(i,subCity){
                            var cityIDs = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
                                if(subCity.cityID == cityIDs[0]){
                                $rootScope.$applyAsync(function () {
                                    $scope.selectedCity = subCity;
                                  });
                                }
                                });
                           });
                    }
                    $scope.provinceList2={};
        	    	$scope.cityList2={};
        	        	jQuery.each($scope.provinceList,function(i,e){
        	                $scope.provinceList2[e.provinceID]=e.provinceName;
        	            });
        	        	$scope.cityList =$scope.mapToList($scope.cityList);
        	        	for(var a=0;a<$scope.cityList.length;a++){
        	        		jQuery.each($scope.cityList[a],function(i,e){
        	                    $scope.cityList2[e.cityID]=e.cityName;
        	                });
        	        	}
                      $scope.countyList2={}
                      var req = {
                          cityID : ""
                      };
                      RestClientUtil.ajaxRequest({
                          type: 'POST',
                          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCountyList",
                          data: JSON.stringify(req),
                          success: function (result) {
                              $rootScope.$apply(function () {
                                  if(result.result.resultCode == '1030100000'){
                                      $scope.countyList3 = result.countyList;
                                      jQuery.each($scope.countyList3,function(i,e){
                                          $scope.countyList2[e.countyID]=e.countyName;
                                      });
                                  }else{
                                      $scope.tip=result.result.resultCode;
                                      $('#myModal').modal();
                                  }
                              })

                          },
                          error:function(){
                              $rootScope.$apply(function(data){
                                      $scope.tip="1030120500";
                                      $('#myModal').modal();
                                  }
                              )
                          }
                      })
	   	          }
	        		
	         if($scope.isProvincial)
	         {
        		 if($.cookie("reserved10") != null && $.cookie("reserved10") != undefined && $.cookie("reserved10") == '111')
                 {
        			 $scope.channelSelectMap = [{"key":"15","value": "集客"}];
                 } else if($.cookie("reserved10") != null && $.cookie("reserved10") != undefined && $.cookie("reserved10") == '112'){
                     $scope.channelSelectMap = [{"key":"25","value": "移动云PAAS"}];
                 }else if($.cookie("reserved10") != null && $.cookie("reserved10") != undefined && $.cookie("reserved10") == '113'){
                     $scope.channelSelectMap = [{"key":"35","value": "咪咕音乐"}];
                 }else {
                	 $scope.channelSelectMap = [{"key":"5","value": "省份"}];
                 }
                 $scope.channelObject = $scope.channelSelectMap[0];

                 if($.cookie('provinceID') != null
	        			 &&　$.cookie('provinceID')　!= undefined
	        			 && $.cookie('cityID') != null
	        			 && $.cookie('cityID') != undefined)
	        	 {
	        		var queryProvinceListReq = {};
	        	    /*查询省份*/
	        	    RestClientUtil.ajaxRequest({
	        	      type: 'POST',
	        	      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
	        	      data: JSON.stringify(queryProvinceListReq),
	        	      success: function (data) {
	        	        $rootScope.$apply(function () {
	        	          var result = data.result;
	        	          var provinceList = data.provinceList;
	        	          if (result.resultCode == '1030100000') {
	        	              jQuery.each(provinceList, function (i, e) {
	        	            	if($.cookie('provinceID') == e.provinceID)
	        	            	{
	        	            		$scope.provincialIsNull = false;
	        	            		$scope.provinceList[0] = e;
	        	            		$scope.selectedProvince = e;
	        	            		return;
	        	            	}
	        	          		});
    	              var provinceIds = [];
    	              provinceIds[0] = $.cookie('provinceID');
    	              var queryCityListReq = {};
    	              queryCityListReq.provinceIDs = provinceIds;
    	              /*查询地市*/
    	              RestClientUtil.ajaxRequest({
    	                type: 'POST',
    	                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
    	                data: JSON.stringify(queryCityListReq),
    	                success: function (data) {
    	                  $rootScope.$apply(function () {
    	                    var result = data.result;
    	                    if (result.resultCode == '1030100000') {
    	                      $scope.subCityList = {};
    	                      jQuery.each(data.cityList, function (i, e) {
  	        	            		jQuery.each(e, function (i, e) {
  	    	        	            	if(e.cityID == $.cookie('cityID'))
  	    	        	            	{
  	    	        	            		$scope.cityIsNull = false;
  	    	        	            		$scope.subCityList[0] = e;
  	    	        	            		$scope.selectedCity = e;
  	    	        	            		return;
  	    	        	            	}
  	    	        	              });
  	        	              });
    	                    }else {
    	    	                $scope.tip =result.resultCode;
    	    	                $('#myModal').modal();
    	    	              }
    	                  })
    	                },
    	                error:function(){
    	                    $rootScope.$apply(function(){
    	                        $scope.tip='1030120500';
    	                        $('#myModal').modal();
    	                        }
    	                    )
    	                }
    	              });
	        	       }
	        	          else {
	        	              $scope.tip=data.result.resultCode;
	        	              $('#myModal').modal();
	        	            }
	        	        })
	        	      },
	        	      error:function(){
	        	          $rootScope.$apply(function(){
	        	        	  	  $scope.tip = '1030120500';
	        	                  $('#myModal').modal();
	        	              }
	        	          )
	        	      }
	        	    });
	        	 }
	         }
         } else {
        	 var queryProvinceListReq = {};
        	    /*查询省份*/
        	    RestClientUtil.ajaxRequest({
        	      type: 'POST',
        	      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
        	      data: JSON.stringify(queryProvinceListReq),
        	      success: function (data) {
        	        $rootScope.$apply(function () {
        	          var result = data.result;
        	          if (result.resultCode == '1030100000') {
        	            $scope.provinceList = data.provinceList;
        	            if (!!$scope.provinceList) {
        	              var provinceIds = [];
        	              jQuery.each($scope.provinceList, function (i, e) {
        	                provinceIds[i] = e.provinceID;
        	              });
        	              var queryCityListReq = {};
        	              queryCityListReq.provinceIDs = provinceIds;
        	              
        	              /*查询地市*/
        	              RestClientUtil.ajaxRequest({
        	                type: 'POST',
        	                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
        	                data: JSON.stringify(queryCityListReq),
        	                success: function (data) {
        	                  $rootScope.$apply(function () {
        	                    var result = data.result;
        	                    if (result.resultCode == '1030100000') {
        	                      $scope.cityList = $scope.mapToList(data.cityList);
        	                    }else {
        	    	                $scope.tip =result.resultCode;
        	    	                $('#myModal').modal();
        	    	              }
        	                  })
        	                },
        	                error:function(){
        	                    $rootScope.$apply(function(){
        	                        $scope.tip='1030120500';
        	                        $('#myModal').modal();
        	                        }
        	                    )
        	                }
        	              });
        	            }
        	          }else {
        	              $scope.tip=data.result.resultCode;
        	              $('#myModal').modal();
        	            }
        	        })
        	      },
        	      error:function(){
        	          $rootScope.$apply(function(){
        	        	  	  $scope.tip = '1030120500';
        	                  $('#myModal').modal();
        	              }
        	          )
        	      }
        	    });
         }
         
    };
    //省市联动时调用，用于转化格式
    $scope.mapToList= function(map) {
        var result = [];
        jQuery.each(map, function (_, o) {
        	if(o=='000'){
      		  return;
      	    }
            if (o) {
                o.key =o[0].provinceID;
                result.push(o);
            }
        });
        return result;
    };
    
    //搜索省份改变时，找到对应的市
    $scope.changeSelectedProvince = function(selectedProvince) {
        $scope.subCityList =null;
        if(selectedProvince)
        {
        	if($scope.isProvincial || $scope.loginRoleType=='normalMangager'){
        		$scope.subCityList = $scope.cityList.filter(function(a){
    				return a.parentAuthID == selectedProvince.id;
    			});
        	}
        	if($scope.loginRoleType=='superrManager'){
        		jQuery.each($scope.cityList,function(i,e){
                    if(e.key == selectedProvince.provinceID){
                        $scope.subCityList =e;
                    }
                });
        	}
        	if($scope.loginRoleType=='superrManager' || $scope.isProvincial){
                if($scope.showCountyProIds.includes(selectedProvince.provinceID)){
                    $scope.showCountyFlag = true;
                }else {
                    $scope.showCountyFlag = false;
                }
            }
            if($scope.loginRoleType=='normalMangager'){
                jQuery.each($scope.showCountyProIds,function(i,e){
                    if(e == selectedProvince.fieldVal){
                        $scope.showCountyFlag = true;
                    }else {
                        $scope.showCountyFlag = false;
                    }
                });
            }

        }
        if(!selectedProvince){
            $scope.subCityList =null;
            $scope.showCountyFlag = false;
        }
    }
    
    $scope.changeSelectedCity = function (cityID){
  	  $scope.selectedCityID = cityID;
    };
    //搜索城市改变时，找到对应的区县
    $scope.changeSelectedCity2 = function(selectedCity) {
        $scope.subCountyList =null;
        if(selectedCity)
        {
            if($scope.isProvincial || $scope.loginRoleType=='superrManager'){
                if($scope.showCountyProIds.includes(selectedCity.provinceID)){
                    $scope.setSubCountyList(selectedCity.cityID);
                }
            }
            if($scope.loginRoleType=='normalMangager'){
                $scope.setSubCountyList(selectedCity.fieldVal);

            }
        }
        if(!selectedCity){
            $scope.subCountyList =null;
        }
    }
    $scope.setSubCountyList= function(cityID) {
        var req = {
            cityID : cityID
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCountyList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.subCountyListBak = result.countyList;

                        //非超管，展示管理员有的区县数据权限
                        if($scope.loginRoleType!='superrManager'|| $scope.isProvincial){
                            $scope.subCountyList=[];
                            jQuery.each($scope.cacheCountyList,function(i,e){
                                for (var j = 0; j < $scope.subCountyListBak.length; j++) {
                                    if($scope.subCountyListBak[j].countyID==e.fieldVal){
                                        $scope.subCountyList.push($scope.subCountyListBak[j])
                                    }
                                }
                            });
                        }
                        if($scope.loginRoleType=='superrManager'){
                            $scope.subCountyList = $scope.subCountyListBak
                        }

                    }else{
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }
    $scope.changeSelectedProvinceID = function (selectedProvinceID) {
  	  angular.forEach($scope.cityList, function (e, i) {
  			if (e.key == selectedProvinceID) {
  		        $scope.subCityList = angular.copy(e);
  		      }
  	    });
  	    if (!selectedProvinceID || selectedProvinceID =='000') {
  	      $scope.subCityList = null;
  	      $scope.selectedCityID = null;
  	    }else{
  	    	if($scope.subCityList){
//  	    		$scope.selectedCity =$scope.subCityList[0];
//  	    		$scope.selectedCityID =$scope.selectedCity.cityID;
//  	    		$scope.selectedCityName =$scope.selectedCity.cityName;
  	        	delete($scope.subCityList.key);
  	    	}
  	    }
//  	    if ($scope.subCityList) {
//  	    	$scope.provinceID = selectedProvinceID;
//	    		jQuery.each($scope.subCityList, function (i, e) {
//	    	        if (e.cityID == $scope.enterpriseInfo.cityID) {
//	    	        	$scope.selectedCity = e;
//	    	        }
//	    	      });
//	    		$scope.selectedCityID = $scope.selectedCity.cityID;
//  	      }
    }
    $scope.changeSubServiceTypeFromString = function (val, hangupType) {
        if(val){
            var subServiceTypeValue = "";
            if (val.indexOf("34:") == -1 && val.indexOf("35:") == -1 && val.indexOf("36:") == -1){
                subServiceTypeValue = val.replace("1:","主叫:").replace("2:","被叫:")
                    .replace("3:","主被叫:").replace("8:","挂彩:");
                if(val.indexOf("4") > -1){
                    if(hangupType == null || hangupType == '') {
                        subServiceTypeValue = subServiceTypeValue.replace("4:","被叫挂短:");
                    } else if(hangupType == 1) {
                        subServiceTypeValue = subServiceTypeValue.replace("4:","主叫挂短:");
                    } else if(hangupType == 2) {
                        subServiceTypeValue = subServiceTypeValue.replace("4:","被叫挂短:");
                    }else if(hangupType == 3 && $scope.isSuperManager&&$scope.enterpriseType =='3') {
//                        subServiceTypeValue = subServiceTypeValue.replace("4","行业挂机短信");
                    }
                }
                subServiceTypeValue = subServiceTypeValue.replaceAll(":1",":待提交审核").replaceAll(":2",":待审核").replaceAll(":3",":审核通过").replaceAll(":4",":审核驳回")

                return subServiceTypeValue;
            }else {
                return val.replace("34","数智反诈短信-被叫")
                    .replace("35","数智反诈闪信-主叫")
                    .replace("36","数智反诈闪信-被叫");
            }



        }else {
            return "";
        }
    }

    $scope.changeSubServiceType = function (val, hangupType) {
        if(val){
        	var subServiceTypeValue = "";
            if (val.indexOf("34") == -1 && val.indexOf("35") == -1 && val.indexOf("36") == -1){
            	subServiceTypeValue = val.replace("1","主叫").replace("2","被叫")
                .replace("3","主被叫").replace("8","挂彩");
            	if(val.indexOf("4") > -1){
            		if(hangupType == null || hangupType == '') {
                        subServiceTypeValue = subServiceTypeValue.replace("4","被叫挂短");
                    } else if(hangupType == 1) {
                    	subServiceTypeValue = subServiceTypeValue.replace("4","主叫挂短");
                    } else if(hangupType == 2) {
                    	subServiceTypeValue = subServiceTypeValue.replace("4","被叫挂短");
                    }else if(hangupType == 3 && $scope.isSuperManager&&$scope.enterpriseType =='3') {
                        subServiceTypeValue = subServiceTypeValue.replace("4","行业挂机短信");
                    }
            	}
            	return subServiceTypeValue;
            }else {
                return val.replace("34","数智反诈短信-被叫")
                    .replace("35","数智反诈闪信-主叫")
                    .replace("36","数智反诈闪信-被叫");
            }
        }else {
            return "";
        }
    };
    $scope.changeServiceType = function (val) {
        if(val){
            return val.replace("1","名片").replace("5","热线彩印省份版");
        }else {
            return "";
        }
    };
    $scope.changeChannel = function (val) {
        if(val){
            return val.replace("15","集客").replace("25","移动云PAAS").replace("35","咪咕音乐").replace("5","省份").replace("25","省份");
        }else {
            return "";
        }
    };

    $scope.subserviceMap = {
        "1": "主叫",
        "2": "被叫",
        "3": "主被叫",
        "4": "挂短",
        "8": "挂彩",
        "34": "数智反诈短信-被叫",
        "35": "数智反诈闪信-主叫",
        "36": "数智反诈闪信-被叫",
    };
    $scope.subserviceSelectMap = [
    {"key":"1","value":"主叫"},
    {"key":"2","value": "被叫"},
    {"key":"3","value": "主被叫"},
    // {"key":"4","value": "挂短"},
    {"key":"41","value": "主叫挂短"},
    {"key":"42","value": "被叫挂短"},
    {"key":"8","value": "挂彩"},
    {"key":"34","value": "数智反诈短信-被叫"},
    {"key":"35", "value": "数智反诈闪信-主叫"},
    {"key":"36", "value": "数智反诈闪信-被叫"},
    ];


    $scope.serviceSelectMap = [
        {"key":"1","value":"名片"},
        {"key":"5","value": "热线彩印省份版"},
    ];

    $scope.statusMap = {
        "1": "订购中",
        "0": "订购中",
        "2": "订购失败",
        "3": "订购成功",
        "4": "订购失败",
        "5": "订购失败",
        "6": "取消订购",
        "7": "确认信息下发失败",
        "8": "订购失败",
        "9": "BBOSS同步中",
        "10": "触发同步失败",
        "11": "待加入",
        "12": "待删除",
        "131": "退订中",
        "13": "退订失败"
    };

    $scope.unSubscribeStatusMap = {
        "1": "退订中",
        "2": "退订中",
        "4": "退订中"
    };

    $scope.msisMap = {
        // "0": "订购中",
        "1": "移动手机号码",
        "2": "企业短号",
        "3": "固话",
        "4": "异网手机号码",
        "5": "SIP语音",
        "6": "中间号",
        "7": "点击拨号",
        "8": "语音通知",
        "9": "全网IMS固话",
        "10": "云客服"
    };
    $scope.msisSelectMap = [
        {"key":"1","value":"移动手机号码"},
        {"key":"2","value": "企业短号"},
        {"key":"3","value": "固话"},
        {"key":"4","value": "异网手机号码"},
        {"key":"5","value": "SIP语音"},
        {"key":"6","value": "中间号"},
        {"key":"7","value": "点击拨号"},
        {"key":"8","value": "语音通知"},
        {"key":"9","value": "全国IMS固话"},
        {"key":"10","value": "云客服"},
    ];

    $scope.contentApproveStatusMap = [
        // {"key":"1","value":"待提交审核"},
        {"key":"2","value": "待审核"},
        {"key":"3","value": "审核通过"},
        {"key":"4","value": "审核驳回"}
    ];


    $scope.statusSelectMap = [
        {"key":"0,1","value":"订购中"},
        {"key":"2,4,5,8","value": "订购失败"},
        {"key":"3","value": "订购成功"},
        {"key":"6","value": "取消订购"},
        {"key":"7","value": "确认信息下发失败"},
        // {"key":"8","value": "BBOSS同步失败"},
        {"key":"9","value": "BBOSS同步中"},
        {"key":"10","value": "触发同步失败"},
        {"key":"11","value": "待加入"},
        {"key":"12","value": "待删除"},
        {"key":"131","value": "退订中"},
        {"key":"13","value": "退订失败"}
    ];


    $scope.channelSelectMap = [
	      // {"key":"5","value": "省份"},
	      // {"key":"15","value": "集客"},
          // {"key":"25","value": "移动云PAAS"}

    ];
    $scope.enterpriseTypeList = localStorage.getItem("enterpriseTypeList")
    if($scope.enterpriseTypeList!=null){
        angular.forEach(JSON.parse($scope.enterpriseTypeList),function (item){
            if("112" === item.fieldVal){
                $scope.channelSelectMap.push({"key":"25","value": "移动云PAAS"} );
            }
            if("111" === item.fieldVal){
                $scope.channelSelectMap.push({"key":"15","value": "集客"});
            }
            if("113" === item.fieldVal){
                $scope.channelSelectMap.push({"key":"35","value": "咪咕音乐"});
            }
            if("0" === item.fieldVal){
                $scope.channelSelectMap.push({"key":"5","value": "省份"});
            }
        });
    }
    //改变选择框
    $scope.changeSelected = function (item) {
        if ($.inArray(item, $scope.selectedListTemp) == -1) {
            $scope.selectedListTemp.push(item);
            $scope.selectedList.push(item);
        } else {
            $scope.selectedListTemp.splice($.inArray(item, $scope.selectedListTemp), 1);
            $scope.selectedList.splice($.inArray(item, $scope.selectedList), 1);
        }
        if ($scope.selectedListTemp.length == $scope.collectMemberInfoList.length) {
            $scope.allChoose = true;
        } else {
            $scope.allChoose = false;
        }
    };

    //更改全选框
    $scope.ifSelected = function () {
        console.log("$scope.allChoose::成员管理",$scope.allChoose)
        angular.forEach($scope.selectedListTemp, function (itemTemp) {
            $scope.selectedList.splice($.inArray(itemTemp, $scope.selectedList), 1);
        });
        if (!$scope.allChoose) {
            $scope.selectedListTemp = [];
            angular.forEach($scope.collectMemberInfoList, function (item) {

            	if(item.status != 0 && item.status != 1 && item.status != 9 && item.status != 11 && item.status != 12 && item.reserved4 != 1 && item.reserved4 != 2 && !item.delDisable){
            		item.checked = true;
            		$scope.selectedList.push(item);
            		$scope.selectedListTemp.push(item);
            	}
            })
        } else {
            angular.forEach($scope.collectMemberInfoList, function (item) {

                    item.checked = false;
                    $scope.selectedListTemp = [];
            })

        }
    };
    $scope.getQueryVariable =  function (variable)
    {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] == variable){return pair[1];}
        }
        return(false);
    };
    $scope.loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = ($scope.loginRoleType=='superrManager'||$scope.loginRoleType=='normalMangager');
    $scope.isZhike = ($scope.loginRoleType=='zhike');
    $scope.isAgent = ($scope.loginRoleType=='agent');
    $scope.isProvincial = ($scope.loginRoleType=='provincial');
    $scope.enterpriseType = null;
    if($scope.isZhike){
        $scope.enterpriseType ='1';
    }
    if($scope.isAgent){
        $scope.enterpriseType ='3';
    }
    if($scope.isProvincial){
        $scope.enterpriseType ='5';
    }
    if(!$scope.enterpriseType){
        let enterpriseType = $scope.getQueryVariable("enterpriseType");
        if(enterpriseType === "provincial"){
            $scope.enterpriseType = '5';
        }else if(enterpriseType === "business"){
            $scope.enterpriseType = '7';
        }else{
            $scope.enterpriseType = '3';
        }
    }

    if($scope.enterpriseType === "7"){
        $scope.statusSelectMap = [
            {"key":"0,1","value":"订购中"},
            {"key":"2,4,5,8","value": "订购失败"},
            {"key":"3","value": "订购成功"}
        ];
    }
    $scope.cookieEnterpriseID = JSON.parse($.cookie("enterpriseID"));
    $scope.deleteMember =  function (item) {
        if (item) {
            $scope.oneSelect = true;
            $scope.deleteSelect = item;
        }else{
            $scope.oneSelect = false;
            $scope.deleteSelect = null;
        };
        $('#deleteMemberPop').modal();
    };
    // 删除订购关系
    $scope.deleteOrder = function (item) {
        if (item) {
            $scope.oneSelect = true;
            $scope.deleteSelect = item;
        }else{
            $scope.oneSelect = false;
            $scope.deleteSelect = null;
        };
        $('#deleteOrderPop').modal();
    }
    $scope.deleteOrderConfirm = function () {
        $('#deleteMemberPop').modal("hide");
         let removeReq;
        if (!$scope.deleteSelect) {
           return;
        }
        removeReq = {
            "memberIDList":[$scope.deleteSelect.id],
            "orgID":$scope.deleteSelect.orgId,
            "servType": $scope.deleteSelect.servTypes,
            "enterpriseID":$scope.deleteSelect.enterpriseID
        };
        RestClientUtil.ajaxRequest({
                    type: 'POST',
                    url: "/ecpmp/ecpmpServices/organizationService/deleteOrderRelation",
                    data: JSON.stringify(removeReq),
                    success: function (data) {
                        $rootScope.$apply(function () {
                            var result = data.result;
                            $('#delOrderCancel').click();
                            if (result.resultCode == '1030100000') {
                                $scope.tip = '操作成功';
                                $('#myModal').modal();
                                $scope.enterpriseList();
                            } else {
                                $scope.tip = result.resultCode;
                                $('#myModal').modal();
                            }
                            $scope.oneSelect = false;
                            $scope.deleteSelect = null;
                        })
                    },
                    error: function () {
                        $rootScope.$apply(function () {
                            $('#delOrderCancel').click();
                            $scope.tip = '1030120500';
                            $('#myModal').modal();
                            $scope.oneSelect = false;
                            $scope.deleteSelect = null;
                        })
                    }
                });
    }


    $scope.exportMember = function () {
        if( ($scope.enterpriseID == null || $scope.enterpriseID === '')
            &&($scope.enterpriseName == null || $scope.enterpriseName === '')
            &&($scope.secondEnterpriseID== null || $scope.secondEnterpriseID === '')
            &&($scope.secondEnterpriseName== null || $scope.secondEnterpriseName === '')
            &&($scope.organizationName== null || $scope.organizationName === '')
            &&($scope.serviceTypeObject== null || $scope.serviceTypeObject === '')
            &&($scope.subServiceTypeObject== null || $scope.subServiceTypeObject === '')
            &&($scope.memberNum== null || $scope.memberNum === '')
            &&($scope.subscribeStatusObject== null || $scope.subscribeStatusObject === '')

            &&($scope.msisdnTypeObject== null || $scope.msisdnTypeObject === '')
        ){
        	if($scope.enterpriseType == 3 && 
        			($scope.provinceID== null || $scope.provinceID === '')
                    &&($scope.selectedCity== null || $scope.selectedCity === '')) {
        		$scope.tip = '请填写查询条件';
                $('#myModal').modal();
                return;
        	}
        	if($scope.enterpriseType == 5 &&
        			($scope.selectedProvince== null || $scope.selectedProvince === '')
                    &&($scope.selectedCity== null || $scope.selectedCity === '')
                    &&($scope.channelObject == null || $scope.channelObject === '')) {
        		$scope.tip = '请填写查询条件';
                $('#myModal').modal();
                return;
        	}
        }
        if($scope.enterpriseID != null && $scope.enterpriseID != '' 
        	&& Number.isNaN(+$scope.enterpriseID) && angular.isNumber(+$scope.enterpriseID)){
        	$scope.tip = '请正确输入查询条件';
            $('#myModal').modal();
            return;
        }else {
            if(parseInt($scope.enterpriseID) > 2147483647){
                $scope.tip = '企业编号输入值非法';
                $('#myModal').modal();
                return;
            }
        }
        if($scope.secondEnterpriseID != null && $scope.secondEnterpriseID != '' 
        	&& Number.isNaN(+$scope.secondEnterpriseID) && angular.isNumber(+$scope.secondEnterpriseID)){
        	$scope.tip = '请正确输入查询条件';
            $('#myModal').modal();
            return;
        }else {
            if(parseInt($scope.secondEnterpriseID) > 2147483647){
                $scope.tip = '子企业编号输入值非法';
                $('#myModal').modal();
                return;
            }
        }
        if($scope.memberNum != null && $scope.memberNum != '' 
        	&& Number.isNaN(+$scope.memberNum) && angular.isNumber(+$scope.memberNum)){
        	$scope.tip = '请正确输入查询条件';
            $('#myModal').modal();
            return;
        }


        if($scope.selectedList.length>0){
            let memberIds = [];
            for(let i = 0;i<$scope.selectedList.length;i++ ){
                memberIds.push($scope.selectedList[i].id);
            }
            $scope.reqTemp.memberIds = memberIds;
            //导出勾选
            var req = {
                "param":{"req":JSON.stringify($scope.reqTemp)},
                "url":"/qycy/ecpmp/ecpmpServices/organizationService/downCollectMemCsvFileService",
                "method":"get"
            }
        }else {
            //导出当前页
            var req = {
                "param":{"req":JSON.stringify($scope.reqTemp)},
                "url":"/qycy/ecpmp/ecpmpServices/organizationService/downCollectMemCsvFileService",
                "method":"get"
            }
        }
        //管理员
        if($scope.isSuperManager){
            //分省
            if($scope.enterpriseType == 5) {
                req.param.type = 2;//分省管理员
            }else{
                req.param.type = 1;//代理商管理员
            }
        }else{
            //普通成员
            if($scope.enterpriseType == 5) {
                req.param.type = 4;
            }else{
                req.param.type = 3;
            }
        }
        if($scope.countyList2 != undefined){
            req.param.map = JSON.stringify($scope.countyList2)
        }

        if($scope.reqTemp != undefined)
        {
        	CommonUtils.exportFile(req);
        }
        $scope.reqTemp.memberIds = null;
    };
    $scope.isOpenAgent = function (item,i) {
        if(item.channel == 35){
            item.delDisable = true;
            return true;
        }
        return CommonUtils.isOpenAgent(item.enterpriseID,function (enterprisesids){
            if (i || i==0) {
                $scope.collectMemberInfoList[i].delDisable = enterprisesids && enterprisesids.indexOf(item.enterpriseID) >= 0
            }
            return enterprisesids&&enterprisesids.indexOf(item.enterpriseID)>=0
        });
    }
    //删除操作
    $scope.remove = function (item) {
        $('#deleteMemberPop').modal("hide");
        let removeReq;
        if ($scope.deleteSelect) {
             removeReq = {"collectMemberInfoList":[$scope.deleteSelect]};
        }else {
            //分省查看是否允许管理成员

            if($scope.enterpriseType == 5){
                let reqList = [];
                for(let i = 0; i<$scope.selectedList.length;i++){
                    if($scope.selectedList[i].isAllowMangementMember==0&&$scope.selectedList[i].status!=1&&$scope.selectedList[i].status!=0){
                        reqList.push($scope.selectedList[i]);
                    }
                }
                removeReq = {"collectMemberInfoList":reqList};
            }else{
                let reqList = [];
                for(let i = 0; i<$scope.selectedList.length;i++){
                    if($scope.selectedList[i].status!=1&&$scope.selectedList[i].status!=0){
                        reqList.push($scope.selectedList[i]);
                    }
                }
                removeReq = {"collectMemberInfoList":reqList};
            }
        }
        if(removeReq.collectMemberInfoList.length == 0){
            $scope.tip = '不存在需要删除的成员';
            $('#myModal').modal();
            return;
        }
        for(let i = 0; i<removeReq.collectMemberInfoList.length;i++){
            if(removeReq.collectMemberInfoList[i].delDisable){
                $scope.tip = '存在不允许操作的企业';
                $('#myModal').modal();
                return;
            }
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/batchDeleteMem",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    $('#delMemCancel').click();
                    if (result.resultCode == '1030100000') {
                        $scope.tip = '操作成功';
                        $('#myModal').modal();
                        $scope.enterpriseList();
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#delMemCancel').click();
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };
    //获取queryEnterpriseList接口的数据
    $scope.enterpriseList = function (condition) {
        if(!$scope.isSuperManager){
            $scope.enterpriseID = null;
        }

        if( ($scope.enterpriseID == null || $scope.enterpriseID === '')
            &&($scope.enterpriseName == null || $scope.enterpriseName === '')
            &&($scope.secondEnterpriseID== null || $scope.secondEnterpriseID === '')
            &&($scope.secondEnterpriseName== null || $scope.secondEnterpriseName === '')
            &&($scope.organizationName== null || $scope.organizationName === '')
            &&($scope.serviceTypeObject== null || $scope.serviceTypeObject === '')
            &&($scope.subServiceTypeObject== null || $scope.subServiceTypeObject === '')
            &&($scope.memberNum== null || $scope.memberNum === '')
            &&($scope.subscribeStatusObject== null || $scope.subscribeStatusObject === '')

            &&($scope.msisdnTypeObject== null || $scope.msisdnTypeObject === '')
           ){
        	if($scope.enterpriseType == 3 && 
        			($scope.provinceID== null || $scope.provinceID === '')
                    &&($scope.selectedCity== null || $scope.selectedCity === '')) {
        		$scope.tip = '请填写查询条件';
                $('#myModal').modal();
                return;
        	}
        	if($scope.enterpriseType == 5 &&
        			($scope.selectedProvince== null || $scope.selectedProvince === '')
                    &&($scope.selectedCity== null || $scope.selectedCity === '')
                    &&($scope.channelObject == null || $scope.channelObject === '')) {
        		$scope.tip = '请填写查询条件';
                $('#myModal').modal();
                return;
        	}
            if($scope.enterpriseType == 7 &&
                ($scope.selectedProvince== null || $scope.selectedProvince === '')
                &&($scope.subscribeStatusObject== null || $scope.subscribeStatusObject === '')
                &&($scope.selectedCity== null || $scope.selectedCity === '')
                &&($scope.contentApproveStatus == null || $scope.contentApproveStatus === '')) {
                $scope.tip = '请填写查询条件';
                $('#myModal').modal();
                return;
            }
        }
        if($scope.enterpriseID != null && $scope.enterpriseID != '' 
        	&& Number.isNaN(+$scope.enterpriseID) && angular.isNumber(+$scope.enterpriseID)){
        	$scope.tip = '请正确输入查询条件';
            $('#myModal').modal();
            return;
        }else {
            if(parseInt($scope.enterpriseID) > 2147483647){
                $scope.tip = '企业编号输入值非法';
                $('#myModal').modal();
                return;
            }
        }

        if($scope.secondEnterpriseID != null && $scope.secondEnterpriseID != '' 
        	&& Number.isNaN(+$scope.secondEnterpriseID) && angular.isNumber(+$scope.secondEnterpriseID)){
        	$scope.tip = '请正确输入查询条件';
            $('#myModal').modal();
            return;
        }else {
            if(parseInt($scope.secondEnterpriseID) > 2147483647){
                $scope.tip = '子企业编号输入值非法';
                $('#myModal').modal();
                return;
            }
        }

        if($scope.memberNum != null && $scope.memberNum != '' 
        	&& Number.isNaN(+$scope.memberNum) && angular.isNumber(+$scope.memberNum)){
        	$scope.tip = '请正确输入查询条件';
            $('#myModal').modal();
            return;
        }
        if(!$scope.isSuperManager){
            $scope.enterpriseID = $scope.cookieEnterpriseID;
        }

        $("#allChoose").removeAttr("checked");
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        let queryType = $scope.enterpriseType =='3'?"2":"1";
        queryType =  $scope.enterpriseType =='7'?"3":queryType;
        if(condition!='justPage'){
            var req = {
                "queryType": queryType,
                "enterpriseID":$scope.isSuperManager?null:$scope.cookieEnterpriseID,
                "queryMemberCond":{
                    "parentEnterpriseID":$scope.enterpriseType !=='3'?$scope.secondEnterpriseID:$scope.enterpriseID,
                    "enterpriseID":$scope.enterpriseType !=='3'?$scope.enterpriseID:$scope.secondEnterpriseID,
                    "parentEnterpriseName":$scope.enterpriseType !=='3'?$scope.secondEnterpriseName:$scope.enterpriseName,
                    "enterpriseName":$scope.enterpriseType !=='3'?$scope.enterpriseName:$scope.secondEnterpriseName,
                    "orgName":$scope.organizationName,
                    "servTypeList":$scope.serviceTypeObject!=null?[$scope.serviceTypeObject.key]:null,

                    "msisdnType":$scope.msisdnTypeObject!=null?$scope.msisdnTypeObject.key:null,
                    "msisdn":$scope.memberNum,
                    "status":$scope.subscribeStatusObject!=null?$scope.subscribeStatusObject.key.split(","):null,
                    "extInfo":null,
                    "contentApproveStatus":$scope.contentApproveStatus!=null?$scope.contentApproveStatus.key:null,
                },
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize":$scope.pageInfo[0].pageSize,
                    "isReturnTotal": "1"
                }
            };
            var subServType = $scope.subServiceTypeObject!=null?[$scope.subServiceTypeObject.key]:null;
        	if($scope.subServiceTypeObject != null && $scope.subServiceTypeObject.key == '41') {
        		subServType = [4];
        		req.queryMemberCond.hangupType = 1;
        	}
        	if($scope.subServiceTypeObject != null && $scope.subServiceTypeObject.key == '42') {
        		subServType = [4];
        		req.queryMemberCond.hangupType = 2;
        	}
            if($scope.subServiceTypeObject != null && $scope.subServiceTypeObject.key == '43') {
                subServType = [4];
                req.queryMemberCond.hangupType = 3;
            }

        	req.queryMemberCond.subServTypeList = subServType;
            req.queryMemberCond.provinceIDs =[];
            req.queryMemberCond.cityIDs = [];
            //分省
            if($scope.enterpriseType == 5) {
            	if($scope.channelObject != null)
        		{
            		req.queryMemberCond.channel = $scope.channelObject.key;
        		}
	            if($scope.loginRoleType=='superrManager'){
	            	if ($scope.selectedProvince==undefined) {
	            		req.queryMemberCond.provinceIDs = [];
	                }
	                 else{
	                	if($scope.selectedProvince.provinceID != '000')
	                	{
	                		req.queryMemberCond.provinceIDs = [$scope.selectedProvince.provinceID];
	                	}
	                }
	            	if ($scope.selectedCity==undefined) {
	            		req.queryMemberCond.cityIDs = [];
	                }
	                 else{
	                    req.queryMemberCond.cityIDs = [$scope.selectedCity.cityID];
	                }
                    if ($scope.selectedCounty==undefined) {
                        var countyIds = [];
                        countyIds.push(-1)
                            jQuery.each($scope.countyList3, function (i, e) {
                                countyIds.push(e.countyID)
                            });


                        req.queryMemberCond.countyIDs =countyIds
                    }
                    else{
                        req.queryMemberCond.countyIDs = [$scope.selectedCounty.countyID];
                    }
	            }
	            if($scope.isProvincial ||$scope.loginRoleType=='normalMangager'){
	            	var provinceIds = [];
	                if($scope.selectedProvince==undefined){
	            		req.queryMemberCond.provinceIDs = [];

	                }else{
	                	if($scope.selectedProvince.fieldVal != '000')
	                	{
	                		provinceIds = [$scope.selectedProvince.fieldVal];
	                	}
	                }
	                req.queryMemberCond.provinceIDs = provinceIds;
	            	if ($scope.selectedCity==undefined) {
	                    var cityIds = [];
	               
	                    req.queryMemberCond.cityIDs = cityIds;
	                }
	                 else{
	                    req.queryMemberCond.cityIDs = [$scope.selectedCity.fieldVal];
	                }
	                 if($scope.loginRoleType=='normalMangager'){
                         if ($scope.selectedCounty==undefined) {
                             var countyIds = [];
                             countyIds.push(-1)
                             if($scope.selectedCity==undefined){
                                 jQuery.each($scope.cacheCountyList, function (i, e) {
                                     countyIds.push(e.fieldVal)
                                 });
                             }else{
                                 jQuery.each($scope.subCountyList, function (i, e) {
                                     countyIds.push(e.countyID)
                                 });
                             }
                             req.queryMemberCond.countyIDs =countyIds
                         }
                         else{
                             req.queryMemberCond.countyIDs = [$scope.selectedCounty.countyID];
                         }
                     }

	            }
	
	            if($scope.loginRoleType=='superrManager'){
	                if(req.queryMemberCond.provinceIDs.length!=0||req.queryMemberCond.cityIDs.length!=0) {
	                   sessionStorage.setItem("cacheProvinceIDs",JSON.stringify(req.queryMemberCond.provinceIDs));
	                   sessionStorage.setItem("cacheCityIDs",JSON.stringify(req.queryMemberCond.cityIDs));
	                }
	                // if(sessionStorage.getItem("cacheProvinceIDs")) {
	                //    req.queryMemberCond.provinceIDs = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
	                // }
	                // if(sessionStorage.getItem("cacheCityIDs")) {
	                //    req.queryMemberCond.cityIDs = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
	                // }
	            }
	
	            if($scope.isProvincial ||$scope.loginRoleType=='normalMangager'){
	            	if(req.queryMemberCond.provinceIDs.length!=0||req.queryMemberCond.cityIDs.length!=0) {
	                   sessionStorage.setItem("cacheEnterpriseName",$scope.enterpriseName == undefined ? "" : $scope.enterpriseName);
	                   sessionStorage.setItem("cacheProvinceIDs",JSON.stringify(req.queryMemberCond.provinceIDs));
	                   sessionStorage.setItem("cacheCityIDs",JSON.stringify(req.queryMemberCond.cityIDs));
	                }
	                // if(sessionStorage.getItem("cacheProvinceIDs")) {
	                //    req.queryMemberCond.provinceIDs = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
	                // }
	                // if(sessionStorage.getItem("cacheCityIDs")) {
	                //    req.queryMemberCond.cityIDs = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
	                // }
	            }
            }
            else {
            	if ($scope.provinceID != null && $scope.provinceID != '000') {
            		req.queryMemberCond.provinceIDs = [$scope.provinceID];
                  if($scope.selectedCity && $scope.provinceID !='000'){
                	  req.queryMemberCond.cityIDs = [$scope.selectedCity.cityID];
                  }
                  //为创建企业的城市赋值
                  if ($scope.selectedCityID) {
                	  req.queryMemberCond.cityIDs = [$scope.selectedCityID];
                  }
                }
            }
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.pageParameter.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }
        console.log(req);

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryCollectMember",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '1030100000'){
                        $scope.collectMemberInfoList =result.collectMemberInfoList;
                        angular.forEach($scope.collectMemberInfoList, function (item,index) {
                            $scope.isOpenAgent(item,index);
                        })
                        //获取页面的总条数与总页面
                        $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                        if($scope.pageInfo[0].totalCount==0){
                            // $scope.pageInfo[0].totalPage=0;
                            $scope.pageInfo[0].currentPage=1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage=1;
                        }else{
                            $scope.pageInfo[0].totalPage=Math.ceil(parseInt(result.totalNum)/parseInt($scope.pageInfo[0].pageSize));
                        }
                    }else{
                        $scope.queryEnterpriseList=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }

    $scope.sync = function (item) {
        $scope.syncMember=item;
        $('#syncMemberPop').modal();
    }

    $scope.reSyncMember = function () {
        $('#syncMemberPop').modal("hide");

        if($scope.syncMember.channel === '15'){
            var syncReq = {
                "memberID": $scope.syncMember.id,
                "orgID": $scope.syncMember.oriOrgID == undefined ? $scope.syncMember.orgId : $scope.syncMember.oriOrgID,
                "enterpriseID": $scope.syncMember.enterpriseID,
                "servType": $scope.syncMember.servTypes
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/organizationService/retrySyncMember",
                data: JSON.stringify(syncReq),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '1030100000') {
                            $scope.tip = '操作成功';
                            $('#myModal').modal();
                            $scope.enterpriseList();
                        } else {
                            $scope.tip = result.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                        $scope.tip = '1030120500';
                        $('#myModal').modal();
                    })
                }
            });
        }else {
            var syncReq = {
                "memberID": $scope.syncMember.id,
                "orgID": $scope.syncMember.orgId,
                "enterpriseID": $scope.syncMember.enterpriseID,
                "servType": $scope.syncMember.servTypes
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/organizationService/syncMember",
                data: JSON.stringify(syncReq),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '1030100000') {
                            $scope.tip = '操作成功';
                            $('#myModal').modal();
                            $scope.enterpriseList();
                        } else {
                            $scope.tip = result.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                        $scope.tip = '1030120500';
                        $('#myModal').modal();
                    })
                }
            });
        }
    };
});
