var app = angular.module("myApp", ["util.ajax", "top.menu","angularI18n","cy.uploadify","service.common"])
app.controller('enterpriseController', function ($scope, $rootScope, $http, $location, RestClientUtil,CommonUtils) {
	
	  $scope.filePicker = "filePicker";
	  $scope.accepttype = "jpg,jpeg,png";
	  $scope.isValidate = true;
	  $scope.filesize = 20;
	  $scope.mimetypes = ".jpg,.jpeg,.png";
	  
	  $scope.isCreateThumbnail = true;
	  $scope.uploadurl ='/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
	  $scope.uploadDesc = "必填，仅支持一张图片，仅支持jpg，jpeg，png格式";
	  $scope.numlimit = 1;
	  /*$scope.urlList = [];*/
	  $scope.uploadParam = {
	    enterpriseId: $scope.id ||'',
	    fileUse: 'businessLicense'
	  };
	
	  $scope.$on("uploadifyid1",function(event,fileUrl){
          $scope.businessLicenseURL = fileUrl;
	  });

  /* 从url中获取参数 */
  $scope.convertUrlToPara = function ($scope) {
    var url = angular.copy(window.location.href);
    url = url.split("?")[1];
    /*.substring(0, url.length-2)*/
    var res = {};
    if (!!url) {
      var para = url.split("&");
      var arr = [];
      var len = para.length;
      for (var i = 0; i < len; i++) {
        arr = para[i].split("=");
        res[arr[0]] = arr[1];
      }
    }
    return res;
  };

  /* 查询省市 */
  $scope.queryProvinceAndCity = function ($scope) {
    var queryProvinceListReq = {};
    /*查询省份*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
      data: JSON.stringify(queryProvinceListReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.provinceList = data.provinceList;
            if (!!$scope.provinceList) {
              var provinceIds = [];
              jQuery.each($scope.provinceList, function (i, e) {
                provinceIds[i] = e.provinceID;
              });
              var queryCityListReq = {};
              queryCityListReq.provinceIDs = provinceIds;
              
              /*查询地市*/
              RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
                data: JSON.stringify(queryCityListReq),
                success: function (data) {
                  $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                      $scope.cityList = $scope.mapToList(data.cityList);
                      $scope.changeSelectedProvince($scope.enterpriseInfo.provinceID);
                    }else {
    	                $scope.tip =result.resultCode;
    	                $('#myModal').modal();
    	              }
                  })
                },
                error:function(){
                    $rootScope.$apply(function(){
                        $scope.tip='**********';
                        $('#myModal').modal();
                        }
                    )
                }
              });
            }
          }else {
              $scope.tip=data.result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
        	  	  $scope.tip = '**********';
                  $('#myModal').modal();
              }
          )
      }
    });
  };


  /* 初始化新增企业 */
  $scope.initEnterprise = function ($scope) {
    $scope.enterpriseInfo = {};
    $scope.accountInfo = {};
    $scope.fileUrl ='';
    var loginRoleType=$.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
    $scope.isProvincial=true;
    $scope.enterpriseType = 5;

    $scope.mpPackageinfoName = '';
    $scope.mpPackageinfoQuota = '';
    $scope.rxPackageinfoName = '';
    $scope.rxPackageinfoQuota = '';
    $scope.rxPackageinfoUsedQuota = '';
    $scope.rxPackageinfoUsableQuota = '';
    $scope.rxTotalNumberDeliveries = 0;
    $scope.rxProvincialTotalNumberDeliveries = 0;
    $scope.rxQuotaCap = 0;

    $scope.enterpriseNameValidate = true;
    $scope.msisdnValidate = true;
    $scope.accountNameValidate = true;
    $scope.accountNameExist = false;
    $scope.passwordValidate = true;
    $scope.rePasswordValidate = true;
    $scope.descValidate =true;
    $scope.businessLicenseVali ='true';
    $scope.passwordValidateDesc = '';
    $scope.queryProvinceAndCity($scope);
    $scope.id = JSON.parse($.cookie("enterpriseID"));
      //查询业务开关
      $scope.querySyncServiceRuleList($scope);
    $scope.queryEnterpriseDetails($scope);
    $scope.querySyncServiceRulePackage($scope);


  };

    $scope.querySyncServiceRuleList = function ($scope) {
        var req = {
            "enterpriseID": parseInt($scope.id)
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceRuleList",
            data: JSON.stringify(req),
            async:false,
            success: function (result) {
                $scope.serviceControl = result.serviceControl || {};
                $.removeCookie('reserved4', { path: '/'});
                $.cookie('reserved4', $scope.serviceControl.reserved4,{ path: '/'});
            }
        })
    }
    $scope.querySyncServiceRulePackage = function ($scope){
        var req = {
            "enterpriseID": parseInt($scope.id)
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    console.log(result)
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.subServerTypeList = result.syncServiceRuleList || [];
                        console.log("222222222222 = " + result.syncServiceRuleList);
                        if (result.syncServiceRuleList !=null && result.syncServiceRuleList.length > 0){
                            for(let i = 0;i<result.syncServiceRuleList.length;i++){
                                var o = result.syncServiceRuleList[i];
                                // if (o.servType == 2){
                                //     o.reservedsEcpmp.reserved4 = '{"productName":"正式10元套餐","quatoDesc":"本异网000条,网内111条,异网222条","quatoAll":0,"quatoCurr":111,"quatoDiff":222}';
                                // }
                                if (o.servType == 1 && (o.reservedsEcpmp.reserved4 != undefined && o.reservedsEcpmp.reserved4 != '')){
                                    let parse = JSON.parse(o.reservedsEcpmp.reserved4);
                                    $scope.mpPackageinfoName = parse.productName;
                                    $scope.mpPackageinfoQuota = parse.quatoDesc;
                                }
                                if (o.servType == 2 && (o.reservedsEcpmp.reserved4 != undefined && o.reservedsEcpmp.reserved4 != '')) {
                                    let parse = JSON.parse(o.reservedsEcpmp.reserved4);
                                    $scope.rxPackageinfoName = parse.productName;
                                    $scope.rxPackageinfoQuota = parse.quatoDesc;
                                }

                            }
                        }
                        $scope.queryHotlineSubscribeList();

                    } else {
                        $scope.subServerTypeList = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

  $scope.querySyncServiceRule = function ($scope) {
	    var serverTime = CommonUtils.getServerTime();
	    $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
	    //下拉框(投递方式)
	    $scope.subServTypeChoise = [];
	    var req = {
	      "enterpriseID": parseInt($scope.id)
	    };
	    RestClientUtil.ajaxRequest({
	      type: 'POST',
	      url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
	      data: JSON.stringify(req),
	      success: function (result) {
	        $rootScope.$apply(function () {
	        console.log(result)
	          var data = result.result;
	          if (data.resultCode == '**********') {
	            $scope.subServerTypeList = result.syncServiceRuleList || [];
	            var serverTypeMap = new Map();
	            // 业务设置页面是否显示名片异网投递配置
	            var showMpPlatformConfig = false;
	            // 业务设置页面是否显示热线异网投递配置
	            var showRxPlatformConfig = false;

	            // //查询业务开关
                // $scope.querySyncServiceRuleList($scope)

	            for (var i in $scope.subServerTypeList) {
	              var item = $scope.subServerTypeList[i];
	              //开通或暂停状态才能投递
	              if ((item.status == 1 || item.status == 3) && $scope.nowTime <= item.expiryTime && $scope.nowTime >= item.effectiveTime) {
	            	  if (item.servType == 1)
	                  {
	            		  serverTypeMap.set("mp", "23");
	            		  showMpPlatformConfig = true;
	                  }
	            	  else if (item.servType == 5)
	                  {
	            		  serverTypeMap.set("rxsf", "40");
	            		  showRxPlatformConfig = true;
	                  }
	            	  else if (item.servType == 2)
	            	  {
	            	      //中移政企额外判断开关reserve4
	            	      if($scope.reserved10 == "111"
                              &&　($scope.serviceControl ==null || $scope.serviceControl.reserved4 == null || $scope.serviceControl.reserved4.indexOf("2") == -1)){
	            	          continue;
                          }

	            		  serverTypeMap.set("rx", "30,49")
	            	  }
	              }
	            }
	            
	            $.cookie("showMpPlatformConfig", showMpPlatformConfig, {path:'/'});
	            $.cookie("showRxPlatformConfig", showRxPlatformConfig, {path:'/'});
	            
	            // if (serverTypeMap.size < 3)
	            // {
	            	var serverTypeString = "[22, 24, 7";
	            	if (serverTypeMap.get("mp")
                        && ($scope.reserved10 !== "112" || ($scope.reserved10 === "112"&&$scope.ydyMp)))
	                {
	            		serverTypeString = serverTypeString + "," + serverTypeMap.get("mp");
	                }
	            	if (serverTypeMap.get("rxsf"))
	                {
	            		serverTypeString = serverTypeString + "," + serverTypeMap.get("rxsf");
	                }
	            	if (serverTypeMap.get("rx")
                        && ($scope.reserved10 !== "112" || ($scope.reserved10 === "112"&&$scope.ydyRx)))
	                {
	            		serverTypeString = serverTypeString + "," + serverTypeMap.get("rx");
	                }
	            	serverTypeString = serverTypeString + "]";

                      if($scope.reserved10 === "111"){
                          serverTypeString = serverTypeString
                              .replaceAll("40","76")
                              .replaceAll(",30","")
                              .replaceAll(",49","");
                      }
	            	$scope.proSupServerType = serverTypeString;
	            	console.log(serverTypeString);


	            	$.cookie("proSupServerType",serverTypeString,{path:'/'});
	            // }
	          } else {
	            $scope.subServerTypeList = [];
	            $scope.tip = data.resultCode;
	            $('#myModal').modal();
	          }
	        })
	      },
	      error: function () {
	        $rootScope.$apply(function () {
	          $scope.tip = '**********';
	          $('#myModal').modal();
	        })
	      }
	    });
	  }

    $scope.reserved10 = null;
  /*查询企业详情 */
  $scope.queryEnterpriseDetails = function ($scope) {
    var req = {};
    req.id = $scope.id;
    var pageParameter = {};
    pageParameter.pageNum = 1;
    pageParameter.pageSize = 1000;
    pageParameter.isReturnTotal = 1;
    req.pageParameter = angular.copy(pageParameter);
    /*查询企业列表*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.enterpriseInfo = data.enterprise;
            $scope.reserved10 = $scope.enterpriseInfo.reservedsEcpmp.reserved10;
              if($scope.enterpriseType === 5 && $scope.reserved10 === "111" && $scope.rxQuotaCap.toString().length >= 15){
                  $scope.rxPackageinfoUsableQuota = '不限量';
              }else{
                  $scope.rxPackageinfoUsableQuota = ($scope.rxQuotaCap - $scope.rxPackageinfoUsedQuota) >0 ? ($scope.rxQuotaCap - $scope.rxPackageinfoUsedQuota) : 0;
              }
              if ($scope.rxPackageinfoName == ''){
                  $scope.rxPackageinfoUsedQuota = '';
                  $scope.rxPackageinfoUsableQuota = '';
              }

            //移动云订购关系
              $.removeCookie('reserved10', { path: '/'});

              $.cookie('reserved10', $scope.reserved10,{ path: '/'});

              if($scope.reserved10 === "112"){
                $scope.queryOrderList()
            }else {
                if ($scope.isSuperManager)
                {
                    $.removeCookie('proSupServerType', { path: '/'});
                    $scope.querySyncServiceRule($scope);
                }
            }
              $scope.setEnterpriseDetails($scope);

          } else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
          }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };
    // 获取当前时间YYYYMMDDHHMMSS
    $scope.getCurrentDate = function () {
        var today = new Date();
        var year = today.getFullYear()+'';
        var month = today.getMonth() + 1;
        month = month < 10 ? '0'+month : month;
        var day = today.getDate() < 10 ? '0'+today.getDate() : today.getDate();
        var hours = today.getHours() < 10 ? '0'+today.getHours() : today.getHours();
        var mins = today.getMinutes() < 10 ? '0'+today.getMinutes() : today.getMinutes();
        var secs = today.getSeconds() < 10 ? '0'+today.getSeconds() : today.getSeconds();

        return year + month + day + hours + mins + secs;
    }


    // 查询热线订购关系列表接口
    $scope.queryHotlineSubscribeList = function () {
        var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID": parseInt($scope.id),
                "servType": 2,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            async: false,
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '**********') {
                        $scope.subscribeList = data.subscribeInfoList || [];
                        if ($scope.subscribeList.length > 0) {
                            $scope.rxQuotaCap = $scope.subscribeList[0].amount;
                            for(let i = 0;i<$scope.subscribeList.length;i++){
                                var o = $scope.subscribeList[i];
                                $scope.rxTotalNumberDeliveries = $scope.rxTotalNumberDeliveries + o.actualUseAmount;
                            }
                        }
                        $scope.queryHotlineProvincialSubscribeList();
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }

    // 查询热线彩印省份版订购关系列表接口
    $scope.queryHotlineProvincialSubscribeList = function () {
        var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID": parseInt($scope.id),
                "servType": 5,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            async: false,
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '**********') {
                        $scope.subscribeList = data.subscribeInfoList || [];
                        if ($scope.subscribeList.length > 0) {
                            for(let i = 0;i<$scope.subscribeList.length;i++){
                                var o = $scope.subscribeList[i];
                                $scope.rxProvincialTotalNumberDeliveries = $scope.rxProvincialTotalNumberDeliveries + o.actualUseAmount;
                            }
                        }

                        // 已用配额取投递数总和
                        $scope.rxPackageinfoUsedQuota = ($scope.rxProvincialTotalNumberDeliveries + $scope.rxTotalNumberDeliveries);
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }


    $scope.queryOrderList = function () {
        var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID":  $scope.id,
                // "servType": 1,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                if (data.result.resultCode == '**********') {
                    $scope.orderListData = data.subscribeInfoList || [].concat();

                    $scope.orderData = {"1_4": {},"1_5":{},"1_6":{},"2_4":{},"2_5":{},"2_6":{}}
                    for(let i = 0;i<$scope.orderListData.length;i++){
                        var o = $scope.orderListData[i];
                        if(o.status == null || o.status == 1){
                            if($scope.orderData[o.servType + "_" + o.productType]){
                                $scope.orderData[o.servType + "_" + o.productType][o.productID] =o.reservedsEcpmp.reserved8||1;
                            }
                        }
                    }
                    $scope.ydyMp = false;
                    for(let key in $scope.orderData["1_6"]){
                        if($scope.orderData["1_4"][$scope.orderData["1_6"][key]]){
                            $scope.ydyMp = true;
                            break;
                        }
                    }
                    $scope.ydyRx = false;
                    for(let key in $scope.orderData["2_6"]){
                        if($scope.orderData["2_4"][$scope.orderData["2_6"][key]]){
                            $scope.ydyRx = true;
                            break;
                        }
                    }
                    if ($scope.isSuperManager)
                    {
                        $.removeCookie('proSupServerType', { path: '/'});
                        $scope.querySyncServiceRule($scope);
                    }
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });

    }
  /* 非新增时企业详情 赋值*/
  $scope.setEnterpriseDetails = function ($scope) {
    $scope.passwordValidate = true;
    $scope.enterpriseNameTemp = $scope.enterpriseInfo.enterpriseName;
    $scope.custIDTemp = $scope.enterpriseInfo.custID;
    $scope.organizationIDTemp = $scope.enterpriseInfo.organizationID;
    $scope.msisdnTemp = $scope.enterpriseInfo.msisdn;
    $scope.cityIDTemp = $scope.enterpriseInfo.cityID;
    $scope.enterpriseDescTemp = $scope.enterpriseInfo.enterpriseDesc;
    $scope.provinceID = $scope.enterpriseInfo.provinceID;
    $scope.provinceIDTemp = $scope.enterpriseInfo.provinceID;
    $scope.businessLicenseURL = $scope.enterpriseInfo.businessLicenseURL;
    $scope.businessLicenseURLTemp = $scope.enterpriseInfo.businessLicenseURL;
    $scope.accountInfo = $scope.enterpriseInfo.accountInfo;
    $scope.fileUrl = CommonUtils.formatPic($scope.enterpriseInfo.businessLicenseURL).review;
    $scope.urlList = [$scope.fileUrl];
    $scope.countyIDTemp = $scope.enterpriseInfo.countyID;
    if ($scope.countyIDTemp && $scope.countyIDTemp!=-1) {
          $scope.showCountyFlag = true;
    }
    if ($scope.cityList) {
      $scope.changeSelectedProvince($scope.enterpriseInfo.provinceID);
      $scope.provinceID = $scope.enterpriseInfo.provinceID;
      if($scope.subCityList){
		jQuery.each($scope.subCityList, function (i, e) {
	        if (e.cityID == $scope.enterpriseInfo.cityID) {
	        	$scope.selectedCity = e;
	        }
	      });
		$scope.selectedCityID = $scope.selectedCity.cityID;
	  }
    }
  };

  $scope.mapToList = function (map) {
    var result = [];
    jQuery.each(map, function (_, o) {
    	if(_=='000'){
    		return;
    	}
        if (o) {
            o.key =o[0].provinceID;
            result.push(o);
        }
    });
    return result;
  };
  /* 新增时修改省份 */
  $scope.changeSelectedProvince = function (selectedProvinceID) {
	  angular.forEach($scope.cityList, function (e, i) {
			if (e.key == selectedProvinceID) {
		        $scope.subCityList = angular.copy(e);
		      }
	    });
	    if (!selectedProvinceID || selectedProvinceID =='000') {
	      $scope.subCityList = null;
	      $scope.selectedCityID = null;
	    }else{
	    	if($scope.subCityList){
	    		$scope.selectedCity =$scope.subCityList[0];
	    		$scope.selectedCityID =$scope.selectedCity.cityID;
	    		$scope.selectedCityName =$scope.selectedCity.cityName;
	        	delete($scope.subCityList.key);
	    	}
	    }
	    if ($scope.subCityList) {
  	    	$scope.provinceID = selectedProvinceID;
    		jQuery.each($scope.subCityList, function (i, e) {
    	        if (e.cityID == $scope.enterpriseInfo.cityID) {
    	        	$scope.selectedCity = e;
    	        }
    	      });
    		$scope.selectedCityID = $scope.selectedCity.cityID;
	    }
	    //查看、修改、新增时控制区县显示和区县列表
        if ($scope.selectedCity) {
            if ($scope.countyIDTemp && $scope.countyIDTemp!=-1) {
                $scope.showCountyFlag = true;
            }
            var req = {
                cityID : $scope.selectedCity.cityID
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCountyList",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        if(result.result.resultCode == '**********'){
                            $scope.subCountyList = result.countyList;
                            if ($scope.countyIDTemp) {
                                jQuery.each($scope.subCountyList, function (i, e) {
                                    if (e.countyID == $scope.countyIDTemp) {
                                        $scope.selectedCounty = e;
                                    }
                                });
                            }
                        }else{
                            $scope.tip=result.result.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error:function(){
                    $rootScope.$apply(function(data){
                            $scope.tip="**********";
                            $('#myModal').modal();
                        }
                    )
                }
            })
        }
  }


  /* 验证手机号 */
  $scope.checkMsisdn = function (msisdn) {
    $scope.msisdnValidate = $scope.validate(msisdn, 11, /^[0-9]{11}$/);
  };

  /* 验证描述 */
  $scope.checkDesc = function (desc) {
    if(desc){
    	if (desc.length > 1024) {
    		$scope.descValidate = false;
          } else {
        	  $scope.descValidate =true;
          }
    }else{
    	$scope.descValidate = true;
    }
  };
  
  /*验证密码，通过后台调用checkPwdRule接口 */
  $scope.checkPassword = function (condition) {
	$scope.condition =condition;
    $scope.passwordValidate = true;
    $scope.passwordValidateDesc = "";
    var pwd = $scope.accountInfo.password;
    var rePwd = $scope.accountInfo.rePassword;
    if(!$scope.accountInfo.password){
    	$scope.checkRePassword(pwd, rePwd,$scope.condition);
    	return;
    }else{
    	var checkPwdRuleReq = {};
        checkPwdRuleReq.password = pwd;
        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
          data: JSON.stringify(checkPwdRuleReq),
          success: function (data) {
            $rootScope.$apply(function () {
              var result = data.result;
              if (result.resultCode != '**********') {
                $scope.passwordValidate = false;
                  var passwordRuleList = data.passwordRuleList;

                  if(result.resultCode == '**********'){
                	  $scope.passwordValidateDesc ='ENTERPRISE_PASSWORDDESC';
                  }else{
                	  if(!passwordRuleList){
                		  $scope.passwordValidateDesc = result.resultDesc;
                	  }else{
                		  for (var i = 0; i < passwordRuleList.length; i++) {
                            	// $scope.passwordValidateDesc  = $scope.passwordValidateDesc + passwordRuleList[i].ruleName;
                              $scope.passwordValidateDesc = passwordRuleList[i].ruleName;

                                // $scope.passwordValidateDesc  = $scope.passwordValidateDesc + ";";
                              }
                		  $scope.passwordValidateDesc=$scope.passwordValidateDesc.substring(0, $scope.passwordValidateDesc.length - 1);
                	  }
                  }
              } else {
            	if(!!rePwd){
            		$scope.checkRePassword(pwd, rePwd,$scope.condition);
            	}
              }
            })
          },
          error:function(){
              $rootScope.$apply(function(){
                  $scope.tip='**********';
                  $('#myModal').modal();
                  }
              )
          }
        });
    }
  };

  /*验证确认密码，与密码相同 */
  $scope.checkRePassword = function (pwd, rePwd,condition) {
	$scope.condition =condition;
    $scope.rePasswordValidate = true;
    if (!rePwd) {
    	if(!pwd){
    		if($scope.condition =='save'){
        	    $scope.update();
        	}else{
        		return;
        	}
    	}else{
    		$scope.rePasswordValidate = false;
    	}
    }else{
        if (pwd != rePwd) {
          $scope.rePasswordValidate = false;
        }else{
        	if($scope.condition =='save'){
        	    $scope.update();
        	}
        }
    }
  };

  /*保存前验证与赋值 */
  $scope.beforeSave = function () {
	$scope.condition ='save';
    $scope.enterpriseInfo.enterpriseType = $scope.enterpriseType;
    $scope.accountInfo.msisdn = $scope.enterpriseInfo.msisdn;//创建用户的手机号为创建企业的联系人手机号
    //为创建企业的省份赋值
    if ($scope.provinceID) {
    	$scope.enterpriseInfo.provinceID = $scope.provinceID;
      if($scope.selectedCity && $scope.provinceID !='000'){
    	  $scope.enterpriseInfo.cityID = $scope.selectedCity.cityID;
      }
      //为创建企业的城市赋值
      if ($scope.selectedCityID) {
    	  $scope.enterpriseInfo.cityID = $scope.selectedCityID;
      }
    }
    
    //保存前再校验一遍密码规则
    $scope.checkPassword($scope.condition);
  };

  /*更新*/
  $scope.update = function () {
  if ($scope.enterpriseNameExist == true || $scope.accountNameExist == true ||
        $scope.passwordValidate == false || $scope.rePasswordValidate == false) {
			return;
    }
    var updateEnterpriseReq = {
    		"enterprise":{
    			"accountInfo":{
    				"msisdn":$scope.enterpriseInfo.msisdn,
    				"password":$scope.accountInfo.password
    			},
    			"id":$scope.enterpriseInfo.id,
    			"enterpriseName":$scope.enterpriseInfo.enterpriseName,
    			"enterpriseType":$scope.enterpriseType,
    			"custID":$scope.enterpriseInfo.custID,
    			"organizationID":$scope.enterpriseInfo.organizationID,
    			"enterpriseDesc":$scope.enterpriseInfo.enterpriseDesc,
    			"msisdn":$scope.enterpriseInfo.msisdn,
    			"cityID":$scope.enterpriseInfo.cityID,
    			"provinceID":$scope.enterpriseInfo.provinceID,
    			"businessLicenseURL":$scope.businessLicenseURL,
    		}
    };
    if($scope.enterpriseNameTemp ==updateEnterpriseReq.enterprise.enterpriseName){
    	delete(updateEnterpriseReq.enterprise.enterpriseName);
    }
    if($scope.custIDTemp ==updateEnterpriseReq.enterprise.custID){
    	delete(updateEnterpriseReq.enterprise.custID);
    }
    if($scope.organizationIDTemp ==updateEnterpriseReq.enterprise.organizationID){
    	delete(updateEnterpriseReq.enterprise.organizationID);
    }
    if($scope.enterpriseDescTemp == updateEnterpriseReq.enterprise.enterpriseDesc){
    	delete(updateEnterpriseReq.enterprise.enterpriseDesc);
    }
    if($scope.msisdnTemp ==updateEnterpriseReq.enterprise.msisdn){
    	delete(updateEnterpriseReq.enterprise.msisdn);
    	delete(updateEnterpriseReq.enterprise.accountInfo.msisdn);
    }
    if($scope.provinceIDTemp ==updateEnterpriseReq.enterprise.provinceID){
    	delete(updateEnterpriseReq.enterprise.provinceID);
    }
    if($scope.cityIDTemp ==updateEnterpriseReq.enterprise.cityID){
    	delete(updateEnterpriseReq.enterprise.cityID);
    }
    if($scope.businessLicenseURLTemp ==updateEnterpriseReq.enterprise.businessLicenseURL){
    	delete(updateEnterpriseReq.enterprise.businessLicenseURL);
    }
    updateEnterpriseReq.enterprise.operatorID = $.cookie("accountID");
    if(!updateEnterpriseReq.enterprise.accountInfo.password){
    	delete(updateEnterpriseReq.enterprise.accountInfo.password);
    }
    if(!updateEnterpriseReq.enterprise.accountInfo.msisdn){
    	delete(updateEnterpriseReq.enterprise.accountInfo.msisdn);
    }
    if(JSON.stringify(updateEnterpriseReq.enterprise.accountInfo) == "{}"){
    	delete(updateEnterpriseReq.enterprise.accountInfo);
    }else{
    	updateEnterpriseReq.enterprise.accountInfo.accountID =  $scope.accountInfo.accountID;
    	updateEnterpriseReq.enterprise.accountInfo.enterpriseID =  $scope.accountInfo.enterpriseID;
    	updateEnterpriseReq.enterprise.accountInfo.operatorID = $.cookie("accountID");
    	updateEnterpriseReq.enterprise.accountInfo.accountType = $scope.accountInfo.accountType;
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/updateEnterprise",
      data: JSON.stringify(updateEnterpriseReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
        	  if($scope.isSuperManager){
        		  location.href = '../enterpriseList/enterpriseList.html';
        	  }else{
        		  $scope.tip = 'COMMON_SAVESUCCESS';
                  $('#myModal').modal();
        	  }
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };
  
  
  /*点击取消返回列表页面*/
  $scope.cancelToEnterpriseList = function () {
    	$scope.tip='COMMON_RETURNTOLIST';
        $('#ensureToList').modal();
  };
  
  /*确认返回列表页面*/
  $scope.ensureToList = function () {
	    location.href = '../enterpriseList/enterpriseList.html';
  };
  /*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
    	if(reg){
    		if (!reg.test(context)) {
	          return false;
	        } else {
	          return true;
	        }
    	}else{
    		return true;
    	}
      }
    }
  };
});




