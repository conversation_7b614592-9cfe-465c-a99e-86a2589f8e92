<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.InterfaceControlMapper">

    <select id="queryInterfaceControl" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.InterfaceControlQueryWrapper">
        SELECT
            if(es.parentEnterpriseID is null,es.id,es.parentEnterpriseID) enterpriseID,
            if(es2.enterpriseName is null,es.enterpriseName,es2.enterpriseName) enterpriseName,
            if(es.parentEnterpriseID is null,"-",es.id) subEnterpriseID,
            if(es2.enterpriseName is null,"-",es.enterpriseName) subEnterpriseName,
            es.enterpriseType,
            es.reserved10,
            ic1.`status` MPMemberStatus,
            ic2.`status` MPContentStatus,
            ic3.`status` RXContentStatus,
            ic4.`status` RXDeliveryStatus
        FROM
            ecpm_t_enterprise_simple es
                LEFT JOIN ecpm_t_enterprise_simple es2 ON es.parentEnterpriseID = es2.ID
                LEFT JOIN ecpm_t_interface_control ic1 ON ic1.enterpriseId = es.ID and ic1.interfaceType = 1
                LEFT JOIN ecpm_t_interface_control ic2 ON ic2.enterpriseId = es.ID and ic2.interfaceType = 2
                LEFT JOIN ecpm_t_interface_control ic3 ON ic3.enterpriseId = es.ID and ic3.interfaceType = 3
                LEFT JOIN ecpm_t_interface_control ic4 ON ic4.enterpriseId = es.ID and ic4.interfaceType = 4
                LEFT JOIN ecpm_t_thirdparty_access ta ON ta.platformId = es.ID
            where (es.status = 1) and es.enterpriseType in (2,3,5)
              and ta.platformId is not null
            <if test="enterpriseID != null and enterpriseID != ''">
                and es.id = #{enterpriseID}
            </if>
            <if test="enterpriseName != null and enterpriseName != ''">
                and es.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>
            <if test="parentEnterpriseID != null and parentEnterpriseID != ''">
                and es.parentEnterpriseID = #{parentEnterpriseID}
            </if>
            <if test="parentEnterpriseName != null and parentEnterpriseName != ''">
                and es2.enterpriseName =  like concat("%", #{parentEnterpriseName}, "%")
            </if>
            <if test="enterpriseType != null and enterpriseType != '' and  enterpriseType != 5 and  enterpriseType != 15 and  enterpriseType != 25 and  enterpriseType != 35">
                and es.enterpriseType = #{enterpriseType}
            </if>
            <if test='enterpriseType == null or enterpriseType == "" or enterpriseType != "5"'>
                and es.id not in (
                    select id from ecpm_t_enterprise_simple where enterpriseType = 5 and ((es.reserved10 != '111' and es.`reserved10` != '112') OR es.`reserved10` IS NULL)
                )
            </if>
            <if test='enterpriseType != null and enterpriseType != "" and enterpriseType == "15"'>
                and es.enterpriseType = 5 and es.reserved10 = '111'
            </if>
            <if test='enterpriseType != null and enterpriseType != "" and enterpriseType == "25"'>
                and es.enterpriseType = 5 and es.reserved10 = '112'
            </if>
            <if test='enterpriseType != null and enterpriseType != "" and enterpriseType == "35"'>
                and es.enterpriseType = 5 and es.reserved10 = '113'
            </if>
        ORDER BY es.createDate DESC
        limit #{pageNum},#{pageSize}
    </select>

    <select id="queryInterfaceControlCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
        ecpm_t_enterprise_simple es
        LEFT JOIN ecpm_t_enterprise_simple es2 ON es.parentEnterpriseID = es2.ID
        LEFT JOIN ecpm_t_interface_control ic1 ON ic1.enterpriseId = es.ID and ic1.interfaceType = 1
        LEFT JOIN ecpm_t_interface_control ic2 ON ic2.enterpriseId = es.ID and ic2.interfaceType = 2
        LEFT JOIN ecpm_t_interface_control ic3 ON ic3.enterpriseId = es.ID and ic3.interfaceType = 3
        LEFT JOIN ecpm_t_interface_control ic4 ON ic4.enterpriseId = es.ID and ic4.interfaceType = 4
        LEFT JOIN ecpm_t_thirdparty_access ta ON ta.platformId = es.ID
        where (es.status = 1) and es.enterpriseType in (2,3,5)
        and ta.platformId is not null
        <if test="enterpriseID != null and enterpriseID != ''">
            and es.id = #{enterpriseID}
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            and es.enterpriseName like concat("%", #{enterpriseName}, "%")
        </if>
        <if test="parentEnterpriseID != null and parentEnterpriseID != ''">
            and es.parentEnterpriseID = #{parentEnterpriseID}
        </if>
        <if test="parentEnterpriseName != null and parentEnterpriseName != ''">
            and es2.enterpriseName =  like concat("%", #{parentEnterpriseName}, "%")
        </if>
        <if test="enterpriseType != null and enterpriseType != '' and  enterpriseType != 5 and  enterpriseType != 15 and  enterpriseType != 25 and  enterpriseType != 35">
            and es.enterpriseType = #{enterpriseType}
        </if>
        <if test='enterpriseType == null or enterpriseType == ""'>
            and es.id not in (
                select id from ecpm_t_enterprise_simple where enterpriseType = 5 and ((es.reserved10 != '111' and es.`reserved10` != '112') OR es.`reserved10` IS NULL)
            )
        </if>
        <if test='enterpriseType != null and enterpriseType != "" and enterpriseType == "15"'>
            and es.enterpriseType = 5 and es.reserved10 = '111'
        </if>
        <if test='enterpriseType != null and enterpriseType != "" and enterpriseType == "25"'>
            and es.enterpriseType = 5 and es.reserved10 = '112'
        </if>
        <if test='enterpriseType != null and enterpriseType != "" and enterpriseType == "35"'>
            and es.enterpriseType = 5 and es.reserved10 = '113'
        </if>

    </select>


    <select id="queryProvinceInterfaceControl" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.InterfaceControlQueryWrapper">
        SELECT
            ic.provinceName enterpriseName,
            '5' enterpriseType,
            ic.`status` MPMemberStatus,
            ic.provinceId provinceId,
            ic2.`status` MPContentStatus,
            ic3.`status` RXContentStatus,
            ic4.`status` RXDeliveryStatus
        FROM
            ecpm_t_interface_control ic
                LEFT JOIN ecpm_t_interface_control ic2 ON ic2.provinceId = ic.provinceId and ic2.interfaceType = 2
                LEFT JOIN ecpm_t_interface_control ic3 ON ic3.provinceId = ic.provinceId and ic3.interfaceType = 3
                LEFT JOIN ecpm_t_interface_control ic4 ON ic4.provinceId = ic.provinceId and ic4.interfaceType = 4
        where ic.interfaceType = 1 and ic.provinceId is not null
        ORDER BY ic.createTime DESC
        limit #{pageNum},#{pageSize}
    </select>

    <select id="queryProvinceInterfaceControlCount" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            ecpm_t_interface_control ic
                LEFT JOIN ecpm_t_interface_control ic2 ON ic2.provinceId = ic.provinceId and ic2.interfaceType = 2
                LEFT JOIN ecpm_t_interface_control ic3 ON ic3.provinceId = ic.provinceId and ic3.interfaceType = 3
                LEFT JOIN ecpm_t_interface_control ic4 ON ic4.provinceId = ic.provinceId and ic4.interfaceType = 4
        where ic.interfaceType = 1 and ic.provinceId is not null
    </select>

    <select id="getInterfaceControl" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.InterfaceControlWrapper">
        SELECT * FROM ecpm_t_interface_control
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseId != null and enterpriseId != ''">
                and enterpriseId like #{enterpriseId}
            </if>
            <if test="type != null and type != ''">
                and type like #{type}
            </if>
            <if test="interfaceType != null and interfaceType != ''">
                and interfaceType like #{interfaceType}
            </if>
            <if test="provinceId != null and provinceId != ''">
                and provinceId = #{provinceId}
            </if>
        </trim>
    </select>

    <insert id="insertControl" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.InterfaceControlWrapper">
        INSERT INTO ecpm_t_interface_control
            (enterpriseId, type, status, interfaceType, createTime, updateTime)
        VALUES (#{enterpriseId}, #{type}, #{status}, #{interfaceType}, now(), now());
    </insert>
    <update id="updateControlStatus" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.InterfaceControlWrapper">
        UPDATE ecpm_t_interface_control
        SET status = #{status}, updateTime = now()
        WHERE enterpriseId = #{enterpriseId}
          AND type = #{type}
          AND interfaceType = #{interfaceType};
    </update>

    <update id="updateProvinceControlStatus" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.InterfaceControlWrapper">
        UPDATE ecpm_t_interface_control
        SET status = #{status}, updateTime = now()
        WHERE provinceId = #{provinceId}
          AND type = #{type}
          AND interfaceType = #{interfaceType}
    </update>
</mapper>