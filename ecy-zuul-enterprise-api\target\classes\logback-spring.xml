<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <property name="APP_Name" value="ecy-zuul-enterprise-api" />
	<property name="LOG_HOME" value="/data/logs" />
    <contextName>${APP_Name}</contextName>

	<jmxConfigurator/>
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
           <pattern>
               ${CONSOLE_LOG_PATTERN}
           </pattern>
        </encoder>
    </appender>

    <appender name="FILEERROR"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_Name}/app_${HOSTNAME}_error_s.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${APP_Name}/${HOSTNAME}_%d{yyyy-MM-dd__HH}_error.%i.log</FileNamePattern>
            <maxFileSize>500MB</maxFileSize>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <includeMdcKeyName>op</includeMdcKeyName>
            <includeMdcKeyName>X-B3-TraceId</includeMdcKeyName>
            <includeMdcKeyName>rst</includeMdcKeyName>
            <customFields>
                {
                "sv":"${APP_Name}",
                "hs":"${HOSTNAME}"
                }
            </customFields>
            <fieldNames>
                <message>msg</message>
            </fieldNames>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="FILEWARN"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_Name}/app_${HOSTNAME}_warn_s.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
               <!--日志文件输出的文件名-->
               <FileNamePattern>${LOG_HOME}/${APP_Name}/${HOSTNAME}_%d{yyyy-MM-dd__HH}_warn.%i.log</FileNamePattern>
               <maxFileSize>500MB</maxFileSize>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
  			<includeMdcKeyName>op</includeMdcKeyName>
            <includeMdcKeyName>X-B3-TraceId</includeMdcKeyName>
            <includeMdcKeyName>rst</includeMdcKeyName>
            <customFields>
                {
                "sv":"${APP_Name}",
                "hs":"${HOSTNAME}"
                }
            </customFields>
            <fieldNames>
                <message>msg</message>
            </fieldNames>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>warn</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="FILEINFO"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_Name}/app_${HOSTNAME}_info_s.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${APP_Name}/${HOSTNAME}_%d{yyyy-MM-dd__HH}_info.%i.log</FileNamePattern>
            <maxFileSize>500MB</maxFileSize>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <includeMdcKeyName>op</includeMdcKeyName>
            <includeMdcKeyName>X-B3-TraceId</includeMdcKeyName>
            <includeMdcKeyName>rst</includeMdcKeyName>
            <customFields>
                {
                "sv":"${APP_Name}",
                "hs":"${HOSTNAME}"
                }
            </customFields>
            <fieldNames>
                <message>msg</message>
            </fieldNames>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>info</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="FILEDEBUG"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_Name}/app_${HOSTNAME}_debug_s.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${APP_Name}/${HOSTNAME}_%d{yyyy-MM-dd__HH}_debug.%i.log</FileNamePattern>
            <maxFileSize>500MB</maxFileSize>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <includeMdcKeyName>op</includeMdcKeyName>
            <includeMdcKeyName>X-B3-TraceId</includeMdcKeyName>
            <includeMdcKeyName>rst</includeMdcKeyName>
            <customFields>
                {
                "sv":"${APP_Name}",
                "hs":"${HOSTNAME}"
                }
            </customFields>
            <fieldNames>
                <message>msg</message>
            </fieldNames>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>debug</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="FILETRACE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_Name}/app_${HOSTNAME}_trace_s.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${APP_Name}/${HOSTNAME}_%d{yyyy-MM-dd__HH}_trace.%i.log</FileNamePattern>
            <maxFileSize>500MB</maxFileSize>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <includeMdcKeyName>op</includeMdcKeyName>
            <includeMdcKeyName>X-B3-TraceId</includeMdcKeyName>
            <includeMdcKeyName>rst</includeMdcKeyName>
            <customFields>
                {
                "sv":"${APP_Name}",
                "hs":"${HOSTNAME}"
                }
            </customFields>
            <fieldNames>
                <message>msg</message>
            </fieldNames>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>trace</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <root level="INFO">
        <appender-ref ref="STDOUT" />



        <appender-ref ref="FILEERROR" />
        <appender-ref ref="FILEWARN" />
        <appender-ref ref="FILEINFO" />
        <appender-ref ref="FILEDEBUG" />
        <appender-ref ref="FILETRACE" />
    </root>

</configuration>