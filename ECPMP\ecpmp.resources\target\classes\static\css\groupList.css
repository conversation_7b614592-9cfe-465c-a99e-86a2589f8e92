@charset "UTF-8";
[ng-cloak] {
  display: none !important;
}

 .modal-footer{
     text-align: center;
 }
 
 .form-horizontal .control-label {
	padding-top: 10px;
	/* padding-bottom: 15px; */
	margin-bottom: 0;
	text-align: right;
	white-space:nowrap;
}
 .form-horizontal .control-input {
	padding-top: 8px;
}
 .form-horizontal .control-btn {
	padding-top: 8px;
}
.cooperation-nav{
    margin-bottom: 15px;
    margin-left: 20px;
}

.cooperation-manage .form-inline {
   
    padding: 20px 0px 20px 10px;
    overflow: hidden;
}

.form-group label{
    height: 34px;
    margin-bottom: 0
}
.form-group .control-label icon {
	color: #ff254c;
	vertical-align: sub;
	margin-right: 2px;
}
.cooperation-manage .form-group select{
    margin-left: 20px;
}

.cooperation-manage .coorPeration-table th:first-child, .cooperation-manage .coorPeration-table td:first-child{
  padding-left: 30px!important;
}

.handle ul li icon.addNumber-icon {
    background-position: -55px 0px;
}

.handle ul li icon.importNumber-icon {
    background-position: -91px 0px;
}

.tabtn{
	display: inline-block;
	border-radius: 0.5rem;
	background-color: #FFFFFF;
	font-size: 16px;
	height: 44px;
	line-height: 44px;
	color: #c3c3c3;
	padding: 0 40px;
}
.cur-tabtn{
	color: #705de1;
	box-shadow: 0 1px 6px #e5e5e5;
}

.dialog-690{
    width: 690px;
}
.dialog-800{
    width: 800px;
}
.dialog-900{
    width: 900px;
}
.dialog-1000{
    width: 1000px;
}
option{
  overflow-x:hidden;
  width:341px;
  text-overflow: ellipsis
}
