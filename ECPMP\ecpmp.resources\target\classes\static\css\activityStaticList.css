.cooperation-manage .form-inline {
	margin: 0 20px;
	background: #fff;
	border-radius: 4px;
	padding: 20px;
}

.cooperation-manage .form-group {
	margin-right: 20px;
	padding-top: 10px;
	padding-bottom: 20px;
}
.form-control:focus {
	border-color: #7360e1;
}
.cooperation-manage .add-table .add-btn .export-icon {
	display: inline-block;
	width: 20px;
	height: 20px;
	background: url(../assets/images/btnIcons18.png) no-repeat;
	vertical-align: middle;
	background-position: -107px 0px;
}
.cooperation-manage .coorPeration-table th, td{
	padding:12px 0px 12px 0px !important; 
}
.handle {
	overflow: hidden;
}
body .handle ul li icon {
	width: 17px;
	height: 20px;
	display: inline-block;
	background: url(../assets/images/tableEditIcons18.png) no-repeat;
	vertical-align: bottom;
}

body .handle ul li icon.query-icon {
	background-position: -73px 0px;
}

.handle ul li icon.edit-icon {
	background-position: 0 0;
}

.handle ul li icon.delete-icon {
	background-position: -18px 0;
}

.handle ul li icon.set-icon {
	background-position: -38px 0;
}
.form-horizontal .control-label {
	padding-top: 22px;
	margin-bottom: 0;
	text-align: right;
}
.cooperation-manage .form-horizontal {
	margin: 0 20px;
	background: #fff;
	border-radius: 4px;
}
.cond-div {
	padding-top: 15px;
}