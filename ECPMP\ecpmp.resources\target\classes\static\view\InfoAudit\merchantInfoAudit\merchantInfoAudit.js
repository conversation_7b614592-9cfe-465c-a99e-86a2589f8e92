
var app = angular.module("cyApp",["util.ajax","page","top.menu","angularI18n","service.common"])

app.controller('infoAudit', function ($scope,$rootScope,$filter,$location,RestClientUtil,CommonUtils) {
	
	$scope.checkEmpty=function(){
		if(!$scope.checkDesc){
			if($scope.auditStatus==3){
				$("input[name='checkDesc']").attr('style','border-color:red');
			};
			$scope.isEmpty = true ;
			$scope.isFirstTime =false;
		}else{
			$("input[name='checkDesc']").removeAttr("style");
			$scope.isEmpty = false ;
			$scope.isFirstTime =false;
		}
      };  
	//驳回按钮
	  $scope.cz = function(item){
		  $("input[name='checkDesc']").removeAttr("style");
		  $scope.isFirstTime =true;
		  $scope.isEmpty = true;
		  $scope.auditStatus=3;
		  $scope.checkDesc="";
          $('#impoMebrPop').modal();
          $scope.id=item.id; 
          
      };  
      //通过按钮
      $scope.czs = function(item){ 
    	  $("input[name='checkDesc']").removeAttr("style");
    	  $scope.isEmpty = true;
      	  $scope.isFirstTime =true;
    	  $scope.auditStatus=2;
    	  $scope.checkDesc="";
          $('#impoMebrPop').modal();
          $scope.id=item.id;
      };  
      
      
      $scope.picturePop= function(item,con){ 
    	  $scope.pictureUrl = "";
    	  $("#picUrlListPop").modal();
    	 if(con == "businessLicense"){
    		 $scope.pictureUrl = CommonUtils.formatPic(item.businessLicenseURL).review;
    	 }else if(con=="idCardPositive"){
    		 $scope.pictureUrl = CommonUtils.formatPic(item.idCardPositiveURL).review;
    	 }else{
    		 $scope.pictureUrl = CommonUtils.formatPic(item.idCardOppositeURL).review;
    	 }
      };
      
      
    //初始化参数
    $scope.init = function () {
    	$scope.isEmpty = false ;
    	$scope.operatorID = $.cookie('accountID');
    	//初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        
        $scope.statusMap={
                "1":"待审核",
                "2":"审核通过",
                "3":"驳回"
            };
        $scope.statusList = [
        					 {"name":"待审核","value":1},
        					 {"name":"审核通过","value":2},
        					 {"name":"驳回","value":3}
        					];
        
        
       
        $scope.queryMerchantInfoList();
    };
    
  
    //获取queryEnterpriseList查询商户接口的数据
    $scope.queryMerchantInfoList = function (condition) {
    	 $scope.style={"opacity":"0.5", "cursor":"default"};
    	if(condition!='justPage'){
            var req = {
            		"enterpriseType":4,
                    "sortType":2,
                    "sortField":1,
                    "enterpriseName":$scope.merchantName,
                    "pageParameter": {
                        "pageNum": 1,
                        "pageSize":$scope.pageInfo[0].pageSize,
                        "isReturnTotal": "1"
                    }
            };
            if (!$scope.selectedStatus) {
            	delete(req.auditStatus);
            }
             else{
                req.auditStatus = $scope.selectedStatus
            }
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.pageParameter.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }

    
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                    	 $scope.MerchantInfoList = result.enterpriseList||[];
                         $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                         $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !==0 ?Math.ceil($scope.pageInfo[0].totalCount / parseInt($scope.pageInfo[0].pageSize)) :1;
                         angular.forEach($scope.MerchantInfoList, function (MerchantInfo) { 
                        	 if(!MerchantInfo.businessLicenseURL){
                        		 MerchantInfo.style1=$scope.style;
                        	 }else{
                        		 MerchantInfo.businessLicenseName =  CommonUtils.formatPic(MerchantInfo.businessLicenseURL).picName;
                        	 };
                        	 if(!MerchantInfo.idCardPositiveURL){
                        		 MerchantInfo.style2=$scope.style;
                             }else{
                            	 MerchantInfo.idCardPositiveName =  CommonUtils.formatPic(MerchantInfo.idCardPositiveURL).picName;
                        	 };
                        	 if(!MerchantInfo.idCardOppositeURL){
                        		 MerchantInfo.style3=$scope.style;
                             }else{
                            	 MerchantInfo.idCardOppositeName =  CommonUtils.formatPic(MerchantInfo.idCardOppositeURL).picName;
                        	 };
                         });
                         
                    }else{
                        $scope.merchantListData=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    };
    
  //获取updateEnterprise审批接口的数据
    $scope.checkMerchantInfo = function () {
    	if(!$scope.checkDesc&&$scope.auditStatus==3){
    		return;
    	}
    	var checkReq = {};
    	checkReq.enterprise = {};
    	checkReq.enterprise.id = $scope.id;
    	checkReq.enterprise.auditDesc = $scope.checkDesc;
    	checkReq.enterprise.auditStatus = $scope.auditStatus;
    	checkReq.enterprise.enterpriseType = 4;
    	checkReq.enterprise.operatorID = $scope.operatorID;
    	RestClientUtil.ajaxRequest({
    	      type: 'POST',
    	      url: "/ecpmp/ecpmpServices/enterpriseManageService/updateEnterprise",
    	      data: JSON.stringify(checkReq),
    	      success: function (data) {
    	        $rootScope.$apply(function () {
    	          var result = data.result;
    	          if (result.resultCode == '**********') {
    	        	  window.location.href='merchantInfoAudit.html';
    	          }else {
    	              $scope.tip = result.resultCode;
                      $('#myModal').modal();
    	            }
    	        })
    	      },
    	      error:function(){
    	          $rootScope.$apply(function(){
    	              $scope.tip='1030120500';
                      $('#myModal').modal();
    	              }
    	          )
    	      }
    	    });
    }
})
