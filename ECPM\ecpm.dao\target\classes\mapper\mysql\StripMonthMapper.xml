<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.StripMonthMapper">
    <resultMap id="MonStripMemberWrapper"
               type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MonStripMemberWrapper">
        <result property="servType" column="servType" javaType="java.lang.Integer" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="memberID" column="ID"
                javaType="java.lang.Long" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="pxDeliveryStatus" column="pxDeliveryStatus" javaType="java.lang.Integer" />
        <result property="pxDiffDeliveryStatus" column="pxDiffDeliveryStatus" javaType="java.lang.Integer" />
        <result property="gdDeliveryStatus" column="gdDeliveryStatus"
                javaType="java.lang.Integer" />
        <result property="gdDiffDeliveryStatus" column="gdDiffDeliveryStatus"
                javaType="java.util.Date" />
        <result property="gcDeliveryStatus" column="gcDeliveryStatus" javaType="java.lang.Integer" />
    </resultMap>

    <resultMap id="MonquotaDeliveryStatWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MonquotaMemConWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="memberID" column="memberID" javaType="java.lang.Long" />
        <result property="ydpxDeliveryCount" column="ydpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltpxDeliveryCount" column="ltpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxpxDeliveryCount" column="dxpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="pxDeliveryStatus" column="pxDeliveryStatus" javaType="java.lang.Integer" />
        <result property="pxDiffDeliveryStatus" column="pxDiffDeliveryStatus" javaType="java.lang.Integer" />
        <result property="ydgdDeliveryCount" column="ydgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltgdDeliveryCount" column="ltgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxgdDeliveryCount" column="dxgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="gdDeliveryStatus" column="gdDeliveryStatus" javaType="java.lang.Integer" />
        <result property="gdDiffDeliveryStatus" column="gdDiffDeliveryStatus" javaType="java.lang.Integer" />
        <result property="ydgcDeliveryCount" column="ydgcDeliveryCount" javaType="java.lang.Integer" />
        <result property="gcDeliveryStatus" column="gcDeliveryStatus" javaType="java.lang.Integer" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="branchType" column="branchType" javaType="java.lang.String" />        
    </resultMap>

    <insert id="insertWrapper">
        insert into ecpm_t_monquota_delivery_stat
            (
            id,
            memberID,
            ydpxDeliveryCount,
            ltpxDeliveryCount,
            dxpxDeliveryCount,
            pxDeliveryStatus,
            pxDiffDeliveryStatus,
            ydgdDeliveryCount,
            ltgdDeliveryCount,
            dxgdDeliveryCount,
            gdDeliveryStatus,
            gdDiffDeliveryStatus,
            ydgcDeliveryCount,
            gcDeliveryStatus,
            createTime,
            updateTime,
            msisdn,
            enterpriseID,
            callDeliveryStatus,
            calledDeliveryStatus
            )
            values(
            #{id},
            #{memberID},
            #{ydpxDeliveryCount},
            #{ltpxDeliveryCount},
            #{dxpxDeliveryCount},
            #{pxDeliveryStatus},
            #{pxDiffDeliveryStatus},
            #{ydgdDeliveryCount},
            #{ltgdDeliveryCount},
            #{dxgdDeliveryCount},
            #{gdDeliveryStatus},
            #{gdDiffDeliveryStatus},
            #{ydgcDeliveryCount},
            #{gcDeliveryStatus},
            now(),
            now(),
            #{msisdn},
            #{enterpriseID},
            #{callDeliveryStatus},
            #{calledDeliveryStatus}
            )
    </insert>
    
    <select id="queryForDelete" resultMap="MonquotaDeliveryStatWrapper">
        SELECT 
        memberID,
		ydpxDeliveryCount,
		ltpxDeliveryCount,
		dxpxDeliveryCount,
		pxDeliveryStatus,
		pxDiffDeliveryStatus,
		ydgdDeliveryCount,
		ltgdDeliveryCount,
		dxgdDeliveryCount,
		gdDeliveryStatus,
		gdDiffDeliveryStatus,
		ydgcDeliveryCount,
		gcDeliveryStatus,
		callDeliveryStatus,
		calledDeliveryStatus,
		createTime,
		updateTime,
		msisdn
        FROM ecpm_t_monquota_delivery_stat a
		where a.enterpriseID = #{enterpriseID} 
		and a.msisdn = #{msisdn}
		and DATE_FORMAT(a.createTime, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m')
		order by a.createTime desc
		limit 0,1
    </select>
    
    <select id="queryByMemberID" resultMap="MonquotaDeliveryStatWrapper">
        SELECT 
        memberID,
		ydpxDeliveryCount,
		ltpxDeliveryCount,
		dxpxDeliveryCount,
		pxDeliveryStatus,
		pxDiffDeliveryStatus,
		ydgdDeliveryCount,
		ltgdDeliveryCount,
		dxgdDeliveryCount,
		gdDeliveryStatus,
		gdDiffDeliveryStatus,
		ydgcDeliveryCount,
		gcDeliveryStatus,
		callDeliveryStatus,
		calledDeliveryStatus,
		createTime,
		updateTime,
		msisdn
        FROM ecpm_t_monquota_delivery_stat a
		where a.memberID = #{memberID}
    </select>
    <delete id="deleteByMsisdn">
        delete from ecpm_t_monquota_delivery_stat where msisdn = #{msisdn};
    </delete>
</mapper>