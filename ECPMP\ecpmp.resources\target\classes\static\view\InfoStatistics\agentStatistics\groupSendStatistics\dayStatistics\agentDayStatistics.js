var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否超管
        $scope.isSuperManager = false;
        $scope.isAgent = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isAgent = (loginRoleType == 'agent');

        $scope.enterpriseID = "";

        if ($scope.isAgent) {
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
            $scope.enterpriseType ='3';
        }


        //默认业务类别为：不限
        $scope.serviceType = "";

        //默认业务形态为：不限
        $scope.subservType = "";

        $scope.enterpriseName = "";

        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            }
        ];

        //下拉框(业务形态)SUBSERVTYPE
        $scope.subservTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "屏显"
            },
            {
                id: 2,
                name: "挂机短信"
            },
            {
                id: 3,
                name: "挂机彩信"
            },
            {
                id: 4,
                name: "增彩"
            }

        ];

        //初始化搜索条件
        $scope.initSel = {
            startTime: "",
            endTime: "",
            search: false,
        };

        $scope.exportFile=function(){
            var req = {
              "param":{
                "enterpriseName":$scope.enterpriseName,
                "serviceType":'4',
                "startDate":$scope.initSel.startTime,
                "endDate":$scope.initSel.endTime,
                "areaDimension":3,
                "timeDimension":1,
                "enterpriseID":$scope.enterpriseID,
                "parentEnterpriseID":"",
                "cityID":"",
                "provinceID":"",
                "enterpriseType":2,
                "type":13,
                "token":$scope.token,
                "isExport":1
              },
              "url":"/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile",
              "method":"get"
            }
            CommonUtils.exportFile(req);
          }

        //$scope.queryEnterpriseStatInfo();
    }

    $scope.getServiceType = function (serviceType,subServType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 3) {
            return "广告彩印";
        }
        else if (serviceType == 4 && subServType == 16) {
            return "增彩";
        }
        else if (serviceType == 4 && subServType == 17) {
            return "短信";
        }
        else if (serviceType == 4 && subServType == 3) {
            return "屏显";
        }
        else if (serviceType == 4 && subServType == 8) {
            return "彩信";
        }
    };

    $scope.getSubServType = function (subServType) {
        if (subServType == 1) {
            return "主叫屏显";
        }
        else if (subServType == 2) {
            return "被叫屏显";
        }
        else if (subServType == 4) {
            return "挂机短信";
        }
        else if (subServType == 8) {
            return "挂机彩信";
        }
        else if (subServType == 3) {
            return "屏显";
        }
        else if (subServType == 16) {
            return "挂机增彩";
        }
    };

    $scope.getChargeType = function (chargeType) {
        if (chargeType == 1) {
            return "按条计费";
        }
        else if (chargeType == 2) {
            return "包月计费";
        }
    };

    $scope.getTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        return year + "-" + month + "-" + day;
    }

    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true
    });

    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;

        if (startTime !== '')
        {
            $scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
        }
        else
        {
            $scope.initSel.startTime = "";
        }

        if (endTime !== '')
        {
            $scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
        }
        else
        {
            $scope.initSel.endTime = "";
        }

        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }
    }

    //后续post的函数
    $scope.queryEnterpriseStatInfo = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "areaDimension": 3,
                "timeDimension": 1,
                "enterpriseType": 2,
                "provinceID": $scope.provinceID,
                "enterpriseName": $scope.enterpriseName || '',
                "enterpriseID": $scope.enterpriseID || '',
                "serviceType": '4',
                "startDate": $scope.initSel.startTime || '',
                "endDate": $scope.initSel.endTime || '',
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };

            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile?enterpriseName=" + $scope.enterpriseName + "&serviceType=" + $scope.serviceType + "&startDate=" +
                $scope.initSel.startTime + "&endDate=" + $scope.initSel.endTime + "&areaDimension=3" + "&timeDimension=1" + "&enterpriseID=" + $scope.enterpriseID +  "&parentEnterpriseID=" + "&cityID=" + "&provinceID=" + "&enterpriseType=2" + "&type=10";
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseStatInfo",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.StatInfoListData = result.enterpriseStatInfoList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[0].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    }
                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                }
                )
            }
        });

    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })


}])