var app = angular.module("myApp", ["util.ajax", "page", "cy.uploadify", "cy.uploadifyfile","angularI18n", "service.common"])
app.controller("XwBatchsubListController", function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
  $scope.init = function () {

    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    //下拉框
    $scope.dealStatusList = [
      {
        id: "1",
        name: "处理中"
      },
      {
        id: "2",
        name: "处理完成"
      }
    ];
    $scope.initBusinessURLContainer($scope);

    $('#createBatchSubTask').on('hidden.bs.modal', function (e) {
        $rootScope.$apply(function () {
          if ($scope.uploader) {
            $scope.uploader.reset();
          }
          $scope.showUpload = false;
          $scope.businessLicenseURL_ = "";
          $scope.fileUrl_ = "";
          $scope.urlList_ = [];
          //清空表单验证
          $scope.myForm.$setPristine();
          $("#filePicker").find("span").text("导入文件");
          $scope.errorInfo = "";
          $scope.fileName = "";
          $scope.fileUrl = "";
        })


    })
    $('#createBatchSubTask').on('show.bs.modal', function (e) {
        $scope.showUpload = true;

    })
    $scope.queryH5BatchSubTask();
  };

  $scope.h5BatchSubTaskData = [];
  $scope.createBatchSubTaskModel = function () {
	    $scope.businessLicenseURL_ = "";
	      $scope.fileUrl = "";

    $("#createBatchSubTask").modal();
  }
  $scope.showBusinessURL = function(){

      $("#showBusinessURL").modal('show');

  }

  $scope.hideBusinessURL = function(){

      $("#showBusinessURL").modal('hide');

  }
  //查询
  $scope.queryH5BatchSubTask = function (condition) {
    var req = {};
    if (condition != 'justPage') {
    	var dealStatus = [];
    	if ($scope.dealStatus && $scope.dealStatus == "1")
        {
    		dealStatus = [0, 1];
        }
    	else if ($scope.dealStatus && $scope.dealStatus == "2")
        {
    		dealStatus = [2];
        }
    	
      req = {
        "taskID": $scope.taskId || '',
        "dealStatus": dealStatus,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryH5BatchSubTaskTemp = angular.copy(req);
    } else {
      req = $scope.queryH5BatchSubTaskTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryH5BatchSubTask",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(result);
          if (data.resultCode == '**********') {
            $scope.h5BatchSubTaskData = result.taskList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.accountContentInfoData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.accountContentInfoData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
  $scope.toDetail=function(item){
  	$.cookie("taskId",item.id,{path:'/'});
  	location.href='../detail/xwBatchsubDetail.html';
  }
  //初始化营业执照上传容器
  $scope.initBusinessURLContainer = function($scope){
      $scope.showUpload = false;
      $scope.fileUrl_ = "";
      $scope.urlList_ = [];
      $scope.urlList_2 = [];
      //初始化营业执照上传容器
      $scope.filePicker_ = "filePicker_";
      $scope.accepttype_ = "jpg,jpeg,png";
      $scope.isValidate_ = false;
      $scope.filesize_ = 20;
      $scope.mimetypes_ = ".jpg,.jpeg,.png";
      $scope.isCreateThumbnail_ = true;
      $scope.uploadurl_ ='/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
      $scope.uploadDesc_ = "仅支持一张图片，仅支持jpg，jpeg，png格式";
      $scope.numlimit_ = 1;
      $scope.uploadParam_ = {
        enterpriseId: "",
        fileUse: 'businessLicense'
      };

      $scope.$on("uploadifyid_2",function(event,fileUrl_){
          if(fileUrl_){
              $scope.urlList_ = [fileUrl_];
              $scope.urlList_2 = [CommonUtils.formatPic(fileUrl_).review];
          }else{
              $scope.urlList_ = [];
		$scope.urlList_2 = [];
          }
          $scope.businessLicenseURL_ = fileUrl_;
      });
      
      // 上传excel
      $scope.errorInfo = "";
      $scope.fileUrl = "";
      $scope.accepttype = "xlsx";
      $scope.isValidate = true;
      $scope.filesize = 20;
      $scope.mimetypes = ".xlsx,.xls";
      $scope.auto = true;
      $scope.isCreateThumbnail = false;
      $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
      $scope.uploadDesc = "仅支持xlsx格式的文件";
      $scope.numlimit = 1;
      $scope.uploadParam = {
        enterpriseId: "",
        fileUse: 'xwbatchSub'
      };
      // 上传excel  END
      $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
        if (broadData.file !== "") {
          $scope.fileName = broadData.file.name;
        } else {
          $scope.fileName = "";
        }
        $scope.uploader = broadData.uploader;
        $scope.errorInfo = broadData.errorInfo;
        $scope.fileUrl = fileUrl;
      });

  }
  $scope.importBatchSub = function () {
	  var businessLicenseURL = $scope.businessLicenseURL_;
	  var excelfileUrl = $scope.fileUrl;
	  console.log(businessLicenseURL + "," + excelfileUrl);
	  var req = {
		        "businessLicenseURL": businessLicenseURL,
		        "excelfileUrl": excelfileUrl
		    };
		    RestClientUtil.ajaxRequest({
		      type: 'POST',
		      url: "/ecpmp/ecpmpServices/contentService/importBatchSub",
		      data: JSON.stringify(req),
		      success: function (data) {
		        $rootScope.$apply(function () {
		          var result = data.result;
		          if (result.resultCode == '**********') {
		            $('#createBatchSubTask').modal("hide");
		            $scope.tip = "导入成功";
		            $('#myModal').modal();
		            $scope.queryH5BatchSubTask();
		          }
		          else {
		            $scope.tip = "新建任务失败";
		            $('#myModal').modal();
		          }
		        })
		      },
		      error: function () {
		        $rootScope.$apply(function () {
		          $scope.tip = '**********';
		          $('#myModal').modal();
		        })
		      }
		    });
  }
});
app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
    }
    return "";
  }
})
app.filter("dealStatus", function () {
  return function (dealStatus) {
    if (dealStatus) {
    	var dealTxt = "";
    	if (dealStatus == 1 || dealStatus == 0)
        {
    		dealTxt = "处理中";
        }
    	else if (dealStatus == 2)
        {
    		dealTxt = "处理完成";
        }
      return dealTxt;
    }
    return "处理中";
  }
})