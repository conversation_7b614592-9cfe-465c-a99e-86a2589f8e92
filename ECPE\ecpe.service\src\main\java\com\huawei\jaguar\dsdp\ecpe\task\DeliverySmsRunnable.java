/*
 * 文 件 名:  DeliverySmsRunnable.java
 * 版    权:  Huawei Technologies Co., Ltd. Copyright YYYY-YYYY,  All rights reserved
 * 描    述:  <描述>
 * 修 改 人:  zWX273370
 * 修改时间:  2019年1月7日
 * 跟踪单号:  <跟踪单号>
 * 修改单号:  <修改单号>
 * 修改内容:  <修改内容>
 */

package com.huawei.jaguar.dsdp.ecpe.task;

import com.huawei.jaguar.dsdp.ecpe.commons.utils.DateUtil;
import com.huawei.jaguar.dsdp.ecpe.commons.utils.DateUtil.DatePattern;
import com.huawei.jaguar.dsdp.ecpe.commons.utils.UserCountUtil;
import com.huawei.jaguar.dsdp.ecpe.constant.DeliveryConstant;
import com.huawei.jaguar.dsdp.ecpe.context.EcpeContextHolder;
import com.huawei.jaguar.dsdp.ecpe.core.FrameSpringBeanUtil;
import com.huawei.jaguar.dsdp.ecpe.core.elasticsearch.ElasticSearchCluster;
import com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentWrapper;
import com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryRuleWrapper;
import com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryTaskWrapper;
import com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryWrapper;
import com.huawei.jaguar.dsdp.ecpe.dao.mapper.ContentMapper;
import com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryMapper;
import com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryTaskMapper;
import com.huawei.jaguar.dsdp.ecpe.exception.EcpeException;
import com.huawei.jaguar.dsdp.ecpe.request.DeductingQuotaReq;
import com.huawei.jaguar.dsdp.ecpe.request.QueryThirdpartyReq;
import com.huawei.jaguar.dsdp.ecpe.response.QueryThirdpartyRsp;
import com.huawei.jaguar.dsdp.ecpe.response.Result;
import com.huawei.jaguar.dsdp.ecpe.result.Constants;
import com.huawei.jaguar.dsdp.ecpe.result.ResultMessage;
import com.huawei.jaguar.dsdp.ecpe.service.impl.HotLineManagementService;
import com.huawei.jaguar.dsdp.ecpe.service.impl.SubscribeServices;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.DeliverySmsService;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.DeliverySmsTemplateService;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.DsumService;
import com.huawei.jaguar.dsdp.ecpe.spi.feign.EcpfepService;
import com.huawei.jaguar.dsdp.ecpe.spi.model.DeliveryResult;
import com.huawei.jaguar.dsdp.ecpe.spi.request.DeliveryResultNotifyReq;
import com.huawei.jaguar.dsdp.ecpe.spi.request.DeliverySmsTemplateReq;
import com.huawei.jaguar.dsdp.ecpe.spi.request.SendMMSReq;
import com.huawei.jaguar.dsdp.ecpe.spi.request.SendSmsReq;
import com.huawei.jaguar.dsdp.ecpe.spi.response.DeliveryResultNotifyRsp;
import com.huawei.jaguar.dsdp.ecpe.spi.response.DeliverySmsResp;
import com.huawei.jaguar.dsdp.ecpe.spi.response.SendMMSRsp;
import com.huawei.jaguar.dsdp.ecpe.spi.response.SendSmsRsp;
import com.huawei.jaguar.dsum.request.QueryEnterpriseInfoReq;
import com.huawei.jaguar.dsum.response.QueryEnterpriseInfoRsp;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.ThreadContext;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <一句话功能简述>
 * <功能详细描述>
 * 
 * <AUTHOR>
 * @version  [版本号, 2019年1月7日]
 * @see  [相关类/方法]
 * @since  [产品/模块版本]
 */
@Slf4j
@Setter
public class DeliverySmsRunnable implements Runnable {
    /**
     * 发送内容
     */
    private ThirdpartyDeliveryWrapper deliveryContent;

    /**
     * 投递dao
     */
    private ThirdpartyDeliveryMapper thirdpartyDeliveryMapper;

    /**
     * 第三方投递短信服务
     */
    private DeliverySmsService deliverySmsService;

    /**
     * 短信投递服务（基于模板）
     */
    private DeliverySmsTemplateService deliverySmsTemplateService;

    /**
     * 订购关系服务
     */
    private SubscribeServices subscribeServices;

    /**
     * 内容信息
     */
    private ContentWrapper contentWrapper;

    /**
     * 短信类型
     */
    private String smsType;

    /**
     * 模板内容
     */
    private String content;

    /**
     * 发送号码
     */
    private String src;

    /**
     * 来源
     */
    private String source;

    /**
     * 首次投递标识
     */
    private String deliveryFirstAction;

    /**
     * 二次投递标识
     */
    private String deliveryFailAction;

    /**
     * 投递规则ID
     */
    private Integer ruleID;

    /**
     * 线程池
     */
    private ThreadPoolExecutor deliveryMsgExecutor;

    /**
     * 热线管理服务
     */
    private HotLineManagementService hotLineManagementService;

    /**
     * 调接口机服务
     */
    private EcpfepService ecpfepService;

    /**
     * 投递数据
     */
    private ThirdpartyDeliveryWrapper delivery;

    /**
     * 内容dao
     */
    private ContentMapper contentMapper;

    /**
     * 投递任务数据库dao
     */
    private ThirdpartyDeliveryTaskMapper thirdpartyDeliveryTaskMapper;

    /**
     * 调用dsum接口
     */
    private DsumService dsumService;

    /**
     * ES服务
     */
    private ElasticSearchCluster elasticSearchCluster;

    /**
     * 同步ES启动场景标识
     */
    private String flag;

    /**
     * 同步ES:投递请求响应码
     */
    private String deliveryRspCode;

    /**
     * 第三方投递请求时间
     */
    private String enterMethodTime;

    /**
     * 第三方投递响应时间
     */
    private String existMethodTime;

    /**
     * 通知第三方投递响应时间
     */
    private String deliveryResult;

    /**
     * 来源
     */
    private String owner;

    /**
     * 彩印发送类型
     */
    private String colorSendType;

    /**
     * 未发送彩印原因
     */
    private String noSendColorReason;

    /**
     * 彩印推送时间
     */
    private Date pushTime;

    /**
     * 匹配的投递规则信息
     */
    private ThirdpartyDeliveryRuleWrapper deliveryRule;

    /**
     * 参数内容
     */
    private List<String> argv;

    /**
     * 热线挂机短信发送格式
     */
    public static final String NORMAL = "NORMAL";

    /**
     * 闪信发送格式
     */
    public static final String FLASH = "FLASH";

    /**
     * 默认短信发送来源
     */
    public static final String SOURCE = "ENTERPRISE";

    /**
     * 异网支持USSD开关0：关 1：开
     */
    private String diffnetUssdSwitch;

    // 201
    private String needPlatformEnterprise;

    private String enterpriseFormulation;


    
    /**
     * 构造函数
     */
    public DeliverySmsRunnable(DeliveryTaskDomain taskDomain) {
        this.content = taskDomain.getContent();
        this.deliveryContent = taskDomain.getDeliveryContent();
        this.deliverySmsService = taskDomain.getDeliverySmsService();
        this.deliverySmsTemplateService = taskDomain.getDeliverySmsTemplateService();
        this.smsType = taskDomain.getSmsType();
        this.src = taskDomain.getSrc();
        this.thirdpartyDeliveryMapper = taskDomain.getThirdpartyDeliveryMapper();
        this.source = taskDomain.getSource();

        this.subscribeServices = taskDomain.getSubscribeServices();
        this.contentWrapper = taskDomain.getContentWrapper();

        this.deliveryFirstAction = taskDomain.getDeliveryFirstAction();
        this.deliveryFailAction = taskDomain.getDeliveryFailAction();
        this.deliveryMsgExecutor = taskDomain.getDeliveryMsgExecutor();
        this.ruleID = taskDomain.getRuleID();

        this.hotLineManagementService = taskDomain.getHotLineManagementService();
        this.ecpfepService = taskDomain.getEcpfepService();
        this.thirdpartyDeliveryTaskMapper = taskDomain.getThirdpartyDeliveryTaskMapper();

        this.deliveryRule = taskDomain.getDeliveryRule();
        this.delivery = taskDomain.getDelivery();
        this.contentMapper = taskDomain.getContentMapper();
        this.dsumService = taskDomain.getDsumService();
        this.elasticSearchCluster = taskDomain.getElasticSearchCluster();
        this.flag = taskDomain.getFlag();
        this.deliveryRspCode = taskDomain.getDeliveryRspCode();
        this.owner = taskDomain.getOwner();
        this.pushTime = taskDomain.getColorPushTime();
        this.enterMethodTime = taskDomain.getEnterMethodTime();
        this.existMethodTime = taskDomain.getExistMethodTime();
        this.argv = taskDomain.getArgv();
        this.diffnetUssdSwitch = taskDomain.getDiffnetUssdSwitch();
        // 201
        this.needPlatformEnterprise = taskDomain.getNeedPlatformEnterprise();
        this.enterpriseFormulation = taskDomain.getEnterpriseFormulation();
    }

    @Override
    public void run() {
        // 线程开始时，清理线程变量，防止线程复用数据篡改
        EcpeContextHolder.setContext(null);

        log.debug("enter delivery sms....");
        DeliverySmsResp resp = null;
        String msgType = String.valueOf(delivery.getMsgType());

        ThirdpartyDeliveryWrapper deliveryWrap = new ThirdpartyDeliveryWrapper();
        
        boolean isDelivery = true;
        if ((DeliveryConstant.MsgType.INTERACTION_USSD.equals(msgType)
            || DeliveryConstant.MsgType.USSD.equals(msgType)
            || DeliveryConstant.MsgType.USSD_NO_DOMAIN.equals(msgType)
            || DeliveryConstant.MsgType.USSD_SECOND_FLASH.equals(msgType)
            || DeliveryConstant.MsgType.MSGTYPE_FIVE.equals(msgType))
            && DeliveryConstant.diffnetUssdSwitch.switchClose.equals(diffnetUssdSwitch)
            && !DeliveryConstant.mobilePlatform.PLATFORM_YD_STR.equals(owner)
                &&DeliveryConstant.SmsType.USSD.equals(smsType))
        {
            isDelivery = false;
        }
        
        try {
            log.info("DeliverySmsRunnable's taskId = {}, src={}, smsType={} deliveryContent={}",
                deliveryContent.getTaskID(), src);
            // 更新定时任务处理状态1处理中
            updateNotifyStatus(DeliveryConstant.ProcessStatus.PROCESSING, null, null, null);

            DeliverySmsTemplateReq req = new DeliverySmsTemplateReq();
            req.setReceiver(deliveryContent.getTarget());
            req.setSenderName(src);
            req.setFormat(smsType);
            req.setSource(StringUtils.isBlank(source) ? DeliveryConstant.DEFAULT_SMS_SOURCE : source);

            if (null != contentWrapper.getId() && StringUtils.isNotBlank(contentWrapper.getContCode())) {
                if (String.valueOf(contentWrapper.getId()).equals(contentWrapper.getContCode())) {
                    req.setMessage(content);
                } else {
                    req.setTemplateID(contentWrapper.getContCode());
                    req.setArgv(argv);
                }
            } else {
                req.setMessage(content);
            }

            if (StringUtils.isNotBlank(owner)) {
                req.setPlatform(Integer.valueOf(owner));
            } else {
                req.setPlatform(DeliveryConstant.mobilePlatform.PLATFORM_YD);
            }
            log.debug("DeliverySmsTemplateReq {}", req);

            deliveryWrap.setId(this.deliveryContent.getId());
            deliveryWrap.setRuleID(this.ruleID);
            deliveryWrap.setInsertTime(this.deliveryContent.getInsertTime());
            if (DeliveryConstant.SmsType.USSD.equals(smsType)) {
                deliveryWrap.setDeliveryChannel(DeliveryConstant.DeliveryAction.USSD);
            } else if (DeliveryConstant.SmsType.FLASH.equals(smsType)) {
                deliveryWrap.setDeliveryChannel(DeliveryConstant.DeliveryAction.SMS_FLASH);
            } else if (DeliveryConstant.SmsType.NORMAL.equals(smsType)) {
                deliveryWrap.setDeliveryChannel(DeliveryConstant.DeliveryAction.SMS_NORMAL);
            }

            // 记录投递推送时间
            if (DeliveryConstant.SmsType.USSD.equals(smsType)) {
                deliveryWrap.setUssdPushTime(new Date());
                // ES中彩印推送时间为首次投递推送时间
                pushTime = null == pushTime ? deliveryWrap.getUssdPushTime() : pushTime;
            } else {
                deliveryWrap.setPushTime(new Date());
                // ES中彩印推送时间为首次投递推送时间
                pushTime = null == pushTime ? deliveryWrap.getPushTime() : pushTime;
            }
            log.info("DeliverySmsRunnable sendSmsTemplate start, taskId = {},req = {}", deliveryContent.getTaskID(),
                req);
            if (contentWrapper.getContentType()!=null&&contentWrapper.getContentType() == 6)
            {
                req.setServType("2");
                req.setCorpType(String.valueOf(contentWrapper.getSupportEnterpriseType()));
            }
            // 调中央彩印平台推送彩印
            ArrayList<String> cmppTemplateID = FrameSpringBeanUtil.cmppTemplateID;
            Long wrapperId = contentWrapper.getId();
            if ((DeliveryConstant.MsgType.USSD_SECOND_FLASH.equals(msgType) || DeliveryConstant.MsgType.MSGTYPE_FIVE.equals(msgType))
                && null != deliveryFailAction){
                resp = new DeliverySmsResp();
                resp.setStatus(991); //不进行USSD投递,直接失败
                resp.setErrorMsg("不进行USSD投递,直接失败");
            }else if (isDelivery)
            {
                Integer subServType = contentWrapper.getSubServType();
                Integer enterpriseID = contentWrapper.getEnterpriseID();
                List<String> enterpriseIDs = null;
                if (StringUtils.isNotEmpty(enterpriseFormulation))
                {
                    enterpriseIDs = Arrays.asList(enterpriseFormulation.split("\\|"));
                }
                String direction = deliveryContent.getDirection();
                if (cmppTemplateID.contains(wrapperId.toString()))
                {
                    //cmpp调中央接口的sms/send投递参数组装
                    SendSmsReq sendSmsReq = new SendSmsReq();
                    sendSmsReq.setSenderName(req.getSenderName());
                    sendSmsReq.setReceiver(req.getReceiver());
                    sendSmsReq.setMessage(req.getMessage());
                    sendSmsReq.setSource(SOURCE);
                    int index = cmppTemplateID.indexOf(wrapperId.toString());
                    //配置项第一个index为0是热线挂短,index为1是闪信
                    if (0 == index)
                    {
                        sendSmsReq.setFormat(NORMAL);
                    }
                    else
                    {
                        sendSmsReq.setFormat(FLASH);
                    }

                    log.debug("Cmpp ready to send sms ,sendSmsReq is {}", req);
//                    if(DeliveryConstant.SubServType.GJCX==subServType){
//                        if(Integer.valueOf(delivery.getPlatform()).equals(DeliveryConstant.mobilePlatform.PLATFORM_YD)){
//                            if (StringUtils.isNotEmpty(needPlatformEnterprise)) {
//                                if(enterpriseIDs.contains(enterpriseID.toString())){
//                                    if(direction.equals("MO")){
//                                        String scalling = deliveryContent.getScalling();
//                                        sendSmsReq.setSenderAddress(src+scalling);
//                                    }else if(direction.equals("MT")){
//                                        String called = deliveryContent.getCalled();
//                                        sendSmsReq.setSenderAddress(src+called);
//                                    }
//                                }
//                            }
//                        }
//                    }
                    SendSmsRsp sendSmsRsp = deliverySmsService.sendSms(sendSmsReq);
                    log.debug("Cmpp SendSmsRsp is {}", sendSmsRsp);
                    if (null != sendSmsRsp)
                    {
                        resp = buildToDeliverySmsResp(sendSmsRsp);
                    }else{
                        resp = new DeliverySmsResp();
                        resp.setProvider(0);
                    }

                }else if(DeliveryConstant.SmsType.MMS.equals(smsType)||DeliveryConstant.SmsType.EBANMMS.equals(smsType)){
                    //增彩 彩信走彩信投递接口
                    SendMMSReq sendMMSReq = new SendMMSReq();
                    sendMMSReq.setUuid(contentWrapper.getContCode());
                    if(DeliveryConstant.SmsType.EBANMMS.equals(smsType)){
                        sendMMSReq.setChannel("2");
                    }
                    sendMMSReq.setSenderAddress(src);
                    sendMMSReq.setCallingMdn(deliveryContent.getTarget());
                    sendMMSReq.setCalledMdn(deliveryContent.getTarget());

//                    if(DeliveryConstant.SubServType.GJCX==subServType){
                        if(Integer.valueOf(delivery.getPlatform()).equals(DeliveryConstant.mobilePlatform.PLATFORM_YD)){
                            if (CollectionUtils.isNotEmpty(enterpriseIDs)) {
                                if(enterpriseIDs.contains(enterpriseID.toString())){
                                    if(direction.equals("MO")){
                                        String scalling = deliveryContent.getScalling();
                                        sendMMSReq.setSenderAddress(src+scalling);
                                    }else if(direction.equals("MT")){
                                        String called = deliveryContent.getCalled();
                                        sendMMSReq.setSenderAddress(src+called);
                                    }
                                }
                            }
                        }
//                    }
                    SendMMSRsp sendMMSRsp =  deliverySmsService.sendMMS(sendMMSReq);
                    if(sendMMSRsp!=null){
                        resp = buildToDeliveryMMSResp(sendMMSRsp);
                    }

                } else {
//                    if(DeliveryConstant.SubServType.CALLER==subServType||DeliveryConstant.SubServType.CALLED==subServType||DeliveryConstant.SubServType.CALLER_CALLED_SCREEN==subServType||DeliveryConstant.SubServType.GJDX==subServType){
                        if(Integer.valueOf(delivery.getPlatform()).equals(DeliveryConstant.mobilePlatform.PLATFORM_YD)){
                            if (CollectionUtils.isNotEmpty(enterpriseIDs)) {
                                if(enterpriseIDs.contains(enterpriseID.toString())){
                                    if(direction.equals("MO")){
                                        String scalling = deliveryContent.getScalling();
                                        req.setSenderName(src+scalling);
                                    }else if(direction.equals("MT")){
                                        String called = deliveryContent.getCalled();
                                        req.setSenderName(src+called);
                                    }
                                }
                            }
                        }
//                    }
                        resp = deliverySmsTemplateService.sendSmsTemplate(req);
                }
            }
            log.info("DeliverySmsRunnable sendSmsTemplate end, taskId = {},resp = {}", deliveryContent.getTaskID(),
                resp);

        } catch (Exception e) {
            log.error("send sms error.", e);
        }
        log.debug("DeliverySmsResp {}", resp);

        if (null != resp && null != resp.getStatus()) {
            // 根据动作类型记录对应结果
            if (DeliveryConstant.SmsType.USSD.equals(smsType)) {
                deliveryWrap.setUssdSentResult(resp.getStatus().toString());
            } else {
                deliveryWrap.setSentResult(resp.getStatus().toString());
            }
        } else {
            if (DeliveryConstant.SmsType.USSD.equals(smsType)) {
                deliveryWrap.setUssdSentResult(DeliveryConstant.SendResult.FAILED);
            } else {
                deliveryWrap.setSentResult(DeliveryConstant.SendResult.FAILED);
            }
            
            if (!isDelivery)
            {
                deliveryWrap.setUssdSentResult(DeliveryConstant.SendResult.DIFFNET_OFF_FAILED);
            }
        }

        if (DeliveryConstant.SmsType.USSD.equals(smsType)) {
            delivery.setUssdSentResult(deliveryWrap.getUssdSentResult());
        } else {
            delivery.setSentResult(deliveryWrap.getSentResult());
        }
        deliveryWrap.setUpdateTime(new Date());
        String msg = null;
        if(null != resp&&null!= resp.getErrorMsg()){
        	msg = resp.getErrorMsg();
        }
        if (null != resp && DeliveryConstant.SmsStatus.SUCCESS.equals(resp.getStatus())) {
            deliveryWrap.setTransactionID(resp.getCorrelator());

            if (DeliveryConstant.SmsType.USSD.equals(smsType)) {
                deliveryWrap.setUssdSentResult(DeliveryConstant.SendResult.SUCCESS);
            } else {
                deliveryWrap.setSentResult(DeliveryConstant.SendResult.SUCCESS);
            }
            if(resp.getProvider() != null){
                deliveryWrap.setProvider(resp.getProvider());
            }
            // 更新彩印中央投递结果
            thirdpartyDeliveryMapper.updateDeliverySendResult(deliveryWrap);
            // 更新定时任务处理状态0待处理
            updateNotifyStatus(DeliveryConstant.ProcessStatus.WAIT, null, null, null);
        } else {
            if(resp != null && resp.getProvider() != null){
                deliveryWrap.setProvider(resp.getProvider());
            }
        	deliveryWrap.setMsg(msg);
            thirdpartyDeliveryMapper.updateDeliverySendResult(deliveryWrap);

            // ES记录彩印推送失败原因
            if (DeliveryConstant.SmsType.USSD.equals(smsType)) {
                noSendColorReason = deliveryWrap.getUssdSentResult();
            } else {
                noSendColorReason = deliveryWrap.getSentResult();
            }

            // 判断是否需要二次投递
            if (null != deliveryFailAction && !DeliveryConstant.DeliveryAction.NO_SEND.equals(deliveryFailAction)) {
                // 再次投递
                deliverySecond();
            }
            // 投递失败，还原配额
            else {
                if(resp != null && resp.getProvider() != null){
                    delivery.setProvider(resp.getProvider());
                }
                // 获取第三方投递请求时间和响应时间
                ThirdpartyDeliveryTaskWrapper taskWrapper = new ThirdpartyDeliveryTaskWrapper();

                // 获取最终投递结果
                List<DeliveryResult> deliveryResults = getDeliveryResult(msg);
                //迭代8修改,挂机长短信算多个配额
                Long useCount = countLongContent(deliveryContent,content);

                if (!DeliveryConstant.MsgType.USSD.equals(msgType)
                    && !DeliveryConstant.MsgType.FLASH_NEED_RESULT.equals(msgType)
                    && !DeliveryConstant.MsgType.MSGTYPE_FIVE.equals(msgType)
                    && !DeliveryConstant.MsgType.USSD_NO_DOMAIN.equals(msgType)
                    && !DeliveryConstant.MsgType.MSGTYPE_SIX.equals(msgType)
                    && !DeliveryConstant.MsgType.MSGTYPE_NINE.equals(msgType)
                    && !DeliveryConstant.MsgType.INTERACTION_USSD.equals(msgType)
                    && !DeliveryConstant.MsgType.MSGTYPE_MMS.equals(msgType)) {
                    // 更新定时任务通知状态1.已通知和处理状态2处理成功
                    updateNotifyStatus(DeliveryConstant.ProcessStatus.SUCCESS, DeliveryConstant.NotifyStatus.SUCCESS,
                        new Date(), DeliveryConstant.NOTIFY_COUNT);

                    // 还原配额
                    //迭代8修改,挂机长短信算多个配额

                    backQuota(owner,useCount);

                    // 如果是还原配额 还原那两个字段
                    setSubIDAndOrderIDNull(deliveryWrap);

                    // 记录ES数据
                    syncDeliveryToES(taskWrapper);
                } else {
                    // 通知第三方调用接口机ecfep投递接口通知接口
                    DeliveryResultNotifyRsp rsp = notifyThirdDeliveryResult(taskWrapper, deliveryResults);

                    // 如果接口机返回成功，更新定时任务通知时间及状态，记录ES数据
                    if (null != rsp && null != rsp.getResult()
                        && DeliveryConstant.THIRDPARTY_SUCCESS.equals(rsp.getResult().getResultCode())) {
                        // 更新定时任务通知状态1.已通知和处理状态2处理成功
                        updateNotifyStatus(DeliveryConstant.ProcessStatus.SUCCESS,
                            DeliveryConstant.NotifyStatus.SUCCESS, new Date(), DeliveryConstant.NOTIFY_COUNT);

                        // 还原配额
                        //迭代8修改,挂机长短信算多个配额
                        backQuota(owner,useCount);

                        // 如果是还原配额 还原那两个字段
                        setSubIDAndOrderIDNull(deliveryWrap);

                        // 记录ES数据
                        syncDeliveryToES(taskWrapper);
                    } else {
                        // 更新定时任务处理状态3处理失败
                        updateNotifyStatus(DeliveryConstant.ProcessStatus.FAILED, null, null,
                            DeliveryConstant.NOTIFY_COUNT);
                    }
                }
            }
        }
        
        // 业务结束，需手动清空线程变量
        ThreadContext.clearAll();
        
        log.debug("exit delivery sms....");
    }

    private DeliverySmsResp buildToDeliverySmsResp(SendSmsRsp sendSmsRsp)
    {
        DeliverySmsResp resp = new DeliverySmsResp();
        resp.setStatus(Integer.parseInt(sendSmsRsp.getStatus()));
        resp.setCorrelator(sendSmsRsp.getCorrelator());
        resp.setErrorMsg(sendSmsRsp.getErrorMsg());
        resp.setSmsIdentifier(sendSmsRsp.getSmsIdentifier());
        resp.setProvider(0);
        return resp;
    }

    private DeliverySmsResp buildToDeliveryMMSResp(SendMMSRsp sendMMSRsp)
    {
        DeliverySmsResp resp = new DeliverySmsResp();
        if(sendMMSRsp.getCode()!=null&&sendMMSRsp.getCode()==0){
            resp.setStatus(200);
        }else {
            resp.setStatus(sendMMSRsp.getCode());
        }
        resp.setCorrelator(sendMMSRsp.getCorrelator());
        resp.setErrorMsg(sendMMSRsp.getText());
        return resp;
    }


    /**
    * 计算内容应该扣减还原的配额
    * @param
    * @return
    * @see [类、类#方法、类#成员]
    */
    private Long countLongContent(ThirdpartyDeliveryWrapper deliveryContent, String content)
    {
        Long useCount = 1L;
        try
        {
            Integer msgtype = deliveryContent.getMsgType();
            int length = content == null ? 0 : content.length();
            useCount=UserCountUtil.getCountByLength(msgtype.toString(),length);
           /* if (NumberUtils.createInteger(DeliveryConstant.DELIVERY_REQ_SMS_SUBMIT_MSGTYPE).equals(msgtype) && length > 70)
            {
                Integer i = Integer.valueOf((length / 67) + (length % 67 == 0 ? 0 : 1));
                useCount = i.longValue();
            }else if((NumberUtils.createInteger("2").equals(msgtype)||
            		NumberUtils.createInteger("4").equals(msgtype)||
            				NumberUtils.createInteger("12").equals(msgtype)||
            						NumberUtils.createInteger("14").equals(msgtype))&& length > 70){
            			 Integer i = Integer.valueOf((length / 67) + (length % 67 == 0 ? 0 : 1));
                         useCount = i.longValue();
            }*/
        }
        catch (Exception e)
        {
            log.error("count useCount error",e);
            return useCount;
        }
        return useCount;
    }

    /**
     * 异步同步投递信息到ES
     * @param taskWrapper
     */
    private void syncDeliveryToES(ThirdpartyDeliveryTaskWrapper taskWrapper) {
        // 拼装ES线程池参数
        DeliveryTaskDomain taskParam = new DeliveryTaskDomain();
        taskParam.setExistMethodTime(existMethodTime);
        taskParam.setEnterMethodTime(enterMethodTime);
        if (null != taskWrapper.getNotifyThirdReqTime()) {
            taskParam.setThirdDeliveryReqTime(
                DateUtil.format(taskWrapper.getNotifyThirdReqTime(), DatePattern.yyyyMMddHHmmssSSS1));
        }
        if (null != taskWrapper.getNotifyThirdRspTime()) {
            taskParam.setThirdDeliveryRspTime(
                DateUtil.format(taskWrapper.getNotifyThirdRspTime(), DatePattern.yyyyMMddHHmmssSSS1));
        }
        taskParam.setEnterpriseID(contentWrapper.getEnterpriseID());
        taskParam.setColorSendType(colorSendType);
        taskParam.setNoSendColorReason(noSendColorReason);
        taskParam.setDeliveryResult(deliveryResult);
        taskParam.setColorPushTime(pushTime);
        taskParam.setDelivery(delivery);
        taskParam.setContentMapper(contentMapper);
        taskParam.setThirdpartyDeliveryTaskMapper(thirdpartyDeliveryTaskMapper);
        taskParam.setDsumService(dsumService);
        taskParam.setElasticSearchCluster(elasticSearchCluster);
        taskParam.setFlag(flag);
        taskParam.setDeliveryRspCode(deliveryRspCode);
        taskParam.setSrc(src);
        taskParam.setOwner(owner);
        taskParam.setHotLineManagementService(hotLineManagementService);
        Runnable task = new DeliveryToEsRunnable(taskParam);
        try {
            this.deliveryMsgExecutor.submit(task);
        } catch (Exception e) {
            log.error("DeliveryMsgExecutor.submit error", e);
        }
    }

    /**
     * 调用ecpfep接口通知第三方投递结果
     * @param taskWrapper
     * @param deliveryResults
     * @return
     */
    private DeliveryResultNotifyRsp notifyThirdDeliveryResult(ThirdpartyDeliveryTaskWrapper taskWrapper,
        List<DeliveryResult> deliveryResults) {
        DeliveryResultNotifyReq notifyReq = new DeliveryResultNotifyReq();
        notifyReq.setTaskID(deliveryContent.getTaskID().toString());
        notifyReq.setDeliveryResults(deliveryResults);

        QueryThirdpartyReq queryThirdpartyReq = new QueryThirdpartyReq();
        queryThirdpartyReq.setPlatformID(contentWrapper.getEnterpriseID());
        QueryThirdpartyRsp queryThirdpartyRsp = hotLineManagementService.queryThirdParty(queryThirdpartyReq);
        DeliveryResultNotifyRsp rsp = null;
        if (null != queryThirdpartyRsp && null != queryThirdpartyRsp.getThirdpartyAccess()) {
            notifyReq.setPlatformID(queryThirdpartyRsp.getThirdpartyAccess().getThirdAccount());
            notifyReq.setThirdPassword(queryThirdpartyRsp.getThirdpartyAccess().getThirdPassword());
            notifyReq.setNotifyURL(StringUtils.isBlank(deliveryContent.getNotifyUrl())
                ? queryThirdpartyRsp.getThirdpartyAccess().getCallbackUrl() : deliveryContent.getNotifyUrl());
            // 设置通知第三方请求时间
            taskWrapper.setNotifyThirdReqTime(new Date());

            // 迭代五模拟投递接口引入改造,如果notifyURL为空直接构造返回成功响应
            if (StringUtils.isBlank(notifyReq.getNotifyURL())) {
                Result result = new Result(DeliveryConstant.THIRDPARTY_SUCCESS, ResultMessage.SUCCESS_MESSAGE);
                rsp = new DeliveryResultNotifyRsp();
                rsp.setResult(result);
            } else {
                // 通知接口机
                checkIsCmppNotifyReq(notifyReq);
                rsp = ecpfepService.deliveryResultNotify(notifyReq);
            }
        }

        // 设置通知第三方响应时间
        taskWrapper.setNotifyThirdRspTime(new Date());

        // 记录通知第三方时间请求时间和响应时间
        updateThirdDeliveryRspTime(taskWrapper);
        return rsp;
    }

    /**
     * 依据req判断通知第三方是不是cmpp,如果是cmpp,带上task表中cmpp的请求信息和access表的ip端口
     * @return
     * @see [类、类#方法、类#成员]
     */
    private void checkIsCmppNotifyReq(DeliveryResultNotifyReq deliveryResultNotifyReq)
    {
        //如果通知地址为TCP: 开头,去task表里面依据id 查询cmppReqJson
        if (deliveryResultNotifyReq.getNotifyURL().endsWith(DeliveryConstant.CmppContans.cmppNotifyUrlSuffix))
        {
            String taskID = deliveryResultNotifyReq.getTaskID();
            List<ThirdpartyDeliveryTaskWrapper> thirdpartyDeliveryTaskWrappers =
                thirdpartyDeliveryTaskMapper.queryDeliveryTaskByID(Long.parseLong(taskID));
            if (CollectionUtils.isEmpty(thirdpartyDeliveryTaskWrappers))
            {
                log.error("query deliveryTask by ID error,the result is empty.taskID is {}",taskID);
            }
            ThirdpartyDeliveryTaskWrapper thirdpartyDeliveryTaskWrapper = thirdpartyDeliveryTaskWrappers.get(0);
            Integer enterpriseID = thirdpartyDeliveryTaskWrapper.getEnterpriseID();
            deliveryResultNotifyReq.setEnterpriseId(enterpriseID.toString());
            String cmppSubmitJson = thirdpartyDeliveryTaskWrapper.getCmppSubmitReqJson();
            deliveryResultNotifyReq.setCmppSubmitReqJson(cmppSubmitJson);
        }
    }

    /**
     * 判断最终投递结果
     * @return
     */
    private List<DeliveryResult> getDeliveryResult(String msg) {
        log.debug("getDeliveryResult msgtype:{},diffnetUssdSwitch:{},Platform:{}",delivery.getMsgType(),diffnetUssdSwitch,delivery.getPlatform());

        List<DeliveryResult> deliveryResults = new ArrayList<DeliveryResult>();
        DeliveryResult deliveryResultObj = new DeliveryResult();
        deliveryResultObj.setTarget(deliveryContent.getTarget());
        // 201
        if (StringUtils.isNotEmpty(needPlatformEnterprise)) {
            QueryEnterpriseInfoReq queryEnterpriseInfoReq = new QueryEnterpriseInfoReq();
            queryEnterpriseInfoReq.setId(contentWrapper.getEnterpriseID());
            QueryEnterpriseInfoRsp queryEnterpriseInfoRsp = dsumService.queryEnterpriseInfo(queryEnterpriseInfoReq);
            String parentEnterpriseID = contentWrapper.getEnterpriseID().toString();
            if (null != queryEnterpriseInfoRsp
                    && null != queryEnterpriseInfoRsp.getEnterprise()
                    && null != queryEnterpriseInfoRsp.getEnterprise().getParentEnterpriseID()
            ){
                parentEnterpriseID = queryEnterpriseInfoRsp.getEnterprise().getParentEnterpriseID().toString();
            }
            List<String> enterpriseIDs = Arrays.asList(needPlatformEnterprise.split("\\|"));
            if (enterpriseIDs.contains(parentEnterpriseID)) {
                deliveryResultObj.setPlatform(owner);
            }
        }


        // 判断投递彩印中央类型和第三方最终结果码
        if (null != deliveryRule) {
            if (DeliveryConstant.DeliveryAction.USSD.equals(deliveryRule.getDeliveryFirstAction())
                && DeliveryConstant.DeliveryAction.SMS_FLASH.equals(deliveryRule.getDeliveryFailedAction())) {
                deliveryResult = Constants.Delivery.USSD_FLASHSM_FAIL;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH_AFTER_FAIL;
                //010 运营商为异网（非移动），msgtype=3/5 入库ES时，置sendType=1
                if((delivery.getMsgType().equals(3)||delivery.getMsgType().equals(5))
                        &&!"1".equals(delivery.getPlatform())
                        &&DeliveryConstant.diffnetUssdSwitch.switchClose.equals(diffnetUssdSwitch)){
                    colorSendType =DeliveryConstant.CY_SEND_TYPE_FLASH;
                }
            } else if (DeliveryConstant.DeliveryAction.USSD.equals(deliveryRule.getDeliveryFirstAction())
                    && (deliveryRule.getDeliveryFailedAction()==null||"*".equals(deliveryRule.getDeliveryFailedAction())))
            {
                deliveryResult = Constants.Delivery.USSD_FAIL;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_USSD;
            }else if (DeliveryConstant.DeliveryAction.SMS_NORMAL.equals(deliveryRule.getDeliveryFailedAction())
                && null == deliveryFailAction) {
                deliveryResult = Constants.Delivery.FLASHSM_FAIL;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_NORMAL;
            } else if (DeliveryConstant.DeliveryAction.SMS_FLASH.equals(deliveryRule.getDeliveryFailedAction())
                && null == deliveryFailAction) {
                deliveryResult = Constants.Delivery.FLASHSM_FAIL;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH;
            } else if (DeliveryConstant.DeliveryAction.SMS_NORMAL.equals(deliveryRule.getDeliveryFirstAction())
                && null != deliveryFailAction) {
                deliveryResult = Constants.Delivery.SM_DELIVERY_FAILED;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_NORMAL;
            } else if (DeliveryConstant.DeliveryAction.SMS_FLASH.equals(deliveryRule.getDeliveryFirstAction())
                && null != deliveryFailAction) {
                deliveryResult = Constants.Delivery.FLASHSM_FAIL;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH;
            } else if (DeliveryConstant.DeliveryAction.MMS.equals(deliveryRule.getDeliveryFirstAction())) {
                deliveryResult = Constants.Delivery.FLASHSM_FAIL;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_MMS;
            }
            else {
                deliveryResult = Constants.Delivery.FLASHSM_FAIL;
                colorSendType = DeliveryConstant.CY_SEND_TYPE_FLASH;
            }
        }
        deliveryResultObj.setStatus(deliveryResult);
        //迭代十一修改返回彩印中央返回的错误描述
        deliveryResultObj.setMsg(msg==null?Constants.Delivery.DELIVERY_RESULTMSG_MAP.get(deliveryResult):msg);
        deliveryResultObj.setChargeNum(deliveryContent.getChargeNum());
        //deliveryResultObj.setMsg(Constants.Delivery.DELIVERY_RESULTMSG_MAP.get(deliveryResult));
        deliveryResults.add(deliveryResultObj);
        return deliveryResults;
    }

    /**
     * 更新投递定时任务表通知状态和处理状态
     * @param processStatus 处理状态0：待处理；1：处理中；2：处理成功；3：处理失败
     * @param notifyStatus 通知状态0：未通知；1：已通知
     * @param notifyTime 通知时间
     * @param notifyCount 通知次数累加值
     */
    private void updateNotifyStatus(Integer processStatus, Integer notifyStatus, Date notifyTime, Integer notifyCount) {
        Map<String, Object> map = new HashMap<String, Object>();
        List<Long> ids = new ArrayList<Long>();
        ids.add(deliveryContent.getTaskID());
        map.put("notifyTime", notifyTime);
        map.put("notifyStatus", notifyStatus);
        map.put("processStatus", processStatus);
        // 通知次数notifyCount+1
        map.put("notifyCount", notifyCount);
        map.put("updateTime", new Date());
        map.put("list", ids);
        thirdpartyDeliveryTaskMapper.updateNotifyStatusForSchedule(map);
    }

    /**
     * 还原配额
     * 
     * @see [类、类#方法、类#成员]
     */
    private void backQuota(String mobilePlatform,Long useCount) {
        DeductingQuotaReq deductingQuotaReq =
            new DeductingQuotaReq(contentWrapper.getEnterpriseID(), new Date(), contentWrapper.getServType(),
                contentWrapper.getSubServType(), contentWrapper.getChargeType(), mobilePlatform, -useCount);

        QueryEnterpriseInfoReq queryEnterpriseInfoReq = new QueryEnterpriseInfoReq();
        queryEnterpriseInfoReq.setId(contentWrapper.getEnterpriseID());
        QueryEnterpriseInfoRsp queryEnterpriseInfoRsp = dsumService.queryEnterpriseInfo(queryEnterpriseInfoReq);
        if(queryEnterpriseInfoRsp.getEnterprise()!=null){
            deductingQuotaReq.setEnterpriseType(queryEnterpriseInfoRsp.getEnterprise().getDsumReserveds().getReserved10());

            try {
                subscribeServices.deductingQuota(deductingQuotaReq);
            } catch (EcpeException e) {
                log.error("back quota exception, e=" + e + " ,req=" + deductingQuotaReq);
            }
        }


    }

    private void deliverySecond() {
        DeliveryTaskDomain taskParam = new DeliveryTaskDomain();
        Runnable task = null;
        taskParam.setContent(content);
        taskParam.setDeliveryContent(deliveryContent);
        taskParam.setSrc(src);
        taskParam.setThirdpartyDeliveryMapper(thirdpartyDeliveryMapper);
        taskParam.setDeliverySmsService(deliverySmsService);
        taskParam.setDeliverySmsTemplateService(deliverySmsTemplateService);
        taskParam.setSource(source);

        taskParam.setSubscribeServices(subscribeServices);
        taskParam.setContentWrapper(contentWrapper);

        // 不再投递，置空
        taskParam.setDeliveryFailAction(null);
        taskParam.setDeliveryMsgExecutor(deliveryMsgExecutor);
        taskParam.setDeliveryFirstAction(deliveryFirstAction);
        taskParam.setDeliveryMsgExecutor(deliveryMsgExecutor);
        taskParam.setRuleID(ruleID);

        taskParam.setHotLineManagementService(hotLineManagementService);
        taskParam.setEcpfepService(ecpfepService);

        // ES需要数据
        taskParam.setDeliveryRule(deliveryRule);
        taskParam.setDelivery(delivery);
        taskParam.setContentMapper(contentMapper);
        taskParam.setThirdpartyDeliveryTaskMapper(thirdpartyDeliveryTaskMapper);
        taskParam.setDsumService(dsumService);
        taskParam.setElasticSearchCluster(elasticSearchCluster);
        taskParam.setFlag(DeliveryConstant.FLAG_ES_DELIVERY_FAILED);
        taskParam.setDeliveryRspCode(deliveryRspCode);
        taskParam.setOwner(owner);
        taskParam.setExistMethodTime(existMethodTime);
        taskParam.setEnterMethodTime(enterMethodTime);
        taskParam.setColorSendType(colorSendType);
        taskParam.setNoSendColorReason(noSendColorReason);
        taskParam.setColorPushTime(pushTime);
        taskParam.setArgv(argv);
        taskParam.setDiffnetUssdSwitch(diffnetUssdSwitch);
        // 移动ussd调用原接口
        if (DeliveryConstant.mobilePlatform.PLATFORM_YD.equals(Integer.valueOf(owner))
            && DeliveryConstant.DeliveryAction.USSD.equals(deliveryFailAction)) {
            task = new DeliveryUssdRunnable(taskParam);
        } else {
            if (DeliveryConstant.DeliveryAction.SMS_NORMAL.equals(deliveryFailAction)) {
                taskParam.setSmsType(DeliveryConstant.SmsType.NORMAL);
                task = new DeliverySmsRunnable(taskParam);
            } else if (DeliveryConstant.DeliveryAction.SMS_FLASH.equals(deliveryFailAction)) {
                taskParam.setSmsType(DeliveryConstant.SmsType.FLASH);
                task = new DeliverySmsRunnable(taskParam);
            } else {
                taskParam.setSmsType(DeliveryConstant.SmsType.USSD);
                task = new DeliverySmsRunnable(taskParam);
            }
        }

        try {
            this.deliveryMsgExecutor.submit(task);
        } catch (Exception e) {
            log.error("DeliveryMsgExecutor.submit error", e);
        }

    }

    /**
     * 如果是还原配额，需要把那两个字段更新成空
     * <功能详细描述>
     * @param delivery
     * @see [类、类#方法、类#成员]
     */
    private void setSubIDAndOrderIDNull(ThirdpartyDeliveryWrapper delivery) {
        delivery.setSubscribeID(null);
        delivery.setOrderID(null);

        List<ThirdpartyDeliveryWrapper> deliveryList = new ArrayList<ThirdpartyDeliveryWrapper>();
        deliveryList.add(delivery);

        // ES字段置空
        this.delivery.setSubscribeID(null);
        this.delivery.setOrderID(null);

        // 还原配额之后把账本id和订单id都置空.
        thirdpartyDeliveryMapper.updateSubscribeIdAndOrderId(deliveryList);
    }

    private void updateThirdDeliveryRspTime(ThirdpartyDeliveryTaskWrapper taskWrapper) {
        if (null == deliveryContent || null == deliveryContent.getTaskID()) {
            log.error("deliveryContent or taskID is null, deliveryContent=" + deliveryContent);
            return;
        }

        taskWrapper.setId(deliveryContent.getTaskID());

        thirdpartyDeliveryTaskMapper.updateDeliveryAboutTime(taskWrapper);
    }

}
