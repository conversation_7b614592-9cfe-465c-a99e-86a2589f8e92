var app = angular.module("myApp", ["util.ajax", "service.common", "angularI18n", "top.menu"]);
app.controller('orderController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {

    //初始化页面
    $scope.init = function () {
        //默认企业名称
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.accountName = $.cookie('accountName') || '';
        $scope.isSuperManager = false;
        $scope.operatorID = $.cookie('accountID') || '';
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isAgent = loginRoleType == 'agent';

        //绑定是否体验版，通过函数来改变值
        $scope.isExperience = 0;

        //默认屏显配额是不限，字段值为
        $scope.pingxianisLimit = 0;
        //默认挂机短信是不限，字段值为
        $scope.guaduanisLimit = 0;
        $scope.guaduanisLimitCUCC = 0;
        $scope.guaduanisLimitCTCC = 0;
        //默认挂机彩信是不限，字段值为
        $scope.guacaiisLimit = 0;
        $scope.gjzcisLimit = 0;
        $scope.expireTime = "";
        $scope.effictiveTime = "";

        //下拉框(业务类别)
        $scope.servTypeChoise = [
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            },
            {
                id: 4,
                name: "群发业务"
            }
        ];
        $scope.pxType = 0;
        $scope.gdType = 0;
        $scope.gdTypeCUCC = 0;
        $scope.gdTypeCTCC = 0;
        $scope.gcType = 0;
        $scope.zcType = 0;
        $scope.cxType = 0;
        $scope.queryOrderList();
        $scope.queryProductInfoList()
    };
    $scope.queryProductInfoList = function () {
        var req = {
            "productQuery": {
                "isUse": 1
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1000,
                "isReturnTotal": "1"
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryProductList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.allProductList = result.productList;
                    }
                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    }
    //查询代理商下的订单
    $scope.queryOrderList = function () {
        $scope.orderNameList = [];
        var req = {
            "enterpriseID": $scope.enterpriseID,
            "orderType": "1",
            "isReturnOrderItem": 1,
            "status": 1
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                        $scope.orderList = data.orderList || [];
                        if ($scope.orderList.length > 0) {
                            $scope.selectedOrder = $scope.orderList[0];
                            //生效时间和失效时间
                            $('.input-daterange').datepicker({
                                format: "yyyy-mm-dd",
                                weekStart: 0,
                                language: "zh-CN",
                                autoclose: true
                            });
                            $scope.showPeiE($scope.selectedOrder);
                        } else {
                            $scope.eventType = 1;
                            $scope.tip = "暂无订单，请先到代理商下创建订单";
                            $('#myModal').modal();
                        }

                    }
                    else {
                        $scope.eventType = 0;
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.eventType = 0;
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    $scope.querySubscribeList = function (selectedOrder, isCreate) {

        var subreq = {
            "orderID": selectedOrder.orderCode
        };
        $scope.baoyueRest = Infinity;
        $scope.gdbaoyueRest = Infinity;
        $scope.gcbaoyueRest = Infinity;
        $scope.px_anciRest_cmcc = Infinity;
        $scope.px_anciRest_cucc = Infinity;
        $scope.px_anciRest_ctcc = Infinity;

        $scope.guaduan_anciRest = Infinity;
        $scope.guaduan_anciRestCUCC = Infinity;
        $scope.guaduan_anciRestCTCC = Infinity;
        $scope.guacai_anciRest = Infinity;
        $scope.gjzc_anciRest = Infinity;

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryQuotaByOrder",
            data: JSON.stringify(subreq),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                        $scope.quotaUsedList = data.quotaUsedList;
                        angular.forEach($scope.quotaUsedList, function (item) {
                            switch (item.subServType) {
                                case 1:
                                    //包月剩余量
                                    $scope.baoyueRest = item.remainUseMemberCount / $scope.memberCount;
                                    break;
                                case 2:
                                    //移动包月剩余量
                                    if (item.mobilePlatform === "1") {
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 2) {
                                                $scope.baoyueRest = item.remainUseMemberCount / $scope.memberCount;
                                            }
                                        }
                                    }
                                    if (item.mobilePlatform === "2") {
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 1) {
                                                $scope.px_anciRest_cucc = item.remainUseAmount / $scope.pinxian_amount_cucc;
                                            }
                                        }
                                    }
                                    if (item.mobilePlatform === "3") {
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 1) {
                                                $scope.px_anciRest_ctcc = item.remainUseAmount / $scope.pinxian_amount_ctcc;
                                            }
                                        }
                                    }
                                    break;
                                case 3:
                                	if(item.servType == 4){
                                		if (item.mobilePlatform === "1") {                  //加if判断 20191107
                                            if (item.isLimit === 1) {
                                                //剩余量
                                                $scope.groupSendScreenCMCC_anciRest = item.remainUseAmount / $scope.groupScreenCMCC_amount;
                                            }
                                        }

                                        if (item.mobilePlatform === "2") {                  //联通群发短信
                                            if (item.isLimit === 1) {
                                                //剩余量
                                                $scope.groupSendScreenCUCC_anciRest = item.remainUseAmount / $scope.groupScreenCUCC_amount;
                                            }
                                        }

                                        if (item.mobilePlatform === "3") {                  //联通群发短信
                                            if (item.isLimit === 1) {
                                                //剩余量
                                                $scope.groupSendScreenCTCC_anciRest = item.remainUseAmount / $scope.groupScreenCTCC_amount;
                                            }
                                        }
                                	}
                                	else{
                                    if (item.mobilePlatform === "1") {// 移动屏显按次剩余量
//
                                        if (item.chargeType == 2) {
                                            $scope.baoyueRest = item.remainUseMemberCount / $scope.memberCount;
                                        }
                                        else {
                                            $scope.px_anciRest_cmcc = item.remainUseAmount / $scope.pinxian_amount;
                                        }
                                    }
                                    if (item.isLimit === 1) {
                                        if (item.mobilePlatform === "2") {// 联通屏显按次剩余量
                                            $scope.px_anciRest_cucc = item.remainUseAmount / $scope.pinxian_amount_cucc;
                                        }
                                        if (item.mobilePlatform === "3") {// 电信屏显按次剩余量
                                            $scope.px_anciRest_ctcc = item.remainUseAmount / $scope.pinxian_amount_ctcc;
                                        }
                                    }
                                	}
                                    break;
                                case 10:

                                    // 移动按次按次剩余量
                                    if (item.isLimit === 1) {
                                        if (item.mobilePlatform === "1") {
                                            $scope.px_anciRest_cmcc = item.remainUseAmount / $scope.pinxian_amount;
                                        }
                                    }
                                    break;
                                case 4:
                                    if (item.mobilePlatform === "1") {     //加if判断,移动挂短
                                        if (item.isLimit === 1) {
                                            if (item.chargeType == 2) {
                                                $scope.gdbaoyueRest = item.remainUseMemberCount / $scope.gdmemberCount;
                                            } else {
                                                //挂机短信剩余量
                                                $scope.guaduan_anciRest = item.remainUseAmount / $scope.guaduan_amount;
                                            }
                                        }
                                    }

                                    if (item.mobilePlatform === "2") {        //联通挂短  add 20191107
                                        if (item.isLimit === 1) {
                                            //联通挂机短信剩余量
                                            $scope.guaduan_anciRestCUCC = item.remainUseAmount / $scope.guaduan_amountCUCC;
                                        }
                                    }

                                    if (item.mobilePlatform === "3") {        //电信挂短   add 20191107
                                        if (item.isLimit === 1) {
                                            //电信挂机短信剩余量
                                            $scope.guaduan_anciRestCTCC = item.remainUseAmount / $scope.guaduan_amountCTCC;
                                        }
                                    }

                                    break;
                                case 8:
                                	if (item.servType == 4) {
                                		if (item.isLimit === 1) {
                                            $scope.cx_anciRest = item.remainUseAmount / $scope.cx_amount;
                                            if (item.isExperience == 1) {
                                                $scope.isExperienceLimit = true;
                                            } else {
                                                $scope.isNoExperienceLimit = true;
                                            }
                                        }
									} else {
										if (item.isLimit === 1) {
											if (item.chargeType == 2) {
												$scope.gcbaoyueRest = item.remainUseMemberCount / $scope.gcmemberCount;
											} else {
												//挂机彩信剩余量
												$scope.guacai_anciRest = item.remainUseAmount / $scope.guacai_amount;
											}
										}
									}
                                    break;
                                case 16:
                                	if (item.servType == 2) {
                                		if (item.isLimit === 1) {
                                			//挂机彩信剩余量
											$scope.gjzc_anciRest = item.remainUseAmount / $scope.gjzc_amount;
										}
									} else {

										if (item.isLimit === 1) {
											$scope.zengcai_anciRest = item.remainUseAmount / $scope.zengcai_amount;
											if (item.isExperience == 1) {
												$scope.isExperienceLimit = true;
											} else {
												$scope.isNoExperienceLimit = true;
											}
										}
									}
                                    break;
                                case 17:
                                    if (item.mobilePlatform === "1") {                  //加if判断 20191107
                                        if (item.isLimit === 1) {
                                            //剩余量
                                            $scope.groupSendSMSCMCC_anciRest = item.remainUseAmount / $scope.groupSMSCMCC_amount;
                                        }
                                    }

                                    if (item.mobilePlatform === "2") {                  //联通群发短信
                                        if (item.isLimit === 1) {
                                            //剩余量
                                            $scope.groupSendSMSCUCC_anciRest = item.remainUseAmount / $scope.groupSMSCUCC_amount;
                                        }
                                    }

                                    if (item.mobilePlatform === "3") {                  //联通群发短信
                                        if (item.isLimit === 1) {
                                            //剩余量
                                            $scope.groupSendSMSCTCC_anciRest = item.remainUseAmount / $scope.groupSMSCTCC_amount;
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        })
                        if ($scope.groupSendSMSCMCC_anciRest !== Infinity) {
                            $scope.groupSendSMSCMCC_tip = "剩余配额：" + $scope.groupSendSMSCMCC_anciRest;
                        } else {
                            $scope.groupSendSMSCMCC_tip = "剩余配额：不限";
                        }
                        if ($scope.groupSendSMSCUCC_anciRest !== Infinity) {
                            $scope.groupSendSMSCUCC_tip = "剩余配额：" + $scope.groupSendSMSCUCC_anciRest;
                        } else {
                            $scope.groupSendSMSCUCC_tip = "剩余配额：不限";
                        }
                        if ($scope.groupSendSMSCTCC_anciRest !== Infinity) {
                            $scope.groupSendSMSCTCC_tip = "剩余配额：" + $scope.groupSendSMSCTCC_anciRest;
                        } else {
                            $scope.groupSendSMSCTCC_tip = "剩余配额：不限";
                        }

                        if ($scope.groupSendScreenCMCC_anciRest !== Infinity) {
                            $scope.groupSendScreenCMCC_tip = "剩余配额：" + $scope.groupSendScreenCMCC_anciRest;
                        } else {
                            $scope.groupSendScreenCMCC_tip = "剩余配额：不限";
                        }
                        if ($scope.groupSendScreenCUCC_anciRest !== Infinity) {
                            $scope.groupSendScreenCUCC_tip = "剩余配额：" + $scope.groupSendScreenCUCC_anciRest;
                        } else {
                            $scope.groupSendScreenCUCC_tip = "剩余配额：不限";
                        }
                        if ($scope.groupSendScreenCTCC_anciRest !== Infinity) {
                            $scope.groupSendScreenCTCC_tip = "剩余配额：" + $scope.groupSendScreenCTCC_anciRest;
                        } else {
                            $scope.groupSendScreenCTCC_tip = "剩余配额：不限";
                        }

                        if ($scope.zc_anciRest_tip !== Infinity) {
                            $scope.zc_anciRest_tip = "剩余配额：" + $scope.zengcai_anciRest;
                        } else {
                            $scope.zc_anciRest_tip = "剩余配额：不限";
                        }
                        if ($scope.cx_anciRest_tip !== Infinity) {
                        	$scope.cx_anciRest_tip = "剩余配额：" + $scope.cx_anciRest;
                        } else {
                        	$scope.cx_anciRest_tip = "剩余配额：不限";
                        }
                        if ($scope.px_anciRest_cmcc !== Infinity) {
                            $scope.px_anciRest_tip = "剩余配额：" + $scope.px_anciRest_cmcc;
                        } else {
                            $scope.px_anciRest_tip = "剩余配额：不限";
                        }
                        if ($scope.px_anciRest_cucc !== Infinity) {
                            $scope.px_anciRest_tip_cucc = "剩余配额：" + $scope.px_anciRest_cucc;
                        } else {
                            $scope.px_anciRest_tip_cucc = "剩余配额：不限";
                        }
                        if ($scope.px_anciRest_ctcc !== Infinity) {
                            $scope.px_anciRest_tip_ctcc = "剩余配额：" + $scope.px_anciRest_ctcc;
                        } else {
                            $scope.px_anciRest_tip_ctcc = "剩余配额：不限";
                        }
                        if ($scope.baoyueRest !== Infinity) {
                            $scope.baoyueRest_tip = "剩余配额：" + $scope.baoyueRest;
                        } else {
                            $scope.baoyueRest_tip = "剩余配额：不限";
                        }

                        if ($scope.gdbaoyueRest !== Infinity) {
                            $scope.gdbaoyueRest_tip = "剩余配额：" + $scope.gdbaoyueRest;
                        } else {
                            $scope.gdbaoyueRest_tip = "剩余配额：不限";
                        }

                        if ($scope.gcbaoyueRest !== Infinity) {
                            $scope.gcbaoyueRest_tip = "剩余配额：" + $scope.gcbaoyueRest;
                        } else {
                            $scope.gcbaoyueRest_tip = "剩余配额：不限";
                        }

                        if ($scope.guaduan_anciRest !== Infinity) {
                            $scope.guaduan_anciRest_tip = "剩余配额：" + $scope.guaduan_anciRest;
                        } else {
                            $scope.guaduan_anciRest_tip = "剩余配额：不限";
                        }

                        if ($scope.guaduan_anciRestCUCC !== Infinity) {          //add 20191107
                            $scope.guaduan_anciRest_tipCUCC = "剩余配额：" + $scope.guaduan_anciRestCUCC;
                        } else {
                            $scope.guaduan_anciRest_tipCUCC = "剩余配额：不限";
                        }

                        if ($scope.guaduan_anciRestCTCC !== Infinity) {          //add 20191107
                            $scope.guaduan_anciRest_tipCTCC = "剩余配额：" + $scope.guaduan_anciRestCTCC;
                        } else {
                            $scope.guaduan_anciRest_tipCTCC = "剩余配额：不限";
                        }

                        if ($scope.guacai_anciRest !== Infinity) {
                            $scope.guacai_anciRest_tip = "剩余配额：" + $scope.guacai_anciRest;
                        } else {
                            $scope.guacai_anciRest_tip = "剩余配额：不限";
                        }
                        if ($scope.gjzc_anciRest !== Infinity) {
                        	$scope.gjzc_anciRest_tip = "剩余配额：" + $scope.gjzc_anciRest;
                        } else {
                        	$scope.gjzc_anciRest_tip = "剩余配额：不限";
                        }
                        if (isCreate === "createOrder") {
                            $scope.createOrder();
                        }
                    }
                    else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                });
            }
        });
    };

    /*
     * servType: 业务类型
     * subServType: 子业务类型
     * productType: 产品类型
     * isUse: 是否使用
     * isLimit: 是否限量
     * isExperience: 是否体验版
     * chargeType: 计费类型
     * telco: 运营商类型
     * */
    $scope.filterProductInfoList = function (servType, subServType, productType, isUse, isLimit, isExperience, chargeType, telco) {
        var prod = $scope.allProductList.filter(function (a) {
            return a.servType == servType &&
                a.subServType == subServType &&
                a.productType == productType &&
                a.isUse == isUse &&
                a.isLimit == isLimit &&
                a.isExperience == isExperience &&
                a.chargeType == chargeType &&
                a.reservedsEcpmp.reserved1 == telco;
        });
        return prod;
    };
    $scope.showPeiE = function (selectedOrder) {
        $scope.anci = "";
        $scope.gdanci = "";
        $scope.gdanciCUCC = "";
        $scope.gdanciCTCC = "";
        $scope.gcanci = "";
        $scope.gjzcanci = "";
        $scope.baoyue = "";
        $scope.gdbaoyue = "";
        $scope.gcbaoyue = "";
        $scope.zcanci = "";
        $scope.cxanci = "";

        $scope.pxanci_price = 0;
        $scope.gdanci_price = 0;
        $scope.gdanci_priceCUCC = 0;
        $scope.gdanci_priceCTCC = 0;
        $scope.gcanci_price = 0;
        $scope.gjzcanci_price = 0;
        $scope.baoyue_price = 0;
        $scope.gdbaoyue_price = 0;
        $scope.gcbaoyue_price = 0;
        $scope.zcanci_price = 0;
        $scope.cxanci_price = 0;

        $scope.groupSMSCMCCPrice = 0 ;
        $scope.groupSMSCMCC_price= 0 ;

        $scope.groupSMSCUCCPrice = 0 ;
        $scope.groupSMSCUCC_price= 0 ;

        $scope.groupSMSCTCCPrice = 0 ;
        $scope.groupSMSCTCC_price= 0 ;

        $scope.groupScreenCMCCPrice = 0 ;
        $scope.groupScreenCMCC_price= 0 ;

        $scope.groupScreenCUCCPrice = 0 ;
        $scope.groupScreenCUCC_price= 0 ;

        $scope.groupScreenCTCCPrice = 0 ;
        $scope.groupScreenCTCC_price= 0 ;

        $scope.pxanci_price_cucc = 0;
        $scope.pxanci_price_ctcc = 0;

        $scope.px_noLimit_orderItem = {};
        $scope.px_baoyue_orderItem = {};
        $scope.px_anci_orderItem = {};

        $scope.gd_noLimit_orderItem = {};
        $scope.gd_noLimit_orderItemCUCC = {}
        $scope.gd_noLimit_orderItemCTCC = {}
        $scope.gd_baoyue_orderItem = {};
        $scope.gd_anci_orderItem = {};
        $scope.gd_anci_orderItemCUCC = {};
        $scope.gd_anci_orderItemCTCC = {};

        $scope.gc_noLimit_orderItem = {};
        $scope.gc_baoyue_orderItem = {};
        $scope.gc_anci_orderItem = {};
        $scope.gjzc_anci_orderItem = {};


        $scope.px_anci_orderItem_cucc = {};
        $scope.px_anci_orderItem_ctcc = {};

        $scope.guaduan_noLimit_orderItem = {};
        $scope.guaduan_noLimit_orderItemCUCC = {};
        $scope.guaduan_noLimit_orderItemCTCC = {};
        $scope.guacai_noLimit_orderItem = {};
        $scope.zc_orderItem = {};
        $scope.isZCanci = false;
        $scope.isZCNoLimit = false;
        $scope.zcanci = "";
        $scope.cx_orderItem = {};
        $scope.isCXanci = false;
        $scope.isCXNoLimit = false;
        $scope.cxanci = "";

        // 重置表单校验
        $scope.orderItemDomain.$setPristine();
        $scope.orderItemDomain.$setUntouched();
        $scope.hasPX = false;
        $scope.hasPXCUCC = false;
        $scope.hasPXCTCC = false;
        $scope.hasGD = false;
        $scope.hasGDCUCC = false;
        $scope.hasGDCTCC = false;
        $scope.hasGC = false;
        $scope.hasGJZC = false;
        $scope.hasZC = false;
        $scope.hasCX = false;
        $scope.hasGroupSMSCMCC = false;
        $scope.hasGroupSMSCUCC = false;
        $scope.hasGroupSMSCTCC = false;
        $scope.hasGroupScreenCMCC = false;
        $scope.hasGroupScreenCUCC = false;
        $scope.hasGroupScreenCTCC = false;
        $scope.postPingXianCMCC = false;      //移动屏显
        $scope.postPingXianCUCC = false;      //联通屏显
        $scope.postPingXianCTCC = false;      //电信屏显
        $scope.postGuaDuan = false;           //移动挂短
        $scope.postGuaDuanCUCC = false;       //联通挂短
        $scope.postGuaDuanCTCC = false;       //电信挂短
        $scope.postGuaCai = false;
        $scope.postGJZC = false;
        $scope.isPXLimit = false;
        $scope.isPXanci = false;
        $scope.isPXbaoyue = false;

        $scope.isGDLimit = false;
        $scope.isGDLimitCUCC = false;
        $scope.isGDLimitCTCC = false;
        $scope.isGDanci = false;
        $scope.isGDbaoyue = false;

        $scope.isGCLimit = false;
        $scope.isGCanci = false;
        $scope.isGCbaoyue = false;
        $scope.isGJZCLimit = false;
        $scope.isGJZCanci = false;


        $scope.hasCMCC = false;
        $scope.hasCUCC = false;
        $scope.hasCTCC = false;
        $scope.cmcc = false;
        $scope.cucc = false;
        $scope.ctcc = false;
        $scope.px_anci_over_error = false;
        $scope.gd_anci_over_error = false;
        $scope.gd_anci_over_errorCUCC = false;      //add
        $scope.gd_anci_over_errorCTCC = false;      //add
        $scope.gc_anci_over_error = false;
        $scope.gjzc_anci_over_error = false;
        $scope.px_anci_over_error_cucc = false;
        $scope.px_anci_over_error_ctcc = false;
        $scope.baoyue_over_error = false;
        $scope.gdbaoyue_over_error = false;
        $scope.gcbaoyue_over_error = false;
        
        $scope.postZC = false;
        $scope.postCX = false;
        
        $scope.showGroupSMS = false;
        $scope.showGroupSMSCUCC = false;
        $scope.showGroupSMSCTCC = false;
        
        $scope.showGroupScreen = false;
        $scope.showGroupScreenCUCC = false;
        $scope.showGroupScreenCTCC = false;

        $scope.orderItemList = selectedOrder.orderItemList;
        $scope.servType = selectedOrder.servType;
        $scope.orderCode = selectedOrder.orderCode;
        $scope.isExperience = selectedOrder.isExperience;
        // 切换主订单后改变有限期范围
        $('#start').val("");
        $('#end').val("");
        $scope.effictiveTime = "";
        $scope.expireTime = "";
        $scope.startDate = $scope.formateDate(selectedOrder.effictiveTime);
        $scope.endDate = $scope.formateDate(selectedOrder.expireTime);
        $('#start').datepicker('setStartDate', $scope.startDate);
        $('#start').datepicker('setEndDate', $scope.endDate);
        $('#end').datepicker('setStartDate', $scope.startDate);
        $('#end').datepicker('setEndDate', $scope.endDate);
        angular.forEach($scope.orderItemList, function (item) {
            var product = item.product;

            switch (product.subServType) {
                //主订单屏显配额为包月主叫
                case 1:
                    $scope.hasCMCC = true;
                    $scope.cmcc = true;
                    $scope.hasPX = true;
                    $scope.hasPXCUCC = true;
                    $scope.hasPXCTCC = true;
                    $scope.postPingXianCMCC = true;
                    $scope.isPXbaoyue = true;
                    $scope.pingxianisLimit = 1;
                    $scope.pxType = 2;
                    $scope.productName = product.productName;
                    $scope.baoyue_unitPrice = product.unitPrice || 0;
                    $scope.memberCount = product.memberCount;
                    $scope.baoyue_price = 0;
                    $scope.subServType = 1;
                    $scope.px_baoyue_orderItem = item;
                    break;
                //主订单屏显配额为包月被叫
                case 2:
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        if (product.isLimit === 1) {
                            if (product.chargeType == 2) {
                                $scope.hasCMCC = true;
                                $scope.cmcc = true;
                                $scope.hasPX = true;
                                $scope.postPingXianCMCC = true;
                                $scope.isPXbaoyue = true;
                                $scope.pingxianisLimit = 1;
                                $scope.pxType = 2;
                                $scope.productName = product.productName;
                                $scope.baoyue_unitPrice = product.unitPrice || 0;
                                $scope.memberCount = product.memberCount;
                                $scope.baoyue_price = 0;
                                $scope.subServType = 2;
                                $scope.px_baoyue_orderItem = item;
                            }
                        }
                    }

                    //联通广告屏显订单
                    if (product.reservedsEcpmp.reserved1 === "2") {
                        $scope.hasCUCC = true;
                        $scope.cucc = true;
                        $scope.postPingXianCUCC = true;
                        $scope.pinxian_amount_cucc = item.product.amount;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_cucc = true;
                            $scope.isPXanci_cucc = true;
                            $scope.pxType_cucc = 0;
                            $scope.pingxianisLimit_cucc = 0;
                            $scope.px_noLimit_orderItem_cucc = item;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            $scope.hasPXCUCC = true;
                            $scope.isPXLimit_cucc = false;
                            $scope.isPXanci_cucc = true;
                            $scope.pxType_cucc = 1;
                            $scope.pingxianisLimit_cucc = 1;
                            $scope.px_anci_orderItem_cucc = item;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice_cucc = 0;
                        } else {
                            $scope.PXPrice_cucc = product.unitPrice;
                        }
                    }

                    //电信广告屏显订单
                    if (product.reservedsEcpmp.reserved1 === "3") {
                        $scope.hasCTCC = true;
                        $scope.ctcc = true;
                        $scope.postPingXianCTCC = true;
                        $scope.pinxian_amount_ctcc = item.product.amount;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_ctcc = true;
                            $scope.isPXanci_ctcc = true;
                            $scope.pxType_ctcc = 0;
                            $scope.pingxianisLimit_ctcc = 0;
                            $scope.px_noLimit_orderItem_ctcc = item;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            $scope.hasPXCTCC = true;
                            $scope.isPXLimit_ctcc = false;
                            $scope.isPXanci_ctcc = true;
                            $scope.pxType_ctcc = 1;
                            $scope.pingxianisLimit_ctcc = 1;
                            $scope.px_anci_orderItem_ctcc = item;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice_ctcc = 0;
                        } else {
                            $scope.PXPrice_ctcc = product.unitPrice;
                        }
                    }

                    break;
                case 3:
                	if(product.servType === 4){
                		if (product.reservedsEcpmp.reserved1 === "1") {
                            $scope.showGroupScreen = true;
                            $scope.groupScreenCMCC_orderItem = item;
                            $scope.hasCMCC = true;
                            $scope.cmcc = true;
                            $scope.hasGroupScreenCMCC = true;
                            if (product.isLimit === 1) {
                                $scope.groupScreenCMCC_amount = item.product.amount;
                                $scope.groupScreenCMCCPrice = product.unitPrice;
                                $scope.groupScreenCMCCproductID = product.objectID;
                                $scope.isgroupScreenCMCCNoLimit = false;
                            } else {
                                $scope.isgroupScreenCMCCNoLimit = true;
                            }
                            if ($scope.isExperience === 1) {
                                $scope.groupScreenCMCCPrice = 0;
                            }
                        } else if (product.reservedsEcpmp.reserved1 === "2") {
                            $scope.groupScreenCUCC_orderItem = item;
                            $scope.hasCUCC = true;
                            $scope.cucc = true;
                            $scope.showGroupScreenCUCC = true;
                            $scope.hasGroupScreenCUCC = true;
                            if (product.isLimit == 1) {
                                $scope.groupScreenCUCC_amount = item.product.amount;
                                $scope.groupScreenCUCCPrice = product.unitPrice;
                                $scope.groupScreenCUCCproductID = product.objectID;
                                $scope.isgroupScreenCUCCNoLimit = false;
                            } else {
                                $scope.isgroupScreenCUCCNoLimit = true;
                            }
                        } else if (product.reservedsEcpmp.reserved1 === "3") {
                            $scope.groupScreenCTCC_orderItem = item;
                            $scope.hasCTCC = true;
                            $scope.ctcc = true;
                            $scope.showGroupScreenCTCC = true;
                            $scope.hasGroupScreenCTCC = true;
                            if (product.isLimit == 1) {
                                $scope.groupScreenCTCC_amount = item.product.amount;
                                $scope.groupScreenCTCCPrice = product.unitPrice;
                                $scope.groupScreenCTCCproductID = product.objectID;
                                $scope.isgroupScreenCTCCNoLimit = false;
                            } else {
                                $scope.isgroupScreenCTCCNoLimit = true;
                            }
                        }
                	}
                	else{
                    if (product.reservedsEcpmp.reserved1 === "1") {

                        if (product.chargeType == 2) {               //按人包月
                            $scope.hasCMCC = true;
                            $scope.cmcc = true;
                            $scope.hasPX = true;
                            $scope.postPingXianCMCC = true;
                            $scope.isPXbaoyue = true;
                            $scope.pingxianisLimit = 1;
                            $scope.pxType = 2;
                            $scope.productName = product.productName;
                            $scope.baoyue_unitPrice = product.unitPrice || 0;
                            $scope.memberCount = product.memberCount;
                            $scope.baoyue_price = 0;
                            $scope.subServType = 3;
                            $scope.px_baoyue_orderItem = item;
                        } else {                                    //
                            $scope.hasCMCC = true;
                            $scope.cmcc = true;
                            $scope.hasPX = true;
                            $scope.postPingXianCMCC = true;
                            $scope.pinxian_amount = item.product.amount;

                            //主订单屏显配额为不限
                            if (product.isLimit === 0) {
                                $scope.isPXLimit = true;
                                $scope.isPXanci = false;
                                $scope.pxType = 0;
                                $scope.pingxianisLimit = 0;
                                $scope.px_noLimit_orderItem = item;
                            }
                            //主订单屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.isPXanci = true;
                                $scope.pxType = 1;
                                $scope.pingxianisLimit = 1;
                                $scope.px_anci_orderItem = item;
                            }
                            if ($scope.isExperience === 1) {
                                $scope.PXPrice = 0;
                            } else {
                                $scope.PXPrice = product.unitPrice;
                            }
                        }
                    }
                    if (product.reservedsEcpmp.reserved1 === "2") {
                        $scope.hasCUCC = true;
                        $scope.cucc = true;
                        $scope.hasPXCUCC = true;
                        $scope.postPingXianCUCC = true;
                        $scope.pinxian_amount_cucc = item.product.amount;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_cucc = true;
                            $scope.isPXanci_cucc = true;
                            $scope.pxType_cucc = 0;
                            $scope.pingxianisLimit_cucc = 0;
                            $scope.px_noLimit_orderItem_cucc = item;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            $scope.isPXLimit_cucc = false;
                            $scope.isPXanci_cucc = true;
                            $scope.pxType_cucc = 1;
                            $scope.pingxianisLimit_cucc = 1;
                            $scope.px_anci_orderItem_cucc = item;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice_cucc = 0;
                        } else {
                            $scope.PXPrice_cucc = product.unitPrice;
                        }
                    }
                    if (product.reservedsEcpmp.reserved1 === "3") {
                        $scope.hasCTCC = true;
                        $scope.ctcc = true;
                        $scope.hasPXCTCC = true;
                        $scope.postPingXianCTCC = true;
                        $scope.pinxian_amount_ctcc = item.product.amount;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit_ctcc = true;
                            $scope.isPXanci_ctcc = true;
                            $scope.pxType_ctcc = 0;
                            $scope.pingxianisLimit_ctcc = 0;
                            $scope.px_noLimit_orderItem_ctcc = item;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            $scope.isPXLimit_ctcc = false;
                            $scope.isPXanci_ctcc = true;
                            $scope.pxType_ctcc = 1;
                            $scope.pingxianisLimit_ctcc = 1;
                            $scope.px_anci_orderItem_ctcc = item;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice_ctcc = 0;
                        } else {
                            $scope.PXPrice_ctcc = product.unitPrice;
                        }
                    }
                    }
                    break;
                //主订单包含挂机短信
                case 4:
                    if (product.reservedsEcpmp.reserved1 === "1") {              //移动挂短，加if判断 20191106
                        $scope.hasCMCC = true;
                        $scope.cmcc = true;
                        $scope.hasGD = true;
                        $scope.postGuaDuan = true;
                        //主订单挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimit = true;
                            $scope.gdType = 0;
                            $scope.guaduanisLimit = 0;
                            $scope.guaduan_noLimit_orderItem = item;
                        }
                        //主订单挂机短信为按次
                        if (product.isLimit === 1) {
                            if (product.chargeType == 2) {
                                $scope.hasCMCC = true;
                                $scope.cmcc = true;
                                $scope.hasGD = true;
                                $scope.postGuaDuan = true;
                                $scope.isGDbaoyue = true;
                                $scope.guaduanisLimit = 1;
                                $scope.gdType = 2;
                                $scope.gdproductName = product.productName;
                                $scope.gdbaoyue_unitPrice = product.unitPrice || 0;
                                $scope.gdmemberCount = product.memberCount;
                                $scope.gdbaoyue_price = 0;
                                $scope.subServType = 2;
                                $scope.gd_baoyue_orderItem = item;
                            } else {
                                $scope.guaduan_amount = item.product.amount;
                                $scope.isGDanci = true;
                                $scope.gdType = 1;
                                $scope.guaduanisLimit = 1;
                                $scope.gd_anci_orderItem = item;
                            }
                        }
                        if ($scope.isExperience === 1) {
                            $scope.GDPrice = 0;
                        } else {
                            $scope.GDPrice = product.unitPrice;
                        }
                    }

                    if (product.reservedsEcpmp.reserved1 === "2") {     //联通挂短
                        $scope.hasCUCC = true;
                        $scope.cucc = true;
                        $scope.hasGDCUCC = true;
                        $scope.postGuaDuanCUCC = true;

                        //主订单挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimitCUCC = true;
                            $scope.gdTypeCUCC = 0;
                            $scope.guaduanisLimitCUCC = 0;
                            $scope.guaduan_noLimit_orderItemCUCC = item;
                        }

                        //主订单挂机短信为按次
                        if (product.isLimit === 1) {
                            $scope.guaduan_amountCUCC = item.product.amount;
                            $scope.isGDanciCUCC = true;
                            $scope.gdTypeCUCC = 1;
                            $scope.guaduanisLimitCUCC = 1;
                            $scope.gd_anci_orderItemCUCC = item;
                        }

                        if ($scope.isExperience === 1) {
                            $scope.GDPriceCUCC = 0;
                        } else {
                            $scope.GDPriceCUCC = product.unitPrice;
                        }
                    }

                    if (product.reservedsEcpmp.reserved1 === "3") {     //电信挂短
                        $scope.hasCTCC = true;
                        $scope.ctcc = true;
                        $scope.hasGDCTCC = true;
                        $scope.postGuaDuanCTCC = true;

                        //主订单挂机短信为不限
                        if (product.isLimit === 0) {
                            $scope.isGDLimitCTCC = true;
                            $scope.gdTypeCTCC = 0;
                            $scope.guaduanisLimitCTCC = 0;
                            $scope.guaduan_noLimit_orderItemCTCC = item;
                        }

                        //主订单挂机短信为按次
                        if (product.isLimit === 1) {
                            $scope.guaduan_amountCTCC = item.product.amount;
                            $scope.isGDanciCTCC = true;
                            $scope.gdTypeCTCC = 1;
                            $scope.guaduanisLimitCTCC = 1;
                            $scope.gd_anci_orderItemCTCC = item;
                        }

                        if ($scope.isExperience === 1) {
                            $scope.GDPriceCTCC = 0;
                        } else {
                            $scope.GDPriceCTCC = product.unitPrice;
                        }
                    }
                    break;
                case 8:
                	if (product.servType == 4) {
                		//彩信
                        if (product.reservedsEcpmp.reserved1 === "1") {
                            $scope.cmcc = true;
                            $scope.hasCMCC = true;
                            $scope.hasCX = true;
                            $scope.postCX = true;
                            $scope.isNoExperienceLimit = false;
                            $scope.isExperienceLimit = false;
                            $scope.cx_orderItem = item;
                            //主订单屏显配额为按次
                            if (product.isLimit === 1) {
                                $scope.isCXanci = true;
                                $scope.cx_amount = item.product.amount;
                                if (item.isExperience == 1) {
                                    $scope.isExperienceLimit = true;
                                } else {
                                    $scope.isNoExperienceLimit = true;
                                }
                            } else {
                                $scope.isCXNoLimit = true;
                            }
                            if ($scope.isExperience === 1) {
                                $scope.cxPrice = 0;
                            } else {
                                $scope.cxPrice = product.unitPrice;
                            }
                        }
					} else {
						$scope.hasCMCC = true;
						$scope.cmcc = true;
						$scope.hasGC = true;
						$scope.postGuaCai = true;
						//主订单挂机彩信为不限
						if (product.isLimit === 0) {
							$scope.isGCLimit = true;
							$scope.guacaiType = 0;
							$scope.gcType = 0;
							$scope.guacaiisLimit = 0;
							$scope.guacai_noLimit_orderItem = item;
						}
						//主订单挂机彩信为按次
						if (product.isLimit === 1) {
							if (product.chargeType == 2) {
								$scope.hasCMCC = true;
								$scope.cmcc = true;
								$scope.hasGC = true;
								$scope.postGuaCai = true;
								$scope.isGCbaoyue = true;
								$scope.guacaiisLimit = 1;
								$scope.gcType = 2;
								$scope.gcproductName = product.productName;
								$scope.gcbaoyue_unitPrice = product.unitPrice || 0;
								$scope.gcmemberCount = product.memberCount;
								$scope.gcbaoyue_price = 0;
								$scope.subServType = 2;
								$scope.gc_baoyue_orderItem = item;
								
							} else {
								$scope.guacai_amount = item.product.amount;
								$scope.isGCanci = true;
								$scope.gcType = 1;
								$scope.guacaiisLimit = 1;
								$scope.gc_anci_orderItem = item;
							}
						}
						if ($scope.isExperience === 1) {
							$scope.GCPrice = 0;
						} else {
							$scope.GCPrice = product.unitPrice;
						}
					}
                    break;
                case 10:
                    //移动广告订单
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        $scope.hasCMCC = true;
                        $scope.cmcc = true;
                        $scope.hasPX = true;
                        $scope.postPingXianCMCC = true;
                        $scope.pinxian_amount = item.product.amount;
                        //主订单屏显配额为不限
                        if (product.isLimit === 0) {
                            $scope.isPXLimit = true;
                            $scope.isPXanci = true;
                            $scope.pxType = 0;
                            $scope.pingxianisLimit = 0;
                            $scope.px_noLimit_orderItem = item;
                        }
                        //主订单屏显配额为按次
                        if (product.isLimit === 1) {
                            $scope.isPXanci = true;
                            $scope.pxType = 1;
                            $scope.pingxianisLimit = 1;
                            $scope.px_anci_orderItem = item;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.PXPrice = 0;
                        } else {
                            $scope.PXPrice = product.unitPrice;
                        }
                    }

                    break;
                case 16:
                	if (product.servType == 2) {
                		$scope.hasCMCC = true;
						$scope.cmcc = true;
						$scope.hasGJZC = true;
						$scope.postGJZC = true;
						//主订单挂机彩信为不限
						if (product.isLimit === 0) {
							$scope.isGJZCLimit = true;
							$scope.gjzcType = 0;
							$scope.gjzcisLimit = 0;
							$scope.gjzc_anci_orderItem = item;
						}
						//主订单挂机彩信为按次
						if (product.isLimit === 1) {
							$scope.gjzc_amount = item.product.amount;
							$scope.isGJZCanci = true;
							$scope.gjzcType = 1;
							$scope.gjzcisLimit = 1;
							$scope.gjzc_anci_orderItem = item;
						}
						if ($scope.isExperience === 1) {
							$scope.GJZCPrice = 0;
						} else {
							$scope.GJZCPrice = product.unitPrice;
						}
					} else {
						//增彩
						if (product.reservedsEcpmp.reserved1 === "1") {
							$scope.cmcc = true;
							$scope.hasCMCC = true;
							$scope.hasZC = true;
							$scope.postZC = true;
							$scope.isNoExperienceLimit = false;
							$scope.isExperienceLimit = false;
							$scope.zc_orderItem = item;
							//主订单屏显配额为按次
							if (product.isLimit === 1) {
								$scope.isZCanci = true;
								$scope.zengcai_amount = item.product.amount;
								if (item.isExperience == 1) {
									$scope.isExperienceLimit = true;
								} else {
									$scope.isNoExperienceLimit = true;
								}
							} else {
								$scope.isZCNoLimit = true;
							}
							if ($scope.isExperience === 1) {
								$scope.ZCPrice = 0;
							} else {
								$scope.ZCPrice = product.unitPrice;
							}
						}
					}
                    break;
                case 17:// 短信群发
                    if (product.reservedsEcpmp.reserved1 === "1") {
                        $scope.showGroupSMS = true;
                        $scope.groupSMSCMCC_orderItem = item;
                        $scope.hasCMCC = true;
                        $scope.cmcc = true;
                        $scope.hasGroupSMSCMCC = true;
                        if (product.isLimit === 1) {
                            $scope.groupSMSCMCC_amount = item.product.amount;
                            $scope.groupSMSCMCCPrice = product.unitPrice;
                            $scope.groupSMSCMCCproductID = product.objectID;
                            $scope.isgroupSMSCMCCNoLimit = false;
                        } else {
                            $scope.isgroupSMSCMCCNoLimit = true;
                        }
                        if ($scope.isExperience === 1) {
                            $scope.groupSMSCMCCPrice = 0;
                        }
                    } else if (product.reservedsEcpmp.reserved1 === "2") {
                        $scope.groupSMSCUCC_orderItem = item;
                        $scope.hasCUCC = true;
                        $scope.cucc = true;
                        $scope.showGroupSMSCUCC = true;
                        $scope.hasGroupSMSCUCC = true;
                        if (product.isLimit == 1) {
                            $scope.groupSMSCUCC_amount = item.product.amount;
                            $scope.groupSMSCUCCPrice = product.unitPrice;
                            $scope.groupSMSCUCCproductID = product.objectID;
                            $scope.isgroupSMSCUCCNoLimit = false;
                        } else {
                            $scope.isgroupSMSCUCCNoLimit = true;
                        }
                    } else if (product.reservedsEcpmp.reserved1 === "3") {
                        $scope.groupSMSCTCC_orderItem = item;
                        $scope.hasCTCC = true;
                        $scope.ctcc = true;
                        $scope.showGroupSMSCTCC = true;
                        $scope.hasGroupSMSCTCC = true;
                        if (product.isLimit == 1) {
                            $scope.groupSMSCTCC_amount = item.product.amount;
                            $scope.groupSMSCTCCPrice = product.unitPrice;
                            $scope.groupSMSCTCCproductID = product.objectID;
                            $scope.isgroupSMSCTCCNoLimit = false;
                        } else {
                            $scope.isgroupSMSCTCCNoLimit = true;
                        }
                    }


                    break;
                default:
                    break;
            }
        });
        $scope.querySubscribeList($scope.selectedOrder);
    };

    //给弹出框确认按钮绑定不同事件
    $scope.sure = function (eventType) {
        if (eventType === 1) {
            window.location.href = "../quotaList/quotaList.html"
        }
    };

    $scope.changePingXianType = function (i) {
        if ($scope.pxType !== i) {
            $scope.anci = "";
            $scope.orderItemDomain.anci.$setPristine();
            $scope.orderItemDomain.anci.$setUntouched();
            $scope.px_anci_over_error = false;
            $scope.baoyue_over_error = false;
            $scope.pxanci_price = 0;
            $scope.baoyue_price = 0;
            if (i == 0) {
                //不限
                $scope.pingxianisLimit = 0;
                if (!$scope.px_noLimit_orderItem.objectID) {
                    $scope.px_noLimit_orderItem.product = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 0, $scope.isExperience, 1, 1)[0];
                    $scope.pinxian_amount = $scope.px_noLimit_orderItem.product.amount;
                    $scope.px_noLimit_orderItem.productID = $scope.px_noLimit_orderItem.product.objectID
                }
            }
            if (i == 1) {
                //按次
                $scope.pingxianisLimit = 1;
                if (!$scope.px_anci_orderItem.objectID) {

                    if ($scope.servType == 3) {
                        $scope.px_anci_orderItem.product = $scope.filterProductInfoList($scope.servType, 10, 0, 1, 1, $scope.isExperience, 1, 1)[0];
                    } else {
                        $scope.px_anci_orderItem.product = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 1, $scope.isExperience, 1, 1)[0];
                    }


                    $scope.px_anci_orderItem.productID = $scope.px_anci_orderItem.product.objectID;
                    $scope.pinxian_amount = $scope.px_anci_orderItem.product.amount;
                    if ($scope.selectedOrder.isExperience === 1) {
                        $scope.PXPrice = 0
                    } else {
                        $scope.PXPrice = $scope.px_anci_orderItem.product.unitPrice
                    }
                }
            }
        }
        $scope.pxType = i;
    };
    $scope.changePingXianTypeCUCC = function (i) {
        if ($scope.pxType_cucc !== i) {
            $scope.anci_cucc = "";
            $scope.orderItemDomain.anci_cucc.$setPristine();
            $scope.orderItemDomain.anci_cucc.$setUntouched();
            $scope.px_anci_over_error_cucc = false;
            $scope.baoyue_over_error_cucc = false;
            $scope.pxanci_price_cucc = 0;
            if (i == 0) {
                //不限
                $scope.pingxianisLimit_cucc = 0;
                if (!$scope.px_noLimit_orderItem_cucc.objectID) {
                    $scope.px_noLimit_orderItem_cucc.product = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 0, $scope.isExperience, 1, 2)[0];
                    $scope.pinxian_amount_cucc = $scope.px_noLimit_orderItem_cucc.product.amount;
                    $scope.px_noLimit_orderItem_cucc.productID = $scope.px_noLimit_orderItem_cucc.product.objectID
                }
            }
            if (i == 1) {
                //按次
                $scope.pingxianisLimit_cucc = 1;
                if (!$scope.px_anci_orderItem_cucc.objectID) {

                    if ($scope.servType == 3) {
                        $scope.px_anci_orderItem_cucc.product = $scope.filterProductInfoList($scope.servType, 2, 0, 1, 1, $scope.isExperience, 1, 2)[0];

                    } else {
                        $scope.px_anci_orderItem_cucc.product = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 1, $scope.isExperience, 1, 2)[0];

                    }

                    $scope.px_anci_orderItem_cucc.productID = $scope.px_anci_orderItem_cucc.product.objectID;
                    $scope.pinxian_amount_cucc = $scope.px_anci_orderItem_cucc.product.amount;
                    if ($scope.selectedOrder.isExperience === 1) {
                        $scope.PXPrice_cucc = 0
                    } else {
                        $scope.PXPrice_cucc = $scope.px_anci_orderItem_cucc.product.unitPrice
                    }
                }
            }
        }
        $scope.pxType_cucc = i;
    };
    $scope.changePingXianTypeCTCC = function (i) {
        if ($scope.pxType_ctcc !== i) {
            $scope.anci_ctcc = "";
            $scope.orderItemDomain.anci_ctcc.$setPristine();
            $scope.orderItemDomain.anci_ctcc.$setUntouched();
            $scope.px_anci_over_error_ctcc = false;
            $scope.baoyue_over_error_ctcc = false;
            $scope.pxanci_price_ctcc = 0;
            if (i == 0) {
                //不限
                $scope.pingxianisLimit_ctcc = 0;
                if (!$scope.px_noLimit_orderItem_ctcc.objectID) {
                    $scope.px_noLimit_orderItem_ctcc.product = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 0, $scope.isExperience, 1, 3)[0];
                    $scope.pinxian_amount_ctcc = $scope.px_noLimit_orderItem_ctcc.product.amount;
                    $scope.px_noLimit_orderItem_ctcc.productID = $scope.px_noLimit_orderItem_ctcc.product.objectID
                }
            }
            if (i == 1) {
                //按次
                $scope.pingxianisLimit_ctcc = 1;
                if (!$scope.px_anci_orderItem_ctcc.objectID) {
                    if ($scope.servType == 3) {
                        $scope.px_anci_orderItem_ctcc.product = $scope.filterProductInfoList($scope.servType, 2, 0, 1, 1, $scope.isExperience, 1, 3)[0];
                    } else {
                        $scope.px_anci_orderItem_ctcc.product = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 1, $scope.isExperience, 1, 3)[0];
                    }
                    $scope.px_anci_orderItem_ctcc.productID = $scope.px_anci_orderItem_ctcc.product.objectID;
                    $scope.pinxian_amount_ctcc = $scope.px_anci_orderItem_ctcc.product.amount;
                    if ($scope.selectedOrder.isExperience === 1) {
                        $scope.PXPrice_ctcc = 0
                    } else {
                        $scope.PXPrice_ctcc = $scope.px_anci_orderItem_ctcc.product.unitPrice
                    }
                }
            }
        }
        $scope.pxType_ctcc = i;
    };

    $scope.changeGuaDuanType = function (i) {
        if ($scope.gdType !== i) {
            $scope.gdanci = "";
            $scope.orderItemDomain.gdanci.$setPristine();
            $scope.orderItemDomain.gdanci.$setUntouched();
            $scope.gd_anci_over_error = false;
            $scope.gdbaoyue_over_error = false;
            $scope.gdanci_price = 0;
            $scope.gdbaoyue_price = 0;
            if (i == 0) {
                //不限
                $scope.guaduanisLimit = 0;
                if (!$scope.gd_noLimit_orderItem.objectID) {
                    $scope.gd_noLimit_orderItem.product = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 0, $scope.isExperience, 1, 1)[0];
                    $scope.guaduan_amount = $scope.gd_noLimit_orderItem.product.amount;
                    $scope.gd_noLimit_orderItem.productID = $scope.gd_noLimit_orderItem.product.objectID
                }
            }
            if (i == 1) {
                //按次
                $scope.guaduanisLimit = 1;
                if (!$scope.gd_anci_orderItem.objectID) {
                    $scope.gd_anci_orderItem.product = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 1, $scope.isExperience, 1, 1)[0];
                    $scope.gd_anci_orderItem.productID = $scope.gd_anci_orderItem.product.objectID;
                    $scope.guaduan_amount = $scope.gd_anci_orderItem.product.amount;
                    if ($scope.selectedOrder.isExperience === 1) {
                        $scope.GDPrice = 0
                    } else {
                        $scope.GDPrice = $scope.gd_anci_orderItem.product.unitPrice
                    }
                }
            }
        }
        $scope.gdType = i;
    };

    //add 20191106 start
    $scope.changeGuaDuanTypeCUCC = function (i) {
        if ($scope.gdTypeCUCC !== i) {
            $scope.gdanciCUCC = "";
            $scope.orderItemDomain.gdanciCUCC.$setPristine();
            $scope.orderItemDomain.gdanciCUCC.$setUntouched();
            $scope.gd_anci_over_errorCUCC = false;
            $scope.gdanci_priceCUCC = 0;
            if (i == 0) {
                //不限
                $scope.guaduanisLimitCUCC = 0;
                if (!$scope.gd_noLimit_orderItemCUCC.objectID) {
                    $scope.gd_noLimit_orderItemCUCC.product = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 0, $scope.isExperience, 1, 2)[0];
                    $scope.guaduan_amountCUCC = $scope.gd_noLimit_orderItemCUCC.product.amount;
                    $scope.gd_noLimit_orderItemCUCC.productID = $scope.gd_noLimit_orderItemCUCC.product.objectID
                }
            }
            if (i == 1) {
                //按次
                $scope.guaduanisLimitCUCC = 1;
                if (!$scope.gd_anci_orderItemCUCC.objectID) {
                    $scope.gd_anci_orderItemCUCC.product = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 1, $scope.isExperience, 1, 2)[0];
                    $scope.gd_anci_orderItemCUCC.productID = $scope.gd_anci_orderItemCUCC.product.objectID;
                    $scope.guaduan_amountCUCC = $scope.gd_anci_orderItemCUCC.product.amount;
                    if ($scope.selectedOrder.isExperience === 1) {
                        $scope.GDPriceCUCC = 0
                    } else {
                        $scope.GDPriceCUCC = $scope.gd_anci_orderItemCUCC.product.unitPrice
                    }
                }
            }
        }
        $scope.gdTypeCUCC = i;
    };
    //add 20191106 end

    //add 20191106 start
    $scope.changeGuaDuanTypeCTCC = function (i) {
        if ($scope.gdTypeCTCC !== i) {
            $scope.gdanciCTCC = "";
            $scope.orderItemDomain.gdanciCTCC.$setPristine();
            $scope.orderItemDomain.gdanciCTCC.$setUntouched();
            $scope.gd_anci_over_errorCTCC = false;
            $scope.gdanci_priceCTCC = 0;
            if (i == 0) {
                //不限
                $scope.guaduanisLimitCTCC = 0;
                if (!$scope.gd_noLimit_orderItemCTCC.objectID) {
                    $scope.gd_noLimit_orderItemCTCC.product = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 0, $scope.isExperience, 1, 2)[0];
                    $scope.guaduan_amountCTCC = $scope.gd_noLimit_orderItemCTCC.product.amount;
                    $scope.gd_noLimit_orderItemCTCC.productID = $scope.gd_noLimit_orderItemCTCC.product.objectID
                }
            }
            if (i == 1) {
                //按次
                $scope.guaduanisLimitCTCC = 1;
                if (!$scope.gd_anci_orderItemCTCC.objectID) {
                    $scope.gd_anci_orderItemCTCC.product = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 1, $scope.isExperience, 1, 2)[0];
                    $scope.gd_anci_orderItemCTCC.productID = $scope.gd_anci_orderItemCTCC.product.objectID;
                    $scope.guaduan_amountCTCC = $scope.gd_anci_orderItemCTCC.product.amount;
                    if ($scope.selectedOrder.isExperience === 1) {
                        $scope.GDPriceCTCC = 0
                    } else {
                        $scope.GDPriceCTCC = $scope.gd_anci_orderItemCTCC.product.unitPrice
                    }
                }
            }
        }
        $scope.gdTypeCTCC = i;
    };
    //add 20191106 end

    $scope.changeGuaCaiType = function (i) {

        if ($scope.gcType !== i) {
            $scope.gcanci = "";
            $scope.orderItemDomain.gcanci.$setPristine();
            $scope.orderItemDomain.gcanci.$setUntouched();
            $scope.gc_anci_over_error = false;
            $scope.gcbaoyue_over_error = false;
            $scope.gcanci_price = 0;
            $scope.gcbaoyue_price = 0;
            if (i == 0) {
                //不限
                $scope.guacaiisLimit = 0;
                if (!$scope.gc_noLimit_orderItem.objectID) {
                    $scope.gc_noLimit_orderItem.product = $scope.filterProductInfoList($scope.servType, 8, 0, 1, 0, $scope.isExperience, 1, 1)[0];
                    $scope.guacai_amount = $scope.gc_noLimit_orderItem.product.amount;
                    $scope.gc_noLimit_orderItem.productID = $scope.gc_noLimit_orderItem.product.objectID
                }
            }
            if (i == 1) {
                //按次
                $scope.guacaiisLimit = 1;
                if (!$scope.gc_anci_orderItem.objectID) {
                    $scope.gc_anci_orderItem.product = $scope.filterProductInfoList($scope.servType, 8, 0, 1, 1, $scope.isExperience, 1, 1)[0];
                    $scope.gc_anci_orderItem.productID = $scope.gc_anci_orderItem.product.objectID;
                    $scope.guacai_amount = $scope.gc_anci_orderItem.product.amount;
                    if ($scope.selectedOrder.isExperience === 1) {
                        $scope.GCPrice = 0
                    } else {
                        $scope.GCPrice = $scope.gc_anci_orderItem.product.unitPrice
                    }
                }
            }
        }
        $scope.gcType = i;
    };
    $scope.changeGJZCType = function (i) {
    	
    	if ($scope.gjzcType !== i) {
    		$scope.gjzcanci = "";
    		$scope.orderItemDomain.gjzcanci.$setPristine();
    		$scope.orderItemDomain.gjzcanci.$setUntouched();
    		$scope.gjzc_anci_over_error = false;
    		$scope.gjzcanci_price = 0;
    		if (i == 0) {
    			//不限
    			$scope.gjzcisLimit = 0;
    			if (!$scope.gjzc_anci_orderItem.objectID) {
    				$scope.gjzc_anci_orderItem.product = $scope.filterProductInfoList($scope.servType, 8, 0, 1, 0, $scope.isExperience, 1, 1)[0];
    				$scope.gjzc_amount = $scope.gjzc_anci_orderItem.product.amount;
    				$scope.gjzc_anci_orderItem.productID = $scope.gjzc_anci_orderItem.product.objectID
    			}
    		}
    		if (i == 1) {
    			//按次
    			$scope.gjzcisLimit = 1;
    			if (!$scope.gjzc_anci_orderItem.objectID) {
    				$scope.gjzc_anci_orderItem.product = $scope.filterProductInfoList($scope.servType, 8, 0, 1, 1, $scope.isExperience, 1, 1)[0];
    				$scope.gjzc_anci_orderItem.productID = $scope.gjzc_anci_orderItem.product.objectID;
    				$scope.gjzc_amount = $scope.gjzc_anci_orderItem.product.amount;
    				if ($scope.selectedOrder.isExperience === 1) {
    					$scope.GJZCPrice = 0
    				} else {
    					$scope.GJZCPrice = $scope.gjzc_anci_orderItem.product.unitPrice
    				}
    			}
    		}
    	}
    	$scope.gjzcType = i;
    };
    $scope.chooseCMCC = function () {
        $scope.cmcc = !$scope.cmcc;
    };
    $scope.chooseCUCC = function () {
        $scope.cucc = !$scope.cucc;
    };
    $scope.chooseCTCC = function () {
        $scope.ctcc = !$scope.ctcc;
    };

    //是否展示屏显的配置
    $scope.showPingXian = function () {
        $scope.postPingXianCMCC = !$scope.postPingXianCMCC;
        $scope.anci = "";
        $scope.baoyue = "";
        $scope.pxanci_price = 0;
        $scope.px_anci_over_error = false;
        $scope.orderItemDomain.anci.$setPristine();
        $scope.orderItemDomain.baoyue.$setPristine();
    }

    $scope.showZC = function () {
        $scope.postZC = !$scope.postZC;
        $scope.zcanci = "";
        $scope.ZCPrice = 0;
        $scope.zc_anci_over_error = false;
        $scope.orderItemDomain.zcanci.$setPristine();
    }
    $scope.showCX = function () {
    	$scope.postCX = !$scope.postCX;
    	$scope.cxanci = "";
    	$scope.cxPrice = 0;
    	$scope.cx_anci_over_error = false;
    	$scope.orderItemDomain.cxanci.$setPristine();
    }

    $scope.showPingXianCUCC = function () {
        $scope.postPingXianCUCC = !$scope.postPingXianCUCC;
        $scope.anci_cucc = "";
        $scope.pxanci_price_cucc = 0;
        $scope.px_anci_over_error_cucc = false;
        $scope.orderItemDomain.anci_cucc.$setPristine();
    }

    $scope.showPingXianCTCC = function () {
        $scope.postPingXianCTCC = !$scope.postPingXianCTCC;
        $scope.anci_ctcc = "";
        $scope.pxanci_price_ctcc = 0;
        $scope.px_anci_over_error_ctcc = false;
        $scope.orderItemDomain.anci_ctcc.$setPristine();
    }

    //是否展示挂短的配置
    $scope.showGuaDuan = function () {
        $scope.postGuaDuan = !$scope.postGuaDuan;
        $scope.gdanci = "";
        $scope.gdbaoyue = "";
        $scope.gdanci_price = 0;
        $scope.gd_anci_over_error = false;
        $scope.orderItemDomain.gdanci.$setPristine();
        $scope.orderItemDomain.gdbaoyue.$setPristine();

    }

    //联通挂短  2091106
    $scope.showGuaDuanCUCC = function () {
        $scope.postGuaDuanCUCC = !$scope.postGuaDuanCUCC;               //联通挂短判断标识
        $scope.gdanciCUCC = "";                                         //联通挂短按次
        $scope.gdanci_priceCUCC = 0;                                    //联通挂短按次单价
        $scope.gd_anci_over_errorCUCC = false;
        $scope.orderItemDomain.gdanciCUCC.$setPristine();

    }

    //电信挂短  2091106
    $scope.showGuaDuanCTCC = function () {
        $scope.postGuaDuanCTCC = !$scope.postGuaDuanCTCC;               //电信挂短判断标识
        $scope.gdanciCTCC = "";                                         //电信挂短按次
        $scope.gdanci_priceCTCC = 0;                                    //电信挂短按次单价
        $scope.gd_anci_over_errorCTCC = false;
        $scope.orderItemDomain.gdanciCTCC.$setPristine();

    }

    //是否展示挂彩的配置
    $scope.showGuaCai = function () {
        $scope.postGuaCai = !$scope.postGuaCai;
        $scope.gcanci = "";
        $scope.gcbaoyue = "";
        $scope.gcanci_price = 0;
        $scope.gc_anci_over_error = false;
        $scope.orderItemDomain.gcanci.$setPristine();
        $scope.orderItemDomain.gcbaoyue.$setPristine();
    };
    //是否展示挂机增彩的配置
    $scope.showGJZC = function () {
    	$scope.postGJZC = !$scope.postGJZC;
    	$scope.gjzcanci = "";
    	$scope.gjzcanci_price = 0;
    	$scope.gjzc_anci_over_error = false;
    	$scope.orderItemDomain.gjzcanci.$setPristine();
    };

    //解决小数相乘时  丢失精度的问题
    $scope.accMul = function (arg1, arg2, arg3, arg4) {

        if ("undefined" == typeof arg1) {
            arg1 = 0;
        }
        if ("undefined" == typeof arg2) {
            arg2 = 0;
        }
        if ("undefined" == typeof arg3) {
            arg3 = 0;
        }
        var m = 0;
        var s1 = arg1.toString();
        var s2 = arg2.toString();
        var s3 = arg3.toString();
        try {
            m += s1.split(".")[1].length
        } catch (e) {
        }
        try {
            m += s2.split(".")[1].length
        } catch (e) {
        }
        try {
            m += s3.split(".")[1].length
        } catch (e) {
        }

        if (arg4 === "pxanci") {
            $scope.pxanci_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.px_anciRest_cmcc !== Infinity && arg2 > $scope.px_anciRest_cmcc) {
                $scope.px_anci_over_error = true;
            } else {
                $scope.px_anci_over_error = false;
            }
        }

        if (arg4 === "baoyue") {
            $scope.baoyue_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.baoyueRest !== Infinity && arg2 > $scope.baoyueRest) {
                $scope.baoyue_over_error = true;
            } else {
                $scope.baoyue_over_error = false;
            }
        }
        if (arg4 === "gdanci") {

            $scope.gdanci_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);

            //主订单不是无限的前提下  判断剩余配额
            if ($scope.guaduan_anciRest !== Infinity && arg2 > $scope.guaduan_anciRest) {
                $scope.gd_anci_over_error = true;
            } else {
                $scope.gd_anci_over_error = false;
            }
        }
        if (arg4 === "gdanciCUCC") {                //add 20191107

            $scope.gdanci_priceCUCC = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);

            //主订单不是无限的前提下  判断剩余配额
            if ($scope.guaduan_anciRestCUCC !== Infinity && arg2 > $scope.guaduan_anciRestCUCC) {
                $scope.gd_anci_over_errorCUCC = true;
            } else {
                $scope.gd_anci_over_errorCUCC = false;
            }
        }
        if (arg4 === "gdanciCTCC") {                //add 20191107

            $scope.gdanci_priceCTCC = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);

            //主订单不是无限的前提下  判断剩余配额
            if ($scope.guaduan_anciRestCTCC !== Infinity && arg2 > $scope.guaduan_anciRestCTCC) {
                $scope.gd_anci_over_errorCTCC = true;
            } else {
                $scope.gd_anci_over_errorCTCC = false;
            }
        }
        if (arg4 === "zcanci") {
            $scope.zcanci_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.zengcai_anciRest !== Infinity && arg2 > $scope.zengcai_anciRest) {
                $scope.zc_anci_over_error = true;
            } else {
                $scope.zc_anci_over_error = false;
            }
        }
        if (arg4 === "cxanci") {
        	$scope.cxanci_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
        	//主订单不是无限的前提下  判断剩余配额
        	if ($scope.cx_anciRest !== Infinity && arg2 > $scope.cx_anciRest) {
        		$scope.cx_anci_over_error = true;
        	} else {
        		$scope.cx_anci_over_error = false;
        	}
        }
        if (arg4 === "gsSMSCMCC") {
            $scope.groupSMSCMCC_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.groupSendSMSCMCC_anciRest !== Infinity && arg2 > $scope.groupSendSMSCMCC_anciRest) {
                $scope.groupSendSMSCMCC_over_error = true;
            } else {
                $scope.groupSendSMSCMCC_over_error = false;
            }
            return $scope.groupSMSCMCC_price;
        }
        if (arg4 === "gsSMSCUCC") {
            $scope.groupSMSCUCC_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.groupSendSMSCUCC_anciRest !== Infinity && arg2 > $scope.groupSendSMSCUCC_anciRest) {
                $scope.groupSendSMSCUCC_over_error = true;
            } else {
                $scope.groupSendSMSCUCC_over_error = false;
            }
            return $scope.groupSMSCUCC_price;
        }
        if (arg4 === "gsSMSCTCC") {
            $scope.groupSMSCTCC_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.groupSendSMSCTCC_anciRest !== Infinity && arg2 > $scope.groupSendSMSCTCC_anciRest) {
                $scope.groupSendSMSCTCC_over_error = true;
            } else {
                $scope.groupSendSMSCTCC_over_error = false;
            }
            return $scope.groupSMSCTCC_price;
        }
        
        if (arg4 === "gsScreenCMCC") {
            $scope.groupScreenCMCC_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.groupSendScreenCMCC_anciRest !== Infinity && arg2 > $scope.groupSendScreenCMCC_anciRest) {
                $scope.groupSendScreenCMCC_over_error = true;
            } else {
                $scope.groupSendScreenCMCC_over_error = false;
            }
            return $scope.groupScreenCMCC_price;
        }
        if (arg4 === "gsScreenCUCC") {
            $scope.groupScreenCUCC_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.groupSendScreenCUCC_anciRest !== Infinity && arg2 > $scope.groupSendScreenCUCC_anciRest) {
                $scope.groupSendScreenCUCC_over_error = true;
            } else {
                $scope.groupSendScreenCUCC_over_error = false;
            }
            return $scope.groupScreenCUCC_price;
        }
        if (arg4 === "gsScreenCTCC") {
            $scope.groupScreenCTCC_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.groupSendScreenCTCC_anciRest !== Infinity && arg2 > $scope.groupSendScreenCTCC_anciRest) {
                $scope.groupSendScreenCTCC_over_error = true;
            } else {
                $scope.groupSendScreenCTCC_over_error = false;
            }
            return $scope.groupScreenCTCC_price;
        }

        if (arg4 === "gdbaoyue") {
            $scope.gdbaoyue_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.gdbaoyueRest !== Infinity && arg2 > $scope.gdbaoyueRest) {
                $scope.gdbaoyue_over_error = true;
            } else {
                $scope.gdbaoyue_over_error = false;
            }
        }
        if (arg4 === "gcanci") {
            $scope.gcanci_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.guacai_anciRest !== Infinity && arg2 > $scope.guacai_anciRest) {
                $scope.gc_anci_over_error = true;
            } else {
                $scope.gc_anci_over_error = false;
            }
        }
        if (arg4 === "gcbaoyue") {
            $scope.gcbaoyue_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.gcbaoyueRest !== Infinity && arg2 > $scope.gcbaoyueRest) {
                $scope.gcbaoyue_over_error = true;
            } else {
                $scope.gcbaoyue_over_error = false;
            }
        }
        if (arg4 === "gjzcanci") {
            $scope.gjzcanci_price = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.gjzc_anciRest !== Infinity && arg2 > $scope.guacai_anciRest) {
                $scope.gjzc_anci_over_error = true;
            } else {
                $scope.gjzc_anci_over_error = false;
            }
        }

        if (arg4 === "pxanci_cucc") {
            $scope.pxanci_price_cucc = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.px_anciRest_cucc !== Infinity && arg2 > $scope.px_anciRest_cucc) {
                $scope.px_anci_over_error_cucc = true;
            } else {
                $scope.px_anci_over_error_cucc = false;
            }
        }
        if (arg4 === "pxanci_ctcc") {
            $scope.pxanci_price_ctcc = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) * Number(s3.replace(".", "")) / Math.pow(10, m);
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.px_anciRest_ctcc !== Infinity && arg2 > $scope.px_anciRest_ctcc) {
                $scope.px_anci_over_error_ctcc = true;
            } else {
                $scope.px_anci_over_error_ctcc = false;
            }
        }
    };
    $scope.formateDate = function (date) {
        return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8)
    };

    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            var startTime = document.getElementById("start").value;
            $scope.effictiveTime = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + '000000';
        })
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            var endTime = document.getElementById("end").value;
            $scope.expireTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + '235959';
        })
    });

    //提交配额
    $scope.createOrder = function () {
        $('#formSub').attr('disabled', 'true');
        var req = {
            "order": {
                "orderType": 1,
                "amount": $scope.amount,
                "enterpriseID": $scope.subEnterpriseID,
                "enterpriseName": $scope.subEnterpriseName,
                "servType": $scope.servType,
                "payStatus": 3,
                "effictiveTime": $scope.effictiveTime,
                "isExperience": $scope.isExperience,
                "expireTime": $scope.expireTime,
                "operatorID": $scope.operatorID,
                "orderItemList": [],
                "extInfo": {
                    "quotaOrderID": $scope.selectedOrder.orderCode,
                    "quotaOrderName": $scope.selectedOrder.orderName,
                    "creatorID": $scope.accountName,
                    "editorID": $scope.accountName
                }
            }
        };

        // 移动屏显配额：不限
        if ($scope.cmcc && $scope.postPingXianCMCC && $scope.pingxianisLimit == 0) {
            req.order.orderItemList.push({
                "objectID": $scope.px_noLimit_orderItem.objectID,
                "orderItemCode": $scope.px_noLimit_orderItem.orderItemCode,
                "orderID": $scope.selectedOrder.orderCode,
                "productID": $scope.px_noLimit_orderItem.productID,
                "product": $scope.px_noLimit_orderItem.product,
                "operatorID": $scope.operatorID,
                "unitPrice": 0,
                "quantity": 1,
                "totalAmount": 0
            });
        }

        // 移动屏显配额：按次
        if ($scope.cmcc && $scope.postPingXianCMCC && $scope.pingxianisLimit === 1 && $scope.pxType === 1) {
            //主订单不是无限的前提下  判断剩余配额
            if ($scope.px_anciRest_cmcc !== Infinity && $scope.anci > $scope.px_anciRest_cmcc) {
                $scope.px_anci_over_error = true;
            }
            req.order.orderItemList.push({
                "objectID": $scope.px_anci_orderItem.objectID || -1,
                "orderItemCode": $scope.px_anci_orderItem.orderItemCode || -1,
                "orderID": $scope.selectedOrder.orderCode,
                "productID": $scope.px_anci_orderItem.productID,
                "product": $scope.px_anci_orderItem.product,
                "operatorID": $scope.operatorID,
                "unitPrice": $scope.PXPrice,
                "quantity": $scope.anci,
                "totalAmount": $scope.pxanci_price
            });
        }
        //群发
        if ($scope.servType == 4) {
            //群发增彩
            if ($scope.postZC && $scope.cmcc) {
                //非体验按次
                if ($scope.zcanci != '' && $scope.isZCanci && $scope.isExperience == 0) {
                    req.order.orderItemList.push({
                        "objectID": $scope.zc_orderItem.objectID || -1,
                        "orderItemCode": $scope.zc_orderItem.orderItemCode || -1,
                        "orderID": $scope.selectedOrder.orderCode,
                        "productID": $scope.zc_orderItem.productID,
                        "product": $scope.zc_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.ZCPrice,
                        "quantity": $scope.zcanci,
                        "totalAmount": $scope.zcanci_price
                    });
                } else if ($scope.isExperience == 0 && $scope.isZCNoLimit) { //非体验不限
                    req.order.orderItemList.push({
                        "objectID": $scope.zc_orderItem.objectID,
                        "orderItemCode": $scope.zc_orderItem.orderItemCode,
                        "orderID": $scope.selectedOrder.orderCode,
                        "productID": $scope.zc_orderItem.productID,
                        "product": $scope.zc_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0
                    });
                }
                else if ($scope.isNoExperienceLimit && $scope.zcanci != '' && $scope.isExperience == 1) { //体验按次
                    req.order.orderItemList.push({
                        "objectID": $scope.zc_orderItem.objectID,
                        "orderItemCode": $scope.zc_orderItem.orderItemCode,
                        "orderID": $scope.selectedOrder.orderCode,
                        "productID": $scope.zc_orderItem.productID,
                        "product": $scope.zc_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": $scope.zcanci,
                        "totalAmount": 0
                    });
                } else if ($scope.isExperience == 1 && $scope.isZCNoLimit) { //体验不限
                    req.order.orderItemList.push({
                        "objectID": $scope.zc_orderItem.objectID,
                        "orderItemCode": $scope.zc_orderItem.orderItemCode,
                        "orderID": $scope.selectedOrder.orderCode,
                        "productID": $scope.zc_orderItem.productID,
                        "product": $scope.zc_orderItem.product,
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0
                    });
                }
            }
            //移动群发彩信
            if( $scope.cmcc && $scope.postCX){
                req.order.orderItemList.push({
                    "objectID": $scope.cx_orderItem.objectID || -1,
                    "orderItemCode": $scope.cx_orderItem.orderItemCode || -1,
                    "orderID": $scope.selectedOrder.orderCode,
                    "productID": $scope.cx_orderItem.productID,
                    "product": $scope.cx_orderItem.product,
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.cxPrice||0,
                    "quantity": $scope.cxanci||1,
                    "totalAmount":$scope.cxanci_price||0
                });
            }
            //移动群发短信
            if( $scope.cmcc && $scope.showGroupSMS){
                req.order.orderItemList.push({
                    "objectID": $scope.groupSMSCMCC_orderItem.objectID || -1,
                    "orderItemCode": $scope.groupSMSCMCC_orderItem.orderItemCode || -1,
                    "orderID": $scope.selectedOrder.orderCode,
                    "productID": $scope.groupSMSCMCC_orderItem.productID,
                    "product": $scope.groupSMSCMCC_orderItem.product,
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.groupSMSCMCCPrice||0,
                    "quantity": $scope.groupSendSMSCMCCanci||1,
                    "totalAmount":$scope.groupSMSCMCC_price||0
                });
            }
            //联通群发短信
            if( $scope.cucc && $scope.showGroupSMSCUCC){
                req.order.orderItemList.push({
                    "objectID": $scope.groupSMSCUCC_orderItem.objectID || -1,
                    "orderItemCode": $scope.groupSMSCUCC_orderItem.orderItemCode || -1,
                    "orderID": $scope.selectedOrder.orderCode,
                    "productID": $scope.groupSMSCUCC_orderItem.productID,
                    "product": $scope.groupSMSCUCC_orderItem.product,
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.groupSMSCUCCPrice||0,
                    "quantity": $scope.groupSendSMSCUCCanci||1,
                    "totalAmount":$scope.groupSMSCUCC_price||0
                });
            }
            //电信群发短信
            if( $scope.ctcc && $scope.showGroupSMSCTCC){
                req.order.orderItemList.push({
                    "objectID": $scope.groupSMSCTCC_orderItem.objectID || -1,
                    "orderItemCode": $scope.groupSMSCTCC_orderItem.orderItemCode || -1,
                    "orderID": $scope.selectedOrder.orderCode,
                    "productID": $scope.groupSMSCTCC_orderItem.productID,
                    "product": $scope.groupSMSCTCC_orderItem.product,
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.groupSMSCTCCPrice||0,
                    "quantity": $scope.groupSendSMSCTCCanci||1,
                    "totalAmount":$scope.groupSMSCTCC_price||0
                });
            }
            //移动群发屏显
            if( $scope.cmcc && $scope.showGroupScreen){
                req.order.orderItemList.push({
                    "objectID": $scope.groupScreenCMCC_orderItem.objectID || -1,
                    "orderItemCode": $scope.groupScreenCMCC_orderItem.orderItemCode || -1,
                    "orderID": $scope.selectedOrder.orderCode,
                    "productID": $scope.groupScreenCMCC_orderItem.productID,
                    "product": $scope.groupScreenCMCC_orderItem.product,
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.groupScreenCMCCPrice||0,
                    "quantity": $scope.groupSendScreenCMCCanci||1,
                    "totalAmount":$scope.groupScreenCMCC_price||0
                });
            }
            //联通群发屏显
            if( $scope.cucc && $scope.showGroupScreenCUCC){
                req.order.orderItemList.push({
                    "objectID": $scope.groupScreenCUCC_orderItem.objectID || -1,
                    "orderItemCode": $scope.groupScreenCUCC_orderItem.orderItemCode || -1,
                    "orderID": $scope.selectedOrder.orderCode,
                    "productID": $scope.groupScreenCUCC_orderItem.productID,
                    "product": $scope.groupScreenCUCC_orderItem.product,
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.groupScreenCUCCPrice||0,
                    "quantity": $scope.groupSendScreenCUCCanci||1,
                    "totalAmount":$scope.groupScreenCUCC_price||0
                });
            }
            //电信群发屏显
            if( $scope.ctcc && $scope.showGroupScreenCTCC){
                req.order.orderItemList.push({
                    "objectID": $scope.groupScreenCTCC_orderItem.objectID || -1,
                    "orderItemCode": $scope.groupScreenCTCC_orderItem.orderItemCode || -1,
                    "orderID": $scope.selectedOrder.orderCode,
                    "productID": $scope.groupScreenCTCC_orderItem.productID,
                    "product": $scope.groupScreenCTCC_orderItem.product,
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.groupScreenCTCCPrice||0,
                    "quantity": $scope.groupSendScreenCTCCanci||1,
                    "totalAmount":$scope.groupScreenCTCC_price||0
                });
            }


    }
    //屏显配额：按人包月
    if ($scope.cmcc && $scope.postPingXianCMCC && $scope.pxType === 2) {
        if ($scope.baoyueRest !== Infinity && $scope.baoyue > $scope.baoyueRest) {
            $scope.baoyue_over_error = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.px_baoyue_orderItem.objectID,
            "orderItemCode": $scope.px_baoyue_orderItem.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.px_baoyue_orderItem.productID,
            "product": $scope.px_baoyue_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.px_baoyue_orderItem.product.unitPrice || 0,
            "quantity": $scope.baoyue,
            "totalAmount": $scope.baoyue_price
        });
    }

    // 挂机短信：不限
    if ($scope.cmcc && $scope.postGuaDuan && $scope.guaduanisLimit == 0) {
        req.order.orderItemList.push({
            "objectID": $scope.guaduan_noLimit_orderItem.objectID,
            "orderItemCode": $scope.guaduan_noLimit_orderItem.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.guaduan_noLimit_orderItem.productID,
            "product": $scope.guaduan_noLimit_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": 0,
            "quantity": 1,
            "totalAmount": 0
        });
    }

    // 挂机短信：按次
    if ($scope.cmcc && $scope.postGuaDuan && $scope.guaduanisLimit === 1 && $scope.gdType === 1) {
        //主订单不是无限的前提下  判断剩余配额
        if ($scope.guaduan_anciRest !== Infinity && $scope.gdanci > $scope.guaduan_anciRest) {
            $scope.guaduan_anci_over_error = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.gd_anci_orderItem.objectID || -1,
            "orderItemCode": $scope.gd_anci_orderItem.orderItemCode || -1,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.gd_anci_orderItem.productID,
            "product": $scope.gd_anci_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.GDPrice,
            "quantity": $scope.gdanci,
            "totalAmount": $scope.gdanci_price
        });
    }


    //挂机短信：按人包月
    if ($scope.cmcc && $scope.postGuaDuan && $scope.gdType === 2) {
        if ($scope.gdbaoyueRest !== Infinity && $scope.gdbaoyue > $scope.gdbaoyueRest) {
            $scope.gdbaoyue_over_error = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.gd_baoyue_orderItem.objectID,
            "orderItemCode": $scope.gd_baoyue_orderItem.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.gd_baoyue_orderItem.productID,
            "product": $scope.gd_baoyue_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.gd_baoyue_orderItem.product.unitPrice || 0,
            "quantity": $scope.gdbaoyue,
            "totalAmount": $scope.gdbaoyue_price
        });
    }


    // 挂机彩信：不限
    if ($scope.cmcc && $scope.postGuaCai && $scope.guacaiisLimit == 0) {
        req.order.orderItemList.push({
            "objectID": $scope.guacai_noLimit_orderItem.objectID,
            "orderItemCode": $scope.guacai_noLimit_orderItem.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.guacai_noLimit_orderItem.productID,
            "product": $scope.guacai_noLimit_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": 0,
            "quantity": 1,
            "totalAmount": 0
        });
    }


    // 挂机彩信：按次
    if ($scope.cmcc && $scope.postGuaCai && $scope.guacaiisLimit === 1 && $scope.gcType === 1) {

        //主订单不是无限的前提下  判断剩余配额
        if ($scope.guacai_anciRest !== Infinity && $scope.gcanci > $scope.guacai_anciRest) {
            $scope.guacai_anci_over_error = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.gc_anci_orderItem.objectID || -1,
            "orderItemCode": $scope.gc_anci_orderItem.orderItemCode || -1,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.gc_anci_orderItem.productID,
            "product": $scope.gc_anci_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.GCPrice,
            "quantity": $scope.gcanci,
            "totalAmount": $scope.gcanci_price
        });
    }

    //挂机彩信：按人包月
    if ($scope.cmcc && $scope.postGuaCai && $scope.gcType === 2) {
        if ($scope.gcbaoyueRest !== Infinity && $scope.gcbaoyue > $scope.gcbaoyueRest) {
            $scope.gcbaoyue_over_error = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.gc_baoyue_orderItem.objectID,
            "orderItemCode": $scope.gc_baoyue_orderItem.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.gc_baoyue_orderItem.productID,
            "product": $scope.gc_baoyue_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.gc_baoyue_orderItem.product.unitPrice || 0,
            "quantity": $scope.gcbaoyue,
            "totalAmount": $scope.gcbaoyue_price
        });
    }
    // 挂机增彩
    if ($scope.cmcc && $scope.postGJZC) {

        //主订单不是无限的前提下  判断剩余配额
        if ($scope.gjzc_anciRest !== Infinity && $scope.gjzcanci > $scope.gjzc_anciRest) {
            $scope.gjzc_anci_over_error = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.gjzc_anci_orderItem.objectID || -1,
            "orderItemCode": $scope.gjzc_anci_orderItem.orderItemCode || -1,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.gjzc_anci_orderItem.productID,
            "product": $scope.gjzc_anci_orderItem.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.GJZCPrice || 0,
            "quantity": $scope.gjzcanci || 1,
            "totalAmount": $scope.gjzcanci_price || 0
        });
    }

    // 联通屏显配额：不限
    if ($scope.cucc && $scope.postPingXianCUCC && $scope.pingxianisLimit_cucc == 0) {
        req.order.orderItemList.push({
            "objectID": $scope.px_noLimit_orderItem_cucc.objectID,
            "orderItemCode": $scope.px_noLimit_orderItem_cucc.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.px_noLimit_orderItem_cucc.productID,
            "product": $scope.px_noLimit_orderItem_cucc.product,
            "operatorID": $scope.operatorID,
            "unitPrice": 0,
            "quantity": 1,
            "totalAmount": 0
        });
    }

    // 联通屏显配额：按次
    if ($scope.cucc && $scope.postPingXianCUCC && $scope.pingxianisLimit_cucc === 1 && $scope.pxType_cucc === 1) {
        //主订单不是无限的前提下  判断剩余配额
        if ($scope.px_anciRest_cucc !== Infinity && $scope.anci_cucc > $scope.px_anciRest_cucc) {
            $scope.px_anci_over_error_cucc = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.px_anci_orderItem_cucc.objectID || -1,
            "orderItemCode": $scope.px_anci_orderItem_cucc.orderItemCode || -1,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.px_anci_orderItem_cucc.productID,
            "product": $scope.px_anci_orderItem_cucc.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.PXPrice_cucc,
            "quantity": $scope.anci_cucc,
            "totalAmount": $scope.pxanci_price_cucc
        });
    }

    //add  20191106 start
    // 联通挂机短信：不限
    if ($scope.cucc && $scope.postGuaDuanCUCC && $scope.guaduanisLimitCUCC == 0) {
        req.order.orderItemList.push({
            "objectID": $scope.guaduan_noLimit_orderItemCUCC.objectID,
            "orderItemCode": $scope.guaduan_noLimit_orderItemCUCC.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.guaduan_noLimit_orderItemCUCC.productID,
            "product": $scope.guaduan_noLimit_orderItemCUCC.product,
            "operatorID": $scope.operatorID,
            "unitPrice": 0,
            "quantity": 1,
            "totalAmount": 0
        });
    }

    // 联通挂机短信：按次
    if ($scope.cucc && $scope.postGuaDuanCUCC && $scope.guaduanisLimitCUCC === 1 && $scope.gdTypeCUCC === 1) {
        //主订单不是无限的前提下  判断剩余配额
        if ($scope.guaduan_anciRestCUCC !== Infinity && $scope.gdanciCUCC > $scope.guaduan_anciRestCUCC) {
            $scope.guaduan_anci_over_errorCUCC = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.gd_anci_orderItemCUCC.objectID || -1,
            "orderItemCode": $scope.gd_anci_orderItemCUCC.orderItemCode || -1,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.gd_anci_orderItemCUCC.productID,
            "product": $scope.gd_anci_orderItemCUCC.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.GDPriceCUCC,
            "quantity": $scope.gdanciCUCC,
            "totalAmount": $scope.gdanci_priceCUCC
        });
    }
    //add  20191106 end

    // 电信屏显配额：不限
    if ($scope.ctcc && $scope.postPingXianCTCC && $scope.pingxianisLimit_ctcc == 0) {
        req.order.orderItemList.push({
            "objectID": $scope.px_noLimit_orderItem_ctcc.objectID,
            "orderItemCode": $scope.px_noLimit_orderItem_ctcc.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.px_noLimit_orderItem_ctcc.productID,
            "product": $scope.px_noLimit_orderItem_ctcc.product,
            "operatorID": $scope.operatorID,
            "unitPrice": 0,
            "quantity": 1,
            "totalAmount": 0
        });
    }

    // 电信屏显配额：按次
    if ($scope.ctcc && $scope.postPingXianCTCC && $scope.pingxianisLimit_ctcc === 1 && $scope.pxType_ctcc === 1) {
        //主订单不是无限的前提下  判断剩余配额
        if ($scope.px_anciRest_ctcc !== Infinity && $scope.anci_ctcc > $scope.px_anciRest_ctcc) {
            $scope.px_anci_over_error_ctcc = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.px_anci_orderItem_ctcc.objectID || -1,
            "orderItemCode": $scope.px_anci_orderItem_ctcc.orderItemCode || -1,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.px_anci_orderItem_ctcc.productID,
            "product": $scope.px_anci_orderItem_ctcc.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.PXPrice_ctcc,
            "quantity": $scope.anci_ctcc,
            "totalAmount": $scope.pxanci_price_ctcc
        });
    }

    //add  20191106 start
    // 电信挂机短信：不限
    if ($scope.ctcc && $scope.postGuaDuanCTCC && $scope.guaduanisLimitCTCC == 0) {
        req.order.orderItemList.push({
            "objectID": $scope.guaduan_noLimit_orderItemCTCC.objectID,
            "orderItemCode": $scope.guaduan_noLimit_orderItemCTCC.orderItemCode,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.guaduan_noLimit_orderItemCTCC.productID,
            "product": $scope.guaduan_noLimit_orderItemCTCC.product,
            "operatorID": $scope.operatorID,
            "unitPrice": 0,
            "quantity": 1,
            "totalAmount": 0
        });
    }

    // 电信挂机短信：按次
    if ($scope.ctcc && $scope.postGuaDuanCTCC && $scope.guaduanisLimitCTCC === 1 && $scope.gdTypeCTCC === 1) {
        //主订单不是无限的前提下  判断剩余配额
        if ($scope.guaduan_anciRestCTCC !== Infinity && $scope.gdanciCTCC > $scope.guaduan_anciRestCTCC) {
            $scope.guaduan_anci_over_errorCTCC = true;
        }
        req.order.orderItemList.push({
            "objectID": $scope.gd_anci_orderItemCTCC.objectID || -1,
            "orderItemCode": $scope.gd_anci_orderItemCTCC.orderItemCode || -1,
            "orderID": $scope.selectedOrder.orderCode,
            "productID": $scope.gd_anci_orderItemCTCC.productID,
            "product": $scope.gd_anci_orderItemCTCC.product,
            "operatorID": $scope.operatorID,
            "unitPrice": $scope.GDPriceCTCC,
            "quantity": $scope.gdanciCTCC,
            "totalAmount": $scope.gdanci_priceCTCC
        });
    }
    //add  20191106 end

    if ($scope.px_anci_over_error || $scope.gd_anci_over_error || $scope.gd_anci_over_errorCUCC || $scope.gd_anci_over_errorCTCC || $scope.gc_anci_over_error || $scope.baoyue_over_error
        || $scope.gdbaoyue_over_error || $scope.gcbaoyue_over_error || $scope.px_anci_over_error_cucc || $scope.px_anci_over_error_ctcc) {
        $scope.tip = "剩余配额有更新，请重新填写";
        $('#myModal').modal();
        return;
    }

    RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/orderManageService/createOrder",
        data: JSON.stringify(req),
        success: function (data) {
            $rootScope.$apply(function () {
                if (data.result.resultCode == '1030100000') {

                    $scope.tip = "CREATE_ORDER_SUCCESS_MSG";
                    $('#myModal').modal();

                    setTimeout(function () {
                        location.href = '../quotaList/quotaList.html';
                    }, 1000);


                }
                else {
                    $scope.tip = data.result.resultCode;
                    $('#myModal').modal();
                }
            })
        },
        error: function () {
            $rootScope.$apply(function () {
                    $('#formSub').removeAttr('disabled');
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                }
            )
        }
    });

};

//返回按钮的处理
$scope.goBack = function () {
    //跳转到上一层
    location.href = '../quotaList/quotaList.html';
};

$(function () {
    $('.glyphicon-calendar').on('click', function () {
        $('#time-config').trigger('click');
    })
})
//相反值 val名字
$scope.showEnable = function (val) {
    $scope[val] = !$scope[val];
}

})
;