<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.cutover.dao.ecpm.mapper.OrgRelMapper">
	<resultMap id="orgRelMap"
		type="com.huawei.jaguar.cutover.dao.domain.OrgRelWrapper">
		<result property="id" column="ID" javaType="java.lang.Long" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="orgID" column="orgID" javaType="java.lang.Integer" />
		<result property="orgCode" column="orgCode" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
	</resultMap>

	<update id="batchUpdateOrgCode" parameterType="java.util.List">
		<foreach collection="list" item="orgRelWrapper" separator=";">
			update ecpm_t_org_rel set
			orgCode=#{orgRelWrapper.orgCode},
			updateTime=#{orgRelWrapper.updateTime}
			where
			orgID=#{orgRelWrapper.orgID}
		</foreach>
	</update>

	<select id="queryOrgIDByOrderID" resultMap="orgRelMap">
		select t2.orgID,
		t2.orgCode
		from
		ecpm_t_member_subscribe t1,ecpm_t_org_rel t2
		where
		t1.memberID=t2.ID and t1.orderID=#{orderID}
	</select>


	<select id="queryOrgByOrgID" resultMap="orgRelMap">
		select *
		from
		ecpm_t_org_rel
		where
		orgID=#{orgID}
		and ID =
		#{ID}
		<if test="enterpriseID!=null">
			and enterpriseID= #{enterpriseID}
		</if>
	</select>

	<insert id="insertOrg">
		INSERT INTO
		ecpm_t_org_rel
		(ID,
		enterpriseID,
		orgID,
		orgCode,
		createTime,
		updateTime,
		operatorID
		)
		VALUES
		<if test="orgCodeList !=null and orgCodeList.size() > 0">
			<foreach collection="orgCodeList" item="orgCode" separator=",">
				(
				#{ID},
				#{enterpriseID},
				#{orgID},
				#{orgCode},
				#{createTime},
				#{updateTime},
				#{operatorID}
				)
			</foreach>
		</if>
		<if test="orgCodeList ==null or orgCodeList.size() == 0">
			(
			#{ID},
			#{enterpriseID},
			#{orgID},
			#{orgCode},
			#{createTime},
			#{updateTime},
			#{operatorID}
			)
		</if>
	</insert>

	<delete id="deleteRelByOrgIDList">
		delete from ecpm_t_org_rel where
		orgID in
		<foreach item="orgID" index="index" collection="list" open="("
			separator="," close=")">
			#{orgID}
		</foreach>
	</delete>

	<select id="queryMembers" resultMap="orgRelMap" parameterType="java.util.Map">
		select
		id,
		orgID,
		orgCode,
		createTime,
		updateTime,
		operatorID
		from
		ecpm_t_org_rel where orgID=#{orgID}
		and ID in
		<foreach collection="ids" item="id" open="(" separator=","
			close=")">
			#{id}
		</foreach>
	</select>

	<select id="queryMemberByOrgIDAndMsisdn" resultMap="orgRelMap">
		select
		t1.ID,
		t1.orgID,
		t1.orgCode,
		t1.createTime,
		t1.updateTime,
		t1.operatorID
		from
		ecpm_t_org_rel t1 , ecpm_t_member t2
		where
		t1.ID = t2.ID
		and
		t2.msisdn =
		#{msisdn}
		and
		orgID = #{orgID}
	</select>

	<select id="queryOrgByOrgIDList" resultMap="orgRelMap">
		select orgID, orgCode
		from
		ecpm_t_org_rel
		<trim prefix="where" prefixOverrides="and|or">
			<if test="ids !=null and ids.size() > 0">
				and orgID in
				<foreach collection="ids" item="id" open="(" separator=","
					close=")">
					#{id}
				</foreach>
			</if>
			<if test="orgCodeList !=null and orgCodeList.size() > 0">
				and orgCode in
				<foreach collection="orgCodeList" item="orgCode" open="("
					separator="," close=")">
					#{orgCode}
				</foreach>
			</if>
		</trim>
	</select>

	<select id="queryOrgCodeMsisdnByOrgID" resultMap="orgRelMap">
		select
		t3.msisdn,
		t3.orgCode
		from(
		select
		t.id,
		t1.msisdn,
		t2.orgCode,
		t.orgID,
		t.createTime,
		t.updateTime,
		t.operatorID
		from
		ecpm_t_org_rel
		t,ecpm_t_member
		t1,ecpm_t_content_org t2
		where t.ID = t1.ID
		and
		t2.ownerID = t.orgID
		and t.orgID =
		#{orgID}
		)t3
		group by t3.msisdn,
		t3.orgCode
	</select>

	<select id="queryOrgByOrgIDOrEnterpriseID" resultType="java.lang.Integer">
		select count(id)
		from
		ecpm_t_org_rel
		<trim prefix="where" prefixOverrides="and|or">
			<if test="orgIDList !=null and orgIDList.size() > 0">
				and orgID in
				<foreach collection="orgIDList" item="orgID" open="("
					separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			<if test="enterpriseID!=null">
				enterpriseID= #{enterpriseID}
			</if>
		</trim>
	</select>
	<select id="countMemberSubSuccessByOrgIDOrEnterpriseID"
		resultType="java.lang.Integer">
		select count(id)
		from
		ecpm_t_org_rel rel,ecpm_t_member_subscribe ms
		<trim prefix="where" prefixOverrides="and|or">
			rel.ID=ms.memberID
			<if test="orgIDList !=null and orgIDList.size() > 0">
				and orgID in
				<foreach collection="orgIDList" item="orgID" open="("
					separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			<if test="uselessStatusList != null and uselessStatusList.size()>0">
				and
				<foreach collection="uselessStatusList" item="uselessStatus"
					open="(" separator="and" close=")">
					ms.status != #{uselessStatus}
				</foreach>
			</if>
			<if test="enterpriseID!=null">
				and enterpriseID= #{enterpriseID}
			</if>
		</trim>
	</select>

	<delete id="deleteOrgRels" parameterType="java.util.Map">
		delete from ecpm_t_org_rel where orgID=#{orgID}

		<if test="ids != null">
			and ID in
			<foreach collection="ids" item="id" open="(" separator=","
				close=")">
				#{id}
			</foreach>
		</if>
	</delete>

	<select id="queryMemberOrgByMemberIDList" resultMap="orgRelMap">
		select ID, enterpriseID
		from
		ecpm_t_org_rel
		where ID in
		<foreach collection="list" item="id" open="(" separator=","
			close=")">
			#{id}
		</foreach>
		group by ID,enterpriseID
	</select>
	
	<select id="countMemberByEnterpriseID" resultType="java.lang.Integer">
		select count(1) from 
		(select t1.ID 
		from ecpm_t_org_rel t1, ecpm_t_member_subscribe t2 
		where t1.ID=t2.memberID 
		and t1.enterpriseID=#{enterpriseID}
		and t2.`status`=3 
		group by t1.ID)a
	</select>
	
	<select id="countMemberSubscribeByOrgId" resultType="java.lang.Integer">
		select count(distinct t.msisdn) 
		from ecpm_t_member_subscribe t,ecpm_t_org_rel t1 
		where t.memberID = t1.ID 
		and t1.orgID = #{ID}
	</select>

</mapper>