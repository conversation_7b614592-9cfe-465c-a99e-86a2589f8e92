<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberQuotaMapper">


    <select id="getMemberQuotaByEnterpriseId" parameterType="int" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberQuotaWrapper">
        SELECT *
        FROM ecpm_t_member_quota
        WHERE enterpriseID = #{enterpriseId}
    </select>
    <insert id="addMemberQuota" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberQuotaWrapper">
        INSERT INTO ecpm_t_member_quota (enterpriseID, memberQuota, createTime, updateTime)
        VALUES (#{enterpriseId}, #{memberQuota}, #{createTime}, #{updateTime})
    </insert>

    <update id="updateMemberQuota" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberQuotaWrapper">
        UPDATE ecpm_t_member_quota
        SET memberQuota = #{memberQuota}, updateTime = now()
        WHERE enterpriseID = #{enterpriseId}
    </update>
    <select id="queryMemberQuotaByEnterpriseId" parameterType="int" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberQuotaWrapper">
        SELECT
            mq.*,
            sum( CASE WHEN os.reserved2 IS NOT NULL and os.reserved2 != "" and orl.id IS NOT NULL THEN 1 ELSE 0 END ) memberCount
        FROM
            ecpm_t_member_quota mq
                LEFT JOIN ecpm_t_org_simple os ON os.enterpriseID = mq.enterpriseID
                LEFT JOIN ecpm_t_org_rel orl ON orl.orgID = os.id
        WHERE
            mq.enterpriseID = #{enterpriseId}

        GROUP BY mq.enterpriseID
    </select>
    <select id="queryMemberCountByEnterpriseId" parameterType="int" resultType="java.lang.Integer">
        SELECT
           sum( CASE WHEN os.reserved2 IS NOT NULL and os.reserved2 != "" and orl.id IS NOT NULL THEN 1 ELSE 0 END ) memberCount
        FROM
            ecpm_t_org_simple os
                LEFT JOIN ecpm_t_org_rel orl ON orl.orgID = os.id
        WHERE
            os.enterpriseID = #{enterpriseId}

        GROUP BY os.enterpriseID
    </select>
</mapper>