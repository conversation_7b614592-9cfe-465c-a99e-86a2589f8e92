@charset "UTF-8";
[ng-cloak] {
  display: none !important;
}
@keyframes fadein{
  0%{opacity: 0;
  }
  100%{
    opacity: 1;

  }
}
@-webkit-keyframes fadein{
  0%{opacity: 0;
  }
  100%{
    opacity: 1;

  }
}
@-moz-keyframes fadein{
  0%{opacity: 0;
  }
  100%{
    opacity: 1;

  }
}
@-o-keyframes fadein{
  0%{opacity: 0;
  }
  100%{
    opacity: 1;

  }
}
@-ms-keyframes fadein{
  0%{opacity: 0;
  }
  100%{
    opacity: 1;

  }
}
body{
  animation:fadein 1s linear 1;
  -webkit-animation:fadein 1s linear 1;
  -moz-animation:fadein 1s linear 1;
  -o-animation:fadein 1s linear 1;
  -ms-animation:fadein 1s linear 1;
}

/*css reset*/
body, h1, h2, h3, h4, h5, div, p, ul, li, ol, dd, dl, dt, form, th, td {
  margin: 0;
}

ul, ol, option, textarea {
  padding: 0;
}

body, div, p, ul, li, header, nav, section, footer, article, aside, a {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

ul, li {
  list-style: none;
}

header, nav, section, footer, article, aside {
  display: block \9;
}

/***layout***/
body, html {
  height: 100%;
}
.body-min-width{
  min-width: 1024px;
}
.body-min-width-new{
  min-width: 1800px;
}
.container {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}

.main_right .menu_Bar {
  background: linear-gradient(0deg, #8f83e7, #8171e4, #715de1);
  height: 58px;
}

/*菜单导航*/
.head-nav {
  padding: 20px;
}

.head-nav .frist-tab {
  font-size: 16px;
}

.head-nav .second-tab {
  font-size: 14px;
}

/*input表单光标事件*/
.form-group label {
  font-weight: inherit;
}

.form-control:focus {
  border-color: #7360e1;
}

.form-horizontal .form-group {
  margin-left: 0;
  margin-right: 0;
}

/*搜索按钮统一样式*/
.search-btn {
  background-image: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
  border: 1px solid #8f83e7;
  color: #fff;
}

.search-btn .search-iocn {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../assets/images/btnIcons18.png) no-repeat;
  vertical-align: middle;
}

.search-btn:hover, .search-btn:focus, .search-btn.focus {
  color: #fff;
}

.btn-back {
  border: 1px solid #cccccc;
  background-color: rgb(211,211,211);
}
/*check*/
.redio-btn {
  width: 22px;
  height: 22px;
  display: inline-block;
  background: #dfdfdf;
  border-radius: 20px;
}

.check-btn {
  width: 22px;
  height: 22px;
  display: inline-block;
  background: #dfdfdf;
}

.checked-btn {
  width: 22px;
  height: 22px;
  display: inline-block;
  background: #dfdfdf;
}

.redio-btn.checked {
  background: url(../assets/images/checkIcons22.png) no-repeat;
  background-position: 0 0;
}

.checked-btn.checked {
  background: url(../assets/images/checkIcons22.png) no-repeat;
  background-position: -22px 0;

}

/*switch*/
.switch {
  display: inline-block;
  width: 60px;
  height: 30px;
  border-radius: 20px;
  background: #705de1;
  position: relative;
  cursor: pointer;
  border: 1px solid #705de1;

}

.switch .switch-icon {
  position: absolute;
  width: 28px;
  height: 28px;
  background: #fff;
  border-radius: 20px;
  right: 0;
  top: 0px;
  box-shadow: 0 1px 2px #3f2e9b;
  transition: all 0.8s;
  border: 1px solid #f2f2f2;
}

.switch.off {
  border: 1px solid #888;
  background: #888;
}

.switch.off .switch-icon {
  left: 0;
  top: 0;
  box-shadow: 0 1px 1px #f2f2f2;
  border: 1px solid #f2f2f2;
}

/*日期插件*/
.input-group-addon {
  width: 26px;
  background: #fff;
  border-left: 1px solid #fff;
}

.input-group-addon icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../assets/images/otherIcons20.png) no-repeat;
  vertical-align: middle;
  background-position: 0 0;
}

/*表格样式*/
.coorPeration-table {
  background: #fff;
}

.handle {
  overflow: hidden;
}

.handle ul li {
  display: inline-block;
  margin-right: 20px;
  cursor: pointer;
}

.handle ul li.edit {
  color: #7360e2;
}

.handle ul li.delete {
  color: #ff2549;
}

.handle ul li.set {
  color: #7360e2;
}
.handle ul li.impoMebr {
  color: #7360e2;
}

/*表格内图标*/
.handle ul li icon {
  width: 18px;
  height: 18px;
  display: inline-block;
  background: url(../assets/images/tableEditIcons18.png) no-repeat;
  vertical-align: sub;
  margin-right: 5px;
}

/*编辑*/
.handle ul li icon.edit-icon {
  background-position: 0 0;
}

/*删除*/
.handle ul li icon.delete-icon {
  background-position: -18px 0;
}

/*设置*/
.handle ul li icon.set-icon {
  background-position: -36px 0;
}

/*添加成员*/
.handle ul li icon.add-icon {
  background-position: -54px 0;
}

/*查询查看*/
.handle ul li icon.searchBlu-icon {
  background-position: -72px 0;
}

/*导入成员*/
.handle ul li icon.import-icon {
  background-position: -90px 0;
}

/*导出*/
.handle ul li icon.export-icon {
  background-position: -108px 0;
}

/*增补订单*/
.handle ul li icon.supplem-icon {
  background-position: -126px 0;
}

/*取消-黑色*/
.handle ul li icon.cancleBla-icon {
  background-position: -144px 0;
}

/*开启*/
.handle ul li icon.open-icon {
  background-position: -162px 0;
}

/*关闭-红色锁*/
.handle ul li icon.closeGreen-icon {
  background-position: -180px 0;
}

/*配置*/
.handle ul li icon.deploy-icon {
  background-position: -198px 0;
}

/*驳回*/
.handle ul li icon.reject-icon {
  background-position: -216px 0;
}

/*通过*/
.handle ul li icon.past-icon {
  background-position: -234px 0;
}

/*审核*/
.handle ul li icon.check-icon {
  background-position: -252px 0;
}

.table-tab {
  float: right;
  margin: 0 20px;
}

.pagination > li > a, .pagination > li > span {
  color: #383838;
}

.pagination > .active > a {
  background-image: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
  border-color: #705de1;
  color: #ffffff;
}

.table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
  border: none;
  padding: 12px 8px;
}

.table-striped > tbody > tr:nth-child(odd) {
  background-color: #f2f2f2;
}

.pagination > li > span.page-all:hover {
  background-color: #fff;
  color: #777;
}

.table-hover > tbody > tr:hover {
  background-color: #E8E8E8;
}

/*弹出框*/
.modal-header {
  background: linear-gradient(0deg, #705de1 0%, #8f83e7 100%);
  color: #fff;
}

.modal-header span {
  color: #fff;
}

.modal-footer {
  border: none;
}

/*上传图片*/
.panel-body .file-Btn {
  position: relative;
}

.panel-body .file-loading {
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  width: 130px;
  height: 35px;
  overflow: hidden;
}

.file-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url(../assets/images/btnIcons18.png) no-repeat;
  vertical-align: inherit;
  margin-right: 5px;
  background-position: -54px 5px;
}

/*tab切换*/
.nav-tab {
  display: block;
}

.nav-tab li {
  display: inline-block;
  color: #c3c3c3;
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  background: #fff;
  border-radius: 5px;
  padding: 0 40px;
  cursor: pointer;
  /* width: 86px; */
  text-align: center;
}

.nav-tab li.tab-active {
  color: #7360e1;
  box-shadow: 0 1px 6px #e5e5e5;
}

a:hover {
  text-decoration: none;
  cursor: pointer;
}

.error-border {
  border-color: #ff0000;
}



  /*移动云视觉规范定制主题*/
.theme_ecloud .logo{
	display: none;
}
.theme_ecloud .container .main_left{
	width: 240px;
}
.theme_ecloud .sideNav{
	padding-right: 0;
}
.theme_ecloud .sideNav .navItem, .theme_ecloud .sideNav .subNav li{
	padding: 0;
	line-height: 40px;
}
.theme_ecloud .moreArr{
	background-image: none;
	width: 0;
	height: 0;
	border-top: 4px solid transparent;
	border-left: 4px solid #100e0e;
	border-bottom: 4px solid transparent;
}
.theme_ecloud .navcss.active .navName{
	color:#4f596a;
}
.theme_ecloud .sideNav .navName{
	width: 160px;
	text-align: left;
	font-size: 14px;
}
.theme_ecloud .sideNav .subNav .navName{
	font-size: 14px;
}
.theme_ecloud .subnavcss.active{border-left-color: #337dff;background: #eaf2ff;}
.theme_ecloud .subnavcss{border-left: 3px solid transparent; padding-left:30px !important}
.theme_ecloud .subnavcss:hover{background:#f5f8ff;}
.theme_ecloud .subnavcss.active .navName{color: #337dff;}

.theme_ecloud .navIcon,.theme_ecloud  .operateIcon{
	transform: scale(0.5);
	display: none;
}
.theme_ecloud .main_right .menu_Bar{
	display: none;
}
.theme_ecloud .cooperation-manage .search-btn{
	padding: 5px 4px;
    line-height: 1.4;}
.theme_ecloud .cooperation-manage .search-btn,
.theme_ecloud .search-btn,
.theme_ecloud .modal-footer button.btn[type="submit"],
.theme_ecloud .selBtn{
	background-image: none;
	font-size: 12px;
	border-color: transparent;
	background: #337Dff;
	color:#FFFFFF;
	height: 32px;
}
.theme_ecloud .cooperation-manage .search-btn:hover,
.theme_ecloud .search-btn:hover,
.theme_ecloud .modal-footer button.btn[type="submit"]:hover{
	background: #5392FF;
}
.theme_ecloud .cooperation-manage .search-btn:active,
.theme_ecloud .search-btn:active,
.theme_ecloud .modal-footer button.btn[type="submit"]:active{
	background: #125DE1;
}
.theme_ecloud .btn{
	padding: 6px 4px;
	line-height: 1.5;
	font-size: 12px;
	border-radius: 2px;
}
.theme_ecloud .btn .search-iocn,
.theme_ecloud .cooperation-manage .btn .search-iocn{
	width: 12px;
	height: 12px;
	background-size: cover;
}

.theme_ecloud .form-control{
	height: 32px;
	background: #FFFFFF;
	border: 1px solid #D9D9D9;
}
.theme_ecloud input::-webkit-input-placeholder {
	color:#bbbbbb;
}

.theme_ecloud .cooperation-head{
	color: #337DFF;
	line-height:24px;
	font-size: 12px;
}
.theme_ecloud .cooperation-head>*:last-child{
    color: #333333;
}
.theme_ecloud .cooperation-head .frist-tab::before{
	content:'<';
	color:#555;
	border:1px solid #d9d9d9;
	display:inline-block;
	width:24px;
	height:24px;
	text-align:center;
	margin-right:6px;
}
.theme_ecloud .table>thead>tr>th{
	background: #F4F5F7;
	font-size: 12px;
	color: #031129;

}
.theme_ecloud .table>thead>tr:hover>th{
	background: #F5F8FF;
	color: #333333;
}
.theme_ecloud .container .main_right,body.theme_ecloud{
	background: #ffffff !important;
	font-family: PingFangSC-Regular;
	font-size: 12px;
}
.theme_ecloud .table-striped > tbody > tr:nth-child(odd) {
  background-color: #ffffff;
}
.theme_ecloud .table-striped > tbody > tr:nth-child(even) {
  background-color: #fafafa;
}
.theme_ecloud .table>tbody>tr>td{
	color: #333333;
	letter-spacing: 0;
	text-align: justify;
	font-weight: 400;
}
.theme_ecloud .pagination>.active>a{
	background: #FFFFFF;
	border: 1px solid #337DFF;
	color: #337DFF;
}
.theme_ecloud .no_click a,.theme_ecloud .pagination>li:first-child>a,.theme_ecloud .pagination>li:first-child>span{
	background: #FFFFFF !important;
	border: 1px solid #D9D9D9;
	color:#bbbbbb !important;
}
.theme_ecloud .pagination > li > a,.theme_ecloud .pagination > li > span{
	background: #FFFFFF;
	border: 1px solid #D9D9D9;
	color:#333333;
	padding: 4px 9px;
	line-height: 1.5;
}
.theme_ecloud .form-control:focus {
  border-color: #337DFF;
}
.theme_ecloud .form-control,.theme_ecloud .li-gotoPage a{
	border-radius:2px;
}
.theme_ecloud .pageChoose .form-control,
.theme_ecloud .toPage .form-control,
.theme_ecloud .li-gotoPage a 
{
	height: 28px;
	font-size: 12px;
	padding: 4px 6px;
}
.theme_ecloud .toPage .form-control{
	width: 46px;
}
.theme_ecloud .page-span{
	line-height:28px;
}
.theme_ecloud .form-group div{
	line-height: 24px;
}
.theme_ecloud .webuploader-container{
	border-radius: 2px;
  height: 32px;
}
.theme_ecloud .enterprise-btn .btn{
	margin-right: 10px;
	min-width: 54px;
}
.theme_ecloud .enterprise-title,
.theme_ecloud .service-config,
.theme_ecloud .localTitle span, .title01{
	border-bottom: 1px solid #E5E5E5;
	font-family: MicrosoftYaHei;
	font-size: 14px;
	color: #6F7D96;
	line-height: 18px;
	padding:5px 0
}
.theme_ecloud .modal-header{
	font-size: 16px;
	color: #031129;
	background: #FFFFFF;
	height: 50px;
}
.theme_ecloud .modal-header span{
	color:#999999;
}
.theme_ecloud .modal-content{
	background: #FFFFFF;
	box-shadow: 0 0 10px 0 rgba(153,153,153,0.30);
	border-radius: 4px;
}
.theme_ecloud .modal-body .tip{
	font-size: 14px;
	color: #333333;
	letter-spacing: 0;
	text-align: justify;
	line-height: 21px;
}
.theme_ecloud .modal-body{
	padding: 19px;
}
.theme_ecloud .modal-dialog .modal-footer{
	text-align: right !important;
	border-top: 1px solid #E9EBEF;
	padding-top: 14px;
	padding-bottom: 14px;
}

.theme_ecloud .btn{
	min-width: 54px;
}
.theme_ecloud .modal-footer button.btn.btn-back[type="submit"]{
	background: #FFFFFF;
	border-color: #D9D9D9;
	color: #555555;
}

.theme_ecloud .cooperation-nav{
	border-bottom: 1px solid #d9d9d9;
}
.theme_ecloud .tabtn-menu{
	box-shadow: none;
	font-size: 14px;
	background: #F1F4FA;
	border: 1px solid #D9D9D9;
	border-radius: 2px 2px 0 0;
	border-bottom:none;
	height: 40px;
	line-height: 40px;
	color: #555555;
}
.theme_ecloud .tabtn-menu:hover{
	color: #337DFF;
}
.theme_ecloud .cur-tabtn-menu{
	background: #FFFFFF;
	color: #337DFF;
	position: relative;
  bottom: -1px;
}
.theme_ecloud .switch {
  background: #337DFF;
  border: 1px solid #337DFF;

}
.theme_ecloud .cooperation-manage .add-table .add-btn,
.theme_ecloud .btn-back {
  background: #FFFFFF;
	border: 1px solid #D9D9D9;
	font-size: 12px;
	color: #555555;
	line-height: 18px;
}
.theme_ecloud .cooperation-manage .add-table .add-btn span{
	color: #555555 !important;
}
.theme_ecloud .cooperation-manage .add-table .add-btn .add-iocn{
	width: 18px;
	height: 15px;
	background-position: -16px -3px;
	display: none;
}
.theme_ecloud .handle ul li span{
	color:#337DFF !important;
}
.theme_ecloud .handle ul li icon,
.theme_ecloud .cooperation-manage .add-table .add-btn .export-icon,
.theme_ecloud .cooperation-manage .add-iocn1{
	display: none;
}
.theme_ecloud a,
.theme_ecloud .enterPrise .add-table .add-btn span,
.theme_ecloud .handle ul li.set,
.theme_ecloud .handle ul li.impoMebr{
	color:#337DFF;
}
.theme_ecloud td .handle ul li button{
	background: transparent;
}
.theme_ecloud .range_inputs .btn-success{
	background: #337DFF;
  border: 1px solid #337DFF;
  color:#FFFFFF;
}
.theme_ecloud .range_inputs .btn-default{
	background: #FFFFFF;
	border: 1px solid #D9D9D9;
	color:#555555;
}
.theme_ecloud .daterangepicker select.ampmselect, 
.theme_ecloud .daterangepicker select.hourselect, 
.theme_ecloud .daterangepicker select.minuteselect, 
.theme_ecloud .daterangepicker select.secondselect{
	border: 1px solid #D9D9D9;
}
.theme_ecloud .redio-btn.checked,
.theme_ecloud .checked-btn.checked {
  background: url(../assets/images/ec_checkIcons22.png) no-repeat;
  background-position: -1px -1px;
  border-color: transparent;
}
.theme_ecloud .checked-btn.checked{
	background-position: -25px -3px;
  
}
.theme_ecloud .checked-btn,.theme_ecloud .check-btn{
	background: transparent;
	border: 1px solid #d9d9d9;
}
.theme_ecloud .checked-btn{
	width: 18px;
	height: 18px;
	border-radius: 2px;
}
.theme_ecloud td[title='审核通过'] .ec_status,
.theme_ecloud td[title='变更驳回'] .ec_status,
.theme_ecloud td[title='变更审核中'] .ec_status,
.theme_ecloud td[title='待审核'] .ec_status,
.theme_ecloud td[title='驳回'] .ec_status
.theme_ecloud td[title='审核驳回'] .ec_status,
.theme_ecloud td *[title='变更审核中'],
.theme_ecloud td *[title='待审核'],
.theme_ecloud td *[title='审核通过'],
.theme_ecloud td *[title='驳回'],
.theme_ecloud td *[title='变更驳回'],
.theme_ecloud td *[title='审核驳回'],
.theme_ecloud td *[title='处理完成'],
.theme_ecloud td *[title='处理中'],
.theme_ecloud td *[title='审核失败'],
.theme_ecloud td *[title='导出中'],
.theme_ecloud td *[title='已导出']
{
	border-radius: 2px;
	font-size: 12px;	
	line-height: 18px;
	padding: 1px 6px;
	display: inline-block;
}
.theme_ecloud td[title='审核通过'] .ec_status,
.theme_ecloud td *[title='审核通过'],
.theme_ecloud td *[title='处理完成'],
.theme_ecloud td *[title='已导出']
{
	background: #DBEFD1;
	border: 1px solid #62C978;
	color: #10C038;
}
.theme_ecloud td[title='驳回'] .ec_status,
.theme_ecloud td *[title='驳回'],
.theme_ecloud td *[title='审核失败']{
	background: #FFDFD7;
	border: 1px solid #F04134;
	color: #F04134;
}
.theme_ecloud td[title='变更审核中'] .ec_status,
.theme_ecloud td[title='待审核'] .ec_status,
.theme_ecloud td *[title='变更审核中'],
.theme_ecloud td *[title='待审核'],
.theme_ecloud td *[title='处理中'],
.theme_ecloud td *[title='导出中']{
	background: #E1ECFF;
	border: 1px solid #337DFF;
	color: #337DFF;
}
.theme_ecloud td[title='变更驳回'] .ec_status,
.theme_ecloud td[title='审核驳回'] .ec_status,
.theme_ecloud td *[title='变更驳回'],
.theme_ecloud td *[title='审核驳回']{
	background: #FFF3CD;
	border: 1px solid #FF931D;
	color: #FF931D;
}
.theme_ecloud .ques{
	background: url(../assets/images/ec_ques.png) no-repeat;
}
.theme_ecloud .form-group div.selBtn{
	line-height: 32px;
	height: 32px;
	border-radius: 2px;
}
.theme_ecloud .modal-body label{
	font-size: 12px;
	color: #999999;
	line-height: 18px;
	font-weight:500;
	text-align:right;
}
.theme_ecloud .modal-footer button.btn:nth-child(2):hover{
	background: #FFFFFF;
	border: 1px solid #337DFF;
	color: #337DFF;
}
.theme_ecloud .modal-footer button.btn:nth-child(2):active{
	background: #E1ECFF;
	border: 1px solid #337DFF;
	color: #337DFF;
}
.theme_ecloud .cooperation-manage .add-table .add-btn:disabled{
	background: #F5F5F5;
	border: 1px solid #D9D9D9;
	color: #BBBBBB;
}
.theme_ecloud .second-topmenu .tabtn-menu{
	border-right: none;
}
.theme_ecloud .second-topmenu .tabtn-menu.cur-tabtn-menu{
	border-bottom:2px solid #337DFF;
}

.theme_ecloud .cooperation-head .frist-tab,
.theme_ecloud .cooperation-head .second-tab{
	font-size: 12px;
}

pre{
    font-family: Arial, Helvetica, sans-serif;
    font-size:14px;
    border:none;
    background:transparent;
    line-height: 1.42857143;
    color: #383838;
    margin:0;
    padding:0;
}