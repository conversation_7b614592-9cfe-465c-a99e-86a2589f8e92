<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ContentDiffnetApproveMapper">
    <resultMap id="diffNetApproveTempWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentDiffnetApproveWrapper">
        <result property="ID" column="ID" javaType="java.lang.Integer" />
        <result property="contentID" column="contentID" javaType="java.lang.Long" />
        <result property="platforms" column="platforms" javaType="java.lang.Integer" />
        <result property="wayType" column="wayType" javaType="java.lang.Integer" />        
        <result property="approveStatus" column="approveStatus" javaType="java.lang.String" />
        <result property="approveIdea" column="approveIdea" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />       
    </resultMap>
    
    <resultMap id="diffnetContentWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DiffnetContentWrapper">
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.String" />
        <result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
        <result property="contentID" column="ID" javaType="java.lang.Long" />
        <result property="sign" column="reserved10" javaType="java.lang.String" />        
        <result property="content" column="content" javaType="java.lang.String" />
        <result property="dmUnicomApproveStatus" column="dmUnicomApproveStatus" javaType="java.lang.String" />
		<result property="dmTelecomApproveStatus" column="dmTelecomApproveStatus" javaType="java.lang.String" />
		<result property="hbTelecomApproveStatus" column="hbTelecomApproveStatus" javaType="java.lang.String" />       
		<result property="ltUnicomApproveStatus" column="ltUnicomApproveStatus" javaType="java.lang.String" />       
    </resultMap>

    <insert id="insert">
        insert into ecpm_t_content_diffnet_approve
        (
        contentID,
        platforms,
        wayType,
        approveStatus,
        approveIdea,
        createTime,
        updateTime
        )
        VALUES
        (
        #{contentID},
        #{platforms},
        #{wayType},
        #{approveStatus},
        #{approveIdea},
        now(),
        now()
        )
    </insert>
    
     <delete id="deleteByContentId" parameterType="java.lang.Long">
        delete from ecpm_t_content_diffnet_approve where contentID=#{contentID}
    </delete>
    
    <select id="queryList" resultMap="diffNetApproveTempWrapper">
		select
		contentID,
		platforms,
		wayType,
		approveStatus,
		approveIdea,
		createTime,
		updateTime
		from
		ecpm_t_content_diffnet_approve
		where 
		contentID = #{contentID} and
		platforms = #{platforms} and
		wayType = #{wayType}
	</select>

	<select id="queryListByContentId" resultMap="diffNetApproveTempWrapper">
		select
			contentID,
			platforms,
			wayType,
			approveStatus,
			approveIdea,
			createTime,
			updateTime
		from
			ecpm_t_content_diffnet_approve
		where
			contentID = #{contentID}
	</select>

	<select id="queryDiffnetWayContentList" resultMap="diffnetContentWrapper">
		SELECT s.ID enterpriseID,s.enterpriseName,t.ID,t.reserved10,t.content,
			a1.approveStatus dmUnicomApproveStatus,a2.approveStatus dmTelecomApproveStatus,
			d1.approveStatus cxUnicomApproveStatus,d2.approveStatus cxTelecomApproveStatus,
			c.approveStatus hbTelecomApproveStatus,b.approveStatus ltUnicomApproveStatus
		FROM ecpm_t_content t 
		JOIN ecpm_t_enterprise_simple s ON t.enterpriseID=s.ID
		LEFT JOIN ecpm_t_content_diffnet_approve a1 ON t.ID=a1.contentID AND a1.wayType=2 AND a1.platforms=2
		LEFT JOIN ecpm_t_content_diffnet_approve a2 ON t.ID=a2.contentID AND a2.wayType=2 AND a2.platforms=3
		LEFT JOIN ecpm_t_content_diffnet_approve b ON t.ID=b.contentID AND b.wayType=5 AND b.platforms=3
		LEFT JOIN ecpm_t_content_diffnet_approve c ON t.ID=c.contentID AND c.wayType=6 AND c.platforms=2

		LEFT JOIN ecpm_t_content_diffnet_approve d1 ON t.ID=d1.contentID AND d1.wayType=8 AND d1.platforms=2
		LEFT JOIN ecpm_t_content_diffnet_approve d2 ON t.ID=d2.contentID AND d2.wayType=8 AND d2.platforms=3
		WHERE t.parentID IS NULL AND t.contentType != 3 AND t.oriContentID IS NULL
		AND t.subServType IN (1,2,3) 
		<if test="wayTypeList != null and wayTypeList.size()>0">
			AND (
			<foreach item="wayType" index="index" collection="wayTypeList" open="" separator="OR"
	                                 close="">
	            <if test="wayType !=null and wayType == 2">
	            	<if test="platforms == 2">
	            		((a1.approveStatus=4 OR a1.approveStatus IS NULL) AND t.reserved7 IN ('010','011','110','111'))
	            	</if>
	            	<if test="platforms == 3">
	            		((a2.approveStatus=4 OR a2.approveStatus IS NULL) AND t.reserved7 IN ('001','011','101','111'))
	            	</if>
	            	<if test="platforms == 4">
	            		((a2.approveStatus=4 OR a2.approveStatus IS NULL) AND t.reserved7 IN ('001','011','101','111'))
					 OR ((a2.approveStatus=4 OR a2.approveStatus IS NULL) AND t.reserved7 IN ('001','011','101','111'))
	            	</if>
	            </if>
	            <if test="wayType !=null and wayType == 5">
            		((b.approveStatus=4 OR b.approveStatus IS NULL) AND t.reserved7 IN ('001','011','101','111'))
	            </if>
	            <if test="wayType !=null and wayType == 6">
            		((c.approveStatus=4 OR c.approveStatus IS NULL) AND t.reserved7 IN ('010','011','110','111'))
	            </if>

				<if test="wayType !=null and wayType == 8">
					<if test="platforms == 2">
						((d1.approveStatus=4 OR d1.approveStatus IS NULL) AND t.reserved7 IN ('010','011','110','111'))
					</if>
					<if test="platforms == 3">
						((d2.approveStatus=4 OR d2.approveStatus IS NULL) AND t.reserved7 IN ('001','011','101','111'))
					</if>
					<if test="platforms == 4">
						((d1.approveStatus=4 OR d1.approveStatus IS NULL) AND t.reserved7 IN ('001','011','101','111'))
						OR ((d2.approveStatus=4 OR d2.approveStatus IS NULL) AND t.reserved7 IN ('001','011','101','111'))
					</if>
				</if>
	        </foreach>
	        )
	   	</if>
		<if test="enterpriseTypeList != null and enterpriseTypeList.size()>0">
			AND (
				<foreach item="enterpriseType" index="index" collection="enterpriseTypeList" open="(" separator="or"
                         close=")">
                    <if test="enterpriseType !=null and enterpriseType == 5 ">
		                (s.enterpriseType=5 AND s.reserved10 NOT IN ('111','112'))
		            </if>
		            <if test="enterpriseType !=null and enterpriseType == '15' ">
		                (s.enterpriseType=5 AND s.reserved10='111')
		            </if>
		            <if test="enterpriseType !=null and enterpriseType == '25' ">
		                (s.enterpriseType=5 AND s.reserved10='112')
		            </if>
					<if test="enterpriseType !=null and enterpriseType == '35' ">
						(s.enterpriseType=5 AND s.reserved10='113')
					</if>
		            <if test="enterpriseType !=null and enterpriseType != 5 and enterpriseType != '15' and enterpriseType != '25' and enterpriseType != '35' ">
		                s.enterpriseType=#{enterpriseType}
		            </if>
                </foreach>
				)
		</if>
		<if test="servTypeList != null and servTypeList.size()>0">
			AND t.servType IN 
			<foreach item="servType" index="index" collection="servTypeList" open="(" separator=","
	                                 close=")">
	            #{servType}
	        </foreach>
	   	</if>
	   	<if test="contentID != null">
	   		AND (t.ID=#{contentID} OR t.refYsmbID=#{contentID})
	   	</if>
	</select>
</mapper>