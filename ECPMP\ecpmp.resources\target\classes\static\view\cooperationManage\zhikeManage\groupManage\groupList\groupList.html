<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>分组管理</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../../../css/searchList.css"/>
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <!-- 引入分页组件 -->
    <script type="text/javascript" src="../../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
    <link rel="stylesheet" type="text/css" href="../../../../../css/groupList.css"/>
    <script type="text/javascript" src="groupListCtrl.js"></script>
    <!-- 导入文件组件 -->
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datetimepicker.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datetimepicker.zh-CN.js"></script>
    <script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../../../css/bootstrap-datetimepicker.css" rel="stylesheet" type="text/css" />
    <link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <style>
        .switch {
            display: table-cell;
        }
        .li-disabled{
            opacity: 0.5;
            cursor: not-allowed!important;
        }
        .selInp{
            position: absolute;
            top: 1px;
            left: 16px;
            border:none;
            width: 80%;
            height: 32px;
        }
        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        .delete .btn:focus, .delete .btn:hover, .delete .btn:active {
            outline: none;
            box-shadow: none;
            color: #ff2549;
        }

        .edit .btn:focus, .edit .btn:hover, .edit .btn:active {
            outline: none;
            box-shadow: none;
            color: #7360e2;
        }

        .impoMebr .btn:focus, .impoMebr .btn:hover, .impoMebr .btn:active ,.batchDelMebr ,.batchDelMebr .btn:focus, .batchDelMebr .btn:hover, .batchDelMebr .btn:active{
            outline: none;
            box-shadow: none;
            color: #7360e2;
        }

        /*.li-disabled{*/
        /*opacity: 0.5;*/
        /*cursor: not-allowed!important;*/
        /*}*/
        .handle ul li[disabled] {
            opacity: 0.5;
            cursor: not-allowed !important;
        }

        .handle ul li {
            margin-right: 7px;
        }

        .handle ul li icon {
            margin-right: 2px;
        }
        .errTipcss select{
            border-color: red;
        }
        .body-min-width-1150{
            min-width:1280px;
        }
        th,th span{white-space: nowrap;}
        td{width:auto}
    </style>
</head>

<body ng-app='myApp' ng-controller='groupListController' ng-init="init();" ng-class="isMonthBuQuota?'body-min-width-1150':'body-min-width'">
<div class="cooperation-manage">
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType =='1'">
        <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>
    </div><!-- 管理员直客 -->
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType=='5'">
        <span class="frist-tab" ng-bind="'COMMON_PROENTERPRISE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;

        <span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>

    </div><!-- 管理员分省-->
    <div class="cooperation-head" ng-if="(isSuperManager&&enterpriseType==3) || loginRoleType=='agent' || isSubEnterpirse">
        <span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>
    </div><!-- 管理员二级企业 代理商 子企业-->
    <div class="cooperation-head" ng-if="isZhike">
        <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>
    </div><!-- 直客 -->

    <div class="cooperation-head" ng-if="isProvincial">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'COMMON_GROUPMANAGE'|translate"></span>

    </div><!-- 分省-->
    <top:menu chose-index="3" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
              list-index="10"
              ng-if="isSuperManager && enterpriseType =='1'"></top:menu><!-- 管理员直客 -->
    <!-- 管理员登陆查看代理商二级企业 or 代理商自己登陆 or 二级企业自己登录-->
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
              list-index="11"
              ng-if="(isSuperManager && enterpriseType =='3') || loginRoleType=='agent' || isSubEnterpirse"></top:menu>
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
              list-index="11" ng-if="isZhike && enterpriseType =='1'"></top:menu><!-- 直客 -->
    <top:menu chose-index="{{choseIndex}}"
              page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList" list-index="32"
              apply-val="{{proSupServerType}}"
              ng-if="isSuperManager && enterpriseType =='5'"></top:menu><!-- 管理员分省一级-->
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
              list-index="33" ng-if="enterpriseType =='5' && isEcProvince !=112 && !isMiguMusic"
              ng-class="{true:'',false:'second-topmenu'}[isProvincial]"></top:menu><!--移动云-->
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
              list-index="81" ng-if="enterpriseType =='5' &&  (isEcProvince ==112 || isMiguMusic)"
              ng-class="{true:'',false:'second-topmenu'}[isProvincial]"></top:menu><!-- 分省一级-->
    <div class="cooperation-search">
        <form class="form-horizontal">
            <div class="form-group form-inline">
                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label">
                    <label for="groupName" ng-bind="'GROUP_GROUPNAME'|translate" style="padding-top:6px"></label>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-input">
                    <input autocomplete="off" type="text" class="form-control" id="queryOrgName"
                           placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
                           ng-model="queryOrgName">
                </div>
                <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-btn">
                    <button ng-click="queryGroupList()" type="submit" class="btn search-btn">
                        <icon class="search-iocn">
                        </icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
    <div class="add-table" >
        <div ng-if="!is5GEnterprise">
            <button type="submit" class="btn add-btn" ng-click="addGroup()"
                    ng-disabled="(isAllowMangementMember==2 && enterpriseType == 5 )||isOpenAgentValue || isMiguMusic">
                <icon class="add-iocn"></icon>
                <span ng-bind="'GROUP_ADDGROUP'|translate"></span>
            </button>
            <button type="submit" class="btn add-btn" ng-click="addMenbOutList()"
                    ng-disabled="((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5)||isOpenAgentValue || isMiguMusic" ng-show="!allButton">
                <icon class="add-iocn"></icon>
                <span ng-bind="'GROUP_ADDMEMB'|translate"></span>
            </button>
            <button class="btn add-btn" ng-click="delMenbOutList()"
                    ng-disabled="(((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType==5) || (item.branchType && item.branchType != 22))
                                        ||isOpenAgentValue|| isMiguMusic" ng-show="!batchDelButton">
                <icon class="query-icon"></icon>
                <span ng-bind="'GROUP_EXPORTDELMEMBRECORDS'|translate"></span>
            </button>
        </div>
    </div>
    <div style="margin-left:20px;margin-bottom:20px;">
        <p style="font-size:16px;font-weight:500;" ng-bind="'COMMON_SERVICEINFO'|translate"></p>
    </div>
    <div class="coorPeration-table">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th style="width:11%">
                    <div ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="enterpriseType!='3'"></div>
                    <div ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate" ng-show="enterpriseType=='3'"></div>
                </th>
                <th style="width:7%" ng-bind="'GROUP_ORGNAME'|translate"></th>
                <th style="width:7%" ng-bind="'COMMON_CREATETIME'|translate"></th>
                <th style="width:6%" ng-bind="'GROUP_ORGTYPE'|translate"></th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">屏显三网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">行业挂短三网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">挂彩三网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">屏显本网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">行业挂短本网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">挂彩本网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">屏显异网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">行业挂短异网配额</th>
                <th ng-if="isMonthBuQuota && !isMiguMusic" style="width:6%;white-space: unset">业务融合配额</th>

                <th ng-if="!is5GEnterprise" style="width:40%" ng-bind="'COMMON_OPERATE'|translate"></th>
                <th ng-if="is5GEnterprise"style="width:10%" ng-bind="'COMMON_OPERATE'|translate"></th>
            </tr>
            </thead>
            <tbody>

            <tr ng-repeat="item in groupListData">
                <td><span title="{{enterpriseName}}">{{enterpriseName}}</span></td>
                <td><span title="{{item.orgName}}">{{item.orgName}}</span></td>
                <td><span title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</span></td>
                <td title="{{orgTypeMap[item.orgType]}}">{{orgTypeMap[item.orgType]}}</td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved2==='0'&&item.ecpmReserveds.reserved3!=0 && item.ecpmReserveds.reserved10 != '1'?item.ecpmReserveds.reserved3:'-'}}">
                    {{item.ecpmReserveds.reserved2==='0'&&item.ecpmReserveds.reserved3!=0 && item.ecpmReserveds.reserved10 != '1'?item.ecpmReserveds.reserved3:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved8==='0'&&item.ecpmReserveds.reserved5!=0 && item.ecpmReserveds.reserved10 !== '1'?item.ecpmReserveds.reserved5:'-'}}">
                    {{item.ecpmReserveds.reserved8==='0'&&item.ecpmReserveds.reserved5!=0 && item.ecpmReserveds.reserved10 != '1'?item.ecpmReserveds.reserved5:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved2==='0'&&item.ecpmReserveds.reserved7!=0?item.ecpmReserveds.reserved7:'-'}}">
                    {{item.ecpmReserveds.reserved2==='0'&&item.ecpmReserveds.reserved7!=0?item.ecpmReserveds.reserved7:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved2==='1'&&item.ecpmReserveds.reserved3!=0 && item.ecpmReserveds.reserved10 !== '1'?item.ecpmReserveds.reserved3:'-'}}">
                    {{item.ecpmReserveds.reserved2==='1'&&item.ecpmReserveds.reserved3!=0?item.ecpmReserveds.reserved3:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved8==='1'&&item.ecpmReserveds.reserved5!=0 && item.ecpmReserveds.reserved10 !== '1'?item.ecpmReserveds.reserved5:'-'}}">
                    {{item.ecpmReserveds.reserved8==='1'&&item.ecpmReserveds.reserved5!=0?item.ecpmReserveds.reserved5:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved2==='1'&&item.ecpmReserveds.reserved7!=0?item.ecpmReserveds.reserved7:'-'}}">
                    {{item.ecpmReserveds.reserved2==='1'&&item.ecpmReserveds.reserved7!=0?item.ecpmReserveds.reserved7:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved2==='1'&&item.ecpmReserveds.reserved4!=0?item.ecpmReserveds.reserved4:'-'}}">
                    {{item.ecpmReserveds.reserved2==='1'&&item.ecpmReserveds.reserved4!=0?item.ecpmReserveds.reserved4:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved8==='1'&&item.ecpmReserveds.reserved6!=0?item.ecpmReserveds.reserved6:'-'}}">
                    {{item.ecpmReserveds.reserved8==='1'&&item.ecpmReserveds.reserved6!=0?item.ecpmReserveds.reserved6:'-'}}
                </td>
                <td ng-if="isMonthBuQuota && !isMiguMusic"
                    title="{{item.ecpmReserveds.reserved10 === '1' ? item.ecpmReserveds.reserved3:'-'}}">
                    {{item.ecpmReserveds.reserved10 === '1' ? item.ecpmReserveds.reserved3:'-'}}
                </td>
                <td>
                    <div class="handle">
                        <ul>
                            <li class="set" ng-if="!is5GEnterprise"  ng-disabled="item.branchType && item.branchType != 22">
                                <button class="btn add-btn"
                                        ng-disabled="(item.branchType && item.branchType != 22)||isOpenAgentValue || isMiguMusic"
                                        ng-click="gotoSet(item)" style="padding: 0;">
                                    <icon class="set-icon"></icon>
                                    <span ng-bind="'GROUP_EDIT'|translate"></span>
                                </button>
                            </li>
                            <li class="delete" ng-if="!is5GEnterprise" ng-show="!item.isRemove"  ng-disabled="item.branchType && item.branchType != 22">
                                <button class="btn add-btn"
                                        ng-disabled="(((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType==5) || (item.branchType && item.branchType != 22))
                                        ||isOpenAgentValue|| isMiguMusic"
                                        ng-click="gotoDelete(item)" style="padding: 0;">
                                    <icon class="delete-icon"></icon>
                                    <span ng-bind="'COMMON_DELETE'|translate"></span>
                                </button>
                            </li>
                            <li class="delete" ng-if="!is5GEnterprise" ng-show="item.isRemove"  ng-disabled="item.branchType && item.branchType != 22">
                                <button class="btn add-btn"
                                        ng-disabled="(((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType==5) || (item.branchType && item.branchType != 22))
                                        ||isOpenAgentValue|| isMiguMusic"
                                        ng-click="checkResult(item)" style="padding: 0;">
                                    <icon class="delete-icon"></icon>
                                    <span ng-bind="'CHECK_RESULT'|translate"></span>
                                </button>
                            </li>
                            <li class="query"
                                ng-click="gotoDetail(item)">
                                <icon class="query-icon"></icon>
                                <span ng-bind="'GROUP_SEARCHMEMB'|translate"></span>
                            </li>
                            <li class="edit" ng-if="!is5GEnterprise"  ng-disabled="item.branchType && item.branchType != 22">
                                <button class="btn add-btn"
                                        ng-disabled="(((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType==5) || (item.branchType && item.branchType != 22))
                                        ||isOpenAgentValue||isMiguMusic"
                                        ng-click="allButton?'':addMenbInList(item)" style="padding: 0;" ng-class="{true:'',false:'li-disabled'}[!allButton]">
                                    <icon class="addNumber-icon"></icon>
                                    <span ng-bind="'GROUP_ADDMEMB'|translate"></span>
                                </button>
                            </li>
                            <li class="impoMebr" ng-if="!is5GEnterprise"  ng-disabled="item.branchType && item.branchType != 22">
                                <button class="btn add-btn"
                                        ng-disabled="(((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType==5) || (item.branchType && item.branchType != 22))
                                        ||isOpenAgentValue|| isMiguMusic"
                                        ng-click="allButton?'':impoMebr(item)" style="padding: 0;" ng-class="{true:'',false:'li-disabled'}[!allButton]">
                                    <icon class="import-icon"></icon>
                                    <span ng-bind="'GROUP_IPTMEMB'|translate"></span>
                                </button>
                            </li>
                            <li class="batchDelMebr" ng-if="!is5GEnterprise"  ng-disabled="item.branchType && item.branchType != 22">
                                <!--                                i.	若当前企业不为杭研（当前企业不为指定外部接入子企业，只针对子企业管理）、咪咕音乐（当前企业信息enterpriseType=5，且reserved10=113，只针对分省企业管理），且同步业务规则列表中存在reserved2不为1（不允许成员操作，只针对分省企业管理）-->
                                <button class="btn add-btn"
                                        ng-disabled="(((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType==5) || (item.branchType && item.branchType != 22))
                                        ||isOpenAgentValue|| isMiguMusic"
                                        ng-click="batchDelButton?'':impoDelMebr(item)" style="padding: 0;" ng-class="{true:'',false:'li-disabled'}[!batchDelButton]">
                                    <icon class="import-icon"></icon>
                                    <span ng-bind="'GROUP_IPTDELMEMB'|translate"></span>
                                </button>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
            <tr ng-show="groupListData.length<=0">
                <td style="text-align:center" colspan="13" ng-bind="'COMMON_NODATA'|translate"></td>
            </tr>
            </tbody>
        </table>
    </div>
    <div>
        <ptl-page tableId="0" change="queryGroupList('justPage')"></ptl-page>
    </div>
</div>


<!--新增分组弹出框-->
<div class="modal fade" id="addGroupPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_ADDGROUP'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group black-white">
                        <div class="row" ng-show="isMonthBuQuota ">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>是否为按条包月分组：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <li  class="redio-li" ng-click="changeType('1')"
                                     style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn checked" style="vertical-align: middle;cursor: pointer"></span><span style="cursor: pointer"> 是 </span>
                                </li>
                                <li class="redio-li" ng-click="changeType('0')"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn " style="vertical-align: middle;cursor: pointer"> </span><span style="cursor: pointer"> 否 </span>
                                </li>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span
                                        ng-bind="'GROUP_GROUPNAME'|translate"></span>：</label>
                            <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6" style="width: 46%;margin-top: 10px;">
                                <input type="text" class="form-control" ng-model="addGroupInfo.orgName"
                                       id="addGroupName"
                                       placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
                                       ng-blur="checkDataUnique(addGroupInfo.orgName,3,'')"
                                       ng-show="groupNameVali && !groupNameExist"/>
                                <input type="text" class="form-control" ng-model="addGroupInfo.orgName"
                                       id="addGroupName"
                                       placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
                                       ng-blur="checkDataUnique(addGroupInfo.orgName,3,'')"
                                       style="border-color:red;" ng-show="!groupNameVali || groupNameExist"/>
                                <span style="color:red" ng-show="!groupNameVali || groupNameExist">
										<img src="../../../../../assets/images/reject-icon.png" width="18" height="18"
                                             align="absmiddle">
										<span>{{groupNameDesc|translate}}</span>
									</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group diff">
                        <div class="row" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1 && isShowPX" >
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>屏显是否区分本异网配额：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <li class="redio-li" ng-click="changeDiffere('1')"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn checked" style="vertical-align: middle;cursor: pointer"></span><span style="cursor: pointer"> 是 </span>
                                </li>

                                <li class="redio-li" ng-click="changeDiffere('0')"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn " style="vertical-align: middle;cursor: pointer"> </span><span style="cursor: pointer"> 否 </span>
                                </li>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff == 1 && pxbw && isShowPX">
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!-- <icon>*</icon>-->
                                <span>屏显本网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;position: relative" >
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved3"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                       ng-disabled="!pxbw"
                                />

                                <select id="addGroupInfoReserved3" class="form-control" ng-model="addGroupInfo.reserved3"
                                        ng-options="x.id as x.name for x in numChoise" ng-disabled="!pxbw">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff == 1 && pxyw && isShowPX">
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>屏显异网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;position: relative" >
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved4" ng-disabled="!pxyw"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur=""
                                />
                                <select id="addGroupInfoReserved4" class="form-control" ng-model="addGroupInfo.reserved4" ng-disabled="!pxyw"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff == 0 && (pxbw || pxyw) && isShowPX " >
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>屏显配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved3" ng-disabled="!pxbw && !pxyw || businessInterim"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur=""
                                />
                                <select id="addGroupInfoReserved3" class="form-control" ng-model="addGroupInfo.reserved3" ng-disabled="!pxbw && !pxyw || businessInterim"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group diffgd">
                        <div class="row" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1 && gdFlag">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>行业挂短是否区分本异网配额：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <li class="redio-li" ng-click="changeDiffere2('1')"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn checked" style="vertical-align: middle;cursor: pointer"></span><span style="cursor: pointer"> 是 </span>
                                </li>

                                <li class="redio-li" ng-click="changeDiffere2('0')"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn " style="vertical-align: middle;cursor: pointer"> </span><span style="cursor: pointer"> 否 </span>
                                </li>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff2 == 1 && gdbw && gdFlag" >
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>行业挂短本网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;position: relative">
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved5" ng-disabled="!gdbw"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                />

                                <select id="addGroupInfoReserved5" class="form-control" ng-model="addGroupInfo.reserved5" ng-disabled="!gdbw"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff2 == 1 && gdyw && gdFlag" >
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>行业挂短异网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;position: relative">
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved6" ng-disabled="!gdyw"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                />

                                <select id="addGroupInfoReserved6" class="form-control" ng-model="addGroupInfo.reserved6" ng-disabled="!gdyw"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff2 == 0 && (gdbw || gdyw) && gdFlag" >
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>行业挂短配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved5" ng-disabled="!gdbw && !gdyw || businessInterim"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur=""
                                />
                                <select id="addGroupInfoReserved3" class="form-control" ng-model="addGroupInfo.reserved5" ng-disabled="!gdbw && !gdyw || businessInterim"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff == 1 && gcbw && gcFlag">
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>挂彩本网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;position: relative">
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved7" ng-disabled="!gcbw"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                />

                                <select id="addGroupInfoReserved7" class="form-control" ng-model="addGroupInfo.reserved7"  ng-disabled="!gcbw"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group diffgd">
                        <div class="row" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff2 == 0 && addGroupInfo.diff == 0 && gdFlag && isShowPX">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>业务融合三网配额：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff2 == 0 && addGroupInfo.diff == 0 && gdFlag && isShowPX" >
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>业务融合包：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input autocomplete="off" class="form-control selInp" ng-model="businessInterim"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                       ng-disabled="addGroupInfo.reserved3 || addGroupInfo.reserved5"
                                       ng-blur=""
                                />
                                <select id="addGroupInfoReserved3" class="form-control" ng-model="businessInterim"
                                        ng-disabled="addGroupInfo.reserved3 || addGroupInfo.reserved5"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff == 0 && gcbw && gcFlag" >
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <!--<icon>*</icon>-->
                                <span>挂彩本网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input autocomplete="off" class="form-control selInp" ng-model="addGroupInfo.reserved7" ng-disabled="!gcbw"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur=""
                                />
                                <select id="addGroupInfoReserved3" class="form-control" ng-model="addGroupInfo.reserved7" ng-disabled="!gcbw"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1 ">
                        <div class="row" style="color:#ff2549">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>备注：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <p>1、配额对分组下所有成员生效，按不同业务进行投递上限控制</p>
                                <p>2、是否区分本异网配额选择否时，所填配额值为三网共用配额总量</p>
                                <p>3、业务融合包为三网屏显+挂机短信业务，与其它配额设置互斥</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-click="checkDataUnique(addGroupInfo.orgName,3,'create')"
                        ng-disabled="!groupNameVali || groupNameExist || !addGroupInfo.orgName
                       "
                        ng-bind="'COMMON_OK'|translate"></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addOrgCancel"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>
<!--删除弹出框-->
<div class="modal fade" id="deleteGroupPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <div class="row"
                             style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
                            <div class="text-center">
                                <p ng-bind="'GROUP_SUREDELETEGROUP'|translate"></p>
                                <p style="color:red;margin-top:15px;" ng-bind="'GROUP_SUREDELETEGROUP2'|translate"></p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-click="removeOrg()"
                        ng-bind="'COMMON_OK'|translate"></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="delOrgCancel"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>


<!--编辑弹出框-->
<div class="modal fade" id="setGroupPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_EDITGROUP'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group black-white2">
                        <div class="row" ng-show="addGroupInfo.isMonthBuQuota == 1  ">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>是否为按条包月分组:</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <li class="redio-li"
                                    style="width:100px;display: inline-block;">
                                    <span
                                            class="check-btn redio-btn checked" style="vertical-align: middle;cursor: not-allowed">
                                    </span>
                                    <span style="cursor: not-allowed"> 是 </span>
                                </li>

                                <li class="redio-li"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn " style="vertical-align: middle;cursor: not-allowed">
                                </span><span style="cursor: not-allowed"> 否 </span>
                                </li>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span ng-bind="'GROUP_GROUPNAME'|translate"></span>：
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6" style="width: 46%;margin-top: 10px;">
                                <input type="text" class="form-control" ng-model="setGroupInfo.orgName"
                                       id="setGroupName addGroupName"
                                       placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
                                       ng-blur="checkDataUnique(setGroupInfo.orgName,4,'')"
                                       ng-show="groupNameVali && !groupNameExist"/>
                                <input type="text" class="form-control" ng-model="setGroupInfo.orgName"
                                       id="setGroupName addGroupName"
                                       placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
                                       ng-blur="checkDataUnique(setGroupInfo.orgName,4,'')"
                                       style="border-color:red;" ng-show="!groupNameVali || groupNameExist"/>
                                <span style="color:red" ng-show="!groupNameVali || groupNameExist">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>{{groupNameDesc|translate}}</span>
									</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group diff2">
                        <div class="row" ng-show="addGroupInfo.isMonthBuQuota == 1 && isShowPX">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>屏显是否区分本异网配额：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <li class="redio-li"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn checked" style="vertical-align: middle;cursor: not-allowed"></span><span style="cursor: not-allowed"> 是 </span>
                                </li>

                                <li class="redio-li"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn " style="vertical-align: middle;cursor: not-allowed"> </span><span style="cursor: not-allowed"> 否 </span>
                                </li>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff == 1 && pxbw &&  isShowPX">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>屏显本网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" ng-class="{'errTipcss':selInpErr}" style="width: 46%;margin-top: 10px;">
                                <input ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-model="setGroupInfo.reserved3"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved3,4,'reserved3')"
                                />
                                <select id="addGroupInfoReserved3" class="form-control" ng-model="setGroupInfo.reserved3" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved3,4,'reserved3')">

                                </select>

                                <span style="color:red" ng-show="selInpErr">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff == 1 && pxyw && isShowPX">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>屏显异网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" ng-class="{'errTipcss':selInpErr2}" style="width: 46%;margin-top: 10px;">
                                <input ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-model="setGroupInfo.reserved4"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved4,4,'reserved4')"
                                />
                                <select id="addGroupInfoReserved4" class="form-control" ng-model="setGroupInfo.reserved4" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved4,4,'reserved4')">

                                </select>
                                <span style="color:red" ng-show="selInpErr2">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>

                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff == 0 && (pxbw || pxyw) && isShowPX">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>屏显配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input ng-show="setGroupInfo.reserved10 != '1'" ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-class="{'errTipcss':selInpErr}" ng-model="setGroupInfo.reserved3"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved3,4,'reserved3')"
                                />
                                <input ng-show="setGroupInfo.reserved10 == '1'" ng-disabled="true" autocomplete="off" class="form-control selInp" ng-class="{'errTipcss':selInpErr}" ng-model=""
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved3,4,'reserved3')"
                                />
                                <select ng-show="setGroupInfo.reserved10 != '1'" id="addGroupInfoReserved3" class="form-control" ng-model="setGroupInfo.reserved3" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved3,4,'reserved3')">

                                </select>
                                <select ng-show="setGroupInfo.reserved10 == '1'"  class="form-control" ng-model="" ng-disabled="true"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved3,4,'reserved3')">
                                </select>
                                <span style="color:red" ng-show="selInpErr">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group diffgd2">
                        <div class="row" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1 && gdFlag">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>行业挂短是否区分本异网配额：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <li class="redio-li"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn checked" style="vertical-align: middle;cursor: not-allowed"></span><span style="cursor: not-allowed"> 是 </span>
                                </li>

                                <li class="redio-li"
                                    style="width:100px;display: inline-block;"><span
                                        class="check-btn redio-btn " style="vertical-align: middle;cursor: not-allowed"> </span><span style="cursor: not-allowed"> 否 </span>
                                </li>

                            </div>
                        </div>
                    </div>

                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff2 == 1 && gdbw && gdFlag">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>行业挂短本网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" ng-class="{'errTipcss':selInpErr3}" style="width: 46%;margin-top: 10px;">
                                <input ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-model="setGroupInfo.reserved5"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved5,4,'reserved5')"
                                />
                                <select id="addGroupInfoReserved5" class="form-control" ng-model="setGroupInfo.reserved5" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved5,4,'reserved5')">

                                </select>
                                <span style="color:red" ng-show="selInpErr3">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>

                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff2 == 1 && gdyw && gdFlag">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>行业挂短异网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" ng-class="{'errTipcss':selInpErr4}" style="width: 46%;margin-top: 10px;">
                                <input ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-model="setGroupInfo.reserved6"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved6,4,'reserved6')"
                                />
                                <select id="addGroupInfoReserved6" class="form-control" ng-model="setGroupInfo.reserved6" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved6,4,'reserved6')">

                                </select>
                                <span style="color:red" ng-show="selInpErr4">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>

                            </div>
                        </div>
                    </div>


                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff2 == 0 && (gdbw || gdyw) && gdFlag">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>行业挂短配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input ng-show="setGroupInfo.reserved10 != '1'" ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-class="{'errTipcss':selInpErr3}" ng-model="setGroupInfo.reserved5"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved5,4,'reserved5')"
                                />
                                <select ng-show="setGroupInfo.reserved10 != '1'" id="addGroupInfoReserved5" class="form-control" ng-model="setGroupInfo.reserved5" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved5,4,'reserved5')">

                                </select>
                                <input ng-show="setGroupInfo.reserved10 == '1'" ng-disabled="true" autocomplete="off" class="form-control selInp" ng-class="{'errTipcss':selInpErr3}" ng-model=""
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved5,4,'reserved5')"
                                />
                                <select class="form-control" ng-model="" ng-disabled="true"
                                        ng-show="setGroupInfo.reserved10 == '1'"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved5,4,'reserved5')">
                                </select>
                                <span style="color:red" ng-show="selInpErr3">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff == 0 && gcbw && gcFlag">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>挂彩配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-class="{'errTipcss':selInpErr5}" ng-model="setGroupInfo.reserved7"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved7,4,'reserved7')"
                                />
                                <select id="addGroupInfoReserved7" class="form-control" ng-model="setGroupInfo.reserved7" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved7,4,'reserved7')">

                                </select>
                                <span style="color:red" ng-show="selInpErr5">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1 && addGroupInfo.diff == 1 && gcbw && gcFlag">
                        <div class="row">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>挂彩本网配额数量：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" ng-class="{'errTipcss':selInpErr5}" style="width: 46%;margin-top: 10px;">
                                <input ng-disabled="!isMonthBuQuota" autocomplete="off" class="form-control selInp" ng-model="setGroupInfo.reserved7"
                                       id="addGroupName"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur="checkDataUnique(setGroupInfo.reserved7,4,'reserved7')"
                                />
                                <select id="addGroupInfoReserved7" class="form-control" ng-model="setGroupInfo.reserved7" ng-disabled="!isMonthBuQuota"
                                        ng-options="x.id as x.name for x in numChoise" ng-change="checkDataUnique(setGroupInfo.reserved7,4,'reserved7')">

                                </select>
                                <span style="color:red" ng-show="selInpErr5">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>新输入的配额必须大于原有配额</span>
									</span>

                            </div>
                        </div>
                    </div>
                    <div class="form-group diffgd">
                        <div class="row" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff2 == 0 && addGroupInfo.diff == 0 && gdFlag &&  isShowPX">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <icon>*</icon>
                                <span>业务融合三网配额：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isMonthBuQuota && addGroupInfo.isMonthBuQuota == 1  && addGroupInfo.diff2 == 0 && addGroupInfo.diff == 0  && gdFlag && isShowPX" >
                        <div class="row" ng-show="isMonthBuQuota">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>业务融合包：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <input ng-show="setGroupInfo.reserved10 == 1" autocomplete="off" class="form-control selInp" ng-model="setGroupInfo.reserved3"
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       id="addGroupName"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur=""
                                />
                                <input ng-show="setGroupInfo.reserved10 != 1" autocomplete="off" class="form-control selInp" ng-model=""
                                       oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                                       ng-disabled="true"
                                       placeholder="请选择或输入配额数量"
                                       ng-blur=""
                                />
                                <select ng-show="setGroupInfo.reserved10 == 1"  class="form-control" ng-model="setGroupInfo.reserved3"
                                        ng-options="x.id as x.name for x in numChoise">

                                </select>
                                <select ng-show="setGroupInfo.reserved10 != 1" class="form-control" ng-model=""
                                        ng-disabled="true"
                                        ng-options="x.id as x.name for x in numChoise">
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="addGroupInfo.isMonthBuQuota == 1">
                        <div class="row" style="color:#ff2549">
                            <label class="col-lg-4 col-xs-4 col-sm-4 col-md-4 control-label">
                                <span>备注：</span>
                            </label>
                            <div class="col-lg-6 col-xs-6 col-sm-6" style="width: 46%;margin-top: 10px;">
                                <p>1、配额对分组下所有成员生效，按不同业务进行投递上限控制</p>
                                <p>2、是否区分本异网配额选择否时，所填配额值为三网共用配额总量</p>
                                <p>3、业务融合包为三网屏显+挂机短信业务，与其它配额设置互斥</p>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-click="checkDataUnique(setGroupInfo.orgName,4,'create')"
                        ng-disabled="!groupNameVali || groupNameExist || !setGroupInfo.orgName || (setGroupInfo.orgName ==groupNameTemp && (!setGroupInfo.reserved3 && !setGroupInfo.reserved4 && !setGroupInfo.reserved5 && !setGroupInfo.reserved6 && !setGroupInfo.reserved7))
                          || selInpErr || selInpErr2 || selInpErr3 || selInpErr4 || selInpErr5
"



                        ng-bind="'COMMON_SAVE'|translate"></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="setOrgCancel"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>
<!--导入成员弹出框-->
<div class="modal fade" id="impoMebrPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_IPTMEMB'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;"
                               ng-bind="'COMMON_FILENAME'|translate"></label>
                        <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
                            <input type="text" class="form-control" ng-model="fileName" id="addGroupName"
                                   placeholder="请导入.xlsx表格格式文件" style="width: 100%;" ng-disabled="true"/>
                            <!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
                        </div>
                        <cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
                                          uploadifyid="uploadifyid" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto"
                                          style="margin-left: 15px;float: left;">
                        </cy:uploadifyfile>
                    </div>
                    <div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 110px;" ng-show="errorInfo===''"></div>
                    <div style="color:#ff0000;margin: 10px 0 0 110px;" ng-show="errorInfo!==''">
                        <span class="uplodify-error-img"></span>
                        <span ng-bind="errorInfo|translate"></span>
                    </div>
                    <div class="downloadRow" style="margin: 10px 0 0 29px;">
                        <!--     分省企业管理 + 集客 + 安徽 无异网手机号                   -->
                        <a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate2_yd.xlsx" class="downMod"
                           style="margin-right: 40px;"
                           ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
                           ng-show="enterpriseType == 5 && isZyzq == 1 && provinceId == 12 "></a>
                        <!--     分省企业管理 + 非集客 + 安徽 有异网手机号               -->
                        <a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate2.xlsx" class="downMod"
                           style="margin-right: 40px;"
                           ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
                           ng-show="enterpriseType == 5 && isZyzq != 1 && provinceId == 12 "></a>
                        <a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate.xlsx" class="downMod"
                           style="margin-right: 40px;"
                           ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
                           ng-show="enterpriseType != 5"></a>
                        <a target="_blank" href="/qycy/ecpmp/assets/createMemberTemplate2_new.xlsx" class="downMod"
                           style="margin-right: 40px;"
                           ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"
                           ng-show="enterpriseType == 5 && provinceId != 12"></a>
                        <span style="color: #705de1 !important; font-size: 12px;"
                              ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-click="importMember()"
                        ng-disabled="errorInfo!==''||fileUrl==''">确认导入
                </button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addOrgCancel">返回</button>
            </div>
        </div>
    </div>
</div>

<!--导入删除成员弹出框-->
<div class="modal fade" id="impoDelMebrPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_IPTDELMEMB'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;">
                            <icon>*</icon>
                            <span ng-bind="'COMMON_FILENAME'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
                            <input type="text" class="form-control" ng-model="fileName" id="addGroupName"
                                   placeholder="必填，仅支持xlsx格式" style="width: 100%;" ng-disabled="true"/>
                            <!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
                        </div>
                        <cy:uploadifyfile filelistid="fileList2" filepickerid="filePicker2" accepttype="accepttype"
                                          uploadifyid="uploadifyid2" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadDelParam" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto"
                                          style="margin-left: 15px;float: left;">
                        </cy:uploadifyfile>
                    </div>
                    <div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 110px;" ng-show="errorInfo===''"></div>
                    <div style="color:#ff0000;margin: 10px 0 0 110px;" ng-show="errorInfo!==''">
                        <span class="uplodify-error-img"></span>
                        <span ng-bind="errorInfo|translate"></span>
                    </div>
                    <div class="downloadRow" style="margin: 10px 0 0 29px;">
                        <a target="_blank" href="/qycy/ecpmp/assets/deleteMemberTemplate.xlsx" class="downMod"
                           style="margin-right: 40px;"
                           ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
                        <span style="color: #705de1 !important; font-size: 12px;"
                              ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-click="importDelMember()"
                        ng-disabled="errorInfo!==''||fileUrl==''">确认导入
                </button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addOrgCancel">返回</button>
            </div>
        </div>
    </div>
</div>


<!--c)	导出成员删除记录弹窗-->
<div class="modal fade" id="detailDelFailListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div role="document" class="modal-dialog dialog-1000">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="mydelModalLabel" ng-bind="'GROUP_EXPORTDELMEMBRECORDS'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
                                   ng-bind="'GROUP_NEWMEMBMSISDN'|translate"></label>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                <input type="text" class="form-control" id=""
                                       placeholder="{{'GROUP_PLEASEINPUTMEMBMSISDN'|translate}}"
                                       ng-model="initSel.msisdn">
                            </div>
                            <label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
                                   ng-bind="'GROUP_ORGNAME'|translate"></label>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                <input type="text" class="form-control" id=""
                                       placeholder="{{'GROUP_PLEASEINPUTGROUPNAME'|translate}}"
                                       ng-model="groupName">
                            </div>
                            <label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
                                   ng-bind="'COMMON_CREATETIME'|translate"></label>
                            <div class="col-lg-4 col-md-3 col-sm-3 col-xs-3 time" style="margin-right: 5px">
                                <div class="input-daterange input-group" id="datepicker">
                                    <input type="text" class="input-md form-control" autocomplete="off" id="start" style="padding: 2px"
                                           ng-keyup="searchOn()"/>
                                    <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                                    <input type="text" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
                                </div>
                            </div>
<!--                            退订时间-->
                            <label class="col-lg-1 col-xs-1 col-sm-3 col-md-1 control-label"
                                   ng-bind="'UNSUB_TIME_COUNT'|translate"></label>
                            <div class="col-lg-4 col-md-3 col-sm-3 col-xs-3 time" style="margin-top:5px">
                                <div class="input-daterange input-group" id="datepicker">
                                    <input type="text" class="input-md form-control" autocomplete="off" id="unsubStart" style="padding: 2px"
                                           ng-keyup="unsubSearchOn()"/>
                                    <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                                    <input type="text" class="input-md form-control" autocomplete="off" id="unsubEnd" ng-keyup="unsubSearchOn()"/>
                                </div>
                            </div>
<!--                            退订状态-->
                            <label class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
                                   style="white-space:nowrap" ng-bind="'UNSUB_STATUS'|translate"></label>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top:5px">
                                <select class="form-control" ng-model="initSel.unsubStatus"
                                        ng-options="x.id as x.name for x in servTypeStatus">
                                </select>
                            </div>
<!--                            退订渠道-->
                            <label class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
                                   style="white-space:nowrap" ng-bind="'UNSUB_CHANNEL'|translate"></label>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top:5px">
                                <select class="form-control" ng-model="initSel.channel"
                                        ng-options="x.id as x.name for x in servTypeChannel">
                                </select>
                            </div>
<!--                          搜索、 导出-->
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="margin-top:5px">
                                <button class="btn bg_purple search-btn btn1" ng-click="queryDelFailMemberList()">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                                <button class="btn bg_purple search-btn btn1" ng-click="exportUnsubFailBatch()">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'DETAIL_EXPORT'|translate"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="coorPeration-table">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
<!--                            <th><span title="序号">序号</span></th>-->
                            <th><span title="分组名称">成员号码</span></th>
                            <th><span title="分组名称">分组名称</span></th>
                            <th><span title="创建日期">创建时间</span></th>
                            <th><span title="创建日期">退订时间</span></th>
                            <th><span title="创建日期">退订状态</span></th>
                            <th><span title="创建日期">退订渠道</span></th>
                            <th><span title="创建日期">失败原因</span></th>
                            <th><span title="创建日期">错误码</span></th>
<!--                            <th><span title="操作">操作</span></th>-->
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="item in unsubFailBatchList">
<!--                            <td>{{$index + 1}}</td>-->
                            <td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
                            <td><span title="{{item.orgName}}">{{item.orgName}}</span></td>
                            <td><span title="{{changeTimeFormtat(item.subTime)}}">{{changeTimeFormtat(item.subTime)}}</span></td>
                            <td><span title="{{changeTimeFormtat(item.unsubTime)}}">{{changeTimeFormtat(item.unsubTime)}}</span></td>
                            <td><span title="{{unsubStatusMap[item.unsubStatus]}}">{{unsubStatusMap[item.unsubStatus]}}</span></td>
                            <td><span title="{{channelStatusMap[item.channel]}}">{{channelStatusMap[item.channel]}}</span></td>
                            <td><span title="{{(item.resultDesc && item.resultDesc != '0') ? item.resultDesc : '-'}}">{{(item.resultDesc && item.resultDesc != '0' && item.unsubStatus != '1') ? item.resultDesc : '-'}}</span></td>
                            <td><span title="{{(item.resultCode && item.resultCode != '0') ? item.resultCode : '-'}}">{{(item.resultCode && item.resultCode != '0' && item.unsubStatus != '1') ? item.resultCode : '-'}}</span></td>
<!--                            <td>-->
<!--                                <button class="btn bg_purple search-btn btn1" ng-click="exportUnsubFailBatch(item)">导出</button>-->
<!--                            </td>-->
                        </tr>
                        <tr ng-show="unsubFailBatchList.length === 0">
                            <td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <ptl-page tableId="1" change="queryDelFailMemberList('justPage')"></ptl-page>
            </div>
        </div>
    </div>
    <div class="modal-footer">
    </div>
</div>

<!--新增成员弹出框-->
<div class="modal fade" id="addMenbPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_ADDMEMB'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">

                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
                                <span ng-bind="'GROUP_MEMBNAME'|translate"></span>：</label>
                            <div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
                                <input type="text" class="form-control" ng-model="addMenbInfo.memberName"
                                       placeholder="{{'GROUP_PLEASEINPUTMEMBNAME'|translate}}"
                                       ng-blur="checkMemberName(addMenbInfo.memberName)"
                                       ng-show="memberNameVali"/>
                                <input type="text" class="form-control" ng-model="addMenbInfo.memberName"
                                       placeholder="{{'GROUP_PLEASEINPUTMEMBNAME'|translate}}"
                                       ng-blur="checkMemberName(addMenbInfo.memberName)"
                                       style="border-color:red;" ng-show="!memberNameVali"/>
                                <span style="color:red" ng-show="!memberNameVali">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>{{memberNameDesc|translate}}</span>
									</span>
                            </div>
                        </div>
                    </div>
                    <!-- 号码类型 -->
                    <div class="form-group" ng-show="enterpriseType == 5">
                        <div class="row">
                            <label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
                                <icon>*</icon>
                                <span ng-bind="'GROUP_MSISDNTYPE'|translate"></span>：</label>
                            <div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
                                <select class="form-control"
                                        id="msisdnType1"
                                        ng-model="selectedId"
                                        ng-options="x.id as x.msisdnType for x in msisdnTypeList"
                                        ng-blur="selectedType(selectedId)">
                                    <option value="" ng-bind="'COMMON_SELECT'|translate"></option>
                                </select>
                                <span style="color:red;" ng-show="checkMsisdnType == 'false'">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-bind="'GROUP_MSISDNTYPESELECT'|translate"></span>
									</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
                                <icon>*</icon>
                                <span ng-bind="'GROUP_NEWMEMBMSISDN'|translate"></span>：</label>
                            <div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
                                <input type="text" class="form-control" ng-model="addMenbInfo.msisdn"
                                       placeholder="{{'GROUP_PLEASEINPUTNEWMEMBMSISDN'|translate}}"
                                       ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')"
                                       ng-show="memberMsisdnVali && !memberMsisdnExist"/>
                                <input type="text" class="form-control" ng-model="addMenbInfo.msisdn"
                                       placeholder="{{'GROUP_PLEASEINPUTNEWMEMBMSISDN'|translate}}"
                                       ng-blur="checkDataUnique(addMenbInfo.msisdn,2,'')" style="border-color:red;"
                                       ng-show="!memberMsisdnVali || memberMsisdnExist"/>
                                <span style="color:red" ng-show="!memberMsisdnVali || memberMsisdnExist">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span>{{memberMsisdnDesc|translate}}</span>
									</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-3 col-xs-4 col-sm-4 col-md-3 control-label">
                                <icon>*</icon>
                                <span ng-bind="'GROUP_MEMBORG'|translate"></span>：</label>
                            <div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
                                <select class="form-control" ng-model="addMenbInfo.selectedOrg"
                                        title="{{addMenbInfo.selectedOrg.orgName}}"
                                        ng-options="x.orgName for x in groupListType"
                                        ng-disabled="fromList =='true'" ng-blur="checkSelect()"
                                        ng-show="orgSelected =='true'" id="addMenbSelect1">
                                    <option value="" ng-bind="'COMMON_SELECT'|translate"></option>
                                </select>
                                <select class="form-control" ng-model="addMenbInfo.selectedOrg"
                                        title="{{addMenbInfo.selectedOrg.orgName}}"
                                        ng-options="x.orgName for x in groupListType"
                                        ng-disabled="fromList =='true'" ng-blur="checkSelect()"
                                        style="border-color:red;"
                                        ng-show="orgSelected =='false'" id="addMenbSelect2">
                                    <option value="" ng-bind="'COMMON_SELECT'|translate"></option>
                                </select>
                                <span style="color:red;" ng-show="orgSelected =='false'">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-bind="'GROUP_MEMBORGSELECT'|translate"></span>
									</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-show="enterpriseType != 5"
                        ng-click="checkDataUnique(addMenbInfo.msisdn,2,'create')"
                        ng-disabled="!addMenbInfo.msisdn || !addMenbInfo.selectedOrg || !memberMsisdnVali
									|| !memberNameVali || memberMsisdnExist"
                        ng-bind="'COMMON_SAVE'|translate"></button>

                <button type="submit" class="btn btn-primary search-btn"
                        ng-show="enterpriseType == 5"
                        ng-click="checkDataUnique(addMenbInfo.msisdn,2,'create')"
                        ng-disabled="!addMenbInfo.msisdn || !addMenbInfo.selectedOrg || !memberMsisdnVali
									|| !memberNameVali || memberMsisdnExist || !selectedMsisdnID"
                        ng-bind="'COMMON_SAVE'|translate"></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="addMemCancel"
                        ng-bind="'COMMON_CANCLE'|translate"></button>
            </div>
        </div>
    </div>
</div>
<!--成员查询弹出框-->
<div class="modal fade" id="detailListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div role="document" class="modal-dialog dialog-1000">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'GROUP_MEMBSEARCH'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <div class="row">
                            <label class="col-lg-1 col-xs-3 col-sm-3 col-md-2 control-label"
                                   ng-bind="'GROUP_MEMBMSISDN'|translate"></label>
                            <div class="col-lg-5 col-xs-6 col-sm-6 col-md-3">
                                <input type="text" class="form-control" id=""
                                       placeholder="{{'GROUP_PLEASEINPUTMEMBMSISDN'|translate}}"
                                       ng-model="msisdn">
                            </div>
                            <label class="col-lg-1 col-xs-3 col-sm-3 col-md-1 control-label"
                                   ng-bind="'GROUP_STATUS'|translate" ng-show="isEcProvince == 111"></label>
                            <select class="col-lg-3 col-xs-3 col-sm-3 col-md-1 form-control"
                                    style="width: 150px;"
                                    id="msisdnType1"
                                    ng-model="status"
                                    ng-options="x.id as x.name for x in statusChoise"
                                    ng-show="isEcProvince == 111">
                                <option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
                            </select>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                <button class="btn bg_purple search-btn btn1" ng-click="queryMemberList(selectedItem)">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="add-table" style="margin-left:12px;margin-top:12px;">
                    <button class="btn" style="width:105px;color:#7360e1" type="button" ng-click="exportMember()"
                            ng-bind="'GROUP_MEMBEXPORT'|translate"></button>
                    <button class="btn"
                            ng-if="!is5GEnterprise"
                            ng-disabled="(selectedListTemp.length==0 || ((isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5) )||isOpenAgentValue|| isMiguMusic"
                            style="width:105px; margin-left:10px;color:#7360e1"
                            type="button" ng-click="removeMemberPop('all')"
                            ng-bind="'GROUP_BATCHDELETE'|translate"></button>
                    <button class="btn"
                            ng-if="!is5GEnterprise"
                            ng-disabled="(selectedListTemp.length==0 || ((isAllowMoveMember) && enterpriseType == 5) )||isOpenAgentValue || isMiguMusic"
                            style="width:105px; margin-left:10px;color:#7360e1"
                            type="button" ng-click="moveMemberPop('all')"
                            ng-bind="'GROUP_BATCHMOVE'|translate"></button>
                    <button ng-if="enterpriseType == 3 || (isAllowMangementMember==0 && enterpriseType == 5 && !isZYZQ)" class="btn" style="width:105px;color:#7360e1" type="button"
                            ng-click="exportFaildList()"
                            ng-bind="'FAILDLIST_EXPORT'|translate"></button>
                    <button ng-if="isEcProvince == 111" ng-disabled="selectedListTemp.length==0" class="btn"
                            style="width:105px;color:#7360e1" type="button" ng-click="bsync('all')"
                            ng-bind="'BATCH_GROUP_SYNCMEMB'|translate"></button>
                </div>

                <div class="coorPeration-table" style="max-height: 530px;overflow: auto" ng-style="{'overflow-x':isZYZQ?'scroll':'auto'}">
                    <table class="table table-striped table-hover" style="width:auto;    min-width: 100%;">
                        <thead>
                        <tr>
                            <th  ng-show="!is5GEnterprise" style="padding-left:30px;width: 5%;">
                                <input type="checkbox" ng-model="allChoose" ng-click="ifSelected()">
                            </th>
                            <th ng-bind="'GROUP_MEMBNAME'|translate"></th>
                            <th ng-bind="'GROUP_MEMBMSISDN'|translate"></th>
                            <th ng-bind="'GROUP_PROVINCE'|translate"></th>

                            <th ng-bind="'GROUP_MSISDNTYPE'|translate" ng-show="enterpriseType == 5"></th>
                            <th ng-bind="'GROUP_STATUS'|translate"></th>

                            <th ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota"><span title="本网配额数">本网配额数</span></th>
                            <th ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota"><span title="异网配额数">异网配额数</span></th>
                            <th ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota"><span title="本网剩余配额">本网剩余配额</span></th>
                            <th ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota"><span title="异网剩余配额">异网剩余配额</span></th>

                            <th ng-show="isShow&&isMonthBuQuota&&groupDiffType==0 && isShowShareQuota">屏显剩余配额</th>
                            <th ng-show="isShow&&isMonthBuQuota&&groupDiffType2==0 && isShowShareQuota" >行业挂短剩余配额</th>
                            <th ng-show="isShow&&isMonthBuQuota">挂彩剩余配额</th>

                            <th ng-show="isShow&&isMonthBuQuota&&groupDiffType==1 && isShowShareQuota">本网屏显剩余配额</th>
                            <th ng-show="isShow&&isMonthBuQuota&&groupDiffType==1 && isShowShareQuota">异网屏显剩余配额</th>


                            <th ng-show="isShow&&isMonthBuQuota&&groupDiffType2==1 && isShowShareQuota">本网行业挂短剩余配额</th>
                            <th ng-show="isShow&&isMonthBuQuota&&groupDiffType2==1 && isShowShareQuota">异网行业挂短剩余配额</th>
                            <!--                            <th ng-show="isShow&&isMonthBuQuota&&groupDiffType==1">本网挂彩剩余配额</th>-->

                            <th ng-show="isShow&&isMonthBuQuota && !isShowShareQuota">业务融合剩余配额</th>

                            <th ng-if="!isZYZQ" ng-bind="'GROUP_RSPDESC'|translate" ng-show="isEcProvince == 111"></th>
                            <th ng-if="isZYZQ" ng-bind="'GROUP_RSPDESC2'|translate" ng-show="isEcProvince == 111"></th>

                            <th >
                                <div style="float: left" ng-show="!isShow&&isMonthBuQuota&&groupDiffType" ng-click="isShow=true">>></div>
                                <div style="float: left" ng-show="isShow&&isMonthBuQuota&&groupDiffType" ng-click="isShow=false"><<</div>
                                <div style="float: left" ng-if="!is5GEnterprise">  操作</div></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="item in memberListData">
                            <td ng-if="!is5GEnterprise">
                                <input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked"
                                       ng-disabled="item.status == 1 || item.status == 9 || item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4' || ((item.status == 12 || item.status == 11) && item.rspDesc != '接口响应超时')">
                            </td>
                            <td><span title="{{item.memberName}}">{{item.memberName}}</span></td>
                            <td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
                            <td><span title="{{item.provinceName}}-{{item.cityName}}">{{item.provinceName}}-{{item.cityName}}</span></td>

                            <td ng-show="enterpriseType == 5"><span title="{{item.ecpmReserveds.reserved2}}">{{item.ecpmReserveds.reserved2}}</span>
                            </td>
                            <td ng-show="(item.status == 3  || item.status == 13) && (item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')">
                                <span title="{{unSubscribeStatusMap[item.reserved4]}}">{{unSubscribeStatusMap[item.reserved4]}}</span>
                            </td>
                            <td ng-hide="(item.status == 3  || item.status == 13)  && (item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')">
                                <span title="{{statusMap[item.status]}}">{{statusMap[item.status]}}</span></td>
                            <td ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota "><span title="{{item.currCount||'-'}}">{{item.currCount||'-'}}</span>
                            <td ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota"><span title="{{item.diffCount == null?'-':item.diffCount}}">{{item.diffCount == null?'-':item.diffCount}}</span>
                            <td ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota"><span title="{{item.pxCurrBalanceCount}}">{{item.pxCurrBalanceCount}}</span>
                            <td ng-show="(isZYZQ || isMiguMusic) && isShowShareQuota"><span title="{{item.diffCount == null?'-':item.pxDiffBalanceCount}}">{{item.diffCount == null?'-':item.pxDiffBalanceCount}}</span>

                                <!--							<td ng-show="isShow&&isMonthBuQuota&&groupDiffType==0"><span title="{{item.pxBalanceCount}}">{{item.pxBalanceCount}}</span>-->
                                <!--                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType2==0"><span title="{{item.gdBalanceCount}}">{{item.gdBalanceCount}}</span>-->
                                <!--                            <td ng-show="isShow&&isMonthBuQuota"><span title="{{item.gcBalanceCount}}">{{item.gcBalanceCount}}</span>-->
                                <!--                            pxCurrBalanceCount-->
                                <!--                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType==1"><span title="{{item.pxCurrBalanceCount}}">{{item.pxCurrBalanceCount}}</span>-->
                                <!--                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType==1"><span title="{{item.pxDiffBalanceCount}}">{{item.pxDiffBalanceCount}}</span>-->
                                <!--                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType2==1"><span title="{{item.gdCurrBalanceCount}}">{{item.gdCurrBalanceCount}}</span>-->
                                <!--                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType2==1"><span title="{{item.gdDiffBalanceCount}}">{{item.gdDiffBalanceCount}}</span>-->
                                <!--&lt;!&ndash;                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType==1"><span title="{{item.gcCurrBalanceCount}}">{{item.gcCurrBalanceCount}}</span>&ndash;&gt;-->

                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType==0 && isShowShareQuota"><span title="{{selectItem.ecpmReserveds.reserved3 == 0 &&selectItem.ecpmReserveds.reserved10 == 1?'-':item.pxBalanceCount}}">{{selectItem.ecpmReserveds.reserved3 == 0&&selectItem.ecpmReserveds.reserved10 == 1 ?'-':item.pxBalanceCount}}</span>
                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType2==0 && isShowShareQuota"><span title="{{selectItem.ecpmReserveds.reserved5 == 0 &&selectItem.ecpmReserveds.reserved10 == 1?'-':item.gdBalanceCount}}">{{selectItem.ecpmReserveds.reserved5 == 0&&selectItem.ecpmReserveds.reserved10 == 1 ?'-':item.gdBalanceCount}}</span>
                            <td ng-show="isShow&&isMonthBuQuota"><span title="{{selectItem.ecpmReserveds.reserved7 == 0 ?'-':item.gcBalanceCount}}">{{selectItem.ecpmReserveds.reserved7 == 0 ?'-':item.gcBalanceCount}}</span>

                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType==1 && isShowShareQuota"><span title="{{selectItem.ecpmReserveds.reserved3 == 0 &&selectItem.ecpmReserveds.reserved10 == 1?'-':item.pxCurrBalanceCount}}">{{selectItem.ecpmReserveds.reserved3 == 0&&selectItem.ecpmReserveds.reserved10 == 1?'-':item.pxCurrBalanceCount}}</span>
                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType==1 && isShowShareQuota"><span title="{{selectItem.ecpmReserveds.reserved4 == 0 ?'-':item.pxDiffBalanceCount}}">{{selectItem.ecpmReserveds.reserved4 == 0 ?'-':item.pxDiffBalanceCount}}</span>


                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType2==1 && isShowShareQuota"><span title="{{selectItem.ecpmReserveds.reserved5 == 0 &&selectItem.ecpmReserveds.reserved10 == 1?'-':item.gdCurrBalanceCount}}">{{selectItem.ecpmReserveds.reserved5 == 0&&selectItem.ecpmReserveds.reserved10 == 1 ?'-':item.gdCurrBalanceCount}}</span>
                            <td ng-show="isShow&&isMonthBuQuota&&groupDiffType2==1 && isShowShareQuota"><span title="{{selectItem.ecpmReserveds.reserved6 == 0 ?'-':item.gdDiffBalanceCount}}">{{selectItem.ecpmReserveds.reserved6 == 0 ?'-':item.gdDiffBalanceCount}}</span>
                            <td ng-show="isShow&&isMonthBuQuota && !isShowShareQuota"><span title="{{item.shareQuotaBalanceCount ?item.shareQuotaBalanceCount :'-'}}">{{item.shareQuotaBalanceCount ?item.shareQuotaBalanceCount :'-'}}</span>

                            <td ng-show="isEcProvince == 111"><span title="{{item.rspDesc}}">{{item.rspDesc}}</span>
                            </td>
                            <td  ng-if="!is5GEnterprise">
                                <div class="handle">
                                    <ul>
                                        <li class="removeMemP" ng-style="{ color:isOpenAgentValue? '#a297df' : '#7360e2' }"
                                            ng-click="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5 || isOpenAgentValue || isMiguMusic?'': removeMemberPop(item)"
                                            ng-hide="(item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')"
                                            ng-if="(item.status != 1 && item.status != 9 && item.status != 12 && item.status != 11 && item.status != 13) || (item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')"
                                            ng-bind="'COMMON_DELETE'|translate"
                                            ng-disabled="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5|| isMiguMusic"></li>
                                        <li class="removeMemP" ng-style="{ color:isOpenAgentValue? '#a297df' : '#7360e2' }"
                                            ng-click="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5 ||  isOpenAgentValue || isMiguMusic?'':  removeMemberPop(item)"
                                            ng-hide="(item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')"
                                            ng-if="item.status == 13" ng-bind="'COMMON_DELETE_AGAIN'|translate"
                                            ng-disabled="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5|| isMiguMusic"></li>
                                        <li class="moveMemP" ng-style="{ color:isOpenAgentValue? '#a297df' : '#7360e2' }"
                                            ng-click="isAllowMoveMember && enterpriseType == 5 ||  isOpenAgentValue|| isMiguMusic?'':  moveMemberPop(item)"
                                            ng-if="item.status == 3"
                                            ng-hide="(item.reserved4 == '1' || item.reserved4 == '2' || item.reserved4 == '4')"
                                            ng-bind="'COMMON_MOVE'|translate"
                                            ng-disabled="isAllowMoveMember && enterpriseType == 5|| isMiguMusic"></li>
                                        <li class="sync" ng-click=" isOpenAgentValue?'': sync(item)" ng-style="{ color:isOpenAgentValue? '#a297df' : '#7360e2' }" ng-bind="'GROUP_SYNCMEMB'|translate"
                                            ng-if="!(enterpriseType == 5 && isZYZQ) && (item.status ==2 || item.status ==4 || item.status ==8)"></li>
                                        <li class="bsync" ng-click="bsync(item)" ng-style="{ color:isOpenAgentValue? '#a297df' : '#7360e2' }" ng-bind="'GROUP_SYNCMEMB'|translate"
                                            ng-if="enterpriseType == 5 && isZYZQ && (item.status ==2 || item.status ==4 || item.status ==8)"></li>
                                        <li class="bsync" ng-click="bbsync(item)" ng-style="{ color:isOpenAgentValue? '#a297df' : '#7360e2' }" ng-bind="'GROUP_SYNCMEMB_UNSUBSCRIBE'|translate"
                                            ng-if="enterpriseType == 5 && isZYZQ && (item.status ==12 && item.rspDesc=='接口响应超时')"></li>
                                        <li class="bsync" ng-click="bbsync(item)"  ng-style="{ color:isOpenAgentValue? '#a297df' : '#7360e2' }" ng-bind="'GROUP_SYNCMEMB'|translate"
                                            ng-if="enterpriseType == 5 && isZYZQ && (item.status ==11 && item.rspDesc=='接口响应超时')"></li>
                                    </ul>
                                </div>
                            </td>
                            <!--<td style="font-size: small;">-->
                            <!--<button ng-click="removeMemberPop(item)"  ng-if="item.status != 12 &&  item.status != 11" class="btn add-btn" ng-bind="'COMMON_DELETE'|translate" ng-disabled="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5" >-->

                            <!--</button>-->
                            <!--<button ng-click="moveMemberPop(item)"  ng-if="item.status == 3" class="btn add-btn" ng-bind="'COMMON_MOVE'|translate" ng-disabled="(isAllowMangementMember==1||isAllowMangementMember==2) && enterpriseType == 5" >-->

                            <!--</button>-->
                            <!--<a ng-click="sync(item)" ng-bind="'GROUP_SYNCMEMB'|translate"-->
                            <!--ng-if="item.status ==2 || item.status ==4 || item.status ==6 || item.status ==7">-->
                            <!--</a>-->
                            <!--<a ng-click="bsync(item)" ng-bind="'GROUP_SYNCMEMB'|translate"-->
                            <!--ng-if="item.status ==8">-->
                            <!--</a>-->
                            <!--</td>-->
                        </tr>

                        <tr ng-show="memberListData.length<=0">
                            <td style="text-align:center" colspan="11" ng-bind="'COMMON_NODATA'|translate"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div>
                    <ptl-page tableId="1" change="queryMemberList(selectedItem,'justPage')"></ptl-page>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

<!--成员删除确认框弹出框-->
<div class="modal fade" id="deleteMemberPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <div class="row"
                             style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
                            <div class="text-center">
                                <span>请确认是否删除分组成员</span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-click="oneSelect?remove('one'):remove('all')"
                        ng-bind="'COMMON_OK'|translate"></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="delMemCancel"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>
<!--成员移动框弹出框-->
<div class="modal fade" id="moveMemberPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width: 400px; margin: 0 auto;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" ng-bind="'COMMON_TIPMOVE'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div style="text-align: center;">
                        <span>成员分组</span>
                        <select class="form-control" ng-model="selectedOrg"
                                title="{{addMenbInfo.selectedOrg.orgName}}"
                                ng-options="x.orgName for x in groupListType"
                                ng-disabled="" ng-blur="checkSelect()" style="width: 250px; display: inline;">
                            <option value="" ng-bind="'COMMON_SELECT'|translate"></option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-click="oneSelect?move('one'):move('all')"
                        ng-bind="'COMMON_OK'|translate"></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" id="delMemCancel"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!--小弹出框-->
<!-- <button type="button" class="btn btn-primary" id="open-model" data-toggle="modal" data-target=".bs-example-modal-sm">Large modal</button> -->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-2" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center" style="text-align: center;">
                    <p style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bs-example-modal-sm" id="myModal2" tabindex="-3" role="dialog" aria-labelledby="myModalLabel" style="z-index: 2">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center" style="text-align: center;">
                    <p style='font-size: 16px;color:#383838'>
                        {{tip2|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>
<!-- 导入失败弹窗-->
<div class="modal fade bs-example-modal-sm" id="importResultModel" tabindex="-5" role="dialog" style="z-index: 1"
     aria-labelledby="importResultLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="importResultLabel" ng-bind="'GROUP_IPTMBRRESULT'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center" style="text-align: center;">
                    <p style='font-size: 16px;color:#6E6E6E'>
                        {{tip|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button id="downloadFail" class="btn btn-primary search-btn"
                        ng-click="downloadFailFile()"
                        ng-bind="'GROUP_DOWNLOADFAILFILE'|translate"></button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_CLOSE'|translate"></button>
            </div>
        </div>
    </div>
</div>
</body>


</html>