body {
    background: #F8F8F8;
}

.calendar-table {
    display: none !important;
}

input[name="daterangepicker_start"],
input[name="daterangepicker_end"] {
    display: none !important;
}
.cooperation-head{
    padding:20px;
}

.time {
    position: relative;
}

.time i {
    position: absolute;
    bottom: 10px;
    right: 30px;
    top: auto;
    cursor: pointer;
}

.form-group .data-time {
    display: flex;
}

.form-group .data-time .to {
    padding: 0 20px;
}

.form-group .date .form-control {
    width: 200px;
}

.orderManage-nav {
    margin: 0 20px;
}

.cooper-title {
    margin: 20px;
    color: #383838;
    font-size: 16px;
}

.cooper-tab {
    margin: 0 20px;
    background: #fff;
    border-radius: 2px;
    padding: 36px 10px 16px;
}

.form-group .control-label icon {
    color: #ff254c;
    vertical-align: sub;
    margin-right: 2px;
}

.form-group div {
    line-height: 34px;
}

.form-group div li {
    display: inline-block;
    margin-right: 10px;
    padding-right: 10px;
    cursor: pointer;
}

.form-group div li span {
    vertical-align: middle;
    margin-right: 4px;
}

.form-group .data-time {
    display: flex;
}

.form-group .data-time .to {
    padding: 0 20px;
}

.form-group .date .form-control {
    width: 200px;
}

.order-btn {
    margin: 40px 20px;
}

.order-btn .btn {
    margin-right: 20px;
    vertical-align: top;
}