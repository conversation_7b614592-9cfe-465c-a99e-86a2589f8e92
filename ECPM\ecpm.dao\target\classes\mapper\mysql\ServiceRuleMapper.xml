<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ServiceRuleMapper">
    <resultMap id="serviceRuleWrapper"
               type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ServiceRuleWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer"/>
        <result property="servRuleCode" column="servRuleCode" javaType="java.lang.String"/>
        <result property="servType" column="servType" javaType="java.lang.Integer"/>
        <result property="subServType" column="subServType" javaType="java.lang.Integer"/>
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer"/>
        <result property="oneTemplateDayLimit" column="oneTemplateDayLimit" javaType="java.lang.Integer"/>
        <result property="oneDeliveryInterval" column="oneDeliveryInterval" javaType="java.lang.Integer"/>
        <result property="isUse" column="isUse" javaType="java.lang.Integer"/>
        <result property="creatorID" column="creatorID" javaType="java.lang.Integer"/>
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer"/>
        <result property="extInfo" column="extInfo" javaType="java.lang.String"/>
        <result property="reserved1" column="reserved1" javaType="java.lang.String"/>
        <result property="reserved2" column="reserved2" javaType="java.lang.String"/>
        <result property="reserved3" column="reserved3" javaType="java.lang.String"/>
        <result property="reserved4" column="reserved4" javaType="java.lang.String"/>
        <result property="reserved5" column="reserved5" javaType="java.lang.String"/>
        <result property="reserved6" column="reserved6" javaType="java.lang.String"/>
        <result property="reserved7" column="reserved7" javaType="java.lang.String"/>
        <result property="reserved8" column="reserved8" javaType="java.lang.String"/>
        <result property="reserved9" column="reserved9" javaType="java.lang.String"/>
        <result property="reserved10" column="reserved10" javaType="java.lang.String"/>
        <result property="createTime" column="createTime" javaType="java.util.Date"/>
        <result property="operateTime" column="operateTime" javaType="java.util.Date"/>
        <result property="periodType" column="periodType" javaType="java.lang.Integer"/>
    </resultMap>

    <resultMap id="serviceControlWrapper"
               type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ServiceControlWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer"/>
        <result property="servRuleCode" column="servRuleCode" javaType="java.lang.String"/>
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer"/>
        <result property="cardPrintStatus" column="cardPrintStatus" javaType="java.lang.String"/>
        <result property="adPrintStatus" column="adPrintStatus" javaType="java.lang.String"/>
        <result property="cardPrintStatus" column="cardPrintStatus" javaType="java.lang.String"/>
        <result property="hotlinePrintStatus" column="hotlinePrintStatus" javaType="java.lang.String"/>
        <result property="groupSendStatus" column="groupSendStatus" javaType="java.lang.String"/>
        <result property="platformStatus" column="platformStatus" javaType="java.lang.String"/>
        <result property="creatorID" column="creatorID" javaType="java.lang.Integer"/>
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer"/>
        <result property="createTime" column="createTime" javaType="java.util.Date"/>
        <result property="operateTime" column="operateTime" javaType="java.util.Date"/>
        <result property="extInfo" column="extInfo" javaType="java.lang.String"/>
        <result property="reserved1" column="reserved1" javaType="java.lang.String"/>
        <result property="reserved2" column="reserved2" javaType="java.lang.String"/>
        <result property="reserved3" column="reserved3" javaType="java.lang.String"/>
        <result property="reserved4" column="reserved4" javaType="java.lang.String"/>
        <result property="reserved5" column="reserved5" javaType="java.lang.String"/>
        <result property="reserved6" column="reserved6" javaType="java.lang.String"/>
        <result property="reserved7" column="reserved7" javaType="java.lang.String"/>
        <result property="reserved8" column="reserved8" javaType="java.lang.String"/>
        <result property="reserved9" column="reserved9" javaType="java.lang.String"/>
        <result property="reserved10" column="reserved10" javaType="java.lang.String"/>
        <result property="rxPlatformStatus" column="rxPlatformStatus" javaType="java.lang.String"/>
    </resultMap>

    <select id="queryServiceRuleList" resultMap="serviceRuleWrapper">
        SELECT
        ID,
        servRuleCode,
        servType,
        subServType,
        enterpriseID,
        oneTemplateDayLimit,
        oneDeliveryInterval,
        isUse,
        creatorID,
        operatorID,
        extInfo,
        createTime,
        operateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        periodType
        from ecpm_t_serv_rule
        <trim prefix="where" prefixOverrides="and">
            <if test="ID != null">
                and ID= #{id}
            </if>
            <if test="enterpriseID != null">
                and enterpriseID= #{enterpriseID}
            </if>
            <if test="subServType != null">
                and subServType= #{subServType}
            </if>
            <if test="servType != null">
                and servType= #{servType}
            </if>
            <if test="isUse != null">
                and isUse= #{isUse}
            </if>
            <if test="servRuleCode != null and errDesc !=''">
                and servRuleCode= #{servRuleCode}
            </if>
            <if test="enterpriseIDList != null">
                and enterpriseID in
                <foreach item="enterpriseID" index="index" collection="enterpriseIDList"
                         open="(" separator="," close=")">
                    #{enterpriseID}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="queryDefaultServiceRuleList" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ServiceRuleWrapper">
        SELECT
        ID,
        servRuleCode,
        servType,
        subServType,
        enterpriseID,
        oneTemplateDayLimit,
        oneDeliveryInterval,
        isUse,
        creatorID,
        operatorID,
        extInfo,
        createTime,
        operateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        periodType
        from ecpm_t_serv_rule
        where
        enterpriseID is null and subServType = 0 and servType in (1,2,3,4)
    </select>

    <select id="batchQueryServiceID" resultType="java.lang.Integer"
            parameterType="java.util.List">
        SELECT t.ID from ecpm_t_serv_rule t where t.ID in
        <foreach item="id" index="index" collection="list"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryServiceRuleListByServRuleCode" resultMap="serviceRuleWrapper"
            parameterType="java.util.List">
        SELECT
        ID,
        servRuleCode,
        servType,
        subServType,
        enterpriseID,
        oneTemplateDayLimit,
        oneDeliveryInterval,
        isUse,
        creatorID,
        operatorID,
        extInfo,
        createTime,
        operateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        periodType
        from ecpm_t_serv_rule where servRuleCode in
        <foreach item="servRuleCode" index="index" collection="list"
                 open="(" separator="," close=")">
            #{servRuleCode}
        </foreach>
    </select>

    <select id="queryServiceRuleListByServRuleID" resultMap="serviceRuleWrapper"
            parameterType="java.util.List">
        SELECT
        ID,
        servRuleCode,
        servType,
        subServType,
        enterpriseID,
        oneTemplateDayLimit,
        oneDeliveryInterval,
        isUse,
        creatorID,
        operatorID,
        extInfo,
        createTime,
        operateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        periodType
        from ecpm_t_serv_rule where ID in
        <foreach item="id" index="index" collection="list"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdateServiceRule" parameterType="java.util.List">
        <foreach close=";" collection="list" index="index" item="serviceRuleWrapper"
                 open="" separator=";">
            update ecpm_t_serv_rule set
            servRuleCode= #{serviceRuleWrapper.servRuleCode},
            servType= #{serviceRuleWrapper.servType},
            subServType= #{serviceRuleWrapper.subServType},
            enterpriseID= #{serviceRuleWrapper.enterpriseID},
            oneTemplateDayLimit= #{serviceRuleWrapper.oneTemplateDayLimit},
            oneDeliveryInterval= #{serviceRuleWrapper.oneDeliveryInterval},
            operatorID= #{serviceRuleWrapper.operatorID},
            isUse= #{serviceRuleWrapper.isUse},
            extInfo= #{serviceRuleWrapper.extInfo},
            operateTime= #{serviceRuleWrapper.operateTime},
            reserved1= #{serviceRuleWrapper.reserved1},
            reserved2= #{serviceRuleWrapper.reserved2},
            reserved3= #{serviceRuleWrapper.reserved3},
            reserved4= #{serviceRuleWrapper.reserved4},
            reserved5= #{serviceRuleWrapper.reserved5},
            reserved6= #{serviceRuleWrapper.reserved6},
            reserved7= #{serviceRuleWrapper.reserved7},
            reserved8= #{serviceRuleWrapper.reserved8},
            reserved9= #{serviceRuleWrapper.reserved9},
            reserved10= #{serviceRuleWrapper.reserved10},
            periodType= #{serviceRuleWrapper.periodType}
            where ID =#{serviceRuleWrapper.id}
        </foreach>
    </update>

    <insert id="batchInsertServiceRule">
        INSERT INTO ecpm_t_serv_rule
        (ID,
        servRuleCode,
        servType,
        subServType,
        enterpriseID,
        oneTemplateDayLimit,
        oneDeliveryInterval,
        isUse,
        creatorID,
        operatorID,
        extInfo,
        createTime,
        operateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        periodType
        )
        VALUES
        <foreach collection="list" item="serviceRuleWrapper"
                 separator=",">
            (
            nextval('ecpm_seq_serviceRule'),
            #{serviceRuleWrapper.servRuleCode},
            #{serviceRuleWrapper.servType},
            #{serviceRuleWrapper.subServType},
            #{serviceRuleWrapper.enterpriseID},
            #{serviceRuleWrapper.oneTemplateDayLimit},
            #{serviceRuleWrapper.oneDeliveryInterval},
            #{serviceRuleWrapper.isUse},
            #{serviceRuleWrapper.operatorID},
            #{serviceRuleWrapper.operatorID},
            #{serviceRuleWrapper.extInfo},
            #{serviceRuleWrapper.createTime},
            #{serviceRuleWrapper.operateTime},
            #{serviceRuleWrapper.reserved1},
            #{serviceRuleWrapper.reserved2},
            #{serviceRuleWrapper.reserved3},
            #{serviceRuleWrapper.reserved4},
            #{serviceRuleWrapper.reserved5},
            #{serviceRuleWrapper.reserved6},
            #{serviceRuleWrapper.reserved7},
            #{serviceRuleWrapper.reserved8},
            #{serviceRuleWrapper.reserved9},
            #{serviceRuleWrapper.reserved10},
            #{serviceRuleWrapper.periodType}
            )
        </foreach>
    </insert>

    <delete id="batchDeleteServiceRule" parameterType="java.util.List">
        delete from ecpm_t_serv_rule where ID in
        <foreach item="id" index="index" collection="list"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateServiceControl">
        update ecpm_t_serv_control set
        <trim suffixOverrides="," suffix="where enterpriseID = #{enterpriseID}">
            operateTime = now(),
            <if test="servRuleCode!=null and servRuleCode!=''">
                servRuleCode = #{servRuleCode},
            </if>
            <if test="enterpriseID!=null and enterpriseID!=''">
                enterpriseID = #{enterpriseID},
            </if>
            <if test="cardPrintStatus!=null and cardPrintStatus!=''">
                cardPrintStatus = #{cardPrintStatus},
            </if>
            <if test="adPrintStatus!=null and adPrintStatus!=''">
                adPrintStatus = #{adPrintStatus},
            </if>
            <if test="hotlinePrintStatus!=null and hotlinePrintStatus!=''">
                hotlinePrintStatus = #{hotlinePrintStatus},
            </if>
            <if test="groupSendStatus!=null and groupSendStatus!=''">
                groupSendStatus = #{groupSendStatus},
            </if>
            <if test="platformStatus!=null and platformStatus!=''">
                platformStatus = #{platformStatus},
            </if>
            <if test="rxPlatformStatus!=null and rxPlatformStatus!=''">
                rxPlatformStatus = #{rxPlatformStatus},
            </if>
            <if test="unSubscribeStatus!=null and unSubscribeStatus!=''">
                unSubscribeStatus = #{unSubscribeStatus},
            </if>
            <if test="creatorID!=null and creatorID!=''">
                creatorID = #{creatorID},
            </if>
            <if test="operatorID!=null and operatorID!=''">
                operatorID = #{operatorID},
            </if>
            <if test="extInfo!=null and extInfo!=''">
                extInfo = #{extInfo},
            </if>
            <if test="reserved1!=null and reserved1!=''">
                reserved1 = #{reserved1},
            </if>
            <if test="reserved2!=null and reserved2!=''">
                reserved2 = #{reserved2},
            </if>
            <if test="reserved3!=null and reserved3!=''">
                reserved3 = #{reserved3},
            </if>
            <if test="reserved4!=null and reserved4!=''">
                reserved4 = #{reserved4},
            </if>
            <if test="reserved5!=null and reserved5!=''">
                reserved5 = #{reserved5},
            </if>
            <if test="reserved6!=null and reserved6!=''">
                reserved6 = #{reserved6},
            </if>
            <if test="reserved7!=null and reserved7!=''">
                reserved7 = #{reserved7},
            </if>
            <if test="reserved8!=null and reserved8!=''">
                reserved8 = #{reserved8},
            </if>
            <if test="reserved9!=null and reserved9!=''">
                reserved9 = #{reserved9},
            </if>
            <if test="reserved10!=null and reserved10!=''">
                reserved10 = #{reserved10}
            </if>
            <if test="reserved10!=null and reserved10!=''">
                reserved10 = #{reserved10}
            </if>
            <if test="ywApproveCallback!=null and ywApproveCallback!=''">
                ywApproveCallback = #{ywApproveCallback}
            </if>
        </trim>

    </update>

    <insert id="insertServiceControl">
        INSERT INTO ecpm_t_serv_control
        (ID,
        servRuleCode,
        enterpriseID,
        cardPrintStatus,
        adPrintStatus,
        hotlinePrintStatus,
        groupSendStatus,
        platformStatus,
        rxPlatformStatus,
        unSubscribeStatus,
        creatorID,
        operatorID,
        createTime,
        operateTime,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        ywApproveCallback
        )
        VALUES
        (#{id},
        #{servRuleCode},
        #{enterpriseID},
        #{cardPrintStatus},
        #{adPrintStatus},
        #{hotlinePrintStatus},
        #{groupSendStatus},
        #{platformStatus},
        #{rxPlatformStatus},
        #{unSubscribeStatus},
        #{creatorID},
        #{operatorID},
        #{createTime},
        #{operateTime},
        #{extInfo},
        #{reserved1},
        #{reserved2},
        #{reserved3},
        #{reserved4},
        #{reserved5},
        #{reserved6},
        #{reserved7},
        #{reserved8},
        #{reserved9},
        #{reserved10},
        #{ywApproveCallback}
        )
    </insert>

    <select id="queryServiceControlById" parameterType="java.lang.Integer" resultMap="serviceControlWrapper">
        SELECT
        ID,
        servRuleCode,
        enterpriseID,
        cardPrintStatus,
        adPrintStatus,
        hotlinePrintStatus,
        groupSendStatus,
        platformStatus,
        unSubscribeStatus,
        creatorID,
        operatorID,
        createTime,
        operateTime,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        rxPlatformStatus,
        ywApproveCallback
        from ecpm_t_serv_control
        where
        enterpriseID = #{enterpriseID}
    </select>

    <delete id="deleteServiceControlById" parameterType="java.lang.Integer">
        delete from ecpm_t_serv_control where ID = #{id}
    </delete>


    <select id="queryDefaultServiceRule" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ServiceRuleWrapper" parameterType="java.util.Map">
        SELECT
        ID,
        servRuleCode,
        servType,
        subServType,
        enterpriseID,
        oneTemplateDayLimit,
        oneDeliveryInterval,
        isUse,
        creatorID,
        operatorID,
        extInfo,
        createTime,
        operateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        periodType
        from ecpm_t_serv_rule
        where (enterpriseID is null or enterpriseID = '')
        <if test="servType != null and servType !=''">
            and servType= #{servType}
        </if>
    </select>

    <select id="queryGreaterDefaultServiceRule" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ServiceRuleWrapper" parameterType="java.util.Map">
        SELECT
        r.id,
        r.servRuleCode ,
        r.servType ,
        r.subServType ,
        r.enterpriseID ,
        r.operatorID ,
        r.isUse ,
        r.extInfo ,
        r.operateTime ,
        r.reserved1 ,
        r.reserved2 ,
        r.reserved3 ,
        r.reserved4 ,
        r.reserved5 ,
        r.reserved6 ,
        r.reserved7 ,
        r.reserved8 ,
        r.reserved9 ,
        r.reserved10,
        if( r.oneTemplateDayLimit > dr.oneTemplateDayLimit,dr.oneTemplateDayLimit,r.oneTemplateDayLimit) oneTemplateDayLimit,
        if( r.oneDeliveryInterval &lt; dr.oneDeliveryInterval,dr.oneDeliveryInterval,r.oneDeliveryInterval) oneDeliveryInterval
        FROM
        ecpm_t_serv_rule r
        LEFT JOIN ecpm_t_enterprise_simple e ON e.id = r.enterpriseID
        JOIN ecpm_t_serv_rule dr ON dr.servType = r.servType
        AND dr.periodType = r.periodType
        AND dr.enterpriseID IS NULL
        WHERE
        ( r.enterpriseID = #{enterpriseID} OR e.parentEnterpriseID = #{enterpriseID} )
        AND ( r.oneTemplateDayLimit > dr.oneTemplateDayLimit OR r.oneDeliveryInterval &lt; dr.oneDeliveryInterval )
    </select>

</mapper>