<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.MemberMapper">
	<resultMap type="com.huawei.jaguar.dsdp.ecpe.dao.domain.MemberWrapper"
		id="memberMap">
		<result property="id" column="ID" />
		<result property="orgID" column="orgID" />
		<result property="memberName" column="memberName" />
		<result property="msisdn" column="msisdn" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="createTime" column="createTime" />
		<result property="updateTime" column="updateTime" />
		<result property="operatorID" column="operatorID" />
	</resultMap>

	<sql id="member_map">
		ID,orgID,memberName,msisdn,status,errCode,errDesc,orderID,enterpriseID,extInfo,reserved1,reserved2,reserved3,reserved4,createTime,updateTime,operatorID
	</sql>

	<insert id="createMember"
		parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.MemberWrapper"
		keyProperty="id" useGeneratedKeys="true">
		insert into ecpe_t_member
		(
		ID,
		orgID,
		memberName,
		msisdn,
		enterpriseID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID
		) values
		(
		#{id},
		#{orgID},
		#{memberName},
		#{msisdn},
		#{enterpriseID},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{createTime},
		#{updateTime},
		#{operatorID}
		)
	</insert>
	
	<select id="countMemberByMsisdnAndOrgID" resultType="java.lang.Integer">
        select 
        count(1)
        from ecpe_t_member
        where
		msisdn=#{msisdn}
		and 
		orgID=#{orgID}
    </select>
    
    <delete id="batchDeleteMemberByOrgIDAndIDs">
		delete from ecpe_t_member where 1=1
		<if test="memberIDList !=null">
		 and ID in
		<foreach item="memberID" index="index" collection="memberIDList"
			open="(" separator="," close=")">
			#{memberID}
		</foreach>
		</if>
		and 
		orgID = #{orgID}
	</delete>
    
</mapper>