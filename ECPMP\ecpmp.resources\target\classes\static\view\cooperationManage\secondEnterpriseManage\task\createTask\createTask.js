var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "service.common"])
app.controller('CreateTaskController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.operate = $location.search().operate || 'add';
    $scope.init = function () {
        $scope.tempServiceSms = 17;
        $scope.tempServiceZC = 16;
        $scope.tempServiceScreen = 3;
        $scope.tempServiceScreenUSSD = 18;	
        $scope.tempServiceScreenFlash = 19;
        $scope.tempServiceCaixin = 8;
        $scope.tempServiceCaixinServiceType = 20;
        $scope.msg = "请输入彩印内容1~750字";
        //获取enterpriseID
        $scope.enterpriseID = $.cookie('subEnterpriseID');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.operatorID = $.cookie('accountID');
        $scope.enterpriseName = $.cookie('subEnterpriseName') || '';
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.operate = $location.search().operate || '';
        if ($scope.operate != 'add') {
            $scope.taskID = $.cookie('objectID');
        }
        //获取群发开关
        $scope.isStatusOpen = 0;
        $scope.getStatus();
        // if ($scope.operate == 'detail')
        // {
        // //下拉框(投递方式)
        //    $scope.serviceTypeChoise = [
        //        {
        //            id: 16,
        //            name: "增彩"
        //
        //        }
        //    ];
        // }
        // 上传excel
        $scope.accepttypeExcel = "xlsx";
        $scope.isValidateExcel = true;
        $scope.filesizeExcel = 20;
        $scope.mimetypesExcel = ".xlsx,.xls";
        $scope.autoExcel = true;
        $scope.isCreateThumbnailExcel = false;
        $scope.uploadurlExcel = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDescExcel = "仅支持xlsx格式的文件";
        $scope.numlimitExcel = 1;
        $scope.urlListExcel = [];
        $scope.uploadParamExcel = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms',
            use: 'msidsnList'
        };
        // 上传excel  END
        $scope.$on("uploadifyidExcel", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileNameExcel = broadData.file.name;
            } else {
                $scope.fileNameExcel = "";
            }
            $scope.fileUrlExcel = fileUrl;
        });

        $scope.uploadMsisdns = 0;

        // 上传video
        $scope.accepttypeVideo = "mp4,3gp";
        $scope.isValidateVideo = true;
        $scope.filesizeVideo = 2;
        $scope.mimetypesVideo = ".mp4,.3gp";
        $scope.autoVideo = true;
        $scope.isCreateThumbnailVideo = false;
        $scope.uploadurlVideo = '/qycy/ecpmp/ecpmpServices/fileService/uploadVideo';
        $scope.uploadDescVideo = "仅支持mp4,3gp格式的文件";
        $scope.numlimitVideo = 3;
        $scope.urlListVideo = [];
        $scope.uploadParamVideo = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms',
            use: 'video'
        };
        // 上传Video  END
        $scope.$on("uploadifyidVideo", function (event, fileUrl, index, broadData) {
            if (fileUrl) {
                $scope.contentList.push({
                    frameType: 3,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).watch,
                    frameTxt: "",
                    filesize: broadData.file.size
                })
            }
            $scope.errorInfoVideo = broadData.errorInfo;
        });

        //上传图片
        $scope.accepttypeImg = "jpg,gif";
        $scope.isValidateImg = true;
        $scope.filesizeImg = 0.2;
        $scope.mimetypesImg = ".jpg,.gif";
        $scope.isCreateThumbnailImg = false;
        $scope.uploadurlImg = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.numlimitImg = 1000;
        $scope.uploadParamImg = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms'
        };

        $scope.$on("uploadifyidImg", function (event, fileUrl, fileData) {
            if (fileUrl) {
                $scope.contentList.push({
                    frameType: 1,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).review,
                    frameTxt: "",
                    filesize: fileData.file.size
                })
            } else if (fileData != '' || fileData != undefined) {
            }
        });
        //上传图片
        $scope.accepttypeImgCaixin = "jpg,jpeg,png";
        $scope.isValidateImgCaixin = true;
        $scope.filesizeImgCaixin = 0.28;
        $scope.mimetypesImgCaixin = ".jpg,.jpeg,.png,.JPG,.JPEG,.PNG";
        $scope.isCreateThumbnailImgCaixin = false;
        $scope.uploadurlImgCaixin = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.numlimitImgCaixin = 1000;
        $scope.uploadParamImgCaixin = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms_cx'
        };
        $scope.$on("uploadifyidImgCaixin", function (event, fileUrl, fileData) {
        	if (fileUrl) {
        		$scope.contentList.push({
        			frameType: 1,
        			framePicUrl: fileUrl,
        			formatFramePicUrl: CommonUtils.formatPic(fileUrl).review,
        			frameTxt: "",
        			filesize: fileData.file.size
        		})
        	} else if (fileData != '' || fileData != undefined) {
        	}
        });
        $scope.contentList = [];
        //总的文本区域长度
        $scope.ctnTextSumLength = 0;
        //总图片大小
        $scope.allPicSize = 0;
        //图片的张数
        $scope.picLength = 0;
        //视频数量
        $scope.videoLength = 0;
        //文本框的个数
        $scope.ctnTextMount = 0;
        $scope.ctnTextSum = "";
        if ($scope.operate != 'detail') {
            $scope.$watch('contentList', function (newVal, oldVal) {
                $scope.ctnTextSumLength = 0;
                $scope.allPicSize = 0;
                $scope.picLength = 0;
                $scope.videoLength = 0;
                $scope.ctnTextSum = "";
                $scope.ctnTextMount = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameType == 1) {
                        $scope.picLength += 1;
                    } else if (newVal[i].frameType == 3) {
                        $scope.videoLength += 1;
                    } else {
                        $scope.ctnTextMount += 1;
                    }
                    $scope.ctnTextSumLength += newVal[i].frameTxt.length;
                    $scope.ctnTextSum += newVal[i].frameTxt;
                    $scope.allPicSize += newVal[i].filesize;
                }
                //删除的话，就去再次校验敏感词
                if (newVal.length < oldVal.length) {
                    $scope.sensitiveCheck($scope.ctnTextSum, 2)
                }
            }, true)
        }

        //敏感校验标识,0\1预留，2表示增彩的文本内容
        $scope.isSensitive = [false, false, false];
        $scope.uploadMsisdnList = [];
        //初始化显示的信息
        $scope.sensitiveWords = {
            "0": [],
            "1": [],
            "2": []
        };
        $scope.sensitiveWordsStr = {
            "0": '',
            "1": '',
            "2": ''
        }

        $scope.groupSendTaskInfo = {
            enterpriseID: $scope.enterpriseID,
            taskName: '',
            serviceType: null,
            taskType: 1,
            content: {},
            groupSendTaskMsisdn: []
        };
        $scope.taskNameValidate = true;
        $scope.msisdnListValidate = true;
        $scope.contentTitleValidate = true;
        $scope.signatureValidate = true;
        $scope.contentValidate = true;
        $scope.contentValidateScreen = true;
        $scope.serviceTypeMap = {
    		"16": "增彩",
            "17": "短信",
            "18": "USSD",
            "19": "闪信",
            "20": "彩信",
        };


        $scope.statusMap = {
            "1": "待审核",
            "2": "驳回",
            "3": "审核通过",
            "4": "发送完成",
            "5": "发送失败",
            "6": "发送中",
            "7": "配额扣减失败",
            "8": "创建中"
        };

        if ($scope.operate != 'add') {
            $scope.queryGroupSendTaskInfo();
        }
    }
    //获取当前企业群发业务开关
    $scope.getStatus = function () {
        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": "4",
            "subservType": "16"
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
            data: JSON.stringify(req),
            success: function (result) {
                 $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
                        $scope.isStatusOpen = result.switchState;
                    }
                    if (result.switchState == 1) {
                        if ($scope.serviceTypeChoise == null) {
                            $scope.serviceTypeChoise = [];
                        }
                        //下拉框(投递方式)
                        $scope.serviceTypeChoise.push({
                            id: 16,
                            name: "增彩"
                        });
                        //添加时默认增彩
                        if ($scope.groupSendTaskInfo.serviceType == null || $scope.operate === 'add') {

                            $scope.groupSendTaskInfo.serviceType = 16;
                            $scope.changeServiceType(16);
                        }
                    }
                 })
            },
            error: function () {
                 $rootScope.$apply(function (data) {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                     }
                 )
            }
        })

        //短信
        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": "4",
            "subservType": $scope.tempServiceSms
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
            data: JSON.stringify(req),
            success: function (result) {
                 $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
                        $scope.isStatusOpen = result.switchState;
                    }
                    if (result.switchState == 1) {
                        if ($scope.serviceTypeChoise == null) {
                            $scope.serviceTypeChoise = [];
                        }

                        //下拉框(投递方式)
                        $scope.serviceTypeChoise.push({
                            id: $scope.tempServiceSms,
                            name: "短信"
                        });
                        if ($scope.groupSendTaskInfo.serviceType == null) {
                            $scope.groupSendTaskInfo.serviceType = $scope.tempServiceSms;
                            $scope.changeServiceType($scope.tempServiceSms);
                        }

                    }
                 })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
        //屏显
        var req = {
            "enterpriseId": $scope.enterpriseID,
            "servType": "4",
            "subservType": $scope.tempServiceScreen
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
            data: JSON.stringify(req),
            success: function (result) {
                 $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
                        $scope.isStatusOpen = result.switchState;
                    }
                    if (result.switchState == 1) {
                        if ($scope.serviceTypeChoise == null) {
                            $scope.serviceTypeChoise = [];
                        }
                        //下拉框(投递方式)
                        $scope.serviceTypeChoise.push({
                            id: $scope.tempServiceScreenUSSD,
                            name: "USSD"
                        });
                        $scope.serviceTypeChoise.push({
                            id: $scope.tempServiceScreenFlash,
                            name: "闪信"
                        });
                        if ($scope.groupSendTaskInfo.serviceType == null) {
                            $scope.groupSendTaskInfo.serviceType = $scope.tempServiceScreenUSSD;
                            $scope.changeServiceType($scope.tempServiceScreenUSSD);
                        }

                    }
                 })
            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
        //彩信
        var req = {
        	"enterpriseId": $scope.enterpriseID,
        	"servType": "4",
        	"subservType": $scope.tempServiceCaixin
        };
        RestClientUtil.ajaxRequest({
        	type: 'POST',
        	url: "/ecpmp/ecpmpServices/contentService/querySwitchState",
        	data: JSON.stringify(req),
        	success: function (result) {
        		 $rootScope.$apply(function () {
        		if (result.result.resultCode == '1010100000' && $scope.isStatusOpen == 0) {
        			$scope.isStatusOpen = result.switchState;
        		}
        		if (result.switchState == 1) {
        			if ($scope.serviceTypeChoise == null) {
        				$scope.serviceTypeChoise = [];
        			}
        			//下拉框(投递方式)
        			$scope.serviceTypeChoise.push({
        				id: $scope.tempServiceCaixinServiceType,
        				name: "彩信"
        			});
        			if ($scope.groupSendTaskInfo.serviceType == null) {
        				$scope.groupSendTaskInfo.serviceType = $scope.tempServiceCaixinServiceType;
        				$scope.changeServiceType($scope.tempServiceCaixinServiceType);
        			}
        			
        		}
        		 })
        	},
        	error: function () {
        		$rootScope.$apply(function (data) {
        			$scope.tip = "**********";
        			$('#myModal').modal();
        		}
        		)
        	}
        })
    };
    //校验任务名称
    $scope.checkTaskName = function (taskName) {
        $scope.taskNameValidate = $scope.validate(taskName, 30, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
    };
    //校验签名名称
    $scope.checkSignName = function (signName) {

        if (signName) {
            $scope.signatureValidate = $scope.validate(signName, 10, null, true);
        } else {
            $scope.signatureValidate = true;
        }
        if ($scope.groupSendTaskInfo.content.content) {
            $scope.checkSmsContent($scope.groupSendTaskInfo.content.content, 3, '');
        }
    };

    //校验增彩标题
    $scope.checkContentTitle = function (contentTitle) {
        $scope.contentTitleValidate = $scope.validate(contentTitle, 12, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
    };
    //校验彩信标题
    $scope.checkCXContentTitle = function (contentTitle) {
    	$scope.contentTitleValidate = $scope.validate(contentTitle, 20, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
    };

    /*校验各个字段*/
    $scope.validate = function (context, maxlength, reg) {
        if (!context) {
            return false;
        } else {
            if (context.length > maxlength) {
                return false;
            } else {
                if(reg){
                    if (!reg.test(context)) {
                        return false;
                    } else {
                        return true;
                    }
                }else {
                    return true;
                }
            }
        }
    };
    var nextDate = new Date(new Date().getTime()+86400000);
    $('#datepicker').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        startDate: nextDate,
        language: "zh-CN",
        clearBtn: true,
        initialDate:nextDate,
        autoclose: true,

    }).on('changeDate', function (e) {
        $scope.groupSendTaskInfo.timingTime = $scope.dateFormat("YYYY-mm-dd HH:MM", new Date(e.date));
    }).on('blur', function (e) {
        $scope.groupSendTaskInfo.timingTime;
    });

    //修改业务类型
    $scope.changeServiceType = function (val) {
        if (val) {
            //选择短信时
            if (val === $scope.tempServiceSms || val === $scope.tempServiceScreenUSSD || val === $scope.tempServiceScreenFlash) {
                $scope.contentList = [];
                //增彩内容插入假的值以过校验
                $scope.contentList.push({
                    framePicUrl: "",
                    formatFramePicUrl: "",
                    frameTxt: true,
                    filesize: ""
                });
                $scope.groupSendTaskInfo.content.contentTitle = true;

                //清空短信坑的内容
                $scope.groupSendTaskInfo.content.signature = ""; //签名
                $scope.groupSendTaskInfo.timingTime = null; //发送时间
                $scope.groupSendTaskInfo.content.content = ""; //内容

                $scope.contentTitleValidate = true;
                $scope.msg = "请输入彩印内容1~750字";
                if(val === $scope.tempServiceScreenUSSD || val === $scope.tempServiceScreenFlash){
                	$scope.msg = "请输入彩印内容1~62字";
                }

            } else {
                //清空增彩内容
                $scope.contentList = [];
                $scope.groupSendTaskInfo.content.contentTitle = "";

                //短信内容插入假的值以过校验
                $scope.signatureValidate = true;
                $scope.groupSendTaskInfo.content.content = true;
                $scope.contentValidate = true;
                $scope.contentTitleValidate = true;
                $scope.contentValidateScreen = true;
                //
            }
        }
    };

    $scope.addMsisdn = function () {
        $scope.groupSendTaskInfo.groupSendTaskMsisdn.push({
            msisdn: ''
        });
    }

    $scope.deleteMsisdn = function (index) {
        $scope.groupSendTaskInfo.groupSendTaskMsisdn.splice(index, 1);
        $scope.checkMsisdnList($scope.groupSendTaskInfo.groupSendTaskMsisdn);
    }

    //校验接收号码
    $scope.checkMsisdnList = function (msisdnList) {
        $scope.msisdnListValidate = true;
        $scope.msisdnListDesc = '';
        var msisdnListTemp = [];
        if (msisdnList) {
            jQuery.each(msisdnList, function (i, e) {
                msisdnListTemp.push(e.msisdn);
            });
            var msisdnList_unique = msisdnListTemp.filter(function (element, index, array) {
                return array.indexOf(element) === index;
            });
            if (msisdnList_unique.length < msisdnList.length) {
                $scope.msisdnListValidate = false;
                $scope.msisdnListDesc = '接收号码重复';
            }
            jQuery.each(msisdnList, function (i, e) {
                if (!e.msisdn) {
                    $scope.msisdnListValidate = false;
                    $scope.msisdnListDesc = '接收号码必填，仅支持数字输入';
                }
            });
            if (!$scope.msisdnListValidate) {
                return;
            }
        }
    }

    $scope.addTextCtn = function () {
        $scope.contentList.push({
            framePicUrl: "",
            formatFramePicUrl: "",
            frameTxt: "",
            filesize: ""
        })
    }

    $scope.deleteCtnOrPic = function (index) {
        $scope.contentList.splice(index, 1);
        $scope.fileListImg;
    }

    $scope.goBack = function () {
        window.location.href = '../taskList/taskList.html';
    }

    //提交前校验
    $scope.checkBeforeSubmit = function () {
        $scope.checkTaskName($scope.groupSendTaskInfo.taskName);
        $scope.checkMsisdnList($scope.groupSendTaskInfo.groupSendTaskMsisdn);
        $scope.checkContentTitle($scope.groupSendTaskInfo.content.contentTitle);
        if (!$scope.taskNameValidate || !$scope.contentTitleValidate || !$scope.msisdnListValidate) {
            return;
        }
        // if(!$scope.groupSendTaskInfo.timingTime){
        //    $scope.groupSendTaskInfo.timingTime = $("#datepicker").val();
        // }
        if ($scope.groupSendTaskInfo.timingTime) {
            var date = new Date($scope.groupSendTaskInfo.timingTime);
            if (!date.getTime() || date <= new Date()) {
                return;
            }
            $scope.groupSendTaskInfo.timingTime = date;
        }
        if ($scope.ctnTextSum) {
            $scope.sensitiveCheck($scope.ctnTextSum, 2, 'save');
        } else {
            $scope.submitReview();
        }
    }

    $scope.submitReview = function () {
    	$scope.groupSendTaskInfo.subServType = $scope.groupSendTaskInfo.serviceType;
    	if ($scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenUSSD || $scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenFlash) {
    		$scope.groupSendTaskInfo.subServType = 3;
        }
    	if ($scope.groupSendTaskInfo.serviceType === $scope.tempServiceCaixinServiceType) {
    		$scope.groupSendTaskInfo.subServType = 8;
    	}
        var req = {
            "groupSendTaskInfo": {
                "enterpriseID": parseInt($scope.enterpriseID),
                "objectID": $scope.operate == 'modify' ? parseInt($scope.taskID) : '',
                "taskName": $scope.groupSendTaskInfo.taskName,
                "serviceType": $scope.groupSendTaskInfo.serviceType,
                "taskType": 1,
                "content": {
                    "contentID": $scope.operate == 'modify' ? $scope.contentInfoDetail.contentID : '',
                    "enterpriseID": $scope.enterpriseID,
                    "thirdpartyType": 0,
                    "enterpriseName": $scope.enterpriseName,
                    "servType": 4,
                    "subServType": $scope.groupSendTaskInfo.subServType,
                    "contentType": 5,
                    "operatorID": $scope.operatorID,
                    "contentTitle": $scope.groupSendTaskInfo.content.contentTitle,
                    "content": $scope.groupSendTaskInfo.content.content, //内容
                    "signature": $scope.groupSendTaskInfo.content.signature //签名
                },
                "timingTime": $scope.groupSendTaskInfo.timingTime, //发送时间
                "groupSendTaskMsisdn": $scope.groupSendTaskInfo.groupSendTaskMsisdn
            },
            "fileUrl": $scope.fileUrlExcel
        };
        //短信时不需要contentFrameMappingList
        if ($scope.groupSendTaskInfo.serviceType === $scope.tempServiceSms || $scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenUSSD || $scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenFlash) {
            req.groupSendTaskInfo.content.contentType = 1;
            req.groupSendTaskInfo.content.contentTitle = "";
            req.groupSendTaskInfo.taskType = 2;
        } else {

            //清空跳过校验时存的内容
            req.groupSendTaskInfo.content.signature = ""; //签名
            req.groupSendTaskInfo.timingTime = null; //发送时间
            req.groupSendTaskInfo.content.content = ""; //内容
            //彩信需要发送时间
            if ($scope.groupSendTaskInfo.serviceType === $scope.tempServiceCaixinServiceType) {
        		req.groupSendTaskInfo.taskType = 2;
        		req.groupSendTaskInfo.timingTime = $scope.groupSendTaskInfo.timingTime;
			}

            // 封装图片内容
            req.groupSendTaskInfo.content.contentFrameMappingList = [];
            var totalSize = 0;
            for (var j in $scope.contentList) {
                var item = $scope.contentList[j];
                if (item.frameType == 1) {
                    req.groupSendTaskInfo.content.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 1,
                        framePicUrl: item.framePicUrl,
                        framePicSize: item.filesize
                    });
                } else if (item.frameType == 3) {
                    req.groupSendTaskInfo.content.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 3,
                        framePicUrl: item.framePicUrl,
                        framePicSize: item.filesize
                    });
                } else {
                    req.groupSendTaskInfo.content.contentFrameMappingList.push({
                        frameNo: parseInt(j) + 1,
                        frameType: 2,
                        frameTxt: item.frameTxt,
                    });
                }
                totalSize += item.filesize;
            }
            if($scope.groupSendTaskInfo.serviceType === $scope.tempServiceZC&&totalSize>2*1024*1024){
                $scope.tip = "增彩包大小超过2M";
                $('#myModal').modal();
                return;
            }
        }


        if ($scope.operate == 'add') {
            $scope.createGroupSendTask(req);
        } else if ($scope.operate == 'modify') {
            var taskInfo = req.groupSendTaskInfo;
            var changed = false;
            if (taskInfo.groupSendTaskMsisdn.length != 0) {
                changed = true;
            }
            if (req.fileUrl) {
                changed = true;
            }
            if( taskInfo.content.contentFrameMappingList&&taskInfo.content.contentFrameMappingList.length == $scope.contentInfoDetail.contentFrameMappingList.length) {
                for (var i = 0; i < taskInfo.content.contentFrameMappingList.length; i++) {
                    if (taskInfo.content.contentFrameMappingList[i].frameType == 2) {
                        if (taskInfo.content.contentFrameMappingList[i].frameTxt
                            != $scope.contentInfoDetail.contentFrameMappingList[i].frameTxt) {
                            changed = true;
                        }
                    } else {
                        if (taskInfo.content.contentFrameMappingList[i].framePicUrl
                            != $scope.contentInfoDetail.contentFrameMappingList[i].framePicUrl) {
                            changed = true;
                        }
                    }
                }
            } else {
                changed = true;
            }
            if (taskInfo.taskName == $scope.groupSendTaskInfoQueryed.taskName &&
                taskInfo.serviceType == $scope.groupSendTaskInfoQueryed.serviceType &&
                taskInfo.content.contentTitle == $scope.contentInfoDetail.contentTitle &&
                !changed) {
                $scope.req = {
                    "groupSendTaskInfo": $scope.groupSendTaskInfoQueryed
                };
                $scope.tip = "未有内容修改，是否提交？";
                $('#updatePop').modal();
            } else {
                $scope.updateGroupSendTask(req);
            }
        }

    };

    $scope.createGroupSendTask = function (req) {
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/createGroupSendTask",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        window.location.href = '../taskList/taskList.html';
                    }
                    else if (data.resultCode == '1030120090') {
                        $scope.noRepeat = false;
                        $scope.tip = "营帐业务关闭";
                        $('#myModal').modal();
                    }
                    else {
                        $scope.tip = data.resultCode;
                        if (data.resultDesc == "the amount is not enough") {
                            $scope.tip = "1030120049";
                        }
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    $scope.updateGroupSendTask = function (req) {
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/updateGroupSendTask",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        window.location.href = '../taskList/taskList.html';
                    } else if (data.resultCode == '1030120090') {
                        $scope.noRepeat = false;
                        $scope.tip = "营帐业务关闭";
                        $('#myModal').modal();
                    }else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });
    }

    $scope.queryGroupSendTaskInfo = function () {
        var req = {
            "taskID": parseInt($scope.taskID)
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/groupSendTaskService/queryGroupSendTaskInfo",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                //$rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030100000') {
                    $scope.groupSendTaskInfoQueryed = result.groupSendTaskInfo;
                    $scope.mapDataToHtml();
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
                //})
            },
            error: function () {
                //$rootScope.$apply(function () {
                $scope.tip = '**********';
                $('#myModal').modal();
                //})
            }
        });
    }

    $scope.exportFile = function () {
        var req = {
            "param": {
                "taskID": parseInt($scope.taskID),
                //"token": $.cookie("token"),
                //"isExport": 0
            },
            "url": "/qycy/ecpmp/ecpmpServices/groupSendTaskService/downGroupSendMsisdnCsvFileService",
            "method": "get"
        }
        CommonUtils.exportFile(req);
    };

    $scope.dateFormat = function (fmt, date) {
        let ret;
        let opt = {
            "Y+": date.getFullYear().toString(),        // 年
            "m+": (date.getMonth() + 1).toString(),     // 月
            "d+": date.getDate().toString(),            // 日
            "H+": date.getHours().toString(),           // 时
            "M+": date.getMinutes().toString(),         // 分
            "S+": date.getSeconds().toString()          // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
            ret = new RegExp("(" + k + ")").exec(fmt);
            if (ret) {
                fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
            }
        }
        return fmt;
    };

    $scope.mapDataToHtml = function () {
        $scope.groupSendTaskInfo.taskName = $scope.groupSendTaskInfoQueryed.taskName;
        $scope.groupSendTaskInfo.serviceType = $scope.groupSendTaskInfoQueryed.serviceType;
        $scope.changeServiceType( $scope.groupSendTaskInfoQueryed.serviceType);
        $scope.groupSendTaskInfo.status = $scope.groupSendTaskInfoQueryed.status;
        $scope.groupSendTaskInfo.groupSendTaskMsisdn = [];
        $scope.contentInfoDetail = $scope.groupSendTaskInfoQueryed.content;
        if ($scope.groupSendTaskInfoQueryed.timingTime) {

            $scope.groupSendTaskInfo.timingTime = $scope.dateFormat("YYYY-mm-dd HH:MM", new Date($scope.groupSendTaskInfoQueryed.timingTime));
            $scope.groupSendTaskInfo.timingTimeTemp = $scope.groupSendTaskInfo.timingTime;
        }
        if ($scope.contentInfoDetail) {
            if($scope.contentInfoDetail.contentTitle){
                $scope.groupSendTaskInfo.content.contentTitle = $scope.contentInfoDetail.contentTitle;

            }
            $scope.groupSendTaskInfo.content.content = $scope.contentInfoDetail.content;
            $scope.groupSendTaskInfo.content.signature = $scope.contentInfoDetail.signature;
            if ($scope.contentInfoDetail.contentFrameMappingList && $scope.contentInfoDetail.contentFrameMappingList.length > 0) {
                for (var j in $scope.contentInfoDetail.contentFrameMappingList) {
                    var item = $scope.contentInfoDetail.contentFrameMappingList[j];
                    //图片类型
                    if (item.frameType === 1) {
                        $scope.contentList.push({
                            frameType: 1,
                            framePicUrl: item.framePicUrl,
                            formatFramePicUrl: CommonUtils.formatPic(item.framePicUrl).review,
                            frameTxt: "",
                            filesize: item.framePicSize
                        })
                    } else if (item.frameType === 2) {
                        $scope.contentList.push({
                            frameType: 2,
                            framePicUrl: "",
                            formatFramePicUrl: "",
                            frameTxt: item.frameTxt,
                            filesize: ""
                        })
                    } else if (item.frameType === 3) {
                        $scope.contentList.push({
                            frameType: 3,
                            framePicUrl: item.framePicUrl,
                            formatFramePicUrl: CommonUtils.formatPic(item.framePicUrl).watch,
                            frameTxt: "",
                            filesize: item.framePicSize
                        })
                    } else {

                    }
                }
            }
        }
    }

    $scope.checkSmsContent = function (content, index, condition) {
        if(content){
            if($scope.groupSendTaskInfo.content.signature){
                content = $scope.groupSendTaskInfo.content.signature + content;
            }
            if($scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenUSSD
                || $scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenFlash){
            	$scope.contentValidateScreen = $scope.validate(content, 62, null, true);
            }
            else{
            	$scope.contentValidate = $scope.validate(content, 750, null, true);
            }
            $scope.sensitiveCheck(content, index, condition);
        }else {
        	if($scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenUSSD || $scope.groupSendTaskInfo.serviceType === $scope.tempServiceScreenFlash){
            	$scope.contentValidateScreen = true;
            }
            else{
            	$scope.contentValidate = true;
            }
        }
    };

    //校验敏感词
    $scope.sensitiveCheck = function (content, index, condition) {
        if (!content) {
            return;
        }
        content = content.replace(/\s/g, '');
        $scope.condition = condition;
        var req = {
            "content": content || '',
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            async:false,
            data: JSON.stringify(req),
            success: function (result) {
                // $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '1030120017') {
                    $scope.sensitiveWords[index] = result.sensitiveWords || [];
                    if ($scope.sensitiveWords[index].length > 0) {
                        $scope.isSensitive[index] = true;
                        $scope.sensitiveWordsStr[index] = $scope.sensitiveWords[index].join('、');
                    } else {
                        $scope.isSensitive[index] = false;
                    }
                } else if (data.resultCode == '1030100000') {
                    $scope.sensitiveWords[index] = [];
                    $scope.isSensitive[index] = false;
                    if($scope.condition =='save'){
                        $scope.submitReview();
                    }
                }else{
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
                // })
            },
            error: function () {
                // $rootScope.$apply(function () {
                $scope.tip = '**********';
                $('#myModal').modal();
                // })
            }
        });
    };

});

app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])