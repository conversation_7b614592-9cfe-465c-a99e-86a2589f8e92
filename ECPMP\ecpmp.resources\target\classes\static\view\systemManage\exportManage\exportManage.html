<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>导出文件下载</title>
    <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
    <link href="../../../css/reset.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap-datepicker.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
    <link rel="stylesheet" type="text/css" href="../../../css/searchList.css"/>
    <link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
    <link href="../../../css/bootstrap-datepicker.css" rel="stylesheet">

    <script type="text/javascript" src="exportManageCtrl.js"></script>



    <style>
        label {
            min-width: 120px;
        }
        .col-xs-1{
            width: 1%;
        }
        .cond-div {
            min-width: 240px;
        }
    </style>

</head>
<body ng-app="myApp" ng-controller="ExportManagementController" ng-init="init()" class="body-min-width">
<div class="cooperation-manage">
    <div class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'EXPORT_MANAGEMENT' | translate">导出文件管理</span>
    </div>
    <div class="cooperation-search">
        <form class="form-horizontal">
            <div class="form-group">
                <label for="fileName" class="col-xs-1 control-label"
                       style="white-space:nowrap" ng-bind="'FILE_NAME'|translate"></label>

                <div class="cond-div col-xs-2">
                    <input type="text" autocomplete="off" class="form-control" id="fileName"
                           placeholder="{{'COMMON_INPUT_FILENAME'|translate}}" ng-model="fileName">
                </div>

                <label for="fileType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
                       style="white-space: nowrap;" ng-bind="'FILE_TYPE'|translate"></label>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
                    <select class="form-control" name="fileType" ng-model="fileType"
                            ng-options="x.id as x.name for x in fileTypeSelect" style="max-width:200px"
                            ng-disabled="fileTypeShow">
                    </select>
                </div>
                <div style="width: 506px">
                    <label for="exportTime" class="col-xs-1 control-label" ng-bind="'EXPORT_TIME'|translate"></label>
                    <div class="cond-div col-xs-4 time" style="width: 75%">
                        <div class="input-daterange input-group" id="datepicker">
                            <input type="text" class="input-md form-control" autocomplete="off" id="start"
                                   ng-keyup="searchOn()"/>
                            <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                            <input type="text" class="input-md form-control" autocomplete="off" id="end"
                                   ng-keyup="searchOn()"/>
                        </div>
                    </div>
                </div>
                <div class="cond-div col-xs-4">
                    <button type="submit" class="btn search-btn" ng-click="queryExportTaskInfoList()"
                            style="margin-left: 20px">
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
            </div>

        </form>
    </div>
    <div style="margin: 20px">
        <p style="font-size: 16px" ng-bind="'EXPORT_FILE_LIST'|translate"></p>
    </div>
</div>
<div class="coorPeration-table">
    <table class="table table-striped table-hover">
        <thead>
        <tr>
            <th style="width:15%" ng-bind="'FILE_CODE'|translate"></th>
            <th style="width:25%" ng-bind="'FILE_NAME'|translate"></th>
            <th style="width:15%" ng-bind="'FILE_REMARKS'|translate"></th>
            <th style="width:25%" ng-bind="'FILE_TYPE'|translate"></th>
            <th style="width:15%" ng-bind="'EXPORT_TIME'|translate"></th>
            <th style="width:15%" ng-bind="'FILE_STATUS'|translate"></th>
            <th style="width:20%" ng-bind="'COMMON_OPERATE'|translate"></th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="item in exportTaskInfoData">
            <td><span title="{{item.taskID}}" ng-bind="item.taskID"></span></td>
            <td><span title="{{item.fileName}}" ng-bind="item.fileName"></span></td>
            <td><span title="{{item.ecpmReserveds.reserved1}}" ng-bind="item.ecpmReserveds.reserved1"></span></td>
            <td><span title="{{fileTypeMap[item.taskType]}}" ng-bind="fileTypeMap[item.taskType]"></span></td>
            <td><span title="{{item.exportTime|formatDate}}" ng-bind="item.exportTime|formatDate"></span></td>
            <td><span title="{{item.taskStatus|formatExportStatus}}"
                      ng-bind="item.taskStatus|formatExportStatus"></span></td>
            <td class="coorPeration-table-a">
                <a ng-if="!showDownloadByCreateTimeAndTaskStatus(item.createTime,item.taskStatus)"
                   href="/qycy/ecpmp/ecpmpServices/exportTaskService/download?taskID={{item.taskID}}" target="_self"
                   ng-bind="'EXPORT_DOWNLOAD'|translate"></a>
                <span ng-if="showDownloadByCreateTimeAndTaskStatus(item.createTime,item.taskStatus)" ng-bind="'EXPORT_DOWNLOAD'|translate" style="color: #a29b9b;"></span>
            </td>
        </tr>
        <tr ng-show="exportTaskInfoData===null||exportTaskInfoData.length===0">
            <td style="text-align:center" colspan="7">暂无数据</td>
        </tr>
        </tbody>
    </table>
</div>
<div>
    <ptl-page tableId="0" change="queryExportTaskInfoList('justPage')"></ptl-page>
</div>

<!-- 重置密码 -->
<div class="modal fade" id="resPassword" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:450px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">重置密码</h4>
            </div>
            <div class="modal-body">
                <p style="text-align:center;margin-top:30px;font-size:16px">请确认是否重置密码?</p>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-click="resetPassword()">是
                </button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        id="resetBlack" style="margin-left: 20px">否
                </button>
            </div>
        </div>
    </div>
</div>
<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     style="z-index:555555;">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838;text-align: center'>
                        {{tip|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer" style="text-align: center">
                <button type="submit" class="btn btn-primary" data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>
</body>
</html>