<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberUnsubRecordMapper" >
    <insert id="batchInsert" parameterType="java.util.List" >
        insert into ecpm_t_member_unsub_record (
            id,
            enterprise_id,
            msisdn,
            org_id,
            org_name,
            org_type,
            create_time,
            sub_time,
            unsub_time,
            unsub_status,
            channel,
            result_code,
            result_desc,
            batch_no,
            order_id
        )
        values 
        <foreach collection ="list" item="item" index="index" separator =",">
        	(
        	#{item.id},
            #{item.enterpriseId},
            #{item.msisdn},
            #{item.orgId},
            #{item.orgName},
            #{item.orgType},
            #{item.createTime},
            #{item.subTime},
            #{item.unsubTime},
            #{item.unsubStatus},
            #{item.channel},
            #{item.resultCode},
            #{item.resultDesc},
            #{item.batchNo},
            #{item.orderId}
            )
        </foreach>
    </insert>
    <update id="updateStatusById">
        UPDATE
            ecpm_t_member_unsub_record
        SET
            unsub_status = #{unsubStatus},
            result_code = #{resultCode},
            result_desc = #{resultDesc}
        WHERE id = #{id}
    </update>
    <select id="queryUnsubRecordBatch" resultType="com.huawei.jaguar.dsdp.ecpm.model.MemberUnsubRecord">
        SELECT
        id,
        enterprise_id enterpriseId,
        msisdn,
        org_id orgId,
        org_name orgName,
        org_type orgType,
        create_time createTime,
        sub_time subTime,
        unsub_time unsubTime,
        unsub_status unsubStatus,
        channel,
        result_code resultCode,
        result_desc resultDesc,
        batch_no batchNo,
        order_id orderId
        FROM
        ecpm_t_member_unsub_record
        WHERE enterprise_id = #{req.enterpriseId}
        <if test="req.msisdn != null and req.msisdn != ''">
            AND msisdn LIKE CONCAT('%', #{req.msisdn}, '%')
        </if>
        <if test="req.orgName != null and req.orgName != ''">
            AND org_name LIKE CONCAT('%', #{req.orgName}, '%')
        </if>
        <if test="req.orgType != null">
            AND org_type = #{req.orgType}
        </if>
        <if test="req.subTimeStart != null and req.subTimeStart != ''">
            AND sub_time <![CDATA[>=]]> STR_TO_DATE(#{req.subTimeStart}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.subTimeEnd != null and req.subTimeEnd != ''">
            AND sub_time <![CDATA[<=]]> STR_TO_DATE(#{req.subTimeEnd}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.unsubTimeStart != null and req.unsubTimeStart != ''">
            AND unsub_time <![CDATA[>=]]> STR_TO_DATE(#{req.unsubTimeStart}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.unsubTimeEnd != null and req.unsubTimeEnd != ''">
            AND unsub_time <![CDATA[<=]]> STR_TO_DATE(#{req.unsubTimeEnd}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.unsubStatus != null">
            AND unsub_status = #{req.unsubStatus}
        </if>
        <if test="req.channel != null">
            AND channel = #{req.channel}
        </if>
        ORDER BY create_time DESC
        LIMIT #{pageStart},#{pageSize}
    </select>
    <select id="queryUnsubRecordBatchTotal" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM
        ecpm_t_member_unsub_record
        WHERE enterprise_id = #{req.enterpriseId}
        <if test="req.msisdn != null and req.msisdn != ''">
            AND msisdn LIKE CONCAT('%', #{req.msisdn}, '%')
        </if>
        <if test="req.orgName != null and req.orgName != ''">
            AND org_name LIKE CONCAT('%', #{req.orgName}, '%')
        </if>
        <if test="req.orgType != null">
            AND org_type = #{req.orgType}
        </if>
        <if test="req.subTimeStart != null and req.subTimeStart != ''">
            AND sub_time <![CDATA[>=]]> STR_TO_DATE(#{req.subTimeStart}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.subTimeEnd != null and req.subTimeEnd != ''">
            AND sub_time <![CDATA[<=]]> STR_TO_DATE(#{req.subTimeEnd}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.unsubTimeStart != null and req.unsubTimeStart != ''">
            AND unsub_time <![CDATA[>=]]> STR_TO_DATE(#{req.unsubTimeStart}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.unsubTimeEnd != null and req.unsubTimeEnd != ''">
            AND unsub_time <![CDATA[<=]]> STR_TO_DATE(#{req.unsubTimeEnd}, '%Y%m%d%H%i%s')
        </if>
        <if test="req.unsubStatus != null">
            AND unsub_status = #{req.unsubStatus}
        </if>
        <if test="req.channel != null">
            AND channel = #{req.channel}
        </if>
    </select>
    <select id="selectByOrderId" resultType="com.huawei.jaguar.dsdp.ecpm.model.MemberUnsubRecord">
        SELECT
        id,
        enterprise_id enterpriseId,
        msisdn,
        org_id orgId,
        org_name orgName,
        org_type orgType,
        create_time createTime,
        sub_time subTime,
        unsub_time unsubTime,
        unsub_status unsubStatus,
        channel,
        result_code resultCode,
        result_desc resultDesc,
        batch_no batchNo,
        order_id orderId
        FROM
        ecpm_t_member_unsub_record
        WHERE order_id = #{orderId}

    </select>
    <select id="selectByOrderAndMsisdn" resultType="com.huawei.jaguar.dsdp.ecpm.model.MemberUnsubRecord">
        SELECT
            id,
            enterprise_id enterpriseId,
            msisdn,
            org_id orgId,
            org_name orgName,
            org_type orgType,
            create_time createTime,
            sub_time subTime,
            unsub_time unsubTime,
            unsub_status unsubStatus,
            channel,
            result_code resultCode,
            result_desc resultDesc,
            batch_no batchNo,
            order_id orderId
        FROM
            ecpm_t_member_unsub_record
        WHERE order_id = #{orderId}
        AND msisdn = #{msisdn}
    </select>

    <select id="selectByMsisdnAndSubTime" resultType="com.huawei.jaguar.dsdp.ecpm.model.MemberUnsubRecord">
        SELECT
            id,
            enterprise_id enterpriseId,
            msisdn,
            org_id orgId,
            org_name orgName,
            org_type orgType,
            create_time createTime,
            sub_time subTime,
            unsub_time unsubTime,
            unsub_status unsubStatus,
            channel,
            result_code resultCode,
            result_desc resultDesc,
            batch_no batchNo,
            order_id orderId
        FROM
            ecpm_t_member_unsub_record
        WHERE msisdn = #{msisdn}
          AND sub_time = #{subTime}
          AND enterprise_id = #{enterpriseId}
        order by create_time desc
    </select>
</mapper>