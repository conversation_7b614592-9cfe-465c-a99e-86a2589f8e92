<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper.MemberUnsubRecordMapper">

    <select id="queryUnsubRecord" resultType="com.huawei.jaguar.dsdp.bill.dao.domain.MemberUnsubRecord">
        SELECT
            id,
            enterprise_id enterpriseId,
            msisdn,
            org_id orgId,
            org_name orgName,
            org_type orgType,
            create_time createTime,
            sub_time subTime,
            unsub_time unsubTime,
            unsub_status unsubStatus,
            channel,
            result_code resultCode,
            result_desc resultDesc,
            batch_no batchNo,
            order_id orderId
        FROM
            ecpm_t_member_unsub_record
        WHERE msisdn = #{msisdn}
          AND enterprise_id = #{enterpriseId}
          AND sub_time = #{subTime}
          AND create_time > STR_TO_DATE(CONCAT(#{startTime}, '01 00:00:00'), '%Y%m%d %H:%i:%s')
          AND create_time <![CDATA[ < ]]> STR_TO_DATE(CONCAT(#{endTime}, '01 00:00:00'), '%Y%m%d %H:%i:%s')
        order by create_time desc
    </select>
</mapper>