<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SyncServiceRuleHisMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.SyncServiceRuleHisWrapper" >
        <result column="transactionID" property="transactionID" jdbcType="VARCHAR" />
        <result column="enterpriseID" property="enterpriseID" jdbcType="INTEGER" />
        <result column="corpID" property="corpID" jdbcType="VARCHAR" />
        <result column="oprDate" property="oprDate" jdbcType="TIMESTAMP" />
        <result column="servType" property="servType" jdbcType="TINYINT" />
        <result column="subServType" property="subServType" jdbcType="TINYINT" />
        <result column="bossID" property="bossID" jdbcType="VARCHAR" />
        <result column="effectiveTime" property="effectiveTime" jdbcType="TIMESTAMP" />
        <result column="expiryTime" property="expiryTime" jdbcType="TIMESTAMP" />
        <result column="channel" property="channel" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="pushMaxTimes" property="pushMaxTimes" jdbcType="INTEGER" />
        <result column="memNumber" property="memNumber" jdbcType="INTEGER" />
        <result column="chargeMsisdn" property="chargeMsisdn" jdbcType="VARCHAR" />
        <result column="experienceNum" property="experienceNum" jdbcType="INTEGER" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="reserved1" property="reserved1" jdbcType="VARCHAR" />
        <result column="reserved2" property="reserved2" jdbcType="VARCHAR" />
        <result column="reserved3" property="reserved3" jdbcType="VARCHAR" />
        <result column="reserved4" property="reserved4" jdbcType="VARCHAR" />
        <result column="reserved5" property="reserved5" jdbcType="VARCHAR" />
        <result column="reserved6" property="reserved6" jdbcType="VARCHAR" />
        <result column="reserved7" property="reserved7" jdbcType="VARCHAR" />
        <result column="reserved8" property="reserved8" jdbcType="VARCHAR" />
        <result column="reserved9" property="reserved9" jdbcType="VARCHAR" />
        <result column="reserved10" property="reserved10" jdbcType="VARCHAR" />
        <result column="cancelTime" property="cancelTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SyncServiceRuleHisWrapper" >
        insert into ecpm_t_sync_service_rule_his (transactionID, enterpriseID, corpID, 
            oprDate, servType, subServType, 
            bossID, effectiveTime, expiryTime, 
            channel, status, pushMaxTimes, 
            memNumber, chargeMsisdn, experienceNum, 
            createTime, updateTime, reserved1, 
            reserved2, reserved3, reserved4, 
            reserved5, reserved6, reserved7, 
            reserved8, reserved9, reserved10, 
            cancelTime)
        values (#{transactionID,jdbcType=VARCHAR}, #{enterpriseID,jdbcType=INTEGER}, #{corpID,jdbcType=VARCHAR}, 
            #{oprDate,jdbcType=TIMESTAMP}, #{servType,jdbcType=TINYINT}, #{subServType,jdbcType=TINYINT}, 
            #{bossID,jdbcType=VARCHAR}, #{effectiveTime,jdbcType=TIMESTAMP}, #{expiryTime,jdbcType=TIMESTAMP}, 
            #{channel,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{pushMaxTimes,jdbcType=INTEGER}, 
            #{memNumber,jdbcType=INTEGER}, #{chargeMsisdn,jdbcType=VARCHAR}, #{experienceNum,jdbcType=INTEGER}, 
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{reserved1,jdbcType=VARCHAR}, 
            #{reserved2,jdbcType=VARCHAR}, #{reserved3,jdbcType=VARCHAR}, #{reserved4,jdbcType=VARCHAR}, 
            #{reserved5,jdbcType=VARCHAR}, #{reserved6,jdbcType=VARCHAR}, #{reserved7,jdbcType=VARCHAR}, 
            #{reserved8,jdbcType=VARCHAR}, #{reserved9,jdbcType=VARCHAR}, #{reserved10,jdbcType=VARCHAR}, 
            #{cancelTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SyncServiceRuleHisWrapper" >
        insert into ecpm_t_sync_service_rule_his
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="transactionID != null" >
                transactionID,
            </if>
            <if test="enterpriseID != null" >
                enterpriseID,
            </if>
            <if test="corpID != null" >
                corpID,
            </if>
            <if test="oprDate != null" >
                oprDate,
            </if>
            <if test="servType != null" >
                servType,
            </if>
            <if test="subServType != null" >
                subServType,
            </if>
            <if test="bossID != null" >
                bossID,
            </if>
            <if test="effectiveTime != null" >
                effectiveTime,
            </if>
            <if test="expiryTime != null" >
                expiryTime,
            </if>
            <if test="channel != null" >
                channel,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="pushMaxTimes != null" >
                pushMaxTimes,
            </if>
            <if test="memNumber != null" >
                memNumber,
            </if>
            <if test="chargeMsisdn != null" >
                chargeMsisdn,
            </if>
            <if test="experienceNum != null" >
                experienceNum,
            </if>
            <if test="createTime != null" >
                createTime,
            </if>
            <if test="updateTime != null" >
                updateTime,
            </if>
            <if test="reserved1 != null" >
                reserved1,
            </if>
            <if test="reserved2 != null" >
                reserved2,
            </if>
            <if test="reserved3 != null" >
                reserved3,
            </if>
            <if test="reserved4 != null" >
                reserved4,
            </if>
            <if test="reserved5 != null" >
                reserved5,
            </if>
            <if test="reserved6 != null" >
                reserved6,
            </if>
            <if test="reserved7 != null" >
                reserved7,
            </if>
            <if test="reserved8 != null" >
                reserved8,
            </if>
            <if test="reserved9 != null" >
                reserved9,
            </if>
            <if test="reserved10 != null" >
                reserved10,
            </if>
            <if test="cancelTime != null" >
                cancelTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="transactionID != null" >
                #{transactionID,jdbcType=VARCHAR},
            </if>
            <if test="enterpriseID != null" >
                #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="corpID != null" >
                #{corpID,jdbcType=VARCHAR},
            </if>
            <if test="oprDate != null" >
                #{oprDate,jdbcType=TIMESTAMP},
            </if>
            <if test="servType != null" >
                #{servType,jdbcType=TINYINT},
            </if>
            <if test="subServType != null" >
                #{subServType,jdbcType=TINYINT},
            </if>
            <if test="bossID != null" >
                #{bossID,jdbcType=VARCHAR},
            </if>
            <if test="effectiveTime != null" >
                #{effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expiryTime != null" >
                #{expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="channel != null" >
                #{channel,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=TINYINT},
            </if>
            <if test="pushMaxTimes != null" >
                #{pushMaxTimes,jdbcType=INTEGER},
            </if>
            <if test="memNumber != null" >
                #{memNumber,jdbcType=INTEGER},
            </if>
            <if test="chargeMsisdn != null" >
                #{chargeMsisdn,jdbcType=VARCHAR},
            </if>
            <if test="experienceNum != null" >
                #{experienceNum,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="reserved1 != null" >
                #{reserved1,jdbcType=VARCHAR},
            </if>
            <if test="reserved2 != null" >
                #{reserved2,jdbcType=VARCHAR},
            </if>
            <if test="reserved3 != null" >
                #{reserved3,jdbcType=VARCHAR},
            </if>
            <if test="reserved4 != null" >
                #{reserved4,jdbcType=VARCHAR},
            </if>
            <if test="reserved5 != null" >
                #{reserved5,jdbcType=VARCHAR},
            </if>
            <if test="reserved6 != null" >
                #{reserved6,jdbcType=VARCHAR},
            </if>
            <if test="reserved7 != null" >
                #{reserved7,jdbcType=VARCHAR},
            </if>
            <if test="reserved8 != null" >
                #{reserved8,jdbcType=VARCHAR},
            </if>
            <if test="reserved9 != null" >
                #{reserved9,jdbcType=VARCHAR},
            </if>
            <if test="reserved10 != null" >
                #{reserved10,jdbcType=VARCHAR},
            </if>
            <if test="cancelTime != null" >
                #{cancelTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
</mapper>