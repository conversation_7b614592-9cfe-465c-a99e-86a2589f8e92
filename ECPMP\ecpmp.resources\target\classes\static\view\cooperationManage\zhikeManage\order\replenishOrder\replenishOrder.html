<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="replenishOrder.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>

    <script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <link href="../../../../../css/replenishOrder.css" rel="stylesheet">
    <style>
        [ng-cloak] {
            display: none !important;
        }

        .data div {
            padding-top: 0px !important;
        }

        .upload div {
            padding-top: 0px;
        }

        .webuploader-pick {
            padding: 10px 12px !important;
        }

        .input-min-width {
            min-width: 190px;
        }

        .error-ver img, .error-ver > span {
            vertical-align: middle;
        }
    </style>
</head>

<body ng-app='myApp' ng-controller='replenishOrderController' ng-init="init();" class="body-min-width">
<div class="order-manage">
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType == '1'">
        <span class="frist-tab" ng-bind="'CREATEORDER_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CREATEORDER_ORDERMANAGE'|translate"></span>
    </div>
    <div ng-if="isSuperManager && enterpriseType == '2'" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
    </div>
    <top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/queryOrderDetail"
              list-index="2" ng-if="isSuperManager && enterpriseType == '1'"></top:menu>
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/queryOrderDetail"
              list-index="6" ng-if="!isSuperManager"></top:menu>
    <top:menu chose-index="2"
              page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/orderList/orderList"
              list-index="51" ng-if="isSuperManager && enterpriseType == '2'"></top:menu>
    <div class="cooper-messsage" ng-cloak>
        <div class="cooper-title">
            <span ng-bind="'CREATEORDER_ORDERINFO'|translate"></span>
        </div>
        <div class="cooper-tab">
            <form class="form-horizontal" name="orderBase" novalidate>
                <div class="form-group">
                    <div class="row">
                        <label for="" class="col-lg-2 col-xs-3  col-sm-2 col-md-2 control-label"
                               ng-bind="'CREATEORDER_ENTERPRISENAME'|translate"></label>

                        <div class="col-lg-2 col-xs-3 col-sm-3 col-md-3">
                            <p ng-bind="initOrderInfo.enterpriseName"></p>
                        </div>
                        <label for="" class="col-lg-2 col-xs-3  col-sm-2 col-md-2 control-label"
                               ng-bind="'CREATEORDER_SERVTYPE'|translate"></label>

                        <div class="col-lg-2 col-xs-3 col-sm-3 col-md-3">
                            <p ng-bind="businessTypeMap[servType]"></p>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <label for="" class="col-lg-2 col-xs-3  col-sm-2 col-md-2 control-label"
                               ng-bind="'MAINORDERID'|translate"></label>

                        <div class="col-lg-2 col-xs-3 col-sm-3 col-md-3">
                            <p ng-bind="initOrderInfo.orderCode"></p>
                        </div>
                        <label for="" class="col-lg-2 col-xs-3  col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CREATEORDER_ORDERNAME'|translate"></span></label>

                        <div class="col-lg-2 col-xs-3 col-sm-3 col-md-3">
                            <p ng-bind="initOrderInfo.orderName"></p>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <label for="amount" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CREATEORDER_AMOUNT'|translate"></span></label>

                        <div class="col-lg-2 col-xs-3 col-sm-3 col-md-3">
                            <input type="text" class="form-control" autocomplete="off"
                                   placeholder="{{'CREATEORDER_INPUTAMOUNT'|translate}}" name="amount" id="amount"
                                   ng-model="amount" required
                                   pattern="(^[0-9]{1,17}$)|(^[0-9]{1,17}[\.]{1}[0-9]{1,3}$)">
                            <span style="color:red" ng-show="orderBase.amount.$dirty && orderBase.amount.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderBase.amount.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderBase.amount.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXAMOUNT'|translate"></span>
									</span>
                        </div>
                    </div>
                </div>

                <div id="ZZMX" class="form-group upload">
                    <div class="row">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <sapn ng-bind="'CREATEORDER_TRANSFERATTACHURL'|translate"></sapn>
                        </label>

                        <div class="col-lg-4 col-xs-5 col-sm-6 col-md-4">
                            <input type="text" class="form-control" ng-model="orderDetailURL.fileName"
                                   id="orderDetailURLFileName"
                                   placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
                                   title={{orderDetailURL.fileName}}>
                        </div>
                        <cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
                                          uploadifyid="uploadifyid1" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadParam2" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList2" createthumbnail="isCreateThumbnail" auto="auto"
                                          class="col-xs-2">
                        </cy:uploadifyfile>
                        <div style="color:#ff0000;" ng-show="orderDetailURL.errorInfo">
                            <span class="uplodify-error-img"></span>
                            <span ng-bind="orderDetailURL.errorInfo|translate"></span>
                        </div>
                    </div>
                </div>

                <div id="DDMX" class="form-group upload">
                    <div class="row">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <sapn ng-bind="'CREATEORDER_ORDERDETAILURL'|translate"></sapn>
                        </label>

                        <div class="col-lg-4 col-xs-5 col-sm-6 col-md-4">
                            <input type="text" class="form-control" ng-model="transferAttachURL.fileName"
                                   id="transferAttachURLFileName"
                                   placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
                                   title={{transferAttachURL.fileName}}>
                        </div>
                        <cy:uploadifyfile filelistid="fileList2" filepickerid="filePicker2" accepttype="accepttype"
                                          uploadifyid="uploadifyid2" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList1" createthumbnail="isCreateThumbnail" auto="auto"
                                          class="col-xs-2">
                        </cy:uploadifyfile>
                        <div style="color:#ff0000;" ng-show="transferAttachURL.errorInfo">
                            <span class="uplodify-error-img"></span>
                            <span ng-bind="transferAttachURL.errorInfo|translate"></span>
                        </div>
                    </div>
                </div>

                <div id="TYDD" class="form-group upload">
                    <div class="row">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <sapn ng-bind="'CREATEORDER_ORDEREXPERIENCE'|translate"></sapn>
                        </label>

                        <div class="col-lg-4 col-xs-5 col-sm-6 col-md-4">
                            <input type="text" class="form-control" ng-model="transferAttachURL.fileName"
                                   id="transferAttachURLFileName"
                                   placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
                                   title={{transferAttachURL.fileName}}>
                        </div>
                        <cy:uploadifyfile filelistid="fileList2" filepickerid="filePicker3" accepttype="accepttype"
                                          uploadifyid="uploadifyid2" validate="isValidate" filesize="filesize"
                                          mimetypes="mimetypes"
                                          formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc"
                                          numlimit="numlimit"
                                          urllist="urlList1" createthumbnail="isCreateThumbnail" auto="auto"
                                          class="col-xs-2">
                        </cy:uploadifyfile>
                        <div style="color:#ff0000;" ng-show="transferAttachURL.errorInfo">
                            <span class="uplodify-error-img"></span>
                            <span ng-bind="transferAttachURL.errorInfo|translate"></span>
                        </div>
                    </div>
                </div>

            </form>
        </div>

        <div class="cooper-title">
            <span class="red">*</span>
            <span ng-bind="'CREATEORDER_ORDERITEMLIST'|translate"></span>
            <span style="color:red" ng-show="(servType != '4' && (!(cmcc || cucc || ctcc)
				||(!(cmcc && (postPingXianCMCC || postGuaCai || postGuaDuan || showGroupzc)) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)))
				|| (servType =='3'&& isExperience == 1 && !(cmcc && postPingXianCMCC))
				||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))
				||(servType == '4' && (isZcNoLimit === true&&iscxNoLimit === true))
						 ">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
						<span ng-bind="'CREATEORDER_MININPUTDESC'|translate"></span>
					</span>
        </div>

        <div class="cooper-tab">
            <form class="form-horizontal" name="orderItemDomain" novalidate>
                <div ng-show="hasCMCC===true  &&  servType !='4'" class="form-group">
                    <!--移动-->
                    <div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="chooseCMCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cmcc]"></span>
                                <span>移动</span>
                            </li>
                        </div>
                    </div>

                    <div style="margin-left: 65px;" ng-show="cmcc===true">
                        <div ng-show="hasPX" class="form-group">
                            <!--移动屏显-->
                            <div class="form-group">
                                <div class="row">
                                    <li class="col-xs-2 check-li" ng-click="showPingXianCMCC()">
                                        <span class="check-btn checked-btn"
                                              ng-class="{true:'checked',false:''}[postPingXianCMCC]"></span>
                                        <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"
                                              ng-show="servType !=3"></span>
                                        <!--<span ng-bind="'CREATEORDER_GUANGGAOPEIE'|translate"-->
                                        <!--ng-show="servType ==3"></span> -->
                                        <span ng-bind="'CREATEORDER_GUANGGAOANDPEIE'|translate"
                                              ng-show="servType ==3"></span>
                                    </li>
                                </div>
                            </div>
                            <!--按次-->
                            <div class="form-group" ng-show="initOrderInfo.pxByTimes!=='' && postPingXianCMCC">
                                <div class="row">
                                    <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap;"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="PXPrice"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 input-min-width error-ver" style="padding-top: 0px">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="anci"
                                               ng-model="anci" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.anci.$dirty && orderItemDomain.anci.$invalid && hasPX">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.anci.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.anci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="accMul(PXPrice,anci)||0"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>

                            <!--旧的包月产品-->
                            <div class="form-group"
                                 ng-show="servType !=3&&(initOrderInfo.caller.memberCount!=='' || initOrderInfo.called.memberCount!=='')&& postPingXianCMCC && productType == '0'">
                                <div class="row">
                                    <li class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                    </li>
                                    <!--旧的主叫产品-->
                                    <label class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 control-label"
                                           ng-show="initOrderInfo.caller.memberCount!==''"
                                           ng-bind="'CREATEORDER_CALLER'|translate">
                                    </label>

                                    <div class="col-xs-2 input-min-width error-ver"
                                         ng-show="initOrderInfo.caller.memberCount!==''">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                               name="peirenzhu" ng-model="peirenzhu" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.peirenzhu.$dirty && orderItemDomain.peirenzhu.$invalid && hasPX">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.peirenzhu.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.peirenzhu.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         ng-show="initOrderInfo.caller.c!==''">
                                        <p ng-bind="'CREATEORDER_PRESON'|translate"></p>
                                    </div>
                                    <div class="col-xs-2 input-min-width error-ver"
                                         ng-show="initOrderInfo.caller.memberCount!==''">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_XIANECI'|translate}}"
                                               name="xiancizhu" required disabled pattern="^[0-9]{1,9}$"
                                               ng-model="xiancizhu">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.xiancizhu.$dirty && orderItemDomain.xiancizhu.$invalid && hasPX">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20"
                                                     align="absmiddle">
												<span ng-show="orderItemDomain.xiancizhu.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.xiancizhu.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-2 col-sm-2 col-md-2"
                                         ng-show="initOrderInfo.caller.memberCount!==''"><p
                                            ng-bind="'CREATEORDER_CIREN'|translate"></p></div>
                                </div>
                            </div>

                            <!--旧的被叫产品-->
                            <div class="form-group"
                                 ng-show="servType !=3&&initOrderInfo.called.memberCount!=='' && postPingXianCMCC && productType == '0'">
                                <div class="row">
                                    <label class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 control-label"
                                           ng-bind="'CREATEORDER_CALLED'|translate"></label>

                                    <div class="col-xs-2 input-min-width error-ver">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                               name="peirenbei" ng-model="peirenbei" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.peirenbei.$dirty && orderItemDomain.peirenbei.$invalid && hasPX">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20"
                                                     align="absmiddle">
												<span ng-show="orderItemDomain.peirenbei.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.peirenbei.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"><p
                                            ng-bind="'CREATEORDER_PRESON'|translate"></p>
                                    </div>
                                    <div class="col-xs-2 input-min-width error-ver">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_XIANECI'|translate}}" ng-model="xiancibei"
                                               name="xiancibei" required disabled pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.xiancibei.$dirty && orderItemDomain.xiancibei.$invalid && hasPX">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.xiancibei.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.xiancibei.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-2 col-sm-2 col-md-2"><p
                                            ng-bind="'CREATEORDER_CIREN'|translate"></p>
                                    </div>
                                </div>
                            </div>
                            <!-- 新包月产品 -->
                            <div class="form-group"
                                 ng-show="servType !=3&&hasPX && productType == '2' && postPingXianCMCC"
                                 style="height:40px">
                                <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                    style="line-height:40px">
                                    <span class="check-btn redio-btn checked" style="vertical-align: middle"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </li>
                                <div class="col-xs-2 input-min-width error-ver">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                           name="peirenyue" ng-model="peirenyue" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.peirenyue.$dirty && orderItemDomain.peirenyue.$invalid && postPingXianCMCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20" align="absmiddle">
											<span ng-show="orderItemDomain.peirenyue.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.peirenyue.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                </div>
                                <label class="col-xs-2 control-label"
                                       ng-bind="'TAOCAN_CHOOSE'|translate" style="width: 110px"></label>

                                <div class="col-xs-1" style="min-width: 210px">
                                    <select id="productName" class="form-control" ng-model="productID"
                                            ng-change="getPorductUnitPrice()"
                                            ng-options="x.objectID as x.productName for x in productList"
                                            ng-disabled="true"></select>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="padding-top: 6px;white-space: nowrap;padding-left: 0"
                                     ng-show="isExperience == '0'">
                                    <span ng-bind="accSum(peirenyue)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <!--挂短-->
                        <div ng-show="hasGD&&servType !=3" class="form-group">
                            <div class="form-group">
                                <div class="row">
                                    <li class="col-xs-2 check-li" ng-click="showGuaDuan()">
                                        <span class="check-btn checked-btn"
                                              ng-class="{true:'checked',false:''}[postGuaDuan]"></span>
                                        <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                    </li>
                                </div>
                            </div>
                            <div class="form-group" ng-show="initOrderInfo.guaduanAmount!=='' && postGuaDuan">
                                <div class="row">
                                    <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap;"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="GDPrice"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 input-min-width error-ver" style="padding-top: 0px">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="gdanci"
                                               ng-model="gdanci" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.gdanci.$dirty && orderItemDomain.gdanci.$invalid && hasGD">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.gdanci.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.gdanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="accMul(GDPrice,gdanci)||0"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group"
                                 ng-show="hasGD && gdproductType == '2' && postGuaDuan" style="height:40px">
                                <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                    style="line-height:40px">
                                    <span class="check-btn redio-btn checked" style="vertical-align: middle"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </li>
                                <div class="col-xs-2 input-min-width error-ver">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                           name="guaDuanPrice" ng-model="guaDuanPrice" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.guaDuanPrice.$dirty && orderItemDomain.guaDuanPrice.$invalid && postGuaDuan">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20" align="absmiddle">
											<span ng-show="orderItemDomain.guaDuanPrice.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.guaDuanPrice.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                </div>
                                <label class="col-xs-2 control-label"
                                       ng-bind="'TAOCAN_CHOOSE'|translate" style="width: 110px"></label>

                                <div class="col-xs-1" style="min-width: 210px">
                                    <select id="gdProductName" class="form-control" ng-model="gdproductID"
                                            ng-change="getPorductPrice()"
                                            ng-options="x.objectID as x.productName for x in guaDuanBaoyueProductList"
                                            ng-disabled="true"></select>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="padding-top: 6px;white-space: nowrap;padding-left: 0"
                                     ng-show="isExperience == '0'">
                                    <span ng-bind="accGDSum(guaDuanPrice)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <!--挂彩-->
                        <div ng-show="hasGC&&servType !=3" class="form-group">
                            <div class="form-group">
                                <div class="row">
                                    <li class="col-xs-2 check-li" ng-click="showGuaCai()">
                                        <span class="check-btn checked-btn"
                                              ng-class="{true:'checked',false:''}[postGuaCai]"></span>
                                        <span ng-bind="'CREATEORDER_GUACAI'|translate"></span>
                                    </li>
                                </div>
                            </div>
                            <div class="form-group" ng-show="initOrderInfo.guacaiAmount!=='' && postGuaCai">
                                <div class="row">
                                    <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1  redio-li">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap;"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="GCPrice"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 input-min-width error-ver" style="padding-top: 0px">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="gcanci"
                                               ng-model="gcanci" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.gcanci.$dirty && orderItemDomain.gcanci.$invalid && hasGC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.gcanci.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.gcanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="accMul(GCPrice,gcanci)||0"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group"
                                 ng-show="hasGC && gcproductType == '2' " style="height:40px">
                                <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                    style="line-height:40px">
                                    <span class="check-btn redio-btn checked" style="vertical-align: middle"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </li>
                                <div class="col-xs-2 input-min-width error-ver">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIEP'|translate}}"
                                           name="guaCaiPrice" ng-model="guaCaiPrice" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.guaCaiPrice.$dirty && orderItemDomain.guaCaiPrice.$invalid && postGuaCai">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20" align="absmiddle">
											<span ng-show="orderItemDomain.guaCaiPrice.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.guaCaiPrice.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                </div>
                                <label class="col-xs-2 control-label"
                                       ng-bind="'TAOCAN_CHOOSE'|translate" style="width: 110px"></label>

                                <div class="col-xs-1" style="min-width: 210px">
                                    <select id="gcProductName" class="form-control" ng-model="gcproductID"
                                            ng-change="getPorductGCPrice()"
                                            ng-options="x.objectID as x.productName for x in guaCaiBaoyueProductList"
                                            ng-disabled="true"></select>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="padding-top: 6px;white-space: nowrap;padding-left: 0"
                                     ng-show="isExperience == '0'">
                                    <span ng-bind="accGCSum(guaCaiPrice)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                        <!--挂机增彩-->
                        <div ng-show="hasZC&&servType == 2" class="form-group">
                            <div class="form-group">
                                <div class="row">
                                    <li class="col-xs-2 check-li" ng-click="showEnable('showGroupzc')">
                                        <span class="check-btn checked-btn"
                                              ng-class="{true:'checked',false:''}[showGroupzc]"></span>
                                        <span ng-bind="'CREATEORDER_ZENGCAI'|translate"></span>
                                    </li>
                                </div>
                            </div>
                            <div class="form-group" ng-show="showGroupzc">
                                <div class="row">
                                    <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1  redio-li">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap;"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="ZCPrice"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 input-min-width error-ver" style="padding-top: 0px">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="gjzcanci"
                                               ng-model="gjzcanci" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.gjzcanci.$dirty && orderItemDomain.gjzcanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.gjzcanci.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.gjzcanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="accMul(zcPrice,gjzcanci)||0"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
				</div>
                <div ng-show="hasCUCC===true &&  servType !='4'" class="form-group">
                    <!--联通-->
                    <div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="chooseCUCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cucc]"></span>
                                <span>联通</span>
                            </li>
                        </div>
                    </div>

                    <div style="margin-left: 65px" ng-show="cucc===true">
                        <div ng-show="hasPXCUCC" class="form-group">    <!--此行新增 20191107-->
                            <!--屏显配额-->
                            <div class="form-group">
                                <div class="row">
                                    <li class="col-xs-2 check-li" ng-click="showPingXianCUCC()">
                                    <span class="check-btn checked-btn"
                                          ng-class="{true:'checked',false:''}[postPingXianCUCC]"></span>
                                        <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"></span>
                                    </li>
                                </div>
                            </div>

                            <div class="form-group" ng-show="postPingXianCUCC">
                                <div class="row">
                                    <!--按次-->
                                    <div class="col-lg-10 col-xs-10"></div>
                                    <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                        style="padding-top: 7px;">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" style="white-space: nowrap;"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="PXPrice_cucc"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>

                                    <div class="col-xs-2 input-min-width error-ver">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="anci_cucc"
                                               ng-model="anci_cucc" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.anci_cucc.$dirty && orderItemDomain.anci_cucc.$invalid && postPingXianCUCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20" align="absmiddle">
											<span ng-show="orderItemDomain.anci_cucc.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
																<span ng-show="orderItemDomain.anci_cucc.$error.pattern"
                                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="isExperience == '0'"
                                         style="white-space: nowrap">
                                        <span ng-bind="accMul(PXPrice_cucc,anci_cucc)||0"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--addby hyj 20191107 top-->
                        <!--挂短-->
                        <div ng-show="hasGDCUCC&&servType == 2" class="form-group">
                            <div class="form-group">
                                <div class="row">
                                    <li class="col-xs-2 check-li" ng-click="showGuaDuanCUCC()">
                                        <span class="check-btn checked-btn"
                                              ng-class="{true:'checked',false:''}[postGuaDuanCUCC]"></span>
                                        <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                    </li>
                                </div>
                            </div>
                            <div class="form-group" ng-show="initOrderInfo.guaduanAmountCUCC!=='' && postGuaDuanCUCC">
                                <div class="row">
                                    <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap;"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="GDPriceCUCC"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 input-min-width error-ver" style="padding-top: 0px">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="gdanciCUCC"
                                               ng-model="gdanciCUCC" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.gdanciCUCC.$dirty && orderItemDomain.gdanciCUCC.$invalid && hasGDCUCC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.gdanciCUCC.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.gdanciCUCC.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                         style="padding-top: 0px;white-space: nowrap"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="accMul(GDPriceCUCC,gdanciCUCC)||0"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--addby hyj 20191107 end-->
                    </div>
                </div>
                <div ng-show="hasCTCC===true &&  servType !='4'" class="form-group">
                    <!--电信-->
                    <div class="form-group">
                        <div class="row">
                            <li class="col-xs-2 check-li" ng-click="chooseCTCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[ctcc]"></span>
                                <span>电信</span>
                            </li>
                        </div>
                    </div>

                    <div style="margin-left: 65px" ng-show="ctcc===true">
                        <div ng-show="hasPXCTCC" class="form-group">                <!--此行新增 20191107-->
                            <!--屏显配额-->
                            <div class="form-group">
                                <div class="row">
                                    <li class="col-xs-2 check-li" ng-click="showPingXianCTCC()">
                                    <span class="check-btn checked-btn"
                                          ng-class="{true:'checked',false:''}[postPingXianCTCC]"></span>
                                        <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"></span>
                                    </li>
                                </div>
                            </div>

                            <div class="form-group" ng-show="postPingXianCTCC">
                                <div class="row">
                                    <!--按次-->
                                    <div class="col-lg-10 col-xs-10"></div>
                                    <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                        style="padding-top: 7px;">
                                        <span class="check-btn redio-btn checked"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" style="white-space: nowrap;"
                                         ng-show="isExperience == '0'">
                                        <span ng-bind="PXPrice_ctcc"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>

                                    <div class="col-xs-2 input-min-width error-ver">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="anci_ctcc"
                                               ng-model="anci_ctcc" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="orderItemDomain.anci_ctcc.$dirty && orderItemDomain.anci_ctcc.$invalid && postPingXianCTCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20" align="absmiddle">
											<span ng-show="orderItemDomain.anci_ctcc.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
																<span ng-show="orderItemDomain.anci_ctcc.$error.pattern"
                                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="isExperience == '0'"
                                         style="white-space: nowrap">
                                        <span ng-bind="accMul(PXPrice_ctcc,anci_ctcc)||0"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--20191107-->
                    <!--addby hyj 20191107 top-->
                    <!--挂短-->
                    <div style="margin-left: 65px" ng-show="ctcc===true" class="form-group">
                        <div class="form-group" ng-show="hasGDCTCC&&servType == 2" >
                            <div class="row">
                                <li class="col-xs-2 check-li" ng-click="showGuaDuanCTCC()">
                                        <span class="check-btn checked-btn"
                                              ng-class="{true:'checked',false:''}[postGuaDuanCTCC]"></span>
                                    <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                </li>
                            </div>
                        </div>
                        <div class="form-group" ng-show="initOrderInfo.guaduanAmountCTCC!=='' && postGuaDuanCTCC">
                            <div class="row">
                                <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li">
                                    <span class="check-btn redio-btn checked"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="padding-top: 0px;white-space: nowrap;"
                                     ng-show="isExperience == '0'">
                                    <span ng-bind="GDPriceCTCC"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2 input-min-width error-ver" style="padding-top: 0px">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}" name="gdanciCTCC"
                                           ng-model="gdanciCTCC" required pattern="^[0-9]{1,9}$">
                                    <span style="color:red"
                                          ng-show="orderItemDomain.gdanciCTCC.$dirty && orderItemDomain.gdanciCTCC.$invalid && hasGDCTCC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.gdanciCTCC.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.gdanciCTCC.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
									</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="padding-top: 0px;white-space: nowrap"
                                     ng-show="isExperience == '0'">
                                    <span ng-bind="accMul(GDPriceCTCC,gdanciCTCC)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--addby hyj 20191107 end-->
                    <!--20191107-->
                </div>
				<!--移动-->
                <div class="form-group" ng-show=" servType =='4' && hasCMCC">
                    <div class="row">
                        <li class="col-xs-2 check-li" ng-click="chooseCMCC()">
                            <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cmcc]"></span>
                            <span>移动</span>
                        </li>
                    </div>
                </div>
                <div ng-show="servType =='4' && cmcc===true" style="margin-left: 65px;">
                    <div class="form-group" ng-show="isgroupScreenCMCCNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupScreen')"
                                      ng-class="{true:'checked',false:''}[showGroupScreen]"></span>
                                <span>屏显：</span>
                            </li>
                        </div>
                        <!--按次-->
                        <div ng-show="showGroupScreen" class="row">
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                style="padding-top: 7px;">
						<span class="check-btn redio-btn pingXian" id="screenType3"
                                      ng-class="{true:'checked',false:''}[true]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>

                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupScreenCMCCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendScreenCMCCanci"
                                       ng-model="groupSendScreenCMCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupScreenCMCCanci.$dirty && orderItemDomain.groupScreenCMCCanci.$invalid">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                         align="absmiddle">
							<span ng-show="orderItemDomain.groupScreenCMCCanci.$error.required"
                                          ng-bind="'REQUIRED'|translate">

                                    </span>
							<span ng-show="orderItemDomain.groupScreenCMCCanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                     </span>
						</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupScreenCMCCPrice,groupSendScreenCMCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>

                    </div>
                    <div class="form-group" ng-show="iscxNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupcx')"
                                      ng-class="{true:'checked',false:''}[showGroupcx]"></span>
                                <span>彩信:</span>
                            </li>
                        </div>
                            <div ng-show="showGroupcx" class="row">
                                <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="zengCaiType3"
                                          ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>

                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="white-space: nowrap;">
                                    <span ng-bind="cxPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2 input-min-width error-ver">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                           name="cxanci"
                                           ng-model="cxanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="orderItemDomain.cxanci.$dirty && orderItemDomain.cxanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.cxanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.cxanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(cxPrice,cxanci)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>

                    </div>
                    <div class="form-group" ng-show="isZcNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupzc')"
                                      ng-class="{true:'checked',false:''}[showGroupzc]"></span>
                                <span>增彩:</span>
                            </li>
                        </div>
                            <div ng-show="showGroupzc" class="row">
                                <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="zengCaiType3"
                                          ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>

                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="white-space: nowrap;">
                                    <span ng-bind="ZCPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-2 input-min-width error-ver">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                           name="zcanci"
                                           ng-model="zcanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="orderItemDomain.zcanci.$dirty && orderItemDomain.zcanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.zcanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.zcanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(ZCPrice,zcanci)||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>

                    </div>
                    <div class="form-group" ng-show="isgroupSMSCMCCNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupSMS')"
                                      ng-class="{true:'checked',false:''}[showGroupSMS]"></span>
                                <span>短信：</span>
                            </li>
                        </div>
                        <!--按次-->
                        <div ng-show="showGroupSMS" class="row">
                            <div class="col-lg-10 col-xs-10"></div>
                            <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                style="padding-top: 7px;">
						<span class="check-btn redio-btn pingXian" id="smsType3"
                                      ng-class="{true:'checked',false:''}[true]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>

                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupSMSCMCCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendSMSCMCCanci"
                                       ng-model="groupSendSMSCMCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSMSCMCCanci.$dirty && orderItemDomain.groupSMSCMCCanci.$invalid">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                         align="absmiddle">
							<span ng-show="orderItemDomain.groupSMSCMCCanci.$error.required"
                                          ng-bind="'REQUIRED'|translate">

                                    </span>
							<span ng-show="orderItemDomain.groupSMSCMCCanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                     </span>
						</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupSMSCMCCPrice,groupSendSMSCMCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>

                    </div>
                </div>
                <!--联通-->
                <div class="form-group" ng-show=" servType =='4' && hasCUCC">
                    <div class="row">
                        <li class="col-xs-2 check-li" ng-click="chooseCUCC()">
                            <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cucc]"></span>
                            <span>联通</span>
                        </li>
                    </div>
                </div>
                <!--增彩产品-->
                <!--ng-if="isExperience == '0' && chargeTypeGuaDuan==1"-->
                <div ng-show=" servType =='4' && cucc===true" style="margin-left: 65px;">
                	<div class="form-group" ng-show="isgroupScreenCUCCNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupScreenCUCC')"
                                      ng-class="{true:'checked',false:''}[showGroupScreenCUCC]"></span>
                                <span>屏显：</span>
                            </li>
                        </div>
                        <div class="row" ng-show="showGroupScreenCUCC">
                            <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[true]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupScreenCUCCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="true">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendScreenCUCCanci"
                                       ng-model="groupSendScreenCUCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendScreenCUCCanci.$dirty && orderItemDomain.groupSendScreenCUCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendScreenCUCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendScreenCUCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupScreenCUCCPrice,groupSendScreenCUCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isgroupSMSCUCCNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupSMSCUCC')"
                                      ng-class="{true:'checked',false:''}[showGroupSMSCUCC]"></span>
                                <span>短信：</span>
                            </li>
                        </div>
                        <div class="row" ng-show="showGroupSMSCUCC">
                            <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[true]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupSMSCUCCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="true">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendSMSCUCCanci"
                                       ng-model="groupSendSMSCUCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendSMSCUCCanci.$dirty && orderItemDomain.groupSendSMSCUCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupSMSCUCCPrice,groupSendSMSCUCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-group" ng-show=" servType =='4' && hasCTCC">
                    <div class="row">
                        <li class="col-xs-2 check-li" ng-click="chooseCTCC()">
                            <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[ctcc]"></span>
                            <span>电信</span>
                        </li>
                    </div>
                </div>
                <!--增彩产品-->
                <!--ng-if="isExperience == '0' && chargeTypeGuaDuan==1"-->
                <div ng-show=" servType =='4' && ctcc===true" style="margin-left: 65px;">
                	<div class="form-group" ng-show="isgroupScreenCTCCNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupScreenCTCC')"
                                      ng-class="{true:'checked',false:''}[showGroupScreenCTCC]"></span>
                                <span>屏显：</span>
                            </li>
                        </div>
                        <div class="row" ng-show="showGroupScreenCTCC">
                            <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[true]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupScreenCTCCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="true">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendScreenCTCCanci"
                                       ng-model="groupSendScreenCTCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendScreenCTCCanci.$dirty && orderItemDomain.groupSendScreenCTCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendScreenCTCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendScreenCTCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupScreenCTCCPrice,groupSendScreenCTCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group" ng-show="isgroupSMSCTCCNoLimit === false">
                        <div class="row">
                            <li class="col-xs-2 check-li">
                                <span class="check-btn checked-btn" ng-click="showEnable('showGroupSMSCTCC')"
                                      ng-class="{true:'checked',false:''}[showGroupSMSCTCC]"></span>
                                <span>短信：</span>
                            </li>
                        </div>
                        <div class="row" ng-show="showGroupSMSCTCC">
                            <li class="col-xs-2 col-lg-offset-1 col-xs-offset-1 col-sm-offset-2 col-md-offset-1 redio-li"
                                style="padding-top: 7px;">
										<span class="check-btn redio-btn pingXian"
                                              ng-class="{true:'checked',false:''}[true]"></span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="white-space: nowrap;">
                                <span ng-bind="groupSMSCTCCPrice"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                            <div class="col-xs-2 input-min-width error-ver" ng-show="true">
                                <input type="text" class="form-control" autocomplete="off"
                                       placeholder="{{'CREATEORDER_INPUTPEIE'|translate}}"
                                       name="groupSendSMSCTCCanci"
                                       ng-model="groupSendSMSCTCCanci" required pattern="^[0-9]{1,9}$">

                                <span style="color:red"
                                      ng-show="orderItemDomain.groupSendSMSCTCCanci.$dirty && orderItemDomain.groupSendSMSCTCCanci.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
										<span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                            </div>
                            <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="true"
                                 ng-if="isExperience == '0'"
                                 style="padding-top: 7px;white-space: nowrap">
                                <span ng-bind="accMul(groupSMSCTCCPrice,groupSendSMSCTCCanci)||0"></span>
                                <span ng-bind="'COMMON_YUAN'|translate"></span>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <div class="row">
                        <label class="col-lg-2 col-xs-3 col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CREATEORDER_TIME'|translate"></span></label>

                        <div class="col-lg-6 col-xs-9 col-sm-10 col-md-10 data">
                            <div class="data-time">
                                <div class="input-group date  star-time" aria-disabled="true">
                                    <input type="text" class="form-control" ng-model="initOrderInfo.effictiveTime"
                                           disabled>
                                </div>
                                <span class="to" ng-bind="'TO'|translate"></span>

                                <div class="input-group date  end-time" aria-disabled="true">
                                    <input type="text" class="form-control" ng-model="initOrderInfo.expireTime"
                                           disabled>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="order-btn row"><!--  orderItemDomain.gdanci.$error.required   -->
        <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
             style="padding-left: 30px;">
            <button type="submit" class="btn btn-primary search-btn" ng-disabled="
							orderBase.amount.$invalid ||
							!transferAttachURL.upload || transferAttachURL.errorInfo
							||(servType !='4' && ((cmcc && postPingXianCMCC && initOrderInfo.pxByTimes!=='' && orderItemDomain.anci.$invalid)
							||(cmcc &&  postGuaDuan && initOrderInfo.guaduanAmount!=='' && orderItemDomain.gdanci.$invalid)
							||(cmcc && showGroupzc && orderItemDomain.gjzcanci.$invalid)
							||(cmcc &&  postGuaCai && initOrderInfo.guacaiAmount!=='' && orderItemDomain.gcanci.$invalid) ||
							(orderItemDomain.peirenzhu.$invalid&& initOrderInfo.caller.memberCount!=='' && hasPX && postPingXianCMCC && productType == '0' && cmcc) ||
							(orderItemDomain.xiancizhu.$invalid&& initOrderInfo.caller.memberCount!=='' && hasPX && postPingXianCMCC && productType == '0' && cmcc) ||
							(orderItemDomain.peirenbei.$invalid&& initOrderInfo.called.memberCount!=='' && hasPX && postPingXianCMCC && productType == '0' && cmcc) ||
							(orderItemDomain.xiancibei.$invalid&& initOrderInfo.called.memberCount!=='' && hasPX && postPingXianCMCC && productType == '0' && cmcc) ||
							(orderItemDomain.peirenyue.$invalid && hasPX && postPingXianCMCC && productType == '2' && cmcc) ||
							(orderItemDomain.guaDuanPrice.$invalid && hasGD && postGuaDuan && gdproductType == '2' && cmcc) ||
							(orderItemDomain.guaCaiPrice.$invalid && hasGC && postGuaCai && gcproductType == '2' && cmcc) ||
							(cucc && postPingXianCUCC && orderItemDomain.anci_cucc.$invalid) ||
							(cucc && postGuaDuanCUCC && initOrderInfo.guaduanAmountCUCC!=='' && orderItemDomain.gdanciCUCC.$invalid) ||
							(ctcc && postPingXianCTCC && orderItemDomain.anci_ctcc.$invalid) ||
							(ctcc && postGuaDuanCTCC && initOrderInfo.guaduanAmountCTCC!=='' && orderItemDomain.gdanciCTCC.$invalid)
							||!(cmcc || cucc || ctcc)
							||(!(cmcc && (postPingXianCMCC || postGuaCai || postGuaDuan || showGroupzc)) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)) && servType !='3')
							||(servType =='3' && !(cmcc && postPingXianCMCC) && isExperience == 1)
							||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))
							||(servType =='4' && cmcc && showGroupzc && orderItemDomain.zcanci.$invalid)
							||(servType =='4' && cmcc && showGroupcx && orderItemDomain.cxanci.$invalid)
							||(servType =='4' && cmcc && showGroupSMS && orderItemDomain.groupSMSCMCCanci.$invalid)
							||(servType =='4' && cucc && showGroupSMSCUCC && orderItemDomain.groupSendSMSCUCCanci.$invalid)
							||(servType =='4' && ctcc && showGroupSMSCTCC && orderItemDomain.groupSendSMSCTCCanci.$invalid)
							||(servType =='4' && cmcc && showGroupScreen && orderItemDomain.groupScreenCMCCanci.$invalid)
							||(servType =='4' && cucc && showGroupScreenCUCC && orderItemDomain.groupSendScreenCUCCanci.$invalid)
							||(servType =='4' && ctcc && showGroupScreenCTCC && orderItemDomain.groupSendScreenCTCCanci.$invalid)
							||(servType =='4' && !(cmcc || cucc || ctcc))
							||(servType =='4' && (cmcc && !(showGroupSMS || showGroupzc || showGroupcx || showGroupScreen)) && (cucc && !(showGroupSMSCUCC || showGroupScreenCUCC)) 
								&& (cucc && !(showGroupSMSCTCC || showGroupScreenCTCC)))
					"
                    ng-click="createOrder()" ng-bind="'COMMON_SAVE'|translate" id="formSub">
            </button>
            <button type="submit" class="btn btn-back" ng-click="goBack()" ng-bind="'COMMON_BACK'|translate"></button>
        </div>
    </div>

</div>

<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center"><p style='font-size: 16px;color:#383838'>
                    {{tip|translate}}
                </p></div>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>

</body>
</html>