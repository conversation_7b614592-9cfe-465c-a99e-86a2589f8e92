<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DiffNetApproveTempMapper">
    <resultMap id="diffNetApproveTempWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DiffNetApproveTempWrapper">
        <result property="ID" column="ID" javaType="java.lang.Integer" />
        <result property="contentID" column="contentID" javaType="java.lang.Integer" />
        <result property="platform" column="platform" javaType="java.lang.Integer" />
        <result property="approveStatus" column="approveStatus" javaType="java.lang.String" />
        <result property="approveIdea" column="approveIdea" javaType="java.lang.String" />
        <result property="approveTime" column="approveTime" javaType="java.util.Date" />
        <result property="wayType" column="wayType" javaType="java.lang.Integer" />        
    </resultMap>

    <select id="queryDiffNetApproveTempInfoListByContentID"  resultMap="diffNetApproveTempWrapper" parameterType="java.lang.Integer">
        select
        ID,contentID,platform,approveStatus,approveIdea,approveTime,wayType from
        ecpm_t_diffnet_approve_temp
        where contentID=#{contentID}
    </select>

    <insert id="insertDiffNetApproveTempInfo">
        insert into ecpm_t_diffnet_approve_temp
        (
        contentID,
        platform,
        approveStatus,
        approveIdea,
        approveTime,
        wayType
        )
        VALUES
        (
        #{contentID},
        #{platform},
        #{approveStatus},
        #{approveIdea},
        #{approveTime},
        #{wayType}
        )
    </insert>

    <delete id="deleteDiffNetApproveTempInfoByContentID" parameterType="java.lang.Integer">
        delete from ecpm_t_diffnet_approve_temp where contentID=#{contentID}
    </delete>
</mapper>