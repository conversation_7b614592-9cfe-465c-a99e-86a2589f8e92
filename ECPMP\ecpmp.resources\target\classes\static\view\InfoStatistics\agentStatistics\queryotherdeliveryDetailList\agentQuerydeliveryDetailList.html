<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11" />
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
	<link href="../../../../css/reset.css" rel="stylesheet" />
	<link href="../../../../css/mian.css" rel="stylesheet"/>
	<link href="../../../../css/datepicker3.css" rel="stylesheet"/>
	<link href="../../../../css/layout.css" rel="stylesheet"/>
	<link href="../../../../css/searchList.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
	<link rel="stylesheet" href="../../../../css/font-awesome.min.css"/>
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
	<link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<link href="../../../../css/statistics.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
	<script type="text/javascript" src="agentQuerydeliveryDetailList.js"></script>

	<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
	font-weight: normal;
	white-space: nowrap;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
</style>
<style>
    .clearf:after{
    content:'';
    clear:both;
        height:0;
        display:block;
    }
</style>
<style>
	.sameLine{
		float: left;
	}
</style>
</head>
<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" class="body-min-width">
<div class="cooperation-manage">
	<div class="cooperation-head" ng-show="!isAgent"><span class="frist-tab" ng-bind="'AGENTSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'DELIVERYDETAIL_MG'|translate"></span></div>
	<div class="cooperation-head" ng-show="isAgent"><span class="frist-tab" ng-bind="'COMMON_INFOSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'BUSINESS_CARD_COLOR_PRINTING_DELIVERY_DETAILS'|translate"></span></div>
	<top:menu chose-index="3" page-url="/qycy/ecpmp/view/InfoStatistics/agentStatistics" list-index="42" ng-show="!isAgent"></top:menu>
	<form class="form-horizontal">
		<div class="form-group">

			<label for="enterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate" ng-show="!isAgent"></label>
			<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div" ng-show="!isAgent">
				<input type="text" ng-change="clearmsg()" autocomplete="off" class="form-control" id="enterpriseID"  placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISEID'|translate}}"   ng-model="initSel.enterpriseID">
			</div>
			
			<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="!isAgent"></label>
			<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div" ng-show="!isAgent">
				<input type="text" ng-change="clearmsg()"autocomplete="off" class="form-control" id="enterpriseName"  placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"   ng-model="initSel.enterpriseName">
			</div>

			<label for="serviceNum" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'BUSINESS_NUMBER'|translate"></label>
			<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
				<input type="text" ng-change="clearmsg()" autocomplete="off" class="form-control" id="serviceNum" placeholder="{{'PLEASE_ENTER_BUSINESS_NUMBER'|translate}}"   ng-model="initSel.serviceNum">
			</div>

			<label for="receiveNum" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'RECEIVED'|translate"></label>
			<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
				<input type="text" ng-change="clearmsg()" autocomplete="off" class="form-control" id="receiveNum" placeholder="{{'ENTERPRISE_PLEASEINPUTRECEIVENUM'|translate}}"   ng-model="initSel.receiveNum">
			</div>

			<span style="color:red;line-height: 34px;display: block;" ng-show="!checkUnique">
					<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
					<span>{{uniqueTip}}</span>
				</span>
			<div class="clearf"> </div>
			<label class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></label>
			<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 time">
				<div class="input-daterange input-group" id="datepicker">
					<input type="text" class="input-md form-control" autocomplete="off" id="start" ng-keyup="searchOn()"/>
					<span class="input-group-addon" ng-bind="'TO'|translate"></span>
					<input type="text" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
				</div>
			</div>

			<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
				<button ng-click="querydeliveryDetailList()" type="submit" class="btn search-btn" ng-disabled="initSel.search"><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
			</div>
		</div>
	</form>
	<!-- <form class="form-horizontal">
		<div class="form-group">

			<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></label>
			<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
				<input type="text" autocomplete="off" class="form-control" id="enterpriseName"  placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"   ng-model="initSel.enterpriseName">
			</div>

			<label for="receiveNum" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'COMMON_NUMBER'|translate"></label>
			<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
				<input type="text" autocomplete="off" class="form-control" id="receiveNum" placeholder="{{'ENTERPRISE_PLEASEINPUTRECEIVENUM'|translate}}"   ng-model="initSel.receiveNum">
			</div>

			<label class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></label>
			<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 time">
				<div class="input-daterange input-group" id="datepicker">
					<input type="text" class="input-md form-control" autocomplete="off" id="start" ng-keyup="searchOn()"/>
					<span class="input-group-addon" ng-bind="'TO'|translate"></span>
					<input type="text" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
				</div>
			</div>

			<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
				<button ng-click="querydeliveryDetailList()" type="submit" class="btn search-btn" ng-disabled="initSel.search"><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
			</div>
		</div>
	</form> -->

	<!-- <div class="add-table">
		<button id="exportSpokesList" class="btn add-btn" ng-click="exportFile()">
			<icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span>
		</button>
	</div> -->
	<div class="add-table" ng-show="!isAgent">
		<button ng-click="exportFile()" id="exportSpokesList" type="submit" class="btn add-btn" ><icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span></button>
	</div>
	<div class="sameLine" style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-bind="'BUSINESS_CARD_COLOR_PRINTING_DELIVERY_DETAILS'|translate"></div>
	<div class="sameLine" style="padding:0px 20px 10px 20px;font-size:14px;color: red" ng-bind="'COLOR_PRINTING_OF_BUSINESS_CARDS_AND_DELIVERY_DETAILS_OF_HOTLINE_PROVINCE_EDITION'|translate"></div>
	<div class="coorPeration-table">
		<table class="table table-striped table-hover">
			<thead>
			<tr>
				<th style="width:9%" ng-bind="'ENTERPRISE_AGENTID'|translate"></th>
				<th style="width:8%" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>

				<!--<th style="width:8%" ng-bind="'TASKID'|translate"></th>-->
				<th style="width:9%" ng-bind="'CYID'|translate"></th>
				<th style="width:12%" ng-bind="'CALLING'|translate"></th>
				<th style="width:12%" ng-bind="'CALLED'|translate"></th>
				<th style="width:12%" ng-bind="'CONTENT_POSTTIME'|translate"></th>
				<th style="width:8%" ng-bind="'DELIVERYTYPE_DELAY_SECOND'|translate"></th>
				<th style="width:9%" ng-bind="'DELIVERYTYPE_OPERATOR'|translate"></th>
				<th style="width:8%" ng-bind="'DELIVERYTYPE'|translate"></th>
				<th style="width:8%" ng-bind="'DELIVERYRESULT'|translate"></th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="item in deliveryDetailListData">
				<td><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
				<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
				<!--<td><span title="{{item.taskID}}">{{item.taskID}}</span></td>-->
				<td><span title="{{item.cyID}}">{{item.cyID}}</span></td>
				<td><span title="{{item.calling}}">{{item.calling}}</span></td>
				<td><span title="{{item.called}}">{{item.called}}</span></td>
				<td><span title="{{showTime(item.deliveryTime)}}">{{showTime(item.deliveryTime)}}</span></td>
				<td><span title="{{item.timeDiffer}}">{{item.timeDiffer}}</span></td>
				<td>
					<span title="{{'MOBILE'|translate}}" ng-show="item.telco==='YD'"  ng-bind="'MOBILE'|translate"></span>
					<span title="{{'UNICOM'|translate}}" ng-show="item.telco==='LT'"  ng-bind="'UNICOM'|translate"></span>
					<span title="{{'TELECOM'|translate}}" ng-show="item.telco==='DX'"  ng-bind="'TELECOM'|translate"></span>
				</td>
				<td><span title="{{getDeliveryType(item.deliveryType)}}">{{getDeliveryType(item.deliveryType)}}</span></td>
				<td><span title="{{getResult(item.deliveryResult)}}">{{getResult(item.deliveryResult)}}</span></td>
			</tr>
			<tr ng-show="deliveryDetailListData.length<=0">
				<td style="text-align:center" colspan="11" ng-bind="'COMMON_NODATA'|translate"></td>
			</tr>
			</tbody>
		</table>
	</div>

	<div>
		<ptl-page tableId="0" change="querydeliveryDetailList('justPage')"></ptl-page>
	</div>
</div>

<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div class="modal-dialog modal-sm" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
			</div>
			<div class="modal-body">
				<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
				</p>
				</div>
			</div>
			<div class="modal-footer" style="text-align:center">
				<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
			</div>
		</div>
	</div>
</div>

<!-- 文件导出弹窗 -->
<div class="modal fade bs-example-modal-sm" id="exportFile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
				<h4 class="modal-title ng-binding" id="exportFileTitle" ng-bind="'DETAIL_EXPORT'|translate"></h4>
			</div>

			<div class="modal-body">
				<form class="form-horizontal ng-pristine ng-invalid ng-invalid-required" name="myForm" novalidate="">
					<div class="form-group">
						<label class="col-sm-2 control-label ng-binding" ng-bind="'COMMON_REMARKS'|translate"></label>

						<div class="cond-div col-sm-6">
							<input type="text" class="form-control ng-pristine ng-untouched ng-invalid ng-invalid-required" placeholder="{{'COMMON_INPUT_REMARKS'|translate}}" name="remarks" ng-model="remarks" ng-change="checkEmpty()" required="">
						</div>
						<div class="cond-div col-sm-4">
							<span style="color:red" ng-show="isEmptyFileName" class="ng-hide">
								<span ng-bind="'COMMON_FILENAME_MUST_FILL'|translate" class="ng-binding"></span>
							</span>
							<span style="color:red" ng-show="isGreaterFileName" class="ng-hide">
								<span ng-bind="'COMMON_FILENAME_SIZE'|translate" class="ng-binding"></span>
							</span>
							<span style="color:red" ng-show="isSpecialCharacters" class="ng-hide">
								<span ng-bind="'SPECIAL_CHARACTERS'|translate" class="ng-binding"></span>
							</span>
						</div>
						<div class="cond-div col-sm-10">
							<span style="font-size: 10px;color: red;">导出后请到业务管理-导出文件下载对应文件</span>
<!--							<span style="font-size: 10px;color: red;">代理商名片/广告统计明细</span>-->
							<span style="font-size: 10px;color: red;" ng-bind="'EXPORT_TIPS'|translate"></span>

						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button class="btn bg_purple ng-binding" ng-click="submitExportTask()" type="submit" ng-disabled="!isFileNamePass" ng-bind="'COMMON_OK'|translate"></button>
				<button class="btn  ng-binding" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
			</div>
		</div>
	</div>
</div>

</body>
</html>