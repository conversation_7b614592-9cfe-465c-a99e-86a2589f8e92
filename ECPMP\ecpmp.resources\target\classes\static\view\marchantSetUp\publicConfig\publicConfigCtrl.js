var app = angular.module("myApp", ["util.ajax", "angularI18n", "cy.uploadify","service.common"])
app.controller('publicConfigCtrl', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {

  $scope.init = function () {
    $scope.operatorID = $.cookie('accountID');
    $scope.accepttype = "jpg,jpeg,png";
    $scope.isValidate = true;
    $scope.filesize = 20;
    $scope.mimetypes = ".jpg,.jpeg,.png";

    $scope.isCreateThumbnail = true;
    $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
    $scope.uploadDesc = "必填，仅支持一张图片，仅支持jpg，jpeg，png格式";
    $scope.numlimit = 1;
    $scope.urlList = [];
    $scope.uploadParam = {
      enterpriseId: '',
      fileUse: 'qrCode'
    };
    $scope.publicName = "";
    $scope.qrCode = "";
    $scope.explain = "";
    $scope.id = "";
    $scope.isExist = false;
    $scope.isEdit = false;
    $scope.isError = false;

    $scope.queryOfficialAccountList();
  };

  $scope.queryOfficialAccountList = function () {
    RestClientUtil.ajaxRequest({
      type: 'POST',
        url: "/ecpmp/ecpmpServices/OfficialAccountService/queryOfficialAccountList",
      data: JSON.stringify({}),
      success: function (data) {
        $rootScope.$apply(function () {
          if (data && data.result && data.result.resultCode === "**********") {
            var list = data.officialAccountList;
            if (angular.isArray(list) && list.length > 0) {
              $scope.isExist = true;
              $scope.isEdit = false;
              $scope.isError = false;
              $scope.publicName = list[0].name;
              $scope.qrCode = list[0].qrCode;
              $scope.explain = list[0].desc;
              $scope.id = list[0].id;
              $scope.imgUrl = CommonUtils.formatPic($scope.qrCode).review;
              $scope.urlList = [$scope.imgUrl];
            } else {
              $scope.isExist = true;
              $scope.isEdit = false;
              $scope.isError = false;
              $scope.publicName = "";
              $scope.qrCode = "";
              $scope.imgUrl = "";
              $scope.urlList =[];
              $scope.explain = "";
            }
          } else {
            $scope.isError = true;
            $scope.isExist = true;
            $scope.isEdit = false;
            $scope.publicName = "";
            $scope.qrCode = "";
            $scope.imgUrl = "";
            $scope.urlList =[];
            $scope.explain = "";
            $scope.tip = data.result.resultCode;
            $("#publicModal").modal();
          }
        })
      },
      error: function (data) {
        $rootScope.$apply(function () {
          $scope.isError = true;
          $scope.isExist = true;
          $scope.isEdit = false;
          $scope.publicName = "";
          $scope.qrCode = "";
          $scope.imgUrl = "";
          $scope.explain = "";
          $scope.tip = data.result.resultCode;
          $("#publicModal").modal();
        })
      }
    })
  };


  $scope.save = function () {
    var req = {
      "officialAccount": {
        "id": $scope.id,
        "name": $scope.publicName,
        "qrCode": $scope.qrCode,
        "desc": $scope.explain,
        "operatorID": $scope.operatorID
      }
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/OfficialAccountService/saveOfficialAccount",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          if (data && data.result && data.result.resultCode === "**********") {
            $scope.tip = "COMMON_SUBMIT_SUCCESS";
            $("#publicModal").modal();
            $('#publicModal').on('hide.bs.modal', function () {
              $scope.queryOfficialAccountList();
              $(this).off("hide.bs.modal");
            });
          } else {
            $scope.tip = data.result.resultCode;
            $("#publicModal").modal();
          }
        })
      },
      error: function (data) {
        $rootScope.$apply(function () {
        })
      }
    })
  };

  $scope.$on("uploadifyid", function (event, fileUrl) {
    $scope.qrCode = fileUrl;
  })
});

app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}]);