<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryTaskMapper">
	<resultMap id="DeliveryTaskModel" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryTaskWrapper">
		<result property="id" column="ID"/>
		<result property="msgType" column="msgtype"/>
		<result property="src" column="src"/>
		<result property="biztype" column="biztype"/>
		<result property="notifyURL" column="notifyURL"/>
		<result property="enterpriseID" column="enterpriseID"/>
		<result property="insertTime" column="insertTime"/>
		<result property="triggerdeliveryTime" column="triggerdeliveryTime"/>
		<result property="notifyTime" column="notifyTime"/>
		<result property="deliverystatus" column="deliverystatus"/>
		<result property="notifyStatus" column="notifyStatus"/>
		
		<result property="thirdDeliveryReqTime" column="thirdDeliveryReqTime"/>
		<result property="thirdDeliveryRspTime" column="thirdDeliveryRspTime"/>
		<result property="notifyThirdReqTime" column="notifyThirdReqTime"/>
		<result property="notifyThirdRspTime" column="notifyThirdRspTime"/>
		<result property="processStatus" column="processStatus"/>
		<result property="notifyCount" column="notifyCount"/>
	</resultMap>

	<resultMap type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryTaskWrapper" id="deliveryTaskMap">
		<result property="id" column="ID" />
		<result property="msgType" column="msgtype" />
		<result property="src" column="src" />
		<result property="biztype" column="biztype" />
		<result property="notifyURL" column="notifyURL" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="triggerdeliveryTime" column="triggerdeliveryTime" />
		<result property="notifyTime" column="notifyTime" />
		<result property="deliverystatus" column="deliverystatus" />
		<result property="notifyStatus" column="notifyStatus" />
		
		<result property="thirdDeliveryReqTime" column="thirdDeliveryReqTime"/>
		<result property="thirdDeliveryRspTime" column="thirdDeliveryRspTime"/>
		<result property="notifyThirdReqTime" column="notifyThirdReqTime"/>
		<result property="notifyThirdRspTime" column="notifyThirdRspTime"/>
	</resultMap>

	<sql id="thirdpartyDeliveryTaskCol">
		ID,
		msgtype,
		src,
		biztype,
		notifyURL,
		enterpriseID,
		insertTime,
		triggerdeliveryTime,
		notifyTime,
		deliverystatus,
		notifyStatus,
		thirdDeliveryReqTime,
		thirdDeliveryRspTime,
		notifyThirdReqTime,
		notifyThirdRspTime,
		processStatus,
		notifyCount
	</sql>
	<sql id="thirdpartyDeliveryTaskCol2">
		ID,
		msgtype,
		src,
		biztype,
		notifyURL,
		enterpriseID,
		insertTime,
		triggerdeliveryTime,
		notifyTime,
		deliverystatus,
		notifyStatus,
		thirdDeliveryReqTime,
		thirdDeliveryRspTime,
		notifyThirdReqTime,
		notifyThirdRspTime,
		processStatus,
		notifyCount,
		cmppSubmitReqJson
	</sql>
	<insert id="insertThirdpartyDeliveryTask" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryTaskWrapper">
		insert into ecpe_t_thirdparty_delivery_task
		(
			<include refid="thirdpartyDeliveryTaskCol2"/>
		) 
		values
		(
		#{id},
		#{msgType},
		#{src},
		#{biztype},
		#{notifyURL},
		#{enterpriseID},
		#{insertTime},
		#{triggerdeliveryTime},
		#{notifyTime},
		#{deliverystatus},
		#{notifyStatus},
		#{thirdDeliveryReqTime},
		#{thirdDeliveryRspTime},
		#{notifyThirdReqTime},
		#{notifyThirdRspTime},
		#{processStatus},
		#{notifyCount},
		#{cmppSubmitReqJson}
		)
	</insert>
	
	<select id="getNextDeliveryTaskID" resultType="java.lang.Long">
		select next value for MYCATSEQ_ECPE_T_THIRDPARTY_DELIVERY_TASK
	</select>
	
    <select id="queryDeliveryTaskByID" resultMap="DeliveryTaskModel">
		select <include refid="thirdpartyDeliveryTaskCol2"/>
		  from ecpe_t_thirdparty_delivery_task
		 where ID = #{taskID}
	</select>
	
	<select id="queryDeliveryTaskHistoryByID" resultMap="DeliveryTaskModel">
		select <include refid="thirdpartyDeliveryTaskCol"/>
		  from ecpe_t_thirdparty_delivery_task_history
		 where ID = #{taskID}
	</select>
	
	<update id="updateNotifyStatusForSchedule">
		UPDATE ecpe_t_thirdparty_delivery_task t 
		   SET 
		   <trim suffixOverrides=",">
			<if test="notifyStatus !=null">
				notifyStatus = #{notifyStatus},
			</if>
			<if test="notifyTime !=null">
				notifyTime = #{notifyTime},
			</if>
			<if test="processStatus !=null">
            	processStatus = #{processStatus},
            </if>
            <if test="updateTime !=null">
            	updateTime = #{updateTime},
            </if>
            <if test="notifyCount !=null">
            	notifyCount = notifyCount+1,
            </if>
		</trim>
		 WHERE t.ID IN
			<foreach collection="list" item="taskID" separator="," open="(" close=")">
				#{taskID}
			</foreach>
			<if test="insertTime !=null">
		     and insertTime = #{insertTime}
		    </if>
	</update>
	
	<update id="updateDeliveryAboutTime">
		UPDATE ecpe_t_thirdparty_delivery_task  SET 
		<trim suffixOverrides=",">
			<if test="thirdDeliveryReqTime !=null">
				thirdDeliveryReqTime = #{thirdDeliveryReqTime},
			</if>
			<if test="thirdDeliveryRspTime !=null">
				thirdDeliveryRspTime = #{thirdDeliveryRspTime},
			</if>
			<if test="notifyThirdReqTime !=null">
            	notifyThirdReqTime = #{notifyThirdReqTime},
            </if>
            <if test="notifyThirdRspTime !=null">
            	notifyThirdRspTime = #{notifyThirdRspTime},
            </if>
            
            <if test="processStatus !=null">
            	processStatus = #{processStatus},
            </if>
            <if test="notifyStatus !=null">
            	notifyStatus = #{notifyStatus},
            </if>
            <if test="notifyCount !=null">
            	notifyCount = notifyCount+1,
            </if>
            <if test="updateTime !=null">
            	updateTime = #{updateTime},
            </if>
            <if test="notifyTime !=null">
            	notifyTime = #{notifyTime},
            </if>
		</trim>
		WHERE id = #{id}
		<if test="insertTime !=null">
		     and insertTime = #{insertTime}
		</if>
	</update>
	
	<delete id="deleteThirdpartyDeliveryTaskHistoryByID" parameterType="java.lang.Long">
	    delete from ecpe_t_thirdparty_delivery_task_history where ID=#{ID}
	</delete>

	<delete id="batchDeleteThirdpartyDeliveryTaskHistoryByID" parameterType="java.util.List">
		delete from ecpe_t_thirdparty_delivery_task_history where ID in
		<foreach item="id" index="index" collection="list"
				 open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>