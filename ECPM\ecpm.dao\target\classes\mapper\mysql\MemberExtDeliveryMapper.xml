<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberExtDeliveryMapper">
	<resultMap id="memberExtDeliveryWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberExtDeliveryWrapper">
		<result property="ID" column="ID" javaType="java.lang.Integer" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="deliveryCount" column="deliveryCount" javaType="java.lang.Integer" />
		<result property="deliveryStatus" column="deliveryStatus" javaType="java.lang.Integer" />
		<result property="leftCount" column="leftCount" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
	</resultMap>
	<select id="queryMemberExtDelivery" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberExtDeliveryWrapper">
		select ID,
		msisdn,
		subServType,
		deliveryCount,
		deliveryStatus,
		leftCount,
		enterpriseID
		from ecpm_t_member_ext_delivery
		<trim prefix="where" prefixOverrides="and|or">
			<if test="msisdn != null">
				msisdn=#{msisdn}
			</if>
			<if test="subServType != null">
				and subServType=#{subServType}
			</if>
			<if test="deliveryStatus != null">
				and deliveryStatus=#{deliveryStatus}
			</if>
			<if test="enterpriseID != null">
				and enterpriseID=#{enterpriseID}
			</if>
		</trim>
	</select>

	<select id="queryExternalProductSubscribe" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberExtDeliveryWrapper">
		SELECT 
			t1.msisdn,
			t3.subServType,
			SUM(t2.limitQuotaNum) AS limitQuotaNum,
			t5.enterpriseID,
			t6.enterpriseCode,
			MAX(IF(t2.productType=0,t1.extProID,NULL)) extProID,
			MAX(IF(t2.productType=0,t2.productName,NULL)) productName,
			1 packageType
		FROM 
			ecpm_t_external_product_subscribe t1 
			LEFT JOIN ecpm_t_external_product t2  ON t1.extProID = t2.productID
			JOIN ecpm_t_member_ext_delivery t3  ON t1.msisdn = t3.msisdn
			JOIN ecpm_t_member t4 ON t1.msisdn = t4.msisdn
			JOIN ecpm_t_org_rel t5 ON t4.ID = t5.ID AND t5.enterpriseID = t3.enterpriseID
			LEFT JOIN ecpm_t_enterprise_simple t6 ON t5.enterpriseID = t6.ID
		WHERE 
			deliveryStatus = 0 
			AND IFNULL(t1.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
			AND NOW() &lt;= IFNULL(t1.expiryTime, '2999-12-31 23:59:59')
			AND t2.productType in (0, 1)
			<if test="msisdn != null">
				AND t1.msisdn=#{msisdn}
			</if>
		GROUP BY t1.msisdn, t3.subServType, t5.enterpriseID
		
		UNION
		
		SELECT 
			t1.msisdn,
			t3.subServType,
			t2.limitQuotaNum,
			t5.enterpriseID,
			t6.enterpriseCode,
			t1.extProID,
			t2.productName,
			2 packageType
		FROM 
			ecpm_t_external_product_subscribe t1 
			LEFT JOIN ecpm_t_external_product t2  ON t1.extProID = t2.productID
			LEFT JOIN ecpm_t_member_ext_delivery t3  ON t1.msisdn = t3.msisdn
			JOIN ecpm_t_member t4 ON t1.msisdn = t4.msisdn
			JOIN ecpm_t_org_rel t5 ON t4.ID = t5.ID AND t5.enterpriseID = t3.enterpriseID
			LEFT JOIN ecpm_t_enterprise_simple t6 ON t5.enterpriseID = t6.ID
		WHERE 
			deliveryStatus = 0 
			AND IFNULL(t1.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
			AND NOW() &lt;= IFNULL(t1.expiryTime, '2999-12-31 23:59:59')
			AND t2.productType = 2
			<if test="msisdn != null">
				AND t1.msisdn=#{msisdn}
			</if>
	</select>

	<select id="queryExternalProductSubscribeAllStatus" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberExtDeliveryWrapper">
		SELECT
		t1.msisdn,
		t3.subServType,
		SUM(t2.limitQuotaNum) AS limitQuotaNum,
		t5.enterpriseID,
		t6.enterpriseCode,
		MAX(IF(t2.productType=0,t1.extProID,NULL)) extProID,
		MAX(IF(t2.productType=0,t2.productName,NULL)) productName,
		1 packageType
		FROM
		ecpm_t_external_product_subscribe t1
		LEFT JOIN ecpm_t_external_product t2  ON t1.extProID = t2.productID
		LEFT JOIN ecpm_t_member_ext_delivery t3  ON t1.msisdn = t3.msisdn
		LEFT JOIN ecpm_t_member t4 ON t1.msisdn = t4.msisdn
		LEFT JOIN ecpm_t_org_rel t5 ON t4.ID = t5.ID
		LEFT JOIN ecpm_t_enterprise_simple t6 ON t5.enterpriseID = t6.ID
		WHERE
		IFNULL(t1.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
		AND NOW() &lt;= IFNULL(t1.expiryTime, '2999-12-31 23:59:59')
		AND t2.productType in (0, 1)
		<if test="msisdn != null">
			AND t1.msisdn=#{msisdn}
		</if>
		GROUP BY t1.msisdn, t3.subServType, t5.enterpriseID

		UNION

		SELECT
		t1.msisdn,
		t3.subServType,
		t2.limitQuotaNum,
		t5.enterpriseID,
		t6.enterpriseCode,
		t1.extProID,
		t2.productName,
		2 packageType
		FROM
		ecpm_t_external_product_subscribe t1
		LEFT JOIN ecpm_t_external_product t2  ON t1.extProID = t2.productID
		LEFT JOIN ecpm_t_member_ext_delivery t3  ON t1.msisdn = t3.msisdn
		LEFT JOIN ecpm_t_member t4 ON t1.msisdn = t4.msisdn
		LEFT JOIN ecpm_t_org_rel t5 ON t4.ID = t5.ID
		LEFT JOIN ecpm_t_enterprise_simple t6 ON t5.enterpriseID = t6.ID
		WHERE
		IFNULL(t1.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
		AND NOW() &lt;= IFNULL(t1.expiryTime, '2999-12-31 23:59:59')
		AND t2.productType = 2
		<if test="msisdn != null">
			AND t1.msisdn=#{msisdn}
		</if>
	</select>

	<select id="queryExternalProduct" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberExtDeliveryWrapper">
		SELECT 
			t1.ID,
			t1.msisdn,
			t1.deliveryStatus,
			t1.subServType,
			t2.extProID,
			t2.effectiveTime,
			t2.expiryTime
		FROM ecpm_t_member_ext_delivery t1 
		JOIN ecpm_t_member t4 ON t1.msisdn = t4.msisdn
		JOIN ecpm_t_org_rel t5 ON t4.ID = t5.ID AND t5.enterpriseID = t1.enterpriseID
		LEFT JOIN ecpm_t_external_product_subscribe t2 ON t1.msisdn = t2.msisdn
		WHERE
			IFNULL(t2.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
			AND NOW() &lt;= IFNULL(t2.expiryTime, '2999-12-31 23:59:59')
			AND (
				t1.deliveryStatus = 0 and t2.extProID is null
				or
				t1.deliveryStatus <![CDATA[<>]]> 0 and t2.extProID is not null
			)
	</select>
	
	<insert id="createMemberExtDelivery">
		insert into ecpm_t_member_ext_delivery
		(
		msisdn,
		subServType,
		deliveryCount,
		deliveryStatus,
		lastUpdateTime,
		leftCount,
		enterpriseID
		)
		values
		(
		#{msisdn},
		#{subServType},
		#{deliveryCount},
		#{deliveryStatus},
		now(),
		#{leftCount},
		#{enterpriseID}
		)
	</insert>

	<delete id="deleteMemberExtDelivery">
		delete from ecpm_t_member_ext_delivery where ID =
		#{ID}
	</delete>
	
	<update id="updateMemberExtDelivery">
		update ecpm_t_member_ext_delivery set
        <if test="msisdn != null">
            msisdn=#{msisdn},
        </if>
        <if test="subServType != null">
            subServType=#{subServType},
        </if>
        <if test="deliveryCount != null">
            deliveryCount=#{deliveryCount},
        </if>
		<if test="leftCount != null">
			leftCount = if(#{leftCount}>=0,#{leftCount},0),
		</if>
        <if test="deliveryStatus != null">
            deliveryStatus=#{deliveryStatus},
        </if>
        	lastUpdateTime = now()
		where ID=#{ID}
	</update>
	
	<update id="updateDeliveryCount">
		update 
			ecpm_t_member_ext_delivery 
		set 
			deliveryCount=#{deliveryCount},
			lastUpdateTime = now()
	</update>
	
	<update id="updateCountOrStatus">
		update 
			ecpm_t_member_ext_delivery 
		set
	        <if test="deliveryCount != null">
	            deliveryCount = #{deliveryCount},
	        </if>
	        <if test="deliveryStatus != null">
	            deliveryStatus = #{deliveryStatus},
	        </if>
			<if test="leftCount != null">
				leftCount = if(#{leftCount}>=0,#{leftCount},0),
			</if>
        	lastUpdateTime = now()
		where
			msisdn = #{msisdn}
			<if test="subServType != null">
				and subServType = #{subServType}
			</if>
			<if test="enterpriseID != null">
				and enterpriseID = #{enterpriseID}
			</if>
	</update>

	<select id="querySharePacUsedNum" resultType="java.lang.Integer">
		SELECT 
			SUM(t2.deliveryCount)
		FROM ecpm_t_external_product_subscribe t1 
		LEFT JOIN ecpm_t_member_ext_delivery t2 ON t1.msisdn = t2.msisdn
		LEFT JOIN ecpm_t_member m on m.msisdn = t2.msisdn
		LEFT JOIN ecpm_t_org_rel rel on rel.id = m.ID
		WHERE
			IFNULL(t1.effectiveTime, '1990-01-01 00:00:00') &lt;= NOW()
			AND NOW() &lt;= IFNULL(t1.expiryTime, '2999-12-31 23:59:59')
			AND t1.extProID = #{extProID}
		    and rel.enterpriseID = #{enterpriseID}
		GROUP BY t1.extProID
	</select>
	
	<insert id="insertIntoBak">
		INSERT INTO ecpm_t_member_ext_delivery_bak
		SELECT t.ID,t.msisdn,t.subServType,t.deliveryCount,t.deliveryStatus,t.lastUpdateTime,t.leftCount,t.enterpriseID,
		#{lastMonth} `month` 
		FROM ecpm_t_member_ext_delivery t;
	</insert>
	
	<delete id="deleteUseless">
		DELETE t FROM ecpm_t_member_ext_delivery t
		LEFT JOIN ecpm_t_member m ON t.msisdn=m.msisdn
		LEFT JOIN ecpm_t_org_rel o ON m.ID=o.ID
		WHERE (t.enterpriseID <![CDATA[<>]]> o.enterpriseID OR o.enterpriseID IS NULL)
	</delete>

	<select id="queryByEnterpriseIDAndMsisdn" resultMap="memberExtDeliveryWrapper">
		select *
		from ecpm_t_member_ext_delivery
		where (deliveryStatus = 1 or deliveryStatus = 2) and msisdn in
		<foreach item="msisdn" index="index" collection="msisdnList"
				 open="(" separator="," close=")">
			#{msisdn}
		</foreach>
		<if test="subServType != null">
			and subServType = #{subServType}
		</if>
		<if test="enterpriseID != null">
			and enterpriseID = #{enterpriseID}
		</if>
	</select>
</mapper>