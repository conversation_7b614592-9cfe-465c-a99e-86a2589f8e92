var app = angular.module("myApp", ["util.ajax", "top.menu","angularI18n","cy.uploadify","service.common"])
app.controller('merchantController', function ($scope, $rootScope, $http, $location, RestClientUtil,CommonUtils) {
	  
	  $scope.filePicker = "filePicker1,filePicker2,filePicker3";
	  $scope.accepttype = "jpg,jpeg,png";
	  $scope.isValidate = true;
	  $scope.filesize = 20;
	  $scope.mimetypes = ".jpg,.jpeg,.png";
	  
	  $scope.isCreateThumbnail = true;
	  $scope.uploadurl ='/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
	  $scope.uploadDesc = "必填，仅支持一张图片，仅支持jpg，jpeg，png格式";
	  $scope.numlimit = 1;
	  $scope.businessLicenseUrlList = [];
	  $scope.cardPositiveUrlList = [];
	  $scope.cardOppositeUrlList = [];
	  
	  $scope.$on("uploadifyid1",function(event,fileUrl){
		  if (fileUrl !== "") {
			  $scope.businessLicenseUrl={};
			  $scope.businessLicenseUrl.upload = fileUrl;
	      } else {
	    	  $scope.businessLicenseUrl=null;
	      }
	  });
	  $scope.$on("uploadifyid2",function(event,fileUrl){
		  if (fileUrl !== "") {
			  $scope.idCardPositiveUrl={};
			  $scope.idCardPositiveUrl.upload = fileUrl;
	      } else {
	    	  $scope.idCardPositiveUrl=null;
	      }
	  });
	  $scope.$on("uploadifyid3",function(event,fileUrl){
		  if (fileUrl !== "") {
			  $scope.idCardOppositeUrl={};
			  $scope.idCardOppositeUrl.upload = fileUrl;
	      } else {
	    	  $scope.idCardOppositeUrl=null;
	      }
	  });

  /*查询企业详情 */
  $scope.init = function () {
	  $scope.merchantId = $.cookie("enterpriseID");
	  $scope.operate = $.cookie("operationType");
	  
	  $scope.licenseUploadParam = {
	    "enterpriseId": $.cookie("enterpriseID") ||'',
	    "fileUse": 'businessLicense'
	  };
	  $scope.cardPositiveUploadParam = {
	    "enterpriseId": $.cookie("enterpriseID") ||'',
	    "fileUse": 'idCardPositive'
	  };
	  $scope.cardOppositeUploadParam = {
	    "enterpriseId": $.cookie("enterpriseID") ||'',
	    "fileUse": 'idCardOpposite'
	  };
	  
	  $scope.merchant ={};
	  $scope.merchantNameValidate =true;
	  $scope.msisdnValidate =true;
	  $scope.merchantNameExist = false;
	  $scope.queryMerchantDetails();
  }
  
  /* 从url中获取参数 */
  $scope.convertUrlToPara = function () {
    var url = angular.copy(window.location.href);
    url = url.split("?")[1];
    var res = {};
    if (url) {
      var para = url.split("&");
      var arr = [];
      var len = para.length;
      for (var i = 0; i < len; i++) {
        arr = para[i].split("=");
        res[arr[0]] = arr[1];
      }
    }
    return res;
  };
  
  /*查询企业详情 */
  $scope.queryMerchantDetails = function () {
    var req = {};
    req.id = $scope.merchantId;
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.merchant = data.enterprise;
            $scope.setMerchantDetails();
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };

  /* 非新增时企业详情 赋值*/
  $scope.setMerchantDetails = function () {
	$scope.merchantNameTemp = $scope.merchant.enterpriseName;
	$scope.msisdnTemp = $scope.merchant.msisdn;
	$scope.businessLicenseURLTemp = $scope.merchant.businessLicenseURL;
	$scope.idCardPositiveURLTemp = $scope.merchant.idCardPositiveURL;
	$scope.idCardOppositeURLTemp = $scope.merchant.idCardOppositeURL;
	$scope.businessLicenseUrl ='';
	$scope.idCardPositiveUrl ='';
	$scope.idCardOppositeUrl ='';
	if ($scope.operate =='detail') {
        $scope.businessLicenseUrl = CommonUtils.formatPic($scope.merchant.businessLicenseURL);
        $scope.businessLicenseUrl.upload =$scope.merchant.businessLicenseURL;
        $scope.businessLicenseUrlList=[$scope.businessLicenseUrl.review];

        $scope.idCardPositiveUrl = CommonUtils.formatPic($scope.merchant.idCardPositiveURL);
        $scope.idCardPositiveUrl.upload =$scope.merchant.idCardPositiveURL;
        $scope.cardPositiveUrlList=[$scope.idCardPositiveUrl.review];

        $scope.idCardOppositeUrl = CommonUtils.formatPic($scope.merchant.idCardOppositeURL);
        $scope.idCardOppositeUrl.upload =$scope.merchant.idCardOppositeURL;
        $scope.cardOppositeUrlList=[$scope.idCardOppositeUrl.review];
    }

  };
  
  /* 验证企业名称 */
  $scope.checkMerchantName = function (merchantName,condition) {
	$scope.condition =condition;
	$scope.merchantNameValidate =true;
	$scope.merchant.merchantNameDesc ='';
    //$scope.merchantNameValidate = $scope.validate(merchantName, 256, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/);
    if($scope.merchantNameTemp == merchantName && $scope.operate=='edit'){
    	  $scope.merchantNameExist = false;
    	  if($scope.condition =='save'){
    		  $scope.save();
    	  }
		  return;
	  }
    $scope.merchantNameExist = false;
    var QueryAccountListReq = {};
    QueryAccountListReq.content = angular.copy(merchantName);
    QueryAccountListReq.serviceType = 1;
    /*模糊匹配企业名称，匹配到则飘红*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
      data: JSON.stringify(QueryAccountListReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          $scope.merchant.merchantNameDesc = '';
          //是否存在重复的用户名称
          if (result.resultCode =='**********') {
            $scope.merchantNameExist = true;
            $scope.merchant.merchantNameDesc = 'MERCHANT_ENTERPRISENAMEEXIST';
          }
          if(result.resultCode !='**********' && result.resultCode !='**********'){
          	$scope.tip = result.resultCode;
              $('#myModal').modal();
          }
          if($scope.condition =='save'){
    		  $scope.save();
    	  	}
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
    
    /*if (!$scope.merchantNameValidate) {
      $scope.merchant.merchantNameDesc = 'MERCHANT_ENTERPRISENAMEDESC';
    } else {
      $scope.merchantNameExist = false;
      var QueryAccountListReq = {};
      QueryAccountListReq.content = angular.copy(merchantName);
      QueryAccountListReq.serviceType = 1;
      模糊匹配企业名称，匹配到则飘红
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
        data: JSON.stringify(QueryAccountListReq),
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            $scope.merchant.merchantNameDesc = '';
            //是否存在重复的用户名称
            if (result.resultCode =='**********') {
              $scope.merchantNameExist = true;
              $scope.merchant.merchantNameDesc = 'MERCHANT_ENTERPRISENAMEEXIST';
            }
            if(result.resultCode !='**********' && result.resultCode !='**********'){
            	$scope.tip = result.resultCode;
                $('#myModal').modal();
            }
            if($scope.condition =='save'){
      		  $scope.save();
      	  	}
          })
        },
        error:function(){
            $rootScope.$apply(function(){
                $scope.tip='**********';
                $('#myModal').modal();
                }
            )
        }
      });
    }*/
  };
  /* 验证手机号 */
  $scope.checkMsisdn = function (msisdn) {
    $scope.msisdnValidate = $scope.validate(msisdn, 11, /^[0-9]{11}$/);
  };

  /*保存前验证与赋值 */
  $scope.beforeSave = function () {
    var merchant = $scope.merchant;
    $scope.merchant.enterpriseType = 4;
    //保存前再校验一遍唯一性
    $scope.condition ='save';
    $scope.checkMerchantName(merchant.enterpriseName,$scope.condition);
  };
  
  /*保存*/
  $scope.save = function () {
    if ($scope.merchantNameExist == true) {
      return;
    }
    var updateEnterpriseReq = {
    		"enterprise":{
    			"id":$scope.merchant.id,
    			"enterpriseType":$scope.merchant.enterpriseType,
    			"enterpriseName":$scope.merchant.enterpriseName,
    			"msisdn":$scope.merchant.msisdn,
    			"businessLicenseURL":$scope.businessLicenseUrl.upload,
    			"idCardPositiveURL":$scope.idCardPositiveUrl.upload,
    			"idCardOppositeURL":$scope.idCardOppositeUrl.upload,
    		}
    
    };
	if($scope.merchantNameTemp ==updateEnterpriseReq.enterprise.enterpriseName){
    	delete(updateEnterpriseReq.enterprise.enterpriseName);
    }
	if($scope.msisdnTemp ==updateEnterpriseReq.enterprise.msisdn){
    	delete(updateEnterpriseReq.enterprise.msisdn);
    }
	if($scope.businessLicenseURLTemp ==updateEnterpriseReq.enterprise.businessLicenseURL){
    	delete(updateEnterpriseReq.enterprise.businessLicenseURL);
    }
	if($scope.idCardPositiveURLTemp ==updateEnterpriseReq.enterprise.idCardPositiveURL){
    	delete(updateEnterpriseReq.enterprise.idCardPositiveURL);
    }
	if($scope.idCardOppositeURLTemp ==updateEnterpriseReq.enterprise.idCardOppositeURL){
    	delete(updateEnterpriseReq.enterprise.idCardOppositeURL);
    }
    updateEnterpriseReq.enterprise.operatorID = $.cookie("accountID");
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/updateEnterprise",
      data: JSON.stringify(updateEnterpriseReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            location.href = '../merchantList/merchantList.html';
          }else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
        })
      },
      error:function(){
          $rootScope.$apply(function(){
              $scope.tip='**********';
              $('#myModal').modal();
              }
          )
      }
    });
  };

  /*点击取消返回列表页面*/
  $scope.cancelToMerchantList = function () {
    if ($scope.operate == 'detail') {
    	location.href = '../merchantList/merchantList.html';
    }
    else {
    	$scope.tip='COMMON_RETURNTOLIST';
        $('#cancelToList').modal();
    }
  };

  /*修改页面返回列表页面*/
  $scope.editToMerchantList = function () {
      location.href = '../merchantList/merchantList.html';
  };
  $scope.exportFile = function (downloadUrl) {
	  var req = {
			   "param":{
				   "path": downloadUrl,
	                "token": $.cookie("token"),
	                "isExport": 0
			   },
		       "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
		       "method":"get"
		     }
		     CommonUtils.exportFile(req);
  };
  /*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
  
  
});




