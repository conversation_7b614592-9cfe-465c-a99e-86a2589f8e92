var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n", "cy.uploadify", "preview", "service.common"])
app.controller('createAdvertiseCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {

  $scope.loadReady = function () {
    var bodyH = $scope.demoIframe.contents().find("body").get(0).scrollHeight,
        htmlH = $scope.demoIframe.contents().find("html").get(0).scrollHeight,
        maxH = Math.max(bodyH, htmlH), minH = Math.min(bodyH, htmlH),
        h = $scope.demoIframe.height() >= maxH ? minH : maxH;
    if (h < 530) h = 530;
    $scope.demoIframe.height(h);
  }


  $scope.init = function () {
    //防止页面后退
//    history.pushState(null, null, document.URL);
//    window.addEventListener('popstate', function () {
//      history.pushState(null, null, document.URL);
//    });
    $scope.noSign = '0';
    $scope.activityID = $location.$$search.activityID;
    $scope.accountID = $.cookie("accountID");
    $scope.enterpriseType = $.cookie("enterpriseType");
    $scope.enterpriseID = $.cookie("enterpriseID");
    $scope.subEnterpriseID = $.cookie("subEnterpriseID");
    $scope.loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
    $scope.isZhike = ($scope.loginRoleType == 'zhike');
    $scope.isAgent = ($scope.loginRoleType == 'agent');
    //直客
    if ($scope.enterpriseType === "1" || $scope.isZhike) {
      $scope.eId = $scope.enterpriseID;
    }
    //二级企业
    if ($scope.enterpriseType === "3" || $scope.isAgent) {
      $scope.eId = $scope.subEnterpriseID;
      $scope.agentID = $scope.enterpriseID;
      $scope.enterpriseName = $.cookie('enterpriseName');
    }
    $scope.isExist = false;
    $scope.ruleRestCount = 200;
    $scope.MMSRestCount = 750;
    $scope.PXRestCount = 70;
    $scope.notifyRestCount = 62;
    $scope.activityName = "";
    $scope.rewardType = 0;
    $scope.spokePushNum = "";
    $scope.spokeDayNum = "";
    $scope.effectivetime = "";
    $scope.expiretime = "";
    $scope.templateList = [];
    $scope.templateURL = [];
    $scope.bannerList = [];
    $scope.showCities = [];
    $scope.activity_cityList = [];

    $scope.sign="  ";
    $scope.screen_sensitive = "";
    $scope.title_sensitive = "";
    $scope.MMS_sensitive = "";
    $scope.contentTitle = "";
    $scope.MMSPicture = [];
    $scope.MMSContent = "";
    $scope.MMSContent_placeholder = "请先填写挂机彩信标题";
    $scope.nofiledesc = "请先选择需要预览的模板！";

    $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
    $scope.mimetypes = ".jpg,.jpeg,.png";
    $scope.isCreateThumbnail = true;
    $scope.accepttype = "jpg,jpeg,png";
    $scope.uploadParam = {
      enterpriseId: $scope.eId,
      fileUse: "businessLicense",
      h5UseType: "1"
    };

    //彩信图片上传初始化
    $scope.MMS_isValidate = true;
    $scope.MMS_filesize = 0.28;
    $scope.MMS_uploadDesc = "必填，最多可上传5张图片,仅支持jpg，jpeg，png格式，图片总大小不可大于280KB";
    $scope.MMS_numlimit = 5;
    $scope.MMS_urlList = [];

    //banner图片上传初始化
    $scope.banner_isValidate = true;
    $scope.banner_filesize = 5;
    $scope.banner_uploadDesc = "必填，最多支持3张图片，仅支持jpg，jpeg，png格式，不可大于5M";
    $scope.banner_numlimit = 3;
    $scope.banner_urlList = [];
    $scope.contentDesc = '';
    $scope.contentVali = true;

    $('.input-daterange').datepicker({
      format: "yyyy-mm-dd",
      weekStart: 0,
      language: "zh-CN",
      autoclose: true
    });

    $scope.setting = {
      check: {
        enable: true
      },
      view: {
        dblClickExpand: false,
        showLine: false,
        selectedMulti: false,
        showIcon: false
      },
      data: {
        simpleData: {
          enable: true,
          idKey: "id",
          pIdKey: "pId",
          rootPId: ""
        }
      },
      callback: {
        beforeClick: function (treeId, treeNode) {
          if (treeNode.isParent) {
            $scope.zTree.expandNode(treeNode);
            return false;
          } else {
            $scope.demoIframe.attr("src", treeNode.file + ".html");
            var tId = treeNode.tId;
            var selectNode = $('#' + tId).find('a');
            //去除单击节点的背景色
            if (!$(selectNode).hasClass('no-curSelectedNode')) {
              $(selectNode).addClass('no-curSelectedNode')
            }
            return true;
          }
        },
        onClick: function (e, treeId, treeNode, clickFlag) {
          //单击节点名称选中
          $scope.zTree.checkNode(treeNode, !treeNode.checked, true);
        }
      }
    };
    $scope.queryActivityTemplateList()
    $scope.queryProvinceAndCity();

    //查询企业服务开关,当为add时初始化运营商选项按钮和勾选状态
    $scope.queryPlatformStatus();

    //查询所属行业列表
    $scope.queryIndustry($scope);

    //初始化营业执照容器
    $scope.initBusinessURLContainer($scope);



  };

  $scope.changeName = function () {
    if ($scope.myForm.activityName.$invalid) {
      $scope.isExist = false;
    }
  }

  $scope.changeTitle = function () {
    if (!$scope.contentTitle) {
      $scope.MMSContent_placeholder = "请先填写挂机彩信标题";
      $scope.MMSContent = "";
    } else {
      $scope.MMSContent_placeholder = "请输入挂机彩信内容，750字以内";
      $scope.MMSContent = angular.copy($scope.MMSContent_temp);
    }
    $scope.calculateMSSRest();
  };
  $scope.chooseTemplate = function (template, index) {
    $scope.templateIndex = index;
    $scope.templateID = template.templateID;
    $scope.templateURL = [template.templateIconURL];
    $scope.templateIconURL = template.templateIconURL;
  };
  $scope.$on("MMS_uploadifyid", function (event,fileUrl,data) {
        if (data.operationType === "delete") {
            $scope.MMSPicture.splice(data.index, 1)
        } else {
            $scope.MMSPicture.push(fileUrl);
        }
  });
  $scope.$on("banner_uploadifyid", function (event, fileUrl, data) {
    if (data.operationType === "delete") {
      $scope.bannerList.splice(data.index, 1)
    } else {
      $scope.banner = {
        ownertype: 2,
        bannerURL: fileUrl
      };
      $scope.bannerList.push($scope.banner);
    }
  });
  $('#start').on('changeDate', function () {
    $rootScope.$apply(function () {
      var startTime = document.getElementById("start").value;
      startTime = startTime.substring(0, 4) + "/" + startTime.substring(5, 7) + "/" + startTime.substring(8, 10) + ' 00:00:00';
      $scope.effectivetime = (new Date(startTime)).getTime();
    })
  });

  $('#end').on('changeDate', function () {
    $rootScope.$apply(function () {
      var endTime = document.getElementById("end").value;
      endTime = endTime.substring(0, 4) + "/" + endTime.substring(5, 7) + "/" + endTime.substring(8, 10) + ' 23:59:59';
      $scope.expiretime = (new Date(endTime)).getTime();
    })
  });

  //统计规则剩余字数
  $scope.calculateRuleRest = function () {
    if ($scope.activityRuleDesc) {
      $scope.ruleRestCount = 200 - $scope.activityRuleDesc.length;
    } else if ($scope.myForm.activityRuleDesc.$error.maxlength) {
      $scope.ruleRestCount = 0
    } else {
      $scope.ruleRestCount = 200;
    }
  };
  //统计屏显内容剩余字数
  $scope.calculatePXRest = function () {
    if ($scope.pxContent) {
      $scope.PXRestCount = 70 - $scope.pxContent.length;
    } else if ($scope.myForm.pxContent.$error.maxlength) {
      $scope.PXRestCount = 0
    } else {
      $scope.PXRestCount = 70;
    }
    $scope.checkContent();
  };
  //统计彩信内容剩余字数
  $scope.calculateMSSRest = function () {
    if ($scope.MMSContent) {
      $scope.MMSRestCount = 750 - $scope.MMSContent.length;
    } else if ($scope.myForm.MMSContent.$error.maxlength) {
      $scope.MMSRestCount = 0
    } else {
      $scope.MMSRestCount = 750;
    }
  };
  //统计短信通知剩余字数
  $scope.calculateNotifyRest = function () {
    if ($scope.rewardNotify) {
      $scope.notifyRestCount = 62 - $scope.rewardNotify.length;
    } else if ($scope.myForm.rewardNotify.$error.maxlength) {
      $scope.notifyRestCount = 0
    } else {
      $scope.notifyRestCount = 62;
    }
  };
  $scope.queryActivityTemplateList = function () {
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/activityService/queryActivityTemplateList",
      data: JSON.stringify(),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.templateList = result.activityTemplateList;
          } else {
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })

      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  //查询所属行业
      $scope.queryIndustry = function ($scope) {
        //默认非敏感行业:0-非敏感；1-敏感
        $scope.selectedIndustryID = '';
        $scope.isSensitiveIndustry = '';
        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
          success: function (data) {
            $rootScope.$apply(function () {
              var result = data.result;
              if (result.resultCode == '**********') {
                  $scope.industryList = data.industryList;
              }else {
                  $scope.tip=data.result.resultCode;
                  $('#myModal').modal();
                }
            })
          },
          error:function(){
              $rootScope.$apply(function(){
                      $scope.tip = '**********';
                      $('#myModal').modal();
                  }
              )
          }
        });
      };

      //所属行业是否为敏感行业
          $scope.changeIsSensitive = function(selectedIndustry){
              if(selectedIndustry){
                  $scope.selectedIndustryID = selectedIndustry.industryID;
                  $scope.isSensitiveIndustry = selectedIndustry.isSensitiveIndustry;
              }else{
                  $scope.selectedIndustryID = '';
                  $scope.selectedIndustryName = '';
              }
              $scope.businessLicenseURLError = $scope.checkBusinessLicenseURL();
          }

          /* 所属行业为敏感行业，营业执照必填false校验通过 added by qwx806621 2019-9-5*/
                  $scope.checkBusinessLicenseURL = function () {
                      //所属行业必填
                      if ($scope.isSensitiveIndustry != '1'&&$scope.isSensitiveIndustry != '3')
                      {
                         return false;
                      }
                      //敏感行业营业执照必填
                      if ($scope.businessLicenseURL_ == "undefined"||$scope.businessLicenseURL_ == null || $scope.businessLicenseURL_ == "") {
                         return true;
                      }
                      return false;
                  }


  $scope.queryProvinceAndCity = function () {
    /*查询省份*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
      data: JSON.stringify({}),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.zNodes = [];
            $scope.provinceList = data.provinceList;
            angular.forEach($scope.provinceList, function (item, index) {
              if (item.provinceID === '000') {
                $scope.zNodes.push({id: item.provinceID, name: item.provinceName});
                $scope.provinceList.splice(index, 1);
              }
            });
            angular.forEach($scope.provinceList, function (item) {
              //省份和市区中存在重复id,如沈阳和贵州省id重复.加上"p"作为标志位,表示父级
              $scope.zNodes.push({id: item.provinceID + "p", pId: "000", name: item.provinceName});
            });
            if ($scope.provinceList) {
              var provinceIds = [];
              angular.forEach($scope.provinceList, function (item) {
                provinceIds.push(item.provinceID)
              });
              var queryCityListReq = {};
              queryCityListReq.provinceIDs = provinceIds;

              /*查询地市*/
              RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
                data: JSON.stringify(queryCityListReq),
                success: function (data) {
                  $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                      $scope.cityList = data.cityList;
                      var zNodeItem = {id: "", pId: "", name: ""};
                      angular.forEach($scope.cityList, function (eachData, key) {
                        if (key !== "000") {
                          angular.forEach(eachData, function (item) {
                            //父级区域加上标志位"p"后,子级的pId也要对应加上
                            zNodeItem = {id: item.cityID, pId: item.provinceID + "p", name: item.cityName};
                            $scope.zNodes.push(zNodeItem);
                          })
                        }
                      });


                    } else {
                      $scope.tip = result.resultCode;
                      $('#myModal').modal();
                    }
                  })
                },
                error: function () {
                  $rootScope.$apply(function (data) {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                      }
                  )
                }
              });
            }
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function (data) {
              //配置了**********为接口异常情况下提示
              $scope.tip = "**********";
              $('#myModal').modal();
            }
        )
      }
    });
  };

  $scope.addAreaPop = function () {
    $("#chooseAreaPop").modal();
    var t = $("#tree");
    t = $.fn.zTree.init(t, $scope.setting, $scope.zNodes);
    $scope.demoIframe = $("#testIframe");
    $scope.demoIframe.bind("load", $scope.loadReady);
    $scope.zTree = $.fn.zTree.getZTreeObj("tree");
    $scope.zTree.expandNode($scope.zTree.getNodeByParam("id", "000"), true);

    angular.forEach($scope.showCities, function (item) {
      //回显之前选中的节点
      var nodes = $scope.zTree.getNodesByParam("id", item.id, null);
      var parent = nodes[0].getParentNode();
      $scope.zTree.expandNode(parent, true);
      $scope.zTree.checkNode(nodes[0], true, true);
    })

  };

  $scope.saveCity = function () {
    $scope.showCities = [];
    $scope.selectedCities = $scope.zTree.getCheckedNodes();
    console.log($scope.selectedCities);
    angular.forEach($scope.selectedCities, function (item) {
      if (item.pId !== "000" && item.id != "000") {
        $scope.showCities.push(item)
      }
    });
  };

  $scope.removeArea = function (index) {
    $scope.showCities.splice(index, 1);
  };

  $scope.test = function(signature) {
    if(signature=='' || signature === undefined || signature === 'undefined') {
        $scope.sign="";
    } else {
        $scope.sign=" ";
    }

  }
  
  $scope.$watch('signature',function(){
      $scope.checkContent();
  },true)
  
  //212企管平台彩印签名长度优化需求
  $scope.checkContent = function () {
        $scope.contentVali = true;
        if ($scope.pxContent != null && $scope.pxContent != '') {
            var sign = 0;
            if ($scope.signature != null && $scope.signature != '') {
                sign = $scope.signature.length + 2;
            }
            var content = $scope.pxContent.length;
            var str_len = sign + content;
            if (str_len > 70) {
            	$scope.contentVali = false;
            } else {
                $scope.contentVali = true;
            }
        } 
        if (!$scope.contentVali) {
            $scope.contentDesc = "SIGNATURE_SCREEN_CONTENTDESC";
        } else {
            $scope.contentDesc = "";
        }
    };

  $scope.sensitiveCheck = function (content, type) {
    if (!content) {
      switch (type) {
        case "pxContent":
          $scope.screen_sensitive = "";
          break;
        case "contentTitle":
          $scope.title_sensitive = "";
          break;
        case "MMSContent":
          $scope.MMS_sensitive = "";
          break;
        default:
          break;
      }
      return;
    }
    if (type === "MMSContent") {
      $scope.MMSContent_temp = angular.copy($scope.MMSContent);
    }
    content = content.replace(/\s/g, '');
    var req = {
      "content": content || ''
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
      async: false,
      data: JSON.stringify(req),
      success: function (result) {
        var data = result.result;
        if (data.resultCode == '1030120017') {
          $scope.sensitiveWords = result.sensitiveWords.join('、');
          switch (type) {
            case "pxContent":
              $scope.screen_sensitive = $scope.sensitiveWords;
              break;
            case "contentTitle":
              $scope.title_sensitive = $scope.sensitiveWords;
              break;
            case "MMSContent":
              $scope.MMS_sensitive = $scope.sensitiveWords;
              break;
            default:
              break;
          }
        } else if (data.resultCode == '**********') {
          switch (type) {
            case "pxContent":
              $scope.screen_sensitive = "";
              break;
            case "contentTitle":
              $scope.title_sensitive = "";
              break;
            case "MMSContent":
              $scope.MMS_sensitive = "";
              break;
            default:
              break;
          }

        } else {
          $scope.tip = data.resultCode;
          $('#myModal').modal();
        }
      },
      error: function () {
        $scope.tip = '**********';
        $('#myModal').modal();
      }
    });
  };
  $scope.submitActivity = function () {
    $scope.activity_cityList = [];
    var city = {};
    angular.forEach($scope.showCities, function (item) {
      city = {
        "cityID": item.id,
        "cityName": item.name,
        "provinceID": item.pId.substr(0, item.pId.length - 1)
      };
      $scope.activity_cityList.push(city)
    });

    if ($scope.spokePushNum === "" && $scope.spokeDayNum === "") {
      $scope.notify = ""
    } else {
      $scope.notify = $scope.rewardNotify;
    }
    var req = {
      "activityInfo": {
        "enterpriseID": $scope.eId,
        "activityName": $scope.activityName,
        "effectivetime": $scope.effectivetime,
        "expiretime": $scope.expiretime,
        "cityList": $scope.activity_cityList,
        "activityRuleDesc": $scope.activityRuleDesc,
        "contentList": [
          {
            "thirdpartyType": 0,
            "servType": 3,
            "subServType": 2,
            "content": $scope.pxContent,
            "chargeType": 1,
            "approveStatus": 2,
            "enterpriseID": $scope.eId,
            "signature":$scope.signature,
            "platforms":$scope.platforms,
            "industryType":$scope.selectedIndustryID,
            "businessLicenseURL":$scope.businessLicenseURL_,
            "contentType": 1
          }
        ],
        "bannerList": $scope.bannerList,
        "template": {
          "templateID": $scope.templateID,
          "templateIconURL": $scope.templateIconURL
        },
        "rewardCond": {
          "spokePushNum": $scope.spokePushNum,
          "spokeDayNum": $scope.spokeDayNum
        },
        "maxPushNum": $scope.maxPushNum || 0,
        "totalNum": $scope.totalNum || 0,
        "rewardNotify": $scope.notify,
        "ecpmReserveds": {
          "reserved1": $scope.enterpriseName,
          "reserved3": $scope.agentID
        },
        "operatorID": $scope.accountID
      }
    };

    if ($scope.contentTitle && $scope.MMSPicture && $scope.MMSContent) {
      var MMSPictures = []
      for(var i in $scope.MMSPicture){
          MMSPictures.push({
              "frameNo": i,
              "frameType": 1,
              "framePicUrl": $scope.MMSPicture[i]
          })
      }
        MMSPictures.push({
            "frameNo": $scope.MMSPicture.length,
            "frameType": 2,
            "frameTxt": $scope.MMSContent
        })
      var MMSItem = {
        "thirdpartyType": 0,
        "servType": 3,
        "subServType": 8,
        "chargeType": 1,
        "approveStatus": 2,
        "enterpriseID": $scope.eId,
        "contentType": 1,
        "contentTitle": $scope.contentTitle,
        "signature":$scope.signature,
        "platforms":$scope.platforms,
        "industryType":$scope.selectedIndustryID,
        "businessLicenseURL":$scope.businessLicenseURL_,
        "contentFrameMappingList": MMSPictures
      };
      req.activityInfo.contentList.push(MMSItem)
    }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/activityService/submitActivity",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;

          if (result.resultCode == '**********') {
            $scope.isSubmit = true;
            $scope.tip = "COMMON_SUBMIT_SUCCESS";
            $('#myModal').modal();
          }
          else if (result.resultCode == '**********') {
              $scope.tip = "当前企业信控关闭，无法新增内容";
              $('#myModal').modal();
          }
          else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function (data) {
              $scope.tip = data.result.resultCode;
              $('#myModal').modal();
            }
        )
      }
    });
  }

  $scope.goBack = function () {
    window.location.href = "../list/advertisePrint_list.html"
  }

    //初始化营业执照上传容器
    $scope.initBusinessURLContainer = function(){
        //校验营业执照
        $scope.businessLicenseURLError=false;
        //初始化营业执照上传容器
        $scope.filePicker_ = "filePicker2";
        $scope.accepttype_ = "jpg,jpeg,png";
        $scope.isValidate_ = false;
        $scope.filesize_ = 20;
        $scope.mimetypes_ = ".jpg,.jpeg,.png";
        $scope.isCreateThumbnail_ = true;
        $scope.uploadurl_ ='/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.uploadDesc_ = "仅支持一张图片，仅支持jpg，jpeg，png格式";
        $scope.numlimit_ = 1;
        $scope.urlList_ = [];
        $scope.uploadParam_ = {
          enterpriseId: $scope.enterpriseID ||'',
          fileUse: 'businessLicense'
        };

        $scope.$on("uploadifyid_1",function(event,fileUrl_){
            if(fileUrl_){
                $scope.urlList_ = [fileUrl_];
                $scope.urlList_2 = [fileUrl_];
            }else{
                $scope.urlList_ = [];
                $scope.urlList_2 = [];
            }
            $scope.businessLicenseURL_ = fileUrl_;
            $scope.businessLicenseURLError = $scope.checkBusinessLicenseURL();
        });
    }

  //查询企业服务开关
  $scope.queryPlatformStatus = function(){
    var queryServiceControlReq = {
        "enterpriseID": $scope.eId
    }
    console.log($scope.eId);

    $scope.signatureRequired = '0';
    $scope.signatureError=false;
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryServiceControl",
      data: JSON.stringify(queryServiceControlReq),
      success: function (result) {
          $rootScope.$apply(function () {
              $scope.platformStatus = result.platformStatus;
              //if($scope.operateType == 'add'){
            var platformStatus = $scope.platformStatus;
              //查询企业免签名配置
            $scope.checkEnterpriseWithoutSignAdd(platformStatus);
          })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "**********";
              $('#myModal').modal();
        })
      }
    });
  }

  $scope.checkEnterpriseWithoutSignAdd = function (platformStatus){
    var req = {
      "enterpriseID": $scope.enterpriseID
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/enterpriseWithoutSignService/queryEnterpriseWithoutSign",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          if(result.result.resultCode == '**********'){
            $scope.enterpriseWithoutSignListData = result.enterpriseWithoutSignList || [];
            if($scope.enterpriseWithoutSignListData.length === 0){
              //沒有配置过免签名,按原来的流程走
              $scope.platformInitAdd(platformStatus);
            }else{
              //签名不用必填
              $scope.platformInitAddNoSign(platformStatus);
            }
          }else{
            $scope.tip=result.result.resultCode;
            $('#myModal').modal();
          }
        })

      },
      error:function(){
        $rootScope.$apply(function(data){
              $scope.tip="**********";
              $('#myModal').modal();
            }
        )
      }
    })
  }


  $scope.platformInitAdd = function (platformStatus) {
    $scope.platforms = platformStatus;
    //初始化signature必填状态
    $scope.noSign = '1';
    $scope.signatureRequired='0';
    if(platformStatus.charAt(1)=='1'||platformStatus.charAt(2)=='1'){
      $scope.signatureRequired='1';
    }
    for(var i = 0;i<3;i++){
      if(platformStatus.charAt(i)=='0'){
        $(".platforms .check-li").eq(i).css({cursor:'not-allowed',color:'gray'});
      }else{
        //初始化勾选状态
        $(".platforms .check-li").find('span').eq(i).addClass('checked')
        //绑定点击事件
        $('.platforms .check-li').eq(i).on('click', function () {
          if ($(this).find('span').hasClass('checked')&&
              ($(this).siblings().eq(0).find('span').hasClass('checked')||$(this).siblings().eq(1).find('span').hasClass('checked'))) {

            $(this).find('span').removeClass('checked');
          } else {
            $(this).find('span').addClass('checked')
          }
          var _platforms = '';
          for(var i = 0;i<3;i++){
            if($(".platforms .check-li").find('span').eq(i).hasClass('checked')){
              _platforms+='1';
            }else{
              _platforms+='0';
            }
          }
          if(_platforms.charAt(1)=='1'||_platforms.charAt(2)=='1'){
            $scope.signatureRequired='1';
          }else{
            $scope.signatureRequired='0';
          }
          $scope.platforms = _platforms;
          $scope.signatureError = $scope.checkSignature();
        });
      }
    }
    $scope.signatureError = $scope.checkSignature();
  }


  $scope.platformInitAddNoSign = function (platformStatus) {
    $scope.platforms = platformStatus;
    //初始化signature必填状态
    $scope.signatureRequired='0';
    if(platformStatus.charAt(1)=='1'||platformStatus.charAt(2)=='1'){
      $scope.signatureRequired='1';
    }
    for(var i = 0;i<3;i++){
      if(platformStatus.charAt(i)=='0'){
        $(".platforms .check-li").eq(i).css({cursor:'not-allowed',color:'gray'});
      }else{
        //初始化勾选状态
        $(".platforms .check-li").find('span').eq(i).addClass('checked')
        //绑定点击事件
        $('.platforms .check-li').eq(i).on('click', function () {
          if ($(this).find('span').hasClass('checked')&&
              ($(this).siblings().eq(0).find('span').hasClass('checked')||$(this).siblings().eq(1).find('span').hasClass('checked'))) {

            $(this).find('span').removeClass('checked');
          } else {
            $(this).find('span').addClass('checked')
          }
          var _platforms = '';
          for(var i = 0;i<3;i++){
            if($(".platforms .check-li").find('span').eq(i).hasClass('checked')){
              _platforms+='1';
            }else{
              _platforms+='0';
            }
          }
          if(_platforms.charAt(1)=='1'||_platforms.charAt(2)=='1'){
            $scope.signatureRequired='1';
          }else{
            $scope.signatureRequired='0';
          }
          $scope.platforms = _platforms;
          $scope.signatureError = $scope.checkSignature();
        });
      }
    }
    $scope.signatureError = $scope.checkSignature();
  }


  $scope.checkSignatureRequired = function(){
     $scope.signatureRequired = $scope.signatureRequired;
  }

  // 勾选异网运营商，签名必填false校验通过
  $scope.verifySignature = function(){
        $scope.signatureError = $scope.checkSignature();
  }

  $scope.checkSignature = function () {
      var platforms = $scope.platforms;
      //未勾选异网
      if(platforms.charAt(1)=='0'&&platforms.charAt(2)=='0'){
          return false;
      }
      //签名必填
      var obj = $scope.signature;
      if (obj == "undefined" || obj == null || obj == "") {
         return true;
      }else if(obj.length>67){
         $scope.signatureErrorInfo = "SIGNATURE_MAXLENTH_67";
         return true;
      }
      return false;
  }

  /* 所属行业为敏感行业，营业执照必填false校验通过 added by qwx806621 2019-9-5*/
  $scope.checkBusinessLicenseURL = function () {
      //所属行业必填
      if ($scope.isSensitiveIndustry != '1'&&$scope.isSensitiveIndustry != '3')
      {
         return false;
      }
      // 敏感行业营业执照必填
      if ($scope.businessLicenseURL_ == "undefined"||$scope.businessLicenseURL_ == null || $scope.businessLicenseURL_ == "") {
         return true;
      }
      return false;
  }


}]);
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}]);

app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
    }
    return "";
  }
})
app.filter("imgfilter", function (CommonUtils) {
  return function (path) {
    return CommonUtils.formatPic(path).review;
  }
})