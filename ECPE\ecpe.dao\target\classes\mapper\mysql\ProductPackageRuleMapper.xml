<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ProductPackageRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ProductPackageRule">
        <id column="ID" property="id" />
        <result column="packageID" property="packageID" />
        <result column="productID" property="productID" />
        <result column="createTime" property="createTime" />
        <result column="reserved1" property="reserved1" />
        <result column="reserved2" property="reserved2" />
        <result column="reserved3" property="reserved3" />
        <result column="reserved4" property="reserved4" />
        <result column="reserved5" property="reserved5" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, packageID, productID, createTime, reserved1, reserved2, reserved3, reserved4, reserved5
    </sql>

    <select id="queryProductByPackageID" parameterType="java.lang.Integer"
            resultType="com.huawei.jaguar.dsdp.ecpe.dao.domain.ProductInfoWrapper">
        SELECT
        t2.ID as productID,
        t2.productCode,
        t2.productName,
        t2.productDesc,
        t2.productType,
        t2.unitPrice,
        t2.currency,
        t2.productStatus,
        t2.isExperience,
        t2.servType,
        t2.subServType,
        t2.isLimit,
        t2.chargeType,
        t2.amount,
        t2.memberCount,
        t2.maxAmountPerPerson,
        t2.effictiveTime,
        t2.expireTime,
        t2.displayNo,
        t2.isUse,
        t2.createTime,
        t2.operatorID,
        t2.lastUpdateTime,
        t2.reserved1,
        t2.reserved2,
        t2.reserved3,
        t2.reserved4,
        t2.reserved5,
        t2.reserved6,
        t2.reserved7,
        t2.reserved8,
        t2.reserved9,
        t2.reserved10
        FROM
        ecpe_t_product t2
        LEFT JOIN ecpe_t_product_package_rule t1
        on t1.productID = t2.ID
        where t1.packageID = #{packageID}
    </select>

</mapper>