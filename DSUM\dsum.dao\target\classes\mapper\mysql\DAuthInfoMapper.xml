<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.DAuthInfoMapper">
	<resultMap id="dAuthInfoModel" type="com.huawei.jaguar.dsum.dao.domain.DAuthInfoWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="authCode" column="authCode" javaType="java.lang.String" />
		<result property="authName" column="authName" javaType="java.lang.String" />
		<result property="authDesc" column="authDesc" javaType="java.lang.String" />
		<result property="sourceType" column="sourceType" javaType="java.lang.Integer" />
		<result property="tableName" column="tableName" javaType="java.lang.String" />
		<result property="fieldName" column="fieldName" javaType="java.lang.String" />
		<result property="fieldVal" column="fieldVal" javaType="java.lang.String" />
		<result property="parentAuthID" column="parentAuthID" javaType="java.lang.Integer" />
		<result property="operateTime" column="operateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>

<sql id="dauthColumn">
        id,
		authCode,
		authName,
		authDesc,
		sourceType,
		tableName,
		fieldName,
		fieldVal,
		parentAuthID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
    </sql>
	<select id="getDAuthInfoByID" resultMap="dAuthInfoModel">
			select
				id,
				authCode,
				authName,
				authDesc,
				sourceType,
				tableName,
				fieldName,
				fieldVal,
				parentAuthID,
				operateTime,
				operatorID,
				extInfo,
				reserved1,
				reserved2,
				reserved3,
				reserved4,
				reserved5,
				reserved6,
				reserved7,
				reserved8,
				reserved9,
				reserved10 from
				dsum_t_dauth where id = #{id}
    </select>

	<select id="getDAuthInfoByFieldName" resultMap="dAuthInfoModel">
		select
		id,
		authCode,
		authName,
		authDesc,
		sourceType,
		tableName,
		fieldName,
		fieldVal,
		parentAuthID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10 from
		dsum_t_dauth where fieldName = #{fieldName}
	</select>
    
    <select id="getDAuthInfo" resultMap="dAuthInfoModel">
			select
				id,
				authCode,
				authName,
				authDesc,
				sourceType,
				tableName,
				fieldName,
				fieldVal,
				parentAuthID,
				operateTime,
				operatorID,
				extInfo,
				reserved1,
				reserved2,
				reserved3,
				reserved4,
				reserved5,
				reserved6,
				reserved7,
				reserved8,
				reserved9,
				reserved10 from
				dsum_t_dauth where id in
			<foreach item="ids" index="index" collection="list"
				open="(" separator="," close=")">
				#{ids}
			</foreach>
    </select>

    
     <select id="getDAuthInfoListByRoleIDs" resultType="com.huawei.jaguar.dsum.dao.domain.RoleDAuthInfoRelWrapper">
			select
			    t.roleID,
				t1.id,
				t1.authCode,
				t1.authName,
				t1.authDesc,
				t1.sourceType,
				t1.tableName,
				t1.fieldName,
				t1.fieldVal,
				t1.parentAuthID,
				t1.operateTime,
				t1.operatorID			
				from
				dsum_t_role_auth t,dsum_t_dauth t1
				where t.authID = t1.ID
                and t.authType in (2,4) and
                t.roleID in
			   <foreach item="roleIDs" index="index" collection="list"
				open="(" separator="," close=")">
				#{roleIDs}
			   </foreach>
    </select>

	<select id="getEnterpriseDAuthInfoListByRoleIDs" resultType="com.huawei.jaguar.dsum.dao.domain.RoleDAuthInfoRelWrapper">
		select
		t.roleID,
		t1.id,
		t1.authCode,
		t1.authName,
		t1.authDesc,
		t1.sourceType,
		t1.tableName,
		t1.fieldName,
		t1.fieldVal,
		t1.parentAuthID,
		t1.operateTime,
		t1.operatorID
		from
		dsum_t_role_auth t,dsum_t_dauth t1
		where t.authID = t1.ID
		and t.authType = 3 and
		t.roleID in
		<foreach item="roleIDs" index="index" collection="list"
				 open="(" separator="," close=")">
			#{roleIDs}
		</foreach>
	</select>

    <select id="queryDAuthInfo" resultMap="dAuthInfoModel">
	    SELECT
		 <include refid="dauthColumn"/>
		FROM dsum_t_dauth e WHERE 1=1
		           <if test="authName!=null and authName!=''">and authName like concat("%", #{authName}, "%")</if>
		<if test="authCategory==null and (dAuthType=='' or dAuthType == 1)">and fieldName != 'countyID'</if>
		<if test="authCategory==2 and dAuthType == 2">and fieldName='countyID' and parentAuthID in
			<foreach item="cityIDs" index="index" collection="cityIDs"
					 open="(" separator="," close=")">
				#{cityIDs}
			</foreach>
		</if>
		<if test="authType == 3"> and tableName="dsum_t_enterprise" and fieldName="reserved10"</if>

		order by parentAuthID
	</select>
	
	<select id="queryDAuthInfoIDByID" resultType="java.lang.Integer">
			select id from dsum_t_dauth 
			where id in
			<foreach item="ids" index="index" collection="list"
				open="(" separator="," close=")">
				#{ids}
			 </foreach>
    </select>

</mapper>