<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.AccountPwdMapper">

	<resultMap id="accountPasswordInfoMap"
		type="com.huawei.jaguar.dsum.dao.domain.AccountPasswordWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="accountID" column="accountID" javaType="java.lang.Integer" />
		<result property="password" column="password" javaType="java.lang.String" />
		<result property="salt" column="salt" javaType="java.lang.String" />
		<result property="algorithm" column="algorithm" javaType="java.lang.String" />
		<result property="smsValidCode" column="smsValidCode" javaType="java.lang.String" />
		<result property="smsValidCodeExpireTime" column="smsValidCodeExpireTime"
			javaType="java.util.Date" />
		<result property="pwdChkTime" column="pwdChkTime" javaType="java.util.Date" />
		<result property="pwdChkFailTimes" column="pwdChkFailTimes"
			javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="lastUpdateTime" column="lastupdatetime"
			javaType="java.util.Date" />
		<result property="extinfo" column="extinfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

	<!-- 插入用户密码信息 -->
	<insert id="insertPassword">
		INSERT INTO dsum_t_accountpwd
		(id,
		accountID,
		password,
		salt,
		algorithm,
		smsValidCode,
		smsValidCodeExpireTime,
		pwdChkTime,
		pwdChkFailTimes,
		createTime,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		VALUES
		(
		nextval('dsum_sequence_account_pwd'),
		#{accountID},
		#{password},
		#{salt},
		#{algorithm},
		#{smsValidCode},
		#{smsValidCodeExpireTime},
		#{pwdChkTime},
		#{pwdChkFailTimes},
		#{createTime},
		#{lastUpdateTime},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10}
		)
	</insert>

	<!-- 根据用户ID查询用户密码信息 -->
	<select id="getAccountPasswordByAccountID" resultMap="accountPasswordInfoMap">
		select id,
		accountID,
		password,
		salt,
		algorithm,
		smsValidCode,
		smsValidCodeExpireTime,
		pwdChkTime,
		pwdChkFailTimes,
		createTime,
		lastupdatetime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from dsum_t_accountpwd
		where
		accountID = #{accountID}
	</select>

	<!-- 根据用户ID更新用户密码信息 -->
	<update id="updatePasswordByAccountID">
		update dsum_t_accountpwd
		set
		<if test="password!=null and password!=''">
			password= #{password},
		</if>
		<if test="salt!=null and salt!=''">
			salt= #{salt},
		</if>
		<if test="algorithm!=null and algorithm!=''">
			algorithm= #{algorithm},
		</if>
		<if test="smsValidCode!=null and smsValidCode!=''">
			smsValidCode= #{smsValidCode},
		</if>
		<if test="smsValidCodeExpireTime!=null">
			smsValidCodeExpireTime= #{smsValidCodeExpireTime},
		</if>
		<if test="lastUpdateTime!=null">
			lastupdatetime= #{lastUpdateTime},
		</if>
		accountID=#{accountID}
		where
		accountID=#{accountID}
	</update>

	<!-- 根据用户ID更新用户验证码信息，保证验证码只能使用一次 -->
	<update id="emptySmsValidCodeByAccountID">
		update dsum_t_accountpwd
		set
		<if test="lastUpdateTime!=null">
			lastupdatetime= #{lastUpdateTime},
		</if>
		smsValidCode='',smsValidCodeExpireTime=null
		where accountID=#{accountID}
	</update>

	<delete id="batchDeleteAccountPwdByIDs" parameterType="java.util.List">
		delete from dsum_t_accountpwd where accountID in
		<foreach item="accountID" index="index" collection="list"
			open="(" separator="," close=")">
			#{accountID}
		</foreach>
	</delete>
	
	<!-- 根据用户ID更新用户验证码校验次数及校验时间信息，用于账号锁定控制 -->
    <update id="clearPwdCheckInfo">
        update dsum_t_accountpwd
        set
        pwdChkFailTimes='0',pwdChkTime=null
        where accountID=#{accountID}
    </update>
    
    <!-- 根据用户ID更新用户验证码校验次数及校验时间信息，用于账号锁定控制 -->
    <update id="updatePwdCheckInfo">
        update dsum_t_accountpwd
            set accountID = #{accountID}
            <if test = "flag==1">
                ,pwdChkFailTimes = 1
                ,pwdChkTime = #{pwdChkTime}
            </if>
            <if test = "flag==2">
                ,pwdChkFailTimes = pwdChkFailTimes + 1
            </if>
        where accountID = #{accountID}
    </update>
</mapper>