.webuploader-container {
  border-radius: 4px;
  height: 35px;
  line-height: 35px;
}
.webuploader-element-invisible {
  position: absolute !important;
  clip: rect(1px 1px 1px 1px); /* IE6, IE7 */
}
.webuploader-pick {
  display: inline;
  position: relative;
  cursor: pointer;
  /* padding: 10px 15px;
  color: #fff; */
  border-radius: 3px;
  overflow: hidden;
  text-align: center;
  padding: 10px 12px;
  height: 100%;
}

.webuploader-pick-disable {
  opacity: 0.6;
  pointer-events:none;
}
.uploader-demo .thumbnail {
  width: 110px;
  height: 110px;
}

.uploader-demo .thumbnail img {
  width: 100%;
}

/***************************************************************/
.uploader-list {
  width: 100%;
  overflow: hidden;
}

.file-item {
  float: left;
  position: relative;
  margin: 0 20px 10px 0;
  padding: 4px;
}

.file-item .error {
  position: absolute;
  top: 4px;
  left: 4px;
  right: 4px;
  background: red;
  color: white;
  text-align: center;
  height: 20px;
  font-size: 14px;
  line-height: 23px;
}

.file-item .info {
  position: absolute;
  left: 4px;
  bottom: 4px;
  right: 4px;
  height: 20px;
  line-height: 20px;
  text-indent: 5px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 12px;
  z-index: 10;
}

.uplodify-del{
  background: url("/qycy/ecpmp/assets/images/tableEditIcons18.png");
  width: 18px;
  height: 18px;
  display: inline-block;
  background-position: -215px,0;
  vertical-align: middle;
  cursor: pointer;
}

.name-container{
  margin-right: 12px;
}

.upload-state-done:after {
  content: "\f00c";
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  position: absolute;
  bottom: 0;
  right: 4px;
  color: #4cae4c;
  z-index: 99;
}

.upload-state-done-file:after {
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 32px;
  position: absolute;
  bottom: 0;
  right: 4px;
  color: #4cae4c;
  z-index: 99;
}


/* 自定义上传样式  用于上传图片可删除   */
.uploader-demo .uploader-list div.file-panel {
  position: absolute;
  height: 0;
  filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, startColorstr='#80000000', endColorstr='#80000000') \0;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  z-index: 300;
}

.uploader-demo .uploader-list div.file-panel span {
  width: 24px;
  height: 24px;
  display: inline;
  float: right;
  text-indent: -9999px;
  overflow: hidden;
  background: url(/qycy/ecpmp/assets/images/icons.png) no-repeat;
  margin: 5px 1px 1px;
  cursor: pointer;
}

.uploader-demo .uploader-list div.file-panel span.rotateLeft {
  background-position: 0 -24px;
}

.uploader-demo .uploader-list div.file-panel span.rotateLeft:hover {
  background-position: 0 0;
}

.uploader-demo .uploader-list div.file-panel span.rotateRight {
  background-position: -24px -24px;
}

.uploader-demo .uploader-list div.file-panel span.rotateRight:hover {
  background-position: -24px 0;
}

.uploader-demo .uploader-list div.file-panel span.cancel {
  background-position: -48px -24px;
}

.uploader-demo .uploader-list div.file-panel span.cancel:hover {
  background-position: -48px 0;
}

/* 自定义上传样式  用于上传图片可删除   */
.panel-body {
  display: inline-block;
  padding: 0 15px 0 0;
  vertical-align: bottom;
}
.uplodify-error-img{
  background-image: url(/qycy/ecpmp/assets/images/reject-icon.png);
  background-position: -219px,0;
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
}
