<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace ="com.huawei.jaguar.dsdp.ecpe.dao.mapper.HotlineMapper">
 
  <resultMap type="com.huawei.jaguar.dsdp.ecpe.dao.domain.HotlineWrapper"
		id="hotlineMapper">
	    <result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer"/>
		<result property="hotlineNo" column="hotlineNo" javaType="java.lang.String"/>
		<result property="hotlinePicUrl" column="hotlinePicUrl" javaType="java.lang.String"/>
		<result property="isUse" column="isUse" javaType="java.lang.Integer"/>
		<result property="createTime" column="createTime" javaType="java.util.Date"/>
		<result property="updateTime" column="updateTime" javaType="java.util.Date"/>
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer"/>
	</resultMap>
	
	<sql id="hotlineColumn">
        enterpriseID,
        hotlineNo,
        hotlinePicUrl,
        isUse,
        createTime,
        updateTime,
        operatorID
    </sql>
    
    <sql id="hotlineFullColumn">
        id,
        enterpriseID,
        hotlineNo,
        hotlinePicUrl,
        isUse,
        createTime,
        updateTime,
        operatorID
    </sql>
    
	
	<insert id="batchInsertHotline">
		INSERT INTO ecpe_t_hotline
		(
		  <include refid="hotlineFullColumn" />
		)
		VALUES
		<foreach collection="list" item="hotlineMapper"
			separator=",">
			(
			#{hotlineMapper.id},
			#{hotlineMapper.enterpriseID},
			#{hotlineMapper.hotlineNo},
			#{hotlineMapper.hotlinePicUrl},
			#{hotlineMapper.isUse},
			#{hotlineMapper.createTime},
			#{hotlineMapper.updateTime},
			#{hotlineMapper.operatorID}
			)
		</foreach>
	</insert>
	
	<update id="updateHotline">
		UPDATE ecpe_t_hotline SET
		<trim suffixOverrides="," suffix="where ID = #{id}">
            <if test="hotlineNo!=null and hotlineNo!=''">hotlineNo= #{hotlineNo},</if>
            hotlinePicUrl= #{hotlinePicUrl},
            <if test="updateTime!=null">updateTime= #{updateTime},</if>
            <if test="operatorID!=null">operatorID= #{operatorID}</if>
        </trim>
	</update>

	<select id="queryHotline" parameterType="java.lang.Integer" resultMap="hotlineMapper">
		SELECT 
		<include refid="hotlineFullColumn"></include>
		FROM ecpe_t_hotline t WHERE t.ID = #{id}
	</select>
	
	<delete id="batchDeleteHotlineByID" parameterType="java.util.List">
		DELETE FROM ecpe_t_hotline WHERE ID IN
		<foreach item="id" index="index" collection="list"
			open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
		<delete id="batchDeleteHotlineByNo" parameterType="java.util.List">
		DELETE FROM ecpe_t_hotline WHERE hotlineNo IN
		<foreach item="hotlineNo" index="index" collection="list"
			open="(" separator="," close=")">
			#{hotlineNo}
		</foreach>
	</delete>
	<delete id="deleteHotlineByNoAndEnterpriseID">
		DELETE FROM ecpe_t_hotline WHERE hotlineNo=#{hotlineNo} and enterpriseID=#{enterpriseID}
	</delete>


</mapper>
