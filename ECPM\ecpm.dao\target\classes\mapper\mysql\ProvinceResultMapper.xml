<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ProvinceResultMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceResultWrapper" >
        <id column="ID" property="id" jdbcType="INTEGER" />
        <result column="provinceID" property="provinceID" jdbcType="VARCHAR" />
        <result column="servType" property="servType" jdbcType="INTEGER" />
        <result column="subServType" property="subServType" jdbcType="INTEGER" />
        <result column="syncType" property="syncType" jdbcType="VARCHAR" />
        <result column="msisdn" property="msisdn" jdbcType="VARCHAR" />
        <result column="result" property="result" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ProvinceResultWrapper" >
        insert into ecpm_t_province_result 
        	(provinceID, 
        	servType, 
            subServType, 
            syncType, 
            msisdn, 
            result, 
            createTime, 
            updateTime
        <if test="approveStatus != null">
            ,approveStatus
        </if>
        <if test="approveTime != null">
            ,approveTime
        </if>
            )
        values 
        	(#{provinceID,jdbcType=VARCHAR}, 
        	#{servType,jdbcType=INTEGER}, 
            #{subServType,jdbcType=INTEGER}, 
            #{syncType,jdbcType=VARCHAR}, 
            #{msisdn,jdbcType=VARCHAR}, 
            #{result,jdbcType=VARCHAR}, 
            SYSDATE(),
			SYSDATE()
        <if test="approveStatus != null">
            ,#{approveStatus}
        </if>
        <if test="approveTime != null">
            ,#{approveTime}
        </if>
            )
    </insert>
</mapper>