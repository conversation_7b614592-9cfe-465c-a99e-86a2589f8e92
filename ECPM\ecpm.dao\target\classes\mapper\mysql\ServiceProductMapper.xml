<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ServiceProductMapper">

    <select id="querySPEffectiveByEnterpriseId"
            resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        SELECT *
        FROM ecpm_t_enterprise_service_product
        WHERE enterpriseID = #{enterpriseID}
          AND effectiveTime &lt;= NOW()
          AND expiryTime > NOW();
    </select>
    <select id="querySPEffectiveByEnterpriseIdAndStatus"
            resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        SELECT * FROM ecpm_t_enterprise_service_product
        WHERE enterpriseID = #{enterpriseID}
        AND effectiveTime &lt;= NOW()
        AND expiryTime > NOW()
        AND effStatus in
        <foreach item="status" index="index" collection="statusList"
                 open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>
    <insert id="addEnterpriseServiceProduct"
            parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        INSERT INTO ecpm_t_enterprise_service_product (enterpriseID, productID, productName, effStatus, effectiveTime,
                                                       expiryTime, createTime, updateTime)
        VALUES (#{enterpriseId}, #{productId}, #{productName}, #{effStatus}, #{effectiveTime}, #{expiryTime},
                #{createTime}, #{updateTime})
    </insert>

    <update id="updateSPEffStatus"
            parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        UPDATE ecpm_t_enterprise_service_product
        SET updateTime = now(),
            expiryTime = #{nextMonthFirstDay},
            effStatus  = CASE
                             WHEN effStatus = 2 AND effectiveTime >= #{nextMonthFirstDay} THEN 5
                             ELSE effStatus
                END
        WHERE enterpriseID = #{enterpriseId}
          AND effectiveTime &lt;= #{nextMonthFirstDay}
          AND expiryTime > #{nextMonthFirstDay}
    </update>
    <select id="querySuccessSPByEnterpriseId" parameterType="int"
            resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        SELECT *
        FROM ecpm_t_enterprise_service_product
        WHERE enterpriseID = #{enterpriseId}
          AND effStatus IN (1, 2, 3)
          AND expiryTime > NOW()
    </select>
    <select id="getPendingEnterpriseServiceProducts"
            resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        SELECT *
        FROM ecpm_t_enterprise_service_product
        WHERE effectiveTime &lt;= NOW()
          AND expiryTime > NOW()
          AND effStatus = 2
    </select>
    <update id="updateSPEffStatusByTask"
            parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        UPDATE ecpm_t_enterprise_service_product
        SET updateTime = now(),
            effStatus  = #{effStatus}
        <if test="retryCount != null">
            ,retryCount = #{retryCount}
        </if>
        WHERE enterpriseID = #{enterpriseId}
        AND ID = #{id}

    </update>

    <update id="updateServiceProduct"
            parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        UPDATE ecpm_t_enterprise_service_product
        SET updateTime = now(),
            productID = #{productId},
            productName = #{productName},
            expiryTime = #{expiryTime}
        WHERE enterpriseID = #{enterpriseId}
        AND ID = #{id}
    </update>

    <select id="queryNextEffectiveByEnterpriseId"
            resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        SELECT *
        FROM ecpm_t_enterprise_service_product
        WHERE enterpriseID = #{enterpriseID}
          AND effectiveTime &lt;= #{nextMonthFirstDay}
          AND effStatus = 2
          AND expiryTime > #{nextMonthFirstDay};
    </select>

    <update id="updateExpiryTimeByEnterpriseId"
            parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseServiceProductWrapper">
        UPDATE ecpm_t_enterprise_service_product
        SET updateTime = now(),
            expiryTime = #{expiryTime}
        WHERE enterpriseID = #{enterpriseId}
          AND ID = #{id}
    </update>


    <delete id="deleteFromByEnterpriseId">
        delete from ecpm_t_enterprise_service_product where enterpriseID = #{enterpriseId} AND ID = #{id}
    </delete>
</mapper>