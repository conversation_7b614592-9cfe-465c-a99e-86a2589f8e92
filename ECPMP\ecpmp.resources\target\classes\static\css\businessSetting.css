body {
    background: #f2f2f2;
}

.modal-footer {
    text-align: center;
}

.fontGreen {
    color: rgb(48, 147, 25)
}

.fontRed {
    color: rgb(252, 70, 93);
}

.form-horizontal .control-label {
    padding-top: 10px;
    padding-bottom: 15px;
    margin-bottom: 0;
    text-align: right;
}

.cooperation-head {
    padding: 20px;
}

.cooperation-head .frist-tab {
    font-size: 16px;
}

.cooperation-head .second-tab {
    font-size: 14px;
}

.cooperation-nav {
    margin-bottom: 15px;
    margin-left: 20px;
}

.cooperation-manage .form-inline {
    margin: 0 20px;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    height: 60px;
}


.cooperation-manage label {
    font-weight: normal;
}

/*.cooperation-manage .form-group {*/
    /*margin-right: 20px;*/
/*}*/

.form-control:focus {
    border-color: #7360e1;
}

.form-group label {
    height: 34px;
    margin-bottom: 0
}

.cooperation-tit {
    margin: 20px 25px;
}

.form-horizontal-tit {
    font-weight: bolder;
    font-size: 16px;
    margin-bottom: 15px;
    margin-left: 20px;
}
.form-fst {
    padding-top: 10px;
}
.switch-info{
    color: #aaa;
    margin-left: 30px;
}
.switch{
    width: 40px;
    height: 20px;
}
.switch .switch-icon{
    width: 18px;
    height: 18px;
}
.form-inline .form-control{
    width: 100%;
}
.offtip{
    height: 20px;
    display: inline-block;
    vertical-align: top;
    color: #999999;
    padding-left: 20px;
}
.bgWhite{
    background-color: white;
    border-radius: 4px;
    padding: 15px 0px 0px;
    overflow: hidden;
    margin-bottom: 15px;
}