var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('activityDetailListCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
  $scope.init = function () {
    $scope.token=$.cookie('token')||'';
    $scope.enterpriseID = $location.search().enterpriseID;
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 2,
        "totalCount": 20,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    $scope.activityName = "";
    $scope.queryAcitivityStatList();
  };
  $scope.goBack = function () {
    //传入objectID，作为唯一标识
    location.href = '../activityStatisticsList/activityStatisticsList.html';
  };

  $scope.queryAcitivityStatList = function (condition) {
    var req;
    if (condition != 'justPage') {
      req = {
        "activityStatCond": {
          "enterpriseID": $scope.enterpriseID,
          "activityName": $scope.activityName
        },
        "page": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
      $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/activityService/downActivityStatCsvFile?activityName=" +
          $scope.activityName + "&enterpriseID=" + $scope.enterpriseID
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      req = $scope.reqTemp;
      req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/activityService/queryActivityStatList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.activityStatList = result.activityStatList;
            console.log($scope.activityStatList);
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize));
            if ($scope.pageInfo[0].totalPage == 0) {
              $scope.pageInfo[0].totalPage = 1;
            }
          } else {
            $scope.activityStatList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })

      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
        )
      }
    });

  }

  $scope.exportList = function () {
      var req = {
        "param":{
        	"activityID":"",
			"processStatus":"",
			"enterpriseName":"",
			"agentEnterpriseName":"",
			"enterpriseType":"",
			"type":1,
			"activityName":$scope.activityName,
			"enterpriseID":$scope.enterpriseID,
			"token":$scope.token,
			"isExport":1
        },
        "url":"/qycy/ecpmp/ecpmpServices/activityService/downActivityStatCsvFile",
        "method":"get"
      }
      CommonUtils.exportFile(req);
}

}])
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])

app.filter('newDate', function () {
  return function (date) {
    var new_date = date.substr(0, 4) + "." + date.substr(4, 2) + "." + date.substr(6.2);
    return new_date;
  }
});