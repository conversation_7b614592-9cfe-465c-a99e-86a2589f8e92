<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ContNotifyMapper">
	<resultMap id="contNotifyWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContNotifyWrapper">
		<result property="contID" column="contID" javaType="java.lang.Long" />
		<result property="thirdPartyType" column="thirdPartyType"
			javaType="java.lang.Integer" />
		<result property="auditResult" column="auditResult" javaType="java.lang.Integer" />
		<result property="auditDesc" column="auditDesc" javaType="java.lang.String" />
		<result property="auditTime" column="auditTime" javaType="java.util.Date" />
		<result property="notifyStatus" column="notifyStatus" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseCode" column="enterpriseCode" javaType="java.lang.String" />
		<result property="notifyFailTimes" column="notifyFailTimes"
			javaType="java.lang.Integer" />
		<result property="lastNotifyTime" column="lastNotifyTime"
			javaType="java.util.Date" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="limitFailNum" column="limitFailNum" javaType="java.lang.Integer" />
		<result property="nowTime" column="nowTime" javaType="java.util.Date" />
		<result property="queryLimitNum" column="queryLimitNum"
			javaType="java.lang.Integer" />
		<result property="platform" column="platform" javaType="java.lang.Integer" />
		<result property="ywApproveCallback" column="ywApproveCallback" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="notifyType" column="notifyType" javaType="java.lang.Integer" />

	</resultMap>

	<select id="queryContNotifyList" resultMap="contNotifyWrapper">
		SELECT
		t.contID,t.thirdPartyType,t.auditResult,t.auditDesc,t.auditTime,t.notifyStatus,t.notifyFailTimes,t.enterpriseCode,t.enterpriseID,
		t.lastNotifyTime,t.createTime,t.updateTime, t.platform, c.ywApproveCallback,t.reserved1, t.notifyType from ecpm_t_cont_notify t
		JOIN ecpm_t_enterprise_simple s ON t.`enterpriseID` = s.`ID`
		LEFT JOIN ecpm_t_serv_control c ON c.`enterpriseID` = IF (s.`enterpriseType` = 3, s.`parentEnterpriseID`, s.`ID`)
		where (t.thirdpartyType = 1 and t.notifyStatus is null and t.auditResult is not null)
		or
		(t.thirdpartyType = 1 and t.notifyStatus = 1 and t.notifyFailTimes <![CDATA[ < ]]>
		#{limitFailNum} and
		date_add(t.lastnotifyTime, interval
		(pow(2,t.notifyFailTimes+1)-2) second) <![CDATA[ < ]]>
		#{nowTime} )
		order by t.updateTime desc
		limit #{queryLimitNum}
	</select>

	<update id="batchUpdateContNotify" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index" item="contNotifyWrapper"
			open="" separator=";">
			update ecpm_t_cont_notify 
			<trim prefix="set" suffixOverrides=",">
				<if test="contNotifyWrapper.notifyStatus != null">
					notifyStatus= #{contNotifyWrapper.notifyStatus},
				</if>
				<if test="contNotifyWrapper.notifyFailTimes != null">
					notifyFailTimes= #{contNotifyWrapper.notifyFailTimes},
				</if>
				<if test="contNotifyWrapper.lastNotifyTime != null">
					lastNotifyTime= #{contNotifyWrapper.lastNotifyTime},
				</if>
				<if test="contNotifyWrapper.updateTime != null">
					updateTime= #{contNotifyWrapper.updateTime}
				</if>
			</trim>
			where contID =
			#{contNotifyWrapper.contID} and platform = #{contNotifyWrapper.platform}
		</foreach>
	</update>
	
	<select id="queryByContentId" resultMap="contNotifyWrapper">
		SELECT
		contID,thirdPartyType,auditResult,auditDesc,auditTime,notifyStatus,notifyFailTimes,enterpriseCode,enterpriseID,
		lastNotifyTime,createTime,updateTime,platform from ecpm_t_cont_notify where contID = #{contID} and platform = #{platform}
	</select>
	
	<update id="UpdateContNotify">
		update ecpm_t_cont_notify 
        set
        auditResult = #{auditResult},
        auditDesc = #{auditDesc},
        notifyStatus = null,
        auditTime = now(),
        updateTime = now()
		where contID = #{contID} and platform = #{platform}
	</update>
	
</mapper>