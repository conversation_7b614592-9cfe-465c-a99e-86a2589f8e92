<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EnterpriseMonthStatMapper">
	<resultMap id="enterpriseMonthStatWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseMonthStatWrapper">
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="enterpriseType" column="enterpriseType"
			javaType="java.lang.Integer" />
		<result property="parentEnterpriseID" column="parentEnterpriseID"
			javaType="java.lang.Integer" />
		<result property="provinceID" column="provinceID" javaType="java.lang.String" />
		<result property="cityID" column="cityID" javaType="java.lang.String" />
		<result property="statMonth" column="statMonth" javaType="java.lang.String" />
		<result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="chargeType" column="chargeType" javaType="java.lang.Integer" />
		<result property="memberCount" column="memberCount" javaType="java.lang.Integer" />
		<result property="deliveryMemberCount" column="deliveryMemberCount" 
			javaType="java.lang.Integer" />
		<result property="useCount" column="useCount" javaType="java.lang.Long" />
		<result property="experienceCount" column="experienceCount"
			javaType="java.lang.Long" />
		<result property="experienceStartTime" column="experienceStartTime" 
			javaType="java.util.Date" />
		<result property="experienceEndTime" column="experienceEndTime" 
			javaType="java.util.Date" />	
		<result property="experienceTotalCount" column="experienceTotalCount" 
			javaType="java.lang.Long" />
		<result property="experienceRemainCount" column="experienceRemainCount" 
			javaType="java.lang.Long" />
		<result property="ussdCount" column="ussdCount" javaType="java.lang.Long" />
		<result property="flashCount" column="flashCount" javaType="java.lang.Long" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="useCountMobile" column="useCountMobile" javaType="java.lang.Long" />
		<result property="useCountUnicom" column="useCountUnicom" javaType="java.lang.Long" />
		<result property="useCountTelecom" column="useCountTelecom" javaType="java.lang.Long" />
		<result property="enhancedDeliveryCount" column="enhancedDeliveryCount" javaType="java.lang.Long" />
		<result property="enterpriseCount" column="enterpriseCount" javaType="java.lang.Long" />
		<result property="subProvinceType" column="subProvinceType"/>
		<result property="hangupType" column="hangupType" javaType="java.lang.Integer" />
	</resultMap>
	
	<!-- 以企业为维度查询企业统计信息 -->
	<select id="queryEnterpriseMonthStat" resultMap="enterpriseMonthStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType_vr subServType,
		chargeType,
		experienceStartTime,
		experienceEndTime,
		subProvinceType,
		SUM(memberCount) memberCount,
		SUM(deliveryMemberCount) deliveryMemberCount,
		SUM(useCount) useCount,
		SUM(experienceCount) experienceCount,
		SUM(experienceTotalCount) experienceTotalCount,
		SUM(experienceRemainCount) experienceRemainCount,
		SUM(ussdCount) ussdCount,
		SUM(flashCount) flashCount,
		SUM(useCountMobile) useCountMobile,
		SUM(useCountUnicom) useCountUnicom,
		SUM(useCountTelecom) useCountTelecom,
		SUM(enhancedDeliveryCount) enhancedDeliveryCount,
		hangupType
		from ( select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType_vr,
		chargeType,
		experienceStartTime,
		experienceEndTime,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		enhancedDeliveryCount,
		subProvinceType,
		hangupType
		from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
				<if test="serviceType =='4' ">
					and (useCount>0 or enhancedDeliveryCount>0)
				</if>
				<if test="serviceType !='4' ">
					and (useCount>0 or memberCount>0)
				</if>
			</if>
			<if test="serviceType ==null ">
				and serviceType != '4'
				and (useCount>0 or memberCount>0)
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		) a
		GROUP BY 		
		statMonth,
		enterpriseID,
		serviceType,
		subServType_vr,
		chargeType,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		experienceStartTime,
		experienceEndTime,
		subProvinceType,
		hangupType
		order by statMonth desc,enterpriseID,serviceType,subServType,chargeType
		limit #{pageNum},#{pageSize}
	</select>

	<!-- 以企业为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseMonthStatCount" resultType="java.lang.Integer">
		SELECT
		count(0) FROM (
		SELECT subServType_vr FROM (
		SELECT
		subServType_vr,
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		chargeType,
		experienceStartTime,
		experienceEndTime,
		subProvinceType
		from ecpm_t_enterprise_month_stat FORCE INDEX(idx_ems_one)
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>

			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>

			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
				<if test="serviceType ==4 ">
					and (useCount>0 or enhancedDeliveryCount>0)
				</if>
				<if test="serviceType !=4 ">
					and (useCount>0 or memberCount>0)
				</if>
			</if>
			<if test="serviceType ==null ">
				and serviceType != '4'
				and (useCount>0 or memberCount>0)
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		) a
		GROUP BY statMonth,
		enterpriseID,
		serviceType,
		subServType_vr,
		chargeType,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		experienceStartTime,
		experienceEndTime,
		subProvinceType
		) b
	</select>
	
	<!-- 以省为维度查询企业统计信息 -->
	<select id="queryEnterpriseMonthStatByProvince" resultMap="enterpriseMonthStatWrapper">
		SELECT a.*,b.memberCount,b.enterpriseCount FROM ( select
		provinceID,
		statMonth,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		IF(SUM(IFNULL(experienceTotalCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceTotalCount,0))) AS experienceTotalCount,
		IF(SUM(IFNULL(experienceRemainCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceRemainCount,0))) AS experienceRemainCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by statMonth, serviceType, subServType, provinceID,enterpriseType,subProvinceType,hangupType
		order by statMonth desc ,serviceType desc,subServType desc,provinceID desc,enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize}  ) a left JOIN
		(SELECT s.statMonth,s.serviceType,s.provinceID,s.subProvinceType,SUM(s.memberCount) memberCount,COUNT(1) enterpriseCount
		FROM ecpm_t_enterprise_month_stat_service s
		WHERE 1 = 1
		<if test="startDate != null and endDate != null">
			and (s.statMonth <![CDATA[ >= ]]> #{startDate}
			and s.statMonth <![CDATA[ <= ]]> #{endDate})
		</if>
		<if test="startDate != null and endDate == null">
			and s.statMonth <![CDATA[ >= ]]> #{startDate}
		</if>
		<if test="startDate == null and endDate != null">
			and s.statMonth <![CDATA[ <= ]]> #{endDate}
		</if>
		<if test="subProvinceType !=null ">
			and s.subProvinceType=#{subProvinceType}
		</if>
		GROUP BY s.statMonth,s.serviceType,s.provinceID,s.subProvinceType) b
		on b.provinceID = a.provinceID and b.statMonth = a.statMonth and a.serviceType = b.serviceType and a.subProvinceType = b.subProvinceType
	</select>
	
	<!-- 以省为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseMonthStatCountByProvince" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	count(0)
		 from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>

			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>

		group by statMonth, serviceType, subServType, provinceID,enterpriseType,subProvinceType) t
	</select>
	
	<!-- 以市为维度查询企业统计信息 -->
	<select id="queryEnterpriseMonthStatByCity" resultMap="enterpriseMonthStatWrapper">
		SELECT a.*,b.memberCount,b.enterpriseCount FROM ( select
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		IF(SUM(IFNULL(experienceTotalCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceTotalCount,0))) AS experienceTotalCount,
		IF(SUM(IFNULL(experienceRemainCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceRemainCount,0))) AS experienceRemainCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>

			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by statMonth, serviceType, subServType, provinceID, cityID,enterpriseType,subProvinceType,hangupType
		order by statMonth desc,serviceType desc,subServType desc,provinceID desc, cityID desc,enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize}  ) a left JOIN
		(SELECT s.statMonth,s.serviceType,s.provinceID,s.cityID,s.subProvinceType,SUM(s.memberCount) memberCount,COUNT(1) 	   enterpriseCount
		FROM ecpm_t_enterprise_month_stat_service s
		WHERE 1 = 1
		<if test="startDate != null and endDate != null">
			and (s.statMonth <![CDATA[ >= ]]> #{startDate}
			and s.statMonth <![CDATA[ <= ]]> #{endDate})
		</if>
		<if test="startDate != null and endDate == null">
			and s.statMonth <![CDATA[ >= ]]> #{startDate}
		</if>
		<if test="startDate == null and endDate != null">
			and s.statMonth <![CDATA[ <= ]]> #{endDate}
		</if>
		<if test="subProvinceType !=null ">
			and s.subProvinceType=#{subProvinceType}
		</if>
		GROUP BY s.statMonth,s.serviceType,s.provinceID,s.cityID,s.subProvinceType) b
		on b.provinceID = a.provinceID and b.statMonth = a.statMonth and a.serviceType = b.serviceType
		and a.cityID = b.cityID and a.subProvinceType = b.subProvinceType
	</select>
	
	<!-- 以市为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseMonthStatCountByCity" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	count(0)
		 from ecpm_t_enterprise_month_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>

			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by statMonth, serviceType, subServType, provinceID, cityID,enterpriseType,subProvinceType) t
	</select>
	
	<select id="queryEnterpriseMonthStatByDate" resultMap="enterpriseMonthStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceStartTime,
		experienceEndTime,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		updateTime,
		status,
		useCountMobile,
		useCountUnicom,
		useCountTelecom
		from ecpm_t_enterprise_month_stat t1
		where 
		t1.enterpriseID=#{enterpriseID}
		and t1.serviceType=#{serviceType}
		and t1.subServType=#{subServType}
		and t1.chargeType=#{chargeType}
		and t1.statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
	</select>
	
	<update id="updateEnterpriseMonthCountStat">
		update ecpm_t_enterprise_month_stat
		set
		<trim suffixOverrides="," suffix="where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType} and chargeType=#{chargeType} and statMonth=#{statMonth}">
			<if test="deliveryMemberCount != null">
				deliveryMemberCount=#{deliveryMemberCount},
			</if>
			<if test="ussdCount != null">
				ussdCount=#{ussdCount},
			</if>
			<if test="flashCount != null">
				flashCount=#{flashCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="memberCount != null">
				memberCount=#{memberCount},
			</if>
			<if test="useCount != null">
				useCount=#{useCount},
			</if>
			<if test="experienceCount != null">
				experienceCount=#{experienceCount},
			</if>
			<if test="experienceStartTime != null">
				experienceStartTime=#{experienceStartTime},
			</if>
			<if test="experienceEndTime != null">
				experienceEndTime=#{experienceEndTime},
			</if>
			<if test="experienceTotalCount != null">
				experienceTotalCount=#{experienceTotalCount},
			</if>
			<if test="experienceRemainCount != null">
				experienceRemainCount=#{experienceRemainCount},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
			<if test="useCountMobile != null">
				useCountMobile=#{useCountMobile},
			</if>
			<if test="useCountUnicom != null">
				useCountUnicom=#{useCountUnicom},
			</if>
			<if test="useCountTelecom != null">
				useCountTelecom=#{useCountTelecom},
			</if>
			<if test="enhancedDeliveryCount != null">
				enhancedDeliveryCount=#{enhancedDeliveryCount},
			</if>
		</trim>
	</update>
	
	<insert id="insertEnterpriseMonthCountStat">
		insert into
		ecpm_t_enterprise_month_stat
		(
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceStartTime,
		experienceEndTime,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		updateTime,
		status,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		enhancedDeliveryCount,
		subProvinceType,
		useCountMobileMq,
		hangupType
		)
		values
		(
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseType},
		#{parentEnterpriseID},
		#{provinceID},
		#{cityID},
		#{statMonth},
		#{serviceType},
		#{subServType},
		#{chargeType},
		#{memberCount},
		#{deliveryMemberCount},
		#{useCount},
		#{experienceCount},
		#{experienceStartTime},
		#{experienceEndTime},
		#{experienceTotalCount},
		#{experienceRemainCount},
		#{ussdCount},
		#{flashCount},
		#{updateTime},
		#{status},
		#{useCountMobile},
		#{useCountUnicom},
		#{useCountTelecom},
		#{enhancedDeliveryCount},
		(SELECT CASE
					es.reserved10
					WHEN "112" THEN
						3
					WHEN "111" THEN
						2
					WHEN "113" THEN
						4
					ELSE 1 END FROM ecpm_t_enterprise_simple es WHERE id = #{enterpriseID} and es.enterpriseType = 5),
		#{useCountMobileMq},
		#{hangupType}
		)
	</insert>
	
	<update id="updateEnterpriseMonthCountStatForUseCount">
		update ecpm_t_enterprise_month_stat
		set
		<trim suffixOverrides="," suffix="where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType} and chargeType=#{chargeType} and statMonth=#{statMonth}">
			<if test="deliveryMemberCount != null">
				deliveryMemberCount=#{deliveryMemberCount},
			</if>
			<if test="ussdCount != null">
				ussdCount=#{ussdCount},
			</if>
			<if test="flashCount != null">
				flashCount=#{flashCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="memberCount != null">
				memberCount=#{memberCount},
			</if>
			<if test="useCount != null">
				useCount= useCount - #{useCount},
			</if>
			<if test="experienceCount != null">
				experienceCount=#{experienceCount},
			</if>
			<if test="experienceStartTime != null">
				experienceStartTime=#{experienceStartTime},
			</if>
			<if test="experienceEndTime != null">
				experienceEndTime=#{experienceEndTime},
			</if>
			<if test="experienceTotalCount != null">
				experienceTotalCount=#{experienceTotalCount},
			</if>
			<if test="experienceRemainCount != null">
				experienceRemainCount=#{experienceRemainCount},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
		</trim>
	</update>
	
		<!-- 更新企业日统计使用量 -->
	<update id="updateEnterpriseMonthStat">
		update ecpm_t_enterprise_month_stat
		set
		<if test="useCount != null">
			useCount=useCount + #{useCount},
		</if>
		<if test="updateTime != null">
			updateTime=#{updateTime}
		</if>
		where chargeType=#{chargeType}
		<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
			and enterpriseID in
			<foreach item="enterpriseID" index="index" collection="enterpriseIDList"
				open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		and serviceType=#{serviceType} and subServType=#{subServType} and
		statMonth=#{statMonth}
	</update>
	
	<select id="queryEnterpriseMonthStatForSum" resultMap="enterpriseMonthStatWrapper">
	    select enterpriseid,sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount ,sum(ifnull(useCount,0)) as useCount ,avg(ifnull(status,0)) status,
	    sum(ifnull(ussdCount,0)) as ussdCount,sum(ifnull(flashCount,0)) as flashCount,
	    sum(ifnull(experienceCount,0)) as experienceCount
	    from ecpm_t_enterprise_month_stat 
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType} 
        and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
		and statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
		group by enterpriseid 
	</select>
	
		<delete id="deleteEnterpriseMonthStatWrapper">
		delete from ecpm_t_enterprise_month_stat where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType} and statMonth=#{statMonth}
		and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
	</delete>
	
	<select id="queryEnterpriseMonthStatByDateForSp" resultMap="enterpriseMonthStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceStartTime,
		experienceEndTime,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		updateTime,
		status
		from ecpm_t_enterprise_month_stat t1
		where 
		t1.enterpriseID=#{enterpriseID}
		and t1.serviceType=#{serviceType}
		and t1.subServType=#{subServType}
		and t1.chargeType=#{chargeType}
		and t1.statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
	</select>
	
	<delete id="deleteEnterpriseMonthStatWrapperForBefore">
		delete from ecpm_t_enterprise_month_stat where statMonth=#{statMonth} and enterpriseType = #{enterpriseType}
	</delete>
	
	<select id="queryEnterpriseMonthStatForAgentSum" resultMap="enterpriseMonthStatWrapper">
	    select sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount ,sum(ifnull(useCount,0)) as useCount ,avg(ifnull(status,0)) status,
	    sum(ifnull(ussdCount,0)) as ussdCount,sum(ifnull(flashCount,0)) as flashCount,
	    sum(ifnull(experienceCount,0)) as experienceCount,IF(SUM(IFNULL(experienceRemainCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceRemainCount,0))) as experienceRemainCount
	    from ecpm_t_enterprise_month_stat 
	    where parentEnterpriseID = #{parentEnterpriseID} and serviceType=#{serviceType} and subServType =#{subServType}
	    and chargeType=#{chargeType}
	    and statMonth in 
		<foreach item="statMonth" index="index" collection="monthList" open="("
			separator="," close=")">
			#{statMonth}
		</foreach>
        group by parententerpriseid
	</select>
	
	<update id="updateEnterpriseName">
		update ecpm_t_enterprise_month_stat t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>
	
	<update id="updateEnterpriseMonthStatMemberCount">
		update ecpm_t_enterprise_month_stat
		set
		memberCount = #{memberCount}
		where enterpriseID=#{enterpriseID}
		and serviceType=#{serviceType} and statMonth=#{statMonth}
	</update>

	<select id="deleteStatServiceByDayStat">
		delete from ecpm_t_enterprise_month_stat_service where statMonth = #{statDate}
	</select>

	<insert id="insertStatServiceByDayStat">
		Insert into ecpm_t_enterprise_month_stat_service (enterpriseID,enterpriseName, enterpriseType,provinceID,cityID,statMonth,serviceType,memberCount,subProvinceType)
		SELECT DISTINCT
		enterpriseID,
		enterpriseName,
		enterpriseType,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		memberCount,
		(SELECT CASE
					es.reserved10
					WHEN "112" THEN
						3
					WHEN "111" THEN
						2
					WHEN "113" THEN
						4
					ELSE 1 END FROM ecpm_t_enterprise_simple es WHERE id = enterpriseID and es.enterpriseType = 5)
		FROM ecpm_t_enterprise_month_stat
		Where statMonth=#{statDate} and enterpriseType=5
	</insert>

	<insert id="insertAreaByMonthStat">
		insert into ecpm_t_enterprise_month_stat_area (enterpriseType,provinceID,cityID,countyID,statMonth,serviceType,subServType,
													   chargeType,deliveryMemberCount,useCount,experienceCount,experienceTotalCount, experienceRemainCount, ussdCount,flashCount,updateTime,useCountMobile,useCountUnicom,useCountTelecom,enhancedDeliveryCount,subProvinceType,useCountMobileMq,hangupType)
		SELECT d.enterpriseType, d.provinceID,d.cityID,s.countyID,
			   d.statMonth,
			   d.serviceType,
			   d.subServType_vr,
			   d.chargeType,
			   SUM(d.deliveryMemberCount) AS deliveryMemberCount,
			   SUM(d.useCount) AS useCount,
			   SUM(d.experienceCount) AS experienceCount,
			   IF(SUM(IFNULL(experienceTotalCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceTotalCount,0))) AS experienceTotalCount,
			   IF(SUM(IFNULL(experienceRemainCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceRemainCount,0))) AS experienceRemainCount,
			   SUM(d.ussdCount) AS ussdCount,
			   SUM(d.flashCount)	AS flashCount,
			   now(),
			   SUM(d.useCountMobile)	AS useCountMobile,
			   SUM(d.useCountUnicom)	AS useCountUnicom,
			   SUM(d.useCountTelecom) AS useCountTelecom,
			   SUM(d.enhancedDeliveryCount) AS enhancedDeliveryCount,
			   d.subProvinceType,
			   SUM(d.useCountMobileMq) AS useCountMobileMq,
			   hangupType
		FROM ecpm_t_enterprise_month_stat d FORCE INDEX(idx_emc_two), ecpm_t_enterprise_simple s
		WHERE d.statMonth = #{statMonth} AND d.enterpriseType = 5 AND d.enterpriseID = s.id
		GROUP BY d.provinceID,d.cityID, s.countyID,  d.enterpriseType,d.subProvinceType, d.statMonth, d.serviceType, d.subServType_vr, d.chargeType, d.hangupType;
	</insert>

	<insert id="insertCollectByMonthStat">
		INSERT INTO ecpm_t_enterprise_month_stat_collect (
			status,
			enterpriseID,
			enterpriseName,
			enterpriseType,
			parentEnterpriseID,
			provinceID,
			cityID,
			statMonth,
			serviceType,
			subServType,
			chargeType,
			memberCount,
			updateTime,
			deliveryMemberCount,
			useCount,
			experienceCount,
			experienceTotalCount,
			experienceRemainCount,
			useCountMobile,
			useCountUnicom,
			useCountTelecom,
			ussdCount,
			flashCount,
			enhancedDeliveryCount,
			subProvinceType,
			hangupType
		) SELECT * FROM (SELECT
			  1,
			  enterpriseID,
			  enterpriseName,
			  enterpriseType,
			  parentEnterpriseID,
			  provinceID,
			  cityID,
			  statMonth,
			  serviceType,
			  subServType_vr subServType,
			  chargeType,
			  memberCount,
			  now(),
			  SUM( deliveryMemberCount ) deliveryMemberCount,
			  SUM( useCount ) useCount,
			  SUM( experienceCount ) experienceCount,
			  SUM( experienceTotalCount ) experienceTotalCount,
			  SUM( experienceRemainCount ) experienceRemainCount,
			  SUM( useCountMobile ) useCountMobile,
			  SUM( useCountUnicom ) useCountUnicom,
			  SUM( useCountTelecom ) useCountTelecom,
			  SUM( ussdCount ) ussdCount,
			  SUM( flashCount ) flashCount,
			  SUM( enhancedDeliveryCount ) enhancedDeliveryCount,
			  (SELECT CASE
						  es.reserved10
						  WHEN "112" THEN
							  3
						  WHEN "111" THEN
							  2
						  WHEN "113" THEN
							  4
						  ELSE 1 END FROM ecpm_t_enterprise_simple es WHERE id = enterpriseID and es.enterpriseType = 5),
			  hangupType
		FROM
			ecpm_t_enterprise_month_stat
		WHERE
			statMonth = #{statMonth}
		GROUP BY
			statMonth,
			enterpriseID,
			serviceType,
			subServType_vr,
			chargeType,
			enterpriseName,
			enterpriseType,
			parentEnterpriseID,
			provinceID,
			cityID,
			memberCount,
			hangupType) a WHERE (a.serviceType = 4 AND (a.useCount>0 OR a.enhancedDeliveryCount>0)) OR (a.serviceType != 4 AND (a.useCount>0 OR a.memberCount>0));
	</insert>

	<insert id="insertServiceAreaByMonthStat">
		insert into ecpm_t_enterprise_month_stat_service_area(enterpriseType,provinceID,cityID,countyID,statMonth,serviceType,memberCount,subProvinceType,enterpriseCount, updateTime
		) SELECT t.`enterpriseType`,t.`provinceID`,t.`cityID`,s.countyID, t.statMonth,t.`serviceType`,
				 SUM(t.`memberCount`) memberCount, t.`subProvinceType`,
				 SUM(1) enterpriseCount,
				 now()
		FROM ecpm_t_enterprise_month_stat_service t,ecpm_t_enterprise_simple s
		WHERE t.statMonth = #{statMonth} AND t.enterpriseType = 5 AND t.enterpriseID = s.id
		GROUP BY t.provinceID,t.cityID, s.countyID,  t.enterpriseType,t.subProvinceType, t.statMonth, t.serviceType;
	</insert>

	<delete id="deleteAreaByMonthStat">
		delete from ecpm_t_enterprise_month_stat_area where statMonth = #{statMonth}
		<if test="endMonth != null">
			or statMonth <![CDATA[ <= ]]> #{endMonth}
		</if>
	</delete>

	<delete id="deleteCollectByMonthStat">
		delete from ecpm_t_enterprise_month_stat_collect where statMonth = #{statMonth}
		<if test="endMonth != null">
			or statMonth <![CDATA[ <= ]]> #{endMonth}
		</if>
	</delete>

	<delete id="deleteServiceAreaByMonthStat">
		delete from ecpm_t_enterprise_month_stat_service_area where statMonth = #{statMonth}
		<if test="endMonth != null">
			or statMonth <![CDATA[ <= ]]> #{endMonth}
		</if>
	</delete>

	<select id="queryEnterpriseMonthStatByProvinceForArea"  resultMap="enterpriseMonthStatWrapper">
		SELECT a.*,b.memberCount,b.enterpriseCount FROM ( select
		provinceID,
		statMonth,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		IF(SUM(IFNULL(experienceTotalCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceTotalCount,0))) AS experienceTotalCount,
		IF(SUM(IFNULL(experienceRemainCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceRemainCount,0))) AS experienceRemainCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_month_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statMonth, serviceType, subServType, provinceID,enterpriseType,subProvinceType,hangupType
		order by statMonth desc ,serviceType desc,subServType desc,provinceID desc,enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize}  ) a left JOIN
		(SELECT s.statMonth,s.serviceType,s.provinceID,s.subProvinceType,SUM(s.memberCount) memberCount,SUM(s.enterpriseCount) enterpriseCount
		FROM ecpm_t_enterprise_month_stat_service_area s
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		GROUP BY s.statMonth,s.serviceType,s.provinceID,s.subProvinceType) b
		on b.provinceID = a.provinceID and b.statMonth = a.statMonth and a.serviceType = b.serviceType and a.subProvinceType = b.subProvinceType
	</select>

	<select id="queryEnterpriseMonthStatCountByProvinceForArea" resultType="java.lang.Integer">
		select count(1) from (select
		statMonth
		from ecpm_t_enterprise_month_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statMonth, serviceType, subServType, provinceID,enterpriseType,subProvinceType,hangupType) a
	</select>

	<select id="queryEnterpriseMonthStatByCityForArea"  resultMap="enterpriseMonthStatWrapper">
		SELECT a.*,b.memberCount,b.enterpriseCount FROM ( select
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		IF(SUM(IFNULL(experienceTotalCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceTotalCount,0))) AS experienceTotalCount,
		IF(SUM(IFNULL(experienceRemainCount,0)) > 9223372036854775807, 9223372036854775807, SUM(IFNULL(experienceRemainCount,0))) AS experienceRemainCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_month_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statMonth, serviceType, subServType, provinceID,cityID, enterpriseType,subProvinceType,hangupType
		order by statMonth desc ,serviceType desc,subServType desc,provinceID desc,cityID desc, enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize}  ) a left JOIN
		(SELECT s.statMonth,s.serviceType,s.provinceID,s.cityID, s.subProvinceType,SUM(s.memberCount) memberCount,SUM(s.enterpriseCount) enterpriseCount
		FROM ecpm_t_enterprise_month_stat_service_area s
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		GROUP BY s.statMonth,s.serviceType,s.provinceID,s.cityID, s.subProvinceType) b
		on b.provinceID = a.provinceID and b.cityID = a.cityID and b.statMonth = a.statMonth and a.serviceType = b.serviceType and a.subProvinceType = b.subProvinceType
	</select>

	<select id="queryEnterpriseMonthStatCountByCityForArea" resultType="java.lang.Integer">
		select count(1) from (select
		statMonth
		from ecpm_t_enterprise_month_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statMonth, serviceType, subServType, provinceID,cityID, enterpriseType,subProvinceType,hangupType) a
	</select>

	<select id="queryEnterpriseMonthStatForArea" resultMap="enterpriseMonthStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statMonth,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		experienceTotalCount,
		experienceRemainCount,
		ussdCount,
		flashCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		enhancedDeliveryCount,
		subProvinceType,
		hangupType
		from ecpm_t_enterprise_month_stat_collect
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
						 open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and  e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="serviceType ==null ">
				and serviceType != '4'
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		order by statMonth desc,enterpriseID,serviceType,subServType,chargeType
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryEnterpriseMonthStatCountForArea" resultType="java.lang.Integer">
		SELECT
		count(0) FROM (
		SELECT subServType FROM
		ecpm_t_enterprise_month_stat_collect
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>

			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
						 open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>

			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="serviceType ==null ">
				and serviceType != '4'
			</if>
			<if test="startDate != null and endDate != null">
				and (statMonth <![CDATA[ >= ]]> #{startDate}
				and statMonth <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statMonth <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statMonth <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		) a
	</select>
</mapper>