/**
 * Created by cWX322788 on 2018/12/14.
 *
 * 上传组件
 * @param filepickerid   保证同一个页面中多个上传组件filepickerid唯一
 * @param filelistid   缩略图存放的地方 createthumbnail为true时才有用  保证同一个页面中多个上传组件filelistid唯一
 * @param namelistid   文件名存放的地方 createthumbnail为false时才有用 保证同一个页面中多个上传组件namelistid唯一
 * @param uploadifyid   保证同一个页面中多个上传组件uploadifyid唯一
 * @param accepttype   支持上传的的文件类型,逗号分隔 "jpg,png"
 * @param validate   是否需要必填校验
 * @param filesize   上传文件最大size
 * @param mimetypes   文件选择时可选择的文件类型
 * @param formdata   上传请求对象
 * @param uploadurl   上传文件的请求地址
 * @param desc   上传要求描述
 * @param numlimit   上传文件最大数量限制
 * @param urllist   文件ulr数组 ["URL1","URL2"]
 * @param createthumbnail   是否创建缩略图
 *
 */
angular.module("cy.uploadifyfile", []).directive("cyUploadifyfile", function ($rootScope, $timeout) {
  return {
    restrict: 'AE',
    templateUrl: '/qycy/ecpmp/directives/cy-uploadifyfile/cy-uploadifyfile.html',
//    templateUrl: '../../directives/cy-uploadifyfile/cy-uploadifyfile.html',
    scope: {
      filepickerid: "@",
      filelistid: "@",
      namelistid: "@",
      uploadifyid: "@",
      accepttype: "=",
      validate: "=",
      filesize: "=",
      mimetypes: "=",
      formdata: "=",
      uploadurl: "=",
      desc: "=",
      numlimit: "=",
      urllist: "=",
      createthumbnail: "=",
      auto: "=",
      callback:"&",

    },
    replace: true,
    transclude: true,
    controller: function ($scope, $element, $attrs, $rootScope) {
//      document.onkeydown = function (e) {
//        var ev = (typeof event != 'undefined') ? window.event : e;
//        if (ev.keyCode == 13) {
//          return false;
//        }
//      }
      $scope.errorInfo = "";
      $scope.buttonName = "导入文件";
      if($scope.formdata.use =='msidsnList'){
    	  $scope.buttonName = "模板导入";
      }else if($scope.formdata.use =='video'){
    	  $scope.buttonName = "点击添加视频";
      }else if($scope.formdata.use ="batch"){
    	  $scope.buttonName = "批量导入";
      }
      $timeout(function () {
        $scope.createthumbnail = $scope.createthumbnail === undefined ? true : $scope.createthumbnail;
        $scope.validate = $scope.validate === undefined ? false : $scope.validate;
        // Web Uploader定义
        $scope.uploader;
        var $ = jQuery,
            ratio = window.devicePixelRatio || 1,

        // 创建缩略图
        // 如果为非图片文件，可以不用调用此方法。
            thumbnailWidth = 100 * ratio,
            thumbnailHeight = 100 * ratio;

        // 初始化Web Uploader
        $scope.uploader = WebUploader.create({

          // 选完文件后，是否自动上传。
          auto: $scope.auto === undefined ? true : $scope.auto,

          // swf文件路径
          swf: '/qycy/ecpmp/assets/Uploader.swf',

          // 文件接收服务端。
          server: $scope.uploadurl,

          // 选择文件的按钮。可选。
          // 内部根据当前运行是创建，可能是input元素，也可能是flash.
          pick: "#" + $scope.filepickerid,

          // 只允许选择图片文件。
          // 只允许选择图片文件。
          accept: {
            title: 'Images',
            extensions: $scope.accepttype,
            mimeTypes: $scope.mimetypes
          },
          // 上传数量。
          fileNumLimit: $scope.numlimit || 1,

          // 上传单个文件大小。
          fileSingleSizeLimit: 1024 * 1024 * $scope.filesize,

          formData: $scope.formdata,

          duplicate: true
        });

        $scope.uploader.on("ready", function () {
          $scope.broadData = {
            file: "",
            uploader: $scope.uploader,
            errorInfo: ""
          }
          if ($scope.urllist && $scope.urllist.length > 0) {
            var getFileBlob = function (url, cb) {
              var xhr = new XMLHttpRequest();
              xhr.open("GET", url);
              xhr.responseType = "blob";
              xhr.addEventListener('load', function () {
                cb(xhr.response);
              });
              xhr.send();
            };
            var blobToFile = function (blob, name) {
              blob.lastModifiedDate = new Date();
              blob.name = name;
              return blob;
            };

            var getFileObject = function (filePathOrUrl, cb) {
              var fileName = filePathOrUrl.substring(filePathOrUrl.lastIndexOf("/") + 1);
              fileName = decodeURIComponent(fileName);
              getFileBlob(filePathOrUrl, function (blob) {
                cb(blobToFile(blob, fileName));
              });
            };

            //需要编辑的图片列表
            $.each($scope.urllist, function (index, item) {
              getFileObject(item, function (fileObject) {
                var wuFile = new WebUploader.Lib.File(WebUploader.guid('rt_'), fileObject);
                var file = new WebUploader.File(wuFile);
                file.setStatus('complete');
                $scope.uploader.addFiles(file);
              })
            });
          }
        });

        //添加文件前校验
        $scope.uploader.on('beforeFileQueued', function (file) {
          $rootScope.$apply(function () {
            $scope.uploader.reset();
            $scope.errorInfo = "";
          })
        });

        $scope.uploader.on('error', function (type) {
          $rootScope.$apply(function () {
            if (type == "Q_TYPE_DENIED") {//文件类型不满足
              var accepttype = angular.copy($scope.accepttype).replace(/,/g, "，");
              $scope.errorInfo = "仅支持" + accepttype + "类型的文件"
            } else if (type == "F_EXCEED_SIZE") {//添加的文件总大小超出
              $scope.errorInfo = "请上传小于" + $scope.filesize + "M的文件"
            } else if (type == "Q_EXCEED_NUM_LIMIT") {
              $scope.errorInfo = "文件数量超出上限，请删除后再试";
            } else {
              $scope.errorInfo = "上传出错";
            }
            $scope.broadData.errorInfo = $scope.errorInfo;
            $scope.broadData.file = "";
            $rootScope.$broadcast($scope.uploadifyid, "", "", $scope.broadData);
          })
        });

        // 当有文件添加进来的时候
        $scope.uploader.on('fileQueued', function (file) {
          $rootScope.$apply(function () {
        	  if($scope.formdata){
        		  if($scope.formdata.use =='video'){
        			  $("#" + $scope.filepickerid).find("span").text("点击添加视频");
        		  }else{
        			  $("#" + $scope.filepickerid).find("span").text("重新导入");
        		  }
        	  }else{
        		  $("#" + $scope.filepickerid).find("span").text("重新导入");
        	  }
          });
          $scope.urllist = [];
          console.log(file);
        });

        // 文件上传过程中创建进度条实时显示。
        $scope.uploader.on('uploadProgress', function (file, percentage) {

          var $li = $('#' + file.id),
              $percent = $li.find('.progress span');

          // 避免重复创建
          if (!$percent.length) {
            $percent = $('<p class="progress"><span></span></p>')
                .appendTo($li)
                .find('span');
          }

          $percent.css('width', percentage * 100 + '%');
        });

        // 文件上传成功，给item添加成功class, 用样式标记上传成功。
        $scope.uploader.on('uploadSuccess', function (file, response) {
          $rootScope.$apply(function () {
            var result = response.result;
            if (result.resultCode == '1030100000') {

              // 创建缩略图
              // thumbnailWidth x thumbnailHeight 为 100 x 100
              if ($scope.createthumbnail) {//是否创建缩略图
                var $list = $('#' + $scope.filelistid);
                var $li = $(
                            '<div id="' + file.id + '" class="file-item thumbnail">' +
                            '<p class="imgWrap"><img></p>' +
                            '<div class="info">' + file.name + '</div>' +
                            '</div>'
                    ),
                    $img = $li.find('img');

                var $btns = $('<div class="file-panel">' +
                    '<span class="cancel" >删除</span>').appendTo($li);
                $li.on('mouseenter', function () {
                  $btns.stop().animate({height: 30});
                });
                $li.on('mouseleave', function () {
                  $btns.stop().animate({height: 0});
                });
                // $list为容器jQuery实例
                $list.append($li);
                $btns.on('click', 'span', function () {
                  var index = $(this).index();
                  var file_index = $(this).parent().parent().index()
                  switch (index) {
                    case 0:
                      $rootScope.$apply(function () {
                        $scope.uploader.removeFile(file);
                        removeFile(file, file_index);
                        if ($scope.validate && $("#" + $scope.filelistid).find(".file-item").length === 0) {
                          $scope.errorInfo = "必填";
                        }
                      });
                      return;
                  }
                });
                $scope.uploader.makeThumb(file, function (error, src) {
                  if (error) {
                    $img.replaceWith('<span>不能预览</span>');
                    return;
                  }
                  $img.attr('src', src);
                  $('#' + file.id).addClass('upload-state-done');
                }, thumbnailWidth, thumbnailHeight);
              } else {//不创建缩略图时显示文件名
                var $nameList = $('#' + $scope.namelistid);
                var $nameLi = $(
                        '<span id="' + file.id + '" class="name-container">' +
                        '<span class="nameInfo">' + file.name + '</span>' +
                        '<span class="uplodify-del uplodify-del-' + file.id + '"></span>' +
                        '</span>'
                );
                // $list为容器jQuery实例
                $nameList.append($nameLi);

                $(".uplodify-del-" + file.id).on('click', function () {
                  var index = $(this).index();
                  var file_index = $(this).parent().index();
                  switch (index) {
                    case 1:
                      $rootScope.$apply(function () {
                        $scope.uploader.removeFile(file);
                        removeFile(file, file_index);
                        if ($scope.validate && $("#" + $scope.filelistid).find(".name-container").length === 0) {
                          $scope.errorInfo = "必填";
                        }
                      });
                      return;
                  }
                });
              }

            } else if (result.resultCode == '1030120012') {
              top.location.href = "/qycy/ecpmp/view/login/login.html";
            } else if (result.resultCode == '1030120002') {
              $scope.uploader.removeFile(file);
              var accepttype = angular.copy($scope.accepttype).replace(/,/g, "，");
              $scope.errorInfo = "仅支持" + accepttype + "类型的文件"
            } else {
              $scope.uploader.removeFile(file);
              $scope.errorInfo = result.resultCode;

            }
            $scope.broadData = {
              file: file,
              uploader: $scope.uploader,
              errorInfo: $scope.errorInfo,
              fileUrl:response.fileUrl
            }
            $rootScope.$broadcast($scope.uploadifyid, response.fileUrl, "", $scope.broadData);
              $scope.callback({"data": $scope.broadData})
          })

        });

        // 文件上传失败，显示上传出错。
        $scope.uploader.on('uploadError', function (file, reson) {
//          var $li = $('#' + file.id),
//              $error = $li.find('div.error');
//          // 避免重复创建
//          if (!$error.length) {
//            $error = $('<div class="error"></div>').appendTo($li);
//          }
//          $error.text('上传失败');
          console.log(reson);
          $rootScope.$apply(function () {
            if (reson === 'server' || reson === 'abort' || reson === 'http') {
              $scope.errorInfo = "1030120500"
            } else {
              $scope.errorInfo = '图片无法识别，请更换图片或浏览器尝试解决';
            }
            $scope.uploader.removeFile(file);
            removeFile(file);
            $scope.broadData = {
              file: file,
              uploader: $scope.uploader,
              errorInfo: $scope.errorInfo
            };
            $rootScope.$broadcast($scope.uploadifyid, "", "", $scope.broadData);
          });
        });

        // 完成上传完了，成功或者失败，先删除进度条。
        $scope.uploader.on('uploadComplete', function (file, response) {
          $('#' + file.id).find('.progress').remove();
        });

        if($attrs.resetErrTip){ //清除错误提示（使用场景：弹窗 点击关闭弹窗时 清除错误提示）
          $scope.$on("event.resetErrTip", function (event) {
            $scope.errorInfo = ''
          });
        }

        // 负责view的销毁
        function removeFile(file, index) {
          var $li = $('#' + file.id);
          $li.off().find('.file-panel').off().end().remove();
          var fileUrl = "";
          $rootScope.$broadcast($scope.uploadifyid, fileUrl, index);
        }
      });

    }
  }
});