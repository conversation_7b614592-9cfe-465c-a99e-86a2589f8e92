var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n", "service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {
        $scope.accountID = $.cookie('accountID') || '1000';
        $scope.isFileNamePass = false;
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            enterpriseName: "",
            receiveNum: "",
            startTime: "",
            endTime: "",
            enterpriseID: "",
            search: false,
        };
        $scope.enterpriseTypeMap = [
            {
                id: "1",
                name: "直客"
            },
            {
                id: "3",
                name: "代理商"
            }, {
                id: "5",
                name: "分省"
            }


        ];

        //通知方式
        $scope.notificationMap = {
            "1": "使用通知式USSD发送"
            , "2": "使用闪信发送"
            , "3": "通知式USSD投递失败时改由闪信投递"
            , "4": "使用闪信发送，需要回执结果"
            , "5": "通知式USSD投递失败时改由闪信投递，需要回执结果"
            , "6": "短信"
            , "8": "使用通知式USSD发送(无域查询)"
        }
         //通知方式
        $scope.notificationMethods = [
            {
                id: "1",
                name: "使用通知式USSD发送"
            },
            {
                id: "2",
                name: "使用闪信发送"
            },
            {
                id: "3",
                name: "通知式USSD投递失败时改由闪信投递"
            },
            {
                id: "4",
                name: "使用闪信发送，需要回执结果"
            },
            {
                id: "5",
                name: "通知式USSD投递失败时改由闪信投递，需要回执结果"
            },
            {
                id: "6",
                name: "短信"
            },
            {
                id: "8",
                name: "使用通知式USSD发送(无域查询)"
            },
        ];


    /*
    * 导出文件弹窗
    */
    $scope.initSel.enterpriseType = null;
    $scope.initSel.msgType = null;
    $scope.exportFile = function () {
        $scope.remarks = "";
        $scope.isGreaterFileName = false;
        $scope.isEmptyFileName = false;
        $scope.isSpecialCharacters = false;
        $scope.isFileNamePass = true;
        $("#exportFile").modal("show");
    };
    /*
       * 文件名校验
       */
        $scope.checkEmpty = function(){
            $scope.isFileNamePass = true;
            $scope.isGreaterFileName = false;


            var reg = /^[a-z0-9\u4e00-\u9fa5]+$/i
            // if(!$scope.remarks){
            //     $scope.isGreaterFileName = false;
            //     $scope.isEmptyFileName = true;
            //     $scope.isFileNamePass = false;
            //     $scope.isSpecialCharacters = false
            // }else if(!reg.test($scope.remarks)){
            //     $scope.isGreaterFileName = false;
            //     $scope.isEmptyFileName = false;
            //     $scope.isFileNamePass = false;
            //     $scope.isSpecialCharacters = true
            // }else

            if($scope.remarks.length > 255){
                $scope.isEmptyFileName = false;
                $scope.isGreaterFileName = true;
                $scope.isFileNamePass = false;
                $scope.isSpecialCharacters = false
            }
            //     else {
            //     $scope.isEmptyFileName = false;
            //     $scope.isGreaterFileName = false;
            //     $scope.isSpecialCharacters = false
            //     $scope.isFileNamePass = true;
            // }
        };
    /*
       * 提交导出文件信息
       */
    $scope.submitExportTask = function () {
        $("#exportFile").modal("hide");
        var params = JSON.parse(JSON.stringify($scope.initSel));
        var exportTaskReq = {
            "exportTaskInfo": {
                "fileName":null,
                "remarks":$scope.remarks,
                "taskType": 7,
                "taskStatus": 0,
                "operatorID": $scope.accountID,
                "params": params
            }
        };
        exportTaskReq.exportTaskInfo.params.token =  $scope.token;
        exportTaskReq.exportTaskInfo.params.isExport = 1;
        exportTaskReq.exportTaskInfo.params.startDate =  $scope.getTimeToReq($scope.initSel.startTime);
        exportTaskReq.exportTaskInfo.params.endDate = $scope.getTimeToReq($scope.initSel.endTime);
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/exportTaskService/createExportTask",
            data: JSON.stringify(exportTaskReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        if(data.desc){
                            $scope.tip = data.desc;
                            $('#myModal').modal();
                        }
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '1030120500';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    $scope.uniqueTip = "";
    $scope.checkUnique = true;
    // $scope.querydeliveryDetailList();
}
    $scope.clearmsg = function () {

        $scope.uniqueTip = "";
        $scope.checkUnique = true;
    }

    $scope.getResult = function (deliveryResult) {

        if (deliveryResult == 0 || deliveryResult == 90) {
            return "成功";
        }
        else {
            return "失败";
        }
    }

    $scope.getDeliveryType = function (deliveryType) {

        if (deliveryType == 1) {
            return "USSD";
        }
        else if (deliveryType == 2) {
            return "闪信";
        }
        else if (deliveryType == 3) {
            return "USSD失败转闪信";
        }
        else if (deliveryType == 4) {
            return "短信";
        }
        else if (deliveryType == 5) {
            return "彩信";
        }
        else if (deliveryType == 6) {
            return "增彩";
        }
    }

    $scope.getTime = function (time) {
        if (time == null || time == "") {
            return "";
        }

        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        var hour = time.slice(8, 10);
        var minutes = time.slice(10, 12);
        var ss = time.slice(12, 14);
        return year + "-" + month + "-" + day + " " + hour + ":" + minutes + ":" + ss;
    };
    /**
     * 获取时间戳
     * @param timeStr 时间，是为yyyyMMddHHmmss
     * @returns
     */
    $scope.formateTime= function (timeStr) {
        if(timeStr){
            return timeStr.substring(0, 4) + "/" + timeStr.substring(4, 6) + "/" + timeStr.substring(6, 8) + " " + timeStr.substring(8, 10) + ":" + timeStr.substring(10, 12) + ":" + timeStr.substring(12, 14)
        }else{
            return '';
        }

    };

    $scope.formateArgs = function (val) {
        var a = [];
        if(val){
            a = JSON.parse(val);
            return a.join(",")
            }
    };
    // $('.input-daterange').datetimepicker({
    //     format: "yyyy-mm-dd hh",
    //     weekStart: 0,
    //     clearBtn: true,
    //     language: "zh-CN",
    //     autoclose: true
    // });
    $('#start').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:1
    });
    $('#end').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:1

    });
$('#start').on('changeDate', function () {
    $rootScope.$apply(function () {
            $scope.searchOn("start");
        }
    )
});

$('#end').on('changeDate', function () {
    $rootScope.$apply(function () {
            $scope.searchOn("end");
        }
    )
});

//判断搜索按钮是否置灰
$scope.searchOn = function (val) {
    $scope.uniqueTip = "";
    $scope.checkUnique = true;
    var startTime = document.getElementById("start").value;
    var endTime = document.getElementById("end").value;
    if(startTime>endTime){
        if("start" === val){
            endTime = startTime;
        }else{
            startTime = endTime;

        }
    }
    if (startTime !== '') {
        $scope.initSel.startTime = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + startTime.substring(11, 13) +'0000';
        // $("#start").val(startTime.substring(0, 13) + ":00");
        $("#start").datetimepicker("setDate", new Date(startTime.substring(0, 13) + ":00") );
    }
    else {
        $scope.initSel.startTime = "";
    }

    if (endTime !== '') {
        $scope.initSel.endTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + endTime.substring(11, 13) + '5959';
        // $("#end").val(endTime.substring(0, 13) + ":00");
        $("#end").datetimepicker("setDate",  new Date(endTime.substring(0, 13) + ":00") );


    }
    else {
        $scope.initSel.endTime = "";
    }

    if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
        $scope.initSel.search = false;
    }
    else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
        $scope.initSel.search = false;
    }
    else {
        $scope.initSel.search = true;
    }
    $scope.initSel.startDate =   $scope.initSel.startTime ;
    $scope.initSel.endDate = $scope.initSel.endTime;
};

$scope.showTime = function (time) {
    if (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        var hour = time.slice(8, 10);
        var minute = time.slice(10, 12);
        var second = time.slice(12, 14);
        return year + month + day + hour + minute + second;
    }
    else {
        return ''
    }

}
$scope.getTimeToReq = function(timeStr){
    var startTime = timeStr.substring(0,4) + "/" + timeStr.substring(4,6) + "/" + timeStr.substring(6,8) + " " + timeStr.substring(8,10) + ":" + timeStr.substring(10,12) + ":" + timeStr.substring(12,14)
    return new Date(startTime).getTime();
}
//后续post的函数
$scope.querydeliveryDetailList = function (condition) {
    $scope.uniqueTip = "";
    $scope.checkUnique = true;


    if (condition != 'justPage') {
        var req = $scope.initSel;
        req.page = {
            "pageNum": 1,
            "pageSize": parseInt($scope.pageInfo[0].pageSize),
            "isReturnTotal": "1",
        };
        $scope.pageInfo[0].currentPage = 1;
        $scope.reqTemp = angular.copy(req);

    } else {
        //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
        var req = $scope.reqTemp;
        req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
        req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }

    RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryThirdPartyDeliveryList",
        data: JSON.stringify(req),
        success: function (result) {
            $rootScope.$apply(function () {
                var data = result.result;
                if (data.resultCode == '**********') {
                    $scope.deliveryDetailListData = result.deliveryDetailList || [];
                    $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                    $scope.pageInfo[0].totalPage = result.totalNum !== "0" ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
                }
                else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }
            })
        },
        error: function () {
            $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                }
            )
        }
    });



};

$(function () {
    $('.glyphicon-calendar').on('click', function () {
        $('#time-config').trigger('click');
    })
})

}]);


