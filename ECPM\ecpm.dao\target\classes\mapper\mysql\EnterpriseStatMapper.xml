<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EnterpriseStatMapper">
	<resultMap id="enterpriseStatWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseStatWrapper">
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="enterpriseType" column="enterpriseType"
			javaType="java.lang.Integer" />
		<result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
		<result property="activityCount" column="activityCount"
			javaType="java.lang.Long" />
		<result property="spokeCount" column="spokeCount" javaType="java.lang.Long" />
		<result property="screenCount" column="screenCount" javaType="java.lang.Long" />
		<result property="endPhoneCount" column="endPhoneCount"
			javaType="java.lang.Long" />
		<result property="openedTime" column="openedTime" javaType="java.lang.String" />
		<result property="updateTime" column="updateTime" javaType="java.lang.String" />
		<result property="deductTime" column="deductTime" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reservedQuota1" column="reservedQuota1"
			javaType="java.lang.Long" />
		<result property="reservedQuota2" column="reservedQuota2"
			javaType="java.lang.Long" />
		<result property="reservedQuota3" column="reservedQuota3"
			javaType="java.lang.Long" />
		<result property="reservedQuota4" column="reservedQuota4"
			javaType="java.lang.Long" />
		<result property="reservedQuota5" column="reservedQuota5"
			javaType="java.lang.Long" />
		<result property="reservedQuota6" column="reservedQuota6"
			javaType="java.lang.Long" />
		<result property="reservedQuota7" column="reservedQuota7"
			javaType="java.lang.Long" />
		<result property="reservedQuota8" column="reservedQuota8"
			javaType="java.lang.Long" />
		<result property="reservedQuota9" column="reservedQuota9"
			javaType="java.lang.Long" />
		<result property="reservedQuota10" column="reservedQuota10"
			javaType="java.lang.Long" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>
	<!-- 只查询如下字段 lwx595992 -->
	<resultMap id="enterpriseStatMiniWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseStatMiniWrapper">
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="enterpriseType" column="enterpriseType"
			javaType="java.lang.Integer" />
		<result property="openedTime" column="openedTime" javaType="java.lang.String" />
		<result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
		<result property="screenCount" column="screenCount" javaType="java.lang.Long" />
	</resultMap>

	<select id="queryEnterpriseStat" resultMap="enterpriseStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		serviceType,
		activityCount,
		spokeCount,
		screenCount,
		endPhoneCount,
		openedTime,
		updateTime,
		deductTime,
		extInfo,
		reservedQuota1,
		reservedQuota2,
		reservedQuota3,
		reservedQuota4,
		reservedQuota5,
		reservedQuota6,
		reservedQuota7,
		reservedQuota8,
		reservedQuota9,
		reservedQuota10,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from ecpm_t_enterprise_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID != null">
				enterpriseID=#{enterpriseID}
			</if>
			<if test="enterpriseName !=null  and enterpriseName !=''">
				and enterpriseName like #{enterpriseName}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
		</trim>
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryEnterpriseStatCount" resultType="java.lang.Integer">
		SELECT
		count(0) from ecpm_t_enterprise_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID != null">
				enterpriseID=#{enterpriseID}
			</if>
			<if test="enterpriseName !=null  and enterpriseName !=''">
				and enterpriseName like #{enterpriseName}
			</if>
		</trim>
	</select>

	<select id="queryEnterpStatCountWithActivity" resultType="java.lang.Integer">
		select
		count(t.enterpriseID)
		from ecpm_t_enterprise_stat t
		where 1=1 
		<!-- 当参数processStatus活动进行状态为空时或0时,不关联活动表 -->
		<if test="processStatus !=null and processStatus !=0">
			and  t.enterpriseID in
		(
		select c.enterpriseID
		from ecpm_t_activity c
		<trim prefix="where" prefixOverrides="and|or">
			<choose>
				<!--processStatus为1.未开始时，查询出审核状态为审批通过并且当前时间小于生效时间的数据 -->
				<when test="processStatus eq 1">
					and c.auditStatus=2
					and c.effectivetime <![CDATA[ > ]]>
					#{nowTime}
				</when>
				<!--processStatus为2.进行中时，查询出审核状态为审批通过且当前时间大于生效时间且当前时间小于失效时间的数据 -->
				<when test="processStatus eq 2">
					and c.auditStatus=2
					and c.effectivetime <![CDATA[ <= ]]>
					#{nowTime}
					and c.expiretime <![CDATA[ >= ]]>
					#{nowTime}
				</when>
				<!--processStatus为3.已结束时，查询出审核状态为审批通过且当前时间大于失效时间的数据 -->
				<when test="processStatus eq 3">
					and c.auditStatus=2
					and c.expiretime <![CDATA[ < ]]>
					#{nowTime}
				</when>
			</choose>
		</trim>
		)
		</if>
		
		<if test="enterpriseID != null">
			and t.enterpriseID=#{enterpriseID}
		</if>
		<if test="authEnterpriseIDs !=null and authEnterpriseIDs.size>0">
			and t.enterpriseID in
			<foreach item="enterpriseID" index="index" collection="authEnterpriseIDs" open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		<if test="enterpriseName !=null  and enterpriseName !=''">
			and t.enterpriseName like "%"#{enterpriseName}"%"
		</if>
		<if test="serviceType !=null">
			and t.serviceType = #{serviceType}
		</if>
		order by t.openedTime desc
	</select>

	<select id="queryEnterpriseStatWithActivity" resultMap="enterpriseStatWrapper">
		select
		t.enterpriseID,
		t.enterpriseName,
		t.enterpriseType,
		t.serviceType,
		t.activityCount,
		t.spokeCount,
		t.screenCount,
		t.endPhoneCount,
		date_format(t.openedTime,'%Y%m%d%H%i%s') as openedTime,
		t.updateTime,
		t.deductTime,
		t.extInfo,
		t.reservedQuota1,
		t.reservedQuota2,
		t.reservedQuota3,
		t.reservedQuota4,
		t.reservedQuota5,
		t.reservedQuota6,
		t.reservedQuota7,
		t.reservedQuota8,
		t.reservedQuota9,
		t.reservedQuota10,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved5,
		t.reserved6,
		t.reserved7,
		t.reserved8,
		t.reserved9,
		t.reserved10
		from ecpm_t_enterprise_stat t
		where 1=1 
		<!-- 当参数processStatus活动进行状态为空时或0时,不关联活动表 -->
		<if test="processStatus !=null and processStatus !=0">
			and  t.enterpriseID in
		(
		select c.enterpriseID
		from ecpm_t_activity c
		<trim prefix="where" prefixOverrides="and|or">
			<choose>
				<!--processStatus为1.未开始时，查询出审核状态为审批通过并且当前时间小于生效时间的数据 -->
				<when test="processStatus eq 1">
					and c.auditStatus=2
					and c.effectivetime <![CDATA[ > ]]>
					#{nowTime}
				</when>
				<!--processStatus为2.进行中时，查询出审核状态为审批通过且当前时间大于生效时间且当前时间小于失效时间的数据 -->
				<when test="processStatus eq 2">
					and c.auditStatus=2
					and c.effectivetime <![CDATA[ <= ]]>
					#{nowTime}
					and c.expiretime <![CDATA[ >= ]]>
					#{nowTime}
				</when>
				<!--processStatus为3.已结束时，查询出审核状态为审批通过且当前时间大于失效时间的数据 -->
				<when test="processStatus eq 3">
					and c.auditStatus=2
					and c.expiretime <![CDATA[ < ]]>
					#{nowTime}
				</when>
			</choose>
		</trim>
		)
		</if>
		
		<if test="enterpriseID != null">
			and t.enterpriseID=#{enterpriseID}
		</if>
		<if test="authEnterpriseIDs !=null and authEnterpriseIDs.size>0">
			and t.enterpriseID in
			<foreach item="enterpriseID" index="index" collection="authEnterpriseIDs" open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		<if test="enterpriseName !=null  and enterpriseName !=''">
			and t.enterpriseName like "%"#{enterpriseName}"%"
		</if>
		<if test="serviceType !=null">
			and t.serviceType = #{serviceType}
		</if>
		order by t.openedTime desc
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryEnterpriseStatByServiceType" resultMap="enterpriseStatMiniWrapper">
		select
		enterpriseID,
		enterpriseName,	
		enterpriseType,	
		date_format(openedTime,'%Y%m%d%H%i%s') as openedTime,
		date_format(deductTime,'%Y%m%d%H%i%s') as deductTime,			
		serviceType,
		screenCount
		from ecpm_t_enterprise_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID != null">
				enterpriseID=#{enterpriseID}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
		</trim>
	</select>

	<update id="updateEnterpriseStat">
		update ecpm_t_enterprise_stat set
		enterpriseID=#{enterpriseID}
		<if test="updateTime != null">
			,updatetime=#{updateTime}
		</if>
		<if test="now != null">
			,openedTime=#{now}
		</if>
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType}
	</update>

	<update id="updateServiceTypeCount">
		update ecpm_t_enterprise_stat
		set
		<if test="screenCount != null">
			screenCount = #{screenCount},
		</if>
		<if test="screenCountAddAmount != null">
			screenCount = screenCount+ #{screenCountAddAmount},
		</if>		
		<if test="endPhoneCount != null">
			endPhoneCount = endPhoneCount+ #{endPhoneCount},
		</if>
		<if test="deductTime != null">
			deducttime=#{deductTime},
		</if>
		<if test="nowTime != null">
			updatetime=#{nowTime}
		</if>
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType}
	</update>

	<insert id="createEnterpriseStat">
		insert into
		ecpm_t_enterprise_stat
		(
		enterpriseID,
		enterpriseName,
		enterpriseType,
		serviceType,
		activityCount,
		spokeCount,
		screenCount,
		endPhoneCount,
		openedTime,
		updateTime,
		deductTime,
		status,
		extInfo,
		reservedQuota1,
		reservedQuota2,
		reservedQuota3,
		reservedQuota4,
		reservedQuota5,
		reservedQuota6,
		reservedQuota7,
		reservedQuota8,
		reservedQuota9,
		reservedQuota10,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		values
		(
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseType},
		#{serviceType},
		#{activityCount},
		#{spokeCount},
		#{screenCount},
		#{endPhoneCount},
		#{openedTime},
		#{updateTime},
		#{deductTime},
		#{status},
		#{extInfo},
		#{reservedQuota1},
		#{reservedQuota2},
		#{reservedQuota3},
		#{reservedQuota4},
		#{reservedQuota5},
		#{reservedQuota6},
		#{reservedQuota7},
		#{reservedQuota8},
		#{reservedQuota9},
		#{reservedQuota10},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10}
		)
	</insert>
	
	
	<update id="updateActivityAcount">
		update ecpm_t_enterprise_stat 
		set activitycount = activitycount+1 
		where enterpriseId = #{enterpriseID} and serviceType = #{servType}
	</update>
	
	<update id="updateFromToCyQueryStatInfoTask">
		update ecpm_t_enterprise_stat
		set
		<if test="screenCount != null">
			screenCount = #{screenCount},
		</if>
		<if test="updateTime != null">
			updatetime=#{updateTime}
		</if>
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType}
	</update>
	
	<select id="searchBusinessCardEnterprise" resultMap="enterpriseStatWrapper">
      	select 
      		t.enterpriseID,
			t.enterpriseName,
			t.enterpriseType,
			t.serviceType,
			t.activityCount,
			t.spokeCount,
			t.screenCount,
			t.endPhoneCount,
			t.openedTime,
			t.updateTime,
			t.deductTime,
			t.extInfo
      	from ecpm_t_enterprise_stat t
      	where
      		<!--名片彩印 -->
      		 t.serviceType=1 or t.serviceType=2
    </select>
    
	<select id="getServiceIDByType" resultType="java.lang.String">
		select serviceID from ecpm_t_servicetype_rel
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseType != null">
				and enterpriseType = #{enterpriseType}
			</if>
			<if test="serviceType != null">
				and serviceType = #{serviceType}
			</if>
			<if test="subServiceType != null">
				and subServiceType = #{subServiceType}
			</if>
		</trim>
		limit 1
	</select>
	
	<update id="updateStatByAuditnotify">
		update ecpm_t_enterprise_stat
		<trim prefix="set" prefixOverrides=",">
		</trim>
		where enterpriseID = #{enterpriseID}
		  and serviceType = 1
	</update>
	
	<!-- 根据企业ID+业务类型(3)+开通时间不为空 ，查询企业表 -->
    <select id="queryEnterpriseStatCountByOpenTime" resultType="java.lang.Integer">
        SELECT
        count(0) from ecpm_t_enterprise_stat
        where openedTime is not null
        and enterpriseID=#{enterpriseID}
        and serviceType=#{serviceType}
    </select>
    
    <update id="updateOpenTime" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseStatWrapper">
        update ecpm_t_enterprise_stat
        set
            openedTime=#{openedTime}
        where
        enterpriseID=#{enterpriseID} and serviceType=#{serviceType}
    </update>
    
    <update id="updateSpokeCount" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseStatWrapper">
        update ecpm_t_enterprise_stat
        set
        spokeCount = (case 
					when IFNULL(spokeCount, 0)= 0
					then  1
					else  spokeCount+1
					end ) 
        where
        enterpriseID=#{enterpriseID} and serviceType=#{serviceType}
    </update>
    
    <update id="updateEnterpriseName">
		update ecpm_t_enterprise_stat t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>
</mapper>