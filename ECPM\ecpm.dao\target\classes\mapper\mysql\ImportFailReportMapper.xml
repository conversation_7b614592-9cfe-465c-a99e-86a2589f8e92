<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ImportFailReportMapper">
    <resultMap id="ImportFailReportMapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ImportFailReportWrapper">
        <result column="ID" property="ID"/>
        <result column="dataType" property="dataType"/>
        <result column="objectID" property="objectID"/>
        <result column="createTime" property="createTime"/>
        <result column="failReason" property="failReason"/>
    </resultMap>
    <insert id="insertAll" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ImportFailReportWrapper">
        INSERT INTO `ecpm_t_import_fail_report`
            (`dataType`, `objectID`, `createTime`, `failReason`)
        VALUES ( #{dataType},#{objectID},#{createTime},#{failReason});
    </insert>

    <select id="queryAll" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ImportFailReportWrapper">
        SELECT f.ID,f.dataType,f.objectID,f.createTime,f.failReason, c.enterpriseID,c.enterpriseName
        FROM `ecpm_t_import_fail_report` f left join ecpm_t_content c on f.objectID = c.id
        where `dataType` = 1;
    </select>

    <select id="queryAllByDataType" resultType="java.util.Map" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ImportFailReportWrapper">
        SELECT ID,objectID,createTime,failReason FROM `ecpm_t_import_fail_report` where `dataType` = 1;
    </select>
    
    <delete id="DeleteOneYearData" >
        delete from ecpm_t_import_fail_report where createTime &lt; #{createTime};
    </delete>

</mapper>
