var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n", "service.common"])
app.controller('merchantActivityAuditCtrl', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
  $scope.init = function () {
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];

    //判断用户角色
    $scope.isSuperManager = false;
    $scope.isZhike = false;
    $scope.isAgent = false;
    var loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
    $scope.isZhike = (loginRoleType == 'zhike');
    $scope.isAgent = (loginRoleType == 'agent');
    $scope.enterpriseID = "";
    if ($scope.isZhike)
    {
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
    }

    $scope.auditStatusList = [
      {"key": "1", "value": "待审核"},
      {"key": "2", "value": "审核通过"},
      {"key": "3", "value": "审核驳回"}
    ];
    $scope.enterpriseName = "";
    $scope.activityName = "";
    $scope.auditStatus = "";
    $scope.activityID = "";
    $scope.getActivityList();
    $scope.enterpriseinfo();
  };
  
  $scope.enterpriseinfo = function () 
  {
  	if (null == $.cookie('enterpriseID')) 
  	{
			return;
		}
  	var req = {
  	        "id": $.cookie('enterpriseID'),
  	        "pageParameter": {
  	          "pageNum": 1,
  	          "pageSize": 100,
  	          "isReturnTotal": "1"
  	        }
  	      }
  	      /*查询企业列表*/
  	      RestClientUtil.ajaxRequest({
  	        type: 'POST',
  	        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
  	        data: JSON.stringify(req),
  	        success: function (data) {
  	          $rootScope.$apply(function () {
  	            var result = data.result;
  	            if (result.resultCode == '1030100000') {
  	            	console.log(data.enterprise);
  	              $scope.enterpriseName = data.enterprise.enterpriseName;
  	              $scope.businessStatus = data.enterprise.businessStatus;
  	              $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
  	              console.log($scope.businessStatus);
  	              if ($scope.businessStatus == 1) {
  	            	  $('#Modalisaengt').modal();
  	              }
  	            }
  	          })
  	        }
  	      });
  };

$scope.getArea = function(cityList,proviceList) {
  var area = "";
  var provices = "";
  var citys = "";

  if (proviceList != "") {
    for (var i = 0; i < proviceList.length; i++) {
      if (proviceList[i].provinceName) {
        provices = provices + proviceList[i].provinceName + "\\";
      }
    }
  }

  if (cityList != "") 
  {
    for (var i = 0; i < cityList.length; i++) {
      if (cityList[i].cityName) {
          citys = citys + cityList[i].cityName + "\\";
      }
    }
  }

  area = provices + citys;
  if (area != "") {
    area = area.slice(0, -1);
    area = area.length > 512 ? area.slice(0, 509) + '...' : area;
  }
  return area;
};

//计算两个日期的天数差
$scope.datedifference = function(strDateStart, strDateEnd) {    //sDate1和sDate2是2006-12-18格式  
  var strSeparator = "-"; //日期分隔符
  var oDate1;
  var oDate2;
  var iDays;
  oDate1= strDateStart.split(strSeparator);
  oDate2= strDateEnd.split(strSeparator);
  var strDateS = new Date(oDate1[0], oDate1[1]-1, oDate1[2]);
  var strDateE = new Date(oDate2[0], oDate2[1]-1, oDate2[2]);
  iDays = parseInt(Math.abs(strDateS - strDateE ) / 1000 / 60 / 60 /24);//把相差的毫秒数转换为天数 
  return iDays + 1;
};

  $scope.getActivityList = function (condition) {
    var req;
    if (condition != 'justPage') {
      req = {
        "isExamine":"1",
        "activityInfoCond": {
          "enterpriseID": $scope.enterpriseID || "",
          "enterpriseName": $scope.enterpriseName,
          "activityName": $scope.activityName,
          "activityID": $scope.activityID,
          "auditStatus": $scope.auditStatus === null ? "" : $scope.auditStatus.key,
          "sortType":2,
          "sortField":2,
        },
        "page": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      req = $scope.reqTemp;
      req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    req.activityInfoCond.getContent=0;
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/activityService/queryActivityList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.activityInfoListData=result.activityInfoList||[];

            angular.forEach($scope.activityInfoListData, function (item) {
              //代言天数的日期生成
              var myDate = new Date();
              var year = myDate.getFullYear();    //获取完整的年份(4位,1970-????)
              var month = myDate.getMonth() + 1;       //获取当前月份(0-11,0代表1月)
              var date = myDate.getDate();        //获取当前日(1-31)
              var today = year + "-" + month + "-" + date;
              //当前时间大于活动结束时间
              if (item.expiretime && item.effectivetime && (myDate.getTime() > item.expiretime))
              {
                var effectivetime = CommonUtils.dateTimeFormate(item.effectivetime);
                item.effectivetime = effectivetime.year + "-" + effectivetime.month + "-" + effectivetime.day;
                var expiretime = CommonUtils.dateTimeFormate(item.expiretime);
                item.expiretime = expiretime.year + "-" + expiretime.month + "-" + expiretime.day;

                item.spokeDayNum = $scope.datedifference(item.effectivetime,item.expiretime);
              }//当前时间小于活动开始时间
              else if (item.effectivetime && (myDate.getTime() < item.effectivetime))
              {
                item.spokeDayNum = 0;
              }
              else if (item.expiretime && item.effectivetime)
              {
                var effectivetime = CommonUtils.dateTimeFormate(item.effectivetime);
                item.effectivetime = effectivetime.year + "-" + effectivetime.month + "-" + effectivetime.day;
                item.spokeDayNum = $scope.datedifference(item.effectivetime,today);
              }

              //活动有效期生成
              if (item.effectivetime) {
                var effectivetime = CommonUtils.dateTimeFormate(item.effectivetime);
                item.effectivetime = effectivetime.year + "-" + effectivetime.month + "-" + effectivetime.day;
              }
              if (item.expiretime) {
                var expiretime = CommonUtils.dateTimeFormate(item.expiretime);
                item.expiretime = expiretime.year + "-" + expiretime.month + "-" + expiretime.day;
              }

              //创建时间生成
              if (item.createTime == null || item.createTime == "")
              {
                item.createTime = "";
              }
              else
              {
                item.createTime = item.createTime.slice(0, 4) + "-" + item.createTime.slice(4, 6) + "-" + item.createTime.slice(6, 8) + " " + item.createTime.slice(8, 10) + ":" + item.createTime.slice(10, 12);
              } 

            })

            $scope.pageInfo[0].totalCount = parseInt(result.totalcount) || 0;
            $scope.pageInfo[0].totalPage=result.totalcount!==0 ?Math.ceil(result.totalcount/parseInt($scope.pageInfo[0].pageSize)):1;
          } else {
            $scope.activityInfoList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#activityModal').modal();
          }
        })

      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#activityModal').modal();
            }
        )
      }
    });
  };

  $scope.toDetail = function (item) {
    window.location = "activityDetail/activityDetail.html?activityID=" + item.activityID;
  }


})