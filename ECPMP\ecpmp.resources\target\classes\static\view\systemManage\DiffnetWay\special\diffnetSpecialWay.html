<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>特殊设置</title>
    <link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
    <link href="../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../css/searchList.css" rel="stylesheet" />
    <script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery-1.11.1.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>    
    <!--分页-->
    <script type="text/javascript" src="../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
    <script type="text/javascript" src="../../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../directives/preview/preview.css"/>
    <script src="../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <script src="../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="diffnetSpecialWay.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../css/hotlineContentManage.css"/>
    <link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
    
    <style>
        .cooperation-manage .coorPeration-table th,td{
            padding-left: 20px !important;
        }

        .delMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../../assets/images/delete.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 136px;
            z-index:99;
        }
        .addMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../../assets/images/add.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 106px;
            z-index:99;
        }
        .handle ul li icon.manage-icon {
            background-position: -126px 0;
        }

        .handle ul li icon.add-icon {
            background-position: -55px 0;
        }

        .handle ul li icon.import-icon {
            background-position: -90px 0;
        }

        .table th.adjustable-width {
            width: 25%;
        }

        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        #filePicker_ div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        .form-group div li {
            display: inline-block;
            margin-right: 10px;
            padding-right: 10px;
            cursor: pointer;
        }

        .form-group div li span {
            vertical-align: middle;
            margin-right: 4px;
        }

        /* media for adjustable search-table width  */
        @media (max-width: 1850px) {
            .table th.adjustable-width {
                width: 28%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1600px) {
            .table th.adjustable-width {
                width: 30%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1300px) {
            .table th.adjustable-width {
                width: 33%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1100px) {
            .table th.adjustable-width {
                width: 42%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        .ng-dirty.ng-invalid {
            border-color: red;
        }

        .ng-dirty.invalid {
            border-color: red;
        }

        .label-supply {
            display: inline-block;
            float: left;
            padding-right: 15px;
            padding-left: 15px;
        }
        .clearf:after{
            content:'';
            clear:both;
            height:0;
            display:block;
        }
        .seach-high div{
        	margin: 5px;
        }
    </style>
</head>

<!--
<body ng-app='myApp' ng-controller='orderListController' ng-init="init();" class="body-min-width">
-->
<body ng-app='myApp' ng-controller='specialController' ng-init="init();">
    <div class="cooperation-manage" style="overflow-x: scroll;">
       <div class="cooperation-head" ng-show="isSuperManager"><span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab">异网通道设置</span></div>
		<top:menu chose-index="2" page-url="/qycy/ecpmp/view/systemManage/DiffnetWay" list-index="79" ng-show="isSuperManager"></top:menu>
        <div class="cooperation-search">
            <form class="form-horizontal">
                <div class="form-group form-inline" style="margin-left: 0px;margin-right: 0px;">
                    <!--内容编号查询-->
                    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4" style="margin-bottom: 5px;">
                    	<label class="control-label label-supply">模板ID</label>
                        <input type="text" autocomplete="off" class="form-control" id="contentNo"
                               placeholder="请输入模板ID" ng-model="initSel.contentNo">
                    </div>
                    <!--企业编号查询-->
                    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4"  style="margin-bottom: 5px;">
                    	<label class="control-label label-supply">企业编号</label>
                        <input type="text" autocomplete="off" class="form-control" id="enterpriseID"
                               placeholder="请输入企业编号" ng-model="initSel.enterpriseID">
                    </div>
                    <!--企业名称查询-->
                    <div class="col-lg-4 col-md-4 col-sm-4 col-xs-4"  style="margin-bottom: 5px;">
                    	<label class="control-label label-supply">企业名称</label>
                        <input type="text" autocomplete="off" class="form-control" id="enterpriseName"
                               placeholder="请输入企业名称" ng-model="initSel.enterpriseName">
                    </div>
	                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-6 time">
	                	<label class="control-label label-supply" for="exportTime">创建时间</label>
	                        <div class="input-daterange input-group" id="datepicker">
	                            <input type="text" class="input-md form-control" autocomplete="off" id="start"
	                                   ng-keyup="searchOn()"/>
	                            <span class="input-group-addon" ng-bind="'TO'|translate"></span>
	                            <input type="text" class="input-md form-control" autocomplete="off" id="end"
	                                   ng-keyup="searchOn()"/>
	                        </div>
	                    </div>
                    <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
                        <button ng-click="queryDiffnetDeliveryWayContent()" type="submit" class="btn search-btn">
                            <icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span>
                        </button>
                    </div>
                </div>
            </form>
        </div>
        <div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="gotoAdd()">
                <icon class="add-iocn"></icon><span style="color:#705de1" ng-bind="'COMMON_ADD'|translate"></span>
            </button>
            <button id="exportContentInfoList" class="btn add-btn" ng-click="exportContentFile()">
                <icon class="export-icon"></icon>
                <span style="color:#705de1" ng-bind="'DETAIL_EXPORT'|translate" style="margin-left: 5px;"></span>
            </button>
            
            <button id="exportContentInfoList" class="btn add-btn" ng-click="gotoDelete()">
                <span style="color:#705de1" style="margin-left: 5px;">批量删除</span>
            </button>
             <button type="submit" class="btn add-btn" ng-click="importDiffWayType()" style="position: relative;">
	            <icon class="add-iocn"></icon>
	            <span style="color:#705de1" style="margin-left: 5px;">导入</span>
	        </button>
			<button id="exportContentInfoList" class="btn add-btn" ng-click="exportFile()">
				<icon class="export-icon"></icon>
				<span style="color:#705de1" style="margin-left: 5px;">导出失败清单</span>
			</button>
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;">
        	<p style="font-size: 16px;font-weight: 500;">模板ID列表</p>
    	</div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                <tr>
					<th style="padding-left:30px;width: 10%;"><input type="checkbox" ng-model="allChoose" ng-click="ifSelected()" id="allChoose"></th>                
                    <th style="width:20%;">模板ID</th>
                    <th style="width:20%;">企业编号</th>
                    <th style="width:20%;">企业名称</th>
                    <th style="width:20%;">创建时间</th>
                    <th style="width:20%;text-align:center;vertical-align:middle;">联通</th>
                    <th style="width:20%;text-align:center;vertical-align:middle;">电信</th>
                    <th style="width:30%;" ng-bind="'COMMON_OPERATE'|translate"></th>
                </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in contentListData">
						<td  style="padding-left:30px;width: 10%;"><input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked" ng-disabled="false"></td>
                        <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
                        <td><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
                        <td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
                        <td><span title="{{item.createTime|formatDate}}" ng-bind="item.createTime|formatDate"></span></td>
                        <td align='center' valian='middle'><span
                                title="{{wayTypeMap[item.unicomWayType] ? wayTypeMap[item.unicomWayType] : '-'}}">{{wayTypeMap[item.unicomWayType] ? wayTypeMap[item.unicomWayType] : '-'}}</span>
                        </td>
                        <td align='center' valian='middle'><span
                                title="{{wayTypeMap[item.telcomWayType] ? wayTypeMap[item.telcomWayType] : '-'}}">{{wayTypeMap[item.telcomWayType] ? wayTypeMap[item.telcomWayType] : '-'}}</span>
                        </td>

                        <td>
                            <div class="handle">
                                <ul>
                                	<!-- 编辑 -->
                                	<li ng-show = "item.status != '0' && item.status != '1'"  class="query" ng-click="gotoModify(item)">
                                        <icon class="edit-icon"></icon><span>编辑</span>
                                    </li>
                                    <!--删除-->
                                    <li ng-show = "item.status != '0' && item.status != '1'" class="delete" ng-click="gotoDelete(item)">
                                        <icon class="delete-icon"></icon><span
                                            ng-bind="'COMMON_DELETE'|translate"></span>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="contentListData.length<=0">
                        <td style="text-align:center" colspan="10" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div>
            <ptl-page tableId="0" change="queryDiffnetDeliveryWayContent('justPage')"></ptl-page>
        </div>
    </div>
	<div class="modal fade" id="addDiffnetContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     	style="overflow: auto">
	    <div class="modal-dialog" role="document">
	        <div class="modal-content">
	            <div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
	                        aria-hidden="true">&times;</span></button>
	                <h4 class="modal-title" id="myModalLabel">{{operateTitle}}</h4>
	            </div>
	            <div class="cooper-tab">
	                <form class="form-horizontal" name="myForm" novalidate>
	
	                    <div class="form-group union" style="margin-top: 10px;">
	                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
<!--	                            <icon>*</icon>-->
	                            <span>联通</span>
	                        </label>
	                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
		                        <li style="width: 80px;margin-top: 8px;font-size: 14px;" class="check-li"  ng-click="changeUnionPlatform('0')"><span class="check-btn checked-btn"> </span>东盟
	                    		</li>
								<li ng-show="false" style="width: 80px;margin-top: 8px;font-size: 14px;" class="check-li"  ng-click="changeUnionPlatform('1')"><span class="check-btn checked-btn"> </span>号百
								</li>
								<li style="width: 100px;margin-top: 8px;font-size: 14px;" class="check-li"  ng-click="changeUnionPlatform('2')"><span class="check-btn checked-btn"> </span>联通在线
								</li>
								<li style="width: 100px;margin-top: 8px;font-size: 14px;" class="check-li"  ng-click="changeUnionPlatform('3')"><span class="check-btn checked-btn"> </span>彩讯
								</li>
	                        </div>
	                    </div>
	
	                    <div class="form-group telecom">
	                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
<!--	                            <icon>*</icon>-->
	                            <span>电信</span>
	                        </label>
	                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
	                            <li style="width: 80px;margin-top: 8px;font-size: 14px;" class="check-li" ng-click="changeTelecomPlatform('0')"><span class="check-btn checked-btn"> </span>东盟
	                    		</li>
								<li style="width: 80px;margin-top: 8px;font-size: 14px;" class="check-li" ng-click="changeTelecomPlatform('1')"><span class="check-btn checked-btn"> </span>号百
	                    		</li>
								<li style="width: 80px;margin-top: 8px;font-size: 14px;" class="check-li" ng-click="changeTelecomPlatform('2')"><span class="check-btn checked-btn"> </span>彩讯
								</li>
	                        </div>
	                    </div>
	                   
	
	                    <div class="form-group" style="padding-top:5px">
	
	                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
	                            <icon>*</icon>
	                            <span>模板ID</span>
	                        </label>
	                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
	                                <input ng-disabled="operType == 1" class="form-control" type="text" name="contentID" 
												ng-model='diffnetDeliveryWayContent.contentID' 
		                           				placeholder="请输入模板ID">
	                        </div>
	                    </div>
	
	                </form>
	            </div>
	            <div class="modal-footer">
	                <!--110迭代：(!contentVali||isSensitive) 中取消 ||isSensitive -->
	                <button type="submit" ng-disabled="!diffnetDeliveryWayContent.contentID
	                || (unionArr[0] == 0 && unionArr[1] == 0 && unionArr[2] == 0 && unionArr[3] == 0
	                 && telecomArr[0] == 0 && telecomArr[1] == 0 && telecomArr[2] == 0) "
	                        class="btn btn-primary search-btn"
	                        ng-click="save()">确认</button>
	                <button type="submit" class="btn " data-dismiss="modal"
	                        aria-label="Close" id="addHotlineContentCancel">返回</button>
	            </div>
	        </div>
	    </div>
	</div>
	<!--导入名单弹出框-->
	<div class="modal fade" id="importPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_INPUTALL'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group" style="padding-bottom:0">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;" ng-bind="'COMMON_FILENAME'|translate"></label>
								<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
									<input type="text" class="form-control" ng-model="fileName" id="addGroupName" placeholder="{{'IMPORTXLSXTABLEFILE'|translate}}"
												 style="width: 100%;" ng-disabled="true" />
								</div>
								<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype" uploadifyid="uploadifyid"
																	validate="isValidate" filesize="filesize" mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl"
																	desc="uploadDesc" numlimit="numlimit" urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
								</cy:uploadifyfile>
							</div>
							<div style="color:#ff0000;margin: 0px 0 10px 100px;" ng-show="errorInfo!==''">
								<span class="uplodify-error-img"></span>
								<span ng-bind="errorInfo|translate"></span>
							</div>
							<div class="downloadRow col-sm-10" style="margin: 0 0 0 16px;">
								<a target="_blank" href="/qycy/ecpmp/assets/importDiffnetWayType.xlsx" class="downMod" style="margin-right: 40px;"
									 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
								<span style="color: #705de1 !important; font-size: 12px;" ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center;padding: 30px">
						<button type="submit" class="btn btn-primary search-btn" ng-click="importBatch()" ng-disabled="errorInfo!==''||fileUrl==''" ng-bind="'CONFIRMIMPORT'|translate"></button>
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="addOrgCancel" ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>		
	
	<div class="modal fade bs-example-modal-sm" id="deleteDiffWay" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
	    <div class="modal-dialog modal-sm" role="document">
	        <div class="modal-content" style="width:390px">
	            <div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
	                        aria-hidden="true">&times;</span></button>
	                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
	            </div>
	            <div class="modal-body">
	                <div class="text-center">
	                    <p style='font-size: 16px;color:#383838'>是否确认删除？</p>
	                </div>
	            </div>
	            <div class="modal-footer">
	                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
	                        ng-click="save()"></button>
	                <button id="deleteDiffWayCancel" type="submit" class="btn " data-dismiss="modal"
	                        aria-label="Close" ng-bind="'NO'|translate"></button>
	            </div>
	        </div>
	    </div>
	</div>
	
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <p ng-show="tip" style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p>
                    </div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
	<!-- 文件导出弹窗 -->
	<div class="modal fade bs-example-modal-sm" id="exportFile" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
					<h4 class="modal-title ng-binding" id="exportFileTitle" ng-bind="'DETAIL_EXPORT'|translate"></h4>
				</div>

				<div class="modal-body">
					<form class="form-horizontal ng-pristine ng-invalid ng-invalid-required" name="myForm" novalidate="">
						<div class="form-group">
							<label class="col-sm-2 control-label ng-binding" ng-bind="'COMMON_REMARKS'|translate"></label>

							<div class="cond-div col-sm-6"  style="padding-top:0px">
								<input type="text" class="form-control ng-pristine ng-untouched ng-invalid ng-invalid-required" placeholder="{{'COMMON_INPUT_REMARKS'|translate}}" name="remarks" ng-model="remarks" ng-change="checkEmpty()" required="">
							</div>
							<div class="cond-div col-sm-4">
							<span style="color:red" ng-show="isEmptyFileName" class="ng-hide">
								<span ng-bind="'COMMON_FILENAME_MUST_FILL'|translate" class="ng-binding"></span>
							</span>
								<span style="color:red" ng-show="isGreaterFileName" class="ng-hide">
								<span ng-bind="'COMMON_FILENAME_SIZE'|translate" class="ng-binding"></span>
							</span>
								<span style="color:red" ng-show="isSpecialCharacters" class="ng-hide">
								<span ng-bind="'SPECIAL_CHARACTERS'|translate" class="ng-binding"></span>
							</span>
							</div>
							<div class="cond-div col-sm-10">
								<span style="font-size: 10px;color: red;">导出后请到业务管理-导出文件下载对应文件</span>
<!--								<span ng-bind="fileNameSpan" style="font-size: 10px;color: red;"></span>-->
								<span style="font-size: 10px;color: red;" ng-bind="'EXPORT_TIPS'|translate"></span>

							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer">
					<button class="btn bg_purple ng-binding" ng-click="submitExportTask()" type="submit" ng-disabled="!isFileNamePass" ng-bind="'COMMON_OK'|translate"></button>
					<button class="btn  ng-binding" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	

</body>

</html>