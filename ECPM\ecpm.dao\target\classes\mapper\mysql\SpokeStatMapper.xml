<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SpokeStatMapper">
	<resultMap id="spokeStatWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.SpokeStatWrapper">
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="cityCode" column="cityCode" javaType="java.lang.String" />
		<result property="cityName" column="cityName" javaType="java.lang.String" />
		<result property="provinceCode" column="provinceCode" javaType="java.lang.String" />
		<result property="provinceName" column="provinceName" javaType="java.lang.String" />
		<result property="activityCount" column="activityCount"
			javaType="java.lang.Integer" />
		<result property="rewardCount" column="rewardCount" javaType="java.lang.Integer" />
		<result property="dayCount" column="dayCount" javaType="java.lang.Integer" />
		<result property="screenCount" column="screenCount" javaType="java.lang.Integer" />
		<result property="endPhoneCount" column="endPhoneCount"
			javaType="java.lang.Integer" />
		<result property="firstParticipateTime" column="firstParticipateTime"
			javaType="java.util.Date" />
		<result property="callingCount" column="callingCount" javaType="java.lang.Integer" />
		<result property="calledCount" column="calledCount" javaType="java.lang.Integer" />
		<result property="gjdxCount" column="gjdxCount" javaType="java.lang.Integer" />
		<result property="gjcxCount" column="gjcxCount" javaType="java.lang.Integer" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
	</resultMap>

	<update id="updateSpokeStat">
		update ecpm_t_spoke_stat set
		<trim suffixOverrides="," suffix="where msisdn = #{msisdn} ">
			<if test="dayCount!=null">
				dayCount= dayCount - #{dayCount},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime}
			</if>
		</trim>
	</update>
	
	<update id="updateSpokeStatAddDayCount">
		update ecpm_t_spoke_stat t1 set
		<trim suffixOverrides="," suffix="where msisdn = #{msisdn} ">
			<if test="dayCount!=null">
				dayCount= dayCount + #{dayCount},
			</if>
			<if test="updateTime!=null">
				updateTime= #{updateTime},
			</if>
			activityCount= (  
				case when (select count(1) from ecpm_t_spoke_activity_stat t2
						where t2.activityID=#{activityID} and msisdn = #{msisdn}
					) =0 then t1.activityCount + 1
				else activityCount end
			)
		</trim>
	</update>

	<!--查询代言统计 -->
	<select id="querySpokeStatList" resultMap="spokeStatWrapper">
		select
		t.msisdn,
		t1.cityCode,
		t1.cityName,
		t1.provinceCode,
		t1.provinceName,
		t.activityCount,
		t.rewardCount,
		t.dayCount,
		t.screenCount,
		t.endPhoneCount,
		t.firstParticipateTime,
		t.callingCount,
		t.calledCount,
		t.gjdxCount,
		t.gjcxCount,
		t.updateTime,
		t.operatorID
		from ecpm_t_spoke_stat t,ecpm_t_city t1
		<trim prefix="where" prefixOverrides="and|or">
			t.cityCode = t1.cityCode
			<if test="msisdn != null and msisdn != ''">
				and t.msisdn like #{msisdn}
			</if>
			<if test="enterpriseDataAuthList !=null and enterpriseDataAuthList.size>0">
				and t1.provinceCode in
				<foreach item="provinceCode" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
					#{provinceCode}
				</foreach>
			</if>
		</trim>
		order by t.updateTime desc,t.msisdn desc
		limit
		#{pageNo},#{pageSize}
	</select>

	<!--查询代言统计数量 -->
	<select id="countSpokeStat" resultType="java.lang.Integer">
		select
		count(1)
		from ecpm_t_spoke_stat  t,ecpm_t_city t1
		<trim prefix="where" prefixOverrides="and|or">
		t.cityCode = t1.cityCode
			<if test="msisdn != null and msisdn != ''">
				and t.msisdn like #{msisdn}
			</if>
			<if test="enterpriseDataAuthList !=null and enterpriseDataAuthList.size>0">
				and t1.provinceCode in
				<foreach item="provinceCode" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
					#{provinceCode}
				</foreach>
			</if>
		</trim>
	</select>

	<insert id="insertSpokeStat">
		INSERT INTO ecpm_t_spoke_stat
		(
		msisdn,
        cityCode,
        activityCount,
        rewardCount,
     	dayCount,
     	screenCount,
       	endPhoneCount,
        firstParticipateTime,
        callingCount,
        calledCount,
        gjdxCount,
        gjcxCount,
        updateTime,
        operatorID
		)
		VALUES
		(
		#{msisdn},
		#{cityCode},
		#{activityCount},
		#{rewardCount},
		#{dayCount},
		#{screenCount},
		#{endPhoneCount},
		#{firstParticipateTime},
		#{callingCount},
		#{calledCount},
		#{gjdxCount},
		#{gjcxCount},
		#{updateTime},
		#{operatorID}
		)
	</insert>
	
	<update id="batchUpdateSpokeStat" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="spokeStatWrapper" open="" separator=";">
			update ecpm_t_spoke_stat set
			rewardCount = rewardCount + 1,
			updateTime= #{spokeStatWrapper.updateTime}
			where msisdn =#{spokeStatWrapper.msisdn}
		</foreach>
	</update>

	<update id="batchUpdateSpokeStatAmount" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index" item="spokeStatWrapper"
			open="" separator=";">
			update ecpm_t_spoke_stat set
			<if test="spokeStatWrapper.screenCount!=null">
			   screenCount =  IFNULL(screenCount, 0) + #{spokeStatWrapper.screenCount},
			</if>
			<if test="spokeStatWrapper.endPhoneCount!=null">			
			   endPhoneCount = IFNULL(endPhoneCount, 0)+ #{spokeStatWrapper.endPhoneCount},
			</if>
			updateTime=	#{spokeStatWrapper.updateTime}
			where msisdn =#{spokeStatWrapper.msisdn}
		</foreach>
	</update>
	
	<update id="batchUpdateSpokeStatDayCount" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index" item="spokeStatWrapper"
			open="" separator=";">
			update ecpm_t_spoke_stat 
			set
			dayCount = IFNULL(dayCount, 0) + 1,
			updateTime = #{spokeStatWrapper.updateTime}
			where 
			msisdn = #{spokeStatWrapper.msisdn}
		</foreach>
	</update>
</mapper>