<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css">
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/mian.css" rel="stylesheet"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
    <link href="../../../../../css/layout.css" rel="stylesheet"/>
    <link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
    <link href="../../../../../css/createOrder.css" rel="stylesheet">
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <!--引入JS-->

    <script type="text/javascript" src="createQuota.js"></script>

    <style>
        .upload div {
            padding-top: 0px;
        }

        .webuploader-pick {
            padding: 10px 12px !important;
        }

        .check-li span {
            vertical-align: middle;
            cursor: pointer;
        }

        .min-width-li {
            min-width: 130px;
        }

        .min-input {
            min-width: 230px;
        }

        .error-ver img, .error-ver > span {
            vertical-align: middle;
        }
    </style>
</head>

<body>
<div ng-app="myApp" ng-init="init();" ng-controller="orderController" class="body-min-width">
    <div class="cooperation-manage">
        <div class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate" ng-show="isSuperManager"></span>
            <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate" ng-show="isAgent"></span>
            &nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'COMMON_QUOTAMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'ADD_QUOTA'|translate"></span>
        </div>

        <div class="cooper-title" ng-bind="'CREATEORDER_ORDERINFO'|translate"></div>

        <div class="cooper-tab">
            <form class="form-horizontal" name="orderBase" novalidate>
                <div class="form-group">
                    <div>
                        <!--企业名称-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"
                               ng-bind="'CREATEORDER_ENTERPRISENAME'|translate"></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3"><p ng-bind="subEnterpriseName"></p></div>

                        <!--订单选择-->
                        <label for="servType" class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">订单选择：</label>

                        <div class="col-lg-3 col-xs-4 col-sm-3 col-md-3">
                            <select id="servType" class="form-control" ng-model="selectedOrder"
                                    ng-options="x as x.orderName for x in orderList"
                                    ng-change="showPeiE(selectedOrder)"></select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div>
                        <!--是否体验版-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_EXPERIENCE'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">
                            <span ng-show="selectedOrder.isExperience===0" ng-bind="'NO'|translate"></span>
                            <span ng-show="selectedOrder.isExperience===1" ng-bind="'YES'|translate"></span>
                        </div>

                        <!--业务类别-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_SERVTYPE'|translate"></span>
                        </label>

                        <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                            <span ng-show="selectedOrder.servType===1"
                                  ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>
                            <span ng-show="selectedOrder.servType===2"
                                  ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></span>
                            <span ng-show="selectedOrder.servType===3"
                                  ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
                            <span ng-show="selectedOrder.servType===4"
                                  ng-bind="'ZENGCAIQUNFA'|translate"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div class="row">
                        <!--订单金额-->
                        <label for="amount" class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CREATEORDER_AMOUNT'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">
                            <input type="text" class="form-control" autocomplete="off"
                                   placeholder="{{'CREATEORDER_INPUTAMOUNT'|translate}}" name="amount" id="amount"
                                   ng-model="amount" required
                                   pattern="(^[0-9]{1,17}$)|(^[0-9]{1,17}[\.]{1}[0-9]{1,3}$)">
                            <span style="color:red" ng-show="orderBase.amount.$dirty && orderBase.amount.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                         align="absmiddle">
									<span ng-show="orderBase.amount.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderBase.amount.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXAMOUNT'|translate"></span>
								</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- 业务配额代码 -->
        <div class="cooper-title">
            <span class="red">*</span>
            <span ng-bind="'CREATEORDER_ORDERITEMLIST'|translate"></span>
            <span style="color:red" ng-show="!(cmcc || cucc || ctcc)
				||(servType !='4' && ((!(cmcc && (postPingXianCMCC || postGuaCai || postGJZC || postGuaDuan)) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)))
				|| (servType =='3'&& isExperience == 1 && !(cmcc && postPingXianCMCC))
				||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))
                ">
					<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                         style="vertical-align: middle">
					<span ng-bind="'CREATEORDER_MININPUTDESC'|translate"
                          style="vertical-align: middle;font-size: 14px"></span>
				</span>
        </div>
        <div class="cooper-tab">
            <form class="form-horizontal" name="orderItemDomain" novalidate>
                <div ng-show="hasCMCC">
                    <div class="form-group">
                        <div class="row">
                            <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li" ng-click="chooseCMCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cmcc]"></span>
                                <span>移动</span>：
                            </li>
                        </div>
                    </div>
                    <div ng-show="cmcc===true" style="margin-left: 40px">
                        <!--屏显配额-->
                        <div ng-show="hasPX &&  servType !='4'">
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showPingXian()" style="display: inline-block">
                                        <span class="checked-btn checked"
                                              ng-class="{true:'checked',false:''}[postPingXianCMCC]"></span>
                                        <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"
                                              ng-show="selectedOrder.servType !=3"></span>
                                        <!--<span ng-bind="'CREATEORDER_GUANGGAOPEIE'|translate"-->
                                        <!--ng-show="selectedOrder.servType ==3"></span> -->
                                        <span ng-bind="'CREATEORDER_GUANGGAOANDPEIE'|translate"
                                              ng-show="selectedOrder.servType ==3"></span>
                                    </div>
                                </div>
                            </div>

                            <div ng-show="postPingXianCMCC">
                                <div class="form-group" ng-show="isPXLimit&&servType !='3'">
                                    <!--不限-->
                                    <div ng-click="changePingXianType(0)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[pxType===0]"></span>
                                        <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                    </div>
                                </div>
                                
                                <div class="form-group" ng-show="isPXanci">
                                    <!--按次-->
                                    <div ng-click="changePingXianType(1)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[pxType===1]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                        <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                             ng-show="pxType===1 && postPingXianCMCC && selectedOrder.isExperience===0">
                                            <span ng-bind="PXPrice" style="margin-left: 15px"></span>
                                            <span ng-bind="'COMMON_YUAN'|translate"></span>
                                        </div>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{px_anciRest_tip}}" ng-disabled="pxType!==1" name="anci"
                                               ng-model="anci" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(PXPrice,anci,pinxian_amount,'pxanci')">
                                        <span style="color:red" class="error-ver"
                                              ng-show="((orderItemDomain.anci.$dirty && orderItemDomain.anci.$invalid) || px_anci_over_error) && pxType===1 && postPingXianCMCC">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.anci.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.anci.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="px_anci_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-xs-1"
                                         ng-show="pxType===1 && postPingXianCMCC && selectedOrder.isExperience===0"
                                         style="white-space: nowrap">
                                        <span ng-bind="pxanci_price||0"></span><span
                                            ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                                       ng-bind="'CREATEORDER_DESC3'|translate" ng-show="selectedOrder.servType ==3"></p>
                                </div>

                                <div class="form-group" ng-show="isPXbaoyue&&servType !='3'">
                                    <!--按人/包月-->
                                    <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
										<span class="redio-btn" id="pingXianType3"
                                              ng-class="{true:'checked',false:''}[pxType===2]"></span>
                                        <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               ng-keyup="accMul(baoyue_unitPrice,baoyue,memberCount,'baoyue')"
                                               placeholder="{{baoyueRest_tip}}" ng-disabled="pxType!==2" name="baoyue"
                                               ng-model="baoyue" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="((orderItemDomain.baoyue.$dirty && orderItemDomain.baoyue.$invalid) || baoyue_over_error) && pxType===2 && postPingXianCMCC">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20"
                                                     align="absmiddle">
												<span ng-show="orderItemDomain.baoyue.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.baoyue.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="baoyue_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="check-li col-xs-1" style="min-width: 100px;max-width: 100px">
                                        <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                                    </div>
                                    <div class="col-xs-2" ng-bind="productName" style="max-width: 130px"></div>
                                    <div class="col-xs-1" ng-show="selectedOrder.isExperience===0">
                                        <span ng-bind="baoyue_price"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div ng-show="hasGD&&servType !='3'">
                            <!--挂机短信-->
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showGuaDuan()" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[postGuaDuan]"></span>
                                        <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div ng-show="postGuaDuan">
                                <div class="form-group" ng-show="isGDLimit">
                                    <!--不限-->
                                    <div ng-click="changeGuaDuanType(0)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[gdType===0]"></span>
                                        <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                    </div>
                                </div>
                                <div class="form-group" ng-show="isGDanci">
                                    <!--按次-->
                                    <div ng-click="changeGuaDuanType(1)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[gdType===1]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                        <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                             ng-show="gdType===1 && postGuaDuan && selectedOrder.isExperience===0">
                                            <span ng-bind="GDPrice" style="margin-left: 15px"></span>
                                            <span ng-bind="'COMMON_YUAN'|translate"></span>
                                        </div>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{guaduan_anciRest_tip}}" ng-disabled="gdType!==1"
                                               name="gdanci"
                                               ng-model="gdanci" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(GDPrice,gdanci,guaduan_amount,'gdanci')">
                                        <span style="color:red" class="error-ver"
                                              ng-show="((orderItemDomain.gdanci.$dirty && orderItemDomain.gdanci.$invalid) || gd_anci_over_error) && gdType===1 && postGuaDuan">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.gdanci.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.gdanci.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="gd_anci_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-xs-1"
                                         ng-show="gdType===1 && postGuaDuan && selectedOrder.isExperience===0"
                                         style="white-space: nowrap">
                                        <span ng-bind="gdanci_price||0"></span><span
                                            ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                                <div class="form-group" ng-show="isGDbaoyue&&servType =='1'">
                                    <!--按人/包月-->
                                    <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
										<span class="redio-btn" id="guaDuanType3"
                                              ng-class="{true:'checked',false:''}[gdType===2]"></span>
                                        <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               ng-keyup="accMul(gdbaoyue_unitPrice,gdbaoyue,gdmemberCount,'gdbaoyue')"
                                               placeholder="{{gdbaoyueRest_tip}}" ng-disabled="gdType!==2"
                                               name="gdbaoyue"
                                               ng-model="gdbaoyue" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="((orderItemDomain.gdbaoyue.$dirty && orderItemDomain.gdbaoyue.$invalid) || gdbaoyue_over_error) && gdType===2 && postGuaDuan">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20"
                                                     align="absmiddle">
												<span ng-show="orderItemDomain.gdbaoyue.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.gdbaoyue.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="gdbaoyue_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="check-li col-xs-1" style="min-width: 100px;max-width: 100px">
                                        <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                                    </div>
                                    <div class="col-xs-2" ng-bind="gdproductName" style="max-width: 130px"></div>
                                    <div class="col-xs-1" ng-show="selectedOrder.isExperience===0">
                                        <span ng-bind="gdbaoyue_price"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div ng-show="hasGC&&servType !='3' && servType !='4'">
                            <!--挂机彩信-->
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showGuaCai()" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[postGuaCai]"></span>
                                        <span ng-bind="'CREATEORDER_GUACAI'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div ng-show="postGuaCai">
                                <div class="form-group" ng-show="isGCLimit">
                                    <!--不限-->
                                    <div ng-click="changeGuaCaiType(0)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[gcType===0]"></span>
                                        <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                    </div>
                                </div>
                                <div class="form-group" ng-show="isGCanci">
                                    <!--按次-->
                                    <div ng-click="changeGuaCaiType(1)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[gcType===1]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                        <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                             ng-show="gcType===1 && postGuaCai && selectedOrder.isExperience===0">
                                            <span ng-bind="GCPrice" style="margin-left: 15px"></span>
                                            <span ng-bind="'COMMON_YUAN'|translate"></span>
                                        </div>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{guacai_anciRest_tip}}" ng-disabled="gcType!==1"
                                               name="gcanci"
                                               ng-model="gcanci" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(GCPrice,gcanci,guacai_amount,'gcanci')">
                                        <span style="color:red" class="error-ver"
                                              ng-show="((orderItemDomain.gcanci.$dirty && orderItemDomain.gcanci.$invalid) || gc_anci_over_error) && gcType===1 && postGuaCai">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.gcanci.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.gcanci.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="gc_anci_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-xs-1"
                                         ng-show="gcType===1 && postGuaCai && selectedOrder.isExperience===0"
                                         style="white-space: nowrap">
                                        <span ng-bind="gcanci_price||0"></span><span
                                            ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                                <div class="form-group" ng-show="isGCbaoyue&&servType =='1'">
                                    <!--按人/包月-->
                                    <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
										<span class="redio-btn" id="guaCaiType3"
                                              ng-class="{true:'checked',false:''}[gcType===2]"></span>
                                        <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               ng-keyup="accMul(gcbaoyue_unitPrice,gcbaoyue,gcmemberCount,'gcbaoyue')"
                                               placeholder="{{gcbaoyueRest_tip}}" ng-disabled="gcType!==2"
                                               name="gcbaoyue"
                                               ng-model="gcbaoyue" required pattern="^[0-9]{1,9}$">
                                        <span style="color:red"
                                              ng-show="((orderItemDomain.gcbaoyue.$dirty && orderItemDomain.gcbaoyue.$invalid) || gcbaoyue_over_error) && gcType===2 && postGuaCai">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20"
                                                     align="absmiddle">
												<span ng-show="orderItemDomain.gcbaoyue.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.gcbaoyue.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="gcbaoyue_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="check-li col-xs-1" style="min-width: 100px;max-width: 100px">
                                        <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                                    </div>
                                    <div class="col-xs-2" ng-bind="gcproductName" style="max-width: 130px"></div>
                                    <div class="col-xs-1" ng-show="selectedOrder.isExperience===0">
                                        <span ng-bind="gcbaoyue_price"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <!-- 挂机增彩 -->
                        <div ng-show="hasGJZC&&servType == '2'">
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showGJZC()" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[postGJZC]"></span>
                                        <span ng-bind="'CREATEORDER_ZENGCAI'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div ng-show="postGJZC">
                                <div class="form-group" ng-show="isGJZCLimit">
                                    <!--不限-->
                                    <div ng-click="changeGJZCType(0)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[gjzcType===0]"></span>
                                        <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                    </div>
                                </div>
                                <div class="form-group" ng-show="isGJZCanci">
                                    <!--按次-->
                                    <div ng-click="changeGJZCType(1)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[gjzcType===1]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                        <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                             ng-show="gjzcType===1 && postGJZC && selectedOrder.isExperience===0">
                                            <span ng-bind="GJZCPrice" style="margin-left: 15px"></span>
                                            <span ng-bind="'COMMON_YUAN'|translate"></span>
                                        </div>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{gjzc_anciRest_tip}}" ng-disabled="gjzcType!==1"
                                               name="gjzcanci"
                                               ng-model="gjzcanci" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(GJZCPrice,gjzcanci,gjzc_amount,'gjzcanci')">
                                        <span style="color:red" class="error-ver"
                                              ng-show="((orderItemDomain.gjzcanci.$dirty && orderItemDomain.gjzcanci.$invalid) || gjzc_anci_over_error) && gjzcType===1 && postGJZC">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.gjzcanci.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.gjzcanci.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="gjzc_anci_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-xs-1"
                                         ng-show="gjzcType===1 && postGJZC && selectedOrder.isExperience===0"
                                         style="white-space: nowrap">
                                        <span ng-bind="gjzcanci_price||0"></span><span
                                            ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!--群发屏显-->
                        <div class="form-group" ng-show="hasGroupScreenCMCC && servType =='4'">
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showEnable('showGroupScreen')" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[showGroupScreen]"></span>
                                        <span >屏显:</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row"  ng-show="showGroupScreen">
                                <!--不限-->
                                <li ng-show="isgroupScreenCMCCNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li">
									<span class="check-btn redio-btn" id="ScreenType4"
                                          ng-class="{true:'checked',false:''}[isgroupScreenCMCCNoLimit]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc" style="margin-left:30px"></span>
                                </li>
                                <!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li ng-show="!isgroupScreenCMCCNoLimit" class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="ScreenType3"
                                          ng-class="{true:'checked',false:''}[!isgroupScreenCMCCNoLimit]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>

                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupScreenCMCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="white-space: nowrap;">
                                    <span ng-bind="groupScreenCMCCPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-3 input-min-width error-ver" ng-show="!isgroupScreenCMCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{groupSendScreenCMCC_tip}}"
                                           name="groupScreenCMCCanci"
                                           ng-model="groupSendScreenCMCCanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="(orderItemDomain.groupScreenCMCCanci.$dirty && orderItemDomain.groupScreenCMCCanci.$invalid)||groupSendScreenCMCC_over_error">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupScreenCMCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
                                        <span ng-show="groupSendScreenCMCC_over_error"
                                              ng-bind="'GO_BEYOND'|translate"></span>
										<span ng-show="orderItemDomain.groupScreenCMCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupScreenCMCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(groupScreenCMCCPrice,groupSendScreenCMCCanci,groupScreenCMCC_amount,'gsScreenCMCC')||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                        
                        <!--彩信-->
                        <div class="form-group" ng-show="hasCX && servType =='4'">
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showCX()" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[postCX]"></span>
                                        <span ng-bind="'CAIXINSERVICE'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row" ng-show="postCX">
                                <!--不限-->
                                <li  ng-show="isCXNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li" style="padding-top: 7px;">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[cxType===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </li>
                                <div class="col-lg-10 col-xs-10"></div>
                                <div ng-show="isCXanci">
                                    <!--按次-->
                                    <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li" style="padding-top: 7px;">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[cxType===0]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="cxType===0" style="white-space: nowrap;">
                                        <span ng-bind="cxPrice"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <div class="col-xs-3 input-min-width error-ver">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{cx_anciRest_tip}}"  name="cxanci"
                                               ng-model="cxanci" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(cxPrice,cxanci,cx_amount,'cxanci')">
                                        <span style="color:red"
                                              ng-show="((orderItemDomain.cxanci.$dirty && orderItemDomain.cxanci.$invalid) || cx_anci_over_error)">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.cxanci.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.cxanci.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="cx_anci_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" style="padding-top: 7px;white-space: nowrap" ng-show="cxType===0">
                                        <span ng-bind="cxanci_price||0" ></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <!--<div class="col-xs-2 control-left-label" ng-bind="'COMMON_TIAO'|translate"></div>-->
                                </div>
                            </div>

                        </div>
                        <!--增彩-->
                        <div class="form-group" ng-show="hasZC && servType =='4'">
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showZC()" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[postZC]"></span>
                                        <span ng-bind="'ZENGCAI'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row" ng-show="postZC">
                                <!--不限-->
                                <li  ng-show="isZCNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li" style="padding-top: 7px;">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[zcType===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </li>
                                <div class="col-lg-10 col-xs-10"></div>
                                <div ng-show="isZCanci">
                                    <!--按次-->
                                    <li class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li" style="padding-top: 7px;">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[zcType===0]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    </li>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="zcType===0" style="white-space: nowrap;">
                                        <span ng-bind="ZCPrice"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <div class="col-xs-3 input-min-width error-ver">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{zc_anciRest_tip}}"  name="zcanci"
                                               ng-model="zcanci" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(ZCPrice,zcanci,zengcai_amount,'zcanci')">
                                        <span style="color:red"
                                              ng-show="((orderItemDomain.zcanci.$dirty && orderItemDomain.zcanci.$invalid) || zc_anci_over_error)">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.zcanci.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.zcanci.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="zc_anci_over_error"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" style="padding-top: 7px;white-space: nowrap" ng-show="zcType===0">
                                        <span ng-bind="zcanci_price||0" ></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    <!--<div class="col-xs-2 control-left-label" ng-bind="'COMMON_TIAO'|translate"></div>-->
                                </div>
                            </div>

                        </div>

						<!--群发短信-->
                        <div class="form-group" ng-show="hasGroupSMSCMCC && servType =='4'">
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showEnable('showGroupSMS')" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[showGroupSMS]"></span>
                                        <span >短信:</span>
                                    </div>
                                </div>
                            </div>
                            <div class="row"  ng-show="showGroupSMS">
                                <!--不限-->
                                <li ng-show="isgroupSMSCMCCNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li">
									<span class="check-btn redio-btn" id="smsType4"
                                          ng-class="{true:'checked',false:''}[isgroupSMSCMCCNoLimit]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc" style="margin-left:30px"></span>
                                </li>
                                <!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li ng-show="!isgroupSMSCMCCNoLimit" class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="smsType3"
                                          ng-class="{true:'checked',false:''}[!isgroupSMSCMCCNoLimit]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>

                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupSMSCMCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="white-space: nowrap;">
                                    <span ng-bind="groupSMSCMCCPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-3 input-min-width error-ver" ng-show="!isgroupSMSCMCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{groupSendSMSCMCC_tip}}"
                                           name="groupSMSCMCCanci"
                                           ng-model="groupSendSMSCMCCanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="(orderItemDomain.groupSMSCMCCanci.$dirty && orderItemDomain.groupSMSCMCCanci.$invalid)||groupSendSMSCMCC_over_error">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
										<span ng-show="orderItemDomain.groupSMSCMCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
                                        <span ng-show="groupSendSMSCMCC_over_error"
                                              ng-bind="'GO_BEYOND'|translate"></span>
										<span ng-show="orderItemDomain.groupSMSCMCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>
									</span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupSMSCMCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(groupSMSCMCCPrice,groupSendSMSCMCCanci,groupSMSCMCC_amount,'gsSMSCMCC')||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    
                    </div>
                </div>
                <!--联通-->
                <div ng-show="hasCUCC">
                    <div class="form-group">
                        <div class="row">
                            <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li" ng-click="chooseCUCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[cucc]"></span>
                                <span>联通</span>：
                            </li>
                        </div>
                    </div>
                    
                    <!--屏显配额-->
                    <div ng-show="cucc===true &&  servType !='4'" style="margin-left: 40px">
                        <div class="form-group" ng-show="hasPXCUCC">
                            <div class="check-li col-xs-12">
                                <div ng-click="showPingXianCUCC()" style="display: inline-block">
                                    <span class="checked-btn checked"
                                          ng-class="{true:'checked',false:''}[postPingXianCUCC]"></span>
                                    <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="postPingXianCUCC">
                            <div class="form-group" ng-show="isPXLimit_cucc&&servType !='3'">
                                <!--不限-->
                                <div ng-click="changePingXianTypeCUCC(0)"
                                     class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[pxType_cucc===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>
                            <div class="form-group" ng-show="isPXanci_cucc">
                                <!--按次-->
                                <div ng-click="changePingXianTypeCUCC(1)"
                                     class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn"
                                          ng-class="{true:'checked',false:''}[pxType_cucc===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                         ng-show="pxType_cucc===1 && postPingXianCUCC && selectedOrder.isExperience===0">
                                        <span ng-bind="PXPrice_cucc" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                    
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{px_anciRest_tip_cucc}}" ng-disabled="pxType_cucc!==1"
                                           name="anci_cucc"
                                           ng-model="anci_cucc" required pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(PXPrice_cucc,anci_cucc,pinxian_amount_cucc,'pxanci_cucc')">
                                    <span style="color:red" class="error-ver"
                                          ng-show="((orderItemDomain.anci_cucc.$dirty && orderItemDomain.anci_cucc.$invalid) || px_anci_over_error_cucc) && pxType_cucc===1 && postPingXianCUCC">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
										<span ng-show="orderItemDomain.anci_cucc.$error.required"
                                              ng-bind="'REQUIRED'|translate"></span>
										<span ng-show="orderItemDomain.anci_cucc.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										<span ng-show="px_anci_over_error_cucc" ng-bind="'GO_BEYOND'|translate"></span>
									</span>
                                </div>
                                <div class="col-xs-1"
                                     ng-show="pxType_cucc===1 && postPingXianCUCC && selectedOrder.isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="pxanci_price_cucc||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <!--add by hyj top-->
                        <div ng-show="hasGDCUCC&&servType =='2'">
                            <!--挂机短信-->
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showGuaDuanCUCC()" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[postGuaDuanCUCC]"></span>
                                        <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div ng-show="postGuaDuanCUCC">
                                <div class="form-group" ng-show="isGDLimitCUCC">
                                    <!--不限-->
                                    <div ng-click="changeGuaDuanTypeCUCC(0)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[gdTypeCUCC===0]"></span>
                                        <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                    </div>
                                </div>
                                <div class="form-group" ng-show="isGDanciCUCC">
                                    <!--按次-->
                                    <div ng-click="changeGuaDuanTypeCUCC(1)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[gdTypeCUCC===1]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                        <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                             ng-show="gdTypeCUCC===1 && postGuaDuanCUCC && selectedOrder.isExperience===0">
                                            <span ng-bind="GDPriceCUCC" style="margin-left: 15px"></span>
                                            <span ng-bind="'COMMON_YUAN'|translate"></span>
                                        </div>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{guaduan_anciRest_tipCUCC}}" ng-disabled="gdTypeCUCC!==1"
                                               name="gdanciCUCC"
                                               ng-model="gdanciCUCC" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(GDPriceCUCC,gdanciCUCC,guaduan_amountCUCC,'gdanciCUCC')">
                                        <span style="color:red" class="error-ver"
                                              ng-show="((orderItemDomain.gdanciCUCC.$dirty && orderItemDomain.gdanciCUCC.$invalid) || gd_anci_over_errorCUCC) && gdTypeCUCC===1 && postGuaDuanCUCC">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.gdanciCUCC.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.gdanciCUCC.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="gd_anci_over_errorCUCC"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-xs-1"
                                         ng-show="gdTypeCUCC===1 && postGuaDuanCUCC && selectedOrder.isExperience===0"
                                         style="white-space: nowrap">
                                        <span ng-bind="gdanci_priceCUCC||0"></span><span
                                            ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <!--add by hyj end-->
                    </div>

					<!--群发屏显-->
                    <div  class="form-group" style="margin-left: 40px;" ng-show="hasGroupScreenCUCC && servType =='4' && cucc===true">
                        <div class="form-group">
                         <div class="check-li col-xs-12">
                             <div ng-click="showEnable('showGroupScreenCUCC')" style="display: inline-block">
                                  <span class="checked-btn checked"
                                        ng-class="{true:'checked',false:''}[showGroupScreenCUCC]"></span>
                                 <span >屏显:</span>
                             </div>
                         </div>
                     	</div>
                        <div class="row" ng-show="showGroupScreenCUCC">
                                <!--不限-->
                                <li ng-show="isgroupScreenCUCCNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li">
									<span class="check-btn redio-btn" id="ScreenType4"
                                          ng-class="{true:'checked',false:''}[isgroupScreenCUCCNoLimit]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc" style="margin-left:30px"></span>
                                </li>
								<!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li ng-show="!isgroupScreenCUCCNoLimit" class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="ScreenType3"
                                          ng-class="{true:'checked',false:''}[!isgroupScreenCUCCNoLimit]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupScreenCUCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="white-space: nowrap;">
                                    <span ng-bind="groupScreenCUCCPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-3 input-min-width error-ver" ng-show="!isgroupScreenCUCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{groupSendScreenCUCC_tip}}"
                                           name="groupSendScreenCUCCanci"
                                           ng-model="groupSendScreenCUCCanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="(orderItemDomain.groupSendScreenCUCCanci.$dirty && orderItemDomain.groupSendScreenCUCCanci.$invalid)||groupSendScreenCUCC_over_error">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
                                        <span ng-show="orderItemDomain.groupSendScreenCUCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
                                        <span ng-show="orderItemDomain.groupSendScreenCUCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>

                                         <span ng-show="groupSendSMSCUCC_over_error"
                                               ng-bind="'GO_BEYOND'|translate"></span>
                                    </span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupScreenCUCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(groupScreenCUCCPrice,groupSendScreenCUCCanci,groupScreenCUCC_amount,'gsScreenCUCC')||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>

                        </div>
                    </div>
					
                    <!--群发短信-->
                    <div  class="form-group" style="margin-left: 40px;" ng-show="hasGroupSMSCUCC && servType =='4' && cucc===true">
                        <div class="form-group">
                         <div class="check-li col-xs-12">
                             <div ng-click="showEnable('showGroupSMSCUCC')" style="display: inline-block">
                                  <span class="checked-btn checked"
                                        ng-class="{true:'checked',false:''}[showGroupSMSCUCC]"></span>
                                 <span >短信:</span>
                             </div>
                         </div>
                     	</div>
                        <div class="row" ng-show="showGroupSMSCUCC">
                                <!--不限-->
                                <li ng-show="isgroupSMSCUCCNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li">
									<span class="check-btn redio-btn" id="smsType4"
                                          ng-class="{true:'checked',false:''}[isgroupSMSCUCCNoLimit]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc" style="margin-left:30px"></span>
                                </li>
								<!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li ng-show="!isgroupSMSCUCCNoLimit" class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="smsType3"
                                          ng-class="{true:'checked',false:''}[!isgroupSMSCUCCNoLimit]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupSMSCUCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="white-space: nowrap;">
                                    <span ng-bind="groupSMSCUCCPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-3 input-min-width error-ver" ng-show="!isgroupSMSCUCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{groupSendSMSCUCC_tip}}"
                                           name="groupSendSMSCUCCanci"
                                           ng-model="groupSendSMSCUCCanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="(orderItemDomain.groupSendSMSCUCCanci.$dirty && orderItemDomain.groupSendSMSCUCCanci.$invalid)||groupSendSMSCUCC_over_error">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
                                        <span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
                                        <span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>

                                         <span ng-show="groupSendSMSCUCC_over_error"
                                               ng-bind="'GO_BEYOND'|translate"></span>
                                    </span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupSMSCUCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(groupSMSCUCCPrice,groupSendSMSCUCCanci,groupSMSCUCC_amount,'gsSMSCUCC')||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>

                        </div>
                    </div>
                    
                    
                </div>
                <!--电信-->
                <div ng-show="hasCTCC">
                    <div class="form-group">
                        <div class="row">
                            <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li" ng-click="chooseCTCC()">
                                <span class="check-btn checked-btn" ng-class="{true:'checked',false:''}[ctcc]"></span>
                                <span>电信</span>：
                            </li>
                        </div>
                    </div>
                    <!--屏显配额-->
                    <div ng-show="ctcc===true &&  servType !='4'" style="margin-left: 40px">
                        <div class="form-group" ng-show="hasPXCTCC">
                            <div class="check-li col-xs-12">
                                <div ng-click="showPingXianCTCC()" style="display: inline-block">
                                    <span class="checked-btn checked"
                                          ng-class="{true:'checked',false:''}[postPingXianCTCC]"></span>
                                    <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="postPingXianCTCC">
                            <div class="form-group" ng-show="isPXLimit_ctcc&&servType !='3'">
                                <!--不限-->
                                <div ng-click="changePingXianTypeCTCC(0)"
                                     class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[pxType_ctcc===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>
                            <div class="form-group" ng-show="isPXanci_ctcc">
                                <!--按次-->
                                <div ng-click="changePingXianTypeCTCC(1)"
                                     class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn"
                                          ng-class="{true:'checked',false:''}[pxType_ctcc===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                         ng-show="pxType_ctcc===1 && postPingXianCTCC && selectedOrder.isExperience===0">
                                        <span ng-bind="PXPrice_ctcc" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{px_anciRest_tip_ctcc}}" ng-disabled="pxType_ctcc!==1"
                                           name="anci_ctcc"
                                           ng-model="anci_ctcc" required pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(PXPrice_ctcc,anci_ctcc,pinxian_amount_ctcc,'pxanci_ctcc')">
                                    <span style="color:red" class="error-ver"
                                          ng-show="((orderItemDomain.anci_ctcc.$dirty && orderItemDomain.anci_ctcc.$invalid) || px_anci_over_error_ctcc) && pxType_ctcc===1 && postPingXianCTCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="orderItemDomain.anci_ctcc.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.anci_ctcc.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
											<span ng-show="px_anci_over_error_ctcc"
                                                  ng-bind="'GO_BEYOND'|translate"></span>
										</span>
                                </div>
                                <div class="col-xs-1"
                                     ng-show="pxType_ctcc===1 && postPingXianCTCC && selectedOrder.isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="pxanci_price_ctcc||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <!--20191107 top-->
                        <!--add by hyj top-->
                        <div ng-show="hasGDCTCC&&servType =='2'">
                            <!--挂机短信-->
                            <div class="form-group">
                                <div class="check-li col-xs-12">
                                    <div ng-click="showGuaDuanCTCC()" style="display: inline-block">
                                         <span class="checked-btn checked"
                                               ng-class="{true:'checked',false:''}[postGuaDuanCTCC]"></span>
                                        <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                    </div>
                                </div>
                            </div>
                            <div ng-show="postGuaDuanCTCC">
                                <div class="form-group" ng-show="isGDLimitCTCC">
                                    <!--不限-->
                                    <div ng-click="changeGuaDuanTypeCTCC(0)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn checked"
                                              ng-class="{true:'checked',false:''}[gdTypeCTCC===0]"></span>
                                        <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                    </div>
                                </div>
                                <div class="form-group" ng-show="isGDanciCTCC">
                                    <!--按次-->
                                    <div ng-click="changeGuaDuanTypeCTCC(1)"
                                         class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                        <span class="redio-btn" ng-class="{true:'checked',false:''}[gdTypeCTCC===1]"></span>
                                        <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                        <div style="display: inline-block;padding-top: 0; position: absolute;right: 0;"
                                             ng-show="gdTypeCTCC===1 && postGuaDuanCTCC && selectedOrder.isExperience===0">
                                            <span ng-bind="GDPriceCTCC" style="margin-left: 15px"></span>
                                            <span ng-bind="'COMMON_YUAN'|translate"></span>
                                        </div>
                                    </div>
                                    <div class="col-xs-2 min-input">
                                        <input type="text" class="form-control" autocomplete="off"
                                               placeholder="{{guaduan_anciRest_tipCTCC}}" ng-disabled="gdTypeCTCC!==1"
                                               name="gdanciCTCC"
                                               ng-model="gdanciCTCC" required pattern="^[0-9]{1,9}$"
                                               ng-keyup="accMul(GDPriceCTCC,gdanciCTCC,guaduan_amountCTCC,'gdanciCTCC')">
                                        <span style="color:red" class="error-ver"
                                              ng-show="((orderItemDomain.gdanciCTCC.$dirty && orderItemDomain.gdanciCTCC.$invalid) || gd_anci_over_errorCTCC) && gdTypeCTCC===1 && postGuaDuanCTCC">
												<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                     height="20">
												<span ng-show="orderItemDomain.gdanciCTCC.$error.required"
                                                      ng-bind="'REQUIRED'|translate"></span>
												<span ng-show="orderItemDomain.gdanciCTCC.$error.pattern"
                                                      ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
												<span ng-show="gd_anci_over_errorCTCC"
                                                      ng-bind="'GO_BEYOND'|translate"></span>
											</span>
                                    </div>
                                    <div class="col-xs-1"
                                         ng-show="gdTypeCTCC===1 && postGuaDuanCTCC && selectedOrder.isExperience===0"
                                         style="white-space: nowrap">
                                        <span ng-bind="gdanci_priceCTCC||0"></span><span
                                            ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>


                            </div>

                        </div>
                        <!--add by hyj end-->
                        <!--20191107 end-->
                    </div>
                	<!--群发屏显-->
                    <div  class="form-group" style="margin-left: 40px;" ng-show="hasGroupScreenCTCC && servType =='4' && ctcc===true">
                        <div class="form-group">
                         <div class="check-li col-xs-12">
                             <div ng-click="showEnable('showGroupScreenCTCC')" style="display: inline-block">
                                  <span class="checked-btn checked"
                                        ng-class="{true:'checked',false:''}[showGroupScreenCTCC]"></span>
                                 <span >屏显:</span>
                             </div>
                         </div>
                     	</div>
                        <div class="row" ng-show="showGroupScreenCTCC">
                                <!--不限-->
                                <li ng-show="isgroupScreenCTCCNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li">
									<span class="check-btn redio-btn" id="ScreenType4"
                                          ng-class="{true:'checked',false:''}[isgroupScreenCTCCNoLimit]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc" style="margin-left:30px"></span>
                                </li>
								<!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li ng-show="!isgroupScreenCTCCNoLimit" class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="ScreenType3"
                                          ng-class="{true:'checked',false:''}[!isgroupScreenCTCCNoLimit]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupScreenCTCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="white-space: nowrap;">
                                    <span ng-bind="groupScreenCTCCPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-3 input-min-width error-ver" ng-show="!isgroupScreenCTCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{groupSendScreenCTCC_tip}}"
                                           name="groupSendScreenCTCCanci"
                                           ng-model="groupSendScreenCTCCanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="(orderItemDomain.groupSendScreenCTCCanci.$dirty && orderItemDomain.groupSendScreenCTCCanci.$invalid)||groupSendScreenCTCC_over_error">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
                                        <span ng-show="orderItemDomain.groupSendScreenCTCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
                                        <span ng-show="orderItemDomain.groupSendScreenCTCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>

                                         <span ng-show="groupSendScreenCTCC_over_error"
                                               ng-bind="'GO_BEYOND'|translate"></span>
                                    </span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupScreenCTCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(groupScreenCTCCPrice,groupSendScreenCTCCanci,groupScreenCTCC_amount,'gsScreenCTCC')||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>

                        </div>
                    </div>
                	<!--群发短信-->
                    <div  class="form-group" style="margin-left: 40px;" ng-show="hasGroupSMSCTCC && servType =='4' && ctcc===true">
                        <div class="form-group">
                         <div class="check-li col-xs-12">
                             <div ng-click="showEnable('showGroupSMSCTCC')" style="display: inline-block">
                                  <span class="checked-btn checked"
                                        ng-class="{true:'checked',false:''}[showGroupSMSCTCC]"></span>
                                 <span >短信:</span>
                             </div>
                         </div>
                     	</div>
                        <div class="row" ng-show="showGroupSMSCTCC">
                                <!--不限-->
                                <li ng-show="isgroupSMSCTCCNoLimit" class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li">
									<span class="check-btn redio-btn" id="smsType4"
                                          ng-class="{true:'checked',false:''}[isgroupSMSCTCCNoLimit]"> </span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-class></span>
                                    <span ng-bind="desc" style="margin-left:30px"></span>
                                </li>
								<!--按次-->
                                <div class="col-lg-10 col-xs-10"></div>
                                <li ng-show="!isgroupSMSCTCCNoLimit" class="col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 redio-li"
                                    style="padding-top: 7px;">
									<span class="check-btn redio-btn pingXian" id="smsType3"
                                          ng-class="{true:'checked',false:''}[!isgroupSMSCTCCNoLimit]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                </li>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupSMSCTCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="white-space: nowrap;">
                                    <span ng-bind="groupSMSCTCCPrice"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                                <div class="col-xs-3 input-min-width error-ver" ng-show="!isgroupSMSCTCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{groupSendSMSCTCC_tip}}"
                                           name="groupSendSMSCTCCanci"
                                           ng-model="groupSendSMSCTCCanci" required pattern="^[0-9]{1,9}$">

                                    <span style="color:red"
                                          ng-show="(orderItemDomain.groupSendSMSCTCCanci.$dirty && orderItemDomain.groupSendSMSCTCCanci.$invalid)||groupSendSMSCTCC_over_error">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
                                        <span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.required"
                                              ng-bind="'REQUIRED'|translate">

                                        </span>
                                        <span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.pattern"
                                              ng-bind="'CREATEORDER_MAXINPUTDESC'|translate">

                                         </span>

                                         <span ng-show="groupSendSMSCTCC_over_error"
                                               ng-bind="'GO_BEYOND'|translate"></span>
                                    </span>
                                </div>
                                <div class="col-lg-1 col-xs-1 col-sm-1 col-md-1" ng-show="!isgroupSMSCTCCNoLimit"
                                     ng-if="isExperience == '0'"
                                     style="padding-top: 7px;white-space: nowrap">
                                    <span ng-bind="accMul(groupSMSCTCCPrice,groupSendSMSCTCCanci,groupSMSCTCC_amount,'gsSMSCTCC')||0"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>

                        </div>
                    </div>
                	
                </div>

                <div class="form-group" ng-show="servType !='4'" style="margin-left: 24px;">
                    <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                       ng-bind="'CREATEORDER_DESC1'|translate" ng-show="selectedOrder.servType !=3"></p>
                </div>

                <!--有效期-->
                <div class="form-group" style="margin-left: 40px;">
                    <div class="col-xs-12">
                        <span ng-bind="'CREATEORDER_TIME'|translate"></span>
                    </div>
                    <div class="input-daterange input-group col-lg-1 col-xs-2 col-sm-2 col-md-2 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2" id="datepicker"
                         style="min-width: 415px">
                        <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off"
                               id="start"/>
                        <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                        <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off"
                               id="end"/>
                    </div>
                </div>

                <div class="form-group" style="margin-left: 24px;">
                    <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                       ng-bind="'CREATEORDER_DESC2'|translate"></p>
                </div>
            </form>
        </div>
        <div class="form-group">
            <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                 style="padding-left: 30px;">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-disabled="orderBase.amount.$invalid ||effictiveTime==='' ||expireTime ===''
									||(servType != '4' && (!(cmcc || cucc || ctcc)
									||(!(cmcc && (postPingXianCMCC || postGuaCai || postGJZC || postGuaDuan)) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)) && servType !='3')
									||cmcc && postPingXianCMCC && pxType===1 && orderItemDomain.anci.$invalid
									||cmcc && postPingXianCMCC && pxType===2 && orderItemDomain.baoyue.$invalid
									||cmcc && postGuaDuan && gdType==1 && orderItemDomain.gdanci.$invalid
									||cmcc && postGuaDuan && gdType==2 && orderItemDomain.gdbaoyue.$invalid
									||cmcc && postGuaCai && gcType==1 && orderItemDomain.gcanci.$invalid
									||cmcc && postGuaCai && gcType==2 && orderItemDomain.gcbaoyue.$invalid
									||cmcc && postGJZC && gjzcType==1 && orderItemDomain.gjzcanci.$invalid
									||cucc && postPingXianCUCC && pxType_cucc===1 && orderItemDomain.anci_cucc.$invalid
									||cucc && postGuaDuanCUCC && gdTypeCUCC==1 && orderItemDomain.gdanciCUCC.$invalid
									||ctcc && postPingXianCTCC && pxType_ctcc===1 && orderItemDomain.anci_ctcc.$invalid
									||ctcc && postGuaDuanCTCC && gdTypeCTCC==1 && orderItemDomain.gdanciCTCC.$invalid
									||px_anci_over_error||gd_anci_over_error||gd_anci_over_errorCUCC||gd_anci_over_errorCTCC||gc_anci_over_error||px_anci_over_error_cucc
									||px_anci_over_error_ctcc || baoyue_over_error
									||(servType =='3' && !(cmcc && postPingXianCMCC) && isExperience == 1)
									||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))
                                    ||(servType == '4' && postZC && cmcc && (isExperienceLimit && orderItemDomain.zcanci.$invalid))
                                    ||(servType == '4' && postCX && cmcc && (isNoExperienceLimit && orderItemDomain.cxanci.$invalid))
                                    ||(servType == '4' && showGroupSMS && cmcc && !isgroupSMSCMCCNoLimit && (orderItemDomain.groupSMSCMCCanci.$invalid || groupSendSMSCMCC_over_error))
                                    ||(servType == '4' && showGroupSMSCUCC && cmcc && !isgroupSMSCUCCNoLimit && (orderItemDomain.groupSMSCUCCanci.$invalid || groupSendSMSCUCC_over_error))
                                    ||(servType == '4' && showGroupSMSCTCC && cmcc && !isgroupSMSCTCCNoLimit && (orderItemDomain.groupSMSCTCCanci.$invalid || groupSendSMSCTCC_over_error))
                                    ||(servType == '4' && showGroupScreen && cmcc && !isgroupScreenCMCCNoLimit && (orderItemDomain.groupScreenCMCCanci.$invalid || groupSendScreenCMCC_over_error))
                                    ||(servType == '4' && showGroupScreenCUCC && cmcc && !isgroupScreenCUCCNoLimit && (orderItemDomain.groupScreenCUCCanci.$invalid || groupSendScreenCUCC_over_error))
                                    ||(servType == '4' && showGroupScreenCTCC && cmcc && !isgroupScreenCTCCNoLimit && (orderItemDomain.groupScreenCTCCanci.$invalid || groupSendScreenCTCC_over_error))
                                    ||(servType == '4' && !(cmcc || cucc || ctcc))
							        ||(servType =='4' && cmcc && !(showGroupSMS || postZC || postCX || showGroupScreen) &&  !(cucc || ctcc))"
"
                        ng-click="querySubscribeList(selectedOrder,'createOrder')" ng-bind="'COMMON_SAVE'|translate"
                        id="formSub">
                </button>
                <button type="submit" class="btn btn-back" ng-click="goBack()"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>

    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                    </p></div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"
                            ng-click="sure(eventType)"></button>
                </div>
            </div>
        </div>
    </div>
    <span style="visibility: hidden;position: absolute;">{{amount}}</span>
    <span style="visibility: hidden;position: absolute;">{{anci}}</span>
    <span style="visibility: hidden;position: absolute;">{{baoyue}}</span>
    <span style="visibility: hidden;position: absolute;">{{gdanci}}</span>
    <span style="visibility: hidden;position: absolute;">{{gdbaoyue}}</span>
    <span style="visibility: hidden;position: absolute;">{{gcanci}}</span>
    <span style="visibility: hidden;position: absolute;">{{gcbaoyue}}</span>
    <span style="visibility: hidden;position: absolute;">{{anci_cucc}}</span>
    <span style="visibility: hidden;position: absolute;">{{anci_ctcc}}</span>
</div>

</body>
</html>
