<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityContentRelMapper">
    <resultMap id="activityWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivityContentRelWrapper">       
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="activityID" column="activityID" javaType="java.lang.Integer" />
        <result property="contentID" column="contentID" javaType="java.lang.Long" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
    </resultMap>
    
    <select id="getContentIDByActivityID" resultMap="activityWrapper">
        select 
        ID,
		activityID,
		contentID,
		createTime,
		updateTime
        from ecpm_t_activity_contentrel
        <trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				activityID=#{activityID}
			</if>
		</trim>
    </select>
    <select id="getActContentRelByContentID" resultMap="activityWrapper">
        select 
        ID,
		activityID,
		contentID,
		createTime,
		updateTime
        from ecpm_t_activity_contentrel
        <trim prefix="where" prefixOverrides="and|or">
			<if test="contentID != null">
				contentID=#{contentID}
			</if>
		</trim>
    </select>
    
	<select id="getContentIDsByActivityID" resultType="java.lang.Long">
        select 
        	contentID
        from ecpm_t_activity_contentrel
        <trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				activityID=#{activityID}
			</if>
		</trim>
    </select>



    <insert id="createActivityContentrel">
		insert into ecpm_t_activity_contentrel
			(
			activityID,
			contentID,
			createTime,
			updateTime
			)
		values
			(
			#{activityID},
			#{contentID},
			#{createTime},
			#{updateTime}
			)
	</insert>


	<select id="getContentIDsByActivityIdList" resultType="java.lang.Long">
		select
		contentID
		from ecpm_t_activity_contentrel
		where activityID in
		<foreach collection="list" item="activityId" index="index" open="(" close=")" separator=",">
			#{activityId}
		</foreach>
	</select>

</mapper>