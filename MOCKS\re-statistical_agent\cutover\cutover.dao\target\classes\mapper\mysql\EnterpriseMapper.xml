<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.cutover.dao.dsum.mapper.EnterpriseMapper">
	<resultMap id="enterpriseWrapper"
		type="com.huawei.jaguar.cutover.dao.domain.EnterpriseWrapper">
		<result property="id" column="id" />
		<result property="enterpriseCode" column="enterpriseCode" />
		<result property="enterpriseName" column="enterpriseName" />
		<result property="enterpriseDesc" column="enterpriseDesc" />
		<result property="enterpriseType" column="enterpriseType" />
		<result property="organizationID" column="organizationID" />
		<result property="custID" column="custID" />
		<result property="businessLicenseID" column="businessLicenseID" />
		<result property="businessLicenseURL" column="businessLicenseURL" />
		<result property="idCardPositiveURL" column="IDCardPositiveURL" />
		<result property="idCardOppositeURL" column="IDCardOppositeURL" />
		<result property="contract" column="contract" />
		<result property="auditStatus" column="auditStatus" />
		<result property="auditDesc" column="auditDesc" />
		<result property="msisdn" column="msisdn" />
		<result property="createTime" column="createTime" />
		<result property="operatorID" column="operatorID" />
		<result property="lastUpdateTime" column="lastUpdateTime" />
		<result property="parentEnterpriseID" column="parentEnterpriseID" />
		<result property="parentEnterpriseName" column="parentEnterpriseName" />
		<result property="provinceID" column="provinceID" />
		<result property="status" column="status" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="reserved5" column="reserved5" />
		<result property="reserved6" column="reserved6" />
		<result property="reserved7" column="reserved7" />
		<result property="reserved8" column="reserved8" />
		<result property="reserved9" column="reserved9" />
		<result property="reserved10" column="reserved10" />	
	</resultMap>


	<select id="queryEnterpriseList" resultMap="enterpriseWrapper">
		SELECT
		t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
		t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
		t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
		t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
		t.reserved10,t.parentEnterpriseName from dsum_t_enterprise t 
		where (reserved2 != 1 or reserved2 is null)
		<trim prefix="and" prefixOverrides="and|or">
		<if test="enterpriseIDs != null and enterpriseIDs.size()>0">
			and t.id in
			<foreach item="id" index="index" collection="enterpriseIDs"
				open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
		<if test="enterpriseName !=null  and enterpriseName !=''">
			and t.enterpriseName like concat("%", #{enterpriseName}, "%")
		</if>
		
		<if test="parentEnterpriseName !=null  and parentEnterpriseName !=''">
			and t.parentEnterpriseName like concat("%", #{parentEnterpriseName}, "%")
		</if>
		
		<if test="createStartTime !=null and createEndTime !=null">
			and (t.createTime <![CDATA[ <= ]]> #{createEndTime}
			and t.createTime <![CDATA[ >= ]]> #{createStartTime})
		</if>
		
		<if test="createStartTime != null and createEndTime == null">
			and t.createTime <![CDATA[ >= ]]> #{createStartTime}
		</if>
		
		<if test="createStartTime == null and createEndTime != null">
			and t.createTime <![CDATA[ <= ]]> #{createEndTime}
		</if>
		
		<if test="organizationID !=null  and organizationID !=''">
			and t.organizationID like concat("%", #{organizationID}, "%")
		</if>
		
		<if test="cityIDs != null and cityIDs.size()>0">
			and t.cityID in
			<foreach item="cityIDs" index="index" collection="cityIDs"
				open="(" separator="," close=")">
				#{cityIDs}
			</foreach>
		</if>
		<if test="enterpriseCode !=null and enterpriseCode !='' ">
			and t.enterpriseCode = #{enterpriseCode}
		</if>
		
		<if test="enterpriseType !=null">
			and t.enterpriseType = #{enterpriseType}
		</if>
		
		<if test="parentEnterpriseID !=null">
			and t.parentEnterpriseID = #{parentEnterpriseID}
		</if>	
		
		<if test="provinceIDs !=null and provinceIDs.size>0">
			and t.provinceID in
			<foreach item="provinceID" index="index" collection="provinceIDs" open="(" separator="," close=")">
				#{provinceID}
			</foreach>
		</if>
		
		<if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
			and t.parentEnterpriseID in
			<foreach item="parentEnterpriseIDs" index="index" collection="parentEnterpriseIDs"
				open="(" separator="," close=")">
				#{parentEnterpriseIDs}
			</foreach>
		</if>
		
		<if test="auditStatusList != null and auditStatusList.size()>0">
			and t.auditStatus in
			<foreach item="auditStatusList" index="index" collection="auditStatusList"
				open="(" separator="," close=")">
				#{auditStatusList}
			</foreach>
		</if>
		
		<if test="auditStatus !=null ">
			and t.auditStatus = #{auditStatus}
		</if>
		
		<choose>
        	<when test="statusList != null and statusList.size()>0">
        		and t.status in
				<foreach item="status" index="index" collection="statusList"
					open="(" separator="," close=")">
					#{status}
				</foreach>
			</when>
			<otherwise>
				and t.status = 1
			</otherwise>
   		</choose>
   		
		</trim>
		<if test="sortField !=null and sortField !='' and sortType !=null and sortType !=''">
			order by  ${sortType} ${sortField}
		</if>
		<if test="pageNum !=null and pageSize !=null ">
			limit #{pageNum},#{pageSize}
		</if>
	</select>
	
</mapper>