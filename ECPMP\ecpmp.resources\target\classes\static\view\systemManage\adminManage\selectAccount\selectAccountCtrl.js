var app = angular.module("myApp", ["util.ajax", "page",  "angularI18n"])
app.controller("selAccountController", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    $scope.accountID = $location.$$search.accountID;  
    $scope.queryAccountList();
    $scope.roleName = "";
  };
  
  $scope.goBack = function (){
    location.href = "../administrator.html"
  };

  $scope.queryAccountList = function () {
    var req={
      "accountQueryType": 1,
      "accountID": $scope.accountID,
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/queryAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
            if(data.resultCode=='**********'){
              $scope.accountInfo = result.accountInfo;
              $scope.accountID = result.accountInfo.accountID;
              $scope.accountName = result.accountInfo.accountName;
              $scope.fullName = result.accountInfo.fullName;
              $scope.accountStatus = result.accountInfo.accountStatus;
              // $scope.roleName = result.accountInfo.roleName;
              $scope.msisdn = result.accountInfo.msisdn;
              $scope.email = result.accountInfo.email;
              $scope.password = result.accountInfo.password;
              $scope.operatorAccountName = result.accountInfo.operatorAccountName;
              $scope.createTime = result.accountInfo.createTime;
              $scope.lastUpdateTime = result.accountInfo.lastUpdateTime;
              $scope.managerRightType =  result.accountInfo.managerRightType==1?'应用账号':'4A账号';
              $scope.isMiguStaff =  result.accountInfo.isMiguStaff;
              $scope.oaAccount =  result.accountInfo.oaAccount;
                angular.forEach($scope.accountInfo.roleList, function (role) {
                $scope.roleName = $scope.roleName + role.roleName + "/";
              });
              $scope.roleName = $scope.roleName.substr(0, $scope.roleName.length - 1);
            }else{
              $scope.tip=data.resultCode;
                $('#myModal').modal();
            }
        })	
      },
      error:function(){
        $rootScope.$apply(function () {
          $scope.tip="**********";
            $('#myModal').modal(); 
        })
      }
    }); 
  }

    //账号状态
    $scope.getAccountStatus = function (accountStatus) {
      switch(accountStatus) {
          case 1:
           return "正常";
          case 2:
           return "冻结";
          case 3:
           return "停用";
          default:
           return "";
      }
    }
});
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}]);
app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
    }
    return "";
  }
});