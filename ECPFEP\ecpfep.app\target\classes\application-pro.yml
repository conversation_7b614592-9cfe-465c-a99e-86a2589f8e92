spring:
  http:
    encoding:
      force: true
      charset: UTF-8
  freemarker:
    allow-request-override: false
    cache: false
    check-template-location: true
    charset: UTF-8
    content-type: text/html; charset=utf-8
    prefer-file-system-access: false
    expose-request-attributes: false
    expose-session-attributes: false
    expose-spring-macro-helpers: false
    suffix: .ftl
    template-loader-path: classpath:/templates

eureka:
   client:
     serviceUrl:
       defaultZone: http://*************:19000/eureka/,http://*************:19000/eureka/,http://*************:19000/eureka/
   instance:
     preferIpAddress: true
     instanceId: ${spring.cloud.client.ip-address}:28445

logging:
   config:
      classpath: log4j2.xml