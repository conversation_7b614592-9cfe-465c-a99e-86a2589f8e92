<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpaxb.dao.mapper.AxbXMapper">



    <select id="queryAxbXList" resultType="com.huawei.jaguar.dsdp.ecpaxb.dao.domain.AxbX">
        SELECT
            *
        FROM
            ecpe_t_axb_x x
                left JOIN ecpe_t_axb_sub s on s.telX = x.telX and s.expireTime >= NOW()
        WHERE
            x.areaCode = #{areaCode}
          and s.subid is null

    </select>

    <insert id="insertAxbSub">
        insert into ecpe_t_axb_sub
        (
            subid,
            enterpriseID,
            enterpriseCode,
            enterpriseName,
            telA,
            telB,
            areaCode,
            telX,
            effictiveTime,
            expireTime,
            createTime,
            updateTime,
            parentEnterpriseID,
            parentEnterpriseCode,
            parentEnterpriseName
        ) values
            (
                #{subid},
                #{enterpriseID},
                #{enterpriseCode},
                #{enterpriseName},
                #{telA},
                #{telB},
                #{areaCode},
                #{telX},
                #{effictiveTime},
                #{expireTime},
                now(),
                now(),
                #{parentEnterpriseID},
                #{parentEnterpriseCode},
                #{parentEnterpriseName}
            )
    </insert>

    <insert id="insertAxbPushReport">
        insert into ecpe_t_axb_push_report
        (
            requestId,
            subid,
            pushType,
            callType,
            telA,
            telB,
            telX,
            createTime,
            updateTime
        ) values
        (
            #{requestId},
            #{subid},
            #{pushType},
            #{callType},
            #{telA},
            #{telB},
            #{telX},
            now(),
            now()
        )
    </insert>


    <select id="queryAxbSubBySubid" resultType="com.huawei.jaguar.dsdp.ecpaxb.dao.domain.AxbSub">
        SELECT
            *
        FROM
             ecpe_t_axb_sub
        WHERE
            subid  = #{subid}
            and expireTime>=now()
    </select>

    <select id="queryAxbSub" resultType="com.huawei.jaguar.dsdp.ecpaxb.dao.domain.AxbSub">
        SELECT
            *
        FROM
            ecpe_t_axb_sub
        <trim prefix="where" prefixOverrides="and|or">
<!--            <if test="subid != null and subid != ''">-->
                and subid  = #{subid}
<!--            </if>-->
            <if test="expireTime != null">
                and expireTime >= #{expireTime}
            </if>

            <if test="telA != null and telA != ''">
                and telA=#{telA}
            </if>
            <if test="telB != null and telB != ''">
                and telB=#{telB}
            </if>
        </trim>
    </select>

    <update id="updateAxbSubBySubid">
        update ecpe_t_axb_sub set expireTime = #{expireTime} where subid = #{subid}
    </update>

    <select id="queryAxbPushReportByRequestId" resultType="com.huawei.jaguar.dsdp.ecpaxb.dao.domain.AxbPushReport">
        SELECT
            *
        FROM
            ecpe_t_axb_push_report
        WHERE
            requestId  = #{requestId}
    </select>

    <select id="queryAxbPushReportByDate" resultType="com.huawei.jaguar.dsdp.ecpaxb.dao.domain.AxbPushReportEX">
        SELECT
            s.enterpriseCode,
            s.enterpriseName,
            s.parentEnterpriseCode,
            s.parentEnterpriseName,
            pr.pushType,
            pr.callType,
            s.effictiveTime,
            s.expireTime,
            if(pr.telA is null,s.telA,pr.telA) telA,
            if(pr.telB is null,s.telB,pr.telB) telB,
            if(pr.telX is null,s.telX,pr.telX) telX,
            s.areaCode,
            s.subid,
            pr.createTime
        FROM
            ecpe_t_axb_push_report pr
                LEFT JOIN ecpe_t_axb_sub s ON s.subid = pr.subid
            WHERE pr.createTime >= #{startDate} and pr.createTime&lt;=#{endDate}
            and s.subid is not null
    </select>
</mapper>