<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DefraudNumMapper">
    <resultMap id="DefraudNumWrapper"
               type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DefraudNumWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="mobile" column="mobile" javaType="java.lang.String" />
        <result property="suspectAt" column="suspectAt" javaType="java.lang.String" />
        <result property="empowerNo" column="empowerNo" javaType="java.lang.String" />
        <result property="status" column="status" javaType="java.lang.String" />
        <result property="code" column="code" javaType="java.lang.Integer" />
        <result property="createTime" column="createTime" javaType="Date" />
    </resultMap>
<insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.DefraudNumWrapper">
    INSERT INTO `ecpm_t_defraud_num` (
        `enterpriseID`,
        `mobile`,
        `suspectAt`,
        `empowerNo`,
        `status`,
        `code`,
        `createTime`
    )
    VALUES
    (
        #{enterpriseID},
        #{mobile},
        #{suspectAt},
        #{empowerNo},
        #{status},
        #{code},
        #{createTime}
    );
</insert>
</mapper>