
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>活动管理</title>
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/activityManageList.css" rel="stylesheet" />
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>

<script type="text/javascript" src="activityManageListCtrl.js"></script>
</head>
<body ng-app='myApp' style="min-width:1024px;" ng-controller='actManageListController' ng-init="init();">
    <div class="cooperation-manage container-fluid" >
        <div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_MERCHANT'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></span></div>
        <top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/merchantInfoManage/activityManage/activityManageList" list-index="17"></top:menu>
        <div class="cooperation-search">
            <form class="form-horizontal">
                    <div class="form-group form-inline">
                            <label for="actName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap" ng-bind="'COMMON_ACTIVITYNAME'|translate"></label>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                                    <input autocomplete="off" type="text" class="form-control" id="actName" placeholder="{{'COMMON_PLEASEINPUTACTIVITYMANAGE'|translate}}" ng-model="initSel.activityName">
                            </div>
                            <label for="orderStatus" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap" ng-bind="'COMMON_STATUS'|translate">状态</label>
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                                    <select id="orderStatus" class="form-control" ng-model="initSel.auditStatus" ng-options="x.id as x.name for x in actStatusChoise" >
                                    </select>
                            </div>
        
                            <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1">
                                <button type="submit" class="btn search-btn" ng-click="queryActivityList()">
                                    <icon class="search-iocn"></icon>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                            </div>
                            
                        </div>
            </form>
        </div>
        <div style="margin:20px 20px;">
                <p style="font-size: 16px;font-weight: 500;" ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></p>
            </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th ng-bind="'COMMON_ACTIVITYNAME'|translate"></th>
                        <th ng-bind="'ACTIVITY_EXPIRYDATE'|translate"></th>
                        <th ng-bind="'COMMON_STATUS'|translate"></th>
                        <th ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in activityInfoList">
                        <td title="{{item.activityName}}">{{item.activityName}}</td>
                        <td title="{{formatDate(item.effectivetime,item.expiretime)}}">{{formatDate(item.effectivetime,item.expiretime)}}</td>
                        <td title="{{statusMap[item.auditStatus]}}">{{statusMap[item.auditStatus]}}</td>
                        <td>
                            <div class="handle">
                                <ul>
                                    <li class="query" ng-click="queryActDetail(item.activityID)">
                                        <icon class="query-icon"></icon><span ng-bind="'ACTIVITY_CHECK'|translate"></span></li>
                                    <li class="query" ng-click="querySpokesperson(item.activityID)">
                                        <icon class="query-icon"></icon><span ng-bind="'SPOKES_CHECK'|translate"></span></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="activityInfoList.length<=0">
                        <td style="text-align:center" colspan="4" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    <div>
        <ptl-page tableId="0" change="queryActivityList('justPage')"></ptl-page>
      </div>
    </div>

<!--小弹出框-->
        <!-- <button type="button" class="btn btn-primary" id="open-model" data-toggle="modal" data-target=".bs-example-modal-sm">Large modal</button> -->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838'>
                                {{tip|translate}}
                            </p></div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>
    
</body>
<style>
    .handle ul li icon{
        background: url(../../../../../assets/images/sideNavIcons.png)no-repeat;
    }
</style>
</html>