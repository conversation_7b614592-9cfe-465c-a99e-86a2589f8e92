# 号码认证模块开发文档

## 功能概述
号码认证模块主要用于展示和管理企业号码认证相关信息，包括公司信息、品牌费、号码量、审核状态等数据。该模块包含两个主要页面：

1. 列表页面 (numberAuthentication.html)：展示所有号码认证记录
2. 详情页面 (check.html)：展示指定认证记录的详细信息

## 系统架构

### 2.1 技术栈

- **前端框架**：AngularJS (v1.x)
- **UI组件**：Bootstrap 3.x
- **数据交互**：RESTful API
- **第三方插件**：
  - bootstrap-datepicker（日期选择器）
  - angular-translate（国际化）
  - 自定义分页组件（ptl-page）

### 2.2 系统结构

系统分为两个主要页面：
- **号码认证列表页**：展示所有企业的号码认证信息
- **号码认证详情页**：展示特定企业的号码认证详细信息

## 功能说明

### 3.1 号码认证列表页 (numberAuthentication.html)

#### 主要功能：
- **条件查询**：按公司名称、审核状态和首次审核时间范围筛选数据
- **数据展示**：列表展示符合条件的号码认证企业信息
- **数据导出**：导出筛选后的企业号码认证数据
- **查看详情**：跳转到特定企业的号码认证详情页

#### 数据字段：
- 公司名称
- 品牌费（1有/0无）
- 号码量
- 创建时间
- 首次审核时间
- 审核状态（1待审核/2通过/3驳回）

### 3.2 号码认证详情页 (check.html)

#### 主要功能：
- **条件查询**：按认证号码、公司名称、展示名称、审核状态、上线状态和时间范围筛选数据
- **详情展示**：列表展示符合条件的号码认证详细信息
- **数据导出**：导出筛选后的号码认证详情数据
- **返回功能**：返回到号码认证列表页

#### 数据字段：
- 序号
- 认证号码
- 公司名称
- 展示名称
- LOGO（1有/0无）
- 审核状态（1未审核/2通过3驳回）
- 上线状态（1未上线/2已上线/3已下线）
- 开始时间
- 结束时间

## 文件结构

```
ecpmp.resources/src/main/resources/static/view/systemManage/numberAuthentication/
├── numberAuthentication.html    # 号码认证列表页面
├── numberAuthentication.js      # 号码认证列表页控制器
└── check/                       # 号码认证详情页目录
    ├── check.html               # 号码认证详情页面
    └── check.js                 # 号码认证详情页控制器
```

## API接口说明

### 5.1 号码认证列表接口

#### 5.1.1 查询企业列表
- **URL**: `/ecpmp/ecpmpServices/msisdnAuthService/queryEnterprise`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "enterpriseName": "企业名称",
    "auditStatus": "审核状态",
    "startAuditTime": "开始时间",
    "endAuditTime": "结束时间",
    "pageParameter": {
      "pageSize": 10,
      "pageNum": 1,
      "isReturnTotal": "1"
    }
  }
  ```
- **响应字段**:
  - msisdnAuthEnterpriseInfos: 号码认证企业信息列表
  - totalNum: 总记录数

#### 5.1.2 导出企业数据
- **URL**: `/qycy/ecpmp/ecpmpServices/msisdnAuthService/downEnterprise`
- **方法**: GET
- **请求参数**: 同查询参数（无分页信息）

### 5.2 号码认证详情接口

#### 5.2.1 查询详情列表
- **URL**: `/ecpmp/ecpmpServices/msisdnAuthService/queryDetail`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "msisdnAuthEnterpriseId": "企业ID",
    "authMsisdn": "认证号码",
    "enterpriseName": "公司名称",
    "showName": "展示名称",
    "auditStatus": "审核状态",
    "onLineState": "上线状态",
    "startTime": "开始时间",
    "endTime": "结束时间",
    "pageParameter": {
      "pageSize": 10,
      "pageNum": 1,
      "isReturnTotal": "1"
    }
  }
  ```
- **响应字段**:
  - msisdnAuthDetailInfos: 号码认证详情列表
  - totalNum: 总记录数

#### 5.2.2 导出详情数据
- **URL**: `/qycy/ecpmp/ecpmpServices/msisdnAuthService/downDetail`
- **方法**: GET
- **请求参数**: 同查询参数（无分页信息）

## 代码实现注意事项

### 7.1 分页处理
- 系统使用自定义的`ptl-page`分页组件
- 分页信息存储在`$scope.pageInfo`数组中
- 翻页时通过`search('justPage')`方法保留当前查询条件进行查询

### 7.2 日期处理
- 使用bootstrap-datepicker进行日期选择
- 开始日期自动添加"00:00:00"后缀
- 结束日期自动添加"23:59:59"后缀

### 7.3 状态显示
- 使用不同颜色和样式的标签显示审核状态和上线状态
- 通过CSS类`status-badge`、`status-pending`、`status-success`等实现视觉差异化

### 7.4 错误处理
- 对API请求的错误进行捕获和显示
- 根据不同HTTP状态码显示相应错误信息
- 通过模态框展示错误提示



## 常见问题与解决方案

### 8.1 页面加载异常
- 检查网络请求是否正常
- 确认API接口返回数据结构是否符合预期
- 查看浏览器控制台是否有JavaScript错误

### 8.2 分页问题
- 确保`pageInfo`对象正确初始化
- 检查分页参数的传递是否正确
- 验证分页组件的事件绑定是否有效

### 8.3 导出功能失败
- 检查导出API是否可访问
- 确认查询参数格式是否正确
- 验证文件下载权限是否足够
