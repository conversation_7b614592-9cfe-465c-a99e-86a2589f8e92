<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.cutover.dao.ecpm.mapper.ContentMapper">
	<resultMap id="contentWrapper"
		type="com.huawei.jaguar.cutover.dao.domain.ContentWrapper">
		<result property="id" column="ID" javaType="java.lang.Long" />
		<result property="contCode" column="contCode" javaType="java.lang.String" />
		<result property="contRuleID" column="contRuleID" javaType="java.lang.String" />
		<result property="thirdPartyType" column="thirdpartyType"
			javaType="java.lang.Integer" />
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="blackWhiteListType" column="blackwhiteListType"
			javaType="java.lang.Integer" />
		<result property="content" column="content" javaType="java.lang.String" />
		<result property="contentTitle" column="contentTitle" javaType="java.lang.String" />
		<result property="chargeType" column="chargeType" javaType="java.lang.Integer" />
		<result property="deliveryDate" column="deliveryDate" javaType="java.lang.String" />
		<result property="approveStatus" column="approveStatus"
			javaType="java.lang.Integer" />
		<result property="approveIdea" column="approveIdea" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="enterpriseCode" column="enterpriseCode"
			javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="sceneDesc" column="sceneDesc" javaType="java.lang.String" />
		<result property="deliveryType" column="deliveryType" javaType="java.lang.String" />
		<result property="industryType" column="industryType" javaType="java.lang.String" />
		<result property="applyProperty" column="applyProperty"
			javaType="java.lang.Integer" />
		<result property="unicomDelivery" column="unicomDelivery"
			javaType="java.lang.Integer" />
		<result property="scene" column="scene" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="syncStatus" column="syncStatus" javaType="java.lang.Integer" />
		<result property="frequencyID" column="frequencyID" javaType="java.lang.String" />
		<result property="statID" column="statID" javaType="java.lang.String" />
		<result property="maxPushPerDay" column="maxPushPerDay"
			javaType="java.lang.Integer" />
		<result property="pushInterval" column="pushInterval" javaType="java.lang.Integer" />
		<result property="auditTime" column="auditTime" javaType="java.util.Date" />
		<result property="deductTime" column="deductTime" javaType="java.util.Date" />
		<result property="subServTypeEachMemberCall" column="subServTypeEachMemberCall"
			javaType="java.lang.Long" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

	<select id="queryContentListBytEnterpriseIDs" resultMap="contentWrapper">
		select
		t1.contRuleID,
		t1.enterpriseID,
		t1.ID,
		t1.chargeType,
		t1.servType,
		t1.subServType
		from
		ecpm_t_content t1
		where t1.enterpriseID in
		<foreach item="enterpriseID" index="index" collection="enterpriseIDList"
			open="(" separator="," close=")">
			#{enterpriseID}
		</foreach>
		order by updateTime desc,ID desc
		limit #{pageNum},#{pageSize}
	</select>

	<select id="countContentListBytEnterpriseIDs" resultType="java.lang.Integer"
		parameterType="java.util.List">
		select
		count(1) from ecpm_t_content t1
		where t1.enterpriseID in
		<foreach item="enterpriseID" index="index" collection="list"
			open="(" separator="," close=")">
			#{enterpriseID}
		</foreach>
	</select>

</mapper>