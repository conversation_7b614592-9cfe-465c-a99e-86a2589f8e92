var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n", "service.common"])
app.controller('orderListController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
  $scope.init = function () {
    $scope.token = $.cookie('token') || '';
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];

    $scope.getTime = function (createTime) {
      var year = createTime.slice(0, 4);
      var month = createTime.slice(4, 6);
      var day = createTime.slice(6, 8);
      var hour = createTime.slice(8, 10);
      var minutes = createTime.slice(10, 12);
      var seconds = createTime.slice(12, 14);
      return year + "-" + month + "-" + day + " " + hour + ":" + minutes + ":" + seconds;
    }

    //下拉框(支付状态)
    $scope.payStatusChoise = [
      {
        id: "",
        name: "不限"
      },
      {
        id: "1",
        name: "未支付"
      },
      {
        id: "2",
        name: "支付中"
      },
      {
        id: "3",
        name: "已支付"
      },
      {
        id: "4",
        name: "支付失败"
      }
    ];

    $scope.orderListData = [];

    //初始化搜索条件
    $scope.initSel = {
      orderCode: "",
      enterpriseName: "",
      payStatus: "",
      startTime: "",
      endTime: "",
      search: false,
    };
    $scope.exportFile = function () {
      var req = {
        "param": {
          "orderCode": $scope.reqTemp.orderCode,
          "enterpriseName": $scope.reqTemp.enterpriseName,
          "enterpriseType": 4,
          "payStatus": $scope.reqTemp.payStatus,
          "createStartTime": $scope.createStartTime,
          "createEndTime": $scope.createEndTime,
          "isReturnOrderItem": 1,
          "orderType": "",
          "token": $scope.token,
          "isExport": 1
        },
        "url": "/qycy/ecpmp/ecpmpServices/activityService/downOrderCsvFile",
        "method": "get"
      }
      CommonUtils.exportFile(req);
    }
    $scope.queryOrderList();
  };

  $scope.getPayChannel = function (payChannel) {
    if (payChannel == 1) {
      return "一网通";
    }
    else if (payChannel == 2) {
      return "签约支付";
    }
    else if (payChannel == 3) {
      return "和包";
    }
    else if (payChannel == 4) {
      return "支付宝";
    }
    else if (payChannel == 5) {
      return "财付通";
    }
    else if (payChannel == 6) {
      return "微信";
    }
    else if (payChannel == 7) {
      return "对公支付";
    }
    else if (payChannel == 9) {
      return "网银支付";
    }
    else if (payChannel == 10) {
      return "银联信用";
    }
    else if (payChannel == 11) {
      return "咪咕币";
    }
    else if (payChannel == 12) {
      return "话费支付";
    }
    else if (payChannel == 13) {
      return "充值卡";
    }
    else if (payChannel == 14) {
      return "扫描支付";
    }
    else if (payChannel == 15) {
      return "条码支付";
    }
  };

  $scope.getPayStatus = function (payStatus) {
    if (payStatus == 1) {
      return "未支付";
    }
    else if (payStatus == 2) {
      return "支付中";
    }
    else if (payStatus == 3) {
      return "已支付";
    }
    else if (payStatus == 4) {
      return "支付失败";
    }
  };

  $('.input-daterange').datepicker({
    format: "yyyy-mm-dd",
    weekStart: 0,
    language: "zh-CN",
    clearBtn: true,
    autoclose: true
  });

  $('#start').on('changeDate', function () {
    $rootScope.$apply(function () {
      $scope.searchOn();
    })
  });

  $('#end').on('changeDate', function () {
    $rootScope.$apply(function () {
      $scope.searchOn();
    })
  });

  //判断搜索按钮是否置灰
  $scope.searchOn = function () {
    var startTime = document.getElementById("start").value;
    var endTime = document.getElementById("end").value;

    if (startTime !== '') {
      $scope.initSel.startTime = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + '000000';
    }
    else {
      $scope.initSel.startTime = "";
    }

    if (endTime !== '') {
      $scope.initSel.endTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + '235959';
    }
    else {
      $scope.initSel.endTime = "";
    }

    if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
      $scope.initSel.search = false;
    }
    else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
      $scope.initSel.search = false;
    }
    else {
      $scope.initSel.search = true;
    }
  }

  $scope.queryOrderList = function (condition) {
    $scope.createStartTime = $scope.initSel.startTime||"";
    $scope.createEndTime = $scope.initSel.endTime||"";
    if (condition != 'justPage') {
      var req = {
        "orderCode": $scope.initSel.orderCode || '',
        "enterpriseName": $scope.initSel.enterpriseName || '',
        "enterpriseType": 4,
        "payStatus": $scope.initSel.payStatus || '',
        "createStartTime": $scope.createStartTime || '',
        "createEndTime": $scope.createEndTime || '',
        "isReturnOrderItem": 1,//待确认
        "orderType": 1,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1",
        }
      }
      $scope.pageInfo[0].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
      $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/activityService/downOrderCsvFile?orderCode=" + $scope.reqTemp.orderCode + "&enterpriseName=" + $scope.reqTemp.enterpriseName + "&payStatus=" +
          $scope.reqTemp.payStatus + "&startTime=" + $scope.reqTemp.startTime + "&endTime=" + $scope.reqTemp.endTime + "&isReturnOrderItem=1" + "&orderType=";
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.reqTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/orderManageService/queryOrderList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.orderListData = result.orderList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.orderListData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "**********";
              $('#myModal').modal();
            }
        )
      }
    });
  }

  $(function () {
    $('.glyphicon-calendar').on('click', function () {
      $('#time-config').trigger('click');
    })
  })
}])

app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])