var app = angular.module("myApp",["util.ajax",'page',"top.menu","angularI18n","service.common"])
app.controller('actStatListController', 
		['$scope','$rootScope','$location','RestClientUtil','CommonUtils',function ($scope,$rootScope,$location,RestClientUtil,CommonUtils) {
    $scope.init=function(){
      $scope.token = $.cookie("token");
      var loginRoleType=$.cookie('loginRoleType');
      $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
      $scope.isZhike = (loginRoleType=='zhike');
      $scope.isAgent = (loginRoleType=='agent');
      $scope.isProvincial = (loginRoleType=='provincial');
      if($scope.isSuperManager){
    	  $scope.enterpriseType ='3';
      }
    //下拉框(活动状态)
      $scope.actStatusChoise = [
          {
              id: "1",
              name: "未开始"
          },
          {
              id: "2",
              name: "进行中"
          }, {
              id: "3",
              name: "已结束"
          }
      ];
        //初始化分页信息
        $scope.pageInfo =[
            {
              "totalPage": 1,
              "totalCount": 0,
              "pageSize": '10',
              "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
              }
          ];
        $scope.queryactStatList();
        $scope.enterpriseinfo();
    };
    
    $scope.formatStatus=function(str1,str2){
        var result="";
        var now =new Date();
        if(str1 && str2){
        	var date1 =new Date(str1.substring(0, 4) + "-" + str1.substring(4, 6) + "-" + str1.substring(6, 8) + ' 00:00:00');
        	var date2 =new Date(str2.substring(0, 4) + "-" + str2.substring(4, 6) + "-" + str2.substring(6, 8) + ' 23:59:59');
        	if(now < date1){
        		result ="未开始";
        	}else if(date1 <=now && now <=date2){
        		result ="进行中";
        	}else{
        		result ="已结束";
        	}
        }
        return result;
    }
    
    $scope.formatDate=function(str1,str2){
        var newDateStr="";
        if(str1 && str2){
        	newDateStr=str1.substring(0, 4) + "-" + str1.substring(4, 6) + "-" + str1.substring(6, 8) + "～"
        		+ str2.substring(0, 4) + "-" + str2.substring(4, 6) + "-" + str2.substring(6, 8);
        }
        return newDateStr;
    }
    
    $scope.gotoDetail=function(item){
    	$scope.selectedItem = item;
    	$scope.initSel = {
    		  msisdn: "",
        };
    	$scope.querySpokesmanList();
    };
    
    $scope.formatDateSpokes=function(para1,para2){
        if(!para1||!para2){
            return ' ';
        }
        var newDateStr1="",
            newDateStr2="";
        newDateStr1=para1.substr(0,4)+'.'+para1.substr(4,2)+'.'+para1.substr(6,2);
        newDateStr2=para2.substr(0,4)+'.'+para2.substr(4,2)+'.'+para2.substr(6,2);
        return newDateStr1+'～'+newDateStr2;
    };
    
    $scope.formatRegion=function(province,city){
        if(!province&&!city){
            return ' '
        }
        return province.provinceName+(city.cityName ? "-"+city.cityName:'');
    };
    
    $scope.ensureToSend=function(){
		$scope.tip='COMMON_SENDWINNINGNOTIFY';
        $('#ensureToSend').modal();
	}
	
   $scope.sendWinningNotify=function(){
     var req = {
         "activityID":$scope.selectedItem.activityID,
     };
     RestClientUtil.ajaxRequest({
         type: 'POST',
         url: "/ecpmp/ecpmpServices/activityService/sendWinningNotify",
         data: JSON.stringify(req),
         success: function (result) {
             $rootScope.$apply(function () {
                 var data = result.result;
                 if (data.resultCode == '1030100000') {
                	 $scope.tip = "SPOKES_SENDNOTIFYSUCC";
                     $('#myModal').modal();
                 } else {
                     $scope.tip = data.resultCode;
                     $('#myModal').modal();
                 }
             })
         },
         error: function () {
             $rootScope.$apply(function () {
                 $scope.tip = "1030120500";
                 $('#myModal').modal();
             }
             )
         }
     });
   }
   
    $scope.querySpokesmanList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "msisdn":$scope.initSel.msisdn,
                "activityID":parseInt($scope.selectedItem.activityID),
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[1].pageSize),
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTempSpokes = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTempSpokes;
            req.page.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[1].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/spokeService/querySpokesmanList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.spokesmanList = result.spokesmanList||[];
                        $scope.pageInfo[1].totalCount = parseInt(result.totalcount) || 0;
                        $scope.pageInfo[1].totalPage = 
                        	$scope.pageInfo[1].totalCount!==0 ? Math.ceil(result.totalcount / parseInt($scope.pageInfo[1].pageSize)):1;
                        $('#querySpokesmanList').modal();
                    } else {
                        $scope.spokesmanList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.spokesmanList = [];
                    $scope.pageInfo[1].currentPage = 1;
                    $scope.pageInfo[1].totalCount = 0;
                    $scope.pageInfo[1].totalPage = 1;
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                }
                )
            }
        });
    }
    
    $scope.exportFile=function(){
      var req = {
        "param":{
          "activityID":$scope.reqTemp.activityStatCond.activityID||"",
          "activityName":$scope.reqTemp.activityStatCond.activityName||"",
          "processStatus":$scope.reqTemp.activityStatCond.processStatus||"",
          "enterpriseName":$scope.reqTemp.activityStatCond.enterpriseName||"",
          "agentEnterpriseName":$scope.reqTemp.activityStatCond.agentEnterpriseName||"",
          "enterpriseType":$scope.reqTemp.activityStatCond.enterpriseType||"",
          "enterpriseID": "",
          "type":3,
          "token":$scope.token,
          "isExport":1
        },
        "url":"/qycy/ecpmp/ecpmpServices/activityService/downActivityStatCsvFile",
        "method":"get"
      }
      CommonUtils.exportFile(req);
    }
	$scope.queryactStatList = function (condition) {
        if(condition!='justPage'){
            var req={
                "activityStatCond":{
                    "activityID":$scope.activityID||"",
                    "activityName":$scope.activityName||"",
                    "processStatus":$scope.processStatus||"",
                    "enterpriseName":$scope.enterpriseName||"",
                    "agentEnterpriseName":$scope.agentEnterpriseName||"",
                    "enterpriseType":$scope.enterpriseType||"",
                },
                "page":{
                    "pageNum":1,
                    "pageSize":parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal":"1",
                }
            };
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.page.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
			type: 'post',
			url: "/ecpmp/ecpmpServices/activityService/queryActivityStatList",
			data: JSON.stringify(req),
			success: function (result) {
				$rootScope.$apply(function () {
                    var data = result.result;
                    if(data.resultCode=='1030100000'){
                        $scope.activityStatList=result.activityStatList||[];
                        $scope.pageInfo[0].totalCount=parseInt(result.totalAmount)||0;
                        $scope.pageInfo[0].totalPage = 
                        	result.totalAmount!=0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)):1;
                    }else{
                        $scope.activityStatList=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
				})
				
            },
            error:function(){
                $rootScope.$apply(function(){
                    $scope.activityStatList=[];
                    $scope.pageInfo[0].currentPage=1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage=1;
                    $scope.tip="1030120500";
                    $('#myModal').modal();
                    }
                )
            }
		});
          
	};
	
	  $scope.enterpriseinfo = function () 
	  {
	  	if (null == $.cookie('enterpriseID')) 
	  	{
				return;
			}
	  	var req = {
	  	        "id": $.cookie('enterpriseID'),
	  	        "pageParameter": {
	  	          "pageNum": 1,
	  	          "pageSize": 100,
	  	          "isReturnTotal": "1"
	  	        }
	  	      }
	  	      /*查询企业列表*/
	  	      RestClientUtil.ajaxRequest({
	  	        type: 'POST',
	  	        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
	  	        data: JSON.stringify(req),
	  	        success: function (data) {
	  	          $rootScope.$apply(function () {
	  	            var result = data.result;
	  	            if (result.resultCode == '1030100000') {
	  	            	console.log(data.enterprise);
	  	              $scope.enterpriseName = data.enterprise.enterpriseName;
	  	              $scope.businessStatus = data.enterprise.businessStatus;
	  	              $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
	  	              console.log($scope.businessStatus);
	  	              if ($scope.businessStatus == 1) {
	  	            	  $('#Modalisaengt').modal();
	  	              }
	  	            }
	  	          })
	  	        }
	  	      });
	  };
	
	
	
}])
app.config(['$locationProvider', function($locationProvider) {
    $locationProvider.html5Mode({
        enabled:true,
        requireBase:false
    });
  }])