<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.TaskRetryMapper">
    <resultMap id="taskRetryWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.TaskRetryWrapper">       
        <result property="taskID" column="taskId" javaType="java.lang.Integer" />
        <result property="taskType" column="taskType" javaType="java.lang.Integer" />
        <result property="taskdesc" column="taskdesc" javaType="java.lang.String" />
        <result property="dateOrMonth" column="dateOrMonth" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="Date" />
        <result property="updateTime" column="updateTime" javaType="Date" />
        <result property="taskStatus" column="taskStatus" javaType="java.lang.Integer" />
    </resultMap>
    
    <select id="getRetryTasks" resultMap="taskRetryWrapper">
		select taskId ,taskType ,taskdesc ,dateOrMonth ,taskStatus ,createTime ,updateTime from ecpm_t_retry_task where taskStatus = 0     
    </select>
    
	<update id="updateRetryTaskStatus">
		update ecpm_t_retry_task set taskStatus = #{taskStatus} , updateTime = #{updateTime} where taskId = #{taskID} 
    </update>
    
</mapper>