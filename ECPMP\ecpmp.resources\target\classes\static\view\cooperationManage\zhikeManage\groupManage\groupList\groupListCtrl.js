var app = angular.module("myApp", ["util.ajax", "page", "top.menu", "angularI18n", "cy.uploadifyfile","service.common"])
/**/
app.controller('groupListController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        $scope.groupListType = [];
        $scope.filtChannel = $location.search().enterpriseType;
        $scope.isZyzq = sessionStorage.getItem("isZyzq") || "";
        $(document).on('hidden.bs.modal', '.modal', function () {
            $('.modal:visible').length && $(document.body).addClass('modal-open');
        });
        $('#addMenbPop').on('shown.bs.modal', function () {
            var evt = document.getElementById('addMenbSelect1');
            var index =0;
            for(var i=0;i<evt.options.length;i++){
                evt.options[i].setAttribute("title", evt.options[i].text);
                if($scope.getTextWidth(evt.options[i].label) >344){
                    index =$scope.getSubIndex(evt.options[i].text,i);
                    evt.options[i].label = evt.options[i].text.substr(0,index)+'...';
                }
            }

            evt = document.getElementById('addMenbSelect2');
            for(var i=0;i<evt.options.length;i++){
                evt.options[i].setAttribute("title", evt.options[i].text);
                if($scope.getTextWidth(evt.options[i].label) >344){
                    index =$scope.getSubIndex(evt.options[i].text,i);
                    evt.options[i].label = evt.options[i].text.substr(0,index)+'...';
                }
            }
        })


        $scope.reserved3 = function(){
            // $('#addGroupInfoReserved3').trigger('keyup');
            let select = document.getElementById('addGroupInfoReserved3')
            //
            select.setAttribute("size",select.options.length)

            $scope.addGroupInfoReserved3 = true;
            // select.focus();
            // var WshShell = new ActiveXObject("Wscript.Shell");
            // try {
            //     WshShell.SendKeys("%{DOWN}");
            // } catch(e){}
            // WshShell.Quit;
        }
        $scope.closeReserved3 = function(){
            // $('#addGroupInfoReserved3').trigger('keyup');
            let select = document.getElementById('addGroupInfoReserved3');
            $scope.addGroupInfoReserved3 = false;
            select.setAttribute("size",0)
            // select.focus();
            // var WshShell = new ActiveXObject("Wscript.Shell");
            // try {
            //     WshShell.SendKeys("%{DOWN}");
            // } catch(e){}
            // WshShell.Quit;
        }
        $scope.numChoise = [
            {
                id: 100,
                name: "100"
            },
            {
                id: 200,
                name: "200"
            },
            {
                id: 300,
                name: "300"
            },
            {
                id: 500,
                name: "500"
            },
            {
                id: 1000,
                name: "1000"
            }
        ];
        $scope.token = $.cookie("token");
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        $scope.selectedRetryListTemp = [];
        $scope.isSuperManager = false;
        $scope.errorInfo = "";
        $scope.fileUrl = "";
        $scope.businessInterim= "";
        $scope.isShowShareQuota = true; // 初始化展示 业务融合包状态
        $scope.desc = "必填，仅支持xlsx格式";
        var loginRoleType=$.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
        $scope.isZhike = (loginRoleType=='zhike');
        $scope.isAgent = (loginRoleType=='agent');
        $scope.isProvincial = (loginRoleType=='provincial');
        $scope.isSubEnterpirse = (loginRoleType == 'subEnterprise');
        $scope.enterpriseType= $.cookie('enterpriseType') || '';
        $scope.loginRoleType = loginRoleType;
        if($scope.isZhike){
            $scope.enterpriseType ='1';
        }
        if($scope.isAgent){
            $scope.enterpriseType ='3';
        }
        if($scope.isProvincial){
            $scope.enterpriseType ='5';
        }
        if($scope.isSubEnterpirse){
            $.cookie("subEnterpriseID",$.cookie('enterpriseID'),{path:'/'});
        }
        //获取enterpriseID
        $scope.enterpriseID = parseInt($.cookie('enterpriseID'));
        if($scope.enterpriseType == '3' && $.cookie('subEnterpriseID')){
            $scope.enterpriseID = parseInt($.cookie('subEnterpriseID'));
        }
        //获取企业id配置项信息
        CommonUtils.isOpenAgent($scope.enterpriseID,function (enterprisesids){
            $scope.isOpenAgentValue = enterprisesids&&enterprisesids.indexOf($scope.enterpriseID)>=0;
            console.log('group',$scope.isOpenAgentValue);
        });

        $scope.provinceId = null;
        $scope.isEcProvince = null;
        $scope.is5GEnterprise = false;
        $scope.queryEnterpriseInfo();
        // 获取名片彩印套餐数据
        $scope.getProperties()
        // 获取行业挂机短信套餐数据
        $scope.getHYProperties()
        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'batchCreateMember'
        };
        $scope.uploadDelParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'importBatchDeleteMember'
        };
        // 上传excel  END
        $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileName = broadData.file.name;
            } else {
                $scope.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.errorInfo = broadData.errorInfo;
            $scope.fileUrl = fileUrl;
        });

        // 删除成员上传excel  END
        $scope.$on("uploadifyid2", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileName = broadData.file.name;
            } else {
                $scope.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.errorInfo = broadData.errorInfo;
            $scope.fileUrl = fileUrl;
        });

        if (!$scope.isSuperManager) {

        }
        $scope.enterpriseName = $.cookie('enterpriseName') || '180';
        $scope.accountID = $.cookie('accountID') || '1000';
        $scope.groupNameExist = false;
        $scope.memberMsisdnExist = false;
        $scope.groupNameVali = true;
        $scope.memberMsisdnVali = true;
        $scope.memberNameVali = true;
        $scope.orgSelected = 'true';
        $scope.choseIndex = 3;
        $scope.isZYZQ = $.cookie('reserved10') == "111";
        if ($scope.enterpriseType =='5' && $scope.isSuperManager)
        {
            var proSupServerType = $.cookie('proSupServerType');
            $scope.proSupServerType = $.cookie('proSupServerType');
            if (proSupServerType)
            {
                var value = JSON.parse(proSupServerType);
                for (var i = 0; i < value.length; i++) {
                    var index = value[i];
                    if (index == 23)
                    {
                        $scope.choseIndex = i;
                    }
                }
            }
        }

        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.orgTypeMap = {
            "1": "实体组织",
            "2": "虚拟组织"
        };
        $scope.ListStatusMap = {
            "1": "生效",
            "2": "失效",
            "4": "预生效"
        };

        $scope.DeliveryStatusMap = {
            "1": "使用中",
            "0": "超出配额",
            "": "-",
            null:"-"
        };
        $scope.servTypeStatus = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "退订成功"
            },{
                id: 2,
                name: "退订失败"
            },{
                id: 3,
                name: "退订中"
            },{
                id: 4,
                name: "企管订购关系退订"
            },
        ];
        $scope.servTypeChannel = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "反向退订-企管平台"
            },
            // {
            //     id: 2,
            //     name: "正向退订-分省"
            // },
            // {
            //     id: 3,
            //     name: "正向退订-集客大厅"
            // },
            {
                id: 4,
                name: "正向退订-中央平台"
            },
        ];

        /*$scope.statusMap={
         "0":"待提交同步请求",
         "1":"提交同步请求成功",
         "2":"提交同步请求失败",
         "3":"待提交同步请求",
         "4":"提交同步请求成功",
         "5":"提交同步请求失败",
         };*/
        $scope.statusMap = {
            "0": "订购中",
            "1": "订购中",
            "2": "订购失败",
            "3": "订购成功",
            "4": "订购失败",
            "5": "订购失败",
            "6": "取消订购",
            "7": "确认信息下发失败",
            "8": "订购失败",
            "9": "BBOSS同步中",
            "11": "待加入",
            "12": "待删除",
            "13": "退订失败"
        };
        $scope.unSubscribeStatusMap = {
            "1": "退订中",
            "2": "退订中",
            "4": "退订中"
        };
        $scope.groupListData = [];

        $scope.status = null;
        $scope.statusChoise = [
            {
                id: 0,
                name: "订购中"
            },
            {
                id: 3,
                name: "订购成功"
            },
            {
                id: 8,
                name: "订购失败"
            },
            {
                id: 9,
                name: "BBOSS同步中"
            },
            {
                id: 11,
                name: "待加入"
            },
            {
                id: 12,
                name: "待删除"
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            msisdn: "",
            startTime: "",
            endTime: "",
            orgName: "",
            unsubTimeStart:"",
            unsubTimeEnd:"",
            unsubStatus:"",
            channel:"",
        };
        //退订状态
        $scope.unsubStatusMap = {
            "1": "退订成功",
            "2": "退订失败",
            "3": "退订中",
            "4": "企管订购关系退订"
        };
        //退订渠道
        $scope.channelStatusMap = {
            "1": "反向退订-企管平台",
            "2": "正向退订-分省",
            "3": "正向退订-集客大厅",
            "4": "正向退订-中央平台"
        };


        $scope.queryGroupList();

        $scope.pushChannel();
        //ServiceRule判断按钮权限
        //$scope.getButtonPermissions();
    };

    $scope.getButtonPermissions = function (){
        var req = {
            "enterpriseID": parseInt($scope.enterpriseID),
            "servTypes": [1]
        };
        $scope.isAllowMangementMember = 2;
        $scope.isAllowMoveMember = true;
        var serverTime = CommonUtils.getServerTime();
        $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.stopSubServerTypeList = [];
                        $scope.subServerTypeList = result.syncServiceRuleList || [];
                        var edits = document.getElementsByClassName("edit");
                        var impoMebrs = document.getElementsByClassName("impoMebr");
                        for (var i in $scope.subServerTypeList) {
                            var item = $scope.subServerTypeList[i];
                            $scope.isAllowMoveMember = !item.allowMoveMember;
                            if ($scope.nowTime >= item.effectiveTime && (!item.expiryTime||$scope.nowTime <= item.expiryTime)) {
                                if(!item.isAllowMangementMember ||item.isAllowMangementMember == 0){
                                    $scope.isAllowMangementMember = 0;
                                    break;
                                }else {
                                    $scope.isAllowMangementMember = 1;
                                }
                            }
                        }
                        $scope.allButton = false;
                        if($scope.enterpriseType ==="5" && $scope.isEcProvince === "111"){
                            if ($scope.subServerTypeList !== []) {
                                for (var i in $scope.subServerTypeList) {
                                    var item = $scope.subServerTypeList[i];
                                    if (!(((!item.expiryTime)||item.effectiveTime<$scope.nowTime< item.expiryTime) && item.status === 1)) {
                                        //置灰链接新增成员、导入成员
                                        $scope.allButton = true;
                                        break;
                                    }
                                }
                            }else{
                                //所有都置灰
                                $scope.allButton = true;
                            }
                        }
                        $scope.batchDelButton = false;
                        if($scope.enterpriseType ==="5" && $scope.isEcProvince === "111"){
                            if ($scope.subServerTypeList !== []) {
                                for (var i in $scope.subServerTypeList) {
                                    var item = $scope.subServerTypeList[i];
                                    if (!(((!item.expiryTime)||item.effectiveTime<$scope.nowTime< item.expiryTime) && item.status === 1)) {
                                        //i.	若当前企业不为杭研（当前企业不为指定外部接入子企业，只针对子企业管理）、咪咕音乐（当前企业信息enterpriseType=5，且reserved10=113，只针对分省企业管理），且同步业务规则列表中存在reserved2不为1（不允许成员操作，只针对分省企业管理），则查询结果列表新增链接
                                        $scope.batchDelButton = true;
                                        break;
                                    }
                                }
                            }else{
                                //所有都置灰
                                $scope.batchDelButton = true;
                            }
                        }
                    } else {
                        $scope.subServerTypeList = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
    $scope.pushChannel = function (){
        if($scope.filtChannel == "provincial"){
            var chanel1 = {
                id: 2,
                name: "正向退订-分省"
            }
            var chanel2 = {
                id: 3,
                name: "正向退订-集客大厅"
            }
            $scope.servTypeChannel.push(chanel1)
            $scope.servTypeChannel.push(chanel2)
        }
    }
    // 获取当前时间YYYYMMDDHHMMSS
    $scope.getCurrentDate = function () {
        var today = new Date();
        var year = today.getFullYear()+'';
        var month = today.getMonth() + 1;
        month = month < 10 ? '0'+month : month;
        var day = today.getDate() < 10 ? '0'+today.getDate() : today.getDate();
        var hours = today.getHours() < 10 ? '0'+today.getHours() : today.getHours();
        var mins = today.getMinutes() < 10 ? '0'+today.getMinutes() : today.getMinutes();
        var secs = today.getSeconds() < 10 ? '0'+today.getSeconds() : today.getSeconds();

        return year + month + day + hours + mins + secs;
    }
    $scope.isMonthBuQuota = false;
    $scope.isMonthBuQuota0= false;
    $scope.pxbw = false;
    $scope.pxyw = false;
    $scope.gdbw = false;
    $scope.gdyw = false;
    $scope.gcbw = false;

    $scope.pxFlag = true;
    $scope.gdFlag= true;
    $scope.gcFlag= true;
    $scope.isShowPX = true;

    // 获取订购列表信息
    $scope.queryOrderList = function () {
        var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID": $scope.enterpriseID,
                "servType": 1,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };

        var subServTypes = new Array();

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '**********') {
                        // debugger
                        $scope.orderListData = data.subscribeInfoList || [];
                        for(let i = 0;i<$scope.orderListData.length;i++) {
                            if ($scope.isZYZQ) {
                                $scope.orderListData[i].reservedsEcpmp.reserved4 = 0;
                            }
                            if ($scope.orderListData[i].reservedsEcpmp
                                && 1 == $scope.orderListData[i].reservedsEcpmp.reserved4) {
                                $scope.isMonthBuQuota = true;
                            }
                            if ($scope.orderListData[i].reservedsEcpmp
                                && 0 == $scope.orderListData[i].reservedsEcpmp.reserved4) {
                                $scope.isMonthBuQuota0 = true;
                            }
                            if ($scope.orderListData[i].subServType == 1 || $scope.orderListData[i].subServType == 2 || $scope.orderListData[i].subServType == 3) {
                                if ($scope.orderListData[i].reservedsEcpmp && $scope.orderListData[i].reservedsEcpmp.reserved1 == 1) {
                                    $scope.pxbw = true;
                                } else if ($scope.orderListData[i].reservedsEcpmp && ($scope.orderListData[i].reservedsEcpmp.reserved1 == 2 || $scope.orderListData[i].reservedsEcpmp.reserved1 == 3)) {
                                    $scope.pxyw = true;
                                }
                            } else if ($scope.orderListData[i].subServType == 4) {
                                if ($scope.orderListData[i].reservedsEcpmp && $scope.orderListData[i].reservedsEcpmp.reserved1 == 1) {
                                    $scope.gdbw = true;
                                } else if ($scope.orderListData[i].reservedsEcpmp && ($scope.orderListData[i].reservedsEcpmp.reserved1 == 2 || $scope.orderListData[i].reservedsEcpmp.reserved1 == 3)) {
                                    $scope.gdyw = true;
                                }
                            } else if ($scope.orderListData[i].subServType == 8) {
                                $scope.gcbw = true;
                            }

                            //若当前企业信息needServiceProduct=1
                            if ($scope.needServiceProduct == 1){
                                if ($scope.orderListData[i].reservedsEcpmp
                                    && 1 == $scope.orderListData[i].reservedsEcpmp.reserved4) {
                                    $scope.isMonthBuQuota = true;
                                    subServTypes.push($scope.orderListData[i].subServType)
                                }
                            }
                        }

                        //若不存在屏显（subServType=1/2/3）按条包月的订购关系，或企业业务套餐存在记录，则页面隐藏屏显
                        if ($scope.needServiceProduct == 1 && $scope.isMonthBuQuota) {
                            //若不存在屏显（subServType=1/2/3）按条包月的订购关系
                            if (!subServTypes.includes(1) && !subServTypes.includes(2) && !subServTypes.includes(3)) {
                                $scope.pxFlag = false
                            }
                            //若不存在挂短（subServType=4）按条包月的订购关系
                            if (!subServTypes.includes(4) || $scope.serviceProductExistOther) {
                                $scope.gdFlag = false
                            }
                            //若不存在挂彩（subServType=8）按条包月的订购关系
                            if (!subServTypes.includes(8)) {
                                $scope.gcFlag = false
                            }
                            if (!$scope.pxFlag || $scope.serviceProductExist) {
                                $scope.isShowPX = false;
                            }
                        }
                        //如果不存在有效的非按条包月，则添加时只能添加按条包月
                        if(!$scope.isMonthBuQuota0){
                            // $('.black-white .redio-li').eq(1).unbind("ng-click");
                            $('.black-white .redio-li').eq(1).find("span").css({cursor: 'not-allowed', color: 'gray'});
                        }
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });

    }
    $scope.queryEnterpriseInfo = function () {
        var req = {};
        req.id = $scope.enterpriseID;
        var pageParameter = {};
        pageParameter.pageNum = 1;
        pageParameter.pageSize = 1000;
        pageParameter.isReturnTotal = 1;
        req.pageParameter = angular.copy(pageParameter);
        /*查询企业列表*/
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$applyAsync(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.enterpriseName = data.enterprise.enterpriseName;
                        $scope.provinceId = data.enterprise.provinceID;
                        $scope.isZYZQ = data.enterprise.reservedsEcpmp.reserved10 == "111"?true:false;
                        $scope.isMiguMusic = data.enterprise.reservedsEcpmp.reserved10 == "113"?true:false;
                        $scope.parentEnterpriseID = data.enterprise.parentEnterpriseID;
                        $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});

                        CommonUtils.getProperties(["ecpmp.5GCard.enterprises"],function(params){
                                                    let enterprises = params["ecpmp.5GCard.enterprises"];
                                                    if(enterprises.split(",").includes($scope.parentEnterpriseID+"") && $scope.isSubEnterpirse){
                                                        $scope.is5GEnterprise = true;
                                                    }
                                                });

                        $scope.needServiceProduct = data.enterprise.needServiceProduct;
                        $scope.isEcProvince = data.enterprise.reservedsEcpmp.reserved10;
                        $scope.getButtonPermissions();


                        $scope.queryServiceProduct();
                        $scope.queryOrderList();

                    } else {

                        $scope.getButtonPermissions();
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $scope.getButtonPermissions();
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
    /* 从url中获取参数 */
    $scope.convertUrlToPara = function () {
        var url = angular.copy(window.location.href);
        url = url.split("?")[1];
        /*.substring(0, url.length-2)*/
        var res = {};
        if (!!url) {
            var para = url.split("&");
            var arr = [];
            var len = para.length;
            for (var i = 0; i < len; i++) {
                arr = para[i].split("=");
                res[arr[0]] = arr[1];
            }
        }
        return res;
    };

    $scope.removeMemberPop =  function (item) {
        if (item != 'all') {
            $scope.oneSelect = true;
            $scope.deleteSelect = item;
        }else{
            $scope.oneSelect = false;
        };
        $('#deleteMemberPop').modal();
    };
    $scope.moveMemberPop =  function (item) {
        $scope.groupListType =  [];
        $scope.selectedOrg = "";
        var group = $scope.findFromGroupList($scope.orgID, $scope.groupListAllData);
        var oriOrgID = group.oriOrgID;
        angular.forEach($scope.groupListAllData, function (temp) {
            if(temp.id != $scope.orgID && temp.branchType == "22" && temp.id != oriOrgID ){
                $scope.groupListType.push(temp);
            }
        });
        if (item != 'all') {
            $scope.oneSelect = true;
            $scope.deleteSelect = item;
        }else{
            $scope.oneSelect = false;
        }
        $('#moveMemberPop').modal();
    };
//找分组
    $scope.findFromGroupList = function (orgId, groupList) {
        for (let i = 0; i < groupList.length; i++) {
            if (groupList[i].id == orgId) {
                return groupList[i];
            }
        }
    }
    //移动操作
    $scope.move = function (item) {
        if(!$scope.selectedOrg){
            $scope.tip = '请选择分组';
            $('#myModal').modal();
            return;
        }
        if ($scope.groupListAllMap)
        {
            var newOriOrgID = $scope.groupListAllMap.get($scope.selectedOrg.id)? $scope.groupListAllMap.get($scope.selectedOrg.id) : 1;
            var oldOriOrgID = $scope.groupListAllMap.get($scope.orgID)?$scope.groupListAllMap.get($scope.orgID):2;

            if (newOriOrgID == oldOriOrgID || newOriOrgID == $scope.orgID || $scope.selectedOrg.id == oldOriOrgID)
            {
                $scope.tip = '目标分组不允许移动';
                $('#myModal').modal();
                return;
            }
        }
        if (item != 'all') {
            var removeReq = {
                "memberList": [$scope.deleteSelect],
                "sourceOrgID": $scope.deleteSelect.orgId,
                "targetOrgId": $scope.selectedOrg.id,
                "enterpriseID": $scope.enterpriseID,
                "servType":1
            };
        } else {
            var memberInfoList = [];
            angular.forEach($scope.memberListData, function (temp) {
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if(temp.id == $scope.selectedListTemp[i]&&temp.status == 3){
                        memberInfoList.push(temp);
                    }
                }
            });
            if(memberInfoList.length===0){
                $scope.tip = '需要移动的成员数量为0';
                $('#myModal').modal();
                return;
            }
            var removeReq = {
                //批量删除只支持当前页
                //"memberIDList":$scope.selectedList,
                "memberList":memberInfoList,
                "sourceOrgID": $scope.orgID,
                "targetOrgId": $scope.selectedOrg.id,
                "enterpriseID": $scope.enterpriseID,
                "servType":1
            };
            var flag =true;
            angular.forEach($scope.memberListData, function (temp) {
                //汇总订购状态为待加入、待删除的记录列表
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if(temp.id == $scope.selectedListTemp[i]&&(temp.status == 11 || temp.status == 12) && flag){
                        $scope.tip = '1030120111';
                        $('#myModal').modal();
                        flag = false;
                        break;
                    }
                }
            });
            if(!flag)
            {
                return;
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/moveMember",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    $('#delMemCancel').click();
                    $('#moveMemberPop').modal("hide");
                    if (result.resultCode == '**********') {
                        $scope.tip = "提交成功";
                        $('#myModal').modal();
                        $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
                        var queryOri = {"id": $scope.orgID};
                        $scope.queryMemberList(queryOri);

                    }else if(result.resultCode == '**********') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = "余额不充足，无法移动成员";
                        $('#myModal').modal();
                    } else if(result.resultCode == '1030120089') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = result.resultDesc;
                        $('#myModal').modal();
                    } else if(result.resultCode == '1030120090') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = "当前企业信控关闭，无法移动成员";
                        $('#myModal').modal();
                    }else if(result.resultCode == '1030120097') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = "目标分组关联的套餐包分组成员超配额";
                        $('#myModal').modal();
                    }
                    else if(result.resultCode == '1030120092') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = "目标分组与原分组所有设置需要保持一致（配额可高于原分组），配额数量将继承原分组配额";
                        $('#myModal').modal();
                    }


                    else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#delMemCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    //删除操作
    $scope.remove = function (item) {
        if (item != 'all') {
            var removeReq = {
                "memberIDList": [$scope.deleteSelect.id],
                "orgID": $scope.deleteSelect.orgId,
                "enterpriseID": $scope.enterpriseID,
                "servType":1
            };
        } else {
            var removeReq = {
                //批量删除只支持当前页
                //"memberIDList":$scope.selectedList,
                "memberIDList": $scope.selectedListTemp,
                "orgID": $scope.orgID,
                "enterpriseID": $scope.enterpriseID,
                "servType":1
            };
            var flag =true;
            angular.forEach($scope.memberListData, function (temp) {
                //汇总订购状态为待加入、待删除的记录列表
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if(temp.id == $scope.selectedListTemp[i]&&(temp.status == 11 || temp.status == 12) && flag){
                        $scope.tip = '1030120111';
                        $('#myModal').modal();
                        flag = false;
                        break;
                    }
                }
            });
            if(!flag)
            {
                return;
            }
        };
        var queryOri = {"id": $scope.orgID};

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/batchDeleteMember",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    $('#delMemCancel').click();
                    if (result.resultCode == '**********') {
                        $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
                        $scope.queryMemberList(queryOri);
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#delMemCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    //删除分组
    $scope.removeOrg = function () {
        var item = $scope.selectedItemDel;
        var removeReq = {
            "ids": [item.id],
            "operatorID": $scope.accountID,
            "enterpriseID": $scope.enterpriseID,
            "servType":1
        };
        item.isRemove = true;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/deleteOrganization",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        // $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
                        //$scope.queryGroupList();
                        $('#delOrgCancel').click();
                    } else {
                        $scope.tip = result.resultCode;
                        $('#delOrgCancel').click();
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#delOrgCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    //改变选择框
    $scope.changeSelected = function (item) {
        if ($.inArray(item.id, $scope.selectedListTemp) == -1) {
            $scope.selectedListTemp.push(item.id);
            $scope.selectedList.push(item.id);
        } else {
            $scope.selectedListTemp.splice($.inArray(item.id, $scope.selectedListTemp), 1);
            $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
        }
        if ($scope.selectedListTemp.length == $scope.memberListData.length) {
            $scope.allChoose = true;
        } else {
            $scope.allChoose = false;
        }
    };

    //更改全选框
    $scope.ifSelected = function () {
        console.log("$scope.allChoose----",$scope.allChoose)
        // debugger
        angular.forEach($scope.selectedListTemp, function (itemTemp) {
            $scope.selectedList.splice($.inArray(itemTemp.id, $scope.selectedList), 1);
        });
        if (!$scope.allChoose) {
            $scope.selectedListTemp = [];
            angular.forEach($scope.memberListData, function (item) {
                if(item.status != 1 && item.status != 9 && item.reserved4 != 1 && item.reserved4 != 2 && item.status != 12 && item.status != 11){
                    item.checked = true;
                    $scope.selectedList.push(item.id);
                    $scope.selectedListTemp.push(item.id);
                } else if ((item.status == 12 || item.status == 11) && item.rspDesc =='接口响应超时'){
                    item.checked = true;
                    $scope.selectedList.push(item.id);
                    $scope.selectedListTemp.push(item.id);
                }

            })
        } else {
            angular.forEach($scope.memberListData, function (item) {
                if(!item.status != 12 && !item.status != 11){
                    item.checked = false;
                    $scope.selectedListTemp = [];
                }
            })

        }
    };
    $scope.exportMember = function () {
        var req = {
            "param": {
                "orgID": $scope.orgID,
                "msisdn": $scope.msisdn,
                "containBranchOrg": 1,
                "enterpriseID": $scope.enterpriseID,
                "enterpriseType": $scope.enterpriseType,
                "token": $scope.token,
                "isExport": 1,
                "serviceType": 1

            },
            "url": "/qycy/ecpmp/ecpmpServices/organizationService/downMemberCsvFileService",
            "method": "get"
        }
        if($scope.isMonthBuQuota == 1&&$scope.groupDiffType){
            req.param.groupDiffType = $scope.groupDiffType;
        }
        if($scope.isMonthBuQuota == 1&&$scope.groupDiffType2){
            req.param.groupDiffType2 = $scope.groupDiffType2;
        }
        req.param.orgInfo = JSON.stringify($scope.selectedItem.ecpmReserveds);
        if(($scope.isZYZQ || $scope.isMiguMusic)&&$scope.groupDiffType){
            req.param.groupDiffType = $scope.groupDiffType;
            req.param.reserved4 = $scope.subServerTypeList[0].reservedsEcpmp.reserved4;
        }
        if(!req.param.groupDiffType){
            req.param.groupDiffType = 3;
        }
        if(!req.param.groupDiffType2){
            req.param.groupDiffType2 = 3;
        }
        if($scope.reserved10){
            req.param.reserved10 = $scope.reserved10;
        }
        CommonUtils.exportFile(req);
    };
    $scope.exportFaildList = function () {
        var req = {
            "param":{
                "orgID":$scope.orgID
            },
            "url":"/qycy/ecpmp/ecpmpServices/organizationService/downMemberSubFaild",
            "method":"get"
        }
        CommonUtils.exportFile(req);
    };

    $scope.exportUnsubFailBatch = function (item) {
        var req = {
            "param":{"req":JSON.stringify($scope.reqTemp)},
            "url":"/qycy/ecpmp/ecpmpServices/organizationService/downUnsubRecord",
            "method":"get"
        }
        if($scope.reqTemp != undefined)
        {
            CommonUtils.exportFile(req);
        }
    };

    //展示box
    $scope.showBox = function () {
        $scope.selectedListTemp = [];
        angular.forEach($scope.memberListData, function (memberData) {
            var index = $scope.selectedList.indexOf(memberData.id);
            if (index != -1) {
                $scope.selectedListTemp.push(memberData.id);
                item.checked = true;
            }
            ;
        });
    };

    $scope.formatDate = function (str) {
        if (!str) {
            return 'format error';
        }
        var newDateStr = "";
        newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
        return newDateStr;

    };
    $scope.addGroupInfo = {};

    //新增分组弹窗
    $scope.addGroup = function () {
        $scope.groupNameExist = false;
        $scope.groupNameVali = true;
        $scope.newGroup = true;
        $scope.groupNameDesc = '';
        $scope.addGroupInfo = {};
        $scope.groupNameTemp = null;
        $scope.businessInterim = "";
        $scope.addGroupInfo.enterpriseID = $scope.enterpriseID;
        $scope.addGroupInfo.operatorID = $scope.accountID;
        $scope.addGroupInfo.isMonthBuQuota = 1;
        $scope.addGroupInfo.diff = 1;
        $scope.addGroupInfo.diff2 = 0;

        $('#addGroupPop').modal();
        $scope.changeType(1);
        $scope.changeDiffere(1);
        $scope.changeDiffere2(0)
        $scope.isATBY=false
    };

    $scope.changeType = function (val,type) {
        //type为后续增加的参数，仅为了编辑分组时给addGroupInfo.isMonthBuQuota赋值
        //只有存在有效的非按条包月订单，新增分组时才可选择非按条包月
        if($scope.isMonthBuQuota0){
            $('.black-white .redio-li').eq(val).find('span').removeClass('checked');
            $('.black-white .redio-li').eq(val==0?1:0).find('span').addClass('checked');
            $('.black-white2 .redio-li').eq(val).find('span').removeClass('checked');
            $('.black-white2 .redio-li').eq(val==0?1:0).find('span').addClass('checked');

            $scope.addGroupInfo.isMonthBuQuota = val;

            if (val == 1){
                $scope.isATBY =true;
            }
        }

        //(补充：在编辑分组时可以赋值，因为编辑需要使用到addGroupInfo.isMonthBuQuota来做判断显示单选框)
        if(type == "edit"){
            $scope.addGroupInfo.isMonthBuQuota = val;
        }
    };
    $scope.changeDiffere = function (val) {
        $('.diff .redio-li').eq(val).find('span').removeClass('checked');
        $('.diff .redio-li').eq(val==0?1:0).find('span').addClass('checked');
        $('.diff2 .redio-li').eq(val).find('span').removeClass('checked');
        $('.diff2 .redio-li').eq(val==0?1:0).find('span').addClass('checked');
        $scope.addGroupInfo.diff = val;
        $scope.addGroupInfo.reserved3 = null;
        $scope.addGroupInfo.reserved4 = null;
        // $scope.addGroupInfo.reserved5 = null;
        // $scope.addGroupInfo.reserved6 = null;
        // $scope.addGroupInfo.reserved7 = null;
    };
    $scope.changeDiffere2 = function (val) {
        if(val === undefined){
            val = 0;
        }
        $('.diffgd .redio-li').eq(val).find('span').removeClass('checked');
        $('.diffgd .redio-li').eq(val==0?1:0).find('span').addClass('checked');
        $('.diffgd2 .redio-li').eq(val).find('span').removeClass('checked');
        $('.diffgd2 .redio-li').eq(val==0?1:0).find('span').addClass('checked');
        $scope.addGroupInfo.diff2 = val;
        // $scope.addGroupInfo.reserved3 = null;
        // $scope.addGroupInfo.reserved4 = null;
        $scope.addGroupInfo.reserved5 = null;
        $scope.addGroupInfo.reserved6 = null;
        // $scope.addGroupInfo.reserved7 = null;

    };

    //编辑
    $scope.gotoSet = function (item) {
        console.log(item.branchType && item.branchType!= 22);
        $scope.groupNameExist = false;
        $scope.groupNameVali = true;
        $scope.newGroup = false;

        $scope.groupNameDesc = '';
        // $scope.selectedItem = item;
        $scope.setGroupInfo = {};
        $scope.setGroupInfo.id = item.id;
        $scope.setGroupInfo.enterpriseID = $scope.enterpriseID;
        $scope.setGroupInfo.operatorID = $scope.accountID;
        $scope.setGroupInfo.orgName = item.orgName;
        $scope.groupNameTemp = item.orgName;
        // $scope.queryMemberList(item);
        $('#setGroupPop').modal();
        $scope.changeType(item.ecpmReserveds.reserved2&&!$scope.isZYZQ?1:0,"edit");
        $scope.changeDiffere(item.ecpmReserveds.reserved2);
        $scope.changeDiffere2(item.ecpmReserveds.reserved8);

        $scope.setGroupInfo.reserved2 = item.ecpmReserveds.reserved2;
        $scope.setGroupInfo.reserved8 = item.ecpmReserveds.reserved8;

        $scope.setGroupInfo.reserved3 = parseInt(item.ecpmReserveds.reserved3);
        $scope.reservededit3 = $scope.setGroupInfo.reserved3;
        $scope.selInpErr = false;
        $scope.selInpErr2 = false;
        $scope.setGroupInfo.reserved4 = parseInt(item.ecpmReserveds.reserved4);
        $scope.setGroupInfo.reserved5 = parseInt(item.ecpmReserveds.reserved5);
        $scope.setGroupInfo.reserved6 = parseInt(item.ecpmReserveds.reserved6);
        $scope.setGroupInfo.reserved7 = parseInt(item.ecpmReserveds.reserved7);
        $scope.setGroupInfo.reserved10 = parseInt(item.ecpmReserveds.reserved10);

        $scope.reservededit4 = $scope.setGroupInfo.reserved4;
        $scope.reservededit5 = $scope.setGroupInfo.reserved5;
        $scope.reservededit6 = $scope.setGroupInfo.reserved6;
        $scope.reservededit7 = $scope.setGroupInfo.reserved7;

    };

    //删除
    $scope.gotoDelete = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteGroupPop').modal();
    };

    //刷新页面
    $scope.checkResult = function (item) {
        $scope.queryGroupList();
    };

    //查询单个分组
    $scope.gotoDetail = function (item) {
        $scope.isShow = false;
        $scope.msisdn = '';
        $scope.selectedItem = item;
        $scope.isShowShareQuota = true;
        $scope.status = null;
        $scope.queryMemberList(item);
        $scope.groupDiffType =  item.ecpmReserveds.reserved2;
        $scope.groupDiffType2 =  item.ecpmReserveds.reserved8;
        $scope.reserved10 =  item.ecpmReserveds.reserved10;
        if ($scope.reserved10 == 1 && $scope.groupDiffType == 0 && $scope.groupDiffType2 == 0){
            $scope.isShowShareQuota = false
        }
        $scope.selectItem = item;
        if($scope.isZYZQ || $scope.isMiguMusic){
            $scope.groupDiffType =  2;
            $scope.groupDiffType2 =  2;

        }
        $('#detailListPop').modal();
    };
    $scope.checkSelectLabelLen = function (text,offset) {
        label =text;
        length = angular.copy(label.length);
        for ( var j = 0; j < label.length; j++) {
            if (label.charAt(j) > '~' || label.charAt(j) < ' ') {
                length = length + offset;
            }
        }
        return length;
    }

    $scope.getSubIndex = function (label,i) {
        var index =0;
        for(var j=0;i<label.length;j++){
            var labelTemp=label.substr(0,j);
            if($scope.getTextWidth(labelTemp) >341){
                index = j-1;
                return index;
            }
        }
        return index;
    }

    /**
     * js获取文本显示宽度
     * @param str: 文本
     * @return 文本显示宽度
     */
    $scope.getTextWidth = function(str) {
        var w = $('body').append($('<span stlye="display:none;" id="textWidth"/>')).find('#textWidth').html(str).width();
        $('#textWidth').remove();
        return w;
    }

    //新增成员弹窗--表格外
    $scope.addMenbOutList = function () {

        $scope.groupListType = [];
        angular.forEach($scope.groupListAllData, function (temp) {
            if(temp.branchType == "22"){
                $scope.groupListType.push(temp);
            }
        });
        $scope.memberMsisdnExist = false;
        $scope.memberMsisdnVali = true;
        $scope.memberNameVali = true;
        $scope.memberMsisdnDesc = '';
        $scope.memberNameDesc = '';
        $scope.orgSelected = 'true';
        $scope.addMenbInfo = {};
        $scope.fromList = 'false';
        $scope.selectedMsisdnID = "";
        $scope.selectedMsisdnType = "";
        $scope.selectedId = "";
        $scope.checkMsisdnType = 'true';
        $scope.addMenbInfo.operatorID = $scope.accountID;
        $('#addMenbPop').modal();
    };

    //新增成员弹窗--表格内
    $scope.addMenbInList = function (item) {
        $scope.groupListType = $scope.groupListData;
        $scope.memberMsisdnExist = false;
        $scope.memberMsisdnVali = true;
        $scope.memberNameVali = true;
        $scope.memberMsisdnDesc = '';
        $scope.memberNameDesc = '';
        $scope.orgSelected = 'true';
        $scope.addMenbInfo = {};
        $scope.addMenbInfo.selectedOrg = item;
        $scope.addMenbInfo.operatorID = $scope.accountID;
        $scope.fromList = 'true';
        $scope.selectedMsisdnID = "";
        $scope.selectedMsisdnType = "";
        $scope.selectedId = "";
        $scope.checkMsisdnType = 'true';
        $('#addMenbPop').modal();
    };

    $('#start').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });
    $('#end').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });
    $('#unsubStart').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });
    $('#unsubEnd').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });

    $('#start').on('changeDate', function () {
        $scope.searchOn("start");
    });

    $('#end').on('changeDate', function () {
        $scope.searchOn("end");
    });
    $('#unsubStart').on('changeDate', function () {
        $scope.unsubSearchOn("unsubStart");
    });

    $('#unsubEnd').on('changeDate', function () {
        $scope.unsubSearchOn("unsubEnd");
    });

//判断搜索按钮是否置灰
    $scope.searchOn = function (val) {
        $scope.uniqueTip = "";
        $scope.checkUnique = true;
        let startTime = document.getElementById("start").value;
        let endTime = document.getElementById("end").value;
        if((startTime !== '' && endTime != '') && startTime>endTime){
            if("start" === val){
                endTime = startTime;
            }else{
                startTime = endTime;
            }
        }

        if (startTime !== '') {
            $scope.initSel.startTime = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + startTime.substring(11, 13) + startTime.substring(14, 16) +'00';
            // $scope.initSel.startTime = startTime +':00';
            $("#start").datetimepicker("setDate", new Date(startTime.substring(0, 16) + ":00") );
        }
        else {
            $scope.initSel.startTime = "";
        }

        if (endTime !== '') {
            $scope.initSel.endTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + endTime.substring(11, 13) + startTime.substring(14, 16) + '59';
            // $scope.initSel.endTime = endTime +':00';
            $("#end").datetimepicker("setDate",  new Date(endTime.substring(0, 16) + ":00") );


        }
        else {
            $scope.initSel.endTime = "";
        }

        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }

    };
    //判断搜索按钮是否置灰 退订时间处理
    $scope.unsubSearchOn = function (val) {
        $scope.uniqueTip = "";
        $scope.checkUnique = true;
        let startTime = document.getElementById("unsubStart").value;
        let endTime = document.getElementById("unsubEnd").value;
        if((startTime !== '' && endTime != '') && startTime>endTime){
            if("unsubStart" === val){
                endTime = startTime;
            }else{
                startTime = endTime;
            }
        }

        if (startTime !== '') {
            $scope.initSel.unsubTimeStart = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + startTime.substring(11, 13) + startTime.substring(14, 16) +'00';
            // $scope.initSel.unsubTimeStart = startTime +':00';
            $("#unsubStart").datetimepicker("setDate", new Date(startTime.substring(0, 16) + ":00") );
        }
        else {
            $scope.initSel.unsubTimeStart = "";
        }

        if (endTime !== '') {
            $scope.initSel.unsubTimeEnd = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + endTime.substring(11, 13) + startTime.substring(14, 16) + '59';

            // $scope.initSel.unsubTimeEnd = endTime+':00';
            $("#unsubEnd").datetimepicker("setDate",  new Date(endTime.substring(0, 16) + ":00") );


        }
        else {
            $scope.initSel.unsubTimeEnd = "";
        }

        if ($scope.initSel.unsubTimeStart === '' && $scope.initSel.unsubTimeEnd === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.unsubTimeStart !== '' && $scope.initSel.unsubTimeEnd !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }

    };


    //导出成员删除记录弹窗
    $scope.delMenbOutList = function () {
        //若日期清掉，starttime和endtime都设置为空
        $scope.initSel.startTime = "";
        $scope.initSel.endTime = "";
        $scope.initSel.msisdn = "";
        $scope.initSel.unsubTimeStart = "";
        $scope.initSel.unsubTimeEnd = "";
        $scope.initSel.unsubStatus = "";
        $scope.initSel.channel = "";

        document.getElementById("start").value = "";
        document.getElementById("end").value = "";
        document.getElementById("unsubStart").value = "";
        document.getElementById("unsubEnd").value = "";
        $scope.groupName = "";
        $scope.msisdn = '';
        $scope.isShow = false;

        $scope.status = null;

        $scope.queryDelFailMemberList();

        // 打开名为'detailDelFailListPop'的模态框
        $('#detailDelFailListPop').modal();
    };

    //导入成员
    $scope.impoMebr = function (item) {
        $scope.orgID = item.id;
        $('#impoMebrPop').modal();
        $('#impoMebrPop').on('hidden.bs.modal', function () {
            $rootScope.$apply(function () {
                $("#filePicker").find("span").text("导入文件");
                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.errorInfo = "";
                $scope.fileName = "";
                $scope.fileUrl = "";
            })

        });
    };
    //导入删除成员
    $scope.impoDelMebr = function (item) {
        $scope.orgID = item.id;
        $('#impoDelMebrPop').modal();
        $('#impoDelMebrPop').on('hidden.bs.modal', function () {
            $rootScope.$apply(function () {
                $("#filePicker2").find("span").text("导入文件");
                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.errorInfo = "";
                $scope.fileName = "";
                $scope.fileUrl = "";
            })

        });
    };
    //删除
    $scope.delComp = function () {
        $('#delCompPop').modal();
    };

    $scope.importMember = function () {
        var req = {
            "enterpriseID": $scope.enterpriseID,
            "operatorID": $scope.accountID,
            "orgID": $scope.orgID,
            "path": $scope.fileUrl,
            "servType": 1
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/batchCreateMember",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $('#impoMebrPop').modal("hide");
                        if(data.existOtherProvince == 1){
                            $scope.tip2 = '成员号码中包括跨省号码';
                            $('#myModal2').modal();
                        }else{
                            $scope.tip = "导入成功";
                            $('#myModal').modal();
                        }

                    }else if(result.resultCode == '**********') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = "余额不充足，无法新增成员";
                        $('#myModal').modal();
                    }else if(result.resultCode == '1010120076') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = "当前企业信控关闭，无法新增成员";
                        $('#myModal').modal();
                    } else if(result.resultCode == '1030120038') {
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = "成员数量超出额度";
                        $('#myModal').modal();
                    }
                    else if (data.failedListUrl) {
                        if(data.existOtherProvince == 1){
                            $scope.tip2 = '成员号码中包括跨省号码';
                            $('#myModal2').modal();
                        }
                        $('#impoMebrPop').modal("hide");
                        $scope.tip = data.failedNum + "条导入失败，请查看失败清单。";
                        $('#importResultModel').modal();
                        $scope.failedFileUrl = data.failedListUrl;

                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //下载导入失败清单
    $scope.downloadFailFile = function () {
        var req = {
            "param":{
                "path":$scope.failedFileUrl,
                "token":$scope.token,
                "isExport":0
            },
            "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
            "method":"get"
        }
        CommonUtils.exportFile(req);
    }

    $scope.importDelMember = function () {
        var req = {
            "enterpriseID": $scope.enterpriseID,
            "operatorID": $scope.accountID,
            "orgID": $scope.orgID,
            "path": $scope.fileUrl,
            "servType": 1
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/importBatchDeleteMember",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $('#impoDelMebrPop').modal("hide");
                        $scope.tip = "导入成功";
                        $('#myModal').modal();
                    } else if (data.failedListUrl) {
                        $('#impoDelMebrPop').modal("hide");
                        $scope.tip = data.failedNum + "条导入失败，请查看失败清单。";
                        $('#importResultModel').modal();
                        $scope.failedFileUrl = data.failedListUrl;
                    } else {
                        $('#impoDelMebrPop').modal("hide");
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //查询分组列表
    $scope.queryGroupList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "enterpriseID": $scope.enterpriseID,
                "orgName": $scope.queryOrgName || '',
                "sortType": 2,
                "sortField": 1,
                "branchType": "22",
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryGroupListTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.queryGroupListTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryOrganizationList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$applyAsync(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.groupListData = result.organizationList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.groupListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });

        //查询号码类型列表
        RestClientUtil.ajaxRequest({
            type: 'GET',
            url: "/ecpmp/ecpmpServices/organizationService/queryMsisdnTypeList",
            success: function (result) {
                if (result.result.resultCode == '**********') {
                     $scope.isZyzq = sessionStorage.getItem("isZyzq") || "";
                    //$scope.msisdnTypeList = result.msisdnTypeList || [];
                    if (result.msisdnTypeList&&result.msisdnTypeList.length>3) {
                        var arr = result.msisdnTypeList || [];
                        var arr1 = arr.splice(1,2);
                        if(!$scope.provinceId){
                            setTimeout(function () {
                                if($scope.provinceId === "12"){
                                   if($scope.isZyzq  != 1) {  // 非集客
                                      arr.splice(2,6);
                                   }else{
                                      arr.splice(1,7);
                                   }
                                }else {
                                   arr.splice(1,7);
                                }
                            },1500);
                        }else {
                          if($scope.provinceId === "12"){
                               if($scope.isZyzq  != 1) { // 非集客
                                    arr.splice(2,6);
                                 }else{
                                    arr.splice(1,7);
                                 }
                          }else {
                              arr.splice(1,7);

                          }
                        }
                        $scope.msisdnTypeList = arr;
                    }
                } else {
                    $scope.msisdnTypeList = [];
                    $('#myModal').modal();
                }
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });

        var allReq = {
            "enterpriseID": $scope.enterpriseID,
            "orgName": '',
            "sortType": 2,
            "sortField": 1,
            "branchType": "22",
            "pageParameter": {
                "pageNum": 1,
                "pageSize":20000
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryOrganizationList",
            data: JSON.stringify(allReq),
            success: function (result) {
                $rootScope.$applyAsync(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.groupListAllData = result.organizationList || [];
                        var groupListAllMap = new Map();
                        angular.forEach($scope.groupListAllData, function (temp) {
                            groupListAllMap.set(temp.id, temp.oriOrgID);
                        });
                        $scope.groupListAllMap = groupListAllMap;
                    } else {
                        $scope.groupListAllData = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.selectedType = function (id) {
        if(id){
            $scope.selectedMsisdnID = id;
            $scope.checkMsisdnType = 'true';
        }else{
            $scope.selectedMsisdnID = "";
            $scope.selectedMsisdnType = "";
            $scope.checkMsisdnType = 'false';

        }
    }

    //新增分组
    $scope.createGroup = function () {
        $scope.addGroupInfo.reserved2 = $scope.addGroupInfo.diff;
        $scope.addGroupInfo.reserved8 = $scope.addGroupInfo.diff2;
        $scope.addGroupInfo.ecpmReserveds = {};
        $scope.addGroupInfo.ecpmReserveds.reserved2 = $scope.addGroupInfo.reserved2;
        $scope.addGroupInfo.ecpmReserveds.reserved3 =  $scope.addGroupInfo.reserved3;
        $scope.addGroupInfo.ecpmReserveds.reserved4 =  $scope.addGroupInfo.reserved4;
        $scope.addGroupInfo.ecpmReserveds.reserved5 =  $scope.addGroupInfo.reserved5;
        $scope.addGroupInfo.ecpmReserveds.reserved6 =  $scope.addGroupInfo.reserved6;
        $scope.addGroupInfo.ecpmReserveds.reserved7 =  $scope.addGroupInfo.reserved7;
        $scope.addGroupInfo.ecpmReserveds.reserved8 =  $scope.addGroupInfo.reserved8;
        $scope.addGroupInfo.ecpmReserveds.reserved10 =  $scope.addGroupInfo.reserved10;
        if(!$scope.isMonthBuQuota || $scope.addGroupInfo.isMonthBuQuota == 0){
            $scope.addGroupInfo.ecpmReserveds = {};
        }

        if ($scope.needServiceProduct == 1){
            if ((!$scope.pxFlag || $scope.serviceProductExist)){
                if ($scope.isATBY){
                    $scope.addGroupInfo.ecpmReserveds.reserved2 = 1;
                }
                $scope.addGroupInfo.ecpmReserveds.reserved3 = null;
                $scope.addGroupInfo.ecpmReserveds.reserved4 = null;
            }
            if (!$scope.gdFlag){
                $scope.addGroupInfo.ecpmReserveds.reserved5 = null;
                $scope.addGroupInfo.ecpmReserveds.reserved6 = null;
                $scope.addGroupInfo.ecpmReserveds.reserved8 = 0;
            }
            if (!$scope.gcFlag){
                $scope.addGroupInfo.ecpmReserveds.reserved7 =  null;
            }
        }
        if($scope.businessInterim){
            $scope.addGroupInfo.reserved5 = $scope.businessInterim;
            $scope.addGroupInfo.reserved3 = $scope.businessInterim;
            $scope.addGroupInfo.ecpmReserveds.reserved5 = $scope.businessInterim;
            $scope.addGroupInfo.ecpmReserveds.reserved3 = $scope.businessInterim;
            $scope.addGroupInfo.ecpmReserveds.reserved10 = 1;
            $scope.addGroupInfo.reserved10 = 1;

        }

        //不区分本异网配额的时候，reserved4提交的配额置为空
        if($scope.addGroupInfo.diff == 0){
            $scope.addGroupInfo.ecpmReserveds.reserved4 = null;
        }
        // //不区分本异网配额的时候，reserved4提交的配额置为空
        //  if($scope.addGroupInfo.diff2 == 0){
        //      $scope.addGroupInfo.ecpmReserveds.reserved8 = null;
        //  }

        var req = {
            "organization": $scope.addGroupInfo,
            "servType": 1
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/createOrganization",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.queryGroupList();
                        $('#addOrgCancel').click();
                    } else {
                        $('#addOrgCancel').click();
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#addOrgCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    //重新同步操作
    $scope.sync = function (item) {
        var syncReq = {
            "memberID": item.id,
            "orgID": $scope.orgID,
            "enterpriseID": $scope.enterpriseID,
            "servType": 1
        };
        var queryOri = {"id": $scope.orgID};

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/syncMember",
            data: JSON.stringify(syncReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.queryMemberList(queryOri);
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.bbsync = function (item) {
        var syncReq = {
            "orgID": $scope.orgID,
            "enterpriseID": $scope.enterpriseID,
            "servType": 1,
            "retryReason": 1
        };
        if(item == 'all') {
            var flag = true;
            var memberIDsList = [];
            var selectedRetryListTemp = [];
            angular.forEach($scope.memberListData, function (temp) {
                //汇总订购状态为待加入、待删除的记录列表
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if (temp.id == $scope.selectedListTemp[i]&& (temp.status == 11 || temp.status == 12)){
                        selectedRetryListTemp.push($scope.selectedListTemp[i]);
                    } else if (temp.id == $scope.selectedListTemp[i]) {
                        memberIDsList.push($scope.selectedListTemp[i]);
                    }
                    if(temp.id == $scope.selectedListTemp[i]&&(temp.status != 8 && temp.status != 11 && temp.status != 12) && flag){
                        if (temp.status != 8) {
                            $scope.tip = '1030120107';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        } else if(temp.status != 11 && temp.status != 12){
                            $scope.tip = '1030120111';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        } else {
                            $scope.tip = '1030120107';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        }
                    }
                }
            });
            if(!flag)
            {
                return;
            }
            syncReq.memberIDs = selectedRetryListTemp;
        } else {
            syncReq.memberID = item.id;
        }
        var queryOri = {"id": $scope.orgID};
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/retrySyncMember",
            data: JSON.stringify(syncReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.queryMemberList(queryOri);
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }


//bboss重新同步操作
    $scope.bsync = function (item) {
        var syncReq = {
            "orgID": $scope.orgID,
            "enterpriseID": $scope.enterpriseID,
            "servType": 1
        };
        if(item == 'all') {
            var flag = true;
            var memberIDsList = [];
            angular.forEach($scope.memberListData, function (temp) {
                //汇总订购状态为待加入、待删除的记录列表
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if (temp.id == $scope.selectedListTemp[i]&& (temp.status == 11 || temp.status == 12)){
                        $scope.selectedRetryListTemp.push($scope.selectedListTemp[i]);
                    } else if (temp.id == $scope.selectedListTemp[i]) {
                        memberIDsList.push($scope.selectedListTemp[i]);
                    }
                    if(temp.id == $scope.selectedListTemp[i]&&(temp.status != 8 && temp.status != 11 && temp.status != 12) && flag){
                        if (temp.status != 8) {
                            $scope.tip = '1030120107';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        } else if(temp.status != 11 && temp.status != 12){
                            $scope.tip = '1030120111';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        } else {
                            $scope.tip = '1030120107';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        }
                    }
                }
            });
            if(!flag)
            {
                return;
            }
            syncReq.memberIDs = memberIDsList;
        } else {
            syncReq.memberID = item.id;
        }
        if ($scope.selectedRetryListTemp != null && $scope.selectedRetryListTemp.length > 0){
            $scope.bbsync('all');
        }
        console.log(syncReq);
        var queryOri = {"id": $scope.orgID};
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/retrySyncMember",
            data: JSON.stringify(syncReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.queryMemberList(queryOri);
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    //新增成员
    $scope.createMember = function () {
        $scope.checkMemberName($scope.addMenbInfo.memberName);
        $scope.checkSelect();
        if ($scope.orgSelected == 'false' || !$scope.memberNameVali) {
            return;
        }
        var orgID = $scope.addMenbInfo.selectedOrg.id;
        $scope.addMenbInfo.enterpriseID = $scope.enterpriseID;
        delete($scope.addMenbInfo.selectedOrg);
        $scope.addMenbInfo.ecpmReserveds = {};
        $scope.addMenbInfo.ecpmReserveds.reserved2 = $scope.selectedMsisdnID;  //号码类型id

        var servType = 1;
        if($scope.selectedMsisdnID && ($scope.selectedMsisdnID ==3 || $scope.selectedMsisdnID ==2)){
            servType = 5;
        }

        if ($scope.addMenbInfo.msisdn){
            $scope.addMenbInfo.msisdn = $scope.addMenbInfo.msisdn.replace(/\b(0+)/gi,"");
        }
        var memberList = [];
        memberList[0] = $scope.addMenbInfo;
        var req = {
            "memberList": memberList,
            "orgID": orgID,
            "enterpriseID":$scope.enterpriseID,
            "servType": servType
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/createMember",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $('#addMemCancel').click();
                        if (result.memberList && result.memberList.length > 0) {
                            $scope.tip = result.memberList[0].result.resultCode;
                            $('#myModal').modal();
                        } else {
                            if (result.existOtherProvince == 1) {
                                $scope.tip2 = '该成员号码为跨省号码';
                                $('#myModal2').modal();
                            } else {
                                $scope.tip = 'GROUP_CREATEMEMBSUCC';
                                $('#myModal').modal();
                            }

                        }
                    }
                    else if(data.resultCode == '1030120089') {
                        $('#impoMebrPop').modal("hide");
                        $('#addMemCancel').click();
                        $scope.tip = data.resultDesc;
                        $('#myModal').modal();
                    }
                    else if(data.resultCode == '1030120090') {
                        $('#impoMebrPop').modal("hide");
                        $('#addMemCancel').click();
                        $scope.tip = "当前企业信控关闭，无法新增成员";
                        $('#myModal').modal();
                    }
                    else if(data.resultCode == '1030199999') {
                        $('#impoMebrPop').modal("hide");
                        $('#addMemCancel').click();
                        $scope.tip = "新增成员失败";
                        $('#myModal').modal();
                    }
                    else {
                        $('#addMemCancel').click();
                        if(result.memberList && result.memberList.length>0){
                            $scope.tip = result.memberList[0].result.resultCode;
                        }else{
                            $scope.tip = data.resultCode;
                        }
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#addMemCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.queryMemberList = function (item, condition) {
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        $scope.allChoose = false;
        if (condition != 'justPage') {
            $scope.orgID = item.id;
            var req = {
                "member": {
                    "id": "",
                    "msisdn": $scope.msisdn || "",
                    "enterpriseID": $scope.enterpriseID
                },
                "containBranchOrg": 1,
                "orgID": item.id,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[1].pageSize),
                    "isReturnTotal": "1",
                }
            };
            if($scope.isEcProvince == 111 && $scope.status != null) {
                if($scope.status == 0) {
                    req.member.statusList = [0,1];
                } else {
                    req.member.statusList = [$scope.status];
                }
            }
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
        }
        //        $scope.memberListData=[{"memberName":"1","msisdn":"2", "status":"3","id":"a","orgID":"v"},
        //        	{"memberName":"1","msisdn":"2", "status":"3","id":"b","orgID":"v"},
        //        	{"memberName":"1","msisdn":"2", "status":"3","id":"c","orgID":"v"},
        //        	{"memberName":"1","msisdn":"2", "status":"3","id":"d","orgID":"v"}];
        //        $scope.orgID="v"
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryMember",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.memberListData = result.memberList;
                        $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;
                        if($scope.subServerTypeList[0]?.reservedsEcpmp?.reserved4){
                            let object = JSON.parse($scope.subServerTypeList[0].reservedsEcpmp.reserved4);
                            $scope.memberListData.forEach(function (value){
                                value.currCount = object.quatoCurr;
                                value.diffCount = object.quatoDiff
                            })
                        }

                    } else {
                        $scope.memberListData = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.changeTimeFormtat = function (dateTimeString) {
       if(!dateTimeString){ return ""};
        // 解析字符串为 Date 对象
        var year = dateTimeString.substring(0, 4);
        var month = dateTimeString.substring(4, 6) - 1; // 月份从0开始，需要减1
        var day = dateTimeString.substring(6, 8);
        var hours = dateTimeString.substring(8, 10);
        var minutes = dateTimeString.substring(10, 12);
        var seconds = dateTimeString.substring(12);

        var date = new Date(Date.UTC(year, month, day, hours, minutes, seconds));

        // 获取各个部分并格式化
        var formattedDateTime = date.toISOString().replace('T', ' ').substring(0, 19);
        return formattedDateTime
    }

    $scope.queryDelFailMemberList = function (condition) {
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        $scope.allChoose = false;

        if (condition != 'justPage') {
            $scope.orgID = $scope.orgID;

            var req = {
                "enterpriseId": $scope.enterpriseID,
                "orgType": 1,
                "orgName": $scope.groupName,
                "orgID": $scope.orgID,
                // 创建时间开始
                "subTimeStart": $scope.initSel.startTime,
                // 创建时间结束
                "subTimeEnd": $scope.initSel.endTime,
                // 成员号码
                "msisdn":$scope.initSel.msisdn,
                // 退订时间
                "unsubTimeStart":$scope.initSel.unsubTimeStart,
                "unsubTimeEnd":$scope.initSel.unsubTimeEnd,
                // 退订状态
                "unsubStatus":$scope.initSel.unsubStatus,
                // 退订渠道
                "channel":$scope.initSel.channel,

                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[1].pageSize),
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
            req.pageParameter.isReturnTotal = "1";
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryUnsubRecord",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.unsubFailBatchList = result.memberUnsubRecords;
                        $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;

                    } else {
                        $scope.unsubFailBatchList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    /*校验select*/
    $scope.checkSelect = function () {
        $scope.orgSelected = 'true';
        if (!$scope.addMenbInfo?.selectedOrg) {
            $scope.orgSelected = 'false';
        }
    }

    /*校验各个字段*/
    $scope.validate = function (context, maxlength, reg) {
        if (!context) {
            return false;
        } else {
            if (context.length > maxlength) {
                return false;
            } else {
                if (!reg.test(context)) {
                    return false;
                } else {
                    return true;
                }
            }
        }
    };

    /* 验证名称是否超长*/
    $scope.checkMemberName = function (context) {
        $scope.memberNameVali = true;
        if (context) {
            if (context.length > 256) {
                $scope.memberNameVali = false;
            } else {
                var reg = /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/;
                if (!reg.test(context)) {
                    $scope.memberNameVali = false;
                } else {
                    $scope.memberNameVali = true;
                }
            }
            if (!$scope.memberNameVali) {
                $scope.memberNameDesc = 'GROUP_MEMBNAMEDESC';
                return;
            }
        }
    }

    /* 验证名称唯一 */
    $scope.checkDataUnique = function (context, type, condition) {
        $scope.type = type;
        $scope.condition = condition;
        if (type == '4')
        {
            if(condition == 'reserved3'){
                $scope.selInpErr = context < $scope.reservededit3;
                if($scope.selInpErr){
                    return;
                }
            }
            if(condition == 'reserved4'){
                $scope.selInpErr2 = context < $scope.reservededit4;
                if($scope.selInpErr2){
                    return;
                }
            }
            if(condition == 'reserved5'){
                $scope.selInpErr3 = context < $scope.reservededit5;
                if($scope.selInpErr3){
                    return;
                }
            }
            if(condition == 'reserved6'){
                $scope.selInpErr4 = context < $scope.reservededit6;
                if($scope.selInpErr4){
                    return;
                }
            }
            if(condition == 'reserved7'){
                $scope.selInpErr5 = context < $scope.reservededit7;
                if($scope.selInpErr5){
                    return;
                }
            }
        }

        if (type == '2') {
            if ($scope.enterpriseType && $scope.enterpriseType == '5') {
                $scope.memberMsisdnVali = $scope.validate(context, 11, /^[0-9]{11}$/);
            }else{
                $scope.memberMsisdnVali = $scope.validate(context, 11, /^[0-9]{11}$/);
            }

            if (!$scope.memberMsisdnVali && $scope.enterpriseType == '5') {
                $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC';
                return;
            }else if(!$scope.memberMsisdnVali){
                $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC';
                return;
            }
        } else {
            //如果非按条包月分组或者 直客的添加分组，才需要校验修改分组时分组名有没有变
            if(!$scope.isMonthBuQuota || $scope.addGroupInfo.isMonthBuQuota == 0){
                if(!$scope.newGroup&&$scope.groupNameTemp ==$scope.setGroupInfo.orgName){
                    return;
                }
            }

            $scope.groupNameVali = $scope.validate(context, 30, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/);
            if (!$scope.groupNameVali) {
                $scope.groupNameDesc = 'GROUP_GROUPNAMEDESC';
                return;
            }
        }
        //修改分组时，分组有reserved3配额时，可以不用修改分组名只修改配额，符合此情况在这个方法阻断，不去校验分组名（如果分组名有修改还是会校验）
        if(document.getElementById('setGroupPop').style.display == 'block' && $scope.setGroupInfo.reserved2){
            if(!$scope.newGroup&&$scope.groupNameTemp ==$scope.setGroupInfo.orgName){
                console.log("包月分组名修改，名称未变，则进阻断方法，不校验名称，直接修改")
                if ($scope.type == '4'  && $scope.condition == 'create') {
                    console.log(4);
                    $scope.changeGroup();
                }
                return;
            }
        }
        var checkDataUniqueReq = {};
        // checkDataUniqueReq.serviceType = type;
        checkDataUniqueReq.orgType = 1;
        type == '4' ? checkDataUniqueReq.serviceType = '3' : checkDataUniqueReq.serviceType = type;
        if (type == '3' || type == '4') {
            checkDataUniqueReq.content = $scope.enterpriseID + ":" + context;
        } else {
            checkDataUniqueReq.enterpriseID = $scope.enterpriseID;
            checkDataUniqueReq.content = context;
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
            data: JSON.stringify(checkDataUniqueReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    $scope.groupNameExist = false;
                    $scope.memberMsisdnExist = false;
                    $scope.groupNameDesc = '';
                    $scope.memberMsisdnDesc = '';
                    //是否存在重复的用户名称
                    if (result.resultCode == '1030120018') {
                        if ($scope.type == '2') {
                            $scope.memberMsisdnExist = true;
                            $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNEXIST';
                        }
                        if ($scope.type == '3' || $scope.type == '4') {
                            $scope.groupNameExist = true;
                            $scope.groupNameDesc = 'GROUP_ORGNAMEEXIST';
                        }
                    }
                    if (result.resultCode == '**********' && $scope.condition == 'create') {
                        $scope.condition = '';
                        if ($scope.type == '2') {
                            $scope.createMember();
                        }
                        if ($scope.type == '3') {
                            $scope.createGroup();
                        }
                        if ($scope.type == '4') {
                            console.log(4);
                            $scope.changeGroup();
                        }
                    }
                    if (result.resultCode != '1030120018' && result.resultCode != '**********') {
                        if ($scope.type == '2') {
                            $('#addMemCancel').click();
                        } else {
                            $('#addOrgCancel').click();
                        }
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    if ($scope.type == '2') {
                        $('#addMemCancel').click();
                    } else {
                        $('#addOrgCancel').click();
                    }
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    /*  $scope.$watch('setGroupInfo.reserved3',function(newValue,oldValue){
          if(document.getElementById('setGroupPop').style.display == 'block') {
              $scope.selInpErr = newValue < $scope.reservededit3;
              console.log($scope.selInpErr)
          }else{
              $scope.selInpErr = false
          }
      })*/
    //编辑分组
    $scope.changeGroup = function () {
        $scope.setGroupInfo.reserved2 = $scope.addGroupInfo.diff;
        $scope.setGroupInfo.reserved8 = $scope.addGroupInfo.diff2;

        $scope.setGroupInfo.ecpmReserveds = {};
        $scope.setGroupInfo.ecpmReserveds.reserved2 = $scope.setGroupInfo.reserved2;
        $scope.setGroupInfo.ecpmReserveds.reserved3 =  $scope.setGroupInfo.reserved3;
        $scope.setGroupInfo.ecpmReserveds.reserved4 =  $scope.setGroupInfo.reserved4;
        $scope.setGroupInfo.ecpmReserveds.reserved5 =  $scope.setGroupInfo.reserved5;
        $scope.setGroupInfo.ecpmReserveds.reserved6 =  $scope.setGroupInfo.reserved6;
        $scope.setGroupInfo.ecpmReserveds.reserved7 =  $scope.setGroupInfo.reserved7;
        $scope.setGroupInfo.ecpmReserveds.reserved8 =  $scope.setGroupInfo.reserved8;
        $scope.setGroupInfo.ecpmReserveds.reserved10 =  $scope.setGroupInfo.reserved10;
        if($scope.setGroupInfo.reserved10 == 1){
            $scope.setGroupInfo.ecpmReserveds.reserved5 = $scope.setGroupInfo.reserved3;
        }

        if ($scope.needServiceProduct == 1){
            if (!$scope.pxFlag || $scope.serviceProductExist){
                $scope.setGroupInfo.ecpmReserveds.reserved2 = null;
                $scope.setGroupInfo.ecpmReserveds.reserved3 = null;
                $scope.setGroupInfo.ecpmReserveds.reserved4 = null;
            }
            if (!$scope.gdFlag){
                $scope.setGroupInfo.ecpmReserveds.reserved5 = null;
                $scope.setGroupInfo.ecpmReserveds.reserved6 = null;
                $scope.setGroupInfo.ecpmReserveds.reserved8 = 0;
            }
            if (!$scope.gcFlag){
                $scope.setGroupInfo.ecpmReserveds.reserved7 =  null;
            }
        }



        var req = {
            "organization": $scope.setGroupInfo,
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/updateOrganization",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.queryGroupList();
                        $('#setOrgCancel').click();
                    } else {
                        $('#setOrgCancel').click();
                        $scope.tip = data.resultDesc;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#setOrgCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    /* 删除成员 */
    $scope.delDataUnique = function (context, type, condition) {
        $scope.type = type;
        $scope.condition = condition;
        if (type == '2') {
            if ($scope.enterpriseType && $scope.enterpriseType == '5') {
                $scope.memberMsisdnVali = $scope.validate(context, 12, /^\d{3,12}$/);
            }else{
                $scope.memberMsisdnVali = $scope.validate(context, 11, /^[0-9]{11}$/);
            }

            if (!$scope.memberMsisdnVali && $scope.enterpriseType == '5') {
                $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE';
                return;
            }else if(!$scope.memberMsisdnVali){
                $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC';
                return;
            }
        } else {
            $scope.groupNameVali = $scope.validate(context, 30, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/);
            if (!$scope.groupNameVali) {
                $scope.groupNameDesc = 'GROUP_GROUPNAMEDESC';
                return;
            }
        }
        var checkDataUniqueReq = {};
        checkDataUniqueReq.serviceType = type;
        if (type == '3') {
            checkDataUniqueReq.content = $scope.enterpriseID + ":" + context;
        } else {
            checkDataUniqueReq.content = context;
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
            data: JSON.stringify(checkDataUniqueReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    $scope.groupNameExist = false;
                    $scope.memberMsisdnExist = false;
                    $scope.groupNameDesc = '';
                    $scope.memberMsisdnDesc = '';
                    //是否存在重复的用户名称
                    if (result.resultCode == '1030120018') {
                        if ($scope.type == '2') {
                            $scope.memberMsisdnExist = true;
                            $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNEXIST';
                        }
                        if ($scope.type == '3') {
                            $scope.groupNameExist = true;
                            $scope.groupNameDesc = 'GROUP_ORGNAMEEXIST';
                        }
                    }
                    if (result.resultCode == '**********' && $scope.condition == 'create') {
                        $scope.condition = '';
                        if ($scope.type == '2') {
                            $scope.createMember();
                        }
                        if ($scope.type == '3') {
                            $scope.createGroup();
                        }
                    }
                    if (result.resultCode != '1030120018' && result.resultCode != '**********') {
                        if ($scope.type == '2') {
                            $('#addMemCancel').click();
                        } else {
                            $('#addOrgCancel').click();
                        }
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    if ($scope.type == '2') {
                        $('#addMemCancel').click();
                    } else {
                        $('#addOrgCancel').click();
                    }
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    /**
     * 根据当前企业ID，调用查询企业业务套餐接口（queryServiceProduct），获取企业业务套餐
     * @param item
     *
     */
    $scope.queryServiceProduct=function() {
        var req = {
            "enterpriseID": $scope.enterpriseID
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryServiceProduct",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1010100000') {
                        $scope.serviceProduct = result.serviceProductArray;
                        $scope.serviceGdProduct = result.gdSharePackageArray;
                        //企业业务套餐存在记录
                        // serviceProductExist代表屏显订购情况。serviceProductExistOther值是否存在挂短套餐或共享包
                        if($scope.serviceGdProduct && $scope.serviceGdProduct?.length){
                            $scope.gdResultObj = $scope.serviceGdProduct.filter((item1) => {
                                return item1.effective == 1
                            })
                            if($scope.gdResultObj && $scope.gdResultObj[0]?.id){
                                $scope.serviceProductExistOther = true
                            }else{
                                $scope.serviceProductExistOther = false
                            }
                        }
                        if($scope.serviceProduct){

                            $scope.resultObj = $scope.productTypeList.filter((item1) => {
                                return $scope.serviceProduct.some((item2) => {
                                    return item1.productId === item2.productID;
                                });
                            });
                            $scope.resultObjHY = $scope.HYproductTypeList.filter((item1) => {
                                return $scope.serviceProduct.some((item2) => {
                                    return item1.productId === item2.productID;
                                });
                            });
                            if(($scope.resultObjHY && $scope.resultObjHY?.length) || ($scope.gdResultObj && $scope.gdResultObj[0]?.id)){
                                $scope.serviceProductExistOther = true
                            }else{
                                $scope.serviceProductExistOther = false
                            }
                            if($scope.resultObj && $scope.resultObj?.length){
                                $scope.serviceProductExist = true
                            }else{
                                $scope.serviceProductExist = false
                            }

                        }else {
                            $scope.serviceProductExist = false

                        }

                    }
                })
            }
        })
    }
    /**
     * 调用ecpmp的获取配置项接口（getProperties），获取企业名片彩印业务套餐配置项
     * 解析为企业业务套餐列表
     */
    $scope.getProperties = function() {
        var keys=new Array("ecpmp.enterprise.service.product");
        var req = {
            "keys": keys
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/getProperties",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '**********') {
                        for(var key in result.props){
                            var productStr = result.props[ key ];
                        }
                        var products = productStr.substring(1,productStr.length-1);
                        var productList = products.split("|");
                        var productTypes = new Array();
                        for (let i = 0; i < productList.length; i++) {
                            productTypes.push($.parseJSON(productList[i]))
                        }
                        $scope.productTypeList = productTypes
                    } else {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = result.result.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }

    /**
     * 调用ecpmp的获取配置项接口（getHYProperties），获取企业行业挂机短信业务套餐配置项
     * 解析为企业业务套餐列表
     */
    $scope.getHYProperties = function() {
        var keys=new Array("ecpmp.enterprise.gd.service.product");
        var req = {
            "keys": keys
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/getProperties",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '**********') {

                        for(var key in result.props){
                            var productStr = result.props[ key ];
                        }
                        var products = productStr.substring(1,productStr.length-1);
                        var productList = products.split("|");
                        var productTypes = new Array();
                        for (let i = 0; i < productList.length; i++) {
                            productTypes.push($.parseJSON(productList[i]))
                        }
                        $scope.HYproductTypeList = productTypes

                    } else {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = result.result.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }
}])


app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])