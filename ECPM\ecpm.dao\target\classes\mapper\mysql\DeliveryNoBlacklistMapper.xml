<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DeliveryNoBlacklistMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DeliveryNoBlacklistWrapper" >
        <id column="id" property="id" jdbcType="INTEGER" />
        <result column="servType" property="servType" jdbcType="TINYINT" />
        <result column="hotlineNo" property="hotlineNo" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        id, servType, hotlineNo, createTime, updateTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_delivery_no_blacklist
        where id = #{id,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from ecpm_t_delivery_no_blacklist
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.DeliveryNoBlacklistWrapper" >
        insert into ecpm_t_delivery_no_blacklist (id, servType, hotlineNo, 
            createTime, updateTime)
        values (#{id,jdbcType=INTEGER}, #{servType,jdbcType=TINYINT}, #{hotlineNo,jdbcType=VARCHAR}, 
            #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.DeliveryNoBlacklistWrapper" >
        insert into ecpm_t_delivery_no_blacklist
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="servType != null" >
                servType,
            </if>
            <if test="hotlineNo != null" >
                hotlineNo,
            </if>
            <if test="createTime != null" >
                createTime,
            </if>
            <if test="updateTime != null" >
                updateTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="servType != null" >
                #{servType,jdbcType=TINYINT},
            </if>
            <if test="hotlineNo != null" >
                #{hotlineNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.DeliveryNoBlacklistWrapper" >
        update ecpm_t_delivery_no_blacklist
        <set >
            <if test="servType != null" >
                servType = #{servType,jdbcType=TINYINT},
            </if>
            <if test="hotlineNo != null" >
                hotlineNo = #{hotlineNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                updateTime = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.DeliveryNoBlacklistWrapper" >
        update ecpm_t_delivery_no_blacklist
        set servType = #{servType,jdbcType=TINYINT},
            hotlineNo = #{hotlineNo,jdbcType=VARCHAR},
            createTime = #{createTime,jdbcType=TIMESTAMP},
            updateTime = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

	<select id="queryForDeliveryNoBlack" resultMap="BaseResultMap">
		select 
			<include refid="Base_Column_List" />
		from
			ecpm_t_delivery_no_blacklist t
		where 
			t.servType = #{servType}
			and t.hotlineNo in
			<foreach collection="hotlineNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
	</select>
	
	<insert id="insertHotlineNoList" >
		insert into ecpm_t_delivery_no_blacklist 
			(
			servType, 
			hotlineNo, 
            createTime, 
            updateTime
            )
        values 
        <foreach collection="list" item="item" separator=",">
        	(
        	#{item.servType,jdbcType=TINYINT}, 
        	#{item.hotlineNo,jdbcType=VARCHAR}, 
            #{item.createTime,jdbcType=TIMESTAMP}, 
            #{item.updateTime,jdbcType=TIMESTAMP}
            )
		</foreach>
	</insert>

	<delete id="deleteForDeliveryNoBlack">
		delete from
			ecpm_t_delivery_no_blacklist
		where 
			servType = #{servType}
			and hotlineNo in
			<foreach collection="hotlineNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
	</delete>

	<select id="queryDeliveryNoBlacklist" resultMap="BaseResultMap">
		select 
			<include refid="Base_Column_List" />
		from
			ecpm_t_delivery_no_blacklist t
		where 
			1=1
			<if test="servType != null" >
				and t.servType = #{servType}
			</if>
			<if test="hotlineNo != null and hotlineNo != ''" >
				and INSTR(t.hotlineNo, #{hotlineNo})
			</if>
		limit #{startIndex}, #{pageSize}
	</select>

	<select id="queryDeliveryNoBlacklistCount" resultType="java.lang.Integer">
		select 
			count(1)
		from
			ecpm_t_delivery_no_blacklist t
		where 
			1=1
			<if test="servType != null" >
				and t.servType = #{servType}
			</if>
			<if test="hotlineNo != null and hotlineNo != ''" >
				and INSTR(t.hotlineNo, #{hotlineNo})
			</if>
	</select>
	
</mapper>