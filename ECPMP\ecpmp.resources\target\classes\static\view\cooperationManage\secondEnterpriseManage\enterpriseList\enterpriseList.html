<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>代理商管理  > 子企业管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
	<link href="../../../../css/reset.css" rel="stylesheet" />
	<link href="../../../../css/mian.css" rel="stylesheet"/>
	<link href="../../../../css/datepicker3.css" rel="stylesheet"/>
	<link href="../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/daterangepicker/daterangepicker.min.js"></script>
	<link href="../../../../css/daterangepicker.min.css" rel="stylesheet">
	<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
	<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
	<link rel="stylesheet" href="../../../../css/font-awesome.min.css"/>
	<link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="enterpriseListCtrl.js"></script>

	<style>
		.webuploader-pick {
			padding: 10px 12px !important;
		}
		.li-disabled{
			opacity: 0.5;
			cursor: not-allowed!important;
		}
		.handle ul li {
    		margin-right: 7px;
		}
		.handle ul li icon {
    		margin-right: 2px;
		}
	</style>
</head>
<body ng-app="myApp" class="body-min-width-new">
	<div class="cooperation-manage container-fluid" style="overflow-x: scroll;" ng-controller="EnterpriselistCtrl" ng-init="init();" ng-cloak>
		<div class="cooperation-head" ng-show="isSuperManager">
			<span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"> </span> >
			<span class="second-tab" ng-bind="'COMMON_SUBENTERPRISEMANAGE'|translate"></span>
		</div>
		<div class="cooperation-head" ng-show="isAgent">
			<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"> </span> >
			<span class="second-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>
		</div>

<div ng-show="businessStatus !=1">
		<div class="cooperation-search" ng-show="isSuperManager">
			<form  class="form-horizontal"name="myForm" novalidate>
				<div class="form-group">

					<label for="parentEnterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_AGENTNAME'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="parentEnterpriseName" class="form-control"
							placeholder="{{'ENTERPRISE_PLEASEINPUTAGENTNAME'|translate}}"
							ng-model="parentEnterpriseName">
					</div>

					<label for="parentEnterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_AGENTID2'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="parentEnterpriseID" class="form-control"
							placeholder="{{'ENTERPRISE_PLEASEINPUTAGENTID2'|translate}}"
							ng-model="parentEnterpriseID">
					</div>


					<label for="enterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="enterpriseID" class="form-control"
							placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISEID'|translate}}"
							ng-model="enterpriseID">
					</div>

					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></label>

					<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="enterpriseName" class="form-control"
							placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME'|translate}}"
							ng-model="enterpriseName">
					</div>

					<label for="time-config" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ITEM_EFFICTIVETIME'|translate"></label>
					<div class="cond-div  col-lg-3 col-md-3 col-sm-3 col-xs-3 input-daterange input-group"
							id="datepicker" style="padding-left:15px;padding-right:15px;float:left">
						<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="startSm"/>
						<span class="input-group-addon" ng-bind="'TO'|translate"></span>
						<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="endSm"/>
					</div>

					<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
						<button type="submit" class="btn search-btn" ng-click="enterpriseList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="cooperation-search" ng-show="isAgent">
			<form  class="form-horizontal"name="myForm" novalidate>
				<div class="form-group">

					<label for="enterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<input type="text" id="enterpriseID" class="form-control"
							placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISEID'|translate}}"
							ng-model="enterpriseID">
					</div>

					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISENAME1'|translate"></label>

					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<input type="text" id="enterpriseName" class="form-control"
							placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME1'|translate}}"
							ng-model="enterpriseName">
					</div>

					<label for="time-config" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ITEM_EFFICTIVETIME'|translate"></label>

					<div class="cond-div  col-lg-3 col-md-3 col-sm-3 col-xs-3 input-daterange input-group"
							id="datepicker" style="padding-left:15px;padding-right:15px;float:left">
						<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="startAg"/>
						<span class="input-group-addon" ng-bind="'TO'|translate"></span>
						<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="endAg"/>
					</div>

					<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
						<button type="submit" class="btn search-btn" ng-click="enterpriseList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>

		<div class="add-table" ng-show="isAgent">
			<button type="submit" class="btn add-btn" ng-click="gotoAdd()">
				<icon class="add-iocn"></icon>
				<span ng-bind="'ENTERPRISE_ADDSUBENTERPRISE'|translate"></span>
			</button>
		</div>
		<div style="margin-left: 20px;margin-bottom: 20px;margin-top: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></p>
        </div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:85px" ng-bind="'ENTERPRISE_AGENTID2'|translate" ng-show="isSuperManager"></th>
					<th style="width:110px" ng-bind="'ENTERPRISE_AGENTNAME'|translate" ng-show="isSuperManager"></th>
					<th style="width:85px" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></th>
					<th style="width:110px" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></th>
					<th style="width:130px" ng-bind="'ENTERPRISE_SUBENTERPRISECREATETIME'|translate"></th>
					<th style="width:130px" ng-bind="'CONTENTAUDIT_AUDITPASSTIME'|translate"></th>
					<th style="width:90px" ng-bind="'COMMON_STATUS'|translate"></th>
					<th style="width:560px" ng-bind="'COMMON_OPERATE'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in queryEnterpriseList">
					<td ng-show="isSuperManager"><span title="{{item.parentEnterpriseID}}">{{item.parentEnterpriseID}}</span></td>
					<td ng-show="isSuperManager"><span title="{{item.parentEnterpriseName}}">{{item.parentEnterpriseName}}</span></td>
					<td><span title="{{item.id}}">{{item.id||""}}</span></td>
					<td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
					<td title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</td>
					<td title="item.extInfo.auditTime?{{item.extInfo.auditTime.substring(0,item.extInfo.auditTime.length-3)}}:''">
						{{item.extInfo.auditTime?item.extInfo.auditTime.substring(0,item.extInfo.auditTime.length-3):''}}</td>
					<td title="{{statusMap[item.auditStatus]}}">{{statusMap[item.auditStatus]}}</td>

					<td>
						<div class="handle">
							<ul>
								<li class="edit"  ng-show="isAgent" ng-click="item.auditStatus===2?toBussiness(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2]">
								<icon class="edit-icon"></icon>
								<!--业务管理-->
								<span ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>
								<!--业务配置-->
								<!--<span ng-bind="'GROUP_BUSSINESSCONF'|translate" ng-show="isSuperManager"></span>-->
							</li>
								<!--配额管理-->
								<!--  <li class="edit" ng-click="item.auditStatus===2?toQuota(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'COMMON_QUOTAMANAGE'|translate"></span>
								</li>-->
								<!--名片彩印-->
								<li class="edit" ng-click="(item.auditStatus===2&&item.mmquotastatus===1)?toMingpian(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2&&item.mmquotastatus===1]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>
								</li>
								<!--热线彩印-->
								<li class="edit" ng-click="(item.auditStatus===2&&item.rxquotastatus===1)?toHotLine(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2&&item.rxquotastatus===1]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'COMMON_ENTERPRISEHOTLINE1'|translate"></span>
								</li>
								<!--广告彩印-->
							 <li class="edit" ng-click="(item.auditStatus===2&&item.ggquotastatus===1)?toAdver(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2&&item.ggquotastatus===1]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
								</li>
								<!--黑白名单-->
								<li class="edit" ng-show="!isSuperManager" ng-click="item.auditStatus===2?toHeiBaiSecond(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'BLACKWHITE_LIST'|translate"></span>
								</li>
								<!--企业通知-->
								<li class="edit" ng-click="(item.auditStatus===2&&item.qyquotastatus===1)?toTask(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2&&item.qyquotastatus===1]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'SENDTASK'|translate"></span>
								</li>
								<!--接入设置-->
								<li class="edit" ng-show="!isSuperManager" ng-click="item.auditStatus===2?toAccessSetting(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2]">
									<icon class="edit-icon"></icon>
									<span ng-bind="'ACCESSSETTINGS'|translate"></span>
								</li>

								<!--******** 管理员登录时，子企业管理新增配置，内含业务配置、黑白名单、接入设置-->
								<li class="edit" ng-show="isSuperManager" ng-click="item.auditStatus===2?toBussiness(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2]">
									<icon class="edit-icon"></icon>
									<!--配置改设置-->
									<span ng-bind="'SETTINGS'|translate" ></span>
								</li>
								<!--******** 管理员登录时，子企业管理新增配置，内含业务配置、黑白名单、接入设置-->
								<li class="edit" ng-show="isAgent" ng-click="item.auditStatus===2?toBussiness2(item):''" ng-class="{true:'',false:'li-disabled'}[item.auditStatus===2]">
									<icon class="edit-icon"></icon>
									<!--配置改设置-->
									<span ng-bind="'BUSINESSSETTING'|translate" ></span>
								</li>
								<!--********-end-->
								<li ng-show="item.accountInfo != null && item.accountInfo.accountName == null && item.auditStatus == 2 "
									class="query" ng-click="createAccount(item)">
									<icon class="package-icon"></icon>
									<span ng-bind="'ACCOUNT_CREATE'|translate"></span>
								</li>
								<li ng-show="item.accountInfo !=null && item.accountInfo.accountName != null"
									class="query" ng-click="accountDetail(item)">
									<icon class="package-icon"></icon>
									<span ng-bind="'ACCOUNT_DETAIL'|translate"></span>
								</li>

								<!-- 新增注销按钮	-->
								<li class="edit" ng-show="isSuperManager || isAgent" ng-click=" item.auditStatus===1 ?  '' : logoutAccount(item)"
									ng-class="{false:'',true:'li-disabled'}[item.auditStatus===1]"
								>
<!--									<icon class="edit-icon"></icon>-->
									<!--注销-->
									<span ng-bind="'WRITE_OFF'|translate" ></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-if="queryEnterpriseList.length<=0 && isSuperManager">
					<td style="text-align:center" colspan="8" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				<tr ng-if="queryEnterpriseList.length<=0 && isAgent">
					<td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="enterpriseList('justPage')"></ptl-page>
		</div>
	<!-- 注销弹框 -->
	<div class="modal fade" id="deleteAccount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width:450px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabelDel">确认注销</h4>
				</div>
				<div class="modal-body">
					<p style="text-align:center;margin-top:30px;font-size:16px">请确认是否进行注销?</p>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn" ng-click="delAccount()"
					>确认
					</button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
							id="delAccountCancel" style="margin-left: 20px">取消
					</button>
				</div>
			</div>
		</div>
	</div>
		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate">
						</button>
					</div>
				</div>
			</div>
		</div>
</div>

	<!-- 业务未开通 -->
	<div ng-show="businessStatus ==1" class="modal fade bs-example-modal-sm" id="Modalisaengt" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" >
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 18px;color:#383838'>
							业务未开通
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
		<!-- 查看/生成账号弹窗 -->
		<div class="modal fade bs-example-modal-sm" id="accountDetailWindow" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document" style="width: 467px;">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="accountDetailOPName"></h4>
					</div>
					<div class="cooper-tab">
						<form class="form-horizontal" name="myForm" novalidate>
							<div class="form-group">
								<label class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="text-align: right">
									<span ng-bind="'ENTERPRISE_ENTERPRISENAME2'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6" style="width: 60%;">
									<span ng-bind="accountDetailEnterpriseName"></span>
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="text-align: right">
									<span ng-bind="'SUB_ENTERPRISE_ACCOUNT_NAME'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6" style="width: 60%;">
									<span ng-bind="enterpriseAccountName"></span>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_BACK'|translate">
						</button>
					</div>
				</div>
			</div>
		</div>

	</div>
</body>

</html>