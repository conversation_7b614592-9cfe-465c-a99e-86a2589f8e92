<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.UserOperterInfoMapper">

    <resultMap id="userOperterInfoMap" type="com.huawei.jaguar.dsum.dao.domain.UserOperterInfoWrapper">
        <result property="objectID" column="ID"/>
        <result property="operTime" column="opertime"/>
        <result property="operName" column="operName"/>
        <result property="operatorID" column="operatorID"/>
        <result property="operDesc" column="operDesc"/>
        <result property="extInfo" column="extInfo"/>
        <result property="reserved1" column="reserved1"/>
        <result property="reserved2" column="reserved2"/>
        <result property="reserved3" column="reserved3"/>
        <result property="reserved4" column="reserved4"/>
        <result property="reserved5" column="reserved5"/>
        <result property="reserved6" column="reserved6"/>
        <result property="reserved7" column="reserved7"/>
        <result property="reserved8" column="reserved8"/>
        <result property="reserved9" column="reserved9"/>
        <result property="reserved10" column="reserved10"/>
        <result property="accountName" column="accountName"/>
        <result property="sourceIP" column="sourceIP"/>
    </resultMap>

    
    <insert id="insert">
        INSERT INTO dsum_t_oper_log (
				opertime,
				operName,
				operatorID,
				operDesc,
				extInfo,
				reserved1,
				reserved2,
				reserved3,
				reserved4,
				reserved5,
				reserved6,
				reserved7,
				reserved8,
				reserved9,
				reserved10,
				sourceIP
			)
			VALUES
				(
					#{operTime}, #{operName}, #{operatorID}, #{operDesc}, #{extInfo}, #{reserved1}, #{reserved2}, #{reserved3}, #{reserved4}, #{reserved5}, #{reserved6},#{reserved7},#{reserved8},#{reserved9},#{reserved10},#{sourceIP}
				)
    </insert>

	<select id="query" resultMap="userOperterInfoMap">
		SELECT
		o.*,
		IFNULL(c.accountName,pc.accountName)  accountName
		FROM
		dsum_t_oper_log o
		LEFT JOIN dsum_t_account c on o.operatorID = c.id
		LEFT JOIN dsum_t_platform_account pc on pc.accountID  = o.operatorID
		where 1 = 1
		<trim prefix="and" prefixOverrides="and|or">
        	<if test="id != null and id != ''">
				and o.id = #{id}
			</if>
        	<if test="operatorID != null and operatorID != ''">
				and o.operatorID = #{operatorID}
			</if>
			<if test="accountName != null and accountName != ''">
				and (c.accountName like concat("%", #{accountName}, "%") or pc.accountName like concat("%", #{accountName}, "%"))
			</if>
			<if test="operNameList != null and operNameList.size()>0">
				and o.operName in
				<foreach item="operName" index="index" collection="operNameList" open="(" separator="," close=")">
					#{operName}
				</foreach>
			</if>
			<if test="operTimeBegin != null and operTimeBegin != ''">
				and o.operTime <![CDATA[ > ]]> #{operTimeBegin}
			</if>
			<if test="operTimeEnd != null and operTimeEnd != ''">
				and o.operTime <![CDATA[ < ]]> #{operTimeEnd}
			</if>
			<if test="filtOperatorIDs != null and filtOperatorIDs.size()>0">
				and o.operatorID not in
				<foreach item="id" index="index" collection="filtOperatorIDs" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
		</trim>
		ORDER BY o.opertime DESC
		limit #{pageNo},#{pageSize} 
	</select>
	
	<select id="queryCount" resultType="java.lang.Integer">
		SELECT count(0)
		FROM dsum_t_oper_log o
		LEFT JOIN dsum_t_account c on o.operatorID = c.id
		LEFT JOIN dsum_t_platform_account pc on pc.accountID  = o.operatorID
		where 1=1
        <trim prefix="and" prefixOverrides="and|or">
        	<if test="id != null and id != ''">
				and o.id = #{id}
			</if>
        	<if test="operatorID != null and operatorID != ''">
				and o.operatorID = #{operatorID}
			</if>
			<if test="accountName != null and accountName != ''">
				and (c.accountName like concat("%", #{accountName}, "%") or pc.accountName like concat("%", #{accountName}, "%"))
			</if>
			<if test="operNameList != null and operNameList.size()>0">
				and o.operName in
				<foreach item="operName" index="index" collection="operNameList" open="(" separator="," close=")">
					#{operName}
				</foreach>
			</if>
			<if test="operTimeBegin != null and operTimeBegin != ''">
				and o.operTime <![CDATA[ > ]]> #{operTimeBegin}
			</if>
			<if test="operTimeEnd != null and operTimeEnd != ''">
				and o.operTime <![CDATA[ < ]]> #{operTimeEnd}
			</if>
			<if test="filtOperatorIDs != null and filtOperatorIDs.size()>0">
				and o.operatorID not in
				<foreach item="id" index="index" collection="filtOperatorIDs" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>

		</trim>
	</select>
</mapper>