document.write("<script type='text/javascript' src='/qycy/ecpmp/frameworkJs/jsencrypt.js'></script>");
document.write("<script type='text/javascript' src='/qycy/ecpmp/frameworkJs/crypto-js/crypto-js.js'></script>");
document.write("<script type='text/javascript' src='/qycy/ecpmp/frameworkJs/crypto-js/cryptoUtil.js'></script>");
angular.module("util.ajax", [
]).service("RestClientUtil", function ($http) {
	// 设置cookie
	function setCookie(name,value)
	{
		var Days = 1;
		var exp = new Date();
		exp.setTime(exp.getTime() + Days*24*60*60*1000);
		document.cookie = name + "="+ escape (value) + ";expires=" + exp.toGMTString();
	}
	//获取cookie
	function getCookie(name)
	{
		var arr,reg=new RegExp("(^| )"+name+"=([^;]*)(;|$)");
		if(arr=document.cookie.match(reg))
			return unescape(arr[2]);
		else
			return null;
	}
	// 删除cookie
	function delCookie(name)
	{
		var exp = new Date();
		exp.setTime(exp.getTime() - 1);
		var cval=getCookie(name);
		if(cval!=null)
			document.cookie= name + "="+cval+";expires="+exp.toGMTString();
	}

	//获取密钥对(生成RSA公钥和私钥)
	function getRsaKeys(func){
		// if(sessionStorage.hasOwnProperty('privateKey')) return;
		window.crypto.subtle.generateKey(
			{
				name: "RSA-OAEP",
				modulusLength: 2048, //can be 1024, 2048, or 4096
				publicExponent: new Uint8Array([0x01, 0x00, 0x01]),
				hash: {name: "SHA-512"}, //can be "SHA-1", "SHA-256", "SHA-384", or "SHA-512"
			},
			true, //whether the key is extractable (i.e. can be used in exportKey)
			["encrypt", "decrypt"] //must be ["encrypt", "decrypt"] or ["wrapKey", "unwrapKey"]
		).then(function(key){
			window.crypto.subtle.exportKey(
				"pkcs8",
				key.privateKey
			).then(function(keydata1){
				window.crypto.subtle.exportKey(
					"spki",
					key.publicKey
				).then(function(keydata2){
					var privateKey = RSA2text(keydata1,1);
					var publicKey = RSA2text(keydata2);
					func(privateKey,publicKey);
				}).catch(function(err){
					//console.error(err);
				});
			})
				.catch(function(err){
					//console.error(err);
				});
		})
			.catch(function(err){
				//console.error(err);
			});
	}

	function RSA2text(buffer,isPrivate=0) {
		var binary = '';
		var bytes = new Uint8Array(buffer);
		var len = bytes.byteLength;
		for (var i = 0; i < len; i++) {
			binary += String.fromCharCode(bytes[i]);
		}
		var base64 = window.btoa(binary);
		var text = base64.replace(/[^\x00-\xff]/g,"$&\x01").replace(/.{64}\x01?/g,"$&\n");
		return text;
	}

	function deCryptEncryptionKey(enCryptKey,privateKey){ //解密密钥
		privateKey = privateKey || sessionStorage.getItem('privateKey');
		var decrypt = new JSEncrypt();
		decrypt.setPrivateKey(privateKey);
		var enck = decrypt.decrypt(enCryptKey)
		return enck;
	}

	window.onunload = function() {
		delCookie('CryptKey');
		delCookie('privateKey');
		sessionStorage.removeItem('CryptKey');
		sessionStorage.removeItem('privateKey');
	};

	var excludes = ['/ecpmp/qycy/getCryptKey','/ecpmp/ecpmpServices/loginService/getVerificationCode'];

	var fn = {
		doAjaxReqest:function(b,cryptKey,iv){
			//初始化绑定默认的属性
			$.ajaxDefaults = $.ajaxDefaults || {};
			$.ajaxDefaults.property = {
				async: true,
				type: "POST",
				contentType: "application/json",
				cache: false,
				data: {},
				dataType: "json",
				timeout: 20000,
				success: function (result, textStatus, request) {

				},
				error: function (result, textStatus, request) {
					//          console.log(result);
				}
			};


			if (timefn) {
				window.clearTimeout(timefn);
			}
			var p = $.extend({}, $.ajaxDefaults.property, b || {});
			var timefn;
			const encrypt = new JSEncrypt();
			const key = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANW45vowD1WF7WGJO///YmziLY/wROrIsuKy9YVoi2k8F64o7a0yEw9YmvqOBIN4eTBXMBgxNRK7vAcMbVnh74sCAwEAAQ==";
			encrypt.setPublicKey(key);
			p.url = p.url.trim();
			if (p.url == "/ecpmp/ecpmpServices/contentService/querySyncServiceRule")
			{
				var obp = JSON.parse(p.data);
				obp.enterpriseID = encrypt.encrypt(obp.enterpriseID + "");
				p.data = JSON.stringify(obp);
			}
			if (p.url == "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule")
			{
				var obp = JSON.parse(p.data);
				obp.password = encrypt.encrypt(obp.password + "");
				p.data = JSON.stringify(obp);
			}

			$.ajax({
				async: p.async,
				type: p.type,
				contentType: p.contentType,
				url: "/qycy" + p.url,
				dataType: p.dataType,
				data: p.data,
				beforeSend: function (e,xhr,o) {
					if(cryptKey){
						xhr.data = CryptoJS.ecpmpEncrypt(cryptKey,xhr.data,iv);
					}else if(b.url == '/ecpmp/qycy/getCryptKey'){
						isdoing = true;
					}
					if (!p.notLoad) {
						timefn = setTimeout(function () {
							$("body").append('<div id="load-ajax" style="position: fixed;top: 0;z-index: 1200;width: 100%;height: 100%;background: #000 url(/qycy/ecpmp/assets/images/loading.gif) center center no-repeat;background-size: 3%;opacity: 0.3;">');
						}, 500)
					}
				},
				complete: function () {
					if (!p.notLoad) {
						window.clearTimeout(timefn);
						$("#load-ajax").remove();
					}
				},
				success: function (result, textStatus, request) {

					if(!cryptKey){
						p.success(result, textStatus, request);
					}else{
						if (result && result.result && result.result.resultCode == "**********") {//登录超时
							$.removeCookie('accountID', { path: '/'});
							$.removeCookie('accountInfo', { path: '/'});
							$.removeCookie('enterpriseID', { path: '/'});
							$.removeCookie('enterpriseName', { path: '/'});
							$.removeCookie('token', { path: '/'});
							$.removeCookie('source', { path: '/'});
							var isGroupRemind = $.cookie("isGroupRemind");
							if (!p.notLoad) {
								window.clearTimeout(timefn);
								$("#load-ajax").remove();
							}

							if(result.logoutRedirtUrl){
								top.location.href = result.logoutRedirtUrl;
								return;
							}

							//已在登录页不刷新
							if(top.location.href.indexOf("/view/login/login.html")>0){
								return;
							}
							top.location.href = "/qycy/ecpmp/view/login/login.html"
								+ (isGroupRemind==='true'?("?isGroupRemind=" + isGroupRemind):"");
						} else {
							if(typeof(result) =='string'){
								result = CryptoJS.ecpmpDecrypt(cryptKey,result,iv);
								if(result == ''){}else{
									result = JSON.parse(result);
								}
							}
							if (!p.notLoad) {
								window.clearTimeout(timefn);
								$("#load-ajax").remove();
							}
							p.success(result, textStatus, request);
						}
					}
				},
				error: function (data) {
					if(data.status==399){
						sessionStorage.removeItem('CryptKey');
						sessionStorage.removeItem('privateKey');
						sessionStorage.removeItem('publicKey');
						$.removeCookie('accountID', { path: '/'});
						$.removeCookie('accountInfo', { path: '/'});
						$.removeCookie('enterpriseID', { path: '/'});
						$.removeCookie('enterpriseName', { path: '/'});
						$.removeCookie('token', { path: '/'});
						if(top.location.href.indexOf("/view/login/login.html")>0){
							return;
						}
						top.location.href = "/qycy/ecpmp/view/login/login.html";
						return;
					};
					if (!p.notLoad) {
						window.clearTimeout(timefn);
						$("#load-ajax").remove();
					}
					p.error(data);
				}
			});

		},
		ajaxRequest: function (b) {
			console.log(11,b.url);
			const _this = this;
			if(!excludes.includes(b.url)){
				queryCryptKey(b).then(function onFulfilled(value){

					var keys =  value;
					console.log(keys.enCryptKey);
					var cryptKey = deCryptEncryptionKey(keys.enCryptKey,keys.privateKey);
					var iv = '71c6a6b79ba34677';
					console.log(22,keys.option.url);

					_this.doAjaxReqest(keys.option,cryptKey,iv)
				}).catch(function onRejected(error){
					// error
				});

			}else{
				if(isdoing && b.url == '/ecpmp/qycy/getCryptKey'){
					return false;
				}
				_this.doAjaxReqest(b)
			}
		}
	};
	window.onload = function() {
		addEcloudClass();
							  };
	var isdoing = false,after_request = [];
	function todo_afterRequest(){// 未得到密钥是并发请求，不想多次请求getCryptKey，导致丢失的请求在这执行
		after_request.forEach(item=>{
			fn.ajaxRequest(item);
		})
		after_request = [];
	}

	function queryCryptKey(option){//获取密钥和私钥
		return new Promise(function (resolve, reject) {
			var cryptKey;
			var privateKey = sessionStorage.getItem('privateKey');
			// var condition = document.cookie.match('CryptKey')&&privateKey;
			var  condition = sessionStorage.hasOwnProperty('CryptKey')&&privateKey;
			if(condition){
				cryptKey = sessionStorage.getItem('CryptKey');
				//cryptKey = getCookie('CryptKey');

				//privateKey = getCookie('privateKey');

				resolve({privateKey:privateKey,enCryptKey:cryptKey,option:option});
			}else{
				getRsaKeys(function(a,b){
					sessionStorage.removeItem('privateKey');
					sessionStorage.removeItem('publicKey');
					var req = {
						"publicKey": b
					};
					if(isdoing){
						after_request.push(option);
					}
					fn.ajaxRequest({
						url:'/ecpmp/qycy/getCryptKey',
						data: JSON.stringify(req),
						success: function (result, textStatus, response) {
							cryptKey = result.cryptKey;
							//sessionStorage.setItem('CryptKey',cryptKey);
							// setCookie('CryptKey',cryptKey);
							sessionStorage.setItem('CryptKey',cryptKey);
							sessionStorage.setItem('privateKey',a);
							sessionStorage.setItem('publicKey',b);
							// setCookie('privateKey',a);
							isdoing = false;
							todo_afterRequest();
							resolve({privateKey:a,enCryptKey:cryptKey,option:option});
						}
					})
				})
			};


		})

	};
	//获取移动云标识
	function addEcloudClass(){
		//忘记密码页面不获取
		if(window.location.href.indexOf("forgetPwd/forgetPwd.html")>0){
			return;
		}
		var req = {};
		var loginRoleType = getCookie('loginRoleType');
		if(loginRoleType == "superrManager"||loginRoleType == "normalMangager"){
			return;
		}
		req.id = getCookie('enterpriseID');
		if(!req.id){
			return;
		}
		let enterprisereserved10Re = getCookie('enterprisereserved10');
		if(enterprisereserved10Re && enterprisereserved10Re.indexOf(req.id)>-1){
			if(enterprisereserved10Re.split('_')[1] === '112'){
				document.getElementsByTagName("body")[0].className = 'theme_ecloud';
				replaceHtmlLogo();
			}
			return;
		}
		var pageParameter = {};
		pageParameter.pageNum = 1;
		pageParameter.pageSize = 1000;
		pageParameter.isReturnTotal = 1;
		fn.ajaxRequest({
			url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
			data: JSON.stringify(req),
			success: function (data, textStatus, response) {
				var result = data.result;
				if (result.resultCode == '1030100000') {
					if(data.enterprise.reservedsEcpmp.reserved10 == '112'){
						document.getElementsByTagName("body")[0].className = 'theme_ecloud';
						replaceHtmlLogo();
					}
					setCookie("enterprisereserved10",req.id  + "_" + data.enterprise.reservedsEcpmp.reserved10);
				}
			}
		})
	}



	function replaceHtmlLogo(){
		// try{
		// 	setTimeout(()=>{
		// 		const els = $(".cooperation-head");
		// 		var reg = new RegExp("[\\u4E00-\\u9FFF]+","g");
		// 		for(let i=0;i<els.length;i++){
		// 			const codes = $(els[i]).html();
		// 			if(reg.test(codes)){
		// 				$(els[i]).html(codes.replaceAll("&gt;","<span style='color: #999999'>/</span>"));
		// 			}else{
		// 				throw '还不是中文'
		// 			}
		// 		}
		// 	},100);
		// }catch(e){
		// 	// handle the exception
		// 	replaceHtmlLogo()
		// }

	}

	return fn;
});

