var app = angular.module("myApp", ["util.ajax", "angularI18n", "service.common"]);
app.controller("roleManageViewCtrl", function ($scope, $rootScope, RestClientUtil, $location) {
  $scope.openFirst = true;
  $scope.openSecond = true;
  $scope.openThird = true;
  $scope.openFourth = true;
  $scope.provinceList = [];
  $scope.provinceList2 = [];
  $scope.cityList = [];
  $scope.showCountyProIds = ["10"];
  $scope.countyList = [];
  $scope.cityIDs = [];
  $scope.rebuildFirstList = [];
  $scope.rebuildSecondList = [];
  $scope.roleName = "";
  $scope.roleDesc = "";
  $scope.role_dAuthInfoList = [];
  $scope.enterpriseTypeList = [];
  $scope.init = function () {
    $scope.roleID = $location.$$search.roleID;
    $scope.queryRole($scope.roleID);
    
  };

  $scope.rebuildList = function (oldList) {
    var newlist = [];
    angular.forEach(oldList, function (item) {
      if (item.parentAuthID == null) {
        //重构后的社会合作管理权限
        newlist.push(item)
      }
    });
    angular.forEach(newlist, function (rebuildData) {
      rebuildData.childList = [];
      angular.forEach(oldList, function (oldData) {
        if (oldData.parentAuthID === rebuildData.id) {
          oldData.checked = false;
          angular.forEach($scope.role_fAuthInfoList, function (roleAuth) {
            if (roleAuth.id === oldData.id) {
              oldData.checked = true;
            }
          });
          rebuildData.childList.push(oldData);
        }
      })
    });
    return newlist;
  };

  //查询角色信息
  $scope.queryRole = function (id) {
    var req = {
      roleIDList:[id]
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryRoleList",
      async: false,
      data: JSON.stringify(req),
      success: function (data) {
        if (data && data.result && data.result.resultCode === "1030100000") {
          $scope.role_fAuthInfoList = data.roleList[0].functionAuthList;
          $scope.role_dAuthInfoList = data.roleList[0].dateAuthList;
          $scope.role_enterpriseDAuthInfoList = data.roleList[0].enterpriseDateAuthList;
          $scope.roleName = data.roleList[0].roleName;
          $scope.roleDesc = data.roleList[0].roleDesc;
          
        //获取社会合作管理权限
          $scope.getAuthInfoList({
            authCategory: 1,
            authBelongType: 1
          }, function (data) {
            if (data && data.result && data.result.resultCode === "1030100000") {
              $scope.social_fAuthInfoList = data.functionAuthList;
              //重新构建社会合作管理权限
              $scope.rebuildFirstList = $scope.rebuildList($scope.social_fAuthInfoList);
            } else {
              $scope.tip = data.result.resultCode;
              $("#roleModal").modal();
            }
          }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          });

          //获取分省合作管理权限
          $scope.getAuthInfoList({
            authCategory: 1,
            authBelongType: 2
          }, function (data) {
            if (data && data.result && data.result.resultCode === "1030100000") {
              $scope.province_fAuthInfoList = data.functionAuthList;
              //重新构建社会合作管理权限
              $scope.rebuildSecondList = $scope.rebuildList($scope.province_fAuthInfoList);
            } else {
              $scope.tip = data.result.resultCode;
              $("#roleModal").modal();
            }
          }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          });

          //获取分省合作数据权限
          $scope.getAuthInfoList({
            authCategory: 2
          }, function (data) {
            if (data && data.result && data.result.resultCode === "1030100000") {
              $scope.dAuthInfoList = data.dateAuthList;
              angular.forEach($scope.dAuthInfoList, function (item, index) {
                if (item.fieldName == "provinceID") {
                  item.checked = false;
                  $scope.provinceList.push(item)
                }
                if(item.fieldName == "reserved10" && item.tableName == "dsum_t_enterprise"){
                  $scope.enterpriseTypeList.push(item);
                }
              });
              //获取企业数据权限
              $scope.dAuthInfoList2 = angular.copy(data.dateAuthList);
              angular.forEach($scope.dAuthInfoList2, function (item, index) {
                if (item.fieldName == "provinceID") {
                  item.checked = false;
                  $scope.provinceList2.push(item)
                }
              });
              //勾选企业数据权限
              if ($scope.role_enterpriseDAuthInfoList!=null && $scope.role_enterpriseDAuthInfoList.length > 0) {
                angular.forEach($scope.role_enterpriseDAuthInfoList, function (row) {
                  angular.forEach($scope.provinceList2, function (item) {
                    if (row.fieldName === "provinceID" && row.id === item.id) {
                      item.checked = true;
                    }
                  });
                });
              }

              if ($scope.role_dAuthInfoList!=null && $scope.role_dAuthInfoList.length > 0) {
                $scope.role_cityList = [];
                $scope.role_countyList = [];
                angular.forEach($scope.role_dAuthInfoList, function (row) {
                  if (row.fieldName === "cityID") {
                    //角色所拥有的市级列表
                    $scope.role_cityList.push(row);
                  }
                  if (row.fieldName === "countyID") {
                    //角色所拥有的区县级列表
                    $scope.role_countyList.push(row);
                  }
                  //勾选角色所拥有的省级区域
                  angular.forEach($scope.provinceList, function (item) {
                    if (row.fieldName === "provinceID" && row.id === item.id) {
                      item.checked = true;
                      //配置有区县信息的省份，如果选中了，则展示数据权限（区县）
                      if($scope.showCountyProIds.includes(item.fieldVal)){
                        $scope.showCountyFlag = true;
                      }
                    }
                  });
                  //已勾选的省级区域下的所有市级区域
                  angular.forEach($scope.dAuthInfoList, function (item2) {
                    if (row.fieldName == "provinceID" && item2.fieldName == "cityID" && item2.parentAuthID === row.id) {
                      item2.checked = false;
                      $scope.cityList.push(item2)
                    }
                  });
                  //勾选角色所拥有的企业类型权限
                  angular.forEach($scope.enterpriseTypeList, function (item) {
                    if (item.fieldName == "reserved10" && item.tableName == "dsum_t_enterprise" && row.id === item.id) {
                      item.checked = true;
                    }
                  });
                });
              }
              //勾选角色所拥有的市级区域
              angular.forEach($scope.role_cityList, function (item) {
                angular.forEach($scope.cityList, function (row) {
                  if (row.id === item.id) {
                    row.checked = true;
                    //汇总已勾选的市级区域有区县信息的地市编码
                    angular.forEach($scope.showCountyProIds, function (proId) {
                      if(proId==row.parentAuthID){
                        $scope.cityIDs.push(row.fieldVal)
                      }
                    })
                  }
                });
              });
              //如果地市含有区县，查出已勾选的地市区域下含有的区县数据,查到后进行勾选（isCheckCounty为1进行勾选）
              if($scope.cityIDs.length>0){
                $scope.getAuthInfoList({
                  authCategory: 2,
                  dauthType: 2,
                  cityIDs: $scope.cityIDs
                }, function (data) {
                  if (data && data.result && data.result.resultCode === "1030100000") {
                    $scope.dAuthInfoList3 = data.dateAuthList;
                    angular.forEach($scope.dAuthInfoList3, function (item, index) {
                      $scope.countyList.push(item)

                    });
                    //勾选角色所拥有的区县级区域
                    angular.forEach($scope.role_countyList, function (item) {
                      angular.forEach($scope.countyList, function (row) {
                        if (row.id === item.id) {
                          row.checked = true;
                        }
                      });
                    });
                  } else {
                    $scope.tip = data.result.resultCode;
                    $("#roleModal").modal();
                  }
                }, function (data) {
                  $scope.tip = data.result.resultCode;
                  $("#roleModal").modal();
                });
              }

            } else {
              $scope.tip = data.result.resultCode;
              $("#roleModal").modal();
            }
          }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          });
        } else {
          $scope.tip = data.result.resultCode;
          $("#roleModal").modal();
        }
      },
      error: function (data) {
        $rootScope.$apply(function () {
        })
      }
    })
  };
//
//
//  $scope.role_fAuthInfoList = [
//    {
//      "id": 1,
//      "parentAuthID": null,
//      "authName": "合作管理"
//    },
//    {
//      "id": 2,
//      "parentAuthID": null,
//      "authName": "信息审核"
//    },
//    {
//      "id": 3,
//      "parentAuthID": 1,
//      "authName": "直客管理"
//    },
//
//    {
//      "id": 5,
//      "parentAuthID": 2,
//      "authName": "二级企业审核"
//    }
//  ];
//

//  $scope.role_dAuthInfoList = [
//    {
//      "id": 1,
//      "parentAuthID": null,
//      "fieldName": "provinceID",
//      "authName": "江苏"
//    },
//    {
//      "id": 2,
//      "parentAuthID": null,
//      "fieldName": "provinceID",
//      "authName": "安徽"
//    },
//    {
//      "id": 3,
//      "parentAuthID": 1,
//      "fieldName": "cityID",
//      "authName": "南京"
//    },
//    {
//      "id": 4,
//      "parentAuthID": 2,
//      "fieldName": "cityID",
//      "authName": "合肥"
//    },
//    {
//      "id": 5,
//      "parentAuthID": 2,
//      "fieldName": "cityID",
//      "authName": "滁州"
//    },
//    {
//      "id": 6,
//      "parentAuthID": 2,
//      "fieldName": "cityID",
//      "authName": "ss"
//    }
//  ];

  $scope.getAuthInfoList = function (req, success, error) {
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryAuths",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          success(data);
        })
      },
      error: function (data) {
        $rootScope.$apply(function () {
          error();
        })
      }
    })
  }
//  $scope.social_fAuthInfoList = [
//    {
//      "id": 1,
//      "parentAuthID": null,
//      "authName": "合作管理"
//    },
//    {
//      "id": 2,
//      "parentAuthID": null,
//      "authName": "信息审核"
//    },
//    {
//      "id": 3,
//      "parentAuthID": 1,
//      "authName": "直客管理"
//    },
//    {
//      "id": 4,
//      "parentAuthID": 2,
//      "authName": "内容审核"
//    },
//    {
//      "id": 5,
//      "parentAuthID": 2,
//      "authName": "二级企业审核"
//    },
//    {
//      "id": 6,
//      "parentAuthID": 2,
//      "authName": "商户信息审核"
//    }
//  ];

//
//  $scope.province_fAuthInfoList = [
//    {
//      "id": 1,
//      "parentAuthID": null,
//      "authName": "合作管理"
//    },
//    {
//      "id": 2,
//      "parentAuthID": null,
//      "authName": "信息审核"
//    },
//    {
//      "id": 3,
//      "parentAuthID": 1,
//      "authName": "直客管理"
//    },
//    {
//      "id": 4,
//      "parentAuthID": 2,
//      "authName": "内容审核"
//    },
//    {
//      "id": 5,
//      "parentAuthID": 2,
//      "authName": "二级企业审核"
//    },
//    {
//      "id": 6,
//      "parentAuthID": 2,
//      "authName": "商户信息审核"
//    }
//  ];
//  //重新构建分省合作管理
//  $scope.rebuildSecondList = $scope.rebuildList($scope.province_fAuthInfoList);
//  console.log("rebuildFirstList=", $scope.rebuildFirstList);
//  console.log("rebuildSecondList=", $scope.rebuildSecondList);
//
//
//  $scope.province_dAuthInfoList = [
//    {
//      "id": 1,
//      "parentAuthID": null,
//      "fieldName": "provinceID",
//      "authName": "江苏"
//    },
//    {
//      "id": 2,
//      "parentAuthID": null,
//      "fieldName": "provinceID",
//      "authName": "安徽"
//    },
//    {
//      "id": 3,
//      "parentAuthID": 1,
//      "fieldName": "cityID",
//      "authName": "南京"
//    },
//    {
//      "id": 4,
//      "parentAuthID": 2,
//      "fieldName": "cityID",
//      "authName": "合肥"
//    },
//    {
//      "id": 5,
//      "parentAuthID": 2,
//      "fieldName": "cityID",
//      "authName": "滁州"
//    },
//    {
//      "id": 6,
//      "parentAuthID": 2,
//      "fieldName": "cityID",
//      "authName": "ss"
//    }
//  ];




  $scope.goBack = function () {
    window.location.href = "../list/roleManage_list.html";
  }

})

app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])