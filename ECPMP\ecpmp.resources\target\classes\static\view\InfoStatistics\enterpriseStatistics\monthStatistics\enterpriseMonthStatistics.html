<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
<link href="../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../css/mian.css" rel="stylesheet"/>
<link href="../../../../css/datepicker3.css" rel="stylesheet"/>
<link href="../../../../css/layout.css" rel="stylesheet"/>
<link href="../../../../css/searchList.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link rel="stylesheet" href="../../../../css/font-awesome.min.css"/>
<link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../directives/page/page.js"></script>
<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<link href="../../../../css/statistics.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="enterpriseMonthStatistics.js"></script>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
}
body,html{
		overflow: auto;
}
.input-daterange {
	padding-top: 0px !important;
}
.table > thead > tr > th,td{
			padding: 12px 4px!important;
		}
.clearf:after{
    content:'';
    clear:both;
        height:0;
        display:block;
}
</style>

</head>
<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" style="min-width: 1400px">
	<div class="cooperation-manage">
		<div class="cooperation-head" ng-show="!isProvincial"><span class="frist-tab" ng-bind="'ENTERPRISESTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'MONTHSTATISTICS'|translate"></span></div>
		<div class="cooperation-head" ng-show="isProvincial"><span class="frist-tab" ng-bind="'COMMON_INFOSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'MONTHSTATISTICS'|translate"></span></div>
		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/InfoStatistics/enterpriseStatistics" list-index="29" ng-show="!isProvincial"></top:menu>
			<form class="form-horizontal">
				<div class="form-group">

					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="!isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div" ng-show="!isProvincial">
						<input type="text" autocomplete="off" class="form-control" id="enterpriseName" placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"   ng-model="enterpriseName">
					</div>

					

					<!-- <label for="province" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'COMMON_PROVINCE'|translate"></label>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" ng-show="isSuperManager">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.provinceName for x in provinceList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" ng-show="isProvincial || isNormalMangager">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.authName for x in provinceList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div> -->
					<label for="province" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'COMMON_PROVINCE'|translate" ng-show="isSuperManager || isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class1" ng-show="isSuperManager || isProvincial">
						<select class="form-control" name="province" ng-model="selectedProvince" ng-options="x.provinceName for x in provinceList"
							 ng-change="changeSelectedProvincebychaoguan(selectedProvince)">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-show="!isProvincial"></option>
						</select>
					</div>
					<label for="province" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'COMMON_PROVINCE'|translate" ng-show="!isSuperManager && !isProvincial"></label>
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 cond-div class1" ng-show="!isSuperManager && !isProvincial">
						<select class="form-control" name="province" ng-model="selectedProvince" ng-options="x.authName for x in provinceList"
							 ng-change="changeSelectedProvince(selectedProvince)">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<label for="city" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'CITY'|translate" ng-show="isSuperManager || isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class2" ng-show="isSuperManager || isProvincial">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity" ng-options="x.cityName for x in subCityList"
							 ng-disabled="selectedProvince===null||selectedProvince.provinceID==='000'">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"  ng-show="!isProvincial"></option>
						</select>
					</div>
					<label for="city" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'CITY'|translate" ng-show="!isSuperManager && !isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class2" ng-show="!isSuperManager && !isProvincial">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity" ng-options="x.authName for x in subCityList"
							 ng-disabled="selectedProvince===null||selectedProvince.provinceID==='000'">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="clearf"> </div>
					<label for="serviceType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
						<select class="form-control" name="serviceType" ng-model="serviceType" ng-options="x.id as x.name for x in serviceTypeChoise" style="max-width:200px">
						</select>
					</div>

					<label ng-show="!isProvincial" for="subProvinceType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'ENTERPRISE_TYPE'|translate"></label>
					<div ng-show="!isProvincial" class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
						<select class="form-control" name="subProvinceType" ng-model="subProvinceType" ng-options="x.id as x.name for x in subProvinceTypeSelect" style="max-width:200px">
						</select>
					</div>

					<label class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></label>
					<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 time">
						<div class="input-daterange input-group" id="datepicker">
								<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="start" ng-keyup="searchOn()"/>
								<span class="input-group-addon" ng-bind="'TO'|translate"></span>
								<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
						</div>
					</div>

					<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
							<button ng-click="queryEnterpriseStatInfo()" type="submit" ng-disabled="initSel.search" class="btn search-btn" ><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
				</div>
			</form>

			<div class="add-table">
					<button id="exportSpokesList" class="btn add-btn" ng-click="exportFile()"><icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span></button>
			</div>

			<div style="font-weight:bold;padding:0px 20px 10px 20px">
				<span style="font-size:14px;padding:20px 20px 0px 0px" ng-bind="'MONTHSTATISTICS'|translate">
					{{ enterpriseStatCollect.enterpriseCount }}
				</span>
				<span ng-show="showStat" style="color:red">
					<span style="padding:20px 20px 0px 0px">
						时间：{{ enterpriseStatCollect.time }}
					</span>
					<span style="padding:20px 20px 0px 0px">
						企业总数：{{ enterpriseStatCollect.enterpriseCount }}
					</span>
					<span style="padding:20px 20px 0px 0px">
						名片彩印成员数：{{ enterpriseStatCollect.mpMemberCount }}
					</span>
					<span style="padding:20px 20px 0px 0px">
						名片彩印投递总数：{{ enterpriseStatCollect.mpUseCount }}
					</span>
					<span style="padding:20px 20px 0px 0px">
						热线彩印省份版成员数：{{ enterpriseStatCollect.rxsfMemberCount }}
					</span>
					<span style="padding:20px 20px 0px 0px">
						热线彩印省份版投递总数：{{ enterpriseStatCollect.rxsfUseCount }}
					</span>
				</span>
			</div>
			
			<div class="coorPeration-table">
					<table class="table table-striped table-hover">
						<thead>
							<tr>
								<th style="width:7%" ng-bind="'COMMON_PROVINCE'|translate"></th>
								<th style="width:5%" ng-bind="'CITY'|translate"></th>
								<th style="width:6%" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></th>
								<th ng-show="!isProvincial" style="width:6%" ng-bind="'ENTERPRISE_TYPE'|translate"></th>
								<th style="width:12%" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>
								<th style="width:7%" ng-bind="'YEARMONTH'|translate"></th>
								<th style="width:7%" ng-bind="'OTHER_PROVICE_CONTENTAUDIT_FIRSTTYPE'|translate"></th>
								<th style="width:8%" ng-bind="'OTHER_PROVICE_CONTENTAUDIT_SECONDTYPE'|translate"></th>
								<th style="width:8%" ng-bind="'MEMBERCOUNT'|translate"></th>
								<th style="width:8%" ng-bind="'OTHER_PROVICE_USECOUNT'|translate"></th>
								<th style="width:8%" ng-bind="'CMCC_USE'|translate"></th>
								<th style="width:8%" ng-bind="'CUCC_USE'|translate"></th>
								<th style="width:8%" ng-bind="'CTCC_USE'|translate"></th>
								<!-- <th style="width:8%" ng-bind="'EXPERIENCECOUNT'|translate"></th>
								<th style="width:8%" ng-bind="'REALCOUNT'|translate"></th> -->
							</tr>
						</thead>
						<tbody>
								<tr ng-repeat="item in StatInfoListData">
									<td><span title="{{provinceList2[item.provinceID]}}">{{provinceList2[item.provinceID]}}</span></td>
									<td><span title="{{cityList2[item.cityID]}}">{{cityList2[item.cityID]}}</span></td>
									<td><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
									<td ng-show="!isProvincial"><span title="{{subProvinceTypeMap[item.subProvinceType]}}">{{subProvinceTypeMap[item.subProvinceType]}}</span></td>
									<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
									<td><span title="{{getTime(item.statMonth)}}">{{getTime(item.statMonth)}}</span></td>
									<td><span title="{{getServiceType(item.serviceType)}}">{{getServiceType(item.serviceType)}}</span></td> 
									<td><span title="{{getSubServType(item.subServType, item.hangupType)}}">{{getSubServType(item.subServType, item.hangupType)}}</span></td>
									<td><span title="{{item.memberCount}}">{{item.memberCount}}</span></td>
									<td><span title="{{item.useCount}}">{{item.useCount}}</span></td>
									<td><span title="{{item.useCountMobile}}">{{item.useCountMobile}}</span></td>
									<td><span title="{{item.useCountUnicom}}">{{item.useCountUnicom}}</span></td>
									<td><span title="{{item.useCountTelecom}}">{{item.useCountTelecom}}</span></td>
									<!-- <td><span title="{{item.experienceCount}}">{{item.experienceCount}}</span></td>
									<td><span title="{{item.tureUseCount}}">{{item.tureUseCount}}</span></td> -->
								</tr>
								<tr ng-show="StatInfoListData.length<=0">
									<td style="text-align:center" colspan="13" ng-bind="'COMMON_NODATA'|translate"></td>
								</tr>
							</tbody>
					</table>
				</div>

				<div>
					<ptl-page tableId="0" change="queryEnterpriseStatInfo('justPage')"></ptl-page>
				</div>
		</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>