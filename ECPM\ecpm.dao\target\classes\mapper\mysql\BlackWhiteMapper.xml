<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.BlackWhiteMapper">
	<resultMap id="blackWhite"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.BlackWhiteWrapper">
		<result property="id" column="ID" />
		<result property="servType" column="servType" />
		<result property="subServType" column="subServType" />
		<result property="blackWhiteListType" column="blackWhiteListType" />
		<result property="memberName" column="memberName" />
		<result property="msisdn" column="msisdn" />
		<result property="isUse" column="isUse" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="orgCode" column="orgCode" />
		<result property="operatorID" column="operatorID" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="operateTime" column="operateTime" javaType="java.util.Date" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
	</resultMap>




	<select id="getBlackWhiteList" resultMap="blackWhite">
		select ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		isUse,
		enterpriseID,
		operatorID,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from ecpm_t_blackwhite_member
		where
		<foreach item="blackWhite" collection="list" open=" "
			separator="or" close=" ">
			(enterpriseID = #{blackWhite.enterpriseID}
			and blackWhiteListType = #{blackWhite.blackWhiteListType}
			and msisdn = #{blackWhite.msisdn}
			and servType = #{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and subServType is null
			</if>
			)

		</foreach>
	</select>

	<delete id="deleteBlackWhiteList">
		delete from ecpm_t_blackwhite_member where
		<foreach item="blackWhite" collection="list" open=" "
			separator="or" close=" ">
			(enterpriseID = #{blackWhite.enterpriseID}
			and blackWhiteListType = #{blackWhite.blackWhiteListType}
			and msisdn = #{blackWhite.msisdn}
			and servType = #{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and subServType is null
			</if>
			)
		</foreach>
	</delete>

	<select id="searchBlackWhiteList" resultMap="blackWhite">
		select ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		isUse,
		enterpriseID,
		operatorID,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from ecpm_t_blackwhite_member t
		where
		<foreach item="blackWhite" collection="list" open=" "
			separator="or" close=" ">
			(t.enterpriseID = #{blackWhite.enterpriseID}
			and t.blackWhiteListType = #{blackWhite.blackWhiteListType}
			and t.msisdn = #{blackWhite.msisdn}
			and t.servType =
			#{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and t.subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and t.subServType is null
			</if>
			)
		</foreach>
	</select>

	<insert id="addBlackWhiteList">
		insert into ecpm_t_blackwhite_member
		(
		ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		enterpriseID,
		orgCode,
		operatorID,
		isUse,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		)
		values
		<foreach collection="list" item="blackWhite" separator=",">
			(
			#{blackWhite.id},
			#{blackWhite.servType},
			#{blackWhite.subServType},
			#{blackWhite.blackWhiteListType},
			#{blackWhite.memberName},
			#{blackWhite.msisdn},
			#{blackWhite.enterpriseID},
			#{blackWhite.orgCode},
			#{blackWhite.operatorID},
			<if test="blackWhite.isUse != null">
				#{blackWhite.isUse},
			</if>
			<if test="blackWhite.isUse == null">
				'1',
			</if>
			#{blackWhite.createTime},
			#{blackWhite.operateTime},
			#{blackWhite.extInfo},
			#{blackWhite.reserved1},
			#{blackWhite.reserved2},
			#{blackWhite.reserved3},
			#{blackWhite.reserved4}
			)
		</foreach>
	</insert>


	<update id="updateBlackWhiteList" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index" item="blackWhite"
			open="" separator=";">
			update ecpm_t_blackwhite_member
			<trim prefix="set" suffixOverrides=",">
				<!-- <if test="blackWhite.servType != null"> servType=#{blackWhite.servType}, 
					</if> <if test="blackWhite.subServType != null"> subServType=#{blackWhite.subServType}, 
					</if> -->
				<if test="blackWhite.memberName != null and blackWhite.memberName != ''">
					memberName=#{blackWhite.memberName},
				</if>
				<if test="blackWhite.isUse != null">
					isUse=#{blackWhite.isUse},
				</if>
				<if test="blackWhite.operatorID != null">
					operatorID=#{blackWhite.operatorID},
				</if>
				<if test="blackWhite.operateTime != null">
					operateTime=#{blackWhite.operateTime}
				</if>
			</trim>
			where msisdn =#{blackWhite.msisdn}
			and
			blackWhiteListType=#{blackWhite.blackWhiteListType}
			and
			enterpriseID=#{blackWhite.enterpriseID}
			and servType =
			#{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and subServType is null
			</if>
		</foreach>
	</update>

	<!-- 根据blackwhiteListType+enterpriseID+servType+subServType+isUse=1(已使用)+orgCode不为空查询黑白名单 -->
	<select id="queryBlackWhiteList" resultMap="blackWhite">
		select ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		isUse,
		enterpriseID,
		operatorID,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		orgCode
		from
		ecpm_t_blackwhite_member t
		where t.enterpriseID = #{enterpriseID}
		and t.servType = #{servType}
		<if test="subServType != null">
			and subServType = #{subServType}
		</if>
		<if test="subServType == null">
			and subServType is null
		</if>
		and t.blackWhiteListType = #{blackWhiteListType}
		and t.isUse = 1
		and (t.orgCode is not null or t.reserved1 is not null)
	</select>


	<select id="queryBlackWhiteMemberList" resultMap="blackWhite">
		select ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		isUse,
		enterpriseID,
		operatorID,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		orgCode
		from ecpm_t_blackwhite_member t
		where t.enterpriseID = #{enterpriseID} and t.servType = #{servType}
		and t.subServType = #{subServType} and t.blackWhiteListType =
		#{blackWhiteListType}
	</select>

	<update id="updateBlackWhiteMemberOrgCode">
		update
			ecpm_t_blackwhite_member 
		<set>
			<if test="orgCode != null">
				orgCode=#{orgCode},
			</if>
			<if test="reserved1 != null">
				reserved1=#{reserved1},
			</if>
		</set>
		where
			enterpriseID = #{enterpriseID} 
			and servType = #{servType}
			and subServType = #{subServType} 
			and blackWhiteListType = #{blackWhiteListType}
	</update>

	<select id="checkBlackWhiteMemberExist" resultType="java.lang.Integer">
		select count(1)
		from ecpm_t_blackwhite_member t
		where t.enterpriseID =
		#{enterpriseID}
		and t.msisdn= #{msisdn}
		<if test="subServType != null">
			and t.subServType = #{subServType}
		</if>
		<if test="servType != null">
			and t.servType = #{servType}
		</if>
		<if test="blackWhiteListType != null">
			and t.blackWhiteListType = #{blackWhiteListType}
		</if>
	</select>
	
	<select id="queryBlackWhiteGroup" resultMap="blackWhite">
		SELECT t.enterpriseID, t.blackWhiteListType, t.servType, t.subServType, t.orgCode, t.reserved1
		FROM ecpm_t_blackwhite_member t
		WHERE t.isUse = 1
		AND (t.orgCode is not null or t.reserved1 is not null)
		GROUP BY t.enterpriseID, t.blackWhiteListType, t.servType, t.subServType, t.orgCode, t.reserved1
	</select>
	
	<!--查询自增序列 -->
	<select id="querySequence" resultType="java.lang.Integer">
		select nextval('ecpm_seq_blackWhiteList')
	</select>

	<select id="queryForDeliveryNoBlack" resultMap="blackWhite">
		select 
			ID,
			servType,
			subServType,
			blackWhiteListType,
			memberName,
			msisdn,
			isUse,
			enterpriseID,
			orgCode,
			operatorID,
			createTime,
			operateTime,
			extInfo,
			reserved1,
			reserved2,
			reserved3,
			reserved4
		from
			ecpm_t_blackwhite_member t
		where 
			t.blackWhiteListType in (2, 3)
			and t.isUse = 1
			and t.servType = #{servType}
			and t.msisdn in
			<foreach collection="hotlineNoList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
	</select>
</mapper>
