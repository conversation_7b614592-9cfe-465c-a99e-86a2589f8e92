var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
      $scope.token = $.cookie("token");
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否超管
        $scope.isSuperManager = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager');

        $scope.selectedProvince = null;

        //默认为1：名片彩印
        $scope.serviceType = "";
        $scope.subProvinceType = "";
        $scope.subProvinceTypeMap = {"1":"分省","2":"集客", "3": "移动云PAAS","4": "咪咕音乐"};
        $scope.subProvinceTypeSelect = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "分省"
            },
            {
                id: "2",
                name:
                    "集客"
            },
            {
                id: "3",
                name:
                    "移动云PAAS"
            },
            {
                id: "4",
                name:
                    "咪咕音乐"
            }
        ];

        $scope.channelSelectMap = [{
            id: "",
            name: "不限"
        }];
        $scope.enterpriseTypeList = localStorage.getItem("enterpriseTypeList")
        if($scope.enterpriseTypeList!=null){
            angular.forEach(JSON.parse($scope.enterpriseTypeList),function (item){
                if("112" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"3","name": "移动云PAAS"} );
                }
                if("111" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"2","name": "集客"});
                }
                if("113" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"4","name": "咪咕音乐"});
                }
                if("0" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"1","name": "省份"});
                }
            });
        }
        $scope.subProvinceTypeSelect = $scope.channelSelectMap;
        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            } ,
            {
                id: 5,
                name: "热线彩印省份版"
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            provinceName: "0",//归属地
            startTime: "",
            endTime: "",
            search : false,
        };

        if ($scope.isSuperManager) {
            $scope.queryProvinceAndCitybychaoguan();
        }
        else {
            $scope.queryProvinceAndCity();
        }

        //$scope.queryEnterpriseStatInfo();
    }
    $scope.exportFile = function () {
      var req = {
        "param":{
          "provinceID":$scope.provinceID,
          "cityID":$scope.reqTemp.cityID,
          "serviceType":$scope.serviceType,
          "startDate":$scope.initSel.startTime,
          "endDate":$scope.initSel.endTime,
          "enterpriseName":"",
          "parentEnterpriseID":"",
          "areaDimension":2,
          "timeDimension":1,
          "enterpriseID":"",
          "enterpriseType":5,
          "type":4,
          "token":$scope.token,
          "isExport":1,
          "subProvinceType": $scope.subProvinceType || null,

        },
        "url":"/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile",
        "method":"get"
      }
      CommonUtils.exportFile(req);
    };
    $scope.getServiceType = function (serviceType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 3) {
            return "广告彩印";
        }
        else if (serviceType == 5) {
            return "热线彩印省份版";
        }
        else if (serviceType == 7) {
            return "交互彩印";
        }
    };

    $scope.getSubServType = function (subServType, hangupType) {
        if (subServType == 1) {
            return "主叫屏显";
        }
        else if (subServType == 2) {
            return "被叫屏显";
        }
        else if (subServType == 4) {
        	if(hangupType != undefined && hangupType != null && hangupType == 1) {
        		return "主叫挂机短信";
        	}
        	if(hangupType != undefined && hangupType != null && hangupType == 2) {
        		return "被叫挂机短信";
        	}
            return "被叫挂机短信";
        }
        else if (subServType == 8) {
            return "挂机彩信";
        }
        else if (subServType == 3) {
            return "屏显";
        }
    };

    $scope.getTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        return year + "-" + month + "-" + day;
    }

    $('.input-daterange').datepicker({
		format: "yyyy-mm-dd",
		weekStart: 0,
		language: "zh-CN",
		clearBtn: true,
		autoclose: true
	});

	$('#start').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});

	$('#end').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});

	//判断搜索按钮是否置灰
	$scope.searchOn = function () {
		var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;
        $scope.initSel.startTime = "";
        $scope.initSel.endTime = "";

		if (startTime !== '')
		{
			$scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
		}
		
		if (endTime !== '')
		{
			$scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
		}
		
		if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
			$scope.initSel.search = false;
		}
		else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
			$scope.initSel.search = false;
		}
		else {
			$scope.initSel.search = true;
		}
	}

    //后续post的函数
    $scope.queryEnterpriseStatInfo = function (condition) {
        //若日期清掉，starttime和endtime都设置为空
        if ($scope.time == "") {
            $scope.initSel.startTime = "";
            $scope.initSel.endTime = "";
        }

        if (condition != 'justPage') {
            if ($scope.selectedProvince == null) {
                $scope.provinceID = '';
            }
            else {
                $scope.provinceID = $scope.selectedProvince.provinceID || $scope.selectedProvince.fieldVal;
            }

            var req = {
                "areaDimension": 2,
                "timeDimension": 1,
                "enterpriseType":5,
                "subProvinceType": $scope.subProvinceType || null,
                "provinceID": $scope.provinceID,
                "serviceType": $scope.serviceType || '',
                "startDate": $scope.initSel.startTime || '',
                "endDate": $scope.initSel.endTime || '',
                "cityID": "",
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };

            if ($scope.selectedCity == undefined) {
                req.cityID = "";
            }
            else if($scope.selectedCity.provinceID == ""){
                req.cityID = "";
            }
            else
            {
                req.cityID = $scope.selectedCity.cityID || $scope.selectedCity.fieldVal;
            }

            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseStatInfoCsvFile?provinceID=" + $scope.provinceID + "&cityID=" + req.cityID +
                "&serviceType=" + $scope.serviceType + "&startDate=" + $scope.initSel.startTime + "&endDate=" + $scope.initSel.endTime + "&enterpriseName=" + "&areaDimension=2" + 
                "&timeDimension=1" + "&enterpriseID=" + "&parentEnterpriseID=" + "&enterpriseType=5" +"&type=4";
            console.log($scope.exportUrl);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseStatInfo",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if(data.resultCode=='1030100000'){
                $scope.StatInfoListData=result.enterpriseStatInfoList||[];
                $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                $scope.pageInfo[0].totalPage=result.totalNum!==0 ?Math.ceil(result.totalNum/parseInt($scope.pageInfo[0].pageSize)):1;
              }  
              else 
              {
                $scope.tip = data.resultCode;
                $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
            )
          }
        });

    };

    //搜索省份改变时，找到对应的市(超管)
    $scope.changeSelectedProvincebychaoguan = function (selectedProvince) {
        $scope.subCityList = null;
        if (selectedProvince) {

            jQuery.each($scope.cityList, function (i, e) {
                if (e.key == selectedProvince.provinceID) {
                    $scope.subCityList = e;
                }
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
    }

    //从Local Storage中查询超管省的方法
    $scope.queryProvinceAndCitybychaoguan = function () {
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
        $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
        $scope.provinceList2 = {};
        $scope.cityList2 = {};

        //删除全国选项
        var length = $scope.provinceList.length;
        for (var i = 0; i < length; i++) {
            if ($scope.provinceList[i].provinceID == "000") {
                $scope.provinceList.splice(i, 1); //删除下标为i的元素
                break;
            }
        }
 
        jQuery.each($scope.provinceList, function (i, e) {
            $scope.provinceList2[e.provinceID] = e.provinceName;
        });
        $scope.cityList = $scope.mapToList($scope.cityList);
        for (var a = 0; a < $scope.cityList.length; a++) {
            jQuery.each($scope.cityList[a], function (i, e) {
                $scope.cityList2[e.cityID] = e.cityName;
            });
        }
        console.log($scope.cityList2);

    };

    //搜索省份改变时，找到对应的市
    $scope.changeSelectedProvince = function (selectedProvince) {
        $scope.subCityList = null;
        if (selectedProvince) {
            $scope.subCityList = $scope.cityList.filter(function (a) {
                return a.parentAuthID == selectedProvince.id;
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
    }
    //省市联动方法
    $scope.queryProvinceAndCity = function () {
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
        $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
        $scope.provinceList2 = {};
        jQuery.each($scope.provinceList, function (i, e) {
            $scope.provinceList2[e.fieldVal] = e.authName;
        });
        console.log($scope.provinceList2);
        $scope.cityList2 = {};
        jQuery.each($scope.cityList, function (i, e) {
            $scope.cityList2[e.fieldVal] = e.authName;
        });
        console.log($scope.cityList2);
    };

    //省市联动时调用，用于转化格式
    $scope.mapToList = function (map) {
        var result = [];
        jQuery.each(map, function (_, o) {
            if (o) {
                o.key = o[0].provinceID;
                result.push(o);
            }
        });
        return result;
    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])