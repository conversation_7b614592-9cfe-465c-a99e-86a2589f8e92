<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <!--<title>新增活动</title>-->
    <link href="../../../../../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>

    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <!--引入topMenu组件 -->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <!--引入分页组件 -->
    <script type="text/javascript" src="../../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
    <!--引入上传图片组件-->
    <link rel="stylesheet" type="text/css" href="../../../../../css/webuploader.css">
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>

    <!--引入预览图片组件-->
    <link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css"/>
    <script type="text/javascript" src="../../../../../directives/preview/preview.js"></script>

    <!--引入时间控件-->
    <script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
    <link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">

    <!--引入树形结构控件-->
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.ztree.all-3.5.min.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../css/metro.css"/>
    <script type="text/javascript" src="advertisePrint_create.js"></script>

    <style>
		.form-group {
			margin-bottom: 30px;
		}

		.form-group .control-label icon {
			color: #ff254c;
			vertical-align: sub;
			margin-right: 2px;
		}

		textarea {
			resize: none;
		}

		.rest-count {
			position: absolute;
			bottom: 0;
			right: -42px
		}

		.check-li {
			padding: 7px 15px 0 15px;
			float: left;
		}

		.check-li span {
			vertical-align: middle;
			cursor: pointer;
		}

		.label-left {
			min-width: 200px;
		}

		.label-right {
			min-width: 120px
		}

		.control-label {
			padding-top: 7px;
			margin-bottom: 0;
			text-align: right;
		}

		.col-xs-3 {
			min-width: 340px;
		}

		/* 自定义：覆盖选中的背景色 */
		.no-curSelectedNode {
			background-color: #fff !important;
		}

		.choose-area li {
			float: left;
			padding: 6px 0px;
			border: 1px solid #c3c3c3;
			border-radius: 5px;
			margin: 0 15px 15px 0;
			position: relative;
			width: 70px;
			text-align: center;
		}

		.delete-area {
			background: url(/qycy/ecpmp/assets/images/tableEditIcons18.png) no-repeat;
			width: 18px;
			height: 18px;
			background-position: -217px 0;
			display: inline-block;
			position: absolute;
			top: -6px;
			right: -6px;
			cursor: pointer;
		}

		.template {
			float: left;
			width: 150px;
			height: 75px;
			margin-left: 15px;
			position: relative;
		}

		.template img {
			width: 100%;
			height: 100%;
			cursor: pointer;
		}

		.template .redio-btn {
			position: absolute;
			bottom: 0;
			right: 0;
			cursor: pointer;
		}

        .ng-dirty.ng-invalid {
		    border-color: red;
	    }

	    .ng-dirty.invalid {
		    border-color: red;
	    }

		.cooperation-head {
			padding: 20px;
		}
		#MMS_filePicker div:nth-child(2) {
			width: 100% !important;
			height: 100% !important;
		}
    </style>
</head>
<body ng-app="myApp" ng-controller='createAdvertiseCtrl' class="body-min-width" ng-init="init()">
<div>
    <div class="cooperation-head" ng-show="isSuperManager&&enterpriseType==='1'">
        <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ADD_ACTIVITY'|translate"></span>
    </div>
    <div class="cooperation-head" ng-show="isSuperManager&&enterpriseType==='3'">
        <span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ADD_ACTIVITY'|translate"></span>
    </div>
    <div class="cooperation-head" ng-show="isZhike">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ADD_ACTIVITY'|translate"></span>
    </div>
    <div class="cooperation-head" ng-show="isAgent">
        <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ADD_ACTIVITY'|translate"></span>
    </div>
    <form class="form-horizontal" name="myForm" style="padding-top: 40px;">
        <div class="form-group">
            <!--活动名称-->
            <label for="activityName" class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'COMMON_ACTIVITYNAME'|translate"></span>
            </label>

            <div class="col-xs-3">
                <input type="text" class="form-control" name="activityName" id="activityName" ng-model="activityName"
                       ng-maxlength="256" required placeholder="{{'COMMON_PLEASEINPUTACTIVITYMANAGE'|translate}}"
                       autocomplete="off"
                       ng-class="{true:'error-border',false:''}[myForm.activityName.$dirty && myForm.activityName.$invalid||isExist]">
                <span style="color:red" ng-show="myForm.activityName.$dirty && myForm.activityName.$invalid || isExist">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
						<span ng-show="myForm.activityName.$error.required" ng-bind="'REQUIRED'|translate"></span>
						<span ng-show="myForm.activityName.$error.maxlength" ng-bind="'LENGTH_256'|translate"></span>
						<span ng-show="!myForm.activityName.$invalid &&isExist"
                              ng-bind="'ACTIVITY_EXIST'|translate"></span>
					</span>
            </div>

            <!--活动有效期-->
            <label class="col-xs-1 control-label label-right">
                <icon>*</icon>
                <span ng-bind="'ACTIVITY_EXPIRYDATE'|translate"></span>
            </label>

            <div class="input-daterange input-group col-xs-3" id="datepicker"
                 style="padding-left: 15px;padding-right: 15px;">
                <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="start"/>
                <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="end"/>
            </div>
        </div>

        <div class="form-group">
            <!--地区设置-->
            <label class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'ACTIVITY_AREASET'|translate"></span>
            </label>

            <div class="choose-area col-xs-8">
                <ul>
                    <li ng-repeat="item in showCities">
                        <span ng-bind="item.name"></span>
                        <icon class="delete-area" ng-click="removeArea($index)"></icon>
                    </li>
                    <button type="button" class="btn search-btn btn-primary" ng-click="addAreaPop()"
                            style="float: left">
                        <span ng-bind="'ADDAREA'|translate" class="ng-binding"></span>
                    </button>
                </ul>

            </div>
        </div>

        <div class="form-group">
            <!--活动规则-->
            <label class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'ACTIVITY_RULES'|translate"></span>
            </label>

            <div class="col-xs-3">
                <div style="position: relative">
						<textarea class="form-control" rows="6" id="activityRuleDesc" ng-model="activityRuleDesc"
                                  ng-maxlength="200"
                                  required name="activityRuleDesc" ng-change="calculateRuleRest()"
                                  placeholder="{{'PLACEHOLDER_RULES'|translate}}"
                                  ng-class="{true:'error-border',false:''}[myForm.activityRuleDesc.$dirty && myForm.activityRuleDesc.$invalid]">
						</textarea>

                    <div class="rest-count" style="right: -54px">
                        <span ng-bind="ruleRestCount" style="color: #ff0000;"></span>/200
                    </div>
                </div>

                <span style="color:red" ng-show="myForm.activityRuleDesc.$dirty && myForm.activityRuleDesc.$invalid">

						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             style="vertical-align: middle">
						<span ng-show="myForm.activityRuleDesc.$error.required" ng-bind="'REQUIRED'|translate"
                              style="vertical-align: middle"></span>
						<span ng-show="myForm.activityRuleDesc.$error.maxlength"
                              ng-bind="'ACTIVITYRULEDESC_LENGTH'|translate"
                              style="vertical-align: middle"></span>
					</span>
            </div>
            <!--屏显内容-->
            <label class="col-xs-1 control-label label-right">
                <icon>*</icon>
                <span ng-bind="'ACTIVITY_PXCONTENT'|translate"></span>
            </label>

            <div class="col-xs-3">
                <div style="position: relative">
						<textarea class="form-control" rows="6" id="pxContent" ng-model="pxContent" maxlength="70"
                                  required name="pxContent" ng-change="calculatePXRest()"
                                  placeholder="{{'PLACEHOLDER_SCREEN_CONTENT'|translate}}"
                                  ng-blur="PXRestCount>=0?sensitiveCheck(pxContent,'pxContent'):''"
                                  ng-class="{true:'error-border',false:''}[myForm.pxContent.$dirty && myForm.pxContent.$invalid || screen_sensitive!=='' || !contentVali]">
						</textarea>

                    <div class="rest-count">
                        <span ng-bind="PXRestCount" style="color: #ff0000;"></span>/70
                    </div>
                </div>
                <span style="color:red"
                      ng-show="(myForm.pxContent.$dirty && myForm.pxContent.$invalid) || screen_sensitive!=='' || !contentVali">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             style="vertical-align: middle">
						<span ng-show="myForm.pxContent.$error.required" ng-bind="'REQUIRED'|translate"
                              style="vertical-align: middle"></span>
                        <span ng-show='contentDesc'>{{contentDesc|translate}}</span>
						<span ng-show="screen_sensitive!==''&&!myForm.pxContent.$error.maxlength&&!myForm.pxContent.$error.required"
                              style="vertical-align: middle">
							<span ng-bind="'CONTENT_DETECTION'|translate"></span>
							<span ng-bind="screen_sensitive"></span>
							<span ng-bind="'CONTENT_ISSENSITIVEWORDS'|translate"></span>
						</span>
					</span>
            </div>
        </div>
        <!-- 运营商 -->
        <div class="form-group platforms">
            <label class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'PLATFORM'|translate"></span>
            </label>
            <div class="col-xs-3" ng-click="checkSignatureRequired()">
                <li class="check-li">
                    <span class="check-btn checked-btn"> </span>
                    {{'MOBILE'|translate}}
                </li>
                <li class="check-li">
                    <span class="check-btn checked-btn"> </span>
                    {{'UNICOM'|translate}}
                </li>
                <li class="check-li">
                    <span class="check-btn checked-btn"> </span>
                    {{'TELECOM'|translate}}
                </li>
            </div>
            <!--所属行业-->
            <label class="col-xs-1 control-label label-right">
                <icon>*</icon>
                <span ng-bind="'ENTERPRISE_INDUSTRY'|translate"></span></label>
            <div class="col-xs-3">
                <select class="form-control"
                        name="industry1"
                        required
                        ng-model="selectedIndustry"
                        ng-options="x.industryName for x in industryList"
                        ng-change="changeIsSensitive(selectedIndustry)"
                        ng-show="true"
                >
                    <option value="" ng-bind="" ng-show="true"></option>
                </select>

                <span style="color:red" class="uplodify-error-img" ng-show="selectedIndustryErrorInfo"></span>
                <span style="color:red" class="redFont" ng-bind="selectedIndustryErrorInfo|translate"
                      ng-show="selectedIndustryErrorInfo"></span>
            </div>
        </div>
        <!--REQ-113 -->
        <!-- 签名 -->
        <div class="form-group platforms">
            <label class="col-xs-1 control-label label-left">
							<span style="color:red"
                                  ng-bind="'*'|translate"
                                  ng-model="signatureRequired"
                                  ng-show="signatureRequired =='1'&&noSign == '1'">
							</span>
                <span ng-bind="'SIGNATURE'|translate"></span></label>
            <div class="col-xs-3">
                <input
                        type="text" placeholder="请输入1~67字" class="form-control"
                        rows="1" name="signature" ng-model="signature" maxlength="67"
                        ng-required="signatureRequired==='1'&&noSign==='1'"
                        ng-change="verifySignature()"
                />
                <span style="color:red" ng-show="myForm.signature.$invalid&&myForm.signature.$dirty">
                    <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                         align="absmiddle">
                    <span style="color:red" ng-show="myForm.signature.$error.required" ng-bind="'SIGNATURE_REQUIRED'|translate"></span>
                    <span style="color:red" ng-show="myForm.signature.$error.pattern&&!myForm.signature.$error.maxlength" ng-bind="'SIGNATURE_PATTERN_ERROR'|translate"></span>
                    <span style="color:red" ng-show="myForm.signature.$error.maxlength" ng-bind="'SIGNATURE_MAXLENTH_67'|translate"></span>
                </span>
            </div>

            <!--营业执照-->
            <label class="col-xs-1 control-label label-right">
                <span style="color:red"
                      ng-bind="'*'|translate"
                      ng-model="isSensitiveIndustry"
                      ng-show="isSensitiveIndustry =='1' || isSensitiveIndustry =='3'">
                </span>
                <span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span></label>
            <div class="col-xs-3">
                <cy:uploadify filelistid="fileList_" filepickerid="filePicker_" accepttype="accepttype_"
                              uploadifyid="uploadifyid_1" validate="isValidate_" filesize="filesize_"
                              mimetypes_="mimetypes_"
                              formdata="uploadParam_" uploadurl="uploadurl_" desc="uploadDesc_" numlimit="numlimit_"
                              urllist="urlList_" createthumbnail="isCreateThumbnail_" namelistid="nameList_"
                              class="" ng-show="operateType !='detail'">
                </cy:uploadify>
            </div>

        </div>
        <div class="form-group">
            <!--挂机彩信标题-->
            <label for="contentTitle" class="col-xs-1 control-label label-left">
                <span ng-bind="'GJCX_TITLE'|translate"></span>
            </label>

            <div class="col-xs-3">
                <input type="text" class="form-control" name="contentTitle" id="contentTitle" ng-model="contentTitle"
                       ng-maxlength="20" ng-blur="sensitiveCheck(contentTitle,'contentTitle')" autocomplete="off"
                       ng-class="{true:'error-border',false:''}[myForm.contentTitle.$dirty && myForm.contentTitle.$invalid || title_sensitive!=='']"
                       ng-change="changeTitle()" placeholder="{{'PLACEHOLDER_MSS_TITLE'|translate}}">
                <span style="color:red"
                      ng-show="(myForm.contentTitle.$dirty && myForm.contentTitle.$invalid) || title_sensitive!==''">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             style="vertical-align: middle">
						<span ng-show="myForm.contentTitle.$error.maxlength" ng-bind="'CONTENTTITLE_LENGTH'|translate"
                              style="vertical-align: middle"></span>
						<span ng-show="title_sensitive!==''&&!myForm.contentTitle.$error.maxlength&&!myForm.contentTitle.$error.required"
                              style="vertical-align: middle">
							<span ng-bind="'CONTENT_DETECTION'|translate"></span>
							<span ng-bind="title_sensitive"></span>
							<span ng-bind="'CONTENT_ISSENSITIVEWORDS'|translate"></span>
						</span>
					</span>
            </div>
        </div>
        <div class="form-group">
            <!--挂机彩信内容-->
            <label class="col-xs-1 control-label label-left">
                <span ng-bind="'GJCX_CONTENT'|translate"></span>
            </label>

            <div class="col-xs-3">
                <div style="position: relative">
						<textarea class="form-control" rows="4" id="MMSContent" ng-model="MMSContent" ng-maxlength="750"
                                  name="MMSContent" ng-disabled="contentTitle===''||contentTitle===undefined"
                                  ng-required="contentTitle!==''&&contentTitle!==undefined"
                                  ng-change="calculateMSSRest()"
                                  ng-blur="MMSRestCount>=0?sensitiveCheck(MMSContent,'MMSContent'):''"
                                  placeholder="{{MMSContent_placeholder}}"
                                  ng-class="{true:'error-border',false:''}[myForm.MMSContent.$dirty && myForm.MMSContent.$invalid || MMS_sensitive!=='']">
						</textarea>

                    <div class="rest-count" style="right: -54px;">
                        <span ng-bind="MMSRestCount" style="color: #ff0000;"></span>/750
                    </div>
                </div>
                <span style="color:red"
                      ng-show="myForm.MMSContent.$dirty && myForm.MMSContent.$invalid || MMS_sensitive!==''">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             style="vertical-align: middle">
						<span ng-show="myForm.MMSContent.$error.required" ng-bind="'REQUIRED'|translate"
                              style="vertical-align: middle"></span>
						<span ng-show="myForm.MMSContent.$error.maxlength" ng-bind="'GJCXMMSCONTENT_LENGTH'|translate"
                              style="vertical-align: middle"></span>
						<span ng-show="MMS_sensitive!==''&&!myForm.MMSContent.$error.maxlength&&!myForm.MMSContent.$error.required"
                              style="vertical-align: middle">
							<span ng-bind="'CONTENT_DETECTION'|translate"></span>
							<span ng-bind="MMS_sensitive"></span>
							<span ng-bind="'CONTENT_ISSENSITIVEWORDS'|translate"></span>
						</span>
					</span>
            </div>
        </div>
        <div class="form-group" ng-show="contentTitle">
            <!--挂机彩信图片-->
            <label class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'CX_PICTURE'|translate"></span>
            </label>
            <cy:uploadify filelistid="MMS_fileList" filepickerid="MMS_filePicker" accepttype="accepttype"
                          uploadifyid="MMS_uploadifyid" validate="MMS_isValidate" filesize="MMS_filesize" total-file-size = "{{MMS_filesize}}"
                          mimetypes="mimetypes"
                          formdata="uploadParam" uploadurl="uploadurl" desc="MMS_uploadDesc" numlimit="MMS_numlimit"
                          urllist="MMS_urlList" createthumbnail="isCreateThumbnail"
                          style="margin-left: 15px;float: left;">
            </cy:uploadify>
        </div>
        <div class="form-group">
            <!--活动banner-->
            <label class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'ACTIVITY_BANNER'|translate"></span>
            </label>
            <cy:uploadify filelistid="banner_fileList" filepickerid="banner_filePicker" accepttype="accepttype"
                          uploadifyid="banner_uploadifyid" validate="banner_isValidate" filesize="banner_filesize"
                          mimetypes="mimetypes"
                          formdata="uploadParam" uploadurl="uploadurl" desc="banner_uploadDesc"
                          numlimit="banner_numlimit"
                          urllist="banner_urlList" createthumbnail="isCreateThumbnail"
                          style="margin-left: 15px;float: left;">
            </cy:uploadify>
        </div>
        <div class="form-group">
            <!--模板选择-->
            <label class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'TEMPLETE_CHOOSE'|translate"></span>
            </label>

            <div style="width: 800px;float: left">
                <div ng-repeat="item in templateList track by $index" class="template"
                     ng-click="chooseTemplate(item,$index)"
                     style="margin-bottom: 10px">
                    <img ng-src="{{item.templateIconURL|imgfilter}}"/>
                    <span class="redio-btn"
                          ng-class="{true:'checked',false:''}[templateIndex===$index || item.templateID===templateID]"></span>
                </div>
                <ptl:preview urllist='templateURL' style="display: inline-block;margin:20px;"
                             nofiledesc="nofiledesc"></ptl:preview>
            </div>
        </div>
        <div class="form-group">
            <!--获奖条件-->
            <label class="col-xs-1 control-label label-left">
                <span ng-bind="'ACTIVITY_WINNINGCONDITIONS'|translate"></span>
            </label>

            <div>
                <!--代言条数-->
                <div style="float: left">
                    <div>
                        <div ng-click="rewardType=1;spokeDayNum=''" class="check-li">
                            <span class="redio-btn" ng-class="{true:'checked',false:''}[rewardType===1]"></span>
                            <span ng-bind="'ACTIVITY_SPOKENUM'|translate"></span>
                        </div>
                        <input type="text" class="form-control" name="spokePushNum" ng-model="spokePushNum"
                               autocomplete="off"
                               style="width: 200px;float: left;" ng-disabled="rewardType!==1"
                               pattern="^[1-9][0-9]{0,8}$">

                        <div class="clearfix"></div>
                    </div>
                    <div style="color:red;margin-left: 110px;"
                         ng-show="myForm.spokePushNum.$dirty && myForm.spokePushNum.$invalid">
                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
                        <span ng-show="myForm.spokePushNum.$error.pattern" ng-bind="'BETWEEN_NUMBER'|translate"></span>
                    </div>
                </div>
                <!--代言天数-->
                <div style="float: left">
                    <div>
                        <div ng-click="rewardType=2;spokePushNum=''" class="check-li" style="margin-left: 30px">
                            <span class="redio-btn" ng-class="{true:'checked',false:''}[rewardType===2]"></span>
                            <span ng-bind="'SPOKES_SPOKESDAYS'|translate"></span>
                        </div>
                        <input type="text" class="form-control" name="spokeDayNum" ng-model="spokeDayNum"
                               autocomplete="off"
                               style="width: 200px;float: left;" ng-disabled="rewardType!==2"
                               pattern="^[1-9][0-9]{0,8}$">

                        <div class="clearfix"></div>
                    </div>
                    <div style="color:red;margin-left: 140px;"
                         ng-show="myForm.spokeDayNum.$dirty && myForm.spokeDayNum.$invalid">
                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
                        <span ng-show="myForm.spokeDayNum.$error.pattern" ng-bind="'BETWEEN_NUMBER'|translate"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-group">
            <!--每天总投递量(屏显+挂机)-->
            <label class="col-xs-1 control-label label-left">
                <span ng-bind="'TOTAL_DAILY'|translate"></span>
            </label>

            <div class="col-xs-3">
                <input type="text" class="form-control" name="maxPushNum" ng-model="maxPushNum"
                       ng-class="{true:'error-border',false:''}[myForm.maxPushNum.$dirty && myForm.maxPushNum.$invalid]"
                       pattern="^[1-9][0-9]{0,8}$" autocomplete="off">
                <span style="color:red" ng-show="myForm.maxPushNum.$dirty && myForm.maxPushNum.$invalid">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
						<span ng-show="myForm.maxPushNum.$error.pattern" ng-bind="'BETWEEN_NUMBER'|translate"></span>
					</span>
            </div>
        </div>
        <div class="form-group">
            <!--代言总条数(屏显+挂机)-->
            <label class="col-xs-1 control-label label-left">
                <span ng-bind="'SPOKE_TOTLE_NUM'|translate"></span>
            </label>

            <div class="col-xs-3">
                <input type="text" class="form-control" name="totalNum" ng-model="totalNum"
                       ng-class="{true:'error-border',false:''}[myForm.totalNum.$dirty && myForm.totalNum.$invalid]"
                       pattern="^[1-9][0-9]{0,8}$" autocomplete="off">
                <span style="color:red" ng-show="myForm.totalNum.$dirty && myForm.totalNum.$invalid">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
						<span ng-show="myForm.totalNum.$error.pattern" ng-bind="'BETWEEN_NUMBER'|translate"></span>
					</span>
            </div>
        </div>
        <div class="form-group" ng-show="spokePushNum!==''||spokeDayNum!==''">
            <!--通知短信-->
            <label class="col-xs-1 control-label label-left">
                <icon>*</icon>
                <span ng-bind="'NOTIFY_MESSAGE'|translate"></span>
            </label>

            <div class="col-xs-3">
                <div style="position: relative">
						<textarea class="form-control" rows="6" ng-model="rewardNotify" ng-maxlength="62"
                                  ng-change="calculateNotifyRest()" ng-required="spokeDayNum!==''||spokePushNum!==''"
                                  ng-class="{true:'error-border',false:''}[myForm.rewardNotify.$dirty && myForm.rewardNotify.$invalid]"
                                  name="rewardNotify" placeholder="{{'PLACEHOLDER_NOTIFY'|translate}}">
						</textarea>

                    <div class="rest-count">
                        <span ng-bind="notifyRestCount" style="color: #ff0000;"></span>/62
                    </div>
                </div>

                <span style="color:red" ng-show="myForm.rewardNotify.$dirty && myForm.rewardNotify.$invalid">
						<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             style="vertical-align: middle">
						<span ng-show="myForm.rewardNotify.$error.required" ng-bind="'REQUIRED'|translate"
                              style="vertical-align: middle"></span>
						<span ng-show="myForm.rewardNotify.$error.maxlength" ng-bind="'MMSCONTENT_LENGTH'|translate"
                              style="vertical-align: middle"></span>
					</span>
            </div>
        </div>
    </form>
    <div style="margin: 40px 20px;margin-left: 18%;">
        <!-- 110迭代：ng-disabled取消 screen_sensitive!=='' || title_sensitive!=='' || MMS_sensitive!=='' || -->
        <button type="submit" class="btn btn-primary search-btn" ng-click="submitActivity()"
                ng-bind="'COMMON_SUBMIT'|translate"
                ng-disabled="myForm.$invalid || showCities.length===0 || effectivetime==='' || expiretime==='' || isExist ||(contentTitle&&MMSPicture==='')||
							bannerList.length===0 || templateURL.length===0||(signatureError && noSign==='1')||businessLicenseURLError || !contentVali">
        </button>
        <button class="btn" style="margin-left: 10px" ng-bind="'COMMON_BACK'|translate" ng-click="goBack()"></button>
    </div>

    <!--选择地区弹框-->
    <div class="modal fade" id="chooseAreaPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         data-backdrop="static" data-keyboard="false">
        <div role="document" class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" ng-bind="'CHOOSE_AREA'|translate"></h4>
                </div>
                <div class="modal-body" style="height: 500px;overflow-y: scroll">
                    <ul id="tree" class="ztree" style="margin-left: 60px"></ul>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn btn-primary search-btn" ng-click="saveCity()" data-dismiss="modal"
                            ng-bind="'COMMON_OK'|translate" aria-label="Close"></button>
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="delMemCancel"
                            ng-bind="'COMMON_BACK'|translate"></button>
                </div>
            </div>
        </div>
    </div>
    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" data-backdrop="static"
         data-keyboard="false" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true" ng-click="isSubmit===true?goBack():''">&times;</span></button>
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center"><p style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p></div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate" ng-click="isSubmit===true?goBack():''">
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>