<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.MenuMapper">

	<resultMap id="menuMap" type="com.huawei.jaguar.dsum.dao.domain.MenuWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="menuCode" column="menuCode" javaType="java.lang.String" />
		<result property="menuName" column="menuName" javaType="java.lang.String" />
		<result property="menuDesc" column="menuDesc" javaType="java.lang.String" />
		<result property="menuURL" column="menuURL" javaType="java.lang.String" />
		<result property="imageURL" column="imageURL" javaType="java.lang.String" />
		<result property="authID" column="authID" javaType="java.lang.Integer" />
		<result property="parentID" column="parentID" javaType="java.lang.Integer" />
		<result property="operateTime" column="operatetime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="displayNo" column="displayNo" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

	<select id="queryMenuByParentIdAndAuthID" resultMap="menuMap">
		select id,menuCode,menuName,menuDesc,menuURL,
		imageURL,authID,parentID,operatetime,operatorID,displayNo
		from dsum_t_menu where
		parentID in
		<foreach item="parentId" index="index" collection="list" open="("
			separator="," close=")">
			#{parentId}#
		</foreach>
		and
		authID in (
		select t1.authID from dsum_t_role_auth t1
		where t1.authType = 1 and t1.roleID in(
		select t0.roleID from dsum_t_account_role t0
		where t0.accountID = #{accountID}
		)
		)
	</select>

	<select id="queryTopMenuByAuthId" resultMap="menuMap">
		select id,menuCode,menuName,menuDesc,menuURL,
		imageURL,authID,parentID,operatetime,operatorID,displayNo
		from dsum_t_menu where
		parentID IS NULL
		and
		authID in (
		select t1.authID from dsum_t_role_auth t1
		where t1.authType = 1 and t1.roleID in(
		select t0.roleID from dsum_t_account_role t0
		where t0.accountID = #{accountID}
		)
		)
	</select>
</mapper>