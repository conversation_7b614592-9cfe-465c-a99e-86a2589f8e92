var app = angular.module("myApp", ["util.ajax", "page", "top.menu", "angularI18n", "cy.uploadify", "service.common"])
app.controller("hotLineController", function ($scope, $rootScope, $timeout, $location, RestClientUtil, CommonUtils) {
  $scope.init = function () {
    $scope.isSuperManager = false;
    $scope.isShowAdd = false;
    $scope.isShowEdit = false;
    $scope.operatorID = $.cookie('accountID');
    $scope.loginRoleType=$.cookie('loginRoleType');
    $scope.isSuperManager = ($scope.loginRoleType=='superrManager'||$scope.loginRoleType=='normalMangager');
    $scope.isProvincial = ($scope.loginRoleType=='provincial');

    $scope.enterpriseID = $.cookie('enterpriseID') || '';
    //获取enterpriseName
    $scope.enterpriseName = $.cookie('enterpriseName')||'';
    $scope.choseIndex = 5;
    $scope.isZYZQ = $.cookie('reserved10') == "111";
    if ($scope.isSuperManager)
    {
    	var proSupServerType = $.cookie('proSupServerType');
        $scope.proSupServerType = $.cookie('proSupServerType');
        if (proSupServerType)
        {
            var value = JSON.parse(proSupServerType);
            for (var i = 0; i < value.length; i++) {
	            var index = value[i];
	            if (!$scope.isZYZQ && index == 30)
	            {
	            	$scope.choseIndex = i;
	            }
                if ($scope.isZYZQ && index == 76)
                {
                  $scope.choseIndex = i;
                }
            }
        }
    }
    
    $scope.isAllow = true;

    if ($scope.isProvincial && !$scope.isSuperManager)
    {
    	var proSupType = $.cookie('proSupType');
    	$scope.isAllow = false;
    	if (proSupType)
        {
            var value = JSON.parse(proSupType);
            for (var i = 0; i < value.length; i++) {
	            var index = value[i];
	            if (index == 2)
	            {
	            	$scope.isAllow = true;
	            }
            }
        }
    }
    if ($scope.isAllow)
    {
    	// if (!$scope.isSuperManager&&!$scope.enterpriseName) {
    	      var req = {};
    	      req.id = $scope.enterpriseID;
    	      var pageParameter = {};
    	      pageParameter.pageNum = 1;
    	      pageParameter.pageSize = 1000;
    	      pageParameter.isReturnTotal = 1;
    	      req.pageParameter = pageParameter;
    	      /*查询企业列表*/
    	      RestClientUtil.ajaxRequest({
    	        type: 'POST',
    	        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
    	        data: JSON.stringify(req),
    	        success: function (data) {
    	          $rootScope.$apply(function () {
    	            var result = data.result;
    	            if (result.resultCode == '1030100000') {
    	              $scope.enterpriseName = data.enterprise.enterpriseName;
                      $scope.enterpriseType = data.enterprise.enterpriseType;
                      $scope.enterpriseReserved10 = data.enterprise.reservedsEcpmp.reserved10;
                      $scope.isZYZQ = $scope.enterpriseReserved10 == "111";
                    } else {
    	              $scope.tip = result.resultCode;
    	              $('#myModal').modal();
    	            }
    	          })
    	        },
    	        error: function () {
    	          $rootScope.$apply(function () {
    	            $scope.tip = '**********';
    	            $('#myModal').modal();
    	          })
    	        }
    	      });
    	// }
    	    $scope.uploadParam = {
    	      enterpriseId: $scope.enterpriseID || '',
    	      fileUse: 'hotlineLicense'
    	    };
    	    //初始化分页信息
    	    $scope.pageInfo = [
    	      {
    	        "totalPage": 1,
    	        "totalCount": 0,
    	        "pageSize": '10',
    	        "currentPage": 1
    	      },
    	      {
    	        "totalPage": 1,
    	        "totalCount": 0,
    	        "pageSize": '10',
    	        "currentPage": 1
    	      }
    	    ];
    	    $scope.statusMap = {
    	      1: "主叫屏显",
    	      2: "被叫屏显",
    	      3: "主被叫彩印",
    	      4: "挂机短信",
    	      8: "挂机彩信"
    	    };
    	    $scope.approveStatusMap = {
    	      "1": "审核失败",
    	      "2": "待审核",
    	      "3": "审核通过",
    	      "4": "审核驳回"
    	    };
    	    $scope.queryHotInfoList();
    }
    else
    {
        $('#bussinessNo').modal();
    }
    
  };

  $scope.gotoHotLineContent=function(){
    location.href="../hotLineContentManage/hotlineContentManage.html"
  }
  $scope.gotoUssd=function(){
    location.href="../ussdAccessSettings/ussdAccessSettings.html"
  }
  $scope.isShowHotPic = true;
  $scope.hotContentInfoListData = [];
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
  $scope.hotMsisdnExist = false;
  $scope.hotMsisdnVali = true;
  $scope.addHotlineInfo = {
    "id": "",
    "enterpriseID": "",
    "hotlineNo": "",
    "hotlinePicUrl": "",
    "auditStatus": "",
    "auditOpinion": "",
    "operatorID": ""
  };
  $scope.updateHotlineInfo = {
    "id": "",
    "enterpriseID": "",
    "hotlineNo": "",
    "hotlinePicUrl": "",
    "auditStatus": "",
    "auditOpinion": "",
    "operatorID": ""
  };
  //img
  $scope.filePicker = "filePicker";
  $scope.accepttype = "jpg,png";
  $scope.isValidate = false;
  $scope.filesize = 5;
  $scope.mimetypes = ".jpg,.png";

  $scope.isCreateThumbnail = true;
  $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
  $scope.uploadDesc = "格式为jpg、png，图片大小不可大于5M";
  $scope.numlimit = 1;
  $scope.fileUrl = "";

  $scope.$on("uploadifyidHotLine", function (event, fileUrl) {
    if (fileUrl != "") {
      $scope.fileUrl = fileUrl;
      $(".add-table button").unbind("click").click(function () {
        $('.cancel').trigger('click');
      })
    }
  });
  $scope.$on("uploadifyid1", function (event, fileUrl) {
    $scope.fileUrl = fileUrl;
  });

  $scope.closeAdd = function () {
    $scope.isShowAdd = false;
    $("body").removeClass("modal-open")
  }

  $scope.closeEdit = function () {
    $scope.isShowEdit = false;
    $("body").removeClass("modal-open")
  }

  //新增热线弹窗
  $scope.addHot = function () {
    $scope.hotMsisdnDesc = '';
    $scope.hotMsisdnVali = true;
    $scope.errorInfo = "";
    $scope.addHotlineInfo.hotlineNo = "";
    $scope.fileUrl = "";
    $scope.isShowAdd = true;
    $timeout(function () {
      $("#addHotLine").modal();
    }, 500);
  };
  //新增热线
  $scope.createHotline = function () {
	if($scope.addHotlineInfo.hotlineNo.length>14)
	{
		$scope.tip = '号码必填，仅支持输入1-14位数字';
        $('#myModal').modal();
        return;
	}
    var req = {
      "hotlineList": [
        {
          "enterpriseID": $scope.enterpriseID,
          "hotlineNo": $scope.addHotlineInfo.hotlineNo,
          "hotlinePicUrl": $scope.fileUrl,
          "auditStatus": $scope.addHotlineInfo.auditStatus || "",
          "auditOpinion": $scope.addHotlineInfo.auditOpinion || "",
          "operatorID": $scope.operatorID,
        }
      ],
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/addHotline",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(data);
          if (data.resultCode == '1030100000') {
            if(result.failedHotlineList&&result.failedHotlineList.length>0){
              $scope.tip = '1030120029';
              $('#myModal').modal();
            }else{
              $scope.isShowAdd = false;
              $("body").removeClass("modal-open");
              $scope.queryHotInfoList();
            }
            
          } else {
            $scope.isShowAdd = false;
            $("body").removeClass("modal-open")
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function (err) {
        $rootScope.$apply(function () {
          $scope.isShowAdd = false;
          $("body").removeClass("modal-open")
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };
  //编辑热线弹窗
  $scope.updateHot = function (item) {
    if(item.hotlinePicUrl != ""){
      $scope.urlList = [CommonUtils.formatPic(item.hotlinePicUrl).review];
      $scope.fileUrl = item.hotlinePicUrl;
    };
    $scope.hotMsisdnDesc = '';
    $scope.hotMsisdnVali = true;
    $scope.addHotlineInfo = {};
    $scope.addHotlineInfo.id = item.id;
    $scope.addHotlineInfo.hotlineNo = item.hotlineNo;
    $scope.addHotlineInfo.enterpriseID = item.enterpriseID;
    $scope.addHotlineInfo.auditStatus = item.auditStatus;
    $scope.addHotlineInfo.auditOpinion = item.auditOpinion;
    $scope.isShowEdit = true;
    $timeout(function () {
      $rootScope.$apply(function () {
        $("#updateHotLine").modal();
      })
    }, 500);
  };
  // 编辑热线
  $scope.updateHotline = function () {
    var req = {
      "hotline": {
        "id": $scope.addHotlineInfo.id,
        "hotlineNo": $scope.addHotlineInfo.hotlineNo,
        "hotlinePicUrl": $scope.fileUrl,
        "operatorID": $scope.operatorID,
      },
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/updateHotline",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.isShowEdit = false;
            $("body").removeClass("modal-open")
            $scope.queryHotInfoList();
          } else {
            $scope.isShowEdit = false;
            $("body").removeClass("modal-open")
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
          $scope.fileUrl="";
        })
      },
      error: function (err) {
        $rootScope.$apply(function () {
          $scope.isShowEdit = false;
          $("body").removeClass("modal-open")
          $scope.tip = '**********';
          $('#myModal').modal();
          $scope.fileUrl="";
        })
      }
    })
  }
  //删除热线弹窗
  $scope.delHot = function (item) {
    $scope.selectedItemDel = item;
    $("#deleteHotLine").modal();
  };
  // 删除热线
  $scope.delHotline = function () {
    var item = $scope.selectedItemDel;
    var hotline ={
           "enterpriseID":item.enterpriseID,
           "hotlineNo":item.hotlineNo
    }
    var req = {
       "hotlines": [hotline],
      "hotlineNoList": [item.hotlineNo],
      "operatorID":$scope.operatorID
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/deleteHotline",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $('#delHotlineCancel').click();
            $scope.queryHotInfoList();
          } else {
            $('#delHotlineCancel').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function (err) {
        $rootScope.$apply(function () {
          $('#delHotlineCancel').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  $scope.picturePop = function (item, con) {
    $scope.pictureUrl = "";
    $("#picUrlListPop").modal();
    if (con == "hotlineLicense") {
      var index = item.hotlinePicUrl.indexOf("=");
      var strR = item.hotlinePicUrl.substring(index + 1, item.hotlinePicUrl.length);
      $scope.pictureUrl = CommonUtils.formatPic(strR).review;
    }
  };
  //内容查询弹窗
  $scope.selHot = function (item) {
    $scope.queryHot(item);
    $("#selectHotLine").modal();
  };
  // 内容查询
  $scope.queryHot = function (item, condition) {
    $scope.contentInfoList = [];
    $scope.selectedItem = item;
    $scope.content_msisdn = item.hotlineNo;
    if (condition != 'justPage') {
      var req = {
        "contentName": $scope.content || "",
        "contentTypeList":[1,2],
        "enterpriseID": parseInt($scope.enterpriseID),
        "hotlineNo": item.hotlineNo || '',
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[1].pageSize),
          "isReturnTotal": "1",
        }
      };
      $scope.pageInfo[1].currentPage = 1;
      $scope.queryContentInfoListTemp = angular.copy(req);
    } else {
      var req = $scope.queryContentInfoListTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.contentInfoList = result.contentInfoList || [];
            $scope.pageInfo[1].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            var req = $scope.queryContentInfoListTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
            $scope.pageInfo[1].totalCount = 0;
            $scope.pageInfo[1].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };
  //查询热线
  $scope.queryHotInfoList = function (condition) {
    if (condition != 'justPage') {
      var req = {
        "enterpriseID": $scope.enterpriseID,  
        "hotlineNo": $scope.hotlineNo || '',
        "page": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1",
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryHotInfoListTemp = angular.copy(req);
    } else {
      var req = $scope.queryHotInfoListTemp;
      req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }


    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/hotlineService/queryHotlineList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(result);
          if (data.resultCode == '1030100000') {
            $scope.hotContentInfoListData = result.hotlineList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.hotContentInfoListData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.hotContentInfoListData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
});
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}]);