<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>管理员管理</title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../css/searchList.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<!--分页-->
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
	<!-- <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script> -->

	<script type="text/javascript" src="administratorCtrl.js"></script>
	<style>
		label {
			min-width: 120px;
		}

		.cond-div {
			min-width: 240px;
		}
	</style>
 
</head>

<body ng-app="myApp" ng-controller="administratorController" ng-init="init()" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate">
			</span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'ADMINMANAGER'|translate"></span>
		</div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="contentName" class="col-xs-1 control-label" ng-bind="'ACCOUNT_NAME'|translate"></label>

					<div class="cond-div col-xs-2">
						<input type="text" autocomplete="off" class="form-control" id="contentName"
									 placeholder="{{'PLEASEINPUTACCOUNTNAME'|translate}}" ng-model="accountName">
					</div>

					<label for="characterName" class="col-xs-1 control-label" ng-bind="'CHARACTERNAME'|translate"></label>
					<div class="cond-div col-xs-2">
						<input type="text" autocomplete="off" class="form-control" id="characterName"
									 placeholder="{{'PLEASEINPUTCHARACTERNAME'|translate}}" ng-model="roleName">
					</div>
					<label for="accountStatus" class="col-xs-1 control-label" ng-bind="'ACCOUNT_STATUS'|translate"></label>
					<div class="cond-div col-xs-2">
						<select class="form-control" ng-model="accountStatus" id="accountStatus"
								ng-options="x.id as x.name for x in accountStatusList">
							<option value="">不限</option>
						</select>
					</div>
					<div class="cond-div">
						<button type="submit" class="btn search-btn" ng-click="queryAccountInfoList()" style="margin-left: 20px">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>

			</form>
		</div>
		<div class="add-table">
			<button class="btn add-btn" ng-click="gotoAdd()">
				<icon class="add-iocn"></icon>
				<span ng-bind="'CREATEACCOUNT'|translate"></span>
			</button>
		</div>
	</div>
	<div class="coorPeration-table" style="width:100%;">
		<table class="table table-striped table-hover">
			<thead>
			<tr>
				<th style="width: 115px;" ng-bind="'ACCOUNT_NAME'|translate"></th>
				<th style="width: 85px;">账号类型</th>
				<th style="width: 100px;" ng-bind="'ROLE'|translate"></th>
				<th style="width: 100px;" ng-bind="'OA_ACCOUNT'|translate"></th>
				<th style="width: 90px;" ng-bind="'CONTACT'|translate"></th>
				<th style="width: 110px;" ng-bind="'PHONENUMBER'|translate"></th>
				<th style="width: 130px;" ng-bind="'COMMON_CREATETIME'|translate"></th>
				<th style="width: 90px;" ng-bind="'CREATEACCOUNT'|translate"></th>
				<th style="width: 90px;" ng-bind="'ACCOUNT_STATUS'|translate"></th>
				<th style="width: 250px;" ng-bind="'COMMON_OPERATE'|translate"></th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="item in accountInfoList">
				<td><span title="{{item.accountName}}">{{item.accountName}}</span></td>
				<td><span >{{item.managerRightType==1?'应用账号':'4A账号'}}</span></td>
				<td><span title="{{item.roleName}}">{{item.roleName}}</span></td>
				<td><span title="{{item.oaAccount}}">{{item.oaAccount}}</span></td>
				<td><span title="{{item.fullName}}">{{item.fullName}}</span></td>
				<td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
				<td><span title="{{item.createTime|formatDate}}">{{item.createTime|formatDate}}</span></td>
				<td><span title="{{item.operatorAccountName}}">{{item.operatorAccountName}}</span></td>
				<td><span title="{{ getAccountStatus(item.accountStatus)}}">{{getAccountStatus(item.accountStatus)}}</span></td>
				<td class="coorPeration-table-a">
					<div class="handle">
						<ul>
							<li class="delete" ng-click="deleteAccount(item)" ng-show="item.accountID!==1000">
								<icon class="delete-icon"></icon>
								<span ng-bind="'COMMON_DELETE'|translate"></span>
							</li>
							<li class="edit" ng-click="gotoUpdate(item)" ng-show="item.accountID!==1000">
								<icon class="edit-icon"></icon>
								<span ng-bind="'GROUP_EDIT'|translate"></span>
							</li>
							<li class="query" ng-click="gotoSelect(item)">
								<icon class="query-icon" style="background-position: -72px;"></icon>
								<span style="color: #7360e1;" ng-bind="'COMMON_WATCH'|translate"></span>
							</li>
							<li class="query" ng-show="item.accountStatus == 2"  ng-click="handleUpdate(item)">
								<span style="color: #7360e1;" ng-bind="'ACTIVATE_ACTION'|translate"></span>
							</li>
						</ul>
					</div>
				</td>
			</tr>
			<tr ng-show="accountInfoList===null||accountInfoList.length===0">
				<td style="text-align:center" colspan="7">暂无数据</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div>
		<ptl-page tableId="0" change="queryAccountInfoList('justPage')"></ptl-page>
	</div>
	<!-- 删除弹框 -->
	<div class="modal fade" id="deleteAccount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width:450px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabelDel">确认删除</h4>
				</div>
				<div class="modal-body">
					<p style="text-align:center;margin-top:30px;font-size:16px">请确认是否删除该管理员账号?</p>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn" ng-click="delAccount()"
									>删除
					</button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
									id="delAccountCancel" style="margin-left: 20px">返回
					</button>
				</div>
			</div>
		</div>
	</div>
	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
			 style="z-index:555555;">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838;text-align: center'>
							{{tip|translate}}
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
</body>
</html>