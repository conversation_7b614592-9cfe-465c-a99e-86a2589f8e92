<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ContentFrameMapper">
    <resultMap id="contentFrameMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentFrameWrapper">       
        <result property="id" column="id" javaType="java.lang.Long" />
        <result property="cyContID" column="cyContID" javaType="java.lang.Long" />
        <result property="frameNo" column="frameNo" javaType="java.lang.Integer" />
        <result property="frameType" column="frameType" javaType="java.lang.Integer" />
        <result property="frameTxt" column="frameTxt" javaType="java.lang.String" />
        <result property="framePicName" column="framePicName" javaType="java.lang.String" />
        <result property="framePicUrl" column="framePicUrl" javaType="java.lang.String" />
        <result property="framePicSize" column="framePicSize" javaType="java.lang.Integer" />        
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
    </resultMap>
    
    <sql id="contentFrmaeColumn">
    	ID,
        cyContID,
        frameNo,
        frameType,
        frameTxt,
        framePicName,
        framePicUrl,
        framePicSize,
        createTime,
        updateTime,
        operatorID
    </sql>
    
    <insert id="batchInsertContentFrame">
        INSERT INTO ecpm_t_content_frame 
        (
        	<include refid="contentFrmaeColumn" />
        )
        VALUES 
        <foreach collection="list" item="contentFrameWrapper"
            separator=",">
        (
        nextval('ecpm_seq_contentframe'),
        #{contentFrameWrapper.cyContID},
        #{contentFrameWrapper.frameNo},
        #{contentFrameWrapper.frameType},
        #{contentFrameWrapper.frameTxt},
        #{contentFrameWrapper.framePicName},
        #{contentFrameWrapper.framePicUrl},
        #{contentFrameWrapper.framePicSize},
        #{contentFrameWrapper.createTime},
        #{contentFrameWrapper.updateTime},
        #{contentFrameWrapper.operatorID}
        )
        </foreach>
    </insert>
    
    <select id="queryContentFrameByConID" resultMap="contentFrameMap">
    	select <include refid="contentFrmaeColumn" /> from ecpm_t_content_frame where cyContID=#{id}
    </select>
    
    <delete id="deleteContentFrameByCyConID">
    	delete from ecpm_t_content_frame where cyContID=#{cyContID}
    </delete>
</mapper>