<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.BatchsubTaskMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.BatchsubTaskWrapper" >
        <result column="ID" property="id" jdbcType="VARCHAR" />
        <result column="dealStatus" property="dealStatus" jdbcType="INTEGER" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, createTime, updateTime, dealStatus
    </sql>

	<insert id="insert">
		insert into ecpm_t_h5_batchsub_task
		(
		ID,
		createTime,
		updateTime,
		dealStatus
		)
		values
		(
		#{id},
		#{createTime},
		#{updateTime},
		#{dealStatus}
		)
	</insert>
	
	 <delete id="delete" parameterType="java.lang.String">
		delete from
		ecpm_t_h5_batchsub_task where ID=#{id}
	</delete>
	<select id="query" resultMap="BaseResultMap">
		SELECT
        ID, createTime, updateTime, dealStatus
        FROM ecpm_t_h5_batchsub_task t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null">
                and t.ID = #{id}
            </if>
            <if test="dealStatus !=null">
                and t.dealStatus = #{dealStatus}
            </if>
            <if test="createTime !=null">
                and t.createTime &lt;= #{createTime}
            </if>
        </trim>
        order by createTime desc
	</select>
	
	 <update id="batchUpdateStatus">
        update 
        	ecpm_t_h5_batchsub_task
        set 
        	dealStatus = #{dealStatus},
        	updateTime = now()
        where
        	dealStatus = #{orgDealStatus}
            and createTime &lt;= #{createTime}
    </update>
    
    <update id="updateStatus" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.BatchsubTaskWrapper">
        update 
        	ecpm_t_h5_batchsub_task
        set 
        	dealStatus = #{dealStatus},
        	updateTime = #{updateTime}
        where
        	ID = #{id}
    </update>
    
    <select id="queryCount" resultType="java.lang.Integer">
        select 
        count(1)
        from ecpm_t_h5_batchsub_task
         <trim prefix="where" prefixOverrides="and|or">
         	<if test="taskId != null">
                and ID like "%"#{taskId}"%"
            </if>
			<if test="dealStatus != null and dealStatus.size()>0">
				and dealStatus in
				<foreach item="status" index="index" collection="dealStatus"
					open="(" separator="," close=")">
					#{status}
				</foreach>
			</if>
		</trim>
    </select>
    
    <select id="queryAndMember" resultType="com.huawei.jaguar.dsdp.ecpm.model.H5BatchSubTask">
		SELECT a.id,DATE_FORMAT(a.createTime,'%Y%m%d%H%i%s') createTime,a.dealStatus,
		COUNT(1) memberCount,
		COUNT(b.dealStatus=3 OR NULL) successCount,
		COUNT(b.dealStatus>3 OR NULL) faildCount
		FROM ecpm_t_h5_batchsub_task a LEFT JOIN ecpm_t_h5_batchsub_task_member b ON a.id=b.taskID 
		<trim prefix="where" prefixOverrides="and|or">
	    <if test="taskId != null">
	         and a.id like "%"#{taskId}"%"
	    </if>
	    <if test="dealStatus != null and dealStatus.size()>0">
			and a.dealStatus in
			<foreach item="status" index="index" collection="dealStatus"
			open="(" separator="," close=")">
				#{status}
			</foreach>
		</if>
		</trim>     
        GROUP BY a.id
		ORDER BY a.createTime DESC
		<if test="pageNum != null and pageSize != null">
			limit #{pageNum},#{pageSize}
		</if>
	</select>
</mapper>