<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.LargeSegmentMapper">
	<resultMap id="largeSegmentWrapper"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.LargeSegmentWrapper">
		<result property="operators" column="operators" javaType="java.lang.String" />
		<result property="segment" column="segment" javaType="java.lang.String" />
	</resultMap>


	<select id="getLargeSegmentList" resultMap="largeSegmentWrapper">
		select  operators , segment from ecpe_t_large_segment
	</select>

</mapper>
