<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/mian.css" rel="stylesheet"/>
<link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
<link href="../../../../../css/layout.css" rel="stylesheet"/>
<link href="../../../../../css/searchList.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" href="../../../../../css/font-awesome.min.css"/>
<link href="../../../../../css/daterangepicker.min.css" rel="stylesheet">
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>

<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link rel="stylesheet" href="../../../../../css/font-awesome.min.css"/>
<link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<link href="../../../../../css/statistics.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="agentDayStatistics.js"></script>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
	font-weight: normal;
	white-space: nowrap;
}
.daterangepicker td, .daterangepicker th {
	width:auto;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
</style>

</head>
<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head" ng-show="isSuperManager"><span class="frist-tab" ng-bind="'AGENTSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'AGENTDAYSTATISTICS'|translate"></span></div>
		<div class="cooperation-head" ng-show="!isSuperManager"><span class="frist-tab" ng-bind="'DATA_STATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'AGENTDAYSTATISTICS'|translate"></span></div>
		<top:menu chose-index="4" page-url="/qycy/ecpmp/view/InfoStatistics/agentStatistics/groupSendStatistics/" list-index="66" ng-show="isSuperManager"></top:menu>
		<top:menu chose-index="2" page-url="/qycy/ecpmp/view/InfoStatistics/agentStatistics" list-index="48" ng-show="isAgent"></top:menu>
		<top:menu chose-index="0" page-url="/qycy/ecpmp/view/InfoStatistics/agentStatistics/groupSendStatistics"
				  list-index="67"  ng-class="{true:'second-topmenu',false:'second-topmenu'}[isAgent]"></top:menu>
			<form class="form-horizontal">
				<div class="form-group">
					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="isSuperManager"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div" ng-show="isSuperManager">
						<input type="text" autocomplete="off" class="form-control" id="enterpriseName" placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"   ng-model="enterpriseName">
					</div>

					<label class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></label>
					<div class="col-lg-4 col-md-4 col-sm-4 col-xs-4 time">
						<div class="input-daterange input-group" id="datepicker">
							<input type="text" class="input-md form-control" autocomplete="off" id="start" ng-keyup="searchOn()"/>
							<span class="input-group-addon" ng-bind="'TO'|translate"></span>
							<input type="text" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
						</div>
					</div>

					<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
							<button ng-click="queryEnterpriseStatInfo()" type="submit" class="btn search-btn" ng-disabled="initSel.search" ><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
				</div>
			</form>

			<div class="add-table">
				<button id="exportSpokesList" class="btn add-btn" ng-click="exportFile()">
					<icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span>
				</button>
			</div>

			<div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-bind="'DAYSTATISTICSREPORT'|translate"></div>
			<div class="coorPeration-table">
					<table class="table table-striped table-hover">
						<thead>
							<tr>
								<th style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></th>
								<th style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>
								<th style="width:10%" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
								<th style="width:10%" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></th>
								<th style="width:10%" ng-bind="'SEND_AMOUNT'|translate"></th>
								<th style="width:9%" ng-bind="'SEND_SUCCESS'|translate"></th>
								<th style="width:9%" ng-bind="'SEND_FAILED'|translate"></th>
							</tr>
						</thead>
						<tbody>
								<tr ng-repeat="item in StatInfoListData">
									<td><span title="{{item.enterpriseID}}">{{item.enterpriseID}}</span></td>
									<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td>
									<td><span title="{{getServiceType(item.serviceType,item.subServType)}}">{{getServiceType(item.serviceType,item.subServType)}}</span></td>
									<td><span title="{{getTime(item.statMonth)}}">{{getTime(item.statMonth)}}</span></td>
									<td><span title="{{item.enhancedDeliveryCount}}">{{item.enhancedDeliveryCount == null ? 0 : item.enhancedDeliveryCount}}</span></td>
									<td><span title="{{item.useCount}}">{{item.useCount}}</span></td>
									<td><span title="{{item.enhancedDeliveryCount-item.useCount}}">{{item.enhancedDeliveryCount-item.useCount}}</span></td>
								</tr>
								<tr ng-show="StatInfoListData.length<=0">
									<td style="text-align:center" colspan="10" ng-bind="'COMMON_NODATA'|translate"></td>
								</tr>
							</tbody>
					</table>
				</div>

				<div>
					<ptl-page tableId="0" change="queryEnterpriseStatInfo('justPage')"></ptl-page>
				</div>
		</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>