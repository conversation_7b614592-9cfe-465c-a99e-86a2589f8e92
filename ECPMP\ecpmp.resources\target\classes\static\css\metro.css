/*-------------------------------------
zTree Style

version:    3.4
author:     <PERSON>.z
email:      <EMAIL>
website:    http://code.google.com/p/jquerytree/

-------------------------------------*/

.ztree * {
  padding: 0;
  margin: 0;
  font-size: 14px;
  font-family: Verdana, Arial, Helvetica, AppleGothic, sans-serif
}

.ztree {
  margin: 0;
  padding: 5px;
  color: #333
}

.ztree li {
  padding: 0;
  margin: 0;
  list-style: none;
  line-height: 30px;
  text-align: left;
  white-space: nowrap;
  outline: 0
}

.ztree li ul {
  margin: 0;
  padding: 0 0 0 18px
}

.ztree li ul.line {
  background: url(../assets/images/zTree/line_conn.png) 0 0 repeat-y;
}

.ztree li a {
  padding-right: 3px;
  margin: 0;
  cursor: pointer;
  height: 21px;
  color: #333;
  background-color: transparent;
  text-decoration: none;
  display: inline-block
}

.ztree li a:hover {
  text-decoration: underline
}

.ztree li a.curSelectedNode {
  padding-top: 0px;
  background-color: #e5e5e5;
  color: black;
  height: 21px;
  opacity: 0.8;
}

.ztree li a.curSelectedNode_Edit {
  padding-top: 0px;
  background-color: #e5e5e5;
  color: black;
  height: 21px;
  border: 1px #666 solid;
  opacity: 0.8;
}

.ztree li a.tmpTargetNode_inner {
  padding-top: 0px;
  background-color: #aaa;
  color: white;
  height: 21px;
  border: 1px #666 solid;
  opacity: 0.8;
  filter: alpha(opacity=80)
}

.ztree li a.tmpTargetNode_prev {
}

.ztree li a.tmpTargetNode_next {
}

.ztree li a input.rename {
  height: 14px;
  width: 80px;
  padding: 0;
  margin: 0;
  font-size: 12px;
  border: 1px #7EC4CC solid;
  *border: 0px
}

.ztree li span {
  line-height: 21px;
  margin-right: 2px
}

.ztree li span.button {
  line-height: 0;
  margin: 0;
  width: 21px;
  height: 21px;
  display: inline-block;
  vertical-align: middle;
  border: 0 none;
  cursor: pointer;
  outline: none;
  background-color: transparent;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-image: url("../assets/images/zTree/metro.png");
  *background-image: url("../assets/images/zTree/metro.gif")
}

.ztree li span.button.chk {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin-left: 10px;
  margin-right: 5px;
}
/*
.ztree li span.button.chk.checkbox_false_full {
  background-position: -5px -5px;
}
*/

.ztree li span.button.chk.checkbox_false_full {
  background: url(../assets/images/zTree/notcheck.png) no-repeat;
}

.ztree li span.button.chk.checkbox_false_full_focus {
  background: url(../assets/images/zTree/notcheck.png) no-repeat;
}

.ztree li span.button.chk.checkbox_false_part {
  background: url(../assets/images/zTree/checkpart.png) no-repeat;
}

.ztree li span.button.chk.checkbox_false_part_focus {
  background: url(../assets/images/zTree/checkpart.png) no-repeat;
}

.ztree li span.button.chk.checkbox_false_disable {
  background-position: -5px -89px;
}

.ztree li span.button.chk.checkbox_true_full {
  background: url(../assets/images/zTree/check.png) no-repeat;
}

.ztree li span.button.chk.checkbox_true_full_focus {
  background: url(../assets/images/zTree/check.png) no-repeat;
}

.ztree li span.button.chk.checkbox_true_part {
  background: url(../assets/images/zTree/checkpart.png) no-repeat;
}

.ztree li span.button.chk.checkbox_true_part_focus {
  background: url(../assets/images/zTree/checkpart.png) no-repeat;
}

.ztree li span.button.chk.checkbox_true_disable {
  background-position: -26px -89px;
}

.ztree li span.button.chk.radio_false_full {
  background-position: -47px -5px;
}

.ztree li span.button.chk.radio_false_full_focus {
  background-position: -47px -26px;
}

.ztree li span.button.chk.radio_false_part {
  background-position: -47px -47px;
}

.ztree li span.button.chk.radio_false_part_focus {
  background-position: -47px -68px;
}

.ztree li span.button.chk.radio_false_disable {
  background-position: -47px -89px;
}

.ztree li span.button.chk.radio_true_full {
  background-position: -68px -5px;
}

.ztree li span.button.chk.radio_true_full_focus {
  background-position: -68px -26px;
}

.ztree li span.button.chk.radio_true_part {
  background-position: -68px -47px;
}

.ztree li span.button.chk.radio_true_part_focus {
  background-position: -68px -68px;
}

.ztree li span.button.chk.radio_true_disable {
  background-position: -68px -89px;
}

.ztree li span.button.switch {
  width: 16px;
  height: 16px
}

.ztree li span.button.root_open {
  background-position: -92px -54px
}

.ztree li span.button.root_close {
  background-position: -74px -54px
}

.ztree li span.button.roots_open {
  background-position: -105px 0;
}

.ztree li span.button.roots_close {
  background-position: -126px 0;
}

.ztree li span.button.center_open {
  background-position: -105px -21px;
}

.ztree li span.button.center_close {
  background-position: -126px -21px;
}

.ztree li span.button.bottom_open {
  background-position: -105px -42px;
}

.ztree li span.button.bottom_close {
  background-position: -126px -42px;
}

.ztree li span.button.noline_open {
  background-image: url("../assets/images/zTree/down_arrow.png");
  background-size: 16px 16px;
}

.ztree li span.button.noline_close {
  background-image: url("../assets/images/zTree/right_arrow.png");
  background-size: 16px 16px;
}

/*.ztree li span.button.noline_close{background-position: -105px -84px;}*/
.ztree li span.button.root_docu {
  background: none;
}

.ztree li span.button.roots_docu {
  background-position: -84px 0;
}

.ztree li span.button.center_docu {
  background-position: -84px -21px;
}

.ztree li span.button.bottom_docu {
  background-position: -84px -42px;
}

.ztree li span.button.noline_docu {
  background: none;
}

.ztree li span.button.ico_open {
  margin-right: 2px;
  background-position: -147px -21px;
  vertical-align: top;
  *vertical-align: middle
}

.ztree li span.button.ico_close {
  margin-right: 2px;
  margin-right: 2px;
  background-position: -147px 0;
  vertical-align: top;
  *vertical-align: middle
}

.ztree li span.button.ico_docu {
  margin-right: 2px;
  background-position: -147px -42px;
  vertical-align: top;
  *vertical-align: middle
}

.ztree li span.button.edit {
  margin-left: 2px;
  margin-right: -1px;
  background-position: -189px -21px;
  vertical-align: top;
  *vertical-align: middle
}

.ztree li span.button.edit:hover {
  background-position: -168px -21px;
}

.ztree li span.button.remove {
  margin-left: 2px;
  margin-right: -1px;
  background-position: -189px -42px;
  vertical-align: top;
  *vertical-align: middle
}

.ztree li span.button.remove:hover {
  background-position: -168px -42px;
}

.ztree li span.button.add {
  margin-left: 2px;
  margin-right: -1px;
  background-position: -189px 0;
  vertical-align: top;
  *vertical-align: middle
}

.ztree li span.button.add:hover {
  background-position: -168px 0;
}

.ztree li span.button.ico_loading {
  margin-right: 2px;
  background: url(../assets/images/zTree/loading.gif) no-repeat scroll 0 0 transparent;
  vertical-align: top;
  *vertical-align: middle
}

ul.tmpTargetzTree {
  background-color: #FFE6B0;
  opacity: 0.8;
  filter: alpha(opacity=80)
}

span.tmpzTreeMove_arrow {
  width: 16px;
  height: 21px;
  display: inline-block;
  padding: 0;
  margin: 2px 0 0 1px;
  border: 0 none;
  position: absolute;
  background-color: transparent;
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-position: -168px -84px;
  background-image: url("../assets/images/zTree/metro.png");
  *background-image: url("../assets/images/zTree/metro.gif")
}

ul.ztree.zTreeDragUL {
  margin: 0;
  padding: 0;
  position: absolute;
  width: auto;
  height: auto;
  overflow: hidden;
  background-color: #cfcfcf;
  border: 1px #00B83F dotted;
  opacity: 0.8;
  filter: alpha(opacity=80)
}

.zTreeMask {
  z-index: 10000;
  background-color: #cfcfcf;
  opacity: 0.0;
  filter: alpha(opacity=0);
  position: absolute
}
