<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SpokeActivityWinMapper">
	<resultMap id="spokeActivityWinWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.SpokeActivityWinWrapper">
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="activityID" column="activityID" javaType="java.lang.Integer" />
		<result property="isReward" column="isReward" javaType="java.lang.Integer" />
		<result property="screenCount" column="screenCount" javaType="java.lang.Integer" />
		<result property="endPhoneCount" column="endPhoneCount"
			javaType="java.lang.Integer" />
		<result property="callingCount" column="callingCount" javaType="java.lang.Integer" />
		<result property="calledCount" column="calledCount" javaType="java.lang.Integer" />
		<result property="gjdxCount" column="gjdxCount" javaType="java.lang.Integer" />
		<result property="gjcxCount" column="gjcxCount" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="endTime" column="endTime" javaType="java.util.Date" />
		<result property="dayCount" column="dayCount" javaType="java.lang.Integer" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.String" />
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="effectiveTime" column="effectiveTime"
			javaType="java.util.Date" />
		<result property="expireTime" column="expireTime" javaType="java.util.Date" />
		<result property="auditStatus" column="auditStatus" javaType="java.lang.Integer" />
	</resultMap>

	<select id="querySpokeActivityWinList" resultMap="spokeActivityWinWrapper">
		select
		e.ID,
		e.operatorID,
		t.msisdn,
		t.dayCount,
		t.screenCount,
		t.endPhoneCount,
		t.createTime,
		t.endTime,
		t.activityID
		from ecpm_t_spoke_activity_stat t
		left join
		ecpm_t_activity e
		on t.activityID=e.id
		where e.auditStatus=2 and
		t.isReward = 0
		and e.effectiveTime <![CDATA[ <= ]]>
		#{nowTime}
		and e.expiretime <![CDATA[ >= ]]>
		#{yesterday}
	</select>

</mapper>