<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
<link href="../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../css/mian.css" rel="stylesheet"/>
<link href="../../../../css/datepicker3.css" rel="stylesheet"/>
<link href="../../../../css/layout.css" rel="stylesheet"/>
<link href="../../../../css/searchList.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
<link rel="stylesheet" href="../../../../css/font-awesome.min.css"/>
<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../directives/page/page.js"></script>
<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<link href="../../../../css/statistics.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="queryEnterpriseDevStatInfoList.js"></script>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
.table>thead>tr>th, .table>tbody>tr>th, .table>tfoot>tr>th, .table>thead>tr>td, .table>tbody>tr>td, .table>tfoot>tr>td {
	padding: 12px 6px;
}
.clearf:after{
    content:'';
    clear:both;
        height:0;
        display:block;
}
</style>

</head>
<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" style="min-width: 1400px">
	<div class="cooperation-manage">
		<!--<div class="cooperation-head" ng-show="!isProvincial"><span class="frist-tab" ng-bind="'ENTERPRISESTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'DAYSTATISTICS'|translate"></span></div>
		<div class="cooperation-head" ng-show="isProvincial"><span class="frist-tab" ng-bind="'COMMON_INFOSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'DAYSTATISTICS'|translate"></span></div>-->
		<div class="cooperation-head"><span class="frist-tab" ng-bind="'ENTERPRISESTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span
				class="second-tab" ng-bind="'DEVSTATISTICS'|translate"></span></div>
		<top:menu chose-index="6" page-url="/qycy/ecpmp/view/InfoStatistics/enterpriseStatistics" list-index="29" ng-show="!isProvincial"></top:menu>
			<form class="form-horizontal">
				<div class="form-group">

					<!--<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate" ng-show="!isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div" ng-show="!isProvincial">
						<input type="text" autocomplete="off" class="form-control" id="enterpriseName" placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}" ng-model="enterpriseName">
					</div>-->

					
<!-- 
					<label for="province" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'COMMON_PROVINCE'|translate"></label>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" ng-show="isSuperManager">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.provinceName for x in provinceList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2" ng-show="isProvincial || isNormalMangager">
						<select class="form-control" name="province" ng-model="selectedProvince"
										ng-options="x.authName for x in provinceList">
							<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
						</select>
					</div>
					 -->

					<label for="province" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'COMMON_PROVINCE'|translate" ng-show="isSuperManager || isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class1" ng-show="isSuperManager || isProvincial">
							<select class="form-control" name="province" ng-model="selectedProvince" ng-options="x.provinceName for x in provinceList"
							 ng-change="changeSelectedProvincebychaoguan(selectedProvince)">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-show="!isProvincial"></option>
							</select>
					</div>
					<label for="province" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'COMMON_PROVINCE'|translate" ng-show="!isSuperManager && !isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class1" ng-show="!isSuperManager && !isProvincial">
							<select class="form-control" name="province" ng-model="selectedProvince" ng-options="x.authName for x in provinceList"
							 ng-change="changeSelectedProvince(selectedProvince)">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
							</select>
					</div>

					<label for="city" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'CITY'|translate" ng-show="isSuperManager || isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class2" ng-show="isSuperManager || isProvincial">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity" ng-options="x.cityName for x in subCityList"
							 ng-disabled="selectedProvince===null||selectedProvince.provinceID==='000'">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate" ng-show="!isProvincial"></option>
							</select>
					</div>

					<label for="city" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'CITY'|translate" ng-show="!isSuperManager && !isProvincial"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class2" ng-show="!isSuperManager && !isProvincial">
						<select class="form-control" name="city" id="selectedCity" ng-model="selectedCity" ng-options="x.authName for x in subCityList"
							 ng-disabled="selectedProvince===null||selectedProvince.provinceID==='000'">
								<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
							</select>
					</div>

					<label for="provinceType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'ENTERPRISE_TYPE'|translate"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
						<select class="form-control" name="provinceType" ng-model="isZyzq" ng-options="x.id as x.name for x in isZyzqChoise" style="max-width:200px">
						</select>
					</div>

					<div class="clearf"> </div>
					<label for="serviceType" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space: nowrap;" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></label>
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div">
						<select class="form-control" name="serviceType" ng-model="serviceType" ng-options="x.id as x.name for x in serviceTypeChoise" style="max-width:200px">
						</select>
					</div>
					<label class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></label>
					<div class="col-lg-3 col-md-4 col-sm-4 col-xs-4 time">
						<div class="input-daterange input-group" id="datepicker">
							<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="start"/>
							<span class="input-group-addon" ng-bind="'TO'|translate"></span>
							<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="end"/>
						</div>
					</div>

					<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div">
							<button ng-click="queryEnterpriseDevStatInfo()" type="submit" class="btn search-btn" ng-disabled="initSel.search"><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
					<span style="color:red;line-height: 34px;display: block;margin-top: 6px;" ng-show="!checkUnique">
					<img src="../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
					<span>{{uniqueTip}}</span>
				</span>
				</div>
			</form>

			<div class="add-table">
					<button id="exportSpokesList" class="btn add-btn" ng-click="exportFile()"><icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span></button>
			</div>

			<div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-bind="'DEVSTATISTICS1'|translate"></div>
			<div class="coorPeration-table">
					<table class="table table-striped table-hover">
						<thead>
							<tr>
								<th style="width:6%" ng-bind="'COMMON_PROVINCE'|translate"></th>
								<th style="width:6%" ng-bind="'CITY'|translate"></th>
								<th style="width:6%" ng-bind="'ENTERPRISE_TYPE'|translate"></th>
								<th style="width:7%" ng-bind="'OTHER_PROVICE_CONTENTAUDIT_FIRSTTYPE'|translate"></th>
								<th style="width:6%" ng-bind="'ENTERPRISE_NEWADDENTERPRISE'|translate"></th>
								<th style="width:6%" ng-bind="'ENTERPRISE_ARRIVALENTERPRISE'|translate"></th>
								<th style="width:6%" ng-bind="'GROUP_ADDMEMB'|translate"></th>
								<th style="width:6%" ng-bind="'UNSUB_MEMBER_COUNT'|translate"></th>								
								<th style="width:6%" ng-bind="'GROUP_ARRIVALMEMB'|translate"></th>
								<th style="width:14%" ng-bind="'COMMON_OPERATE'|translate"></th>
							</tr>
						</thead>
						<tbody>
								<tr ng-repeat="item in StatInfoListData">
									<td><span title="{{provinceList2[item.provinceID]}}">{{provinceList2[item.provinceID]}}</span></td>
									<td><span title="{{cityList2[item.cityID]}}">{{cityList2[item.cityID]}}</span></td>
									<td><span title="{{getSubProvinceType(item.subProvinceType)}}">{{getSubProvinceType(item.subProvinceType)}}</span></td>
									<td><span title="{{getServiceType(item.serviceType)}}">{{getServiceType(item.serviceType)}}</span></td>
									<td><span title="{{item.newEnterpriseCount}}">{{item.newEnterpriseCount}}</span></td>
									<td><span title="{{item.enterpriseCount}}">{{item.enterpriseCount}}</span></td>
									<td><span title="{{item.newMemberCount}}">{{item.newMemberCount}}</span></td>
									<td><span title="{{item.unsubMemberCount}}">{{item.unsubMemberCount}}</span></td>									
									<td><span title="{{item.memberCount}}">{{item.memberCount}}</span></td>
									<td>
										<div class="handle">
											<ul>
												<li class="query" ng-click="toDetail(item)">
													<icon class="query-icon"></icon>
													<span ng-bind="'OPER_DETAIL1'|translate"></span>
												</li>
											</ul>
										</div>
									</td>
								</tr>
								<tr ng-show="StatInfoListData.length<=0">
									<td style="text-align:center" colspan="13" ng-bind="'COMMON_NODATA'|translate"></td>
								</tr>
							</tbody>
					</table>
				</div>

				<div>
					<ptl-page tableId="0" change="queryEnterpriseDevStatInfo('justPage')"></ptl-page>
				</div>
		</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>