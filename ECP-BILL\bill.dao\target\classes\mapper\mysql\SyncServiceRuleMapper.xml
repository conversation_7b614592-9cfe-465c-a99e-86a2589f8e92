<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper.SyncServiceRuleMapper">
	<resultMap id="syncServiceRuleWrapper"
		type="com.huawei.jaguar.dsdp.bill.dao.domain.SyncServiceRuleWrapper">
		<result property="transactionID" column="transactionID"
			javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="corpID" column="corpID" javaType="java.lang.String" />
		<result property="oprDate" column="oprDate" javaType="java.util.Date" />
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="bossID" column="bossID" javaType="java.lang.String" />
		<result property="effectiveTime" column="effectiveTime"
			javaType="java.util.Date" />
		<result property="expiryTime" column="expiryTime" javaType="java.util.Date" />
		<result property="channel" column="channel" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="chargeMsisdn" column="chargeMsisdn" javaType="java.lang.String" />
		<result property="pushMaxTimes" column="pushMaxTimes" javaType="java.lang.Integer" />
		<result property="memNumber" column="memNumber" javaType="java.lang.Integer" />
		<result property="experienceNum" column="experienceNum"
			javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
		<result property="updateStatusTime" column="updateStatusTime" javaType="java.util.Date" />
	</resultMap>


	<resultMap id="syncServiceRuleRecordWrapper"
			   type="com.huawei.jaguar.dsdp.bill.dao.domain.SyncServiceRuleRecordWrapper">
		<result property="validCount" column="validCount" javaType="java.lang.Integer" />
		<result property="unValidCount" column="unValidCount" javaType="java.lang.Integer" />
	</resultMap>

	<select id="queryServiceRule" resultMap="syncServiceRuleWrapper">
		select transactionID,
		enterpriseID,
		corpID,
		oprDate,
		servType,
		subServType,
		bossID,
		effectiveTime,
		expiryTime,
		channel,
		status,
		chargeMsisdn,
		pushMaxTimes,
		memNumber,
		experienceNum,
		createTime,
		updateTime,
		reserved1,
		reserved4,
		reserved5,
		reserved6,
		reserved10
		from ecpm_t_sync_service_rule
		<trim prefix="where" prefixOverrides="and|or">
			<if test="corpID != null and corpID !=''">
				corpID=#{corpID}
			</if>
			<if test="enterpriseID != null">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="servType != null">
				and servType=#{servType}
			</if>
			<if test="subServType != null">
				and subServType=#{subServType}
			</if>
			<if test="reserved1 != null and reserved1 !=''">
				and reserved1=#{reserved1}
			</if>
			<if test="reserved3 != null and reserved3 !=''">
				and reserved3=#{reserved3}
			</if>
		</trim>
	</select>

	<select id="queryServiceRulesList" resultMap="syncServiceRuleWrapper">
		select transactionID,
		enterpriseID,
		corpID,
		oprDate,
		servType,
		subServType,
		bossID,
		effectiveTime,
		expiryTime,
		channel,
		status,
		chargeMsisdn,
		pushMaxTimes,
		memNumber,
		experienceNum,
		createTime,
		updateTime,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6
		from ecpm_t_sync_service_rule
		<trim prefix="where" prefixOverrides="and|or">
			<if test="corpID != null and corpID !=''">
				corpID=#{corpID}
			</if>
			<if test="enterpriseID != null">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="servTypes != null and servTypes.size()>0">
				and servType in
				<foreach item="servType" index="index" collection="servTypes"
					open="(" separator="," close=")">
					#{servType}
				</foreach>
			</if>
		</trim>
		order by updateTime desc
	</select>

	<select id="queryServiceRulesWithHis" resultMap="syncServiceRuleWrapper">
		select 
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			status,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved2
		from ecpm_t_sync_service_rule
		<trim prefix="where" prefixOverrides="and|or">
			<if test="corpID != null and corpID !=''">
				corpID=#{corpID}
			</if>
			<if test="enterpriseID != null">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="servTypes != null and servTypes.size()>0">
				and servType in
				<foreach item="servType" index="index" collection="servTypes"
					open="(" separator="," close=")">
					#{servType}
				</foreach>
			</if>
		</trim>
		
		union
		
		select 
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			status,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved2
		from ecpm_t_sync_service_rule_his
		<trim prefix="where" prefixOverrides="and|or">
			<if test="corpID != null and corpID !=''">
				corpID=#{corpID}
			</if>
			<if test="enterpriseID != null">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="servTypes != null and servTypes.size()>0">
				and servType in
				<foreach item="servType" index="index" collection="servTypes"
					open="(" separator="," close=")">
					#{servType}
				</foreach>
			</if>
		</trim>
		
		order by updateTime desc
	</select>

	<update id="updateServiceRule">
		update ecpm_t_sync_service_rule set
		<trim suffixOverrides=",">
			<if test="transactionID!=null and transactionID!=''">transactionID= #{transactionID},</if>
			<if test="enterpriseID!=null">enterpriseID= #{enterpriseID},</if>
			<if test="corpID!=null and corpID!=''">corpID= #{corpID},</if>
			<if test="oprDate!=null">oprDate= #{oprDate},</if>
			<if test="status!=null">status= #{status},</if>
			<if test="servType!=null">servType= #{servType},</if>
			<if test="subServType!=null">subServType= #{subServType},</if>
			<if test="bossID!=null and bossID!=''">bossID= #{bossID},</if>
			<if test="effectiveTime!=null">effectiveTime= #{effectiveTime},</if>
			<if test="expiryTime!=null">expiryTime= #{expiryTime},</if>
			<if test="channel !=null and channel !=''">channel= #{channel},</if>
			<if test="chargeMsisdn !=null and chargeMsisdn !=''">chargeMsisdn= #{chargeMsisdn},</if>
			<if test="pushMaxTimes!=null">pushMaxTimes= #{pushMaxTimes},</if>
			<if test="memNumber!=null">memNumber= #{memNumber},</if>
			<if test="experienceNum!=null">experienceNum= #{experienceNum},</if>
			<if test="updateTime!=null">updateTime= #{updateTime},</if>
			<if test="updateStatusTime!=null">updateStatusTime= #{updateStatusTime},</if>
			<if test="reserved4 != null and reserved4 !=''">reserved4= #{reserved4},</if>
			<if test="reserved5 != null and reserved5 !=''">reserved5= #{reserved5},</if>
			<if test="reserved6 != null and reserved6 !=''">reserved6= #{reserved6},</if>

			<if test="reserved1 != null and reserved1 !=''">reserved1= #{reserved1},</if>
			<if test="reserved2 != null and reserved2 !=''">reserved2= #{reserved2}</if>
		</trim>
		where corpID=#{corpID}
		and servType=#{servType}
		and	subServType=#{subServType}
		<if test="reserved3 != null and reserved3 !=''">
			and reserved3=#{reserved3}
		</if>
	</update>

	<insert id="createSyncServiceRule">
		insert into ecpm_t_sync_service_rule
		(
		transactionID,
		enterpriseID,
		corpID,
		oprDate,
		servType,
		subServType,
		bossID,
		effectiveTime,
		expiryTime,
		channel,
		status,
		chargeMsisdn,
		pushMaxTimes,
		memNumber,
		experienceNum,
		createTime,
		updateTime,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6
		)
		values
		(
		#{transactionID},
		#{enterpriseID},
		#{corpID},
		#{oprDate},
		#{servType},
		#{subServType},
		#{bossID},
		#{effectiveTime},
		#{expiryTime},
		#{channel},
		#{status},
		#{chargeMsisdn},
		#{pushMaxTimes},
		#{memNumber},
		#{experienceNum},
		#{createTime},
		#{updateTime},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6}
		)
	</insert>

	<delete id="deleteSyncServiceRule">
		delete from ecpm_t_sync_service_rule
		where
		corpID = #{corpID} and servType = #{servType} and subServType
		= #{subServType}
		<if test="reserved1 != null and reserved1 !=''">
			and reserved1=#{reserved1}
		</if>
		<if test="reserved3 != null and reserved3 !=''">
			and reserved3=#{reserved3}
		</if>
	</delete>
	
	<select id="queryServiceRuleForCont" resultMap="syncServiceRuleWrapper">
		select transactionID,
		enterpriseID,
		corpID,
		oprDate,
		servType,
		subServType,
		bossID,
		effectiveTime,
		expiryTime,
		channel,
		status,
		chargeMsisdn,
		pushMaxTimes,
		memNumber,
		experienceNum,
		createTime,
		updateTime,
		reserved1,
		reserved2
		from ecpm_t_sync_service_rule
		<trim prefix="where" prefixOverrides="and|or">
			<if test="enterpriseID != null">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="servType != null">
				and servType=#{servType}
			</if>
			<if test="subServType != null">
				<if test="subServType==1 || subServType==2">
					and (subServType = #{subServType} or  subServType =3)
				</if>
				<if test="subServType!=1 and subServType!=2">
					and subServType = #{subServType}
				</if>
			</if>
			<if test="reserved1 != null and reserved1 !=''">
				and reserved1=#{reserved1}
			</if>
			<if test="now != null">
				and effectiveTime <![CDATA[ <= ]]>  #{now}
				and expiryTime <![CDATA[ >= ]]> #{now}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
			<if test="mobilePlatforms != null and mobilePlatforms.size()>0">
				and reserved1 in
				<foreach item="reserved1" index="index" collection="mobilePlatforms"
					open="(" separator="," close=")">
					#{reserved1}
				</foreach>
			</if>
		</trim>
	</select>
	
	<select id="queryServiceRuleByStatDate" resultMap="syncServiceRuleWrapper">
		SELECT
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			STATUS,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved10 
		FROM
			ecpm_t_sync_service_rule a 
		WHERE
			a.enterpriseID = #{enterpriseID}
			AND (a.STATUS = 1 OR date_format( a.updateStatusTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate})
			AND date_format( a.effectiveTime, '%Y%m%d' ) <![CDATA[<=]]> #{statDate}
			AND date_format( a.expiryTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate}
			
		UNION
		
		SELECT
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			STATUS,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved10 
		FROM
			ecpm_t_sync_service_rule_his b 
		WHERE
			b.enterpriseID = #{enterpriseID}
			AND date_format( b.cancelTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate}
			AND date_format( b.effectiveTime, '%Y%m%d' ) <![CDATA[<=]]> #{statDate}
			AND date_format( b.expiryTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate}
	</select>
	
	<select id="queryServiceRuleByStatMonth" resultMap="syncServiceRuleWrapper">
		SELECT
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			STATUS,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved10 
		FROM
			ecpm_t_sync_service_rule a 
		WHERE
			a.enterpriseID = #{enterpriseID}
			AND (a.STATUS = 1 OR date_format( a.updateStatusTime, '%Y%m' ) <![CDATA[>=]]> #{statMonth})
			AND date_format( a.effectiveTime, '%Y%m' ) <![CDATA[<=]]> #{statMonth}
			AND date_format( a.expiryTime, '%Y%m' ) <![CDATA[>=]]> #{statMonth}
			
		UNION
		
		SELECT
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			STATUS,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved10 
		FROM
			ecpm_t_sync_service_rule_his b 
		WHERE
			b.enterpriseID = #{enterpriseID}
			AND date_format( b.cancelTime, '%Y%m' ) <![CDATA[>=]]> #{statMonth}
			AND date_format( b.effectiveTime, '%Y%m' ) <![CDATA[<=]]> #{statMonth}
			AND date_format( b.expiryTime, '%Y%m' ) <![CDATA[>=]]> #{statMonth}
	</select>
	
	<select id="queryServiceRuleByStatDateBatch" resultMap="syncServiceRuleWrapper">
		SELECT
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			STATUS,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved10 
		FROM
			ecpm_t_sync_service_rule a 
		WHERE
			a.enterpriseID in 
			<foreach item="enterpriseID" index="index" collection="enterpriseIDs"
					open="(" separator="," close=")">
					#{enterpriseID}
			</foreach>	
			AND (a.STATUS = 1 OR date_format( a.updateStatusTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate})
			AND date_format( a.effectiveTime, '%Y%m%d' ) <![CDATA[<=]]> #{statDate}
			AND date_format( a.expiryTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate}
			
		UNION
		
		SELECT
			transactionID,
			enterpriseID,
			corpID,
			oprDate,
			servType,
			subServType,
			bossID,
			effectiveTime,
			expiryTime,
			channel,
			STATUS,
			chargeMsisdn,
			pushMaxTimes,
			memNumber,
			experienceNum,
			createTime,
			updateTime,
			reserved1,
			reserved10 
		FROM
			ecpm_t_sync_service_rule_his b 
		WHERE
			b.enterpriseID in 
			<foreach item="enterpriseID" index="index" collection="enterpriseIDs"
					open="(" separator="," close=")">
					#{enterpriseID}
			</foreach>
			AND date_format( b.cancelTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate}
			AND date_format( b.effectiveTime, '%Y%m%d' ) <![CDATA[<=]]> #{statDate}
			AND date_format( b.expiryTime, '%Y%m%d' ) <![CDATA[>=]]> #{statDate}
	</select>

	<select id="queryServiceRulesListByNow" resultMap="syncServiceRuleWrapper">
		select transactionID,
		enterpriseID,
		corpID,
		oprDate,
		servType,
		subServType,
		bossID,
		effectiveTime,
		expiryTime,
		channel,
		status,
		chargeMsisdn,
		pushMaxTimes,
		memNumber,
		experienceNum,
		createTime,
		updateTime,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from ecpm_t_sync_service_rule
		<trim prefix="where" prefixOverrides="and|or">
			<if test="prodordSkuNum != null and prodordSkuNum !=''">
				transactionID <![CDATA[<>]]> #{prodordSkuNum}
			</if>
			<if test="enterpriseID != null">
				and enterpriseID = #{enterpriseID}
			</if>
			<if test="now != null">
				and effectiveTime <![CDATA[ <= ]]>  #{now}
				and expiryTime <![CDATA[ >= ]]> #{now}
			</if>
		</trim>
		order by updateTime desc
	</select>


	<select id="queryServiceRuleRecord" resultMap="syncServiceRuleRecordWrapper">
		SELECT
		COUNT(
		(
		expiryTime IS NULL
		OR expiryTime <![CDATA[ > ]]> #{now}
		OR NULL
		)
		) validCount,
		COUNT(expiryTime <![CDATA[ <= ]]> #{now} OR NULL) unValidCount
		FROM
		ecpm_t_sync_service_rule
		WHERE
		enterpriseID = #{enterpriseID}
	</select>

	<select id="queryEcServiceRuleALLCount" resultType="java.lang.Integer">
		SELECT
			count(1)
		FROM
			ecpm_t_sync_service_rule sr
				LEFT JOIN ecpm_t_enterprise_simple es on es.id = sr.enterpriseID
		WHERE
			es.enterpriseType = 5
		  AND (es.reserved10 = "111" or es.reserved10 = "113")
		  AND sr.servType IN ( 1, 5 )
	</select>

	<select id="queryEcServiceRuleALL" resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EcServiceCdrWrapper">
		SELECT
			enterpriseId,enterpriseCode,enterpriseName,es.createDate createTime,servType,sr.reserved5 productCode,sr.reserved6 productNum,sr.oprDate syncTime,
			(CASE WHEN es.reserved10 = "111" THEN 15
				WHEN es.reserved10 = "113" THEN 35
			end) as enterpriseType
		FROM
			ecpm_t_sync_service_rule sr
				LEFT JOIN ecpm_t_enterprise_simple es on es.id = sr.enterpriseID
		WHERE
			es.enterpriseType = 5
		  AND (es.reserved10 = "111" or es.reserved10 = "113")
		  AND sr.servType IN ( 1, 5 )
			limit #{startIndex},#{pageNum}
	</select>
	<select id="queryEcServiceRuleAddCount" resultType="java.lang.Integer">
		SELECT
			count(1)
		FROM
			ecpm_t_ecService_record ec
				LEFT JOIN ecpm_t_enterprise_simple es ON es.id = ec.enterpriseID
		WHERE
			es.enterpriseType = 5
		  AND es.reserved10 = "111"
		  AND ec.servType IN ( 1, 5 )
		  AND ( ec.effStatus IN ( 1, 3 ) OR ec.effStatus IS NULL )
		  and ec.finishTime like concat(#{statDay},"%")
	</select>
	<select id="queryEcServiceRuleAdd" resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EcServiceCdrWrapper">
		SELECT
			enterpriseId,
			enterpriseCode,
			enterpriseName,
			es.createDate createTime,
			servType,
			reqMsg,
			operationSubType,
			ec.updateTime syncTime,
			15 as enterpriseType
		FROM
			ecpm_t_ecService_record ec
				LEFT JOIN ecpm_t_enterprise_simple es ON es.id = ec.enterpriseID
		WHERE
			es.enterpriseType = 5
		  AND es.reserved10 = "111"
		  AND ec.servType IN ( 1, 5 )
		  AND ( ec.effStatus IN ( 1, 3 ) OR ec.effStatus IS NULL )
		  and ec.finishTime like concat(#{statDay},"%")
	</select>

	<select id="querySubEnterpriseAllServiceCount" resultType="java.lang.Integer">
		SELECT
			count(1)
		from
			ecpm_t_enterprise_service_product a
				LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.enterpriseID
		where b.enterpriseType = 3
	</select>
	<select id="querySubEnterpriseAllService"
			resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EcServiceCdrWrapper">
		select
			a.enterpriseID as enterpriseId,
			b.enterpriseCode as enterpriseCode,
			b.enterpriseName as enterpriseName,
			b.createDate as createTime,
			1 as servType,
			a.productID as productCode,
			a.productName as productName,
			(CASE WHEN a.effStatus = 1 THEN 0
				  ELSE
					  a.effStatus
				end) as operationSubType,
			a.createTime as syncTime,
			3 as enterpriseType
		from ecpm_t_enterprise_service_product a
				 LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.enterpriseID
		where b.enterpriseType = 3
			limit #{startIndex},#{pageNum}
	</select>

	<select id="querySubEnterpriseSharedPackgeAllServiceCount" resultType="java.lang.Integer">
		select
			count(1)
		from ecpm_t_sharedPackage_quota a
				 LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.subEnterpriseID
		where b.enterpriseType = 3 and a.type = 2
	</select>

	<select id="querySubEnterpriseSharedPackageAllService"
			resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EcServiceCdrWrapper">
		select
			a.subEnterpriseID as enterpriseId,
			b.enterpriseCode as enterpriseCode,
			b.enterpriseName as enterpriseName,
			b.createDate as createTime,
			date_format(a.updateTime,'%Y%m%d%H%i%s') as updateTime,
			a.quota as productNum,
			1 as servType,
			'10024999' as productCode,
			'行业挂机短信企业共享包' as productName,
			0 as operationSubType,
			a.createTime as syncTime,
			3 as enterpriseType
		from ecpm_t_sharedPackage_quota a
				 LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.subEnterpriseID
		where b.enterpriseType = 3 and a.type = 2
			limit #{startIndex},#{pageNum}
	</select>
	<select id="querySubEnterpriseAddServiceCount" resultType="java.lang.Integer">
		SELECT
			count(1)
		from
			ecpm_t_enterprise_service_product a
				LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.enterpriseID
		where b.enterpriseType = 3 and a.updateTime like concat(#{statDay},"%")
	</select>
	<select id="querySubEnterpriseAddService"
			resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EcServiceCdrWrapper">
		select
			a.enterpriseID as enterpriseId,
			b.enterpriseCode as enterpriseCode,
			b.enterpriseName as enterpriseName,
			b.createDate as createTime,
			1 as servType,
			a.productID as productCode,
			a.productName as productName,
			(CASE WHEN a.createTime = a.updateTime THEN 1
				  ELSE
					  5
				end) as operationSubType,
			a.createTime as syncTime,
			3 as enterpriseType
		from ecpm_t_enterprise_service_product a
				 LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.enterpriseID
		where b.enterpriseType = 3 and a.updateTime like concat(#{statDay},"%")
			limit #{startIndex},#{pageNum}
	</select>

	<select id="querySubEnterpriseSharedPackgeAddServiceCount" resultType="java.lang.Integer">
		select
			count(1)
		from ecpm_t_sharedPackage_quota a
				 LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.subEnterpriseID
		where b.enterpriseType = 3 and a.type = 2 and a.updateTime like concat(#{statDay},"%")
	</select>

	<select id="querySubEnterpriseSharedPackageAddService"
			resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EcServiceCdrWrapper">
		select
			a.subEnterpriseID as enterpriseId,
			b.enterpriseCode as enterpriseCode,
			b.enterpriseName as enterpriseName,
			b.createDate as createTime,
			date_format(a.updateTime,'%Y%m%d%H%i%s') as updateTime,
			a.quota as productNum,
			1 as servType,
			'10024999' as productCode,
			'行业挂机短信企业共享包' as productName,
			(CASE WHEN a.createTime = a.updateTime THEN 1
				  ELSE
					  5
				end) as operationSubType,
			a.createTime as syncTime,
			3 as enterpriseType
		from ecpm_t_sharedPackage_quota a
				 LEFT JOIN ecpm_t_enterprise_simple b on b.id = a.subEnterpriseID
		where b.enterpriseType = 3 and a.type = 2 and a.updateTime like concat(#{statDay},"%")
			limit #{startIndex},#{pageNum}
	</select>
</mapper>