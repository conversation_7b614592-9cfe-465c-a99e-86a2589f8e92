<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
    <title>热线彩印黑名单管理</title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css" />
	<link href="../../../css/reset.css" rel="stylesheet" />
	<link href="../../../css/searchList.css" rel="stylesheet" />

	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-common.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css" />
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
    <script type="text/javascript" src="rxBlacklist.js"></script>
	<!-- 引入菜单组件 -->
	<link href="../../../directives/topMenu/topMenu.css" rel="stylesheet" />
	<script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>
	<!-- 导入文件组件 -->
	<script type="text/javascript" src="../../../frameworkJs/webuploader.js"></script>
	<script src="../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<link href="../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet" />
    <script src="../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
	<link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
    <style>
		.body-min-width{
			min-width: 1100px;
		}
	    /* media for adjustable search-table width  */
	    @media (max-width: 1366px){
  	    .control-label{
	    	width: 86px;
	    	padding-left: 0;
	    	padding-right: 0;
	    	}
	    }
	    #filePicker div:nth-child(2) {
	    	width: 100% !important;
	    	height: 100% !important;
		}
		.cooperation-manage .form-group{
			margin-right: 0;
		}
		.error {
  			border-color: red;
		}
		.ques{
			display: block;
			width: 16px;
			height: 16px;
			background:url(../../../assets/images/ques.png) no-repeat;
			position: absolute;
			top: -8px;
			right: -4px;
			background-size: 100%;
		}
		.tipB{
			position: absolute;
			top: -22px;
			left: 120px;
			background-color: #e5e2fb;
			color: #7462e2;
			padding: 0 5px;
			border-radius: 4px;
		}
	</style>
</head>
<body ng-app="myApp" class="body-min-width">
	<div class="cooperation-manage container-fluid" ng-controller="rxBlacklistCtrl" ng-init="init();" ng-cloak>
	
	    <div class="cooperation-head">
				<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate">
				</span>&nbsp;&gt;&nbsp;<span class="second-tab">热线彩印黑名单管理</span>
	    </div>
	    <div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group" style="padding-left:25px;">
					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap;max-width: 70px;"
					 ng-bind="'BLACKWHITE_NUM'|translate"></label>
					<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
						<input type="text" id="hotlineNo" class="form-control" placeholder="{{'BLACKWHITE_INPUTNUM'|translate}}"
						 ng-model="initSel.hotlineNo">
					</div>
	
					<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
						<button type="submit" class="btn search-btn" ng-click="queryDeliveryNoBlackList()" style="float: right" ng-bind="'COMMON_SEARCH'|translate">
							<icon class="search-iocn"></icon>
						</button>
					</div>
				</div>
			</form>
		</div>
	    <div class="add-table">
	        <button type="submit" class="btn add-btn" ng-click="addDeliveryNoBlackList()" style="position: relative;">
	            <icon class="add-iocn"></icon>
	            <span style="color:#705de1" ng-bind="'BLACKWHITE_ADDBLACK'|translate"></span>
	        </button>
	        <button type="submit" class="btn add-btn" ng-click="impoDeliveryNoBlackList()" style="position: relative;">
	            <icon class="add-iocn"></icon>
	            <span ng-bind="'BLACKWHITE_INPUTALL'|translate"></span>
	        </button>
	        <button type="submit" class="btn add-btn" ng-click="deleteDeliveryNoBlackList()" style="position: relative;"
	        	ng-disabled="selectedListTemp.length==0">
	            <span ng-bind="'GROUP_BATCHDELETE'|translate"></span>
	        </button>
	    </div>
		<p style="font-size: 16px;margin: 12px 25px 12px;" ng-bind="'BLACKWHITE_ALLMISDN'|translate"></p>
	    <div class="coorPeration-table">
	        <table class="table table-striped table-hover">
	            <thead>
	            <tr>
	            	<th style="padding-left:30px;width: 5%;">
	            		<input type="checkbox" ng-model="allChoose" ng-click="ifSelected()">
	            	</th>
	                <th style="width:30%" ng-bind="'COMMON_NUMBER'|translate"></th>
	                <th style="width:35%" ng-bind="'COMMON_CREATETIME'|translate"></th>
	                <th style="width:35%" class="adjustable-width" ng-bind="'COMMON_OPERATE'|translate"></th>
	            </tr>
	            </thead>
	            <tbody>
	            <tr ng-repeat="item in blackWhiteList">
	                <td style="padding-left:30px;width: 5%;"><input type="checkbox" ng-click="changeSelected(item)" ng-model="item.checked"></td>
	                <td><span title="{{item.hotlineNo}}">{{item.hotlineNo}}</span></td>
	                <td ng-bind="item.createTime|formatDate" title="{{item.createTime|formatDate}}"></td>
	                <td>
	                    <div class="handle">
	                        <ul>
	                            <!-- 删除 -->
	                            <li class="delete" ng-click="deleteDeliveryNoBlackList(item)">
	                                <icon class="delete-icon"></icon>
	                                <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
	                            </li>
	                        </ul>
	                    </div>
	                </td>
	            </tr>
	            <tr ng-show="blackWhiteList.length<=0">
	                <td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
	            </tr>
	            </tbody>
	        </table>
	    </div>
	
	    <div style="width: 1000px;">
	        <ptl-page tableId="0" change="queryDeliveryNoBlackList('justPage')"></ptl-page>
	    </div>
		
		<!--添加黑名单弹出框-->
		<div class="modal fade addMenPop" id="addDeliveryNoBlackList" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-mousedown="canclePop($event)"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_ADDBLACK'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal" name="blackListForm">
							<div class="form-group">
								<div class="col-lg-12 col-xs-12 col-sm-12 col-md-12">
									<label for="enterpriseName" class="col-lg-3 col-md-3 col-sm-3 col-xs-3 control-label" style="white-space:nowrap;max-width: 70px;"
									 ng-bind="'BLACKWHITE_NUM'|translate"></label>
									<div class="cond-div col-lg-10 col-md-10 col-sm-10 col-xs-10">
										<input class="form-control" type="text" name="addHotlineNo" 
												ng-model='addDeliveryNoBlackListInfo.hotlineNo' 
												ng-class="{'border-red':!hotlineNoVali}"
		                           				placeholder="{{'NUMBER_PLEASEINPUTHOTLINENUMBER'|translate}}"
		                           				ng-blur="checkHotlineNo()">
										<span style="color:red;line-height: 34px;display: block;" ng-show="!hotlineNoVali">
											<img src="../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
											<span>{{contentDescText}}</span>
										</span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
		                <button type="submit" ng-disabled="(!hotlineNoVali||myForm.$invalid)"
		                        class="btn btn-primary search-btn" ng-bind="'COMMON_SAVE'|translate"
		                        ng-click="beforeCommit()"></button>
						<button type="submit" class="btn btn-back addMemCancel" data-dismiss="modal" 
								aria-label="Close" ng-bind="'COMMON_CANCLE'|translate" 
								ng-mousedown="canclePop($event)" id="addDeliveryNoBlackListCancel"></button>
					</div>
				</div>
			</div>
		</div>
		
		<!--删除弹出框-->
		<div class="modal fade" id="deleteDeliveryNoBlackList" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group">
								<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
									<div class="text-center">
										<span ng-bind="'GROUP_SUREDELETE'|translate"></span>
									</div>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center">
		                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
		                        ng-click="delDeliveryNoBlackList()"></button>
		                <button id="deleteDeliveryNoBlackListCancel" type="submit" class="btn " data-dismiss="modal"
		                        aria-label="Close" ng-bind="'NO'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		
		<!--导入名单弹出框-->
		<div class="modal fade" id="importPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_INPUTALL'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group" style="padding-bottom:0">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;" ng-bind="'COMMON_FILENAME'|translate"></label>
								<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
									<input type="text" class="form-control" ng-model="fileName" id="addGroupName" placeholder="{{'IMPORTXLSXTABLEFILE'|translate}}"
												 style="width: 100%;" ng-disabled="true" />
								</div>
								<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype" uploadifyid="uploadifyid"
																	validate="isValidate" filesize="filesize" mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl"
																	desc="uploadDesc" numlimit="numlimit" urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
								</cy:uploadifyfile>
							</div>
							<div style="color:#ff0000;margin: 0px 0 10px 100px;" ng-show="errorInfo!==''">
								<span class="uplodify-error-img"></span>
								<span ng-bind="errorInfo|translate"></span>
							</div>
							<div class="downloadRow col-sm-10" style="margin: 0 0 0 16px;">
								<a target="_blank" href="/qycy/ecpmp/assets/DeliveryNoBlackListTem.xlsx" class="downMod" style="margin-right: 40px;"
									 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
								<span style="color: #999999; font-size: 12px;" ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center;padding: 30px">
						<button type="submit" class="btn btn-primary search-btn" ng-click="import()" ng-disabled="errorInfo!==''||fileUrl==''" ng-bind="'CONFIRMIMPORT'|translate"></button>
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="addOrgCancel" ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		
		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center">
							<p style='font-size: 16px;color:#383838'>
								{{tip|translate}}
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
	</div>
</body>

</html>