var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n"])

app.controller('defaultFrequencyController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {

        $scope.accountID = $.cookie('accountID');
        const loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');

        $scope.req = {};
        $scope.requestUrl = $scope.getQueryVariable('requestUrl');

        if($scope.getQueryVariable('o')){
            $scope.oString = JSON.parse(decodeURIComponent($scope.getQueryVariable('o')));
            if($scope.oString){
                $scope.req.requestID = $scope.oString.requestID;
            }

        }
        if($scope.req.requestID==null){
            $scope.req.accountID = $scope.accountID;
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/JKService/getLastJKAuthHis",
                data: JSON.stringify($scope.req),
                success: function (data) {
                    $rootScope.$apply(function () {
                        const result = data.result;
                        if (result.resultCode == '**********') {
                            const lastJKAuthHis = data.lastJKAuthHis;
                            $scope.req.requestID = lastJKAuthHis.reuquestID;
                        }
                    })
                }
            })
        }
    };

    $scope.getQueryVariable = function (variable)
    {
        const query = window.location.search.substring(1);
        const vars = query.split("&");
        for (let i = 0; i < vars.length; i++) {
            const pair = vars[i].split("=");
            if (pair[0] == variable) {
                return pair[1];
            }
        }
        return (false);
    };

    $scope.save = function () {
        $scope.req.accountID = $scope.accountID;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/JKService/remoteAuth",
            data: JSON.stringify($scope.req),
            success: function (data) {
                $rootScope.$apply(function () {
                    const result = data.result;
                    if (result.resultCode == '**********') {
                        if("true"==data.authResult){
                            location.href = "../../.."+$scope.requestUrl;
                        }else{
                            $scope.tip = '认证失败，失败原因：';
                            if("error_pwd"===data.failReason){
                                $scope.tip += "短信口令错误，请重新输入"
                            }else if("error_no_request"===data.failReason){
                                $scope.tip += "申请Id不存在"
                            }else if("error_timeout"===data.failReason){
                                $scope.tip += "认证超时"
                            }else if("error_approver"===data.failReason){
                                $scope.tip += "审批人不正确"
                            }else if("error_maxtime"===data.failReason){
                                $scope.tip += "短信口令错误超过3次，申请失败，请重新提交申请"
                            }
                            $('#myModal').modal();
                        }
                    }else {
                        $scope.tip = '系统异常';
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    $scope.reSend = function () {
        $scope.refreshTimes = 30;
        $scope.req.accountID = $scope.accountID;
        $scope.req.requestUrl = $scope.requestUrl;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/JKService/reSendJKPass",
            data: JSON.stringify($scope.req),
            success: function (data) {
                $rootScope.$apply(function () {
                    const result = data.result;
                    if (result.resultCode == '**********') {
                            $scope.tip = '发送成功';
                            $('#myModal').modal();
                    }else {
                        $scope.tip = '失败  ' + result.resultDesc ;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

    $scope.buttonNameTemp = "重新发送";
    $scope.buttonName = "重新发送";
    var t1 = window.setInterval(function () {
        $rootScope.$apply(function () {
            if($scope.refreshTimes>0){
                $scope.refreshTimes =  $scope.refreshTimes - 1 ;
                $scope.buttonName = $scope.buttonNameTemp + "("+$scope.refreshTimes+"s)";
            }else{
                $scope.buttonName = "重新发送";
            }
        });

    },1000);

    $scope.clickModal = function () {
        if( $scope.tip.indexOf("短信口令错误超过3次，申请失败，请重新提交申请")>=0){
            location.href = "../../.."+$scope.requestUrl;
        }
    }

}]);
