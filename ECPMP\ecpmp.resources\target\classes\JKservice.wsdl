<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions targetNamespace="http://ultrapower.com/BussinessSupportService" xmlns:soapenc12="http://www.w3.org/2003/05/soap-encoding" xmlns:tns="http://ultrapower.com/BussinessSupportService" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap11="http://schemas.xmlsoap.org/soap/envelope/" xmlns:wsdlsoap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc11="http://schemas.xmlsoap.org/soap/encoding/" xmlns:soap12="http://www.w3.org/2003/05/soap-envelope">
  <wsdl:types>
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://ultrapower.com/BussinessSupportService">
<xsd:element name="getPolicy4Fort">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in1" nillable="true" type="xsd:string"/>
<xsd:element maxOccurs="1" minOccurs="1" name="in2" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="getPolicy4FortResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="createAppRequest">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="createAppRequestResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="cancelRequest">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="cancelRequestResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryJKStatusByID">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryJKStatusByIDResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="createLoginRequest">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="createLoginRequestResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="createResRequest">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="createResRequestResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="remoteAuth">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="remoteAuthResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryLoginJKStatusForSelfStudy">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryLoginJKStatusForSelfStudyResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryAppOperJKStatus">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryAppOperJKStatusResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryResCmdJKStatus">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryResCmdJKStatusResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="autoAuth">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="autoAuthResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryLoginJKStatus">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="queryLoginJKStatusResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="reSendJKPass">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="reSendJKPassResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="connTest">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="connTestResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="localAuth">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="in0" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
<xsd:element name="localAuthResponse">
<xsd:complexType>
<xsd:sequence>
<xsd:element maxOccurs="1" minOccurs="1" name="out" nillable="true" type="xsd:string"/>
</xsd:sequence>
</xsd:complexType>
</xsd:element>
</xsd:schema>
  </wsdl:types>
  <wsdl:message name="autoAuthRequest">
    <wsdl:part name="parameters" element="tns:autoAuth">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryLoginJKStatusForSelfStudyRequest">
    <wsdl:part name="parameters" element="tns:queryLoginJKStatusForSelfStudy">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getPolicy4FortResponse">
    <wsdl:part name="parameters" element="tns:getPolicy4FortResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryJKStatusByIDResponse">
    <wsdl:part name="parameters" element="tns:queryJKStatusByIDResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="cancelRequestRequest">
    <wsdl:part name="parameters" element="tns:cancelRequest">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryResCmdJKStatusRequest">
    <wsdl:part name="parameters" element="tns:queryResCmdJKStatus">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryAppOperJKStatusResponse">
    <wsdl:part name="parameters" element="tns:queryAppOperJKStatusResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createLoginRequestRequest">
    <wsdl:part name="parameters" element="tns:createLoginRequest">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="localAuthRequest">
    <wsdl:part name="parameters" element="tns:localAuth">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="cancelRequestResponse">
    <wsdl:part name="parameters" element="tns:cancelRequestResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryLoginJKStatusResponse">
    <wsdl:part name="parameters" element="tns:queryLoginJKStatusResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createAppRequestResponse">
    <wsdl:part name="parameters" element="tns:createAppRequestResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="remoteAuthRequest">
    <wsdl:part name="parameters" element="tns:remoteAuth">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createResRequestRequest">
    <wsdl:part name="parameters" element="tns:createResRequest">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryJKStatusByIDRequest">
    <wsdl:part name="parameters" element="tns:queryJKStatusByID">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="localAuthResponse">
    <wsdl:part name="parameters" element="tns:localAuthResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="reSendJKPassResponse">
    <wsdl:part name="parameters" element="tns:reSendJKPassResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryAppOperJKStatusRequest">
    <wsdl:part name="parameters" element="tns:queryAppOperJKStatus">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createResRequestResponse">
    <wsdl:part name="parameters" element="tns:createResRequestResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createLoginRequestResponse">
    <wsdl:part name="parameters" element="tns:createLoginRequestResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="autoAuthResponse">
    <wsdl:part name="parameters" element="tns:autoAuthResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="reSendJKPassRequest">
    <wsdl:part name="parameters" element="tns:reSendJKPass">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryLoginJKStatusForSelfStudyResponse">
    <wsdl:part name="parameters" element="tns:queryLoginJKStatusForSelfStudyResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="createAppRequestRequest">
    <wsdl:part name="parameters" element="tns:createAppRequest">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryResCmdJKStatusResponse">
    <wsdl:part name="parameters" element="tns:queryResCmdJKStatusResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="queryLoginJKStatusRequest">
    <wsdl:part name="parameters" element="tns:queryLoginJKStatus">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="connTestResponse">
    <wsdl:part name="parameters" element="tns:connTestResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="connTestRequest">
    <wsdl:part name="parameters" element="tns:connTest">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="getPolicy4FortRequest">
    <wsdl:part name="parameters" element="tns:getPolicy4Fort">
    </wsdl:part>
  </wsdl:message>
  <wsdl:message name="remoteAuthResponse">
    <wsdl:part name="parameters" element="tns:remoteAuthResponse">
    </wsdl:part>
  </wsdl:message>
  <wsdl:portType name="BussinessSupportServicePortType">
    <wsdl:operation name="getPolicy4Fort">
      <wsdl:input name="getPolicy4FortRequest" message="tns:getPolicy4FortRequest">
    </wsdl:input>
      <wsdl:output name="getPolicy4FortResponse" message="tns:getPolicy4FortResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createAppRequest">
      <wsdl:input name="createAppRequestRequest" message="tns:createAppRequestRequest">
    </wsdl:input>
      <wsdl:output name="createAppRequestResponse" message="tns:createAppRequestResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="cancelRequest">
      <wsdl:input name="cancelRequestRequest" message="tns:cancelRequestRequest">
    </wsdl:input>
      <wsdl:output name="cancelRequestResponse" message="tns:cancelRequestResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryJKStatusByID">
      <wsdl:input name="queryJKStatusByIDRequest" message="tns:queryJKStatusByIDRequest">
    </wsdl:input>
      <wsdl:output name="queryJKStatusByIDResponse" message="tns:queryJKStatusByIDResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createLoginRequest">
      <wsdl:input name="createLoginRequestRequest" message="tns:createLoginRequestRequest">
    </wsdl:input>
      <wsdl:output name="createLoginRequestResponse" message="tns:createLoginRequestResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createResRequest">
      <wsdl:input name="createResRequestRequest" message="tns:createResRequestRequest">
    </wsdl:input>
      <wsdl:output name="createResRequestResponse" message="tns:createResRequestResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="remoteAuth">
      <wsdl:input name="remoteAuthRequest" message="tns:remoteAuthRequest">
    </wsdl:input>
      <wsdl:output name="remoteAuthResponse" message="tns:remoteAuthResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryLoginJKStatusForSelfStudy">
      <wsdl:input name="queryLoginJKStatusForSelfStudyRequest" message="tns:queryLoginJKStatusForSelfStudyRequest">
    </wsdl:input>
      <wsdl:output name="queryLoginJKStatusForSelfStudyResponse" message="tns:queryLoginJKStatusForSelfStudyResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryAppOperJKStatus">
      <wsdl:input name="queryAppOperJKStatusRequest" message="tns:queryAppOperJKStatusRequest">
    </wsdl:input>
      <wsdl:output name="queryAppOperJKStatusResponse" message="tns:queryAppOperJKStatusResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryResCmdJKStatus">
      <wsdl:input name="queryResCmdJKStatusRequest" message="tns:queryResCmdJKStatusRequest">
    </wsdl:input>
      <wsdl:output name="queryResCmdJKStatusResponse" message="tns:queryResCmdJKStatusResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="autoAuth">
      <wsdl:input name="autoAuthRequest" message="tns:autoAuthRequest">
    </wsdl:input>
      <wsdl:output name="autoAuthResponse" message="tns:autoAuthResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryLoginJKStatus">
      <wsdl:input name="queryLoginJKStatusRequest" message="tns:queryLoginJKStatusRequest">
    </wsdl:input>
      <wsdl:output name="queryLoginJKStatusResponse" message="tns:queryLoginJKStatusResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="reSendJKPass">
      <wsdl:input name="reSendJKPassRequest" message="tns:reSendJKPassRequest">
    </wsdl:input>
      <wsdl:output name="reSendJKPassResponse" message="tns:reSendJKPassResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="connTest">
      <wsdl:input name="connTestRequest" message="tns:connTestRequest">
    </wsdl:input>
      <wsdl:output name="connTestResponse" message="tns:connTestResponse">
    </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="localAuth">
      <wsdl:input name="localAuthRequest" message="tns:localAuthRequest">
    </wsdl:input>
      <wsdl:output name="localAuthResponse" message="tns:localAuthResponse">
    </wsdl:output>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BussinessSupportServiceHttpBinding" type="tns:BussinessSupportServicePortType">
    <wsdlsoap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="getPolicy4Fort">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="getPolicy4FortRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="getPolicy4FortResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createAppRequest">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="createAppRequestRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createAppRequestResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="cancelRequest">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="cancelRequestRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="cancelRequestResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryJKStatusByID">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="queryJKStatusByIDRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="queryJKStatusByIDResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createLoginRequest">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="createLoginRequestRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createLoginRequestResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="createResRequest">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="createResRequestRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="createResRequestResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="remoteAuth">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="remoteAuthRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="remoteAuthResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryLoginJKStatusForSelfStudy">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="queryLoginJKStatusForSelfStudyRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="queryLoginJKStatusForSelfStudyResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryAppOperJKStatus">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="queryAppOperJKStatusRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="queryAppOperJKStatusResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryResCmdJKStatus">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="queryResCmdJKStatusRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="queryResCmdJKStatusResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="autoAuth">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="autoAuthRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="autoAuthResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="queryLoginJKStatus">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="queryLoginJKStatusRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="queryLoginJKStatusResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="reSendJKPass">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="reSendJKPassRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="reSendJKPassResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="connTest">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="connTestRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="connTestResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="localAuth">
      <wsdlsoap:operation soapAction=""/>
      <wsdl:input name="localAuthRequest">
        <wsdlsoap:body use="literal"/>
      </wsdl:input>
      <wsdl:output name="localAuthResponse">
        <wsdlsoap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="BussinessSupportService">
    <wsdl:port name="BussinessSupportServiceHttpPort" binding="tns:BussinessSupportServiceHttpBinding">
      <wsdlsoap:address location="http://************:8080/JKService/webservices/BussinessSupportService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
