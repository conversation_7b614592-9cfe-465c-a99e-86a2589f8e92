<!DOCTYPE html>
<html>

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">

	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<!-- <base href="/" /> -->
	<title>addTemplate</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
	<link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css" />
	<link href="../../../../../css/daterangepicker.min.css" rel="stylesheet" type="text/css" />
	<link href="../../../../../css/reset.css" rel="stylesheet" />
	<link href="../../../../../css/addContent.css" rel="stylesheet" />
	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/daterangepicker/daterangepicker.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
	<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css" />
	<script type="text/javascript" src="../../../../../directives/preview/preview.js"></script>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
	<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet" />
	<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
	<script src="../../../../../directives/cy-icon-content/cy-icon-content.js"></script>
	<link href="../../../../../directives/cy-icon-content/cy-icon-content.css" rel="stylesheet"/>
	<script type="text/javascript" src="addTemplate.js"></script>
	<style>
		[ng-cloak]{
            display: none!important;
		}
		.cursor-def{
			cursor: default!important;
		}
		button.preview {
			float: left;
			margin-left: 30px;
		}
		.ng-dirty.ng-invalid {
			border-color: red;
		}
		.form-group {
    		margin-bottom: 20px;
		}
		.form-group label{
			text-align: right;
		}
		.form-group.pushObj .pageChoose{
			display: none;
		}
		.form-group.pushObj .toPage{
			display: none;
		}
		#groupListPop .pageChoose{
			display: none;
		}
		#groupListPop .toPage{
			display: none;
		}
    </style>
</head>

<body ng-app='myApp' ng-controller='addPrintController' ng-init="init();">
<div style="min-width: 1024px;" class="order-manage" ng-cloak>
	<div class="cooperation-head">
		<span ng-show="!isSuperManager" class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>
		<span ng-show="isSuperManager" class="frist-tab" ng-bind="'ENTERPRISE_SECONDLEVEL_MANAGE'|translate"></span>&nbsp;&gt;&nbsp;
		<span class="second-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
		<span ng-show="operateType=='add'" class="second-tab" ng-bind="'TEMPLATE_ADD'|translate"></span>
		<span ng-show="operateType=='modify'" class="second-tab" ng-bind="'TEMPLATE_MODIFY'|translate"></span>
		<span ng-show="operateType=='detail'" class="second-tab" ng-bind="'TEMPLATE_DETAIL'|translate"></span>
	</div>
	<!-- 管理员登陆查看代理商二级企业 or 代理商自己登陆 or 二级企业自己登录-->
	<top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
			  list-index="11"
			  ng-if="(isSuperManager && enterpriseType =='3') || loginRoleType=='agent' || isSubEnterpirse"></top:menu>
	<div class="cooper-messsage">
		<div class="cooper-tab">
			<form class="form-horizontal" name="myForm" novalidate>
				<!-- 模板内容 -->
				<div class="form-group">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label" ng-show="initPrintInfo.subServType!='3'">
						<icon>*</icon><span ng-bind="'TEMPLATE_CONTENT'|translate"></span>：
					</label>
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label" ng-show="initPrintInfo.subServType=='3'">
						<span ng-bind="'TEMPLATE_CALLER_CONTENT'|translate"></span>：
					</label>
					<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
						<select ng-show="operateType == 'add'" class="form-control" ng-model="colorContent" ng-options=" x.content for x in zjChoiceList"
								ng-change=changeColorChoiceType()></select>
						<p ng-show="operateType !='add'" ng-bind="initPrintInfo.colorContent"></p>
					</div>
					<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4" ng-show="myForm.colorContent.$viewValue && myForm.colorContent.$valid && sensitiveWords[1].length>0">
						<p style="padding:10px 20px 10px 20px;color:red;">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
							{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[1]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}
						</p>
					</div>
				</div>
				<div class="form-group">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<span ng-bind="'TEMPLATE_IMPORT_V'|translate"></span>：
					</label>

					<div class="col-lg-5 col-xs-5  col-sm-5 col-md-5" ng-if="operateType!='detail'">
						<div style="width: 596px;">
							<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6" style="padding-left:0">
								<input type="text" class="form-control" ng-model="fileNameCaller" id="addGroupName"
									   placeholder="请导入.xlsx表格格式文件" style="width: 100%;" ng-disabled="true" />
							</div>
							<cy:uploadifyfile filelistid="fileListCaller" filepickerid="filePickerCaller" accepttype="accepttype"
											  uploadifyid="uploadifyidCaller" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
											  formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
											  urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
							</cy:uploadifyfile>
						</div>
						<div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 92px;" ng-show="errorInfoCaller==''"></div>

						<div class="downloadRow col-sm-10" style="width:100%">
							<a target="_blank" href="/qycy/ecpmp/assets/importTemplateVar.xlsx" class="downMod" style="margin-right: 40px;"
							   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
							<span style="color: #705de1 !important; font-size: 12px;">提示：</span><span style="color: #705de1 !important; font-size: 12px;" ng-bind="'TEMPLATE_IMPORTTIP'|translate"></span>


						</div>
					</div>
					<div  class="col-lg-1 col-xs-1  col-sm-1 col-md-1">
						<button class="btn btn-back" ng-bind="'TEMPLATE_DELETE'|translate" ng-click="deleteTemplate(1)"></button>
					</div>
					<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
						<div style="color:#ff0000;" ng-show="errorInfoCaller!=''">
							<span class="uplodify-error-img"></span>
							<span ng-bind="errorInfoCaller|translate"></span>
						</div>
					</div>
				</div>
				<!--	被叫模板内容-->
				<div class="form-group" ng-show="initPrintInfo.subServType=='3'">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<span ng-bind="'TEMPLATE_CALLED_CONTENT'|translate"></span>：
					</label>
					<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
						<select ng-show="operateType == 'add' " class="form-control" ng-model="bjContent" ng-options=" x.content for x in bjChoiceList"
								ng-change=changeBjChoiceType() ></select>
						<p ng-show="operateType !='add'" ng-bind="initPrintInfo.calledContent"></p>
					</div>
					<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4" ng-show="myForm.calledContent.$viewValue && myForm.calledContent.$valid && sensitiveWords[2].length>0">
						<p style="padding:10px 20px 10px 20px;color:red;">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
							{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[2]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}
						</p>
					</div>
				</div>
				<div class="form-group" ng-show="initPrintInfo.subServType=='3'">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<span ng-bind="'TEMPLATE_IMPORT_V'|translate"></span>：
					</label>

					<div class="col-lg-5 col-xs-5  col-sm-5 col-md-5" ng-if="operateType!='detail'">
						<div style="width: 596px;">
							<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6" style="padding-left:0">
								<input type="text" class="form-control" ng-model="fileNameCalled" id="addGroupName"
									   placeholder="请导入.xlsx表格格式文件" style="width: 100%;" ng-disabled="true" />
							</div>
							<cy:uploadifyfile filelistid="fileListCalled" filepickerid="filePickerCalled" accepttype="accepttype"
											  uploadifyid="uploadifyidCalled" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
											  formdata="uploadParam" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
											  urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
							</cy:uploadifyfile>
						</div>
						<div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 92px;" ng-show="errorInfoCalled==''"></div>

						<div class="downloadRow col-sm-10" style="width:100%">
							<a target="_blank" href="/qycy/ecpmp/assets/importTemplateVar.xlsx" class="downMod" style="margin-right: 40px;"
							   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
							<span style="color: #705de1 !important; font-size: 12px;">提示：</span><span style="color: #705de1 !important; font-size: 12px;" ng-bind="'TEMPLATE_IMPORTTIP'|translate"></span>
						</div>
					</div>
					<div  class="col-lg-1 col-xs-1  col-sm-1 col-md-1">
						<button class="btn btn-back" ng-bind="'TEMPLATE_DELETE'|translate" ng-click="deleteTemplate(2)"></button>
					</div>
					<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
						<div style="color:#ff0000;" ng-show="errorInfoCalled!=''">
							<span class="uplodify-error-img"></span>
							<span ng-bind="errorInfoCalled|translate"></span>
						</div>
					</div>
				</div>
				<!-- 投递方式 -->
				<div class="form-group">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<icon>*</icon><span ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></span>：
					</label>
					<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
						<select ng-show="operateType!='detail'" class="form-control" ng-model="initPrintInfo.subServType" ng-options="x.id as x.name for x in subServTypeChoise"
								ng-change=changeSubServerType() ng-disabled="operateType!='add'"></select>
						<p ng-show="operateType=='detail'" ng-bind="statusMap[initPrintInfo.subServType]"></p>
					</div>
					<div ng-show="initPrintInfo.subServType == 1 || initPrintInfo.subServType == 2 || initPrintInfo.subServType == 3">
						<cy:icon:content  content-type="{{contentType}}">
						</cy:icon:content>
					</div>
				</div>

				<!-- 黑白名单 -->
				<div class="form-group black-white">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"><!-- {{'BLACKWHITE_LIST'|translate}}： -->
						<icon>*</icon><span ng-bind="'BLACKWHITE_LIST'|translate"></span>：
					</label>
					<div class="col-lg-4 col-xs-5  col-sm-5 col-md-5 use-name">
						<li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li" ng-click="changeBlackwhiteListType()"><span class="check-btn redio-btn checked"> </span> {{'COMMON_NO_USE'|translate}}</li>
						<li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li" ng-click="changeBlackwhiteListType()"><span class="check-btn redio-btn" style="position:relative"> </span> {{'COMMON_USE_BLACK'|translate}}
							<div ng-show="(initPrintInfo.blackwhiteListType == 1) && (showBlack == false) && (operateType !='detail')" style="position: absolute;top: 20px;color:red;">{{'NO_USE_BLACK'|translate}}</div>
						</li>
						<li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li" ng-click="changeBlackwhiteListType()"><span class="check-btn redio-btn" style="position:relative"> </span> {{'COMMON_USE_WHITE'|translate}}
							<div ng-show="(initPrintInfo.blackwhiteListType == 2) && (showWhite == false) && (operateType !='detail')" style="position: absolute;top: 20px;color:red;">{{'NO_USE_WHITE'|translate}}</div>
						</li>
					</div>
				</div>

				<!-- 推送日期 -->
				<div class="form-group pushDate">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<icon>*</icon><span ng-bind="'CONTENT_PUSH_DATE'|translate"></span>：
					</label>
					<div class="col-lg-10 col-xs-9  col-sm-10 col-md-10">
						<li class="check-li islimit1"><span class="check-btn checked-btn checked"> </span>
							{{'ENTERPRISE_NOLIMITED'|translate}}</li>
						<li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_MONDAY'|translate}}</li>
						<li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_TUESDAY'|translate}}</li>
						<li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_WEDNESDAY'|translate}}</li>
						<li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_THURSDAY'|translate}}</li>
						<li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_FRIDAY'|translate}}</li>
						<li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_SATURDAY'|translate}}</li>
						<li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_SUNDAY'|translate}}</li>
					</div>
				</div>

				<!-- 运营商 -->
				<div class="form-group platforms" ng-show="!inArray(initPrintInfo.subServType)">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<icon>*</icon><span ng-bind="'PLATFORM'|translate"></span>：
					</label>
					<div class="col-lg-10 col-xs-9  col-sm-10 col-md-10" ng-click="checkSignatureRequired()" >
						<li class="check-li">
							<span class="check-btn checked-btn"> </span>
							{{'MOBILE'|translate}}
						</li>
						<li class="check-li">
							<span class="check-btn checked-btn"> </span>
							{{'UNICOM'|translate}}
						</li>
						<li class="check-li" >
							<span class="check-btn checked-btn" > </span>
							{{'TELECOM'|translate}}
						</li>
						<cy:icon:content content-type="5">
						</cy:icon:content>
					</div>
				</div>
				<!-- 场景描述 -->
				<div class="form-group platforms"
					 ng-show=" (initPrintInfo.subServType == '4'
					 || initPrintInfo.subServType == '4.1'
					 || initPrintInfo.subServType == '4.2'
					 || initPrintInfo.subServType == '4.3')
					 && signatureRequired =='1'">
					<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
						<span style="color:red"
							  ng-bind="'*'|translate">
						</span>
						<span ng-bind="'SENCE_DESC'|translate"></span>：
					</label>
					<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
									<textarea style="height:80px;"
											  placeholder="请输入场景描述1~128字"
											  class="form-control"
											  rows="3" name="sceneDesc" ng-model="initPrintInfo.sceneDesc"
											  ng-maxlength="128"
											  ng-required="(initPrintInfo.subServType == '4'
												 || initPrintInfo.subServType == '4.1'
												 || initPrintInfo.subServType == '4.2'
												 || initPrintInfo.subServType == '4.3')
												 && signatureRequired =='1'"
											  ng-blur="sensitiveCheck(initPrintInfo.sceneDesc,1)"
											  ng-disabled="operateType=='detail'">
									</textarea>
						<span style="color:red"
							  ng-show="myForm.sceneDesc.$dirty && myForm.sceneDesc.$invalid">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
											 align="absmiddle">
										<span ng-show="myForm.sceneDesc.$error.required&&(initPrintInfo.subServType == '4'
											 || initPrintInfo.subServType == '4.1'
											 || initPrintInfo.subServType == '4.2'
											 || initPrintInfo.subServType == '4.3')"
											  ng-bind="'SCENEDESC_REQUIRE'|translate"></span>
										<span ng-show="myForm.sceneDesc.$error.maxlength"
											  ng-bind="'SCENEDESC_WORDSLIMIT_256'|translate"></span>
									</span>
					</div>
				</div>
				<!--REQ-113 -->
				<!-- 签名 -->
				<div class="form-group platforms">
					<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span style="color:red"
								  ng-bind="'*'|translate"
								  ng-model="signatureRequired"
								  ng-show="signatureRequired =='1'&& noSign == '1'">
							</span>
						<span ng-bind="'SIGNATURE'|translate"></span>：</label>
					<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
						<input type="text" class="form-control" placeholder="请输入1~67字" rows="1" name="signature"
							   ng-model="initPrintInfo.signature"
							   ng-required="signatureRequired==='1'&& noSign == '1'"
							   maxlength="67"
							   ng-disabled="operateType=='detail'"/>
						<span style="color:red" ng-show="myForm.signature.$invalid&&myForm.signature.$dirty">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
							<span style="color:red" ng-show="myForm.signature.$error.required" ng-bind="'SIGNATURE_REQUIRED'|translate"></span>
							<span style="color:red" ng-show="myForm.signature.$error.pattern&&!myForm.signature.$error.maxlength" ng-bind="'SIGNATURE_PATTERN_ERROR'|translate"></span>
							<span style="color:red" ng-show="myForm.signature.$error.maxlength" ng-bind="'SIGNATURE_MAXLENTH_67'|translate"></span>
						</span>
					</div>
					<cy:icon:content content-type="4">
					</cy:icon:content>
				</div>
				<!-- 所属行业 -->
				<div class="form-group industry">
					<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
						<span ng-bind="'ENTERPRISE_INDUSTRY'|translate"></span>：</label>
					<div class="col-lg-3 col-xs-4 col-sm-4 col-md-4">
						<select class="form-control"
								name="industry1"
								required
								ng-model="selectedIndustry"
								ng-options="x.industryName for x in industryList"
								ng-change="changeIsSensitive(selectedIndustry)"
								ng-show="operateType =='add'||operateType =='detail'||operateType =='modify'"
								ng-disabled="operateType =='detail'"
						>
							<option value="" ng-bind="" ng-show="operateType !='detail'"></option>
						</select>
						<span ng-if="isSensitiveIndustry=='1'&& operateType !='detail'" style="color:#c3c3c3" class="redFont">{{selectedIndustry.industryName}}必须上传营业执照</span>
						<span ng-if="isSensitiveIndustry=='2'&& operateType !='detail'" style="color:#c3c3c3" class="redFont">{{selectedIndustry.industryName}}必须上传授权函或业务协议</span>
						<span ng-if="isSensitiveIndustry=='3'&& operateType !='detail'" style="color:#c3c3c3" class="redFont">{{selectedIndustry.industryName}}必须上传营业执照和版号证明</span>
						<span style="color:red" class="uplodify-error-img" ng-show="selectedIndustryErrorInfo"></span>
						<span  style="color:red" class="redFont" ng-bind="selectedIndustryErrorInfo|translate" ng-show="selectedIndustryErrorInfo"></span>
					</div>
				</div>

				<!-- 其他资质 -->
				<div class="form-group">
					<div style="margin:0px" class="row" ng-cloak>
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<icon ng-show="isSensitiveIndustry =='2' || isSensitiveIndustry =='3'">*</icon><span ng-bind="'ENTERPRISE_CERTIFICATEURLLIST'|translate"></span>：
						</label>
						<div style="display: table;" class="col-lg-6 col-xs-7 col-sm-7 col-md-7">
							<div class="ctn-pic-list" ng-repeat="item in colorContentAndFileList" style="height:50px">
								<div class="pic-wrapper" ng-if="item.frameFileUrl">
									<!--                                <img style="float:left;max-width: 250px;border: 1px solid darkgrey;"-->
									<!--                                     ng-src="{{item.formatFrameFileUrl}}" alt="">-->
									<a ng-click="item.frameFileUrl?exportFile(item.frameFileUrl):false"
									   title="{{item.filename}}" style="display: inline-block;width: 285px;{{operateType!='detail' ? 'overflow: hidden;' :''}};white-space: nowrap;text-overflow: ellipsis;"
									   ng-style="" class="ng-binding">
										{{item.filename}}</a>
									<button ng-hide="operateType=='detail'" ng-click="deleteCtnOrFile($index)"
											type="submit" class="btn btn-primary search-btn "
											style="position: absolute;left: 290px;">
										<span ng-bind="'COMMON_DELETE'|translate"></span></button>
								</div>
							</div>
							<div ng-show="operateType!='detail'" style="margin-left: 0;padding-left: 0;" ng-required="isSensitiveIndustry=='2' || isSensitiveIndustry =='3'"
								 class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview">
								<cy:uploadify ng-show="fileLength<6" ng-if="operateType !='detail'"
											  filelistid="fileList2" filepickerid="filePicker_certi" accepttype="accepttype2"
											  uploadifyid="uploadifyid2" validate="isValidate2" filesize="filesize2"
											  mimetypes="mimetypes2" formdata="uploadParam2" uploadurl="uploadurl2" desc="uploadCertiDesc"
											  numlimit="numlimit2" createthumbnail="isCreateThumbnail2" style="float: left;">
								</cy:uploadify>
								<!-- 图片最大张数时展示的上传图片框(只用于展示，没有作用) -->
								<button ng-required="isSensitiveIndustry=='2' || isSensitiveIndustry =='3'" disabled style="float:left;margin-right: 15px;"
										ng-show="fileLength>5" type="submit"
										class="btn btn-primary search-btn">
									<span style="float:left" ng-bind="'上传文件'|translate"></span></button>
								<br>
								<div style="color: #c3c3c3;" ng-show="fileLength>5" class="ng-binding">
									最多支持6张图片，仅支持jpg，bmp，png，jpeg格式
								</div>
								<div class="downloadRow col-sm-12">
									<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】号码使用声明书.docx" style="margin-left: -15px;"
									   ng-bind="'NUMBER_USE_TEMP_DOWNLOD'|translate"></a>
								</div>
								<!--									<div class="downloadRow col-sm-12">-->
								<!--										<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】品牌归属声明书.docx" style="margin-left: -15px;"-->
								<!--											 ng-bind="'BRAND_ATTR_TEMP_DOWNLOD'|translate"></a>-->
								<!--									</div>-->
								<div class="downloadRow col-sm-12">
									<a target="_blank" href="/qycy/ecpmp/assets/签名授权书-移动模板.docx" style="margin-left: -15px;"
									   ng-bind="'SIGN_AUTH_TEMP_DOWNLOD'|translate"></a>
								</div>
							</div>
							<cy:icon:content content-type="6">
							</cy:icon:content>
						</div>
					</div>
				</div>

				<!-- 营业执照 -->
				<div class="form-group industry">

					<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span style="color:red"
									  ng-bind="'*'|translate"
									  ng-model="isSensitiveIndustry"
									  ng-show="isSensitiveIndustry =='1' || isSensitiveIndustry =='3'">
								</span>
						<span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span>：</label>

					<cy:uploadify filelistid="fileList_" filepickerid="filePicker_" accepttype="accepttype_"
								  uploadifyid="uploadifyid_1" validate="isValidate_" filesize="filesize_" mimetypes_="mimetypes_"
								  formdata="uploadParam_"  uploadurl="uploadurl_" desc="uploadDesc_" numlimit="numlimit_"
								  urllist="urlList_" createthumbnail="isCreateThumbnail_" namelistid="nameList_"
								  class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-show="operateType !='detail'">
					</cy:uploadify>
					<input class="form-control" name="businessLicenseURL_" ng-model="businessLicenseURL_" ng-required="isSensitiveIndustry=='1' || isSensitiveIndustry =='3'" ng-hide="true">
					<img ng-src="{{fileUrl_}}" width="100" height="100" align="absmiddle"
						 ng-if="operateType=='detail'&&fileUrl_" style="margin-left:15px">


				</div>
				<div class="form-group">
					<div ng-show="urlList_2.length>0"
						 class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2
								col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview">
						<ptl-preview style="float:left;margin-left:-5px;" urllist='urlList_2'></ptl-preview>
					</div>
				</div>

				<!-- 推送时间 -->
				<div class="form-group">
					<div>
						<label style="height:34px;padding-top:0px;" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
							<icon>*</icon><span ng-bind="'CONTENT_PUSH_TIME'|translate"></span>：
						</label>
						<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 time">
							<input onfocus="this.blur()" autocomplete="off" type="text" id="time-config" name="time" ng-model="time" class="form-control"
								   ng-disabled="operateType=='detail'" required>
							<i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
							<span style="color:red" ng-show="(myForm.time.$dirty && myForm.time.$invalid)||timeError">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
									<span ng-show="myForm.time.$error.required" ng-bind="'CONTENT_PUSHTIMEREQUIRE'|translate"></span>
									<span ng-show="timeError" ng-bind="'CONTENT_TIMEERRORTIP'|translate"></span>
								</span>
						</div>
					</div>
				</div>

				<!-- 推送对象 -->
				<div class="form-group pushObj">
					<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
						<icon>*</icon><span ng-bind="'CONTENT_PUSHOBJ'|translate"></span>：
					</label>

					<button type="submit" ng-if="operateType!='detail'&&OrganizationListTotal>50"
							class="btn btn-primary search-btn ng-scope"
							ng-click="groupListPop()">
						<span class="ng-binding">添加配置对象</span></button>

					<!--分组小于50个时-->
					<div ng-if="OrganizationListTotal <= 50" class="col-lg-10 col-xs-9  col-sm-10 col-md-10">
						<li class="check-li islimit2" >
							<span class="check-btn checked-btn checked"> </span>
							{{'ENTERPRISE_NOLIMITED'|translate}}
						</li>
						<li class="check-li" ng-repeat="item in orgList" title="{{item.orgName}}({{item.memberCount||0}})人" on-repeat-finished-render>
								<span class="check-btn checked-btn" title="{{item.orgName}}({{item.memberCount||0}})人" >
								</span>{{item.orgName}}({{item.memberCount||0}})人
						</li>
					</div>

					<!--分组大于于50个时-->
					<div ng-if="OrganizationListTotal > 50" class="col-lg-10 col-xs-9  col-sm-10 col-md-10" style="width: 842px;margin-left: 138px;">
						<li style="margin: 10px;width: 190px;line-height: 20px" class="check-li-other islimit2" ng-if="isNoLimit">
							</span>{{'ENTERPRISE_NOLIMITED'|translate}}
						</li>
						<div ng-show="checkOrganizationList.length<=50">
							<li style="float: left;margin: 10px;height: 26px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;width: 140px;line-height: 20px"
								class="check-li-checked" ng-if="!isNoLimit"
								ng-repeat="item in checkOrganizationList" title="{{item.orgName}}({{item.memberCount||0}})人"
								on-repeat-finished-render>
		                           <span title="{{item.orgName}}({{item.memberCount||0}})人">{{item.orgName}}({{item.memberCount||0}})人
                            </span>
							</li>
						</div>

						<div ng-show="checkOrganizationList.length>50 && !isNoLimit" >
							<li style="float: left;margin: 10px;height: 26px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;width: 140px;line-height: 20px"
								class="check-li-checked"
								ng-repeat="item in checkOrganizationListTemp" title="{{item.orgName}}({{item.memberCount||0}})人"
								on-repeat-finished-render title="{{item.orgName}}({{item.memberCount||0}})人">
								<span title="{{item.orgName}}({{item.memberCount||0}})人"></span>{{item.orgName}}({{item.memberCount||0}})人

							</li>
							<ptl-page style="width: 772px" tableId="1" change="queryCheckedOrg (pageInfo)"></ptl-page>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
	<div class="order-btn row">
		<div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
			 style="padding-left: 30px;">
			<!--110迭代：myForm.$invalid 取消||isSensitive[0]||isSensitive[1]||isSensitive[2]-->
			<button type="submit" ng-if="operateType!='detail'" class="btn btn-primary search-btn"
					ng-disabled="(checkCallContentSign == 1)||(checkContentSign == 1)
					||myForm.$invalid||timeError||(colorContentAndFileList.length==0 && (isSensitiveIndustry=='2' || isSensitiveIndustry =='3'))||
					orgList.length <= 0 || !chosePushObj || ((initPrintInfo.blackwhiteListType == 1) && (showBlack == false))
					|| ((initPrintInfo.blackwhiteListType == 2) && (showWhite == false))
					|| pushObjArrTemp.indexOf('1') == -1
					|| (OrganizationListTotal > 50 && checkOrganizationList.length<=0)
                    || (platforms == '000')"
					ng-click="diffNetAuthMaterialsConfirm()"><span ng-bind="'PUBLIC_NUMBER_SUBMIT'|translate"></span></button>
			<button type="submit" class="btn btn-back" ng-click="goBack()"><span ng-bind="'COMMON_BACK'|translate"></span></button>
		</div>
	</div>
</div>

<div class="modal fade" id="templateSubmitPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
			</div>
			<div class="modal-body">
				<form class="form-horizontal">
					<div class="form-group">
						<div class="row" style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
							<div class="text-center">
								<span ng-bind="'TEMPLATE_SUBMIT_COMMON'|translate"></span>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer">
				<button type="submit" class="btn btn-primary search-btn" data-dismiss="modal" aria-label="Close" ng-click="queryMemberCntByChgType()" ng-bind="'COMMON_OK'|translate"></button>
				<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="closeCancel" ng-bind="'COMMON_BACK'|translate"></button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="width: 300px !important;">
	<div class="modal-dialog modal-sm model-lg model-md" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
			</div>
			<div class="modal-body">
				<div class="text-center">
					<p ng-show="tip" style='font-size: 16px;color:#383838'>
						{{tip|translate}}
					</p>
					<p ng-show="noRepeat" style='font-size: 16px;color:#383838;word-break: break-all;'>
						<!-- 你选择的分组xxx已经在其他内容或者模板中关联了，请重新选择 -->
						<!-- {{tip|translate}} -->
						{{'CONTENT_CHOSENGROUP'|translate}}{{failOrgName}}{{'CONTENT_TEMPLATE_HASRELATED'|translate}}
					</p>
				</div>
			</div>
			<div class="modal-footer" style="text-align:center">
				<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
			</div>
		</div>
	</div>
</div>

<!--分组弹出框-->
<div class="modal fade" id="groupListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div role="document" class="modal-dialog dialog-1000" style="width: 760px;">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
						aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" ng-bind="'COMMON_GROUPMANAGE'|translate"></h4>
			</div>
			<div class="modal-body">
				<div class="form-horizontal">
					<div class="form-group">
						<div class="row">
							<div class="col-lg-5 col-xs-6 col-sm-6 col-md-5" style=" width: 30%;">
								<input type="text" class="form-control"
									   placeholder="{{'GROUP_NAME_CONTENT'|translate}}"
									   ng-model="groupName">
							</div>
							<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
								<button class="btn bg_purple search-btn btn1" ng-click="queryOrg(pageInfo)">
									<span class="icon btnIcon search"></span>
									<span ng-bind="'COMMON_SEARCH'|translate"></span>
								</button>
							</div>
							<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
								<button class="btn bg_purple search-btn btn1" ng-click="submitSelect()">
									<span class="icon btnIcon search"></span>
									<span ng-bind="'COMMON_OK'|translate"></span>
								</button>
							</div>
							<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
								<button class="btn bg_purple search-btn btn1" ng-click="groupSelectAll()">
									<span class="icon btnIcon search"></span>
									<span ng-bind="'COMMON_SELECT_ALL'|translate"></span>
								</button>
							</div>
							<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
								<button class="btn bg_purple search-btn btn1" ng-click="resetGroupSelect()">
									<span class="icon btnIcon search"></span>
									<span ng-bind="'COMMON_RESET'|translate"></span>
								</button>
							</div>
							<span style="color: red">清空所有分页选项</span>
						</div>
					</div>
				</div>
			</div>

			<div class="coorPeration-table" style="max-height: 530px;overflow: auto">
				<div class="pushObj max">
					<li style="margin: 10px;width: 130px" class="check-li">
                        <span style="float: left;margin-right: 6px;width: 20px;height: 20px; "class="check-btn checked-btn  isLimit" value="all">
							</span>
						<div style="width: 97px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">{{'ENTERPRISE_NOLIMITED'|translate}}</div>
					</li>

					<li style="float: left;margin: 10px;height: 26px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;width: 130px;" class="check-li group-info" ng-repeat="item in orgList" on-repeat-finished-render title="{{item.orgName}}({{item.memberCount||0}})人">
                        <span style="float: left;margin-right: 6px;width: 20px;height: 20px;" class="check-btn checked-btn" value="{{item.id}}" title="{{item.orgName}}({{item.memberCount||0}})人">

                    </span>
						<div style="float: left;width: 97px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">{{item.orgName}}({{item.memberCount||0}})人</div>
					</li>
				</div>
			</div>

			<div>
				<ptl-page tableId="0" change="queryOrg (pageInfo, 'justPage')"></ptl-page>
			</div>
			<div class="modal-footer">
			</div>
		</div>
	</div>
</div>

<!--提交异网授权材料二次确认弹出框-->
<div class="modal fade" id="diffNetAuthMaterials" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
			</div>
			<div class="modal-body">
				<form class="form-horizontal">
					<div class="form-group">
						<div class="row" style="width: 400px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
							<div class="text-center">
								<span ng-bind="'DIFFNET_AUTH_MATERIALS'|translate"></span>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer" style="text-align:center">
				<button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_UPLOADED'|translate"
						ng-click="diffNetAuthMaterialsUploaded()"></button>
				<button type="submit" id="diffNetAuthMaterialsCancel" class="btn" data-dismiss="modal"
						aria-label="Close" ng-bind="'COMMON_NOT_UPLOAD'|translate"></button>
			</div>
		</div>
	</div>
</div>

</body>
</html>