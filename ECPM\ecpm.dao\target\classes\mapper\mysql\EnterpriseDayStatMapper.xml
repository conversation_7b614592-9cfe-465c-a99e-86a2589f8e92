<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EnterpriseDayStatMapper">
	<resultMap id="enterpriseDayStatWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EnterpriseDayStatWrapper">
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="enterpriseType" column="enterpriseType"
			javaType="java.lang.Integer" />
		<result property="parentEnterpriseID" column="parentEnterpriseID"
			javaType="java.lang.Integer" />
		<result property="provinceID" column="provinceID" javaType="java.lang.String" />
		<result property="cityID" column="cityID" javaType="java.lang.String" />
		<result property="statDate" column="statDate" javaType="java.lang.String" />
		<result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="chargeType" column="chargeType" javaType="java.lang.Integer" />
		<result property="memberCount" column="memberCount" javaType="java.lang.Integer" />
		<result property="deliveryMemberCount" column="deliveryMemberCount" 
			javaType="java.lang.Integer" />
		<result property="useCount" column="useCount" javaType="java.lang.Long" />
		<result property="experienceCount" column="experienceCount"
			javaType="java.lang.Long" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="useCountMobile" column="useCountMobile" javaType="java.lang.Long" />
		<result property="useCountUnicom" column="useCountUnicom" javaType="java.lang.Long" />
		<result property="useCountTelecom" column="useCountTelecom" javaType="java.lang.Long" />
		<result property="enhancedDeliveryCount" column="enhancedDeliveryCount" javaType="java.lang.Long" />
		<result property="enterpriseCount" column="enterpriseCount" javaType="java.lang.Long" />
		<result property="subProvinceType" column="subProvinceType"/>
		<result property="useCountMobileMq" column="useCountMobileMq" javaType="java.lang.Long" />
		<result property="hangupType" column="hangupType" javaType="java.lang.Integer" />
	</resultMap>



	<!-- 以企业为维度查询企业统计信息 -->
	<select id="queryEnterpriseDayStat" resultMap="enterpriseDayStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		ussdCount,
		flashCount,
		enhancedDeliveryCount,
		subProvinceType,
		hangupType
		from ecpm_t_enterprise_day_stat_collect
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>

			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
						 open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator=","
							 close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator=","
							 close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator=","
							 close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="serviceType ==null ">
				and serviceType != '4'
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
		</trim>
		order by statDate DESC,enterpriseID DESC,serviceType DESC,subServType DESC,chargeType DESC
		limit #{pageNum},#{pageSize}
	</select>

	<!-- 以企业为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseDayStatCount" resultType="java.lang.Integer">
		SELECT
		COUNT(0) FROM (
		SELECT serviceType
		FROM ecpm_t_enterprise_day_stat_collect
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>

			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
						 open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				and enterpriseID in ( SELECT
				e.id
				FROM
				ecpm_t_enterprise_simple e
				LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
				where
				((
				e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 5
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (e.provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
						or e.provinceID is null
					</if>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (e.cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					<if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
						or e.cityID is null
					</if>

					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (e.countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					<if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
						or e.countyID is null
					</if>
					)
				</if>
				)
				)
				)
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="serviceType ==null ">
				and serviceType != '4'
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim> ) a
	</select>
	
<!-- 以省为维度查询企业统计信息 -->
	<select id="queryEnterpriseDayStatByProvince" resultMap="enterpriseDayStatWrapper">
		SELECT statDate,serviceType,subServType,provinceID,subProvinceType,
		count(*) enterpriseCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		sum(memberCount) memberCount,
		hangupType
		FROM ( SELECT a.*,b.memberCount FROM (
		select
		provinceID,
		statDate,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		SUM(useCountMobile) AS useCountMobile,
		SUM(useCountUnicom) AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="enterpriseDataAuthList"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>

			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
		</trim>
		group by statDate, serviceType, subServType,provinceID,enterpriseType,subProvinceType,hangupType
		order by statDate desc,serviceType desc,subServType desc,provinceID desc,enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize} ) a
		left JOIN ecpm_t_enterprise_day_stat_service b
		on b.provinceID = a.provinceID and b.statDate = a.statDate and a.serviceType = b.serviceType and a.subProvinceType = b.subProvinceType
		<trim prefix="where" prefixOverrides="and|or">
		<if test="provinceIDs != null and provinceIDs.size()>0">
			and b.provinceID in
			<foreach item="provinceID" index="index" collection="provinceIDs"
					 open="(" separator="," close=")">
				#{provinceID}
			</foreach>
		</if>
		<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
			and b.provinceID in
			<foreach item="provinceID" index="index" collection="enterpriseDataAuthList"
					 open="(" separator="," close=")">
				#{provinceID}
			</foreach>
		</if>
		<if test="cityIDs != null and cityIDs.size()>0">
			and b.cityID in
			<foreach item="cityID" index="index" collection="cityIDs"
					 open="(" separator="," close=")">
				#{cityID}
			</foreach>
		</if>
		<if test="startDate != null and endDate != null">
			and (b.statDate <![CDATA[ >= ]]> #{startDate}
			and b.statDate <![CDATA[ <= ]]> #{endDate})
		</if>
		<if test="startDate != null and endDate == null">
			and b.statDate <![CDATA[ >= ]]> #{startDate}
		</if>
		<if test="startDate == null and endDate != null">
			and b.statDate <![CDATA[ <= ]]> #{endDate}
		</if>
		<if test="subProvinceType !=null ">
			and b.subProvinceType=#{subProvinceType}
		</if>
		</trim>
		) a
		GROUP BY statDate,serviceType,provinceID,subServType,subProvinceType,deliveryMemberCount,
		useCount,
		experienceCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		hangupType
	</select>
	
	<!-- 以省为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseDayStatCountByProvince" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	statDate
		 from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="enterpriseDataAuthList"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by statDate, serviceType, subServType, provinceID,enterpriseType,subProvinceType) t
	</select>
	
	<!-- 以市为维度查询企业统计信息 -->
	<select id="queryEnterpriseDayStatByCity" resultMap="enterpriseDayStatWrapper">
		 SELECT statDate,serviceType,subServType,provinceID,subProvinceType,cityID,
		count(*) enterpriseCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		sum(memberCount) memberCount,
		hangupType
		FROM ( SELECT a.*,b.memberCount FROM (
		select
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		SUM(useCountMobile) AS useCountMobile,
		SUM(useCountUnicom) AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
					and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="enterpriseDataAuthList"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by statDate, serviceType, subServType, provinceID,cityID,enterpriseType,subProvinceType,hangupType
		order by statDate desc,serviceType desc,subServType desc,provinceID desc, cityID desc,enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize}  ) a
		left JOIN ecpm_t_enterprise_day_stat_service b
		on a.provinceID = b.provinceID and a.serviceType = b.serviceType and  a.statDate = b.statDate
		and a.subProvinceType = b.subProvinceType and  a.cityID = b.cityID
		<trim prefix="where" prefixOverrides="and|or">

		<if test="provinceIDs != null and provinceIDs.size()>0">
			and b.provinceID in
			<foreach item="provinceID" index="index" collection="provinceIDs"
					 open="(" separator="," close=")">
				#{provinceID}
			</foreach>
		</if>
		<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
			and b.provinceID in
			<foreach item="provinceID" index="index" collection="enterpriseDataAuthList"
					 open="(" separator="," close=")">
				#{provinceID}
			</foreach>
		</if>
		<if test="cityIDs != null and cityIDs.size()>0">
			and b.cityID in
			<foreach item="cityID" index="index" collection="cityIDs"
					 open="(" separator="," close=")">
				#{cityID}
			</foreach>
		</if>
		<if test="startDate != null and endDate != null">
			and (b.statDate <![CDATA[ >= ]]> #{startDate}
			and b.statDate <![CDATA[ <= ]]> #{endDate})
		</if>
		<if test="startDate != null and endDate == null">
			and b.statDate <![CDATA[ >= ]]> #{startDate}
		</if>
		<if test="startDate == null and endDate != null">
			and b.statDate <![CDATA[ <= ]]> #{endDate}
		</if>
		<if test="subProvinceType !=null ">
			and b.subProvinceType=#{subProvinceType}
		</if>
		</trim>
		) a
		GROUP BY statDate,serviceType,provinceID,cityID,subProvinceType,subServType,deliveryMemberCount,
		useCount,
		experienceCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		hangupType
	</select>
	
	<!-- 以市为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseDayStatCountByCity" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	statDate
		 from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="enterpriseDataAuthList"
						 open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by statDate, serviceType, subServType, provinceID,cityID,enterpriseType,subProvinceType) t
	</select>

	<!-- 更新企业日统计使用量 -->
	<update id="updateEnterpriseDayStat">
		update ecpm_t_enterprise_day_stat
		set
		<if test="useCount != null">
			useCount=useCount + #{useCount},
		</if>
		<if test="updateTime != null">
			updateTime=#{updateTime}
		</if>
		where chargeType = 2 and
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType}  and statDate=#{statDate}
	</update>
	
	<select id="queryEnterpriseDayStatByDate" resultMap="enterpriseDayStatWrapper">
		select 
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		IF(t1.useCount IS NULL,0,t1.useCount) AS useCount,
		experienceCount,
		updateTime,
		status,
		IF(t1.useCountMobile IS NULL,0,t1.useCountMobile) AS useCountMobile,
		IF(t1.useCountUnicom IS NULL,0,t1.useCountUnicom) AS useCountUnicom,
		IF(t1.useCountTelecom IS NULL,0,t1.useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_day_stat t1 
		where 
		<if test="enterpriseID !=null ">
			t1.enterpriseID=#{enterpriseID}
		</if>
		<if test="enterpriseIDs != null and enterpriseIDs.size()>0">
			t1.enterpriseID in
			<foreach item="content" index="index" collection="enterpriseIDs"
				open="(" separator="," close=")">
				#{content.enterpriseID}
			</foreach>
		</if>
		<if test="status !=null">
			and status=#{status}
		</if>
		and t1.serviceType=#{serviceType}
		<if test="subServType !=null">
		and t1.subServType=#{subServType}
		</if>
		and t1.chargeType=#{chargeType}
		<if test="hangupType !=null">
			and t1.hangupType= #{hangupType}
		</if>
		<if test="hangupType ==null">
			and t1.hangupType is null
		</if>
		and t1.statDate in 
		<foreach item="statDate" index="index" collection="dateList" open="("
			separator="," close=")">
			#{statDate}
		</foreach>
		<if test="serviceTypeList != null and serviceTypeList.size()>0">
		and t1.serviceType in
		<foreach item="serviceType" index="index" collection="serviceTypeList" open="("
			separator="," close=")">
			#{serviceType}
		</foreach>
		</if>
		<if test="subServTypeList != null and subServTypeList.size()>0">
		and t1.subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
		</if>
	</select>

	
	<select id="queryEnterpriseDayStatByEnterpriseID"
		resultMap="enterpriseDayStatWrapper">
		SELECT * from ecpm_t_enterprise_day_stat where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType}
		<if test="subServType !=null">
			and subServType=#{subServType}
		</if>
		<if test="statDate !=null">
			and statDate=#{statDate}
		</if>
	</select>
	
	<update id="updateEnterpriseDayStatByEnterpriseID">
		update ecpm_t_enterprise_day_stat
		set
		<trim suffixOverrides=",">
			<if test="useCount != null">
				useCount=useCount - #{useCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="enterpriseType !=null">
				enterpriseType=#{enterpriseType},
			</if>
			<if test="parentEnterpriseID !=null">
				parentEnterpriseID=#{parentEnterpriseID},
			</if>
			<if test="cityID !=null">
				cityID=#{cityID},
			</if>
			<if test="provinceID !=null">
				provinceID=#{provinceID}
			</if>
		</trim>
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType}
		<if test="subServType !=null">
			and subServType=#{subServType}
		</if>
		<if test="statDate !=null">
			and statDate=#{statDate}
		</if>
	</update>
	
	<insert id="insertEnterpriseDayStatByEnterpriseID">
		INSERT INTO ecpm_t_enterprise_day_stat
		(enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		updateTime,
		status,
		subProvinceType
		)
		VALUES
		(
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseType},
		#{parentEnterpriseID},
		#{provinceID},
		#{cityID},
		#{statDate},
		#{serviceType},
		#{subServType},
		#{chargeType},
		#{memberCount},
		#{deliveryMemberCount},
		#{useCount},
		#{experienceCount},
		#{updateTime},
		#{status},
		(SELECT CASE
					es.reserved10
					WHEN "112" THEN
						3
					WHEN "111" THEN
						2
					WHEN "113" THEN
						4
					ELSE 1 END FROM ecpm_t_enterprise_simple es WHERE id = #{enterpriseID} and es.enterpriseType = 5)
		)
	</insert>
	
	<update id="updateEnterpriseDayCountStat">
		update ecpm_t_enterprise_day_stat
		set
		<trim suffixOverrides="," suffix="where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType} and chargeType=#{chargeType} and statDate=#{statDate}">
			<if test="memberCount != null">
				memberCount=#{memberCount},
			</if>
			<if test="deliveryMemberCount != null">
				deliveryMemberCount=#{deliveryMemberCount},
			</if>
			<if test="useCount != null">
				useCount=#{useCount},
			</if>
			<if test="useCountMobile != null">
				useCountMobile=#{useCountMobile},
			</if>
			<if test="useCountUnicom != null">
				useCountUnicom=#{useCountUnicom},
			</if>
			<if test="useCountTelecom != null">
				useCountTelecom=#{useCountTelecom},
			</if>
			<if test="experienceCount != null">
				experienceCount=#{experienceCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
			<if test="enhancedDeliveryCount != null">
				enhancedDeliveryCount=#{enhancedDeliveryCount},
			</if>
			<if test="ussdCount != null">
				ussdCount=#{ussdCount},
			</if>
			<if test="flashCount != null">
				flashCount=#{flashCount},
			</if>
		</trim>
	</update>
	
	<!-- 批量更新 lwx595992-->
	<update id="updateEnterpriseDayCountStats">
		<foreach collection="list" item="item" index="index" open=""
			close="" separator=";">
			update ecpm_t_enterprise_day_stat
			set
			<trim suffixOverrides=","
				suffix="where enterpriseID=#{item.enterpriseID} and serviceType=#{item.serviceType} and subServType=#{item.subServType} and chargeType=#{item.chargeType} and statDate=#{item.statDate}">
				<if test="item.memberCount != null">
					memberCount=#{item.memberCount},
				</if>
				<if test="item.deliveryMemberCount != null">
					deliveryMemberCount=#{item.deliveryMemberCount},
				</if>
				<if test="item.useCount != null">
					useCount=#{item.useCount},
				</if>
				<if test="item.useCountMobile != null">
					useCountMobile=#{item.useCountMobile},
				</if>
				<if test="item.useCountUnicom != null">
					useCountUnicom=#{item.useCountUnicom},
				</if>
				<if test="item.useCountTelecom != null">
					useCountTelecom=#{item.useCountTelecom},
				</if>
				<if test="item.memberCount != null">
					memberCount=#{item.memberCount},
				</if>
				<if test="item.experienceCount != null">
					experienceCount=#{item.experienceCount},
				</if>
				<if test="item.updateTime != null">
					updateTime=#{item.updateTime},
				</if>
				<if test="item.status != null">
					status=#{item.status},
				</if>
			</trim>
		</foreach>
	</update>
	<!-- 批量插入 lwx595992 -->
	<insert id="insertEnterpriseDayCountStats">
		insert into
		ecpm_t_enterprise_day_stat
		(
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		experienceCount,
		updateTime,
		status,
		ussdCount,
		flashCount,
		subProvinceType,
		useCountMobileMq,
		enhancedDeliveryCount,
		hangupType
		)
		values
		<foreach collection="list" item="wrapper" separator=",">
			(
			#{wrapper.enterpriseID},
			#{wrapper.enterpriseName},
			#{wrapper.enterpriseType},
			#{wrapper.parentEnterpriseID},
			#{wrapper.provinceID},
			#{wrapper.cityID},
			#{wrapper.statDate},
			#{wrapper.serviceType},
			#{wrapper.subServType},
			#{wrapper.chargeType},
			#{wrapper.memberCount},
			#{wrapper.deliveryMemberCount},
			#{wrapper.useCount},
			#{wrapper.useCountMobile},
			#{wrapper.useCountUnicom},
			#{wrapper.useCountTelecom},
			#{wrapper.experienceCount},
			#{wrapper.updateTime},
			#{wrapper.status},
			#{wrapper.ussdCount},
			#{wrapper.flashCount},
			(SELECT CASE
			es.reserved10
			WHEN "112" THEN
			3
			WHEN "111" THEN
			2
			WHEN "113" THEN
			4
			ELSE 1 END FROM ecpm_t_enterprise_simple es WHERE id = #{wrapper.enterpriseID} and es.enterpriseType = 5),
			#{wrapper.useCountMobileMq},
			#{wrapper.enhancedDeliveryCount},
			#{wrapper.hangupType}
			)
		</foreach>
	</insert>
	
	<insert id="insertEnterpriseDayCountStat">
		insert into
		ecpm_t_enterprise_day_stat
		(
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		updateTime,
		status,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		ussdCount,
		flashCount,
		enhancedDeliveryCount,
		subProvinceType,
		useCountMobileMq
		)
		values
		(
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseType},
		#{parentEnterpriseID},
		#{provinceID},
		#{cityID},
		#{statDate},
		#{serviceType},
		#{subServType},
		#{chargeType},
		#{memberCount},
		#{deliveryMemberCount},
		#{useCount},
		#{experienceCount},
		#{updateTime},
		#{status},
		#{useCountMobile},
		#{useCountUnicom},
		#{useCountTelecom},
		#{ussdCount},
		#{flashCount},
		#{enhancedDeliveryCount},
		(SELECT CASE
					es.reserved10
					WHEN "112" THEN
						3
					WHEN "111" THEN
						2
					WHEN "113" THEN
						4
					ELSE 1 END
		FROM ecpm_t_enterprise_simple es WHERE id = #{enterpriseID} and es.enterpriseType = 5),
		#{useCountMobileMq}
		)
	</insert>
	
	<!-- 更新企业日统计使用量 -->
	<update id="updateEnterpriseDayStatByID">
		update ecpm_t_enterprise_day_stat
		set
		<if test="useCount != null">
			useCount=useCount + #{useCount},
		</if>
		<if test="updateTime != null">
			updateTime=#{updateTime}
		</if>
		where chargeType=#{chargeType}
		<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
			and enterpriseID in
			<foreach item="enterpriseID" index="index" collection="enterpriseIDList"
				open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		and serviceType=#{serviceType} and subServType=#{subServType} and statDate=#{statDate}
	</update>
	
	<!--统计一个月的使用量和体验量  -->
	<select id="countExperienceAndUse" resultMap="enterpriseDayStatWrapper">
		select 
		sum(t1.useCount) useCount,sum(t1.useCountMobile) useCountMobile,sum(t1.useCountUnicom) useCountUnicom,sum(t1.useCountTelecom) useCountTelecom,
		sum(t1.experienceCount) experienceCount,sum(ifnull(t1.enhancedDeliveryCount,0)) enhancedDeliveryCount,t1.serviceType,t1.subServType,t1.enterpriseID,t1.chargeType,sum(t1.useCountMobileMq) useCountMobileMq,t1.hangupType
		from ecpm_t_enterprise_day_stat t1 
		where 
		t1.enterpriseID=#{enterpriseID}
		and t1.serviceType=#{serviceType}
		and t1.subServType=#{subServType}
		and t1.chargeType=#{chargeType}
		<if test="hangupType !=null">
			and t1.hangupType= #{hangupType}
		</if>
		<if test="hangupType ==null">
			and t1.hangupType is null
		</if>
		and t1.statDate in 
		<foreach item="statDate" index="index" collection="dateList" open="("
			separator="," close=")">
			#{statDate}
		</foreach>
		group by t1.enterpriseID,t1.chargeType,t1.serviceType,t1.subServType,t1.hangupType
	</select>
	
	<select id="queryEnterpriseDayStatForSum" resultMap="enterpriseDayStatWrapper">
	    select enterpriseid,sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount,sum(ifnull(useCount,0)) as useCount,sum(ifnull(useCountMobile,0)) as useCountMobile,
	    sum(ifnull(useCountUnicom,0)) as useCountUnicom,sum(ifnull(useCountTelecom,0)) as useCountTelecom,avg(ifnull(status,0)) status,
	    sum(ifnull(experienceCount,0)) as experienceCount,
		sum(ifnull(ussdCount,0)) as ussdCount,
		sum(ifnull(flashCount,0)) as flashCount
	    from ecpm_t_enterprise_day_stat 
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType}
		and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
		<if test="statDate !=null">
			and statDate=#{statDate}
		</if>
		group by enterpriseid 
	</select>
		
	
	<delete id="deleteEnterpriseDayStatWrapper">
		delete from ecpm_t_enterprise_day_stat where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType} and statDate=#{statDate}
		and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
	</delete>
	
	<select id="queryEnterpriseDayStatByDateForSp" resultMap="enterpriseDayStatWrapper">
		select 
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		updateTime,
		status
		from ecpm_t_enterprise_day_stat t1 
		where 
		<if test="enterpriseID !=null ">
			t1.enterpriseID=#{enterpriseID}
		</if>
		<if test="status !=null">
			and status=#{status}
		</if>
		and t1.serviceType=#{serviceType}
		and t1.chargeType=#{chargeType}
		and t1.statDate in 
		<foreach item="statDate" index="index" collection="dateList" open="("
			separator="," close=")">
			#{statDate}
		</foreach>
		and t1.subServType=#{subServType}
	</select>
	
	<select id="queryDayStatFirst" resultMap="enterpriseDayStatWrapper">
		select enterpriseID from ecpm_t_enterprise_day_stat 
		where statDate=#{statDate} 
			and enterpriseType = #{enterpriseType} 
			limit 1
	</select>
	
	<delete id="deleteEnterpriseDayStatWrapperForBefore">
		delete from ecpm_t_enterprise_day_stat 
		where statDate=#{statDate} 
			and enterpriseType = #{enterpriseType} 
			<if test="chargeType != null">
				and chargeType =#{chargeType}
			</if>
	</delete>
	
	<select id="queryEnterpriseDayStatForAgentSum" resultMap="enterpriseDayStatWrapper">
	    select sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount,sum(ifnull(useCount,0)) as useCount,sum(ifnull(useCountMobile,0)) as useCountMobile,
	    sum(ifnull(useCountUnicom,0)) as useCountUnicom,sum(ifnull(useCountTelecom,0)) as useCountTelecom,avg(ifnull(status,0)) status,
	    sum(ifnull(experienceCount,0)) as experienceCount,
		sum(ifnull(ussdCount,0)) as ussdCount,
		sum(ifnull(flashCount,0)) as flashCount
	    from ecpm_t_enterprise_day_stat where parentEnterpriseID = #{parentEnterpriseID} and serviceType=#{serviceType} and subServType =#{subServType} 
	    and chargeType=#{chargeType}
	    <if test="statDate !=null">
			and statDate=#{statDate}
		</if>
        group by parententerpriseid
	</select>
	
	<update id="updateEnterpriseName">
		update ecpm_t_enterprise_day_stat t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>

	<!-- 查询日统计 -->
	<select id="queryEnterpriseDayStatByStatDate" resultMap="enterpriseDayStatWrapper" parameterType="java.lang.String">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		ussdCount,
		flashCount,
		subProvinceType
		from ecpm_t_enterprise_day_stat
		where statDate = #{statDate}
	</select>
	
	<update id="updateEnterpriseDayStatMemberCount">
		update ecpm_t_enterprise_day_stat
		set
		memberCount = #{memberCount}
		where enterpriseID=#{enterpriseID}
		and serviceType=#{serviceType} and statDate=#{statDate}
	</update>
	<insert id="insertCollectByDayStat">
		INSERT INTO ecpm_t_enterprise_day_stat_collect (
			STATUS,
			enterpriseID,
			enterpriseName,
			enterpriseType,
			parentEnterpriseID,
			provinceID,
			cityID,
			statDate,
			serviceType,
			subServType,
			chargeType,
			memberCount,
			updateTime,
			deliveryMemberCount,
			useCount,
			experienceCount,
			useCountMobile,
			useCountUnicom,
			useCountTelecom,
			ussdCount,
			flashCount,
			enhancedDeliveryCount,
			subProvinceType,
			hangupType
		) SELECT * FROM (SELECT
							 1 STATUS,
							 enterpriseID,
							 enterpriseName,
							 enterpriseType,
							 parentEnterpriseID,
							 provinceID,
							 cityID,
							 statDate,
							 serviceType,
							 subServTypeTemp subServType,
							 chargeType,
							 memberCount,
							 MAX( updateTime ) updateTime,
							 SUM( deliveryMemberCount ) deliveryMemberCount,
							 SUM( useCount ) useCount,
							 SUM( experienceCount ) experienceCount,
							 SUM( useCountMobile ) useCountMobile,
							 SUM( useCountUnicom ) useCountUnicom,
							 SUM( useCountTelecom ) useCountTelecom,
							 SUM( ussdCount ) ussdCount,
							 SUM( flashCount ) flashCount,
							 SUM( enhancedDeliveryCount ) enhancedDeliveryCount,
							 (SELECT CASE
										 es.reserved10
										 WHEN "112" THEN
											 3
										 WHEN "111" THEN
											 2
										 WHEN "113" THEN
											 4
										 ELSE 1 END FROM ecpm_t_enterprise_simple es WHERE id = enterpriseID AND es.enterpriseType = 5),
							 hangupType
						 FROM
							 ecpm_t_enterprise_day_stat
						 WHERE
							 statDate = #{statDate}
						 GROUP BY
							 statDate,
							 enterpriseID,
							 serviceType,
							 subServTypeTemp,
							 chargeType,
							 enterpriseName,
							 enterpriseType,
							 parentEnterpriseID,
							 provinceID,
							 cityID,
							 memberCount,
							 hangupType) a WHERE (a.serviceType = 4 AND (a.useCount>0 OR a.enhancedDeliveryCount>0)) OR (a.serviceType != 4 AND (a.useCount>0 OR a.memberCount>0));
	</insert>
	<select id="deleteCollectByDayStat">
		delete from ecpm_t_enterprise_day_stat_collect where statDate = #{statDate}
	</select>


	<select id="deleteStatServiceByDayStat">
		delete from ecpm_t_enterprise_day_stat_service where statDate = #{statDate}
	</select>

	<!--<insert id="insertStatServiceByDayStat">
		Insert into ecpm_t_enterprise_day_stat_service (enterpriseID,enterpriseName, enterpriseType,provinceID,cityID,statDate,serviceType,memberCount,subProvinceType)
		SELECT DISTINCT
		enterpriseID,
		enterpriseName,
		enterpriseType,
		provinceID,
		cityID,
		statDate,
		serviceType,
		memberCount,
		(SELECT IF ( es.reserved10 = "111", 2, 1 ) FROM ecpm_t_enterprise_simple es WHERE id = enterpriseID and es.enterpriseType = 5)
		FROM ecpm_t_enterprise_day_stat
		Where statDate=#{statDate} and enterpriseType=5
	</insert>-->

	<insert id="insertStatServiceByDayStat">
		INSERT INTO ecpm_t_enterprise_day_stat_service (enterpriseID,enterpriseName, enterpriseType,provinceID,cityID,statDate,serviceType,memberCount, subProvinceType,newMemberCount, unsubMemberCount,isNewEnterprise)
		SELECT DISTINCT
		t.enterpriseID,
		t.enterpriseName,
		t.enterpriseType,
		t.provinceID,
		t.cityID,
		t.statDate,
		t.serviceType,
		t.memberCount,
		(SELECT CASE
					es.reserved10
					WHEN "112" THEN
						3
					WHEN "111" THEN
						2
					WHEN "113" THEN
						4
					ELSE 1 END FROM ecpm_t_enterprise_simple es WHERE id = t.enterpriseID AND es.enterpriseType = 5) subProvinceType,
		IFNULL((CASE WHEN t.serviceType IN (1,5) THEN IFNULL(ms.newMemberCount,0)+IFNULL(db.newMemberCount,0) ELSE NULL END),0) newMemberCount,
		(SELECT COUNT(1) FROM ecpm_t_delete_back d WHERE d.subStatus IN (3, 13) AND d.enterpriseID = t.enterpriseID AND d.orgType = (CASE WHEN t.serviceType = 1 THEN 1 WHEN t.serviceType = 5 THEN 3 ELSE 0 END)  
    	AND DATE_FORMAT(d.createTime,'%Y%m%d')=#{statDate}) unsubMemberCount,
		(CASE WHEN s.enterpriseID IS NULL THEN 1 ELSE 0 END) isNewEnterprise
		FROM ecpm_t_enterprise_day_stat t
		LEFT JOIN (
			SELECT c.enterpriseID,c.orgType,COUNT(1) newMemberCount FROM ecpm_t_member_subscribe a
				JOIN ecpm_t_org_rel b ON a.memberID = b.ID
				JOIN ecpm_t_org_simple c ON b.orgID = c.ID
				WHERE a.status IN ( 3, 5, 13 )
				AND DATE_FORMAT(a.subSuccessTime,'%Y%m%d')=#{statDate}
				GROUP BY c.enterpriseID,c.orgType
		) ms ON ms.enterpriseID=t.enterpriseID AND ms.orgType=(CASE t.serviceType WHEN 1 THEN 1	WHEN 5 THEN 3 ELSE 0 END)
		LEFT JOIN (
			SELECT c.enterpriseID,c.orgType,COUNT(1) newMemberCount FROM ecpm_t_delete_back a
				JOIN ecpm_t_org_simple c ON a.orgID = c.ID
				WHERE a.subStatus IN ( 3, 5, 13 )
				AND DATE_FORMAT(a.subSuccessTime,'%Y%m%d')=#{statDate}
				GROUP BY c.enterpriseID,c.orgType

		) db ON db.enterpriseID=t.enterpriseID AND db.orgType=(CASE t.serviceType WHEN 1 THEN 1	WHEN 5 THEN 3 ELSE 0 END)
		LEFT JOIN (SELECT DISTINCT enterpriseID FROM ecpm_t_enterprise_day_stat_service WHERE statDate &lt;=#{statDateLastDay} AND enterpriseType=5) s ON s.enterpriseID=t.enterpriseID
		WHERE t.enterpriseType=5
		AND t.statDate=#{statDate}

	</insert>

	<select id="queryEnterpriseIdByStatDate" resultMap="enterpriseDayStatWrapper">
		SELECT  
			enterpriseID,
			serviceType
		FROM ecpm_t_enterprise_day_stat
		WHERE enterpriseType = 5
		AND statDate = #{statDate}
	</select>

	<update id="updateIsNewEnterprise">
		UPDATE ecpm_t_enterprise_day_stat_service
		SET isNewEnterprise = #{isNewEnterprise}
		WHERE statDate = #{statDate}
		<if test="enterpriseIds != null and enterpriseIds.size() > 0">
			AND id in
			<foreach item="id" index="index" collection="enterpriseIds"
					 open="(" separator="," close=")">
				#{id}
			</foreach>
		</if>
	</update>

	<select id="queryMenberByEnterpriseId" resultType="java.lang.String">
		SELECT
			msisdn 
		FROM
			ecpm_t_member_subscribe a
			LEFT JOIN ecpm_t_org_rel b ON a.memberID = b.ID
			LEFT JOIN ecpm_t_org_simple c ON b.orgID = c.ID 
		WHERE
			c.enterpriseID = #{enterpriseID}
			AND c.orgType = #{orgType}
			AND a.STATUS IN ( 3, 5, 13 ) 
			
		UNION
		
		SELECT
			msisdn 
		FROM
			ecpm_t_delete_back a
			LEFT JOIN ecpm_t_org_simple c ON a.orgID = c.ID 
		WHERE
			c.enterpriseID = #{enterpriseID}
			AND c.orgType = #{orgType}
			AND a.subStatus IN ( 3, 5, 13 )
	</select>

	<update id="updateIsNewEntAndNewMemCount">
		UPDATE ecpm_t_enterprise_day_stat_service
		SET 
			isNewEnterprise = #{isNewEnterprise},
			newMemberCount = #{newMemberCount}
		WHERE 
			statDate = #{statDate} 
			and enterpriseID = #{enterpriseID}
			and serviceType = #{servType}
	</update>

	<insert id="insertAreaByDayStat">
		insert into ecpm_t_enterprise_day_stat_area (enterpriseType,provinceID,cityID,countyID,statDate,serviceType,subServType,
													 chargeType,deliveryMemberCount,useCount,experienceCount,updateTime,useCountMobile,useCountUnicom,useCountTelecom,enhancedDeliveryCount,ussdCount,flashCount,subProvinceType,useCountMobileMq,hangupType)
		SELECT d.enterpriseType, d.provinceID,d.cityID,s.countyID,
			   d.statDate,
			   d.serviceType,
			   d.subServTypeTemp,
			   d.chargeType,
			   SUM(d.deliveryMemberCount) AS deliveryMemberCount,
			   SUM(d.useCount) AS useCount,
			   SUM(d.experienceCount) AS experienceCount,
			   now(),
			   SUM(d.useCountMobile)	AS useCountMobile,
			   SUM(d.useCountUnicom)	AS useCountUnicom,
			   SUM(d.useCountTelecom) AS useCountTelecom,
			   SUM(d.enhancedDeliveryCount) AS enhancedDeliveryCount,
			   SUM(d.ussdCount) AS ussdCount,
			   SUM(d.flashCount)	AS flashCount,
			   d.subProvinceType,
			   SUM(d.useCountMobileMq) AS useCountMobileMq,
			   hangupType
		FROM ecpm_t_enterprise_day_stat d FORCE INDEX(idx_enterprise_day_stat_date), ecpm_t_enterprise_simple s
		WHERE d.statDate = #{statDate} AND d.enterpriseType = 5 AND d.enterpriseID = s.id
		GROUP BY d.provinceID,d.cityID, s.countyID,  d.enterpriseType,d.subProvinceType, d.statDate, d.serviceType,d.subServTypeTemp, d.chargeType, d.hangupType;
	</insert>

	<insert id="insertServiceAreaByDayStat">
		insert into ecpm_t_enterprise_day_stat_service_area(enterpriseType,provinceID,cityID,countyID,statDate,serviceType,memberCount,subProvinceType,newMemberCount,newEnterpriseCount,unsubMemberCount,enterpriseCount, updateTime
		) SELECT t.`enterpriseType`,t.`provinceID`,t.`cityID`,s.countyID, t.statDate,t.`serviceType`,
				 SUM(t.`memberCount`) memberCount, t.`subProvinceType`,
				 SUM(t.newMemberCount) newMemberCount,
				 SUM(t.isNewEnterprise) newEnterpriseCount,
				 SUM(t.unsubMemberCount) unsubMemberCount,
				 SUM(1) enterpriseCount,
				 now()
		FROM ecpm_t_enterprise_day_stat_service t FORCE INDEX(idx_group2),ecpm_t_enterprise_simple s
		WHERE t.statDate = #{statDate} AND t.enterpriseType = 5 AND t.enterpriseID = s.id
		GROUP BY t.provinceID,t.cityID, s.countyID,  t.enterpriseType,t.subProvinceType, t.statDate, t.serviceType;
	</insert>

	<delete id="deleteAreaByDayStat">
		delete from ecpm_t_enterprise_day_stat_area where statDate = #{statDate}
		<if test="endDate != null">
			or statDate <![CDATA[ <= ]]> #{endDate}
		</if>
	</delete>

	<delete id="deleteServiceAreaByDayStat">
		delete from ecpm_t_enterprise_day_stat_service_area where statDate = #{statDate}
		<if test="endDate != null">
			or statDate <![CDATA[ <= ]]> #{endDate}
		</if>
	</delete>

	<select id="queryEnterpriseDayStatByProvinceForArea" resultMap="enterpriseDayStatWrapper">
		SELECT a.*,b.memberCount,b.enterpriseCount FROM ( select
		provinceID,
		statDate,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_day_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statDate, serviceType, subServType, provinceID,enterpriseType,subProvinceType,hangupType
		order by statDate desc ,serviceType desc,subServType desc,provinceID desc,enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize}  ) a left JOIN
		(SELECT s.statDate,s.serviceType,s.provinceID,s.subProvinceType,SUM(s.memberCount) memberCount,SUM(s.enterpriseCount)  enterpriseCount
		FROM ecpm_t_enterprise_day_stat_service_area s
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				(( 1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		GROUP BY s.statDate,s.serviceType,s.provinceID,s.subProvinceType) b
		on b.provinceID = a.provinceID and b.statDate = a.statDate and a.serviceType = b.serviceType and a.subProvinceType = b.subProvinceType
	</select>

	<select id="queryEnterpriseDayStatCountByProvinceForArea" resultType="java.lang.Integer">
		select count(1) from (select
		statDate
		from ecpm_t_enterprise_day_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statDate, serviceType, subServType, provinceID,enterpriseType,subProvinceType,hangupType) a
	</select>

	<select id="queryEnterpriseDayStatByCityForArea" resultMap="enterpriseDayStatWrapper">
		SELECT a.*,b.memberCount,b.enterpriseCount FROM ( select
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		subProvinceType,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(ussdCount) AS ussdCount,
		SUM(flashCount)	AS flashCount,
		SUM(useCountMobile)	AS useCountMobile,
		SUM(useCountUnicom)	AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom,
		hangupType
		from ecpm_t_enterprise_day_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statDate, serviceType, subServType, provinceID,cityID, enterpriseType,subProvinceType,hangupType
		order by statDate desc ,serviceType desc,subServType desc,provinceID desc,cityID desc, enterpriseType desc,subProvinceType desc
		limit #{pageNum},#{pageSize}  ) a left JOIN
		(SELECT s.statDate,s.serviceType,s.provinceID,s.cityID, s.subProvinceType,SUM(s.memberCount) memberCount,SUM(s.enterpriseCount)  enterpriseCount
		FROM ecpm_t_enterprise_day_stat_service_area s
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				(( 1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		GROUP BY s.statDate,s.serviceType,s.provinceID,s.cityID, s.subProvinceType) b
		on b.provinceID = a.provinceID and b.cityID = a.cityID and b.statDate = a.statDate and a.serviceType = b.serviceType and a.subProvinceType = b.subProvinceType
	</select>

	<select id="queryEnterpriseDayStatCountByCityForArea" resultType="java.lang.Integer">
		select count(1) from (select
		statDate
		from ecpm_t_enterprise_day_stat_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="(provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
				((1=1
				<if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
					and (provinceID in
					<foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
					)
				</if>
				<if test="cityDataAuthList != null and cityDataAuthList.size()>0">
					and (cityID in
					<foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
						#{cityID}
					</foreach>
					)
				</if>
				<if test="countyDataAuthList != null and countyDataAuthList.size()>0">
					and (countyID in
					<foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
						#{countyID}
					</foreach>
					)
				</if>
				)
				<if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
					or provinceID is null
				</if>
				)
			</if>
			<if test="subProvinceType !=null ">
				and subProvinceType=#{subProvinceType}
			</if>
			<if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
				and subProvinceType in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>

			<if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
				and subProvinceType not in
				<foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
						 open="(" separator="," close=")">
					#{dAuth}
				</foreach>
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
		</trim>
		group by statDate, serviceType, subServType, provinceID,cityID, enterpriseType,subProvinceType,hangupType) a
	</select>
</mapper>