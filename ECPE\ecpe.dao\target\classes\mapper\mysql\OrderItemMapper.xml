<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.OrderMapper">

	<resultMap id="OrderItemWrapper"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.OrderItemWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="orderItemCode" column="orderItemCode" javaType="java.lang.String" />
		<result property="orderID" column="orderID" javaType="java.lang.String" />
		<result property="productID" column="productID" javaType="java.lang.Integer" />
		<result property="unitPrice" column="unitPrice" javaType="java.math.BigDecimal" />
		<result property="quantity" column="quantity" javaType="java.lang.Integer" />
		<result property="totalAmount" column="totalAmount"	javaType="java.math.BigDecimal" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="lastUpdateTime" column="lastUpdateTime" javaType="java.util.Date" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

	<!--查询订单子项列表 -->
	<select id="queryOrderItemList" resultMap="OrderItemWrapper">
		select
		ID,
		orderItemCode,
		orderID,
		productID,
		unitPrice,
		quantity,
		totalAmount,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_orderitem
		<trim prefix="where" prefixOverrides="and|or">
			<if test="ID!=null">
				and ID = #{ID}
			</if>
			<if test="orderID!=null and orderID!=''">
				and orderID = #{orderID}
			</if>
			<if test="orderItemCode!=null  and orderItemCode!=''">
				and orderItemCode = #{orderItemCode}
			</if>
		</trim>
		order by createTime desc
	</select>

	<insert id="batchInsertOrderItem">
		INSERT INTO ecpe_t_orderitem
		(ID,
		orderItemCode,
		orderID,
		productID,
		unitPrice,
		quantity,
		totalAmount,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		VALUES
		<foreach collection="list" item="orderItemWrapper"
			separator=",">
			(
			next value for MYCATSEQ_ECPE_T_ORDERITEM,
			#{orderItemWrapper.orderItemCode},
			#{orderItemWrapper.orderID},
			#{orderItemWrapper.productID},
			#{orderItemWrapper.unitPrice},
			#{orderItemWrapper.quantity},
			#{orderItemWrapper.totalAmount},
			#{orderItemWrapper.createTime},
			#{orderItemWrapper.operatorID},
			#{orderItemWrapper.lastUpdateTime},
			#{orderItemWrapper.extInfo},
			#{orderItemWrapper.reserved1},
			#{orderItemWrapper.reserved2},
			#{orderItemWrapper.reserved3},
			#{orderItemWrapper.reserved4},
			#{orderItemWrapper.reserved5},
			#{orderItemWrapper.reserved6},
			#{orderItemWrapper.reserved7},
			#{orderItemWrapper.reserved8},
			#{orderItemWrapper.reserved9},
			#{orderItemWrapper.reserved10}
			)
		</foreach>
	</insert>
		
	<update id="updateOrderItem">
		UPDATE ecpe_t_orderitem SET 
		<trim suffixOverrides="," suffix="where orderItemCode = #{orderItemCode}">
			<if test="productID!=null">
            	productID = #{productID},
            </if>
            <if test="unitPrice!=null">
            	unitPrice = #{unitPrice},
            </if>
            <if test="quantity!=null">
            	quantity = #{quantity},
            </if>
            <if test="totalAmount!=null">
            	totalAmount = #{totalAmount},
            </if>
			<if test="createTime!=null">
            	createTime = #{createTime},
            </if>
			<if test="operatorID!=null">
				operatorID = #{operatorID},
			</if>
            <if test="lastUpdateTime!=null">
            	lastUpdateTime = #{lastUpdateTime},
            </if>
            <if test="extInfo!=null and extInfo!=''">extInfo= #{extInfo},</if>
            <if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
            <if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
            <if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
            <if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
            <if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
            <if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
            <if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
            <if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
            <if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
            <if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
		</trim> 
	</update>
	
	<delete id="deleteOrderItem">
		delete from ecpe_t_orderitem
		 where orderID = #{orderID}		 
		<if test="orderItemCode.size() > 0" >
			and orderItemCode not in (
			<foreach collection="orderItemCode" item="orderItemCode" separator="," index="index">
				#{orderItemCode}
			</foreach>
			)
		</if>
	</delete>

	<delete id="deleteOrder">
		delete from ecpe_t_order where ID  = #{ID}
	</delete>
</mapper>