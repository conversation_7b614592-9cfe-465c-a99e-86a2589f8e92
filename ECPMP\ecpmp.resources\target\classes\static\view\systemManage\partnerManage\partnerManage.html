<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>合作账号管理</title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<!--分页-->
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<link rel="stylesheet" type="text/css" href="../../../css/searchList.css"/>
	<link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">

	<script type="text/javascript" src="partnerManageCtrl.js"></script>

	<style>
		label {
			min-width: 120px;
		}

		.cond-div {
			min-width: 240px;
		}
	</style>

</head>
<body ng-app="myApp" ng-controller="AccountManagementController" ng-init="init()" class="body-min-width">
	<div class="cooperation-manage">
		<div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COOPERATIVE_MANAGEMENT' | translate">合作账号管理</span>
		</div>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<label for="accountName" class="col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'ACCOUNT_NAME'|translate"></label>

					<div class="cond-div col-xs-2">
						<input type="text" autocomplete="off" class="form-control" id="accountName"
									 placeholder="{{'PLEASEINPUTACCOUNTNAME'|translate}}" ng-model="accountName">
					</div>

					<label for="enterpriseType" class="col-xs-1 control-label" ng-bind="'COOPERATIONTYPE'|translate"></label>
					<div class="cond-div col-xs-2">
						<select class="form-control" ng-model="enterpriseType" id="enterpriseType"
										ng-options="x.id as x.name for x in enterpriseTypeList">
							<option value="">不限</option>
						</select>
					</div>
					<div class="cond-div">
						<button type="submit" class="btn search-btn" ng-click="queryPartnerList()" style="margin-left: 20px">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>

			</form>
		</div>
		<div style="margin: 20px">
			<p style="font-size: 16px" ng-bind="'COOPERATIVELIST'|translate"></p>
		</div>
	</div>
	<div class="coorPeration-table">
		<table class="table table-striped table-hover">
			<thead>
			<tr>
				<th style="width:13%" ng-bind="'ACCOUNT_NAME'|translate"></th>
				<th style="width:12%" ng-bind="'COOPERATIONTYPE'|translate"></th>
				<th style="width:10%" ng-bind="'CONTACT'|translate"></th>
				<th style="width:15%" ng-bind="'PHONENUMBER'|translate"></th>
				<th style="width:15%" ng-bind="'COMMON_CREATETIME'|translate"></th>
				<th style="width:10%" ng-bind="'CREATEACCOUNT'|translate"></th>
				<th style="width:7%" ng-bind="'LOCK_STATUS'|translate"></th>
				<th style="width:7%" ng-bind="'GROUP_STATUS'|translate"></th>

				<th style="width:23%" ng-bind="'COMMON_OPERATE'|translate"></th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="item in accountContentInfoData">
				<td><span title="{{item.accountName}}" ng-bind="item.accountName"></span></td>
				<td><span title="{{getType(item)}}"
									ng-bind="getType(item)"></span></td>
				<td><span title="{{item.fullName}}" ng-bind="item.fullName"></span></td>
				<td><span title="{{item.msisdn}}" ng-bind="item.msisdn"></span></td>
				<td><span title="{{item.createTime|formatDate}}" ng-bind="item.createTime|formatDate"></span></td>
				<td><span title="{{item.operatorAccountName}}" ng-bind="item.operatorAccountName"></span></td>
				<td><span title="{{item.lockStatus=='1'?'锁定':''}}" ng-bind="item.lockStatus=='1'?'锁定':''"></span></td>
				<td>
					<span title="{{getAccountStatus(item.accountStatus)}}"
						  ng-bind="getAccountStatus(item.accountStatus)">
					</span>
				</td>
				<td class="coorPeration-table-a">
					<div class="handle">
						<ul>
							<li class="edit" ng-click="resPwd(item)">
								<icon class="edit-icon"></icon>
								<span ng-bind="'RESETPASSWORD'|translate"></span>
							</li>
							<li ng-show="item.lockStatus=='1'" class="edit" ng-click="unlock(item)">
								<icon class="edit-icon"></icon>
								<span >解锁账号</span>
							</li>
							<li ng-show="onlySuperManager && item.accountStatus != '3' && (item.enterpriseType == '2' ||item.enterpriseType == '3')" class="edit" ng-click="unBlock(item)">
								<span >停用</span>
							</li>
							<li ng-show="onlySuperManager && item.accountStatus == '3' && item.enterpriseType == '3'" class="edit" ng-click="enable(item)">
								<span >启用</span>
							</li>
						</ul>
					</div>
				</td>
			</tr>
			<tr ng-show="accountContentInfoData===null||accountContentInfoData.length===0">
				<td style="text-align:center" colspan="9">暂无数据</td>
			</tr>
			</tbody>
		</table>
	</div>
	<div>
		<ptl-page tableId="0" change="queryPartnerList('justPage')"></ptl-page>
	</div>

	<!-- 重置密码 -->
	<div class="modal fade" id="resPassword" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width:450px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel">重置密码</h4>
				</div>
				<div class="modal-body">
					<p style="text-align:center;margin-top:30px;font-size:16px">请确认是否重置密码?</p>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn"
									ng-click="resetPassword()">是
					</button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
									id="resetBlack" style="margin-left: 20px">否
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- 锁定账号 -->
	<div class="modal fade" id="unLockAccount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width:450px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" >解锁账号</h4>
				</div>
				<div class="modal-body">
					<p style="text-align:center;margin-top:30px;font-size:16px">该账号将被解锁？</p>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn"
							ng-click="unLockAccount()">是
					</button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
							id="unLockAccountBtn" style="margin-left: 20px">否
					</button>
				</div>
			</div>
		</div>
	</div>
	<!--停用账号-->
	<div class="modal fade" id="unBlockAccount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width:450px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" >停用账号</h4>
				</div>
				<div class="modal-body">
					<p style="text-align:center;margin-top:30px;font-size:16px">请确认是否停用该账号！</p>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn"
							ng-click="unBlockAccount()">是
					</button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
							id="unBlockAccountBtn" style="margin-left: 20px">否
					</button>
				</div>
			</div>
		</div>
	</div>

	<!--启用账号-->
	<div class="modal fade" id="enableAccount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog" role="document">
			<div class="modal-content" style="width:450px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" >启用账号</h4>
				</div>
				<div class="modal-body">
					<p style="text-align:center;margin-top:30px;font-size:16px">请确认是否启用该账号！</p>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn"
							ng-click="enableAccount()">是
					</button>
					<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
							id="enableAccountBtn" style="margin-left: 20px">否
					</button>
				</div>
			</div>
		</div>
	</div>
	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
			 style="z-index:555555;">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838;text-align: center'>
							{{tip|translate}}
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align: center">
					<button type="submit" class="btn btn-primary search-btn" data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
</body>
</html>