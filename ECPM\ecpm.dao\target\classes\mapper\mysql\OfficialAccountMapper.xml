<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.OfficialAccountMapper">
	<resultMap id="officialAccountWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.OfficialAccountWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="name" column="name" javaType="java.lang.String" />
		<result property="qrCode" column="qrCode" javaType="java.lang.String" />
		<result property="officalDesc" column="officalDesc" javaType="java.lang.String" />
		<result property="isUse" column="isUse" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
	</resultMap>

	<insert id="saveOfficialAccount">
		INSERT INTO ecpm_t_offical_account
		(ID,
		name,
		qrCode,
		officalDesc,
		isUse,
		createTime,
		updateTime,
		operatorID,
		extInfo
		)
		VALUES
		(
		nextval('ecpm_seq_officalaccount'),
		#{name},
		#{qrCode},
		#{officalDesc},
		#{isUse},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{extInfo}
		)
	</insert>

	<select id="queryOfficialAccountList" resultMap="officialAccountWrapper">
		SELECT
		t.id,t.name,t.qrCode,t.officalDesc,t.isUse,t.createTime,t.updateTime,
		t.operatorID,t.extInfo from ecpm_t_offical_account t
	</select>

	<select id="queryOfficialAccountCount" resultType="java.lang.Integer">
		SELECT
		count(1)
		from ecpm_t_offical_account t where t.id=#{id}
	</select>

	<update id="updateOfficialAccountCount">
		update ecpm_t_offical_account
		set name=#{name},
		qrCode=#{qrCode},
		officalDesc=#{officalDesc},
		isUse=#{isUse},
		updateTime=#{updateTime},
		operatorID=#{operatorID}
		where id=#{id}
	</update>
</mapper>