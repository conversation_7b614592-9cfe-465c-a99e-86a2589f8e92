<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title>活动管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/mian.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>

	<script type="text/javascript" src="../../../../../directives/preview/preview.js"></script>
	<link href="../../../../../directives/preview/preview.css" rel="stylesheet"/>

	<script type="text/javascript" src="queryActDetailCtrl.js"></script>
	<style>
		p {
			margin: 0 0 10px;
		}

		.column01 p img {
			vertical-align: text-top;
		}

		.cyTableCell span {
			word-break: break-all;
		}

		.actInfoIE {
			display: inline-block;
		}
	</style>

</head>
<body ng-app='myApp' style="min-width:1024px;" ng-controller='activityDetailCtrl' ng-init="init();">
	<div class="main">

		<p class="localTitle">
			<span ng-bind="'COMMON_MERCHANT'|translate"></span> >
			<span ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></span> >
			<span ng-bind="'ACTIVITY_CHECK'|translate"></span>
		</p>

		<!--基本信息-->
		<p class="title01" ng-bind="'COMMON_BASEINFO'|translate"></p>
		<div class="column01">
			<div class="cyTable">
				<!--活动名称-->
				<p class="cyTableCell wp50">
					<span ng-bind="'COMMON_ACTIVITYNAME'|translate"></span>：
					<span ng-bind="activityInfo.activityName"></span>
				</p>
				<!--活动有效期-->
				<p class="cyTableCell">
					<span ng-bind="'ACTIVITY_EXPIRYDATE'|translate"></span>：
					<span ng-bind="activityInfo.effectivetime"></span>&nbsp;-&nbsp;
					<span ng-bind="activityInfo.expiretime"></span>
				</p>
			</div>
			<!--地区设置-->
			<p>
				<span ng-bind="'ACTIVITY_AREASET'|translate"></span>：
				<span ng-bind="citynameAll"></span>
			</p>
			<!--活动规则-->
			<p style="display: table">
				<span ng-bind="'ACTIVITY_RULES'|translate" style="display: table-cell;width: 56px;"></span>：
				<span ng-bind="activityInfo.activityRuleDesc" style="display: table-cell;word-break: break-all"></span>
			</p>
			<div class="cyTable">
				<!--活动banner图-->
				<p class="cyTableCell wp50">
					<span ng-bind="'ACTIVITY_BANNER'|translate"></span>：
					<a href="{{item.download}}" target="_blank" ng-repeat="item in bannerList" ng-show="bannerList.length>0">
						<img ng-src="{{item.review}}" width="100"/>
					</a>
					<span ng-show="bannerList.length==0" ng-bind="'NOT_UPLOAD'|translate"></span>
				</p>
				<!--活动背景图-->
				<p class="cyTableCell" ng-show="activityInfo.backLogoURL!=''&&activityInfo.backLogoURL!=null">
					<span ng-bind="'ACTIVITY_BACKGROUND_PIC'|translate"></span>：
					<a href="{{backgroundURL.download}}" target="_blank">
						<img ng-src="{{backgroundURL.review}}" width="100"/>
					</a>
				</p>
			</div>
		</div>

		<!--屏显代言-->
		<p class="title01" ng-bind="'ACTIVITY_PXSPOKE'|translate"></p>
		<div class="column01">
			<p>
				<!--屏显内容-->
				<span ng-bind="'ACTIVITY_PXCONTENT'|translate"></span>：
				<span ng-bind="pxcontent"></span>
			</p>
			<div class="cyTable">
				<!--同一用户投递时间间隔-->
				<p class="cyTableCell wp50">
					<span ng-bind="'ACTIVITY_TYYHTDSJJG'|translate"></span>：
					<span ng-bind="pxpushInterval"></span>秒
				</p>

				<p class="cyTableCell">
					<!--同一用户每天投递次数-->
					<span ng-bind="'ACTIVITY_TYYHMTTDCS'|translate"></span>：
					<span ng-bind="pxmaxPushPerDay"></span>次
				</p>
			</div>
		</div>

		<!--挂机代言-->
		<p class="title01" ng-bind="'ACTIVITY_GJSPOKE'|translate"></p>
		<div class="column01">
			<p>
				<!--挂机标题-->
				<span ng-bind="'GJ_TITLE'|translate"></span>：
				<span ng-bind="contentTitle"></span>
			</p>
			<p>
				<!--挂机内容-->
				<span ng-bind="'ACTIVITY_GJCONTENT'|translate"></span>：
				<span ng-bind="gjcontent"></span>
			</p>
			<!--挂机彩信-->
			<p>
				<span ng-bind="'ACTIVITY_GJMMS'|translate"></span>：
				<a href="{{item.download}}" target="_blank" ng-repeat="item in gjcxList" ng-show="gjcxList.length>0">
					<img ng-src="{{item.review}}" width="100"/>
				</a>
				<span ng-show="gjcxList.length==0" ng-bind="'NOT_UPLOAD'|translate"></span>
			</p>
			<div class="cyTable">
				<p class="cyTableCell wp50">
					<!--同一用户投递时间间隔-->
					<span ng-bind="'ACTIVITY_TYYHTDSJJG'|translate"></span>：
					<span ng-bind="gjpushInterval"></span>秒
				</p>

				<p class="cyTableCell">
					<!--同一用户每天投递次数-->
					<span ng-bind="'ACTIVITY_TYYHMTTDCS'|translate"></span>：
					<span ng-bind="gjmaxPushPerDay"></span>次
				</p>
			</div>
		</div>

		<!--获奖条件-->
		<p class="title01" ng-bind="'ACTIVITY_WINNINGCONDITIONS'|translate"></p>
		<div class="column01">
			<div class="cyTable">
				<!--代言条数-->
				<p class="cyTableCell wp50">
					<span ng-bind="'ACTIVITY_SPOKENUM'|translate"></span>：
					<span ng-bind="activityInfo.rewardCond.spokePushNum"></span>条
				</p>
				<!--代言天数-->
				<p class="cyTableCell">
					<span ng-bind="'SPOKES_SPOKESDAYS'|translate"></span>：
					<span ng-bind="activityInfo.rewardCond.spokeDayNum">10</span>天
				</p>
			</div>
			<!--获奖说明-->
			<p>
				<span ng-bind="'ACTIVITY_AWARD_DESCRIPTION'|translate"></span>：
				<span ng-bind="activityInfo.rewardExplain"></span>
			</p>
			<!--获奖通知-->
			<p>
				<span ng-bind="'ACTIVITY_AWARD_NOTICE'|translate"></span>：
				<span ng-bind="activityInfo.rewardNotify"></span>
			</p>
		</div>

		<!--频次设置-->
		<p class="title01" ng-bind="'ACTIVITY_FREQUENCY_SETTING'|translate"></p>
		<div class="column01">
			<div class="cyTable">
				<!--每天总投递量-->
				<p class="cyTableCell wp50">
					<span ng-bind="'ACTIVITY_TOTAL_DAILY_DELIVERY'|translate"></span>：
					<span ng-bind="activityInfo.maxPushNum"></span>条
				</p>
				<!--代言总条数-->
				<p class="cyTableCell">
					<span ng-bind="'ACTIVITY_SPOKE_TOTLE_NUM'|translate"></span>：
					<span ng-bind="activityInfo.totalNum"></span>条
				</p>
			</div>
		</div>

		<!--其他信息-->
		<p class="title01" ng-bind="'ACTIVITY_OTHER_INFO'|translate"></p>
		<!--备注-->
		<div class="column01">
			<p>
				<span ng-bind="'ACTIVITY_REMARKS'|translate"></span>：
				<span ng-bind="activityInfo.memo"></span></p>
		</div>

		<!--活动模板-->
		<p class="title01" ng-bind="'ACTIVITY_TEMPLETE'|translate"></p>
		<div class="column01">
			<div class="cyTable">
				<!--选择模板-->
				<p class="cyTableCell wp50">
					<span ng-bind="'ACTIVITY_CHOOSE_TEMPLETE'|translate"></span>：
					<span ng-bind="activityInfo.template.templateName"></span>&nbsp;&nbsp;
					<ptl:preview urllist='templateIconURL' style="display: inline-block;float: none!important;"></ptl:preview>
					<!--<a ng-click="preview()">预览</a>-->
				</p>

			</div>

		</div>

		<!--活动信息-->
		<p class="title01" ng-bind="'ACTIVITY_INFO'|translate"></p>
		<div class="column01">
			<div class="cyTable">
				<!--活动链接-->
				<p class="cyTableCell wp50 ">
					<span ng-bind="'ACTIVITY_CHOOSE_LINK'|translate"></span>：
					<span class="actInfo" ng-bind="activityInfo.activityUrl">http://mall.migudm.cn</span></p>
				<!--活动二维码-->
				<p class="cyTableCell">
					<span ng-bind="'ACTIVITY_QRCODE'|translate"></span>：
					<a href="{{activityQrCode.download}}" target="_blank"
						 ng-show="activityInfo.activityQrCode!=''&&activityInfo.activityQrCode!=null">
						<img ng-src="{{activityQrCode.review}}" width="100"/>
					</a>
					<span ng-show="activityInfo.activityQrCode==''||activityInfo.activityQrCode==null"
								ng-bind="'NOT_UPLOAD'|translate"></span>
			</div>
			<p>
				<!--创建时间-->
				<span ng-bind="'ITEM_EFFICTIVETIME'|translate"></span>：
				<span ng-bind="createTime"></span>
			</p>
		</div>
		<div>
			<button type="submit" class="btn" ng-click="goBack()" style="margin: 20px 0 0 0"
							ng-bind="'COMMON_BACK'|translate"></button>
		</div>
	</div>
	<!--小弹出框-->
	<!-- <button type="button" class="btn btn-primary" id="open-model" data-toggle="modal" data-target=".bs-example-modal-sm">Large modal</button> -->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{tip|translate}}
					</p></div>
				</div>
				<div class="modal-footer">
					<!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
					<button type="submit" class="btn" data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</body>
</html>