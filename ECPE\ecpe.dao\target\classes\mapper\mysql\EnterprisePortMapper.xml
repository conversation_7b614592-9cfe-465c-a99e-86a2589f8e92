<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.EnterprisePortMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.EnterprisePortWrapper">
        <id column="ID" property="id"/>
        <result column="enterpriseID" property="enterpriseID"/>
        <result column="enterpriseCode" property="enterpriseCode"/>
        <result column="enterpriseType" property="enterpriseType"/>
        <result column="port" property="port"/>
        <result column="accessCode" property="accessCode"/>
        <result column="status" property="status"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="reserved1" property="reserved1"/>
        <result column="reserved2" property="reserved2"/>
        <result column="reserved3" property="reserved3"/>
        <result column="reserved4" property="reserved4"/>
        <result column="reserved5" property="reserved5"/>
        <result column="reserved6" property="reserved6"/>
        <result column="reserved7" property="reserved7"/>
        <result column="reserved8" property="reserved8"/>
        <result column="reserved9" property="reserved9"/>
        <result column="reserved10" property="reserved10"/>
        <result column="enterpriseName" property="enterpriseName"/>
        <result column="parentEnterpriseID" property="parentEnterpriseID"/>
        <result column="parentEnterpriseName" property="parentEnterpriseName"/>
        <result column="groupSendSmsPort" property="groupSendSmsPort"/>
        <result column="groupSendFlashPort" property="groupSendFlashPort"/>
        <result column="groupSendMmsPort" property="groupSendMmsPort"/>
        <result column="groupSendFluid" property="groupSendFluid"/>        
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID AS id, enterpriseID, enterpriseCode, enterpriseType, port, 
        accessCode, status, createTime, updateTime, reserved1, reserved2, 
        reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, 
        reserved9, reserved10, enterpriseName, parentEnterpriseID, parentEnterpriseName,
        groupSendSmsPort, groupSendFlashPort, groupSendMmsPort, groupSendFluid
    </sql>

    <select id="getEnterprisePortList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ecpe_t_enterprise_port
    </select>

    <select id="getEnterprisePortWrapperByEnterId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ecpe_t_enterprise_port
        where enterpriseID =#{enterpriseId}
        order by updateTime DESC
    </select>

    <select id="queryEnterpriseByAccessCode" resultMap="BaseResultMap">
        select * from ecpe_t_enterprise_port t where  t.`accessCode` != '' and #{accessCode} like concat(t.accessCode,'%')
    </select>
    
    <insert id="insertEnterprisePort">
    	insert into ecpe_t_enterprise_port(
    	enterpriseID, enterpriseCode, enterpriseType, port, 
        accessCode, status, createTime, updateTime, reserved1, reserved2, 
        reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, 
        reserved9, reserved10, enterpriseName, parentEnterpriseID, parentEnterpriseName,
        groupSendSmsPort, groupSendFlashPort, groupSendMmsPort, groupSendFluid)
        value
        (
        	#{enterpriseID},
        	#{enterpriseCode},
        	#{enterpriseType},
        	#{port},
        	#{accessCode},
        	#{status},
        	now(),
        	now(),
        	#{reserved1},
        	#{reserved2},
        	#{reserved3},
        	#{reserved4},
        	#{reserved5},
        	#{reserved6},
        	#{reserved7},
        	#{reserved8},
        	#{reserved9},
        	#{reserved10},
        	#{enterpriseName},
        	#{parentEnterpriseID},
        	#{parentEnterpriseName},
        	#{groupSendSmsPort},
        	#{groupSendFlashPort},
        	#{groupSendMmsPort},
        	#{groupSendFluid}        	
        )
    </insert>
    <update id="updateEnterprisePortByEnterId" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.EnterprisePortWrapper">
    	update ecpe_t_enterprise_port
    	<trim prefix="set" suffixOverrides=",">
			<if test="accessCode != null">
				accessCode=#{accessCode},
			</if>
			<if test="groupSendSmsPort != null">
				groupSendSmsPort=#{groupSendSmsPort},
			</if>
			<if test="groupSendFlashPort != null">
				groupSendFlashPort=#{groupSendFlashPort},
			</if>
			<if test="groupSendMmsPort != null">
				groupSendMmsPort=#{groupSendMmsPort},
			</if>	
			<if test="groupSendFluid != null">
				groupSendFluid=#{groupSendFluid},
			</if>
			updateTime = now()
		</trim>
		where enterpriseID = #{enterpriseID} or parentEnterpriseID = #{enterpriseID}
    </update>
    <select id="queryEnterprisePortList" resultMap="BaseResultMap">
    	select
        <include refid="Base_Column_List"/>
        from ecpe_t_enterprise_port
        where 1=1 
        	<if test="parentEnterpriseName != null">
        		and parentEnterpriseName like "%"#{parentEnterpriseName}"%"
        	</if>
        	<if test="parentEnterpriseID != null">
        		and parentEnterpriseID = #{parentEnterpriseID}
        	</if>
        	<if test="subEnterpriseName != null">
        		and enterpriseName like "%"#{subEnterpriseName}"%"
        	</if>
        	<if test="enterpriseIDs != null and enterpriseIDs.size > 0">
        		and enterpriseID in 
        		<foreach collection="enterpriseIDs" item="enterpriseID" index="index" open="(" separator="," close=")">  
					#{enterpriseID}
				</foreach>
        	</if>
        	<if test="enterpriseType != null">
        		and enterpriseType = #{enterpriseType}
        	</if>
        	<if test="port != null">
        		and (accessCode like "%"#{port}"%" or groupSendSmsPort like "%"#{port}"%" or groupSendFlashPort like "%"#{port}"%" or groupSendMmsPort like "%"#{port}"%")
        	</if>

        order by IF (accessCode IS NOT NULL,'1',IF (groupSendSmsPort IS NOT NULL,'1',IF (groupSendFlashPort IS NOT NULL,'1',IF (groupSendMmsPort IS NOT NULL,'1','0')))) desc,updateTime desc
        limit #{pageNum},#{pageSize}
    </select>
    <select id="queryEnterprisePortListCount" resultType="java.lang.Integer">
    	select
        count(0)
        from ecpe_t_enterprise_port
          where 1=1 
        	<if test="parentEnterpriseName != null">
        		and parentEnterpriseName like "%"#{parentEnterpriseName}"%"
        	</if>
        	<if test="parentEnterpriseID != null">
        		and parentEnterpriseID = #{parentEnterpriseID}
        	</if>
        	<if test="subEnterpriseName != null">
        		and enterpriseName like "%"#{subEnterpriseName}"%"
        	</if>
        	<if test="enterpriseIDs != null and enterpriseIDs.size > 0">
        		and enterpriseID in 
        		<foreach collection="enterpriseIDs" item="enterpriseID" index="index" open="(" separator="," close=")">  
					#{enterpriseID}
				</foreach>
        	</if>
        	<if test="enterpriseType != null">
        		and enterpriseType = #{enterpriseType}
        	</if>
        	<if test="port != null">
        		and (accessCode like "%"#{port}"%" or groupSendSmsPort like "%"#{port}"%" or groupSendFlashPort like "%"#{port}"%" or groupSendMmsPort like "%"#{port}"%")
        	</if>
    </select>
    
    <select id="checkUniqueData" resultMap="BaseResultMap">
    	SELECT 
    	<include refid="Base_Column_List"/> 
    	FROM ecpe_t_enterprise_port t 
    	WHERE ((t.enterpriseType = 3  and t.parentEnterpriseID != #{parentEnterpriseID}) or (t.enterpriseType = 2  and t.enterpriseID != #{parentEnterpriseID})) 
    	AND 
        <if test="ports != null and ports.size > 0">
    	<foreach collection="ports" item="port" open="(" separator="OR" close=")">  
    		(LOCATE(CONCAT('|', #{port},'|'), CONCAT('|',groupSendSmsPort,'|',groupSendFlashPort,'|',groupSendMmsPort,'|'))) 
		</foreach>
		</if>
		<if test="hotPorts != null and hotPorts.size > 0">
    	<foreach collection="hotPorts" item="port" open="(" separator="OR" close=")">  
    		(LOCATE(CONCAT('|', #{port},'|'), CONCAT('|',accessCode,'|'))) 
		</foreach>
		</if>
    	limit 0, 1
    </select>
    
</mapper>
