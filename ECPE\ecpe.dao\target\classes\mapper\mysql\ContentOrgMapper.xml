<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ContentOrgMapper">
	<resultMap id="contentOrgMap"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentOrgWrapper">
		<result property="id" column="ID" />
		<result property="cyContID" column="cyContID" />
		<result property="ownerType" column="ownerType" />
		<result property="ownerID" column="ownerID" />
		<result property="createTime" column="createTime" />
		<result property="updateTime" column="updateTime" />
		<result property="operatorID" column="operatorID" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="orgCode" column="orgCode" />
		<result property="orgName" column="orgName" />
		<result property="hotlineNo" column="hotlineNo" />
	</resultMap>
	
	<sql id="content_org_wrapper">
		ID,cyContID,ownerType,ownerID,createTime,updateTime,operatorID,
		extInfo,reserved1,reserved2,reserved3,reserved4,orgCode,orgName,hotlineNo
	</sql>
	
	<insert id="insertContentOrg" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.ContentWrapper">
		INSERT INTO ecpe_t_content_org
		(
			<include refid="content_org_wrapper"></include>
		)
		VALUES
		(
		#{id},
		#{cyContID},
		#{ownerType},
		#{ownerID},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{orgCode},
		#{orgName},
		#{hotlineNo}
		)
	</insert>
	
	<delete id="deleteContentOrgByContentID" parameterType="java.lang.Long">
	    delete from ecpe_t_content_org where cyContID=#{cyContID}
	</delete>
	
	<select id="queryCountByPhoneAndTemplate" resultType="java.lang.Integer">
		select
			count(*)
		from ecpe_t_content_org t 
		where t.cyContID = #{cyContID}
		and  t.hotlineNo= #{hotlineNo}
	</select>

</mapper>