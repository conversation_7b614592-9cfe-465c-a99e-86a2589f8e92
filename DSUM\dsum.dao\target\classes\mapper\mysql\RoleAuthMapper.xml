<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.RoleAuthMapper">
	<resultMap id="roleAuthModel" type="com.huawei.jaguar.dsum.dao.domain.RoleAuthWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="roleID" column="roleID" javaType="java.lang.Integer" />
		<result property="authType" column="authType" javaType="java.lang.Integer" />
		<result property="authID" column="authID" javaType="java.lang.Integer" />
		<result property="operateTime" column="operateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>

    <insert id="batchCreateRoleAuth">
    INSERT INTO dsum_t_role_auth
		(id,
		roleID,
		authType,
		authID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10)
		VALUES
		<foreach collection="list" item="roleAuthWrapper"
			separator=",">
			(
			nextval('seq_sequence_roleAuth'),
			#{roleAuthWrapper.roleID},
			#{roleAuthWrapper.authType},
			#{roleAuthWrapper.authID},
			#{roleAuthWrapper.operateTime},
			#{roleAuthWrapper.operatorID},
			#{roleAuthWrapper.extInfo},
			#{roleAuthWrapper.reserved1},
			#{roleAuthWrapper.reserved2},
			#{roleAuthWrapper.reserved3},
			#{roleAuthWrapper.reserved4},
			#{roleAuthWrapper.reserved5},
			#{roleAuthWrapper.reserved6},
			#{roleAuthWrapper.reserved7},
			#{roleAuthWrapper.reserved8},
			#{roleAuthWrapper.reserved9},
			#{roleAuthWrapper.reserved10}
			)
		</foreach>
	</insert>
	

	<select id="getRoleAuth" resultMap="roleAuthModel">
		select
		id,
		roleID,
		authType,
		authID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10 from
		dsum_t_role_auth
		<trim prefix="where" prefixOverrides="and|or">
		<if test="roleID != null">
			and roleID = #{roleID}
		</if>
		<if test="authType !=null  and authType !=''">
			and authType = #{authType}
		</if>
		</trim>
	</select>
	
	<delete id="deleteRoleAuth">
	delete from dsum_t_role_auth
	<trim prefix="where" prefixOverrides="and|or">

		and roleID = #{roleID}

		<if test="authType !=null">
			and authType = #{authType}
		</if>

		<if test="authTypeNotEqual !=null">
			and authType != #{authTypeNotEqual}
		</if>
		</trim>
	</delete>
	
	<delete id="deleteRoleAuthByRoleID" parameterType="java.util.List">
		delete from dsum_t_role_auth where roleID in
		<foreach collection="list" item="roleIDs" index="no" open="("
			separator="," close=")">
			#{roleIDs}
		</foreach>
	</delete>
    
</mapper>