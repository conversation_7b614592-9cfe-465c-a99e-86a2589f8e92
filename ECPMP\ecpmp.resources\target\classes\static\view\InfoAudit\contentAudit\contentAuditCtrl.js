var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n"])
app.controller('contentAuditCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 2,
        "totalCount": 20,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    $scope.isRefYsmbChoise = [
	  {
        id: "",
        name: "不限"
	  },
	  {
        id: 0,
        name: "自定义内容"
	  },
	  {
        id: 1,
        name: "固定模板内容"
	  }
	];
    $scope.enterpriseID = "";
    $scope.enterpriseName = "";
    $scope.contentID = "";
    $scope.transContentID = "";
    $scope.contentTitile = "";
    $scope.contentName = "";
    $scope.isRefYsmb = "";
    $scope.isSuperManager = false;

    $scope.getApproveStatus = function (approveStatus) {
      if (approveStatus == 1) {
        return "审核失败";
      }
      else if (approveStatus == 2) {
        return "待审核";
      }
      else if (approveStatus == 3) {
        return "审核通过";
      }
      else if (approveStatus == 4) {
        return "审核驳回";
      }
    }

      $scope.getUnicomApproveStatus = function (unicomApproveStatus) {
          if (unicomApproveStatus == -1) {
            return "--";
          }
          else if (unicomApproveStatus == 1) {
              return "审核失败";
            }
            else if (unicomApproveStatus == 2) {
              return "待审核";
            }
            else if (unicomApproveStatus == 3) {
              return "审核通过";
            }
            else if (unicomApproveStatus == 4) {
              return "审核驳回";
            }
      }

    $scope.loginRoleType=$.cookie('loginRoleType');
    $scope.isSuperManager = ($scope.loginRoleType=='superrManager'||$scope.loginRoleType=='normalMangager');
    if ($scope.isSuperManager||$scope.loginRoleType=='agent') {
      $scope.enterpriseID = "";
    } else {
      $scope.enterpriseID = $.cookie('enterpriseID');
    }
    $scope.getContentInfoList();
    $scope.enterpriseinfo();
  };
  
  $scope.enterpriseinfo = function () 
  {
  	if (null == $.cookie('enterpriseID')) 
  	{
			return;
		}
  	var req = {
  	        "id": $.cookie('enterpriseID'),
  	        "pageParameter": {
  	          "pageNum": 1,
  	          "pageSize": 100,
  	          "isReturnTotal": "1"
  	        }
  	      }
  	      /*查询企业列表*/
  	      RestClientUtil.ajaxRequest({
  	        type: 'POST',
  	        url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
  	        data: JSON.stringify(req),
  	        success: function (data) {
  	          $rootScope.$apply(function () {
  	            var result = data.result;
  	            if (result.resultCode == '1030100000') {
  	            	console.log(data.enterprise);
  	              //$scope.enterpriseName = data.enterprise.enterpriseName;
  	              $scope.businessStatus = data.enterprise.businessStatus;
  	              $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
  	              console.log($scope.businessStatus);
  	              if ($scope.businessStatus == 1) {
  	            	  $('#Modalisaengt').modal();
  	              }
  	            }
  	          })
  	        }
  	      });
  };
  $scope.getContentInfoList = function (condition) {
    var contentIDList = null;

    if ($scope.contentID !== "" && $scope.contentID != null) {
        if(!Number.isNaN(+$scope.contentID) && $scope.bigNumCompare($scope.contentID,'2147483647') === 1){
            $scope.tip = "内容编码输入值非法";
            $('#myModal').modal();
            return
        }else {
            var rs = /^[0-9]*$/.test($scope.contentID);
            if (!rs) {
                contentIDList = [-1];
            } else {
                contentIDList = [$scope.contentID];
            }
        }
    }
    var req;
    if (condition != 'justPage') {
      req = {
        "enterpriseID": $scope.enterpriseID,
        "enterpriseName": $scope.enterpriseName,
        "contentIDList": contentIDList,
        "contentTitile": $scope.contentTitile,
        "contentName": $scope.contentName,
        "isRefYsmb": $scope.isRefYsmb,
        "getBelongOrg": 0,
        "getFrame": 0,
        "getPushTime": 0,
        "getSwitchState": 0,
        "contentTypeList":[1,2],
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      req = $scope.reqTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.contentInfoList = result.contentInfoList;
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount!==0 ? Math.ceil($scope.pageInfo[0].totalCount / parseInt($scope.pageInfo[0].pageSize)):1;
          } else {
            $scope.contentInfoList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
        )
      }
    });
  }

    $scope.bigNumCompare = function(a, b) {
            let back = 0
            let max = Math.ceil(Math.max(a.length, b.length) / 15)
            //分成多少段,从左边开始
            for (let i = max; i > 0; i--) {
                let num1 = $scope.getMidNum(a, a.length - i * 15, 15)
                let num2 = $scope.getMidNum(b, b.length - i * 15, 15)
                //15位数字相减
                let cur = num1 - num2
                if (cur < 0) {
                    back = -1
                    break
                } else if (cur > 0) {
                    back = 1
                    break
                }
            }
            return back
    }

    $scope.getMidNum = function(str, start, len) {
    if (start + len > 0) {
      return +str.substr(start < 0 ? 0 : start, start < 0 ? start + len : len)
    } else {
      return 0
    }
  }

}]);
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}]);

app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
    }
    return "";
  }
})