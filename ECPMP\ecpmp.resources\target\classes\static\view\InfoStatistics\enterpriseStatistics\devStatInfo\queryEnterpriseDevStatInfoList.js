var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {

        $scope.notRefresh = JSON.parse(sessionStorage.getItem("notRefresh"));
        if($scope.notRefresh == undefined || $scope.notRefresh == ""){
            sessionStorage.setItem("searchParam",JSON.stringify(''))
        }else{
            sessionStorage.setItem("notRefresh",JSON.stringify(''))
        }

      $scope.token = $.cookie("token");
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否分省管理员
        $scope.isProvincial = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isProvincial = (loginRoleType == 'provincial');
        $scope.isSuperManager = (loginRoleType=='superrManager');
        $scope.isNormalMangager = (loginRoleType=='normalMangager');

        $scope.enterpriseID = "";
        
        if ($scope.isProvincial)
        {
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
        }

        $scope.selectedProvince = null;

        //默认为1：名片彩印
        $scope.serviceType = "";

        $scope.enterpriseName = "";

        //下拉框(业务类别)
        $scope.serviceTypeChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 5,
                name: "热线彩印省份版"
            }
        ];
        $scope.isZyzq = "";
        $scope.isZyzqChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "分省"
            },
            {
                id: 2,
                name: "集客"
            },
            {
                id: "3",
                name:
                    "移动云PAAS"
            },
            {
                id: "4",
                name:
                    "咪咕音乐"
            }
        ];

        $scope.channelSelectMap = [{
            id: "",
            name: "不限"
        }];
        $scope.enterpriseTypeList = localStorage.getItem("enterpriseTypeList")
        if($scope.enterpriseTypeList!=null){
            angular.forEach(JSON.parse($scope.enterpriseTypeList),function (item){
                if("112" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"3","name": "移动云PAAS"} );
                }
                if("111" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"2","name": "集客"});
                }
                if("0" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"1","name": "省份"});
                }
                if("113" === item.fieldVal){
                    $scope.channelSelectMap.push({"id":"4","name": "咪咕音乐"});
                }
            });
        }
        $scope.isZyzqChoise = $scope.channelSelectMap;
        if (sessionStorage.getItem("searchParam") && sessionStorage.getItem("searchParam") !== "null" && sessionStorage.getItem("searchParam") !== "undefind")
        {
            var searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
            console.log("缓存省："+searchParam.provinceID)
            if(searchParam.subProvinceType!=null && searchParam.subProvinceType!=undefined){
                $scope.isZyzq = searchParam.subProvinceType;
            }
            if(searchParam.serviceType!=null && searchParam.serviceType!=undefined){
                $scope.serviceType = searchParam.serviceType;
            }

        }

        $scope.selectedProvince = null;
        if ($scope.isSuperManager || $scope.isProvincial) {
            $scope.queryProvinceAndCitybychaoguan(1);
        }
        else {
            $scope.queryProvinceAndCity(1);
        }

        //初始化搜索条件
        $scope.initSel = {
            provinceName: "0",//归属地
            startTime: "",
            endTime: "",
            search:false,
        };
        //加载缓存中的搜索条件
        //$scope.initSearchParams();
        $scope.initDatePicker();
        $scope.queryEnterpriseDevStatInfo();
        $scope.checkUnique = true;
    }


    $scope.initSearchParams = function () {
        if($scope.isSuperManager){
            if (sessionStorage.getItem("cacheProvinceIDs") && sessionStorage.getItem("cacheProvinceIDs") !== "null" && sessionStorage.getItem("cacheProvinceIDs") !== "undefind")
            {
                jQuery.each($scope.provinceList,function(i,e){
                    var provinceIDs = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
                    if([e.provinceID] == provinceIDs[0]){
                        $rootScope.$applyAsync(function () {
                            $scope.selectedProvince = e;
                            $scope.changeSelectedProvince(e);
                        });
                    }
                });
            }

            if (sessionStorage.getItem("cacheCityIDs") && sessionStorage.getItem("cacheCityIDs") !== "null" && sessionStorage.getItem("cacheCityIDs") !== "undefind")
            {
                jQuery.each($scope.cityList,function(i,e){
                    jQuery.each(e,function(i,subCity){
                        var cityIDs = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
                        if(subCity.cityID == cityIDs[0]){
                            $rootScope.$applyAsync(function () {
                                $scope.selectedCity = subCity;
                                $scope.changeSelectedCity($scope.selectedCity);
                            });
                        }
                    });
                });
            }
        }
    }
    $scope.initDatePicker = function(){
        $('.input-daterange').datepicker({
            format: "yyyy-mm-dd",
            weekStart: 0,
            clearBtn: true,
            language: "zh-CN",
            autoclose: true
        });
        $('#start').on('changeDate', function () {
            var startTime = document.getElementById("start").value;

            $rootScope.$applyAsync(function () {
                var dt= startTime.substring(0,4) + '/' +startTime.substring(5,7) + '/' +startTime.substring(8,10) + ' '+'00:00:00';
                $scope.startTime =new Date(dt);
            });
            var dt= startTime.substring(0,4) + '/' +startTime.substring(5,7) + '/' +startTime.substring(8,10) + ' '+'00:00:00';
            //如果上次的时间为Invalid Date，说明点击了清除，此处用于修复插件本身的bug--（点击清除后可选择开始时间大于结束时间）
            var lastStartTime = $scope.startTime;
            $scope.startTime =new Date(dt);
            if(lastStartTime == "Invalid Date" && $scope.startTime != "Invalid Date"){
                $('#end').datepicker('setDate', new Date(dt));
            }
            console.log("startTime:"+$scope.startTime)
            $scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7) + startTime.substring(8,10) + '000000';
            // //alert($scope.initSel.startTime)
        });
        /*if(sessionStorage.getItem("cacheStartTime") && sessionStorage.getItem("cacheStartTime") !== null && sessionStorage.getItem("cacheStartTime") != "null" && sessionStorage.getItem("cacheStartTime") != "undefined")
        {
            $('#start').datepicker('setDate', new Date(JSON.parse(sessionStorage.getItem("cacheStartTime"))));
        }*/

        $('#end').on('changeDate', function () {
            var endTime = document.getElementById("end").value;

            $rootScope.$applyAsync(function () {
                var dt = endTime.substring(0,4) + '/' +endTime.substring(5,7) +'/' + endTime.substring(8,10) + ' ' + '23:59:59';
                $scope.endTime =new Date(dt);
                console.log("endTime:"+$scope.endTime)
            })
            var dt = endTime.substring(0,4) + '/' +endTime.substring(5,7) +'/' + endTime.substring(8,10) + ' ' + '23:59:59';
            $scope.endTime =new Date(dt);
            $scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7) + endTime.substring(8,10) + '235959';
        });
        /*if(sessionStorage.getItem("cacheEndTime") && sessionStorage.getItem("cacheEndTime") !== null && sessionStorage.getItem("cacheEndTime") != "null" && sessionStorage.getItem("cacheEndTime") != "undefined")
        {
            $('#end').datepicker('setDate', new Date(JSON.parse(sessionStorage.getItem("cacheEndTime"))));
        }*/
        /*$('#startAg').on('changeDate', function () {
            var startTime = document.getElementById("startAg").value;

            $rootScope.$apply(function () {
                var dt= startTime.substring(0,4) + '/' +startTime.substring(5,7) + '/' +startTime.substring(8,10) + ' '+'00:00:00';
                $scope.startTime =new Date(dt);
            })
        });


        $('#endAg').on('changeDate', function () {
            var endTime = document.getElementById("endAg").value;

            $rootScope.$apply(function () {
                var dt = endTime.substring(0,4) + '/' +endTime.substring(5,7) +'/' + endTime.substring(8,10) + ' ' + '23:59:59';
                $scope.endTime =new Date(dt);
            })
        });*/

        var startTime = $scope.getFirDayInThisMon();
        var endTime = $scope.getThisDayInThisMon();
        var dt1= startTime.substring(0,4) + '/' +startTime.substring(4,6) + '/' +startTime.substring(6,8) + ' '+'00:00:00';
        var dt2 = endTime.substring(0,4) + '/' +endTime.substring(4,6) +'/' + endTime.substring(6,8) + ' ' + '23:59:59';
        if (sessionStorage.getItem("searchParam") && sessionStorage.getItem("searchParam") !== "null" && sessionStorage.getItem("searchParam") !== "undefind")
        {
            var searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
            console.log("缓存省："+searchParam.provinceID)
            if(searchParam.startDate!=null && searchParam.startDate!=undefined){
                $scope.initSel.startTime = searchParam.startDate;
                dt1= searchParam.startDate.substring(0,4) + '/' +searchParam.startDate.substring(4,6) + '/' +searchParam.startDate.substring(6,8) + ' '+'00:00:00';
            }
            if(searchParam.endDate!=null && searchParam.endDate!=undefined){
                $scope.initSel.endTime = searchParam.endDate;
                dt2 = searchParam.endDate.substring(0,4) + '/' +searchParam.endDate.substring(4,6) +'/' + searchParam.endDate.substring(6,8) + ' ' + '23:59:59';
            }


        }
        $('#start').datepicker('setDate', new Date(dt1));
        $('#end').datepicker('setDate', new Date(dt2));


    }
    $scope.getServiceType = function (serviceType) {
        if (serviceType == 1) {
            return "名片彩印";
        }
        else if (serviceType == 2) {
            return "热线彩印";
        }
        else if (serviceType == 3) {
            return "广告彩印";
        }
        else if (serviceType == 4) {
            return "企业通知";
        }
        else if (serviceType == 5) {
            return "热线彩印省份版";
        }
    };
    $scope.getSubProvinceType = function (subProvinceType) {
        if (subProvinceType == 1) {
            return "分省";
        }
        else if (subProvinceType == 2) {
            return "集客";
        }
        else if (subProvinceType == 3) {
            return "移动云PAAS";
        }else if (subProvinceType == 4) {
            return "咪咕音乐";
        }
    };

    $scope.getSubServType = function (subServType) {
        if (subServType == 1) {
            return "主叫屏显";
        }
        else if (subServType == 2) {
            return "被叫屏显";
        }
        else if (subServType == 4) {
            return "挂机短信";
        }
        else if (subServType == 8) {
            return "挂机彩信";
        }
        else if (subServType == 3) {
            return "屏显";
        }
    };

    $scope.getTime = function (time) {
        var year = time.slice(0, 4);
        var month = time.slice(4, 6);
        var day = time.slice(6, 8);
        return year + "-" + month + "-" + day;
    }

    //获取当前月的第一天以及当前天
    $scope.getFirDayInThisMon = function () {
        let date = new Date();
        date.setDate(1);
        let month = parseInt(date.getMonth() + 1);
        let day = date.getDate();
        if (month < 10) {
                month = '0' + month
            }
        else
        {
            month = '' + month
        }
        if (day < 10) {
                day = '0' + day
            }
        else
        {
        	day = '' + day
        }
        return(date.getFullYear() + month + day + '000000');


    }
    $scope.getThisDayInThisMon = function () {
        let date2 = new Date()
        let month2 = parseInt(date2.getMonth() + 1);
        let day2 = date2.getDate();
        if (month2 < 10) {
            month2 = '0' + month2
        }
        else
        {
        	month2 = '' + month2
        }
        if (day2 < 10) {
            day2 = '0' + day2
        }
        else
        {
        	day2 = '' + day2
        }
        return(date2.getFullYear() + month2 + day2 + '235959');
    }

    /*$('.input-daterange').datepicker({
		format: "yyyy-mm-dd",
		weekStart: 0,
		language: "zh-CN",
		clearBtn: true,
		autoclose: true
	});

	$('#start').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});*/
  $scope.exportFile = function () {
      let provinceID ='';
      if (!$scope.isProvincial && $scope.selectedProvince != null && $scope.selectedProvince != undefined) {
          if ($scope.isSuperManager) {
              provinceID = $scope.selectedProvince.provinceID;
          }
          if ($scope.isProvincial || $scope.isNormalMangager) {
              provinceID = $scope.selectedProvince.fieldVal;
          }
      }
      let cityID ='';

      if (!$scope.isProvincial && $scope.selectedCity != null && $scope.selectedCity != undefined) {
          if ($scope.isSuperManager) {
        	  cityID = $scope.selectedCity.cityID;
          }
          if ($scope.isProvincial || $scope.isNormalMangager) {
        	  cityID = $scope.selectedCity.fieldVal;
          }
      }
      var req = {
          "param":{"req":JSON.stringify($scope.reqTemp),"type":1},
          "url":"/qycy/ecpmp/ecpmpServices/enterpriseManageService/downEnterpriseDevStatInfoCsvFile",
          "method":"get"
      }

      if($scope.reqTemp != undefined)
      {
          CommonUtils.exportFile(req);
      }
  };
	/*$('#end').on('changeDate', function () {
	$rootScope.$apply(function () {
		$scope.searchOn();
	})
	});*/

	//判断搜索按钮是否置灰
	$scope.searchOn = function () {
		var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;
        $scope.initSel.startTime = "";
        $scope.initSel.endTime = "";

		if (startTime !== '')
		{
			$scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
		}
		
		if (endTime !== '')
		{
			$scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
		}
		
		if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
			$scope.initSel.search = false;
		}
		else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
			$scope.initSel.search = false;
		}
		else {
			$scope.initSel.search = true;
		}
	}

    //后续post的函数
    $scope.queryEnterpriseDevStatInfo = function (condition) {
        //若日期清掉，starttime和endtime都设置为空
        if ($scope.time == "") {
            $scope.initSel.startTime = "";
            $scope.initSel.endTime = "";
        }
        $scope.checkUnique = true;
        if (condition != 'justPage') {
            var req = {
                "areaDimension": 2,
                "enterpriseType":5,
                "subProvinceType":$scope.isZyzq,
                "enterpriseName": $scope.enterpriseName || '',
                "provinceID": $scope.provinceID,
                "cityID":$scope.cityID,
                "serviceType": $scope.serviceType || '',
                "startDate": $scope.initSel.startTime || '',
                "endDate": $scope.initSel.endTime || '',
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            //alert("省:"+$scope.selectedProvince)
            //alert("市:"+$scope.selectedCity)
            if ($scope.selectedProvince != null && $scope.selectedProvince != undefined) {
            	if ($scope.isSuperManager) {
            		req.provinceID = $scope.selectedProvince.provinceID;
                    //alert(req.provinceID)
            	}
            	if ($scope.isProvincial || $scope.isNormalMangager) {
            		req.provinceID = $scope.selectedProvince.fieldVal;

            	}
            }
            if ($scope.selectedCity != null && $scope.selectedCity != undefined) {
                if ($scope.isSuperManager) {
                	req.cityID = $scope.selectedCity.cityID;
                    //alert(req.cityID)
                }
                if ($scope.isProvincial || $scope.isNormalMangager) {
                	req.cityID = $scope.selectedCity.fieldVal;

                }
            }
            if ($scope.initSel.startTime != null && $scope.initSel.startTime != ""
            		&& $scope.initSel.startTime == $scope.initSel.endTime) {
            	req.getCollectData = 1;
            }
            if(req.startDate == undefined || req.startDate == ''){
                req.startDate = $scope.getFirDayInThisMon();
            }
            if(req.endDate == undefined || req.endDate == ''){
                req.endDate = $scope.getThisDayInThisMon();
            }

            $scope.pageInfo[0].currentPage = 1;


            if (sessionStorage.getItem("searchParam") && sessionStorage.getItem("searchParam") !== "null" && sessionStorage.getItem("searchParam") !== "undefind")
            {
                var searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
                console.log("缓存省："+searchParam.provinceID)
                if(searchParam.provinceID!=null && searchParam.provinceID!=undefined){
                    req.provinceID = searchParam.provinceID;
                }
                if(searchParam.cityID!=null && searchParam.cityID!=undefined){
                    req.cityID = searchParam.cityID;
                }

            }
            $scope.reqTemp = angular.copy(req);
            if(req.startDate.length < 14 || req.endDate.length <14)
            {
                $scope.uniqueTip = "请输入日期查询条件";
                $scope.checkUnique = false;
                return;
            }
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            if(req.startDate.length < 14 || req.endDate.length <14)
            {
                $scope.uniqueTip = "请输入日期查询条件";
                $scope.checkUnique = false;
                return;
            }
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
            req.page.isReturnTotal = "0";
        }

        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseDevStatInfo",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if(data.resultCode=='1030100000'){
                $scope.StatInfoListData=result.enterpriseDevStatInfoList||[];
                if (condition != 'justPage') {
                    $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                    $scope.pageInfo[0].totalPage=result.totalNum!==0 ?Math.ceil(result.totalNum/parseInt($scope.pageInfo[0].pageSize)):1;
                }
              }  
              else 
              {
                $scope.tip = data.resultCode;
                $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = "1030120500";
              $('#myModal').modal();
            }
            )
          }
        });

    };
  //搜索省份改变时，找到对应的市(超管)
    $scope.changeSelectedProvincebychaoguan = function (selectedProvince,type) {
        $scope.subCityList = null;
        if (selectedProvince) {

            jQuery.each($scope.cityList, function (i, e) {
                if (e.key == selectedProvince.provinceID) {
                    $scope.subCityList = e;
                }
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
        if (sessionStorage.getItem("searchParam") && sessionStorage.getItem("searchParam") !== "null" && sessionStorage.getItem("searchParam") !== "undefind" && type == 1)
        {
            //alert(1111);
            var searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
            console.log("缓存市："+searchParam.cityID)
            if(searchParam.cityID!=null && searchParam.cityID!=undefined){
                jQuery.each($scope.subCityList,function(i,e){
                    if([e.cityID] == searchParam.cityID){
                        $rootScope.$applyAsync(function () {
                            $scope.selectedCity = e;
                            //alert("省后:"+$scope.selectedProvince)
                            //alert("市后:"+$scope.selectedCity)
                        });
                    }
                });
                // sessionStorage.setItem("searchParam",JSON.stringify(''))
            }
            sessionStorage.setItem("searchParam",JSON.stringify(''))
        }

    }

    //从Local Storage中查询超管省的方法
    $scope.queryProvinceAndCitybychaoguan = function (type) {

    	if ($scope.isProvincial)
        {
    		if($.cookie('provinceID') != null 
       			 &&　$.cookie('provinceID')　!= undefined
       			 && $.cookie('cityID') != null
       			 && $.cookie('cityID') != undefined)
       	 {
       		var queryProvinceListReq = {};
       	    /*查询省份*/
       	    RestClientUtil.ajaxRequest({
       	      type: 'POST',
       	      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
       	      data: JSON.stringify(queryProvinceListReq),
       	      success: function (data) {
       	        $rootScope.$apply(function () {
       	          var result = data.result;
       	          var provinceList = data.provinceList;
       	          if (result.resultCode == '1030100000') {
       	              jQuery.each(provinceList, function (i, e) {
       	            	if($.cookie('provinceID') == e.provinceID)
       	            	{
       	            		$scope.provinceList = {};
       	            		$scope.provinceList2 = {};

       	            		$scope.provinceList[0] = e;
       	            		$scope.selectedProvince = e;
       	            		$scope.provinceList2[e.provinceID] = e.provinceName;
       	            		return;
       	            	}
       	          		});
	              var provinceIds = [];
	              provinceIds[0] = $.cookie('provinceID');
	              var queryCityListReq = {};
	              queryCityListReq.provinceIDs = provinceIds;
	              /*查询地市*/
	              RestClientUtil.ajaxRequest({
	                type: 'POST',
	                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
	                data: JSON.stringify(queryCityListReq),
	                success: function (data) {
	                  $rootScope.$apply(function () {
	                    var result = data.result;
	                    if (result.resultCode == '1030100000') {
	                      $scope.subCityList = {};
	                      jQuery.each(data.cityList, function (i, e) {
	        	            		jQuery.each(e, function (i, e) {
	    	        	            	if(e.cityID == $.cookie('cityID'))
	    	        	            	{
	    	        	            		$scope.subCityList = {};
	    	        	            		$scope.selectedCity = e;
  	    	        	            		$scope.subCityList[0] = e;

	    	        	            		$scope.cityList2 = {};
	    	        	                    $scope.cityList2[e.cityID] = e.cityName;

	    	        	            		return;
	    	        	            	}
	    	        	              });
	        	              });
	                    }else {
	    	                $scope.tip =result.resultCode;
	    	                $('#myModal').modal();
	    	              }
	                  })
	                },
	                error:function(){
	                    $rootScope.$apply(function(){
	                        $scope.tip='1030120500';
	                        $('#myModal').modal();
	                        }
	                    )
	                }
	              });
       	       }
       	          else {
       	              $scope.tip=data.result.resultCode;
       	              $('#myModal').modal();
       	            }
       	        })
       	      },
       	      error:function(){
       	          $rootScope.$apply(function(){
       	        	  	  $scope.tip = '1030120500';
       	                  $('#myModal').modal();
       	              }
       	          )
       	      }
       	    });
       	 }
        }
    	else
        {
    		$scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
            $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
            $scope.provinceList2 = {};
            $scope.cityList2 = {};

            //删除全国选项
            var length = $scope.provinceList.length;
            for (var i = 0; i < length; i++) {
                if ($scope.provinceList[i].provinceID == "000") {
                    $scope.provinceList.splice(i, 1); //删除下标为i的元素
                    break;
                }
            }
     
            jQuery.each($scope.provinceList, function (i, e) {
                $scope.provinceList2[e.provinceID] = e.provinceName;
            });
            $scope.cityList = $scope.mapToList($scope.cityList);
            for (var a = 0; a < $scope.cityList.length; a++) {
                jQuery.each($scope.cityList[a], function (i, e) {
                    $scope.cityList2[e.cityID] = e.cityName;
                });
            }
            console.log($scope.cityList2);
            $scope.searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
            if (sessionStorage.getItem("searchParam") && sessionStorage.getItem("searchParam") !== "null" && sessionStorage.getItem("searchParam") !== "undefind" && type == 1)
            {

                var searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
                console.log("缓存省："+searchParam.provinceID)
                if(searchParam.provinceID!=null && searchParam.provinceID!=undefined){
                    //alert(2222);
                    jQuery.each($scope.provinceList,function(i,e){
                        if([e.provinceID] == searchParam.provinceID){
                            $rootScope.$applyAsync(function () {
                                $scope.selectedProvince = e;
                                $scope.changeSelectedProvincebychaoguan(e,1);
                            });
                        }
                    });
                    // sessionStorage.setItem("searchParam",JSON.stringify(''))
                }

            }
        }
        
        

    };

    //搜索省份改变时，找到对应的市
    $scope.changeSelectedProvince = function (selectedProvince) {
        $scope.subCityList = null;
        if (selectedProvince) {
            $scope.subCityList = $scope.cityList.filter(function (a) {
                return a.parentAuthID == selectedProvince.id;
            });
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
        if (sessionStorage.getItem("searchParam") && sessionStorage.getItem("searchParam") !== "null" && sessionStorage.getItem("searchParam") !== "undefind")
        {
            var searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
            console.log("缓存省："+searchParam.cityID)
            if(searchParam.cityID!=null && searchParam.cityID!=undefined){
                jQuery.each($scope.subCityList,function(i,e){
                    if([e.fieldVal] == searchParam.cityID){
                        $rootScope.$applyAsync(function () {
                            $scope.selectedCity = e;
                        });
                    }
                });
                // sessionStorage.setItem("searchParam",JSON.stringify(''))
            }
            sessionStorage.setItem("searchParam",JSON.stringify(''))
        }
    }
    //省市联动方法
    $scope.queryProvinceAndCity = function () {
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
        $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
        console.log("查看："+JSON.stringify($scope.provinceList))
        $scope.provinceList2 = {};
        jQuery.each($scope.provinceList, function (i, e) {
            $scope.provinceList2[e.fieldVal] = e.authName;
        });
        console.log($scope.provinceList2);
        $scope.cityList2 = {};
        jQuery.each($scope.cityList, function (i, e) {
            $scope.cityList2[e.fieldVal] = e.authName;
        });
        console.log($scope.cityList2);
        if (sessionStorage.getItem("searchParam") && sessionStorage.getItem("searchParam") !== "null" && sessionStorage.getItem("searchParam") !== "undefind")
        {
            var searchParam = JSON.parse(sessionStorage.getItem("searchParam"));
            console.log("缓存省："+searchParam.provinceID)
            if(searchParam.provinceID!=null && searchParam.provinceID!=undefined){
                jQuery.each($scope.provinceList,function(i,e){
                    if([e.fieldVal] == searchParam.provinceID){

                        $rootScope.$applyAsync(function () {
                            $scope.selectedProvince = e;
                            $scope.changeSelectedProvince(e);
                        });
                    }
                });
                // sessionStorage.setItem("searchParam",JSON.stringify(''))
            }

        }
    };
    //跳转至发展量统计详情页面
    $scope.toDetail=function(item){
        /* $.cookie("enterpriseID",item.id,{path:'/'});
         $.cookie("enterpriseName",item.enterpriseName,{path:'/'});*/
        // $.cookie("searchParam",$scope.reqTemp,{path:'/'});
        sessionStorage.setItem("searchParam",JSON.stringify($scope.reqTemp));
        console.log("item的值："+JSON.stringify(item));
        // sessionStorage.setItem("item",JSON.stringify(item));
        $.cookie("item",JSON.stringify(item),{path:'/'});
        location.href='devStatInfoDetail/devStatInfoDetail.html';
    }

    //省市联动时调用，用于转化格式
    $scope.mapToList = function (map) {
        var result = [];
        jQuery.each(map, function (_, o) {
            if (o) {
                o.key = o[0].provinceID;
                result.push(o);
            }
        });
        return result;
    };
    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })

}])