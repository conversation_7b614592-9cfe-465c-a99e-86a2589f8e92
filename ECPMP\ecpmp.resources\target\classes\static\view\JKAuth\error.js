var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n"])

app.controller('defaultFrequencyController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {
        $scope.accountID = $.cookie('accountID');
        $scope.req = {};
        $scope.code = $scope.getQueryVariable('code');
        if("0001" === $scope.code){
            $scope.tip = '请通过4a管控平台访问';
            $('#myModal').modal();
            return;
        }

        if("0002" === $scope.code){
            $scope.tip = '4a管控平台凭据已失效，请重新通过4a管控平台访问';
            $('#myModal').modal();
            return;
        }
        $scope.errorMsg = $scope.getQueryVariable('errorMsg');
        if($scope.errorMsg){
            $scope.tip = '4a账号验证失败，请联系管理员处理';
            $('#myModal').modal();
            return;
        }
    };

    $scope.getQueryVariable = function (variable)
    {
        const query = window.location.search.substring(1);
        const vars = query.split("&");
        for (let i = 0; i < vars.length; i++) {
            const pair = vars[i].split("=");
            if (pair[0] == variable) {
                return pair[1];
            }
        }
        return (false);
    };


}]);
