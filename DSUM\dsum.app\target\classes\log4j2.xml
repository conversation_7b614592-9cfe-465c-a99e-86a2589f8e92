<?xml version="1.0" encoding="UTF-8"?>
<configuration status="OFF" packages="com.huawei.jaguar.dsum.commons.log">
    <properties>
        <!-- 文件输出格式 -->
        <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread] %c [%L] -| %msg%n</property>
    </properties>

    <appenders>

        <RollingFile name="STD_OUT_REDIRECTOR" filePattern="${sys:app.home}/logs/server/server.out.%i" fileName="${sys:app.home}/logs/server/server.out">
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="1024 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="DEBUG_LOG" filePattern="${sys:app.home}/logs/debug/debug.log.%i" fileName="${sys:app.home}/logs/debug/debug.log">
            <PatternLayout
                    pattern="%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%logExt|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingFile>
        <RollingFile name="SQL_LOG" filePattern="${sys:app.home}/logs/debug/debug.log.%i" fileName="${sys:app.home}/logs/debug/sql.log">
            <PatternLayout
                    pattern="%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%logExt|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingFile>

        <RollingFile name="TASK_LOG" filePattern="${sys:app.home}/logs/debug/task.log.%i" fileName="${sys:app.home}/logs/debug/task.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%traceid|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="Intface_LOG" filePattern="${sys:app.home}/logs/debug/interface.log.%i" fileName="${sys:app.home}/logs/debug/interface.log">
            <PatternLayout
                    pattern="%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}€€€%p €€€interface€€€%m %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="Feign_LOG" filePattern="${sys:app.home}/logs/debug/feign.log.%i" fileName="${sys:app.home}/logs/debug/feign.log">
            <PatternLayout
                    pattern="%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}€€€%p €€€feign€€€%m %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="100 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>

        <RollingFile name="OPERATE_LOG" filePattern="${sys:app.home}/logs/operate/operate.log.%i" fileName="${sys:app.home}/logs/operate/operate.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <RollingFile name="SERVER" filePattern="${sys:app.home}/logs/server/server.log.%i" fileName="${sys:app.home}/logs/server/server.log">
            <PatternLayout
                    pattern="%t|%d{yyyy-MM-dd HH:mm:ss.sss}{${sys:local.timezone}}|%p|%m|%exception|%l %n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
           
         <RollingFile name="COLLECTION_INTERFACE" filePattern="${sys:app.home}/data/logs/dsum/${sys:localhostname}_%d{yyyy-MM-dd__HH}_trace.%i.log" 
        fileName="${sys:app.home}/data/logs/dsum/trace_s.log" >
            <PatternLayout pattern="%m%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="500 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="100"/>
        </RollingFile>

        <Console name="CONSOLE" target="system_out">
            <PatternLayout pattern="${PATTERN}"/>
        </Console>

    </appenders>

    <loggers>
        <logger name="com.huawei.jaguar.dsum.dao.mapper" additivity="false" level="DEBUG">
            <appender-ref ref="SQL_LOG"/>
            <appenderref ref="CONSOLE"/>
            <appenderref ref="STD_OUT_REDIRECTOR"/>
        </logger>
        
        <logger name="com.huawei.jaguar.dsum" additivity="false" level="ERROR">
            <appender-ref ref="DEBUG_LOG"/>
            <Filters>
                <LogNameFliter regex=".*Mapper.*" level="debug,info" onMatch="DENY" onMismatch="ACCEPT"/>
            </Filters>
        </logger>

        <logger name="Runtime@operate" additivity="false" level="INFO">
            <appender-ref ref="OPERATE_LOG"/>
        </logger>
        
        
        <logger name="Runtime@DSUM_TASK" additivity="false" level="INFO">
            <appender-ref ref="TASK_LOG"/>
        </logger>

        <logger name="Runtime@interface" additivity="false" level="INFO">
            <appender-ref ref="Intface_LOG"/>
        </logger>

        <logger name="Runtime@feign" additivity="false" level="INFO">
            <appender-ref ref="Feign_LOG"/>
        </logger>

        <logger name="com.huawei" additivity="false" level="info">
            <appender-ref ref="STD_OUT_REDIRECTOR"/>
        </logger>
                
         <AsyncLogger name="collection.interface.log" additivity="false" level="info">
            <appender-ref ref="COLLECTION_INTERFACE"/>
        </AsyncLogger>

        <root level="info">
            <appenderref ref="STD_OUT_REDIRECTOR"/>
            <appender-ref ref="SERVER"/>
            <appender-ref ref="CONSOLE"/>
        </root>
    </loggers>

</configuration>


