<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.UserResponseContentMapper">
    <resultMap id="UserResponseWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.UserResponseContent">
        <result property="accessCode" column="accessCode" javaType="java.lang.String" />
        <result property="userPhone" column="userPhone" javaType="java.lang.String" />
        <result property="feedbackContent" column="feedbackContent" javaType="java.lang.String" />
        <result property="productCode" column="productCode" javaType="java.lang.String" />
        <result property="productName" column="productName" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="orgId" column="orgId" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.String" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
    </resultMap>
    
	<select id="findByMap" resultMap="UserResponseWrapper">
		select
		*
		from ecpm_t_user_response_content
		<trim prefix="where" prefixOverrides="and|or">
			<if test="accessCode != null">
				and accessCode=#{accessCode}
			</if>
			<if test="userPhone != null">
				and userPhone=#{userPhone}
			</if>
			<if test="productCode != null">
				and productCode=#{productCode}
			</if>
		</trim>
	</select>

	<insert id="insert">
		insert into ecpm_t_user_response_content
		(
		accessCode,
		userPhone,
		feedbackContent,
		productCode,
		productName,
		orgId,
		enterpriseID,
		createTime,
		updateTime
		)
		values
		(
		#{accessCode},
		#{userPhone},
		#{feedbackContent},
		#{productCode},
		#{productName},
		#{orgId},
		#{enterpriseID},
		now(),
		now()
		)
	</insert>
	
	<update id="update">
		update ecpm_t_user_response_content set
        	<trim suffixOverrides="," suffix="where userPhone = #{userPhone}">
            updateTime = now(),
            <if test="feedbackContent!=null and feedbackContent!=''">
                feedbackContent = #{feedbackContent},
            </if>
            <if test="productCode!=null and productCode!=''">
                productCode = #{productCode},
            </if>
            <if test="accessCode!=null and accessCode!=''">
                accessCode = #{accessCode},
            </if>
            <if test="productName!=null and productName!=''">
                productName = #{productName},
            </if>
            <if test="orgId!=null and orgId!=''">
                orgId = #{orgId},
            </if>
             <if test="enterpriseID!=null and enterpriseID!=''">
                enterpriseID = #{enterpriseID},
            </if>
            <if test="createTime!=null">
                createTime = #{createTime},
            </if>
        </trim>
	</update>
</mapper>