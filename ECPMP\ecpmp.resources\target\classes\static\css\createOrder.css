body {
    background: #F8F8F8;
    overflow: auto;
}

p {
    max-width:100%;
    word-break: break-all;
}

.time {
    position: relative;
}

.time i {
    position: absolute;
    bottom: 10px;
    right: 24px;
    top: auto;
    cursor: pointer;
}

.cooper-title {
    margin: 20px;
    color: #383838;
    font-size: 16px;
}

.cooper-tab {
    margin: 0 20px;
    background: #fff;
    border-radius: 2px;
    padding: 16px 10px 16px;
}

.red {
    color: red;
}

.cooperation-manage .form-inline {
    margin: 0 5px;
    border-radius: 4px;
    padding: 5px;
}

.input-group-addon icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(../../../../../assets/images/otherIcons20.png) no-repeat;
    vertical-align: middle;
    background-position: 0 0;
}

.form-group .control-label icon {
    color: #ff254c;
    vertical-align: sub;
    margin-right: 2px;
}

.form-group .data-time {
    display: flex;
}

.form-group .data-time .to {
    padding: 0 20px;
}

.form-group .date .form-control {
    width: 200px;
}

.order-btn {
    margin: 40px 20px;
}

.order-btn .btn {
    margin-right: 20px;
}

.form-group div {
    line-height: 34px;
    padding-top: 7px;
}

.form-group div li {
    display: inline-block;
    padding-right: 10px;
    cursor: pointer;
}

.form-group div li span {
    vertical-align: middle;
    margin-right: 4px;
}

.upload-img {
    padding: 20px 0;
}

.upload-img .images {
    position: relative;
    width: 100px;
    height: 100px;
    display: inline-block;
}

#fileList {
    display: none;
}

#fileList2 {
    display: none;
}

.upload-img .images .delete-btn {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    top: -140px;
    right: -30px;
    background: url(../../../../../assets/images/otherIcons20.png) no-repeat;
    background-position: -20px 0;
    cursor: pointer;
}

.upload-img .images .delete-btn2 {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    top: -140px;
    right: -30px;
    background: url(../../../../../assets/images/otherIcons20.png) no-repeat;
    background-position: -20px 0;
    cursor: pointer;
}

.upload-img img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    cursor: pointer;
    margin-right: 20px;
}

.upload-img .btn {
    vertical-align: bottom;
}

.panel-body {
    display: inline-block;
    padding: 0 15px 0 0;
    vertical-align: bottom;
}

.show-img {
    display: inline-block;
}

.check-btn.checked-btn.checked {
    background-position: -22px 0;
}

.cooperation-head {
    padding: 20px;
}

.cooperation-head .frist-tab {
    font-size: 16px;
}

.cooperation-head .second-tab {
    font-size: 14px;
}
