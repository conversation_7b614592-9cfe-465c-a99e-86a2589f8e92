<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.BannerMapper">
	<resultMap id="banner" type="com.huawei.jaguar.dsdp.ecpm.model.Banner">       
        <result property="ownerType" column="ownertype" javaType="java.lang.Integer" />
        <result property="ownerID" column="ownerID" javaType="java.lang.Integer" />
        <result property="bannerURL" column="bannerURL" javaType="java.lang.String" />
        <result property="playTime" column="playTime" javaType="java.lang.Integer" />
    </resultMap>
    
    <resultMap type="com.huawei.jaguar.dsdp.ecpm.dao.domain.BannerWrapper"
		id="bannerWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="ownerType" column="ownertype" javaType="java.lang.Integer" />
		<result property="ownerID" column="ownerID" javaType="java.lang.Integer" />
		<result property="bannerURL" column="actibannerURLvityID" javaType="java.lang.String" />
		<result property="playTime" column="playTime" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.String" />
	</resultMap>
    <select id="getBanner" resultMap="banner">
       	select t.ownertype, t.ownerID , t.bannerURL ,t.playTime
       		from ecpm_t_banner t 
        <trim prefix="where" prefixOverrides="and|or">
			<if test="ownerType != null">
				and t.ownertype = #{ownerType}
			</if>
			<if test="ownerIDList != null and ownerIDList.size()>0">
				and t.ownerID in
				<foreach item="ownerID" index="index" collection="ownerIDList"
					open="(" separator="," close=")">
					#{ownerID}
				</foreach>
			</if>
		</trim>
    </select>
	
	
	

	<insert id="createBanner">
		insert into ecpm_t_banner
		(
		ownertype,
		ownerID,
		bannerURL,
		playTime,
		createTime,
		updateTime,
		operatorID
		)
		values
		<foreach collection="list" item="bannerWrapper"
			separator=",">
		(
		#{bannerWrapper.ownerType},
		#{bannerWrapper.ownerID},
		#{bannerWrapper.bannerURL},
		#{bannerWrapper.playTime},
		#{bannerWrapper.createTime},
		#{bannerWrapper.updateTime},
		#{bannerWrapper.operatorID}
		)
		</foreach>
	</insert>
	
	<!-- 根据活动id删除banner -->
	<delete id="deleteBannerByActiviyID">
		delete from ecpm_t_banner where ownertype = 2 and ownerID = #{activityID}
	</delete>
</mapper>