########################################################
###server
########################################################
spring.profiles.active=dev
#spring.profiles.active=local
spring.application.name: ecp-bill
server.servlet.context-path=/bill
server.ip=127.0.0.1

########################################################
###datasource
########################################################
mybatis.mapper-locations: classpath*:mapper/mysql/*.xml

########################################################
###zk connector name
########################################################
zk.connector.name: ecpm.configerZKClient

########################################################
###redis
########################################################
#redis.server.connect=*************:17379|*************:17379|*************:17379
#redis.server.connect=**************:26750|**************:26751
#redis.server.password=
#redis.server.connectionTimeout=2000
#redis.server.maxIdle=80
#redis.server.maxWaitMillis=60000
#redis.server.maxTotal=100
#redis.server.soTimeout=2000
#redis.server.singleKeyTimeOut=100
#redis.server.mutiKeyTimeOut=200


########################################################
###\u5F69\u5370\u4E2D\u592E\u5E73\u53F0 url
########################################################
cybase.common.application.name=CY-BASE-COMMON-PARAMS

cybase.sms.application.name=colortest

cybase.content.application.name=colortest

cybase.rule.application.name=cy-core-pushrule

cybase.order.application.name=colortest

cybase.content.audit.name=colortest

cybase.number.application.name=colortest

cybase.shumei.application.name=colortest

content-contentBelongOrg-size=20

hotline-hotLinesList-length=50

ecpfep.application.url=10.124.73.14:18901

content-contentvar-size=50

management.security.enabled=false

logging.level.com.huawei.jaguar.dsdp.bill.dao.mapper: debug
logging.level.com.huawei.jaguar.dsdp.bill.dao.mapper2: debug


# Server
server.port=21005

# Spring HTTP
spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8

# Freemarker
spring.freemarker.allow-request-override=false
spring.freemarker.cache=false
spring.freemarker.check-template-location=true
spring.freemarker.charset=UTF-8
spring.freemarker.content-type=text/html; charset=utf-8
spring.freemarker.prefer-file-system-access=false
spring.freemarker.expose-request-attributes=false
spring.freemarker.expose-session-attributes=false
spring.freemarker.expose-spring-macro-helpers=false
spring.freemarker.suffix=.ftl
spring.freemarker.template-loader-path=classpath:/templates

# Spring Cloud Stream (Kafka)
spring.cloud.stream.kafka.binder.zkNodes=10.124.129.229:2181,10.124.129.230:2181,10.124.129.231:2181
spring.cloud.stream.kafka.binder.brokers=10.124.129.229:9092,10.124.129.230:9092,10.124.129.231:9092
spring.cloud.stream.default-binder=kafka
spring.cloud.stream.bindings.input.content-type=application/json

# Spring Cloud Config
spring.cloud.config.name=bill
spring.cloud.config.enabled=true
spring.cloud.config.label=master
spring.cloud.config.profile=resource,serviceConfig,subprovince.serviceConfig
spring.cloud.config.discovery.enabled=true
spring.cloud.config.discovery.service-id=CY-CONFIG-SERVER

# Eureka
eureka.client.serviceUrl.defaultZone=http://127.0.0.1:19000/eureka/
eureka.instance.prefer-ip-address=true
eureka.instance.instance-id=${spring.cloud.client.ip-address}:${server.port}

# Hystrix
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=20000

# Ribbon
ribbon.ReadTimeout=20000
ribbon.ConnectTimeout=5000
ribbon.eureka.enabled=true

# Logging
logging.config=classpath:log4j2.xml

spring.main.allow-bean-definition-overriding=true
spring.config.import=optional:configserver:

feign.circuitbreaker.enabled=true


# 或者只开放特定端点
management.endpoints.web.exposure.include=refresh,health,info