<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.AccountRoleMapper">
	<resultMap id="accountRoleMap"
		type="com.huawei.jaguar.dsum.dao.domain.AccountRoleWrapper">
		<result property="id" column="id" />
		<result property="accountID" column="accountID" />
		<result property="roleID" column="roleID" />
		<result property="operateTime" column="operateTime" />
		<result property="operatorID" column="operatorID" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
		<result property="reserved5" column="reserved5" />
		<result property="reserved6" column="reserved6" />
		<result property="reserved7" column="reserved7" />
		<result property="reserved8" column="reserved8" />
		<result property="reserved9" column="reserved9" />
		<result property="reserved10" column="reserved10" />
	</resultMap>

	<insert id="batchInsertAccountRole">
		INSERT INTO dsum_t_account_role
		(id,
		accountID,
		roleID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		VALUES
		<foreach collection="list" item="accountRoleWrapper"
			separator=",">
			(
			nextval('dsum_sequence_account_role'),
			#{accountRoleWrapper.accountID},
			#{accountRoleWrapper.roleID},
			#{accountRoleWrapper.operateTime},
			#{accountRoleWrapper.operatorID},
			#{accountRoleWrapper.extInfo},
			#{accountRoleWrapper.reserved1},
			#{accountRoleWrapper.reserved2},
			#{accountRoleWrapper.reserved3},
			#{accountRoleWrapper.reserved4},
			#{accountRoleWrapper.reserved5},
			#{accountRoleWrapper.reserved6},
			#{accountRoleWrapper.reserved7},
			#{accountRoleWrapper.reserved8},
			#{accountRoleWrapper.reserved9},
			#{accountRoleWrapper.reserved10}
			)
		</foreach>
	</insert>

	<delete id="batchDeleteByAccountID" parameterType="java.util.List">
		delete from dsum_t_account_role where accountID in
		<foreach item="accountRoleWrapper" index="index" collection="list"
			open="(" separator="," close=")">
			#{accountRoleWrapper.accountID}
		</foreach>
	</delete>

	<select id="queryAccountRoleByAccountID" resultMap="accountRoleMap">
		SELECT
		id,
		accountID,
		roleID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		dsum_t_account_role t where
		t.accountID =
		#{accountID}
	</select>

	<select id="batchQueryAccountRoleByAccountIDs" resultMap="accountRoleMap">
		SELECT
		id,
		accountID,
		roleID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		dsum_t_account_role t where
		accountID in
		<foreach item="accountID" index="index" collection="accountIDs"
			open="(" separator="," close=")">
			#{accountID}
		</foreach>
	</select>
	
	<select id="queryAccountRoleByRoleID" resultMap="accountRoleMap">
		SELECT
		id,
		accountID,
		roleID,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		dsum_t_account_role t where
		t.roleID in
		<foreach item="roleIDs" index="index" collection="list"
			open="(" separator="," close=")">
			#{roleIDs}
		</foreach>
	</select>
	
	<select id="queryRoleIDs" resultType="java.lang.Integer">
		select distinct t.roleID
		from dsum_t_account_role t
		where t.accountID in
		<foreach item="accountIDs" index="index" collection="list" open="(" separator="," close=")">
			#{accountIDs}
		</foreach>
	</select>
	
</mapper>