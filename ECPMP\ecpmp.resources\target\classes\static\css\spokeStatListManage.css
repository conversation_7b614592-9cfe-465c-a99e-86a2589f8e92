@charset "UTF-8";
.table th{
    width:12.5%;
    min-width: 100px;
}
table{
    table-layout:fixed;
}
table td{
    width:100%;
    word-break:keep-all;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}

.form-horizontal .control-label {
	padding-top: 8px;
	padding-bottom: 15px;
	margin-bottom: 0;
	text-align: right;
}
.modal-body .center {
	text-align: center;
}
.modal-footer{
    text-align: center;
}
.fontGreen{
    color: rgb(48, 147,25)
}
.fontRed{
    color:rgb(252,70,93);
}

.cooperation-nav{
    margin-bottom: 15px;
    margin-left: 20px;
}
.cooperation-manage{
    min-width: 1024px;
}
.cooperation-manage .form-inline {
    padding: 20px 0px 20px 10px;
    overflow: hidden;
}

.cooperation-manage .form-group {
    min-width: 255px;
}
.form-group input {
    margin-left: 10px;
}

.form-group label{
    height: 34px;
    margin-bottom: 0
}

.cooperation-manage .add-table .add-btn .export-iocn {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: url(../assets/images/btnIcons18.png)no-repeat;
    vertical-align: middle;
    background-position: -107px 0;
}

.coorPeration-table {
    min-width: 850px;
}
.cooperation-manage .coorPeration-table th,td{
    padding-left: 0px !important;        
}

.handle ul li icon {
    background: url(../assets/images/sideNavIcons.png)no-repeat;
}

.handle ul li icon.query-icon {
    background-position: -50px -25px;
}
.tabtn{
	display: inline-block;
	border-radius: 0.5rem;
	background-color: #FFFFFF;
	font-size: 16px;
	height: 44px;
	line-height: 44px;
	color: #c3c3c3;
	padding: 0 40px;
}
.cur-tabtn{
	color: #705de1;
	box-shadow: 0 1px 6px #e5e5e5;
}
@media (max-width: 1280px){
   .form-inline .form-control{
       width: 140px;
   }
   .cooperation-manage .form-group select{
       width: 140px;
   }
}
