<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.AccountLockMapper">

	<resultMap id="accountLockInfoMap"
		type="com.huawei.jaguar.dsum.dao.domain.AccountLockWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="accountID" column="accountID" javaType="java.lang.Integer" />
		<result property="accountName" column="accountName" javaType="java.lang.String" />
		<result property="accountType" column="accountType" javaType="java.lang.Integer" />
		<result property="lockType" column="lockType" javaType="java.lang.Integer" />
		<result property="lockExpireTime" column="lockExpireTime" javaType="java.util.Date" />
		<result property="operateTime" column="operateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="extinfo" column="extinfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
	</resultMap>

	<!-- 插入账号锁定信息 -->
	<insert id="insert">
		INSERT INTO dsum_t_account_lock
		(id,
		accountID,
		accountName,
		accountType,
		lockType,
		lockExpireTime,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		)
		VALUES
		(
		nextval('dsum_sequence_account_lock'),
		#{accountID},
		#{accountName},
		#{accountType},
		#{lockType},
		#{lockExpireTime},
		#{operateTime},
		#{operatorID},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10}
		)
	</insert>

	<!-- 根据用户名称和用户类型查询账号锁定信息 -->
	<select id="fetch" resultMap="accountLockInfoMap">
		select id,
		accountID,
		accountName,
		accountType,
		lockType,
		lockExpireTime,
		operateTime,
		operatorID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from dsum_t_account_lock
		where
		accountName = #{accountName} and accountType = #{accountType}
	</select>

	<!-- 根据用户ID更新用户密码信息 -->
	<delete id="delete">
		delete from dsum_t_account_lock
		where
        accountName = #{accountName} and accountType = #{accountType}
	</delete>
</mapper>