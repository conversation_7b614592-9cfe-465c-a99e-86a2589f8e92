var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n", "cy.uploadifyfile","service.common","ngSanitize"])
app.controller('templateListController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.CommonUtils = CommonUtils;
    $scope.init = function () {
      $scope.token = $.cookie("token");
      $scope.operatorID=$.cookie("accountID")||'';
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.isZYZQ = $.cookie('reserved10') == "111";
        //中移政企热线开关
        $scope.reserved4 = $.cookie('reserved4')!=null&&$.cookie('reserved4').indexOf("2")!=-1;
        var loginRoleType=$.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
        $scope.isProvincial = (loginRoleType=='provincial');
        
        $scope.enterpriseType=$.cookie('enterpriseType');

        $scope.choseIndex = 4;

        if ($scope.enterpriseType =='5' && $scope.isSuperManager)
        {
        	var proSupServerType = $.cookie('proSupServerType');
            $scope.proSupServerType = $.cookie('proSupServerType');
            if (proSupServerType)
            {
                var value = JSON.parse(proSupServerType);
                for (var i = 0; i < value.length; i++) {
    	            var index = value[i];
    	            if (index == 40)
    	            {
    	            	$scope.choseIndex = i;
    	            }
                }
            }
        }
        $scope.orgNameList = function (belongOrgList) {
            if (!belongOrgList) {
                return "";
            }
            var orgNameHtml = "";
            for (var i = 0; i < belongOrgList.length; i++) {
                if (belongOrgList[i].orgName) {
                    orgNameHtml = orgNameHtml + belongOrgList[i].orgName + "|";
                }
            }
            if (orgNameHtml == "") {
                return "";
            }
            orgNameHtml=orgNameHtml.slice(0,-1)
            orgNameHtml=orgNameHtml.length>512?orgNameHtml.slice(0,509)+'...' :orgNameHtml;
			//去掉最后的一位|
            return orgNameHtml;
        }
        $scope.pushTime = function (startTime, endTime) {

            var startTimeHtml = startTime.slice(0, -3);
            var endTimeHtml = endTime.slice(0, -3);

            return startTimeHtml + "~" + endTimeHtml;
        }

        $scope.deliveryDateMap = function (deliveryDate) {
            //先判断特殊场景
            if (deliveryDate == "1111111") {
                return "不限";
            }
            //返回给前台的初始字符串
            var deliveryDateHtml = "";
            for (var i = 0; i < 7; i++) {
                if (deliveryDate.charAt(i) == "1") {
                    switch (i) {
                        case 0:
                            deliveryDateHtml = deliveryDateHtml + "周一|";
                            break;
                        case 1:
                            deliveryDateHtml = deliveryDateHtml + "周二|";
                            break;
                        case 2:
                            deliveryDateHtml = deliveryDateHtml + "周三|";
                            break;
                        case 3:
                            deliveryDateHtml = deliveryDateHtml + "周四|";
                            break;
                        case 4:
                            deliveryDateHtml = deliveryDateHtml + "周五|";
                            break;
                        case 5:
                            deliveryDateHtml = deliveryDateHtml + "周六|";
                            break;
                        case 6:
                            deliveryDateHtml = deliveryDateHtml + "周日|";
                            break;
                    }
                }
            }
            //去掉最后的一位|
            return deliveryDateHtml.slice(0, -1);
        }
        $scope.approveStatusMap = {
            "1": "审核失败",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        }
        $scope.servTypeMap = {
            "1": "名片彩印",
            "2": "热线彩印",
            "3": "广告彩印"
        }
        $scope.unicomApproveStatusMap = {
        		"1": "审核失败",
                "-1": "--",
                "2": "待审核",
                "3": "审核通过",
                "4": "审核驳回"
            }
        $scope.subServTypeMap = {
        		"1": "主叫彩印",
                "2": "被叫彩印",
    			"3": "主被叫彩印",
                "4": "被叫挂机短信",
                "8": "挂机彩信"
        }
        $scope.subServChoise = [
            {
                id:"",
                name:"不限"
            },
            {
                id: 1,
                name: "主叫彩印"
            },
            {
                id: 2,
                name: "被叫彩印"
            },
            {
                id: 3,
                name: "主被叫彩印"
            },
            {
                id: 4,
                name: "被叫挂机短信"
            },
            {
            	id: 8,
            	name: "挂机彩信"
            }
        ];
        $scope.auditStatusChoise=[
            {
                id:"",
                name:"不限"
            },
            {
                id: 2,
                name: "待审核"
            },
            {
                id: 3,
                name: "审核通过"
            },
            {
                id: 4,
                name: "审核驳回"
            },
            {
                id: 1,
                name: "审核失败"
            }

        ]
        $scope.contentInfoData = [];
        $scope.enterpriseID = $.cookie('enterpriseID') || '';

        //初始化搜索条件
        $scope.initSel = {
            contentInfo:"",
            subServType:"",
            auditStatus:""
        };
        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
        enterpriseId: $scope.enterpriseID,
        fileUse: 'importContentTemplate'
        };
        $scope.errorInfo='';
        $scope.fileUrl='';
        // 上传excel  END
        $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
        if (broadData.file !== "") {
            $scope.fileName = broadData.file.name;
        } else {
            $scope.fileName = "";
        }
        $scope.uploader = broadData.uploader;
        $scope.errorInfo = broadData.errorInfo;
        $scope.fileUrl = fileUrl;
        });
        $scope.queryContentInfoList();
    };
    $scope.gotoAdd = function () {
        location.href = '../addProvinceTemplateList/addProvinceTemplate.html?operateType=add';
    }
    $scope.gotoDetail = function (item) {
    	$.cookie("parentID",item.parentID,{path:'/'});
        location.href = '../addProvinceTemplateList/addProvinceTemplate.html?contentID=' + item.contentID + '&operateType=detail';
    }
    $scope.gotoModify = function (item) {
        //审核中状态不可修改
        if(item.approveStatus==2){
            $scope.tip= "1030121000";
            $('#myModal').modal();
            return;
        }
        $.cookie("parentID",item.parentID,{path:'/'});
        location.href = '../addProvinceTemplateList/addProvinceTemplate.html?contentID=' + item.contentID + '&operateType=modify';
    }
    //关闭的弹出框
    $scope.close=function(item){
        $scope.selectedItemClose = item;
        $('#closeTemplatePop').modal();
    }
    //关闭确认按钮
    $scope.closeTemplate = function () {
        var Req = {
        "objectID": $scope.selectedItemClose.objectID,
        "objectType":1,
        "operationType":1
        };
        RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/contentservices/operationContent",
        data: JSON.stringify(Req),
        success: function (data) {
            $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '1030100000') {
                $('#closeCancel').click();
                $scope.tip = "导入成功";
                $('#myModal').modal();
            } else {
                $scope.tip = result.resultCode;
                $('#closeCancel').click();
                $('#myModal').modal();
            }
            $scope.queryContentInfoList();
            })
        },
        error: function () {
            $rootScope.$apply(function () {
            $('#closeCancel').click();
            $scope.tip = '1030120500';
            $('#myModal').modal();
            })
        }
        });
    };
     //导入模板
    $scope.importTem = function (item) {
        $scope.importContentID = item.contentID;
        $scope.contentVal=item.content;
        $('#impoTemplatePop').modal();
        $('#impoTemplatePop').on('hidden.bs.modal', function () {
        $rootScope.$apply(function () {
            $("#filePicker").find("span").text("导入文件");
            if ($scope.uploader) {
            $scope.uploader.reset();
            }
            $scope.errorInfo = "";
            $scope.fileName = "";
         })
        });
    };
    //删除内容弹窗
    $scope.deleteTempContent = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteTempContent').modal();
    };
  //删除名片内容
    $scope.delTempCardContent = function () {
        var item = $scope.selectedItemDel;
        var removeReq = {
            "contentID": item.contentID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/deleteTemplate",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1010100000') {
                        $('#deleteTempCardCancel').click();
                        $scope.queryContentInfoList();
                    } else {
                        $('#deleteTempCardCancel').click();
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteTempCardCancel').click();
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    }
    $scope.importTemplate = function () {
        var req = {
            "contentID":$scope.importContentID,
            "contentVal":$scope.contentVal,
            "path": $scope.fileUrl,
        };
        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/contentService/importTemplateVar",
          data: JSON.stringify(req),
          success: function (data) {
            $rootScope.$apply(function () {
              var result = data.result;
              if (result.resultCode == '1030100000') {
                $('#impoTemplatePop').modal("hide");
                $scope.tip = "导入成功";
                $('#myModal').modal();
              } else if (data.failedListUrl) {
                $('#impoTemplatePop').modal("hide");
                $scope.tip = data.failedNum + "条导入失败，请查看失败文件";
                $('#myModal').modal();
                var req = {
                  "param":{
                    "path":data.failedListUrl,
                    "token":$scope.token,
                    "isExport":0
                  },
                  "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
                  "method":"get"
                }
                CommonUtils.exportFile(req);
              } else {
                $scope.tip = result.resultCode;
                $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = '1030120500';
              $('#myModal').modal();
            })
          }
        });
      }
    $scope.queryContentInfoList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "contentName": $scope.initSel.contentInfo || '',
                "enterpriseID": parseInt($scope.enterpriseID),
                "servTypeList": [5],
                "contentTypeList": [2],
                "approveStatus":$scope.initSel.auditStatus,
    	        "getFrame": 0,
    	        "getSwitchState": 0,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            if($scope.initSel.subServType!=''){
                req.subServTypeList=[parseInt($scope.initSel.subServType)];
            }
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.contentInfoData = result.contentInfoList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.contentInfoData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                }
                )
            }
        });

    }
    //查询模板变量
    $scope.queryVars = function (item) {
      $scope.msisdn = '';
      $scope.selectedItem = item;
      $scope.pageInfo[1].pageSize = '10';
      $scope.queryTemplateVarContent(item);
      $('#varListPop').modal();
    };
    //改变选择框
    $scope.changeSelected = function (item) {
      if ($.inArray(item.valContentID, $scope.selectedListTemp) == -1) {
        $scope.selectedListTemp.push(item.valContentID);
        $scope.selectedList.push(item.valContentID);
      } else {
        $scope.selectedListTemp.splice($.inArray(item.valContentID, $scope.selectedListTemp), 1);
        $scope.selectedList.splice($.inArray(item.valContentID, $scope.selectedList), 1);
      }
      if ($scope.selectedListTemp.length == $scope.varListData.length) {
        $scope.allChoose = true;
      } else {
        $scope.allChoose = false;
      }
    };
    //更改全选框
    $scope.ifSelected = function () {
      angular.forEach($scope.selectedListTemp, function (itemTemp) {
        $scope.selectedList.splice($.inArray(itemTemp, $scope.selectedList), 1);
      });
      if ($scope.allChoose) {
        $scope.selectedListTemp = [];
        angular.forEach($scope.varListData, function (item) {
          if (item.approveStatus == 3 || item.approveStatus == 4|| item.approveStatus == 1) {
        	  item.checked = true;
        	  $scope.selectedList.push(item.valContentID);
        	  $scope.selectedListTemp.push(item.valContentID);
          }
        })
      } else {
        angular.forEach($scope.varListData, function (item) {
          item.checked = false;
          $scope.selectedListTemp = [];
        })

      }
    };
    $scope.varListAuditStatus = "";
    $scope.queryTemplateVarContent = function (item, condition) {
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        $scope.allChoose = false;
        //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
        if (condition == 'justPage') {
            var req = $scope.reqVar;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
        } else if (condition == 'forDelete') {
      	  var req = $scope.reqVar;
      	  if ($scope.pageInfo[1].currentPage > 1
      			  && $scope.pageInfo[1].totalPage == $scope.pageInfo[1].currentPage
      			  && $scope.deleteSize == $scope.varListData.length) {
      		$scope.pageInfo[1].currentPage -= 1;
      	  }
      	  req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
      	  req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
        } else {
            $scope.templateContentID = item.contentID;
            var req = {
    		  "templateContentID": $scope.templateContentID,
                "approveStatus": $scope.varListAuditStatus,
                "msisdn": $scope.msisdn || "",
              "pageParameter": {
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[1].pageSize),
                "isReturnTotal": "1",
              }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqVar = angular.copy(req);
        }
        RestClientUtil.ajaxRequest({
          type: 'POST',
          url: "/ecpmp/ecpmpServices/contentService/queryTemplateVarContent",
          data: JSON.stringify(req),
          success: function (result) {
            $rootScope.$apply(function () {
              var data = result.result;
              if (data.resultCode == '1010100000') {
                $scope.varListData = result.templateVarContentList;
                $scope.pageInfo[1].totalCount = parseInt(result.totalAmount) || 0;
                $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[1].pageSize)) : 1;
              } else {
                $scope.varListData = [];
                $scope.pageInfo[1].currentPage = 1;
                $scope.pageInfo[1].totalCount = 0;
                $scope.pageInfo[1].totalPage = 1;
                $scope.tip = data.resultCode;
    	        $('#myModal').modal();
              }
            })
          },
          error: function () {
            $rootScope.$apply(function () {
              $scope.tip = '1030120500';
              $('#myModal').modal();
            })
          }
        });
      }
    $scope.removeContentPop = function (item) {
  	  if (item != 'all') {
  		  $scope.oneSelect = true;
  		  $scope.deleteSelect = item;
  	  }else{
  		  $scope.oneSelect = false;
  	  };
  	  $('#deleteContentPop').modal();
    };
    //删除操作
    $scope.remove = function (item) {
      $scope.queryOri = {"contentID": $scope.templateContentID};

      $scope.selectedSize = $scope.selectedListTemp.length;
      // 成功删除的数量
      $scope.deleteSize = 0;
      
      $scope.timefn = setTimeout(function () {
			$("body").append('<div id="load-ajax" style="position: fixed;top: 0;z-index: 1200;width: 100%;height: 100%;background: #000 url(/qycy/ecpmp/assets/images/loading.gif) center center no-repeat;background-size: 3%;opacity: 0.3;">');
		}, 500)
      $("#load-ajax").add($scope.timefn);
      
      angular.forEach($scope.selectedListTemp, function (itemTemp) {
    	  $scope.deleteHotlineContent(itemTemp);
      });
      
      $('#delVarCancel').click();
    };
    $scope.deleteHotlineContent = function (id) {
      var removeReq = {
		"id": id,
		"operaterType": 1
      };
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
        data: JSON.stringify(removeReq),
        notLoad: true,
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '1030100000') {
              $scope.selectedList.splice($.inArray(id, $scope.selectedList), 1);
              $scope.deleteSize++;
            }
            $scope.selectedSize = $scope.selectedSize - 1;
            if ($scope.selectedSize == 0)
            {
            	window.clearTimeout($scope.timefn);
        		$("#load-ajax").remove();
                $scope.queryTemplateVarContent($scope.queryOri, 'forDelete');
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
        	  $scope.selectedSize = $scope.selectedSize - 1;
              if ($scope.selectedSize == 0)
              {
            	  window.clearTimeout($scope.timefn);
            	  $("#load-ajax").remove();
            	  $scope.queryTemplateVarContent($scope.queryOri, 'forDelete');
              }
          })
        }
      });
    };
}])
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])