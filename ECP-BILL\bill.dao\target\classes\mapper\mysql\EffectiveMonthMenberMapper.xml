<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper.EffectiveMonthMenberMapper">

<select id ="findByPage" resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EffectiveMonthMenberWrapper">
	select * from ecpm_t_effective_month_menber where dealMonth = #{dealMonth}
	order by dealMonth  limit #{pageNum},#{pageSize}
</select>

<select id ="getCount" resultType="java.lang.Integer">
	select count(1) from ecpm_t_effective_month_menber where dealMonth = #{dealMonth}
	order by dealMonth  
</select>

<select id ="find" resultType="com.huawei.jaguar.dsdp.bill.dao.domain.EffectiveMonthMenberTempWrapper">
		select c.enterpriseID as enterpriseID,c.enterpriseCode as enterpriseCode,c.enterpriseType as enterpriseType,
		c.enterpriseName as enterpriseName,c.servType as serviceType,c.subServType as subServiceType,
		m.msisdn as msisdn,m.reserved2 as msisdnType,o.ownerID as memberGroup,c.reserved4 as provinceID ,
		c.reserved5 as cityID,c.chargeType as chargeType ,c.pkgID as productCode,c.pkgPrice  as pkgPrice, c.ID  as contId from ecpm_t_member m 
		left join ecpm_t_member_subscribe s on s.memberID=m.ID  
		left join ecpm_t_org_rel r on  r.ID = s.memberID 
		left join ecpm_t_content_org  o on o.ownerID = r.orgID 
		left join ecpm_t_content c on c.ID=o.cyContID 
		where c.ID in (select contentID from ecpm_t_dealEnContractOrdQuotaTemp where enterpriseID  =#{enterpriseID})
		and s.status = 3;
</select>
	<insert id="insetAll">
	        INSERT INTO ecpm_t_effective_month_menber
        (ID,
        dealMonth,
        enterpriseID,
        enterpriseCode,
        enterpriseName,
        enterpriseType,
        parentEnterpriseID,
        parentEnterpriseCode,
        parentEnterpriseName,
        serviceType,
        subServiceType,
        msisdn,
        msisdnType,
        memberGroup,
        effictiveTime,
        chargeNumber,
        orderID,
        subscribeId,
        provinceID,
        cityID,
        businessID,
        productCode,
        packageCode,
        ratePlanID,
        chargeType
        )
        VALUES
        <foreach collection="list" item="temp"
                 separator=",">
            (
            null,
            #{temp.dealMonth},
            #{temp.enterpriseID},
            #{temp.enterpriseCode},
            #{temp.enterpriseName},
            #{temp.enterpriseType},
            #{temp.parentEnterpriseID},
            #{temp.parentEnterpriseCode},
            #{temp.parentEnterpriseName},
            #{temp.serviceType},
            #{temp.subServiceType},
            #{temp.msisdn},
            #{temp.msisdnType},
            #{temp.memberGroup},
            #{temp.effictiveTime},
            #{temp.chargeNumber},
            #{temp.orderID},
            #{temp.subscribeId},
            #{temp.provinceID},
            #{temp.cityID},
            #{temp.businessID},
            #{temp.productCode},
            #{temp.packageCode},
            #{temp.ratePlanID},
            #{temp.chargeType}
            )
        </foreach>
    </insert>
</mapper>