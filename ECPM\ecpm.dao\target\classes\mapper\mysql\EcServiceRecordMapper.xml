<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.EcServiceRecordMapper">
	<resultMap id="ecServiceRecordWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.EcServiceRecordWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="corpID" column="corpID" javaType="java.lang.String" />
		<result property="servType" column="servType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="prodordSkuNum" column="prodordSkuNum" javaType="java.lang.String" />
		<result property="prodistSkuNum" column="prodistSkuNum" javaType="java.lang.String" />
		<result property="operationSubType" column="operationSubType" javaType="java.lang.String" />
		<result property="timeStamp" column="timeStamp" javaType="Date" />
		<result property="effectiveTime" column="effectiveTime" javaType="Date" />
		<result property="fileTime" column="fileTime" javaType="Date" />
		<result property="contactorName" column="contactorName" javaType="java.lang.String" />
		<result property="contactorPhone" column="contactorPhone" javaType="java.lang.String" />
		<result property="contactorMail" column="contactorMail" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
		<result property="isValid" column="isValid" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.String" />
		<result property="skuNum" column="skuNum" javaType="java.lang.String" />
		<result property="count" column="count" javaType="java.lang.Integer" />
		
	</resultMap>

	<select id="queryByprodordSkuNum" resultMap="ecServiceRecordWrapper">
		SELECT ID,
		enterpriseID,
		corpID,
		servType,
		subServType,
		prodordSkuNum,
		prodistSkuNum,
		operationSubType,
		TIMESTAMP,
		effectiveTime,
		fileTime,
		contactorName,
		contactorPhone,
		contactorMail,
		createTime,
		updateTime,
		isValid,
		STATUS,
		skuNum
		FROM ecpm_t_ecService_record
		where 1=1
		<if test="enterpriseID!=null">
			and enterpriseID=#{enterpriseID}
		</if>
		<if test="corpID!=null">
			and corpID=#{corpID}
		</if>
		<if test="servType!=null">
			and servType =#{servType}
		</if>
		<if test="subServType!=null">
			and subServType =#{subServType}
		</if>
		<if test="prodordSkuNum!=null">
			and prodordSkuNum =#{prodordSkuNum}
		</if>
		<if test="prodistSkuNum!=null">
			and prodistSkuNum =#{prodistSkuNum}
		</if>
		<if test="operationSubType!=null">
			and operationSubType =#{operationSubType}
		</if>
		<if test="isValid!=null">
			and isValid =#{isValid}
		</if>
		<if test="status!=null">
			and status =#{status}
		</if>
		<if test="contactorName!=null">
			and contactorName =#{contactorName}
		</if>
		<if test="contactorPhone!=null">
			and contactorPhone =#{contactorPhone}
		</if>
	</select>

	<insert id="insertEcRecord">
	INSERT INTO
	ecpm_t_ecService_record
	(enterpriseID,
	corpID,
	servType,
	subServType,
	prodordSkuNum,
	prodistSkuNum,
	operationSubType,
	TIMESTAMP,
	effectiveTime,
	fileTime,
	contactorName,
	contactorPhone,
	contactorMail,
	createTime ,
	updateTime,
	isValid,
	STATUS,
	skuNum,
	count,
	reqMsg,
	 effStatus
	)VALUES
	(#{enterpriseID},
	#{corpID},
	#{servType},
	#{subServType},
	#{prodordSkuNum},
	#{prodistSkuNum},
	#{operationSubType},
	#{timeStamp},
	#{effectiveTime},
	#{fileTime},
	#{contactorName},
	#{contactorPhone},
	#{contactorMail},
	SYSDATE(),
	SYSDATE(),
	#{isValid},
	#{status},
	#{skuNum},
	#{count},
	#{reqMsg},
	#{effStatus}

	)
	</insert>
	<insert id="insertEcRecordNew">
		INSERT INTO
			ecpm_t_ecService_record
		(enterpriseID,
		 corpID,
		 servType,
		 subServType,
		 prodordSkuNum,
		 prodistSkuNum,
		 operationSubType,
		 TIMESTAMP,
		 effectiveTime,
		 fileTime,
		 contactorName,
		 contactorPhone,
		 contactorMail,
		 createTime ,
		 updateTime,
		 isValid,
		 STATUS,
		 skuNum,
		 count,
		 reqMsg,
		 effStatus,
		 finishTime
		)VALUES
			(#{enterpriseID},
			 #{corpID},
			 #{servType},
			 #{subServType},
			 #{prodordSkuNum},
			 #{prodistSkuNum},
			 #{operationSubType},
			 #{timeStamp},
			 #{effectiveTime},
			 #{fileTime},
			 #{contactorName},
			 #{contactorPhone},
			 #{contactorMail},
			 SYSDATE(),
			 SYSDATE(),
			 #{isValid},
			 #{status},
			 #{skuNum},
			 #{count},
			 #{reqMsg},
			 #{effStatus},
			 SYSDATE()
			)
	</insert>

	<update id="updteEcRecord">
	UPDATE ecpm_t_ecService_record 
		SET
		<trim suffixOverrides="," suffix="where prodordSkuNum = #{prodordSkuNum}">
			<if test="status !=null">
				status= #{status},
			</if>
			<if test="isValid !=null">
				isValid= #{isValid},
			</if>
			<if test="contactorMail!=null">
				contactorMail =#{contactorMail},
			</if>
			<if test="contactorName!=null">
				contactorName =#{contactorName},
			</if>
			<if test="contactorPhone!=null">
				contactorPhone =#{contactorPhone},
			</if>
			<if test="fileTime!=null">
				fileTime =#{fileTime},
			</if>
			<if test="count!=null">
				count =#{count},
			</if>
			updateTime = SYSDATE()
		</trim>
	</update>

	<update id="updteEcRecordByID">
	UPDATE ecpm_t_ecService_record 
		SET
		<trim suffixOverrides="," suffix="where id = #{id}">
			<if test="status !=null">
				status= #{status},
			</if>
			<if test="isValid !=null">
				isValid= #{isValid},
			</if>
			<if test="contactorMail!=null">
				contactorMail =#{contactorMail},
			</if>
			<if test="contactorName!=null">
				contactorName =#{contactorName},
			</if>
			<if test="contactorPhone!=null">
				contactorPhone =#{contactorPhone},
			</if>
			<if test="fileTime!=null">
				fileTime =#{fileTime},
			</if>
			<if test="count!=null">
				count =#{count},
			</if>
			updateTime = SYSDATE()
		</trim>
	</update>
	
	<update id="updteByprodistSkuNum">
	UPDATE ecpm_t_ecService_record
		SET
		<if test="isValid !=null">
				isValid= #{isValid},
			</if>
			updateTime = SYSDATE()
		where servType=#{servType}
		<if test="prodistSkuNum!=null">
		  and prodistSkuNum = #{prodistSkuNum}
         </if>
		<if test="enterpriseID!=null">
			and enterpriseID = #{enterpriseID}
		</if>


	</update>

	<select id="query" resultMap="ecServiceRecordWrapper">
		SELECT ID,
		enterpriseID,
		corpID,
		servType,
		subServType,
		prodordSkuNum,
		prodistSkuNum,
		operationSubType,
		TIMESTAMP,
		effectiveTime,
		fileTime,
		contactorName,
		contactorPhone,
		contactorMail,
		createTime,
		updateTime,
		isValid,
		STATUS,
		skuNum
		FROM ecpm_t_ecService_record
		where 1=1
		<if test="enterpriseID!=null">
			and enterpriseID=#{enterpriseID}
		</if>
		<if test="servType!=null">
			and servType =#{servType}
		</if>
		<if test="isValid!=null">
			and isValid =#{isValid}
		</if>
			and operationSubType in (1,2,3,4,5,9)
			order by createTime desc
	</select>
	
	<select id="queryEcServiceRecord" resultMap="ecServiceRecordWrapper">
		SELECT ID,
		enterpriseID,
		corpID,
		servType,
		subServType,
		prodordSkuNum,
		prodistSkuNum,
		operationSubType,
		TIMESTAMP,
		effectiveTime,
		fileTime,
		contactorName,
		contactorPhone,
		contactorMail,
		createTime,
		updateTime,
		isValid,
		STATUS,
		skuNum,
		count,
		reqMsg
		FROM ecpm_t_ecService_record
		where 1=1
		<if test="enterpriseID!=null">
			and enterpriseID=#{enterpriseID}
		</if>
		<if test="servType!=null">
			and servType =#{servType}
		</if>
		<if test="isValid!=null">
			and isValid =#{isValid}
		</if>
		<if test="operationSubType_in!=null">
			and operationSubType in (${operationSubType_in})
		</if>
		<if test="contactorPhone!=null">
			and contactorPhone =#{contactorPhone}
		</if>
		<if test="status!=null">
			and status =#{status}
		</if>
		order by createTime desc
	</select>

	<select id="queryPendingEcServiceRecord" resultMap="ecServiceRecordWrapper">
		SELECT ID,
		enterpriseID,
		corpID,
		servType,
		subServType,
		prodordSkuNum,
		prodistSkuNum,
		operationSubType,
		TIMESTAMP,
		effectiveTime,
		fileTime,
		contactorName,
		contactorPhone,
		contactorMail,
		createTime,
		updateTime,
		isValid,
		STATUS,
		skuNum,
		count,
		reqMsg
		FROM ecpm_t_ecService_record
		where 1=1
			and effStatus = 2

		<if test="status!=null">
			and status =#{status}
		</if>
		<if test="servType!=null">
			and servType =#{servType}
		</if>
		<if test="enterpriseID!=null">
			and enterpriseID =#{enterpriseID}
		</if>
		ORDER BY createTime
	</select>
	<update id="updteEcRecordEffStatus">
		UPDATE ecpm_t_ecService_record
		SET
		effStatus = #{effStatus},
		<if test="isValid !=null">
			isValid= #{isValid},
		</if>
		updateTime = SYSDATE()
		where id = #{id}


	</update>

	<update id="updteEcRecordEffStatusSuccess">
		UPDATE ecpm_t_ecService_record
		SET
		effStatus = #{effStatus},
		<if test="isValid !=null">
			isValid= #{isValid},
		</if>
		retryCount = 0,
		updateTime = SYSDATE(),
		finishTime = CASE
		WHEN finishTime IS NULL THEN SYSDATE()
		ELSE finishTime
		END
		where id = #{id}
	</update>

	<update id="updteEcRecordEffStatusFail">
		UPDATE ecpm_t_ecService_record
		SET
		effStatus = case when retryCount &gt;= #{reCount} then 4 else effStatus end,
		retryCount = retryCount + 1,
		<if test="isValid !=null">
			isValid= #{isValid},
		</if>
		updateTime = SYSDATE()
		where id = #{id}
	</update>

</mapper>