<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper2.QueryProvinceMapper">

    <resultMap id="provinceMap" type="com.huawei.jaguar.dsdp.bill.dao.domain2.ProvinceWrapper">
        <result property="provinceID" column="provinceID"/>
        <result property="provinceName" column="provinceName"/>
    </resultMap>

    <select id="queryProvinceList" resultMap="provinceMap">
        SELECT t.provinceID,t.provinceName from dsum_t_province t 
    </select>

</mapper>