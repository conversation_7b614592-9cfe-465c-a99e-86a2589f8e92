var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n"])
app.controller('orderListController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {
        $scope.isSuperManager = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isAgent = loginRoleType == 'agent';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';

        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.statusMap = {
            "1": "生效",
            "2": "失效",
            "3": "预生效"
        }
        $scope.orderListData = [];
        //下拉框(订单状态)
        $scope.orderStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "生效"
            },
            {
                id: "2",
                name: "失效"
            },
            {
                id: "3",
                name: "预生效"
            }
        ];
        //业务类型
        $scope.businessTypeChoise = {
            "1": "名片彩印",
            "2": "热线彩印",
            "3": "广告彩印",
            "4": "企业通知"
        }
        $scope.businessTypeList = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "名片彩印"
            },
            {
                id: "2",
                name: "热线彩印"
            },
            {
                id: "3",
                name: "广告彩印"
            }
            ,
            {
                id: "4",
                name: "企业通知"
            }
        ];
        //初始化搜索条件
        $scope.businessType = "";
        $scope.queryOrderList();
    };
    $scope.formatDate = function (str) {
        if (!str) {
            return '';
        }
        var newDateStr = "";
        newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
        return newDateStr;

    }


    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        language: "zh-CN",
        clearBtn: true,
        autoclose: true
    });

    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        })
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        })
    });

    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;
        $scope.startTime = "";
        $scope.endTime = "";

        if (startTime !== '') {
            $scope.startTime = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + '000000';
        }

        if (endTime !== '') {
            $scope.endTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + '235959';
        }

        if ($scope.startTime === '' && $scope.endTime === '') {
            $scope.search = false;
        }
        else if ($scope.startTime !== '' && $scope.endTime !== '') {
            $scope.search = false;
        }
        else {
            $scope.search = true;
        }
    }

    $scope.gotoAdd = function () {
        location.href = '../createQuota/createQuota.html';
    }
    $scope.gotoDetail = function (item) {
        //传入objectID，作为唯一标识
        location.href = '../queryQuotaDetail/queryQuotaDetail.html?orderCode=' + item.orderCode + '&objectID=' + item.objectID;
    }
    $scope.gotoUpdate = function (item) {
        //传入objectID，作为唯一标识
        location.href = '../updateQuota/updateQuota.html?orderCode=' + item.orderCode + '&objectID=' + item.objectID;
    }
    $scope.queryOrderList = function (condition) {
        var req = {
            "enterpriseID": $scope.subEnterpriseID,
            "servType": $scope.businessType,
            "startTime": $scope.startTime,
            "endTime": $scope.endTime,
            "orderType": 1,
            "isReturnOrderItem": 1,
            "pageParameter": {
                "pageNum": 1,
                "pageSize": $scope.pageInfo[0].pageSize,
                "isReturnTotal": "1"
            }
        };
        if (condition != 'justPage') {
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryQuotaList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.orderListData = result.orderList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil($scope.pageInfo[0].totalCount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                        var hasPX = true;
                        for (var k in $scope.orderListData) {
                            var item = $scope.orderListData[k];
                            for (var m in item.orderItemList) {
                                var innerItem = item.orderItemList[m].product;
                                if (innerItem.chargeType === 1) {
                                    hasPX = false;
                                }
                            }
                            if (hasPX) {
                                item.tcType = 1;
                            } else {
                                item.tcType = 2;
                            }
                        }
                        // $scope.orderListData = result.orderQuotaInfo || [];
                        // angular.forEach($scope.orderListData, function (item) {
                        //     $scope.px_cmcc = item.isScreenShowLimit === 0 ? '不限' : item.isScreenShowLimit === null ? '--' : item.screenShowItemNum === null ? '--' : item.screenShowItemNum;
                        //
                        //     $scope.px_cucc = item.isScreenShowLimitLT === 0 ? '不限' : item.isScreenShowLimitLT === null ? '--' : item.screenShowItemNumLT;
                        //     $scope.px_ctcc = item.isScreenShowLimitDX === 0 ? '不限' : item.isScreenShowLimitDX === null ? '--' : item.screenShowItemNumDX;
                        //     item.px = "移动(" + $scope.px_cmcc + ") | 联通(" + $scope.px_cucc + ") | 电信(" + $scope.px_ctcc + ")"
                        // });
                        // $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        // $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.orderListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.orderListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });

    }
}])
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])
app.filter('dataFormat', function() { //可以注入依赖
    return function(e) {
        const pattern = /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/;
        return e.replace(pattern, '$1/$2/$3');
    }
});