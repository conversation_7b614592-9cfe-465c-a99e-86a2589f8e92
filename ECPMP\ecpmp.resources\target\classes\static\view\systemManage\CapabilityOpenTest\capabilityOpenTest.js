 var app = angular.module("myApp", ["util.ajax", "page", "angularI18n","top.menu","service.common"])
app.controller('capabilityOpenTest', ['$scope', '$rootScope', '$timeout', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $timeout, $location, RestClientUtil,CommonUtils) {
  $scope.init = function () {
    $scope.isSuperManager = false;
    $scope.loginRoleType = $.cookie('loginRoleType');
    $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
    var keys=new Array("deliveryTestEnterpriseID","deliveryTestEnterpriseName");
    CommonUtils.getProperties(keys,function(params){
      var enterpriseID = params["deliveryTestEnterpriseID"]; 
      var enterpriseName = params["deliveryTestEnterpriseName"];
      $scope.enterpriseID   = enterpriseID;
      $scope.enterpriseName = enterpriseName;
      $scope.queryContentInfoList("",enterpriseID);
      $scope.selectNotification;
      //$scope.createHotlineContent("",enterpriseID);
      //$scope.beforeCommit("",enterpriseID);
      //$scope.createHotlineContent("",enterpriseName);
    });
    $scope.capabilityOpenTestListData = [];
    $scope.selectedList = [];
    $scope.list =[];
    $scope.list.push({
    	contentID:"",
    	instruct: "",
    	content:"",
    	replyvalid:false,
    	replyisensitive:""
    });
    $scope.showType = "";
    $scope.hotMsisdnVali = true;
    $scope.contentVali = true;
    $scope.eerorcontentVali = true;
    $scope.hotnorequired = true;
    $scope.operatorID = $.cookie('accountID');
    $scope.hotlineMsisdnAdd = '';
    $scope.hotlineMsisdnDel = '';
    $scope.hotlineList = [];
    $scope.contentBelongOrgList = [];
    $scope.notificationMethod={};
    $scope.chooseAllAddHotLine = false;
    $scope.hasChoseDelHotLine = false;
    $scope.$watch('contentBelongOrgList', function (newVal, oldVal) {
      $scope.hasChoseDelHotLine = false;
      if (newVal.length > 0) {
        for (var i in newVal) {
          if (newVal[i].checked) {
            $scope.hasChoseDelHotLine = true;
            break;
          }
        }
        var str = JSON.stringify(newVal);
        var index = str.indexOf('"checked":false');
        //同步全选按钮
        $scope.chooseAllDelHotLine = index === -1 ? true : false;
      }
    }, true)

    //彩印内容类型
    $scope.typeMap = {
      "1": "主叫彩印",
      "2": "被叫彩印",
      "3": "主被叫彩印",
      "7": "交互彩印"
    };
    //状态信息
    $scope.hotlineStatusMap = {
      "1": "审核失败",
      "2": "待审核",
      "3": "审核通过",
      "4": "审核驳回"
    };
    //通知方式
    $scope.notificationMethods = [
      {
        id: "2",
        name: "使用闪信发送"
      },
      {
        id: "4",
        name: "使用闪信发送，需要回执结果"
      },
    ];
    angular.forEach($scope.notificationMethods,function(item,index){
      $scope.notificationMethod[item.id]=item.name;
    })
    
    //通知方式
    $scope.ussdnotifyMethods = [
      {
        id: "7",
        name: "使用交互式USSD发送"
      }  
    ];
    angular.forEach($scope.ussdnotifyMethods,function(item,index){
      $scope.notificationMethod[item.id]=item.name;
    })
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      },
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      },
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];

    //下拉框(投递方式)
    $scope.subServTypeChoise = [
      {
        id: "1",
        name: "CONTENTAUDIT_CALLPX"
      },
      {
        id: "2",
        name: "CONTENTAUDIT_CALLEDPX"
      }
      
    ];
  };   
  $scope.goback = function () {
    $('.close').click();
  };

  // 新增热线内容弹窗
  $scope.addDeliveryTestContent = function () {
	$scope.errorContentId = '';
	$scope.list = [];
	$scope.list.push({
    	contentID:"",
    	instruct: "",
    	content:"",
    	replyvalid:false,
    	replyisensitive:""
    });
    $scope.operate = 'add';
    $scope.hotMsisdnDesc = '';
    $scope.errorInfo = "";
    $scope.addHotlineContentInfo = {};
    $scope.addHotlineContentInfo.content = '';
    $scope.addHotlineContentInfo.hotlineNo = '';   
    $scope.isSensitive = false;
    $scope.errorisSensitive = false;
    $scope.hotMsisdnVali = true;
    $scope.hotMsisdnExist = false;
    $scope.hotnorequired = true;
    $scope.contentVali = true;
    $scope.errorcontentVali = true;
    $scope.addHotlineContentInfo.subServType = "1";
    $scope.hotMsisdnDesc = '';
    $scope.contentDesc = '';
    $timeout(function () {
      $scope.myForm.$setPristine();
			$scope.myForm.$setUntouched();
      $('#addDeliveryTestContent').modal();
    }, 500);      
  };
  
  //ussd
  $scope.replyCheckissen = function (que) {
	  $scope.replycontentDesc = '';
	  var replyContent = "";
	  $scope.replycontentDesc = 'HOTLINE_CONTENTDESC';
	  for (var i = 0; i < $scope.list.length; i++) 
		 {
		  replyContent = $scope.list[que].content;
			if(""==replyContent || replyContent.length>62)
			{
				$scope.list[que].replyvalid = true;
				$scope.list[que].replyisensitive = '';
			}
			else
			{
				$scope.list[que].replyvalid = false;
				$scope.sensitiveChecknew(replyContent,que);
			}
			
		 }
  };
  // 编辑热线内容弹窗
  $scope.updateHotlineContent = function (item) {
	  $scope.list = [];
    //待审核状态不可修改
    if (item.approveStatus == 2) {
      $scope.tip = "1030121000";
      $('#myModal').modal();
      return;
    }
    $scope.itemContentID =item.contentID;
    $scope.operate = 'update';
    $scope.contentVali = true;
    $scope.errorcontentVali = true;
    $scope.addHotlineContentInfo = {};
    $scope.hotMsisdnVali = true;
    $scope.hotMsisdnExist = false;
    $scope.addHotlineContentInfo.subServType = item.subServType.toString();
    $scope.addHotlineContentInfo.contentID = item.contentID;
    $scope.addHotlineContentInfo.content = item.content || '';
    $scope.addHotlineContentInfo.hotlineNo = item.contentBelongOrgList[0].hotlineNo || '';
    if ($scope.addHotlineContentInfo.subServType == 7) {
    	// $scope.list = angular.copy();
        $scope.list = [];
    	for(var i = 0;i< item.subset.length;i++){
    	    if(!item.subset[i].instruct||item.subset[i].instruct!="$$ERROR"){
                $scope.list.push(item.subset[i]);
            }
        }
    	var error = item.errorContent.split("|");
    	$scope.errorContentId = error[0];
    	$scope.addHotlineContentInfo.errorInstructionRely = error[1] || '';
	}
    $scope.hotMsisdnDesc = '';
    $scope.contentDesc = '';
    $('#addDeliveryTestContent').modal();
  };
  // 删除热线内容弹窗
  $scope.deleteHotlineContent = function (item) {
    $scope.selectedItemDel = item;
    $('#deleteHotlineContent').modal();
  };
  // 新增号码弹窗
  $scope.addHotLinePop = function (item) { 
	
    $scope.selectedItem = item; 
    $scope.hotlineMsisdnAdd = "";
    $scope.addHotlineNo = "";
    if (item.subServType == 7) 
    {
    	$scope.selectedMethod = "7";
	}
    else
    {
    	$scope.selectedMethod = "1";
    }
    
    $scope.showType = item.subServType;
    $scope.itemContentID =item.contentID;
    $scope.addForm.$setPristine();
    $scope.addForm.$setUntouched();
    $('#addHotlinePop').modal();
    // $timeout(function () {
    //   $scope.addForm.$setPristine();
		// 	$scope.addForm.$setUntouched();
    //   $('#addHotlinePop').modal();
    // }, 500);  
    
  };
  
  //新增指令回复
  $scope.addMsisdn = function(){
	 if($scope.list.length<3){
	        $scope.list.push({
	        	contentID:"",
	        	instruct: "",
	        	content:"",
	        	replyvalid:false,
	        	replyisensitive:""
	        })
	 }
  };
  
  // 投递模拟弹窗
  $scope.managerDelivery = function (item) {
	//待审核状态不可修改
	    if (item.approveStatus != 3) {
	      $scope.tip = "1030121001";
	      $('#myModal').modal();
	      return;
	    }
    $scope.hotlineMsisdnDel="";
    $scope.selectedItem = item;
    $scope.pageInfo[2].pageSize='10';
    $scope.queryContentRelObjectList(item, 'search');
    $('#managerDelivery').modal();
  };
  //模拟投递  搜索热线号码
  $scope.queryContentRelObjectList = function (item, condition) {
    $scope.hasChoseDelHotLine = false;
    if (condition != 'justPage') {
      var req = {
        "contentID": item.contentBelongOrgList[0].cyContID,
        "target":$scope.hotlineMsisdnDel,
        "page": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[2].pageSize),
          "isReturnTotal": "1",
        }
      };
      $scope.pageInfo[2].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.reqTemp;
      req.page.pageNum = parseInt($scope.pageInfo[2].currentPage);
      req.page.pageSize = parseInt($scope.pageInfo[2].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/deliveryTestService/queryDeliveryTarget",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.contentBelongOrgList = result.deliveryTargetList || [];
            if($scope.contentBelongOrgList.length>0){
              for (var i in $scope.contentBelongOrgList) {
                $scope.contentBelongOrgList[i].checked = false;
              }
            }
            $scope.pageInfo[2].totalCount = parseInt(result.totalNum)|| 0;
            $scope.pageInfo[2].totalPage = $scope.pageInfo[2].totalCount !== 0 ? Math.ceil($scope.pageInfo[2].totalCount / parseInt($scope.pageInfo[2].pageSize)) : 1;
          } else {
            $scope.contentBelongOrgList = [];
            $scope.pageInfo[2].currentPage = 1;
            $scope.pageInfo[2].totalCount = 0;
            $scope.pageInfo[2].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  };
  $scope.selectAllDelHotLine = function () {
    if ($scope.chooseAllDelHotLine) {
      for (var i in $scope.contentBelongOrgList) {
        $scope.contentBelongOrgList[i].checked = true;
      }
    } else {
      for (var i in $scope.contentBelongOrgList) {
        $scope.contentBelongOrgList[i].checked = false;
      }
    }
  }
  $scope.sureDelHotLine = function (condition, item) {
    $scope.singleOrBatch = condition;
    $scope.delItem = item;
    $('#deleteHotLinePop').modal();
  }

  $scope.selectNotification = function (x) {
    $scope.selectedMethod =x;
  }
  
  //号码新增
  $scope.createHotline = function (id,selectedMethod) {
    var req = {
      "deliveryTarget":{
         "contentID":id,
         "target":$scope.addHotlineNo,
         "msgtype":selectedMethod
      }    
      
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/deliveryTestService/createDeliveryTarget",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          $scope.tip = data.resultCode;
          if (data.resultCode == '1030100000') {
            $('#addHotlinePop').modal('hide');
            $scope.tip = '添加成功';
            $('#myModal').modal();
            $scope.queryContentRelObjectList($scope.selectedItem, 'search');
          }else{
            $scope.tip = data.resultCode;
            if($scope.tip=='1030120051'){
              $scope.tip='热线内容中热线号码已关联';
            }
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#addHotlinePop').modal('hide');
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  };

  /*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
  
//彩印内容必填，最长62个字
  $scope.checkHotlineContent = function (num) {
    if (num ==1 ) {
    	$scope.contentVali = true;
    	if ($scope.addHotlineContentInfo.content) {
    	      if ($scope.addHotlineContentInfo.content.length > 62) {
    	        $scope.contentVali = false;
    	      } else {
    	        $scope.contentVali = true;
    	      }
    	    } else {
    	      $scope.sensitiveWords = [];
    	      $scope.isSensitive = false;
    	      $scope.sensitiveWordsStr = "";
    	      $scope.contentVali = false;
    	    }
    	if (!$scope.contentVali) {
    	      $scope.contentDesc = 'HOTLINE_CONTENTDESC';
    	    } else {
    	      $scope.contentDesc = "";
    	    }
	}
    else if(num ==99)
    {
    	$scope.errorcontentVali = true;
    	if ($scope.addHotlineContentInfo.errorInstructionRely) {
    	      if ($scope.addHotlineContentInfo.errorInstructionRely.length > 62) {
    	        $scope.errorcontentVali = false;
    	      } else {
    	        $scope.errorcontentVali = true;
    	      }
    	    } else {
    	      $scope.errorsensitiveWords = [];
    	      $scope.errorisSensitive = false;
    	      $scope.errorsensitiveWordsStr = "";
    	      $scope.errorcontentVali = false;
    	    }
    	if (!$scope.errorcontentVali) {
    	      $scope.errorcontentDesc = 'HOTLINE_CONTENTDESC';
    	    } else {
    	      $scope.errorcontentDesc = "";
    	    }
    }
  };
  
  //指令校验
  $scope.checkinstruction = function () {
    $scope.instructiontips = false;
    $scope.replyconentdiff = false;
    for (var i = 0; i < $scope.list.length; i++) 
	 {
    	var reg=/^[a-zA-Z0-9]{1,10}$/;
    	if (!reg.test($scope.list[i].instruct)) 
    	{
    		$scope.instructiontips = true;
		}
    	if (!$scope.list[i].content) 
    	{
    		$scope.replyconentdiff = true;
		}
	 }
  };
  //敏感词校验
  $scope.sensitiveCheck = function (num) {
	 
    $scope.checkHotlineContent(num);
    if ((!$scope.contentVali || !$scope.hotMsisdnVali) || !$scope.errorcontentVali) {
      // return;
    } else {
      $scope.sensitiveWords = [];
      $scope.isSensitive = false;
      $scope.sensitiveWordsStr = "";
      $scope.errorisSensitive = false;
      $scope.errorsensitiveWordsStr = "";
      var content = null;
      if (num == 1) {
    	  content = $scope.addHotlineContentInfo.content || '';
     }else if(num == 99)
     {
    	 content = $scope.addHotlineContentInfo.errorInstructionRely || '';
     }
      var req = {
 		     "content": content
 		  };
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
        data: JSON.stringify(req),
        success: function (result) {
          $rootScope.$apply(function () {
            var data = result.result;
            if (data.resultCode == '1030120017') {
              $scope.sensitiveWords = result.sensitiveWords || [];
              if ($scope.sensitiveWords.length > 0) {
            	 if (num == 1) {
            		 $scope.isSensitive = true;
                     $scope.sensitiveWordsStr = $scope.sensitiveWords.join('、');
                 }
            	 else if(num == 99)
                 {
            		 $scope.errorisSensitive = true;
                     $scope.errorsensitiveWordsStr = $scope.sensitiveWords.join('、');
                 }  
              } else {
            	  if (num == 1) {
            		  $scope.isSensitive = false;
                  }
             	 else if(num == 99)
                  {
             		 $scope.errorisSensitive = false;
                  } 
              }
            } else if (data.resultCode == '1030100000') {
            	if (num == 1) {
            		$scope.sensitiveWords = [];
                    $scope.isSensitive = false;
                }
           	 	else if(num == 99)
                {
           	 	$scope.sensitiveWords = [];
                $scope.errorisSensitive = false;
                }
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
            $scope.tip = '1030120500';
            $('#myModal').modal();
          }
          )
        }
      });
    }
  };
  
//敏感词校验
  $scope.sensitiveChecknew = function (content,que) {
      var sensitiveWords = [];
//      var isSensitive = false;
      var sensitiveStr = "";
      var req = {
 		     "content": content
 		  };
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
        data: JSON.stringify(req),
        success: function (result) {
          $rootScope.$apply(function () {
            var data = result.result;
            if (data.resultCode == '1030120017') {
//            	var sensitiveWords = [];
            	sensitiveWords = result.sensitiveWords || [];
              if (sensitiveWords.length > 0) {
            	 sensitiveStr = sensitiveWords.join('、');
              }
              else
              {
            	  $scope.list[que].replyisensitive = "";
              }
              if (sensitiveStr) 
			    {
			    	$scope.list[que].replyisensitive = sensitiveStr;
			    	
				}
			    else
			    {
			    	$scope.list[que].replyisensitive = '';
			    }
            } else if (data.resultCode == '1030100000') {
            	sensitiveWords = [];
            	if(que){
                    $scope.list[que].replyisensitive = '';
                }

            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
            $scope.tip = '1030120500';
            $('#myModal').modal();
          }
          )
        }
      });
     
  };

  //确定提交前敏感词校验
  $scope.beforeCommit = function (id,enterpriseID,type) {
	if (type == 1) {
		$scope.checkHotlineContent();
		if (!$scope.contentVali || !$scope.hotMsisdnVali) {
		      return;
		    }
	}
	else{
		$scope.instructiontips = false;
		$scope.checkinstruction();

		//110迭代：取消敏感词校验
/*	    var errorcheck = $scope.sensitiveCheck(99);
	    if (errorcheck) 
	    {
	    	$scope.errorisSensitive = true;
	    	$scope.errorsensitiveWordsStr = errorcheck;
	    	return;
		}*/
	    for (var i = 0; i < $scope.list.length; i++) 
		 {
			var replyContent = $scope.list[i].content;
			$scope.sensitiveChecknew(replyContent,i);
		 }
	    
		if ($scope.replyconentdiff || $scope.instructiontips) {
			return;
		}
		if (!$scope.errorcontentVali) {
			return;
		}
	}
	
    var req = {
      "content": $scope.addHotlineContentInfo.content || '',
    };

	//110迭代：取消提交时进行敏感词校验，直接进行新增内容
      $scope.createHotlineContent(id,enterpriseID);
    /*RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030120017') {
            $scope.sensitiveWords = result.sensitiveWords || [];
            if ($scope.sensitiveWords.length > 0) {
              $scope.isSensitive = true;
              $scope.sensitiveWordsStr = $scope.sensitiveWords.join('、');
            } else {
              $scope.isSensitive = false;           
              $scope.createHotlineContent(id,enterpriseID);
            }
          } else if (data.resultCode == '1030100000') {
            $scope.sensitiveWords = [];
            $scope.isSensitive = false;           
            $scope.createHotlineContent(id,enterpriseID);
          }else{
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        }
        )
      }
    });*/
  }
  //新增(编辑)热线内容
  $scope.createHotlineContent = function (id,enterpriseID) {
	 var replyContent = "";
	 var replyContentID = '';
	 for (var i = 0; i < $scope.list.length; i++) 
	 {
		replyContent = replyContent + $scope.list[i].instruct +","+$scope.list[i].content;
		
		if ("" == $scope.list[i].contentID) 
		{
			$scope.list[i].contentID = 1;
		}
		
		replyContentID = replyContentID +$scope.list[i].contentID;
		if (i < $scope.list.length - 1) {
			replyContent = replyContent + "|";
			replyContentID = replyContentID + ",";
		}
	 }
    var req = {
      "enterpriseID":parseInt($scope.enterpriseID),
      "enterpriseName":$scope.enterpriseName,
      "content": $scope.addHotlineContentInfo.content,
      "hotlineNo":$scope.addHotlineContentInfo.hotlineNo,
      "subServType":parseInt($scope.addHotlineContentInfo.subServType),
      "operatorID":parseInt($scope.operatorID),
      "replyContent":replyContent,
      "errorContent":$scope.addHotlineContentInfo.errorInstructionRely
    };
    var serviceUrl = '/ecpmp/ecpmpServices/deliveryTestService/createDeliveryTestContent';
    if ($scope.operate == 'update') {
      serviceUrl = '/ecpmp/ecpmpServices/deliveryTestService/updateDeliveryTestContent';
      req={
        "contentID":id,
        "content":$scope.addHotlineContentInfo.content,
        "replyContent":replyContent,
        "errorContent":$scope.addHotlineContentInfo.errorInstructionRely,
        "errorContentID":$scope.errorContentId,
        "replyContentID":replyContentID
      }
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: serviceUrl,
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $('#addHotlineContentCancel').click();
            $scope.queryContentInfoList("",enterpriseID);
            
          } else {
            $('#addHotlineContentCancel').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#addHotlineContentCancel').click();
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }
  //投递触发
  $scope.deliveryTrigger = function (item) {
    var req = {
      "enterpriseID": $scope.enterpriseID,
      "targetID":item.id
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/deliveryTestService/deliveryTest",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '1030100000'){
            $scope.tip = "投递成功";
            $('#myModal').modal();
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }         
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }
   //模拟投递 删除号码（单个或批量）
  $scope.singleOrBatchDelHotLine = function (condition) {
    $('#deleteHotLinePop').modal('hide');
    //单个删除热线号码
    var req = {
      "contentID":$scope.selectedItem.contentID
    };
    if (condition == 'single') {
      req.targetIDList = [$scope.delItem.id]
    } else if (condition == 'batch') {//批量添加
      req.targetIDList = [];
      for (var i in $scope.contentBelongOrgList) {
        if ($scope.contentBelongOrgList[i].checked) {
          req.targetIDList.push($scope.contentBelongOrgList[i].id)
        }
      }
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/deliveryTestService/deleteDeliveryTarget",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.tip = '删除成功';
            $('#myModal').modal();
            $scope.queryContentRelObjectList($scope.selectedItem, 'search');
          }else{
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });

  }
  //删除热线内容
  $scope.delHotlineContent = function (enterpriseID) {
    var item = $scope.selectedItemDel;
    var enterpriseID = $scope.enterpriseID;
    console.log(item);
    var removeReq = {
    "enterpriseID":parseInt($scope.enterpriseID),
      "contentID": parseInt(item.contentID)
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/deliveryTestService/deleteDeliveryTestContent",
      data: JSON.stringify(removeReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '1030100000') {
            $scope.queryContentInfoList("",enterpriseID);
            $('#deleteHotlineContentCancel').click();

          } else {
            $('#deleteHotlineContentCancel').click();
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#deleteHotlineContentCancel').click();
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }
  
//筛选内容
  $scope.getContentlist = function (list) {
	var contentlist = [];
	var sonlist = [];
    for (var int = 0; int < list.length; int++) {
		if (null == list[int].replyParentID) {
			contentlist.push(list[int]);
		}
	}
    return contentlist;
  };
  //查询热线内容列表
  $scope.queryContentInfoList = function (condition,enterpriseID) {
    if (condition !== 'justPage') {
      var contentlist = [];
      var contentNo = $scope.contentNo;
      console.log(angular.isNumber(+$scope.contentNo));
      if(contentNo!=null&&contentNo!==""){
          if(!Number.isNaN(+$scope.contentNo) && $scope.bigNumCompare($scope.contentNo,'2147483647') === 1){
              $scope.tip = "内容编号输入值非法";
              $('#myModal').modal();
              return;
          }else if(Number.isNaN(+$scope.contentNo)){
              $scope.tip = "内容编号输入值非法";
              $('#myModal').modal();
              return;
          } else {
              contentlist.push(contentNo);
          }
      }else{
        contentlist = [];
      }
      
      var req = {
        "enterpriseID":parseInt(enterpriseID),
        "contentIDList":contentlist || [],
        "contentName": $scope.content || '',        
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1",
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryHotlineContentInfoListTemp = angular.copy(req);
    } else {
      //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.queryHotlineContentInfoListTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.capabilityOpenTestListData = result.contentInfoList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = result.totalAmount !== 0 ? Math.ceil(result.totalAmount / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.capabilityOpenTestListData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.capabilityOpenTestListData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }

    $scope.bigNumCompare = function(a, b) {
        let back = 0
        let max = Math.ceil(Math.max(a.length, b.length) / 15)
        //分成多少段,从左边开始
        for (let i = max; i > 0; i--) {
            let num1 = $scope.getMidNum(a, a.length - i * 15, 15)
            let num2 = $scope.getMidNum(b, b.length - i * 15, 15)
            //15位数字相减
            let cur = num1 - num2
            if (cur < 0) {
                back = -1
                break
            } else if (cur > 0) {
                back = 1
                break
            }
        }
        return back
    }

    $scope.getMidNum = function(str, start, len) {
        if (start + len > 0) {
            return +str.substr(start < 0 ? 0 : start, start < 0 ? start + len : len)
        } else {
            return 0
        }
    }
}])
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])