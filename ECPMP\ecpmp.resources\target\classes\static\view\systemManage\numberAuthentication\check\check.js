var myApp = angular.module("myApp", [
  "pascalprecht.translate",
  "util.ajax",
  "page",
  "angularI18n",
  "service.common",
]);

// 定义号码认证详情控制器
myApp.controller("checkController", [
  "$scope",
  "$rootScope",
  "$filter",
  "$location",
  "RestClientUtil",
  "CommonUtils",
  function (
    $scope,
    $rootScope,
    $filter,
    $location,
    RestClientUtil,
    CommonUtils
  ) {
    // 初始化函数
    $scope.init = function () {
      // 初始化列表数据
      $scope.listData = [];
      // 初始化查询参数
      $scope.queryParams = {
        msisdnAuthEnterpriseId: CommonUtils.getQueryVariable("id"),
        authMsisdn: "",
        enterpriseName: "",
        showName: "",
        auditStatus: "",
        onLineState: "",
        startTime: "",
        endTime: "",
        pageParameter: {
          pageSize: 10,
          pageNum: 1,
          isReturnTotal: "1",
        },
      };
      // 初始化分页信息 - 确保先初始化这个对象
      $scope.pageInfo = [
        {
          totalPage: 1,
          totalCount: 0,
          pageSize: 10,
          currentPage: 1,
        },
      ];

      // 初始化审核状态选项
      $scope.auditStatusOptions = [
        { id: "", name: "全部" },
        { id: "1", name: "未审核" },
        { id: "2", name: "通过" },
        { id: "3", name: "驳回" },
      ];

      // 初始化上线状态选项
      $scope.onlineStatusOptions = [
        { id: "", name: "全部" },
        { id: "1", name: "未上线" },
        { id: "2", name: "已上线" },
        { id: "3", name: "已下线" },
      ];

      // 初始化审核状态映射表
      $scope.auditStatusMap = {
        1: "未审核",
        2: "通过",
        3: "驳回",
      };

      // 初始化上线状态映射表
      $scope.onlineStatusMap = {
        1: "未上线",
        2: "已上线",
        3: "已下线",
      };

      // 初始化日期选择器
      $(".input-daterange").datepicker({
        language: "zh-CN",
        autoclose: true,
        format: "yyyy-mm-dd",
        clearBtn: true,
        todayHighlight: false,
      });

      // // 设置分页组件参数
      // $("#0").attr("totalpage", $scope.pageInfo[0].totalPage);
      // $("#0").attr("currentpage", $scope.pageInfo[0].currentPage);
      // $("#0").attr("pagesize", $scope.pageInfo[0].pageSize);

      // 加载初始数据
      $scope.search();
    };

    // 获取审核状态文本
    $scope.getAuditStatusText = function (status) {
      return $scope.auditStatusMap[status] || "-";
    };

    // 获取上线状态文本
    $scope.getOnlineStatusText = function (status) {
      return $scope.onlineStatusMap[status] || "-";
    };

    // 格式化日期时间
    $scope.formatDateTime = function (dateTime) {
      if (!dateTime) return "-";
      return $filter("date")(dateTime, "yyyy-MM-dd");
    };

    // 查询数据
    $scope.search = function (isJustPage) {
      // 处理分页信息
      if (isJustPage != "justPage") {
        $scope.pageInfo[0].currentPage = 1;
      }

      // 构建查询参数
      var params = {
        msisdnAuthEnterpriseId: $scope.queryParams.msisdnAuthEnterpriseId,
        authMsisdn: $scope.queryParams.authMsisdn,
        enterpriseName: $scope.queryParams.enterpriseName,
        showName: $scope.queryParams.showName,
        auditStatus: $scope.queryParams.auditStatus,
        onLineState: $scope.queryParams.onLineState,
        startTime: $scope.queryParams.startTime,
        endTime: $scope.queryParams.endTime,
        pageParameter: {
          pageSize: $scope.pageInfo[0].pageSize,
          pageNum: $scope.pageInfo[0].currentPage,
          isReturnTotal: "1",
        },
      };

      // 发送请求获取数据
      RestClientUtil.ajaxRequest({
        type: "POST",
        url: "/ecpmp/ecpmpServices/msisdnAuthService/queryDetail",
        data: JSON.stringify(params),
        success: function (result) {
          $rootScope.$apply(function () {
            if (
              result &&
              result.result &&
              result.result.resultCode == "1030100000"
            ) {
              $scope.listData = result.msisdnAuthDetailInfos || [];

              // 设置分页信息
              $scope.pageInfo[0].totalCount = result.totalNum || 0;
              $scope.pageInfo[0].totalPage = Math.ceil(
                $scope.pageInfo[0].totalCount / $scope.pageInfo[0].pageSize
              );

              // 设置分页组件参数
              $("#0").attr("totalpage", $scope.pageInfo[0].totalPage);
              $("#0").attr("currentpage", $scope.pageInfo[0].currentPage);
              $("#0").attr("pagesize", $scope.pageInfo[0].pageSize);
            } else {
              var data = result ? result.result : null;
              $scope.tip = data ? data.resultDesc : "查询号码认证详情失败";
              $("#myModal").modal("show");

              // 清空列表数据
              $scope.listData = [];
              $scope.pageInfo[0].totalCount = 0;
              $scope.pageInfo[0].totalPage = 1;
              // 更新分页组件
              $("#0").attr("totalpage", 1);
              $("#0").attr("currentpage", 1);
              $("#0").attr("pagesize", 10);
            }
          });
        },
        error: function (xhr) {
          $rootScope.$apply(function () {
            // 根据错误状态码显示不同提示
            var errorMessage = "系统错误，请稍后重试";
            if (xhr && xhr.status) {
              if (xhr.status === 404) {
                errorMessage = "请求的资源不存在";
              } else if (xhr.status === 403) {
                errorMessage = "无权限访问该资源";
              } else if (xhr.status === 500) {
                errorMessage = "服务器内部错误";
              } else if (xhr.status === 0) {
                errorMessage = "网络连接异常，请检查网络";
              }
            }
            $scope.tip = errorMessage;
            $("#myModal").modal("show");
            // 清空列表数据
            $scope.listData = [];
          });
        },
      });
    };

    // 导出数据
    $scope.exportData = function () {
      var req = {
        param: { req: JSON.stringify($scope.queryParams) },
        url: "/qycy/ecpmp/ecpmpServices/msisdnAuthService/downDetail",
        method: "get",
      };
      if ($scope.queryParams != undefined) {
        CommonUtils.exportFile(req);
      }
    };

    // 返回列表页
    $scope.goBack = function () {
      window.location.href = "../numberAuthentication.html";
    };

    // 监听时间选择变化
    $("#start").on("changeDate", function () {
      $rootScope.$apply(function () {
        // 时间为yyyy-mm-dd 00:00:00
        $scope.queryParams.startTime = $("#start").val();
        if ($scope.queryParams.startTime) {
          $scope.queryParams.startTime =
            $scope.queryParams.startTime.substring(0, 10) + " 00:00:00";
        }
      });
    });

    $("#end").on("changeDate", function () {
      $rootScope.$apply(function () {
        // 时间为yyyy-mm-dd 23:59:59
        $scope.queryParams.endTime = $("#end").val();
        if ($scope.queryParams.endTime) {
          $scope.queryParams.endTime =
            $scope.queryParams.endTime.substring(0, 10) + " 23:59:59";
        }
      });
    });
  },
]);
