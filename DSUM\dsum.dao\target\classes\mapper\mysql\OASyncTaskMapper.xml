<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.OASyncTaskMapper">
    <resultMap id="taskMap"
               type="com.huawei.jaguar.dsum.dao.domain.OASyncTaskWrapper">
        <result property="id" column="id"/>
        <result property="dataDate" column="dataDate"/>
        <result property="syncStatus" column="syncStatus"/>

    </resultMap>


    <select id="queryOASyncTaskBydataDate" resultMap="taskMap">
		SELECT
		  id,dataDate,syncStatus
		FROM
		dsum_t_oa_sync_task
			where  dataDate = #{dataDate}
	</select>

    <insert id="insertOASyncTask" parameterType="com.huawei.jaguar.dsum.dao.domain.OASyncTaskWrapper">
		insert into dsum_t_oa_sync_task (
		dataDate,
		syncStatus
		)values(
		 #{dataDate},
		 #{syncStatus}
		)
	</insert>

    <update id="updateSyncStatus" parameterType="com.huawei.jaguar.dsum.dao.domain.OASyncTaskWrapper">
		update  dsum_t_oa_sync_task set syncStatus = #{syncStatus} where id =#{id}
	</update>
</mapper>