var app = angular.module("myApp", ["util.ajax", "page", "top.menu", "angularI18n", "cy.uploadifyfile","service.common"])
/**/
app.controller('groupListController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
  $scope.init = function () {
    $scope.groupListType = [];
    $(document).on('hidden.bs.modal', '.modal', function () {
      $('.modal:visible').length && $(document.body).addClass('modal-open');
    });
    $('#addMenbPop').on('shown.bs.modal', function () {
      var evt = document.getElementById('addMenbSelect1');
      var index =0;
      for(var i=0;i<evt.options.length;i++){
        evt.options[i].setAttribute("title", evt.options[i].text);
        if($scope.getTextWidth(evt.options[i].label) >344){
          index =$scope.getSubIndex(evt.options[i].text,i);
          evt.options[i].label = evt.options[i].text.substr(0,index)+'...';
        }
      }

      evt = document.getElementById('addMenbSelect2');
      for(var i=0;i<evt.options.length;i++){
        evt.options[i].setAttribute("title", evt.options[i].text);
        if($scope.getTextWidth(evt.options[i].label) >344){
          index =$scope.getSubIndex(evt.options[i].text,i);
          evt.options[i].label = evt.options[i].text.substr(0,index)+'...';
        }
      }
    })
    $scope.token = $.cookie("token");
    $scope.selectedList = [];
    $scope.selectedListTemp = [];
    $scope.selectedRetryListTemp = [];
    $scope.isSuperManager = false;
    $scope.errorInfo = "";
    $scope.fileUrl = "";
    $scope.desc = "必填，仅支持xlsx格式";
    var loginRoleType=$.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
    $scope.isZhike = (loginRoleType=='zhike');
    $scope.isAgent = (loginRoleType=='agent');
    $scope.isProvincial = (loginRoleType=='provincial');
    $scope.enterpriseType=$.cookie('enterpriseType');
    $scope.isZYZQ = false;

    //中移政企热线开关
    $scope.reserved4 = $.cookie('reserved4')!=null&&$.cookie('reserved4').indexOf("2")!=-1;
    $scope.provinceId = '';
    if($scope.isZhike){
    	$scope.enterpriseType ='1';
    }
    if($scope.isAgent){
    	$scope.enterpriseType ='3';
    }
    if($scope.isProvincial){
    	$scope.enterpriseType ='5';
    }

    //获取enterpriseID
    $scope.enterpriseID = parseInt($.cookie('enterpriseID'));
    if($scope.enterpriseType =='3'){
    	$scope.enterpriseID = parseInt($.cookie('subEnterpriseID'));
    }
    
    $scope.choseIndex = 4;

    if ($scope.enterpriseType =='5' && $scope.isSuperManager)
    {
    	var proSupServerType = $.cookie('proSupServerType');
        $scope.proSupServerType = $.cookie('proSupServerType');
        if (proSupServerType)
        {
            var value = JSON.parse(proSupServerType);
            for (var i = 0; i < value.length; i++) {
	            var index = value[i];
	            if (index == 40)
	            {
	            	$scope.choseIndex = i;
	            }
            }
        }
    }
	$scope.isAllow = true;
    if ($scope.isProvincial && !$scope.isSuperManager)
    {
    	var proSupType = $.cookie('proSupType');
    	$scope.isAllow = false;
    	if (proSupType)
        {
            var value = JSON.parse(proSupType);
            for (var i = 0; i < value.length; i++) {
	            var index = value[i];
	            if (index == 5)
	            {
	            	$scope.isAllow = true;
	            }
            }
        }
    }
    
    if ($scope.isAllow)
    {
        $scope.queryEnterpriseInfo();

        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
          enterpriseId: $scope.enterpriseID,
          fileUse: 'batchCreateMember'
        };
        // 上传excel  END
        $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
          if (broadData.file !== "") {
            $scope.fileName = broadData.file.name;
          } else {
            $scope.fileName = "";
          }
          $scope.uploader = broadData.uploader;
          $scope.errorInfo = broadData.errorInfo;
          $scope.fileUrl = fileUrl;
        });

        // 删除成员上传excel  END
        $scope.$on("uploadifyid2", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileName = broadData.file.name;
            } else {
                $scope.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.errorInfo = broadData.errorInfo;
            $scope.fileUrl = fileUrl;
        });

        if (!$scope.isSuperManager) {

        }
        $scope.enterpriseName = $.cookie('enterpriseName') || '180';
        $scope.accountID = $.cookie('accountID') || '1000';
        $scope.groupNameExist = false;
        $scope.memberMsisdnExist = false;
        $scope.groupNameVali = true;
        $scope.memberMsisdnVali = true;
        $scope.memberNameVali = true;
        $scope.orgSelected = 'true';
        
    	//初始化分页信息
        $scope.pageInfo = [
          {
            "totalPage": 1,
            "totalCount": 0,
            "pageSize": '10',
            "currentPage": 1
          },
          {
            "totalPage": 1,
            "totalCount": 0,
            "pageSize": '10',
            "currentPage": 1
          }
        ];
        $scope.orgTypeMap = {
          "1": "实体组织",
          "2": "虚拟组织",
          "3": "实体组织"
        };
        $scope.ListStatusMap = {
          "1": "生效",
          "2": "失效",
          "4": "预生效"
        };
        /*$scope.statusMap={
         "0":"待提交同步请求",
         "1":"提交同步请求成功",
         "2":"提交同步请求失败",
         "3":"待提交同步请求",
         "4":"提交同步请求成功",
         "5":"提交同步请求失败",
         };*/
        $scope.statusMap = {
          "0": "订购中",
          "1": "订购中",
          "2": "订购失败",
          "3": "订购成功",
          "4": "订购失败",
          "5": "订购失败",
          "8": "订购失败",
          "9": "BBOSS同步中",
          "10": "触发同步失败",
          "11": "待加入",
          "12": "待删除",
          "13": "退订失败"
        };
        $scope.groupListData = [];
        $scope.servTypeStatus = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "退订成功"
            },{
                id: 2,
                name: "退订失败"
            },{
                id: 3,
                name: "退订中"
            },{
                id: 4,
                name: "企管订购关系退订"
            },
        ];
        $scope.servTypeChannel = [
            {
                id: "",
                name: "不限"
            },
            {
                id: 1,
                name: "反向退订-企管平台"
            },{
                id: 2,
                name: "正向退订-分省"
            },{
                id: 3,
                name: "正向退订-集客大厅"
            },{
                id: 4,
                name: "正向退订-中央平台"
            },
        ];

        $scope.status = null;
        $scope.statusChoise = [
           {
               id: 0,
               name: "订购中"
           },
           {
               id: 3,
               name: "订购成功"
           },
           {
               id: 8,
               name: "订购失败"
           },
           {
               id: 9,
               name: "BBOSS同步中"
           },
           {
               id: 11,
               name: "待加入"
           },
           {
               id: 12,
               name: "待删除"
           }
        ];

        //初始化搜索条件
        $scope.initSel = {
            msisdn: "",
            startTime: "",
            endTime: "",
            orgName: "",
            unsubTimeStart:"",
            unsubTimeEnd:"",
            unsubStatus:"",
            channel:""
        };
        $scope.unsubStatusMap = {
            "1": "退订成功",
            "2": "退订失败",
            "3": "退订中",
            "4": "企管订购关系退订"
        };
        //退订渠道
        $scope.channelStatusMap = {
            "1": "反向退订-企管平台",
            "2": "正向退订-分省",
            "3": "正向退订-集客大厅",
            "4": "正向退订-中央平台"
        };

          //ServiceRule判断按钮权限
    }
    else
    {
        $('#bussinessNo').modal();
    }
    
  };

    $scope.getButtonPermissions = function (){
        var req = {
            "enterpriseID": parseInt($scope.enterpriseID),
            "servTypes": [5]
        };
        $scope.isAllowMangementMember = 2;
        $scope.isAllowMoveMember = true;
        var serverTime = CommonUtils.getServerTime();
        $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    console.log(result)
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.stopSubServerTypeList = [];
                        $scope.subServerTypeList = result.syncServiceRuleList || [];
                        var edits = document.getElementsByClassName("edit");
                        var impoMebrs = document.getElementsByClassName("impoMebr");
                        for (var i in $scope.subServerTypeList) {
                            var item = $scope.subServerTypeList[i];
                            $scope.isAllowMoveMember = !item.allowMoveMember;
                            if ($scope.nowTime >= item.effectiveTime && (!item.expiryTime||$scope.nowTime <= item.expiryTime)) {
                                if(!item.isAllowMangementMember ||item.isAllowMangementMember == 0){
                                    $scope.isAllowMangementMember = 0;
                                    break;
                                }else {
                                    $scope.isAllowMangementMember = 1;
                                }
                            }
                        }
                        $scope.allButton = false;
                        if($scope.enterpriseType ==="5" && $scope.isEcProvince === "111"){
                            if ($scope.subServerTypeList !== []) {
                                for (var i in $scope.subServerTypeList) {
                                    var item = $scope.subServerTypeList[i];
                                    if (!(((!item.expiryTime)||item.effectiveTime<$scope.nowTime< item.expiryTime) && item.status === 1)) {
                                        //置灰链接新增成员、导入成员
                                        $scope.allButton = true;
                                        break;
                                    }
                                }
                            }else {
                                //所有都置灰
                                $scope.allButton = true;
                            }
                        }
                        $scope.batchDelButton = false;
                        if($scope.enterpriseType ==="5" && $scope.isEcProvince === "111"){
                            if ($scope.subServerTypeList !== []) {
                                for (var i in $scope.subServerTypeList) {
                                    var item = $scope.subServerTypeList[i];
                                    if (!(((!item.expiryTime)||item.effectiveTime<$scope.nowTime< item.expiryTime) && item.status === 1)) {
                                        //i.	若当前企业不为杭研（当前企业不为指定外部接入子企业，只针对子企业管理）、咪咕音乐（当前企业信息enterpriseType=5，且reserved10=113，只针对分省企业管理），且同步业务规则列表中存在reserved2不为1（不允许成员操作，只针对分省企业管理），则查询结果列表新增链接
                                        $scope.batchDelButton = true;
                                        break;
                                    }
                                }
                            }else{
                                //所有都置灰
                                $scope.batchDelButton = true;
                            }
                        }
                    } else {
                        $scope.subServerTypeList = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

  $scope.queryEnterpriseInfo = function () {
    var req = {};
    req.id = $scope.enterpriseID;
    var pageParameter = {};
    pageParameter.pageNum = 1;
    pageParameter.pageSize = 1000;
    pageParameter.isReturnTotal = 1;
    req.pageParameter = angular.copy(pageParameter);
    /*查询企业列表*/
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$applyAsync(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.enterpriseName = data.enterprise.enterpriseName;
            $scope.isZYZQ = data.enterprise.reservedsEcpmp.reserved10 == "111"?true:false;
            $scope.provinceId = data.enterprise.provinceID;
            $scope.isEcProvince = data.enterprise.reservedsEcpmp.reserved10;
            $scope.queryGroupList();
            $scope.getButtonPermissions();
          } else {
            $scope.getButtonPermissions();
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $scope.getButtonPermissions();
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
  /* 从url中获取参数 */
  $scope.convertUrlToPara = function () {
    var url = angular.copy(window.location.href);
    url = url.split("?")[1];
    /*.substring(0, url.length-2)*/
    var res = {};
    if (!!url) {
      var para = url.split("&");
      var arr = [];
      var len = para.length;
      for (var i = 0; i < len; i++) {
        arr = para[i].split("=");
        res[arr[0]] = arr[1];
      }
    }
    return res;
  };
    $scope.moveMemberPop =  function (item) {
        $scope.groupListType =  [];
        angular.forEach($scope.groupListAllData, function (temp) {
            if(temp.id != $scope.orgID){
                $scope.groupListType.push(temp);
            }
        });
        if (item != 'all') {
            $scope.oneSelect = true;
            $scope.deleteSelect = item;
        }else{
            $scope.oneSelect = false;
        }
        $('#moveMemberPop').modal();
    };
    //移动操作
    $scope.move = function (item) {
        if(!$scope.selectedOrg){
            $scope.tip = '请选择分组';
            $('#myModal').modal();
            return;
        }
        if (item != 'all') {
            var removeReq = {
                "memberList": [$scope.deleteSelect],
                "sourceOrgID": $scope.orgID,
                "targetOrgId": $scope.selectedOrg.id,
                "enterpriseID": $scope.enterpriseID,
                "servType":5
            };
        } else {
            var memberInfoList = [];
            angular.forEach($scope.memberListData, function (temp) {
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if(temp.id == $scope.selectedListTemp[i]&&temp.status == 3){
                        memberInfoList.push(temp);
                    }
                }
            });
            if(memberInfoList.length===0){
                $scope.tip = '需要移动的成员数量为0';
                $('#myModal').modal();
                return;
            }
            var removeReq = {
                //批量删除只支持当前页
                //"memberIDList":$scope.selectedList,
                "memberList":memberInfoList,
                "sourceOrgID": $scope.orgID,
                "targetOrgId": $scope.selectedOrg.id,
                "enterpriseID": $scope.enterpriseID,
                "servType":5
            };

            var flag =true;
            angular.forEach($scope.memberListData, function (temp) {
                //汇总订购状态为待加入、待删除的记录列表
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if(temp.id == $scope.selectedListTemp[i]&&(temp.status == 11 || temp.status == 12) && flag){
                        $scope.tip = '1030120111';
                        $('#myModal').modal();
                        flag = false;
                        break;
                    }
                }
            });
            if(!flag)
            {
                return;
            }

        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/moveMember",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    $('#delMemCancel').click();
                    $('#moveMemberPop').modal("hide");
                    if (result.resultCode == '**********') {
                        $scope.tip = "提交成功";
                        $('#myModal').modal();
                        $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
                        var queryOri = {"id": $scope.orgID};
                        $scope.queryMemberList(queryOri);

                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#delMemCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

  $scope.removeMemberPop =  function (item) {
	  if (item != 'all') {
		  $scope.oneSelect = true;
		  $scope.deleteSelect = item;
	  }else{
		  $scope.oneSelect = false;
	  };
	  $('#deleteMemberPop').modal();
  };

  //删除操作
  $scope.remove = function (item) {
    if (item != 'all') {
      var removeReq = {
        "memberIDList": [$scope.deleteSelect.id],
        "orgID": $scope.orgID,
          "enterpriseID": $scope.enterpriseID,
          "servType":5
      };
    } else {
      var removeReq = {
        //批量删除只支持当前页
        //"memberIDList":$scope.selectedList,
        "memberIDList": $scope.selectedListTemp,
        "orgID": $scope.orgID,
        "enterpriseID": $scope.enterpriseID,
        "servType":5
      };

        var flag =true;
        angular.forEach($scope.memberListData, function (temp) {
            //汇总订购状态为待加入、待删除的记录列表
            for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                if(temp.id == $scope.selectedListTemp[i]&&(temp.status == 11 || temp.status == 12) && flag){
                    $scope.tip = '1030120111';
                    $('#myModal').modal();
                    flag = false;
                    break;
                }
            }
        });
        if(!flag)
        {
            return;
        }

    };
    var queryOri = {"id": $scope.orgID};

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/batchDeleteMember",
      data: JSON.stringify(removeReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
           $('#delMemCancel').click();
          if (result.resultCode == '**********') {
            $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
            $scope.queryMemberList(queryOri);
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
        $('#delMemCancel').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  //删除分组
  $scope.removeOrg = function () {
    var item = $scope.selectedItemDel;
    var removeReq = {
      "ids": [item.id],
      "operatorID": $scope.accountID,
        "enterpriseID": $scope.enterpriseID,
        "servType":5
    };
    item.isRemove = true;

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/deleteOrganization",
      data: JSON.stringify(removeReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            // $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
            //$scope.queryGroupList();
            $('#delOrgCancel').click();
          } else {
            $scope.tip = result.resultCode;
            $('#delOrgCancel').click();
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#delOrgCancel').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  //改变选择框
  $scope.changeSelected = function (item) {
    if ($.inArray(item.id, $scope.selectedListTemp) == -1) {
      $scope.selectedListTemp.push(item.id);
      $scope.selectedList.push(item.id);
    } else {
      $scope.selectedListTemp.splice($.inArray(item.id, $scope.selectedListTemp), 1);
      $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
    }
    if ($scope.selectedListTemp.length == $scope.memberListData.length) {
      $scope.allChoose = true;
    } else {
      $scope.allChoose = false;
    }
  };

  //更改全选框
  $scope.ifSelected = function () {
      console.log("$scope.allChoose::热线彩印省份版",$scope.allChoose)
    angular.forEach($scope.selectedListTemp, function (itemTemp) {
      $scope.selectedList.splice($.inArray(itemTemp.id, $scope.selectedList), 1);

    });
    if (!$scope.allChoose) {
      $scope.selectedListTemp = [];
      angular.forEach($scope.memberListData, function (item) {
    	if(item.status != 1 && item.status != 9 && item.status != 12 && item.status != 11){
    	  item.checked = true;
    	  $scope.selectedList.push(item.id);
    	  $scope.selectedListTemp.push(item.id);
    	} else if ((item.status == 12 || item.status == 11) && item.rspDesc =='接口响应超时'){
            item.checked = true;
            $scope.selectedList.push(item.id);
            $scope.selectedListTemp.push(item.id);
        }
      })
    } else {
      angular.forEach($scope.memberListData, function (item) {
        item.checked = false;
        $scope.selectedListTemp = [];
      })

    }
  };
  $scope.exportMember = function () {
    var req = {
        "param": {
            "orgID": $scope.orgID,
            "msisdn": $scope.msisdn,
            "containBranchOrg": 1,
            "enterpriseID": $scope.enterpriseID,
            "enterpriseType": $scope.enterpriseType,
            "token": $scope.token,
            "isExport": 1,
            "serviceType": 5
      },
      "url":"/qycy/ecpmp/ecpmpServices/organizationService/downMemberCsvFileService",
      "method":"get"
    }
    CommonUtils.exportFile(req);
  };
  $scope.exportFaildList = function () {
	    var req = {
	      "param":{
	        "orgID":$scope.orgID
	      },
	      "url":"/qycy/ecpmp/ecpmpServices/organizationService/downMemberSubFaild",
	      "method":"get"
	    }
	    CommonUtils.exportFile(req);
   };

    $scope.exportUnsubFailBatch = function (item) {
        var req = {
            "param":{"req":JSON.stringify($scope.reqTemp)},
            "url":"/qycy/ecpmp/ecpmpServices/organizationService/downUnsubRecord",
            "method":"get"
        }
        if($scope.reqTemp != undefined)
        {
            CommonUtils.exportFile(req);
        }
    };

  //展示box
  $scope.showBox = function () {
    $scope.selectedListTemp = [];
    angular.forEach($scope.memberListData, function (memberData) {
      var index = $scope.selectedList.indexOf(memberData.id);
      if (index != -1) {
        $scope.selectedListTemp.push(memberData.id);
        item.checked = true;
      }
      ;
    });
  };

  $scope.formatDate = function (str) {
    if (!str) {
      return 'format error';
    }
    var newDateStr = "";
    newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
    return newDateStr;

  };

  //新增分组弹窗
  $scope.addGroup = function () {
    $scope.groupNameExist = false;
    $scope.groupNameVali = true;
    $scope.newGroup = true;
    $scope.groupNameDesc = '';
    $scope.addGroupInfo = {};
    $scope.groupNameTemp = null;
    $scope.addGroupInfo.enterpriseID = $scope.enterpriseID;
    $scope.addGroupInfo.operatorID = $scope.accountID;
    $scope.addGroupInfo.orgType = 3;
    $('#addGroupPop').modal();
  };

  //编辑
  $scope.gotoSet = function (item) {
    $scope.groupNameExist = false;
    $scope.groupNameVali = true;
    $scope.newGroup = false;
    $scope.groupNameDesc = '';
    // $scope.selectedItem = item;
    $scope.setGroupInfo = {};
    $scope.setGroupInfo.id = item.id;
    $scope.setGroupInfo.enterpriseID = $scope.enterpriseID;
    $scope.setGroupInfo.operatorID = $scope.accountID;
    $scope.setGroupInfo.orgName = item.orgName;
    $scope.setGroupInfo.orgType = item.orgType;
    $scope.groupNameTemp = item.orgName;
    // $scope.queryMemberList(item);
    $('#setGroupPop').modal();
  };

  //删除
  $scope.gotoDelete = function (item) {
    $scope.selectedItemDel = item;
    $('#deleteGroupPop').modal();
  };

  //刷新页面
  $scope.checkResult = function (item) {
	  $scope.queryGroupList();
  };
  
  //查询单个分组
  $scope.gotoDetail = function (item) {
    $scope.msisdn = '';
    $scope.selectedItem = item;
    $scope.status = null;
    $scope.queryMemberList(item);
    $('#detailListPop').modal();
  };
  $scope.checkSelectLabelLen = function (text,offset) {
	label =text;
	length = angular.copy(label.length);
	for ( var j = 0; j < label.length; j++) {
		if (label.charAt(j) > '~' || label.charAt(j) < ' ') {
			length = length + offset;
		}
	}
	return length;
  }

  $scope.getSubIndex = function (label,i) {
	  var index =0;
	  for(var j=0;i<label.length;j++){
		var labelTemp=label.substr(0,j);
		if($scope.getTextWidth(labelTemp) >341){
			index = j-1;
			return index;
		}
	  }
	  return index;
  }

  /**
   * js获取文本显示宽度
   * @param str: 文本
   * @return 文本显示宽度
   */
  $scope.getTextWidth = function(str) {
        var w = $('body').append($('<span stlye="display:none;" id="textWidth"/>')).find('#textWidth').html(str).width();
        $('#textWidth').remove();
        return w;
  }

  //新增成员弹窗--表格外
  $scope.addMenbOutList = function () {
    $scope.groupListType =  $scope.groupListAllData;
    $scope.memberMsisdnExist = false;
    $scope.memberMsisdnVali = true;
    $scope.memberMsisdnExistCode = false;
    $scope.memberMsisdnValiCode = true;
    $scope.memberNameVali = true;
    $scope.memberMsisdnDesc = '';
    $scope.memberNameDesc = '';
    $scope.orgSelected = 'true';
    $scope.addMenbInfo = {};
    $scope.fromList = 'false';
    $scope.selectedMsisdnID = "";
    $scope.selectedMsisdnType = "";
    $scope.selectedId = "";
    $scope.checkMsisdnType = 'true';
    $scope.areaCode = "";
    $scope.addMenbInfo.operatorID = $scope.accountID;
    $('#addMenbPop').modal();
  };

  //新增成员弹窗--表格内
  $scope.addMenbInList = function (item) {
    $scope.groupListType = $scope.groupListData;
    $scope.memberMsisdnExist = false;
    $scope.memberMsisdnVali = true;
    $scope.memberMsisdnExistCode = false;
    $scope.memberMsisdnValiCode = true;
    $scope.memberNameVali = true;
    $scope.memberMsisdnDesc = '';
    $scope.memberNameDesc = '';
    $scope.orgSelected = 'true';
    $scope.addMenbInfo = {};
    $scope.addMenbInfo.selectedOrg = item;
    $scope.addMenbInfo.operatorID = $scope.accountID;
    $scope.fromList = 'true';
    $scope.selectedMsisdnID = "";
    $scope.selectedMsisdnType = "";
    $scope.selectedId = "";
    $scope.checkMsisdnType = 'true';
    $('#addMenbPop').modal();
  };

    $('#start').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });
    $('#end').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });
    $('#unsubStart').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });
    $('#unsubEnd').datetimepicker({
        format: "yyyy-mm-dd hh:ii",
        weekStart: 0,
        dateFormat: 'yyyy-mm-dd',//日期显示格式
        timeFormat: 'hh:ii',//时间显示格式
        clearBtn: true,
        language: "zh-CN",
        autoclose: true,
        minView:0,
        minuteStep: 1
    });

    $('#start').on('changeDate', function () {
        $scope.searchOn("start");
    });

    $('#end').on('changeDate', function () {
        $scope.searchOn("end");
    });
    $('#unsubStart').on('changeDate', function () {
        $scope.unsubSearchOn("unsubStart");
    });

    $('#unsubEnd').on('changeDate', function () {
        $scope.unsubSearchOn("unsubEnd");
    });

//判断搜索按钮是否置灰
    $scope.searchOn = function (val) {
        $scope.uniqueTip = "";
        $scope.checkUnique = true;
        let startTime = document.getElementById("start").value;
        let endTime = document.getElementById("end").value;
        if((startTime !== '' && endTime != '') && startTime>endTime){
            if("start" === val){
                endTime = startTime;
            }else{
                startTime = endTime;
            }
        }

        if (startTime !== '') {
            $scope.initSel.startTime = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + startTime.substring(11, 13) + startTime.substring(14, 16) +'00';
            // $scope.initSel.startTime = startTime +':00';
            $("#start").datetimepicker("setDate", new Date(startTime.substring(0, 16) + ":00") );
        }
        else {
            $scope.initSel.startTime = "";
        }

        if (endTime !== '') {
            $scope.initSel.endTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + endTime.substring(11, 13) + startTime.substring(14, 16) + '59';
            // $scope.initSel.endTime = endTime +':00';
            $("#end").datetimepicker("setDate",  new Date(endTime.substring(0, 16) + ":00") );


        }
        else {
            $scope.initSel.endTime = "";
        }

        if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }

    };
    //判断搜索按钮是否置灰 退订时间处理
    $scope.unsubSearchOn = function (val) {
        $scope.uniqueTip = "";
        $scope.checkUnique = true;
        let startTime = document.getElementById("unsubStart").value;
        let endTime = document.getElementById("unsubEnd").value;
        if((startTime !== '' && endTime != '') && startTime>endTime){
            if("unsubStart" === val){
                endTime = startTime;
            }else{
                startTime = endTime;
            }
        }

        if (startTime !== '') {
            $scope.initSel.unsubTimeStart = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + startTime.substring(11, 13) + startTime.substring(14, 16) +'00';
            // $scope.initSel.unsubTimeStart = startTime +':00';
            $("#unsubStart").datetimepicker("setDate", new Date(startTime.substring(0, 16) + ":00") );
        }
        else {
            $scope.initSel.unsubTimeStart = "";
        }

        if (endTime !== '') {
            $scope.initSel.unsubTimeEnd = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + endTime.substring(11, 13) + startTime.substring(14, 16) + '59';

            // $scope.initSel.unsubTimeEnd = endTime+':00';
            $("#unsubEnd").datetimepicker("setDate",  new Date(endTime.substring(0, 16) + ":00") );


        }
        else {
            $scope.initSel.unsubTimeEnd = "";
        }

        if ($scope.initSel.unsubTimeStart === '' && $scope.initSel.unsubTimeEnd === '') {
            $scope.initSel.search = false;
        }
        else if ($scope.initSel.unsubTimeStart !== '' && $scope.initSel.unsubTimeEnd !== '') {
            $scope.initSel.search = false;
        }
        else {
            $scope.initSel.search = true;
        }

    };


    //导出成员删除记录弹窗
    $scope.delMenbOutList = function () {
        //若日期清掉，starttime和endtime都设置为空
        $scope.initSel.startTime = "";
        $scope.initSel.endTime = "";
        $scope.initSel.msisdn = "";
        $scope.initSel.unsubTimeStart = "";
        $scope.initSel.unsubTimeEnd = "";
        $scope.initSel.unsubStatus = "";
        $scope.initSel.channel = "";

        document.getElementById("start").value = "";
        document.getElementById("end").value = "";
        document.getElementById("unsubStart").value = "";
        document.getElementById("unsubEnd").value = "";
        $scope.groupName = "";
        $scope.msisdn = '';
        $scope.isShow = false;

        $scope.status = null;

        $scope.queryDelFailMemberList();

        // 打开名为'detailDelFailListPop'的模态框
        $('#detailDelFailListPop').modal();
    };

  //导入成员
  $scope.impoMebr = function (item) {
    $scope.orgID = item.id;
    $('#impoMebrPop').modal();
    $('#impoMebrPop').on('hidden.bs.modal', function () {
      $rootScope.$apply(function () {
        $("#filePicker").find("span").text("导入文件");
        if ($scope.uploader) {
          $scope.uploader.reset();
        }
        $scope.errorInfo = "";
        $scope.fileName = "";
        $scope.fileUrl = "";
      })

    });
  };

    //导入删除成员
    $scope.impoDelMebr = function (item) {
        $scope.orgID = item.id;
        $('#impoDelMebrPop').modal();
        $('#impoDelMebrPop').on('hidden.bs.modal', function () {
            $rootScope.$apply(function () {
                $("#filePicker2").find("span").text("导入文件");
                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.errorInfo = "";
                $scope.fileName = "";
                $scope.fileUrl = "";
            })

        });
    };

  //删除
  $scope.delComp = function () {
    $('#delCompPop').modal();
  };

  $scope.importMember = function () {
    var req = {
        "enterpriseID": $scope.enterpriseID,
        "operatorID": $scope.accountID,
        "orgID": $scope.orgID,
        "path": $scope.fileUrl,
        "servType": 5
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/batchCreateMember",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $('#impoMebrPop').modal("hide");
            $scope.tip = "导入成功";
            $('#myModal').modal();
          } else if (data.failedListUrl) {
            $('#impoMebrPop').modal("hide");
            $scope.tip = data.failedNum + "条导入失败，请查看失败清单。";
            $('#importResultModel').modal();
            $scope.failedFileUrl = data.failedListUrl;
          } else {
              $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }

  //下载导入失败清单
  $scope.downloadFailFile = function () {
    var req = {
      "param":{
        "path":$scope.failedFileUrl,
        "token":$scope.token,
        "isExport":0
      },
      "url":"/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
      "method":"get"
    }
    CommonUtils.exportFile(req);
  }

    $scope.importDelMember = function () {
        var req = {
            "enterpriseID": $scope.enterpriseID,
            "operatorID": $scope.accountID,
            "orgID": $scope.orgID,
            "path": $scope.fileUrl,
            "servType": 5
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/importBatchDeleteMember",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $('#impoDelMebrPop').modal("hide");
                        $scope.tip = "导入成功";
                        $('#myModal').modal();
                    } else if (data.failedListUrl) {
                        $('#impoDelMebrPop').modal("hide");
                        $scope.tip = data.failedNum + "条导入失败，请查看失败清单。";
                        $('#importResultModel').modal();
                        $scope.failedFileUrl = data.failedListUrl;

                    } else {
                        $('#impoDelMebrPop').modal("hide");
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

  //查询分组列表
  $scope.queryGroupList = function (condition) {
    if (condition != 'justPage') {
      var req = {
        "enterpriseID": $scope.enterpriseID,
        "orgName": $scope.queryOrgName || '',
        "sortType": 2,
        "sortField": 1,
        "orgType":3,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1",
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryGroupListTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.queryGroupListTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/queryOrganizationList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$applyAsync(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.groupListData = result.organizationList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.groupListData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });

    //查询号码类型列表
    RestClientUtil.ajaxRequest({
      type: 'GET',
      url: "/ecpmp/ecpmpServices/organizationService/queryMsisdnTypeList",
      success: function (result) {
          if (result.result.resultCode == '**********') {
            //$scope.msisdnTypeList = result.msisdnTypeList || [];
            if (result.msisdnTypeList&&result.msisdnTypeList.length>3) {
            var arr = result.msisdnTypeList || [];
            
            if($scope.isZYZQ){
            	result.msisdnTypeList.splice(0,1);
            	result.msisdnTypeList.splice(0,3);
            	if(!$scope.provinceId){
                    setTimeout(function () {
                        if($scope.provinceId === "12"){
                            
                        }else {
                        	result.msisdnTypeList.splice(5,1);

                        }
                    },1500);
                }else {
                	if($scope.provinceId === "12"){
                        
                    }else {
                    	result.msisdnTypeList.splice(5,1);

                    }
                }
            	var arr1 = result.msisdnTypeList
            }
            else{
            	var arr1 = arr.splice(2,1);
            }
            $scope.msisdnTypeList = arr1;
            console.log($scope.msisdnTypeList);
            }
          } else {
            $scope.msisdnTypeList = [];
            $('#myModal').modal();
          }
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });

    var allReq = {
      "enterpriseID": $scope.enterpriseID,
      "orgName": '',
      "sortType": 2,
      "sortField": 1,
      "orgType": 3,
      "pageParameter": {
        "pageNum": 1,
        "pageSize":10000
      }
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/queryOrganizationList",
      data: JSON.stringify(allReq),
      success: function (result) {
        $rootScope.$applyAsync(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.groupListAllData = result.organizationList || [];
          } else {
            $scope.groupListAllData = [];
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  $scope.selectedType = function (id) {
    if(id){
        $scope.selectedMsisdnID = id;
         $scope.checkMsisdnType = 'true';
         $scope.memberMsisdnExist = false;
         $scope.memberMsisdnVali = true;
         $scope.memberMsisdnExistCode = false;
         $scope.memberMsisdnValiCode = true;
         $scope.areaCode = "";
         $scope.addMenbInfo.msisdn = "";
    }else{
        $scope.selectedMsisdnID = "";
        $scope.selectedMsisdnType = "";
        $scope.checkMsisdnType = 'false';

    }
  }

  //新增分组
  $scope.createGroup = function () {
    $scope.addGroupInfo.orgName;
    var req = {
      "organization": $scope.addGroupInfo,
      "servType": 5
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/createOrganization",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.queryGroupList();
            $('#addOrgCancel').click();
          } else {
            $('#addOrgCancel').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#addOrgCancel').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  //重新同步操作
  $scope.sync = function (item) {
      var syncReq = {
        "memberID": item.id,
        "orgID": $scope.orgID,
        "enterpriseID": $scope.enterpriseID,
        "servType": 5
      };
    var queryOri = {"id": $scope.orgID};

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/syncMember",
      data: JSON.stringify(syncReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.queryMemberList(queryOri);
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  //新增成员
  $scope.createMember = function () {
    $scope.checkMemberName($scope.addMenbInfo.memberName);
    $scope.checkSelect();
    if ($scope.orgSelected == 'false' || !$scope.memberNameVali) {
      return;
    }
    var orgID = $scope.addMenbInfo.selectedOrg.id;
    $scope.addMenbInfo.enterpriseID = $scope.enterpriseID;
    delete($scope.addMenbInfo.selectedOrg);
    $scope.addMenbInfo.ecpmReserveds = {};
    $scope.addMenbInfo.ecpmReserveds.reserved2 = $scope.selectedMsisdnID;  //号码类型id

    var servType = 1;
    if($scope.selectedMsisdnID && $scope.selectedMsisdnID != 1){
        servType = 5;
    }
    $scope.addMenbInfo.msisdn = $scope.areaCode + $scope.addMenbInfo.msisdn
    if ($scope.selectedMsisdnID != 5 && $scope.selectedMsisdnID != 6 && $scope.selectedMsisdnID != 7 && $scope.selectedMsisdnID != 8
    		 && $scope.selectedMsisdnID != 9 && $scope.selectedMsisdnID != 10)
    {
    	if ($scope.addMenbInfo.msisdn){
            $scope.addMenbInfo.msisdn = $scope.addMenbInfo.msisdn.replace(/\b(0+)/gi,"");
        }
    }
    
    var memberList = [];
    memberList[0] = $scope.addMenbInfo;
    var req = {
      "memberList": memberList,
      "orgID": orgID,
      "enterpriseID":$scope.enterpriseID,
      "servType": servType
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/createMember",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $('#addMemCancel').click();
            if(result.memberList && result.memberList.length>0){
            	$scope.tip = result.memberList[0].result.resultCode;
                $('#myModal').modal();
            }else{
            	$scope.tip = 'GROUP_CREATEMEMBSUCC';
                $('#myModal').modal();
            }
          }
          else if(data.resultCode == '1030199999') {
              $('#addMemCancel').click();
              $scope.tip = "新增成员失败";
              $('#myModal').modal();
          }
          else {
            $('#addMemCancel').click();
            if(result.memberList && result.memberList.length>0){
            	$scope.tip = result.memberList[0].result.resultCode;
            }else{
            	$scope.tip = data.resultCode;
            }
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#addMemCancel').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

  $scope.queryMemberList = function (item, condition) {
    $scope.selectedList = [];
    $scope.selectedListTemp = [];
    $scope.allChoose = false;
    if (condition != 'justPage') {
      $scope.orgID = item.id;
      var req = {
        "member": {
          "id": "",
          "msisdn": $scope.msisdn || "",
          "enterpriseID": $scope.enterpriseID
        },
        "orgID": item.id,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[1].pageSize),
          "isReturnTotal": "1",
        }
      };
      if($scope.enterpriseType == 5 && $scope.isZYZQ && $scope.status != null) {
    	  if($scope.status == 0) {
    		  req.member.statusList = [0,1];
    	  } else {
    		  req.member.statusList = [$scope.status];
    	  }
      }
      $scope.pageInfo[1].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.reqTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
    }
    //        $scope.memberListData=[{"memberName":"1","msisdn":"2", "status":"3","id":"a","orgID":"v"},
    //        	{"memberName":"1","msisdn":"2", "status":"3","id":"b","orgID":"v"},
    //        	{"memberName":"1","msisdn":"2", "status":"3","id":"c","orgID":"v"},
    //        	{"memberName":"1","msisdn":"2", "status":"3","id":"d","orgID":"v"}];
    //        $scope.orgID="v"
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/queryMember",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.memberListData = result.memberList;
            $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;
          } else {
            $scope.memberListData = [];
            $scope.pageInfo[1].currentPage = 1;
            $scope.pageInfo[1].totalCount = 0;
            $scope.pageInfo[1].totalPage = 1;
            $scope.tip = data.resultCode;
	        $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });

  };
    $scope.changeTimeFormtat = function (dateTimeString) {
        if(!dateTimeString) { return ''; }
        // 解析字符串为 Date 对象
        var year = parseInt(dateTimeString.substring(0, 4));
        var month = parseInt(dateTimeString.substring(4, 6)) - 1; // 月份从0开始，需要减1
        var day = parseInt(dateTimeString.substring(6, 8));
        var hours = parseInt(dateTimeString.substring(8, 10)) ;
        var minutes = parseInt(dateTimeString.substring(10, 12));
        var seconds = parseInt(dateTimeString.substring(12));
        var date = new Date(Date.UTC(year, month, day, hours, minutes, seconds));
        // 获取各个部分并格式化
        var formattedDateTime = date.toISOString().replace('T', ' ').substring(0, 19);
        return formattedDateTime;
    }
    $scope.queryDelFailMemberList = function (condition) {
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        $scope.allChoose = false;

        if (condition != 'justPage') {
            $scope.orgID = $scope.orgID;

            var req = {
                "enterpriseId": $scope.enterpriseID,
                "orgType": 3,
                "orgName": $scope.groupName,
                "orgID": $scope.orgID,
                // 创建时间开始
                "subTimeStart": $scope.initSel.startTime,
                // 创建时间结束
                "subTimeEnd": $scope.initSel.endTime,
                // 成员号码
                "msisdn":$scope.initSel.msisdn,
                // 退订时间
                "unsubTimeStart":$scope.initSel.unsubTimeStart,
                "unsubTimeEnd":$scope.initSel.unsubTimeEnd,
                // 退订状态
                "unsubStatus":$scope.initSel.unsubStatus,
                // 退订渠道
                "channel":$scope.initSel.channel,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[1].pageSize),
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[1].pageSize);
            req.pageParameter.isReturnTotal = "1";

        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/queryUnsubRecord",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.unsubFailBatchList = result.memberUnsubRecords;
                        $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[1].totalPage = $scope.pageInfo[1].totalCount !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;

                    } else {
                        $scope.unsubFailBatchList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };

  /*校验select*/
  $scope.checkSelect = function () {
    $scope.orgSelected = 'true';
    if (!$scope.addMenbInfo.selectedOrg) {
      $scope.orgSelected = 'false';
    }
  }

  /*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };

  /* 验证名称是否超长*/
  $scope.checkMemberName = function (context) {
    $scope.memberNameVali = true;
    if (context) {
      if (context.length > 256) {
        $scope.memberNameVali = false;
      } else {
        var reg = /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/;
        if (!reg.test(context)) {
          $scope.memberNameVali = false;
        } else {
          $scope.memberNameVali = true;
        }
      }
      if (!$scope.memberNameVali) {
        $scope.memberNameDesc = 'GROUP_MEMBNAMEDESC';
        return;
      }
    }
  }

  /* 验证名称唯一 */
  $scope.checkDataUnique = function (context, type, condition) {
    $scope.type = type;
    $scope.condition = condition;
    if (type == '2') {
        if ($scope.enterpriseType && $scope.enterpriseType == '5') {
        	if ($scope.selectedMsisdnID == 10)
            {
                $scope.memberMsisdnVali = $scope.validate(context, 12, /^\d{3,12}$/);

            }
        	else
            {
        		 $scope.memberMsisdnVali = $scope.validate(context, 16, /^\d{3,16}$/);
                 if($scope.selectedMsisdnID == 3||$scope.selectedMsisdnID == 9){
                     if($scope.selectedMsisdnID == 3){
                         $scope.memberMsisdnValiCode = $scope.validate($scope.areaCode, 4, /^[0-9]+[0-9]*$/);
                     }else {
                         $scope.memberMsisdnValiCode = $scope.validate($scope.areaCode, 5, /^[0-9]+[0-9]*$/);
                     }
                     if (!$scope.memberMsisdnValiCode) {
                 		$scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE_CODE';
                 		if($scope.selectedMsisdnID == 9){
                             $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE_CODE2';

                         }
                 		return;
                 	}
                 	$scope.memberMsisdnVali = $scope.validate(context, 8, /^\d{7,8}$/);
                 	if (!$scope.memberMsisdnVali) {
               		  $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE_TEL';
               		  return;
               	  	}
                 }
            }
           
        }else{
            $scope.memberMsisdnVali = $scope.validate(context, 11, /^[0-9]{11}$/);
        }

      if (!$scope.memberMsisdnVali && $scope.enterpriseType == '5') {
    	  if ($scope.selectedMsisdnID == 10)
          {
    		  $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE_YUN';
  		      return;
          }
    	  if($scope.selectedMsisdnID != 3){
		    $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE';
		    return;
    	  }
    	  else{
    		  $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE_CODE';
    		return;
    	  }
      }else if(!$scope.memberMsisdnVali){
        $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC';
        return;
      }
    } 
    else {
      if(!$scope.newGroup&&$scope.groupNameTemp ==$scope.setGroupInfo.orgName){
    	  return;
      }
      $scope.groupNameVali = $scope.validate(context, 30, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/);
      if (!$scope.groupNameVali) {
        $scope.groupNameDesc = 'GROUP_GROUPNAMEDESC';
        return;
      }
    }
    var checkDataUniqueReq = {};
    // checkDataUniqueReq.serviceType = type;
    type == '4' ? checkDataUniqueReq.serviceType = '3' : checkDataUniqueReq.serviceType = type;
    if (type == '3' || type == '4') {
      checkDataUniqueReq.content = $scope.enterpriseID + ":" + context;
      checkDataUniqueReq.orgType = 3;
    } else {
      checkDataUniqueReq.enterpriseID = $scope.enterpriseID;
      checkDataUniqueReq.content = context;
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
      data: JSON.stringify(checkDataUniqueReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          $scope.groupNameExist = false;
          $scope.memberMsisdnExist = false;
          $scope.memberMsisdnExistCode = false;
          $scope.groupNameDesc = '';
          $scope.memberMsisdnDesc = '';
          //是否存在重复的用户名称
          if (result.resultCode == '1030120018') {
            if ($scope.type == '2') {
              $scope.memberMsisdnExist = true;
              $scope.memberMsisdnExistCode = true;
              $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNEXIST';
            }
            if ($scope.type == '3' || $scope.type == '4') {
              $scope.groupNameExist = true;
              $scope.groupNameDesc = 'GROUP_ORGNAMEEXIST';
            }
          }
          if (result.resultCode == '**********' && $scope.condition == 'create') {
            $scope.condition = '';
            if ($scope.type == '2') {
              $scope.createMember();
            }
            if ($scope.type == '3') {
              $scope.createGroup();
            }
            if ($scope.type == '4') {
              console.log(4);
              $scope.changeGroup();
            }
          }
          if (result.resultCode != '1030120018' && result.resultCode != '**********') {
            if ($scope.type == '2') {
              $('#addMemCancel').click();
            } else {
              $('#addOrgCancel').click();
            }
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          if ($scope.type == '2') {
            $('#addMemCancel').click();
          } else {
            $('#addOrgCancel').click();
          }
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };
  //编辑分组
  $scope.changeGroup = function () {
    var req = {
      "organization": $scope.setGroupInfo,
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/updateOrganization",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.queryGroupList();
            $('#setOrgCancel').click();
          } else {
            $('#setOrgCancel').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#setOrgCancel').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };
  console.log("success:"+111);
  /* 删除成员 */
  $scope.delDataUnique = function (context, type, condition) {
    $scope.type = type;
    $scope.condition = condition;
    if (type == '2') {
        if ($scope.enterpriseType && $scope.enterpriseType == '5') {
            $scope.memberMsisdnVali = $scope.validate(context, 16, /^\d{3,16}$/);
        }else{
            $scope.memberMsisdnVali = $scope.validate(context, 11, /^[0-9]{11}$/);
        }

      if (!$scope.memberMsisdnVali && $scope.enterpriseType == '5') {
        $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC_PROVINCE';
        return;
      }else if(!$scope.memberMsisdnVali){
        $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNDESC';
        return;
      }
    } else {
      $scope.groupNameVali = $scope.validate(context, 30, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/);
      if (!$scope.groupNameVali) {
        $scope.groupNameDesc = 'GROUP_GROUPNAMEDESC';
        return;
      }
    }
    var checkDataUniqueReq = {};
    checkDataUniqueReq.serviceType = type;
    if (type == '3') {
      checkDataUniqueReq.content = $scope.enterpriseID + ":" + context;
    } else {
      checkDataUniqueReq.content = context;
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
      data: JSON.stringify(checkDataUniqueReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          $scope.groupNameExist = false;
          $scope.memberMsisdnExist = false;
          $scope.groupNameDesc = '';
          $scope.memberMsisdnDesc = '';
          //是否存在重复的用户名称
          if (result.resultCode == '1030120018') {
            if ($scope.type == '2') {
              $scope.memberMsisdnExist = true;
              $scope.memberMsisdnExistCode = true;
              $scope.memberMsisdnDesc = 'GROUP_MEMBMSISDNEXIST';
            }
            if ($scope.type == '3') {
              $scope.groupNameExist = true;
              $scope.memberMsisdnExistCode = true;
              $scope.groupNameDesc = 'GROUP_ORGNAMEEXIST';
            }
          }
          if (result.resultCode == '**********' && $scope.condition == 'create') {
            $scope.condition = '';
            if ($scope.type == '2') {
              $scope.createMember();
            }
            if ($scope.type == '3') {
              $scope.createGroup();
            }
          }
          if (result.resultCode != '1030120018' && result.resultCode != '**********') {
            if ($scope.type == '2') {
              $('#addMemCancel').click();
            } else {
              $('#addOrgCancel').click();
            }
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          if ($scope.type == '2') {
            $('#addMemCancel').click();
          } else {
            $('#addOrgCancel').click();
          }
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };

    $scope.bbsync = function (item) {
        var syncReq = {
            "orgID": $scope.orgID,
            "enterpriseID": $scope.enterpriseID,
            "servType": 1,
            "retryReason": 1
        };
        if(item == 'all') {
            var flag = true;
            var memberIDsList = [];
            var selectedRetryListTemp = [];
            angular.forEach($scope.memberListData, function (temp) {
                //汇总订购状态为待加入、待删除的记录列表
                for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                    if (temp.id == $scope.selectedListTemp[i]&& (temp.status == 11 || temp.status == 12)){
                        selectedRetryListTemp.push($scope.selectedListTemp[i]);
                    } else if (temp.id == $scope.selectedListTemp[i]) {
                        memberIDsList.push($scope.selectedListTemp[i]);
                    }
                    if(temp.id == $scope.selectedListTemp[i]&&(temp.status != 8 && temp.status != 11 && temp.status != 12) && flag){
                        if (temp.status != 8) {
                            $scope.tip = '1030120107';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        } else if (temp.status != 11 && temp.status != 12) {
                            $scope.tip = '1030120111';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        } else {
                            $scope.tip = '1030120107';
                            $('#myModal').modal();
                            flag = false;
                            break;
                        }
                    }
                }
            });
            if(!flag)
            {
                return;
            }
            syncReq.memberIDs = selectedRetryListTemp;
        } else {
            syncReq.memberID = item.id;
        }
        var queryOri = {"id": $scope.orgID};
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/organizationService/retrySyncMember",
            data: JSON.stringify(syncReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.queryMemberList(queryOri);
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
  
//bboss重新同步操作
  $scope.bsync = function (item) {
      var syncReq = {
        "orgID": $scope.orgID,
        "enterpriseID": $scope.enterpriseID,
        "servType": 5
      };
	  if(item == 'all') {
		  var flag = true;
          var memberIDsList = [];
		  angular.forEach($scope.memberListData, function (temp) {
              //汇总订购状态为待加入、待删除的记录列表
			  for(var i = 0;i<$scope.selectedListTemp.length;i++ ){
                  if (temp.id == $scope.selectedListTemp[i]&& (temp.status == 11 || temp.status == 12)){
                      $scope.selectedRetryListTemp.push($scope.selectedListTemp[i]);
                  } else if (temp.id == $scope.selectedListTemp[i]) {
                      memberIDsList.push($scope.selectedListTemp[i]);
                  }
                  if(temp.id == $scope.selectedListTemp[i]&&(temp.status != 8 && temp.status != 11 && temp.status != 12) && flag){
                      if (temp.status != 8) {
                          $scope.tip = '1030120107';
                          $('#myModal').modal();
                          flag = false;
                          break;
                      } else if (temp.status != 11 && temp.status != 12) {
                          $scope.tip = '1030120111';
                          $('#myModal').modal();
                          flag = false;
                          break;
                      } else {
                          $scope.tip = '1030120107';
                          $('#myModal').modal();
                          flag = false;
                          break;
                      }
                  }
			  }
          });
		  if(!flag)
		  {
			  return;
		  }
          syncReq.memberIDs = memberIDsList;
	  } else {
		  syncReq.memberID = item.id;
	  }
      if ($scope.selectedRetryListTemp != null && $scope.selectedRetryListTemp.length > 0){
          $scope.bbsync('all');
      }
    var queryOri = {"id": $scope.orgID};

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/organizationService/retrySyncMember",
      data: JSON.stringify(syncReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $scope.queryMemberList(queryOri);
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  };
}])


app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])