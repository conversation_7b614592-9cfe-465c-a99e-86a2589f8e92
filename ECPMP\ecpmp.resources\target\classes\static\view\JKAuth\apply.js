var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n"])

app.controller('defaultFrequencyController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {
        $scope.accountID = $.cookie('accountID');
        const loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');

        $scope.req = {};
        //下拉框 24小时
        $scope.periodTypeList = [];
        for (let i = 1; i <= 24; i++) {
            $scope.periodTypeList.push({
                id: i,
                name: i
            })
        }

        $scope.errorMsg = $scope.getQueryVariable('errorMsg');
        if($scope.errorMsg){
            $scope.tip = '4a账号验证失败，请联系管理员处理';
            $('#myModal').modal();
            return;
        }

        $scope.req.duration = 1;
        $scope.requestUrl = $scope.getQueryVariable('requestUrl');

        $scope.approver = $scope.getQueryVariable('approver');
        $scope.approverList = [];
        $scope.approverList.push({
            id:"",
            name:"请选择",
            tel:"1212"
        });
        if($scope.approver){
            let approvers = JSON.parse(decodeURIComponent($scope.approver));
            for(let i = 0;i<approvers.length;i++){
                let o = approvers[i].split("G|T");
                if(o.length>2){
                    $scope.approverList.push({
                        id:o[0],
                        name:o[1],
                        tel:o[2]
                    })
                }

            }

        }
        if($scope.approverList.length>0){
            $scope.req.selectedApprover = $scope.approverList[0].id;
        }

    };

    $scope.getQueryVariable = function (variable)
    {
        const query = window.location.search.substring(1);
        const vars = query.split("&");
        for (let i = 0; i < vars.length; i++) {
            const pair = vars[i].split("=");
            if (pair[0] == variable) {
                return pair[1];
            }
        }
        return (false);
    };
    $scope.save = function () {
        $scope.req.accountID = $scope.accountID;
        $scope.req.requestUrl = $scope.requestUrl;
        if($scope.approverList.length>0){
            for(let i = 0 ;i<$scope.approverList.length;i++){
                if($scope.req.selectedApprover!=null&&$scope.req.selectedApprover!=""){
                    if($scope.approverList[i].id == $scope.req.selectedApprover){
                        $scope.req.approvermsisdn =$scope.approverList[i].tel;
                        $scope.req.approverName =$scope.approverList[i].name;
                    }
                }

            }
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/JKService/createAppRequest",
            data: JSON.stringify($scope.req),
            success: function (data) {
                $rootScope.$apply(function () {
                    const result = data.result;
                    if (result.resultCode == '**********') {
                        const lastJKAuthHis = data.lastJKAuthHis;
                        if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="unApprove"){
                            location.href = '../JKAuth/authResult/authResult.html?requestUrl='+$scope.requestUrl;
                        }else if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="approved"){
                            location.href = '../JKAuth/authResult/authResult.html?requestUrl='+$scope.requestUrl;
                        }
                    }else if(result.resultCode == '999999999'){
                            $scope.tip = '调用金库失败' + ":" + result.resultDesc;
                            $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };

}]);
