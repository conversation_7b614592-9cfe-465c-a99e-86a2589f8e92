<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title></title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>

	<!-- 引入头部组件 -->
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<script type="text/javascript" src="advertisePrint_list.js"></script>
	<style>
		.cooperation-manage .coorPeration-table th:first-child, td:first-child {
			padding-left: 30px !important;
		}

		td div {
			width: 100%;
			word-break: keep-all;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.label-min {
			min-width: 100px;
		}

		.div-min {
			min-width: 180px;
		}
		.li-disabled{
			opacity: 0.5;
			cursor: not-allowed !important;
		}
	</style>
</head>
<!--
<body ng-app='myApp' class="body-min-width" ng-controller='advertisePrintListCtrl' ng-init="init();" ng-cloak>
-->
<body ng-app='myApp' class="body-min-width-new" ng-controller='advertisePrintListCtrl' ng-init="init();" ng-cloak>
	<div class="cooperation-manage" style="overflow-x: scroll;">
		<div class="cooperation-head" ng-show="isSuperManager&&enterpriseType==='1'">
			<span class="frist-tab" ng-bind="'COMMON_COOPERATION'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
		</div>
		<div class="cooperation-head" ng-show="isSuperManager&&enterpriseType==='3'">
			<span class="frist-tab" ng-bind="'COMMON_COOPERATION'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
		</div>
		<div class="cooperation-head" ng-show="isZhike">
			<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
		</div>
		<div class="cooperation-head" ng-show="isAgent">
			<span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
			<span class="second-tab" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
		</div>

		<top:menu chose-index="9"
							page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/advertisePrint/list"
							list-index="55" ng-if="isSuperManager&&enterpriseType === '1'"></top:menu>
		<div class="cooperation-search">
			<form class="form-horizontal" name='myForm'>
				<div class="form-group">
					<!--活动名称-->
					<label for="activityName" class="col-xs-1 control-label label-min" ng-bind="'COMMON_ACTIVITYNAME'|translate"></label>

					<div class="cond-div col-xs-2 div-min">
						<input autocomplete="off" name='activityName' type="text" id="activityName" class="form-control"
									 placeholder="{{'COMMON_ACTIVITYNAME'|translate}}" ng-model="activityName">
					</div>
					<!--审核状态-->
					<label class="col-xs-1 control-label label-min" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></label>

					<div class="col-xs-2 cond-div div-min">
						<select class="form-control" ng-model="auditStatus"
										ng-options="x.key as x.value for x in auditStatusList">
							<!--<option value="">不限</option>-->
						</select>
					</div>
					<!--活动状态-->
					<label class="col-xs-1 control-label label-min" ng-bind="'ACTIVITY_STATUS'|translate" ng-show="auditStatus==='2'">
					</label>

					<div class="col-xs-2 cond-div div-min" ng-show="auditStatus==='2'">
						<select class="form-control" ng-model="processStatus"
										ng-options="x.key as x.value for x in processStatusList">
							<!--<option value="">不限</option>-->
						</select>
					</div>

					<div class="cond-div col-xs-1" style="margin-left: 35px;">
						<button ng-click="getActivityList()" class="btn search-btn">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="gotoAdd()">
				<icon class="add-iocn"></icon>
				<span ng-bind="'ADD_ACTIVITY'|translate"></span>
			</button>
		</div>
		<p style="font-size: 14px;font-weight: bold;padding:0 0 10px 20px;"
			 ng-bind="'COMMON_ACTIVITYMANAGE'|translate"></p>

		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:12.5%;" ng-bind="'COMMON_ACTIVITYNAME'|translate"></th>
					<th style="width:12.5%;" ng-bind="'COMMON_EXPIRYDATE'|translate"></th>
					<th style="width:12.5%;" ng-bind="'ACTIVITY_AREASET'|translate"></th>
					<th style="width:12.5%;" ng-bind="'ACTIVITY_RULES'|translate"></th>
					<th style="width:10%;" ng-bind="'SPOKES_SPOKESDAYS'|translate"></th>
					<th style="width:10%;" ng-bind="'ACTIVITY_SPOKENUM'|translate"></th>
					<th style="width:10%;" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>
					<th style="width:10%;" ng-bind="'CONTENTAUDIT_AUDITADVICE'|translate"></th>
					<th style="width:20%;" ng-bind="'COMMON_OPERATE'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in activityInfoList">
					<td ng-bind="item.activityName" title="{{item.activityName}}"></td>
					<td title="{{item.effectivetime}} ~ {{item.expiretime}}">{{item.effectivetime}} ~ {{item.expiretime}}</td>
					<td ng-bind="item.allCity" title="{{item.allCity}}"></td>
					<td ng-bind="item.activityRuleDesc" title="{{item.activityRuleDesc}}"></td>
					<td ng-bind="item.spokeDayNum" title="{{item.spokeDayNum}}"></td>
					<td ng-bind="item.totalNum" title="{{item.totalNum}}"></td>
					<td>
						<span ng-bind="'ACTIVITY_AUDITING'|translate" title="{{'ACTIVITY_AUDITING'|translate}}"
									ng-show="item.auditStatus===1"></span>
						<span ng-bind="'ACTIVITY_AUDIT_PASS'|translate" title="{{'ACTIVITY_AUDIT_PASS'|translate}}"
									ng-show="item.auditStatus===2"></span>
						<span ng-bind="'ACTIVITY_AUDIT_REJECTED'|translate" title="{{'ACTIVITY_AUDIT_REJECTED'|translate}}"
									ng-show="item.auditStatus===3||item.auditStatus===0"></span>
					</td>
					<td ng-bind="item.auditOpinion" title="{{item.auditOpinion}}"></td>
					<td>
						<div class="handle">
							<ul>
								<!--删除-->
								<li ng-show = "item.auditStatus != '1'" class="delete" ng-click="deleteBusinessCardContent(item)">
									<icon class="delete-icon"></icon>
									<span ng-bind="'COMMON_DELETE'|translate"></span>
								</li>
								<!--暂停-->
								<li ng-show = "(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.auditStatus == '2' && (item.isPrematureTerminated != '2' && item.isPrematureTerminated != '3')" class="edit" ng-click="item.switchState===1?suspendAdvertise(item,'1'):''" ng-class="{true:'',false:'li-disabled'}[item.switchState===1]">
									<span style="color:#7360e2" ng-bind="'COMMON_SUSPEND'|translate"></span>
								</li>
								<!--恢复-->
								<li ng-show = "(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.auditStatus == '2' && item.isPrematureTerminated == '3'" class="edit" ng-click="item.switchState===1?suspendAdvertise(item,'0'):''" ng-class="{true:'',false:'li-disabled'}[item.switchState===1]">
									<span style="color:#ff2549" ng-bind="'COMMON_RECOVERY'|translate"></span>
								</li>
								<li class="edit" ng-click="toEdit(item)">
									<icon class="edit-icon"></icon>
									<span ng-bind="'GROUP_EDIT'|translate"></span>
								</li>
								<li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'COMMON_WATCH'|translate"></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="activityInfoList===null || activityInfoList.length<=0">
					<td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="getActivityList('justPage')"></ptl-page>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838' ng-bind="tip|translate"></p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

	<!--删除广告弹窗-->
	<div class="modal fade bs-example-modal-sm" id="deleteAdvertise" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content" style="width:390px">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838' ng-bind="'ADVERTISE_SUREDELETEADVERTISE'|translate"></p>
					</div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
							ng-click="delAdvertise()"></button>
					<button id="deleteAdvertiseCancel" type="submit" class="btn " data-dismiss="modal"
							aria-label="Close" id="addAdvertiseCancel" ng-bind="'NO'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</body>
</html>