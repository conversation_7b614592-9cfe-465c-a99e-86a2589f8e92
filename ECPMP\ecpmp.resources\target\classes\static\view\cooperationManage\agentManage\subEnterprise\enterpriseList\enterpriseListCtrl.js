var app = angular.module("myApp",["util.ajax",'page',"top.menu","angularI18n"])
//自定义filter,格式化日期
app.filter('newDate',function(){
    return function(date){
        var new_date =  date.substr(0,4)+"-"+date.substr(4,2)+"-"+date.substr(6.2);
        return new_date;
    }
});
app.controller('EnterpriselistCtrl', function ($scope,$rootScope,$filter,$location,RestClientUtil) {
    //初始化参数
    $scope.init = function () {
        $scope.productType = 0 ;
        $scope.quotaDescription='';
        $scope.HYquotaDescription='';

        // $scope.gdProductID='';
        // $scope.gdProductName='';
        $scope.gdSharequota='';

        $scope.pxEffective=1;
        $scope.gdEffective=1;
        $scope.id = $location.search().id || '';
        $scope.selectedProvince = null;
        $scope.beyondPackageSwitch = "";
        $scope.beyondHYPackageSwitch = "";
        $scope.statusMap={
            "1":"审核中",
            "2":"审核通过",
            "3":"驳回",
            "4":"变更审核中",
            "5":"变更驳回"
        };

        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        //搜索时初始化参数
        $scope.initSel = {
            enterpriseName: "",//企业名称
            organizationID: "",//企业机构代码
            provinceName: "0"//归属地
        };
        var loginRoleType=$.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
        $scope.isZhike = (loginRoleType=='zhike');
        $scope.isAgent = (loginRoleType=='agent');
        $scope.isProvincial = (loginRoleType=='provincial');
        $scope.parentEnterpriseID=$.cookie('enterpriseID');
        $scope.enterpriseList();
        $scope.enterpriseID = parseInt($.cookie('enterpriseID')) || '';
        $scope.showInsideResource();

        if (true) {
            var req = {
                "id": $scope.enterpriseID,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": 100,
                    "isReturnTotal": "1"
                }
            }
            /*查询企业列表*/
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
                data: JSON.stringify(req),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '**********') {
//                    $scope.enterpriseName = data.enterprise.enterpriseName;
                            $scope.businessStatus = data.enterprise.businessStatus;
                            $.cookie('enterpriseName', $scope.enterpriseName, {path: '/'});
                            console.log($scope.businessStatus);
                            if ($scope.businessStatus == 1) {
                                $('#myModalsub').modal();
                            }
                        }
                    })
                }
            });
        }

    };



    $scope.checkSelect = function (){
        if($scope.productType){
            $scope.quotaDescription =  $scope.productType.quotaDescription;
            $scope.productName = $scope.productType.productName;
            $scope.productId=$scope.productType.productId;
        }else{
            $scope.quotaDescription =  "";
            $scope.productName = "";
            $scope.productId= "";
        }


    }
    // 行业挂机短信套餐选择
    $scope.checkHYSelect = function (){
        if($scope.HYproductType){
            $scope.HYquotaDescription =  $scope.HYproductType.quotaDescription;
            $scope.gdProductName = $scope.HYproductType.productName;
            $scope.gdProductID=$scope.HYproductType.productId;
            $scope.msisdnValidate = true;
        }else{
            if($scope.gdEffective != 1){
                $scope.HYquotaDescription = "";
                $scope.gdProductID="";
                $scope.HYHXProduct = "";
                $scope.gdProductName = "";
            }

        }
    }
    // 注销二次确认弹窗
    $scope.logoutAccount = function (item){
        $scope.selectedItemLogout = item;
        $("#deleteAccount").modal();
    }
    // 注销
    $scope.delAccount = function () {
        var item = $scope.selectedItemLogout;
        var req = {
            "operatorID": item.operatorID,
            "enterpriseId": item.id
        };
        console.log(req,item.operatorID,item.id)
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/cancelEnterprise",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $('#delAccountCancel').click();
                        $scope.enterpriseList();
                    } else {
                        $('#delAccountCancel').click();
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function (err) {
                $rootScope.$apply(function () {
                    $('#delAccountCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    /**
     * 根据当前子企业企业ID，调用ecpm查询企业业务套餐接口
     * （queryServiceProduct），获取企业业务套餐信息，并根据企业业务套餐信息选中“套餐名称”下拉选项，“套餐配额”展示对应产品的配额描述
     * @param item
     *
     */
    $scope.queryServiceProduct=function(item){
        var req = {
            "enterpriseID":item.id
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryServiceProduct",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        debugger
                        $scope.serviceProduct=result.serviceProductArray
                        $scope.nextServiceProduct=result.nextServiceProductArray
                        $scope.gdSharePackage = result.gdSharePackageArray
                        // 获取名片彩印套餐数据
                        $scope.getProperties()

                        
                    // 如果gdSharePackage有值则判断时间大显示谁，状态也修改
                        if($scope.gdSharePackage && $scope.gdSharePackage.length==1){
                            $scope.gdSharePackageOneMax = $scope.gdSharePackage[0]
                            // $scope.changeHYPlatform($scope.gdSharePackage[0]?.effective == '1' ? 1 : 0)
                            // 获取行业挂机短信套餐数据
                            $scope.getHYProperties()
                        }else if($scope.gdSharePackage && $scope.gdSharePackage.length==2){
                            $scope.maxGdSharePackage = $scope.gdSharePackage.reduce((prev, current) => (prev.updateTime > current.updateTime ? prev : current));
                            if($scope.maxGdSharePackage?.id){
                                $scope.gdSharePackageTwoMax = $scope.maxGdSharePackage
                                // $scope.changeHYPlatform($scope.maxGdSharePackage?.effective == '1' ? 1 : 0)
                            }
                            // 获取行业挂机短信套餐数据
                            $scope.getHYProperties()
                        }else{
                            // 获取行业挂机短信套餐数据
                            $scope.getHYProperties()
                        }

                    }else {
                        $scope.productType = 0 ;
                        $scope.quotaDescription='';
                        $scope.HYproductType = 0 ;
                        $scope.HYquotaDescription='';
                    }
                })
            }
        })
    }

    /**
     * 调用ecpm企业业务套餐开通接口（submitServiceProduct），enterpriseID取当前子企业企业ID，
     * productID取“套餐名称”选中枚举值。若接口返回错误，则提示保存失败
     * @param item
     */

    $scope.submitServiceProduct=function(){
        if ($scope.IsHX){
            if ($scope.productName == undefined){
                if($scope.HXProduct){
                    $scope.productName = $scope.HXProduct.productName
                }
            }
            if ($scope.productId == undefined){
                if($scope.HXProduct){
                    $scope.productId = $scope.HXProduct.productId
                }
            }
            // if ($scope.gdProductName == undefined){
            //     if($scope.HYHXProduct){
            //         $scope.gdProductName = $scope.HYHXProduct.productName
            //     }
            // }
            // if ($scope.gdProductID == undefined){
            //     if($scope.HYHXProduct){
            //         $scope.gdProductID = $scope.HYHXProduct.productId
            //     }
            // }
        }
        if($scope.gdSharequota && $scope.HYquotaDescription){
            $scope.tip="1030120117";
            $('#myModal').modal();
            return
        }
        if(!$scope.gdSharequota && !$scope.HYquotaDescription && !$scope.productId){
            $scope.tip="1030120121";
            $('#myModal').modal();
            return
        }
        // if($scope.gdSharequota < 1000){
        //     $scope.tip="1030120122";
        //     $('#myModal').modal();
        //     return
        // }
        var req = {
            "enterpriseID":$scope.submitServiceProductEnterpriseID,
            "productName":$scope.productName,
            "productID":$scope.productId,
            "beyondPackageSwitch":$scope.supersuitePackageSwitch,
            "gdProductID":$scope.gdProductID, // 挂机短信套餐id
            "gdProductName":$scope.gdProductName, // 挂机短信套餐名称
            "gdSharequota":$scope.gdSharequota, // 挂机短信企业共享包配额
            "pxEffective":$scope.pxEffective, // 屏显是否立即生效，1 立即，2 次月
            "gdEffective":$scope.gdEffective, // 挂短是否立即生效，1 立即，2 次月
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/submitServiceProduct",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    debugger
                    if(result.result.resultCode == '**********'){
                        $scope.productName=undefined;
                        $scope.gdProductName=undefined;
                        $scope.gdProductID=undefined;
                        $scope.productId=undefined;
                        $('#insideResource').modal('hide')
                        $scope.tip="1010120108";
                        $('#myModal').modal();
                    }else if(result.result.resultCode == '1010120014') { // 无业务开关数据
                        $scope.productName=undefined;
                        $scope.gdProductName=undefined;
                        $scope.gdProductID=undefined;
                        $scope.productId=undefined;
                        $('#insideResource').modal('hide')
                        $scope.tip="1030120116";
                        $('#myModal').modal();
                    }
                    else if(result.result.resultCode == '1010120204') { // 套餐不可向下调整
                        $scope.productName=undefined;
                        $scope.gdProductName=undefined;
                        $scope.gdProductID=undefined;
                        $scope.productId=undefined;
                        // $('#insideResource').modal('hide')
                        $scope.tip="1030120118";
                        $('#myModal').modal();
                    }else if(result.result.resultCode == '1010120203') { // 共享包配额不可向下调整
                        $scope.productName=undefined;
                        $scope.gdProductName=undefined;
                        $scope.gdProductID=undefined;
                        $scope.productId=undefined;
                        // $('#insideResource').modal('hide')
                        $scope.tip="1030120119";
                        $('#myModal').modal();
                    }else if(result.result.resultCode == '1010120202') { // 行业挂机短信企业共享配额最少1000
                        $scope.productName=undefined;
                        $scope.gdProductName=undefined;
                        $scope.gdProductID=undefined;
                        $scope.productId=undefined;
                        // $('#insideResource').modal('hide')
                        $scope.tip="1030120122";
                        $('#myModal').modal();
                    }
                    else {
                        $scope.productName=undefined;
                        $scope.gdProductName=undefined;
                        $scope.gdProductID=undefined;
                        $scope.productId=undefined;
                        $('#insideResource').modal('hide')
                        $scope.tip="1010120107";
                        $('#myModal').modal();
                    }
                })
            },
            error:function(){
                $rootScope.$apply(function(data){
                        $('#insideResource').modal('hide')
                        $scope.tip="1010120107";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }
    /**
     *  根据当前子企业企业ID，调用ecpm查询企业成员配额接口（queryMemberQuota），获取企业成员配额信息
     */
    $scope.queryMemberQuota=function(item){
        var req = {
            "enterpriseID":item.id,
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryMemberQuota",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.memberQuota = result.memberQuota
                        $scope.memberCount = result.memberCount
                        if ($scope.memberQuota == undefined){
                            $scope.memberQuota=0
                        }
                        if ($scope.memberCount == undefined){
                            $scope.memberCount=0
                        }
                        $scope.availablMembers = $scope.memberQuota
                        if ($scope.availablMembers<$scope.memberCount){
                            $scope.remainingMembers = 0
                        }else {
                            $scope.remainingMembers = $scope.memberQuota - $scope.memberCount
                        }
                        if (0 == $scope.availablMembers){
                            $scope.availablMembers=''
                        }
                    }
                })
            }
        })
    }
    /**
     * 调用ecpm提交企业成员配额接口（submitMemberQuota），enterpriseID取当前子企业企业ID，
     * memberQuota取“可用成员数”输入值。若接口返回错误，则提示保存失败
     * @param str
     *
     */
    $scope.submitMemberQuota=function(){
        var req = {
            "enterpriseID":$scope.submitMemberQuotaEnterpriseID,
            "memberQuota":$scope.availablMembers
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/submitMemberQuota",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $('#OrderMemberQuotaWindow').modal('hide');
                        $scope.tip="1010120108";
                        $('#myModal').modal();
                    }else if (result.result.resultCode == '1010120097'){
                        $('#OrderMemberQuotaWindow').modal('hide');
                        $scope.tip="1010120109";
                        $('#myModal').modal();
                    }
                    else {
                        $('#OrderMemberQuotaWindow').modal('hide');
                        $scope.tip="1010120107";
                        $('#myModal').modal();
                    }
                })
            },
            error:function(result){
                $rootScope.$apply(function(){
                        $('#OrderMemberQuotaWindow').modal('hide');
                        $scope.tip="1010120107";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }
    $scope.formatDate=function(str){
        if(!str){
            return '';
        }
        var newDateStr="";
        newDateStr=str.substring(0, 4) + "-" + str.substring(4, 6) + "-" + str.substring(6, 8) + " "
            + str.substring(8, 10) + ":" + str.substring(10, 12);
        return newDateStr;
    }
    //跳转至新增直客页面
    $scope.gotoAdd=function(){
        location.href='../createEnterprise/createEnterprise.html?operationType=add';
    }
    //跳转至企业详情页面,传递organizationID
    $scope.toDetail=function(item){
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../createEnterprise/createEnterprise.html?operationType=detail';
        $.subEnterprise=item;
    }

    //跳转至企业修改页面,传递enterpriseID
    $scope.toEdit=function(item){
        //审核中状态不可修改
        if(item.auditStatus==1 || item.auditStatus==4){
            $scope.tip= "1030121000";
            $('#myModal').modal();
            return;
        }
        $.cookie("subEnterpriseID",item.id,{path:'/'});
        $.cookie("subEnterpriseName",item.enterpriseName,{path:'/'});
        location.href='../createEnterprise/createEnterprise.html?operationType=edit';
    }
    //获取queryEnterpriseList接口的数据
    $scope.enterpriseList = function (condition) {
        if(condition!='justPage'){
            var req = {
                "enterpriseType":3,
                "sortType":2,
                "sortField":1,
                "enterpriseName":$scope.enterpriseName,
                "enterpriseIds":$scope.enterpriseIDQ?[$scope.enterpriseIDQ]:[],
                "parentEnterpriseIDs":$scope.parentEnterpriseID?[$scope.parentEnterpriseID]:[],
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize":$scope.pageInfo[0].pageSize,
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.pageParameter.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode == '**********'){
                        $scope.queryEnterpriseList=result.enterpriseList;
                        //获取页面的总条数与总页面
                        $scope.pageInfo[0].totalCount=parseInt(result.totalNum)||0;
                        if($scope.pageInfo[0].totalCount==0){
                            // $scope.pageInfo[0].totalPage=0;
                            $scope.pageInfo[0].currentPage=1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage=1;
                        }else{
                            $scope.pageInfo[0].totalPage=Math.ceil(parseInt(result.totalNum)/parseInt($scope.pageInfo[0].pageSize));
                        }
                    }else{
                        $scope.queryEnterpriseList=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip=result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error:function(){
                $rootScope.$apply(function(data){
                        $scope.tip="**********";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }
    //若按条包月订购关系列表存在effictiveTime<=当前时间< expireTime的记录，
    // 且当前记录企业信息needServiceProduct=1，则对应记录额外展示链接：套内资源、订单成员配额
    $scope.showInsideResource=function(){
        var now = $scope.getCurrentDate();
        //根据当前代理商企业ID，调用ecpm查询订购关系列表接口（querySubscribeList），
        // servType=1，reserved4=1，获取按条包月订购关系列表。
        var req = {
            "subscribeInfo": {
                "enterpriseID":$scope.parentEnterpriseID,
                "servType": 1,
                "reservedsEcpmp":{
                    "reserved4": "1",
                },
            }
        }
        $scope.isInsideResource=false
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                        console.log(data)
                        if (data.result.resultCode == '**********') {
                            $scope.subscribeList = data.subscribeInfoList || [];
                            if ($scope.subscribeList.length > 0) {
                                for (let i = 0; i < $scope.subscribeList.length; i++) {
                                    var subscribe = $scope.subscribeList[i];
                                    if (subscribe.effictiveTime<=now && now<subscribe.expireTime){
                                        $scope.isInsideResource=true
                                    }
                                }
                            }
                        } else {
                            $scope.isInsideResource=false
                            $scope.tip = data.result.resultCode;
                            $('#myModal').modal();
                        }

                    }
                )
            },
            error: function (data) {
                $rootScope.$apply(function () {
                    $scope.isInsideResource=false
                    $scope.tip = data.result.resultCode;
                    $('#myModal').modal();
                })
            }
        })
    }
    $scope.resetPage = function (){
        $scope.submitServiceProductEnterpriseID="";
        $scope.openEnterpriseName="";
        $scope.productName = undefined;
        $scope.productId = undefined;
        $scope.HYproductType = 0 ;
        $scope.HYquotaDescription='';
        $scope.productType = 0 ;
        $scope.quotaDescription='';
        $scope.supersuitePackageSwitch = ""
        $scope.gdProductID = undefined // 挂机短信套餐id
        $scope.gdProductName = undefined // 挂机短信套餐名称
        $scope.gdSharequota = "" // 挂机短信企业共享包配额
        $scope.pxEffective = 1 // 屏显是否立即生效，1 立即，2 次月
        $scope.gdEffective = 1 // 挂短是否立即生效，1 立即，2 次月
        $scope.msisdnValidate = true;
        $scope.hyDisabledStatus = "";  // 默认不禁止行业套餐选择
        $scope.gdSharePackageTwoMax = "";
        $scope.gdSharePackageOneMax = "";

    }
    /**
     * 打开套内资源弹窗
     */
    $scope.openInsideResource=function(item){
        $scope.resetPage()
        if (item.auditStatus == 2){
            $scope.queryServiceProduct(item);
            $scope.submitServiceProductEnterpriseID = item.id;
            $scope.openEnterpriseName = item.enterpriseName;

            $('#insideResource').modal();
            $scope.getQueryServiceRuleList();
        }else {
            $scope.tip="**********";
            $('#myModal').modal();
        }
    }
    // 获取超套开关规则
    $scope.getQueryServiceRuleList  = function () {
        var req = {
            enterpriseID: $scope.submitServiceProductEnterpriseID,
            enterpriseType:3
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceRuleList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if(data.result.resultCode == '**********'){
                        var serviceControl  = data.serviceControl;
                        $scope.supersuitePackageSwitch = serviceControl.reserved6
                        if(serviceControl.reserved6 && serviceControl.reserved6.length == 2) {
                            var list = serviceControl.reserved6.split("");
                            $scope.beyondPackageSwitch = list[1];
                            $scope.beyondHYPackageSwitch = 0;
                        }else if(serviceControl.reserved6 && serviceControl.reserved6.length == 3){
                            var list = serviceControl.reserved6.split("");
                            $scope.beyondPackageSwitch = list[1];
                            $scope.beyondHYPackageSwitch = list[2];
                        }
                        else { // 所有超套未开启
                            $scope.beyondPackageSwitch = "";
                            $scope.beyondHYPackageSwitch = "";
                            $scope.supersuitePackageSwitch = "";
                        }
                    }else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            }
        })
    };
    $scope.changeStatus = function () {
        $scope.beyondPackageSwitch = $scope.beyondPackageSwitch != "1" ? "1" : "0";
        let switchList = $scope.supersuitePackageSwitch.split("")
            if($scope.supersuitePackageSwitch){
                if($scope.supersuitePackageSwitch.length == 1){
                    $scope.supersuitePackageSwitch = $scope.supersuitePackageSwitch + "00"
                }else if($scope.supersuitePackageSwitch.length == 2){
                    switchList[1] = $scope.beyondPackageSwitch
                    $scope.supersuitePackageSwitch =  switchList.join("") + '0'
                }else if($scope.supersuitePackageSwitch.length == 3){
                    switchList[1] = $scope.beyondPackageSwitch
                    $scope.supersuitePackageSwitch =  switchList.join("")
                }else{
                    $scope.supersuitePackageSwitch = ''
                }
            }else{
                if($scope.beyondPackageSwitch){
                    $scope.supersuitePackageSwitch = "0"+ $scope.beyondPackageSwitch + "0"
                }else{
                    $scope.supersuitePackageSwitch = ""
                }
            }


    }
    $scope.changeHYStatus = function () {
        $scope.beyondHYPackageSwitch = $scope.beyondHYPackageSwitch != "1" ? "1" : "0";
        let switchList = $scope.supersuitePackageSwitch.split("")
        if($scope.supersuitePackageSwitch) {
            if ($scope.supersuitePackageSwitch.length == 1) {
                $scope.supersuitePackageSwitch = $scope.supersuitePackageSwitch + "00"
            } else if ($scope.supersuitePackageSwitch.length == 2) {
                $scope.supersuitePackageSwitch = switchList.join("") + $scope.beyondHYPackageSwitch
            } else if ($scope.supersuitePackageSwitch.length == 3) {
                switchList[2] = $scope.beyondHYPackageSwitch
                $scope.supersuitePackageSwitch = switchList.join("")
            } else {
                $scope.supersuitePackageSwitch = ""
            }
        }else{
            if($scope.beyondHYPackageSwitch){
                $scope.supersuitePackageSwitch = "00"+ $scope.beyondPackageSwitch
            }else{
                $scope.supersuitePackageSwitch = ""
            }
        }
    }
    // 屏显生效方式
    $scope.changePlatform = function (val) {
        $('.platform .redio-li').eq(val).find('span').removeClass('checked');
        $('.platform .redio-li').eq(val===0?1:0).find('span').addClass('checked');
        if(val===1){
            $scope.pxEffective = 1;
        }else{
            $scope.pxEffective = 2;
        }
        $scope.changePxWay(val);
    };
    // 切换行业挂机生效方式
    $scope.changeHYPlatform = function (val) {
        $('.platform1 .redio-li').eq(val).find('span').removeClass('checked');
        $('.platform1 .redio-li').eq(val===0?1:0).find('span').addClass('checked');
        if(val===1){
            $scope.gdEffective = 1;
        }else{
            $scope.gdEffective = 2;
        }
        $scope.changeHyWay(val);
    };
    // 校验共享包输入条数
    $scope.msisdnValidate = true;
    $scope.checkGdSharequota = function () {
        if($scope.gdSharequota<1000){
            $scope.msisdnValidate = false
        }else{
            $scope.msisdnValidate = true;
        }
    }
    /**
     * 打开订单成员配额弹窗
     */
    $scope.openOrderMemberQuota=function(item){
        $scope.submitMemberQuotaEnterpriseID="";
        $scope.openEnterpriseName="";
        if (item.auditStatus == 2){
            $scope.submitMemberQuotaEnterpriseID = item.id;
            $scope.openEnterpriseName = item.enterpriseName;
            $scope.queryMemberQuota(item)
            $('#OrderMemberQuotaWindow').modal();
        }else {
            $scope.tip="**********";
            $('#myModal').modal();
        }

    }
    $scope.createAccount=function(item){
        if (item.accountInfo == null || (item.accountInfo != null &&item.accountInfo.accountName == null)){
            let createSubEnterpriseAccountReq = {
                "enterpriseId":item.id
            }
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/accountManageService/createSubEnterpriseAccount",
                data: JSON.stringify(createSubEnterpriseAccountReq),
                success: function (result) {
                    $rootScope.$apply(function () {
                        if (result.result.resultCode == '**********') {
                            console.log(result)
                            item.accountInfo.accountName = result.accountInfo.accountName;
                            $scope.accountDetail(item,"add")
                        } else {
                            $scope.tip = result.result.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    })
                }
            });

        }
    }
    $scope.accountDetail=function(item,op){
        $scope.accountDetailOPName="查看账号";
        if(op!=null && op === "add"){
            $scope.accountDetailOPName="生成账号";
        }
        if (item.auditStatus != null){
            $scope.accountDetailEnterpriseName=item.enterpriseName;
            $scope.enterpriseAccountName=item.accountInfo.accountName;
            $('#accountDetailWindow').modal();
        }else {
            $scope.tip="**********";
            $('#myModal').modal();
        }
    }
    $scope.availablMembersChange = function (){
        if ($scope.availablMembers<$scope.memberCount){
            $scope.remainingMembers = 0
        }else if ($scope.availablMembers == ""){
            $scope.remainingMembers = 0
        }else{
            $scope.remainingMembers = parseInt($scope.availablMembers) - $scope.memberCount;
        }

    }

    // 获取当前时间YYYYMMDDHHMMSS
    $scope.getCurrentDate = function () {
        var today = new Date();
        var year = today.getFullYear()+'';
        var month = today.getMonth() + 1;
        month = month < 10 ? '0'+month : month;
        var day = today.getDate() < 10 ? '0'+today.getDate() : today.getDate();
        var hours = today.getHours() < 10 ? '0'+today.getHours() : today.getHours();
        var mins = today.getMinutes() < 10 ? '0'+today.getMinutes() : today.getMinutes();
        var secs = today.getSeconds() < 10 ? '0'+today.getSeconds() : today.getSeconds();

        return year + month + day + hours + mins + secs;
    }
    // 切换屏显生效方式 重置选择套餐
    $scope.changePxWay = function(type){
        //     $scope.pxEffective:1 立即生效   $scope.pxEffective:2 次月生效
        // 根据企业业务套餐产品ID，从企业业务套餐列表获取对应产品名称、配额描述
        let resultObj

        if(type == 1){
            if($scope.serviceProduct && $scope.productTypeList){
                resultObj = $scope.productTypeList.filter((item1) => {
                    return $scope.serviceProduct.some((item2) => {
                        return item1.productId === item2.productID;
                    });
                });
            }
            if(resultObj && resultObj[0]?.quotaDescription){
                $scope.productType = resultObj[0]
                $scope.HXProduct  = $scope.productType
                $scope.IsHX = true
                $scope.quotaDescription = resultObj[0]?.quotaDescription
            }else{
                $scope.productType = 0;
                $scope.quotaDescription='';
                $scope.productType = ""
            }
        }else{
            if($scope.nextServiceProduct && $scope.productTypeList){
                resultObj = $scope.productTypeList.filter((item1) => {
                    return $scope.nextServiceProduct.some((item2) => {
                        return item1.productId === item2.productID;
                    });
                });
            }

            if(resultObj && resultObj[0]?.quotaDescription){
                $scope.productType = resultObj[0]
                $scope.IsHX = true
                $scope.HXProduct  = $scope.productType
                $scope.quotaDescription = resultObj[0]?.quotaDescription
            }else{
                $scope.productType = 0 ;
                $scope.quotaDescription='';
                $scope.productType = "";
            }
        }

     }
    // 切换挂短生效方式 重置选择套餐
    $scope.changeHyWay = function(type){
        //     $scope.gdEffective:1 立即生效   $scope.gdEffective:2 次月生效
        // 根据企业业务套餐产品ID，从企业业务套餐列表获取对应产品名称、配额描述
        debugger
        let gdSharePackageObj;
        let resultObj;
        let resultObj2;
        if(type == 1){
            if($scope.gdSharePackage){
                gdSharePackageObj = $scope.gdSharePackage.filter((item) => {
                    return item.effective === 1;
                });
                if(gdSharePackageObj && gdSharePackageObj[0]?.id){
                    $scope.gdSharequota = gdSharePackageObj[0].quota
                    $scope.hyDisabledStatus = gdSharePackageObj[0].quota
                }else{
                    $scope.gdSharequota = ""
                }
            }else{
                $scope.gdSharequota = ""
            }

            if($scope.serviceProduct && $scope.HYproductTypeList){
                resultObj = $scope.serviceProduct.filter((item1) => {
                    return $scope.HYproductTypeList.some((item2) => {
                        return item2.productId === item1.productID;
                    });
                });
                if(resultObj && resultObj[0]?.productName){
                    resultObj2 = $scope.HYproductTypeList.filter((item1) => {
                        return $scope.serviceProduct.some((item2) => {
                            return item1.productId === item2.productID;
                        });
                    });
                }
                if(resultObj && resultObj[0]?.productName){
                    $scope.HYproductType = resultObj2[0]
                    $scope.HYHXProduct  = $scope.HYproductType
                    $scope.HYquotaDescription = resultObj2[0]?.quotaDescription
                }else{
                    $scope.HYproductType = 0 ;
                    $scope.HYquotaDescription='';
                    $scope.HYHXProduct  = "";
                }
            }else{
                $scope.HYproductType = 0 ;
                $scope.HYquotaDescription='';
                $scope.HYHXProduct  = "";
            }


        }else{
            $scope.hyDisabledStatus = "";
            if($scope.nextServiceProduct){
                resultObj = $scope.nextServiceProduct.filter((item1) => {
                    return $scope.HYproductTypeList.some((item2) => {
                        return item2.productId === item1.productID;
                    });
                });
                if(resultObj && resultObj[0]?.productName){
                    resultObj2 = $scope.HYproductTypeList.filter((item1) => {
                        return $scope.nextServiceProduct.some((item2) => {
                            return item1.productId === item2.productID;
                        });
                    });
                }
            }
            if($scope.gdSharePackage){
                gdSharePackageObj = $scope.gdSharePackage.filter((item) => {
                    return item.effective === 2;
                });
                if(gdSharePackageObj && gdSharePackageObj[0]?.id){
                    $scope.gdSharequota = gdSharePackageObj[0].quota;
                }else{
                    $scope.gdSharequota = "";
                }
            }else{
                $scope.gdSharequota = ""
            }

            if(resultObj && resultObj[0]?.productName){
                $scope.HYproductType = resultObj2[0]
                $scope.HYHXProduct  = $scope.HYproductType
                $scope.HYquotaDescription = resultObj2[0]?.quotaDescription
            }else{
                $scope.HYproductType = 0 ;
                $scope.HYquotaDescription='';
                $scope.HYHXProduct = ""
            }
        }

    }
    /**
     * 调用ecpmp的获取配置项接口（getProperties），获取企业名片彩印业务套餐配置项
     * 解析为企业业务套餐列表
     */
    $scope.IsHX=false
    $scope.getProperties = function() {
        var keys=new Array("ecpmp.enterprise.service.product");
        var req = {
            "keys": keys
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/getProperties",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '**********') {
                        debugger
                        for(var key in result.props){
                            var productStr = result.props[ key ];
                        }
                        var products = productStr.substring(1,productStr.length-1);
                        var productList = products.split("|");
                        var productTypes = new Array();
                        for (let i = 0; i < productList.length; i++) {
                            productTypes.push($.parseJSON(productList[i]))
                        }
                        $scope.productTypeList = productTypes



                        // 筛选出相同的对象，取值展示
                        // 根据企业业务套餐产品ID，从企业业务套餐列表获取对应产品名称、配额描述
                        let resultObj;
                        let resultObjNext;
                        let resultObj3;
                        let resultObjNext3;
                        if($scope.serviceProduct){
                            resultObj = $scope.serviceProduct.filter((item1) => {
                                return $scope.productTypeList.some((item2) => {
                                    return item2.productId === item1.productID;
                                });
                            });
                            if(resultObj && resultObj[0]?.productName){
                                resultObj3 = $scope.productTypeList.filter((item1) => {
                                    return $scope.serviceProduct.some((item2) => {
                                        return item1.productId === item2.productID;
                                    });
                                });
                            }

                        }
                        if($scope.nextServiceProduct){
                            resultObjNext = $scope.nextServiceProduct.filter((item1) => {
                                return $scope.productTypeList.some((item2) => {
                                    return item2.productId === item1.productID;
                                });
                            });
                            if(resultObjNext && resultObjNext[0]?.productName){
                                resultObjNext3 = $scope.productTypeList.filter((item1) => {
                                    return $scope.nextServiceProduct.some((item2) => {
                                        return item1.productId === item2.productID;
                                    });
                                });
                            }


                        }

                        if (resultObj && resultObj[0]?.productName && resultObjNext && resultObjNext[0]?.productName){
                        // 同时存在判断时间大显示
                            if(resultObj[0].updateTime > resultObjNext[0].updateTime){
                            //     显示立即生效方式
                                $scope.productType = resultObj3[0]
                                $scope.IsHX = true
                                $scope.HXProduct  = $scope.productType
                                $scope.quotaDescription = resultObj3[0]?.quotaDescription
                                $scope.changePlatform(1)
                            }else{
                            //     显示次月生效方式
                                $scope.productType = resultObjNext3[0]
                                $scope.HXProduct  = $scope.productType
                                $scope.quotaDescription = resultObjNext3[0]?.quotaDescription
                                $scope.changePlatform(0)
                            }
                        }else if(resultObj && resultObj[0]?.productName){
                        //  渲染立即生效设置
                            $scope.productType = resultObj3[0]
                            $scope.IsHX = true
                            $scope.HXProduct  = $scope.productType
                            $scope.quotaDescription = resultObj3[0]?.quotaDescription
                            $scope.changePlatform(1)
                        }else if(resultObjNext && resultObjNext[0]?.productName){
                        // 渲染次月生效设置
                            $scope.productType = resultObjNext3[0]
                            $scope.IsHX = true

                            $scope.HXProduct  = $scope.productType
                            $scope.quotaDescription = resultObjNext3[0]?.quotaDescription
                            $scope.changePlatform(0)
                        }else{
                            $scope.productType = 0 ;
                            $scope.quotaDescription='';
                            $scope.changePlatform(1)
                        }
                    } else {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = result.result.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }

    /**
     * 调用ecpmp的获取配置项接口（getHYProperties），获取企业行业挂机短信业务套餐配置项
     * 解析为企业业务套餐列表
     */
    $scope.IsHX=false
    $scope.getHYProperties = function() {
        var keys=new Array("ecpmp.enterprise.gd.service.product");
        var req = {
            "keys": keys
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/getProperties",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    debugger
                    if (result.result.resultCode == '**********') {
                        for(var key in result.props){
                            var productStr = result.props[ key ];
                        }
                        var products = productStr.substring(1,productStr.length-1);
                        var productList = products.split("|");
                        var productTypes = new Array();
                        for (let i = 0; i < productList.length; i++) {
                            productTypes.push($.parseJSON(productList[i]))
                        }
                        $scope.HYproductTypeList = productTypes

                        // 筛选出相同的对象，取值展示
                        // 根据企业业务套餐产品ID，从企业业务套餐列表获取对应产品名称、配额描述
                        let resultObj1;
                        let resultObj11;
                        let resultObjNext1;
                        let resultObjNext11;
                        if($scope.serviceProduct){
                            resultObj1 = $scope.serviceProduct.filter((item1) => {
                                return $scope.HYproductTypeList.some((item2) => {
                                    return item2.productId === item1.productID;
                                });
                            });
                            if(resultObj1 && resultObj1[0]?.productName){
                                resultObj11 = $scope.HYproductTypeList.filter((item1) => {
                                    return $scope.serviceProduct.some((item2) => {
                                        return item1.productId === item2.productID;
                                    });
                                });
                            }
                        }
                        if($scope.nextServiceProduct){
                            resultObjNext1 = $scope.nextServiceProduct.filter((item1) => {
                                return $scope.HYproductTypeList.some((item2) => {
                                    return item2.productId === item1.productID;
                                });
                            });
                            if(resultObjNext1 && resultObjNext1[0]?.productName){
                                resultObjNext11 = $scope.HYproductTypeList.filter((item1) => {
                                    return $scope.nextServiceProduct.some((item2) => {
                                        return item1.productId === item2.productID;
                                    });
                                });
                            }
                        }


                        if (resultObj1 && resultObj1[0]?.productName && resultObjNext1 && resultObjNext1[0]?.productName){
                            // 同时存在判断时间大显示
                            if(resultObj1[0].updateTime > resultObjNext1[0].updateTime){
                            // 增加共享包最大时间判断
                                if($scope.gdSharePackageOneMax?.id){
                                    if(resultObj1[0].updateTime > $scope.gdSharePackageOneMax.updateTime){
                                        //     显示立即生效方式
                                        $scope.HYproductType = resultObj11[0]
                                        $scope.IsHX = true
                                        $scope.HYHXProduct  = $scope.HYproductType
                                        $scope.HYquotaDescription = resultObj11[0]?.quotaDescription
                                        $scope.changeHYPlatform(1)
                                    }else{
                                        $scope.changeHYPlatform($scope.gdSharePackageOneMax?.effective == '1' ? 1 : 0)
                                        
                                    }
                                }else if($scope.gdSharePackageTwoMax?.id){
                                    if(resultObj1[0].updateTime > $scope.gdSharePackageTwoMax.updateTime){
                                        //     显示立即生效方式
                                        $scope.HYproductType = resultObj11[0]
                                        $scope.IsHX = true
                                        $scope.HYHXProduct  = $scope.HYproductType
                                        $scope.HYquotaDescription = resultObj11[0]?.quotaDescription
                                        $scope.changeHYPlatform(1)
                                    }else{
                                        $scope.changeHYPlatform($scope.gdSharePackageTwoMax?.effective == '1' ? 1 : 0)
                                    }
                                }else {
                                    //     显示立即生效方式
                                    $scope.HYproductType = resultObj11[0]
                                    $scope.IsHX = true
                                    $scope.HYHXProduct  = $scope.HYproductType
                                    $scope.HYquotaDescription = resultObj11[0]?.quotaDescription
                                    $scope.changeHYPlatform(1)
                                }

                            }else{
                                //     显示次月生效方式
                                $scope.HYproductType = resultObjNext11[0]
                                $scope.IsHX = true
                                $scope.HYHXProduct  = $scope.HYproductType
                                $scope.HYquotaDescription = resultObjNext11[0]?.quotaDescription
                                $scope.changeHYPlatform(0)
                            }
                        }else if(resultObj1 && resultObj1[0]?.productName){
                            // 增加共享包最大时间判断
                            if($scope.gdSharePackageOneMax?.id){
                                if(resultObj1[0].updateTime > $scope.gdSharePackageOneMax.updateTime){
                                    //     显示立即生效方式
                                    $scope.HYproductType = resultObj11[0]
                                    $scope.IsHX = true
                                    $scope.HYHXProduct  = $scope.HYproductType
                                    $scope.HYquotaDescription = resultObj11[0]?.quotaDescription
                                    $scope.changeHYPlatform(1)
                                }else{
                                    $scope.changeHYPlatform($scope.gdSharePackageOneMax?.effective == '1' ? 1 : 0)
                                }
                            }else if($scope.gdSharePackageTwoMax?.id){
                                if(resultObj1[0].updateTime > $scope.gdSharePackageTwoMax.updateTime){
                                    //     显示立即生效方式
                                    $scope.HYproductType = resultObj11[0]
                                    $scope.IsHX = true
                                    $scope.HYHXProduct  = $scope.HYproductType
                                    $scope.HYquotaDescription = resultObj11[0]?.quotaDescription
                                    $scope.changeHYPlatform(1)
                                }else{
                                    $scope.changeHYPlatform($scope.gdSharePackageTwoMax?.effective == '1' ? 1 : 0)
                                }
                            }else{
                                //     显示立即生效方式
                                $scope.HYproductType = resultObj11[0]
                                $scope.IsHX = true
                                $scope.HYHXProduct  = $scope.HYproductType
                                $scope.HYquotaDescription = resultObj11[0]?.quotaDescription
                                $scope.changeHYPlatform(1)
                            }
                        }else if(resultObjNext1 && resultObjNext1[0]?.productName){


                            // 增加共享包最大时间判断
                            if($scope.gdSharePackageOneMax?.id){
                                if(resultObjNext1[0].updateTime > $scope.gdSharePackageOneMax.updateTime){
                                    // 渲染次月生效设置
                                    $scope.HYproductType = resultObjNext11[0]
                                    $scope.IsHX = true
                                    $scope.HYHXProduct  = $scope.HYproductType
                                    $scope.HYquotaDescription = resultObjNext11[0]?.quotaDescription
                                    $scope.changeHYPlatform(0)
                                }else{
                                    $scope.changeHYPlatform($scope.gdSharePackageOneMax?.effective == '1' ? 1 : 0)
                                }
                            }else if($scope.gdSharePackageTwoMax?.id){
                                if(resultObjNext1[0].updateTime > $scope.gdSharePackageTwoMax.updateTime){
                                    // 渲染次月生效设置
                                    $scope.HYproductType = resultObjNext11[0]
                                    $scope.IsHX = true
                                    $scope.HYHXProduct  = $scope.HYproductType
                                    $scope.HYquotaDescription = resultObjNext11[0]?.quotaDescription
                                    $scope.changeHYPlatform(0)
                                }else{
                                    $scope.changeHYPlatform($scope.gdSharePackageTwoMax?.effective == '1' ? 1 : 0)
                                }
                            }else{
                                // 渲染次月生效设置
                                $scope.HYproductType = resultObjNext11[0]
                                $scope.IsHX = true
                                $scope.HYHXProduct  = $scope.HYproductType
                                $scope.HYquotaDescription = resultObjNext11[0]?.quotaDescription
                                $scope.changeHYPlatform(0)
                            }

                        }else{
                            $scope.HYproductType = 0 ;
                            $scope.HYquotaDescription='';
                            if(!$scope.gdSharePackage?.length){
                                $scope.changeHYPlatform(1)
                            }else{
                                if($scope.gdSharePackage && $scope.gdSharePackage.length==1){
                                    $scope.changeHYPlatform($scope.gdSharePackage[0]?.effective == '1' ? 1 : 0)
                                }else if($scope.gdSharePackage && $scope.gdSharePackage.length==2){
                                    $scope.maxGdSharePackage = $scope.gdSharePackage.reduce((prev, current) => (prev.updateTime > current.updateTime ? prev : current));
                                    if($scope.maxGdSharePackage?.id){
                                        $scope.changeHYPlatform($scope.maxGdSharePackage?.effective == '1' ? 1 : 0)
                                    }
                                }
                            }
                        }
                    } else {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = result.result.resultCode;
                    $('#myModal').modal();
                })
            }
        });
    }

})
