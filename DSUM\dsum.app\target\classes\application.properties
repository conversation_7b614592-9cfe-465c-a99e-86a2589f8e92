########################################################
###server
########################################################
spring.profiles.active=dev
spring.application.name=dsum
server.servlet.context-path=/dsum
server.ip=127.0.0.1

########################################################
###datasource
########################################################
mybatis.mapper-locations: classpath*:mapper/mysql/*.xml


management.security.enabled=false

server.port=21000
server.servlet.encoding.force=true
server.servlet.encoding.charset=UTF-8

spring.freemarker.allow-request-override=false
spring.freemarker.cache=false
spring.freemarker.check-template-location=true
spring.freemarker.charset=UTF-8
spring.freemarker.content-type=text/html; charset=utf-8
spring.freemarker.prefer-file-system-access=false
spring.freemarker.expose-request-attributes=false
spring.freemarker.expose-session-attributes=false
spring.freemarker.expose-spring-macro-helpers=false
spring.freemarker.suffix=.ftl
spring.freemarker.template-loader-path=classpath:/templates

spring.cloud.stream.kafka.binder.zkNodes=**************:2181,**************:2181,**************:2181
spring.cloud.stream.kafka.binder.brokers=**************:9092,**************:9092,**************:9092
spring.cloud.stream.default-binder=kafka

spring.cloud.config.name=dsum
spring.cloud.config.enabled=true
spring.cloud.config.label=master
spring.cloud.config.profile=resource,serviceConfig
spring.cloud.config.discovery.enabled=true
spring.cloud.config.discovery.service-id=CY-CONFIG-SERVER

eureka.client.serviceUrl.defaultZone=http://127.0.0.1:19000/eureka/
eureka.instance.preferIpAddress=true
eureka.instance.instanceId=${spring.cloud.client.ip-address}:${server.port}

ribbon.eureka.enabled=true
ribbon.ReadTimeout=20000
ribbon.ConnectTimeout=5000

logging.config.classpath=log4j2.xml

spring.main.allow-bean-definition-overriding=true
spring.config.import=optional:configserver:

feign.circuitbreaker.enabled=true

mybatis.configuration.use-actual-param-name=false

# 或者只开放特定端点
management.endpoints.web.exposure.include=refresh,health,info
