var app = angular.module("myApp",["util.ajax",'page',"top.menu","angularI18n","service.common"])
 app.controller('querySpokesController', ['$scope','$rootScope','$location','RestClientUtil','CommonUtils',function ($scope,$rootScope,$location,RestClientUtil,CommonUtils) {
    $scope.init = function () {
        $scope.activityID = $location.search().activityID || '';
      $scope.token = $.cookie("token");
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
            msisdn: "",
            spokeStatus: "",
            awardStatus:"",
        };
        $scope.spokeStatusMap={
            "1":"代言中",
            "2":"取消代言",
            "3":"结束代言"
        }
        $scope.spokeStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "1",
                name: "代言中"
            },
            {
                id: "2",
                name: "取消代言"
            },
            {
                id: "3",
                name: "结束代言"
            } 
        ];
        $scope.rewardStatusChoise = [
            {
                id: "",
                name: "不限"
            },
            {
                id: "0",
                name: "否"
            },
            {
                id: "1",
                name: "是"
            },
        ];
        $scope.querySpokesmanList();
    };
   $scope.exportFile=function(){
     var req = {
       "param":{
         "msisdn":$scope.reqTemp.msisdn,
         "status": $scope.reqTemp.status,
         "awardStatus":$scope.reqTemp.awardStatus,
         "activityID":$scope.activityID,
         "token":$scope.token,
         "isExport":1
       },
       "url":"/qycy/ecpmp/ecpmpServices/spokeService/downSpokesmanListCsvFile",
       "method":"get"
     }
     CommonUtils.exportFile(req);
   }
    $scope.goBack=function () {
        location.href = '../activityManageList/activityManageList.html';
    }
    $scope.formatDate=function(para1,para2){
        if(!para1||!para2){
            return 'format error';
        }
        var newDateStr1="",
            newDateStr2="";
        newDateStr1=para1.substr(0,4)+'.'+para1.substr(4,2)+'.'+para1.substr(6,2);
        newDateStr2=para2.substr(0,4)+'.'+para2.substr(4,2)+'.'+para2.substr(6,2);
        return newDateStr1+'-'+newDateStr2;
    };
    $scope.formatRegion=function(province,city){
        if(!province&&!city){
            return 'format error'
        }
        return province.provinceName+(city.cityName ? "-"+city.cityName:'');
    };
    $scope.querySpokesmanList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "msisdn":$scope.initSel.msisdn,
                "status":$scope.initSel.spokeStatus,
                "awardStatus":$scope.initSel.awardStatus,
                "activityID":parseInt($scope.activityID),
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1"
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
            $scope.exportUrl="/qycy/ecpmp/ecpmpServices/spokeService/downSpokesmanListCsvFile?msisdn="+$scope.reqTemp.msisdn+"&status="+
            $scope.reqTemp.status+"&awardStatus="+$scope.reqTemp.awardStatus+"&activityID="+$scope.activityID;
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/spokeService/querySpokesmanList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.spokesmanList = result.spokesmanList||[];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalcount) || 0;
                        $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount!==0 ? Math.ceil(result.totalcount / parseInt($scope.pageInfo[0].pageSize)):1;
                    } else {
                        $scope.spokesmanList = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.spokesmanList = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = "**********";
                    $('#myModal').modal();
                }
                )
            }
        });

    }
}])
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}])