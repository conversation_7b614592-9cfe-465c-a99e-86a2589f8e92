#\\u5408\u4F5C\u4F19\u4F34\u8D26\u53F7\u89D2\u8272id
account.roleID=1001|1002|1003

# spi interface application name
spi.interface.application.names=com.huawei.jaguar.dsum.spi.feign.CyBaseCommonParamService:CY-BASE-COMMON-PARAMS|com.huawei.jaguar.dsum.spi.feign.CyBaseSmsService:cy-base-sms

# interface resetPwd newPwd length
interface.resetPwd.newPwd.length=6



scheduling.syncOATaskTask.fixedDelay=0 30/59 * * * ?
#scheduling.syncOATaskTask.fixedDelay=0 0/1 * * * ?
scheduling.syncOATaskTask.taskSwitch=1
scheduling.syncOATaskTask.taskName=SyncOATaskTask
scheduling.syncOATaskTask.taskKey=dsum:scheduling:SyncOATaskTask
scheduling.syncOATaskTask.expireMillis=10000
scheduling.syncOATaskTask.minutes=5
scheduling.syncOATaskTask.changeHour=21
scheduling.syncOATaskTask.ftp.localpath=/home/<USER>/cdr/oaFile
#scheduling.syncOATaskTask.ftp.ip=************
#scheduling.syncOATaskTask.ftp.userName=miguadmin
#scheduling.syncOATaskTask.ftp.userpwd=DTyECn+LokNoYk1GwpV6uA==
#scheduling.syncOATaskTask.ftp.remotepath=/ftp_oa/miguusers/
scheduling.syncOATaskTask.ftp.ip=************
scheduling.syncOATaskTask.port=9134
scheduling.syncOATaskTask.ftp.userName=corporatecftp
scheduling.syncOATaskTask.ftp.userpwd=+IDaOkDYTZmNxRR6D93r1Q==
scheduling.syncOATaskTask.ftp.remotepath=/miguusers/
scheduling.syncOATaskTask.fileSize=100000
scheduling.syncOATaskTask.limitDoSize=100000

#scheduling.checkFrozenAdminTask.fixedDelay=0 30 0 * * ?
scheduling.checkFrozenAdminTask.fixedDelay=0 30 0 * * ?
scheduling.checkFrozenAdminTask.taskSwitch=1
scheduling.checkFrozenAdminTask.taskName=SyncOATaskTask9527
scheduling.checkFrozenAdminTask.taskKey=dsum:scheduling:CheckFrozenAdminTask
scheduling.checkFrozenAdminTask.expireMillis=10000
scheduling.checkFrozenAdminTask.intervalDay=90
scheduling.checkFrozenAdminTask.minutes=5

operName=createorganization:\u521b\u5efa\u5206\u7ec4%0acreate content:\u521b\u5efa\u5185\u5bb9%0aauthenticate:\u767b\u5f55\u9274\u6743%0abatch update servicerule:\u6279\u91cf\u4fee\u6539\u4e1a\u52a1\u89c4\u5219%0acreate enterprise:\u521b\u5efa\u4f01\u4e1a%0acreateaccount:\u521b\u5efa\u8d26\u53f7%0acreateorder:\u521b\u5efa\u8ba2\u5355%0alogout:\u767b\u51fa%0adeletemember:\u5220\u9664\u6210\u5458%0acontent audifresult notify:\u5185\u5bb9\u5ba1\u6838\u7ed3\u679c\u901a\u77e5%0adeleteorganization:\u5220\u9664\u5206\u7ec4%0aaddhotline:\u65b0\u589e\u70ed\u7ebf%0amodify enterprise:\u4fee\u6539\u4f01\u4e1a%0asavethirdparty:\u4fdd\u5b58\u7b2c\u4e09\u65b9\u914d\u7f6e%0aupdatehotline:\u4fee\u6539\u70ed\u7ebf%0aaddblackwhitelist:\u65b0\u589e\u9ed1\u767d\u540d\u5355%0aupdateaccount:\u4fee\u6539\u8d26\u53f7%0agetfauthinfobyid:\u6839\u636eid\u83b7\u53d6\u6743\u9650\u4fe1\u606f%0adeleteblackwhitelist:\u5220\u9664\u9ed1\u767d\u540d\u5355%0abatchdeleteaccounts:\u6279\u91cf\u5220\u9664\u8d26\u53f7%0aupdateorganization:\u4fee\u6539\u5206\u7ec4%0areset password:\u91cd\u7f6e\u5bc6\u7801%0acreatemember:\u521b\u5efa\u6210\u5458%0aregister:\u6ce8\u518c%0adelete enterprise:\u5220\u9664\u4f01\u4e1a%0achangepwd:\u4fee\u6539\u5bc6\u7801%0adeleteaccount:\u5220\u9664\u8d26\u53f7%0adeletecontent:\u5220\u9664\u5185\u5bb9
