<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ThirdpartyAccessMapper">
    <resultMap id="thirdpartyWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ThirdpartyWrapper">
        <result property="id" column="id" javaType="java.lang.Integer" />
        <result property="platformID" column="platformID" javaType="java.lang.Integer" />
        <result property="accessAccount" column="accessAccount" javaType="java.lang.String" />
        <result property="accessPassword" column="accessPassword" javaType="java.lang.String" />
        <result property="callbackUrl" column="callbackUrl" javaType="java.lang.String" />
        <result property="approveCallbackUrl" column="approveCallbackUrl" javaType="java.lang.String" />
		<result property="groupSendNotifyUrl" column="groupSendNotifyUrl" javaType="java.lang.String" />
		<result property="feedbackUrl" column="feedbackUrl" javaType="java.lang.String" />
        <result property="fluid" column="fluid" javaType="java.lang.String" />
        <result property="thirdAccount" column="thirdAccount" javaType="java.lang.String" />
        <result property="thirdPassword" column="thirdPassword" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
        <result property="status" column="status" javaType="java.lang.Integer" />
        <result property="fluidSwitch" column="fluidSwitch" javaType="java.lang.Integer" />
        <result property="msgType" column="msgType" javaType="java.lang.String" />
		<result property="mpApproveCallbackUrl" column="mpApproveCallbackUrl" javaType="java.lang.String" />
		<result property="subcribeResultNotifyUrl" column="subcribeResultNotifyUrl" javaType="java.lang.String" />
		<result property="enterpriseApproveCallbackUrl" column="enterpriseApproveCallbackUrl" javaType="java.lang.String" />
	</resultMap>


    <select id="queryThirdpartyList" resultMap="thirdpartyWrapper">
        select
        id,
   		platformID,
    	accessAccount,
   		accessPassword,
   		callbackUrl,
   		approveCallbackUrl,
		groupSendNotifyUrl,
		feedbackUrl,
   		fluid,
   		thirdAccount,
   		thirdPassword,
  		createTime,
    	updateTime,
  		operatorID,
  		status,
  		fluidSwitch,
  		msgType,
  		diffnetUssdSwitch,
  		subcribeResultNotifyUrl,
		mpApproveCallbackUrl,
		enterpriseApproveCallbackUrl
		from ecpm_t_thirdparty_access
        <trim prefix="where" prefixOverrides="and|or">
			<if test="accessAccount != null and accessAccount!=''">
				and accessAccount=#{accessAccount}
			</if>
			<if test="platformID != null and platformID!=''">
				and platformID=#{platformID}
			</if>
		</trim>
        <if test="pageNo != null and pageNo != '' and pageSize != null and pageSize != ''">
			limit #{pageNo},#{pageSize}
		</if>
    </select>

    <select id="countThirdpartyList" resultType="java.lang.Integer">
        select
        count(0)
        from ecpm_t_thirdparty_access
        <trim prefix="where" prefixOverrides="and|or">
			<if test="accessAccount != null and accessAccount!=''">
				and accessAccount=#{accessAccount}
			</if>
			<if test="platformID != null and platformID!=''">
				and platformID=#{platformID}
			</if>
		</trim>
    </select>

    <!--通过ID查询第三方接入信息 -->
    <select id="queryThirdPartyById" resultMap="thirdpartyWrapper">
        select
        id,
   		platformID,
    	accessAccount,
   		accessPassword,
   		callbackUrl,
   		approveCallbackUrl,
		groupSendNotifyUrl,
		feedbackUrl,
   		fluid,
   		thirdAccount,
   		thirdPassword,
  		createTime,
    	updateTime,
  		operatorID,
		status,
		fluidSwitch,
		msgType,
		diffnetUssdSwitch
        from ecpm_t_thirdparty_access
        where id=#{Id}
    </select>

    <!--新增第三方接入信息 -->
	<insert id="addThirdParty">
		INSERT INTO
		ecpm_t_thirdparty_access
		(
		ID,
		platformID,
		accessAccount,
		accessPassword,
		callbackUrl,
		approveCallbackUrl,
		groupSendNotifyUrl,
		feedbackUrl,
		fluid,
		thirdAccount,
   		thirdPassword,
		createTime,
		updateTime,
		operatorID,
		status,
		fluidSwitch,
		msgType,
		ussdDelay,
		diffnetUssdSwitch,
		mpApproveCallbackUrl,
		subcribeResultNotifyUrl,
		 enterpriseApproveCallbackUrl)
		VALUES
		(
		#{id},
		#{platformID},
		#{accessAccount},
		#{accessPassword},
		#{callbackUrl},
		#{approveCallbackUrl},
		#{groupSendNotifyUrl},
		#{feedbackUrl},
		#{fluid},
		#{thirdAccount},
		#{thirdPassword},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{status},
		#{fluidSwitch},
		#{msgType},
		#{ussdDelay},
		#{diffnetUssdSwitch},
		#{mpApproveCallbackUrl},
		#{subcribeResultNotifyUrl},
		#{enterpriseApproveCallbackUrl}
		)
	</insert>

	<!--更新第三方接入信息 -->
	<update id="updateThirdParty">
		UPDATE ecpm_t_thirdparty_access SET
		<trim suffixOverrides="," suffix="where ID=#{id}">
			<if test="platformID!=null">
				platformID = #{platformID},
			</if>
			<if test="accessAccount!=null  and accessAccount!=''">
				accessAccount = #{accessAccount},
			</if>
			<if test="accessPassword!=null and accessPassword!=''">
				accessPassword = #{accessPassword},
			</if>
			<if test="callbackUrl!=null and callbackUrl!=''">
	           	callbackUrl = #{callbackUrl},
	        </if>
	        <if test="approveCallbackUrl!=null ">
	           	approveCallbackUrl = #{approveCallbackUrl},
	        </if>
			<if test="groupSendNotifyUrl!=null ">
				groupSendNotifyUrl = #{groupSendNotifyUrl},
			</if>
			<if test="feedbackUrl!=null ">
				feedbackUrl = #{feedbackUrl},
			</if>
	        <if test="fluid!=null">
	           	fluid = #{fluid},
	        </if>
	        <if test="thirdAccount !=null and thirdAccount !=''">
	           	thirdAccount = #{thirdAccount},
	        </if>
	        <if test="thirdPassword !=null and thirdPassword !=''">
	           	thirdPassword = #{thirdPassword},
	        </if>
	        <if test="createTime!=null">
	           	createTime = #{createTime},
	        </if>
	        <if test="updateTime!=null">
	           	updateTime = #{updateTime},
	        </if>
			<if test="operatorID!=null">
				operatorID = #{operatorID},
			</if>
			<if test="status!=null">
				status = #{status},
			</if>
			<if test="fluidSwitch!=null">
				fluidSwitch = #{fluidSwitch},
			</if>
			<if test="msgType!=null">
				msgType = #{msgType},
			</if>
			<if test="diffnetUssdSwitch!=null">
				diffnetUssdSwitch = #{diffnetUssdSwitch},
			</if>
			<if test="mpApproveCallbackUrl!=null">
				mpApproveCallbackUrl = #{mpApproveCallbackUrl},
			</if>
			<if test="subcribeResultNotifyUrl!=null">
				subcribeResultNotifyUrl = #{subcribeResultNotifyUrl},
			</if>
			<if test="enterpriseApproveCallbackUrl != null">
				enterpriseApproveCallbackUrl = #{enterpriseApproveCallbackUrl},
			</if>
		</trim>
	</update>

    <!--查询自增序列 -->
	<select id="getThirdPartyID" resultType="java.lang.Integer">
		select
		nextval('ecpm_sequence_thirdpartyAccess');
	</select>

	<select id="queryThirdPartyByAccount" resultType="java.lang.Integer">
		select
		count(1)
		from ecpm_t_thirdparty_access
		where accessAccount=#{accessAccount}
	</select>
</mapper>