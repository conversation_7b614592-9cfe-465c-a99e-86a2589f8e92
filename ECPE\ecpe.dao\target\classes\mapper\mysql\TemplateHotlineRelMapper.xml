<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.TemplateHotlineRelMapper">

	<delete id="batchDeleteTemplateHotlineRel">
		delete from ecpe_t_content_org where 
		hotlineNo in
		<foreach item="hotlineNo" index="index" collection="hotlineList"
			open="(" separator="," close=")">
			#{hotlineNo}
		</foreach>
		and 
		cyContID = #{cyContID}
	</delete>
	
</mapper>