<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>代理商管理  > 子企业管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
			src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="enterpriseListCtrl.js"></script>
	<style>
		.modal-footer{ text-align: center; }
		.switch-info{
			color: #aaa;
			margin-left: 30px;
		}
		.switch{
			width: 40px;
			height: 20px;
		}
		.switch .switch-icon{
			width: 18px;
			height: 18px;
		}
		.li-disabled{
			opacity: 0.5;
			cursor: not-allowed!important;
		}

	</style>
</head>

<body ng-app="myApp" class="body-min-width">
<div class="cooperation-manage container-fluid" ng-controller="EnterpriselistCtrl" ng-init="init();" ng-cloak>
	<div class="cooperation-head" ng-show="isSuperManager">
		<span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"> </span> >
		<span class="second-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>
	</div>
	<div class="cooperation-head" ng-show="isAgent">
		<span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"> </span> >
		<span class="second-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>
	</div>

	<top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/agentManage/subEnterprise/enterprise/enterpriseList"
			  list-index="72" ng-if="isSuperManager"></top:menu>
	<div ng-show="businessStatus ==0">
		<div class="cooperation-search">
			<form  class="form-horizontal"name="myForm" novalidate>
				<div class="form-group" >
					<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></label>

					<div class="cond-div col-lg-2 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="enterpriseName" class="form-control"
							   placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME'|translate}}"
							   ng-model="enterpriseName">
					</div>

					<label for="enterpriseID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
						   style="white-space:nowrap" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></label>

					<div class="cond-div col-lg-2 col-md-3 col-sm-3 col-xs-3">
						<input type="text" id="enterpriseID" class="form-control"
							   placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISEID'|translate}}"
							   ng-model="enterpriseIDQ">
					</div>

					<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
						<button type="submit" class="btn search-btn" ng-click="enterpriseList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<div class="add-table">
			<button type="submit" class="btn add-btn" ng-click="gotoAdd()">
				<icon class="add-iocn"></icon>
				<span ng-bind="'ENTERPRISE_ADDSUBENTERPRISE'|translate"></span>
			</button>
		</div>
		<div style="margin-left: 20px;margin-bottom: 20px;margin-top: 20px;">
			<p style="font-size: 16px;font-weight: 500;" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></p>
		</div>
		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th style="width:10%" ng-bind="'ENTERPRISE_AGENTID'|translate"></th>
					<th style="width:10%" ng-bind="'ENTERPRISE_AGENTNAME'|translate"></th>
					<th style="width:10%" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></th>
					<th style="width:10%" ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></th>
					<th style="width:10%" ng-bind="'COMMON_CREATETIME'|translate"></th>
					<th ng-bind="'COMMON_OPERATE'|translate"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in queryEnterpriseList">
					<td><span title="{{item.parentEnterpriseID}}">{{item.parentEnterpriseID}}</span></td>
					<td><span title="{{item.parentEnterpriseName}}">{{item.parentEnterpriseName}}</span></td>
					<td><span title="{{item.id}}">{{item.id||""}}</span></td>
					<td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
					<td title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</td>
					<td>
						<div class="handle">
							<ul>
								<li class="edit" ng-click="toEdit(item)">
									<icon class="edit-icon"></icon>
									<span ng-bind="'GROUP_EDIT'|translate"></span>
								</li>
								<li class="query" ng-click="toDetail(item)">
									<icon class="query-icon"></icon>
									<span ng-bind="'COMMON_WATCHDETAIL'|translate"></span>
								</li>
								<!--套内资源-->
								<li ng-show="isInsideResource && item.needServiceProduct == 1"
									class="query" ng-click="openInsideResource(item)">
									<icon class="set2-icon"></icon>
									<span ng-bind="'COMMON_INSIDERESOURCE'|translate"></span>
								</li>
								<!--订单成员配额-->
								<li ng-show="isInsideResource && item.needServiceProduct == 1"
									class="query" ng-click="openOrderMemberQuota(item)">
									<icon class="package-icon"></icon>
									<span ng-bind="'COMMON_ORDERMEMBERSQUOTA'|translate"></span>
								</li>
								<li ng-show="item.accountInfo != null && item.accountInfo.accountName == null && item.auditStatus == 2 "
									class="query" ng-click="createAccount(item)">
									<icon class="package-icon"></icon>
									<span ng-bind="'ACCOUNT_CREATE'|translate"></span>
								</li>
								<li ng-show="item.accountInfo !=null && item.accountInfo.accountName != null"
									class="query" ng-click="accountDetail(item)">
									<icon class="package-icon"></icon>
									<span ng-bind="'ACCOUNT_DETAIL'|translate"></span>
								</li>
								<!--注销-->
								<li ng-show="isSuperManager"
									class="query"
									ng-class="{false:'',true:'li-disabled'}[item.auditStatus===1]"
									ng-click=" item.auditStatus===1 ?  '' : logoutAccount(item)">
									<!--									<icon class="glyphicon-off"></icon>-->
									<span ng-bind="'WRITE_OFF'|translate"></span>
								</li>
							</ul>
						</div>
					</td>
				</tr>
				<tr ng-show="queryEnterpriseList.length<=0">
					<td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="enterpriseList('justPage')"></ptl-page>
		</div>


		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
			 style="z-index:555555;">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center">
							<p style='font-size: 16px;color:#383838;text-align: center'>
								{{tip|translate}}
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
								ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		<!-- 注销弹框 -->
		<div class="modal fade" id="deleteAccount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content" style="width:450px">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabelDel">确认注销</h4>
					</div>
					<div class="modal-body">
						<p style="text-align:center;margin-top:30px;font-size:16px">请确认是否进行注销?</p>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn btn-primary search-btn" ng-click="delAccount()"
						>确认
						</button>
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
								id="delAccountCancel" style="margin-left: 20px">取消
						</button>
					</div>
				</div>
			</div>
		</div>
		<!--套内资源弹窗-->
		<div class="modal fade" id="insideResource" tabindex="-1" role="dialog" aria-labelledby="myModalLabelTo"
			 style="overflow: auto">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4  class="modal-title" id="myModalLabelTo">套内资源</h4>
					</div>
					<div class="cooper-tab">
						<form class="form-horizontal" name="myForm" novalidate>
							<div class="form-group" style="font-weight: bold;padding-bottom: 0">子企业名称:<span style="font-weight: normal;padding-left: 2px">{{openEnterpriseName}}</span></div>
							<div class="form-group" style="font-weight: bold;padding-bottom: 0">子企业编号:<span style="font-weight: normal;padding-left: 2px">{{submitServiceProductEnterpriseID}}</span></div>
							<div class="form-group" style="font-weight: bold;padding-bottom: 0">普通套餐包:</div>
							<div class="form-group" style="padding-bottom: 0">
								<label  class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
									<span ng-bind="'ENTERPRISE_PACKAGEINFO_NAMETO1'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<select class="form-control" ng-model="productType"
											ng-options="x.productName for x in productTypeList"
											ng-change="checkSelect()"
									>
										<option value="" ng-bind="'COMMON_SELECT'|translate"></option>
									</select>
								</div>
							</div>
							<div class="form-group" style="padding-bottom: 0">
								<label class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
									<span ng-bind="'ENTERPRISE_PACKAGEINFO_QUOTATO1'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<span  ng-bind="quotaDescription"></span>
								</div>
							</div>

							<div class="form-group" style="padding-bottom: 0">
								<label  class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
									<span ng-bind="'HYGUAJIDUANXINSETTING_NAME'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<select class="form-control" ng-model="HYproductType"
											ng-options="x.productName for x in HYproductTypeList"
											ng-disabled="gdSharequota || hyDisabledStatus"
											ng-change="checkHYSelect()"
									>
										<option value="" ng-bind="'COMMON_SELECT'|translate"></option>
									</select>
								</div>
							</div>
							<div class="form-group" style="padding-bottom: 0">
								<label class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
									<span ng-bind="'HYGUAJIDUANXINSETTING_LIMIT'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<span  ng-bind="HYquotaDescription"></span>
								</div>
							</div>
							<div class="form-group" style="font-weight: bold;padding-bottom: 0">企业共享包:</div>
							<div class="form-group" style="padding-bottom: 0">
								<label class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
									<span ng-bind="'HYSHORT_ENTERPRISE_SHARE_PACKAGE'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<input class="form-control" style="margin: 0px; width: 247px;position: absolute;z-index: 1;"
										   ng-show="msisdnValidate"
										   placeholder="请输入共享包条数"
										   ng-disabled="HYquotaDescription"
										   ng-model="gdSharequota"
										   ng-blur="checkGdSharequota()"
									>
									<input class="form-control" style="border-color:red;margin: 0px; width: 247px;position: absolute;z-index: 1;"
										   ng-show="!msisdnValidate"
										   placeholder="请输入共享包条数"
										   ng-model="gdSharequota"
										   ng-blur="checkGdSharequota()"
									>
								</div>
								<span>条</span>
							</div>
							<div class="form-group" style="padding-bottom: 0" ng-show="!msisdnValidate">
								<label class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
								</label>
								<span style="color:red" class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                行业挂机短信企业共享配额最少1000</span>
							</div>
							<div class="form-group" style="padding-bottom: 0">
								<label class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6" style="color: #ff0000">企业共享包配置需为1000的倍数</div>
							</div>

							<!--屏显设置-->
							<div class="form-group platform" style="padding-bottom: 0">
								<label  class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
									<span ng-bind="'PX_COMMON_SETTING'|translate"></span>
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="width: 46%;">
									<li class="redio-li" ng-click="changePlatform(1)"
										style="width:100px;display: inline-block;">
                                    <span
											class="check-btn redio-btn checked" style="vertical-align: middle">
                                    </span>
										<span>立即生效</span>
									</li>

									<li class="redio-li" ng-click="changePlatform(0)"
										style="width:100px;display: inline-block;"><span
											class="check-btn redio-btn" style="vertical-align: middle">
                                </span><span>次月生效</span>
									</li>
								</div>
							</div>

							<div class="form-group" style="padding-bottom: 0" ng-show="isSuperManager">
								<label class="col-lg-5 col-xs-5  col-sm-5 col-md-5" style="text-align: left">
									<label ng-bind="'PX_MP_BEYOND_CONTROL_CONFIG'|translate"></label>
								</label>
								<div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" >
									<span class="switch switch4"
										  ng-class={'off':(beyondPackageSwitch!='1')}
										  ng-click="changeStatus()">
										<icon class="switch-icon"></icon>
									</span>
								</div>
							</div>

							<!--行业挂机设置-->
							<div class="form-group platform1" style="padding-bottom: 0">
								<label  class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="text-align: left">
									<span ng-bind="'HY_COMMON_SETTING'|translate"></span>
								</label>
								<div class="col-lg-5 col-xs-5  col-sm-5 col-md-5" style="width: 46%;">
									<li class="redio-li" ng-click="changeHYPlatform(1)"
										style="width:100px;display: inline-block;">
                                    <span
											class="check-btn redio-btn checked" style="vertical-align: middle;">
                                    </span>
										<span>立即生效</span>
									</li>

									<li class="redio-li" ng-click="changeHYPlatform(0)"
										style="width:100px;display: inline-block;"><span
											class="check-btn redio-btn" style="vertical-align: middle;">
                                </span><span>次月生效</span>
									</li>
								</div>
							</div>

							<div class="form-group" style="padding-bottom: 0" ng-show="isSuperManager">
								<label class="col-lg-5 col-xs-5  col-sm-5 col-md-5" style="text-align: left">
									<label ng-bind="'HY_MP_BEYOND_CONTROL_CONFIG'|translate"></label>
								</label>
								<div class="col-lg-5 col-xs-6 col-sm-7 col-md-6" >
									<span class="switch switch4"
										  ng-class={'off':(beyondHYPackageSwitch!='1')}
										  ng-click="changeHYStatus()">
										<icon class="switch-icon"></icon>
									</span>
								</div>
							</div>

						</form>
					</div>
					<div class="modal-footer">
						<button type="submit"
								class="btn btn-primary search-btn" ng-bind="'COMMON_SAVE'|translate"
								ng-click="submitServiceProduct()"></button>
						<button id="sm" type="submit" class="btn " data-dismiss="modal"
								aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
					</div>
				</div>
			</div>
		</div>

		<!--订单成员配额弹窗-->
		<div class="modal fade" id="OrderMemberQuotaWindow" tabindex="-1" role="dialog" aria-labelledby="myModalLabelThree"
			 style="overflow: auto">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
								aria-hidden="true">&times;</span></button>
						<h4  class="modal-title" id="myModalLabelThree">订单成员配额</h4>
					</div>
					<div class="cooper-tab">
						<form class="form-horizontal" name="myForm" novalidate>
							<div class="form-group" style="font-weight: bold;padding-bottom: 0;margin-left:45px">子企业名称:<span style="font-weight: normal;padding-left: 2px">{{openEnterpriseName}}</span></div>
							<div class="form-group" style="font-weight: bold;padding-bottom: 0;margin-left:45px">子企业编号:<span style="font-weight: normal;padding-left: 2px">{{submitMemberQuotaEnterpriseID}}</span></div>
							<div class="form-group" style="padding-top:2px">
								<label  class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="text-align: right">
									<span ng-bind="'ENTERPRISE_AVAILABLE_MEMBERS'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<input type="text" id="Id" class="form-control"
										   ng-model="availablMembers" oninput="value=value.replace(/[^\d]/g,'')" ng-blur="availablMembersChange()">
								</div>
							</div>
							<div class="form-group">
								<label class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="text-align: right">
									<span ng-bind="'ENTERPRISE_REMAINING_MEMBERS'|translate"></span>
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<span ng-bind="remainingMembers"></span>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button type="submit"
								class="btn btn-primary search-btn" ng-bind="'COMMON_SAVE'|translate"
								ng-click="submitMemberQuota()"></button>
						<button id="smbt" type="submit" class="btn " data-dismiss="modal"
								aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
					</div>
				</div>
			</div>
		</div>

	</div>
	<!-- 查看/生成账号弹窗 -->
	<div class="modal fade bs-example-modal-sm" id="accountDetailWindow" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document" style="width: 467px;">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="accountDetailOPName"></h4>
				</div>
				<div class="cooper-tab">
					<form class="form-horizontal" name="myForm" novalidate>
						<div class="form-group">
							<label class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="text-align: right">
								<span ng-bind="'ENTERPRISE_ENTERPRISENAME2'|translate"></span>
							</label>
							<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6" style="width: 60%;">
								<span ng-bind="accountDetailEnterpriseName"></span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="text-align: right">
								<span ng-bind="'SUB_ENTERPRISE_ACCOUNT_NAME'|translate"></span>
							</label>
							<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6" style="width: 60%;">
								<span ng-bind="enterpriseAccountName"></span>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_BACK'|translate">
					</button>
				</div>
			</div>
		</div>
	</div>
	<!-- 业务未开通 -->
	<div ng-show="businessStatus ==1" class="modal fade bs-example-modal-sm" id="myModalsub" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 18px;color:#383838'>
							业务未开通
						</p>
					</div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</div>
</body>

</html>