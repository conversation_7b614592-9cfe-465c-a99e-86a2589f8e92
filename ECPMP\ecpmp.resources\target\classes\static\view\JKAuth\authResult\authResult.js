var app = angular.module("myApp", ["util.ajax", "top.menu", "angularI18n"])

app.controller('defaultFrequencyController', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {

        $scope.accountID = $.cookie('accountID');
        const loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');

        $scope.req = {};
        //下拉框 24小时
        $scope.periodTypeList = [];
        for (let i = 1; i <= 24; i++) {
            $scope.periodTypeList.push({
                id: i,
                name: i
            })
        }
        $scope.req.duration = 1;
        $scope.requestUrl = $scope.getQueryVariable('requestUrl');
        // if($scope.getQueryVariable('o')){
        //     $scope.oString = JSON.parse(decodeURIComponent($scope.getQueryVariable('o')));
        //     if($scope.oString){
        //         $scope.req.requestID = $scope.oString.requestID;
        //     }
        //
        // }
        $scope.refresh();

    };

    $scope.getQueryVariable = function (variable)
    {
        const query = window.location.search.substring(1);
        const vars = query.split("&");
        for (let i = 0; i < vars.length; i++) {
            const pair = vars[i].split("=");
            if (pair[0] == variable) {
                return pair[1];
            }
        }
        return (false);
    };
    $scope.refreshTimes = 0;
    $scope.refresh = function (val) {
        $scope.refreshTimes = 5;
        $scope.buttonName = $scope.buttonNameTemp + "("+$scope.refreshTimes+"s)";
        // $scope.req.type = "refresh";
        if(val){
            $scope.req.type = "refresh";
        }
        $scope.req.accountID = $scope.accountID;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/JKService/getLastJKAuthHis",
            data: JSON.stringify($scope.req),
            success: function (data) {
                $rootScope.$apply(function () {
                    const result = data.result;
                    if (result.resultCode == '**********') {
                        const lastJKAuthHis = data.lastJKAuthHis;
                        $scope.req = lastJKAuthHis;
                        if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="unApprove"){
                            lastJKAuthHis.approveStatus = "待审核";
                            if(new Date().getTime()-lastJKAuthHis.lastQueryTime>0){
                                $scope.refreshTimes = 5;
                            }
                            if(val){
                                $scope.tip = '当前申请未审核，请稍后再刷新';
                                $('#myModal').modal();
                            }
                        }else if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="approved"){
                            $scope.tip = '当前申请已通过，请进行短信口令认证';
                            $('#myModal').modal();
                            // location.href = '../verify/verify.html?requestUrl='+$scope.requestUrl;
                        }else if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="refused"){
                            $scope.tip = '当前申请已驳回，请重新申请';
                            $('#myModal').modal();
                            // location.href = "../../.."+$scope.requestUrl;
                        }else {
                            $scope.tip = '其他错误';
                            $('#myModal').modal();
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };
    $scope.buttonNameTemp = "刷新";
    $scope.buttonName = "刷新";
    var t1 = window.setInterval(function () {
        $rootScope.$apply(function () {
            if($scope.refreshTimes>0){
                $scope.refreshTimes =  $scope.refreshTimes - 1 ;
                $scope.buttonName = $scope.buttonNameTemp + "("+$scope.refreshTimes+"s)";
            }else{
                $scope.buttonName = "刷新";
            }
        });

    },1000);

    $scope.clickModal = function () {
        let lastJKAuthHis = $scope.req;
        if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="待审核"){
            lastJKAuthHis.approveStatus = "待审核";
            if(new Date().getTime()-lastJKAuthHis.lastQueryTime>0){
                $scope.refreshTimes = 5;
            }
            $scope.tip = '当前申请未审核，请稍后再刷新';
        }else if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="approved"){
            $scope.tip = '当前申请已通过，请进行短信口令认证';
            location.href = '../verify/verify.html?requestUrl='+$scope.requestUrl;
        }else if(lastJKAuthHis.next === "auth" && lastJKAuthHis.approveStatus==="refused"){
            $scope.tip = '当前申请已驳回，请重新申请';
            location.href = "../../.."+$scope.requestUrl;
        }else {
            $scope.tip = '其他错误';
            location.href = "../../.."+$scope.requestUrl;
        }
    }


}]);
