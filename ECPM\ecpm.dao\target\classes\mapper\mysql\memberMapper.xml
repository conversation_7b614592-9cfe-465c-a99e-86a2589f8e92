<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberMapper">
    <resultMap id="orderBackCallWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrderBackCallWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="orgID" column="orgID" javaType="java.lang.Integer" />
        <result property="orgCode" column="orgCode" javaType="java.lang.String" />
        <result property="productCode" column="productCode" javaType="java.lang.String" />
        <result property="memberName" column="memberName" javaType="java.lang.String" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="status" column="status" javaType="java.lang.Integer" />
        <result property="errCode" column="errCode" javaType="java.lang.String" />
        <result property="errDesc" column="errDesc" javaType="java.lang.String" />
        <result property="orderID" column="orderID" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="extInfo" column="extInfo" javaType="java.lang.String" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="ydpxDeliveryCount" column="ydpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltpxDeliveryCount" column="ltpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxpxDeliveryCount" column="dxpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="ydgdDeliveryCount" column="ydgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltgdDeliveryCount" column="ltgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxgdDeliveryCount" column="dxgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="ydgcDeliveryCount" column="ydgcDeliveryCount" javaType="java.lang.Integer" />
        <result property="isDiff" column="isDiff" javaType="java.lang.String" />
        <result property="isDiff2" column="isDiff2" javaType="java.lang.String" />

        <result property="pxQuota" column="pxQuota" javaType="java.lang.String" />
        <result property="diffPxQuota" column="diffPxQuota" javaType="java.lang.String" />
        <result property="gdQuota" column="gdQuota" javaType="java.lang.String" />
        <result property="difGdQuota" column="difGdQuota" javaType="java.lang.String" />
        <result property="gcQuota" column="gcQuota" javaType="java.lang.String" />
        <result property="orgName" column="orgName" javaType="java.lang.String" />

    </resultMap>

    <resultMap id="memberWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberWrapper">
        <result property="id" column="id" javaType="java.lang.Long" />
        <result property="memberName" column="memberName" javaType="java.lang.String" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="extInfo" column="extInfo" javaType="java.lang.String" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
    </resultMap>

    <resultMap id="memberContentWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberContentWrapper">
        <result property="id" column="id" javaType="java.lang.Long" />
        <result property="orgCode" column="orgCode" javaType="java.lang.String" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="status" column="status" javaType="java.lang.Integer" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="contRuleID" column="contRuleID" javaType="java.lang.String" />
        <result property="memberName" column="memberName" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="parentID" column="parentID" javaType="java.lang.Long" />
        <result property="orgID" column="orgID" javaType="java.lang.Integer" />
    </resultMap>


    <sql id="member_map">
		ID,memberName,msisdn,extInfo,reserved1,reserved2,
		reserved3,reserved4,createTime,updateTime,operatorID
	</sql>

    <update id="updateEcpmOrgMemberByOrderId">
        update ecpm_t_member set
        <trim suffixOverrides="," suffix="where orderID = #{orderID}">
            <if test="errDesc!=null and errDesc!=''">
                errDesc= #{errDesc},
            </if>
            <if test="errCode!=null">
                errCode= #{errCode},
            </if>
            <if test="status!=null">
                status= #{status},
            </if>
        </trim>
    </update>

<!-- orgID必填 ,返回订购名片彩印的成员-->
    <select id="queryMemberByReserved1" resultMap="orderBackCallWrapper">
        select
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.orgCode
        from (
        select
        t1.ID,
        t1.memberName,
        t3.productCode,
        t1.msisdn,
        t3.status,
        t3.orderID,
        t2.enterpriseID,
        t1.createTime,
        t1.updateTime,
        t1.operatorID,
        t1.reserved1,
        t1.reserved2,
        t2.orgCode
        from ecpm_t_member t1 ,ecpm_t_member_subscribe t3,ecpm_t_org_rel t2
        <trim prefix="where" prefixOverrides="and|or">
            t1.ID=t3.memberID and t1.ID=t2.ID and t2.ID=t3.memberID
            and t3.productCode in (
            select t4.productCode from ecpm_t_serv_product t4 where t4.servType in ('1','5')
            union all
            select t5.productCode from ecpm_t_enterprise_code t5
            )
            <if test="memberName != null and memberName != ''">
                and t1.memberName like #{memberName}
            </if>
            <if test="msisdn != null and msisdn != ''">
                and t1.msisdn like #{msisdn}
            </if>
            <if test="reserved1 != null">
                and t1.reserved1= #{reserved1}
            </if>

            <if test="status != null">
                and t3.status= #{status}
            </if>
            <if test="ID != null">
                and t2.ID= #{ID}
            </if>
            <if test="orgCode != null">
                and (t2.orgCode not like concat("%", #{orgCode}, "%") or t2.orgCode is null)
            </if>
            <if test="orgIDs != null  and orgIDs.size()>0" >
                and t2.orgID in
                <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="orgID != null">
                and t2.orgID=#{orgID}
            </if>
        </trim>
        )t group by
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.orgCode
        order by t.ID desc
    </select>

    <!-- orgID必填 ,返回订购名片彩印的成员-->
    <select id="queryMember" resultMap="orderBackCallWrapper">
        select
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.reserved4,
        t.reserved5,
        t.provinceCode,
        t.provinceName,
        t.cityCode,
        t.cityName,
        t.errCode,
        t.errDesc,
        t.orgID,
        t.pxDeliveryStatus,
        t.pxDiffDeliveryStatus,
        t.gdDeliveryStatus,
        t.gdDiffDeliveryStatus,
        t.gcDeliveryStatus,	
		t.ydpxDeliveryCount,
		t.ltpxDeliveryCount,
		t.dxpxDeliveryCount,
		t.ydgdDeliveryCount,
		t.ltgdDeliveryCount,
		t.dxgdDeliveryCount,
		t.ydgcDeliveryCount,
        t.orgName,
		t.isDiff,
        t.isDiff2,
		t.pxQuota,
		t.diffPxQuota,
		t.gdQuota,
        t.sharedQuotaFlag,
		t.difGdQuota,
		t.gcQuota,
		t.oriOrgID
        from (
        select
        t1.ID,
        t1.memberName,
        t3.productCode,
        t1.msisdn,
        t3.status,
        t3.orderID,
        t2.enterpriseID,
        t1.createTime,
        t1.updateTime,
        t1.operatorID,
        t1.reserved1,
        t1.reserved2,
        t1.provinceCode,
        t1.provinceName,
        t1.cityCode,
        t1.cityName,
        t3.errCode,
        t3.errDesc,
        t3.reserved4,
        t3.reserved5,
        t2.orgID,
        t4.pxDeliveryStatus,
        t4.pxDiffDeliveryStatus,
        t4.gdDeliveryStatus,
        t4.gdDiffDeliveryStatus,
        t4.gcDeliveryStatus,	
		IFNULL(t4.ydpxDeliveryCount, 0) ydpxDeliveryCount,
		IFNULL(t4.ltpxDeliveryCount, 0) ltpxDeliveryCount,
		IFNULL(t4.dxpxDeliveryCount, 0) dxpxDeliveryCount,
		IFNULL(t4.ydgdDeliveryCount, 0) ydgdDeliveryCount,
		IFNULL(t4.ltgdDeliveryCount, 0) ltgdDeliveryCount,
		IFNULL(t4.dxgdDeliveryCount, 0) dxgdDeliveryCount,
		IFNULL(t4.ydgcDeliveryCount, 0) ydgcDeliveryCount,
        t5.orgName,
		t5.reserved2 as isDiff,
        t5.reserved8 as isDiff2,
		t5.reserved3 as pxQuota,
		t5.reserved4 as diffPxQuota,
		t5.reserved5 as gdQuota,
		t5.reserved6 as difGdQuota,
		t5.reserved7 as gcQuota,
		t5.reserved10 as sharedQuotaFlag,
		t5.oriOrgID as oriOrgID
        from ecpm_t_member t1 ,ecpm_t_member_subscribe t3
        LEFT JOIN ecpm_t_org_rel t2 ON t2.ID = t3.memberID
        LEFT JOIN ecpm_t_monquota_delivery_stat t4 ON t2.ID = t4.memberID AND t4.status is null
        LEFT JOIN ecpm_t_org_simple t5 ON t2.orgID = t5.id
        <trim prefix="where" prefixOverrides="and|or">
            t1.ID=t3.memberID
            <!-- and t3.productCode in (
            select t4.productCode from ecpm_t_serv_product t4 where t4.servType in ('1','5')
            union all
            select t5.productCode from ecpm_t_enterprise_code t5
            ) -->
            <if test="memberName != null and memberName != ''">
                and t1.memberName like #{memberName}
            </if>
            <if test="msisdn != null and msisdn != ''">
                and t3.msisdn like #{msisdn}
            </if>
            <if test="reserved1 != null">
                and t1.reserved1= #{reserved1}
            </if>
            <if test="status != null">
                and t3.status= #{status}
            </if>
            <if test="null != list">
				and t3.status in
				<foreach item="item" index="index" collection="list" open="("
					separator="," close=")">
					#{item}
				</foreach>
			</if>
            <if test="orgCode != null">
                and t2.orgCode like concat("%", #{orgCode}, "%")
            </if>
            <if test="notLikeOrgCode != null">
                and t2.orgCode not like concat("%", #{notLikeOrgCode}, "%")
            </if>
            <if test="orgCodes != null and orgCodes.size()>0">
                and t2.orgCode REGEXP
                <foreach item="code" index="index" collection="orgCodes" open="(" separator="|" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="notLikeOrgCodes != null  and notLikeOrgCodes.size()>0">
                <foreach item="code2" index="index" collection="notLikeOrgCodes" open="and (" separator="or" close="">
                    t2.orgCode not like concat("%", #{code2}, "%")
                </foreach>
                or orgCode is null )
            </if>
            <if test="orgIDs != null  and orgIDs.size()>0" >
                and (t5.ID in
                <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    OR t5.oriOrgID in
                    <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <if test="ID != null">
                and t2.ID= #{ID}
            </if>
            <if test="orgID != null">
                and (t5.ID=#{orgID}
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    or t5.oriOrgID=#{orgID}
                </if>
                )
            </if>
            <if test="enterpriseID != null">
                and t2.enterpriseID=#{enterpriseID}
            </if>

            <if test="productCode != null and productCode != ''">
                and t3.productCode=#{productCode}
            </if>


            <if test="msisdnList != null  and msisdnList.size()>0" >
                and t3.msisdn in
                <foreach item="msisdn" index="index" collection="msisdnList" open="(" separator="," close=")">
                    #{msisdn}
                </foreach>
            </if>
        </trim>
        )t group by
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.reserved4,
        t.reserved5,
        t.provinceCode,
        t.provinceName,
        t.cityCode,
        t.cityName,
        t.errCode,
        t.errDesc,
        t.orgID,
        t.pxDeliveryStatus,
        t.pxDiffDeliveryStatus,
        t.gdDeliveryStatus,
        t.gdDiffDeliveryStatus,
        t.gcDeliveryStatus,
		t.ydpxDeliveryCount,
		t.ltpxDeliveryCount,
		t.dxpxDeliveryCount,
		t.ydgdDeliveryCount,
		t.ltgdDeliveryCount,
		t.dxgdDeliveryCount,
		t.ydgcDeliveryCount
        order by t.ID desc
        <if test="startIndex != null and pageSize != null">
            limit #{startIndex},#{pageSize}
        </if>

    </select>

    <select id="queryTotalMemberCount" resultType="java.lang.Integer">
        select count(1) from (
        select
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID
        from (
        select
        t1.ID,
        t1.memberName,
        t3.productCode,
        t1.msisdn,
        t3.status,
        t3.orderID,
        t2.enterpriseID,
        t1.createTime,
        t1.updateTime,
        t1.operatorID
        from ecpm_t_member t1 ,ecpm_t_member_subscribe t3,ecpm_t_org_rel t2,ecpm_t_org_simple t6
        <trim prefix="where" prefixOverrides="and|or">
            t1.ID=t3.memberID and t1.ID=t2.ID and t2.ID=t3.memberID and t2.orgID = t6.id
            and t3.productCode in (
            select t4.productCode from ecpm_t_serv_product t4 where t4.servType in ('1','5')
            union all
            select t5.productCode from ecpm_t_enterprise_code t5
            )
            <if test="memberName != null and memberName != ''">
                and t1.memberName like #{memberName}
            </if>
            <if test="msisdn != null and msisdn != ''">
                and t3.msisdn like #{msisdn}
            </if>
            <if test="reserved1 != null">
                and t1.reserved1= #{reserved1}
            </if>
            <if test="status != null">
                and t3.status= #{status}
            </if>
            <if test="null != list">
				and t3.status in
				<foreach item="item" index="index" collection="list" open="("
					separator="," close=")">
					#{item}
				</foreach>
			</if>
            <if test="ID != null">
                and t2.ID= #{ID}
            </if>
            <if test="orgCode != null">
                and t2.orgCode like concat("%", #{orgCode}, "%")
            </if>
            <if test="notLikeOrgCode != null">
                and t2.orgCode not like concat("%", #{notLikeOrgCode}, "%")
            </if>
            <if test="orgCodes != null and orgCodes.size()>0">
                and t2.orgCode REGEXP
                <foreach item="code" index="index" collection="orgCodes" open="(" separator="|" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="notLikeOrgCodes != null  and notLikeOrgCodes.size()>0">
                <foreach item="code2" index="index" collection="notLikeOrgCodes" open="and (" separator="or" close="">
                    t2.orgCode not like concat("%", #{code2}, "%")
                </foreach>
                or orgCode is null )
            </if>
            <if test="orgIDs != null  and orgIDs.size()>0" >
                and (t2.orgID in
                <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    OR t6.oriOrgID in
                    <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <if test="ID != null">
                and t2.ID= #{ID}
            </if>
            <if test="orgID != null">
                and (t2.orgID=#{orgID}
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    or t6.oriOrgID=#{orgID}
                </if>
                )
            </if>
            <if test="productCode != null and productCode != ''">
                and t3.productCode=#{productCode}
            </if>
            <if test="msisdnList != null  and msisdnList.size()>0" >
                and t3.msisdn in
                <foreach item="msisdn" index="index" collection="msisdnList" open="(" separator="," close=")">
                    #{msisdn}
                </foreach>
            </if>
        </trim>
        )t group by
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID)a
    </select>

    <insert id="createMember" keyProperty="id" useGeneratedKeys="true">
		insert into
		ecpm_t_member
		(
		memberName,
		msisdn,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID
		)
		values
		(
		#{memberName},
		#{msisdn},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{createTime},
		#{updateTime},
		#{operatorID}
		)
	</insert>
	
	<update id="batchUpdateSyncStatusSuccess"  parameterType="java.util.List">
	    	update ecpm_t_member 
	    	set
	    	reserved1 = '0'
	    	where 1=1 AND 
	    	<foreach collection="list" item="item" separator="," open="msisdn in (" close=")">
                #{item}
        	</foreach>
	</update>
	
	<update id="batchUpdateSyncStatusError"  parameterType="java.util.List">
	    	update ecpm_t_member 
	    	set
	    	reserved1 = '1'
	    	where 1=1 AND 
	    	<foreach collection="list" item="item" separator="," open="msisdn in (" close=")">
                #{item}
        	</foreach>
	</update>
	
	<update id="updateContentWrapperBatchError">
    update ecpm_t_member set
	    <trim prefix="reserved1=case" suffix="end">
	    <foreach collection="list" item="memberWrapper"  index="index" >
	        when msisdn=#{memberWrapper.msisdn} then '1'
	    </foreach>
	    </trim>
	    <where>
	        <foreach collection="list" item="memberWrapper" separator="or" index="index">
	            msisdn=#{memberWrapper.msisdn}
	        </foreach>
	    </where>
	</update>
	
	<update id="updateContentWrapperBatchSuccess">
    update ecpm_t_member set
	    <trim prefix="reserved1=case" suffix="end">
	    <foreach collection="list" item="memberWrapper"  index="index" >
	        when msisdn=#{memberWrapper.msisdn} then '0'
	    </foreach>
	    </trim>
	    <where>
	        <foreach collection="list" item="memberWrapper" separator="or" index="index">
	            msisdn=#{memberWrapper.msisdn}
	        </foreach>
	    </where>
	</update>
	
	
	<!-- <insert id="createMemberListError" useGeneratedKeys="true" keyProperty="id" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberWrapper">
		insert into
		ecpm_t_member
		(
		msisdn,
		reserved1,
		createTime,
		updateTime
		)
		values
		<foreach collection="list" item="memberWrapper" index="index" separator=",">
      	(#{memberWrapper.msisdn}, '1',
      	#{memberWrapper.createTime},#{memberWrapper.updateTime})
    	</foreach>

	</insert>
	
	<insert id="createMemberListSuccess" useGeneratedKeys="true" keyProperty="id" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberWrapper">
		insert into
		ecpm_t_member
		(
		msisdn,
		reserved1,
		createTime,
		updateTime
		)
		values
		<foreach collection="list" item="memberWrapper" index="index" separator=",">
      	(#{memberWrapper.msisdn}, '0',
      	#{memberWrapper.createTime},#{memberWrapper.updateTime})
    	</foreach>

	</insert> -->

    <update id="updateMember">
		update
		ecpm_t_member
		set
		status=#{status},
		errCode=#{errCode},
		errDesc=#{errDesc},
		orderID=#{orderID}
		where
		ID=#{id}
	</update>
    <update id="changeReserve1ByOrgId">

        UPDATE ecpm_t_member
        SET reserved1 = #{status}
        WHERE
	    id IN (
	        ( SELECT o.id FROM ecpm_t_org_rel o WHERE o.orgID = #{orgId} )
	    )

    </update>

    <update id="changeReserve2ByMsisdn">

        UPDATE ecpm_t_member
        SET reserved2 = #{type}
        WHERE
	    msisdn = #{msisdn}
    </update>

    <!--根据手机号查询成员信息 -->
    <select id="queryMemberByMsisdn" resultMap="memberWrapper">
        SELECT
        t.ID,
        t.memberName,
        t.msisdn,
        t.extInfo,
        t.reserved1,
        t.reserved2,
        t.reserved3,
        t.reserved4,
        t.createTime,
        t.updateTime,
        t.operatorID
        from
        ecpm_t_member t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="msisdn != null and msisdn !=''">
                and t.msisdn= #{msisdn}
            </if>
        </trim>
        order by t.updateTime desc
    </select>

    <select id="queryMemberByMsisdnAndOrg" resultMap="memberWrapper">
		SELECT
		t.id,
		t1.orgID,
		t.memberName,
		t.msisdn,
		t1.enterpriseID,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.createTime,
		t.updateTime,
		t.operatorID
		from
		ecpm_t_member t,ecpm_t_org_rel t1
		where t.ID = t1.ID
		and t.msisdn= #{msisdn}
        <if test="null != orgID and orgID != '' ">
            and t1.orgID= #{orgID}
        </if>
	</select>


    <select id="queryMemberByEnterpriseID" resultMap="memberWrapper">
        SELECT
        t.id,
        t1.orgID,
        t.memberName,
        t.msisdn,
        t1.enterpriseID,
        t.extInfo,
        t.reserved1,
        t.reserved2,
        t.reserved3,
        t.reserved4,
        t.createTime,
        t.updateTime,
        t.operatorID
        from
        ecpm_t_org_rel t1 left join ecpm_t_member t on t.id = t1.ID
        where t1.enterpriseID= #{enterpriseID}
    </select>

    <delete id="deleteMemberByMemIDAndOrgID">
        delete from ecpm_t_member where
        ID in
        <foreach item="id" index="index" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryMemberByIds" resultMap="memberWrapper" parameterType="java.util.List">
        SELECT
        <include refid="member_map" />
        from
        ecpm_t_member
        where ID in
        <foreach collection="list" item="id" open="(" separator="," close=")">
                #{id}
        </foreach>
    </select>

    <select id="queryMemberIsExists" resultType="java.lang.Integer">
		SELECT
		count(t.id)
		from
		ecpm_t_member t,ecpm_t_org_rel t1
		where t.ID = t1.ID
		and t.msisdn= #{msisdn}
	</select>
    <select id="queryMemberIsExistsReturn" resultType="java.lang.Integer">
        SELECT
            t1.enterpriseId
        from
            ecpm_t_member t,ecpm_t_org_rel t1
        where t.ID = t1.ID
          and t.msisdn= #{msisdn}
    </select>
    <select id="queryMemberUnique" resultType="java.lang.Integer">
		SELECT
		count(0)
		from
		ecpm_t_member t,ecpm_t_org_rel t1
		where t.ID = t1.ID
		and t.msisdn=#{msisdn}
		and t1.enterpriseID in(
		select distinct(t.enterpriseID)
		from ecpm_t_org_rel t where t.orgID = #{orgID})
	</select>
    <delete id="deleteOrgRelByOrgID" parameterType="java.lang.Integer">
		delete from ecpm_t_org_rel where orgID=#{orgID}
	</delete>

    <select id="queryOrgMemberListByContID" resultMap="memberContentWrapper" parameterType="java.util.List">
		select m.ID,org.orgCode,m.msisdn from ecpm_t_content
		t,ecpm_t_content_org org,ecpm_t_org_rel rel,ecpm_t_member m,ecpm_t_member_subscribe ms,ecpm_t_serv_product p
		where t.ID = #{contID} and t.ID
		= org.cyContID and org.ownerId =rel.orgID and rel.id = m.id and m.id = ms.memberID and ms.status = 5 and ms.productCode =
		p.productCode and (p.servType = 1 or p.servType = 5)
	</select>

    <select id="queryAllOrgMemberListByContID" resultMap="memberContentWrapper" parameterType="java.util.List">
		select m.ID,org.orgCode,m.msisdn, m.memberName,m.reserved2,rel.orgID,rel.enterpriseID from ecpm_t_content
		t,ecpm_t_content_org org,ecpm_t_org_rel rel,ecpm_t_member m,ecpm_t_member_subscribe ms,ecpm_t_serv_product p
		where t.ID = #{contID} and t.ID
		= org.cyContID and org.ownerId =rel.orgID and rel.id = m.id and m.id = ms.memberID and (ms.status = 5 or ms.status = 3 or ms.status = 13) and ms.productCode =
		p.productCode and p.servType = 1
	</select>
	
	 <select id="queryAllOrgMemberListByContIDToHotline" resultMap="memberContentWrapper" parameterType="java.util.List">
		select m.ID,org.orgCode,m.msisdn from ecpm_t_content
		t,ecpm_t_content_org org,ecpm_t_org_rel rel,ecpm_t_member m,ecpm_t_member_subscribe ms,ecpm_t_serv_product p
		where t.ID = #{contID} and t.ID
		= org.cyContID and org.ownerId =rel.orgID and rel.id = m.id and m.id = ms.memberID and (ms.status = 5 or ms.status = 3) and ms.productCode =
		p.productCode and p.servType = 5
	</select>

    <select id="queryDeductionMemberCountByContentID" resultType="java.lang.Long">
        select
        count(*) as count from ecpm_t_content
        t,ecpm_t_content_org
        org,ecpm_t_org_rel rel,ecpm_t_member
        m,ecpm_t_member_subscribe
        ms,ecpm_t_serv_product p
        where t.ID = #{contID} and t.ID
        = org.cyContID
        and org.ownerId =rel.orgID and rel.id = m.id
        and
        m.id = ms.memberID
        and ms.productCode = p.productCode
        and (p.servType = 1 or p.servType = 5)
        <if test="uselessStatusList != null and uselessStatusList.size()>0">
            and
            <foreach collection="uselessStatusList" item="uselessStatus" open="(" separator="and" close=")">
                ms.status != #{uselessStatus}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and
            <foreach item="status" collection="statusList" open="(" separator="or" close=")">
                ms.status = #{status}
            </foreach>
        </if>
    </select>

    <select id="queryMemberListForDealPersonalContractOrde" resultMap="memberContentWrapper">
		SELECT t1.enterpriseID,t1.contRuleID,t1.orgCode,t1.ID,t1.msisdn,t1.status,t1.subServType, t1.parentID from
		(select
		cont.enterpriseID,cont.ID as contentID,cont.contRuleID as contRuleID,org.orgCode,memb.ID,memb.msisdn,sub.status,cont.subServType, cont.parentID from
		ecpm_t_content cont,ecpm_t_content_org org,ecpm_t_org_rel rel,ecpm_t_member memb,ecpm_t_member_subscribe sub,ecpm_t_serv_product p
		where cont.deductTime <![CDATA[ > ]]> #{beginTime} and cont.ID = org.cyContID and org.ownerId = rel.orgID and rel.ID = memb.ID and memb.ID = sub.memberID and sub.productCode =p.productCode and cont.thirdpartyType = 0 and (cont.servType = 1 or cont.servType = 5) and
		cont.chargeType = 2
		and cont.approveStatus = 3 and cont.status = 0 and p.servType = 1
		) t1
		order by
		t1.enterpriseID asc,t1.contentID asc,t1.ID asc limit #{pageNum},#{pageSize}
	</select>


    <select id="queryMemberByMemberID" resultMap="memberWrapper">
		SELECT
		ID,
		memberName,
		msisdn,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID
		from ecpm_t_member
		where ID=#{id}
	</select>
	
	<select id="queryMemberByMemberIDs" resultMap="memberWrapper">
		SELECT
		ID,
		memberName,
		msisdn,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID
		from ecpm_t_member
		where 1=1
		<if test="id != null">
                and ID=#{id}
        </if>
        <if test="null != list">
			and ID in
			<foreach item="item" index="index" collection="list" open="("
				separator="," close=")">
				#{item}
			</foreach>
		</if>
	</select>
	
    <select id="queryMemberByReserved2" resultMap="memberWrapper">
		SELECT
		ID,
		memberName,
		msisdn,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID
		from ecpm_t_member
		where reserved2=#{reserved2}
	</select>

	<select id="queryDeductionMemberCountByOrgCont" resultType="java.lang.Long">
        select
        count(*) as count from ecpm_t_org_rel rel,ecpm_t_member
        m,ecpm_t_member_subscribe
        ms,ecpm_t_serv_product p
        where rel.id = m.id
        and
        m.id = ms.memberID
        and ms.productCode = p.productCode
        and (p.servType = 1 or p.servType = 5)
        <if test="orgIdList != null and orgIdList.size()>0">
            and
            <foreach item="orgId" collection="orgIdList" open="(" separator="or" close=")">
                rel.orgID = #{orgId}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and
            <foreach item="status" collection="statusList" open="(" separator="or" close=")">
                ms.status = #{status}
            </foreach>
        </if>
    </select>
    
    <select id="queryAllOrgMemberListByOrgIDContID" resultMap="memberContentWrapper" parameterType="java.util.List">
		select m.ID,org.orgCode,m.msisdn from ecpm_t_content_org org,ecpm_t_org_rel rel,ecpm_t_member m,ecpm_t_member_subscribe ms,ecpm_t_serv_product p
		where org.ownerId =rel.orgID and rel.id = m.id and m.id = ms.memberID and (ms.status = 5 or ms.status = 3) and ms.productCode =
		p.productCode and p.servType = 1
		<if test="orgIdList != null and orgIdList.size()>0">
            and
            <foreach item="orgId" collection="orgIdList" open="(" separator="or" close=")">
                org.ownerId = #{orgId}
            </foreach>
        </if>
	</select>


    <select id="queryCollectMember" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.CollectMemberInfoWrapper">
      SELECT
        con.subServTypes,con.servTypes,m.msisdn,m.reserved2 msisdnType,m.memberName,m.id,m.createTime, ms.status,rel.orgID
        ,es1.ID enterpriseID,es1.enterpriseName,es2.ID parentEnterpriseID,es2.enterpriseName parentEnterpriseName
        ,es1.enterpriseType,es1.reserved10,es1.provinceID,es1.cityID,ms.reserved4,es1.countyID,os.orgName,os1.ID as oriOrgID,
        os1.orgName as oriOrgName, con.hangupType,
        con.approveStatuss,
        con.approveIdeas,
        con.contents,
        ms.errDesc subFailedReason
        FROM
        ecpm_t_org_rel rel
        <if test="enterpriseID != null">
            force index(index_enterpriseID_orgID)
        </if>

        LEFT JOIN ecpm_t_member m ON m.ID = rel.ID
        LEFT JOIN (
          SELECT
            GROUP_CONCAT(DISTINCT con.subServType ) AS subServTypes,
            GROUP_CONCAT(DISTINCT con.servType ) AS servTypes,
            GROUP_CONCAT( CONCAT( con.subServType, ":", con.approveStatus ) ) AS approveStatuss,
            GROUP_CONCAT( CONCAT( con.subServType, ":", con.approveIdea ) ) AS approveIdeas,
            GROUP_CONCAT( CONCAT( con.subServType, ":", con.content ) ) AS contents,
            crel.ownerID,
            con.hangupType
          FROM
            (SELECT
                IF( con.subServType = 1 AND con.parentID IS NOT NULL, 3, con.subServType ) subServType,
                con.servType,
                con.id,
				con.hangupType,
                con.approveStatus,
                con.approveIdea,
                IF( con.reserved10 != NULL, CONCAT( "【", con.reserved10, "】", con.content ), con.content ) content
        FROM
                ecpm_t_content con
            WHERE
              (con.subServType = 1 OR con.parentID IS NULL)
                <if test="enterpriseID != null">
                    AND con.enterpriseID = #{enterpriseID}
                </if>
            ) con,
          ecpm_t_content_org crel
        WHERE
          con.id = crel.cyContID
        AND servType IN ( 1, 5 )
        <if test="servTypeList != null and servTypeList.size()>0">
            and con.servType in
            <foreach item="servType" collection="servTypeList" open="(" separator="," close=")">
                #{servType}
            </foreach>
        </if>
        <if test="subServTypeList != null and subServTypeList.size()>0">
            and con.subServType in
            <foreach item="subServType" collection="subServTypeList" open="(" separator="," close=")">
                #{subServType}
            </foreach>
            <if test="hangupType != null">
                <if test="hangupType == 1">and con.hangupType = #{hangupType}</if>
                <if test="hangupType == 2">and (con.hangupType = #{hangupType} or con.hangupType is null)</if>
                <if test="hangupType == 3">and con.hangupType = #{hangupType}</if>
            </if>
            <if test="hangupType == null">
            	and con.hangupType is null
            </if>
        </if>

        <if test="contentApproveStatus != null ">
            AND con.approveStatus = #{contentApproveStatus}
        </if>
        GROUP BY
        crel.ownerID,con.hangupType) con ON con.ownerID = rel.orgID
      LEFT JOIN ecpm_t_member_subscribe ms ON ms.memberID = rel.id
      STRAIGHT_JOIN ecpm_t_enterprise_simple es1 ON es1.ID  = rel.enterpriseID
      LEFT JOIN ecpm_t_enterprise_simple es2 on es1.parentEnterpriseID = es2.id
      LEFT JOIN ecpm_t_org_simple os ON os.id = rel.orgID
      LEFT JOIN ecpm_t_org_simple os1 ON os1.ID = os.oriOrgID
      WHERE m.id is not null
        <if test="enterpriseID != null">
            AND rel.enterpriseID = #{enterpriseID}
        </if>
        <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
            and rel.enterpriseID in ( SELECT
            e.id
            FROM
            ecpm_t_enterprise_simple e
            LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
            where
            ((
            e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
            or
            (
            e.enterpriseType = 5
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (e.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or e.provinceID is null
                </if>
                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (e.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or e.cityID is null
                </if>

                )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (e.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or e.countyID is null
                </if>
                )
            </if>
            )
            )
            )
        </if>
        <if test="parentEnterpriseID != null ">
            AND es2.id = #{parentEnterpriseID}
        </if>
        <if test="enterpriseIDs != null and enterpriseIDs.size()>0">
            and rel.enterpriseID in
            <foreach item="enterpriseID" collection="enterpriseIDs" open="(" separator="," close=")">
                #{enterpriseID}
            </foreach>
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            AND es1.enterpriseName like concat("%", #{enterpriseName}, "%")
        </if>
        <if test="parentEnterpriseName != null and parentEnterpriseName != ''">
            AND es2.enterpriseName like concat("%", #{parentEnterpriseName}, "%")
        </if>

        <if test="enterpriseTypes != null and enterpriseTypes.size()>0">
            and es1.enterpriseType in
            <foreach item="enterpriseType" collection="enterpriseTypes" open="(" separator="," close=")">
                #{enterpriseType}
            </foreach>
        </if>

        <if test="msisdn != null and msisdn != ''">
            AND m.msisdn = #{msisdn}
        </if>
        <if test="msisdnType != null and msisdnType != ''">
            AND m.reserved2 = #{msisdnType}
        </if>
        <if test="status != null and status.size()>0">
            AND ms.status in
            <foreach item="sta" collection="status" open="(" separator="," close=")">
                #{sta}
            </foreach>
        </if>
        <if test="orgName != null and orgName != ''">
            AND ((os1.orgName like concat("%", #{orgName}, "%") and os.oriOrgID is not null)
            OR (os.oriOrgID is null and os.orgName like concat("%", #{orgName}, "%")))
        </if>
        <if test="orgIds != null and orgIds.size()>0">
            and (os.ID in
            <foreach item="id" collection="orgIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            or os.oriOrgID in
            <foreach item="id" collection="orgIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        <if test="servTypeList != null and servTypeList.size()>0">
            and con.servTypes is not null
        </if>
        <if test="subServTypeList != null and subServTypeList.size()>0">
            and con.subServTypes is not null
        </if>
        <if test="contentApproveStatus != null and contentApproveStatus != ''">
            and con.approveStatuss is not null
        </if>
        <if test="provinceID != null and provinceID != '' and  cityID != null and cityID != ''">
      		and (es1.provinceID = #{provinceID} or es1.provinceID = 999 ) and (es1.cityID = #{cityID} or es1.cityID = 9999 )
        </if>
        <if test="provinceID != null and provinceID != '' and  (cityID == null or cityID == '')">
      		and (es1.provinceID = #{provinceID} or es1.provinceID = 999 )
        </if>
        <if test='channel != null and channel != "" and channel == "1"'>
        		and es1.enterpriseType = 1
        </if>
        <if test='channel != null and channel != "" and channel == "2"'>
        		and es1.enterpriseType = 3
        </if>
        <if test='channel != null and channel != "" and channel == "4"'>
        		and es1.enterpriseType = 4
        </if>
        <if test='channel != null and channel != "" and channel == "5"'>
            and es1.enterpriseType = 5 and ((es1.reserved10 != '111' and es1.`reserved10` != '112') OR es1.`reserved10` IS NULL)
        </if>
        <if test='channel != null and channel != "" and channel == "6"'>
        		and es1.enterpriseType = 4
        </if>
        <if test='channel != null and channel != "" and channel == "15"'>
        		and es1.enterpriseType = 5 and es1.reserved10 = '111'
        </if>
        <if test='channel != null and channel != "" and channel == "25"'>
            and es1.enterpriseType = 5 and es1.reserved10 = '112'
        </if>
        <if test='channel != null and channel != "" and channel == "35"'>
            and es1.enterpriseType = 5 and es1.reserved10 = '113'
        </if>
        <if test='countyUnlimit != null and countyUnlimit != "" and countyUnlimit == "1"'>
            AND (es1.countyID IS NULL
            <if test="countyIDs != null and countyIDs.size > 0">
                OR es1.countyID IN
                <foreach item="countyIDs" index="index" collection="countyIDs"
                         open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
            )
        </if>
        <if test='countyUnlimit != null and countyUnlimit != "" and countyUnlimit != "1"'>
            <if test="countyIDs != null and countyIDs.size > 0">
                and es1.countyID IN
                <foreach item="countyIDs" index="index" collection="countyIDs"
                         open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
        </if>
        <if test="reserved4_in!=null">
            and (ms.reserved4 = '1' OR ms.reserved4 = '2')
        </if>
        <if test="reserved4_is_null!=null">
            and ms.reserved4 is null
        </if>
        <if test="reserved4_is_null_or_in !=null" >
            and ( ms.reserved4 is null or  ms.reserved4 in (3,4) )
        </if>
        <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
            and es1.reserved10 in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>

        <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
            and es1.reserved10 not in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>
        order by rel.enterpriseID,rel.orgID
        <if test="startIndex != null and pageSize != null">
            limit #{startIndex},#{pageSize}
        </if>
    </select>

    <select id="queryCollectMemberCount" resultType="integer">
        select count(*) from (
        SELECT
        con.subServTypes,con.servTypes,m.msisdn,m.reserved2 msisdnType,m.memberName,m.id,m.createTime, ms.status,rel.orgID
        ,es1.ID enterpriseID,es1.enterpriseName,es2.ID parentEnterpriseID,es2.enterpriseName parentEnterpriseName, con.hangupType
        FROM
        ecpm_t_org_rel rel
        <if test="enterpriseID != null">
            force index(index_enterpriseID_orgID)
        </if>
        LEFT JOIN ecpm_t_member m ON m.ID = rel.ID
        LEFT JOIN (
        SELECT
        GROUP_CONCAT(DISTINCT con.subServType ) AS subServTypes,
        GROUP_CONCAT(DISTINCT con.servType ) AS servTypes,
        GROUP_CONCAT(con.approveStatus ) AS approveStatuss,

        crel.ownerID,
        con.hangupType
        FROM
        (SELECT
        IF( con.subServType = 1 AND con.parentID IS NOT NULL, 3, con.subServType ) subServType,
        con.servType,
        con.id,
        con.hangupType,
        con.approveStatus
        FROM
        ecpm_t_content con
        WHERE
        (con.subServType = 1 OR con.parentID IS NULL)
        <if test="enterpriseID != null">
            AND con.enterpriseID = #{enterpriseID}
        </if>
        ) con,
        ecpm_t_content_org crel
        WHERE
        con.id = crel.cyContID
        AND servType IN ( 1, 5 )
        <if test="servTypeList != null and servTypeList.size()>0">
            and con.servType in
            <foreach item="servType" collection="servTypeList" open="(" separator="," close=")">
                #{servType}
            </foreach>
        </if>
        <if test="subServTypeList != null and subServTypeList.size()>0">
            and con.subServType in
            <foreach item="subServType" collection="subServTypeList" open="(" separator="," close=")">
                #{subServType}
            </foreach>
            <if test="hangupType != null">
                <if test="hangupType == 1">and con.hangupType = #{hangupType}</if>
                <if test="hangupType == 2">and (con.hangupType = #{hangupType} or con.hangupType is null)</if>
                <if test="hangupType == 3">and con.hangupType = #{hangupType}</if>
            </if>
            <if test="hangupType == null">
            	and con.hangupType is null
            </if>
        </if>
        <if test="contentApproveStatus != null ">
            AND con.approveStatus = #{contentApproveStatus}
        </if>
        GROUP BY
        crel.ownerID,con.hangupType ) con ON con.ownerID = rel.orgID
        LEFT JOIN ecpm_t_member_subscribe ms ON ms.memberID = rel.id
        LEFT JOIN ecpm_t_enterprise_simple es1 ON es1.ID  = rel.enterpriseID
        LEFT JOIN ecpm_t_enterprise_simple es2 on es1.parentEnterpriseID = es2.id
        LEFT JOIN ecpm_t_org_simple os ON os.id = rel.orgID
        LEFT JOIN ecpm_t_org_simple os1 ON os1.ID = os.oriOrgID
        WHERE m.id is not null
        <if test="enterpriseID != null">
            AND rel.enterpriseID = #{enterpriseID}
        </if>
        <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
            and rel.enterpriseID in ( SELECT
            e.id
            FROM
            ecpm_t_enterprise_simple e
            LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID = e.parentEnterpriseID
            where
            ((
            e.enterpriseType != 5 and e.enterpriseType != 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
				or
				(
				e.enterpriseType = 3
				<if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
					and e2.provinceID in
					<foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
						#{provinceID}
					</foreach>
				</if>
				)
            or
            (
            e.enterpriseType = 5
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (e.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or e.provinceID is null
                </if>
                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (e.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or e.cityID is null
                </if>

                )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (e.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or e.countyID is null
                </if>
                )
            </if>
            )
            )
            )
        </if>
        <if test="parentEnterpriseID != null ">
            AND es2.id = #{parentEnterpriseID}
        </if>
        <if test="enterpriseIDs != null and enterpriseIDs.size()>0">
            and rel.enterpriseID in
            <foreach item="enterpriseID" collection="enterpriseIDs" open="(" separator="," close=")">
                #{enterpriseID}
            </foreach>
        </if>
        <if test="enterpriseName != null and enterpriseName != ''">
            AND es1.enterpriseName like concat("%", #{enterpriseName}, "%")
        </if>
        <if test="parentEnterpriseName != null and parentEnterpriseName != ''">
            AND es2.enterpriseName like concat("%", #{parentEnterpriseName}, "%")
        </if>

        <if test="enterpriseTypes != null and enterpriseTypes.size()>0">
            and es1.enterpriseType in
            <foreach item="enterpriseType" collection="enterpriseTypes" open="(" separator="," close=")">
                #{enterpriseType}
            </foreach>
        </if>
        <if test="msisdn != null and msisdn != ''">
            AND m.msisdn = #{msisdn}
        </if>
        <if test="msisdnType != null and msisdnType != ''">
            AND m.reserved2 = #{msisdnType}
        </if>
        <if test="status != null and status.size()>0">
            AND ms.status in
            <foreach item="sta" collection="status" open="(" separator="," close=")">
                #{sta}
            </foreach>
        </if>
        <if test="orgName != null and orgName != ''">
            AND ((os1.orgName like concat("%", #{orgName}, "%") and os.oriOrgID is not null)
            OR (os.oriOrgID is null and os.orgName like concat("%", #{orgName}, "%")))
        </if>
        <if test="orgIds != null and orgIds.size()>0">
            and (os.ID in
            <foreach item="id" collection="orgIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            or os.oriOrgID in
            <foreach item="id" collection="orgIds" open="(" separator="," close=")">
                #{id}
            </foreach>
            )
        </if>
        <if test="servTypeList != null and servTypeList.size()>0">
            and con.servTypes is not null
        </if>
        <if test="subServTypeList != null and subServTypeList.size()>0">
            and con.subServTypes is not null
        </if>
        <if test="contentApproveStatus != null and contentApproveStatus != ''">
            and con.approveStatuss is not null
        </if>
        <if test="provinceID != null and provinceID != '' and  cityID != null and cityID != ''">
            and (es1.provinceID = #{provinceID} or es1.provinceID = 999 ) and (es1.cityID = #{cityID} or es1.cityID = 9999 )
        </if>
        <if test="provinceID != null and provinceID != '' and  (cityID == null or cityID == '')">
            and (es1.provinceID = #{provinceID} or es1.provinceID = 999 )
        </if>
        <if test='channel != null and channel != "" and channel == "1"'>
        		and es1.enterpriseType = 1
        </if>
        <if test='channel != null and channel != "" and channel == "2"'>
        		and es1.enterpriseType = 3
        </if>
        <if test='channel != null and channel != "" and channel == "4"'>
        		and es1.enterpriseType = 4
        </if>
        <if test='channel != null and channel != "" and channel == "5"'>
            and es1.enterpriseType = 5 and ((es1.reserved10 != '111' and es1.`reserved10` != '112') OR es1.`reserved10` IS NULL)
        </if>
        <if test='channel != null and channel != "" and channel == "6"'>
        		and es1.enterpriseType = 4
        </if>
        <if test='channel != null and channel != "" and channel == "15"'>
        		and es1.enterpriseType = 5 and es1.reserved10 = '111'
        </if>
        <if test='channel != null and channel != "" and channel == "25"'>
            and es1.enterpriseType = 5 and es1.reserved10 = '112'
        </if>
        <if test='channel != null and channel != "" and channel == "35"'>
            and es1.enterpriseType = 5 and es1.reserved10 = '113'
        </if>
        <if test='countyUnlimit != null and countyUnlimit != "" and countyUnlimit == "1"'>
            AND (es1.countyID IS NULL
            <if test="countyIDs != null and countyIDs.size > 0">
                OR es1.countyID IN
                <foreach item="countyIDs" index="index" collection="countyIDs"
                         open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
            )
        </if>
        <if test='countyUnlimit != null and countyUnlimit != "" and countyUnlimit != "1"'>
            <if test="countyIDs != null and countyIDs.size > 0">
                and es1.countyID IN
                <foreach item="countyIDs" index="index" collection="countyIDs"
                         open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
        </if>
        <if test="reserved4_in!=null">
            and (ms.reserved4 = '1' OR ms.reserved4 = '2')
        </if>
        <if test="reserved4_is_null!=null">
            and ms.reserved4 is null
        </if>
        <if test="reserved4_is_null_or_in !=null" >
            and ( ms.reserved4 is null or ms.reserved4 in (3,4))
        </if>
        <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
            and es1.reserved10 in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>

        <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
            and es1.reserved10 not in
            <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                     open="(" separator="," close=")">
                #{dAuth}
            </foreach>
        </if>
        ) a
    </select>
    
     <select id="queryMemberByMsisdns" resultMap="memberWrapper" parameterType="java.util.List">
        SELECT
        <include refid="member_map" />
        from
        ecpm_t_member
        where msisdn in
        <foreach collection="list" item="msisdn" open="(" separator="," close=")">
            #{msisdn}
        </foreach>
    </select>

    <select id="queryMemberIdByMsisdns" resultType="java.lang.Long">
        SELECT
        id
        from
        ecpm_t_member
        where msisdn = #{msisdn}

    </select>
	
	<select id="queryOrgMemberSub" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgMemberSubWrapper">
		SELECT 
			a.enterpriseID, 
			a.ID as memberID, 
			d.msisdn,
			d.memberName,
			d.reserved2 as msisdnType,
			b.ID as orgID, 
			a.orgCode,
			b.orgType, 
			b.orgName,
			c.status,
			c.subSuccessTime,
			d.createTime,
			b.createTime as orgCreateTime
		FROM 
			ecpm_t_org_rel a 
			LEFT JOIN ecpm_t_org_simple b ON a.orgID = b.ID
			LEFT JOIN ecpm_t_member_subscribe c ON a.ID = c.memberID
			LEFT JOIN ecpm_t_member d ON a.ID = d.ID
		WHERE 
			a.enterpriseID = #{enterpriseID}
			AND b.orgType = #{orgType}
	</select>


    <update id="updateMemberProvinceAndCity">
        update ecpm_t_member set
        <trim suffixOverrides="," suffix="where msisdn = #{msisdn}">
            <if test="provinceCode!=null and provinceCode!=''">
                provinceCode= #{provinceCode},
            </if>
            <if test="provinceName!=null and provinceName!=''">
                provinceName= #{provinceName},
            </if>
            <if test="cityCode!=null and cityCode!=''">
                cityCode= #{cityCode},
            </if>
            <if test="cityName!=null and cityName!=''">
                cityName= #{cityName},
            </if>
        </trim>
    </update>

    <select id="queryMemberNew" resultMap="orderBackCallWrapper">
        select
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.reserved4,
        t.reserved5,
        t.provinceCode,
        t.provinceName,
        t.cityCode,
        t.cityName,
        t.errCode,
        t.errDesc,
        t.orgID,
        t.pxDeliveryStatus,
        t.pxDiffDeliveryStatus,
        t.gdDeliveryStatus,
        t.gdDiffDeliveryStatus,
        t.gcDeliveryStatus,
        t.ydpxDeliveryCount,
        t.ltpxDeliveryCount,
        t.dxpxDeliveryCount,
        t.ydgdDeliveryCount,
        t.ltgdDeliveryCount,
        t.dxgdDeliveryCount,
        t.ydgcDeliveryCount,
        t.orgName,
        t.isDiff,
        t.isDiff2,
        t.pxQuota,
        t.diffPxQuota,
        t.gdQuota,
        t.sharedQuotaFlag,
        t.difGdQuota,
        t.gcQuota,
        t.oriOrgID
        from (
        select
        t1.ID,
        t1.memberName,
        t3.productCode,
        t1.msisdn,
        t3.status,
        t3.orderID,
        t2.enterpriseID,
        t1.createTime,
        t1.updateTime,
        t1.operatorID,
        t1.reserved1,
        t1.reserved2,
        t1.provinceCode,
        t1.provinceName,
        t1.cityCode,
        t1.cityName,
        t3.errCode,
        t3.errDesc,
        t3.reserved4,
        t3.reserved5,
        t2.orgID,
        t4.pxDeliveryStatus,
        t4.pxDiffDeliveryStatus,
        t4.gdDeliveryStatus,
        t4.gdDiffDeliveryStatus,
        t4.gcDeliveryStatus,
        IFNULL(t4.ydpxDeliveryCount, 0) ydpxDeliveryCount,
        IFNULL(t4.ltpxDeliveryCount, 0) ltpxDeliveryCount,
        IFNULL(t4.dxpxDeliveryCount, 0) dxpxDeliveryCount,
        IFNULL(t4.ydgdDeliveryCount, 0) ydgdDeliveryCount,
        IFNULL(t4.ltgdDeliveryCount, 0) ltgdDeliveryCount,
        IFNULL(t4.dxgdDeliveryCount, 0) dxgdDeliveryCount,
        IFNULL(t4.ydgcDeliveryCount, 0) ydgcDeliveryCount,
        t5.orgName,
        t5.reserved2 as isDiff,
        t5.reserved8 as isDiff2,
        t5.reserved3 as pxQuota,
        t5.reserved4 as diffPxQuota,
        t5.reserved5 as gdQuota,
        t5.reserved6 as difGdQuota,
        t5.reserved7 as gcQuota,
        t5.reserved10 as sharedQuotaFlag,
        t5.oriOrgID as oriOrgID
        from ecpm_t_member t1 ,ecpm_t_member_subscribe t3
        LEFT JOIN ecpm_t_org_rel t2 ON t2.ID = t3.memberID
        LEFT JOIN ecpm_t_monquota_delivery_stat t4 ON t2.ID = t4.memberID AND t4.status is null
        LEFT JOIN ecpm_t_org_simple t5 ON t2.orgID = t5.id
        LEFT JOIN ecpm_t_content_org t6 ON t6.ownerType = 1 AND t6.ownerID = t2.orgID
        <trim prefix="where" prefixOverrides="and|or">
            t1.ID=t3.memberID
            <!-- and t3.productCode in (
            select t4.productCode from ecpm_t_serv_product t4 where t4.servType in ('1','5')
            union all
            select t5.productCode from ecpm_t_enterprise_code t5
            ) -->
            <if test="memberName != null and memberName != ''">
                and t1.memberName like #{memberName}
            </if>
            <if test="msisdn != null and msisdn != ''">
                and t3.msisdn like #{msisdn}
            </if>
            <if test="reserved1 != null">
                and t1.reserved1= #{reserved1}
            </if>
            <if test="status != null">
                and t3.status= #{status}
            </if>
            <if test="null != list">
                and t3.status in
                <foreach item="item" index="index" collection="list" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="syncGroupMemStatus != null and syncGroupMemStatus == 1">
                AND t6.orgCode IS NOT NULL and t2.orgCode like concat("%", t6.orgCode, "%")
            </if>
            <if test="syncGroupMemStatus != null and syncGroupMemStatus == 2">
                AND t6.orgCode IS NOT NULL and (t2.orgCode not like concat("%", t6.orgCode, "%") or t2.orgCode is null)
            </if>
            <if test="orgCode != null">
                and t2.orgCode like concat("%", #{orgCode}, "%")
            </if>
            <if test="notLikeOrgCode != null">
                and (t2.orgCode not like concat("%", #{notLikeOrgCode}, "%") or t2.orgCode is null)
            </if>
            <if test="orgCodes != null and orgCodes.size()>0">
                and t2.orgCode REGEXP
                <foreach item="code" index="index" collection="orgCodes" open="(" separator="|" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="notLikeOrgCodes != null  and notLikeOrgCodes.size()>0">
                <foreach item="code2" index="index" collection="notLikeOrgCodes" open="and (" separator="or" close="">
                    t2.orgCode not like concat("%", #{code2}, "%")
                </foreach>
                or orgCode is null )
            </if>
            <if test="orgIDs != null  and orgIDs.size()>0" >
                and (t5.ID in
                <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    OR t5.oriOrgID in
                    <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <if test="ID != null">
                and t2.ID= #{ID}
            </if>
            <if test="orgID != null">
                and (t5.ID=#{orgID}
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    or t5.oriOrgID=#{orgID}
                </if>
                )
            </if>
            <if test="enterpriseID != null">
                and t2.enterpriseID=#{enterpriseID}
            </if>

            <if test="productCode != null and productCode != ''">
                and t3.productCode=#{productCode}
            </if>


            <if test="msisdnList != null  and msisdnList.size()>0" >
                and t3.msisdn in
                <foreach item="msisdn" index="index" collection="msisdnList" open="(" separator="," close=")">
                    #{msisdn}
                </foreach>
            </if>
            <if test="contentList != null  and contentList.size()>0" >
                and t6.cyContID in
                <foreach item="cyContID" index="index" collection="contentList" open="(" separator="," close=")">
                    #{cyContID}
                </foreach>
            </if>
        </trim>
        )t group by
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.reserved4,
        t.reserved5,
        t.provinceCode,
        t.provinceName,
        t.cityCode,
        t.cityName,
        t.errCode,
        t.errDesc,
        t.orgID,
        t.pxDeliveryStatus,
        t.pxDiffDeliveryStatus,
        t.gdDeliveryStatus,
        t.gdDiffDeliveryStatus,
        t.gcDeliveryStatus,
        t.ydpxDeliveryCount,
        t.ltpxDeliveryCount,
        t.dxpxDeliveryCount,
        t.ydgdDeliveryCount,
        t.ltgdDeliveryCount,
        t.dxgdDeliveryCount,
        t.ydgcDeliveryCount
        order by t.ID desc
        <if test="startIndex != null and pageSize != null">
            limit #{startIndex},#{pageSize}
        </if>

    </select>

    <select id="queryTotalMemberCountNew" resultType="java.lang.Integer">
        SELECT count(1) from (
        select
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.reserved4,
        t.reserved5,
        t.provinceCode,
        t.provinceName,
        t.cityCode,
        t.cityName,
        t.errCode,
        t.errDesc,
        t.orgID,
        t.pxDeliveryStatus,
        t.pxDiffDeliveryStatus,
        t.gdDeliveryStatus,
        t.gdDiffDeliveryStatus,
        t.gcDeliveryStatus,
        t.ydpxDeliveryCount,
        t.ltpxDeliveryCount,
        t.dxpxDeliveryCount,
        t.ydgdDeliveryCount,
        t.ltgdDeliveryCount,
        t.dxgdDeliveryCount,
        t.ydgcDeliveryCount,
        t.orgName,
        t.isDiff,
        t.pxQuota,
        t.diffPxQuota,
        t.gdQuota,
        t.difGdQuota,
        t.gcQuota,
        t.oriOrgID
        from (
        select
        t1.ID,
        t1.memberName,
        t3.productCode,
        t1.msisdn,
        t3.status,
        t3.orderID,
        t2.enterpriseID,
        t1.createTime,
        t1.updateTime,
        t1.operatorID,
        t1.reserved1,
        t1.reserved2,
        t1.provinceCode,
        t1.provinceName,
        t1.cityCode,
        t1.cityName,
        t3.errCode,
        t3.errDesc,
        t3.reserved4,
        t3.reserved5,
        t2.orgID,
        t4.pxDeliveryStatus,
        t4.pxDiffDeliveryStatus,
        t4.gdDeliveryStatus,
        t4.gdDiffDeliveryStatus,
        t4.gcDeliveryStatus,
        IFNULL(t4.ydpxDeliveryCount, 0) ydpxDeliveryCount,
        IFNULL(t4.ltpxDeliveryCount, 0) ltpxDeliveryCount,
        IFNULL(t4.dxpxDeliveryCount, 0) dxpxDeliveryCount,
        IFNULL(t4.ydgdDeliveryCount, 0) ydgdDeliveryCount,
        IFNULL(t4.ltgdDeliveryCount, 0) ltgdDeliveryCount,
        IFNULL(t4.dxgdDeliveryCount, 0) dxgdDeliveryCount,
        IFNULL(t4.ydgcDeliveryCount, 0) ydgcDeliveryCount,
        t5.orgName,
        t5.reserved2 as isDiff,
        t5.reserved3 as pxQuota,
        t5.reserved4 as diffPxQuota,
        t5.reserved5 as gdQuota,
        t5.reserved6 as difGdQuota,
        t5.reserved7 as gcQuota,
        t5.oriOrgID as oriOrgID
        from ecpm_t_member t1 ,ecpm_t_member_subscribe t3
        LEFT JOIN ecpm_t_org_rel t2 ON t2.ID = t3.memberID
        LEFT JOIN ecpm_t_monquota_delivery_stat t4 ON t2.ID = t4.memberID AND t4.status is null
        LEFT JOIN ecpm_t_org_simple t5 ON t2.orgID = t5.id
        LEFT JOIN ecpm_t_content_org t6 ON t6.ownerType = 1 AND t6.ownerID = t2.orgID
        <trim prefix="where" prefixOverrides="and|or">
            t1.ID=t3.memberID
            <!-- and t3.productCode in (
            select t4.productCode from ecpm_t_serv_product t4 where t4.servType in ('1','5')
            union all
            select t5.productCode from ecpm_t_enterprise_code t5
            ) -->
            <if test="memberName != null and memberName != ''">
                and t1.memberName like #{memberName}
            </if>
            <if test="msisdn != null and msisdn != ''">
                and t3.msisdn like #{msisdn}
            </if>
            <if test="reserved1 != null">
                and t1.reserved1= #{reserved1}
            </if>
            <if test="status != null">
                and t3.status= #{status}
            </if>
            <if test="null != list">
                and t3.status in
                <foreach item="item" index="index" collection="list" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="syncGroupMemStatus != null and syncGroupMemStatus == 1">
                AND t6.orgCode IS NOT NULL and t2.orgCode like concat("%", t6.orgCode, "%")
            </if>
            <if test="syncGroupMemStatus != null and syncGroupMemStatus == 2">
                AND t6.orgCode IS NOT NULL and t2.orgCode not like concat("%", t6.orgCode, "%")
            </if>
            <if test="orgCode != null">
                and t2.orgCode like concat("%", #{orgCode}, "%")
            </if>
            <if test="notLikeOrgCode != null">
                and t2.orgCode not like concat("%", #{notLikeOrgCode}, "%")
            </if>
            <if test="orgCodes != null and orgCodes.size()>0">
                and t2.orgCode REGEXP
                <foreach item="code" index="index" collection="orgCodes" open="(" separator="|" close=")">
                    #{code}
                </foreach>
            </if>
            <if test="notLikeOrgCodes != null  and notLikeOrgCodes.size()>0">
                <foreach item="code2" index="index" collection="notLikeOrgCodes" open="and (" separator="or" close="">
                    t2.orgCode not like concat("%", #{code2}, "%")
                </foreach>
                or orgCode is null )
            </if>
            <if test="orgIDs != null  and orgIDs.size()>0" >
                and (t5.ID in
                <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    OR t5.oriOrgID in
                    <foreach item="id" index="index" collection="orgIDs" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <if test="ID != null">
                and t2.ID= #{ID}
            </if>
            <if test="orgID != null">
                and (t5.ID=#{orgID}
                <if test='containBranchOrg != null and containBranchOrg == 1'>
                    or t5.oriOrgID=#{orgID}
                </if>
                )
            </if>
            <if test="enterpriseID != null">
                and t2.enterpriseID=#{enterpriseID}
            </if>

            <if test="productCode != null and productCode != ''">
                and t3.productCode=#{productCode}
            </if>


            <if test="msisdnList != null  and msisdnList.size()>0" >
                and t3.msisdn in
                <foreach item="msisdn" index="index" collection="msisdnList" open="(" separator="," close=")">
                    #{msisdn}
                </foreach>
            </if>
            <if test="contentList != null  and contentList.size()>0" >
                and t6.cyContID in
                <foreach item="cyContID" index="index" collection="contentList" open="(" separator="," close=")">
                    #{cyContID}
                </foreach>
            </if>
        </trim>
        )t group by
        t.ID,
        t.memberName,
        t.productCode,
        t.msisdn,
        t.status,
        t.orderID,
        t.enterpriseID,
        t.createTime,
        t.updateTime,
        t.operatorID,
        t.reserved1,
        t.reserved2,
        t.reserved4,
        t.reserved5,
        t.provinceCode,
        t.provinceName,
        t.cityCode,
        t.cityName,
        t.errCode,
        t.errDesc,
        t.orgID,
        t.pxDeliveryStatus,
        t.pxDiffDeliveryStatus,
        t.gdDeliveryStatus,
        t.gdDiffDeliveryStatus,
        t.gcDeliveryStatus,
        t.ydpxDeliveryCount,
        t.ltpxDeliveryCount,
        t.dxpxDeliveryCount,
        t.ydgdDeliveryCount,
        t.ltgdDeliveryCount,
        t.dxgdDeliveryCount,
        t.ydgcDeliveryCount )a
    </select>

    <select id="queryOldOrgMemberListByContID" resultMap="memberContentWrapper">
        SELECT m.ID, rel.orgCode,m.msisdn, m.memberName,m.reserved2
        FROM ecpm_t_content
                 t,ecpm_t_content_org org,ecpm_t_org_rel rel,ecpm_t_member m,ecpm_t_member_subscribe ms
        WHERE t.ID = #{contID} and t.ID= org.cyContID and org.ownerId =rel.orgID and rel.id = m.id and m.id = ms.memberID and (ms.status = 5 or ms.status = 3 or ms.status = 13)
    </select>


    <select id="queryNewOrgMemberListByOrgid" resultMap="memberContentWrapper" parameterType="java.util.List">
        SELECT m.ID,rel.orgCode,m.msisdn, m.memberName,m.reserved2
        FROM ecpm_t_org_rel rel,ecpm_t_member m,ecpm_t_member_subscribe ms
        WHERE
        rel.id = m.id AND m.id = ms.memberID AND (ms.status = 5 OR ms.status = 3 OR ms.status = 13)
        AND rel.orgId in
        <foreach item="orgid" index="index" collection="orgIDList" open="(" separator="," close=")">
            #{orgid}
        </foreach>
    </select>

</mapper>