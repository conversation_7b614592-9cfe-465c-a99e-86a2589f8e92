<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.GroupSendTaskResultMapper">
	<resultMap id="groupSendResultTask"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.GroupSendTaskResultWrapper">
		<result property="taskID" column="taskID" javaType="java.lang.Long" />
		<result property="taskCode" column="taskCode" javaType="java.lang.String" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="downloadStatus" column="downloadStatus"
			javaType="java.lang.Integer" />
		<result property="noticeTime" column="noticeTime" javaType="Date" />
		<result property="errDesc" column="errdesc" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="argv" column="argv" javaType="java.lang.String" />
		<result property="provider" column="provider" javaType="java.lang.Integer" />
	</resultMap>

	<insert id="batchInsertGroupSendTaskResult">
		insert into ecpe_t_group_send_task_result
		(
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		argv
		)
		values
		<foreach collection="list" item="groupSendResultTask"
			separator=",">
			(
			#{groupSendResultTask.taskID},
			#{groupSendResultTask.taskCode},
			#{groupSendResultTask.msisdn},
			#{groupSendResultTask.status},
			#{groupSendResultTask.downloadStatus},
			#{groupSendResultTask.noticeTime},
			#{groupSendResultTask.errDesc},
			#{groupSendResultTask.reserved1},
			#{groupSendResultTask.reserved2},
			#{groupSendResultTask.reserved3},
			#{groupSendResultTask.reserved4},
			#{groupSendResultTask.argv}
			)
		</foreach>
	</insert>
	<select id="queryPlatformNumByID" resultType="java.util.HashMap">
        SELECT
	 SUM(CASE WHEN reserved1 = 1 THEN 1 ELSE 0 END ) ydNum,
     SUM(CASE WHEN reserved1 = 2 THEN 1 ELSE 0 END ) ltNum,
     SUM(CASE WHEN reserved1 = 3 THEN 1 ELSE 0 END ) dxNum
     FROM ecpe_t_group_send_task_result WHERE taskID = #{taskID}  GROUP BY taskID
    </select>
	<select id="queryGroupSendTaskResultList" resultMap="groupSendResultTask">
		select
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpe_t_group_send_task_result
		<trim prefix="where" prefixOverrides="and|or">
			<if test="null != list">
				status in
				<foreach item="status" index="index" collection="list" open="("
					separator="," close=")">
					#{status}
				</foreach>
			</if>
			<if test="taskID != null and taskID != ''">
				and taskID = #{taskID}
			</if>
		</trim>
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryGroupSendTaskTotalCount" resultType="java.lang.Long">
		select count(1) from ecpe_t_group_send_task_result
		<trim prefix="where" prefixOverrides="and|or">
			<if test="null != list">
				status in
				<foreach item="status" index="index" collection="list" open="("
					separator="," close=")">
					#{status}
				</foreach>
			</if>
			<if test="taskID != null and taskID != ''">
				and taskID = #{taskID}
			</if>
		</trim>
	</select>

	<delete id="deleteGroupSendResultByTaskID">
		delete from ecpe_t_group_send_task_result where
		taskID = #{taskID}
	</delete>

	<update id="updateGroupSendResult">
		<foreach collection="list" item="groupSendResultTask" open=""
			close="" separator=";">
			update ecpe_t_group_send_task_result
			<set>
				<if
					test="null != groupSendResultTask.taskCode and '' != groupSendResultTask.taskCode">
					taskCode = #{groupSendResultTask.taskCode}
				</if>
				<if
					test="null != groupSendResultTask.downloadStatus">
					,downloadStatus = #{groupSendResultTask.downloadStatus}
				</if>
				<if test="null != groupSendResultTask.status">
					,status =#{groupSendResultTask.status}
				</if>
				<if
					test="null != groupSendResultTask.errDesc and '' != groupSendResultTask.errDesc">
					,errdesc = #{groupSendResultTask.errDesc}
				</if>
				<if test="null != groupSendResultTask.noticeTime">
					,noticeTime = #{groupSendResultTask.noticeTime}
				</if>
			</set>
			where taskCode = #{groupSendResultTask.taskCode} and
			msisdn =
			#{groupSendResultTask.msisdn}
		</foreach>
	</update>

	<select id="queryMsisdnByTaskID" resultType="java.lang.String">
		select msisdn from
		ecpe_t_group_send_task_result
		where taskID = #{taskID}
		limit
		#{startIndex},#{pageSize}
	</select>

	<select id="queryCountByTaskID" resultType="java.lang.Long">
		select count(1) from
		ecpe_t_group_send_task_result
		where taskID = #{taskID}
	</select>

	<update id="updateTaskCodeByTaskIDAndMsisdn">
		<foreach collection="list" item="groupSendResultTask" open=""
			close="" separator=";">
			update ecpe_t_group_send_task_result
			<set>
				<if
					test="null != groupSendResultTask.taskCode and '' != groupSendResultTask.taskCode">
					taskCode = #{groupSendResultTask.taskCode},
				</if>
				<if
						test="null != groupSendResultTask.status">
					status = #{groupSendResultTask.status}
				</if>
			</set>
			where taskID = #{groupSendResultTask.taskID} and
			msisdn =
			#{groupSendResultTask.msisdn}
		</foreach>
	</update>

	<select id="queryTaskResultByTaskCode" resultMap="groupSendResultTask">
		select
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpe_t_group_send_task_result
		where
		taskCode =
		#{taskCode}
	</select>

	<select id="queryTaskResultByTaskId" resultMap="groupSendResultTask">
		select
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		argv,
		provider
		from
		ecpe_t_group_send_task_result
		where
		taskID =
		#{taskID}

	</select>
	<select id="queryRealResultByTaskId" resultMap="groupSendResultTask">
		select
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		argv
		from
		ecpe_t_group_send_task_result
		where
		taskID =
		#{taskID}
	</select>

	<select id="queryResultDealByTaskId" resultMap="groupSendResultTask">
		select
		taskID,
		taskCode,
		msisdn,
		status,
		downloadStatus,
		noticeTime,
		errdesc,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from
		ecpe_t_group_send_task_result
		where
		taskID =
		#{taskID}
		and status != 5 and status is not null
	</select>

	<select id="selectTaskCodeByTaskId" resultType="java.lang.String">
		select
			taskCode
		from
			ecpe_t_group_send_task_result
		where
			taskID = #{taskID}
			and msisdn = #{msisdn}
		order by noticeTime DESC
		limit 1
	</select>

	<select id="updateStatusByTaskList" parameterType="java.util.Map">
		UPDATE ecpe_t_group_send_task_result t
		SET
		<trim suffixOverrides=",">
			<if test="status !=null">
				t.status = #{status},
			</if>
			<if test="errDesc !=null">
				t.errdesc = #{errDesc},
			</if>
			<if test="noticeTime !=null">
				t.noticeTime = #{noticeTime},
			</if>
		</trim>
		WHERE t.taskID IN
		<foreach collection="taskIdList" item="taskID" separator="," open="(" close=")">
			#{taskID}
		</foreach>
		 and t.status = 5
	</select>

	<select id="updateByFinishTime" parameterType="java.lang.Long">
		UPDATE ecpe_t_group_send_task_result t
		SET t.finishTime = now()
		WHERE t.taskID = #{taskID}
		and t.finishTime is null
	</select>

	<update id="updateTaskCodeByTaskID">
		update ecpe_t_group_send_task_result t
		set t.taskCode = #{taskCode}
		<if test="status != null">
			, t.status = #{status}
		</if>
		<if test="reserved4 != null">
			, t.reserved4 = #{reserved4}
		</if>
		where t.taskID = #{taskID}
	</update>

	<update id="updateStatusByTaskIdAndTel">
		update ecpe_t_group_send_task_result t
		set t.status = #{status}
		<if test="reserved4 != null">
			, t.reserved4 = #{reserved4}
		</if>
		<if test="taskCode != null">
			, t.taskCode = #{taskCode}
		</if>
		<if test="reserved1 != null">
			, t.reserved1 = #{reserved1}
		</if>
		<if test="errDesc != null">
			, t.errDesc = #{errDesc}
		</if>
		<if test="provider != null">
			, t.provider = #{provider}
		</if>
		where t.taskID = #{taskID} and msisdn = #{msisdn}
	</update>

	<update id="updateStatusAndNoticeTimeByTaskIdAndTel">
		update ecpe_t_group_send_task_result t
		set t.status = #{status}
		<if test="reserved4 != null">
			, t.reserved4 = #{reserved4}
		</if>
		<if test="taskCode != null">
			, t.taskCode = #{taskCode}
		</if>
		<if test="reserved1 != null">
			, t.reserved1 = #{reserved1}
		</if>
		<if test="errDesc != null">
			, t.errDesc = #{errDesc}
		</if>
		<if test="provider != null">
			, t.provider = #{provider}
		</if>
		<if test="noticeTime != null">
			, t.noticeTime = #{noticeTime}
		</if>
		<if test="finishTime != null">
			, t.finishTime = #{finishTime}
		</if>
		where t.taskID = #{taskID} and msisdn = #{msisdn}
	</update>

	<update id="updateResult">
		update ecpe_t_group_send_task_result t
		set
		status = #{status},
		downloadStatus = #{downloadStatus},
		errdesc = #{errDesc},
		noticeTime = #{noticeTime}
		where taskID = #{taskID}
		and msisdn in
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item.msisdn}
		</foreach>
	</update>


	<update id="updateMobilePlatform">
		update ecpe_t_group_send_task_result t
		set
		reserved1 = #{Platform}
		<if test="Platform == null">
			, t.status = 4
			, t.errdesc = "未查询到运营商"
		</if>
		where taskID = #{taskID}
		and msisdn in
		<foreach collection="list" item="item" open="(" close=")" separator=",">
			#{item}
		</foreach>
	</update>

</mapper>