<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.OrganizationMapper">

	<resultMap id="OrganizationWrapper"
		type="com.huawei.jaguar.dsum.dao.domain.OrganizationWrapper">
		<result property="id" column="id" javaType="java.lang.Integer" />
		<result property="orgCode" column="orgCode" javaType="java.lang.String" />
		<result property="orgName" column="orgName" javaType="java.lang.String" />
		<result property="orgDesc" column="orgDesc" javaType="java.lang.String" />
		<result property="orgType" column="orgType" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="lastUpdateTime" column="lastUpdateTime"
			javaType="java.util.Date" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />
		<result property="pxBeyondPackageSwitch" column="pxBeyondPackageSwitch" javaType="java.lang.String" />
		<result property="gdBeyondPackageSwitch" column="gdBeyondPackageSwitch" javaType="java.lang.String" />
	</resultMap>


	<!--查询自增序列 -->
	<select id="getOrganizationID" resultType="java.lang.Integer">
		select nextval('dsum_sequence_org');
	</select>

	<!--新增组织 -->
	<insert id="createOrganization">
		INSERT INTO
		dsum_t_org
		(ID,
		orgCode,
		orgName,
		orgDesc,
		orgType,
		enterpriseID,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		branchType,
		pxBeyondPackageSwitch,
		gdBeyondPackageSwitch,
		oriOrgID)
		VALUES
		(
		#{id},
		#{orgCode},
		#{orgName},
		#{orgDesc},
		#{orgType},
		#{enterpriseID},
		#{createTime},
		#{operatorID},
		#{lastUpdateTime},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10},
		#{branchType},
		#{pxBeyondPackageSwitch},
		#{gdBeyondPackageSwitch},
		#{oriOrgID}
		)
	</insert>

	<!--修改组织 -->
	<update id="updateOrganization">
		update dsum_t_org set
		<trim suffixOverrides="," suffix="where ID = #{id} or oriOrgID = #{id}">
            <if test="orgName!=null and orgName!=''">orgName= #{orgName},</if>
            <if test="orgDesc!=null and orgDesc!=''">orgDesc= #{orgDesc},</if>
            <if test="orgType!=null">orgType= #{orgType},</if>
            <if test="operatorID!=null">operatorID= #{operatorID},</if>
            <if test="lastUpdateTime!=null">lastUpdateTime= #{lastUpdateTime},</if>
            <if test="extInfo!=null and extInfo!=''">extInfo= #{extInfo},</if>
            <if test="reserved1!=null and reserved1!=''">reserved1= #{reserved1},</if>
            <if test="reserved2!=null and reserved2!=''">reserved2= #{reserved2},</if>
            <if test="reserved3!=null and reserved3!=''">reserved3= #{reserved3},</if>
            <if test="reserved4!=null and reserved4!=''">reserved4= #{reserved4},</if>
            <if test="reserved5!=null and reserved5!=''">reserved5= #{reserved5},</if>
            <if test="reserved6!=null and reserved6!=''">reserved6= #{reserved6},</if>
            <if test="reserved7!=null and reserved7!=''">reserved7= #{reserved7},</if>
            <if test="reserved8!=null and reserved8!=''">reserved8= #{reserved8},</if>
            <if test="reserved9!=null and reserved9!=''">reserved9= #{reserved9},</if>
            <if test="reserved10!=null and reserved10!=''">reserved10= #{reserved10},</if>
            <if test="pxBeyondPackageSwitch!=null and pxBeyondPackageSwitch!=''">pxBeyondPackageSwitch= #{pxBeyondPackageSwitch},</if>
            <if test="gdBeyondPackageSwitch!=null and gdBeyondPackageSwitch!=''">gdBeyondPackageSwitch= #{gdBeyondPackageSwitch},</if>
        </trim>
	</update>

	<!--查询组织详情 -->
	<select id="queryOrganizationInfo" resultMap="OrganizationWrapper">
		select
		ID,orgCode,orgName,orgDesc,orgType,enterpriseID,createTime,
		operatorID,lastUpdateTime,extInfo,reserved1,reserved2,reserved3,
		reserved4,reserved5,reserved6,reserved7,reserved8,reserved9,reserved10,branchType,oriOrgID,
		pxBeyondPackageSwitch,gdBeyondPackageSwitch
		from
		dsum_t_org
		where ID=#{id}
	</select>
	<!--查询组织详情 -->
	<select id="queryOrganizationInfoByCode" resultMap="OrganizationWrapper">
		select
			ID,orgCode,orgName,orgDesc,orgType,enterpriseID,createTime,
			operatorID,lastUpdateTime,extInfo,reserved1,reserved2,reserved3,
			reserved4,reserved5,reserved6,reserved7,reserved8,reserved9,reserved10,branchType,oriOrgID,
		    pxBeyondPackageSwitch,gdBeyondPackageSwitch
		from
			dsum_t_org
		where orgCode=#{orgCode} and branchType = "22"
	</select>
	<!--查询组织信息 -->
	<select id="queryOrganization" resultMap="OrganizationWrapper">
		select
		ID,orgCode,orgName,orgDesc,orgType,enterpriseID,createTime,
		operatorID,lastUpdateTime,extInfo,reserved1,reserved2,reserved3,
		reserved4,reserved5,reserved6,reserved7,reserved8,reserved9,reserved10
		from
		dsum_t_org
		<trim prefix="where" prefixOverrides="and|or">
			<if test="id!=null">
				and ID != #{id}
			</if>
			<if test="enterpriseID!=null">
				and enterpriseID = #{enterpriseID}
			</if>
			<if test="orgName!=null  and orgName!=''">
				and orgName = #{orgName}
			</if>
			<if test="orgType!=null">
	        	and orgType = #{orgType}
	       </if>
		</trim>
	</select>


	<!--查询组织列表 -->
	<select id="queryOrganizationList" resultMap="OrganizationWrapper">
		select
		ID,
		orgCode,
		orgName,
		orgDesc,
		orgType,
		enterpriseID,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		branchType,
		oriOrgID
		from
		dsum_t_org
		<trim prefix="where" prefixOverrides="and|or">
			<if test="orgName!=null and orgName!=''">
				and orgName = #{orgName}
			</if>
			<if test="likeOrgName!=null and likeOrgName!=''">
				and orgName like concat("%", #{likeOrgName}, "%")
			</if>
			<if test="enterpriseID!=null and enterpriseID!=''">
				and enterpriseID = #{enterpriseID}
			</if>
	        <if test="orgType!=null">
	        	and orgType = #{orgType}
	       </if>
	       <if test="reserved1!=null">
	        	and reserved1 = #{reserved1}
	       </if>
	       <if test="branchType!=null">
	        	and branchType = #{branchType}
	       </if>
		</trim>
			order by ${sortField} ${sortType}
			limit #{pageNo},#{pageSize}
	</select>

	<!--查询组织数量 -->
	<select id="countOrganization" resultType="java.lang.Integer">
		SELECT
		count(0)
		from dsum_t_org t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="orgName!=null and orgName!=''">
				and orgName = #{orgName}
			</if>
			<if test="likeOrgName!=null and likeOrgName!=''">
				and orgName like concat("%", #{likeOrgName}, "%")
			</if>
			<if test="enterpriseID!=null and enterpriseID!=''">
				and enterpriseID = #{enterpriseID}
			</if>
	        <if test="orgType!=null">
	        	and orgType = #{orgType}
	       </if>
	       <if test="reserved1!=null">
	        	and reserved1 = #{reserved1}
	       </if>
	       <if test="branchType!=null">
	        	and branchType = #{branchType}
	       </if>
		</trim>
	</select>

	<!--删除组织信息 -->
	<delete id="deleteOrganization">
		delete from dsum_t_org where ID in (
		<foreach collection="list" item="id" separator=",">
			#{id}
		</foreach>
		)
	</delete>

	<select id="queryOrganizationNameCount" resultType="java.lang.Integer">
		SELECT
		count(*) from dsum_t_org t where t.orgName =
		#{orgName} and
		t.enterpriseID = #{enterpriseID}
		and t.orgType = #{orgType}
	</select>

	<!--查询组织列表 -->
	<select id="batchQueryOrganizationList" resultMap="OrganizationWrapper">
		select
		ID,
		orgCode,
		orgName,
		orgDesc,
		orgType,
		enterpriseID,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		dsum_t_org
		where ID in (
		<foreach collection="list" item="id" separator=",">
			#{id}
		</foreach>
		)
	</select>

</mapper>