
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<title>登录</title>
<link class="faviconIco" rel="shortcut icon" href="../../assets/images/favicon.ico" type="image/x-icon">
<link rel="stylesheet" type="text/css" href="../../css/bootstrap.min.css" />
<link href="../../css/reset.css" rel="stylesheet" />
<link href="../../css/login.css" rel="stylesheet" />
<script type="text/javascript" src="../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript"
				src="../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../frameworkJs/jsencrypt.js"></script>
	<script type="text/javascript" src="../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="loginCtrl.js"></script>
<script>
  function getBrowser() {
    // 获取浏览器 userAgent
    var ua = navigator.userAgent;
    // 是否为 IE
    var isIE =
      ua.indexOf("compatible") > -1 && ua.indexOf("MSIE") > -1 && !isOpera;
    var isIE11 = ua.indexOf("Trident") > -1 && ua.indexOf("rv:11.0") > -1;
    // 返回结果
    if (isIE11) {
      return "IE11";
    } else if (isIE) {
      // 检测是否匹配
      var re = new RegExp("MSIE (\\d+\\.\\d+);");
      re.test(ua);
      // 获取版本
      var ver = parseFloat(RegExp["$1"]);
      // 返回结果
      if (ver == 7) {
        return "IE7";
      } else if (ver == 8) {
        return "IE8";
      } else if (ver == 9) {
        return "IE9";
      } else if (ver == 10) {
        return "IE10";
      } else {
        return "IE";
      }
    }
    return false; // 非IE浏览器，可以访问
  }

  $(document).ready(function () {
    // 获取浏览器信息
    var userAgent = navigator.userAgent;
    console.log(userAgent, "浏览器信息");
    // ie浏览器不支持访问
    if (getBrowser()) {
      // 替换页面body内容,内容文字居中
      $("body").html(
        '<div class="ie-alert">' +
          '<div class="container">' +
            '<div class="row">' +
              '<div class="col-md-12">' +
                '<div class="alert alert-warning">' +
                  '<strong>提示</strong> 不支持该浏览器访问，建议使用谷歌浏览器进行访问!' +
                '</div>' +
              '</div>' +
            '</div>' +
          '</div>' +
        '</div>'
      );
      $("body").css("display", "flex").css("justify-content", "center");
    }
  });
</script>
<style>
  .form-group .control-label icon {
    color: #ff254c;
    vertical-align: sub;
    margin-right: 2px;
  }
  .form-group .control-label1 icon {
	  color: #ff254c;
	  vertical-align: sub;
	  text-align: left;
  }
  .load-wrap {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background-color: #f0f0f0;
      margin: 0;
    }
  .loader {
      width: 50px;
      height: 50px;
      border: 6px solid #f3f3f3;
      border-top: 6px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

</style>
</head>
	<body  ng-app='myApp' ng-controller='loginController' ng-init="init();">

		<div class="login" ng-if="isShowLogin">
			<div class="login-cont">
				<form class="form-horizontal" name='myForm' novalidate >
					<div class="form-group">
						<label for="account" class="col-sm-1 control-label"><icon class="account"></icon></label>
					    <div class="col-sm-11">
                          <input style="width:210px" required name='account' type="account" class="form-control" id="account"
                          placeholder="{{'ENTERPRISE_PLEASEINPUTACCOUNTNAME'|translate}}" ng-model='userInfo.userAccount' ng-blur="getUserInfo()" >
                        </div>
                        <div>
                        	<span style="color:red;padding-left: 55px" ng-show="myForm.account.$dirty&& myForm.account.$invalid">
                                <span ng-show="myForm.account.$error.required">{{'LOGIN_ACOUNTREQUIRE'|translate}}</span>
                            </span>
                        </div>
					</div>
					<div class="form-group">
						<label for="password" class="col-sm-1 control-label"><icon class="password"></icon></label>
					    <div class="col-sm-11">
                          <input required autocomplete="off" type="password" name="pwd" class="form-control passwordInput"
                          id="password" placeholder="请输入密码" ng-model='userInfo.pwd'>
                        </div>
                        <div class="">
                            <span style="color:red;padding-left: 55px" ng-show="myForm.pwd.$dirty&& myForm.pwd.$invalid">
                                <span ng-show="myForm.pwd.$error.required" >{{'LOGIN_PWDREQUIRE'|translate}}</span>
                            </span>
                        </div>
                    </div>
					<div class="form-group" ng-show="msisdn!=null&&msisdn!=''">
						<label for="sms" class="col-sm-1 control-label">
							<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/sms.png') no-repeat scroll 0 0;display: inline-block;height: 22px;width: 22px;"></icon>
						</label>
						<div class="col-sm-11" style="padding-right:0px">
							<span style="width: 32px" type="text" placeholder="" disabled="disabled"  class="passwordInput form-control"
							>+86</span>
							<input type="" placeholder="请输入手机号" disabled="disabled" ng-model="msisdn" class="passwordInput form-control"
								   name="msisdn" autocomplete="off">
						</div>
					</div>
					<div class="form-group"  ng-show="msisdn!=null&&msisdn!=''">
						<label for="sms" class="col-sm-1 control-label">
							<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/sms.png') no-repeat scroll 0 0;display: inline-block;height: 22px;width: 22px;"></icon>
						</label>

						<div class="col-sm-11" style="padding-right:0px">
							<input type="code"  ng-required="msisdn!=null&&msisdn!=''"   placeholder="请输入短信验证" ng-model="smsVerify" class="passwordInput form-control"
								   name="smsVerify" autocomplete="off">
							<button ng-click="getSmsVerifyCode()" type="button" style="color: #6759a4;"class="btn" id="getSmsCode">获取短信验证
							</button>
						</div>
						<div>
						<span style="color:red;padding-left: 55px"
							  ng-show="myForm.smsVerify.$dirty&&myForm.smsVerify.$invalid">
								<span ng-show="myForm.smsVerify.$error.required">短信验证码必填</span>
						</span>
						</div>
					</div>

					<div class="form-group">
						<label for="code" class="col-sm-1 control-label"><icon class="code"></icon></label>
					    <div class="col-sm-11">
					      <input required type="code" class="form-control codeInput" name='verifycode' id="code" placeholder="{{'LOGIN_VERIFYCODE'|translate}}" ng-model='userInfo.validateCode'>
						  <img class="validateCode" ng-src="{{verifycodeImg}}" alt="verifycode"  ng-click="getValidateCode()">
						  <!--<img ng-click="getValidateCode()" class="refresh" src="../../assets/images/refresh_validate_icon.png" alt="refresh">-->
						</div>
						<div>
							<span style="color:red;padding-left: 55px" ng-show="myForm.verifycode.$dirty&& myForm.verifycode.$invalid">
                                <span ng-show="myForm.verifycode.$error.required" >{{'LOGIN_VERIFYREQUIRE'|translate}}</span>
                            </span>
						</div>
					</div>
					<div style="text-align:right">
					<p>
					<a href="../forgetPwd/forgetPwd.html{{isGroupRemind?'?isGroupRemind=true':''}}">找回密码</a>
					</p>
					</div>
					<button class="btn btn-primary search-btn" ng-click="loginAction()"
							ng-disabled="myForm.$invalid || (msisdn!=null&&msisdn!=''&&!smsVerify)"
					>{{'LOGIN_LOGIN'|translate}}</button>
				</form>
				<div class="tip">需要技术支撑请发送至技术支撑邮箱：<EMAIL></div>

			</div>
		</div>
		<div class="load-wrap" ng-if="!isShowLogin">
			 <h4>加载中...</h4>
			<div class="loader"></div>
		</div>

		<!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-sm" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
                    <div class="modal-body">
                        <div class="text-center"><p style='font-size: 16px;color:#383838'>
                                {{tip|translate}}
                        </p></div>
                    </div>
					<div class="modal-footer" >
						<button  type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
                </div>
            </div>
		</div>
		<!--	提示密码修改弹窗	-->
		<div class="modal fade bs-example-modal-sm" id="checkPwdModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							您的密码已超90天未修改，建议进行修改！
						</p></div>
					</div>
					<div class="modal-footer">
						<button type="submit" class="btn btn-back " ng-click="handleCancelPwd()" data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
						<button type="submit" class="btn search-btn " ng-click="handleConfirmPwd()" data-dismiss="modal" aria-label="Close" ng-bind="'UPDATEPWD'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		<!-- 修改密码弹窗 -->
		<div class="modal fade" id="accountManage" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close" ng-click=""><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'UPDATEPWD'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal" name="updateForm">
							<div class="form-group" style="margin-bottom:0px">


								<div class="row" style="margin-bottom: 10px;">
									<label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label" style="width: 157px;padding-top: 24px;">
										<icon>*</icon>
										<span ng-bind="'NEW_PWD'|translate"></span>
									</label>
									<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8 control-label" style="text-align:left">
										<label for="pwd" class="col-sm-1 control-label" style="padding-top: 10px;">
											<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/sms.png') no-repeat scroll 0 0;display: inline-block;height: 16px;width: 16px;position: absolute;
                                    left: 10px;top: 20px;"></icon>
										</label>
										<input type="password" placeholder="{{'PLEASE_INPUTNEWPWD'|translate}}"  ng-show="pwdValidate"  class="form-control"
											   style="display: inline-block;text-indent:20px;"  name="repwd" ng-model="repwd" required
											   ng-blur="checkNewPwdRule()"  ng-disabled="!originPwdValidate"/>
										<input type="password" placeholder="{{'PLEASE_INPUTNEWPWD'|translate}}" ng-show="!pwdValidate"  class="form-control"
											   style="display: inline-block;text-indent:20px;border-color:red;"  name="repwd" ng-model="repwd" required
											   ng-blur="checkNewPwdRule()" ng-disabled="!originPwdValidate"/>
										<span style="color:red;float:left" ng-show="!pwdValidate">
                                    <img src="../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                    <span>{{newPwdRuleValidate}}</span>
                                </span>
									</div>
								</div>

								<div class="row" style="margin-bottom: 10px;">
									<label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label" style="width: 157px;padding-top: 24px;">
										<icon>*</icon>
										<span ng-bind="'CONFIRM_PWD'|translate"></span>
									</label>
									<div class="col-lg-8 col-xs-8 col-sm-8 col-md-8 control-label">
										<label for="pwd" class="col-sm-1 control-label" style="padding-top: 10px;">
											<icon style="background: rgba(0, 0, 0, 0) url('../../assets/images/sms.png') no-repeat scroll 0 0;display: inline-block;height: 16px;width: 16px;position: absolute;
                                    left: 10px;top: 20px;"></icon>
										</label>
										<input type="password" placeholder="{{'PLEASE_INPUTNEWPWD'|translate}}" ng-show="rePasswordValidate"  class="form-control"
											   style="display: inline-block;text-indent:20px;"  name="confirmPwd" ng-model="confirmPwd"
											   ng-blur="checkRePassword()" ng-disabled="!originPwdValidate"/>
										<input type="password" placeholder="{{'PLEASE_INPUTNEWPWD'|translate}}" ng-show="!rePasswordValidate"  class="form-control"
											   style="display: inline-block;text-indent:20px;border-color:red;"  name="confirmPwd" ng-model="confirmPwd"
											   ng-blur="checkRePassword()" ng-disabled=" !originPwdValidate"/>
										<span style="color:red;float:left" ng-show="!rePasswordValidate">
                                    <img src="../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                    <span>{{'REPWD_EQUALS_NEWPWD'|translate}}</span>
                                </span>
									</div>
								</div>
								<div style="margin:0 10px 10px 45px;">
									<label class="control-label1" style="width: 544px;padding-top: 24px;">
										<icon>*</icon>
										<span ng-bind="'NEW_PWD_TIPS'|translate"></span>
									</label>
								</div>
							</div>

						</form>
					</div>
					<div class="modal-footer" style="margin-left:0px;padding-top:0px">
						<button type="button" class="btn btn-primary search-btn"
								ng-click="confirmUpdate()"
								ng-disabled="updateForm.pwd.$invalid || !pwdValidate || repwd=='' || confirmPwd=='' || !rePasswordValidate " ng-bind="'UPDATE_CONFIRM'|translate"></button>
						<button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close" ng-click=""
								id="updatePwdCancel" style="margin-left: 60px" ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		<!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="myModal1" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p style='font-size: 16px;color:#383838'>
									当前已有用户：&nbsp;{{LoginAlreadyName}}&nbsp;登录，即将跳转菜单页面
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
						</div>
					</div>
				</div>
			</div>

		<script language="JavaScript">
				if (window != top){
					top.location.href = location.href;
				}
		</script>
	</body>
</html>
