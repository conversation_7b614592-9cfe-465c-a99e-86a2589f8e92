var app = angular.module("myApp",["util.ajax",'page',"top.menu","angularI18n","service.common"])
app.controller('spokeListController', ['$scope','$rootScope','$location','RestClientUtil','CommonUtils',function ($scope,$rootScope,$location,RestClientUtil,CommonUtils) {
	// $(function(){
	//     $('#exportSpokeList').bind("click", function () {
	//     	window.open($scope.exportUrl);  
	//     });    
	// });
	$scope.init=function(){
        $scope.msisdn = $.cookie("spokeMsisdn")||'';
        $scope.token=$.cookie("token")||"";
        //初始化分页信息
        $scope.pageInfo =[
            {
              "totalPage": 1,
              "totalCount": 0,
              "pageSize": '10',
              "currentPage": 1
            }
          ];
        $scope.spokeListData=[];
        //初始化搜索条件querySpokesActivityCond.
        $scope.querySpokesActivityCond ={
        	"msisdn": $scope.msisdn,//
        	"activityName": "",//活动名称
        	/*"processStatus": '',//活动进行状态
        	"isNowSpoke": '',//是否当期或正在进行的代言
        	"area":"",//地域编码
        	"isReturnStat":0,//是否返回代言的活动统计信息,0:否1:是*/        
        }
        $scope.querySpokeList();
        $scope.exportFile=function(){
            var req = {
              "param":{
                "msisdn":$scope.querySpokesActivityCond.msisdn,
                "activityName":$scope.querySpokesActivityCond.activityName,
                "token":$scope.token,
                "isExport":1
              },
              "url":"/qycy/ecpmp/ecpmpServices/spokeService/downSpokesListCsvFile",
              "method":"get"
            }
            CommonUtils.exportFile(req);
          }
    };
    $scope.goBack=function(){
    	location.href='../spokeStatListManage/spokeStatListManage.html';
    };
    $scope.formatDate=function(str){
        if(!str){
            return 'format error';
        }
        var newDateStr="";
        newDateStr=str.substr(0,4)+'.'+str.substr(4,2)+'.'+str.substr(6,2);
        return newDateStr;

    };
    
	$scope.querySpokeList = function (condition) {
        if(condition!='justPage'){
            var req={
                "querySpokesActivityCond":$scope.querySpokesActivityCond||'',
                "page":{
                    "pageNum":1,
                    "pageSize":parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal":"1",
                }
            };
            $scope.pageInfo[0].currentPage=1;
            $scope.reqTemp=angular.copy(req);
            $scope.exportUrl = "/qycy/ecpmp/ecpmpServices/spokeService/downSpokesListCsvFile?msisdn="+$scope.querySpokesActivityCond.msisdn
            	+"&activityName="+$scope.querySpokesActivityCond.activityName;
        }else{
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req=$scope.reqTemp;
            req.page.pageNum=parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize=parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
			type: 'POST',
			url: "/ecpmp/ecpmpServices/spokeService/querySpokesList",
			data: JSON.stringify(req),
			success: function (result) {
				$rootScope.$apply(function () {
                    var data = result.result;
                    if(data.resultCode=='1030100000'){
                        $scope.spokeListData=result.spokesActivityInfoList||[];
                        $scope.pageInfo[0].totalCount=parseInt(result.totalcount)||0;
                        $scope.pageInfo[0].totalPage = result.totalNum !==0 ?Math.ceil(result.totalcount / parseInt($scope.pageInfo[0].pageSize)) :1;
                    }else{
                        $scope.spokeListData=[];
                        $scope.pageInfo[0].currentPage=1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage=1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
				})
				
            },
            error:function(){
                $rootScope.$apply(function(){
                    $scope.tip="**********";
                    $('#myModal').modal();
                    }
                )
            }
		});
          
	}
}])
app.config(['$locationProvider', function($locationProvider) {
    $locationProvider.html5Mode({
        enabled:true,
        requireBase:false
    });
  }])