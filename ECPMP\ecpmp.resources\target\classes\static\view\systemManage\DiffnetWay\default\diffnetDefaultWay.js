var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    	$scope.init = function () {
    		$scope.accountID = $.cookie('accountID') || '1000';
	        //判断是否超管
	        $scope.isSuperManager = false;
	        $scope.isAgent = false;
	        var loginRoleType = $.cookie('loginRoleType');
	        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
	        $scope.unionArr = ['0','0','0','0'];
	        $scope.telecomArr = ['0','0','0'];
	        $scope.addExportParamsJson = "";
	        $scope.queryDiffnetDeliveryWay();
         	$scope.isSave = false;
         	$scope.remarks = "";
	    }

	    $scope.changeUnionPlatform = function (val) {
	    	if ($scope.unionArr[val] == 1)
	    	{
	            
	            $('.union .check-li').eq(val).find('span').removeClass('checked');
	            $scope.unionArr[val] = 0;
	    	}
	    	else
	        {
	    		$('.union .check-li').find('span').removeClass('checked');
	            $('.union .check-li').eq(val).find('span').addClass('checked');
	            
	            for (var i =0 ; i< $scope.unionArr.length; i++)
		 		{
	            	if (i == val)
	                {
	            		$scope.unionArr[i] = 1;
	                }
	            	else
	                {
	            		$scope.unionArr[i] = 0;
	                }
		 		}
	        }
	 	};
	 	$scope.changeTelecomPlatform = function (val) {
	    	if ($scope.telecomArr[val] == 1)
	    	{ // 取消选中
	            
	            $('.telecom .check-li').eq(val).find('span').removeClass('checked');
	            $scope.telecomArr[val] = 0;
	    	}
	    	else
	        { // 选中指定的
	    		$('.telecom .check-li').find('span').removeClass('checked');
	            $('.telecom .check-li').eq(val).find('span').addClass('checked');
	            
	            for (var i =0 ; i< $scope.telecomArr.length; i++)
		 		{
	            	if (i == val)
	                {
	            		$scope.telecomArr[i] = 1;
	                }
	            	else
	                {
	            		$scope.telecomArr[i] = 0;
	                }
		 		}
	        }
	 	};
	 	/*
	     * 导出文件弹窗
	     */
	     $scope.exportFile = function () {
	         $scope.isGreaterFileName = false;
	         $scope.isEmptyFileName = false;
	         $scope.isSpecialCharacters = false;
	         $scope.isFileNamePass = true;
	         $("#exportFile").modal("show");
	     };
	     /*
	       * 文件名校验
	       */
	$scope.checkEmpty = function(){
		$scope.isFileNamePass = true;
		$scope.isGreaterFileName = false;


		var reg = /^[a-z0-9\u4e00-\u9fa5]+$/i
		// if(!$scope.remarks){
		//     $scope.isGreaterFileName = false;
		//     $scope.isEmptyFileName = true;
		//     $scope.isFileNamePass = false;
		//     $scope.isSpecialCharacters = false
		// }else if(!reg.test($scope.remarks)){
		//     $scope.isGreaterFileName = false;
		//     $scope.isEmptyFileName = false;
		//     $scope.isFileNamePass = false;
		//     $scope.isSpecialCharacters = true
		// }else

		if($scope.remarks.length > 255){
			$scope.isEmptyFileName = false;
			$scope.isGreaterFileName = true;
			$scope.isFileNamePass = false;
			$scope.isSpecialCharacters = false
		}
		//     else {
		//     $scope.isEmptyFileName = false;
		//     $scope.isGreaterFileName = false;
		//     $scope.isSpecialCharacters = false
		//     $scope.isFileNamePass = true;
		// }
	};
	 	$scope.save = function () {
	 		if ($scope.isSave)
 		    {
 				return;
 		    }
 			$scope.isSave = true;

	 		var diffnetDeliveryWayList = [];
 			var orgPlatforms = [3, 2];
 			var dongmengPlatforms = [];
 			var haobaiPlatforms = [];
			var unicomolPlatforms = [];
			var caiXunPlatforms = [];

 			var union = 0;
 			for (var i=0; i< $scope.unionArr.length; i++)
 		    {
 				if (i == 0 && $scope.unionArr[i] == 1)
 			    {
 					dongmengPlatforms.push(2);
 					union = union + 1;
 			    }
 				else if (i == 1 && $scope.unionArr[i] == 1)
 			    {
 					haobaiPlatforms.push(2);
 					union = union + 1;
 			    }
 				else if (i == 2 && $scope.unionArr[i] == 1)
				{
					unicomolPlatforms.push(2);
					union = union + 1;
				}else if( i== 3 && $scope.unionArr[i] == 1){
                    caiXunPlatforms.push(2);
                    union = union + 1;
				}
 			}
 			if (union == 0)
 			{
 				$scope.tip = "联通必须选择一个通道";
	            $('#myModal').modal();
	 			$scope.isSave = false;

	            return;
 			}
 			
 			var telecom = 0;
 			for (var i=0; i< $scope.telecomArr.length; i++)
 		    {
 				if (i == 0 && $scope.telecomArr[i] == 1)
 			    {
 					dongmengPlatforms.push(3);
 					telecom = telecom + 1;
 			    }
 				else if (i == 1 && $scope.telecomArr[i] == 1)
 			    {
 					haobaiPlatforms.push(3);
 					telecom = telecom + 1;
                }else if (i == 2 && $scope.telecomArr[i] == 1)
                {
                    caiXunPlatforms.push(3);
                    telecom = telecom + 1;
                }
 			}
 			if (telecom == 0)
 			{
 				$scope.tip = "电信必须选择一个通道";
	            $('#myModal').modal();
	 			$scope.isSave = false;

	            return;
 			}
 			
 			for (var i=0; i < $scope.diffnetDeliveryWayList.length; i++)
 			{
 				if ($scope.diffnetDeliveryWayList[i].wayType == 2) // 东盟
 				{
 					$scope.diffnetDeliveryWayList[i].platforms = dongmengPlatforms.join();
 		 			diffnetDeliveryWayList.push($scope.diffnetDeliveryWayList[i]);
 				}
 				else if ($scope.diffnetDeliveryWayList[i].wayType == 5) // 号百
 				{
 					$scope.diffnetDeliveryWayList[i].platforms = haobaiPlatforms.join();
 		 			diffnetDeliveryWayList.push($scope.diffnetDeliveryWayList[i]);
 				}

				else if ($scope.diffnetDeliveryWayList[i].wayType == 6) // 联通在线
				{
					$scope.diffnetDeliveryWayList[i].platforms = unicomolPlatforms.join();
					diffnetDeliveryWayList.push($scope.diffnetDeliveryWayList[i]);
				}else if($scope.diffnetDeliveryWayList[i].wayType == 8) { // 彩讯
                    $scope.diffnetDeliveryWayList[i].platforms = caiXunPlatforms.join();
                    diffnetDeliveryWayList.push($scope.diffnetDeliveryWayList[i]);
				}
 			}
 			

 			var req = {
 	                "isDefault": 1,
 	                "diffnetDeliveryWayList": diffnetDeliveryWayList
 	            };
 			console.log(req);
 			$scope.addExportParamsJson = JSON.stringify(req);
 			RestClientUtil.ajaxRequest({
 	            type: 'POST',
 	            url: "/ecpmp/ecpmpServices/contentService/setDiffnetDeliveryWay",
 	            data: JSON.stringify(req),
 	            success: function (result) {
 	                $rootScope.$apply(function () {

 	                    var data = result.result;
 	                    if (data.resultCode == '1010100000') {
 	                    	 $scope.unionArr = ['0', '0','0','0'];
 	            	        $scope.telecomArr = ['0', '0','0'];
 	                    	$scope.queryDiffnetDeliveryWay();
// 	                    	$scope.tip = "保存成功";
// 	                    	$('#myModal').modal();
 	                    	$scope.exportFile();
 	                    }
 	                    else {
 	                        $scope.tip = data.resultDesc;
 	                        $('#myModal').modal();
	 	                	$scope.isSave = false;

 	                    }
 	                })
 	            },
 	            error: function () {
 	                $rootScope.$apply(function () {
 	   	 				$scope.isSave = false;

 	                    $scope.tip = "**********";
 	                    $('#myModal').modal();
 	                }
 	                )
 	            }
 	        });
	 	}
	 	/*
	       * 提交导出文件信息
	       */
	    $scope.submitExportTask = function(){
	        $("#exportFile").modal("hide");
	        var createExportTaskInfoRequest = {
    			"exportTaskInfo": {
					"fileName":null,
					"remarks":$scope.remarks,
					"taskType": 12,
                    "taskStatus": 0,
                    "operatorID": $scope.accountID,
                    "paramsJson": $scope.addExportParamsJson
                }	
        	}
	        RestClientUtil.ajaxRequest({
	            type: 'POST',
	            url: "/ecpmp/ecpmpServices/exportTaskService/createExportTask",
	            data: JSON.stringify(createExportTaskInfoRequest),
	            success: function (data) {
	                $rootScope.$apply(function () {
	                    var result = data.result;
	                    if (result.resultCode == '**********') {
							if(data.desc){
								$scope.tip = data.desc;
								$('#myModal').modal();
							}
	                    }else {
	                        $scope.tip = result.resultCode;
	                        $('#myModal').modal();
	                    }
	                })
	            },
	            error:function(){
	                $rootScope.$apply(function(){
	                        $scope.tip='**********';
	                        $('#myModal').modal();
	                    }
	                )
	            }
	        });
	    };
	    //后续post的函数
	    $scope.queryDiffnetDeliveryWay = function (condition) {
	    	var req = {
	                "isDefault": 1
	            };

	        RestClientUtil.ajaxRequest({
	            type: 'POST',
	            url: "/ecpmp/ecpmpServices/contentService/queryDiffnetDeliveryWay",
	            data: JSON.stringify(req),
	            success: function (result) {
	                $rootScope.$apply(function () {
	                    var data = result.result;
	                    if (data.resultCode == '1010100000') {
	                        var diffnetDeliveryWayList = result.diffnetDeliveryWayList || [];
	                        $scope.diffnetDeliveryWayList = diffnetDeliveryWayList;
	                        if (diffnetDeliveryWayList)
	                        {
	                        	angular.forEach(diffnetDeliveryWayList, function(item){
	                        		var platforms = item.platforms.split(",");
	                        		for (var i =0 ; i < platforms.length; i++)
	                        		{
	                        			if (platforms[i] == "2")
	                        			{
	                        				if (item.wayType == 2)
	                        			    {
	                        					$scope.changeUnionPlatform(0);
	                        			    }
	                        				else if (item.wayType == 5)
	                        				{
	                        					$scope.changeUnionPlatform(1);
	                        				}
											else if (item.wayType == 6)
											{
												$scope.changeUnionPlatform(2);
											}else if(item.wayType == 8) {
											    $scope.changeUnionPlatform(3);
											}
	                        			}
	                        			else if (platforms[i] == "3")
	                        		    {
	                        		    	if (item.wayType == 2)
	                        			    {
	                        					$scope.changeTelecomPlatform(0);
	                        			    }
	                        				else if (item.wayType == 5)
	                        				{
	                        					$scope.changeTelecomPlatform(1);
	                        				}else if (item.wayType == 8) {
                                                $scope.changeTelecomPlatform(2);
	                        				}
	                        		    }
	                        		}
	                        	
	                        	});	                        	
	                        }
	 	                	$scope.isSave = false;

	                    }
	                    else {
	                        $scope.tip = data.resultDesc;
	                        $('#myModal').modal();
	 	                	$scope.isSave = false;

	                    }
	                })
	            },
	            error: function () {
	                $rootScope.$apply(function () {
 	                	$scope.isSave = false;

	                    $scope.tip = "**********";
	                    $('#myModal').modal();
	                }
	                )
	            }
	        });

	    };


}])