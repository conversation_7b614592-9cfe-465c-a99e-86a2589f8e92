<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title>代理商管理 > 子企业管理 > 新增子企业</title>
		<link href="../../../../../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
		<link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css" />
		<link href="../../../../../css/reset.css" rel="stylesheet" />
		<link href="../../../../../css/searchList.css" rel="stylesheet" type="text/css"/>
		<link href="../../../../../css/createEnterprise.css" rel="stylesheet" type="text/css"/>
		<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
		<script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
		<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
		<!-- 引入top组件 -->
		<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
		<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
		<!-- 导入文件组件 -->
		<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
		<link rel="stylesheet" type="text/css" href="../../../../../css/webuploader.css">
		<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
		<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
		<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
		<!--引入JS-->
		<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
		<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
		
		<script type="text/javascript" src="createEnterprise.js"></script>
		<style>
		#filePicker div:nth-child(2) {
			width: 100% !important;
			height: 100% !important;
		}
		#filePicker2 div:nth-child(2) {
			width: 100% !important;
			height: 100% !important;
		}
	</style>
	</head>

	<body ng-app="myApp" ng-controller='enterpriseController' style="min-width:1460px">
		<div class="enterPrise">
			<div class="cooperation-head" ng-if="isSuperManager">
				<span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate" ng-if="operate !='audit'"></span>
				<span class="frist-tab" ng-bind="'MENU_INFOAUDIT'|translate" ng-if="operate =='audit'"></span>
				<span>&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate" ng-if="operate !='audit'"></span>
				<span ng-if="operate !='audit'">&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-if="operate =='add'" ng-bind="'ENTERPRISE_SUBENTERPRISEADD'|translate"></span>
				<span class="second-tab" ng-if="operate =='edit'" ng-bind="'ENTERPRISE_SUBENTERPRISEEDIT'|translate"></span>
				<span class="second-tab" ng-if="operate =='detail'" ng-bind="'COMMON_WATCHDETAIL'|translate"></span>
				<span class="second-tab" ng-if="operate =='audit'" ng-bind="'COMMON_WATCHDETAIL'|translate"></span>
			</div>
			<div class="cooperation-head" ng-if="isAgent && operate =='add'">
				<span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>
				<span>&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>
				<span>&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEADD'|translate"></span>
			</div>
			<div class="cooperation-head" ng-if="isAgent && (operate =='edit' || operate =='detail')">
				<span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate"></span>
				<span>&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate"></span>
				<span>&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-bind="'ENTERPRISE_ENTERPRISEDETAIL'|translate"></span>
			</div>
			<div class="cooperation-head" ng-if="isAgent && operate =='audit'">
				<span class="frist-tab" ng-bind="'MENU_INFOAUDIT'|translate"></span>
				<span>&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-bind="'MENU_SUBENTERPRISEAUDIT'|translate"></span>
				<span>&nbsp;&gt;&nbsp;</span>
				<span class="second-tab" ng-bind="'COMMON_WATCHDETAIL'|translate"></span>
			</div>
			
			<form  class="form-horizontal" name="myForm" novalidate ng-init="initEnterprise(this)">
			<div class="cooper-messsage">
				<top:menu chose-index="3" page-url="/qycy/ecpmp/view/cooperationManage/agentManage/subEnterprise/enterprise/enterpriseList" 
					list-index="75" ng-show="isSuperManager && operate !='audit'"></top:menu>
				<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/agentManage/subEnterprise/createEnterprise" 
					list-index="70" ng-show="isAgent && (operate =='edit' || operate =='detail')"></top:menu>
				<div class="enterprise-title">
					1.<span ng-bind="'ENTERPRISE_ENTERPRISEINFO'|translate"></span>
				</div>
				<div class="cooper-tab">

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span ng-bind="'ENTERPRISE_AGENTNAME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input class="form-control"
									type="text" id="id" name="id" ng-model="parentEnterpriseName"
									ng-disabled="true"
									title={{parentEnterpriseName}}>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input class="form-control"
									type="text" id="enterpriseName" name="enterpriseName" ng-model="enterpriseInfo.enterpriseName"
									placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME'|translate}}"
									ng-blur="checkEnterpriseName(enterpriseInfo.enterpriseName,'')" 
									ng-disabled="operate =='detail' || operate == 'audit'"
									ng-class="{'redBorder':!!enterpriseInfo.enterpriseNameDesc}"
									title={{enterpriseInfo.enterpriseName}}>
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle" 
									ng-show="!!enterpriseInfo.enterpriseNameDesc">
								<span class="redFont" ng-show="!!enterpriseInfo.enterpriseNameDesc">
									{{enterpriseInfo.enterpriseNameDesc|translate}}</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'ENTERPRISE_ORGANIZATIONID'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="organizationID" 
									ng-model="enterpriseInfo.organizationID" id="organizationID"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTORGANIZATIONID'|translate}}" 
									 ng-disabled="operate =='detail' || operate == 'audit' || (operate =='edit' && isSuperManager)"
									 ng-class="{'redBorder':!organizationIDValidate}"
									 ng-blur="checkOrganizationID(enterpriseInfo.organizationID)"
									 title={{enterpriseInfo.organizationID}}>
								<span class="redFont" ng-show="!organizationIDValidate">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'ENTERPRISE_ORGANIZATIONIDDESC2'|translate"></span>
								</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'COMMON_PROVINCE'|translate"></span>：</label>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-2">
								<select class="form-control"
									name="province" ng-model="provinceID"
									ng-options="x.provinceID as x.provinceName for x in provinceList" 
									ng-change="changeSelectedProvince(provinceID)"
									ng-disabled="operate =='detail'|| operate == 'audit'">
									<option value="" ng-show="operate !='edit'">不限</option>
								</select>
							</div>
							<label class="col-lg-1 col-xs-1 col-sm-1 col-md-1 control-label">
								<span ng-bind="'COMMON_CITY'|translate"></span>：</label>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-2">
								<select class="form-control"
									name="city" ng-model="selectedCity"
									ng-options="x as x.cityName for x in subCityList" 
									ng-show="!provinceID || provinceID =='000'"
									ng-disabled="operate =='detail'|| operate == 'audit'">
									<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
								</select>
								<select class="form-control"
									name="city" ng-model="selectedCity"
									ng-change="changeSelectedCity()"
									ng-options="x as x.cityName for x in subCityList" 
									ng-show="provinceID && provinceID !='000'"
									ng-disabled="operate =='detail'|| operate == 'audit'">
									<option value="" ng-show="false">{{selectedCityName}}</option>
								</select>
							</div>
						</div>
					</div>
					<!--所属行业 added by wwx470949 2019-4-15-->
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'ENTERPRISE_INDUSTRY'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<select class="form-control"
										name="industry1"
										ng-model="selectedIndustry"
										ng-options="x.industryName for x in industryList"
										ng-change="changeIsSensitive(selectedIndustry)"
										ng-show="operate =='add'"
										ng-disabled="operate =='detail'|| operate == 'audit'">
									<option value="" ng-bind="" ng-show="operate !='detail'"></option>
								</select>
								<select class="form-control"
										name="industry2"
										ng-model="industry.industryID"
										ng-change="changeIsSensitive2()"
										ng-show="operate =='detail'|| operate == 'audit'|| operate == 'edit'"
										ng-disabled="operate =='detail'|| operate == 'audit'"
									    ng-options="x.industryID as x.industryName for x in industryList"
										>
								</select>
								<span class="uplodify-error-img" ng-show="selectedIndustryErrorInfo"></span>
								<span class="redFont" ng-bind="selectedIndustryErrorInfo|translate" ng-show="selectedIndustryErrorInfo"></span>
							</div>
						</div>
					</div>
					<!--营业执照-->
					<div class="form-group" ng-show="businessLicenseShow">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span class="redFont"
									  ng-bind="'*'|translate"
									  ng-model="isSensitiveIndustry"
									  ng-show="isSensitiveIndustry =='1' || isSensitiveIndustry =='3'">
								</span>
								<span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span>：</label>
							<div class="col-lg-4 col-xs-5 col-sm-6 col-md-4" ng-show="operate !='detail'&& operate != 'audit'">
								<input type="text" class="form-control" ng-model="businessLicenseURL.fileName" 
									 id="businessLicenseURLFileName"
									 placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
									 title={{businessLicenseURL.fileName}}>
								<span class="uplodify-error-img" ng-show="businessLicenseURL.errorInfo"></span>
								<span class="redFont" ng-bind="businessLicenseURL.errorInfo|translate" ng-show="businessLicenseURL.errorInfo"></span>
							</div>
							<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
								uploadifyid="uploadifyid1" validate="isValidate" filesize="filesize"
								mimetypes="mimetypes"
								formdata="uploadParam2" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
								urllist="urlList2" createthumbnail="isCreateThumbnail" auto="auto"
								style="margin-left: 15px;float: left;"
								ng-show="operate !='detail'&& operate != 'audit'"
								class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
							</cy:uploadifyfile>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5 add-table"
									ng-show="operate=='detail'|| operate == 'audit' && enterpriseInfo.businessLicenseURL">
								<button ng-show="enterpriseInfo.businessLicenseURL" type="submit" class="btn add-btn" style="white-space: normal;text-align:left;"
									ng-click="exportFile(enterpriseInfo.businessLicenseURL)" title={{businessLicenseURL.picName}}>
	            					<span style="word-break: break-all;" ng-bind="businessLicenseURL.picName"></span>
	            				</button>

							</div>
						</div>
					</div>
					<div class="form-group">
						<div class="row">
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5
								col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2">
								<input id="imgUrl" type="text" name="imgUrl" ng-model="businessLicenseURL" 
									ng-show="false" required>
							</div>
						</div>
					</div>
					<!--企业担保函-->
					<div class="form-group" ng-show="guaranteeLetterShow">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'ENTERPRISE_COMMITMENT'|translate"></span>：</label>
							
							<div class="col-lg-4 col-xs-5 col-sm-6 col-md-4" ng-show="operate !='detail'&& operate != 'audit'">
								<input type="text" class="form-control" ng-model="guaranteeLetterUrl.fileName" 
									 id="guaranteeLetterUrlFileName"
									 placeholder="{{'COMMON_IPTTYPE'|translate}}" ng-disabled="true"
									 title={{guaranteeLetterUrl.fileName}}>
								<span class="uplodify-error-img" ng-show="guaranteeLetterUrl.errorInfo"></span>
								<span class="redFont" ng-bind="guaranteeLetterUrl.errorInfo|translate" ng-show="guaranteeLetterUrl.errorInfo"></span>
							</div>
							<cy:uploadifyfile filelistid="fileList2" filepickerid="filePicker2" accepttype="accepttype"
								uploadifyid="uploadifyid2" validate="isValidate" filesize="filesize"
								mimetypes="mimetypes"
								formdata="uploadParam2" uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
								urllist="urlList2" createthumbnail="isCreateThumbnail" auto="auto"
								style="margin-left: 15px;float: left;width:120px;padding-right:0px"
								ng-show="operate !='detail'&& operate != 'audit'"
								class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
							</cy:uploadifyfile>
							<a ng-show="operate =='edit' || operate =='add'" target="_self" href="/qycy/ecpmp/doc/企业彩印业务受理承诺函(模板).doc" class="downMod"
							   style="margin-left: 20px;"
							   ng-bind="'GUARANTEETEMPLATE_DOWNLOD'|translate" download></a>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5 add-table"
									ng-show="operate=='detail'|| operate == 'audit' && guaranteeLetterUrl">
								<button ng-show="enterpriseInfo.extInfo.guaranteeLetterUrl" type="submit" style="white-space: normal;text-align:left;"
									class="btn add-btn" ng-click="exportFile(enterpriseInfo.extInfo.guaranteeLetterUrl)"
									title={{guaranteeLetterUrl.picName}}>
	            					<span style="word-break: break-all;" ng-bind="guaranteeLetterUrl.picName"></span>
	            				</button>

							</div>
						</div>
					</div>
					<div class="form-group">
						<div class="row">
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5
								col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2">
								<input id="imgUrl2" type="text" name="imgUrl" ng-model="guaranteeLetterUrl" 
									ng-show="false" required>
							</div>
						</div>
					</div>
					
					<div class="form-group" ng-show="operate =='detail'|| operate == 'audit'">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'CONTENTAUDIT_SUBMITTIME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="submitTime" 
									ng-model="enterpriseInfo.extInfo.submitTime" id="submitTime"
									 ng-disabled="operate =='detail'|| operate == 'audit'"
									 title={{enterpriseInfo.extInfo.submitTime}}>
							</div>
						</div>
					</div>
					
					<div class="form-group" ng-show="operate =='detail'|| operate == 'audit'">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'CONTENTAUDIT_AUDITPASSTIME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="auditTime" 
									ng-model="enterpriseInfo.extInfo.auditTime" id="auditTime"
									 ng-disabled="operate =='detail'|| operate == 'audit'"
									 title={{enterpriseInfo.extInfo.auditTime}}>
							</div>
						</div>
					</div>
		
					<div class="form-group" ng-show="operate =='detail'|| operate == 'audit'">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'CONTENTAUDIT_AUDITADVICE'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="auditDesc" 
									ng-model="enterpriseInfo.auditDesc" id="auditDesc"
									 ng-disabled="operate =='detail'|| operate == 'audit'"
									 title={{enterpriseInfo.auditDesc}}>
							</div>
						</div>
					</div>
		
					<div class="form-group" ng-show="operate =='detail'">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'CONTENTAUDIT_AUDITOR'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="auditor" 
									ng-model="enterpriseInfo.extInfo.auditor" id="auditor"
									 ng-disabled="operate =='detail'|| operate == 'audit'"
									 title={{enterpriseInfo.extInfo.auditor}}>
							</div>
						</div>
					</div>
				</div>
			</div>
			

			<div class="enterprise-btn"><!-- myForm.imgUrl.$invalid ||  -->
				<button type="submit" class="btn btn-primary search-btn"
				ng-disabled="!enterpriseNameValidate || !enterpriseInfo.enterpriseName || !guaranteeLetterUrl.upload ||
				businessLicenseURL.errorInfo || (!businessLicenseURL.upload&&isSensitiveIndustry =='1') || (!businessLicenseURL.upload&&isSensitiveIndustry =='3') || guaranteeLetterUrl.errorInfo ||
				!organizationIDValidate ||
				enterpriseNameExist"
				ng-click="beforeSave()" ng-if="operate =='add'"
				ng-bind="'COMMON_SAVE'|translate"></button><!-- myForm.imgUrl.$invalid ||  -->
				<button type="submit" class="btn btn-primary search-btn"
				ng-disabled="!enterpriseNameValidate || !enterpriseInfo.enterpriseName || !guaranteeLetterUrl.upload ||
				businessLicenseURL.errorInfo || (!businessLicenseURL.upload&&isSensitiveIndustry =='1') || (!businessLicenseURL.upload&&isSensitiveIndustry =='3') || guaranteeLetterUrl.errorInfo ||
				!organizationIDValidate || 
				enterpriseNameExist"
				ng-click="beforeSave()" ng-if="operate =='edit'"
				ng-bind="'COMMON_SAVE'|translate"></button>
				<button type="submit" class="btn btn-back" ng-click="cancelToEnterpriseList(this)"
					ng-bind="'COMMON_BACK'|translate" 
					ng-show="isSuperManager || (isAgent && operate =='add') 
						||(isAgent && operate =='edit')||(isAgent && operate =='detail' && enterpriseInfo.auditStatus==1)
						||(isAgent && operate =='audit')">
				</button>
			</div>
			</form>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
							</button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p class="tip">
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_OK'|translate"></button>
						</div>
					</div>
				</div>
			</div>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="ensureToList" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p class="tip">
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-click="ensureToList()" 
								ng-bind="'COMMON_OK'|translate"></button>
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_CANCLE'|translate"></button>
						</div>
					</div>
				</div>
			</div>
		</div>

	</body>
</html>