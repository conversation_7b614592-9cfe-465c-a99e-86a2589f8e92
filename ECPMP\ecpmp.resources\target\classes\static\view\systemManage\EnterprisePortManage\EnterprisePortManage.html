<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>企业接口管理</title>
    <link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
    <link href="../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../css/searchList.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
    <script type="text/javascript" src="../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../directives/preview/preview.css"/>
    <script src="../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <script src="../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../css/font-awesome.min.css">
    <script type="text/javascript" src="EnterprisePortManage.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../css/hotlineContentManage.css"/>
    <style>
        .cooperation-manage .coorPeration-table th, td {
            padding-left: 20px !important;
        }

        .delMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../assets/images/delete.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 136px;
            z-index: 99;
        }

        .addMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../assets/images/add.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 106px;
            z-index: 99;
        }

        .handle ul li icon.manage-icon {
            background-position: -126px 0;
        }

        .handle ul li icon.add-icon {
            background-position: -55px 0;
        }

        .handle ul li icon.import-icon {
            background-position: -90px 0;
        }

        .table th.adjustable-width {
            width: 25%;
        }

        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        #filePicker_ div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        .form-group div li {
            display: inline-block;
            margin-right: 10px;
            padding-right: 10px;
            cursor: pointer;
        }

        .form-group div li span {
            vertical-align: middle;
            margin-right: 4px;
        }

        /* media for adjustable search-table width  */
        @media (max-width: 1850px) {
            .table th.adjustable-width {
                width: 28%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1600px) {
            .table th.adjustable-width {
                width: 30%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1300px) {
            .table th.adjustable-width {
                width: 33%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1100px) {
            .table th.adjustable-width {
                width: 42%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        .ng-dirty.ng-invalid {
            border-color: red;
        }

        .ng-dirty.invalid {
            border-color: red;
        }

        .label-supply {
            display: inline-block;
            float: left;
            padding-right: 15px;
            padding-left: 15px;
        }

        .clearf:after {
            content: '';
            clear: both;
            height: 0;
            display: block;
        }
    </style>
</head>
<!--
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()" class="">
-->
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()">
<div class="cooperation-manage" style="overflow-x: scroll;">

    <div class="cooperation-head">
			<span class="frist-tab" ng-bind="'SYSTEMMANAGEMENT'|translate">
			</span>&nbsp;&gt;&nbsp;<span class="second-tab">企业接口管理</span>
    </div>

    <div class="cooperation-search">

        <form class="form-horizontal">
            <div class="form-group form-inline">
                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1" style="margin-top: 7px;">
                    <label
                           ng-bind="'ENTERPRISE_TYPE'|translate"></label>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 class1" style="white-space: nowrap;">
                    <select style="max-width:200px;width: 100%;" class="form-control"
                            ng-model="initSel.enterpriseType" id="enterpriseType"
                            ng-options="x.id as x.name for x in enterpriseTypeChoise"></select>
                </div>

                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 " style="margin-top: 7px;">
                    <label
                           ng-bind="'ENTERPRISE_ENTERPRISENAME1'|translate"></label>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 class1" style="white-space: nowrap;">
                    <input ng-model='initSel.enterpriseID' type="text" autocomplete="off" class="form-control"
                           ng-disabled="isProvinceType"
                           placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISEID'|translate}}">
                </div>
                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1" style="margin-top: 7px;">
                    <label
                           ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></label>
                </div>
                <div class="col-lg-1 col-md-3 col-sm-3 col-xs-3 class1" style="white-space: nowrap;">
                    <input ng-model='initSel.enterpriseName' type="text" autocomplete="off" class="form-control"
                           ng-disabled="isProvinceType"
                           placeholder="{{'HOTLINECONTENT_PLEASEINPUTCONTENTKEYWORDS'|translate}}">
                </div>




                <div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
                    <button ng-click="queryList()" type="submit" class="btn search-btn" style="margin-left: 86px">
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>
                <div class="clearf"></div>
            </div>
        </form>
    </div>

    <div class="coorPeration-table">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th style="width:10%" ng-bind="'ENTERPRISE_TYPE'|translate"></th>
                <th style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></th>
                <th style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></th>

                <th  style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISEID1'|translate"></th>
                <th  style="width:10%" ng-bind="'ENTERPRISE_ENTERPRISENAME2'|translate"></th>

                <th style="width:10%" ng-bind="'MEMBER_SYNCHRONIZATION_INTERFACE'|translate"></th>
                <th style="width:10%" ng-bind="'MEMBER_SYNCHRONIZATION_INTERFACE1'|translate"></th>
                <th style="width:10%" ng-bind="'DELIVERY_INTERFACE'|translate"></th>
                <th style="width:10%" ng-bind="'DELIVERY_INTERFACE1'|translate"></th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="item in contentInfoListData">

                <td><span title="{{getEnterpriseType(item.enterpriseType)}}">{{getEnterpriseType(item.enterpriseType)}}</span></td>
                <td><span title="{{item.enterpriseID?item.enterpriseID:'-'}}">{{item.enterpriseID ? item.enterpriseID : '-'}}</span></td>
                <td><span title="{{item.enterpriseName?item.enterpriseName:'-'}}">{{item.enterpriseName ? item.enterpriseName : '-'}}</span></td>
                <td><span title="{{item.subEnterpriseID?item.subEnterpriseID:'-'}}">{{item.subEnterpriseID?item.subEnterpriseID:'-'}}</span></td>
                <td><span title="{{item.subEnterpriseName?item.subEnterpriseName:'-'}}">{{item.subEnterpriseName?item.subEnterpriseName:'-'}}</span></td>
                <td>
                    <div class="handle">
                        <ul>
                            <li class="delete" ng-show="item.mpMemberStatus" ng-click="changeServTypeFunction(item,1,0)">
                                <span style="color:#705de1" ng-bind="'OPEN_PROT'|translate"></span>
                            </li>
                            <li class="delete" ng-show="!item.mpMemberStatus" ng-click="closeAccount(item,1,1)">
                                <span style="color:#705de1" ng-bind="'CLOSE'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
                <td>
                    <div class="handle">
                        <ul>
                            <li class="delete" ng-show="item.mpContentStatus" ng-click="changeServTypeFunction(item,2,0)">
                                <span style="color:#705de1" ng-bind="'OPEN_PROT'|translate"></span>
                            </li>
                            <li class="delete" ng-show="!item.mpContentStatus" ng-click="closeAccount(item,2,1)">
                                <span style="color:#705de1" ng-bind="'CLOSE'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
                <td>
                    <div class="handle">
                        <ul>
                            <li class="delete" ng-show="item.rxContentStatus" ng-click="changeServTypeFunction(item,3,0)">
                                <span style="color:#705de1" ng-bind="'OPEN_PROT'|translate"></span>
                            </li>
                            <li class="delete" ng-show="!item.rxContentStatus" ng-click="closeAccount(item,3,1)">
                                <span style="color:#705de1" ng-bind="'CLOSE'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
                <td>
                    <div class="handle">
                        <ul>
                            <li class="delete" ng-show="item.rxDeliveryStatus" ng-click="changeServTypeFunction(item,4,0)">
                                <span style="color:#705de1" ng-bind="'OPEN_PROT'|translate"></span>
                            </li>
                            <li class="delete" ng-show="!item.rxDeliveryStatus" ng-click="closeAccount(item,4,1)">
                                <span style="color:#705de1" ng-bind="'CLOSE'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
            <tr ng-show="contentInfoListData.length<=0">
                <td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
            </tr>
            </tbody>
        </table>

    </div>

    <div style="width: 1000px;">
        <ptl-page tableId="0" change="queryList('justPage')"></ptl-page>
    </div>

</div>

<!-- 新增(编辑)热线内容弹窗 -->
<div class="modal fade" id="addObject" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     style="overflow: auto">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>

                <h4 ng-if="operate != 'edit'" class="modal-title" id="myModalLabel">新增记录</h4>
                <h4 ng-if="operate == 'edit'" class="modal-title" id="myModalLabelEdit">修改记录</h4>

            </div>
            <div class="cooper-tab">
                <form class="form-horizontal" name="myForm" novalidate>
                    <div class="form-group" style="padding-top:5px">

                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 ">
                            <icon>*</icon>
                            <span ng-bind="'ENTERPRISE_TYPE'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <select ng-disabled="operate == 'edit'" style="max-width:200px;width: 100%;" class="form-control"
                                    ng-model="addObject.enterpriseType" id="enterpriseTypeChoise"
                                    ng-options="x.id as x.name for x in enterpriseTypeChoiseForInsert"
                                    ng-change="enterpriseList()"></select>
                        </div>
                    </div>
                    <div class="form-group" style="padding-top:5px">
                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 ">
                            <icon>*</icon>
                            <span ng-bind="'ENTERPRISE_SUBENTERPRISENAME1'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <input class="form-control" style="margin: 0px; width: 267px;position: absolute;z-index: 1;"
                                   name="enterpriseNameInput"
                                   ng-model="addObject.name"
                                   ng-change="enterpriseNameInputChange();"
                                   ng-blur="enterpriseNameInputBlur();"
                                   ng-disabled="operate == 'edit'">
                            <select style="width: 267px;position: absolute;z-index: -1;" class="form-control"
                                    name="enterpriseNameSelect"
                                    ng-model="addObject.id" id="contentStatus"
                                    ng-options="x.id as x.enterpriseName for x in queryEnterpriseList"
                                    ng-change="enterpriseNameSelectChange();"
                                    ng-if="operate != 'edit'"
                                    ng-click="enterpriseNameClick();">
                                <option id="other" value="">请选择</option>
                            </select>
                        </div>
                        <button  ng-disabled="operate == 'edit'" ng-click="enterpriseList()" type="submit" class="btn search-btn">
                            <icon class="search-iocn"></icon>
                            <span ng-bind="'COMMON_SEARCH'|translate"></span>
                        </button>

                    </div>


                    <div class="form-group" style="padding-top:5px">

                        <label for="" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 ">
                            <span ng-bind="'ENTERPRISEID'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <input class="form-control" style="margin: 0px; width: 267px;"
                                   name="colorContent"
                                   placeholder="{{msg}}"
                                   ng-model="addObject.id"
                                   disabled="disabled">
                        </div>
                    </div>


                    <div class="form-group servType">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 ">
                            <icon>*</icon>
                            <span>业务类型：</span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <li ng-show="addObject.enterpriseType != 35" class="check-li" ng-click="changeServType('0')" style="padding-top: 8px"><span
                                    class="check-btn checked-btn "> </span>热线彩印
                            </li>
                            <li class="check-li" ng-click="changeServType('1')" style="padding-top: 8px"><span
                                    class="check-btn checked-btn "> </span>名片彩印
                            </li>
                            </li>
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" ng-disabled="!addObject.id"
                        class="btn btn-primary search-btn" ng-bind="'PUBLIC_NUMBER_SUBMIT'|translate"
                        ng-click="beforeCommit()"></button>
                <button id="" type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!-- 删除热线内容弹窗 -->
<div class="modal fade bs-example-modal-sm" id="deleteHotlineContent" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content" style="width:390px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838'>是否确认删除？</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                        ng-click="delHotlineContent()"></button>
                <button id="deleteHotlineContentCancel" type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" ng-bind="'NO'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!-- 关闭二次确认弹框 -->
<div class="modal fade" id="deleteAccount" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content" style="width:450px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabelDel">确认关闭</h4>
            </div>
            <div class="modal-body">
                <p style="text-align:center;margin-top:30px;font-size:16px">请确认是否关闭该接口能力?</p>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn btn-primary search-btn" ng-click="delAccount()"
                >确认
                </button>
                <button type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        id="delAccountCancel" style="margin-left: 20px">取消
                </button>
            </div>
        </div>
    </div>
</div>
<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838'>
                        {{tip | translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>
</body>

</html>
