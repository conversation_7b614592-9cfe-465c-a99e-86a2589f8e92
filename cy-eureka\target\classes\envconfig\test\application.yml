spring:
  application:
    name: cy-eureka
server:
  port: 19008
eureka:
  client:
    register-with-eureka: false
    fetch-registry: false
    serviceUrl:
      defaultZone: http://localhost:19008/eureka/
  instance:
#    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
  server:
    enable-self-preservation: false
    eviction-interval-timer-in-ms: 30000
endpoints:
  shutdown:
    enabled: true
    sensitive: false