<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MsisdnAuthMapper">
    
    <select id="countByEnterpriseId" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ecpm_t_msisdn_auth
        WHERE msisdn_auth_enterprise_id = #{enterpriseId}
    </select>
    
    <select id="countMsisdnAuth" parameterType="com.huawei.jaguar.dsdp.ecpm.request.QueryMsisdnAuthDetailReq"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM ecpm_t_msisdn_auth a
        left join ecpm_t_msisdn_auth_enterprise b on a.msisdn_auth_enterprise_id = b.id
        where a.msisdn_auth_enterprise_id = #{req.msisdnAuthEnterpriseId}
        <if test="req.authMsisdn != null and req.authMsisdn != ''">
            AND a.auth_msisdn = #{req.authMsisdn}
        </if>
        <if test="req.enterpriseName != null and req.enterpriseName != ''">
            AND b.enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')
        </if>
        <if test="req.showName != null and req.showName != ''">
            AND a.show_name LIKE CONCAT('%', #{req.showName}, '%')
        </if>
        <if test="req.auditStatus != null and req.auditStatus != ''">
            AND a.audit_status = #{req.auditStatus}
        </if>
        <if test="req.onLineState != null">
            AND a.on_line_state = #{req.onLineState}
        </if>
        <if test="req.startTime != null">
            AND a.start_time <![CDATA[ >= ]]> #{req.startTime}
        </if>
        <if test="req.endTime != null">
            AND a.end_time <![CDATA[ <= ]]> #{req.endTime}
        </if>
    </select>
    
    <select id="queryMsisdnAuth" parameterType="com.huawei.jaguar.dsdp.ecpm.request.QueryMsisdnAuthDetailReq"
            resultType="com.huawei.jaguar.dsdp.ecpm.model.MsisdnAuthDetailInfo">
        SELECT 
            a.id,
            a.auth_msisdn as authMsisdn,
            a.logo_flag as logoFlag,
            a.audit_status as auditStatus,
            a.on_line_state as onLineState,
            a.start_time as startTime,
            a.end_time as endTime,
            a.show_name as showName,
            b.enterprise_name as enterpriseName
        FROM ecpm_t_msisdn_auth a
        left join ecpm_t_msisdn_auth_enterprise b on a.msisdn_auth_enterprise_id = b.id
        where a.msisdn_auth_enterprise_id = #{req.msisdnAuthEnterpriseId}
        <if test="req.authMsisdn != null and req.authMsisdn != ''">
            AND a.auth_msisdn = #{req.authMsisdn}
        </if>
        <if test="req.enterpriseName != null and req.enterpriseName != ''">
            AND b.enterprise_name LIKE CONCAT('%', #{req.enterpriseName}, '%')
        </if>
        <if test="req.showName != null and req.showName != ''">
            AND a.show_name LIKE CONCAT('%', #{req.showName}, '%')
        </if>
        <if test="req.auditStatus != null and req.auditStatus != ''">
            AND a.audit_status = #{req.auditStatus}
        </if>
        <if test="req.onLineState != null">
            AND a.on_line_state = #{req.onLineState}
        </if>
        <if test="req.startTime != null">
            AND a.start_time <![CDATA[ >= ]]> #{req.startTime}
        </if>
        <if test="req.endTime != null">
            AND a.end_time <![CDATA[ <= ]]> #{req.endTime}
        </if>
        ORDER BY a.create_time DESC
        LIMIT #{pageStart},#{pageSize}
    </select>
    
</mapper> 