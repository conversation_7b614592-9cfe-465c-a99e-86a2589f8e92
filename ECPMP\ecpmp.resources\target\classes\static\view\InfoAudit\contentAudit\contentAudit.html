<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title></title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../css/searchList.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<script type="text/javascript" src="contentAuditCtrl.js"></script>
	<style>
		.cooperation-manage .coorPeration-table th:first-child, td:first-child{
			padding-left: 30px!important;
		}
		td div{
			width: 100%;
			word-break: keep-all;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	</style>
</head>
<body ng-app='myApp' class="body-min-width" ng-controller='contentAuditCtrl' ng-init="init();" ng-cloak>
	<div class="cooperation-manage">
	<div ng-show="businessStatus !=1">
		<div class="cooperation-head"><span class="frist-tab"
			ng-bind="'MENU_INFOAUDIT'|translate"></span>&nbsp;&gt;&nbsp;<span
			class="second-tab" ng-bind="'MENU_CONTENTAUDIT1'|translate"></span></div>
		<div class="cooperation-search">
			<form class="form-horizontal" name='myForm'>
				<div class="form-group">
					<div ng-show="isSuperManager||loginRoleType=='agent'">
						<!--企业名称-->
						<label ng-if="loginRoleType!='agent'" for="enterpriseName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"
								ng-class="{true:'col-lg-1 col-md-1 col-sm-1 col-xs-1',false:'col-lg-1 col-md-2 col-sm-2 col-xs-2'}[isSuperManager]"
								style="white-space:nowrap" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></label>
						<!-- 子企业名称 -->
						<label ng-if="loginRoleType=='agent'" for="enterpriseName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"
								ng-class="{true:'col-lg-1 col-md-1 col-sm-1 col-xs-1',false:'col-lg-1 col-md-2 col-sm-2 col-xs-2'}[isSuperManager]"
								style="white-space:nowrap" ng-bind="'子企业名称'|translate"></label>

						<div class="cond-div" ng-class="{true:'col-lg-3 col-md-3 col-sm-3 col-xs-3',false:'col-lg-2 col-md-4 col-sm-4 col-xs-4'}[isSuperManager]">
							<input type="text" id="enterpriseName" class="form-control"
										 placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"
										 ng-model="enterpriseName">
						</div>
					</div>
					<!--内容ID-->
					<label for="contentID" class="control-label"
								 ng-class="{true:'col-lg-1 col-md-1 col-sm-1 col-xs-1',false:'col-lg-1 col-md-2 col-sm-2 col-xs-2'}[isSuperManager]"
								 style="white-space:nowrap" ng-bind="'CONTENTAUDIT_CONTENTID1'|translate"></label>

					<div class="cond-div"
							 ng-class="{true:'col-lg-3 col-md-3 col-sm-3 col-xs-3',false:'col-lg-2 col-md-4 col-sm-4 col-xs-4'}[isSuperManager]">
						<input autocomplete="off" name='contentID' type="text" id="contentID" class="form-control"
									 placeholder="{{'CONTENTAUDIT_INPUTCONTENTID'|translate}}"
									 ng-model="contentID">
					</div>
					<!--内容主题-->
					<label for="contentTitile" class="control-label"
								 ng-class="{true:'col-lg-1 col-md-1 col-sm-1 col-xs-1',false:'col-lg-1 col-md-2 col-sm-2 col-xs-2'}[isSuperManager]"
								 style="white-space:nowrap" ng-bind="'CONTENTAUDIT_CONTENTTHENE'|translate"></label>

					<div class="cond-div"
							 ng-class="{true:'col-lg-3 col-md-3 col-sm-3 col-xs-3',false:'col-lg-2 col-md-4 col-sm-4 col-xs-4'}[isSuperManager]">
						<input type="text" id="contentTitile" class="form-control"
									 placeholder="{{'CONTENTAUDIT_INPUTTHENE'|translate}}"
									 ng-model="contentTitile">
					</div>
					<!--彩印内容-->
					<label ng-show="loginRoleType!='agent'" for="contentName" class="control-label"
								 ng-class="{true:'col-lg-1 col-md-1 col-sm-1 col-xs-1',false:'col-lg-1 col-md-2 col-sm-2 col-xs-2'}[isSuperManager]"
								 style="white-space:nowrap" ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></label>

					<div ng-show="loginRoleType!='agent'" class="cond-div"
							 ng-class="{true:'col-lg-3 col-md-3 col-sm-3 col-xs-3',false:'col-lg-2 col-md-4 col-sm-4 col-xs-4'}[isSuperManager]">
						<input type="text" id="contentName" class="form-control"
									 placeholder="{{'CONTENTAUDIT_INPUTCONTENT'|translate}}"
									 ng-model="contentName">
					</div>
					
					<!--内容类型-->
					<label for="isRefYsmb" class="control-label"
								 ng-class="{true:'col-lg-1 col-md-1 col-sm-1 col-xs-1',false:'col-lg-1 col-md-2 col-sm-2 col-xs-2'}[isSuperManager]"
								 style="white-space:nowrap" ng-bind="'CONTENT_TYPE'|translate"></label>

					<div class="cond-div"
							 ng-class="{true:'col-lg-3 col-md-3 col-sm-3 col-xs-3',false:'col-lg-2 col-md-4 col-sm-4 col-xs-4'}[isSuperManager]">
						<select style="max-width:220px;width:100%;" class="form-control"
                                ng-model="isRefYsmb" id="isRefYsmb"
                                ng-options="x.id as x.name for x in isRefYsmbChoise"></select>
					</div>

					<div class="cond-div" style="margin-left: 35px;"
							 ng-class="{true:'col-lg-1 col-md-1 col-sm-1 col-xs-1',false:'col-lg-1 col-md-2 col-sm-2 col-xs-2'}[isSuperManager]">
						<button type="submit" class="btn search-btn" ng-disabled="myForm.contentID.$invalid" ng-click="getContentInfoList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<p style="font-size: 14px;font-weight: bold;padding: 25px 0 10px 20px;"
			 ng-bind="'CONTENTAUDIT_CONTENTLIST'|translate"></p>

		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th ng-bind="'CONTENTAUDIT_COOPCODE1'|translate" ng-if="isSuperManager||loginRoleType=='agent'" width="10%"></th>
					<th ng-bind="'CONTENTAUDIT_COOP_ENTERNAME1'|translate" ng-if="isSuperManager&&loginRoleType!='agent'" width="10%"></th>
					<th ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate"  ng-if="loginRoleType=='agent'" width="10%"></th>
					<th ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate" ng-style="{'width':isSuperManager||loginRoleType=='agent'?'8%':'10%'}"></th>
					<th ng-bind="'CONTENT_TYPE'|translate" width="10%"></th>
					<th ng-bind="'CONTENTAUDIT_CONTENTTHENE'|translate" width="10%"></th>
					<th ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate" width="10%"></th>
					<th ng-bind="'CONTENTAUDIT_SUBMITTIME'|translate" width="10%"></th>
					<th ng-bind="'CONTENTAUDIT_AUDITTIME'|translate" width="10%"></th>
					<th ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate" width="8%"></th>
					<th ng-bind="'CONTENTAUDIT_POSTTYPE'|translate" width="8%"></th>
					<th ng-bind="'CONTENTAUDIT_AUDITSTATE_MOVE'|translate" width="8%"></th>
					<th width="8%" ng-bind="'CONTENTAUDIT_AUDITSTATE_UNICOM'|translate"></th>
					<th width="8%" ng-bind="'CONTENTAUDIT_AUDITSTATE_TELECOM'|translate"></th>
					<th ng-bind="'CONTENTAUDIT_AUDITOR'|translate" width="5%"></th>
					<th ng-bind="'CONTENTAUDIT_AUDITADVICE_MOVE'|translate" width="5%"></th>
				</tr>
				</thead>
				<tbody>
					<tr ng-repeat="item in contentInfoList">
						<td ng-if="isSuperManager||loginRoleType=='agent'" ng-bind="item.enterpriseID" title="{{item.enterpriseID}}"></td>
						<td ng-if="isSuperManager||loginRoleType=='agent'" ng-bind="item.enterpriseName" title="{{item.enterpriseName}}"></td>
						<td ng-bind="item.contentID" title="{{item.contentID}}"></td>
						<td>
							<div ng-show="item.refYsmbID==null" ng-bind="'CONTENT_TYPE_DIY'|translate" ng-attr-title="{{'CONTENT_TYPE_DIY'|translate}}"></div>
							<div ng-show="item.refYsmbID!=null" ng-bind="'CONTENT_TYPE_YSMB'|translate" ng-attr-title="{{'CONTENT_TYPE_YSMB'|translate}}"></div>
						</td>
						<td ng-bind="item.contentTitle" title="{{item.contentTitle}}"></td>
						<td ng-if="item.signature&&item.signature!=null&&item.signature!=''&& (((item.servType == '1' || item.servType == '5') && item.subServType != '3')|| (item.servType != '1' && item.servType != '5')) " ng-bind="'【' + item.signature+'】' + (item.content||'')" title="{{'【' + item.signature+'】' + (item.content||'')}}"></td>
						<td ng-if="item.signature&&item.signature!=null&&item.signature!=''&& (item.servType == '1' || item.servType == '5') && item.subServType == '3' " title="【{{item.signature}}】&#10;主叫：{{item.content.split('||||')[0]}}&#10;被叫：{{item.content.split('||||')[1]}}">
							【{{item.signature}}】<br/>主叫：{{item.content.split('||||')[0]}}<br/>被叫：{{item.content.split('||||')[1]}}</td>
						<td ng-if="(item.signature==null||item.signature=='' )&& (item.content=='' || item.content == null)" ng-bind="item.content" title="{{item.content}}"></td>
						<td ng-if="(item.signature==null||item.signature=='' )&& item.content!='' && item.content != null && (item.servType == '1' || item.servType == '5') && item.subServType == '3'" title="主叫：{{item.content.split('||||')[0]}}&#10;被叫：{{item.content.split('||||')[1]}}">
							主叫：{{item.content.split('||||')[0]}}<br/>被叫：{{item.content.split('||||')[1]}}
						</td>
						<td ng-if="(item.signature==null||item.signature=='' )&& item.content!='' && item.content != null && (((item.servType == '1' || item.servType == '5') && item.subServType != '3')|| (item.servType != '1' && item.servType != '5'))" ng-bind="item.content" title="{{item.content}}"></td>
						<td ng-bind="item.createTime|formatDate" title="{{item.createTime|formatDate}}"></td>
						<td ng-bind="item.auditTime|formatDate" title="{{item.auditTime|formatDate}}"></td>
						<td>
							<div ng-bind="'CONTENTAUDIT_MINGPIAN'|translate" ng-attr-title="{{'CONTENTAUDIT_MINGPIAN'|translate}}"
									 ng-show="item.servType===1"></div>
							<div ng-bind="'CONTENTAUDIT_HOTLINE'|translate" ng-attr-title="{{'CONTENTAUDIT_HOTLINE'|translate}}"
									 ng-show="item.servType===2"></div>
							<div ng-bind="'CONTENTAUDIT_ADVERTISE'|translate" ng-attr-title="{{'CONTENTAUDIT_ADVERTISE'|translate}}"
									 ng-show="item.servType===3"></div>
							<div ng-bind="'GROUP_SEND'|translate" ng-attr-title="{{'GROUP_SEND'|translate}}"
								 ng-show="item.servType===4"></div>
							<div ng-bind="'CONTENTAUDIT_HOTLINE_PROVINCE'|translate" ng-attr-title="{{'CONTENTAUDIT_HOTLINE_PROVINCE'|translate}}"
								 ng-show="item.servType===5"></div>


						</td>
						<td>
							<div ng-bind="'CONTENTAUDIT_CALL'|translate" ng-attr-title="{{'CONTENTAUDIT_CALL'|translate}}"
									 ng-show="item.subServType===1"></div>
							<div ng-bind="'CONTENTAUDIT_CALLED'|translate" ng-attr-title="{{'CONTENTAUDIT_CALLED'|translate}}"
									 ng-show="item.subServType===2"></div>
							<div ng-bind="'CONTENTAUDIT_PX'|translate" ng-attr-title="{{'CONTENTAUDIT_PX'|translate}}"
									 ng-show="item.subServType===3"></div>
							<div ng-bind="'CALLER_CONTENTAUDIT_GJDX2'|translate" ng-attr-title="{{'CALLER_CONTENTAUDIT_GJDX2'|translate}}"
									 ng-show="item.subServType===4&&item.hangupType===1"></div>
							<div ng-bind="'CALLED_CONTENTAUDIT_GJDX2'|translate" ng-attr-title="{{'CALLED_CONTENTAUDIT_GJDX2'|translate}}"
									 ng-show="item.subServType===4&&(item.hangupType===2 || item.hangupType==null)"></div>

							<div ng-bind="'HY_CALLED_CONTENTAUDIT_GJDX2'|translate" ng-attr-title="{{'HY_CALLED_CONTENTAUDIT_GJDX2'|translate}}"
								 ng-show="item.subServType===4&&item.hangupType===3"></div>

							<div ng-bind="'CONTENTAUDIT_GJCX'|translate" ng-attr-title="{{'CONTENTAUDIT_GJCX'|translate}}"
									 ng-show="item.subServType===8&&item.servType!==4"></div>
							<div  ng-attr-title="交互USSD"
								 ng-show="item.subServType===7">交互USSD</div>
							<div ng-bind="'USSD_NOTIFY_TYPE10'|translate" ng-attr-title="{{'USSD_NOTIFY_TYPE10'|translate}}"
								 ng-show="item.subServType===16"></div>
							<div  ng-attr-title="彩信"
								  ng-show="item.subServType===8&&item.servType===4">彩信</div>
							<div  ng-attr-title="群发短信"
								 ng-show="item.subServType===17">群发短信</div>

							<!--<div  ng-attr-title="彩信"-->
								  <!--ng-show="item.subServType===3&&item.servType===4&&item.scene===9">USSD</div>-->
							<!--<div  ng-attr-title="彩信"-->
								  <!--ng-show="item.subServType===3&&item.servType===4&&item.scene===10">闪信</div>-->
						</td>
						<td ng-show = "item.servType != '1' && item.servType != '5'"><span title="{{getApproveStatus(item.approveStatus)}}">{{getApproveStatus(item.approveStatus)}}</span></td>
						<td ng-show = "item.servType == '1' || item.servType == '5'"><span title="{{getUnicomApproveStatus(item.mobileApproveStatus)}}">{{getUnicomApproveStatus(item.mobileApproveStatus)}}</span></td>
						<td ng-bind="getUnicomApproveStatus(item.unicomApproveStatus)" title="{{getUnicomApproveStatus(item.unicomApproveStatus)}}"></td>
						<td ng-bind="getUnicomApproveStatus(item.telecomApproveStatus)" title="{{getUnicomApproveStatus(item.telecomApproveStatus)}}"></td>
						<td ng-if="item.servType != '2' && item.subServType == '3'" title="主叫：{{item.auditor.split('||||')[0]}}&#10;被叫：{{item.auditor.split('||||')[1]}}">
							主叫：{{item.auditor.split('||||')[0]}}<br/>被叫：{{item.auditor.split('||||')[1]}}</td>
						<td ng-if="item.servType == '2' || item.subServType != '3'" ng-bind="item.auditor" title="{{item.auditor}}"></td>
						<td>
							<div ng-show="item.approveStatus==1" ng-bind="'CONTENTAUDIT_FAILED'|translate"  ng-attr-title="{{'CONTENTAUDIT_FAILED'|translate}}"></div>
							<div ng-show="item.approveStatus!=1 && item.servType != '1' && item.servType != '5'" ng-bind="item.approveIdea" title="{{item.approveIdea}}"></div>
							<div ng-show="item.approveStatus!=1 && (item.servType == '1' || item.servType == '5') && item.subServType == '3' && item.mobileApproveIdea!=null && item.mobileApproveIdea!=''" title="主叫：{{item.mobileApproveIdea.split('||||')[0]}}&#10;被叫：{{item.mobileApproveIdea.split('||||')[1]}}">
								主叫：{{item.mobileApproveIdea.split('||||')[0]}}<br/>被叫：{{item.mobileApproveIdea.split('||||')[1]}}
							</div>
							<div ng-show="item.approveStatus!=1 && (item.servType == '1' || item.servType == '5') && item.subServType != '3'" title="{{item.mobileApproveIdea}}">
								{{item.mobileApproveIdea}}
							</div>
						</td>

					</tr>
					<tr ng-show="contentInfoList===null || contentInfoList.length<=0">
						<td style="text-align:center" colspan="11" ng-bind="'COMMON_NODATA'|translate"></td>
					</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="getContentInfoList('justPage')"></ptl-page>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838' ng-bind="tip|translate"></p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
		</div>
		<!-- 业务未开通 -->
	<div ng-show="businessStatus ==1" class="modal fade bs-example-modal-sm" id="Modalisaengt" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" >
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 18px;color:#383838'>
							业务未开通
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	</div>

</body>
</html>