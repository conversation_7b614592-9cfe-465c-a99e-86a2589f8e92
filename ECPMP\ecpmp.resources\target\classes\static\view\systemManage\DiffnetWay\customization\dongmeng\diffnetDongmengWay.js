var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('statisticsController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
    	$scope.accountID = $.cookie('accountID') || '1000';
        //判断是否超管
        $scope.isSuperManager = false;
        $scope.isAgent = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.platformArr = ['0', '0'];
        $scope.enterpriseTypeArr = ['0', '0', '0', '0', '0', '0', '0'];
        $scope.servTypeArr = ['0', '0', '0', '0', '0']
        $scope.addExportParamsJson = "";
        $scope.queryDiffnetDeliveryWay();
        $scope.isSave = false;
    }

    $scope.changePlatform = function (val) {
    	if ($scope.platformArr[val] == 1)
    	{
            $('.platform .check-li').eq(val).find('span').removeClass('checked');
            $scope.platformArr[val] = 0;
    	}
    	else
        {
            $('.platform .check-li').eq(val).find('span').addClass('checked');
            $scope.platformArr[val] = 1;
        }
 	};
 	
 	$scope.changeEnterpriseType = function (val) {
    	if ($scope.enterpriseTypeArr[val] == 1)
    	{
            $('.enterpriseType .check-li').eq(val).find('span').removeClass('checked');
            $scope.enterpriseTypeArr[val] = 0;
    	}
    	else
        {
            $('.enterpriseType .check-li').eq(val).find('span').addClass('checked');
            $scope.enterpriseTypeArr[val] = 1;
        }
    	var rx = [0, 1, 2, 3,6];
    	$scope.checkClass(rx, 0);
    	var mp = [0,1,2,3,4,6,7];
    	$scope.checkClass(mp, 1);
    	var rxmp = [2,3];
    	$scope.checkClass(rxmp, 2);
    	var tz = [1];
    	$scope.checkClass(tz, 3);
    	var gg = [0,1,5];
    	$scope.checkClass(gg, 4);
 	};
 	$scope.checkClass = function(enterpriseTypes, val) {
 		var isRemove = false;
 		for (var i = 0; i < enterpriseTypes.length; i++)
 	    {
 			if ($scope.enterpriseTypeArr[enterpriseTypes[i]] == 1 )
 		    {
 				isRemove = true;
 		    }
 	    }
 		if ($scope.countEnterpriseType())
 	    {
				isRemove = true;
 	    }
 		if (isRemove)
 	    {
    		$('.servType .check-li').eq(val).removeClass('hide');
 	    }
 		else
 	    {
    		$scope.addClass(val);
 	    }
 	}
 	$scope.addClass = function(val) {
 		$('.servType .check-li').eq(val).addClass('hide');
		$('.servType .check-li').eq(val).find('span').removeClass('checked');
		$scope.servTypeArr[val] = 0;
 	}
 	$scope.countEnterpriseType = function () {
 		var count = 0;
 		for (var i =0 ; i< $scope.enterpriseTypeArr.length; i++)
 		{
 			count = count + $scope.enterpriseTypeArr[i];
 		}
 		if (count == 0)
 	    {
 			return true;
 	    }
 		return false;
 	}
 	
 	$scope.countServType = function () {
 		var count = 0;
 		for (var i =0 ; i< $scope.servTypeArr.length; i++)
 		{
 			count = count + $scope.servTypeArr[i];
 		}
 		if (count == 0)
 	    {
 			return true;
 	    }
 		return false;
 	}
 	
 	$scope.countPlatform = function () {
 		var count = 0;
 		for (var i =0 ; i< $scope.platformArr.length; i++)
 		{
 			count = count + $scope.platformArr[i];
 		}
 		if (count == 0)
 	    {
 			return true;
 	    }
 		return false;
 	}
 	$scope.changeServType = function (val) {
    	if ($scope.servTypeArr[val] == 1)
    	{
            $('.servType .check-li').eq(val).find('span').removeClass('checked');
            $scope.servTypeArr[val] = 0;
    	}
    	else
        {
            $('.servType .check-li').eq(val).find('span').addClass('checked');
            $scope.servTypeArr[val] = 1;
        }
 	};
 	$scope.check = function () {
 		var servTypeCount = $scope.countServType();
 		var enterpriseTypeCount = $scope.countEnterpriseType();
 		var platformCount = $scope.countPlatform();

 		if (servTypeCount && enterpriseTypeCount && platformCount)
 	    {
 			return true;
 	    }
 		if (!servTypeCount && !enterpriseTypeCount && !platformCount)
 	    {
 			return true;
 	    }
 		return false;
 	}
 	/*
     * 导出文件弹窗
     */
     $scope.exportFile = function () {
         $scope.remarks = "";
         $scope.isGreaterFileName = false;
         $scope.isEmptyFileName = false;
         $scope.isSpecialCharacters = false;
         $scope.isFileNamePass = true;
         $("#exportFile").modal("show");
     };
     /*
      * 文件名校验
      */
	$scope.checkEmpty = function(){
		$scope.isFileNamePass = true;
		$scope.isGreaterFileName = false;


		var reg = /^[a-z0-9\u4e00-\u9fa5]+$/i
		// if(!$scope.remarks){
		//     $scope.isGreaterFileName = false;
		//     $scope.isEmptyFileName = true;
		//     $scope.isFileNamePass = false;
		//     $scope.isSpecialCharacters = false
		// }else if(!reg.test($scope.remarks)){
		//     $scope.isGreaterFileName = false;
		//     $scope.isEmptyFileName = false;
		//     $scope.isFileNamePass = false;
		//     $scope.isSpecialCharacters = true
		// }else

		if($scope.remarks.length > 255){
			$scope.isEmptyFileName = false;
			$scope.isGreaterFileName = true;
			$scope.isFileNamePass = false;
			$scope.isSpecialCharacters = false
		}
		//     else {
		//     $scope.isEmptyFileName = false;
		//     $scope.isGreaterFileName = false;
		//     $scope.isSpecialCharacters = false
		//     $scope.isFileNamePass = true;
		// }
	};
 	$scope.save = function () {
 		if ($scope.check())
 		{
 			if ($scope.isSave)
 		    {
 				return;
 		    }
 			$scope.isSave = true;
 			var diffnetDeliveryWayList = [];
 			var orgPlatforms = [3, 2];
 			var platforms = [];
 			for (var i=0; i< $scope.platformArr.length; i++)
 		    {
 				if ($scope.platformArr[i] == 1)
 			    {
 					platforms.push(orgPlatforms[i]);
 			    }
 			}
 			$scope.diffnetDeliveryWay.platforms = platforms.join();
 			
 			var orgEnterpriseType = [1, 3, 5, 15, 6, 4,25,35];
 			var enterpriseTypes = [];
 			for (var i=0; i< $scope.enterpriseTypeArr.length; i++)
 		    {
 				if ($scope.enterpriseTypeArr[i] == 1)
 			    {
 					enterpriseTypes.push(orgEnterpriseType[i]);
 			    }
 			}
 			$scope.diffnetDeliveryWay.enterpriseType = enterpriseTypes.join();
 			
 			var orgServType = [2, 1, 5, 4, 3];
 			var servTypes = [];
 			for (var i=0; i< $scope.servTypeArr.length; i++)
 		    {
 				if ($scope.servTypeArr[i] == 1)
 			    {
 					servTypes.push(orgServType[i]);
 			    }
 			}
 			$scope.diffnetDeliveryWay.servType = servTypes.join();
 			diffnetDeliveryWayList.push($scope.diffnetDeliveryWay);
 			var req = {
 	                "isDefault": 0,
 	                "diffnetDeliveryWayList": diffnetDeliveryWayList
 	            };
 			$scope.addExportParamsJson = JSON.stringify(req);
 			RestClientUtil.ajaxRequest({
 	            type: 'POST',
 	            url: "/ecpmp/ecpmpServices/contentService/setDiffnetDeliveryWay",
 	            data: JSON.stringify(req),
 	            success: function (result) {
 	                $rootScope.$apply(function () {
 	                    var data = result.result;
 	                    if (data.resultCode == '1010100000') {
 	                    	$scope.platformArr = ['0', '0'];
 	                    	$scope.enterpriseTypeArr = ['0', '0', '0', '0', '0', '0', '0'];
 	                    	$scope.servTypeArr = ['0', '0', '0', '0', '0']
 	                    	$scope.queryDiffnetDeliveryWay();
 	                    	
// 	                    	$scope.tip = "保存成功";
// 	                    	$('#myModal').modal();
 	                    	$scope.exportFile();
 	                    }
 	                    else {
 	                        $scope.tip = data.resultDesc;
 	                        $('#myModal').modal();
	 	                	$scope.isSave = false;

 	                    }
 	                })
 	            },
 	            error: function () {
 	                $rootScope.$apply(function () {
	 	            	$scope.isSave = false;
 	                    $scope.tip = "**********";
 	                    $('#myModal').modal();
 	                }
 	                )
 	            }
 	        });
 		}
 		else
 		{
 			$scope.tip = "运营商、企业类型、业务类型需要均不选或至少选择一项";
            $('#myModal').modal();
 		}
 	}
 	/*
     * 提交导出文件信息
     */
	  $scope.submitExportTask = function(){
	      $("#exportFile").modal("hide");
	      var createExportTaskInfoRequest = {
				"exportTaskInfo": {
					"fileName":null,
					"remarks":$scope.remarks,
	              "taskType": 11,
	              "taskStatus": 0,
	              "operatorID": $scope.accountID,
	              "paramsJson": $scope.addExportParamsJson
	          }	
	  	}
	      console.log(createExportTaskInfoRequest);
	      RestClientUtil.ajaxRequest({
	          type: 'POST',
	          url: "/ecpmp/ecpmpServices/exportTaskService/createExportTask",
	          data: JSON.stringify(createExportTaskInfoRequest),
	          success: function (data) {
	              $rootScope.$apply(function () {
	                  var result = data.result;
	                  if (result.resultCode == '**********') {
						  if(data.desc){
							  $scope.tip = data.desc;
							  $('#myModal').modal();
						  }
	                  }else {
	                      $scope.tip = result.resultCode;
	                      $('#myModal').modal();
	                  }
	              })
	          },
	          error:function(){
	              $rootScope.$apply(function(){
	                      $scope.tip='**********';
	                      $('#myModal').modal();
	                  }
	              )
	          }
	      });
	  };
    //后续post的函数
    $scope.queryDiffnetDeliveryWay = function (condition) {
    	var req = {
                "isDefault": 0,
                "wayType": 2
            };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryDiffnetDeliveryWay",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1010100000') {
                        var diffnetDeliveryWayList = result.diffnetDeliveryWayList || [];
                        if (diffnetDeliveryWayList && diffnetDeliveryWayList[0])
                        {
                        	var diffnetDeliveryWay = diffnetDeliveryWayList[0];
                        	$scope.diffnetDeliveryWay = diffnetDeliveryWay;
                        	var platforms = diffnetDeliveryWay.platforms;
                        	angular.forEach(platforms.split(","), function(item){
                        		switch (parseInt(item)) {
                        			case 2:
                        				$scope.changePlatform(1);
                        				break;
                        			case 3:
                        				$scope.changePlatform(0);
                        				break;
                        			default:
                        				break;
                        		}
                        		
                        	});
                        	var enterpriseTypes = diffnetDeliveryWay.enterpriseType;
                        	angular.forEach(enterpriseTypes.split(","), function(item){
                        		switch (parseInt(item)) {
                        			case 1:
                        				$scope.changeEnterpriseType(0);
                        				break;
                        			case 3:
                        				$scope.changeEnterpriseType(1);
                        				break;
                        			case 5:
                        				$scope.changeEnterpriseType(2);
                        				break;
                        			case 15:
                        				$scope.changeEnterpriseType(3);
                        				break;
                        			case 6:
                        				$scope.changeEnterpriseType(4);
                        				break;
                        			case 4:
                        				$scope.changeEnterpriseType(5);
										break;
									case 25:
										$scope.changeEnterpriseType(6);
                        				break;
									case 35:
										$scope.changeEnterpriseType(7);
										break;
                        			default:
                        				break;
                        		}
                        		
                        	});
                        	
                        	var servTypes = diffnetDeliveryWay.servType;
                        	angular.forEach(servTypes.split(","), function(item){
                        		switch (parseInt(item)) {
                        			case 2:
                        				$scope.changeServType(0);
                        				break;
                        			case 1:
                        				$scope.changeServType(1);
                        				break;
                        			case 5:
                        				$scope.changeServType(2);
                        				break;
                        			case 4:
                        				$scope.changeServType(3);
                        				break;
                        			case 3:
                        				$scope.changeServType(4);
                        				break;
                        			default:
                        				break;
                        		}
                        		
                        	});
                        	
                        	
                        	
                        }
 	                	$scope.isSave = false;

                    }
                    else {
                        $scope.tip = data.resultDesc;
                        $('#myModal').modal();
 	                	$scope.isSave = false;

                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
	                	$scope.isSave = false;

                }
                )
            }
        });

    };



}])