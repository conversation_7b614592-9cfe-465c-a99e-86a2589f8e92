<!DOCTYPE html>
<html>
<head lang="en">
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<meta http-equiv="X-UA-Compatible" content="IE=11"/>
	<title></title>
	<link rel="stylesheet" type="text/css" href="../../../css/bootstrap.min.css"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../css/searchList.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<script type="text/javascript" src="templateAudit.js"></script>
	<style>
		.cooperation-manage .coorPeration-table th:first-child, td:first-child{
			padding-left: 30px!important;
		}
		.cooperation-manage .form-group{
			padding-top: 20px;
			padding-bottom: 20px;
		}
		.form-horizontal .control-label{
			padding-top: 7px;
		}
		td div{
			width: 100%;
			word-break: keep-all;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	</style>
</head>
<body ng-app='myApp' class="body-min-width" ng-controller='templateAuditCtrl' ng-init="init();" ng-cloak>
	<div class="cooperation-manage">
		<div class="cooperation-head"><span class="frist-tab" ng-bind="'MENU_INFOAUDIT'|translate"></span>&nbsp;&gt;&nbsp;<span
			class="second-tab" ng-bind="'TEMPLATE_AUDIT1'|translate"></span></div>
		<div class="cooperation-search">
			<form class="form-horizontal" name='myForm'>
				<div class="form-group">
					<div ng-show="isSuperManager===true">
						<!--企业名称-->
						<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
									 style="white-space:nowrap" ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></label>

						<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" class="cond-div">
							<input type="text" id="enterpriseName" class="form-control"
										 placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"
										 ng-model="enterpriseName">
						</div>
					</div>
					<!--内容ID-->
					<label for="contentID" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'CONTENTAUDIT_CONTENTID'|translate"></label>

					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" class="cond-div">
						<input autocomplete="off" name='contentID' type="text" id="contentID" class="form-control"
									 placeholder="{{'CONTENTAUDIT_INPUTCONTENTID'|translate}}"
									 ng-model="contentID">
					</div>
					<!--模板内容-->
					<label for="templateContent" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
								 style="white-space:nowrap" ng-bind="'TEMPLATE_CONTENT'|translate"></label>

					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" class="cond-div">
						<input style="min-width:180px;" type="text" id="templateContent" class="form-control"
									 placeholder="{{'TEMPLATE_PLH_KEYWORDS'|translate}}"
									 ng-model="templateContent">
					</div>

					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" class="cond-div">
						<button type="submit" class="btn search-btn" ng-click="getContentInfoList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
		<p style="font-size: 14px;font-weight: bold;padding: 25px 0 10px 20px;"
			 ng-bind="'TEMPLATE_AUDITLIST'|translate"></p>

		<div class="coorPeration-table">
			<table class="table table-striped table-hover">
				<thead>
				<tr>
					<th ng-bind="'CONTENTAUDIT_COOPCODE1'|translate" ng-if="isSuperManager===true" width="12%"></th>
					<th ng-bind="'CONTENTAUDIT_COOP_ENTERNAME1'|translate" ng-if="isSuperManager===true" width="11%"></th>
					<th ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate" width="8%"></th>
					<th ng-bind="'TEMPLATE_CONTENT'|translate"></th>
					<th ng-bind="'CONTENTAUDIT_SUBMITTIME'|translate"></th>
					<th ng-bind="'CONTENTAUDIT_AUDITTIME'|translate"></th>
					<th ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
					<th ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></th>
					<th ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>
					<th ng-bind="'CONTENTAUDIT_AUDITADVICE'|translate" width="10%"></th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat="item in contentInfoList">
					<td ng-if="isSuperManager===true" ng-bind="item.enterpriseID" title="{{item.enterpriseID}}"></td>
					<td ng-if="isSuperManager===true" ng-bind="item.enterpriseName" title="{{item.enterpriseName}}"></td>
					<td ng-bind="item.contentID" title="{{item.contentID}}"></td>
					<td ng-if="item.signature&&item.signature!=null&&item.signature!=''" ng-bind="【item.signature】item.content" title="{{item.content}}"></td>
					<td ng-if="item.signature==null||item.signature==''" ng-bind="item.content" title="{{item.content}}"></td>
					<td ng-bind="item.createTime|formatDate" title="{{item.createTime|formatDate}}"></td>
					<td ng-bind="item.auditTime|formatDate" title="{{item.auditTime|formatDate}}"></td>
					<td>
						<div ng-bind="'CONTENTAUDIT_MINGPIAN'|translate" ng-attr-title="{{'CONTENTAUDIT_MINGPIAN'|translate}}"
								 ng-show="item.servType===1"></div>
						<div ng-bind="'CONTENTAUDIT_HOTLINE'|translate" ng-attr-title="{{'CONTENTAUDIT_HOTLINE'|translate}}"
								 ng-show="item.servType===2"></div>
						<div ng-bind="'CONTENTAUDIT_ADVERTISE'|translate" ng-attr-title="{{'CONTENTAUDIT_ADVERTISE'|translate}}"
								 ng-show="item.servType===3"></div>
					</td>
					<td>
						<div ng-bind="'CONTENTAUDIT_CALL'|translate" ng-attr-title="{{'CONTENTAUDIT_CALL'|translate}}"
								 ng-show="item.subServType===1"></div>
						<div ng-bind="'CONTENTAUDIT_CALLED'|translate" ng-attr-title="{{'CONTENTAUDIT_CALLED'|translate}}"
								 ng-show="item.subServType===2"></div>
						<div ng-bind="'CONTENTAUDIT_PX'|translate" ng-attr-title="{{'CONTENTAUDIT_PX'|translate}}"
								 ng-show="item.subServType===3"></div>
						<div ng-bind="'CONTENTAUDIT_GJDX'|translate" ng-attr-title="{{'CONTENTAUDIT_GJDX'|translate}}"
								 ng-show="item.subServType===4"></div>
						<div ng-bind="'CONTENTAUDIT_GJCX'|translate" ng-attr-title="{{'CONTENTAUDIT_GJCX'|translate}}"
								 ng-show="item.subServType===8"></div>
					</td>
					<td ng-bind="getApproveStatus(item.approveStatus)" title="{{getApproveStatus(item.approveStatus)}}"></td>
					<td>
						<div ng-show="item.approveStatus==1" ng-bind="'CONTENTAUDIT_FAILED'|translate"  ng-attr-title="{{'CONTENTAUDIT_FAILED'|translate}}"></div>
						<div ng-show="item.approveStatus!=1" ng-bind="item.approveIdea" title="{{item.approveIdea}}"></div>
					</td>
				</tr>
				<tr ng-show="contentInfoList===null || contentInfoList.length<=0">
					<td style="text-align:center" colspan="11" ng-bind="'COMMON_NODATA'|translate"></td>
				</tr>
				</tbody>
			</table>
		</div>
		<div>
			<ptl-page tableId="0" change="getContentInfoList('justPage')"></ptl-page>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838' ng-bind="tip|translate"></p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
									ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</body>
</html>