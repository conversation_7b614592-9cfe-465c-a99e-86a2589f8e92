<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.cutover.dao.ecpm.mapper.EnterpriseDayStatMapper">
	<resultMap id="enterpriseDayStatWrapper"
		type="com.huawei.jaguar.cutover.dao.domain.EnterpriseDayStatWrapper">
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="enterpriseName" column="enterpriseName"
			javaType="java.lang.String" />
		<result property="enterpriseType" column="enterpriseType"
			javaType="java.lang.Integer" />
		<result property="parentEnterpriseID" column="parentEnterpriseID"
			javaType="java.lang.Integer" />
		<result property="provinceID" column="provinceID" javaType="java.lang.String" />
		<result property="cityID" column="cityID" javaType="java.lang.String" />
		<result property="statDate" column="statDate" javaType="java.lang.String" />
		<result property="serviceType" column="serviceType" javaType="java.lang.Integer" />
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />
		<result property="chargeType" column="chargeType" javaType="java.lang.Integer" />
		<result property="memberCount" column="memberCount" javaType="java.lang.Integer" />
		<result property="deliveryMemberCount" column="deliveryMemberCount" 
			javaType="java.lang.Integer" />
		<result property="useCount" column="useCount" javaType="java.lang.Long" />
		<result property="experienceCount" column="experienceCount"
			javaType="java.lang.Long" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="useCountMobile" column="useCountMobile" javaType="java.lang.Long" />
		<result property="useCountUnicom" column="useCountUnicom" javaType="java.lang.Long" />
		<result property="useCountTelecom" column="useCountTelecom" javaType="java.lang.Long" />
	</resultMap>
	
	<!-- 以企业为维度查询企业统计信息 -->
	<select id="queryEnterpriseDayStat" resultMap="enterpriseDayStatWrapper">
		select
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom
		from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate}
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		order by statDate desc
		limit #{pageNum},#{pageSize}
	</select>

	<!-- 以企业为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseDayStatCount" resultType="java.lang.Integer">
		SELECT
		count(0) from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
	</select>
	
	<!-- 以省为维度查询企业统计信息 -->
	<select id="queryEnterpriseDayStatByProvince" resultMap="enterpriseDayStatWrapper">
		select
		provinceID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		SUM(memberCount) AS memberCount,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		SUM(useCountMobile) AS useCountMobile,
		SUM(useCountUnicom) AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom
		from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, statDate, serviceType, subServType, chargeType
		order by statDate desc
		limit #{pageNum},#{pageSize}
	</select>
	
	<!-- 以省为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseDayStatCountByProvince" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	count(0)
		 from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, statDate, serviceType, subServType, chargeType) t
	</select>
	
	<!-- 以市为维度查询企业统计信息 -->
	<select id="queryEnterpriseDayStatByCity" resultMap="enterpriseDayStatWrapper">
		select
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		SUM(memberCount) AS memberCount,
		SUM(deliveryMemberCount) AS deliveryMemberCount,
		SUM(useCount) AS useCount,
		SUM(experienceCount) AS experienceCount,
		SUM(useCountMobile) AS useCountMobile,
		SUM(useCountUnicom) AS useCountUnicom,
		SUM(useCountTelecom) AS useCountTelecom
		from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, cityID, statDate, serviceType, subServType, chargeType
		order by statDate desc
		limit #{pageNum},#{pageSize}
	</select>
	
	<!-- 以市为维度查询企业统计信息条数 -->
	<select id="queryEnterpriseDayStatCountByCity" resultType="java.lang.Integer">
		select count(0)
		from 
		(select	count(0)
		 from ecpm_t_enterprise_day_stat
		<trim prefix="where" prefixOverrides="and|or">
			<if test="provinceIDs != null and provinceIDs.size()>0">
				and provinceID in
				<foreach item="provinceID" index="index" collection="provinceIDs"
					open="(" separator="," close=")">
					#{provinceID}
				</foreach>
			</if>
			<if test="cityIDs != null and cityIDs.size()>0">
				and cityID in
				<foreach item="cityID" index="index" collection="cityIDs"
					open="(" separator="," close=")">
					#{cityID}
				</foreach>
			</if>
			<if test="enterpriseID !=null ">
				and enterpriseID=#{enterpriseID}
			</if>
			<if test="parentEnterpriseID !=null ">
				and parentEnterpriseID=#{parentEnterpriseID}
			</if>
			<if test="enterpriseName !=null and enterpriseName !=''">
				and enterpriseName like concat("%", #{enterpriseName}, "%")
			</if>
			<if test="enterpriseType !=null and enterpriseType !=''">
				and enterpriseType=#{enterpriseType}
			</if>
			<if test="serviceType !=null ">
				and serviceType=#{serviceType}
			</if>
			<if test="startDate != null and endDate != null">
				and (statDate <![CDATA[ >= ]]> #{startDate} 
				and statDate <![CDATA[ <= ]]> #{endDate})
			</if>
			<if test="startDate != null and endDate == null">
				and statDate <![CDATA[ >= ]]> #{startDate}
			</if>
			<if test="startDate == null and endDate != null">
				and statDate <![CDATA[ <= ]]> #{endDate}
			</if>
			<if test="status != null">
				and status=#{status}
			</if>
		</trim>
		group by provinceID, cityID, statDate, serviceType, subServType, chargeType) t
	</select>
	
	<!-- 更新企业日统计使用量 -->
	<update id="updateEnterpriseDayStat">
		update ecpm_t_enterprise_day_stat
		set
		<if test="useCount != null">
			useCount=useCount + #{useCount},
		</if>
		<if test="updateTime != null">
			updateTime=#{updateTime}
		</if>
		where chargeType = 2 and
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType}  and statDate=#{statDate}
	</update>
	
	<select id="queryEnterpriseDayStatByDate" resultMap="enterpriseDayStatWrapper">
		select 
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		updateTime,
		status,
		useCountMobile,
		useCountUnicom,
		useCountTelecom
		from ecpm_t_enterprise_day_stat t1 
		where 
		<if test="enterpriseID !=null ">
			t1.enterpriseID=#{enterpriseID}
		</if>
		<if test="enterpriseIDs != null and enterpriseIDs.size()>0">
			t1.enterpriseID in
			<foreach item="content" index="index" collection="enterpriseIDs"
				open="(" separator="," close=")">
				#{content.enterpriseID}
			</foreach>
		</if>
		<if test="status !=null">
			and status=#{status}
		</if>
		and t1.serviceType=#{serviceType}
		<if test="subServType !=null">
		and t1.subServType=#{subServType}
		</if>
		and t1.chargeType=#{chargeType}
		and t1.statDate in 
		<foreach item="statDate" index="index" collection="dateList" open="("
			separator="," close=")">
			#{statDate}
		</foreach>
		<if test="serviceTypeList != null and serviceTypeList.size()>0">
		and t1.serviceType in
		<foreach item="serviceType" index="index" collection="serviceTypeList" open="("
			separator="," close=")">
			#{serviceType}
		</foreach>
		</if>
		<if test="subServTypeList != null and subServTypeList.size()>0">
		and t1.subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
		</if>
	</select>

	
	<select id="queryEnterpriseDayStatByEnterpriseID"
		resultMap="enterpriseDayStatWrapper">
		SELECT * from ecpm_t_enterprise_day_stat where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType}
		<if test="subServType !=null">
			and subServType=#{subServType}
		</if>
		<if test="statDate !=null">
			and statDate=#{statDate}
		</if>
	</select>
	
	<update id="updateEnterpriseDayStatByEnterpriseID">
		update ecpm_t_enterprise_day_stat
		set
		<trim suffixOverrides=",">
			<if test="useCount != null">
				useCount=useCount - #{useCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="enterpriseType !=null">
				enterpriseType=#{enterpriseType},
			</if>
			<if test="parentEnterpriseID !=null">
				parentEnterpriseID=#{parentEnterpriseID},
			</if>
			<if test="cityID !=null">
				cityID=#{cityID},
			</if>
			<if test="provinceID !=null">
				provinceID=#{provinceID}
			</if>
		</trim>
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType}
		<if test="subServType !=null">
			and subServType=#{subServType}
		</if>
		<if test="statDate !=null">
			and statDate=#{statDate}
		</if>
	</update>
	
	<insert id="insertEnterpriseDayStatByEnterpriseID">
		INSERT INTO ecpm_t_enterprise_day_stat
		(enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		updateTime,
		status
		)
		VALUES
		(
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseType},
		#{parentEnterpriseID},
		#{provinceID},
		#{cityID},
		#{statDate},
		#{serviceType},
		#{subServType},
		#{chargeType},
		#{memberCount},
		#{deliveryMemberCount},
		#{useCount},
		#{experienceCount},
		#{updateTime},
		#{status}
		)
	</insert>
	
	<update id="updateEnterpriseDayCountStat">
		update ecpm_t_enterprise_day_stat
		set
		<trim suffixOverrides="," suffix="where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and subServType=#{subServType} and chargeType=#{chargeType} and statDate=#{statDate}">
			<if test="memberCount != null">
				memberCount=#{memberCount},
			</if>
			<if test="deliveryMemberCount != null">
				deliveryMemberCount=#{deliveryMemberCount},
			</if>
			<if test="useCount != null">
				useCount=#{useCount},
			</if>
			<if test="useCountMobile != null">
				useCountMobile=#{useCountMobile},
			</if>
			<if test="useCountUnicom != null">
				useCountUnicom=#{useCountUnicom},
			</if>
			<if test="useCountTelecom != null">
				useCountTelecom=#{useCountTelecom},
			</if>
			<if test="experienceCount != null">
				experienceCount=#{experienceCount},
			</if>
			<if test="updateTime != null">
				updateTime=#{updateTime},
			</if>
			<if test="status != null">
				status=#{status},
			</if>
		</trim>
	</update>
	
	<!-- 批量更新 lwx595992-->
	<update id="updateEnterpriseDayCountStats">
		<foreach collection="list" item="item" index="index" open=""
			close="" separator=";">
			update ecpm_t_enterprise_day_stat
			set
			<trim suffixOverrides=","
				suffix="where enterpriseID=#{item.enterpriseID} and serviceType=#{item.serviceType} and subServType=#{item.subServType} and chargeType=#{item.chargeType} and statDate=#{item.statDate}">
				<if test="item.memberCount != null">
					memberCount=#{item.memberCount},
				</if>
				<if test="item.deliveryMemberCount != null">
					deliveryMemberCount=#{item.deliveryMemberCount},
				</if>
				<if test="item.useCount != null">
					useCount=#{item.useCount},
				</if>
				<if test="item.useCountMobile != null">
					useCountMobile=#{item.useCountMobile},
				</if>
				<if test="item.useCountUnicom != null">
					useCountUnicom=#{item.useCountUnicom},
				</if>
				<if test="item.useCountTelecom != null">
					useCountTelecom=#{item.useCountTelecom},
				</if>
				<if test="item.memberCount != null">
					memberCount=#{item.memberCount},
				</if>
				<if test="item.experienceCount != null">
					experienceCount=#{item.experienceCount},
				</if>
				<if test="item.updateTime != null">
					updateTime=#{item.updateTime},
				</if>
				<if test="item.status != null">
					status=#{item.status},
				</if>
			</trim>
		</foreach>
	</update>
	<!-- 批量插入 lwx595992 -->
	<insert id="insertEnterpriseDayCountStats">
		insert into
		ecpm_t_enterprise_day_stat
		(
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		useCountMobile,
		useCountUnicom,
		useCountTelecom,
		experienceCount,
		updateTime,
		status
		)
		values
		<foreach collection="list" item="wrapper" separator=",">
			(
			#{wrapper.enterpriseID},
			#{wrapper.enterpriseName},
			#{wrapper.enterpriseType},
			#{wrapper.parentEnterpriseID},
			#{wrapper.provinceID},
			#{wrapper.cityID},
			#{wrapper.statDate},
			#{wrapper.serviceType},
			#{wrapper.subServType},
			#{wrapper.chargeType},
			#{wrapper.memberCount},
			#{wrapper.deliveryMemberCount},
			#{wrapper.useCount},
			#{wrapper.useCountMobile},
			#{wrapper.useCountUnicom},
			#{wrapper.useCountTelecom},
			#{wrapper.experienceCount},
			#{wrapper.updateTime},
			#{wrapper.status}
			)
		</foreach>
	</insert>
	
	<insert id="insertEnterpriseDayCountStat">
		insert into
		ecpm_t_enterprise_day_stat
		(
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		updateTime,
		status,
		useCountMobile,
		useCountUnicom,
		useCountTelecom
		)
		values
		(
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseType},
		#{parentEnterpriseID},
		#{provinceID},
		#{cityID},
		#{statDate},
		#{serviceType},
		#{subServType},
		#{chargeType},
		#{memberCount},
		#{deliveryMemberCount},
		#{useCount},
		#{experienceCount},
		#{updateTime},
		#{status},
		#{useCountMobile},
		#{useCountUnicom},
		#{useCountTelecom}
		)
	</insert>
	
	<!-- 更新企业日统计使用量 -->
	<update id="updateEnterpriseDayStatByID">
		update ecpm_t_enterprise_day_stat
		set
		<if test="useCount != null">
			useCount=useCount + #{useCount},
		</if>
		<if test="updateTime != null">
			updateTime=#{updateTime}
		</if>
		where chargeType=#{chargeType}
		<if test="enterpriseIDList != null and enterpriseIDList.size()>0">
			and enterpriseID in
			<foreach item="enterpriseID" index="index" collection="enterpriseIDList"
				open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		and serviceType=#{serviceType} and subServType=#{subServType} and statDate=#{statDate}
	</update>
	
	<!--统计一个月的使用量和体验量  -->
	<select id="countExperienceAndUse" resultMap="enterpriseDayStatWrapper">
		select 
		sum(t1.useCount) useCount,sum(t1.useCountMobile) useCountMobile,sum(t1.useCountUnicom) useCountUnicom,sum(t1.useCountTelecom) useCountTelecom,
		sum(t1.experienceCount) experienceCount,t1.serviceType,t1.subServType,t1.enterpriseID,t1.chargeType
		from ecpm_t_enterprise_day_stat t1 
		where 
		t1.enterpriseID=#{enterpriseID}
		and t1.serviceType=#{serviceType}
		and t1.subServType=#{subServType}
		and t1.chargeType=#{chargeType}
		and t1.statDate in 
		<foreach item="statDate" index="index" collection="dateList" open="("
			separator="," close=")">
			#{statDate}
		</foreach>
		group by t1.enterpriseID,t1.chargeType,t1.serviceType,t1.subServType
	</select>
	
	<select id="queryEnterpriseDayStatForSum" resultMap="enterpriseDayStatWrapper">
	    select enterpriseid,sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount,sum(ifnull(useCount,0)) as useCount,sum(ifnull(useCountMobile,0)) as useCountMobile,
	    sum(ifnull(useCountUnicom,0)) as useCountUnicom,sum(ifnull(useCountTelecom,0)) as useCountTelecom,avg(ifnull(status,0)) status,
	    sum(ifnull(experienceCount,0)) as experienceCount
	    from ecpm_t_enterprise_day_stat 
		where
		enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType}
		and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
		<if test="statDate !=null">
			and statDate=#{statDate}
		</if>
		group by enterpriseid 
	</select>
		
	
	<delete id="deleteEnterpriseDayStatWrapper">
		delete from ecpm_t_enterprise_day_stat where enterpriseID=#{enterpriseID} and serviceType=#{serviceType} and chargeType=#{chargeType} and statDate=#{statDate}
		and subServType in 
		<foreach item="subServType" index="index" collection="subServTypeList" open="("
			separator="," close=")">
			#{subServType}
		</foreach>
	</delete>
	
	<select id="queryEnterpriseDayStatByDateForSp" resultMap="enterpriseDayStatWrapper">
		select 
		enterpriseID,
		enterpriseName,
		enterpriseType,
		parentEnterpriseID,
		provinceID,
		cityID,
		statDate,
		serviceType,
		subServType,
		chargeType,
		memberCount,
		deliveryMemberCount,
		useCount,
		experienceCount,
		updateTime,
		status
		from ecpm_t_enterprise_day_stat t1 
		where 
		<if test="enterpriseID !=null ">
			t1.enterpriseID=#{enterpriseID}
		</if>
		<if test="status !=null">
			and status=#{status}
		</if>
		and t1.serviceType=#{serviceType}
		and t1.chargeType=#{chargeType}
		and t1.statDate in 
		<foreach item="statDate" index="index" collection="dateList" open="("
			separator="," close=")">
			#{statDate}
		</foreach>
		and t1.subServType=#{subServType}
	</select>
	
	<delete id="deleteEnterpriseDayStatWrapperForBefore">
		delete from ecpm_t_enterprise_day_stat 
		where statDate=#{statDate} 
		and chargeType =#{chargeType}
		and enterpriseID in
		<foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="("
			separator="," close=")">
			#{enterpriseID}
		</foreach>
	</delete>
	
	<select id="queryEnterpriseDayStatForAgentSum" resultMap="enterpriseDayStatWrapper">
	    select sum(ifnull(deliveryMemberCount,0)) as deliveryMemberCount,sum(ifnull(useCount,0)) as useCount,sum(ifnull(useCountMobile,0)) as useCountMobile,
	    sum(ifnull(useCountUnicom,0)) as useCountUnicom,sum(ifnull(useCountTelecom,0)) as useCountTelecom,avg(ifnull(status,0)) status,
	    sum(ifnull(experienceCount,0)) as experienceCount
	    from ecpm_t_enterprise_day_stat where parentEnterpriseID = #{parentEnterpriseID} and serviceType=#{serviceType} and subServType =#{subServType} 
	    and chargeType=#{chargeType}
	    <if test="statDate !=null">
			and statDate=#{statDate}
		</if>
        group by parententerpriseid
	</select>
	
	<update id="updateEnterpriseName">
		update ecpm_t_enterprise_day_stat t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>
</mapper>