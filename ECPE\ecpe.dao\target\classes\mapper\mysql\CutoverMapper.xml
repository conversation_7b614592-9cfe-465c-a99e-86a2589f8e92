<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.CutoverMapper">
	<resultMap id="OrderModel"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.CutoverWrapper">
		<result property="enterpriseID" column="enterpriseID" />
		<result property="productID" column="productID" />
		<result property="orderItemCode" column="orderItemCode" />
		<result property="orderCode" column="orderCode" />
		<result property="productType" column="productType" />
		<result property="isExperience" column="isExperience" />
		<result property="servType" column="servType" />
		<result property="subServType" column="subServType" />
		<result property="isLimit" column="isLimit" />
		<result property="chargeType" column="chargeType" />
		<result property="amount" column="amount" />
		<result property="memberCount" column="memberCount" />
		<result property="maxAmountPerPerson" column="maxAmountPerPerson" />
		<result property="effictiveTime" column="effictiveTime" />
		<result property="expireTime" column="expireTime" />
		<result property="createTime" column="createTime" />
		<result property="operatorID" column="operatorID" />
		<result property="lastUpdateTime" column="lastupdatetime" />
	</resultMap>

	<select id="queryOrderInfo" resultMap="OrderModel">
		select t.enterpriseID,
			t2.ID as productID, t1.orderItemCode, t.orderCode,
			t2.productType, t2.isExperience, t2.servType, t2.subServType,
			t2.isLimit, t2.chargeType, t2.amount, t2.memberCount,
			t2.maxAmountPerPerson, t.effictiveTime, t.expireTime,
			t.createTime, t.operatorID, t.lastupdatetime
		 from ecpe_t_order t , ecpe_t_orderitem t1, ecpe_t_product t2
		where t.orderCode = t1.orderID
		  and t1.productID = t2.ID
		  and t.enterpriseID in
		<foreach collection="list" item="enterpriseID" index="index" open="(" separator="," close=")">  
			#{enterpriseID}
		</foreach>
		order by t2.isExperience desc, t.expireTime asc
	</select>

	<select id="querySubscribe" resultType="java.lang.Long">
		select ID
		from ecpe_t_subscribe
		where enterpriseID = #{enterpriseID}
		<if test="productID!=null">
			and productID = #{productID}
		</if>
		<if test="orderItemID !=null and orderItemID!=''">
			and orderItemID = #{orderItemID}
		</if>
		<if test="orderID !=null and orderID!=''">
			and orderID = #{orderID}
		</if>
		order by isExperience desc, expireTime asc
	</select>

	<update id="updateSubscribe">
		update ecpe_t_subscribe sub
		   set sub.status = #{status},
		   sub.productType = #{productType},
		   sub.isExperience = #{isExperience},
		   sub.servType = #{servType},
		   sub.subServType = #{subServType},
		   sub.isLimit = #{isLimit},
		   sub.chargeType = #{chargeType},
		   sub.amount = #{amount},
		   sub.actualUseAmount = #{actualUseAmount},
		   sub.memberCount = #{memberCount},
		   sub.maxAmountPerPerson = #{maxAmountPerPerson},
		   sub.effictiveTime = #{effictiveTime},
		   sub.expireTime = #{expireTime},
		   sub.lastupdatetime = #{lastupdatetime},
		   sub.createTime = #{createTime},
		   sub.operatorID = #{operatorID}
		 where sub.ID = #{subscribeID}
	</update>
	
	<insert id="createSubscribe">
		INSERT INTO
		ecpe_t_subscribe
		(id,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime)
		VALUES
		(next value for MYCATSEQ_ECPE_T_SUBSCRIBE,
		#{enterpriseID},
		#{productID},
		#{orderItemID},
		#{orderID},
		#{status},
		#{productType},
		#{isExperience},
		#{servType},
		#{subServType},
		#{isLimit},
		#{chargeType},
		#{amount},
		#{actualUseAmount},
		#{memberCount},
		#{actualUseMemberCount},
		#{maxAmountPerPerson},
		#{effictiveTime},
		#{expireTime},
		#{createTime},
		#{operatorID},
		#{lastUpdateTime}
		)
	</insert>
</mapper>



