<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
<link href="../../../../../css/reset.css" rel="stylesheet"/>
<link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript"
        src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
<!-- 引入菜单组件 -->
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<!--分页-->
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css"/>
<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<script type="text/javascript" src="intelligentCall.js"></script>
<link rel="stylesheet" type="text/css" href="../../../../../css/hotlineContentManage.css"/>

<style>
.form-horizontal .control-label {
	padding-top: 14px !important;
	padding-bottom: 6px;
	font-weight: normal;
	white-space: nowrap;
}
#filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }
.daterangepicker td, .daterangepicker th {
	width:auto;
}
.input-daterange {
	padding-top: 0px !important;
}
body,html{
	overflow: auto;
}
</style>

</head>
<body ng-app='myApp' ng-controller='intelligentCallController' ng-init="init()" class="body-min-width-new">
	<div class="cooperation-manage" style="overflow-x: scroll;">
		<div ng-if="loginRoleType=='agent'" class="cooperation-head">
	        <span class="frist-tab" ng-bind="'COMMON_SUBENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;
	        <span class="second-tab" ng-bind="'WLT_MANAGER'|translate"></span>
	    </div>
        <top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/expressageManage/hotline/intelligentCall"
            list-index="77" ng-if="loginRoleType=='agent'"></top:menu>
			<div class="cooperation-search">
				<form class="form-inline">
					<div class="form-group col-lg-3 col-xs-3  col-sm-3 col-md-3">
						<label for="phoneNum" style="padding-right:30px;" ng-bind="'COMMON_NUMBER'|translate"></label>
						<input type="text" autocomplete="off" class="form-control" id="phoneNum" 
						placeholder="{{'USER_PHONE_PLACEHOLDER'|translate}}" ng-model="phoneNum">
					</div>
					<div class="form-group col-lg-3 col-xs-3  col-sm-3 col-md-3">
						<label for="trackingNumInput" style="padding-right:30px;" ng-bind="'TRACKING_NUMBER'|translate"></label>
						<input type="text" autocomplete="off" class="form-control" id="trackingNumInput" 
						placeholder="{{'TRACKING_PLACEHOLDER'|translate}}"   ng-model="trackingNumInput">
					</div>
					<div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
						<label style="padding-right:30px;" ng-bind="'EXPECT_PLAY_TIME'|translate"></label>
						<div class="input-daterange input-group" id="datepicker">
							<input type="text" class="input-md form-control" autocomplete="off" id="planTimeStart" ng-keyup="searchOn()"/>
							<span class="input-group-addon" ng-bind="'TO'|translate"></span>
							<input type="text" class="input-md form-control" autocomplete="off" id="planTimeEnd" ng-keyup="searchOn()"/>
						</div>
					</div>
            		<div class="form-group col-lg-1 col-xs-1  col-sm-1 col-md-1">
						<button ng-click="queryWltIntelligentCall()" type="submit" class="btn search-btn" ng-disabled="initSel[0].search && initSel[1].search" ><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
				</form>
			</div>
			<div class="add-table">
		       <button type="submit" class="btn add-btn" ng-click="showCall('add')" style="position: relative;">
		           <icon class="add-iocn"></icon>
		           <span style="color:#705de1" ng-bind="'ADD_CALL'|translate"></span>
		       </button>
		       
		       <button type="submit" class="btn add-btn" ng-click="impotIntellgent()" style="position: relative;">
			   	   <icon class="add-iocn"></icon>
				   <span style="color:#705de1" ng-bind="'BLACKWHITE_INPUTALL'|translate"></span>
			   </button>
		    </div>
			
			<div style="margin-left: 20px;margin-bottom: 20px;">
		        <p style="font-size: 16px;font-weight: 500;" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></p>
		    </div>
		    
			<div class="coorPeration-table">
					<table class="table table-striped table-hover">
						<thead>
							<tr>
								<th style="width:9%" ng-bind="'USER_PHONE_NUMBER'|translate"></th>
								<th style="width:9%" ng-bind="'DELIVERY_PHONE_NUMBER'|translate"></th>
								<th style="width:9%" ng-bind="'TRACKING_NUMBER'|translate"></th>
								<th style="width:9%" ng-bind="'SEVICE_TYPE'|translate"></th>
								<th style="width:9%" ng-bind="'SCENEID'|translate"></th>
								<th style="width:9%" ng-bind="'ADDRESS_INFO'|translate"></th>
								<th style="width:9%" ng-bind="'TXT_CONTENT'|translate"></th>
								<th style="width:4%" ng-bind="'VOICE_TYPE'|translate"></th>
								<th style="width:4%" ng-bind="'BROADCAST_GENDER'|translate"></th>
								<th style="width:9%" ng-bind="'EXPECT_PLAY_TIME'|translate"></th>
								<th style="width:15%" ng-bind="'COMMON_OPERATE'|translate"></th>
							</tr>
						</thead>
						<tbody>
								<tr ng-repeat="item in WltIntelligentCallListData">
									<td><span title="{{getNumber(item.caller)}}">{{getNumber(item.caller)}}</span></td>
									<td><span title="{{getNumber(item.callee)}}">{{getNumber(item.callee)}}</span></td> 
									<td><span title="{{item.trackingNum}}">{{item.trackingNum}}</span></td>
									<td><span title="{{getServiceType(item.serviceType)}}">{{getServiceType(item.serviceType)}}</span></td>
									<td><span title="{{getSceneID(item.sceneID)}}">{{getSceneID(item.sceneID)}}</span></td>
									<td><span title="{{item.address}}">{{item.address}}</span></td>
									<td><span title="{{item.text}}">{{item.text}}</span></td>
									<td><span title="{{getLanguage(item.language)}}">{{getLanguage(item.language)}}</span></td>
									<td><span title="{{getGender(item.gender)}}">{{getGender(item.gender)}}</span></td>
									<td><span title="{{getTime(item.planTime)}}">{{getTime(item.planTime)}}</span></td>
									<td>
					                    <div class="handle">
					                       <ul>
					                           	<!-- 查看 -->
					                       	   <li class="query" ng-click="showCall('detail',item)">
													<icon class="query-icon"></icon>
													<span style="color:#705de1" ng-bind="'COMMON_WATCH'|translate"></span>
											   </li>
					                           <!-- 删除 -->
					                           <li class="delete" ng-click="deleteIntelligentCallPop(item)">
					                               <icon class="delete-icon"></icon>
					                               <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
					                           </li>
					                           <!-- 编辑 -->
					                           <li ng-show="item.showEdit == true" class="edit" 
					                            	ng-click="showCall('update',item)">
					                               <icon class="edit-icon"></icon>
					                               <span style="color:#705de1" ng-bind="'GROUP_EDIT'|translate"></span>
					                           </li>
					                       </ul>
					                   </div>
				                   </td>
								</tr>
								<tr ng-show="WltIntelligentCallListData.length<=0">
									<td style="text-align:center" colspan="10" ng-bind="'COMMON_NODATA'|translate"></td>
								</tr>
							</tbody>
					</table>
				</div>

				<div>
					<ptl-page tableId="0" change="queryWltIntelligentCall('justPage')"></ptl-page>
				</div>
		</div>
		
	<!-- 新增(编辑)热线内容弹窗 -->
		<div class="modal fade" id="addIntelligentCall" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
		     style="overflow: auto">
		    <div class="modal-dialog" role="document">
		        <div class="modal-content">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
		                        aria-hidden="true">&times;</span></button>
		                <h4 class="modal-title" id="myModalLabel" ng-bind="'ADD_INTELLIGENTCALL'|translate"></h4>
		            </div>
		            <div class="cooper-tab">
		                <form class="form-horizontal" name="myForm" novalidate>
		                    <div class="form-group serviceType" style="padding-top:34px">
		                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-3 control-label">
		                            <icon>*</icon><span>服务类型：</span>
		                        </label>
		                        <div class="col-lg-8 col-xs-8 col-sm-8">
		                            <li class="redio-li" ng-click="changeServiceType('voiceQuery')" style="width:150px;display: inline-block;"><span
		                                    class="check-btn redio-btn checked" style="vertical-align: middle;"></span>语音询问
		                            </li>
		                            <li class="redio-li" ng-click="changeServiceType('voiceNotify')" style="width:150px;display: inline-block;"><span
		                                    class="check-btn redio-btn" style="vertical-align: middle;"> </span>语音通知
		                            </li>
		                        </div>
		                    </div>
	                        <div class="form-group sceneID" ng-show="serviceType == 'voiceQuery'">
	                        	<label class="col-lg-2 col-xs-2  col-sm-2 col-md-3 control-label">
		                            <icon>*</icon><span>接入场景：</span>
		                        </label>
		                        <div class="col-lg-8 col-xs-8 col-sm-8">
		                            <li class="redio-li" ng-click="changeSceneID('1')" style="width:150px;display: inline-block;"><span
		                                    class="check-btn redio-btn checked" style="vertical-align: middle;"></span>对话性交互
		                            </li>
		                            <li class="redio-li" ng-click="changeSceneID('2')" style="width:150px;display: inline-block;"><span
		                                    class="check-btn redio-btn" style="vertical-align: middle;"> </span>类似USSD选择交互
		                            </li>
		                        </div>
	                        </div>
		                    <div class="form-group">
		                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-3 control-label">
		                            <icon>*</icon><span>{{label}}：</span>
		                        </label>
		                        <div class="col-lg-8 col-xs-8 col-sm-8 col-md-6">
		                          <textarea class="form-control" rows="8" style="margin: 0px; width: 300px; height: 98px;"
		                                  name="content" ng-class="{'border-red':!contentVali||isSensitive}"
		                                  placeholder="{{msg}}" ng-disabled="operate == 'detail'"
		                                  ng-model="context" ng-blur="sensitiveCheck()">
		                          </textarea>
			                    	<span style="color:red" ng-show="!contentVali||isSensitive">
			                            <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
			                                 align="absmiddle">
			                            <span ng-show='contentDesc'>{{contentDesc|translate}}</span>
			                            <span ng-show="serviceType == 'voiceQuery'&& myForm.content.$error.pattern" ng-bind="'ADDRESS_INPUTVAR'|translate"></span>
			                            <span ng-show='isSensitive'>{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
			                        </span>
		                        </div>
		                        <div class="col-lg-8 col-xs-8  col-sm-8 col-md-3" ng-show="sceneID == '1'">
		                            	<span style="color:#705de1;cursor: pointer;" ng-bind="'ADDRESS_TIP'|translate" ng-click="showAddressModal()"></span>
		                        </div>
		                        <div class="col-lg-8 col-xs-8  col-sm-8 col-md-3" ng-show="sceneID == '2'">
		                            	<span style="color:#705de1;cursor: pointer;" ng-bind="'ADDRESS_TIP'|translate" ng-click="showAddressModal()"></span>
		                        </div>
		                    </div>
		                    
		                    <div class="form-group">
		                    	<label class="col-lg-2 col-xs-2  col-sm-2 col-md-3 control-label">
		                            <icon>*</icon><span>语言类型：</span>
		                        </label>
		                        <div class="col-lg-8 col-xs-8 col-sm-8 col-md-6">
			                        <select class="form-control" ng-disabled="operate == 'detail'" name="language" ng-model="language">
										<option value="zh-CN" ng-selected="true">中文</option>
										<option value="en-US">英文</option>
									</select>
								</div>
		                    </div>
		                    <div class="form-group gender">
		                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-3 control-label">
		                            <icon>*</icon><span>语音播报性别：</span>
		                        </label>
		                        <div class="col-lg-8 col-xs-8 col-sm-8 col-md-8">
		                            <li class="redio-li" ng-click="changeGender('Male')" style="width:150px;display: inline-block;"><span
		                                    class="check-btn redio-btn checked" style="vertical-align: middle;"></span>男性
		                            </li>
		                            <li class="redio-li" ng-click="changeGender('Female')" style="width:150px;display: inline-block;"><span
		                                    class="check-btn redio-btn" style="vertical-align: middle;"> </span>女性
		                            </li>
		                        </div>
		                    </div>
		                    <!-- 物流单号 -->
		                   <div class="form-group">
			                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-3 control-label">
		                           <icon>*</icon><span ng-bind="'INPUT_TRACKING_NUMBER'|translate"></span></label>
								<div class="col-lg-8 col-xs-8 col-sm-8">
		                           <input type="text" class="form-control" style="width:300px;display: inline-block"
		                               ng-model="trackingNum" name="trackingNum"
		                               ng-disabled="operate == 'detail'"
		                               placeholder="{{'TRACKING_PLACEHOLDER'|translate}}" required
		                               pattern="^\w{1,128}$" />
		                           <span style="color:red;line-height: 34px;display: block;"
		                               ng-show="myForm.trackingNum.$dirty && myForm.trackingNum.$invalid">
		                               <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
		                                   align="absmiddle">
		                               <span ng-show="myForm.trackingNum.$error.required"
		                                   ng-bind="'TRACKINGNUM_MEMBMSISDNDESC'|translate"></span>
		                               <span ng-show="myForm.trackingNum.$error.pattern"
		                                   ng-bind="'TRACKINGNUM_MEMBMSISDNDESC'|translate"></span>
		                           </span>
		                       </div>
		                   </div>
		                    <div class="form-group">
			                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-3 control-label">
			                    		<icon>*</icon>
										<span>用户号码：</span>
								</label>
								<div class="col-lg-8 col-xs-8 col-sm-8">
									<input type="text" class="form-control" style="width:300px;display: inline-block"
		                                ng-model="caller" name="caller"
		                                ng-disabled="operate == 'detail'"
		                                placeholder="请输入固话号码或手机号码" required
		                                pattern="^[0-9]{1,14}$" />
		                            <span style="color:red;line-height: 34px;display: block;"
		                                ng-show="myForm.caller.$dirty && myForm.caller.$invalid">
		                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
		                                    align="absmiddle">
		                                <span ng-show="myForm.caller.$error.required"
		                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
		                                <span ng-show="myForm.caller.$error.pattern"
		                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
		                            </span>
								</div>
							</div>
							<div class="form-group">
			                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-3 control-label">
			                    		<icon>*</icon>
										<span>投递号码：</span>
								</label>
								<div class="col-lg-8 col-xs-8 col-sm-8">
									<input type="text" class="form-control" style="width:300px;display: inline-block"
		                                ng-model="callee" name="callee"
		                                ng-disabled="operate == 'detail'"
		                                placeholder="请输入固话号码或手机号码" required
		                                pattern="^[0-9]{1,14}$" />
		                            <span style="color:red;line-height: 34px;display: block;"
		                                ng-show="myForm.callee.$dirty && myForm.callee.$invalid">
		                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
		                                    align="absmiddle">
		                                <span ng-show="myForm.callee.$error.required"
		                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
		                                <span ng-show="myForm.callee.$error.pattern"
		                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
		                            </span>
								</div>
							</div>
							<!-- 预计下发时间 -->
							<div class="form-group" ng-show="operate != 'add' && planTimeFlag != ''">
			                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-3 control-label">
			                    		<icon>*</icon><span ng-bind="'EXPECT_PLAY_TIME'|translate"></span></label>
								</label>
								<div class="col-lg-8 col-xs-8 col-sm-8">
									<input type="text" class="form-control" style="width:300px;display: inline-block"
		                                ng-model="planTime" name="planTime"
		                                ng-disabled="operate == 'detail'"
		                                placeholder="请输入格式：YYYY/MM/DD HH24:MI" required
		                                pattern="^[0-9]{4}\/(0[1-9]|1[0-2])\/(0[1-9]|[1-2][0-9]|3[0-1])\s([0-1][0-9]|2[0-3])\:([0-5][0-9])$" />
		                            <span style="color:red;line-height: 34px;display: block;"
		                                ng-show="myForm.planTime.$dirty && myForm.planTime.$invalid">
		                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
		                                    align="absmiddle">
		                                <span ng-show="myForm.planTime.$error.required"
		                                    ng-bind="'PLANTIME_MEMBMSISDNDESC'|translate"></span>
		                                <span ng-show="myForm.planTime.$error.pattern"
		                                    ng-bind="'PLANTIME_MEMBMSISDNDESC'|translate"></span>
		                            </span>
								</div>
							</div>
		                </form>
		            </div>
		            <div class="modal-footer" ng-show="operate != 'detail'">
		                <button type="submit" ng-disabled="(!caller || !callee || !trackingNum || (operate == 'update'&&!planTime) || !context||!contentVali || isSensitive)"
		                        class="btn btn-primary search-btn" ng-bind="'PUBLIC_NUMBER_SUBMIT'|translate"
		                        ng-click="intelligentCall()"></button>
                        <button type="submit" class="btn btn-back" style="margin-left:55px" ng-click="reset()"
              					ng-bind="'COMMON_RESET'|translate"></button>
		            </div>
		        </div>
		    </div>
		</div>
		
		<!--导入名单弹出框-->
		<div class="modal fade" id="impotIntellgentPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_INPUTALL'|translate"></h4>
					</div>
					<div class="modal-body">
						<form class="form-horizontal">
							<div class="form-group" style="padding-bottom:0">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;" ng-bind="'COMMON_FILENAME'|translate"></label>
								<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
									<input type="text" class="form-control" ng-model="fileName" id="addGroupName" placeholder="{{'IMPORTXLSXTABLEFILE'|translate}}"
												 style="width: 100%;" ng-disabled="true" />
								</div>
								<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype" uploadifyid="uploadifyid"
									validate="isValidate" filesize="filesize" mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl"
									desc="uploadDesc" numlimit="numlimit" urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
								</cy:uploadifyfile>
							</div>
							<div style="color:#ff0000;margin: 0px 0 10px 100px;" ng-show="errorInfo!==''">
								<span class="uplodify-error-img"></span>
								<span ng-bind="errorInfo|translate"></span>
							</div>
							<div class="downloadRow col-sm-10" style="margin: 0 0 0 16px;">
								<a target="_blank" href="/qycy/ecpmp/assets/WltIntelligentCallTemplate.xlsx" class="downMod" style="margin-right: 40px;"
									 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
								<span style="color: #705de1 !important; font-size: 12px;" ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
							</div>
						</form>
					</div>
					<div class="modal-footer" style="text-align:center;padding: 30px">
						<button type="submit" class="btn btn-primary search-btn" ng-click="impotIntellgentCall()" ng-disabled="errorInfo!==''||fileUrl==''" ng-bind="'CONFIRMIMPORT'|translate"></button>
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="addOrgCancel" ng-bind="'COMMON_BACK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		
		<!--删除确认框-->
		<div class="modal fade bs-example-modal-sm" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{deleteTip|translate}}
						</p></div>
					</div>
					<div class="modal-footer" style="text-align: center">
						<button type="submit" class="btn btn-primary search-btn ng-binding" ng-click="submitWltIntelligentCall()"
										ng-bind="'COMMON_OK'|translate">
						</button>
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
										ng-bind="'COMMON_CANCLE'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		
		<!--进行语音外呼-->
		<div class="modal fade bs-example-modal-sm" id="callTipModal" tabindex="1" role="dialog" aria-labelledby="myModalLabel">
		    <div class="modal-dialog modal-sm" role="document">
		        <div class="modal-content">
		            <div class="modal-body">
		                <div class="text-center">
		                    <p style='font-size: 16px;color:#383838' ng-bind="'CALL_TIP'|translate">
		                    </p>
		                </div>
		            </div>
		        </div>
		    </div>
		</div>
		
		<!-- 话术 -->
		<div class="modal fade bs-example-modal-sm" id="addressModal" tabindex="0" role="dialog" aria-labelledby="myModalLabel">
		    <div class="modal-dialog modal-sm" role="document">
		        <div class="modal-content" style="width: 500px;left: -100px;">
		            <div class="modal-header">
		                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
		                        aria-hidden="true">&times;</span></button>
		                <h4 class="modal-title" id="myModalLabel" ng-bind="'ADDRESS_TIP'|translate"></h4>
		            </div>
		            <div class="modal-body">
		                <div>
		                    <p style='font-size: 14px;color:#383838' ng-show="sceneID == '1'">
		                    	您的快递已送达$address1，现在是否可以来领取呢？</br>客户回答：1.1可以。1.2不可以。1.3改时间在工作等</br>应答1.1：我们将会在$address1等你来领取+结束语</br>应答1.2：如您现在不方便领取，是否可以寄存在address2</br>应答1.3：你是需要更改配送时间吗</br>客户回答1.2：1.21表同意。1.22表否定或改位置</br>客户回答1.3:1.31表同意。1.32表否定（将应答1.2）</br>应答1.21：将您的快递放在丰巢快递柜+结束语</br>应答1.22：请您指定派送地址。</br>客户回答1.22:说位置（已为你更改配送位置，感谢您的接听，再见）</br>应答1.31：请你指定派送时间</br>客户回答1.31：说时间</br>应答1.31：你选择的配送时间为*，是否确认更改 </br>客户回答1.31 同意（感谢接听再见）不同意（回到请你指定派送时间）
		                    </p>
		                    <p style='font-size: 14px;color:#383838' ng-show="sceneID == '2'">
		                    	语音：您好，您的包裹将于今天派送，请选择派送地点：</br>1、{地址1}(望海路8号丰巢快递柜)；</br>2、{地址2}望海路12号见福便利店；</br>3、{地址3}望海路10号楼；</br>4、联系快递小哥。</br>用户：联系快递小哥 </br>语音：好的，请稍后，正在为您接通快递小哥。</br>转接。
		                    </p>
		                </div>
		            </div>
		        </div>
		    </div>
		</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
							</p>
						</div>
					</div>
					<div class="modal-footer" style="text-align:center">
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>

</body>
</html>