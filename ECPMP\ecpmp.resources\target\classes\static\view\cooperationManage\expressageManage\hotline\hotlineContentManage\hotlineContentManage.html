<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <title>热线内容管理</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <!-- 引入菜单组件 -->
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <!--分页-->
    <script type="text/javascript" src="../../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css"/>
    <script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
	<link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
    <script type="text/javascript" src="hotlineContentManage.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../css/hotlineContentManage.css"/>
    <style>
        .border-red {
            border: 1px solid red;
        }

        .dialog-690 {
            width: 696px;
        }

        .dialog-800 {
            width: 800px;
        }

        .dialog-900 {
            width: 900px;
        }

        .dialog-1000 {
            width: 1000px;
        }

        .delMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../../../assets/images/delete.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 136px;
            z-index:99;
        }
        .addMsinsdn {
            display: inline-block;
            width: 23px;
            height: 23px;
            background: url(../../../../../assets/images/add.png) no-repeat;
            vertical-align: middle;
            background-position: 0 0;
            position: absolute;
            bottom: 0;
            right: 106px;
            z-index:99;
        }
        .handle ul li icon.manage-icon {
            background-position: -126px 0;
        }

        .handle ul li icon.add-icon {
            background-position: -55px 0;
        }

        .handle ul li icon.import-icon {
            background-position: -90px 0;
        }

        .table th.adjustable-width {
            width: 25%;
        }

        #filePicker div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        #filePicker_ div:nth-child(2) {
            width: 100% !important;
            height: 100% !important;
        }

        .form-group div li {
            display: inline-block;
            margin-right: 10px;
            padding-right: 10px;
            cursor: pointer;
        }

        .form-group div li span {
            vertical-align: middle;
            margin-right: 4px;
        }

        /* media for adjustable search-table width  */
        @media (max-width: 1850px) {
            .table th.adjustable-width {
                width: 28%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1600px) {
            .table th.adjustable-width {
                width: 30%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1300px) {
            .table th.adjustable-width {
                width: 33%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        @media (max-width: 1100px) {
            .table th.adjustable-width {
                width: 42%;
            }

            .handle ul li {
                margin-right: 10px;
            }
        }

        .ng-dirty.ng-invalid {
            border-color: red;
        }

        .ng-dirty.invalid {
            border-color: red;
        }
    </style>
</head>
<body ng-app='myApp' ng-controller='hotlineContentController' ng-init="init()" class="body-min-width-new">
<div class="cooperation-manage" style="overflow-x: scroll;">
    <!-- 代理商自己登陆 -->
    <div ng-if="loginRoleType=='agent'" class="cooperation-head">
	        <span class="frist-tab" ng-bind="'COMMON_SUBENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;
	        <span class="second-tab" ng-bind="'WLT_MANAGER'|translate"></span>
	    </div>
    <top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/expressageManage/hotline/HotlineManagement"
            list-index="76" ng-if="loginRoleType=='agent'"></top:menu>
    <div class="cooperation-search">
        <form class="form-inline">
            <div class="form-group col-lg-3 col-xs-3  col-sm-3 col-md-3">
                <label style="padding-right:30px;" for="contentName"
                       ng-bind="'CONTENTAUDIT_HOTCYCONTENT'|translate"></label>
                <input ng-model='content' type="text" autocomplete="off" class="form-control" id="contentName"
                       placeholder="{{'HOTLINECONTENT_PLEASEINPUTCONTENTKEYWORDS'|translate}}">
            </div>
            <div class="form-group col-lg-4 col-xs-4  col-sm-4 col-md-4">
	            <label style="padding-right:30px;" ng-bind="'EXPECT_PLAY_TIME'|translate"></label>
				<div class="time" style="display: inline-block;">
					<div class="input-daterange input-group" id="datepicker">
						<input type="text" class="input-md form-control"  autocomplete="off" id="start" ng-keyup="searchOn()"/>
						<span class="input-group-addon" ng-bind="'TO'|translate"></span>
						<input type="text" class="input-md form-control" autocomplete="off" id="end" ng-keyup="searchOn()"/>
					</div>
				</div>
			</div>
            <div class="form-group col-lg-1 col-xs-1  col-sm-1 col-md-1">
                <button ng-click="queryHotlineContentInfoList()" type="submit" class="btn search-btn" ng-disabled="initSel.search">
                    <icon class="search-iocn"></icon>
                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                </button>
            </div>
        </form>
    </div>
    <div class="add-table">
        <button type="submit" class="btn add-btn" ng-click="addHotlineContent()" style="position: relative;">
            <icon class="add-iocn"></icon>
            <span style="color:#705de1" ng-bind="'COMMON_ADDCONTENT'|translate"></span>
        </button>
        
        <button type="submit" class="btn add-btn" ng-click="importCont()" style="position: relative;">
			<icon class="add-iocn"></icon>
			<span style="color:#705de1" ng-bind="'BLACKWHITE_INPUTALL'|translate"></span>
		</button>
    </div>

    <div style="margin-left: 20px;margin-bottom: 20px;">
        <p style="font-size: 16px;font-weight: 500;" ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></p>
    </div>
    <div class="coorPeration-table">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th style="width:10%" ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></th>
                <th style="width:10%" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>
                <th style="width:10%;" ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
                <th style="width:10%" ng-bind="'CONTENT_NAME'|translate"></th>
                <th style="width:15%" ng-bind="'EXPECT_PLAY_TIME'|translate"></th>
                <th class="adjustable-width" ng-bind="'COMMON_OPERATE'|translate"></th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="item in hotContentInfoListData">
                <td><span title="{{item.contentID}}">{{item.contentID}}</span></td>
                <td style="min-width: 100px"><span
                        title="{{hotlineStatusMap[item.approveStatus]}}">{{hotlineStatusMap[item.approveStatus]}}</span>
                </td>
                <!-- <td><span title="{{getApproveIdea(item)}}">{{getApproveIdea(item)}}</span></td> -->
                <td><span title="{{typeMap[item.subServType]}}">{{typeMap[item.subServType]}}</span></td>
                <td>
                    <span title="{{item.content}}">{{item.content}}</span>
                </td>
                <td><span title="{{getTime(item.planTime)}}">{{getTime(item.planTime)}}</span></td>
                <td>
                    <div class="handle">
                        <ul>
                        	<!--投递-->
                            <li ng-show="loginRoleType == 'agent' && item.approveStatus == '3' && item.status == '0' && item.planTime == null"
                                class="edit"
                                ng-click="delivery(item)">
                                <span style="color:#7360e2" ng-bind="'DELIVERY'|translate"></span>
                            </li>
                            <!-- 删除 -->
                            <li ng-show = "item.approveStatus != '2'" class="delete" ng-click="deleteHotlineContent(item)">
                                <icon class="delete-icon"></icon>
                                <span style="color:#705de1" ng-bind="'COMMON_DELETE'|translate"></span>
                            </li>
                            <!-- 编辑 -->
                            <li ng-show="item.showEdit" class="edit" 
                             	ng-click="updateHotlineContent(item)">
                                <icon class="edit-icon"></icon>
                                <span style="color:#705de1" ng-bind="'GROUP_EDIT'|translate"></span>
                            </li>
                            <!--暂停-->
                            <li ng-show="(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.approveStatus == '3' && (item.status == '0' || item.status == '1')"
                                class="edit"
                                ng-click="suspendHotlineContent(item,'1')">
                                <span style="color:#7360e2" ng-bind="'COMMON_SUSPEND'|translate"></span>
                            </li>
                            <!--恢复-->
                            <li ng-show="(loginRoleType == 'superrManager' || loginRoleType == 'normalMangager') && item.approveStatus == '3' && item.status == '3'"
                                class="edit"
                                ng-click="suspendHotlineContent(item,'0')">
                                <span style="color:#ff2549" ng-bind="'COMMON_RECOVERY'|translate"></span>
                            </li>
                            <!-- 号码管理 -->
                            <li class="edit" ng-click="manageHotLinePop(item)">
                                <icon class="manage-icon"></icon>
                                <span style="color:#705de1" ng-bind="'号码管理'|translate"></span>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
            <tr ng-show="hotContentInfoListData.length<=0">
                <td style="text-align:center" colspan="6" ng-bind="'COMMON_NODATA'|translate"></td>
            </tr>
            </tbody>
        </table>
    </div>

    <div>
        <ptl-page tableId="0" change="queryHotlineContentInfoList('justPage')"></ptl-page>
    </div>

</div>

<!-- 新增(编辑)热线内容弹窗 -->
<div class="modal fade" id="addHotlineContent" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
     style="overflow: auto">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'CONTENT_ADD'|translate"></h4>
            </div>
            <div class="cooper-tab">
                <form class="form-horizontal" name="myForm" novalidate>
                    <div class="form-group" style="padding-top:34px">
                        <label for="inputEmail3" class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></span>
                        </label>
                        <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                            <select id="select" class="form-control" ng-model="addHotlineContentInfo.subServType"
                                    ng-options="x.id as x.name for x in subServTypeChoise"
                                    ng-disabled="operate =='update'" ng-change="changeSubServerType()"></select>
                        </div>
                    </div>
                    <!--交互彩印内容-->
                    <div ng-show="addHotlineContentInfo.subServType == 7">
                        <div class="form-group">
                            <label for="inputPassword3" class="col-ussd-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                <icon>*</icon>
                                <span ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></span>
                            </label>
                            <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                    <textarea class="form-control" rows="6"
                                              style="margin: 0px; width: 267px; height: 98px;"
                                              name="colorContent" ng-class="{'border-red':!contentVali||isSensitive}"
                                              placeholder="{{'PLEASEINPUTCYCONTENT_ONETOFIFTHWORDS'|translate}}"
                                              ng-model="addHotlineContentInfo.content" ng-blur="sensitiveCheckUSSD(addHotlineContentInfo)">
                                      </textarea>

                                <span style="color:red" ng-show="!addHotlineContentInfo.contentVali||addHotlineContentInfo.isSensitive">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
                                        <span ng-show='addHotlineContentInfo.contentDesc'>{{addHotlineContentInfo.contentDesc|translate}}</span>
                                        <span
                                                ng-show='addHotlineContentInfo.isSensitive'>{{'CONTENT_DETECTION'|translate}}{{addHotlineContentInfo.sensitiveWordsStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
                                    </span>
                            </div>
                        </div>

                        <!-- 指令框-->
                    <div  style="position: relative;">
                        <span class="addMsinsdn" ng-click="addMsisdn()" ></span>
                        <span class="delMsinsdn" ng-click="delMsisdn()" ></span>
                        <div  ng-repeat="reply in addHotlineContentInfo.subContentList">
                            <div class="form-group" ng-if="reply.subContentType==1">
                                <label class="col-ussd-3 col-xs-3 col-sm-3 col-md-3 control-label">
                                    <icon>*</icon><span  >回复指令{{$index+1}}</span>
                                </label>
                                <div class="col-lg-8 col-xs-9 col-sm-8 col-md-8">
                                    <input type="text" class="form-control" style="display: inline-block;width:267px;"
                                           ng-model="reply.instruct"
                                           placeholder="{{'INSTRUCTION_TIPS'|translate}}"
                                           id="instruction" name="{{'instruction'+$index}}" required pattern="^[a-zA-Z0-9]{1,10}$$"/>
                                    <span style="color:red;line-height: 34px;display: block;" ng-show="myForm.{{'instruction'+$index}}.$dirty && myForm.{{'instruction'+$index}}.$invalid">
                                            <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
                                            <span ng-show="myForm.{{'instruction'+$index}}.$error.required" ng-bind="'INSTRUCTION_TIPS'|translate"></span>
							                <span ng-show="myForm.{{'instruction'+$index}}.$error.pattern" ng-bind="'INSTRUCTION_TIPS'|translate"></span>
                                        </span>
                                </div>
                            </div>
                            <!-- 指令回复内容框-->
                            <div class="form-group" ng-if="reply.subContentType==1">
                                <label class="col-ussd-3 col-xs-3 col-sm-3 col-md-3 control-label">
                                    <icon>*</icon><span>回复指令交互内容{{$index+1}}</span>
                                </label>
                                <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">

                                        <textarea class="form-control" rows="6" style="margin: 0px; width: 267px; height: 98px;"
                                                  ng-class="{'border-red':reply.replyvalid || reply.replyisensitive}"
                                                  placeholder="{{'PLEASEINPUTCYCONTENT_ONETOFIFTHWORDS'|translate}}"
                                                  ng-model="reply.content" ng-blur="replyCheckissen($index)" >
                                      	</textarea>
                                    <span ng-model="reply.replyvalid" style="color:red" ng-show="reply.replyvalid || reply.replyisensitive">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
                                        <span ng-show='reply.replyvalid'>{{replycontentDesc|translate}}</span>
                                        <span ng-model="reply.replyisensitive"
                                              ng-show="reply.replyisensitive != ''">{{'CONTENT_DETECTION'|translate}}{{reply.replyisensitive}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 是否投递语音 -->
                    <div class="form-group">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                             <icon>*</icon><span ng-bind="'IS_INTELLIGENTCALL'|translate"></span></label>
                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
                            <select class="form-control"
                                    name="isIntelligentCall"
                                    ng-model="isIntelligentCall"
                                    ng-options="x.name for x in isIntelligentCallMap"
                                    ng-change="changeIsIntelligentCall(isIntelligentCall)"
                            >
                            </select>
                        </div>
                    </div>
                 </div>
                    <!-- 地址信息 -->
                    <div class="form-group">
                            <label for="inputPassword3" class="col-ussd-3 col-xs-3  col-sm-3 col-md-3 control-label">
                                <icon>*</icon><span ng-bind="'CONTENTAUDIT_ADDRESS'|translate"></span>
                            </label>
                            <div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
                                    <textarea class="form-control" rows="6"
                                              style="margin: 0px; width: 267px; height: 98px;"
                                              name="address" 
                                              placeholder="{{'ADDRESS_ONETOFIFTHWORDS'|translate}}"
                                              ng-model="address" ng-blur="addressCheck(address)">
                                      </textarea>
                                      
			                          
                                      
                                      <span style="color:red" ng-show="!addressVali||addressSensitive">
                                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
                                        <span ng-show='addressDesc'>{{addressDesc|translate}}</span>
                                        <span
                                                ng-show='addressSensitive'>{{'CONTENT_DETECTION'|translate}}{{addressSensitiveStr}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
                                    </span>
                            </div>
                            <div class="col-lg-6 col-xs-6  col-sm-6 col-md-2">
                            	<span style="color:#705de1;cursor: pointer;" ng-bind="'ADDRESS_TIP'|translate" ng-click="showAddressModal()"></span>
                            </div>
                        </div>
                        
                    
                    <!-- 所属行业 -->
                    <div class="form-group industry">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ENTERPRISE_INDUSTRY'|translate"></span></label>
                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
                            <select class="form-control"
                                    name="industry1"
                                    required
                                    ng-model="selectedIndustry"
                                    ng-options="x.industryName for x in industryList"
                                    ng-change="changeIsSensitive(selectedIndustry)"
                            >
                                <option value="" ng-bind="" ng-show="true"></option>
                            </select>

                            <span style="color:red" class="uplodify-error-img"
                                  ng-show="selectedIndustryErrorInfo"></span>
                            <span style="color:red" class="redFont" ng-bind="selectedIndustryErrorInfo|translate"
                                  ng-show="selectedIndustryErrorInfo"></span>
                        </div>
                    </div>
                    
                    <!-- 物流单号 -->
                    <div class="form-group">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon><span ng-bind="'TRACKING_NUMBER'|translate"></span></label>
                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
                            <input type="text" class="form-control" style="width:300px;display: inline-block"
                                ng-model="trackingNum" name="trackingNum"
                                placeholder="{{'TRACKING_PLACEHOLDER'|translate}}" required
                                pattern="^\w{1,128}$" />
                            <span style="color:red;line-height: 34px;display: block;"
                                ng-show="myForm.trackingNum.$dirty && myForm.trackingNum.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                    align="absmiddle">
                                <span ng-show="myForm.trackingNum.$error.required"
                                    ng-bind="'TRACKINGNUM_MEMBMSISDNDESC'|translate"></span>
                                <span ng-show="myForm.trackingNum.$error.pattern"
                                    ng-bind="'TRACKINGNUM_MEMBMSISDNDESC'|translate"></span>
                            </span>
                        </div>
                    </div>
                    
                    <!-- 用户号码 -->
                    <div class="form-group">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon><span ng-bind="'USER_PHONE_NUMBER'|translate"></span></label>
                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
                            <input type="text" class="form-control" style="width:300px;display: inline-block"
                                ng-model="userNum" name="userNum"
                                ng-disabled="operate =='update'"
                                placeholder="{{'USER_PHONE_NUMBER_PLACEHOLDER'|translate}}" required
                                pattern="^[0-9]{1,14}$" />
                            <span style="color:red;line-height: 34px;display: block;"
                                ng-show="myForm.userNum.$dirty && myForm.userNum.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                    align="absmiddle">
                                <span ng-show="myForm.userNum.$error.required"
                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                                <span ng-show="myForm.userNum.$error.pattern"
                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                            </span>
                        </div>
                    </div>
                    
                    <!-- 投递号码 -->
                    <div class="form-group">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <icon>*</icon><span ng-bind="'DELIVERY_PHONE_NUMBER'|translate"></span></label>
                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
                            <input type="text" class="form-control" style="width:300px;display: inline-block"
                                ng-model="callee" name="callee"
                                placeholder="{{'DELIVERY_PHONE_PLACEHOLDER'|translate}}" required
                                pattern="^[0-9]{1,14}$" />
                            <span style="color:red;line-height: 34px;display: block;"
                                ng-show="myForm.callee.$dirty && myForm.callee.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                    align="absmiddle">
                                <span ng-show="myForm.callee.$error.required"
                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                                <span ng-show="myForm.callee.$error.pattern"
                                    ng-bind="'HOTLINE_MEMBMSISDNDESC'|translate"></span>
                            </span>
                        </div>
                    </div>
                    
                    <!-- 预计下发时间 -->
					<div class="form-group" ng-show="operate != 'add' && planTimeFlag != ''">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
	                    		<icon>*</icon><span ng-bind="'EXPECT_PLAY_TIME'|translate"></span></label>
						</label>
                        <div class="col-lg-7 col-xs-7  col-sm-7 col-md-7">
							<input type="text" class="form-control" style="width:300px;display: inline-block"
                                ng-model="planTime" name="planTime"
                                placeholder="请输入格式：YYYY/MM/DD HH24:MI" required
                                pattern="^[0-9]{4}\/(0[1-9]|1[0-2])\/(0[1-9]|[1-2][0-9]|3[0-1])\s([0-1][0-9]|2[0-3])\:([0-5][0-9])$" />
                            <span style="color:red;line-height: 34px;display: block;"
                                ng-show="myForm.planTime.$dirty && myForm.planTime.$invalid">
                                <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                    align="absmiddle">
                                <span ng-show="myForm.planTime.$error.required"
                                    ng-bind="'PLANTIME_MEMBMSISDNDESC'|translate"></span>
                                <span ng-show="myForm.planTime.$error.pattern"
                                    ng-bind="'PLANTIME_MEMBMSISDNDESC'|translate"></span>
                            </span>
						</div>
					</div>

                    <!-- 营业执照 -->
                    <div class="form-group industry" style="margin-bottom: 0px;" >
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label">
                            <span style="color:red"
                                  ng-bind="'*'|translate"
                                  ng-model="isSensitiveIndustry"
                                  ng-show="isSensitiveIndustry =='1'">
                            </span>
                            <span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span></label>
                        <div class="col-lg-9 col-xs-9  col-sm-9 col-md-9">
                            <cy:uploadify filelistid="fileList_" filepickerid="filePicker_" accepttype="accepttype_"
                                          uploadifyid="uploadifyid_1" validate="isValidate_" filesize="filesize_"
                                          mimetypes_="mimetypes_"
                                          formdata="uploadParam_" uploadurl="uploadurl_" desc="uploadDesc_"
                                          numlimit="numlimit_"
                                          urllist="urlList_" createthumbnail="isCreateThumbnail_" namelistid="nameList_"
                                          ng-if="showUpload">
                            </cy:uploadify>
                            <input class="form-control" name="businessLicenseURL_" ng-model="businessLicenseURL_"
                                   ng-required="isSensitiveIndustry=='1'" ng-hide="true">
                        </div>
                    </div>
                    <div class="form-group" style="margin-bottom: 0px;margin-left:50px;"
                         ng-show="businessLicenseURL_!=''">
                        <label class="col-lg-3 col-xs-3  col-sm-3 col-md-3 control-label"></label>
                        <button type="button" class="btn btn-primary search-btn" ng-click='showBusinessURL()'>预览
                        </button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" ng-disabled="(!addHotlineContentInfo.content||myForm.$invalid) && (addHotlineContentInfo.subServType == 7 &&
                 ((!addHotlineContentInfo.content||!addHotlineContentInfo.contentVali || addHotlineContentInfo.isSensitive)
                 ||(!address||!addressVali || addressSensitive)
                 ||(!callee || !myForm.callee.$error.required || !myForm.callee.$error.pattern)
                 ||(!userNum || !myForm.userNum.$error.required || !myForm.userNum.$error.pattern)
                 ||(!trackingNum || !myForm.trackingNum.$error.required || !myForm.trackingNum.$error.pattern)
                 ||(operate == 'update' && !planTime)
                 ||addHotlineContentInfo.subContentList.length==0
                 || (addHotlineContentInfo.subContentList[0]&&(!addHotlineContentInfo.subContentList[0].content||!addHotlineContentInfo.subContentList[0].instruct||addHotlineContentInfo.subContentList[0].replyvalid||addHotlineContentInfo.subContentList[0].replyisensitive || myForm.instruction0.$error.required||myForm.instruction0.$error.pattern))
                 || (addHotlineContentInfo.subContentList[1]&&(!addHotlineContentInfo.subContentList[1].content||!addHotlineContentInfo.subContentList[1].instruct||addHotlineContentInfo.subContentList[1].replyvalid||addHotlineContentInfo.subContentList[1].replyisensitive || myForm.instruction1.$error.required||myForm.instruction1.$error.pattern))
                 || (addHotlineContentInfo.subContentList[2]&&(!addHotlineContentInfo.subContentList[2].content||!addHotlineContentInfo.subContentList[2].instruct||addHotlineContentInfo.subContentList[2].replyvalid||addHotlineContentInfo.subContentList[2].replyisensitive || myForm.instruction2.$error.required||myForm.instruction2.$error.pattern))
                 || (flashSubContent.content&&(flashSubContent.isSensitive||flashSubContent.contentDesc))||(isSensitiveIndustry==1&&!businessLicenseURL_)||!selectedIndustry))"
                        class="btn btn-primary search-btn" ng-bind="'COMMON_SUBMIT'|translate"
                        ng-click="beforeCommit()"></button>
                <button ng-hide="true" type="submit" class="btn btn-back" data-dismiss="modal" aria-label="Close"
                        ng-click="goback()" id="addHotlineContentCancel" ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!--导入名单弹出框-->
<div class="modal fade" id="importContentPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_INPUTALL'|translate"></h4>
			</div>
			<div class="modal-body">
				<form class="form-horizontal">
					<div class="form-group" style="padding-bottom:0">
						<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label" style="padding-top: 7px;" ng-bind="'COMMON_FILENAME'|translate"></label>
						<div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
							<input type="text" class="form-control" ng-model="fileName" id="addGroupName" placeholder="{{'IMPORTXLSXTABLEFILE'|translate}}"
										 style="width: 100%;" ng-disabled="true" />
						</div>
						<cy:uploadifyfile filelistid="fileList" filepickerid="filePicker" accepttype="accepttype" uploadifyid="uploadifyid_2"
							validate="isValidate" filesize="filesize" mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl"
							desc="uploadDesc" numlimit="numlimit" urllist="urlList" createthumbnail="isCreateThumbnail" auto="auto" style="margin-left: 15px;float: left;">
						</cy:uploadifyfile>
					</div>
					<div style="color:#ff0000;margin: 0px 0 10px 100px;" ng-show="errorInfo!==''">
						<span class="uplodify-error-img"></span>
						<span ng-bind="errorInfo|translate"></span>
					</div>
					<div class="downloadRow col-sm-10" style="margin: 0 0 0 16px;">
						<a target="_blank" href="/qycy/ecpmp/assets/WltUssdTemplate.xlsx" class="downMod" style="margin-right: 40px;"
							 ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
						<span style="color: #705de1 !important; font-size: 12px;" ng-bind="'GROUP_IPTMBRTIP'|translate"></span>
					</div>
				</form>
			</div>
			<div class="modal-footer" style="text-align:center;padding: 30px">
				<button type="submit" class="btn btn-primary search-btn" ng-click="importContent()" ng-disabled="errorInfo!==''||fileUrl==''" ng-bind="'CONFIRMIMPORT'|translate"></button>
				<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="addOrgCancel" ng-bind="'COMMON_BACK'|translate"></button>
			</div>
		</div>
	</div>
</div>

<div class="modal fade bs-example-modal-sm" id="showBusinessURL" tabindex="-1" style="overflow: auto;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" ng-click="hideBusinessURL()"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">预览</h4>
            </div>
            <div class="modal-body">
                <div class="img-wrap" ng-repeat='item in urlList_2'>
                    <img ng-src="{{item}}" alt="">
                </div>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="button" class="btn " ng-click="hideBusinessURL()">确定</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除热线内容弹窗 -->
<div class="modal fade bs-example-modal-sm" id="deleteHotlineContent" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content" style="width:390px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_SUREDELETE'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838' ng-bind="'HOTLINE_SUREDELETEHOTLINE'|translate"></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_DELETE'|translate"
                        ng-click="delHotlineContent()"></button>
                <button id="deleteHotlineContentCancel" type="submit" class="btn " data-dismiss="modal"
                        aria-label="Close" id="addHotlineContentCancel" ng-bind="'NO'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!--号码导入弹出框-->
<div class="modal fade" id="impotHotLineNoPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'BLACKWHITE_INPUTALL'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal form-inline">
                    <div class="form-group" style="width: 596px;">
                        <div class="form-group" style="width: 596px;">
                            <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"
                                   style="padding-top: 7px;" ng-bind="'COMMON_FILENAME'|translate"></label>
                            <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6">
                                <input type="text" class="form-control" ng-model="fileName" id="addGroupName"
                                       placeholder="请导入.xlsx表格格式文件" style="width: 100%;" ng-disabled="true"/>
                                <!-- <button type="submit" class="btn btn-primary" ng-bind="'GROUP_IPTMBRFILE'|translate"></button> -->
                            </div>
                            <cy:uploadifyfile filelistid="fileList" filepickerid="filePicker"
                                              accepttype="accepttype" uploadifyid="uploadifyid" validate="isValidate"
                                              filesize="filesize" mimetypes="mimetypes" formdata="uploadParam"
                                              uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
                                              urllist="urlList"
                                              createthumbnail="isCreateThumbnail" auto="auto"
                                              style="margin-left: 15px;float: left;">
                            </cy:uploadifyfile>
                        </div>
                        <div ng-bind="desc" style="color: #c3c3c3;margin: 10px 0 0 92px;" ng-show="errorInfo==''">
                        </div>
                        <div style="color:#ff0000;margin: 10px 0 0 50px;" ng-show="errorInfo">
                            <span class="uplodify-error-img"></span>
                            <span ng-bind="errorInfo|translate"></span>
                        </div>
                        <div class="downloadRow col-sm-10" style="margin: 20px 0 0 29px;">
                            <a target="_blank" href="/qycy/ecpmp/assets/importHotLineRel.xlsx" class="downMod"
                               style="margin-right: 40px;" ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
                            <span style="color: #705de1 !important; font-size: 12px;">提示：</span><span
                                style="color: #705de1 !important; font-size: 12px;"
                                ng-bind="'TEMPLATE_IMPORTTIP'|translate"></span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn" ng-click="commitImportHotLineNo()"
                        ng-disabled="errorInfo!==''||fileUrl==''">确认导入
                </button>
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        id="addOrgCancel">返回
                </button>
            </div>
        </div>
    </div>
</div>

<!--号码新增弹出框-->
<div class="modal fade bs-example-modal-sm" id="addHotlinePop" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel">
    <div role="document" ng-class="{'modal-dialog':1==1,'dialog-690':pageInfo[1].totalPage<=3,
                    'dialog-800':pageInfo[1].totalPage>3 && pageInfo[1].totalPage<7,
                    'dialog-900':pageInfo[1].totalPage>=7 && pageInfo[1].totalPage<10,
                    'dialog-1000':pageInfo[1].totalPage>=10}">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'HOTLINE_ADD'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div cla ss="form-group">
                        <div class="row">
                            <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                   ng-bind="'HOTLINE_NUMBER'|translate"></label>
                            <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5">
                                <input type="text" class="form-control" id=""
                                       placeholder="{{'HOTLINE_PLEASEINPUTHOTLINENUMBER'|translate}}"
                                       ng-model="hotlineMsisdnAdd">
                            </div>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                <button class="btn bg_purple search-btn btn1"
                                        ng-click="queryHotLineList(selectedItem,'search')">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="add-table" style="margin-left:12px;margin-top:12px;">
                    <button class="btn" ng-disabled="!hasChoseAddHotLine"
                            style="width:105px; margin-left:10px;color:#7360e1" type="button"
                            ng-click="singleOrBatchAddHotLine('batch')"
                            ng-bind="'CONTENT_BATCH_ADD'|translate"></button>
                    <!-- <button class="btn" style="width:105px;color:#7360e1" type="button" ng-click="exportMember()"
                                        ng-bind="'GROUP_MEMBEXPORT'|translate"></button> -->
                </div>

                <div class="" style="max-height: 530px;overflow: auto">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
                            <th style="padding-left:30px;width: 10%;"><input type="checkbox"
                                                                             ng-model="chooseAllAddHotLine"
                                                                             ng-click="selectAllHotLine()"></th>
                            <th style="padding-left:30px" ng-bind="'HOTLINE_NUMBER'|translate"></th>
                            <th style="padding-left:30px;" ng-bind="'COMMON_OPERATE'|translate"></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="item in hotlineList">
                            <td><input type="checkbox" ng-model="item.checked"></td>
                            <td><span title="{{item.hotlineNo}}">{{item.hotlineNo}}</span></td>
                            <td style="font-size: small;">
                                <a ng-click="singleOrBatchAddHotLine('single',item)"
                                   ng-bind="'COMMON_ADD'|translate"></a>
                            </td>
                        </tr>
                        <tr ng-show="hotlineList.length<=0">
                            <td style="text-align:center" colspan="3" ng-bind="'COMMON_NODATA'|translate"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div>
                    <ptl-page tableId="1" change="queryHotLineList(selectedItem,'justPage')"></ptl-page>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

<!--号码管理弹出框-->
<div class="modal fade" id="manageHotLinePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div role="document" ng-class="{'modal-dialog':1==1,'dialog-690':pageInfo[2].totalPage<=3,
                    'dialog-800':pageInfo[2].totalPage>3 && pageInfo[2].totalPage<7,
                    'dialog-900':pageInfo[2].totalPage>=7 && pageInfo[2].totalPage<10,
                    'dialog-1000':pageInfo[2].totalPage>=10}">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'HOTLINE_MANAGEMENT'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div cla ss="form-group">
                        <div class="row">
                            <label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label"
                                   ng-bind="'HOTLINE_NUMBER'|translate"></label>
                            <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5">
                                <input type="text" class="form-control" id=""
                                       placeholder="{{'HOTLINE_PLEASEINPUTHOTLINENUMBER'|translate}}"
                                       ng-model="hotlineMsisdnDel">
                            </div>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
                                <button class="btn bg_purple search-btn btn1"
                                        ng-click="queryContentRelObjectList(selectedItem,'search')">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="add-table" style="margin-left:12px;margin-top:12px;">
                    <button class="btn" ng-disabled="!hasChoseDelHotLine"
                            style="width:105px; margin-left:10px;color:#7360e1" type="button"
                            ng-click="sureDelHotLine('batch')" ng-bind="'GROUP_BATCHDELETE'|translate"></button>
                    <!-- <button class="btn" style="width:105px;color:#7360e1" type="button" ng-click="exportMember()"
                                        ng-bind="'GROUP_MEMBEXPORT'|translate"></button> -->
                </div>

                <div class="" style="max-height: 530px;overflow: auto">
                    <table class="table table-striped table-hover">
                        <thead>
                        <tr>
                            <th style="padding-left:30px;width: 10%;"><input type="checkbox"
                                                                             ng-model="chooseAllDelHotLine"
                                                                             ng-click="selectAllDelHotLine()"></th>
                            <th style="padding-left:30px" ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></th>
                            <th style="padding-left:30px" ng-bind="'HOTLINE_NUMBER'|translate"></th>
                            <th style="padding-left:30px;" ng-bind="'COMMON_OPERATE'|translate"></th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-repeat="item in contentBelongOrgList">
                            <td><input type="checkbox" ng-model="item.checked"></td>
                            <td><span title="{{selectedItem.content}}">{{selectedItem.content}}</span></td>
                            <td><span title="{{item.hotlineNo}}">{{item.hotlineNo}}</span></td>
                            <td style="font-size: small;">
                                <a ng-click="sureDelHotLine('single',item)"
                                   ng-bind="'COMMON_DELETE'|translate"></a>
                            </td>
                        </tr>
                        <tr ng-show="contentBelongOrgList.length<=0">
                            <td style="text-align:center" colspan="4" ng-bind="'COMMON_NODATA'|translate"></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div>
                    <ptl-page tableId="2" change="queryContentRelObjectList(selectedItem,'justPage')"></ptl-page>
                </div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

<!--热线号码管理确认删除框弹出框-->
<div class="modal fade" id="deleteHotLinePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <div class="form-group">
                        <div class="row"
                             style="width: 517px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
                            <div class="text-center">
                                <span ng-bind="'COMMON_DEL_SURE'|translate"></span>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-click="singleOrBatchDelHotLine(singleOrBatch)" ng-bind="'COMMON_OK'|translate"></button>
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" id="delMemCancel"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>
</div>


<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>
<!-- 话术 -->
<div class="modal fade bs-example-modal-sm" id="addressModal" tabindex="0" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content" style="width: 500px;left: -100px;">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'ADDRESS_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div>
                    <p style='font-size: 14px;color:#383838'>
                    	您的快递已送达$address1，现在是否可以来领取呢？</br>客户回答：1.1可以。1.2不可以。1.3改时间在工作等</br>应答1.1：我们将会在$address1等你来领取+结束语</br>应答1.2：如您现在不方便领取，是否可以寄存在address2</br>应答1.3：你是需要更改配送时间吗</br>客户回答1.2：1.21表同意。1.22表否定或改位置</br>客户回答1.3:1.31表同意。1.32表否定（将应答1.2）</br>应答1.21：将您的快递放在丰巢快递柜+结束语</br>应答1.22：请您指定派送地址。</br>客户回答1.22:说位置（已为你更改配送位置，感谢您的接听，再见）</br>应答1.31：请你指定派送时间</br>客户回答1.31：说时间</br>应答1.31：你选择的配送时间为*，是否确认更改 </br>客户回答1.31 同意（感谢接听再见）不同意（回到请你指定派送时间）
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

</html>