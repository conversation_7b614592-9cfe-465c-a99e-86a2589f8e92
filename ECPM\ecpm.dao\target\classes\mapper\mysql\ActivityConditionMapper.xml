<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityConditionMapper">
	<resultMap id="activityConditionWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivityConditionWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="activityID" column="activityID" javaType="java.lang.Integer" />
		<result property="conditionKey" column="conditionKey" javaType="java.lang.String" />
		<result property="conditionValue" column="conditionValue"
			javaType="java.lang.String" />
		<result property="conditionOperator" column="conditionOperator"
			javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.String" />
	</resultMap>

	<insert id="createActivityCondition">
		insert into ecpm_t_activity_condition
		(
		activityID,
		conditionKey,
		conditionValue,
		conditionOperator,
		createTime,
		updateTime,
		operatorID
		)
		values
		<foreach collection="list" item="activityConditionWrapper"
			separator=",">
			(
			#{activityConditionWrapper.activityID},
			#{activityConditionWrapper.conditionKey},
			#{activityConditionWrapper.conditionValue},
			#{activityConditionWrapper.conditionOperator},
			#{activityConditionWrapper.createTime},
			#{activityConditionWrapper.updateTime},
			#{activityConditionWrapper.operatorID}
			)
		</foreach>
	</insert>

	<select id="getActivityCondition" resultMap="activityConditionWrapper">
		select t.activityID
		, t.conditionKey ,t.conditionValue from ecpm_t_activity_condition t
		<trim prefix="where" prefixOverrides="and|or">
			t.activityID in
			<foreach item="activityID" index="index" collection="activityIDList"
				open="(" separator="," close=")">
				#{activityID}
			</foreach>
		</trim>
	</select>

	<delete id="deleteConditionByActivityID">
		delete from ecpm_t_activity_condition where
		activityID = #{activityID}
	</delete>
</mapper>