angular.module('myNav', ["util.ajax", "angularI18n"]).controller('navCtrl', function ($scope,$document, $rootScope, RestClientUtil, T) {
  var iframe = document.getElementById("iframe_w");
  $scope.accountName = $.cookie("accountName");
  $scope.isGroupRemind = $.cookie("isGroupRemind");
  $scope.navlist = [];
  $scope.sequence = function (a, b) {
    if (a.displayNo > b.displayNo) {
      return 1;
    } else if (a.displayNo < b.displayNo) {
      return -1
    } else {
      return 0;
    }
  };
  if ($scope.isGroupRemind==='true'){
      $(".container .main_left .logo img").attr("src","../../assets/images/cylogo2.png")

      $(".faviconIco").attr("href","../../assets/images/favicon2.ico");
      $("title").html("数智反诈管理平台")

  }
  /*日期时间格式化*/
  var dateTimeFormate=function (date) {
    if (!date) {
      return
    } else {
      var obj = {};
      var d = new Date(date);
      obj.year = d.getFullYear();
      obj.month = ('0' + (d.getMonth() + 1)).slice(-2);
      obj.day = ('0' + (d.getDate())).slice(-2);
      obj.hour = ('0' + (d.getHours())).slice(-2);
      obj.minutes = ('0' + (d.getMinutes())).slice(-2);
      obj.seconds = ('0' + (d.getSeconds())).slice(-2);
      return obj
    }
  };
  /*获取服务器时间*/
  var getServerTime=function () {
    var gmt_time = $.ajax({async: false, cache: false}).getResponseHeader("Date");
    var local_time = new Date(gmt_time);
    return dateTimeFormate(local_time);
  };
//
//  $scope.id = JSON.parse($.cookie("enterpriseID"));
//  $scope.queryEnterpriseDetails($scope);
  var loginRoleType=$.cookie('loginRoleType');
  $scope.isAgent = (loginRoleType=='agent');
  $scope.isProvincial = (loginRoleType=='provincial');
  $scope.isSubEnterpirse = (loginRoleType=='subEnterprise');
  if($scope.isSubEnterpirse){
      //子企业账号默认进入分组管理
      iframe.src="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList/groupList.html";
  }
  $scope.isDisplay = false;
  // 获取内容管理配置提示
  $scope.getContentHint = function () {
  var req = {"pageContent": "pageContent"};
  RestClientUtil.ajaxRequest({
    type: 'POST',
    url: "/ecpmp/page/content/hint",
    data: JSON.stringify(req),
    success: function (data) {
      $rootScope.$apply(function () {
        if (data.result.resultCode === "**********") {
          $scope.hintContent = data.contentHintResponses;
          localStorage.setItem('hintContent', JSON.stringify($scope.hintContent));
        }
      })
    }
  })
}
 // 获取代理商单点登录配置接口
  $scope.agentSsoFlag = true; // 默认单点登录状态-隐藏logo等
  $scope.getAgentSSOConfig = function () {
    const enterpriseId = $.cookie("enterpriseID") || "";
    if(enterpriseId && ($scope.isAgent || $scope.isSubEnterpirse)) { // 代理商或子企业
        const req = {
               enterpriseId
            };
      RestClientUtil.ajaxRequest({
           type: 'POST',
           url: "/ecpmp/ecpmpServices/enterpriseManageService/singleSignOnInfo",
           data: JSON.stringify(req),
           timeout: 10000, // 10秒超时
           success: function (data) {
             $rootScope.$apply(function () {
               // 先检查数据是否有效
               if (!data || !data.result) { // 无效
                  $scope.agentSsoFlag = false;
                  return;
               }
               if (data.result.resultCode === "**********") { // 成功
                    const agentInfo = data.agentEnterpriseSingleSignInfoEcpmp;
                    if(agentInfo && agentInfo.agentEnterpriseId){ // 有数据
                       $scope.agentSsoFlag = true;
                    }else{ // 没有数据
                       $scope.agentSsoFlag = false;
                    }
               } else { // 失败
                 $scope.agentSsoFlag = false;
               }
             })
          },
           complete: function(xhr, status) {
              if (status === 'timeout') { // 超时
                $rootScope.$apply(function () {
                  $scope.agentSsoFlag = false;
                });
              }
           },
           error: function () {
            $rootScope.$apply(function () { // 错误
              $scope.agentSsoFlag = false; // 添加此行确保错误时展示logo
            })
           }
     })
    } else { // 非代理商或子企业
      $scope.agentSsoFlag = false;
    }
}
  var req = {
    accountID: $.cookie("accountID"),
    menuIds: []
  };
    //菜单 名片彩印 热线彩印 热线彩印省份版 菜单code
    let serviceMenu = ["w30003","w30004","w30010","w30011"];
    serviceMenu.remove = function(val) {
        var index = this.indexOf(val);
        if (index > -1) {
            this.splice(index, 1);
        }
    };
  RestClientUtil.ajaxRequest({
    type: 'POST',
    url: "/ecpmp/ecpmpServices/enterpriseManageService/queryMenu",
    data: JSON.stringify(req),
    success: function (data) {
      $rootScope.$apply(function () {
        if (data.result.resultCode === "**********") {
          $scope.navlist = data.menuList;
          $scope.navlist.sort($scope.sequence);
          angular.forEach($scope.navlist, function (item) {
            item.currsor = false;
          })
          $scope.getContentHint();
          $scope.getAgentSSOConfig();
        } else {
          $scope.tip = "菜单查询失败";
          $("#menuModal").modal();
        }
      })
    },
    error: function () {
      $rootScope.$apply(function () {
        $scope.tip = "菜单查询失败";
        $("#menuModal").modal();
      })
    }
  })
  if ($scope.isAgent && $scope.isDisplay)
  {
	  var req = {
			  	enterpriseID: $.cookie("enterpriseID")
			  };
			  RestClientUtil.ajaxRequest({
			    type: 'POST',
			    url: "/ecpmp/ecpmpServices/enterpriseManageService/queryBalance",
			    data: JSON.stringify(req),
			    success: function (data) {
			      $rootScope.$apply(function () {
			        if (data.result.resultCode === "**********") {
			          $scope.realBalance = data.realBalance;
			        } else {
			          $scope.tip = "余额查询失败";
			          $("#menuModal").modal();
			        }
			      })
			    },
			    error: function () {
			      $rootScope.$apply(function () {
			        $scope.tip = "余额查询失败";
			        $("#menuModal").modal();
			      })
			    }
			  })
  }
    $scope.querySyncServiceRuleList = function () {
        var req = {
            "enterpriseID": JSON.parse($.cookie("enterpriseID"))


        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceRuleList",
            data: JSON.stringify(req),
            async:false,
            success: function (result) {
                $scope.serviceControl = result.serviceControl || {};
                $.cookie('reserved4', $scope.serviceControl.reserved4, { path: '/', secure: false });

            }
        })
    }
    /*查询企业详情 */
    $scope.queryEnterpriseDetails = function () {
        var id = JSON.parse($.cookie("enterpriseID"))
        if (id == null) {
            return;
        }
        var req = {};
        req.id = id;
        var pageParameter = {};
        pageParameter.pageNum = 1;
        pageParameter.pageSize = 1000;
        pageParameter.isReturnTotal = 1;
        req.pageParameter = angular.copy(pageParameter);
        /*查询企业列表*/
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.enterpriseInfo = data.enterprise;

                        $.cookie('provinceID', data.enterprise.provinceID, { path: '/', secure: false });
                        $.cookie('cityID', data.enterprise.cityID, { path: '/', secure: false });
                        $.cookie('enterpriseType', data.enterprise.enterpriseType, { path: '/', secure: false });
                        $.cookie('enterpriseName', data.enterprise.enterpriseName, {path: '/', secure: false});

                        if(data.enterprise != null && data.enterprise != undefined
                            && data.enterprise.reservedsEcpmp != null && data.enterprise.reservedsEcpmp != undefined)
                        {
                            $.cookie('reserved10', data.enterprise.reservedsEcpmp.reserved10, { path: '/', secure: false });

                        }


//            $scope.setEnterpriseDetails($scope);
                    }else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error:function(){
                $rootScope.$apply(function(){
                        $scope.tip='**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };
  if ($scope.isProvincial)
  {
      //查询业务开关
      $scope.querySyncServiceRuleList()
      $scope.queryEnterpriseDetails();



	  var serverTime = getServerTime();
	    $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
	    //下拉框(投递方式)
	    $scope.subServTypeChoise = [];
	    var req = {
	      "enterpriseID": $.cookie("enterpriseID")
	    };
	    RestClientUtil.ajaxRequest({
	      type: 'POST',
	      url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
	      data: JSON.stringify(req),
	      success: function (result) {
	        $rootScope.$apply(function () {
	        console.log(result)
	          var data = result.result;
	          if (data.resultCode == '**********') {
	            $scope.subServerTypeList = result.syncServiceRuleList || [];
	            var serverTypeMap = new Map();
	            var proSupType = "[0";
	            for (var i in $scope.subServerTypeList) {
	              var item = $scope.subServerTypeList[i];
	              if ((item.status == 1 || item.status == 3) && $scope.nowTime <= item.expiryTime && $scope.nowTime >= item.effectiveTime)
	              {
		              proSupType = proSupType + "," + item.servType;

                      if (item.servType == 1)
                      {
                          serviceMenu.remove("w30003");
                      }
                      else if (item.servType == 5)
                      {
                          serviceMenu.remove("w30010");
                      }
                      else if (item.servType == 2)
                      {
                          setTimeout(function (){
                              //中移政企额外判断开关reserve4
                              if($.cookie("reserved10") == "111"){
                                  return;
                              }
                              serviceMenu.remove("w30004");
                              serviceMenu.remove("w30011");
                          },800)

                      }
	              }
	            }
	            proSupType = proSupType + "]";
	            $.cookie("proSupType",proSupType,{path:'/'});

	          } else {
	            $scope.subServerTypeList = [];
	            $scope.tip = data.resultCode;
	            $('#myModal').modal();
	          }
	        })
	      },
	      error: function () {
	        $rootScope.$apply(function () {
	          $scope.tip = '**********';
	          $('#myModal').modal();
	        })
	      }
	    });
  }

  //点击一级菜单展示二级菜单
  $scope.navSelect = function (item, lists) {
    $scope.navlist2 = [];
    for (var i in lists) {
      if (lists[i] != item) {
        lists[i].currsor = false;
      }
    }
    item.currsor = !item.currsor;
    var req = {
      accountID: $.cookie("accountID"),
      menuIds: []
    };
    req.menuIds = [item.id];
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryMenu",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          if (data.result.resultCode === "**********") {
            $scope.navlist2 = data.menuList;
              if ($scope.isProvincial)
              {
                  let menuList = [];
                  for (let i = 0 ;i<data.menuList.length;i++){
                      let menu = data.menuList[i];
                      if(serviceMenu.indexOf(menu.menuCode)>=0){
                          continue;
                      }
                      if($.cookie("reserved10") == "111"&&  "w30010" === menu.menuCode){
                          menu.menuName = "热线彩印";
                      }

                      menuList.push(menu);
                  }
                  $scope.navlist2 = menuList;
              }
              if(loginRoleType=='agent' || loginRoleType=='zhike' || loginRoleType=='provincial'){
                  if(item.menuName === '审核查询'||item.menuName === '信息统计'||item.menuName === '审核管理'||item.menuName === '统计查询'){
                      $scope.querySubscribeList();
                  }
              }
            $scope.navlist2.sort($scope.sequence);
          } else {
            $scope.tip = "菜单查询失败";
            $("#menuModal").modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = "菜单查询失败";
          $("#menuModal").modal();
        })
      }
    });
    $scope.queryEnterpriseDetails();


  };


  $scope.querySubscribeList = function () {
        var id = JSON.parse($.cookie("enterpriseID"))
        if (id == null) {
            return;
        }

        if(loginRoleType=='agent'|| loginRoleType=='zhike'){
            var req = {};
            var subscribeInfo = {};
            subscribeInfo.enterpriseID = id;
            req.subscribeInfo = angular.copy(subscribeInfo);
            var pageParameter = {};
            pageParameter.pageNum = 1;
            pageParameter.pageSize = 1000;
            pageParameter.isReturnTotal = 1;
            req.pageParameter = angular.copy(pageParameter);
            /*查询企业列表*/
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
                data: JSON.stringify(req),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '**********') {
                            $scope.subscribeInfoList = data.subscribeInfoList;
                            if(data.subscribeInfoList == null || data.subscribeInfoList == undefined){
                                if(loginRoleType=='agent'){
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '热线投递明细'){
                                            return true;
                                        }
                                    });
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '名片投递明细'){
                                            return true;
                                        }
                                    });
                                }
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '活动统计'){
                                            return true;
                                        }
                                    });
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '广告彩印审核记录'){
                                            return true;
                                        }
                                    });
                            }else{
                                var isHideHotline = 0;
                                var isHideBusinessCard = 0;
                                var isAuditRecords = 0;
                                for (var subscribeInfo of $scope.subscribeInfoList) {//遍历array集合
                                    if (isHideHotline === 0) {
                                        if(subscribeInfo.servType === 2){
                                            isHideHotline = 1;
                                        }
                                    }
                                    if( isHideBusinessCard === 0 ){
                                        if(subscribeInfo.servType === 1){
                                            isHideBusinessCard = 1;
                                        }
                                        if(subscribeInfo.servType === 3){
                                            isHideBusinessCard = 1;
                                            isAuditRecords = 1;
                                        }
                                    }
                                }
                                if(loginRoleType=='agent'){
                                    if(isHideHotline === 0){
                                        $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                            if(item.menuName != '热线投递明细'){
                                                return true;
                                            }
                                        });
                                    }
                                    if(isHideBusinessCard === 0){
                                        $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                            if(item.menuName != '名片投递明细'){
                                                return true;
                                            }
                                        });
                                    }
                                }
                                if(isAuditRecords === 0){
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '广告彩印审核记录'){
                                            return true;
                                        }
                                    });
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '活动统计'){
                                            return true;
                                        }
                                    });
                                }
                            }
                        }else {
                            $scope.tip = result.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error:function(){
                    $rootScope.$apply(function(){
                            $scope.tip='**********';
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }
        if(loginRoleType=='provincial'){
            var req = {};
            req.enterpriseID = id;
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
                data: JSON.stringify(req),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '**********') {
                            $scope.syncServiceRuleList = data.syncServiceRuleList;
                            if(data.syncServiceRuleList == null || data.syncServiceRuleList == undefined){
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '热线投递明细'){
                                            return true;
                                        }
                                    });
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '名片/热线省份版统计明细'){
                                            return true;
                                        }
                                    });
                            }else{
                                var isHideHotline = 0;
                                var isHideBusinessCard = 0;
                                for (var SyncServiceRuleEcpmp of $scope.syncServiceRuleList) {//遍历array集合
                                    if (isHideHotline === 0) {
                                        if(SyncServiceRuleEcpmp.servType === 2){
                                            isHideHotline = 1;
                                        }
                                    }
                                    if( isHideBusinessCard === 0 ){
                                        if(SyncServiceRuleEcpmp.servType === 1 || SyncServiceRuleEcpmp.servType === 3 || SyncServiceRuleEcpmp.servType === 5){
                                            isHideBusinessCard = 1;
                                        }
                                    }
                                }
                                if(isHideHotline === 0){
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '热线投递明细'){
                                            return true;
                                        }
                                    });
                                }
                                if(isHideBusinessCard === 0){
                                    $scope.navlist2 = $scope.navlist2.filter(function (item) {
                                        if(item.menuName != '名片/热线省份版统计明细'){
                                            return true;
                                        }
                                    });
                                }
                            }
                        }else {
                            $scope.tip = result.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error:function(){
                    $rootScope.$apply(function(){
                            $scope.tip='**********';
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }
    };

  $scope.accountManageInfo = function () {
    iframe.src = "../login/accountManage/accountManage.html?oldSrc=" + $scope.oldSrc;
  };

  //点击二级菜单展示右侧内容
  $scope.subNavSelect = function (item, lists) {
       sessionStorage.clear();
    for (var i in lists) {
      if (lists[i] != item) lists[i].currsor = false
    }
    document.title = item.menuName;
    item.currsor = true;
    $scope.oldSrc = item.menuURL;
    iframe.src = item.menuURL;
  };

  //登出
  $scope.logout = function () {
    var token = $.cookie('token');
    if (token === null) {
      $.removeCookie('accountID', { path: '/'});
      $.removeCookie('enterpriseID', { path: '/'});
      $.removeCookie('enterpriseName', { path: '/'});
      $.removeCookie('token', { path: '/'});
      $.removeCookie('isGroupRemind', { path: '/'});

      top.location.href = "/qycy/ecpmp/view/login/login.html"
      + ($scope.isGroupRemind==='true'?("?isGroupRemind=" + $scope.isGroupRemind):"");
      return;
    }
    var req = {
      "token": token,
      "accountID": $.cookie("accountID")
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/loginService/logOut",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          if (data.result.resultCode === "**********") {
            $.removeCookie('accountID', { path: '/'});
            $.removeCookie('enterpriseID', { path: '/'});
            $.removeCookie('enterpriseName', { path: '/'});
            $.removeCookie('token', { path: '/'});
            $.removeCookie('enterpriseType', { path: '/'});
            $.removeCookie('isGroupRemind', { path: '/'});
            if(data.logoutRedirtUrl){
                top.location.href = data.logoutRedirtUrl;
                return;
            }
            top.location.href = "../login/login.html"
            + ($scope.isGroupRemind==='true'?("?isGroupRemind=" + $scope.isGroupRemind):"");
          }
        })

      }
    })

  }
});
