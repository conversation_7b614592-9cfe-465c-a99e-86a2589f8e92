<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.OrgRelMapper">
	<resultMap id="orgRelMap"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgRelWrapper">
		<result property="id" column="ID" javaType="java.lang.Long" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
		<result property="orgID" column="orgID" javaType="java.lang.Integer" />
		<result property="orgCode" column="orgCode" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="oldPkgIDs" column="oldPkgIDs" javaType="java.lang.String" />
		<result property="lastMoveTime" column="lastMoveTime" javaType="java.util.Date" />
		<result property="memberName" column="memberName" javaType="java.lang.String" />
		<result property="deliveryCount" column="deliveryCount" javaType="java.lang.Integer" />
		<result property="contRuleID" column="contRuleID" javaType="java.lang.String" />
	</resultMap>

	<update id="batchUpdateOrgCode" parameterType="java.util.List">
		<foreach collection="list" item="orgRelWrapper" separator=";">
			update ecpm_t_org_rel set
			orgCode=#{orgRelWrapper.orgCode},
			updateTime=#{orgRelWrapper.updateTime}
			where
			orgID=#{orgRelWrapper.orgID}
			<if test="orgRelWrapper.id!=null">
				and id= #{orgRelWrapper.id}
			</if>
		</foreach>
	</update>
	<update id="batchDeleteOrgCode" >
		UPDATE ecpm_t_org_rel set
			orgCode =  REPLACE(orgCode,#{orgCode},""),
			orgCode =  REPLACE(orgCode,#{orgCode},""),
			orgCode =  REPLACE(orgCode,#{orgCode},""),
			orgCode =  REPLACE(orgCode,"||","")
		where
		orgCode like concat("%", #{orgCode},"%")
	</update>

	<update id="UpdateOrgId" >
		update ecpm_t_org_rel set
		orgId=#{targetOrgId}
		,lastMoveTime = now()
		<if test="oldPkgIDs!=null">
			,oldPkgIDs= #{oldPkgIDs}
		</if>
		<if test="orgCode!=null">
			,orgCode= #{orgCode}
		</if>
		where
		orgID=#{sourceOrgID}
		and id= #{id}
	</update>
	<update id="UpdateOrgEnterpriseID" >
		update ecpm_t_org_rel set
		enterpriseID = #{enterpriseID}
		where
		id= #{id}
	</update>
	<select id="queryOrgIDByOrderID" resultMap="orgRelMap">
		select t2.orgID,
		t2.orgCode,
		t2.enterpriseID
		from
		ecpm_t_member_subscribe t1,ecpm_t_org_rel t2
		where
		t1.memberID=t2.ID and t1.orderID=#{orderID}
	</select>

	<select id="queryOrgByOrgCode" resultMap="orgRelMap">
		select *
		from
		ecpm_t_org_rel
		where
		orgID = #{orgID} and orgCode like concat("%", #{orgCode},"%")
	</select>

	<select id="queryOrgByOrgID" resultMap="orgRelMap">
		select *
		from
		ecpm_t_org_rel
		where
		orgID=#{orgID}
		and ID =
		#{ID}
		<if test="enterpriseID!=null">
			and enterpriseID= #{enterpriseID}
		</if>
	</select>

	<insert id="insertOrg">
		INSERT INTO
		ecpm_t_org_rel
		(ID,
		enterpriseID,
		orgID,
		orgCode,
		createTime,
		updateTime,
		operatorID
		)
		VALUES
		<if test="orgCodeList !=null and orgCodeList.size() > 0">
			<foreach collection="orgCodeList" item="orgCode" separator=",">
				(
				#{ID},
				#{enterpriseID},
				#{orgID},
				#{orgCode},
				#{createTime},
				#{updateTime},
				#{operatorID}
				)
			</foreach>
		</if>
		<if test="orgCodeList ==null or orgCodeList.size() == 0">
			(
			#{ID},
			#{enterpriseID},
			#{orgID},
			#{orgCode},
			#{createTime},
			#{updateTime},
			#{operatorID}
			)
		</if>
	</insert>

	<delete id="deleteRelByOrgIDList">
		delete from ecpm_t_org_rel where
		orgID in
		<foreach item="orgID" index="index" collection="list" open="("
			separator="," close=")">
			#{orgID}
		</foreach>
	</delete>

	<select id="queryMembers" resultMap="orgRelMap" parameterType="java.util.Map">
		select
		rel.id,
		rel.orgID,
		rel.orgCode,
		rel.createTime,
		rel.updateTime,
		rel.operatorID,
		rel.enterpriseID
		from
		ecpm_t_org_rel rel LEFT JOIN ecpm_t_org_simple os ON rel.orgID = os.ID  where (rel.orgID=#{orgID} or os.oriOrgID = #{orgID})
		and rel.ID in
		<foreach collection="ids" item="id" open="(" separator=","
			close=")">
			#{id}
		</foreach>
	</select>

	<select id="queryMemberByOrgIDAndMsisdn" resultMap="orgRelMap">
		select
		t1.ID,
		t1.orgID,
		t1.orgCode,
		t1.createTime,
		t1.updateTime,
		t1.operatorID,
		t1.oldPkgIDs,
		t1.lastMoveTime
		from
		ecpm_t_org_rel t1 , ecpm_t_member t2
		where
		t1.ID = t2.ID
		and
		t2.msisdn =
		#{msisdn}
		and
		orgID = #{orgID}
	</select>

	<select id="queryMemberByMsisdn" resultMap="orgRelMap">
		select
		t1.ID,
		t1.orgID,
		t1.orgCode,
		t1.createTime,
		t1.updateTime,
		t1.operatorID,
		t1.enterpriseID,
		t3.orgName,
		t3.orgType
		from
		ecpm_t_org_rel t1 , ecpm_t_member t2 , ecpm_t_org_simple t3
		where
		t1.ID = t2.ID and t1.orgID = t3.ID
		and
		t2.msisdn = #{msisdn}

	</select>

	<select id="queryOrgByOrgIDList" resultMap="orgRelMap">
		select orgID, orgCode,msisdn
		from
		ecpm_t_org_rel
		<trim prefix="where" prefixOverrides="and|or">
			<if test="ids !=null and ids.size() > 0">
				and orgID in
				<foreach collection="ids" item="id" open="(" separator=","
					close=")">
					#{id}
				</foreach>
			</if>
			<if test="orgCodeList !=null and orgCodeList.size() > 0">
				and orgCode in
				<foreach collection="orgCodeList" item="orgCode" open="("
					separator="," close=")">
					#{orgCode}
				</foreach>
			</if>
		</trim>
	</select>

	<select id="queryOrgCodeMsisdnByOrgID" resultMap="orgRelMap">
		select
		t3.id,
		t3.msisdn,
		t3.orgCode
		from(
		select
		t.id,
		t1.msisdn,
		t2.orgCode,
		t.orgID,
		t.createTime,
		t.updateTime,
		t.operatorID
		from
		ecpm_t_org_rel
		t,ecpm_t_member
		t1,ecpm_t_content_org t2
		where t.ID = t1.ID
		and
		t2.ownerID = t.orgID
		and t.orgID =
		#{orgID}
		)t3
		group by t3.id,t3.msisdn,
		t3.orgCode
	</select>
	<select id="queryOrgRelByOrgID" resultMap="orgRelMap">

		SELECT
			t1.id,
			t2.msisdn,
			t1.orgCode,
			t1.orgID
		FROM
			ecpm_t_org_rel t1
			LEFT JOIN ecpm_t_member t2 ON t2.ID = t1.id
		WHERE
			orgID =  #{orgID}

	</select>
	<select id="queryOrgRelByOrgIDS" resultType="java.lang.String">
		SELECT
			t2.msisdn
		FROM
			ecpm_t_org_rel t1
				LEFT JOIN ecpm_t_member t2 ON t2.ID = t1.id
		<trim prefix="where" prefixOverrides="and|or">
			<if test="orgIDList !=null and orgIDList.size() > 0">
				and t1.orgID in
				<foreach collection="orgIDList" item="orgID" open="("
						 separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			and t2.msisdn is not null
		</trim>
	</select>

	<select id="queryOrgByOrgIDOrEnterpriseID" resultType="java.lang.Integer">
		select count(id)
		from
		ecpm_t_org_rel
		<trim prefix="where" prefixOverrides="and|or">
			<if test="orgIDList !=null and orgIDList.size() > 0">
				and orgID in
				<foreach collection="orgIDList" item="orgID" open="("
					separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			<if test="enterpriseID!=null">
				enterpriseID= #{enterpriseID}
			</if>
		</trim>
	</select>

	<select id="countMemberSubSuccessByOrgIDOrEnterpriseID"
		resultType="java.lang.Integer">
		select count(id)
		from
		ecpm_t_org_rel rel,ecpm_t_member_subscribe ms
		<trim prefix="where" prefixOverrides="and|or">
			rel.ID=ms.memberID
			<if test="orgIDList !=null and orgIDList.size() > 0">
				and orgID in
				<foreach collection="orgIDList" item="orgID" open="("
					separator="," close=")">
					#{orgID}
				</foreach>
			</if>
			<if test="uselessStatusList != null and uselessStatusList.size()>0">
				and
				<foreach collection="uselessStatusList" item="uselessStatus"
					open="(" separator="and" close=")">
					ms.status != #{uselessStatus}
				</foreach>
			</if>
			<if test="enterpriseID!=null">
				and enterpriseID= #{enterpriseID}
			</if>
		</trim>
	</select>

	<delete id="deleteOrgRels" parameterType="java.util.Map">
		delete from ecpm_t_org_rel where orgID=#{orgID}

		<if test="ids != null">
			and ID in
			<foreach collection="ids" item="id" open="(" separator=","
				close=")">
				#{id}
			</foreach>
		</if>
	</delete>

	<select id="queryMemberOrgByMemberIDList" resultMap="orgRelMap">
		select *
		from
		ecpm_t_org_rel
		where ID in
		<foreach collection="list" item="id" open="(" separator=","
			close=")">
			#{id}
		</foreach>
	</select>

	<select id="countMemberByEnterpriseIDProvince" resultType="java.lang.Integer">
		select count(DISTINCT msisdn) from
		(select t1.ID,t2.msisdn
		from ecpm_t_org_rel t1, ecpm_t_member_subscribe t2 , ecpm_t_org_simple t3
		where t1.ID=t2.memberID
		and t1.orgID = t3.ID
		and t1.enterpriseID=#{enterpriseID}
		and t2.`status`in (3,13,5)
		and t3.orgType = #{orgType}
		group by t1.ID,t2.msisdn
		UNION
		SELECT id,msisdn FROM ecpm_t_delete_back
		WHERE subStatus in (3,13,5)
		and operType in (2,3)
		and orgType = #{orgType}
		and enterpriseID  = #{enterpriseID}
		and objCreateTime &lt;= #{statDateEnd} and createTime &gt;= #{statDateStart}
		)a
	</select>

	<select id="countMemberByEnterpriseID" resultType="java.lang.Integer">
		select count(DISTINCT msisdn) from
		(select t1.ID,t2.msisdn
		from ecpm_t_org_rel t1, ecpm_t_member_subscribe t2 
		where t1.ID=t2.memberID 
		and t1.enterpriseID=#{enterpriseID}
		and t2.`status`in (3,13,5)
		group by t1.ID,t2.msisdn
		UNION
		SELECT id,msisdn FROM ecpm_t_delete_back
		WHERE subStatus in (3,13,5)
		and operType in (2,3)
		and enterpriseID  = #{enterpriseID}
		and objCreateTime &lt;= #{statDateEnd} and createTime &gt;= #{statDateStart}
		)a
	</select>
	
	<select id="countMemberSubscribeByOrgId" resultType="java.lang.Integer">
		select count(distinct t.msisdn) 
		from ecpm_t_member_subscribe t,ecpm_t_org_rel t1 
		where t.memberID = t1.ID 
		and t1.orgID = #{ID}
	</select>

	<select id="countMemberSubscribeByOrgIdForBranchType" resultType="java.lang.Integer">
		select count(distinct t.msisdn) 
		from ecpm_t_member_subscribe t,ecpm_t_org_rel t1, ecpm_t_org_simple s
		where t.memberID = t1.ID 
		and t1.orgID = s.id
		and (s.id = #{ID} or s.oriOrgID = #{ID})
	</select>


	<select id="countMemberSubscribeByOrgIdForBranchType2" resultType="java.lang.Integer">
		SELECT
			count( DISTINCT t.msisdn )
		FROM
			ecpm_t_org_simple s
				LEFT JOIN ecpm_t_org_simple s2 ON (
						s.id = s2.id
					OR s.id = s2.oriOrgID
					OR s.oriOrgID = s2.id
					OR s.oriOrgID = s2.oriOrgID )
				LEFT JOIN ecpm_t_org_rel t1 on t1.orgID = s2.id
				LEFT JOIN ecpm_t_member_subscribe t on t.memberID = t1.ID
		WHERE (s.id = #{ID} or s.oriOrgID = #{ID})
	</select>
	<select id="queryOrgRelByID" resultMap="orgRelMap" >
		select
		t1.ID,
		t2.msisdn,
		t1.orgID,
		t1.orgCode,
		t1.createTime,
		t1.updateTime,
		t1.operatorID,
		t1.enterpriseID
		from ecpm_t_org_rel t1,ecpm_t_member t2
		where t1.ID=t2.ID
		and t1.ID=#{id}
	</select>

	<select id="queryContentByID"  resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgRelWrapper">
		SELECT DISTINCT t2.orgCode,t3.subServType
		FROM ecpm_t_org_rel t1, ecpm_t_content_org t2,ecpm_t_content t3 
		WHERE t1.orgID=t2.ownerID AND t2.cyContID=t3.ID
		AND t1.ID=#{id}
		AND t3.syncStatus='6';
	</select>

	<select id="queryContentAndMemberByID" resultMap="orgRelMap" >
		SELECT DISTINCT t4.msisdn, GROUP_CONCAT(t2.orgCode SEPARATOR ',') AS orgCode, t1.orgID, GROUP_CONCAT(t3.contRuleID SEPARATOR ',') AS contRuleID
		FROM ecpm_t_org_rel t1, ecpm_t_content_org t2, ecpm_t_content t3, ecpm_t_member t4
		WHERE t1.orgID = t2.ownerID AND t2.cyContID = t3.ID AND t1.ID = t4.ID
		AND t4.msisdn=#{id} AND t3.subServType=#{subServType} AND t3.syncStatus = '6'
		GROUP BY t4.msisdn, t1.orgID;
	</select>

	<select id="queryExternalDeliverByOrgID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgRelWrapper" >
		SELECT t1.orgID,org.orgName,t1.ID,t2.memberName,t2.msisdn,t3.deliveryCount,t3.deliveryStatus,t3.subServType,t3.leftCount,t1.enterpriseID,es.enterpriseCode,
		GROUP_CONCAT( CONCAT(p.productID,"_",p.productName) ) productString
		 FROM ecpm_t_org_rel t1 
		LEFT JOIN ecpm_t_member t2  ON t1.ID = t2.ID 
		LEFT JOIN ecpm_t_member_ext_delivery t3 ON t2.msisdn=t3.msisdn AND t3.enterpriseID = t1.enterpriseID
		LEFT JOIN ecpm_t_external_product_subscribe eps on eps.msisdn = t2.msisdn
		and (eps.expiryTime is null or eps.expiryTime>=now())
		and (eps.effectiveTime is null or eps.effectiveTime&lt;=now())
		LEFT JOIN ecpm_t_external_product p on eps.extProID = p.productID
		LEFT JOIN ecpm_t_org_simple org on org.id = t1.orgId
		LEFT JOIN ecpm_t_enterprise_simple es on es.id = t1.enterpriseID
		WHERE 1=1
		<if test="orgIDs != null and orgIDs.size() > 0">
			and  t1.orgID in
			<foreach collection="orgIDs" item="id" open="(" separator=","
					 close=")">
				#{id}
			</foreach>
		</if>
		<if test="msisdnList != null and msisdnList.size() > 0">
			and  t2.msisdn in
			<foreach collection="msisdnList" item="msisdn" open="(" separator=","
					 close=")">
				#{msisdn}
			</foreach>
		</if>
		<if test="deliverStatusList != null and deliverStatusList.size() > 0">
			and t3.deliveryStatus in
			<foreach collection="deliverStatusList" item="d" open="(" separator=","
					 close=")">
				#{d}
			</foreach>
		</if>
		<if test="subServTypes != null and subServTypes.size() > 0">
			and  t3.subServType
			<foreach collection="subServTypes" item="s" open="(" separator=","
					 close=")">
				#{s}
			</foreach>
		</if>
		<if test="subServType != null">
			and t3.subServType = #{subServType}
		</if>
		<if test="deliveryStatus != null">
			and t3.deliveryStatus = #{deliveryStatus}
		</if>
		<if test="corpID != null">
			and es.enterpriseCode = #{corpID}
		</if>


		GROUP BY t1.orgID,org.orgName,t1.ID,t2.memberName,t2.msisdn,t3.deliveryCount,t3.deliveryStatus,t3.subServType,t3.leftCount,t1.enterpriseID,es.enterpriseCode

		<if test="pageNum != null and pageSize != null">
			limit #{pageNum},#{pageSize}
		</if>
	</select>

	<select id="queryExternalDeliverCount" resultType="java.lang.Integer" >
		SELECT count(*)	from (
          SELECT 1
		FROM ecpm_t_org_rel t1
		LEFT JOIN ecpm_t_member t2  ON t1.ID = t2.ID
		LEFT JOIN ecpm_t_member_ext_delivery t3 ON t2.msisdn=t3.msisdn
		LEFT JOIN ecpm_t_external_product_subscribe eps on eps.msisdn = t2.msisdn
		and (eps.expiryTime is null or eps.expiryTime>=now())
		and (eps.effectiveTime is null or eps.effectiveTime&lt;=now())
		LEFT JOIN ecpm_t_external_product p on eps.extProID = p.productID
		LEFT JOIN ecpm_t_org_simple org on org.id = t1.orgId
		LEFT JOIN ecpm_t_enterprise_simple es on es.id = t1.enterpriseID
		WHERE 1=1
		<if test="orgIDs != null and orgIDs.size() > 0">
			and  t1.orgID in
			<foreach collection="orgIDs" item="id" open="(" separator=","
					 close=")">
				#{id}
			</foreach>
		</if>
		<if test="msisdnList != null and msisdnList.size() > 0">
			and  t2.msisdn in
			<foreach collection="msisdnList" item="msisdn" open="(" separator=","
					 close=")">
				#{msisdn}
			</foreach>
		</if>
		<if test="subServTypes != null and subServTypes.size() > 0">
			and  t3.subServType
			<foreach collection="subServTypes" item="s" open="(" separator=","
					 close=")">
				#{s}
			</foreach>
		</if>
		<if test="subServType != null">
			and t3.subServType = #{subServType}
		</if>
		<if test="deliveryStatus != null">
			and t3.deliveryStatus = #{deliveryStatus}
		</if>
		<if test="corpID != null">
			and es.enterpriseCode = #{corpID}
		</if>
		GROUP BY t1.orgID,org.orgName,t1.ID,t2.memberName,t2.msisdn,t3.deliveryCount,t3.leftCount,t1.enterpriseID )a
	</select>

	<update id="updateOrgCode">
		update ecpm_t_org_rel set orgCode = #{orgCode}
		where ID = #{id}
	</update>

	<select id="queryOrgCode" resultMap="orgRelMap">
		select
			*
		from
			ecpm_t_org_rel
		where ID = #{id}
	</select>
</mapper>