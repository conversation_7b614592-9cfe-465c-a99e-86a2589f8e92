
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>活动管理</title>
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../../css/activityManageList.css" rel="stylesheet" />
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
<script type="text/javascript" src="activityStatisticsListCtrl.js"></script>
</head>
<body ng-app='myApp' style="min-width:1024px;" ng-controller='actStatListController' ng-init="init();">
    <div class="cooperation-manage" >
        <div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_INFOSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'COMMON_MERCHANTSTATISTICS'|translate"></span></div>
        <top:menu chose-index="0" page-url="/qycy/ecpmp/view/InfoStatistics/merchantStatistics/activityStatistics/activityStatisticsList" list-index="19"></top:menu>
			<div class="cooperation-search">
				<form class="form-horizontal">
					<div class="form-group">
						<div>
							<label for="enterpriseName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label" ng-bind="'QUERYORDERDETAIL_ENTERPRISENAME'|translate"
										 style="white-space:nowrap"></label>

							<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" >
								<input type="text" id="enterpriseName" class="form-control" placeholder="{{'QUERYORDERDETAIL_ENTERPRISENAMEPLACEHOLDER'|translate}}" ng-model="enterpriseName">
							</div>
						</div>

						<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
							<button type="submit" class="btn search-btn" ng-click="queryactStatList()" style="float: right">
								<icon class="search-iocn"></icon>
								<span ng-bind="'COMMON_SEARCH'|translate"></span>
							</button>
						</div>
					</div>
				</form>
			</div>
        <div class="add-table">
            <button id="exportactivityList" type="submit" class="btn add-btn" ng-click="exportFile()"><icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORT'|translate"></span></button>
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'ACTIVITY_STATISTICS'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th ng-bind="'MERCHANT_MERCHANTID'|translate"></th>
                        <th ng-bind="'MERCHANT_MERCHANTNAME'|translate"></th>
                        <th ng-bind="'ACTIVITY_AMOUNT'|translate"></th>
                        <th ng-bind="'ACTIVITY_SPOKEMANNUM'|translate"></th>
                        <th ng-bind="'ACTIVITY_PXNUM'|translate"></th>
                        <th ng-bind="'ACTIVITY_GJNUM'|translate"></th>
                        <th ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in enterpriseStatList">
                        <td title="{{item.enterpriseID}}">{{item.enterpriseID}}</td>
                        <td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
                        <td title="{{item.activityCount}}">{{item.activityCount}}</td>
                        <td title="{{item.spokeCount}}">{{item.spokeCount}}</td>
                        <td title="{{item.screenCount}}">{{item.screenCount}}</td>
                        <td title="{{item.endPhoneCount}}">{{item.endPhoneCount}}</td>
                        <td>
                            <div class="handle">
                                <ul>
                                    <li class="query" ng-click="gotoDetail(item)">
                                        <icon class="query-icon"></icon><span ng-bind="'COMMON_WATCHDETAIL'|translate"></span></li>
                                    <!-- <li class="edit">
                                        <icon class="edit-icon"></icon>编辑</li> -->
                                    <!-- <li class="delete">
                                        <icon class="delete-icon"></icon>删除</li> -->
                                    <!-- <li class="set">
                                        <icon class="set-icon"></icon>设置</li> -->
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="enterpriseStatList.length<=0">
                        <td style="text-align:center" colspan="7" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    <div>
        <ptl-page tableId="0" change="queryactStatList('justPage')"></ptl-page>
      </div>
    </div>

<!--小弹出框-->
        <!-- <button type="button" class="btn btn-primary" id="open-model" data-toggle="modal" data-target=".bs-example-modal-sm">Large modal</button> -->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838'>
                                {{tip|translate}}
                            </p></div>
                        </div>
                        <div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
						</div>
                    </div>
                </div>
            </div>
    
</body>
<style type="text/css">
	@charset "UTF-8";
	[ng-cloak] {
		display: none !important;
	}
	.cooperation-manage .form-inline {
		margin: 0 20px;
		background: #fff;
		border-radius: 4px;
		padding: 20px;
	}

	.cooperation-manage .form-group {
		margin-right: 20px;
		padding-top: 10px;
		padding-bottom: 20px;
	}
	.form-control:focus {
		border-color: #7360e1;
	}
	.cooperation-manage .add-table .add-btn .export-icon {
		display: inline-block;
		width: 20px;
		height: 20px;
		background: url(../../../../../assets/images/btnIcons18.png) no-repeat;
		vertical-align: middle;
		background-position: -107px 0px;
	}
	.handle {
		overflow: hidden;
	}
	body .handle ul li icon {
		width: 17px;
		height: 20px;
		display: inline-block;
		background: url(../../../../../assets/images/tableEditIcons18.png) no-repeat;
		vertical-align: bottom;
	}

	body .handle ul li icon.query-icon {
		background-position: -73px 0px;
	}

	.handle ul li icon.edit-icon {
		background-position: 0 0;
	}

	.handle ul li icon.delete-icon {
		background-position: -18px 0;
	}

	.handle ul li icon.set-icon {
		background-position: -38px 0;
	}
	.form-horizontal .control-label {
		padding-top: 22px;
		margin-bottom: 0;
		text-align: right;
	}
	.cooperation-manage .form-horizontal {
		margin: 0 20px;
		background: #fff;
		border-radius: 4px;
	}
	.cond-div {
		padding-top: 15px;
	}
</style>
</html>