<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.FilterSegmentMapper">
	<resultMap id="filterSegmentWrapper"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.FilterSegmentWrapper">
		<result property="segment" column="segment" javaType="java.lang.Integer" />
		<result property="citycode" column="citycode" javaType="java.lang.String" />
	</resultMap>

	<select id="getFilterSegments" resultMap="filterSegmentWrapper">
		select  segment ,citycode from ecpe_t_filter_segment
	</select>

</mapper>
