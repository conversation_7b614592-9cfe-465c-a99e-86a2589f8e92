# spi interface application name
spi.interface.application.names=com.huawei.jaguar.dsdp.ecpm.spi.feign.CyBaseCommonParamEcpmService:cy-base-common-params|com.huawei.jaguar.dsdp.ecpm.spi.feign.CyBaseSmsEcpmService:cy-base-sms|com.huawei.jaguar.dsdp.ecpm.spi.feign.CyBaseSmsService:cy-base-sms|com.huawei.jaguar.dsdp.ecpm.spi.feign.CyContentAuditService:cy-core-content-enterprise|com.huawei.jaguar.dsdp.ecpm.spi.feign.CyCoreContentEnterpriseEcpmService:cy-core-content-enterprise|com.huawei.jaguar.dsdp.ecpm.spi.feign.CyCorePushruleservice:cy-core-pushrule|com.huawei.jaguar.dsdp.ecpm.spi.feign.CyHarassService:cy-core-remind|com.huawei.jaguar.dsdp.ecpm.spi.feign.CySubscribeService:cy-sop-user-order|com.huawei.jaguar.dsdp.ecpm.spi.feign.DsumService:dsum|com.huawei.jaguar.dsdp.ecpm.spi.feign.EcpeService:ecpe|com.huawei.jaguar.dsdp.ecpm.spi.feign.EcpfepService:ecpfep

########################################################
### retryStatTask
########################################################
task.retryStatTask.taskName=retryStatTask
task.retryStatTask.taskKey=ecpm:stat:retryStatTask
task.retryStatTask.expireMillis=10000
task.retryStatTask.minutes=5
task.retryStatTask.fixedDelay=0 0/1 * * * ?
task.retryStatTask.taskSwitch=1
task.retryStatTask.produceCodes=09
task.retryStatTask.dealcdrnumber=50000
task.retryStatTask.core_pool_size=8
task.retryStatTask.max_pool_size=10
task.retryStatTask.keepAliveSeconds=3000

########################################################
### DealEnterprisePurchaseOrderQuotaTask
########################################################
task.DealEnterprisePurchaseOrderQuotaTask.taskName=dealEnterprisePurchaseOrderQuotaTask
task.DealEnterprisePurchaseOrderQuotaTask.taskKey=ecpm:enterprise:dealEnterprisePurchaseOrderQuotaTask
task.DealEnterprisePurchaseOrderQuotaTask.expireMillis=36000000
task.DealEnterprisePurchaseOrderQuotaTask.minutes=10
task.DealEnterprisePurchaseOrderQuotaTask.fixedDelay=0 0/30 * * * ?
task.DealEnterprisePurchaseOrderQuotaTask.taskSwitch=1
task.DealAgentEnterpriseOrderQuotaTask.taskName=dealAgentEnterpriseOrderQuotaTask
task.DealAgentEnterpriseOrderQuotaTask.taskKey=ecpm:enterprise:dealAgentEnterpriseOrderQuotaTask
task.DealAgentEnterpriseOrderQuotaTask.expireMillis=3600000
task.DealAgentEnterpriseOrderQuotaTask.minutes=10
task.DealAgentEnterpriseOrderQuotaTask.fixedDelay=0 5 0/1 * * ?
task.DealAgentEnterpriseOrderQuotaTask.taskSwitch=1
task.DealAgentEnterpriseOrderQuotaTask.startTime=020000
task.DealAgentEnterpriseOrderQuotaTask.endTime=021000
task.DealAgentEnterpriseOrderQuotaTask.core_pool_size=5
task.DealAgentEnterpriseOrderQuotaTask.max_pool_size=10
########################################################
### OfflineTask
########################################################
task.OfflineTask.taskName=offlineTask
task.OfflineTask.taskKey=ecpm:activity:offlineTask
task.OfflineTask.expireMillis=3600000
task.OfflineTask.minutes=10
task.OfflineTask.fixedDelay=0 0/30 * * * ?
task.OfflineTask.taskSwitch=1

########################################################
task.BatchH5SubTask.taskName=BatchH5SubTask
task.BatchH5SubTask.taskKey=ecpm:activity:batchH5SubTask
task.BatchH5SubTask.expireMillis=3600000
task.BatchH5SubTask.minutes=10
task.BatchH5SubTask.fixedDelay=0 0/15 * * * ?
task.BatchH5SubTask.taskSwitch=1

########################################################
### OnlineTask
########################################################
task.OnlineTask.taskName=onlineTask
task.OnlineTask.taskKey=ecpm:activity:onlineTask
task.OnlineTask.expireMillis=3600000
task.OnlineTask.minutes=10
task.OnlineTask.fixedDelay=0 0/30 * * * ?
task.OnlineTask.taskSwitch=1

task.spokeActivityWinTask.taskName=spokeActivityWinTask
task.spokeActivityWinTask.taskKey=ecpm:activity:spokeActivityWinTask
task.spokeActivityWinTask.expireMillis=3600000
task.spokeActivityWinTask.minutes=10
task.spokeActivityWinTask.fixedDelay=0 0 11 * * ?
task.spokeActivityWinTask.taskSwitch=1

task.contentNotifyResultTask.taskName=contentNotifyResultTask
task.contentNotifyResultTask.taskKey=ecpm:activity:contentNotifyResultTask
task.contentNotifyResultTask.expireMillis=3600000
task.contentNotifyResultTask.minutes=5
task.contentNotifyResultTask.fixedDelay=*/5 * * * * ?
task.contentNotifyResultTask.taskSwitch=1
task.contentNotifyResultTask.reqname=templatenotify
task.contentNotifyResultTask.requrl=https://10.124.73.17:28002/appenterprise/crmcallback
task.contentNotifyResultTask.accessCode=IcksgS6Ru0W28BV
task.contentNotifyResultTask.accessCodeKey=cdvAE4PERDLFYF7
task.contentNotifyResultTask.reqnameMember=subcribeResultNotify
task.contentNotifyResultTask.requrlMember=https://10.124.73.17:28002/appenterprise/crmcallback

task.merchantStatTask.taskName=merchantStatTask
task.merchantStatTask.taskKey=ecpm:activity:merchantStatTask
task.merchantStatTask.expireMillis=3600000
task.merchantStatTask.minutes=5
task.merchantStatTask.fixedDelay=0 0 7 * * ?
task.merchantStatTask.limitNum=20
task.merchantStatTask.tableName=detail
task.merchantStatTask.queryes.max=3000
task.merchantStatTask.taskSwitch=1
task.adStatTask.core_pool_size=5
task.adStatTask.max_pool_size=10
task.adStatTask.keepAliveSeconds=3000


activity.send.limitNum=10
enterprise.serviceType=3
sendsms.executor.thread.core_pool_size=8
sendsms.executor.thread.max_pool_size=10 
sendsms.executor.thread.keepAliveSeconds=3000
sendsms.format=NORMAL
sendsms.source=OTHER
sendsms.senderName=10658086

createContent.executor.thread.url=/sftp
color.support.channelType=1|2|3|4|5
color.nocheck.channelType=4
color.nocheck.white.ip=**************|**************
token.authSuccessInterfaceName=refresh|subscribe|shopPayNotifyRsp|epcPayNotify|auditnotify|subscribenotify|unsubscribeNotify|getContentAudifResultNotifyResponse|orderBackCall|delete|createOrder|reverseUnsubscribeNotify|singleSignOnInfo
ecpm.authEpcPayNotify.sharedSecret=1qaz@WSX3edc


#ElasticSearchCluster
#elasticsearch.server.connect=*************:9300|*************:9300|*************:9300|*************:9300|*************:9300|*************:9300|*************:9300|*************:9300|*************:9300|*************:9300
#elasticsearch.server.connect=**************:9300
#elasticsearch.server.connect=*************:9300
#elasticsearch.server.clustername=cy-es
elasticsearch.server.databaseName=cy-detail
elasticsearch.server.hotlineDeliveryTableName=delivery_detail
elasticsearch.server.cardAndAdvertisingTableName=delivery_record

content.notify.limitNum=1000
content.notify.limitFailNum=10

ecpm.pageSize=100

#\u6D3B\u52A8\u8BE6\u60C5\u524D\u7F00
submit.activity.QRCodeURL=https://cy.migudm.cn/qycy/h5/mpwap/views/index.html#/activityRep/activityPage?activityID=

#\u751F\u6210\u7684\u4E8C\u7EF4\u7801\u4FDD\u5B58\u7684\u8DEF\u5F84
submit.activity.QRCodeFilePath=/sftp/data/QRCode/

#DealEnterpriseContractOrderQuotaTask
task.DealEnterpriseContractOrderQuotaTask.taskName=dealEnterpriseContractOrderQuotaTask
task.DealEnterpriseContractOrderQuotaTask.taskKey=ecpm:enterprise:DealEnterpriseContractOrderQuotaTask
task.DealEnterpriseContractOrderQuotaTask.expireMillis=3600000
task.DealEnterpriseContractOrderQuotaTask.minutes=10
task.DealEnterpriseContractOrderQuotaTask.fixedDelay=0 30 23 * * ?
task.DealEnterpriseContractOrderQuotaTask.failTimes=5
task.DealEnterpriseContractOrderQuotaTask.taskSwitch=1

#DealPersonalContractOrderQuotaTask
task.DealPersonalContractOrderQuotaTask.taskName=dealPersonalContractOrderQuotaTask
task.DealPersonalContractOrderQuotaTask.taskKey=ecpm:enterprise:DealPersonalContractOrderQuotaTask
task.DealPersonalContractOrderQuotaTask.expireMillis=3600000
task.DealPersonalContractOrderQuotaTask.minutes=10
task.DealPersonalContractOrderQuotaTask.fixedDelay=0 0 */1 * * ?
task.DealPersonalContractOrderQuotaTask.taskSwitch=1

#spoke activity area
ecpm.area.nationan=000
delete.org.exceeds.maximum=1000

task.subProvincialDeliveryCdrUploadTask.taskName=subProvincialDeliveryCdrUploadTask
task.subProvincialDeliveryCdrUploadTask.taskKey=ecpm:enterprise:subProvincialDeliveryCdrUploadTask
task.subProvincialDeliveryCdrUploadTask.expireMillis=300000
task.subProvincialDeliveryCdrUploadTask.minutes=5
task.subProvincialDeliveryCdrUploadTask.fixedDelay=0 5/30 * * * ?
task.subProvincialDeliveryCdrUploadTask.taskSwitch=0
task.subProvincialDeliveryCdrUploadTask.produceCodes=09|12|01|11|16
task.subProvincialDeliveryCdrUploadTask.produceNotTarCodes=09|12|01|16
task.subProvincialDeliveryCdrUploadTask.dealcdrnumber=50000
subProvincialDeliveryCdr.executor.thread.core_pool_size=8
subProvincialDeliveryCdr.executor.thread.max_pool_size=10
subProvincialDeliveryCdr.executor.thread.keepAliveSeconds=3000

task.subProvincialDeliveryHotlineUploadTask.taskName=subProvincialDeliveryHotlineUploadTask
task.subProvincialDeliveryHotlineUploadTask.taskKey=ecpm:enterprise:subProvincialDeliveryHotlineUploadTask
task.subProvincialDeliveryHotlineUploadTask.expireMillis=300000
task.subProvincialDeliveryHotlineUploadTask.minutes=5
task.subProvincialDeliveryHotlineUploadTask.fixedDelay=0 0 3 * * ?
task.subProvincialDeliveryHotlineUploadTask.taskSwitch=0
task.subProvincialDeliveryHotlineUploadTask.dealcdrnumber=100000
subProvincialDeliveryHotline.executor.thread.core_pool_size=8
subProvincialDeliveryHotline.executor.thread.max_pool_size=10
subProvincialDeliveryHotline.executor.thread.keepAliveSeconds=3000

#DirectGuestDayStatTask
task.directGuestDayStatTask.fixedDelay=0 0 0/1 * * ?
task.directGuestDayStatTask.taskName=directGuestDayStatTask
task.directGuestDayStatTask.taskKey=ecpm:enterprise:directGuestDayStatTask
task.directGuestDayStatTask.expireMillis=3600000
task.directGuestDayStatTask.minutes=5
task.directGuestDayStatTask.taskSwitch=1

#DirectGuestMonthStatTask
task.directGuestMonthStatTask.fixedDelay=0 0 22 1 * ?
task.directGuestMonthStatTask.taskName=directGuestMonthStatTask
task.directGuestMonthStatTask.taskKey=ecpm:enterprise:directGuestMonthStatTask
task.directGuestMonthStatTask.expireMillis=3600000
task.directGuestMonthStatTask.minutes=5
task.directGuestMonthStatTask.taskSwitch=1

#SubProvinceDayStatTask
task.subProvinceDayStatTask.fixedDelay=0 0 0/1 * * ?
task.subProvinceDayStatTask.taskName=subProvinceDayStatTask
task.subProvinceDayStatTask.taskKey=ecpm:enterprise:subProvinceDayStatTask
task.subProvinceDayStatTask.expireMillis=3600000
task.subProvinceDayStatTask.minutes=5
task.subProvinceDayStatTask.taskSwitch=1

#DeductingQuotaTask
task.deductingQuotaTask.fixedDelay=0 0 5 * * ?
#task.deductingQuotaTask.fixedDelay=0 0/2 * * * ?
task.deductingQuotaTask.taskName=deductingQuotaTask
task.deductingQuotaTask.taskKey=ecpm:enterprise:deductingQuotaTask
task.deductingQuotaTask.expireMillis=3600000
task.deductingQuotaTask.minutes=5
task.deductingQuotaTask.taskSwitch=1


#SubProvinceMonthStatTask
task.subProvinceMonthStatTask.fixedDelay=0 0 22 1 * ?
task.subProvinceMonthStatTask.taskName=subProvinceMonthStatTask
task.subProvinceMonthStatTask.taskKey=ecpm:enterprise:subProvinceMonthStatTask
task.subProvinceMonthStatTask.expireMillis=3600000
task.subProvinceMonthStatTask.minutes=5
task.subProvinceMonthStatTask.taskSwitch=1

#AgentDayStatTask
task.agentDayStatTask.fixedDelay=0 0 0/1 * * ?
task.agentDayStatTask.taskName=agentDayStatTask
task.agentDayStatTask.taskKey=ecpm:enterprise:agentDayStatTask
task.agentDayStatTask.expireMillis=3600000
task.agentDayStatTask.minutes=5
task.agentDayStatTask.taskSwitch=1

#AgentMonthStatTask
task.agentMonthStatTask.fixedDelay=0 0 22 1 * ?
task.agentMonthStatTask.taskName=agentMonthStatTask
task.agentMonthStatTask.taskKey=ecpm:enterprise:agentMonthStatTask
task.agentMonthStatTask.expireMillis=3600000
task.agentMonthStatTask.minutes=5
task.agentMonthStatTask.taskSwitch=1


#BusinessAnaliysisBillTask
task.businessAnaliysisBillTask.fixedDelay=0 0 1 * * ?
#task.businessAnaliysisBillTask.fixedDelay=0 0/1 * * * ?
task.businessAnaliysisBillTask.taskName=businessAnaliysisBillTask
task.businessAnaliysisBillTask.taskKey=ecpm:enterprise:businessAnaliysisBillTask
task.businessAnaliysisBillTask.expireMillis=300000
task.businessAnaliysisBillTask.minutes=5
task.businessAnaliysisBillTask.taskSwitch=0
business.analysis.bill.task.filepath=/home/<USER>/ecpm_container/modules/ecpm/cdr/province/businessanalysis/hotlineTemp
business.analysis.bill.task.limitDoSize=5000
#filesize M/40*100000
business.analysis.bill.task.fileSize=1247500



task.BusinessContentTempBillTask.fixedDelay=0 15 1 * * ?
#task.BusinessContentTempBillTask.fixedDelay=0 0/1 * * * ?
task.BusinessContentTempBillTask.taskName=businessContentTempBillTask
task.BusinessContentTempBillTask.taskKey=ecpm:enterprise:businessContentTempBillTask
task.BusinessContentTempBillTask.expireMillis=300000
task.BusinessContentTempBillTask.minutes=5
task.BusinessContentTempBillTask.taskSwitch=0
business.contentTemp.bill.task.filepath=/home/<USER>/ecpm_container/modules/ecpm/cdr/province/businessanalysis/ysmbDelivery
business.contentTemp.bill.task.limitDoSize=5000
business.contentTemp.bill.task.fileSize=1000000



#BusinessServiceRuleTask
#task.BusinessServiceRuleTask.fixedDelay=0 0/1 * * * ?
task.BusinessServiceRuleTask.fixedDelay=0 0 2 * * ?
task.BusinessServiceRuleTask.taskName=businessServiceRuleTask
task.BusinessServiceRuleTask.taskKey=ecpm:enterprise:businessServiceRuleTask
task.BusinessServiceRuleTask.expireMillis=300000
task.BusinessServiceRuleTask.minutes=5
task.BusinessServiceRuleTask.taskSwitch=0
business.service.rule.task.filepath=/home/<USER>/ecpm_container/modules/ecpm/cdr/province/businessanalysis/serviceRule
business.service.rule.task.limitDoSize=1000
#filesize M/40*100000
business.service.rule.task.fileSize=1247500
#\u8FDC\u7AEF\u4FE1\u606F
BusinessServiceRuleTask.sftpip=*************
BusinessServiceRuleTask.sftpusername=cyqiye
BusinessServiceRuleTask.sftppassword=+dDY1j5K2rkMXkvzXoH93m0T+H0SapPBcQdMpjiK/Hw=
BusinessServiceRuleTask.sftpipport=21
BusinessServiceRuleTask.sftpconnecttimeount=5000
BusinessServiceRuleTask.sftpremotepath=caiyin
BusinessServiceRuleTask.backUpPath=/home/<USER>/cdr/billBak/businessanalysis/serviceRule




#MemberBillExportTask
task.memberBillExportTask.fixedDelay=0 0 4 1 * ?
task.memberBillExportTask.taskName=memberBillExportTask
task.memberBillExportTask.taskKey=ecpm:enterprise:memberBillExportTask
task.memberBillExportTask.expireMillis=1800000
task.memberBillExportTask.minutes=5
task.memberBillExportTask.taskSwitch=0
member.bill.task.filepath=/home/<USER>/cdr/province/businessanalysis/member
member.bill.task.backUpPath=/home/<USER>/cdr/billBak/businessanalysis/member
member.bill.task.limitDoSize=1000
#filesize M/40*100000
member.bill.task.fileSize=1247500

#BbossBillExportTask
task.bbossBillExportTask.fixedDelay=0 0/30 * * * ?
task.bbossBillExportTask.resetIndex=0 0 0 * * ?
task.bbossBillExportTask.taskName=bbossBillExportTask
task.bbossBillExportTask.taskKey=ecpm:enterprise:bbossBillExportTask
task.bbossBillExportTask.expireMillis=1800000
task.bbossBillExportTask.minutes=5
task.bbossBillExportTask.taskSwitch=0
bboss.bill.task.filepath=/home/<USER>/cdr/province/businessanalysis/bboss
bboss.bill.task.backUpPath=/home/<USER>/cdr/billBak/zyzq/bboss
bboss.bill.task.limitDoSize=1000
bboss.bill.task.limitQuerySize=500
#filesize M/40*100000
bboss.bill.task.fileSize=10319

elasticsearch.server.memberDatabaseName=provice_member
elasticsearch.server.memberTableName=member_record

task.exportActivityTask.fixedDelay=0 0 0/3 *  * ?
task.exportActivityTask.taskName=exportActivityTask
task.exportActivityTask.taskKey=ecpm:enterprise:exportActivityTask
task.exportActivityTask.expireMillis=3600000
task.exportActivityTask.minutes=5
task.exportActivityTask.taskSwitch=1
task.exportActivityTask.pageSize=100
task.exportActivityTask.localPath=/sftp/data/activityData
task.exportActivityTask.titles=\u4EE3\u8A00\u4EBA\u624B\u673A\u53F7|\u4EE3\u8A00\u4EBA\u5F52\u5C5E\u5730|\u4EE3\u8A00\u65F6\u95F4|\u4EE3\u8A00\u5929\u6570|\u5C4F\u663E\u63A8\u9001\u6761\u6570|\u6302\u673A\u63A8\u9001\u6761\u6570



#detailsExportTask
task.detailsExportTask.fixedDelay=0 0/10 * * * ?
task.detailsExportTask.taskName=detailsExportTask
task.detailsExportTask.taskKey=ecpm:enterprise:detailsExportTask
task.detailsExportTask.expireMillis=3600000
task.detailsExportTask.minutes=5
task.detailsExportTask.taskSwitch=1
task.detailsExportTask.pageSize=100
detailsExportTask.executor.thread.core_pool_size=8
detailsExportTask.executor.thread.max_pool_size=10
detailsExportTask.executor.thread.keepAliveSeconds=3000
#\u5BFC\u51FA\u6587\u4EF6\u5B58\u50A8\u5728\u672C\u5730\u7684\u4E34\u65F6\u76EE\u5F55
enterpriseDetailExport.localpath=/home/<USER>/export/enterprise/deliveryTemp
#\u5BFC\u51FA\u6587\u4EF6\u5B58\u50A8\u5728NAS\u7684\u76EE\u5F55,\u586B\u5199NAS\u6620\u5C04\u7684docker\u76EE\u5F55
enterpriseDetailExport.nasPath=/home/<USER>/ecpm_container/modules/ecpm/data/export
#\u5BFC\u51FA\u76F4\u5BA2\u6295\u9012\u660E\u7EC6\u5934\u90E8
enterpriseDetailExport.deliveryDetailCsvFile.head=\u4F01\u4E1A\u7F16\u53F7|\u4F01\u4E1A\u540D\u79F0|taskid|\u6A21\u677FID|\u4E3B\u53EB\u53F7\u7801|\u88AB\u53EB\u53F7\u7801|\u63A5\u6536\u53F7\u7801|\u7B2C\u4E09\u65B9\u6295\u9012\u8BF7\u6C42\u65F6\u95F4|\u80FD\u529B\u5F00\u653E\u5E73\u53F0\u4E0B\u53D1\u8BF7\u6C42\u65F6\u95F4|\u6295\u9012\u7ED3\u679C\u901A\u77E5\u65F6\u95F4|\u6295\u9012\u65F6\u5EF6\uFF08\u6BEB\u79D2\uFF09|\u6295\u9012\u8FD0\u8425\u5546|\u6295\u9012\u7C7B\u578B|\u6295\u9012\u7ED3\u679C
#\u5BFC\u51FA\u5206\u7701\u6295\u9012\u660E\u7EC6\u5934\u90E8
enterpriseDetailExport.provinceDeliveryDetailCsvFile.head=\u4F01\u4E1A\u7F16\u53F7|\u4F01\u4E1A\u540D\u79F0|taskid|\u6A21\u677FID|\u4E3B\u53EB\u53F7\u7801|\u88AB\u53EB\u53F7\u7801|\u63A5\u6536\u53F7\u7801|\u7B2C\u4E09\u65B9\u6295\u9012\u8BF7\u6C42\u65F6\u95F4|\u80FD\u529B\u5F00\u653E\u5E73\u53F0\u4E0B\u53D1\u8BF7\u6C42\u65F6\u95F4|\u6295\u9012\u7ED3\u679C\u901A\u77E5\u65F6\u95F4|\u6295\u9012\u65F6\u5EF6\uFF08\u6BEB\u79D2\uFF09|\u6295\u9012\u8FD0\u8425\u5546|\u539F\u6295\u9012\u7C7B\u578B|\u6295\u9012\u7C7B\u578B|\u6295\u9012\u7ED3\u679C
#\u5BFC\u51FA\u4EE3\u7406\u5546\u6295\u9012\u660E\u7EC6\u5934\u90E8
enterpriseDetailExport.agentDeliveryDetailCsvFile.head=\u4EE3\u7406\u5546\u7F16\u7801|\u4F01\u4E1A\u540D\u79F0|taskid|\u6A21\u677FID|\u4E3B\u53EB\u53F7\u7801|\u88AB\u53EB\u53F7\u7801|\u63A5\u6536\u53F7\u7801|\u7B2C\u4E09\u65B9\u6295\u9012\u8BF7\u6C42\u65F6\u95F4|\u80FD\u529B\u5F00\u653E\u5E73\u53F0\u4E0B\u53D1\u8BF7\u6C42\u65F6\u95F4|\u6295\u9012\u7ED3\u679C\u901A\u77E5\u65F6\u95F4|\u6295\u9012\u65F6\u5EF6\uFF08\u6BEB\u79D2\uFF09|\u6295\u9012\u8FD0\u8425\u5546|\u6295\u9012\u7C7B\u578B|\u6295\u9012\u7ED3\u679C
enterpriseDetailExport.otherDeliveryDetailCsvFile.head=\u4F01\u4E1A\u7F16\u53F7|\u4F01\u4E1A\u540D\u79F0|\u6A21\u677FID|\u4E3B\u53EB\u53F7\u7801|\u88AB\u53EB\u53F7\u7801|\u6295\u9012\u65F6\u95F4|\u6295\u9012\u65F6\u5EF6\uFF08\u79D2\uFF09|\u6295\u9012\u8FD0\u8425\u5546|\u6295\u9012\u7C7B\u578B|\u6295\u9012\u7ED3\u679C
enterpriseDetailExport.agentOtherDeliveryDetailCsvFile.head=\u4EE3\u7406\u5546\u7F16\u7801|\u4F01\u4E1A\u540D\u79F0|\u6A21\u677FID|\u4E3B\u53EB\u53F7\u7801|\u88AB\u53EB\u53F7\u7801|\u6295\u9012\u65F6\u95F4|\u6295\u9012\u65F6\u5EF6\uFF08\u79D2\uFF09|\u6295\u9012\u8FD0\u8425\u5546|\u6295\u9012\u7C7B\u578B|\u6295\u9012\u7ED3\u679C
enterpriseDetailExport.provinceOtherDeliveryDetailCsvFile.head=\u4F01\u4E1A\u7F16\u53F7|\u4F01\u4E1A\u540D\u79F0|\u6A21\u677FID|\u4E3B\u53EB\u53F7\u7801|\u88AB\u53EB\u53F7\u7801|\u6295\u9012\u65F6\u95F4|\u6295\u9012\u65F6\u5EF6\uFF08\u79D2\uFF09|\u6295\u9012\u8FD0\u8425\u5546|\u6295\u9012\u7C7B\u578B|\u6295\u9012\u7ED3\u679C
enterpriseDetailExport.tempDeliveryDetailCsvFile.head=\u6A21\u677FID|\u6A21\u677F\u5185\u5BB9|\u53D8\u91CF|\u6295\u9012\u7C7B\u578B|\u6295\u9012\u65F6\u95F4|\u4EE3\u7406\u5546\u540D\u79F0|\u4F01\u4E1A\u540D\u79F0|\u63A5\u6536\u53F7\u7801
enterpriseDetailExport.importDiffnetWayListDetailCsvFile.head=\u4F01\u4E1A\u7F16\u53F7|\u4F01\u4E1A\u540D\u79F0|\u5185\u5BB9ID|\u5185\u5BB9|\u4E1C\u76DF\u8054\u901A\u5BA1\u6838\u7ED3\u679C|\u4E1C\u76DF\u7535\u4FE1\u5BA1\u6838\u7ED3\u679C|\u8054\u901A\u5728\u7EBF\u5BA1\u6838\u7ED3\u679C|\u53F7\u767E\u5BA1\u6838\u7ED3\u679C|\u5F69\u8BAF\u8054\u901A|\u5F69\u8BAF\u7535\u4FE1
#\u5355\u4E2ACSV\u6587\u4EF6\u5199\u5165\u7684\u6700\u5927\u6570\u636E\u91CF\uFF0C\u8D85\u8FC7\u5C31\u5206\u6587\u4EF6\u5199
enterpriseDetailExport.singleFileMaxSize=500000
enterpriseDetailExport.flashLetter=\u95EA\u4FE1
enterpriseDetailExport.UssdFailedToFalshLetter=USSD\u5931\u8D25\u8F6C\u95EA\u4FE1
enterpriseDetailExport.success=\u6210\u529F
enterpriseDetailExport.failed=\u5931\u8D25
enterpriseDetailExport.mobile=\u79FB\u52A8
enterpriseDetailExport.unicom=\u8054\u901A
enterpriseDetailExport.telecom=\u7535\u4FE1

#\u521B\u5EFA\u6D3B\u52A8\u65F6\u9700\u8981\u5224\u65AD\u4F01\u4E1A\u7C7B\u578B
submit.activity.audit.status.enterprise.type=3|4
submit.activity.check.order=1|3

#\u6295\u9012\u6D4B\u8BD5\u63A5\u53E3\u76F8\u5173\u53C2\u6570
deliveryTest.body.src=10658086
deliveryTest.body.biztype=1
deliveryTest.callEventInfoType.direction=MO
deliveryTest.callEventInfoType.event=IDP

groupSendTaskResultDoCount=1000

batchGroupSendPool.groupSendKeepAliveSeconds=200
batchGroupSendPool.groupSendCorePoolSize=10
batchGroupSendPool.groupSendMaxPoolSize=20
batchGroupSendPool.groupSendQueueCapacity=100
group.send.task.count.one.time=1000

#\u5206\u7701\u6210\u5458\u5BFC\u5165ES
task.ProviceMemberToEsTask.fixedDelay=0 0 5 1 * ?
#task.ProviceMemberToEsTask.fixedDelay=0 0/5 * * * ?

task.ProviceMemberToEsTask.taskName=ProviceMemberToEsTask
task.ProviceMemberToEsTask.taskKey=ecpm:ProviceMember:ProviceMemberToEsTask
task.ProviceMemberToEsTask.expireMillis=3600000
task.ProviceMemberToEsTask.minutes=5
task.ProviceMemberToEsTask.taskSwitch=1
task.ProviceMemberToEsTask.pageSize=100
ProviceMemberToEsTask.executor.thread.core_pool_size=8
ProviceMemberToEsTask.executor.thread.max_pool_size=10
ProviceMemberToEsTask.executor.thread.keepAliveSeconds=3000

task.ProviceMemberByTimeTask.fixedDelay = 0 0/1 * * * ?
task.ProviceMemberByTimeTask.taskName = ProviceMemberByTimeTask
task.ProviceMemberByTimeTask.taskKey = ecpm:ProviceMember:ProviceMemberByTimeTask
task.ProviceMemberByTimeTask.minutes = 5
task.ProviceMemberByTimeTask.expireMillis = 3600000
task.ProviceMemberByTimeTask.taskSwitch=0
task.ProviceMemberByTimeTask.synBusinessProviceCode=12|01
task.ProviceMemberByTimeTask.filterProviceCode=01

ProviceMemberExport.filePath=/home/<USER>/cdr/province/memberTemp
ProviceMemberExport.fileNamePre=All_member_
ProviceMemberExport.singleMaxSize=100000
ProviceMemberExport.enterpriseIDMaxSize=800
task.ProviceMemberExportTask.fixedDelay=0 0 4 1 * ?
task.ProviceMemberExportTask.taskName=ProviceMemberExportTask
task.ProviceMemberExportTask.taskKey=ecpm:ProviceMember:ProviceMemberExportTask
task.ProviceMemberExportTask.expireMillis=3600000
task.ProviceMemberExportTask.minutes=5
task.ProviceMemberExportTask.taskSwitch=0
ProviceMemberExportTask.executor.thread.core_pool_size=8
ProviceMemberExportTask.executor.thread.max_pool_size=10
ProviceMemberExportTask.executor.thread.keepAliveSeconds=3000

#getBBossTokenTask
task.getBBossTokenTask.fixedDelay=0 0 0 1,11,21 * ?
task.getBBossTokenTask.taskName=getBBossTokenTask
task.getBBossTokenTask.taskKey=ecpm:enterprise:getBBossTokenTask
task.getBBossTokenTask.expireMillis=1800000
task.getBBossTokenTask.minutes=5
task.getBBossTokenTask.taskSwitch=1
task.getBBossTokenTask.appId=QYCY5910
task.getBBossTokenTask.appSecret=creBv9jTDT
task.getBBossTokenTask.seconds=1382400

content.platform.redis.expireTime=86400



#\u6587\u4EF6\u4E0A\u4F20\u5730\u5740
ecpm.uploadFile.path=/sftp/data/ecpmUploadFile

#\u6587\u4EF6\u4E0A\u4F20\u7684fileUse
ecpm.uploadFile.fileUse=ebanhanceMms

enterpriseCode.redis.keyPrefix=ecpm:enterpriseCode:
enterpriseCode.redis.expireTime=864000

ecpm.uploadFile.businessLicense.fileUse=businessLicense
ecpm.uploadFile.certificate.fileUse=certiFile
ecpm.cmppRuleID.guaji=01122_null_2_1_500000
ecpm.cmppRuleID.flash=01121_null_2_1_499999

eboss.syncOrderNotify.notifyCount=3
ecpm.channelSrc.zyzq=111
task.loadSubProvincialChannelSrc.redisKey=ecpm:delivery:loadSubProvincialChannelSrc


#\u4F01\u4E1A\u4FE1\u606Fredis\u7F13\u5B58key
ecpm.delivery.enterprise.redisKey=ecpe:delivery:enterprise:
#\u9ED8\u8BA4\u89C4\u5219\u4FE1\u606Fredis\u7F13\u5B58key
ecpm.servrule.default.redisKey=ecpe:servrule:default:
#\u9ED8\u8BA4\u89C4\u5219\u7F13\u5B58\u65F6\u95F4
ecpm.servrule.default.expireTime.redisKey=2592000
#\u4F01\u4E1A\u5F00\u5173\u4FE1\u606Fredis\u7F13\u5B58key
ecpm.serviceControl.info.redisKey=ecpm:serviceControl:info:
#\u4F01\u4E1A\u5F00\u5173\u4FE1\u606Fredis\u7F13\u5B58\u65F6\u95F4
ecpm.serviceControl.expireTime.redisKey=1800
#\u5355\u6B21\u540C\u6B65\u6210\u5458\u4E2A\u6570
ecpm.setGroupMember.batchNum=200
\u5BA1\u6838\u901A\u77E5\u7701\u4EFD\u5217\u8868
audit_notification_provinces=12|cy

notifyMember.maxiNum=1000
verifyMember.maxiNum=1000

400platform.header.secret = ebupt!@#123
400platform.header.sipAccessCode = qgcy
400platform.header.sipVersion = 1.0

400platform.verifyMemberUrl_sip = http://**************:8090/ecpfep/provincialEnterprises
400platform.notifyMemberUrl = http://**************:8090/ecpfep/provincialEnterprises
400platform.verifyMemberUrl_mid = https://ct.open.10086.cn/ecpfep/provincialEnterprises

11platform.header.secret = zjnlkfpt
11platform.header.sipAccessCode = 9990000639
11platform.header.sipVersion = 1.0
#11platform.memberServUrl = https://*************:443/api_test/v1/color
11platform.memberServUrl = https://dev.cmccopen.cn/api/v1/color

tentBill.allmemberbill.fixedDelay=0 0 4 1 * ?
tentBill.allmemberbill.taskSwitch=0
tentBill.allmemberbill.taskName=allmemberbillTask
tentBill.allmemberbill.taskKey=ecpm:stat:allmemberbillTask
tentBill.allmemberbill.expireMillis=10000
tentBill.allmemberbill.minutes=5
tentBill.allmemberbill.limitDoSize=1000
tentBill.allmemberbill.sftpremotepath=/colorprint/membill

tentBill.memberbill.fixedDelay=0 2/60 * * * ?
#tentBill.memberbill.fixedDelay=0 0/1 * * * ?
tentBill.memberbill.taskSwitch=0
tentBill.memberbill.taskName=memberbillTask
tentBill.memberbill.taskKey=ecpm:stat:memberbillTask
tentBill.memberbill.expireMillis=10000
tentBill.memberbill.minutes=5
tentBill.memberbill.sftpremotepath=/colorprint/membill
tentBill.memberbill.fileSize=100000
tentBill.memberbill.limitDoSize=100000
##\u8425\u5E10\u65E5\u589E\u4E8\u91CF\u6210\u5458\u8BDD\u5355\u914D\u7F6E###

task.subsidiaryAdvertDeliveryTask.taskName=subsidiaryAdvertDeliveryTask
task.subsidiaryAdvertDeliveryTask.fixedDelay=0 5/60 * * * ?
task.subsidiaryAdvertDeliveryTask.taskKey=ecpm:stat:subsidiaryAdvertDeliveryTask
task.subsidiaryAdvertDeliveryTask.taskSwitch=0
task.subsidiaryAdvertDeliveryTask.expireMillis=10000
task.subsidiaryAdvertDeliveryTask.minutes=5
task.subsidiaryAdvertDeliveryTask.sftpPath=/colorprint/advertBill
task.subsidiaryAdvertDeliveryTask.task.fileSize=100000
##\u8425\u5E10\u65E5\u589E\u4E8\u91CF\u6210\u5458\u8BDD\u5355\u914D\u7F6E###
#tentBill.sftpip=**************
#tentBill.sftpusername=root
#tentBill.sftppassword=dm@201804
tentBill.sftpipport=22
tentBill.sftpip=************
tentBill.sftpusername=sftp
tentBill.sftppassword=dmba#9102
tentBill.sftpconnecttimeount=5000
tent.card.sftpremotepath=/colorprint/cardbill
tent.hotline.sftpremotepath=/colorprint/hotline
tent.notice.sftpremotepath=/colorprint/notice
#tentBill.localpath = E:/home/<USER>/ecpm_container/modules/ecpm/cdr/tentBill/
#tentBill.locabaklpath = E:/home/<USER>/ecpm_container/modules/ecpm/cdrbak/tentBill/
tentBill.localpath = /home/<USER>/ecpm_container/modules/ecpm/cdr/tentBill/
tentBill.locabaklpath = /home/<USER>/ecpm_container/modules/ecpm/cdrbak/tentBill/
tentBill.maxPageSize=100000

#SubsidiaryHotlineDeliveryTask\u5B50\u4F01\u4E1A
task.subsidiaryHotlineDeliveryTask.fixedDelay=0 10/60 * * * ?
#task.subsidiaryHotlineDeliveryTask.fixedDelay=0 0/1 * * * ?
task.subsidiaryHotlineDeliveryTask.taskName=subsidiaryHotlineDeliveryTask
task.subsidiaryHotlineDeliveryTask.taskKey=ecpm:enterprise:subsidiaryHotlineDeliveryTask
task.subsidiaryHotlineDeliveryTask.taskSwitch=0
task.subsidiaryHotlineDeliveryTask.expireMillis=300000
task.subsidiaryHotlineDeliveryTask.minutes=5
task.subsideDeliveryCdrUploadTask.subsidecdrnumber=100000
task.subsideDelivery.queryesmax=10000

#subsidiaryNoticeDeliveryTask
task.subsidiaryNoticeDeliveryTask.fixedDelay=0 10/60 * * * ?
task.subsidiaryNoticeDeliveryTask.taskName=subsidiaryNoticeDeliveryTask
task.subsidiaryNoticeDeliveryTask.taskKey=ecpm:enterprise:subsidiaryNoticeDeliveryTask
task.subsidiaryNoticeDeliveryTask.taskSwitch=0
task.subsidiaryNoticeDeliveryTask.expireMillis=300000
task.subsidiaryNoticeDeliveryTask.minutes=5

#subsidiaryCardDeliveryTask
task.subsidiaryCardDeliveryTask.fixedDelay=0 5/60 * * * ?
task.subsidiaryCardDeliveryTask.taskName=subsidiaryCardDeliveryTask
task.subsidiaryCardDeliveryTask.taskKey=ecpm:enterprise:subsidiaryCardDeliveryTask
task.subsidiaryCardDeliveryTask.taskSwitch=0
task.subsidiaryCardDeliveryTask.expireMillis=300000
task.subsidiaryCardDeliveryTask.minutes=5

#checkExternalProductSubscribeTask
task.checkExternalProductSubscribeTask.fixedDelay=0 0 0 1 * ?
task.checkExternalProductSubscribeTask.taskName=checkExternalProductSubscribeTask
task.checkExternalProductSubscribeTask.taskKey=ecpm:enterprise:checkExternalProductSubscribeTask
task.checkExternalProductSubscribeTask.taskSwitch=1
task.checkExternalProductSubscribeTask.expireMillis=300000
task.checkExternalProductSubscribeTask.minutes=5

#\u9650\u5236\u67E5\u8BE2\u4F01\u4E1A\u6295\u9012\u6E05\u5355\u63A5\u53E3\u7684\u6570\u91CF
ecmp.queryDeliveryDetailList.LimitNumber=800

#designatedAgentBillTask
task.designatedAgentBillTask.fixedDelay=0 5/60 * * * ?
task.designatedAgentBillTask.taskName=designatedAgentBillTask
task.designatedAgentBillTask.taskKey=ecpm:enterprise:designatedAgentBillTask
task.designatedAgentBillTask.taskSwitch=0
task.designatedAgentBillTask.expireMillis=300000
task.designatedAgentBillTask.minutes=5
task.designatedAgentBillTask.configValue={"cfgList":[{"id":"weilan","savePath":"/sftpDetail/designAgent/weilansftp","enterpriseIDList":["********"],"sftpCfg":{"sftpIP":"*************","sftpPort":"22","sfptPath":"/weilansftp/weilan","sftpAccount":"agentsftp","sftpPwd":"zeX#Gk5645"}}]}
task.designatedAgentBillTask.aesEncryptKey=em05N3dPU1VvblFUdFZpcw==
task.designatedAgentBillTask.aesEncryptIv=RWQzSWMvemRvN2YxdVBHag==

task.dealIntelligentCallTask.fixedDelay=0 */5 * * * ?
task.dealIntelligentCallTask.taskName=dealIntelligentCallTask
task.dealIntelligentCallTask.taskKey=ecpm:enterprise:dealIntelligentCallTask
task.dealIntelligentCallTask.taskSwitch=1
task.dealIntelligentCallTask.expireMillis=300000
task.dealIntelligentCallTask.minutes=5
task.dealIntelligentCallTask.platformid=474d6550f1d146cab56d33b722c61845
ioc.interface.platformId=10001
ioc.interface.accountPasswd=qiguan@123

####\u4E2A\u4EBA \u65E5\u6295\u9012\u5468\u6708\u4E0A\u9650#####
maxPushPerDay=50

ecmp.ecMemberSync.timeout=3000

#YsmbDealTask
task.ysmbDealTask.fixedDelay=0 0/10 * * * ?
task.ysmbDealTask.taskName=ysmbDealTask
task.ysmbDealTask.taskKey=ecpm:enterprise:ysmbDealTask
task.ysmbDealTask.expireMillis=1800000
task.ysmbDealTask.minutes=5
task.ysmbDealTask.taskSwitch=1
task.ysmbDealTask.sleepMillis=1000
task.ysmbDealTask.maxDealNumber=300

delivery.redis.expireTime=604800
delivery.redis.deliveryNoBlackList.expireTime=1800

#RemindDeliveryConfig
task.remindDeliveryTask.fixedDelay=0 33 3 * * ?
task.remindDeliveryTask.taskName=remindDeliveryTask
task.remindDeliveryTask.taskKey=ecpm:enterprise:remindDeliveryTask
task.remindDeliveryTask.taskSwitch=0
task.remindDeliveryTask.expireMillis=300000
task.remindDeliveryTask.minutes=5
remindbill.localpath=/home/<USER>/ecpm_container/modules/ecpm/cdr/tentBill/
remindbill.locabaklpath=/home/<USER>/ecpm_container/modules/ecpm/cdrbak/tentBill/
#remindUpdateConfig
task.remindUpdateTask.fixedDelay=0 33 5 * * ?
task.remindUpdateTask.taskName=remindUpdateTask
task.remindUpdateTask.taskKey=ecpm:enterprise:remindUpdateTask
task.remindUpdateTask.taskSwitch=1
task.remindUpdateTask.expireMillis=300000
task.remindUpdateTask.minutes=5
task.remindUpdateTask.enterpriseIDs=
task.remindUpdateTask.filePath=/home/<USER>/ecpm_container/modules/ecpm/data/remindGroup/upload
task.remindUpdateTask.retryFilePath=/home/<USER>/ecpm_container/modules/ecpm/data/remindbackup/retryFilePath
task.remindUpdateTask.backUpFilePath=/home/<USER>/ecpm_container/modules/ecpm/data/remindbackup/backUpFilePath
task.remindUpdateTask.syncNum=20


task.diffnetWayContentTask.taskName=diffnetWayContentTask
task.diffnetWayContentTask.taskKey=ecpm:enterprise:diffnetWayContentTask
task.diffnetWayContentTask.expireMillis=3600000
task.diffnetWayContentTask.minutes=10
task.diffnetWayContentTask.fixedDelay=0 0/30 * * * ?
task.diffnetWayContentTask.taskSwitch=1
task.diffnetWayContentTask.queryNum=10000
task.diffnetWayContentTask.updateNum=1000

submitDefraudNum.appId = D8EF4A3SCD
submitDefraudNum.appSecret = FD5C9A218B
submitDefraudNum.Url = http://10.124.71.53:9068/txcy/thirdaccess/v1

ydy.productType5.Temps=1113,1110,1111
ydy.productType5.extTempsNum=10

task.deleteFileExportTask.deleteTime=0 0 2 * * ?
task.deleteFileExportTask.taskName=deleteFileExportTask
task.deleteFileExportTask.taskKey=ecpm:enterprise:deleteFileExportTask
task.deleteFileExportTask.expireMillis=3600000
task.deleteFileExportTask.minutes=5
task.deleteFileExportTask.taskSwitch=1
deleteFileExportTask.service.deleteTime=12
ydy.productType6.MemberNum=2500
enterprise.enterpriseDataAuthType=112

task.orderSyncRetryTask.taskName=orderSyncRetryTask
task.orderSyncRetryTask.taskKey=ecpm:stat:orderSyncRetryTask
task.orderSyncRetryTask.expireMillis=10000
task.orderSyncRetryTask.minutes=5
task.orderSyncRetryTask.fixedDelay=0 0/10 * * * ?
task.orderSyncRetryTask.taskSwitch=1
task.orderSyncRetryTask.retryCount=5

#\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD#ExportMsisdnToRemindTask
task.ExportMsisdnToRemindTask.fixedDelay=0 0 3 * * ?
task.ExportMsisdnToRemindTask.taskName=ExportMsisdnToRemindTask
task.ExportMsisdnToRemindTask.taskKey=ecpm:enterprise:ExportMsisdnToRemindTask
task.ExportMsisdnToRemindTask.expireMillis=180000
task.ExportMsisdnToRemindTask.minutes=5
task.ExportMsisdnToRemindTask.taskSwitch=0
task.ExportMsisdnToRemindTask.msisdnToRemind.localpath = /home/<USER>/ecpm_container/modules/ecpm/cdr/msisdnToRemind/
task.ExportMsisdnToRemindTask.msisdnToRemind.locabaklpath = /home/<USER>/cdr/billBak/msisdnToRemindBak/
task.ExportMsisdnToRemindTask.msisdnToRemind.limitDoSize=100000
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpipport=22
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpip=************
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpusername=huaweisftp
task.ExportMsisdnToRemindTask.msisdnToRemind.sftppassword=J3Ia\\$z0fw3
task.ExportMsisdnToRemindTask.msisdnToRemind.sftpremotepath=/home/<USER>/qiyerenzheng/download


task.dealMhWhiteTask.fixedDelay=0 0/5 * * * ?
task.dealMhWhiteTask.taskName=dealMhWhiteTask
task.dealMhWhiteTask.taskKey=ecpm:enterprise:dealMhWhiteTask
task.dealMhWhiteTask.expireMillis=1800000
task.dealMhWhiteTask.minutes=5
task.dealMhWhiteTask.taskSwitch=1
task.dealMhWhiteTask.sleepMillis=1000
task.dealMhWhiteTask.maxDealNumber=300
task.dealMhWhiteTask.download.path=/home/<USER>/mhWhite/
task.dealMhWhiteTask.download.userName=userqg
task.dealMhWhiteTask.download.pwd=*dADFnPBb0
task.dealMhWhiteTask.download.host=************
task.dealMhWhiteTask.download.port=22
task.dealMhWhiteTask.save.path=/home/<USER>/ecpm_container/modules/ecpm/cdr/mhWhite
task.dealMhWhiteTask.enterprise.id=30001035
task.dealMhWhiteTask.enterprise.groupId=226231


task.DealMhWhiteOptionMemberTask.fixedDelay=0 0/5 * * * ?
task.DealMhWhiteOptionMemberTask.taskName=DealMhWhiteOptionMemberTask
task.DealMhWhiteOptionMemberTask.taskKey=ecpm:enterprise:DealMhWhiteOptionMemberTask
task.DealMhWhiteOptionMemberTask.expireMillis=1800000
task.DealMhWhiteOptionMemberTask.minutes=5
task.DealMhWhiteOptionMemberTask.taskSwitch=1
task.DealMhWhiteOptionMemberTask.sleepMillis=1000
task.DealMhWhiteOptionMemberTask.maxDealNumber=300

short.message.template=\u3010\u4E2D\u56FD\u79FB\u52A8 \u548C\u5F69\u5370\u3011\u5C0A\u656C\u7684\u7528\u6237\uFF0C\u60A8\u597D\uFF01\u60A8\u4E8E{\u521B\u5EFA\u65F6\u95F4}\u63D0\u4EA4\u7684{\u4E1A\u52A1\u5B50\u7C7B\u578B}\u5185\u5BB9{\u5BA1\u6838\u7ED3\u679C}{\u5BA1\u6838\u610F\u89C1}\u3002

#DealSyncServiceRuleTask
task.DealSyncServiceRuleTask.fixedDelay=0 0 8,14,17 * * ?
#task.DealSyncServiceRuleTask.fixedDelay=0 0/2 * * * ?
task.DealSyncServiceRuleTask.taskName=DealSyncServiceRuleTask
task.DealSyncServiceRuleTask.taskKey=ecpm:enterprise:DealSyncServiceRuleTask
task.DealSyncServiceRuleTask.expireMillis=1800000
task.DealSyncServiceRuleTask.minutes=5
task.DealSyncServiceRuleTask.taskSwitch=1
task.DealSyncServiceRuleTask.sleepMillis=1000
task.DealSyncServiceRuleTask.maxDealNumber=300

#DeductingQuotaZyrxsfTask
task.DeductingQuotaZyrxsfTask.fixedDelay=0 0 0/1 * * ?
#task.DeductingQuotaZyrxsfTask.fixedDelay=0 0/2 * * * ?
task.DeductingQuotaZyrxsfTask.taskName=DeductingQuotaZyrxsfTask
task.DeductingQuotaZyrxsfTask.taskKey=ecpm:enterprise:DeductingQuotaZyrxsfTask
task.DeductingQuotaZyrxsfTask.expireMillis=1800000
task.DeductingQuotaZyrxsfTask.minutes=5
task.DeductingQuotaZyrxsfTask.taskSwitch=1
task.DeductingQuotaZyrxsfTask.sleepMillis=1000
task.DeductingQuotaZyrxsfTask.maxDealNumber=300

elasticsearch.server.tableName=detail

task.DealMonquotaDeliveryDayStatTask.taskName=dealMonquotaDeliveryDayStatTask
task.DealMonquotaDeliveryDayStatTask.taskKey=ecpm:enterprise:dealMonquotaDeliveryDayStatTask
task.DealMonquotaDeliveryDayStatTask.expireMillis=3600000
task.DealMonquotaDeliveryDayStatTask.minutes=10
task.DealMonquotaDeliveryDayStatTask.fixedDelay=0 12 3 * * ?
task.DealMonquotaDeliveryDayStatTask.taskSwitch=1
task.DealSyncServiceRuleTask.reCount=3
task.DealSyncServiceRuleTask.MGreCount=5
#ZYZQMemberBillExportTask
task.ZYZQMemberBillExportTask.fixedDelay=0 0 4 * * ?
task.ZYZQMemberBillExportTask.taskName=ZYZQMemberBillExportTask
task.ZYZQMemberBillExportTask.taskKey=ecpm:enterprise:ZYZQMemberBillExportTask
task.ZYZQMemberBillExportTask.expireMillis=1800000
task.ZYZQMemberBillExportTask.minutes=5
task.ZYZQMemberBillExportTask.taskSwitch=0
ZYZQMemberBillExportTask.bill.task.filepath=/home/<USER>/cdr/province/businessanalysis/ecService
ZYZQMemberBillExportTask.bill.task.backUpPath=/home/<USER>/cdr/billBak/businessanalysis/ecService
ZYZQMemberBillExportTask.bill.task.limitDoSize=100000
ZYZQMemberBillExportTask.bill.task.fileSize=100000

task.ZYZQMemberBillExportTask.productMap={"000366301":"\u6B63\u5F0F8000\u5143\u5957\u9910","000366201":"\u6B63\u5F0F5000\u5143\u5957\u9910","000366501":"\u6B63\u5F0F15000\u5143\u5957\u9910","000366401":"\u6B63\u5F0F10000\u5143\u5957\u9910","000375901":"\u6B63\u5F0F10\u5143\u5957\u9910","000365801":"\u6B63\u5F0F500\u5143\u5957\u9910","000375801":"\u6B63\u5F0F6\u5143\u5957\u9910","000365701":"\u6B63\u5F0F100\u5143\u5957\u9910","000365901":"\u6B63\u5F0F800\u5143\u5957\u9910","000376401":"\u6B63\u5F0F50\u5143\u5957\u9910","000376301":"\u6B63\u5F0F20\u5143\u5957\u9910","000376201":"\u6B63\u5F0F10\u5143\u5957\u9910","000376101":"\u6B63\u5F0F30\u5143\u5957\u9910","000376001":"\u6B63\u5F0F20\u5143\u5957\u9910","000202701":"\u6B63\u5F0F5\u5143\u5957\u9910","000202801":"\u4F53\u9A8C\u5957\u9910","000202601":"\u4F53\u9A8C\u5957\u9910\uFF08\u5305\u542B\u672C\u5F02\u7F51\uFF09","000313401":"\u4F53\u9A8C\u5957\u9910\uFF08\u79FB\u52A8\u672C\u7F51\uFF09","000366101":"\u6B63\u5F0F3000\u5143\u5957\u9910","000366001":"\u6B63\u5F0F1000\u5143\u5957\u9910"}



task.callbackMhWhiteByCommandTask.fixedDelay=0 0/5 * * * ?
task.callbackMhWhiteByCommandTask.taskName=callbackMhWhiteByCommandTask
task.callbackMhWhiteByCommandTask.taskKey=ecpm:enterprise:callbackMhWhiteByCommandTask
task.callbackMhWhiteByCommandTask.expireMillis=1800000
task.callbackMhWhiteByCommandTask.minutes=5
task.callbackMhWhiteByCommandTask.taskSwitch=1
task.callbackMhWhiteByCommandTask.sleepMillis=1000
task.callbackMhWhiteByCommandTask.maxDealNumber=300
task.callbackMhWhiteByCommandTask.searchTimeBefore=5
task.callbackMhWhiteByCommandTask.write.path=/home/<USER>/ecpm_container/modules/ecpm/cdr/mhWhiteTask
task.callbackMhWhiteByCommandTask.task.backUpPath=/home/<USER>/cdr/billBak/mhWhiteTask
task.callbackMhWhiteByCommandTask.upload.path=/home/<USER>/ymcardfk
task.callbackMhWhiteByCommandTask.file.key=miitfraud
task.callbackMhWhiteByCommandTask.cdr.department=\u5F69\u5370\u4E8B\u4E1A\u90E8
task.callbackMhWhiteByCommandTask.cdr.name=\u738B\u6B22
task.callbackMhWhiteByCommandTask.cdr.contact=***********


task.callbackMhWhiteByPhoneTask.fixedDelay=0 0/30 * * * ?
task.callbackMhWhiteByPhoneTask.taskName=callbackMhWhiteByPhoneTask
task.callbackMhWhiteByPhoneTask.taskKey=ecpm:enterprise:callbackMhWhiteByPhoneTask
task.callbackMhWhiteByPhoneTask.expireMillis=1800000
task.callbackMhWhiteByPhoneTask.minutes=5
task.callbackMhWhiteByPhoneTask.taskSwitch=1
task.callbackMhWhiteByPhoneTask.sleepMillis=1000
task.callbackMhWhiteByPhoneTask.maxDealNumber=300
task.callbackMhWhiteByPhoneTask.searchTimeBefore=690
task.callbackMhWhiteByPhoneTask.write.path=/home/<USER>/ecpm_container/modules/ecpm/cdr/mhWhitePhoneTask
task.callbackMhWhiteByPhoneTask.task.backUpPath=/home/<USER>/cdr/billBak/mhWhitePhoneTask
task.callbackMhWhiteByPhoneTask.upload.path=/home/<USER>/ymcardfk
task.callbackMhWhiteByPhoneTask.file.key=miitfraud
task.callbackMhWhiteByPhoneTask.cdr.department=\u5F69\u5370\u4E8B\u4E1A\u90E8
task.callbackMhWhiteByPhoneTask.cdr.name=\u738B\u6B22
task.callbackMhWhiteByPhoneTask.cdr.contact=***********

task.businessMhWhiteTask.fixedDelay=0 0 4 * * ?
businessMhWhiteTask.bill.task.weekDay=1
businessMhWhiteTask.bill.task.monthDay=1
task.businessMhWhiteTask.taskName=businessMhWhiteTask
task.businessMhWhiteTask.taskKey=ecpm:enterprise:businessMhWhiteTask
task.businessMhWhiteTask.expireMillis=1800000
task.businessMhWhiteTask.minutes=5
task.businessMhWhiteTask.taskSwitch=0
task.businessMhWhiteTask.sleepMillis=1000
task.businessMhWhiteTask.maxDealNumber=300
businessMhWhiteTask.bill.task.filepath=/home/<USER>/cdr/province/businessanalysis/dealMhWhiteTask
businessMhWhiteTask.bill.task.backUpPath=/home/<USER>/cdr/billBak/businessanalysis/mhwhite
businessMhWhiteTask.bill.task.limitDoSize=1000
businessMhWhiteTask.bill.task.fileSize=100000

enterprise.statArea.switch=1
task.dayStatSummaryTask.fixedDelay=0 0 6 * * ?
task.dayStatSummaryTask.taskName=dayStatSummaryTask
task.dayStatSummaryTask.taskKey=ecpm:enterprise:dayStatSummaryTask
task.dayStatSummaryTask.expireMillis=3600000
task.dayStatSummaryTask.minutes=5
task.dayStatSummaryTask.taskSwitch=1

task.monthStatSummaryTask.fixedDelay=0 0 4 2 * ?
task.monthStatSummaryTask.taskName=monthStatSummaryTask
task.monthStatSummaryTask.taskKey=ecpm:enterprise:monthStatSummaryTask
task.monthStatSummaryTask.expireMillis=3600000
task.monthStatSummaryTask.minutes=5
task.monthStatSummaryTask.taskSwitch=1

task.bbossWrongBillWarningTask.fixedDelay=0 0 8 * * ?
task.bbossWrongBillWarningTask.taskName=bbossWrongBillWarningTask
task.bbossWrongBillWarningTask.taskKey=ecpm:enterprise:bbossWrongBillWarningTask
task.bbossWrongBillWarningTask.expireMillis=3600000
task.bbossWrongBillWarningTask.minutes=5
task.bbossWrongBillWarningTask.taskSwitch=0
task.bbossWrongBillWarningTask.localFilePath=/home/<USER>/cdr/billBak/zyzq/bboss
task.bbossWrongBillWarningTask.contactEmail=<EMAIL>,<EMAIL>
task.bbossWrongBillWarningTask.codeMap=F000:\u6587\u4EF6\u5927\u5C0F\u4E0D\u662F508\u7684\u500D\u6570|F001:\u6587\u4EF6\u91CD\u590D\u4E0A\u4F20\uFF0C\u4E3B\u8981\u6307\u5DF2\u7ECF\u6B63\u786E\u5904\u7406\u8FC7\u8BE5\u8BDD\u5355\u6587\u4EF6\uFF08\u62D2\u6536\u6587\u4EF6\u7684\u91CD\u590D\u4E0A\u4F20\u662F\u5141\u8BB8\u7684\uFF09|F002:\u6587\u4EF6\u540D\u4E2D\u7684\u5E8F\u53F7\u975E\u6CD5\uFF0C\u5E94\u8BE5\u4E3A0000\u20149998|F003:\u6587\u4EF6\u540D\u4E2D\u7684\u65E5\u671F\u975E\u6CD5|F004:\u6587\u4EF6\u540D\u65E5\u671F\u65E9\u4E8E\u89C4\u5B9A\u7684\u65F6\u95F4\u8303\u56F4|F005:\u6587\u4EF6\u540D\u65F6\u95F4\u8D85\u524D(\u6587\u4EF6\u540D\u65F6\u95F4\u8D85\u8FC7\u7CFB\u7EDF\u5F53\u524D\u65F6\u95F41\u5929)|F110:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u8BB0\u5F55\u7C7B\u578B\u4E0D\u662F\u201C90\u201D|F120:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u4FDD\u7559\u5B57\u6BB5\u4E0D\u4E3A\u7A7A|F130:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u6587\u4EF6\u5E8F\u53F7\u4E0E\u6587\u4EF6\u540D\u4E2D\u7684\u6587\u4EF6\u5E8F\u53F7\u4E0D\u4E00\u81F4|F140:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u6587\u4EF6\u4EA7\u751F\u65E5\u671F\u4E0E\u6587\u4EF6\u540D\u4E2D\u7684\u65E5\u671F\u4E0D\u4E00\u81F4|F150:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u8BDD\u5355\u603B\u6570\u5408\u8BA1\u4E0E\u7D2F\u8BA1\u503C\u4E0D\u7B26|F170:\u5C3E\u8BB0\u5F55\u4E2D\u7684\u7ED3\u675F\u7B26\u4E0D\u662F\u56DE\u8F66\u6362\u884C
task.bbossWrongBillWarningTask.sftpip=***********
task.bbossWrongBillWarningTask.sftpusername=sftpqycy
task.bbossWrongBillWarningTask.sftppassword=aMeOgYmfwW7iG9HbdlbVWQ==
task.bbossWrongBillWarningTask.sftpipport=1161
task.bbossWrongBillWarningTask.sftpremotepath=/outgoing/CMP/


esSearch.switch=0
task.createNextEsIndexTask.fixedDelay=0 0 4 * * ?
task.createNextEsIndexTask.taskName=createNextEsIndexTask
task.createNextEsIndexTask.taskKey=ecpm:enterprise:createNextEsIndexTask
task.createNextEsIndexTask.expireMillis=3600000
task.createNextEsIndexTask.minutes=5
task.createNextEsIndexTask.taskSwitch=1
task.createNextEsIndexTask.esUrl=http://*************:9200/
ecpm.ThirdpartyAccess.enterprises=30001224,30001354

ZYZQMemberBillExportTask.bill.task.sftpremotepath=/ecService
businessMhWhiteTask.bill.task.sftpremotepath=/mhwhite


task.DealEnterpriseServiceProductTask.fixedDelay=0 40 2 * * ?
task.DealEnterpriseServiceProductTask.taskName=DealEnterpriseServiceProductTask
task.DealEnterpriseServiceProductTask.taskKey=ecpm:enterprise:DealEnterpriseServiceProductTask
task.DealEnterpriseServiceProductTask.expireMillis=1800000
task.DealEnterpriseServiceProductTask.minutes=5
task.DealEnterpriseServiceProductTask.taskSwitch=1
task.DealEnterpriseServiceProductTask.sleepMillis=1000
task.DealEnterpriseServiceProductTask.retryCount=3

ecpm.expirationTimeSeconds=30

task.subcribeResultNotifyTask.fixedDelay=0 0/1 * * * ?
task.subcribeResultNotifyTask.taskName=subcribeResultNotifyTask
task.subcribeResultNotifyTask.taskKey=ecpm:subcribeResultNotifyTask
task.subcribeResultNotifyTask.expireMillis=3600000
task.subcribeResultNotifyTask.minutes=5
task.subcribeResultNotifyTask.taskSwitch=1
task.subcribeResultNotifyTask.retryCountConfiguration=1

#DealSharedPackageQuotaTask
task.DealSharedPackageQuotaTaskConfig.taskName=DealSharedPackageQuotaTask
task.DealSharedPackageQuotaTaskConfig.taskKey=ecpm:activity:DealSharedPackageQuotaTaskConfig
task.DealSharedPackageQuotaTaskConfig.expireMillis=3600000
task.DealSharedPackageQuotaTaskConfig.minutes=5
task.DealSharedPackageQuotaTaskConfig.fixedDelay=0 */5 * * * ?
task.DealSharedPackageQuotaTaskConfig.taskSwitch=1
task.DealSharedPackageQuotaTaskConfig.reqname=receiveSharedPackageExceed
task.DealSharedPackageQuotaTaskConfig.requrl=https://www.118114.net/orders-zs

#DealEnterpriseSharedQuotaTask
task.DealEnterpriseSharedQuotaTaskConfig.taskName=DealEnterpriseSharedQuotaTask
task.DealEnterpriseSharedQuotaTaskConfig.taskKey=ecpm:DealSharedPackageQuotaTaskConfig
task.DealEnterpriseSharedQuotaTaskConfig.expireMillis=3600000
task.DealEnterpriseSharedQuotaTaskConfig.minutes=5
task.DealEnterpriseSharedQuotaTaskConfig.fixedDelay=0 0 22 28-31 * ?
task.DealEnterpriseSharedQuotaTaskConfig.taskSwitch=1
task.DealEnterpriseSharedQuotaTaskConfig.retryCount=1

ecpm.mp.px.package=2000202701|2000375801|2000375901|2000376001|2000376101|2000376201
ecpm.mp.gd.package=1002410|2002418|5002438|8002458

ecpm.gd.beyond.package.switch.redisKey=ecpm:gd:beyond:package:switch:info:
ecpm.gd.beyond.package.switch.expireTime.redisKey=86400

#\u65B0\u589E\u6D59\u6C5F\u540C\u6B65\u6210\u5458\u5206\u6279\u8C03\u7528ecpfep\u63A5\u53E3\uFF0C\u6BCF\u6279\u6570\u91CF
ZHEJIANGMember.BatchMemberSum = 500

subEnterprise.notify.reqname=receiveEnterprise
subEnterprise.notify.reqUrl=https://www.118114.net/orders-zs

subEnterprise.notify.list=[{"reqname":"receiveEnterprise","reqUrl":"https://www.118114.net/orders-zs","id":"20000217"},{"reqname":"subEnterpriseReviewNotify","reqUrl":"https://10.124.73.17:18085/card/external/crmcallback","id":"20000220"}]

enterprise.createContent.secretKey=0123456789ABHAEQ

ecpm.agentSingleSignOn.info=[{"agentEnterpriseId":"20000225","timeoutUrl":"http://218.204.70.188:8124/"}]