<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <!-- <base href="/" /> -->
    <title>新增彩印</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css"/>
    <link href="../../../../../css/daterangepicker.min.css" rel="stylesheet" type="text/css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/addContent.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/daterangepicker/daterangepicker.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
    <script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css"/>
    <script type="text/javascript" src="../../../../../directives/preview/preview.js"></script>
    <script type="text/javascript" src="../../../../../directives/page/page.js"></script>
    <link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
    <script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
    <script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
    <script src="../../../../../directives/cy-icon-content/cy-icon-content.js"></script>
    <link href="../../../../../directives/cy-icon-content/cy-icon-content.css" rel="stylesheet"/>

    <script type="text/javascript" src="addColorPrintCtrl.js"></script>
    <style>
        [ng-cloak] {
            display: none !important;
        }

        .cursor-def {
            cursor: default !important;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            text-align: right;
        }

        .pic-wrapper, .ctn-wrapper {
            overflow: hidden;
            padding: 10px 0;
            position: relative;
        }

        .bg-disabled {
            background: #a69aec;
        }

        .form-group.pushObj .pageChoose{
            display: none;
        }
        .form-group.pushObj .toPage{
            display: none;
        }
        #groupListPop .pageChoose{
            display: none;
        }
        #groupListPop .toPage{
            display: none;
        }
    </style>
</head>

<body ng-app='myApp' ng-controller='addPrintController' ng-init="init();">
<div style="min-width: 1024px;" class="order-manage" ng-cloak>
    <!-- 直客企业自己登陆 -->
    <div ng-if="loginRoleType=='zhike'" class="cooperation-head">
        <span class="frist-tab" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENT_MANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span ng-show="operateType=='add'" class="second-tab" ng-bind="'CONTENTAUDIT_ADD'|translate"></span>
        <span ng-show="operateType=='modify'" class="second-tab" ng-bind="'CONTENTAUDIT_MODIFY'|translate"></span>
        <span ng-show="operateType=='detail'" class="second-tab" ng-bind="'CONTENTAUDIT_DETAIL'|translate"></span>
    </div>
    <!-- 管理员登陆查看直客 -->
    <div ng-if="isSuperManager&&enterpriseType==1" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENT_MANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span ng-show="operateType=='add'" class="second-tab" ng-bind="'CONTENTAUDIT_ADD'|translate"></span>
        <span ng-show="operateType=='modify'" class="second-tab" ng-bind="'CONTENTAUDIT_MODIFY'|translate"></span>
        <span ng-show="operateType=='detail'" class="second-tab" ng-bind="'CONTENTAUDIT_DETAIL'|translate"></span>
    </div>
    <!-- 代理商自己登陆 -->
    <div ng-if="loginRoleType=='agent'" class="cooperation-head">
        <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE '|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENT_MANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span ng-show="operateType=='add'" class="second-tab" ng-bind="'CONTENTAUDIT_ADDCTN'|translate"></span>
        <span ng-show="operateType=='modify'" class="second-tab" ng-bind="'CONTENTAUDIT_MODIFYCTN'|translate"></span>
        <span ng-show="operateType=='detail'" class="second-tab" ng-bind="'CONTENTAUDIT_DETAIL1'|translate"></span>
    </div>
    <!-- 管理员登陆查看代理商 -->
    <div ng-if="(isSuperManager&&enterpriseType==3) || loginRoleType=='agent' || isSubEnterpirse" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CONTENT_MANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span ng-show="operateType=='add'" class="second-tab" ng-bind="'CONTENTAUDIT_ADDCTN'|translate"></span>
        <span ng-show="operateType=='modify'" class="second-tab" ng-bind="'CONTENTAUDIT_MODIFYCTN'|translate"></span>
        <span ng-show="operateType=='detail'" class="second-tab" ng-bind="'CONTENTAUDIT_DETAIL1'|translate"></span>
    </div>
    <!-- 管理员登陆查看直客 -->
    <top:menu chose-index="4" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/content/addColorPrint"
              list-index="7" ng-if="isSuperManager&&enterpriseType==1"></top:menu>
    <!-- 直客用户自己登陆 -->
    <top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/content/queryContentInfoList"
              list-index="9" ng-if="loginRoleType=='zhike'"></top:menu>
    <!-- 管理员登陆查看代理商二级企业 or 代理商自己登陆 or 二级企业自己登录-->
    <top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/groupManage/groupList"
              list-index="11"
              ng-if="(isSuperManager &&enterpriseType==3) || loginRoleType=='agent' || isSubEnterpirse"></top:menu>
    <div class="cooper-messsage">
        <div class="cooper-title">
            1.<span ng-bind="'CONTENTAUDIT_CONTENTINFO'|translate"></span>
            <!-- <span ng-if="loginRoleType=='agent'||enterpriseType==3"
                ng-bind="'ENTERPRISE_ENTERPRISEINFO'|translate"></span> -->
        </div>
    </div>
    <div class="cooper-tab">
        <form class="form-horizontal" name="myForm" novalidate>
            <div class="form-group">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'CONTENTAUDIT_POSTTYPE'|translate"></span>：
                </label>
                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
                    <select ng-if="operateType!='detail'" class="form-control" ng-model="initPrintInfo.subServType"
                            ng-options="x.id as x.name for x in subServTypeChoise" ng-change="changeSubServerType()"
                            ng-disabled="operateType!='add'"></select>
                    <p ng-if="operateType=='detail'" disabled class="form-control">{{pushSubServTypeMap(initPrintInfo.subServType, initPrintInfo.hangupType)}}</p>
                </div>
                <div ng-show="initPrintInfo.subServType == 1 || initPrintInfo.subServType == 2 || initPrintInfo.subServType == 3">
                    <cy:icon:content  content-type="{{contentType}}">
                    </cy:icon:content>
                </div>

            </div>
            <div class="form-group" ng-if="initPrintInfo.subServType=='8'">
                <!-- 彩印标题 -->
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'CONTENT_COLORPRINTTITLE'|translate"></span>：
                </label>
                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
                    <input placeholder="请输入彩印标题1~20字"
                           ng-class="{'ng-invalid':isSensitive[0] && myForm.colorTitle.$viewValue}" autocomplete="off"
                           class="form-control" rows="3" name="colorTitle" ng-maxlength="20"
                           ng-model="initPrintInfo.colorTitle" ng-disabled="operateType=='detail'" required
                           ng-blur="sensitiveCheck(initPrintInfo.colorTitle,0)"/>
                    <span style="color:red" ng-show="myForm.colorTitle.$dirty && myForm.colorTitle.$invalid">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                 align="absmiddle">
							<span ng-show="myForm.colorTitle.$error.required"
                                  ng-bind="'CONTENT_COLORTITLEREQUIRE'|translate"></span>
							<span ng-show="myForm.colorTitle.$error.maxlength"
                                  ng-bind="'CONTENT_COLORTITLELIMIT_256'|translate"></span>
						</span>
                </div>
                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4"
                     ng-show="myForm.colorTitle.$viewValue && myForm.colorTitle.$valid && sensitiveWords[0].length>0">
                    <p style="padding:10px 20px 10px 20px;color: red;">
                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
                        {{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[0]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}
                    </p>
                </div>
            </div>
            <!-- 内容类型 -->
            <div class="form-group content-type" ng-if="!is5GEnterprise">
                <b ng-show="initPrintInfo.subServType=='3'" style="float: left;width: 100%;margin-left: 7.5%;margin-block: 3px;">主叫内容</b>

                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    {{'CONTENT_TYPE'|translate}}：
                </label>
                <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5 use-name" style="width: 300px;">
                    <li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li"
                        ng-click="changeContentType(0)"><span
                            class="check-btn redio-btn checked"> </span>
                        {{'CONTENT_TYPE_DIY'|translate}}
                    </li>
                    <li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li"
                        ng-click="changeContentType(1)"><span
                            class="check-btn redio-btn" style="position:relative"> </span>
                        {{'CONTENT_TYPE_YSGDMB'|translate}}
                    </li>
                </div>

                <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5 use-name">
                    <span style="color: #ff0000;margin-left: 5px">提示：选择预设固定模板时，不填写签名，则默认审核通过</span>
                </div>
            </div>
            <!-- 模板内容 -->
            <div class="form-group" ng-if="initPrintInfo.isRefYsmb==1">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'TEMPLATE_CONTENT'|translate"></span>：
                </label>
                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
                    <select ng-if="operateType!='detail'" class="form-control" ng-model="initPrintInfo.refYsmbID"
                            ng-options="x.id as x.name for x in ysmbChoise"
                            ng-change="changeYsmb(initPrintInfo.refYsmbID)"
                            ng-disabled="operateType=='detail'"></select>
                    <p ng-if="operateType=='detail'" disabled class="form-control"
                       ng-bind="ysmbMap[initPrintInfo.refYsmbID]"></p>
                </div>
            </div>
            <!-- 非挂机彩印 -->
            <div class="form-group" ng-if="initPrintInfo.subServType!='8' && initPrintInfo.isRefYsmb==0">
            	<div class="row" style="margin:0px;display:flex;">
	                <!-- 彩印内容lable -->
	                <!--修改 20190905-->
	                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
	                       ng-show="initPrintInfo.subServType!='3' && initPrintInfo.subServType!='8'">
	                    <icon>*</icon>
	                    <span ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></span>：
	                </label>
	                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"
	                       ng-show="initPrintInfo.subServType=='3'">
	                    <span ng-bind="'CONTENTAUDIT_CALLER_CYCONTENT'|translate"></span>：
	                </label>
	                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
							<textarea style="height:100%;"
	                                  ng-class="{'ng-invalid':isSensitive[1] && myForm.colorContent.$viewValue}"
	                                  ng-if="initPrintInfo.subServType!='8'"
	                                  placeholder="{{(initPrintInfo.subServType == 4.1 || initPrintInfo.subServType == 4.2 || initPrintInfo.subServType == 4) ? '请输入彩印内容1~750字' :'请输入彩印内容1~70字'}}"
	                                  class="form-control"
	                                  rows="3" name="colorContent" ng-model="initPrintInfo.colorContent"
	                                  ng-blur="sensitiveCheck(initPrintInfo.colorContent,1)"
	                                  ng-disabled="operateType=='detail'"></textarea>
	                    <span style="color:red" ng-show="(myForm.colorContent.$dirty && myForm.colorContent.$invalid) || (checkSign==1) ">
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
	                                 align="absmiddle">
								<span ng-show="myForm.colorContent.$error.required&&initPrintInfo.subServType!='8'"
	                                  ng-bind="'CONTENT_COLORREQUIRE'|translate"></span>
								<span ng-show="(initPrintInfo.colorContent>maxSignLength && initPrintInfo.subServType!='8' && (initPrintInfo.subServType!='4' && initPrintInfo.subServType!='4.1' && initPrintInfo.subServType!='4.2')) || (initPrintInfo.subServType!='8' && (initPrintInfo.subServType!='4' && initPrintInfo.subServType!='4.1' && initPrintInfo.subServType!='4.2') && checkSign==1)"
	                                  ng-bind="'SMS_CONTENT_LENGTH_MAX_70'|translate"></span>
								<span ng-show="(initPrintInfo.colorContent>maxSignLength && (initPrintInfo.subServType =='4' || initPrintInfo.subServType =='4.1' || initPrintInfo.subServType =='4.2')) || ((initPrintInfo.subServType =='4' || initPrintInfo.subServType =='4.1' || initPrintInfo.subServType =='4.2') && checkSign==1)"
	                                  ng-bind="guajiLimitError"></span>
							</span>
	                </div>
                    <div class="rest-count col-lg-3 col-xs-4  col-sm-4 col-md-8" ng-show="operateType!='detail'&& (initPrintInfo.subServType=='1' || initPrintInfo.subServType=='3')" style="
							    line-height: 20px;
							    color: #999;
							    font-size: 9px;
							    margin-bottom: -10px;
							    max-width: 40%;
							">
                        <p>主叫彩印：<br>
                            1）只能展示身份，或在身份展示基础上可附带添加企业文化宣传（如：企业文化、企业创始年份、品牌宣传口号、与具体营销场景非强相关联的品牌理念等）或关怀语；<br>
                            2）严禁营销、商业信息（营销、商业内容界定包括但不限于：产品、业务范围、服务范围、业务咨询方式介绍等内容）（移动自有业务宣传经部门许可后，可酌情处理）。<br>
                            “范例”：【xx快递】快递员张三给您来电，请您接听。<br>
                            【需提供依据材料】<br>
                            1、涉及党政军、事业单位的，需提供授权或协议作为审核依据<br>
                            2、涉及银行业务金额、利率等的彩印内容，须提供银行授权<br>
                            3、涉及敏感行业的，需提供营业执照<br>
                            4、申请变量模版，需上传《内容承诺审批函》<br>
                            </p>
                    </div>
                    <div class="rest-count col-lg-3 col-xs-4  col-sm-4 col-md-8" ng-show="operateType!='detail'&& initPrintInfo.subServType=='2'" style="
							    line-height: 20px;
							    color: #999;
							    font-size: 9px;
							    margin-bottom: -10px;
							    max-width: 40%;
							">
                        <p>被叫彩印：可投递商业营销信息<br>
                            “范例”：【xx快递】感谢您的来电，祝您健康快乐。本公司承接全国快递服务，竭诚为您服务。<br>
                            【需提供依据材料】<br>
                            1、涉及党政军、事业单位的，需提供授权或协议作为审核依据<br>
                            2、涉及银行业务金额、利率等的彩印内容，须提供银行授权<br>
                            3、涉及敏感行业的，需提供营业执照<br>
                            4、申请变量模版，需上传《内容承诺审批函》<br>
                            </p>
                    </div>
                    <div class="rest-count col-lg-3 col-xs-4  col-sm-4 col-md-8" ng-show="operateType!='detail'&& initPrintInfo.subServType=='4'" style="
							    line-height: 20px;
							    color: #999;
							    font-size: 9px;
							    margin-bottom: -10px;
							    max-width: 40%;
							">
                        <p>挂机短信：可投递商业营销信息<br>
                            “范例”：【咪咕新空】彩印部感谢您的来电。彩印以手机屏显实现业务宣传，提供多种企业宣传方案，服务全国政企市场，覆盖三大运营商用户。<br>
                            【需提供依据材料】<br>
                            1、涉及党政军、事业单位的，需提供授权或协议作为审核依据<br>
                            2、涉及银行业务金额、利率等的彩印内容，须提供银行授权<br>
                            3、涉及敏感行业的，需提供营业执照<br>
                            4、申请变量模版，需上传《内容承诺审批函》<br>
                            </p>
                    </div>
	                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-8"
	                     ng-show="myForm.colorContent.$viewValue && myForm.colorContent.$valid && sensitiveWords[1].length>0">
	                    <p style="padding:10px 20px 10px 20px;color:red;margin-left:140px;">
	                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
	                             align="absmiddle">
	                        {{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[1]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}
	                    </p>
	                </div>
                </div>
               <div class="row" style="margin:0px">
	               	<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"></label>

                </div>
            </div>

            <!-- 内容类型之被叫内容 -->
            <div class="form-group callcontent-type" ng-show="initPrintInfo.subServType=='3' && !is5GEnterprise">
                <b ng-show="initPrintInfo.subServType=='3'" style="float: left;width: 100%;margin-left: 7.5%;margin-block: 3px;">被叫内容</b>

                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    {{'CONTENT_TYPE'|translate}}：
                </label>
                <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5 use-name" style="width: 300px;">
                    <li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li"
                        ng-click="changeCallContentType(0)"><span
                            class="check-btn redio-btn checked"> </span>
                        {{'CONTENT_TYPE_DIY'|translate}}
                    </li>
                    <li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li"
                        ng-click="changeCallContentType(1)"><span
                            class="check-btn redio-btn" style="position:relative"> </span>
                        {{'CONTENT_TYPE_YSGDMB'|translate}}
                    </li>
                </div>
                <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5 use-name">
                    <span style="color: #ff0000;margin-left: 5px">提示：选择预设固定模板时，不填写签名，则默认审核通过</span>
                </div>
            </div>
            <!-- 模板内容之被叫内容 -->
            <div class="form-group" ng-if="initPrintInfo.subServType=='3' && initPrintInfo.isCallRefYsmb==1">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'TEMPLATE_CONTENT'|translate"></span>：
                </label>
                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
                    <select ng-if="operateType!='detail'" class="form-control" ng-model="initPrintInfo.refYsmbCallID"
                            ng-options="x.id as x.name for x in ysmbCallChoise"
                            ng-disabled="operateType=='detail'"></select>
                    <p ng-if="operateType=='detail'" disabled class="form-control"
                       ng-bind="ysmbMap[initPrintInfo.refYsmbCallID]"></p>
                </div>
            </div>
            <!--主被叫彩印之被叫内容 add 20190905-->
            <div class="form-group" ng-if="initPrintInfo.subServType=='3' && initPrintInfo.isCallRefYsmb==0">
                <div class="row" style="margin:0px;display:flex;">
	                <!-- 彩印内容lable -->
	                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
	                    <span ng-bind="'CONTENTAUDIT_CALLED_CYCONTENT'|translate"></span>：
	                </label>
	                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
							<textarea style="height:100%;"
	                                  ng-class="{'ng-invalid':isSensitive[1] && myForm.colorCallContent.$viewValue}"
	                                  ng-if="initPrintInfo.subServType!='8'"
	                                  placeholder="{{(initPrintInfo.subServType == 4 || initPrintInfo.subServType == 4.1 || initPrintInfo.subServType == 4.2) ? guajiLimitContent :'请输入彩印内容1~70字'}}"
	                                  class="form-control"
	                                  rows="3" name="colorCallContent" ng-model="initPrintInfo.colorCallContent"
	                                  ng-blur="sensitiveCheck(initPrintInfo.colorCallContent,1)"
	                                  ng-disabled="operateType=='detail'"></textarea>
	                    <span style="color:red"
	                          ng-show="(myForm.colorCallContent.$dirty && myForm.colorCallContent.$invalid) || (checkCallSign == 1)">
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
	                                 align="absmiddle">
								<span ng-show="myForm.colorCallContent.$error.required&&initPrintInfo.subServType!='8'"
	                                  ng-bind="'CONTENT_COLORREQUIRE'|translate"></span>
								<span ng-show="(initPrintInfo.colorCallContent>maxSignLength && initPrintInfo.subServType!='8' && initPrintInfo.subServType!='4') || ((initPrintInfo.subServType!='8' && initPrintInfo.subServType!='4') && (checkCallSign == 1))"
	                                  ng-bind="'SMS_CONTENT_LENGTH_MAX_70'|translate"></span>
								<span ng-show="(initPrintInfo.colorCallContent>maxSignLength && initPrintInfo.subServType =='4') || ((initPrintInfo.subServType =='4') && (checkCallSign == 1))"
	                                  ng-bind="guajiLimitError"></span>
							</span>
	                </div>
                    <div class="rest-count col-lg-3 col-xs-4  col-sm-4 col-md-8" ng-show="operateType!='detail'&& initPrintInfo.subServType=='3'" style="
						    line-height: 20px;
						    color: #999;
						    font-size: 9px;
						    margin-bottom: -10px;
						    max-width: 40%;
						">
                        <p>被叫彩印：可投递商业营销信息<br>
                            “范例”：【xx快递】感谢您的来电，祝您健康快乐。本公司承接全国快递服务，竭诚为您服务。<br>
                            【需提供依据材料】<br>
                            1、涉及党政军、事业单位的，需提供授权或协议作为审核依据<br>
                            2、涉及银行业务金额、利率等的彩印内容，须提供银行授权<br>
                            3、涉及敏感行业的，需提供营业执照<br>
                            4、申请变量模版，需上传《内容承诺审批函》<br>
                            </p>
                    </div>
	                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-8"
	                     ng-show="myForm.colorCallContent.$viewValue && myForm.colorCallContent.$valid && sensitiveWords[1].length>0">
	                    <p style="padding:10px 20px 10px 20px;color:red;margin-left:140px;">
	                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
	                             align="absmiddle">
	                        {{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[1]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}
	                    </p>
	                </div>
	            </div>
		        <div class="row" style="margin:0px">
		        	<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"></label>

	             </div>
            </div>


            <!-- 挂机彩印 -->
            <div class="form-group" ng-show="initPrintInfo.subServType=='8'">
				<div style="overflow:hidden;margin:0px" class="row" ng-cloak>
                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'CONTENTAUDIT_CYCONTENT'|translate"></span>：
                    </label>
                    <div style="display: table;width: 83.3334%;" class="col-lg-6 col-xs-7 col-sm-7 col-md-7">

                        <div style="display: flex;width: 100%">
                            <div style="padding-top:7px;color:rgb(153,153,153);margin-right: 5%;" ng-show="colorContentAndPicList.length==0" class="col-lg-3">
                                <p ng-show="operateType!='detail'" ng-bind="'COMMON_ADDPICORTEXT_PLH'|translate" style="float: left"></p>
                                <p ng-show="operateType=='detail'" ng-bind="'COMMON_NO_CTN'|translate" style="float: left"></p>

                            </div>
                            <div style="padding-top: 7px;width: auto;margin-right: 20px;" ng-hide="colorContentAndPicList.length==0">
                                <div class="ctn-pic-list" ng-repeat="item in colorContentAndPicList">
                                    <div class="pic-wrapper" ng-if="item.framePicUrl" style="display: flex;align-items: center;">
                                        <img style="float:left;max-width: 250px;border: 1px solid darkgrey;"
                                             ng-src="{{item.formatFramePicUrl}}" alt="">
                                        <button ng-hide="operateType=='detail'" ng-click="deleteCtnOrPic($index)"
                                                type="submit" class="btn btn-primary search-btn "
                                                style="margin-left: 2rem;">
                                            <span ng-bind="'COMMON_DELETE'|translate"></span></button>
                                    </div>
                                    <div class="ctn-wrapper" ng-if="!item.framePicUrl" style="display: table;width:100%;">
                                        <div style="display: table-cell;">
										<textarea style="max-width: 270px;height: 148px;min-width: 250px;"
                                                  ng-class="{('ng-invalid invalid':isSensitive[2]&&myForm['txtContent'+$index].$viewValue)||ctnTextSumLength>750||myForm['txtContent'+$index].$invalid}"
                                                  placeholder="请输入彩印内容，总长度不能超过750位" class="form-control" rows="6"
                                                  name="{{'txtContent'+$index}}" ng-model="item.frameTxt" required
                                                  ng-blur="sensitiveCheck(ctnTextSum,2)"
                                                  ng-disabled="operateType=='detail'"></textarea>
                                        </div>
                                        <div style="display: table-cell;width: 64%;vertical-align: top;">
                                            <button ng-hide="operateType=='detail'" ng-click="deleteCtnOrPic($index)"
                                                    type="submit" class="btn btn-primary search-btn "
                                                    style="margin: 20px 20px;">
                                                <span ng-bind="'COMMON_DELETE'|translate"></span></button>
                                            <span style="color:red"
                                                  ng-show="(myForm['txtContent'+$index].$dirty && myForm['txtContent'+$index].$invalid)">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20"
                                                 align="absmiddle">
											<span ng-show="myForm['txtContent'+$index].$error.required"
                                                  ng-bind="'CONTENT_COLORREQUIRE'|translate"></span>
										</span>
                                            <!--110迭代：ng-show取消myForm['txtContent'+$index].$dirty &&-->
                                            <span style="color:red"
                                                  ng-show="sensitiveWords[2].length>0 &&item.frameTxt">
										<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                             align="absmiddle">
											<span>
											{{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[2]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}</span>
										</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="rest-count col-lg-3 col-xs-4  col-sm-4 col-md-8" ng-show="operateType!='detail'" style="
										    line-height: 20px;
										    color: #999;
										    font-size: 9px;
										    margin-bottom: 10px;
										    float: right;
										    min-width: 30%;
										    display: inline-block;
										    margin-top: 14px;
										">
                                <p style="float: right;height:100%;width: 100%;line-height: 20px;color: #999;font-size: 9px;">
                                    挂机彩信：可投递商业营销信息<br>
                                    “范例”：【咪咕新空】彩印部感谢您的来电。彩印以手机屏显实现业务宣传，提供多种企业宣传方案，服务全国政企市场，覆盖三大运营商用户。<br>
                                    【需提供依据材料】<br>
                                    1、涉及党政军、事业单位的，需提供授权或协议作为审核依据<br>
                                    2、涉及银行业务金额、利率等的彩印内容，须提供银行授权<br>
                                    3、涉及敏感行业的，需提供营业执照<br>
                                    4、申请变量模版，需上传《内容承诺审批函》<br>
                                    </p>
                            </div>
                        </div>


                    </div>
                    <!-- 多个彩印内容的敏感词校验提示 -->
                    <!-- <div class="col-lg-3 col-xs-3  col-sm-3 col-md-3" ng-show="sensitiveWords[2].length>0">
                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                            align="absmiddle">
                        {{'CONTENT_DETECTION'|translate}}{{sensitiveWordsStr[2]}}{{'CONTENT_ISSENSITIVEWORDS'|translate}}
                    </div> -->
                </div>
                <!-- 添加图片和添加文本 -->
                <div style="overflow:hidden;">
                    <p ng-show="operateType!='detail'"
                       class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                       style="color:red;padding-left: 15px;" ng-bind="'COMMON_CONTENTPICTEXT_LIMITTIP3'|translate">
                    </p>
                    <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                       ng-show="(!inArray(initPrintInfo.subServType))&&((ctnTextSumLength+initPrintInfo.signature.length)>750)" style="color:red;">
                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
                        <span ng-bind="'CONTENT_TEXT_MAXLENGTHTIP_SIGNATURE'|translate"></span>
                    </p>
                    <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                       ng-show="(inArray(initPrintInfo.subServType))&&(ctnTextSumLength>750)" style="color:red;">
                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
                        <span ng-bind="'CONTENT_TEXT_MAXLENGTHTIP'|translate"></span>
                    </p>
                    <p class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                       ng-show="allPicSize>286720" style="color:red;">
                        <img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                             align="absmiddle">
                        <span ng-bind="'CONTENT_FILESIZE_MAXTIP'|translate"></span>
                    </p>
                 </div>
                 <div style="overflow:hidden;margin:0px" class="row">
                 	<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"></label>
                    <!--<div class="rest-count col-lg-3 col-xs-4  col-sm-4 col-md-8" ng-show="operateType!='detail'" style="
										    line-height: 20px;
										    color: #999;
										    font-size: 9px;
										    margin-bottom: 10px;
										">
					                    <p>挂机彩信：可投递商业营销信息<br>
					                   	 “范例”：【咪咕动漫】彩印部感谢您的来电。彩印以手机屏显实现业务宣传，提供多种企业宣传方案，服务全国政企市场，覆盖三大运营商用户。<br>
					                                                             【需提供依据材料】<br>
					                    1、涉及党政军、事业单位的，需提供授权或协议作为审核依据<br>
					                    2、涉及银行业务金额、利率等的彩印内容，须提供银行授权<br>
					                    3、涉及敏感行业的，需提供营业执照<br>
					                    4、申请变量模版，需提交《内容承诺审批函》报备<br>
					                                                            以上材料请发送咪咕资质审核邮箱：<EMAIL></p>
					                </div>-->
                    <div ng-show="operateType!='detail'"
                         class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview" style="margin-left: 0;">
                        <cy:uploadify ng-show="picLength<5"
                                      ng-if="initPrintInfo.subServType=='8'&&operateType !='detail'"
                                      filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
                                      uploadifyid="uploadifyid1" validate="isValidate" filesize="filesize"
                                      mimetypes="mimetypes" formdata="uploadParam" uploadurl="uploadurl" desc=""
                                      numlimit="numlimit" createthumbnail="isCreateThumbnail" style="float: left;">
                        </cy:uploadify>
                        <!-- 图片最大张数时展示的上传图片框 -->
                        <button disabled style="float:left;margin-right: 15px;"
                                ng-show="initPrintInfo.subServType=='8'&&picLength>4" type="submit"
                                class="btn btn-primary search-btn">
                            <icon class="file-icon" style="margin-top: -4px;float: left;"></icon>
                            <span style="float:left" ng-bind="'点击上传图片'|translate"></span></button>
                        <button ng-disabled="ctnTextMount>=5" ng-click="addTextCtn()" style="float:left"
                                ng-if="initPrintInfo.subServType=='8'&&operateType !='detail'" type="submit"
                                class="btn btn-primary search-btn">
                            <span ng-bind="'CONTENT_CLICK_ADDTEXT'|translate"></span></button>
                    </div>
                </div>

            </div>

            <!-- 黑白名单 -->
            <div class="form-group black-white">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    {{'BLACKWHITE_LIST'|translate}}：
                </label>
                <div class="col-lg-4 col-xs-5  col-sm-5 col-md-5 use-name">
                    <li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li"
                        ng-click="changeBlackwhiteListType()"><span
                            class="check-btn redio-btn checked"> </span> {{'COMMON_NO_USE'|translate}}
                    </li>
                    <li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li"
                        ng-click="changeBlackwhiteListType()"><span
                            class="check-btn redio-btn" style="position:relative"> </span>
                        {{'COMMON_USE_BLACK'|translate}}
                        <div ng-show="(initPrintInfo.blackwhiteListType == 1) && (showBlack == false) && (operateType !='detail')"
                             style="position: absolute;top: 20px;color:red;">{{'NO_USE_BLACK'|translate}}
                        </div>
                    </li>
                    <li ng-class="{'cursor-def':operateType=='detail'}" class="redio-li"
                        ng-click="changeBlackwhiteListType()"><span
                            class="check-btn redio-btn" style="position:relative"> </span>
                        {{'COMMON_USE_WHITE'|translate}}
                        <div ng-show="(initPrintInfo.blackwhiteListType == 2) && (showWhite == false) && (operateType !='detail')"
                             style="position: absolute;top: 20px;color:red;">{{'NO_USE_WHITE'|translate}}
                        </div>
                    </li>
                </div>
            </div>

            <!-- 运营商 -->
            <div class="form-group platforms" ng-show="!inArray(initPrintInfo.subServType)">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'PLATFORM'|translate"></span>：
                </label>
                <div class="col-lg-10 col-xs-9  col-sm-10 col-md-10" ng-click="checkSignatureRequired()">
                    <li class="check-li">
                        <span class="check-btn checked-btn"> </span>
                        {{'MOBILE'|translate}}
                    </li>
                    <li class="check-li">
                        <span class="check-btn checked-btn"> </span>
                        {{'UNICOM'|translate}}
                    </li>
                    <li class="check-li">
                        <span class="check-btn checked-btn"> </span>
                        {{'TELECOM'|translate}}
                    </li>
                    <cy:icon:content content-type="5">
                    </cy:icon:content>
                </div>
            </div>
            <!-- 场景描述 -->
            <div class="form-group platforms" ng-show="(initPrintInfo.subServType == '4' || initPrintInfo.subServType == '4.1' || initPrintInfo.subServType == '4.2') && isSceneDesc =='1'">
                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span style="color:red"
                                  ng-bind="'*'|translate">
							</span>
                    <span ng-bind="'SENCE_DESC'|translate"></span>：</label>
                 <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
							<textarea style="height:80px;"
	                                  placeholder="请输入场景描述1~128字"
	                                  class="form-control"
	                                  rows="3" name="sceneDesc" ng-model="initPrintInfo.sceneDesc"
	                                  ng-maxlength="128"
	                                  ng-required="(initPrintInfo.subServType == '4' || initPrintInfo.subServType == '4.1' || initPrintInfo.subServType == '4.2') && isSceneDesc =='1'"
	                                  ng-blur="sensitiveCheck(initPrintInfo.sceneDesc,1)"
	                                  ng-disabled="operateType=='detail'"></textarea>
	                    <span style="color:red"
	                          ng-show="myForm.sceneDesc.$dirty && myForm.sceneDesc.$invalid">
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
	                                 align="absmiddle">
								<span ng-show="myForm.sceneDesc.$error.required&&(initPrintInfo.subServType=='4.1')"
	                                  ng-bind="'CALLER_SCENEDESC_REQUIRE'|translate"></span>
                            <span ng-show="myForm.sceneDesc.$error.required&&(initPrintInfo.subServType=='4.2')"
                                  ng-bind="'CALLED_SCENEDESC_REQUIRE'|translate"></span>
								<span ng-show="myForm.sceneDesc.$error.maxlength"
	                                  ng-bind="'SCENEDESC_WORDSLIMIT_256'|translate"></span>
							</span>
	                </div>
            </div>
            <!--REQ-113 -->
            <!-- 签名 -->
            <div class="form-group platforms" ng-show="!inArray(initPrintInfo.subServType)">
                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
							<span style="color:red"
                                  ng-bind="'*'|translate"
                                  ng-model="signatureRequired"
                                  ng-show="(signatureRequired =='1' && noSign == '1')" >
							</span>
                    <span ng-bind="'SIGNATURE'|translate"></span>：</label>
                <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4">
                    <input type="text" class="form-control" placeholder="请输入1~67字" rows="1" name="signature"
                           ng-model="initPrintInfo.signature"
                           ng-required="(signatureRequired==='1'&&noSign == '1')"
                           maxlength="67"
                           ng-disabled="operateType=='detail'"/>
                    <span style="color:red" ng-show="myForm.signature.$invalid&&myForm.signature.$dirty">
							<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                 align="absmiddle">
							<span style="color:red" ng-show="myForm.signature.$error.required"
                                  ng-bind="'SIGNATURE_REQUIRED'|translate"></span>
							<span style="color:red"
                                  ng-show="myForm.signature.$error.pattern&&!myForm.signature.$error.maxlength"
                                  ng-bind="'SIGNATURE_PATTERN_ERROR'|translate"></span>
							<span style="color:red" ng-show="myForm.signature.$error.maxlength"
                                  ng-bind="'SIGNATURE_MAXLENTH_67'|translate"></span>
						</span>
                </div>
                <cy:icon:content content-type="4">
                </cy:icon:content>
            </div>
            <!-- 所属行业 -->
            <div class="form-group industry">
                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'ENTERPRISE_INDUSTRY'|translate"></span>：</label>
                <div class="col-lg-3 col-xs-4 col-sm-4 col-md-4">
                    <select class="form-control"
                            name="industry1"
                            required
                            ng-model="selectedIndustry"
                            ng-options="x.industryName for x in industryList"
                            ng-change="changeIsSensitive(selectedIndustry)"
                            ng-show="operateType =='add'||operateType =='detail'||operateType =='modify'"
                            ng-disabled="operateType =='detail'"
                    >
                        <option value="" ng-bind="" ng-show="operateType !='detail'"></option>
                    </select>
                    <span ng-if="isSensitiveIndustry=='1' && operateType !='detail' && selectedIndustry.industryName == '金融/银行/保险/会计'" style="color:#c3c3c3" class="redFont">
                        {{selectedIndustry.industryName}}必须上传营业执照，涉及银行业务金额、利率等的彩印内容，须提供银行授权
                    </span>
                    <span ng-if="isSensitiveIndustry=='1' && operateType !='detail' && selectedIndustry.industryName != '金融/银行/保险/会计'" style="color:#c3c3c3" class="redFont">
                        {{selectedIndustry.industryName}}必须上传营业执照
                    </span>
                    <span ng-if="isSensitiveIndustry=='2' && operateType !='detail'" style="color:#c3c3c3" class="redFont">{{selectedIndustry.industryName}}必须上传授权函或业务协议</span>
                    <span ng-if="isSensitiveIndustry=='3' && operateType !='detail'" style="color:#c3c3c3" class="redFont">{{selectedIndustry.industryName}}必须上传营业执照和版号证明</span>
                    <span style="color:red" class="uplodify-error-img" ng-show="selectedIndustryErrorInfo"></span>
                    <span style="color:red" class="redFont" ng-bind="selectedIndustryErrorInfo|translate"
                          ng-show="selectedIndustryErrorInfo"></span>
                </div>
            </div>

            <!-- 其他资质 -->
            <div class="form-group">
                <div style="margin:0px" class="row" ng-cloak>
                    <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                        <icon ng-show="isSensitiveIndustry =='2' || isSensitiveIndustry =='3'">*</icon><span ng-bind="'ENTERPRISE_CERTIFICATEURLLIST'|translate"></span>：
                    </label>
                    <div style="display: table;" class="col-lg-6 col-xs-7 col-sm-7 col-md-7">
                        <div class="ctn-pic-list" ng-repeat="item in colorContentAndFileList">
                            <div class="pic-wrapper" ng-if="item.frameFileUrl">
                                <!--                                <img style="float:left;max-width: 250px;border: 1px solid darkgrey;"-->
                                <!--                                     ng-src="{{item.formatFrameFileUrl}}" alt="">-->
                                <a ng-click="item.frameFileUrl?exportFile(item.frameFileUrl):false"
                                   title="{{item.filename}}" style="display: inline-block;width: 285px;{{operateType!='detail' ? 'overflow: hidden;' :''}};white-space: nowrap;text-overflow: ellipsis;"
                                   ng-style="" class="ng-binding">
                                    {{item.filename}}</a>
                                <button ng-hide="operateType=='detail'" ng-click="deleteCtnOrFile($index)"
                                        type="submit" class="btn btn-primary search-btn "
                                        style="position: absolute;left: 290px;top:50%;margin-top: -17px;">
                                    <span ng-bind="'COMMON_DELETE'|translate"></span></button>
                            </div>
                        </div>
                        <!--                        <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label"></label>-->
                        <div ng-show="operateType!='detail'" style="margin-left: 0;padding-left: 0;" ng-required="isSensitiveIndustry=='2' || isSensitiveIndustry =='3'"
                             class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview">
                            <cy:uploadify ng-show="fileLength<6" ng-if="operateType !='detail'"
                                          filelistid="fileList2" filepickerid="filePicker_certi" accepttype="accepttype2"
                                          uploadifyid="uploadifyid2" validate="isValidate2" filesize="filesize2"
                                          mimetypes="mimetypes2" formdata="uploadParam2" uploadurl="uploadurl2" desc="uploadCertiDesc"
                                          numlimit="numlimit2" createthumbnail="isCreateThumbnail2" style="float: left;">
                            </cy:uploadify>
                            <!-- 图片最大张数时展示的上传图片框(只用于展示，没有作用) -->
                            <button ng-required="isSensitiveIndustry=='2' || isSensitiveIndustry =='3'" disabled style="float:left;margin-right: 15px;"
                                    ng-show="fileLength>5" type="submit"
                                    class="btn btn-primary search-btn">
                                <span style="float:left" ng-bind="'上传文件'|translate"></span>
                            </button>
                            <br>
                            <div style="color: #c3c3c3;" ng-show="fileLength>5" class="ng-binding">
                                最多支持6张图片，仅支持jpg，bmp，png，jpeg格式
                            </div>
							<div class="downloadRow col-sm-12">
								<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】号码使用声明书.docx" style="margin-left: -15px;"
									 ng-bind="'NUMBER_USE_TEMP_DOWNLOD'|translate"></a>
							</div>
<!--							<div class="downloadRow col-sm-12">-->
<!--								<a target="_blank" href="/qycy/ecpmp/assets/【联通电信】品牌归属声明书.docx" style="margin-left: -15px;"-->
<!--									 ng-bind="'BRAND_ATTR_TEMP_DOWNLOD'|translate"></a>-->
<!--							</div>-->
							<div class="downloadRow col-sm-12">
								<a target="_blank" href="/qycy/ecpmp/assets/签名授权书-移动模板.docx" style="margin-left: -15px;"
									 ng-bind="'SIGN_AUTH_TEMP_DOWNLOD'|translate"></a>
							</div>
                        </div>
                        <cy:icon:content content-type="6">
                        </cy:icon:content>
                    </div>
                </div>
            </div>

            <!-- 营业执照 -->
            <div class="form-group industry">

                <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span style="color:red"
                                      ng-bind="'*'|translate"
                                      ng-model="isSensitiveIndustry"
                                      ng-show="isSensitiveIndustry =='1' || isSensitiveIndustry =='3'">
								</span>
                    <span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span>：</label>

                <cy:uploadify filelistid="fileList_" filepickerid="filePicker_" accepttype="accepttype_"
                              uploadifyid="uploadifyid_1" validate="isValidate_" filesize="filesize_"
                              mimetypes_="mimetypes_"
                              formdata="uploadParam_" uploadurl="uploadurl_" desc="uploadDesc_" numlimit="numlimit_"
                              urllist="urlList_" createthumbnail="isCreateThumbnail_" namelistid="nameList_"
                              class="col-lg-5 col-xs-5 col-sm-5 col-md-5" ng-if="pictureComplete">
                </cy:uploadify>
                <input class="form-control" name="businessLicenseURL_" ng-model="businessLicenseURL_"
                       ng-required="isSensitiveIndustry=='1' || isSensitiveIndustry =='3'" ng-hide="true">
                <img ng-src="{{fileUrl_}}" width="100" height="100" align="absmiddle"
                     ng-if="operateType=='detail'&&fileUrl_" style="margin-left:15px">

            </div>


            <div class="form-group">
                <div ng-show="urlList_2.length>0"
                     class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2
								col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview">
                    <ptl-preview style="float:left;margin-left:-5px;" urllist='urlList_2'></ptl-preview>
                </div>
            </div>


            <div class="form-group pushDate">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'CONTENT_PUSH_DATE'|translate"></span>：
                </label>
                <div class="col-lg-10 col-xs-9  col-sm-10 col-md-10">
                    <li class="check-li islimit1"><span class="check-btn checked-btn checked"> </span>
                        {{'ENTERPRISE_NOLIMITED'|translate}}
                    </li>
                    <li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_MONDAY'|translate}}
                    </li>
                    <li class="check-li"><span class="check-btn checked-btn"> </span>
                        {{'CONTENT_TUESDAY'|translate}}
                    </li>
                    <li class="check-li"><span class="check-btn checked-btn"> </span>
                        {{'CONTENT_WEDNESDAY'|translate}}
                    </li>
                    <li class="check-li"><span class="check-btn checked-btn"> </span>
                        {{'CONTENT_THURSDAY'|translate}}
                    </li>
                    <li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_FRIDAY'|translate}}
                    </li>
                    <li class="check-li"><span class="check-btn checked-btn"> </span>
                        {{'CONTENT_SATURDAY'|translate}}
                    </li>
                    <li class="check-li"><span class="check-btn checked-btn"> </span> {{'CONTENT_SUNDAY'|translate}}
                    </li>
                </div>
            </div>
            <!-- 使用订单 -->
            <div class="form-group useOrder">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'BILLING_WAY'|translate"></span>：
                </label>
                <div class="col-lg-10 col-xs-9  col-sm-10 col-md-10">
                    <li ng-class="{'cursorDefault':operateType!='add'}" ng-click="strip()" class="redio-li"><span
                            class="check-btn redio-btn checked"> </span>{{'CONTENT_BILLINGBYITEM'|translate}}
                    </li>
                    <li ng-class="{'cursorDefault':operateType!='add'}"
                        class="redio-li" ng-click="mont()">
							<span class="check-btn redio-btn mont">
							</span> {{'CONTENT_BILLINGBYMONTH'|translate}}
                    </li>
                    <div ng-show="package" style="display: inline-block;vertical-align: bottom;">
                        <div style="display: table;">
                            <p style="display: table-cell;">套餐包：</p>
                            <select class="form-control" ng-model="initproduct.productName"
                                    ng-options="x.id as x.name for x in  pakageChoise" ng-change="changeproduct()"
                            ></select>
                            <!-- <p  disabled class="form-control"
                                ng-bind="statusMap[initPrintInfo.subServType]"></p> -->
                        </div>
                    </div>

                   <input type="text" ng-show="operateType!='add' && showpaksge" ng-disabled="true"
                           ng-model="initproduct.productName" class="form-control"
                           style="display: inline-block;vertical-align: bottom;width: 230px;"/>
                   <!--    <span ng-show="initPrintInfo.subServType=='1'||initPrintInfo.subServType=='2'">(剩余配额：{{currentRemainQuota}})</span>
                    <span>(剩余配额：{{currentRemainQuota}})</span> -->
                </div>
            </div>
            <!--    配置对象-->
            <div class="form-group pushObj">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                    <icon>*</icon>
                    <span ng-bind="'CONTENT_PUSHOBJ'|translate"></span>：
                </label>

                <button type="submit" ng-if="operateType!='detail'&&OrganizationListTotal>50"
                        class="btn btn-primary search-btn ng-scope"
                        ng-click="groupListPop()">
                    <span class="ng-binding">添加配置对象</span></button>


                <!--分组小于50个时-->
                <div ng-if="OrganizationListTotal <= 50" class="col-lg-10 col-xs-9  col-sm-10 col-md-10">
                    <li class="check-li islimit2">
                        <span class="check-btn checked-btn checked">
                        </span>
                        {{'ENTERPRISE_NOLIMITED'|translate}}
                    </li>
                    <li id="groupListCls" class="check-li" ng-repeat="item in orgList" title="{{item.orgName}}({{item.memberCount||0}})人" on-repeat-finished-render>
                        <span class="check-btn checked-btn" title="{{item.orgName}}({{item.memberCount||0}})人">
                        </span>
                        {{item.orgName}}({{item.memberCount||0}})人
                    </li>
                </div>


                <!--分组大于于50个时-->
                <div ng-if="OrganizationListTotal > 50" class="col-lg-10 col-xs-9  col-sm-10 col-md-10" style="width: 842px;margin-left: 138px;">
                    <li style="margin: 10px;width: 190px;line-height: 20px" class="check-li-other islimit2" ng-if="isNoLimit">
                        </span>{{'ENTERPRISE_NOLIMITED'|translate}}
                    </li>
                    <div ng-show="checkOrganizationList.length<=50">
                        <li style="float: left;margin: 10px;height: 26px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;width: 140px;line-height: 20px"
                                class="check-li-checked" ng-if="!isNoLimit"
                            ng-repeat="item in checkOrganizationList" title="{{item.orgName}}({{item.memberCount||0}})人">
                            <span title="{{item.orgName}}({{item.memberCount||0}})人">
                            </span>{{item.orgName}}({{item.memberCount||0}})人
                        </li>
                    </div>

                    <div ng-show="checkOrganizationList.length>50 && !isNoLimit" >
                        <li style="float: left;margin: 10px;height: 26px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;width: 140px;line-height: 20px;"
                                class="check-li-checked"
                            ng-repeat="item in checkOrganizationListTemp" title="{{item.orgName}}({{item.memberCount||0}})人">
                            <span title="{{item.orgName}}({{item.memberCount||0}})人"></span>
                            {{item.orgName}}({{item.memberCount||0}})人
                        </li>
                        <ptl-page style="width: 772px" tableId="1" change="queryCheckedOrg (pageInfo)"></ptl-page>
                    </div>
                </div>
            </div>
            <div class="form-group pushObj" ng-if="(isSubEnterpirse || isSuperManager) && operateType !='detail'">
                <label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                </label>
                <div class="col-lg-5 col-xs-5  col-sm-5 col-md-5" >
                    <div class="col-lg-6 col-xs-6 col-sm-6 col-md-6" style="padding-left:0" >
                        <input type="text" class="form-control" ng-model="fileNamePushObjTemp" id="downPushObjTemp"
                               placeholder="请导入.xlsx表格格式文件" style="width: 100%;"  />
                    </div>
                    <cy:uploadifyfile filelistid="fileListPushObjTemp" filepickerid="filePickerPushObjTemp" accepttype="accepttypePushObjTemp"
                                      uploadifyid="uploadifyidPushObjTemp" validate="isValidatePushObjTemp" filesize="filesizePushObjTemp"
                                      mimetypes="mimetypesPushObjTemp" formdata="uploadParamPushObjTemp" uploadurl="uploadurlPushObjTemp"
                                      desc="uploadDescPushObjTemp" numlimit="numlimitPushObjTemp"
                                      urllist="urlListPushObjTemp" createthumbnail="isCreateThumbnailPushObjTemp"
                                      auto="autoPushObjTemp" style="margin-left: 15px;float: left;"
                                      >
                    </cy:uploadifyfile>
                    <div class="downloadRow col-sm-10" style="width:100%;margin-top:20px;" >
                        <a target="_blank" href="/qycy/ecpmp/assets/importOrgByName.xlsx" class="downMod" style="margin-right: 40px;"
                           ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
                        <span style="color: #705de1 !important; font-size: 12px;">提示：</span>
                        <span style="color: #705de1 !important; font-size: 12px;" ng-bind="'TEMPLATE_IMPORTTIP'|translate"></span>
                        <button class="btn btn-back" ng-bind="'TEMPLATE_DELETE'|translate" ng-click="deleteTemplate()" style="margin-left: 40px;"></button>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="">
                    <label style="height:34px;padding-top:0px;"
                           class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'CONTENT_PUSH_TIME'|translate"></span>：
                    </label>
                    <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 time">
                        <input onfocus="this.blur()" autocomplete="off" type="text" id="time-config" name="time"
                               ng-model="time" class="form-control" ng-disabled="operateType=='detail'" required>
                        <i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
                        <span style="color:red" ng-show="(myForm.time.$dirty && myForm.time.$invalid)||timeError">
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                     align="absmiddle">
								<span ng-show="myForm.time.$error.required"
                                      ng-bind="'CONTENT_PUSHTIMEREQUIRE'|translate"></span>
								<span ng-show="timeError" ng-bind="'CONTENT_TIMEERRORTIP'|translate"></span>
							</span>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="order-btn row">
    <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
         style="padding-left: 30px;">
        <!-- 110迭代：myForm.$invalid 取消 ||isSensitive[0]||isSensitive[1]||isSensitive[2] -->
        <button type="submit" ng-if="operateType!='detail'" class="btn btn-primary search-btn"
                ng-disabled="(checkSign == 1)||(checkCallSign == 1)||myForm.$invalid||timeError||(colorContentAndFileList.length==0&&(isSensitiveIndustry=='2' || isSensitiveIndustry =='3'))||
					(colorContentAndPicList.length==0&&initPrintInfo.subServType == 8)||orgList.length<=0||!chosePushObj||ctnTextSumLength>750||allPicSize>286720
					||((initPrintInfo.blackwhiteListType == 1) && (showBlack == false)) || ((initPrintInfo.blackwhiteListType == 2) && (showWhite == false))
					||(pushObjArrTemp.indexOf('1') == -1)
                    ||(OrganizationListTotal > 50 && checkOrganizationList.length<=0)
                    ||(platforms == '000')
                    "
                ng-click="diffNetAuthMaterialsConfirm()">
            <span ng-bind="'COMMON_SUBMIT'|translate"></span></button>
        <button type="submit" class="btn btn-back" ng-click="goBack()"><span
                ng-bind="'COMMON_BACK'|translate"></span></button>
    </div>
</div>
</div>

<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm model-lg model-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p ng-show="tip" style='font-size: 16px;color:#383838'>
                        {{tip|translate}}
                    </p>
                    <p ng-show="noRepeat" style='font-size: 16px;color:#383838;word-break: break-all;'>
                        <!-- 你选择的分组xxx已经在其他内容中关联了，请重新选择 -->
                        <!-- {{tip|translate}} -->
                        {{'CONTENT_CHOSENGROUP'|translate}}{{failOrgName}}{{'CONTENT_HASRELATED'|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>

<!-- 配额校验弹出层 -->
<div class="modal fade bs-example-modal-sm" id="myModal1" tabindex="-1" role="dialog" style="width: 300px !important;"
     aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm model-lg model-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <!-- 新增 -->
                    <p ng-show="(memberNum||memberRemainQuota)&& (memberNum>memberRemainQuota)&&operateType=='add'"
                       style='font-size: 16px;color:#383838'>
                        <!-- 当前配额不足，已选中成员{{memberNum}}人，当前可用配额{{memberRemainQuota}}人 -->
                        {{'CONTENT_CURRENTQUOTAREMAINSHORT'|translate}}{{memberNum}}
                        {{'CONTENT_CURRENTQUOTAUSEABLE'|translate}}{{memberRemainQuota}}{{'CREATEORDER_PRESON'|translate}}
                    </p>
                    <!-- 修改 -->
                    <p ng-show="(memberNum||memberRemainQuota)&& (memberNum>memberRemainQuota)&&operateType=='modify'"
                       style='font-size: 16px;color:#383838'>
                        <!-- 当前配额不足，本次新增成员{{memberNum}}人，当前可用配额{{memberRemainQuota}}人 -->
                        {{'CONTENT_SHORTOFQUOTE'|translate}}{{memberNum}}
                        {{'CONTENT_CURRENTQUOTAUSEABLE'|translate}}{{memberRemainQuota}}{{'CREATEORDER_PRESON'|translate}}
                    </p>
                    <p ng-show="remainQuota<=0" style='font-size: 16px;color:#383838'>
                        <!-- 当前配额不足，请下单后再创建内容 -->
                        {{'CONTENT_CURRENTQUOTAREMAIN'|translate}}
                    </p>
                </div>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>


<!--分组弹出框-->
<div class="modal fade" id="groupListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div role="document" class="modal-dialog dialog-1000" style="width: 760px;">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" ng-bind="'COMMON_GROUPMANAGE'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="form-horizontal">
                    <div class="form-group">
                        <div class="row">
                            <div class="col-lg-5 col-xs-6 col-sm-6 col-md-5" style=" width: 30%;">
                                <input type="text" class="form-control"
                                       placeholder="{{'GROUP_NAME_CONTENT'|translate}}"
                                       ng-model="groupName">
                            </div>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
                                <button class="btn bg_purple search-btn btn1" ng-click="queryOrg(pageInfo)">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_SEARCH'|translate"></span>
                                </button>
                            </div>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
                                <button class="btn bg_purple search-btn btn1" ng-click="submitSelect()">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_OK'|translate"></span>
                                </button>
                            </div>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
                                <button class="btn bg_purple search-btn btn1" ng-click="groupSelectAll()">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_SELECT_ALL'|translate"></span>
                                </button>
                            </div>
                            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2" style=" width: 12%;">
                                <button class="btn bg_purple search-btn btn1" ng-click="resetGroupSelect()">
                                    <span class="icon btnIcon search"></span>
                                    <span ng-bind="'COMMON_RESET'|translate"></span>
                                </button>
                            </div>
                            <span style="color: red">清空所有分页选项</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="coorPeration-table" style="max-height: 530px;overflow: auto">
                <div class="pushObj max">
                    <li style="margin: 10px;width: 130px" class="check-li">
                        <span style="float: left;margin-right: 6px;width: 20px;height: 20px; "class="check-btn checked-btn  isLimit" value="all">
							</span>
                        <div style="width: 97px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">{{'ENTERPRISE_NOLIMITED'|translate}}</div>
                    </li>

                    <li style="float: left;margin: 10px;height: 26px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;width: 130px;" class="check-li group-info" ng-repeat="item in orgList" on-repeat-finished-render title="{{item.orgName}}({{item.memberCount||0}})人">
                        <span style="float: left;margin-right: 6px;width: 20px;height: 20px;" class="check-btn checked-btn" value="{{item.id}}" title="{{item.orgName}}({{item.memberCount||0}})人">

                    </span>
                        <div style="float: left;width: 97px;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">{{item.orgName}}({{item.memberCount||0}})人</div>
                    </li>
                </div>
            </div>

            <div>
                <ptl-page tableId="0" change="queryOrg (pageInfo, 'justPage')"></ptl-page>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>

</div>

<!--提交异网授权材料二次确认弹出框-->
<div class="modal fade" id="diffNetAuthMaterials" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
	<div class="modal-dialog" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
			</div>
			<div class="modal-body">
				<form class="form-horizontal">
					<div class="form-group">
						<div class="row" style="width: 400px;position: relative;transform: translateX(-50%);left: 50%;margin-left: 0;">
							<div class="text-center">
								<span ng-bind="'DIFFNET_AUTH_MATERIALS'|translate"></span>
							</div>
						</div>
					</div>
				</form>
			</div>
			<div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn btn-primary search-btn" ng-bind="'COMMON_UPLOADED'|translate"
                        ng-click="diffNetAuthMaterialsUploaded()"></button>
                <button type="submit" id="diffNetAuthMaterialsCancel" class="btn" data-dismiss="modal"
                        aria-label="Close" ng-bind="'COMMON_NOT_UPLOAD'|translate"></button>
			</div>
		</div>
	</div>
</div>

</div>
</div>
</body>
<style>

    button.preview {
        float: left;
        margin-left: 30px;
    }

    .clear-preview {
        padding-left: 15px;
    }

    .cursorDefault {
        cursor: default !important;
    }

    .ng-dirty.ng-invalid {
        border-color: red;
    }

    .ng-dirty.invalid {
        border-color: red;
    }

    #filePicker div:nth-child(2) {
        width: 100% !important;
        height: 100% !important;
    }
</style>


</html>
