spring:
  application:
    name: ecy-zuul-enterprise-api
  config:
    import: optional:configserver:${CONFIG_SERVER_URL:http://localhost:8888}
  cloud:
    config:
      name: caiyinConfig
      enabled: true
      label: master
      profile: kafka
      discovery:
        enabled: true
        service-id: cy-config-server

eureka:
  client:
    region: region1
    availability-zones:
      region1: cy_eureka
    service-url:
      cy_eureka: http://127.0.0.1:19000/eureka/
  instance:
    metadata-map:
      cluster: main
    prefer-ip-address: true
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
