spring:
  application:
    name: cy-eureka
server:
  port: 19000
eureka:
  instance:
    appname: ${spring.application.name}
    metadata-map:
      cluster: main
    prefer-ip-address: false
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    lease-expiration-duration-in-seconds: 90
  client:
  #  集群配置
    use-dns-for-fetching-service-urls: false
    prefer-same-zone-eureka: true
    fetch-registry: true
    register-with-eureka: true
    filter-only-up-instances: true
    region: region1
    availability-zones:
      region1: cy_eureka
    service-url:
      cy_eureka: http://127.0.0.1:19000/eureka/
#      cy_eureka: http://${eureka.instance.hostname}:${server.port}/eureka/,http://${eureka-rs1.hostname}:${eureka-rs1.port}/eureka/,http://${eureka-rs2.hostname}:${eureka-rs2.port}/eureka/
  server:
    enable-self-preservation: false
    eviction-interval-timer-in-ms: 30000
endpoints:
  shutdown:
    enabled: true
    sensitive: false