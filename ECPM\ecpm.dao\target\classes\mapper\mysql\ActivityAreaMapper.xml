<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityAreaMapper">
	<resultMap id="activityAreaWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivityAreaWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer" />
		<result property="activityID" column="activityID" javaType="java.lang.Integer" />
		<result property="provinceID" column="provinceID" javaType="java.lang.String" />
		<result property="cityID" column="cityID" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="Date" />
		<result property="updateTime" column="updateTime" javaType="Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
	</resultMap>

	<select id="getAreaByActivityID" resultMap="activityAreaWrapper">
		select ID,
		activityID,
		provinceID,
		cityID,
		createTime,
		updateTime,
		operatorID
		from ecpm_t_activity_area
		<trim prefix="where" prefixOverrides="and|or">
			<if test="activityID != null">
				activityID=#{activityID}
			</if>
		</trim>
	</select>

	<insert id="createActivityArea">
		insert into ecpm_t_activity_area
		(
		activityID,
		provinceID,
		cityID,
		createTime,
		updateTime,
		operatorID
		)
		values
		<foreach collection="list" item="activityAreaWrapper"
			separator=",">
			(
			#{activityAreaWrapper.activityID},
			#{activityAreaWrapper.provinceID},
			#{activityAreaWrapper.cityID},
			#{activityAreaWrapper.createTime},
			#{activityAreaWrapper.updateTime},
			#{activityAreaWrapper.operatorID}
			)
		</foreach>
	</insert>

	<delete id="deleteAreaByActivityID">
		delete from ecpm_t_activity_area where activityID =
		#{activityID}
	</delete>


</mapper>