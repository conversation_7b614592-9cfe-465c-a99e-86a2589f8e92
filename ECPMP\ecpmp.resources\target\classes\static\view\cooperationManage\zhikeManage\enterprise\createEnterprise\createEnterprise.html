<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta http-equiv="X-UA-Compatible" content="ie=edge">
		<title>直客管理－新增企业</title>
		<link href="../../../../../css/bootstrap.min.css" rel="stylesheet" type="text/css"/>
		<link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css" />
		<link href="../../../../../css/reset.css" rel="stylesheet" />
		<link href="../../../../../css/createEnterprise.css" rel="stylesheet" type="text/css"/>
		<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
		<script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
		<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
		<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
		<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
		<!-- 引入分页组件 -->
		<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
		<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />

		<link rel="stylesheet" type="text/css" href="../../../../../css/webuploader.css">
		<link rel="stylesheet" type="text/css" href="../../../../../css/font-awesome.min.css">
		<!--引入JS-->
		<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
		<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
		<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
		<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet"/>
		<link rel="stylesheet" type="text/css" href="../../../../../directives/preview/preview.css" />
		<script type="text/javascript" src="../../../../../directives/preview/preview.js"></script>
		<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
		<script type="text/javascript" src="createEnterprise.js"></script>
		
	</head>

	<body ng-app="myApp" ng-controller='enterpriseController' class="body-min-width">
		<div class="enterPrise">
			<div class="cooperation-head">
				<span class="frist-tab" ng-if="isSuperManager" ng-bind="'COMMON_ZHIKEMANAGE'|translate">
				</span>
				<span ng-if="isSuperManager">&nbsp;&gt;&nbsp;
				</span>
				<span class="second-tab" ng-if="operate =='add'" ng-bind="'ENTERPRISE_ADDENTERPRISE'|translate"></span>
				<span class="second-tab" ng-if="operate =='edit' && isSuperManager" ng-bind="'ENTERPRISE_EDITENTERPRISE'|translate"></span>
				<span class="second-tab" ng-if="operate =='detail'" ng-bind="'ENTERPRISE_ENTERPRISEDETAIL'|translate"></span>
			</div>
			<form  class="form-horizontal" name="myForm" novalidate ng-init="initEnterprise(this)">
			<div class="cooper-messsage">
				<top:menu chose-index="0" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/enterprise/createEnterprise" 
					list-index="3" ng-if="operate =='edit' && operateMr =='2'"></top:menu>
				<div class="enterprise-title">
					1.<span ng-bind="'ENTERPRISE_ENTERPRISEINFO'|translate"></span>
				</div>
				<div class="cooper-tab">

					<div class="form-group" ng-show="operate !='add'&& isSuperManager">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'ENTERPRISE_ENTERPRISEID'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input class="form-control"
									type="text" id="id" name="id" ng-model="enterpriseInfo.id"
									ng-disabled="true"
									title={{enterpriseInfo.id}}>
							</div>
						</div>
					</div>
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
							<span ng-bind="'ENTERPRISE_ENTERPRISENAME1'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input class="form-control"
									type="text" id="enterpriseName" name="enterpriseName" ng-model="enterpriseInfo.enterpriseName"
									placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"
									ng-blur="checkEnterpriseName(enterpriseInfo.enterpriseName,'')" 
									ng-disabled="operate =='detail' || (operate =='edit' &&!isSuperManager)"
									ng-if="!enterpriseInfo.enterpriseNameDesc"
									title={{enterpriseInfo.enterpriseName}}>
								<input class="form-control redBorder"
									type="text" name="enterpriseName" ng-model="enterpriseInfo.enterpriseName"
									placeholder="{{'ENTERPRISE_PLEASEINPUTENTERPRISENAME'|translate}}"
									ng-blur="checkEnterpriseName(enterpriseInfo.enterpriseName,'')" 
									ng-disabled="operate =='detail' || (operate =='edit' &&!isSuperManager)"
									ng-if="!!enterpriseInfo.enterpriseNameDesc"
									title={{enterpriseInfo.enterpriseName}}>
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle" 
									ng-show="!!enterpriseInfo.enterpriseNameDesc">
								<span class="redFont" ng-show="!!enterpriseInfo.enterpriseNameDesc">
									{{enterpriseInfo.enterpriseNameDesc|translate}}</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'ENTERPRISE_CUSTID'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="custID" ng-model="enterpriseInfo.custID" id="custID"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTCUSTID'|translate}}" 
									 ng-disabled="operate =='detail' || (operate =='edit' &&!isSuperManager)"
									 ng-show="custIDValidate"
									 ng-blur="checkCustID(enterpriseInfo.custID)"
									 title={{enterpriseInfo.custID}}>
								<input type="text" class="form-control redBorder" name="custID" ng-model="enterpriseInfo.custID"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTCUSTID'|translate}}" 
									 ng-disabled="operate =='detail' || (operate =='edit' &&!isSuperManager)"
									 ng-show="!custIDValidate"
									 ng-blur="checkCustID(enterpriseInfo.custID)"
									 title={{enterpriseInfo.custID}}>
								<span class="redFont" ng-show="!custIDValidate">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'ENTERPRISE_CUSTIDDESC'|translate"></span>
								</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'ENTERPRISE_ORGANIZATIONID'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="organizationID" 
									ng-model="enterpriseInfo.organizationID" id="organizationID"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTORGANIZATIONID'|translate}}" 
									 ng-disabled="operate =='detail' || (operate =='edit' &&!isSuperManager)"
									 ng-show="organizationIDValidate"
									 ng-blur="checkOrganizationID(enterpriseInfo.organizationID)"
									 title={{enterpriseInfo.organizationID}}>
								<input type="text" class="form-control redBorder" name="organizationID" 
									ng-model="enterpriseInfo.organizationID"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTORGANIZATIONID'|translate}}" 
									 ng-disabled="operate =='detail' || (operate =='edit' &&!isSuperManager)"
									 ng-show="!organizationIDValidate"
									 ng-blur="checkOrganizationID(enterpriseInfo.organizationID)"
									 title={{enterpriseInfo.organizationID}}>
								<span class="redFont" ng-show="!organizationIDValidate">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'ENTERPRISE_ORGANIZATIONIDDESC'|translate"></span>
								</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'ENTERPRISE_MSISDN'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="msisdn" ng-model="enterpriseInfo.msisdn" id="msisdn"
									placeholder="{{'ENTERPRISE_PLEASEINPUTMSISDN'|translate}}" ng-show="msisdnValidate"
									ng-blur="checkMsisdn(enterpriseInfo.msisdn)" ng-disabled="operate =='detail'"
									title={{enterpriseInfo.msisdn}}>
								<input type="text" class="form-control redBorder" name="msisdn" ng-model="enterpriseInfo.msisdn"
									placeholder="{{'ENTERPRISE_PLEASEINPUTMSISDN'|translate}}" ng-show="!msisdnValidate"
									ng-blur="checkMsisdn(enterpriseInfo.msisdn)" ng-disabled="operate =='detail'"
									title={{enterpriseInfo.msisdn}}>
							    <span class="redFont" ng-show="!msisdnValidate">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'ENTERPRISE_MSISDNDESC'|translate"></span>
								</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
								<span ng-bind="'COMMON_PROVINCE'|translate"></span>：</label>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-2">
								<select class="form-control"
									name="province" ng-model="provinceID"
									ng-options="x.provinceID as x.provinceName for x in provinceList" 
									ng-change="changeSelectedProvince(provinceID)"
									ng-disabled="operate =='detail'">
									<option value="" ng-show="operate !='edit'">不限</option>
								</select>
							</div>
							<label class="col-lg-1 col-xs-1 col-sm-1 col-md-1 control-label">
								<span ng-bind="'COMMON_CITY'|translate"></span>：</label>
							<div class="col-lg-2 col-xs-3 col-sm-3 col-md-2">
								<select class="form-control"
									name="city" ng-model="selectedCity"
									ng-options="x as x.cityName for x in subCityList" 
									ng-if="!provinceID || provinceID =='000'"
									ng-disabled="operate =='detail'">
									<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
								</select>
								<select class="form-control"
									name="city" ng-model="selectedCity"
									ng-change="changeSelectedCity(selectedCity.cityID)"
									ng-options="x as x.cityName for x in subCityList" 
									ng-if="provinceID && provinceID !='000'"
									ng-disabled="operate =='detail'">
									<option value="" ng-show="false">{{selectedCityName}}</option>
								</select>
							</div>
						</div>
					</div>
					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></span>：</label>
							<cy:uploadify filelistid="fileList" filepickerid="filePicker" accepttype="accepttype"
									uploadifyid="uploadifyid1" validate="isValidate" filesize="filesize" mimetypes="mimetypes"
									formdata="uploadParam"  uploadurl="uploadurl" desc="uploadDesc" numlimit="numlimit"
									urllist="urlList" createthumbnail="isCreateThumbnail" namelistid="nameList" 
									class="col-lg-5 col-xs-6 col-sm-7 col-md-5" ng-show="operate !='detail'">
							</cy:uploadify>
							
							<img ng-src="{{fileUrl}}" width="100" height="100" align="absmiddle" 
									ng-show="operate=='detail'" style="margin-left:15px">
						</div>
					</div>
					<!-- 预览按钮 -->
					<div class="form-group">
						<div ng-show="urlList2.length>0" 
							class="col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 
								col-lg-6 col-xs-7 col-sm-7 col-md-7 clear-preview">
							<ptl-preview style="float:left;padding-left:3px" urllist='urlList2'></ptl-preview>
						</div>
					</div>
					<div class="form-group">
						<div class="row">
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5
								col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2">
								<input id="imgUrl" type="text" name="imgUrl" ng-model="businessLicenseURL" 
									ng-show="false" required>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="cooper-messsage">
				<div class="enterprise-title">
					2.<span ng-bind="'ENTERPRISE_ACCOUNTINFO'|translate"></span>
				</div>
				<div class="cooper-tab">

					<div class="form-group">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon>*</icon>
								<span ng-bind="'COMMON_ACCOUNTNAME'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="text" class="form-control" name="accountName" ng-model="accountInfo.accountName"
									placeholder="{{'ENTERPRISE_PLEASEINPUTACCOUNTNAME'|translate}}" 
									ng-blur="checkAccountName(accountInfo.accountName,'')"
									ng-disabled="operate !='add'"
									ng-if="accountNameValidate && accountNameExist !=true"
									title={{accountInfo.accountName}}>
								<input type="text" class="form-control redBorder" name="accountName" ng-model="accountInfo.accountName"
									placeholder="{{'ENTERPRISE_PLEASEINPUTACCOUNTNAME'|translate}}" 
									ng-blur="checkAccountName(accountInfo.accountName,'')"
									ng-disabled="operate !='add'"
									ng-if="!accountNameValidate ||accountNameExist ==true"
									title={{accountInfo.accountName}}>
							    <span class="redFont" ng-show="!accountNameValidate">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'ENTERPRISE_ACCOUNTNAMEDESC'|translate"></span>
								</span>
								<span class="redFont" ng-show="accountNameExist">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span ng-bind="'ENTERPRISE_ACCOUNTNAMEEXIST'|translate"></span>
								</span>
							</div>
						</div>
					</div>

					<div class="form-group" ng-show="operate !='detail'">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon ng-if="operate =='add'">*</icon>
								<span ng-bind="'ENTERPRISE_PASSWORD'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="password" class="form-control" name="password" 
									autocomplete="off" ng-model="accountInfo.password"
									ng-blur="checkPassword('')" placeholder="{{'ENTERPRISE_PLEASEINPUTPASSWORD'|translate}}"
									ng-show="!passwordValidateDesc">
								<input type="password" class="form-control redBorder" name="password" 
									autocomplete="off" ng-model="accountInfo.password"
									ng-blur="checkPassword('')" placeholder="{{'ENTERPRISE_PLEASEINPUTPASSWORD'|translate}}" 
									ng-show="passwordValidateDesc">
							    <span class="redFont" ng-show="passwordValidateDesc">
								  <img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
								  <span>{{passwordValidateDesc|translate}}</span>
								</span>
							</div>
						</div>
					</div>

					<div class="form-group" ng-show="operate !='detail'">
						<div class="row">
							<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon ng-if="operate =='add'">*</icon>
								<span ng-bind="'ENTERPRISE_REPASSWORD'|translate"></span>：</label>
							<div class="col-lg-5 col-xs-6 col-sm-7 col-md-5">
								<input type="password" class="form-control" name="rePassword" 
									 autocomplete="off" ng-model="accountInfo.rePassword"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTREPASSWORD'|translate}}" 
									 ng-blur="checkRePassword(accountInfo.password,accountInfo.rePassword,'')"
									ng-show="rePasswordValidate">
								<input type="password" class="form-control redBorder" name="rePassword" 
									 autocomplete="off" ng-model="accountInfo.rePassword"
									 placeholder="{{'ENTERPRISE_PLEASEINPUTREPASSWORD'|translate}}" 
									 ng-blur="checkRePassword(accountInfo.password,accountInfo.rePassword,'')"
									 ng-show="!rePasswordValidate">
								</span>
								<span class="redFont" ng-show="!rePasswordValidate">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
									<span ng-show="operate =='add'" ng-bind="'ENTERPRISE_REPASSWORDDESCINADD'|translate"></span>
									<span ng-show="operate =='edit'" ng-bind="'ENTERPRISE_REPASSWORDDESCINEDIT'|translate"></span>
								</span>
							</div>
						</div>
					</div>
				</div>

			</div>
			<div class="enterprise-btn">
				<button type="submit" class="btn btn-primary search-btn"
				ng-disabled="!enterpriseNameValidate || !enterpriseInfo.enterpriseName || 
				myForm.imgUrl.$invalid ||
				!custIDValidate  || !enterpriseInfo.custID||
				!organizationIDValidate || !enterpriseInfo.organizationID || 
				!msisdnValidate || !enterpriseInfo.msisdn || 
				!accountNameValidate || !accountInfo.accountName ||
				enterpriseNameExist || accountNameExist || 
				passwordValidateDesc || !accountInfo.password||
				!accountInfo.rePassword ||!rePasswordValidate"
				ng-click="beforeSave()" ng-if="operate =='add'"
				ng-bind="'COMMON_SAVE'|translate"></button>
				<button type="submit" class="btn btn-primary search-btn"
				ng-disabled="!enterpriseNameValidate || !enterpriseInfo.enterpriseName || 
				myForm.imgUrl.$invalid ||
				!custIDValidate  || !enterpriseInfo.custID||
				!organizationIDValidate || !enterpriseInfo.organizationID || 
				!msisdnValidate || !enterpriseInfo.msisdn || 
				enterpriseNameExist || 
				passwordValidateDesc ||!passwordValidate ||
				!rePasswordValidate ||(accountInfo.password && !accountInfo.rePassword)"
				ng-click="beforeSave()" ng-if="operate =='edit'"
				ng-bind="'COMMON_SAVE'|translate"></button>
				<button type="submit" class="btn btn-back" ng-click="cancelToEnterpriseList(this)"
					ng-bind="'COMMON_BACK'|translate" ng-show="isSuperManager"></button>
			</div>
			</form>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="addEnterModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p class="tip">
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_OK'|translate"></button>
						</div>
					</div>
				</div>
			</div>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p class="tip">
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_OK'|translate"></button>
						</div>
					</div>
				</div>
			</div>
			<!--小弹出框-->
			<div class="modal fade bs-example-modal-sm" id="ensureToList" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
				<div class="modal-dialog modal-sm" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
							<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
						</div>
						<div class="modal-body">
							<div class="text-center"><p class="tip">
								{{tip|translate}}
							</p></div>
						</div>
						<div class="modal-footer">
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-click="ensureToList()" 
								ng-bind="'COMMON_OK'|translate"></button>
							<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
								ng-bind="'COMMON_CANCLE'|translate"></button>
						</div>
					</div>
				</div>
			</div>
		</div>

	</body>
</html>