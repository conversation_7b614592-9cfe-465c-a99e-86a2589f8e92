var app = angular.module("myApp", ["util.ajax", "angularI18n", "service.common","preview"]);
app.controller('activityDetailCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
  $scope.init = function () {

    $scope.activityID = $location.search().activityID;
    $scope.queryActivity();
    var isIE=false;
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("Opera") > -1; //判断是否Opera浏览器
    var isIE = userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera; //判断是否IE浏览器
    var isEdge = userAgent.indexOf("Windows NT 6.1") > -1 && userAgent.indexOf("Trident/7.0") > -1 && !isIE; //判断是否IE的Edge浏览器
    if(isIE||isEdge){
      $(function(){
        $('.actInfo').addClass('actInfoIE');
      })
    }
  };

  $scope.queryActivity = function () {
    var req = {
      "activityID": $scope.activityID
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/activityService/queryActivity",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.activityInfo = result.activityInfo;
            var effectivetime = CommonUtils.dateTimeFormate($scope.activityInfo.effectivetime);
            $scope.activityInfo.effectivetime = effectivetime.year + "." + effectivetime.month + "." + effectivetime.day;
            var expiretime = CommonUtils.dateTimeFormate($scope.activityInfo.expiretime);
            $scope.activityInfo.expiretime = expiretime.year + "." + expiretime.month + "." + expiretime.day;
            $scope.bannerList = [];
            angular.forEach($scope.activityInfo.bannerList, function (pic) {
              var picObj = CommonUtils.formatPic(pic.bannerURL);
              if(picObj.review.indexOf('/sftp/data')==-1){
                picObj.review=picObj.review.replace('path=','path=/sftp/data');
              }

              $scope.bannerList.push(picObj);
            });
            var url = $scope.activityInfo.backLogoURL;
            $scope.backgroundURL = CommonUtils.formatPic(url);
            if($scope.backgroundURL.review.indexOf('/sftp/data')==-1){
              $scope.backgroundURL.review=$scope.backgroundURL.review.replace('path=','path=/sftp/data');
            }
            $scope.contentList = $scope.activityInfo.contentList;
            if (angular.isArray($scope.contentList) && $scope.contentList.length > 0) {
              angular.forEach($scope.contentList, function (item) {
                if (item.subServType === 2) {
                  $scope.pxcontent = item.content;
                  $scope.pxpushInterval = item.pushInterval;//每天对同一用户的发送间隔;
                  $scope.pxmaxPushPerDay = item.maxPushPerDay;//每天对同一用户的发送限制
                }
                if (item.subServType === 8) {
                  $scope.contentTitle = item.contentTitle;
                  $scope.gjpushInterval = item.pushInterval;//每天对同一用户的发送间隔;
                  $scope.gjmaxPushPerDay = item.maxPushPerDay;//每天对同一用户的发送限制
                  $scope.gjcontent = "";
                  $scope.gjcxList = [];
                  angular.forEach(item.contentFrameMappingList, function (pic) {
                    //图片
                    if (pic.frameType == 1)
                    {
                      var picObj = CommonUtils.formatPic(pic.framePicUrl);
                      $scope.gjcxList.push(picObj);
                    }
                    else (pic.frameType == 2)
                    {
                      $scope.gjcontent = pic.frameTxt;
                    }
                  })

                }
              })
            }

            if ($scope.activityInfo.createTime == null || $scope.activityInfo.createTime == "")
            {
              $scope.createTime = "";
            }
            else
            {
              $scope.createTime = $scope.activityInfo.createTime.slice(0, 4) + "-" + $scope.activityInfo.createTime.slice(4, 6) + "-" + $scope.activityInfo.createTime.slice(6, 8) + " " + $scope.activityInfo.createTime.slice(8, 10) + ":" + $scope.activityInfo.createTime.slice(10, 12);
            }

            $scope.cityList = $scope.activityInfo.cityList;
            $scope.provinceList=$scope.activityInfo.proviceList||[];
            $scope.citynameAll = "";
            $scope.provinceNameAll="";
            angular.forEach($scope.provinceList, function (item) {
              $scope.provinceNameAll = $scope.provinceNameAll + "，" + item.provinceName;
            });
            $scope.provinceNameAll = $scope.provinceNameAll.substr(1);
            angular.forEach($scope.cityList, function (item) {
              $scope.citynameAll = $scope.citynameAll + "，" + item.cityName;
            });
            if($scope.provinceNameAll){
              $scope.citynameAll = $scope.provinceNameAll+$scope.citynameAll;
            }else{
              $scope.citynameAll=$scope.citynameAll.substr(1);
            }
            if ($scope.activityInfo.template) {
              $scope.templateIconURL = [$scope.activityInfo.template.templateIconURL];
            }
            $scope.activityQrCode = CommonUtils.formatPic($scope.activityInfo.activityQrCode);
          } else {
            $scope.activityInfo = {};
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "**********";
              $('#myModal').modal();
            }
        )
      }
    });
  }
  $scope.goBack = function () {
    window.location = "../activityManageList/activityManageList.html"
  }

}])
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])

