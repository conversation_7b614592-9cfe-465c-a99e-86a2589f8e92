<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.QueryCityMapper">

    <resultMap id="provinceMap" type="com.huawei.jaguar.dsum.dao.domain.CityWrapper">
        <result property="cityID" column="cityID"/>
        <result property="cityName" column="cityName"/>
        <result property="provinceID" column="provinceID"/>
        <result property="firstpy" column="firstpy"/>
        <result property="zyzqCityCode" column="zyzqCityCode"/>
    </resultMap>

    <select id="queryCityListByProvinceID" resultMap="provinceMap">
        SELECT t.cityID,t.cityName,t.provinceID,t.firstpy 
        from dsum_t_city t 
        where t.provinceID = #{provinceID}
        order by t.firstpy ASC
    </select>
    
    <select id="queryCityList" resultMap="provinceMap">
        SELECT t.cityID,t.cityName,t.provinceID,t.firstpy,t.zyzqCityCode 
        from dsum_t_city t 
        order by t.firstpy ASC
    </select>
    
    <select id="queryCityByCityID" resultMap="provinceMap">
        SELECT t.cityID,t.cityName,t.provinceID,t.firstpy 
        from dsum_t_city t 
        where t.cityID = #{cityID}
    </select>

</mapper>