<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MonquotaDeliverStatMapper">
    <resultMap id="MonquotaDeliveryStatWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MonquotaMemConWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="memberID" column="memberID" javaType="java.lang.Long" />
        <result property="ydpxDeliveryCount" column="ydpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltpxDeliveryCount" column="ltpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxpxDeliveryCount" column="dxpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="pxDeliveryStatus" column="pxDeliveryStatus" javaType="java.lang.Integer" />
        <result property="pxDiffDeliveryStatus" column="pxDiffDeliveryStatus" javaType="java.lang.Integer" />
        <result property="ydgdDeliveryCount" column="ydgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltgdDeliveryCount" column="ltgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxgdDeliveryCount" column="dxgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="gdDeliveryStatus" column="gdDeliveryStatus" javaType="java.lang.Integer" />
        <result property="gdDiffDeliveryStatus" column="gdDiffDeliveryStatus" javaType="java.lang.Integer" />
        <result property="ydgcDeliveryCount" column="ydgcDeliveryCount" javaType="java.lang.Integer" />
        <result property="gcDeliveryStatus" column="gcDeliveryStatus" javaType="java.lang.Integer" />
        <result property="callDeliveryStatus" column="callDeliveryStatus" javaType="java.lang.Integer" />
        <result property="calledDeliveryStatus" column="calledDeliveryStatus" javaType="java.lang.Integer" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="contRuleID" column="contRuleID" javaType="java.lang.String" />
        <result property="servType" column="servType" javaType="java.lang.Integer" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="enterpriseType" column="enterpriseType" javaType="java.lang.Integer" />
        <result property="orgCode" column="orgCode" javaType="java.lang.String" />
        <result property="orgID" column="orgID" javaType="java.lang.Integer" />
        <result property="branchType" column="branchType" javaType="java.lang.String" />
		<result property="hangupType" column="hangupType" javaType="java.lang.Integer" />
		<result property="contentType" column="contentType" javaType="java.lang.String" />
		<result property="contentOrgMsisdn" column="contentOrgMsisdn" javaType="java.lang.String" />
		<result property="contentId" column="contentId" javaType="java.lang.Long" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
	</resultMap>

    <select id="queryMonquotaWrapper" resultMap="MonquotaDeliveryStatWrapper">
        SELECT 	t.contRuleID contRuleID, 
		t.servType servType,
		t.subServType subServType,
		t.enterpriseID enterpriseID,
		t.enterpriseType enterpriseType,
		t.orgCode orgCode,
		m.ID memberID,
		md.pxDeliveryStatus pxDeliveryStatus,
		md.pxDiffDeliveryStatus pxDiffDeliveryStatus,
		md.gdDeliveryStatus gdDeliveryStatus,
		md.gdDiffDeliveryStatus gdDiffDeliveryStatus,
		md.gcDeliveryStatus gcDeliveryStatus,
		md.callDeliveryStatus callDeliveryStatus,
		md.calledDeliveryStatus calledDeliveryStatus,		
		m.msisdn msisdn,
		t.reserved2 reserved2,
		t.reserved3 reserved3,
		t.reserved4 reserved4,
		t.reserved5 reserved5,
		t.reserved6 reserved6,
		t.reserved7 reserved7,
		t.reserved8 reserved8,
		t.reserved10 reserved10,
		t.ID orgID,
		t.branchType branchType,
		t.hangupType,

		t.contentType,
		t.contentOrgMsisdn,t.contentId
		FROM (
		SELECT 
		s.`ID`,
		c.contRuleID contRuleID, 
		c.servType servType,
		c.subServType subServType,
		c.enterpriseID enterpriseID,
		c.enterpriseType enterpriseType,
		co.orgCode orgCode,
		s.reserved2 reserved2,
		s.reserved3 reserved3,
		s.reserved4 reserved4,
		s.reserved5 reserved5,
		s.reserved6 reserved6,
		s.reserved7 reserved7,
		s.reserved8 reserved8,
		s.reserved10 reserved10,
		s.branchType branchType,
		c.hangupType,

		c.contentType,co.msisdn contentOrgMsisdn,c.id contentId
		FROM ecpm_t_org_simple s 
		LEFT JOIN ecpm_t_content_org co ON s.ID = co.ownerID
		LEFT JOIN ecpm_t_content c ON c.ID = co.cyContID AND (c.status != 2 OR c.status IS NULL)
		WHERE (s.reserved2 IS NOT NULL AND s.reserved2 != "" and s.branchType is not null AND c.isMonthByQuota = 1)) t,
		ecpm_t_org_rel r , ecpm_t_member m
		LEFT JOIN  ecpm_t_monquota_delivery_stat md ON m.ID = md.memberID
		WHERE r.orgID = t.ID AND r.ID = m.ID AND (md.createTime &lt;= #{createTime} or md.createTime is NULL) AND md.status is NULL
		and (t.contentOrgMsisdn is null or t.contentOrgMsisdn = m.msisdn);
	</select>

	<select id="queryMonquotaWrapperByMsisdn" resultMap="MonquotaDeliveryStatWrapper">
		SELECT 	m.ID memberID,
				  md.ydpxDeliveryCount ydpxDeliveryCount,
				  md.ltpxDeliveryCount ltpxDeliveryCount,
				  md.dxpxDeliveryCount dxpxDeliveryCount,
				  md.ydgdDeliveryCount ydgdDeliveryCount,
				  md.ltgdDeliveryCount ltgdDeliveryCount,
				  md.dxgdDeliveryCount dxgdDeliveryCount,
				  md.ydgcDeliveryCount ydgcDeliveryCount,
				  m.msisdn msisdn,
				  t.reserved2 reserved2,
				  t.reserved3 reserved3,
				  t.reserved4 reserved4,
				  t.reserved5 reserved5,
				  t.reserved6 reserved6,
				  t.reserved7 reserved7,
				  t.reserved8 reserved8,
				  t.reserved10 reserved10,
				  t.ID orgID,
				  t.branchType branchType
		FROM (
				 SELECT
					 s.`ID`,
					 s.reserved2 reserved2,
					 s.reserved3 reserved3,
					 s.reserved4 reserved4,
					 s.reserved5 reserved5,
					 s.reserved6 reserved6,
					 s.reserved7 reserved7,
					 s.reserved8 reserved8,
					 s.reserved10 reserved10,
					 s.branchType branchType
				 FROM ecpm_t_org_simple s
				 WHERE (s.reserved2 IS NOT NULL AND s.reserved2 != "" and s.branchType is not null)) t,
			 ecpm_t_org_rel r , ecpm_t_member m
									LEFT JOIN  ecpm_t_monquota_delivery_stat md ON m.ID = md.memberID
		WHERE r.orgID = t.ID AND r.ID = m.ID AND md.msisdn = #{msisdn}  AND md.status is NULL;
	</select>

    <update id="deleteAll">
        delete from ecpm_t_monquota_delivery_stat where status = 1;
    </update>

	<update id="updateAll">
        update ecpm_t_monquota_delivery_stat set status = 1 where createTime &lt;= #{createTime};
    </update>
    
    <select id="queryAddWrapper" resultMap="MonquotaDeliveryStatWrapper">
        SELECT a.ID,a.memberID,a.ydpxDeliveryCount,a.ltpxDeliveryCount,a.dxpxDeliveryCount,a.pxDeliveryStatus,
        a.pxDiffDeliveryStatus,a.ydgdDeliveryCount,a.ltgdDeliveryCount,a.dxgdDeliveryCount,a.gdDeliveryStatus,
        a.gdDiffDeliveryStatus,a.ydgcDeliveryCount,a.gcDeliveryStatus,a.callDeliveryStatus,a.calledDeliveryStatus,
               d.reserved2,d.reserved3,d.reserved4, d.reserved5, d.reserved6, d.reserved7, d.reserved8, d.reserved10, b.msisdn, d.enterpriseID,
               e.servType,e.subServType,co.orgCode,
        d.branchType, d.ID orgID, e.hangupType, e.enterpriseType,e.contentType,co.msisdn contentOrgMsisdn,e.id contentId,d.pxBeyondPackageSwitch,d.gdBeyondPackageSwitch
        FROM ecpm_t_monquota_delivery_stat a
        LEFT JOIN ecpm_t_member b ON a.memberID = b.ID
        LEFT JOIN ecpm_t_org_rel c ON b.ID = c.ID
        LEFT JOIN ecpm_t_org_simple d ON c.orgID = d.ID
        LEFT JOIN ecpm_t_content_org co ON d.ID = co.ownerID  and (co.msisdn is null or co.msisdn =  b.msisdn )
		LEFT JOIN ecpm_t_content e ON e.ID = co.cyContID
		where a.status IS NULL AND e.isMonthByQuota = 1;
    </select>


    <insert id="insertAll">
        insert into
		ecpm_t_monquota_delivery_stat
		(
		memberID,
		ydpxDeliveryCount,
		ltpxDeliveryCount,
		dxpxDeliveryCount,
		pxDeliveryStatus,
		pxDiffDeliveryStatus,
		ydgdDeliveryCount,
		ltgdDeliveryCount,
		dxgdDeliveryCount,
		gdDeliveryStatus,
		gdDiffDeliveryStatus,
		ydgcDeliveryCount,
		gcDeliveryStatus,
		callDeliveryStatus,
		calledDeliveryStatus,
		createTime,
		updateTime,
		msisdn,
		enterpriseID
		)
		values
		<foreach collection="list" item="wrapper" separator=",">
			(
			#{wrapper.memberID},
			#{wrapper.ydpxDeliveryCount},
			#{wrapper.ltpxDeliveryCount},
			#{wrapper.dxpxDeliveryCount},
			#{wrapper.pxDeliveryStatus},
			#{wrapper.pxDiffDeliveryStatus},
			#{wrapper.ydgdDeliveryCount},
			#{wrapper.ltgdDeliveryCount},
			#{wrapper.dxgdDeliveryCount},
			#{wrapper.gdDeliveryStatus},
			#{wrapper.gdDiffDeliveryStatus},
			#{wrapper.ydgcDeliveryCount},
			#{wrapper.gcDeliveryStatus},
			#{wrapper.callDeliveryStatus},
			#{wrapper.calledDeliveryStatus},
			now(),
			now(),
			#{wrapper.msisdn},
			#{wrapper.enterpriseID}
			)
		</foreach>
    </insert>
    
    <update id="batchUpdate">
		<foreach close=";" collection="list" index="index" item="wrapper"
			open="" separator=";">
			update ecpm_t_monquota_delivery_stat
			set 
			ydpxDeliveryCount = #{wrapper.ydpxDeliveryCount},
			ltpxDeliveryCount = #{wrapper.ltpxDeliveryCount},
			dxpxDeliveryCount = #{wrapper.dxpxDeliveryCount},
			pxDeliveryStatus =   IF (pxDeliveryStatus IS not NULL and #{wrapper.pxDeliveryStatus} is null, pxDeliveryStatus, #{wrapper.pxDeliveryStatus}),
			pxDiffDeliveryStatus =  IF (pxDiffDeliveryStatus IS not NULL and #{wrapper.pxDiffDeliveryStatus} is null, pxDiffDeliveryStatus, #{wrapper.pxDiffDeliveryStatus}),
			ydgdDeliveryCount = #{wrapper.ydgdDeliveryCount},
			ltgdDeliveryCount = #{wrapper.ltgdDeliveryCount},
			dxgdDeliveryCount = #{wrapper.dxgdDeliveryCount},
			gdDeliveryStatus = #{wrapper.gdDeliveryStatus},
			gdDiffDeliveryStatus = #{wrapper.gdDiffDeliveryStatus},
			ydgcDeliveryCount = #{wrapper.ydgcDeliveryCount},
			gcDeliveryStatus = #{wrapper.gcDeliveryStatus},
			callDeliveryStatus = #{wrapper.callDeliveryStatus},
			calledDeliveryStatus = #{wrapper.calledDeliveryStatus},			
			updateTime = now()
			where id = #{wrapper.id} AND status is NULL
		</foreach>
    </update>
    
    <update id="batchUpdateStatus">
		<foreach close=";" collection="list" index="index" item="wrapper"
			open="" separator=";">
			update ecpm_t_monquota_delivery_stat
			set 
			<if test="wrapper.pxDeliveryStatus!=null">
				ydpxDeliveryCount = IF (pxDeliveryStatus IS NULL, 0, ydpxDeliveryCount),
				ltpxDeliveryCount = IF (pxDeliveryStatus IS NULL, 0, ltpxDeliveryCount),
				dxpxDeliveryCount = IF (pxDeliveryStatus IS NULL, 0, dxpxDeliveryCount),
				pxDiffDeliveryStatus = IF (pxDeliveryStatus IS NULL, 1, pxDiffDeliveryStatus),
				pxDeliveryStatus = IF (pxDeliveryStatus IS NULL, 1, pxDeliveryStatus), 
			</if>
			<if test="wrapper.gdDeliveryStatus != null">
				ydgdDeliveryCount = IF (gdDeliveryStatus IS NULL, 0, ydgdDeliveryCount),
				ltgdDeliveryCount = IF (gdDeliveryStatus IS NULL, 0, ltgdDeliveryCount),
				dxgdDeliveryCount = IF (gdDeliveryStatus IS NULL, 0, dxgdDeliveryCount),
				gdDiffDeliveryStatus = IF (gdDeliveryStatus IS NULL, 1, gdDiffDeliveryStatus),
				gdDeliveryStatus = IF (gdDeliveryStatus IS NULL, 1, gdDeliveryStatus), 
			</if>
			<if test="wrapper.gcDeliveryStatus!= null">
				ydgcDeliveryCount = IF (gcDeliveryStatus IS NULL, 0, ydgcDeliveryCount),
				gcDeliveryStatus = IF (gcDeliveryStatus IS NULL, 1, gcDeliveryStatus), 
			</if>
			<if test="wrapper.callDeliveryStatus!= null">
				callDeliveryStatus = IF (callDeliveryStatus IS NULL, 1, callDeliveryStatus),
			</if>
			<if test="wrapper.calledDeliveryStatus!= null">
				calledDeliveryStatus = IF (calledDeliveryStatus IS NULL, 1, calledDeliveryStatus),
			</if>
			updateTime = now()
			where memberID = #{wrapper.memberID}  AND status is NULL
		</foreach>
    </update>
    
    <select id="queryByStatus" resultMap="MonquotaDeliveryStatWrapper">
        SELECT t.memberID
        FROM ecpm_t_monquota_delivery_stat t
        where status is NULL AND
        t.memberID in
		<foreach item="memberID" index="index" collection="memberIDList"
				 open="(" separator="," close=")">
			#{memberID}
		</foreach>
		<if test="subServType !=null and (subServType == 3 or subServType == 1 or subServType == 2)">
			and (t.pxDeliveryStatus = 0 and t.pxDiffDeliveryStatus = 0)
		</if>
		<if test="subServType !=null and subServType == 4">
			and (t.gdDeliveryStatus = 0 and t.gdDiffDeliveryStatus = 0)
		</if>
		<if test="subServType !=null and subServType == 8">
			and (t.gcDeliveryStatus = 0)
		</if>
    </select>

	<select id="queryByEnterpriseIDAndMsisdn" resultMap="MonquotaDeliveryStatWrapper">
		SELECT *
		FROM ecpm_t_monquota_delivery_stat t
		where t.enterpriseID = #{enterpriseID} and t.msisdn in
		<foreach item="msisdn" index="index" collection="msisdnList"
				 open="(" separator="," close=")">
			#{msisdn}
		</foreach>
		<if test="subServType !=null and (subServType == 3 or subServType == 1 or subServType == 2)">
			and (t.pxDeliveryStatus = 0 and t.pxDiffDeliveryStatus = 0)
		</if>
		<if test="subServType !=null and subServType == 4">
			and (t.gdDeliveryStatus = 0 and t.gdDiffDeliveryStatus = 0)
		</if>
		<if test="subServType !=null and subServType == 8">
			and (t.gcDeliveryStatus = 0)
		</if>
	</select>

	<select id="queryByStatusX" resultMap="MonquotaDeliveryStatWrapper">
		SELECT t.memberID,t.msisdn
		FROM ecpm_t_monquota_delivery_stat t
		where status is NULL AND
		t.memberID in
		<foreach item="memberID" index="index" collection="memberIDList"
				 open="(" separator="," close=")">
			#{memberID}
		</foreach>
		<if test="subServType !=null and (subServType == 3 or subServType == 1 or subServType == 2)">
			and (t.pxDeliveryStatus = 0 or t.pxDiffDeliveryStatus = 0)
		</if>
		<if test="subServType !=null and subServType == 4">
			and (t.gdDeliveryStatus = 0 or t.gdDiffDeliveryStatus = 0)
		</if>
		<if test="subServType !=null and subServType == 8">
			and (t.gcDeliveryStatus = 0)
		</if>
	</select>
</mapper>