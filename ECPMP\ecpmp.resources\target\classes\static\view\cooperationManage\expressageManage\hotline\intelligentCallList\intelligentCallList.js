var app = angular.module("myApp", ["util.ajax", 'page', "top.menu", "angularI18n","service.common"])
app.controller('intelligentCallListController', ['$scope', '$rootScope', '$location', 'RestClientUtil','CommonUtils', function ($scope, $rootScope, $location, RestClientUtil,CommonUtils) {
    $scope.init = function () {
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //判断是否超管
        $scope.isSuperManager = false;
        $scope.isAgent = false;
        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
        $scope.isAgent = (loginRoleType == 'agent');
        $scope.wltServiceType = $.cookie("wltServiceType");
        $scope.enterpriseID = "";

        if ($scope.isAgent) {
            $scope.enterpriseID = $.cookie('enterpriseID') || '';
        }

        $scope.businessType = 0;
        $scope.result = "";

        //下拉框(业务类别)
        $scope.businessTypeChoise = [
            {
                id: 0,
                name: "交互彩印&语音外呼"
            },
            {
                id: 1,
                name: "手动语音外呼"
            }
        ];
        
        $scope.resultChoise = [
            {
            	id: "",
            	name:"不限"
            },{
            	id: "Success",
            	name:"外呼成功"
            },{
            	id: "CALLER_HANGUP",
            	name:"正常挂机"
            },{
            	id: "CALLER_BUSY",
            	name:"忙"
            },{
            	id: "CALLER_NO_ANSWER",
            	name:"无应答"
            },{
            	id: "CALLER_NOT_REACHABLE",
            	name:"不可达"
            },{
            	id: "CALLER_ROUTE_FAILURE",
            	name:"路由失败"
            },{
            	id: "CALLER_FORBIDDEN",
            	name:"禁止呼叫"
            },{
            	id: "EXCEPTION",
            	name:"系统内部异常"
            }
        ];

        //初始化搜索条件
        $scope.initSel = [{
        		startTime: "",
        		endTime: "",
	            search: false,
        	},{
	        	startTime: "",
	        	endTime: "",
	            search: false,
        	}
        ];
        $scope.detailList=[];
        $scope.detailDeliveryNum = "";
        $scope.detailTrackingNum = "";
        $scope.detailCallTime = "";
        $scope.queryIntelligentCall();
    }
    
    $scope.goback = function () {
        $('.close').click();
    }
    
    $scope.showDetail = function (requestId) {
    	$scope.detailList=[];
        $scope.detailDeliveryNum = "";
        $scope.detailTrackingNum = "";
        $scope.detailCallTime = "";
    	if(requestId != null && requestId != undefined)
    	{
    		var req = {
    			"requestID":requestId
    		};
    		RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/intelligentCallService/queryIntelligentCall",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '1030100000') {
                            if(result.intelligentCallList.length == 1)
                            {
                            	$scope.detailList = result.intelligentCallList[0].detailInfo || [];
                            	$scope.detailDeliveryNum = result.intelligentCallList[0].callee;
                                $scope.detailTrackingNum = result.intelligentCallList[0].trackingNum;
                                $scope.detailCallTime = result.intelligentCallList[0].createTime;
                            }
                        }
                        else {
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                    )
                }
            });
        	$('#addIntelligentCall').modal();
    	}
    }
    
    $scope.getBusinessType = function (businessType) {
        if (businessType == 0) {
            return "交互彩印&语音外呼";
        }
        else if (businessType == 1) {
            return "手动语音外呼";
        }
    }
    
    $scope.getDeliveryResult = function (deliveryResult) {
    	if (deliveryResult == 0) {
    		return "成功";
    	} else if (deliveryResult == 1) {
    		return "失败";
    	}
    }
    
    $scope.getResult = function (result) {
    	if (result == 'Success')
		{
    		return "外呼成功";
		} else if(result == 'CALLER_HANGUP') {
			return "正常挂机";
		} else if(result == 'CALLER_BUSY') {
			return "忙";
		} else if(result == 'CALLER_NO_ANSWER') {
			return "无应答";
		} else if(result == 'CALLER_NOT_REACHABLE') {
			return "不可达";
		} else if(result == 'CALLER_ROUTE_FAILURE') {
			return "路由失败";
		} else if(result == 'CALLER_FORBIDDEN') {
			return "禁止呼叫";
		} else if(result == 'EXCEPTION') {
			return "系统内部异常";
		} else {
			return "";
		}
    }
    
    $scope.getTime = function (time) {
    	if(time != null && time != '' && time != undefined){
    		var year = time.slice(0, 4);
            var month = time.slice(4, 6);
            var day = time.slice(6, 8);
            var hour = time.slice(8, 10);
            var minute = time.slice(10, 12);
            var second = time.slice(12, 14);
            return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
    	} else {
    		return "";
    	}
    }
    
    $scope.getNumber = function(number) {
    	if(number != null && number != '' && number != undefined){
    		if(number.indexOf('86') == 0) {
    			number = number.substring(2,number.length);
  		  	}
    	} else {
    		return "";
    	}
    	return number;
    }
    
    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true
    });

    $('#deliveryStartTime').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#deliveryEndTime').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });
    
    $('#startTime').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn2();
        }
        )
    });

    $('#endTime').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn2();
        }
        )
    });

    //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        var startTime = document.getElementById("deliveryStartTime").value;
        var endTime = document.getElementById("deliveryEndTime").value;

        if (startTime !== '')
        {
            $scope.initSel[0].startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
        }
        else
        {
            $scope.initSel[0].startTime = "";
        }

        if (endTime !== '')
        {
            $scope.initSel[0].endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
        }
        else
        {
            $scope.initSel[0].endTime = "";
        }
        $scope.initSel[0].search = false;
    }
    
    $scope.searchOn2 = function () {
        var startTime = document.getElementById("startTime").value;
        var endTime = document.getElementById("endTime").value;

        if (startTime !== '')
        {
            $scope.initSel[1].startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
        }
        else
        {
            $scope.initSel[1].startTime = "";
        }

        if (endTime !== '')
        {
            $scope.initSel[1].endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
        }
        else
        {
            $scope.initSel[1].endTime = "";
        }
        $scope.initSel[1].search = true;
    }

    //后续post的函数
    $scope.queryIntelligentCall = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "phoneNum": $scope.phoneNum || '',
                "businessType": $scope.businessType,
                "contentID": $scope.contentID || '',
                "trackingNum": $scope.trackingNum || '',
                "result": $scope.result || '',
                "deliveryStartTime":$scope.initSel[0].startTime || '',
                "deliveryEndTime":$scope.initSel[0].endTime || '',
                "startTime": $scope.initSel[1].startTime || '',
                "endTime": $scope.initSel[1].endTime || '',
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[0].pageSize)
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/intelligentCallService/queryIntelligentCall",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.IntelligentCallListData = result.intelligentCallList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalCount) || 0;
                        $scope.pageInfo[0].totalPage = result.totalCount !== 0 ? Math.ceil(result.totalCount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    }
                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "1030120500";
                    $('#myModal').modal();
                }
                )
            }
        });

    };

    $(function () {
        $('.glyphicon-calendar').on('click', function () {
            $('#time-config').trigger('click');
        })
    })


}])