<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ServiceRuleMapper">
    <resultMap id="serviceRuleWrapper"
               type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ServiceRuleWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer"/>
        <result property="servRuleCode" column="servRuleCode" javaType="java.lang.String"/>
        <result property="servType" column="servType" javaType="java.lang.Integer"/>
        <result property="subServType" column="subServType" javaType="java.lang.Integer"/>
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer"/>
        <result property="oneTemplateDayLimit" column="oneTemplateDayLimit" javaType="java.lang.Integer"/>
        <result property="oneDeliveryInterval" column="oneDeliveryInterval" javaType="java.lang.Integer"/>
        <result property="creatorID" column="creatorID" javaType="java.lang.Integer"/>
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer"/>
        <result property="extInfo" column="extInfo" javaType="java.lang.String"/>
        <result property="reserved1" column="reserved1" javaType="java.lang.String"/>
        <result property="reserved2" column="reserved2" javaType="java.lang.String"/>
        <result property="reserved3" column="reserved3" javaType="java.lang.String"/>
        <result property="reserved4" column="reserved4" javaType="java.lang.String"/>
        <result property="reserved5" column="reserved5" javaType="java.lang.String"/>
        <result property="reserved6" column="reserved6" javaType="java.lang.String"/>
        <result property="reserved7" column="reserved7" javaType="java.lang.String"/>
        <result property="reserved8" column="reserved8" javaType="java.lang.String"/>
        <result property="reserved9" column="reserved9" javaType="java.lang.String"/>
        <result property="reserved10" column="reserved10" javaType="java.lang.String"/>
        <result property="createTime" column="createTime" javaType="java.util.Date"/>
        <result property="operateTime" column="operateTime" javaType="java.util.Date"/>
        <result property="periodType" column="periodType" javaType="java.lang.Integer"/>
    </resultMap>
    <sql id="serviceRuleCol">
		ID,
		servRuleCode,
		servType,
		subServType,
		enterpriseID,
		oneTemplateDayLimit,
		oneDeliveryInterval,
		creatorID,
		operatorID,
		extInfo,
		createTime,
		operateTime,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		periodType
	</sql>
    <select id="queryServiceRuleList" resultMap="serviceRuleWrapper">
        SELECT
        <include refid="serviceRuleCol"/>
        from ecpe_t_serv_rule
        <trim prefix="where" prefixOverrides="and|or">
            <if test="ID != null">
                and ID= #{id}
            </if>
            <if test="enterpriseID != null">
                and enterpriseID= #{enterpriseID}
            </if>
            <if test="servRuleCode != null and servRuleCode !=''">
                and servRuleCode= #{servRuleCode}
            </if>
            <if test="enterpriseIDList != null">
                and enterpriseID in
                <foreach item="enterpriseID" index="index" collection="enterpriseIDList"
                         open="(" separator="," close=")">
                    #{enterpriseID}
                </foreach>
            </if>
            <if test="subServType != null and subServType !=''">
                and subServType= #{subServType}
            </if>
            <if test="servType != null and servType !=''">
                and servType= #{servType}
            </if>
            <if test="reserved1 != null and reserved1 !=''">
                and reserved1= #{reserved1}
            </if>
        </trim>
    </select>

    <select id="batchQueryServiceID" resultType="java.lang.Integer"
            parameterType="java.util.List">
        SELECT t.ID from ecpe_t_serv_rule t where t.ID in
        <foreach item="id" index="index" collection="list"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="batchUpdateServiceRule" parameterType="java.util.List">
        <foreach close=";" collection="list" index="index" item="serviceRuleWrapper"
                 open="" separator=";">
            update ecpe_t_serv_rule set
            servRuleCode= #{serviceRuleWrapper.servRuleCode},
            servType= #{serviceRuleWrapper.servType},
            subServType= #{serviceRuleWrapper.subServType},
            enterpriseID= #{serviceRuleWrapper.enterpriseID},
            oneTemplateDayLimit= #{serviceRuleWrapper.oneTemplateDayLimit},
            oneDeliveryInterval= #{serviceRuleWrapper.oneDeliveryInterval},
            operatorID= #{serviceRuleWrapper.operatorID},
            extInfo= #{serviceRuleWrapper.extInfo},
            operateTime= #{serviceRuleWrapper.operateTime},
            reserved1= #{serviceRuleWrapper.reserved1},
            reserved2= #{serviceRuleWrapper.reserved2},
            reserved3= #{serviceRuleWrapper.reserved3},
            reserved4= #{serviceRuleWrapper.reserved4},
            reserved5= #{serviceRuleWrapper.reserved5},
            reserved6= #{serviceRuleWrapper.reserved6},
            reserved7= #{serviceRuleWrapper.reserved7},
            reserved8= #{serviceRuleWrapper.reserved8},
            reserved9= #{serviceRuleWrapper.reserved9},
            reserved10= #{serviceRuleWrapper.reserved10},
            periodType= #{serviceRuleWrapper.periodType}
            where ID =#{serviceRuleWrapper.id}
        </foreach>
    </update>

    <insert id="batchInsertServiceRule">
        INSERT INTO ecpe_t_serv_rule
        (ID,
        servRuleCode,
        servType,
        subServType,
        enterpriseID,
        oneTemplateDayLimit,
        oneDeliveryInterval,
        creatorID,
        operatorID,
        extInfo,
        createTime,
        operateTime,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        periodType
        )
        VALUES
        <foreach collection="list" item="serviceRuleWrapper"
                 separator=",">
            (
            #{serviceRuleWrapper.id},
            #{serviceRuleWrapper.servRuleCode},
            #{serviceRuleWrapper.servType},
            #{serviceRuleWrapper.subServType},
            #{serviceRuleWrapper.enterpriseID},
            #{serviceRuleWrapper.oneTemplateDayLimit},
            #{serviceRuleWrapper.oneDeliveryInterval},
            #{serviceRuleWrapper.creatorID},
            #{serviceRuleWrapper.operatorID},
            #{serviceRuleWrapper.extInfo},
            #{serviceRuleWrapper.createTime},
            #{serviceRuleWrapper.operateTime},
            #{serviceRuleWrapper.reserved1},
            #{serviceRuleWrapper.reserved2},
            #{serviceRuleWrapper.reserved3},
            #{serviceRuleWrapper.reserved4},
            #{serviceRuleWrapper.reserved5},
            #{serviceRuleWrapper.reserved6},
            #{serviceRuleWrapper.reserved7},
            #{serviceRuleWrapper.reserved8},
            #{serviceRuleWrapper.reserved9},
            #{serviceRuleWrapper.reserved10},
            #{serviceRuleWrapper.periodType}
            )
        </foreach>
    </insert>

    <delete id="batchDeleteServiceRule" parameterType="java.util.List">
        delete from ecpe_t_serv_rule where ID in
        <foreach item="id" index="index" collection="list"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="qureyServRuleByCondition" resultMap="serviceRuleWrapper">
        select
        <include refid="serviceRuleCol"/>
        from ecpe_t_serv_rule
        where enterpriseID=#{enterpriseID} and subServType=#{subServType}
    </select>


    <select id="queryDefaultServiceRule" resultMap="serviceRuleWrapper" parameterType="java.util.Map">
        select
        <include refid="serviceRuleCol"/>
        from ecpe_t_serv_rule
        where (enterpriseID is null or enterpriseID = '')
        <if test="servType != null and servType !=''">
            and servType= #{servType}
        </if>
    </select>
</mapper>