<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.DeliveryTimeMapper">
	<resultMap id="deliveryTimeMap"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.DeliveryTimeWrapper">
		<result property="id" column="ID" />
		<result property="cyContID" column="cyContID" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="startTime" column="startTime" />
		<result property="endTime" column="endTime" />
		<result property="createTime" column="createTime" />
		<result property="updateTime" column="updateTime" />
		<result property="operatorID" column="operatorID" />
	</resultMap>
	
	<sql id="delivery_time_wrapper">
		ID,cyContID,enterpriseID,startTime,endTime,createTime,updateTime,operatorID
	</sql>
	
	<insert id="insertDeliveryTime" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.DeliveryTimeWrapper">
		INSERT INTO ecpe_t_cont_deliverytime
		(
			<include refid="delivery_time_wrapper"></include>
		)
		VALUES
		(
		#{id},
		#{cyContID},
		#{enterpriseID},
		#{startTime},
		#{endTime},
		#{createTime},
		#{updateTime},
		#{operatorID}
		)
	</insert>
	
	<delete id="deleteDeliveryTimeByContentID" parameterType="java.lang.Long">
	    delete from ecpe_t_cont_deliverytime where cyContID=#{cyContID}
	</delete>

</mapper>