########################################################
###server
########################################################
spring.profiles.active=dev
spring.application.name: ecpe
server.servlet.context-path=/ecpe
server.port=21002
server.ip=127.0.0.1

########################################################
###datasource
########################################################
mybatis.mapper-locations: classpath*:mapper/mysql/*.xml
cybase.application.name=cy-base-sms
cybase.shumei.application.name=cy-core-shumei
ecpfep.application.url=************:18901
cycore.application.name=cy-core-sms
#cycore.application.name=colortest
management.security.enabled=false


server.servlet.encoding.force=true
server.servlet.encoding.charset=UTF-8

spring.freemarker.allow-request-override=false
spring.freemarker.cache=false
spring.freemarker.check-template-location=true
spring.freemarker.charset=UTF-8
spring.freemarker.content-type=text/html; charset=utf-8
spring.freemarker.prefer-file-system-access=false
spring.freemarker.expose-request-attributes=false
spring.freemarker.expose-session-attributes=false
spring.freemarker.expose-spring-macro-helpers=false
spring.freemarker.suffix=.ftl
spring.freemarker.template-loader-path=classpath:/templates

spring.cloud.stream.kafka.binder.zkNodes=**************:2181,**************:2181,**************:2181
spring.cloud.stream.kafka.binder.brokers=**************:9092,**************:9092,**************:9092
spring.cloud.stream.default-binder=kafka

spring.cloud.config.name=ecpe
spring.cloud.config.enabled=true
spring.cloud.config.label=master
spring.cloud.config.profile=resource,serviceConfig
spring.cloud.config.discovery.enabled=true
spring.cloud.config.discovery.service-id=CY-CONFIG-SERVER

eureka.client.serviceUrl.defaultZone=http://127.0.0.1:19000/eureka/
eureka.instance.preferIpAddress=true
eureka.instance.instanceId=${spring.cloud.client.ip-address}:${server.port}

ribbon.eureka.enabled=true
ribbon.ReadTimeout=20000
ribbon.ConnectTimeout=5000

logging.config.classpath=log4j2.xml

spring.main.allow-bean-definition-overriding=true
spring.config.import=optional:configserver:

feign.circuitbreaker.enabled=true
management.endpoints.web.exposure.include=refresh,health,info