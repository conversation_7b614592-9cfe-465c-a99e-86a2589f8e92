<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.BlackWhiteMapper">
	<resultMap id="blackWhite"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.BlackWhiteWrapper">
		<result property="id" column="ID" />
		<result property="servType" column="servType" />
		<result property="subServType" column="subServType" />
		<result property="blackWhiteListType" column="blackWhiteListType" />
		<result property="memberName" column="memberName" />
		<result property="msisdn" column="msisdn" />
		<result property="isUse" column="isUse" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="operatorID" column="operatorID" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="operateTime" column="operateTime" javaType="java.util.Date" />
		<result property="extInfo" column="extInfo" />
		<result property="reserved1" column="reserved1" />
		<result property="reserved2" column="reserved2" />
		<result property="reserved3" column="reserved3" />
		<result property="reserved4" column="reserved4" />
	</resultMap>


	<select id="getBlackWhiteList" resultMap="blackWhite">
		select ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		isUse,
		enterpriseID,
		operatorID,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from ecpe_t_blackwhite_member where
		<foreach item="blackWhite" collection="list" open=" "
			separator="or" close=" ">
			(enterpriseID=#{blackWhite.enterpriseID} 
			and blackWhiteListType=#{blackWhite.blackWhiteListType} 
			and msisdn=#{blackWhite.msisdn}
			and servType = #{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and subServType is null
			</if>
			)
		</foreach>

	</select>

	<delete id="deleteBlackWhiteList" parameterType="java.util.List">
		delete from ecpe_t_blackwhite_member where
		<foreach item="blackWhite" collection="list" open=" "
			separator="or" close=" ">
			(enterpriseID=#{blackWhite.enterpriseID} 
			and blackWhiteListType=#{blackWhite.blackWhiteListType} 
			and msisdn=#{blackWhite.msisdn}
			and servType = #{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and subServType is null
			</if>
			)
		</foreach>
	</delete>
	<select id="queryBlackWhiteList" resultMap="blackWhite">
		select
		t.ID,
		t.servType,
		t.subServType,
		t.blackWhiteListType,
		t.memberName,
		t.msisdn,
		t.isUse,
		t.enterpriseID,
		t.operatorID,
		t.createTime,
		t.operateTime,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4
		from ecpe_t_blackwhite_member t
		where
		t.enterpriseID =#{enterpriseID}
		<if test="id!=null">and t.ID=#{id}</if>
		<if test="servType!=null">and t.servType=#{servType}</if>
		<if test="subServType!=null and subServType!=''">and t.subServType=#{subServType}</if>
		<if test="blackWhiteListType!=null">and t.blackWhiteListType=#{blackWhiteListType}</if>
		<if test="memberName!=null and memberName!=''">and t.memberName=#{memberName}</if>
		<if test="msisdn!=null and msisdn!=''">and t.msisdn like "%"#{msisdn}"%"</if>
		<if test="isUse!=null">and t.isUse=#{isUse}</if>
		<if test="operatorID!=null">and t.operatorID=#{operatorID}</if>
		<if test="createTime!=null and createTime!=''">and t.createTime=#{createTime}</if>
		<if test="operateTime!=null and operateTime!=''">and t.operateTime=#{operateTime}</if>
		<if test="extInfo!=null and extInfo!=''">and t.extInfo=#{extInfo}</if>
		<if test="reserved1!=null and reserved1!=''">and t.reserved1=#{reserved1}</if>
		<if test="reserved2!=null and reserved2!=''">and t.reserved2=#{reserved2}</if>
		<if test="reserved3!=null and reserved3!=''">and t.reserved3=#{reserved3}</if>
		<if test="reserved4!=null and reserved4!=''">and t.reserved4=#{reserved4}</if>

		limit #{pageNo},#{pageSize}
	</select>

	<select id="countBlackWhite" resultType="java.lang.Integer">
		select
		count(*)
		from ecpe_t_blackwhite_member t where
		t.enterpriseID =#{enterpriseID}
		<if test="id!=null">and t.ID=#{id}</if>
		<if test="servType!=null">and t.servType=#{servType}</if>
		<if test="subServType!=null and subServType!=''">and t.subServType=#{subServType}</if>
		<if test="blackWhiteListType!=null">and t.blackWhiteListType=#{blackWhiteListType}</if>
		<if test="memberName!=null and memberName!=''">and t.memberName=#{memberName}</if>
		<if test="msisdn!=null and msisdn!=''">and t.msisdn like "%"#{msisdn}"%"</if>
		<if test="isUse!=null">and t.isUse=#{isUse}</if>
		<if test="operatorID!=null">and t.operatorID=#{operatorID}</if>
		<if test="createTime!=null and createTime!=''">and t.createTime=#{createTime}</if>
		<if test="operateTime!=null and operateTime!=''">and t.operateTime=#{operateTime}</if>
		<if test="extInfo!=null and extInfo!=''">and t.extInfo=#{extInfo}</if>
		<if test="reserved1!=null and reserved1!=''">and t.reserved1=#{reserved1}</if>
		<if test="reserved2!=null and reserved2!=''">and t.reserved2=#{reserved2}</if>
		<if test="reserved3!=null and reserved3!=''">and t.reserved3=#{reserved3}</if>
		<if test="reserved4!=null and reserved4!=''">and t.reserved4=#{reserved4}</if>
	</select>

	<select id="searchBlackWhiteList" resultMap="blackWhite">
		select ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		isUse,
		enterpriseID,
		operatorID,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		from ecpe_t_blackwhite_member t where
		<foreach item="blackWhite" collection="list" open=" "
			separator="or" close=" ">
			(t.enterpriseID=#{blackWhite.enterpriseID} 
			and t.blackWhiteListType=#{blackWhite.blackWhiteListType} 
			and t.msisdn=#{blackWhite.msisdn}
			and t.servType = #{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and t.subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and t.subServType is null
			</if>
			)
		</foreach>
	</select>

	<insert id="addBlackWhiteList">
		insert into ecpe_t_blackwhite_member
		(
		ID,
		servType,
		subServType,
		blackWhiteListType,
		memberName,
		msisdn,
		isUse,
		enterpriseID,
		operatorID,
		createTime,
		operateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4
		)
		values
		<foreach collection="list" item="blackWhite" separator=",">
			(
			next value for MYCATSEQ_ECPE_T_BLACKWHITE_MEMBER,
			#{blackWhite.servType},
			#{blackWhite.subServType},
			#{blackWhite.blackWhiteListType},
			#{blackWhite.memberName},
			#{blackWhite.msisdn},
			<if test="blackWhite.isUse != null">
				#{blackWhite.isUse},
			</if>
			<if test="blackWhite.isUse == null">
				'1',
			</if>
			#{blackWhite.enterpriseID},
			#{blackWhite.operatorID},
			#{blackWhite.createTime},
			#{blackWhite.operateTime},
			#{blackWhite.extInfo},
			#{blackWhite.reserved1},
			#{blackWhite.reserved2},
			#{blackWhite.reserved3},
			#{blackWhite.reserved4}
			)
		</foreach>
	</insert>


	<update id="updateBlackWhiteList" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index" item="blackWhite"
			open="" separator=";">
			update ecpe_t_blackwhite_member
			<trim prefix="set" suffixOverrides=",">
				<!-- <if test="blackWhite.servType != null">
					servType=#{blackWhite.servType},
				</if>
				<if test="blackWhite.subServType != null">
					subServType=#{blackWhite.subServType},
				</if> -->
				<if test="blackWhite.memberName != null and blackWhite.memberName != ''">
					memberName=#{blackWhite.memberName},
				</if>
				<if test="blackWhite.isUse != null">
					isUse=#{blackWhite.isUse},
				</if>
				<if test="blackWhite.operatorID != null">
					operatorID=#{blackWhite.operatorID},
				</if>
				<if test="blackWhite.operateTime != null">
					operateTime=#{blackWhite.operateTime}
				</if>
			</trim>
			where msisdn=#{blackWhite.msisdn}
			and blackWhiteListType=#{blackWhite.blackWhiteListType}
			and enterpriseID=#{blackWhite.enterpriseID}
			and servType = #{blackWhite.servType}
			<if test="blackWhite.subServType != null">
				and subServType = #{blackWhite.subServType}
			</if>
			<if test="blackWhite.subServType == null">
				and subServType is null
			</if>
		</foreach>
	</update>
	
	<select id="checkBlackWhite" resultType="java.lang.Integer">
		select
		count(*)
		from ecpe_t_blackwhite_member t where 1=1
		<if test="enterpriseID!=null">and t.enterpriseID=#{enterpriseID}</if>
		<if test="msisdn!=null and msisdn!=''">and t.msisdn=#{msisdn}</if>
	</select>

	<!-- 投递接口调用 -->
	<select id="obtainBlackWhiteList" resultMap="blackWhite">
		select
		t.ID,
		t.servType,
		t.subServType,
		t.blackWhiteListType,
		t.memberName,
		t.msisdn,
		t.isUse,
		t.enterpriseID,
		t.operatorID,
		t.createTime,
		t.operateTime,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4
		from ecpe_t_blackwhite_member t
		where
		t.enterpriseID=#{enterpriseID}
		<if test="blackWhiteListType!=null">and t.blackWhiteListType=#{blackWhiteListType}</if>
		<if test="servType!=null">and t.servType=#{servType}</if>
		<choose>
			<when test="subServType!=null">
				and t.subServType=#{subServType}
			</when>
			<otherwise>
				<if test="servType eq 2">and t.subServType is null</if>
			</otherwise>
		</choose>
	</select>

	<select id="queryBlackRed" resultMap="blackWhite">
		select
		t.ID,
		t.servType,
		t.subServType,
		t.blackWhiteListType,
		t.memberName,
		t.msisdn,
		t.isUse,
		t.enterpriseID,
		t.operatorID,
		t.createTime,
		t.operateTime,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4
		from ecpe_t_blackwhite_member t
		WHERE
		t.msisdn = #{msisdn}
	</select>
</mapper>
