<!DOCTYPE html>
<html>
  <head lang="en">
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>号码认证详情</title>
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../../css/bootstrap.min.css"
    />
    <link href="../../../../css/reset.css" rel="stylesheet" />
    <link href="../../../../css/searchList.css" rel="stylesheet" />
    <script
      type="text/javascript"
      src="../../../../frameworkJs/angular.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../frameworkJs/jquery-3.5.0.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../frameworkJs/jquery.cookie.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../frameworkJs/bootstrap.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../frameworkJs/angular-translate/angular-translate.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../service/angular-i18n/angular-i18n.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../service/utils/service-ajax.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../service/utils/service-common.js"
    ></script>
    <!-- 引入菜单组件 -->
    <link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet" />
    <script
      type="text/javascript"
      src="../../../../directives/topMenu/topMenu.js"
    ></script>
    <!--分页-->
    <script
      type="text/javascript"
      src="../../../../directives/page/page.js"
    ></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../../directives/page/page.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../../css/font-awesome.min.css"
    />
    <script type="text/javascript" src="check.js"></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../../css/hotlineContentManage.css"
    />
    <script
      type="text/javascript"
      src="../../../../frameworkJs/bootstrap-datepicker.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"
    ></script>
    <link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet" />
    <link href="../../../../css/datepicker3.css" rel="stylesheet" />
    <script
      type="text/javascript"
      src="../../../../frameworkJs/daterangepicker/daterangepicker.min.js"
    ></script>
    <link
      rel="stylesheet"
      href="../../../../css/daterangepicker.min.css"
      rel="stylesheet"
    />

    <style>
      .cooperation-manage .coorPeration-table th,
      td {
        padding-left: 20px !important;
      }

      .handle ul li icon.view-icon {
        background-position: -55px 0;
      }

      .table th.adjustable-width {
        width: 25%;
      }

      /* media for adjustable search-table width  */
      @media (max-width: 1850px) {
        .table th.adjustable-width {
          width: 28%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      @media (max-width: 1600px) {
        .table th.adjustable-width {
          width: 30%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      @media (max-width: 1300px) {
        .table th.adjustable-width {
          width: 33%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      @media (max-width: 1100px) {
        .table th.adjustable-width {
          width: 42%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      .label-supply {
        display: inline-block;
        float: left;
        padding-right: 15px;
        padding-left: 15px;
      }
      .clearf:after {
        content: "";
        clear: both;
        height: 0;
        display: block;
      }

      .date-picker-container {
        position: relative;
        display: inline-block;
      }

      .date-picker-container input {
        padding-right: 30px;
      }

      .date-picker-container i {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
      }

      .input-daterange {
        padding-top: 0px !important;
      }

      /* 状态标签样式 */
      .status-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: 500;
      }

      .status-pending {
        background-color: #fcf8e3;
        color: #8a6d3b;
        border: 1px solid #faebcc;
      }

      .status-success {
        background-color: #dff0d8;
        color: #3c763d;
        border: 1px solid #d6e9c6;
      }

      .status-reject {
        background-color: #f2dede;
        color: #a94442;
        border: 1px solid #ebccd1;
      }

      .status-online {
        background-color: #d9edf7;
        color: #31708f;
        border: 1px solid #bce8f1;
      }

      .status-offline {
        background-color: #f5f5f5;
        color: #777;
        border: 1px solid #ddd;
      }

      .loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
    </style>
  </head>

  <body ng-app="myApp" ng-controller="checkController" ng-init="init()">
    <div class="cooperation-manage" style="overflow-x: scroll">
      <div class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate">
        </span
        >&nbsp;> <span class="second-tab">号码认证</span>&nbsp;&gt;&nbsp;<span
          class="third-tab"
          >号码认证详情</span
        >
      </div>

      <div class="cooperation-search">
        <form class="form-horizontal">
          <div
            class="form-group form-inline"
            style="margin-left: 0px; margin-right: 0px"
          >
            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="authMsisdn">认证号码</label>
            </div>
            <div
              class="col-lg-2 col-md-2 col-sm-2 col-xs-2"
              style="width: 21.666667%"
            >
              <input
                ng-model="queryParams.authMsisdn"
                type="text"
                autocomplete="off"
                class="form-control"
                id="authMsisdn"
                placeholder="请输入认证号码"
              />
            </div>

            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="enterpriseName">公司名称</label>
            </div>
            <div
              class="col-lg-2 col-md-2 col-sm-2 col-xs-2"
              style="width: 21.666667%"
            >
              <input
                ng-model="queryParams.enterpriseName"
                type="text"
                autocomplete="off"
                class="form-control"
                id="enterpriseName"
                placeholder="请输入公司名称"
              />
            </div>

            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="showName">展示名称</label>
            </div>
            <div
              class="col-lg-2 col-md-2 col-sm-2 col-xs-2"
              style="width: 21.666667%"
            >
              <input
                ng-model="queryParams.showName"
                type="text"
                autocomplete="off"
                class="form-control"
                id="showName"
                placeholder="请输入展示名称"
              />
            </div>
            <div class="clearf"></div>
          </div>

          <div
            class="form-group form-inline"
            style="margin-left: 0px; margin-right: 0px"
          >
            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="auditStatus">号码审核状态</label>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width: 16%">
              <select
                style="max-width: 200px; width: 100%"
                class="form-control"
                ng-model="queryParams.auditStatus"
                id="auditStatus"
                ng-options="x.id as x.name for x in auditStatusOptions"
              ></select>
            </div>

            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="onLineState">上线状态</label>
            </div>
            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2" style="width: 16%">
              <select
                style="max-width: 200px; width: 100%"
                class="form-control"
                ng-model="queryParams.onLineState"
                id="onLineState"
                ng-options="x.id as x.name for x in onlineStatusOptions"
              ></select>
            </div>

            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="start">时间范围</label>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width: 30%">
              <div class="input-daterange input-group" id="datepicker">
                <input
                  type="text"
                  class="input-md form-control"
                  autocomplete="off"
                  id="start"
                  ng-keyup="searchOn()"
                  placeholder="开始日期"
                />
                <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                <input
                  type="text"
                  class="input-md form-control"
                  autocomplete="off"
                  id="end"
                  ng-keyup="searchOn()"
                  placeholder="结束日期"
                />
              </div>
            </div>

            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
              <button ng-click="search()" type="submit" class="btn search-btn">
                <icon class="search-iocn"></icon>
                <span ng-bind="'COMMON_SEARCH'|translate"></span>
              </button>
            </div>
            <div class="clearf"></div>
          </div>
        </form>
      </div>

      <div class="add-table">
        <button id="exportDataBtn" class="btn add-btn" ng-click="exportData()">
          <icon class="export-icon"></icon>
          <span
            style="color: #705de1"
            ng-bind="'DETAIL_EXPORT'|translate"
            style="margin-left: 5px"
          ></span>
        </button>

        <button
          id="goBackBtn"
          class="btn add-btn"
          ng-click="goBack()"
          style="margin-left: 15px"
        >
          <icon class="back-icon"></icon>
          <span style="color: #705de1">返回</span>
        </button>
      </div>

      <div style="margin-left: 20px; margin-bottom: 20px">
        <p style="font-size: 16px; font-weight: 500">号码认证详情</p>
      </div>

      <div class="coorPeration-table">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th style="width: 5%">序号</th>
              <th style="width: 12%">认证号码</th>
              <th style="width: 12%">公司名称</th>
              <th style="width: 12%">展示名称</th>
              <th style="width: 10%">LOGO</th>
              <th style="width: 10%">审核状态</th>
              <th style="width: 10%">上线状态</th>
              <th style="width: 12%">开始时间</th>
              <th style="width: 12%">结束时间</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="item in listData">
              <td>
                {{pageInfo && pageInfo.length > 0 ? (pageInfo[0].currentPage -
                1) * pageInfo[0].pageSize + $index + 1 : $index + 1}}
              </td>
              <td>
                <span title="{{item.authMsisdn}}"
                  >{{item.authMsisdn || '-'}}</span
                >
              </td>
              <td>
                <span title="{{item.enterpriseName}}"
                  >{{item.enterpriseName || '-'}}</span
                >
              </td>
              <td>
                <span title="{{item.showName}}">{{item.showName || '-'}}</span>
              </td>
              <td>{{item.logoFlag == 1 ? '有' : '无'}}</td>
              <td>
                <span
                  ng-class="{
                        'status-badge status-pending': item.auditStatus == '1',
                        'status-badge status-success': item.auditStatus == '2',
                        'status-badge status-reject': item.auditStatus == '3'
                    }"
                  title="{{getAuditStatusText(item.auditStatus)}}"
                >
                  {{getAuditStatusText(item.auditStatus) || '-'}}
                </span>
              </td>
              <td>
                <span
                  ng-class="{
                        'status-badge status-offline': item.onLineState == '1',
                        'status-badge status-online': item.onLineState == '2',
                        'status-badge status-reject': item.onLineState == '3'
                    }"
                  title="{{getOnlineStatusText(item.onLineState)}}"
                >
                  {{getOnlineStatusText(item.onLineState) || '-'}}
                </span>
              </td>
              <td
                ng-bind="formatDateTime(item.startTime)"
                title="{{formatDateTime(item.startTime)}}"
              ></td>
              <td
                ng-bind="formatDateTime(item.endTime)"
                title="{{formatDateTime(item.endTime)}}"
              ></td>
            </tr>
            <!-- 没有数据时显示 -->
            <tr ng-show="listData.length<=0">
              <td
                style="text-align: center"
                colspan="9"
                ng-bind="'COMMON_NODATA'|translate"
              ></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div>
        <ptl-page tableId="0" change="search('justPage')"></ptl-page>
      </div>
    </div>

    <!--提示弹出框-->
    <div
      class="modal fade bs-example-modal-sm"
      id="myModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="myModalLabel"
    >
      <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
            <h4
              class="modal-title"
              id="myModalLabel"
              ng-bind="'COMMON_TIP'|translate"
            ></h4>
          </div>
          <div class="modal-body">
            <div class="text-center">
              <p style="font-size: 16px; color: #383838">{{tip|translate}}</p>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="submit"
              class="btn"
              data-dismiss="modal"
              aria-label="Close"
              ng-bind="'COMMON_OK'|translate"
            ></button>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
