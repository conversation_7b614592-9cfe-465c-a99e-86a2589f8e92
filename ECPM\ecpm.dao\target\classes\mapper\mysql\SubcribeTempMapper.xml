<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SubcribeTempMapper">
    <resultMap id="SubcribeTempWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.SubcribeTempWrapper">
        <result property="id" column="ID" javaType="java.lang.Integer" />
        <result property="customerCode" column="customerCode" javaType="java.lang.String" />
        <result property="orderCode" column="orderCode" javaType="java.lang.String" />
        <result property="insertTime" column="insertTime" javaType="java.util.Date" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
    </resultMap>
    


	<insert id="createTempSubcribe" >
		<selectKey keyProperty="id" resultType="java.lang.Integer" order="AFTER">
			select LAST_INSERT_ID() as id
		</selectKey>
		insert into  ecpm_t_subcribe_temp
		(
		customerCode,
		orderCode,
		insertTime,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5
		)
		values
		(
		#{customerCode},
		#{orderCode},
		#{insertTime},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5}
		)
	</insert>


</mapper>