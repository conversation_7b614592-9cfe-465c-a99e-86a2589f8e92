<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css">
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/mian.css" rel="stylesheet"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet"/>
    <link href="../../../../../css/layout.css" rel="stylesheet"/>
    <link href="../../../../../css/bootstrap-datepicker.css" rel="stylesheet">
    <link href="../../../../../css/createOrder.css" rel="stylesheet">
    <script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/moment/moment.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <!--引入JS-->

    <script type="text/javascript" src="updateQuota.js"></script>

    <style>

        .check-li span {
            vertical-align: middle;
            cursor: pointer;
        }

        .min-width-li {
            min-width: 130px;
        }

        .min-input {
            min-width: 230px;
        }

        .error-ver img, .error-ver > span {
            vertical-align: middle;
        }

        .error-ver {
            color: red;
        }

        .rest-tip {
            padding-left: 15px;
            padding-right: 15px;
            float: left;
        }
    </style>
</head>

<body>
<div ng-app="myApp" ng-init="init();" ng-controller="updateQuotaCtrl" class="body-min-width">
    <div class="cooperation-manage">
        <div class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate" ng-show="isSuperManager"></span>
            <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate" ng-show="isAgent"></span>
            &nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'COMMON_QUOTAMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'MODIFY_PEIE'|translate"></span>
        </div>
        <!--<top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/quota/createQuota"-->
        <!--list-index="4"></top:menu>-->

        <div class="cooper-title" ng-bind="'CREATEORDER_ORDERINFO'|translate"></div>

        <div class="cooper-tab">
            <form class="form-horizontal" name="orderBase" novalidate>
                <div class="form-group">
                    <div>
                        <!--企业名称-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"
                               ng-bind="'CREATEORDER_ENTERPRISENAME'|translate"></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3"><p ng-bind="subEnterpriseName"></p></div>

                        <!--业务类别-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_SERVTYPE'|translate"></span>
                        </label>

                        <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                            <span ng-show="orderInfo.servType===1" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>
                            <span ng-show="orderInfo.servType===2" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></span>
                            <span ng-show="orderInfo.servType===3" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
                            <span ng-show="orderInfo.servType===4" ng-bind="'GROUP_SEND'|translate"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div>
                        <!--是否体验版-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_EXPERIENCE'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">
                            <span ng-show="orderInfo.isExperience===0" ng-bind="'NO'|translate"></span>
                            <span ng-show="orderInfo.isExperience===1" ng-bind="'YES'|translate"></span>
                        </div>

                        <!--订单名称-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">订单名称：</label>

                        <div class="col-lg-3 col-xs-4 col-sm-3 col-md-3">
                            <input type="text" ng-model="orderInfo.extInfo.quotaOrderName" class="form-control"
                                   disabled/>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div>
                        <!--订单金额-->
                        <label for="amount" class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_AMOUNT'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">

                            <input type="text" class="form-control" autocomplete="off"
                                   placeholder="{{'CREATEORDER_INPUTAMOUNT'|translate}}" name="amount" id="amount"
                                   ng-model="orderInfo.amount" required
                                   pattern="(^[0-9]{1,17}$)|(^[0-9]{1,17}[\.]{1}[0-9]{1,3}$)">
                            <span style="color:red" class="error-ver"
                                  ng-show="orderBase.amount.$dirty && orderBase.amount.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                                         align="absmiddle">
									<span ng-show="orderBase.amount.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderBase.amount.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXAMOUNT'|translate"></span>
								</span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- 业务配额代码 -->
        <div class="cooper-title">
            <span class="red">*</span>
            <span ng-bind="'CREATEORDER_ORDERITEMLIST'|translate"></span>
            <span style="color:red" class="error-ver" ng-show="!(postPingXianCMCC || postPingXianCUCC || postPingXianCTCC || postGuaDuan || postGuaCai ||postZC)">
					<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
                         style="vertical-align: middle">
					<span ng-bind="'CREATEORDER_MININPUTDESC'|translate"
                          style="vertical-align: middle;font-size: 14px"></span>
				</span>
        </div>
        <div class="cooper-tab">
            <form class="form-horizontal" name="orderItemDomain" novalidate style="margin-left: 70px">
                <div ng-show="hasCMCC">
                    <div class="form-group">
                        <div class="row">
                            <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                                <span>移动</span>：
                            </li>
                        </div>
                    </div>
                    <!--屏显配额-->
                    <div ng-show="hasPX_cmcc">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate" ng-show="orderInfo.servType !=3 && orderInfo.servType !='4'"></span>
                                    <span ng-show="orderInfo.servType =='4'">屏显：</span>
                                    <!--<span ng-bind="'CREATEORDER_GUANGGAOPEIE'|translate"-->
                                          <!--ng-show="orderInfo.servType ==3"></span>     -->
                                    <span ng-bind="'CREATEORDER_GUANGGAOANDPEIE'|translate"
                                          ng-show="orderInfo.servType ==3"></span>
                                </div>
                            </div>
                        </div>
                        <div ng-show="postPingXianCMCC">
                            <div class="form-group" ng-show="isPXLimit&&orderInfo.servType !='3'">
                                <!--不限-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[pxType===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>
                            <div class="form-group" ng-show="isPXanci">
                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
									<span class="redio-btn" ng-class="{true:'checked',false:''}[pxType===1]"
                                          id="pingXianType2"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="pxType===1 && postPingXianCMCC && isExperience===0">
                                        <span ng-bind="PXPrice" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{px_anciRest_tip}}" ng-disabled="pxType!==1" name="anci"
                                           ng-model="anci" required pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(PXPrice,anci,pinxian_amount,anci_temp,'pxanci')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.anci.$invalid && pxType===1 && postPingXianCMCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="orderItemDomain.anci.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.anci.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                    <span class="error-ver"
                                          ng-show="(px_anci_over_error_cmcc || px_anci_lower_error_cmcc) && !orderItemDomain.anci.$invalid  && pxType===1 && postPingXianCMCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="px_anci_over_error_cmcc"
                                                  ng-bind="'GO_BEYOND'|translate"></span>
											<span ng-show="px_anci_lower_error_cmcc"
                                                  ng-bind="'LOWER_THAN'|translate"></span>
                                                  <span ng-show="px_anci_lower_error_cmcc" ng-bind="px_anciMin_cmcc"></span>
										</span>
                                </div>
                                <div ng-bind="px_anciRest_tip" class="rest-tip"></div>
                                <div class="col-xs-1" ng-show="pxType===1 && postPingXianCMCC && isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="pxanci_price||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                            <div class="form-group" ng-show="isPXbaoyue&&orderInfo.servType !='3'">
                                <!--按人/包月-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
									<span class="redio-btn" id="pingXianType3"
                                          ng-class="{true:'checked',false:''}[pxType===2]"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           ng-keyup="accMul(baoyue_unitPrice,baoyue,memberCount,baoyue_temp,'baoyue')"
                                           placeholder="{{baoyueRest_tip}}" ng-disabled="pxType!==2" name="baoyue"
                                           ng-model="baoyue" required pattern="^[0-9]{1,9}$">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.baoyue.$invalid && pxType===2 && postPingXianCMCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="orderItemDomain.baoyue.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.baoyue.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                    <span class="error-ver"
                                          ng-show="(baoyue_over_error || baoyue_lower_error) && !orderItemDomain.baoyue.$invalid  && pxType===2 && postPingXianCMCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="baoyue_over_error" ng-bind="'GO_BEYOND'|translate"></span>
											<span ng-show="baoyue_lower_error" ng-bind="'LOWER_THAN'|translate"></span>
											<span ng-show="baoyue_lower_error" ng-bind="baoyueMin"></span>
										</span>
                                </div>
                                <div class="check-li col-xs-1" style="min-width: 100px;max-width: 100px">
                                    <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                                </div>
                                <div class="col-xs-2" ng-bind="productName" style="max-width: 130px"></div>
                                <div ng-bind="baoyueRest_tip" class="rest-tip"></div>
                                <div class="col-xs-1" ng-show="isExperience===0">
                                    <span ng-bind="baoyue_price"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--挂机短信-->
                    <div ng-show="hasGD_cmcc&&orderInfo.servType !='3'">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="hasGD">
                            <!--不限-->
                            <div class="form-group" ng-show="isGDLimit">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[gdType===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="isGDanci">

                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
									<span class="redio-btn" ng-class="{true:'checked',false:''}[gdType===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="gdType===1 && postGuaDuan && isExperience===0">
                                        <span ng-bind="GDPrice" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="postGuaDuan&&gdType===1">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="gdanci"
                                           ng-disabled="gdType==0"
                                           name="gdanci" placeholder="{{guaduan_anciRest_tip}}" required
                                           pattern="^[0-9]{1,9}$" ng-show="postGuaDuan"
                                           ng-keyup="accMul(GDPrice,gdanci,guaduan_amount,guaduanInput_temp,'gdanci')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.gdanci.$invalid && gdType===1 && postGuaDuan">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.gdanci.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.gdanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(guaduan_anci_over_error || guaduan_anci_lower_error) && !orderItemDomain.gdanci.$invalid  && gdType===1 && postGuaDuan">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="guaduan_anci_over_error" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="guaduan_anci_lower_error" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="guaduan_anci_lower_error" ng-bind="px_anciMin_cmcc"></span>
								</span>
                                </div>
                                <div ng-bind="guaduan_anciRest_tip" class="rest-tip"
                                     ng-show="postGuaDuan&&gdType===1"></div>
                                <div class="col-xs-1" ng-show="gdType==1 && postGuaDuan && isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="guaduan_price||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                            <!--包月-->
                            <div class="form-group" ng-show="isGDbaoyue">
                                <!--按人/包月-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
									<span class="redio-btn" id="guaDuanType3"
                                          ng-class="{true:'checked',false:''}[gdType===2]"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           ng-keyup="accMul(gdbaoyue_unitPrice,gdbaoyue,gdmemberCount,gdbaoyue_temp,'gdbaoyue')"
                                           placeholder="{{gdbaoyueRest_tip}}" ng-disabled="gdType!==2" name="gdbaoyue"
                                           ng-model="gdbaoyue" required pattern="^[0-9]{1,9}$">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.gdbaoyue.$invalid && gdType===2 && postGuaDuan">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="orderItemDomain.gdbaoyue.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.gdbaoyue.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                    <span class="error-ver"
                                          ng-show="(gdbaoyue_over_error || gdbaoyue_lower_error) && !orderItemDomain.gdbaoyue.$invalid  && gdType===2 && postGuaDuan">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="gdbaoyue_over_error" ng-bind="'GO_BEYOND'|translate"></span>
											<span ng-show="gdbaoyue_lower_error"
                                                  ng-bind="'LOWER_THAN'|translate"></span>
                                                  <span ng-show="gdbaoyue_lower_error" ng-bind="gdbaoyueMin"></span>
										</span>
                                </div>
                                <div class="check-li col-xs-1" style="min-width: 100px;max-width: 100px">
                                    <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                                </div>
                                <div class="col-xs-2" ng-bind="gdproductName" style="max-width: 130px"></div>
                                <div ng-bind="gdbaoyueRest_tip" class="rest-tip"></div>
                                <div class="col-xs-1" ng-show="isExperience===0">
                                    <span ng-bind="gdbaoyue_price"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>
					<!--挂机彩信-->
                    <div ng-show="hasGC_cmcc&&orderInfo.servType !='3'">
                        
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'CREATEORDER_GUACAI'|translate"  ng-show="orderInfo.servType !='4'"></span>
                                    <span ng-show="orderInfo.servType =='4'">彩信： </span>
                                </div>
                            </div>
                        </div>
                        <div ng-show="hasGC">
                            <!--不限-->
                            <div class="form-group" ng-show="isGCLimit">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[gcType===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="isGCanci">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn" ng-class="{true:'checked',false:''}[gcType===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="gcType===1 && postGuaCai && isExperience===0">
                                        <span ng-bind="GCPrice" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="postGuaCai&&gcType===1">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="gcanci"
                                           ng-disabled="gcType==0"
                                           name="gcanci" placeholder="{{guacai_anciRest_tip}}" required
                                           pattern="^[0-9]{1,9}$" ng-show="postGuaCai"
                                           ng-keyup="accMul(GCPrice,gcanci,guacai_amount,guacaiInput_temp,'gcanci')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.gcanci.$invalid && gcType===1 && postGuaCai">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.gcanci.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.gcanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(guacai_anci_over_error || guacai_anci_lower_error) && !orderItemDomain.gcanci.$invalid  && gcType===1 && postGuaCai">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="guacai_anci_over_error" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="guacai_anci_lower_error" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="guacai_anci_lower_error" ng-bind="guacai_anciMin"></span>
								</span>
                                </div>
                                <div ng-bind="guacai_anciRest_tip" class="rest-tip"
                                     ng-show="postGuaCai&&gcType===1"></div>
                                <div class="col-xs-1" ng-show="gcType==1 && postGuaCai && isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="guacai_price||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>

                            <!--包月-->
                            <div class="form-group" ng-show="isGCbaoyue">
                                <!--按人/包月-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
									<span class="redio-btn" id="guaCaiType3"
                                          ng-class="{true:'checked',false:''}[gcType===2]"></span>
                                    <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           ng-keyup="accMul(gcbaoyue_unitPrice,gcbaoyue,gcmemberCount,gcbaoyue_temp,'gcbaoyue')"
                                           placeholder="{{gcbaoyueRest_tip}}" ng-disabled="gcType!==2" name="gcbaoyue"
                                           ng-model="gcbaoyue" required pattern="^[0-9]{1,9}$">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.gcbaoyue.$invalid && gcType===2 && postGuaCai">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="orderItemDomain.gcbaoyue.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.gcbaoyue.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                    <span class="error-ver"
                                          ng-show="(gcbaoyue_over_error || gcbaoyue_lower_error) && !orderItemDomain.gcbaoyue.$invalid  && gcType===2 && postGuaCai">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="gcbaoyue_over_error" ng-bind="'GO_BEYOND'|translate"></span>
											<span ng-show="gcbaoyue_lower_error"
                                                  ng-bind="'LOWER_THAN'|translate"></span>
                                                  <spanng-show="gcbaoyue_lower_error" ng-bind="guacai_anciMin"></span>
										</span>
                                </div>
                                <div class="check-li col-xs-1" style="min-width: 100px;max-width: 100px">
                                    <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                                </div>
                                <div class="col-xs-2" ng-bind="gcproductName" style="max-width: 130px"></div>
                                <div ng-bind="gcbaoyueRest_tip" class="rest-tip"></div>
                                <div class="col-xs-1" ng-show="isExperience===0">
                                    <span ng-bind="gcbaoyue_price"></span>
                                    <span ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>

                        </div>

                    </div>

                    <!--增彩群发-->
                    <div ng-show="hasZC_cmcc&&(orderInfo.servType =='2'||orderInfo.servType =='4')">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'ZENGCAI'|translate" ng-show="orderInfo.servType =='4'"></span>
                                    <span ng-bind="'CREATEORDER_ZENGCAI'|translate" ng-show="orderInfo.servType =='2'"></span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="hasZC">
                            <!--体验不限-->
                            <div class="form-group" ng-show="isZCLimit">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="isZCanci">

                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn" ng-class="{true:'checked',false:''}[zcType===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="zcType===1 && postZC">
                                        <span ng-bind="ZCPrice" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="postZC&&zcType===1">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="zcanci"

                                           name="zcanci" placeholder="{{zengcai_anciRest_tip}}" required
                                           pattern="^[0-9]{1,9}$" ng-show="postZC"
                                           ng-keyup="accMul(ZCPrice,zcanci,zengcai_amount,zengcaiInput_temp,'zcanci')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.zcanci.$invalid  && postZC">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.zcanci.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.zcanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(zengcai_anci_over_error || zengcai_anci_lower_error) && !orderItemDomain.zcanci.$invalid  && zcType===1 && postZC">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="zengcai_anci_over_error" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="zengcai_anci_lower_error" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="zengcai_anci_lower_error" ng-bind="zengcai_anciMin"></span>
									
								</span>
                                </div>
                                <div ng-bind="zengcai_anciRest_tip" class="rest-tip"
                                     ng-show="postZC && zcType===1"></div>
                                <div class="col-xs-1" ng-show="zcType==1 && postZC"
                                     style="white-space: nowrap">
                                    <span ng-bind="zengcai_price||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!--短信群发-->
                    <div ng-show="hasGroupSendSMSCMCC&&orderInfo.servType =='4'">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span >短信：</span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="true">
                            <!--体验不限-->
                            <div class="form-group" ng-show="groupSendSMSCMCCNoLimit">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="!groupSendSMSCMCCNoLimit">
                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn" ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;">
                                        <span ng-bind="groupSendSMSCMCCPrice" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="!groupSendSMSCMCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="groupSendSMSCMCCanci"

                                           name="groupSendSMSCMCCanci" placeholder="{{groupSendSMSCMCC_tip}}" required
                                           pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(groupSendSMSCMCCPrice,groupSendSMSCMCCanci,groupSendSMSCMCCAmount,groupSendSMSCMCC_temp,'groupSendSMSCMCC')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.groupSendSMSCMCCanci.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.groupSendSMSCMCCanci.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.groupSendSMSCMCCanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(groupSendSMSCMCC_anci_over_error || groupSendSMSCMCC_lower_error) && !orderItemDomain.groupSendSMSCMCCanci.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="groupSendSMSCMCC_anci_over_error" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="groupSendSMSCMCC_lower_error" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="groupSendSMSCMCC_lower_error" ng-bind="groupSendSMSCMCC_anciMin"></span>

								</span>
                                </div>
                                <div ng-bind="groupSendSMSCMCC_tip" class="rest-tip"
                                    ></div>
                                <div class="col-xs-1"
                                     style="white-space: nowrap">
                                    <span ng-bind="groupSendSMSCMCC_Price||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--联通-->
                <div ng-show="hasCUCC">
                    <div class="form-group" >
                        <div class="row">
                            <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                                <span>联通</span>：
                            </li>
                        </div>
                    </div>
					
					<!--屏显配额-->
                    <div ng-show="hasPX_cucc">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate" ng-show="orderInfo.servType !='4'"></span>
                                    <span ng-show="orderInfo.servType =='4'">屏显：</span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="postPingXianCUCC">
                            <div class="form-group" ng-show="isPXLimit_cucc&&orderInfo.servType !='3'">
                                <!--不限-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[pxType_cucc===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>
                            <div class="form-group" ng-show="isPXanci_cucc">
                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn"
                                          ng-class="{true:'checked',false:''}[pxType_cucc===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="pxType_cucc===1 && postPingXianCUCC && isExperience===0">
                                        <span ng-bind="PXPrice_cucc" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{px_anciRest_tip_cucc}}" ng-disabled="pxType_cucc!==1"
                                           name="anci_cucc"
                                           ng-model="anci_cucc" required pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(PXPrice_cucc,anci_cucc,pinxian_amount_cucc,anci_temp_cucc,'pxanci_cucc')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.anci_cucc.$invalid && pxType_cucc===1 && postPingXianCUCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="orderItemDomain.anci_cucc.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.anci_cucc.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
										</span>
                                    <span class="error-ver"
                                          ng-show="(px_anci_over_error_cucc || px_anci_lower_error_cucc) && !orderItemDomain.anci_cucc.$invalid  && pxType_cucc===1 && postPingXianCUCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="px_anci_over_error_cucc"
                                                  ng-bind="'GO_BEYOND'|translate"></span>
											<span ng-show="px_anci_lower_error_cucc"
                                                  ng-bind="'LOWER_THAN'|translate"></span>
                                                  <span ng-show="px_anci_lower_error_cucc" ng-bind="px_anciMin_cucc"></span>
										</span>
                                </div>
                                <div ng-bind="px_anciRest_tip_cucc" class="rest-tip"></div>
                                <div class="col-xs-1" ng-show="pxType_cucc===1 && postPingXianCUCC && isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="pxanci_price_cucc||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!--短信群发-->
                    <div ng-show="hasGroupSendSMSCUCC&&orderInfo.servType =='4'">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span >短信：</span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="true">
                            <!--体验不限-->
                            <div class="form-group" ng-show="groupSendSMSCUCCNoLimit">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="!groupSendSMSCUCCNoLimit">
                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn" ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;">
                                        <span ng-bind="groupSendSMSCUCCPrice" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="!groupSendSMSCUCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="groupSendSMSCUCCanci"

                                           name="groupSendSMSCUCCanci" placeholder="{{groupSendSMSCUCC_tip}}" required
                                           pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(groupSendSMSCUCCPrice,groupSendSMSCUCCanci,groupSendSMSCUCCAmount,groupSendSMSCUCC_temp,'groupSendSMSCUCC')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.groupSendSMSCUCCanci.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.groupSendSMSCUCCanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(groupSendSMSCUCC_anci_over_error || groupSendSMSCUCC_lower_error) && !orderItemDomain.groupSendSMSCUCCanci.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="groupSendSMSCUCC_anci_over_error" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="groupSendSMSCUCC_lower_error" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="groupSendSMSCUCC_lower_error" ng-bind="groupSendSMSCUCC_anciMin"></span>

								</span>
                                </div>
                                <div ng-bind="groupSendSMSCUCC_tip" class="rest-tip"
                                    ></div>
                                <div class="col-xs-1"
                                     style="white-space: nowrap">
                                    <span ng-bind="groupSendSMSCUCC_Price||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
					</div>
                    <!--addby hyj 20191107 top-->
                    <!--挂机短信-->
                    <div ng-show="hasGD_cmccCUCC&&orderInfo.servType =='2'">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="hasGDCUCC">
                            <!--不限-->
                            <div class="form-group" ng-show="isGDLimitCUCC">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[gdTypeCUCC===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="isGDanciCUCC">

                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn" ng-class="{true:'checked',false:''}[gdTypeCUCC===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="gdTypeCUCC===1 && postGuaDuanCUCC && isExperience===0">
                                        <span ng-bind="GDPriceCUCC" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="postGuaDuanCUCC&&gdTypeCUCC===1">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="gdanciCUCC"
                                           ng-disabled="gdTypeCUCC==0"
                                           name="gdanciCUCC" placeholder="{{guaduan_anciRest_tipCUCC}}" required
                                           pattern="^[0-9]{1,9}$" ng-show="postGuaDuanCUCC"
                                           ng-keyup="accMul(GDPriceCUCC,gdanciCUCC,guaduan_amountCUCC,guaduanInput_tempCUCC,'gdanciCUCC')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.gdanciCUCC.$invalid && gdTypeCUCC===1 && postGuaDuanCUCC">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.gdanciCUCC.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.gdanciCUCC.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(guaduan_anci_over_errorCUCC || guaduan_anci_lower_errorCUCC) && !orderItemDomain.gdanciCUCC.$invalid  && gdTypeCUCC===1 && postGuaDuanCUCC">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="guaduan_anci_over_errorCUCC" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="guaduan_anci_lower_errorCUCC" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="guaduan_anci_lower_errorCUCC" ng-bind="guaduan_anciMinCUCC"></span>
								</span>
                                </div>
                                <div ng-bind="guaduan_anciRest_tipCUCC" class="rest-tip"
                                     ng-show="postGuaDuanCUCC&&gdTypeCUCC===1"></div>
                                <div class="col-xs-1" ng-show="gdTypeCUCC==1 && postGuaDuanCUCC && isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="guaduan_priceCUCC||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--addby hyj 20191107 end-->
                </div>
                <!--电信-->
                <div ng-show="hasCTCC">
                    <div class="form-group">
                        <div class="row">
                            <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                                <span>电信</span>：
                            </li>
                        </div>
                    </div>

					<!--屏显配额-->
                    <div ng-show="hasPX_ctcc">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'CREATEORDER_PINGXIANPEIE'|translate" ng-show="orderInfo.servType !='4'"></span>
                                    <span ng-show="orderInfo.servType =='4'">屏显：</span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="postPingXianCTCC">
                            <div class="form-group" ng-show="isPXLimit_ctcc&&orderInfo.servType !='3'">
                                <!--不限-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[pxType_ctcc===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>
                            <div class="form-group" ng-show="isPXanci_ctcc">
                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn"
                                          ng-class="{true:'checked',false:''}[pxType_ctcc===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="pxType_ctcc===1 && postPingXianCTCC && isExperience===0">
                                        <span ng-bind="PXPrice_ctcc" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>
                                <div class="col-xs-2 min-input">
                                    <input type="text" class="form-control" autocomplete="off"
                                           placeholder="{{px_anciRest_tip_ctcc}}" ng-disabled="pxType_ctcc!==1"
                                           name="anci_ctcc"
                                           ng-model="anci_ctcc" required pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(PXPrice_ctcc,anci_ctcc,pinxian_amount_ctcc,anci_temp_ctcc,'pxanci_ctcc')">
                                    <span style="color:red" class="error-ver"
                                          ng-show="((orderItemDomain.anci_ctcc.$dirty && orderItemDomain.anci_ctcc.$invalid) || px_anci_over_error_ctcc || px_anci_lower_error_ctcc) && pxType_ctcc===1 && postPingXianCTCC">
											<img src="../../../../../assets/images/reject-icon.png" width="20"
                                                 height="20">
											<span ng-show="orderItemDomain.anci_ctcc.$error.required"
                                                  ng-bind="'REQUIRED'|translate"></span>
											<span ng-show="orderItemDomain.anci_ctcc.$error.pattern"
                                                  ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
											<span ng-show="px_anci_over_error_ctcc"
                                                  ng-bind="'GO_BEYOND'|translate"></span>
											<span ng-show="px_anci_lower_error_ctcc"
                                                  ng-bind="'LOWER_THAN'|translate"></span>
                                                  <span ng-show="px_anci_lower_error_ctcc" ng-bind="px_anciMin_ctcc"></span>
										</span>
                                </div>
                                <div ng-bind="px_anciRest_tip_ctcc" class="rest-tip"></div>
                                <div class="col-xs-1" ng-show="pxType_ctcc===1 && postPingXianCTCC && isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="pxanci_price_ctcc||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>
					
                    <!--短信群发-->
                    <div ng-show="hasGroupSendSMSCTCC&&orderInfo.servType =='4'">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span >短信：</span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="true">
                            <!--体验不限-->
                            <div class="form-group" ng-show="groupSendSMSCTCCNoLimit">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="!groupSendSMSCTCCNoLimit">
                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn" ng-class="{true:'checked',false:''}[true]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;">
                                        <span ng-bind="groupSendSMSCTCCPrice" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="!groupSendSMSCTCCNoLimit">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="groupSendSMSCTCCanci"

                                           name="groupSendSMSCTCCanci" placeholder="{{groupSendSMSCTCC_tip}}" required
                                           pattern="^[0-9]{1,9}$"
                                           ng-keyup="accMul(groupSendSMSCTCCPrice,groupSendSMSCTCCanci,groupSendSMSCTCCAmount,groupSendSMSCTCC_temp,'groupSendSMSCTCC')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.groupSendSMSCTCCanci.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.groupSendSMSCTCCanci.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(groupSendSMSCTCC_anci_over_error || groupSendSMSCTCC_lower_error) && !orderItemDomain.groupSendSMSCTCCanci.$invalid">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="groupSendSMSCTCC_anci_over_error" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="groupSendSMSCTCC_lower_error" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="groupSendSMSCTCC_lower_error" ng-bind="groupSendSMSCTCC_anciMin"></span>

								</span>
                                </div>
                                <div ng-bind="groupSendSMSCTCC_tip" class="rest-tip"
                                    ></div>
                                <div class="col-xs-1"
                                     style="white-space: nowrap">
                                    <span ng-bind="groupSendSMSCTCC_Price||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
					</div>
                    <!--20191107 top-->
                    <!--addby hyj 20191107 top-->
                    <!--挂机短信-->
                    <div ng-show="hasGD_cmccCTCC&&orderInfo.servType =='2'">
                        <div class="form-group">
                            <div class="check-li col-xs-12">
                                <div style="display: inline-block">
                                    <span ng-bind="'CREATEORDER_GUADUAN'|translate"></span>
                                </div>
                            </div>
                        </div>

                        <div ng-show="hasGDCTCC">
                            <!--不限-->
                            <div class="form-group" ng-show="isGDLimitCTCC">
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn checked"
                                          ng-class="{true:'checked',false:''}[gdTypeCTCC===0]"></span>
                                    <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                                </div>
                            </div>

                            <!--按次-->
                            <div class="form-group" ng-show="isGDanciCTCC">

                                <!--按次-->
                                <div class="check-li col-xs-1 col-xs-offset-2 min-width-li">
                                    <span class="redio-btn" ng-class="{true:'checked',false:''}[gdTypeCTCC===1]"></span>
                                    <span ng-bind="'CREATEORDER_ANCI'|translate"></span>

                                    <div style="display: inline-block;padding-top: 0; position: absolute;right: 0px;"
                                         ng-show="gdTypeCTCC===1 && postGuaDuanCTCC && isExperience===0">
                                        <span ng-bind="GDPriceCTCC" style="margin-left: 15px"></span>
                                        <span ng-bind="'COMMON_YUAN'|translate"></span>
                                    </div>
                                </div>

                                <div class="col-xs-2 min-input" ng-show="postGuaDuanCTCC&&gdTypeCTCC===1">
                                    <input type="text" class="form-control" autocomplete="off" ng-model="gdanciCTCC"
                                           ng-disabled="gdTypeCTCC==0"
                                           name="gdanciCTCC" placeholder="{{guaduan_anciRest_tipCTCC}}" required
                                           pattern="^[0-9]{1,9}$" ng-show="postGuaDuanCTCC"
                                           ng-keyup="accMul(GDPriceCTCC,gdanciCTCC,guaduan_amountCTCC,guaduanInput_tempCTCC,'gdanciCTCC')">
                                    <span class="error-ver"
                                          ng-show="orderItemDomain.gdanciCTCC.$invalid && gdTypeCTCC===1 && postGuaDuanCTCC">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="orderItemDomain.gdanciCTCC.$error.required"
                                          ng-bind="'REQUIRED'|translate"></span>
									<span ng-show="orderItemDomain.gdanciCTCC.$error.pattern"
                                          ng-bind="'CREATEORDER_MAXINPUTDESC'|translate"></span>
								</span>
                                    <span class="error-ver"
                                          ng-show="(guaduan_anci_over_errorCTCC || guaduan_anci_lower_errorCTCC) && !orderItemDomain.gdanciCTCC.$invalid  && gdTypeCTCC===1 && postGuaDuanCTCC">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20">
									<span ng-show="guaduan_anci_over_errorCTCC" ng-bind="'GO_BEYOND'|translate"></span>
									<span ng-show="guaduan_anci_lower_errorCTCC" ng-bind="'LOWER_THAN'|translate"></span>
									<span ng-show="guaduan_anci_lower_errorCTCC" ng-bind="guaduan_anciMinCTCC"></span>
								</span>
                                </div>
                                <div ng-bind="guaduan_anciRest_tipCTCC" class="rest-tip"
                                     ng-show="postGuaDuanCTCC&&gdTypeCTCC===1"></div>
                                <div class="col-xs-1" ng-show="gdTypeCTCC==1 && postGuaDuanCTCC && isExperience===0"
                                     style="white-space: nowrap">
                                    <span ng-bind="guaduan_priceCTCC||0"></span><span
                                        ng-bind="'COMMON_YUAN'|translate"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--addby hyj 20191107 end-->
                    <!--20191107 end-->
                </div>
                <div class="form-group">
                    <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                       ng-bind="'CREATEORDER_DESC1'|translate" ng-show="selectedOrder.servType !=3"></p>

                    <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                       ng-bind="'CREATEORDER_DESC3'|translate" ng-show="selectedOrder.servType ==3"></p>
                </div>

                <!--有效期-->
                <div class="form-group">
                    <div class="col-xs-2">
                        <span ng-bind="'CREATEORDER_TIME'|translate"></span>
                    </div>
                    <div class="input-daterange input-group col-xs-3" id="datepicker"
                         style="padding-left: 15px;padding-right: 15px;min-width: 415px">
                        <input class="input-md form-control" ng-value="effictiveTime|newdate:'ymd'" disabled/>
                        <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                        <input class="input-md form-control" ng-value="expireTime|newdate:'ymd'" disabled/>
                    </div>
                </div>

                <div class="form-group">
                    <p class="col-lg-10 col-xs-10 col-sm-10 col-md-10 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2 red"
                       ng-bind="'CREATEORDER_DESC2'|translate"></p>
                </div>
            </form>
        </div>
        <div class="form-group">
            <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                 style="padding-left: 30px;">
                <button type="submit" class="btn btn-primary search-btn"
                        ng-disabled="orderBase.amount.$invalid ||effictiveTime==='' ||expireTime ===''
									||
									(servType != '4' && ((postPingXianCMCC && pxType===1 && orderItemDomain.anci.$invalid)
									||(postPingXianCMCC && pxType===2 && orderItemDomain.baoyue.$invalid)
									||(postGuaDuan && gdType===1 && orderItemDomain.gdanci.$invalid)
									||(postGuaDuan && gdType===2 && orderItemDomain.gdbaoyue.$invalid)
									||(postGuaCai && gcType===1 && orderItemDomain.gcanci.$invalid)
									||(postGuaCai && gcType===2 && orderItemDomain.gcbaoyue.$invalid)
									||(postPingXianCUCC && pxType_cucc===1 && orderItemDomain.anci_cucc.$invalid)
									||(postGuaDuanCUCC && gdTypeCUCC===1 && orderItemDomain.gdanciCUCC.$invalid)
									||(postPingXianCTCC && pxType_ctcc===1 && orderItemDomain.anci_ctcc.$invalid)
       								||(postGuaDuanCTCC && gdTypeCTCC===1 && orderItemDomain.gdanciCTCC.$invalid)
									||px_anci_over_error_cmcc || px_anci_over_error_cucc || px_anci_over_error_ctcc || baoyue_over_error || guaduan_anci_over_error || guaduan_anci_over_errorCUCC || guaduan_anci_over_errorCTCC || guacai_anci_over_error || gdbaoyue_over_error || gcbaoyue_over_error
									||px_anci_lower_error_cmcc || px_anci_lower_error_cucc || px_anci_lower_error_ctcc || baoyue_lower_error || guaduan_anci_lower_error || guaduan_anci_lower_errorCUCC || guaduan_anci_lower_errorCTCC || guacai_anci_lower_error || gdbaoyue_lower_error || gcbaoyue_lower_error))
                                    ||(servType == '4' && (zcType==1  && postZC && (orderItemDomain.zcanci.$invalid || zengcai_anci_over_error || zengcai_anci_lower_error)))
                                    ||(servType == '4' && (hasGroupSendSMSCMCC && !groupSendSMSCMCCNoLimit && (orderItemDomain.groupSendSMSCMCCanci.$invalid || groupSendSMSCMCC_anci_over_error  || groupSendSMSCMCC_lower_error)))
                                    ||(servType == '4' && (hasGroupSendSMSCUCC && !groupSendSMSCUCCNoLimit &&(orderItemDomain.groupSendSMSCUCCanci.$invalid || groupSendSMSCUCC_anci_over_error   || groupSendSMSCUCC_lower_error)))
                                    ||(servType == '4' && (hasGroupSendSMSCTCC && !groupSendSMSCTCCNoLimit &&(orderItemDomain.groupSendSMSCTCCanci.$invalid || groupSendSMSCTCC_anci_over_error   || groupSendSMSCTCC_lower_error)))
"
                        ng-click="querySubscribeList(orderInfo,'updateOrder')" ng-bind="'COMMON_SAVE'|translate" id="formSub">
                </button>
                <button type="submit" class="btn btn-back" ng-click="goBack()"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>

    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                    </p></div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"
                            ng-click="sure(eventType)"></button>
                </div>
            </div>
        </div>
    </div>

</div>

</body>
</html>
