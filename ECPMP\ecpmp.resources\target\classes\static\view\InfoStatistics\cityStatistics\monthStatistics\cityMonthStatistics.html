<!DOCTYPE html>
<html>

<head lang="en">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="X-UA-Compatible" content="IE=11"/>
    <link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css"/>
    <link href="../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../css/mian.css" rel="stylesheet"/>
    <link href="../../../../css/datepicker3.css" rel="stylesheet"/>
    <link href="../../../../css/layout.css" rel="stylesheet"/>
    <link href="../../../../css/searchList.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
    <script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
    <link rel="stylesheet" href="../../../../css/font-awesome.min.css"/>
    <link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
    <script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
    <!-- 引入分页组件 -->
    <link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
    <script type="text/javascript" src="../../../../directives/page/page.js"></script>
    <link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <link href="../../../../css/statistics.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="cityMonthStatistics.js"></script>

    <style>
        .form-horizontal .control-label {
            padding-top: 14px !important;
            padding-bottom: 6px;
        }

        body, html {
            overflow: auto;
        }

        .input-daterange {
            padding-top: 0px !important;
        }

        .cooperation-manage .add-table .add-btn .export-icon {
            background: url(../../../../assets/images/btnIcons18.png) no-repeat;
            vertical-align: middle;
            background-position: -107px 0px;
        }

        .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
            padding: 12px 6px;
        }

        @media (max-width: 1366px) {
            .class1 {
                max-width: 300px;
                min-width: 260px;
                padding-right: 0 !important;
            }

            .class1 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 52px !important;
            }

            .search-content1 {
                margin-left: 52px !important;
            }

            .class2 {
                max-width: 260px;
                min-width: 200px;
                padding-right: 0 !important;
            }

            .class2 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 52px !important;
            }

            .search-content2 {
                margin-left: 52px !important;
            }

            .class3 {
                padding-right: 0 !important;
            }

            .class3 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 52px !important;
            }

            .search-content3 {
                margin-left: 52px !important;
            }

            .class4 {
                max-width: 320px;
                min-width: 260px;
                padding-right: 0 !important;
            }

            .class4 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 62px !important;
            }

            .search-content4 {
                margin-left: 62px !important;
            }

            .class5 {
                padding: 0 !important;
                margin-left: 7px !important;
            }
        }

        @media (max-width: 1280px) {
            .class1 {
                max-width: 300px;
                min-width: 240px;
                padding-right: 0 !important;
            }

            .class1 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 40px !important;
            }

            .search-content1 {
                margin-left: 40px !important;
            }

            .class2 {
                max-width: 240px;
                min-width: 180px;
                padding-right: 0 !important;
            }

            .class2 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 40px !important;
            }

            .search-content2 {
                margin-left: 40px !important;
            }

            .class3 {
                padding-right: 0 !important;
            }

            .class3 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 40px !important;
            }

            .search-content3 {
                margin-left: 40px !important;
            }

            .class4 {
                max-width: 300px;
                min-width: 240px;
                padding-right: 0 !important;
            }

            .class4 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 56px !important;
            }

            .search-content4 {
                margin-left: 56px !important;
            }

            .class5 {
                padding: 0 !important;
                margin-left: 4px !important;
            }
        }

        @media (max-width: 1100px) {
            .class1 {
                max-width: 280px;
                min-width: 220px;
                padding-right: 0 !important;
            }

            .class1 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 28px !important;
            }

            .search-content1 {
                margin-left: 28px !important;
            }

            .class2 {
                max-width: 220px;
                min-width: 160px;
                padding-right: 0 !important;
            }

            .class2 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 28px !important;
            }

            .search-content2 {
                margin-left: 28px !important;
            }

            .class3 {
                padding-right: 0 !important;
            }

            .class3 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 28px !important;
            }

            .search-content3 {
                margin-left: 28px !important;
            }

            .class4 {
                max-width: 280px;
                min-width: 220px;
                padding-right: 0 !important;
            }

            .class4 div:nth-child(1) {
                padding: 7px 0 !important;
                width: 68px !important;
            }

            .search-content4 {
                margin-left: 68px !important;
            }

            .class5 {
                padding: 0 !important;
                margin-left: 0px !important;
            }
        }
    </style>

</head>

<body ng-app='myApp' ng-controller='statisticsController' ng-init="init();" style="min-width: 1400px">
<div class="cooperation-manage">
    <div class="cooperation-head"><span class="frist-tab"
                                        ng-bind="'CITYSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;<span
            class="second-tab" ng-bind="'MONTHSTATISTICS'|translate"></span></div>
    <top:menu chose-index="5" page-url="/qycy/ecpmp/view/InfoStatistics/enterpriseStatistics" list-index="29"
              ng-show="!isProvincial"></top:menu>
    <top:menu chose-index="0" page-url="/qycy/ecpmp/view/InfoStatistics/cityStatistics" list-index="28"
              class="second-topmenu"></top:menu>
    <div class="cooperation-search">
        <form class="form-horizontal">

            <div class="form-group">
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 cond-div class1" ng-show="isSuperManager">
                    <div style="float:left;padding: 9px 6px;width: 52px;white-space: nowrap;"
                         ng-bind="'COMMON_PROVINCE'|translate"></div>
                    <div style="margin-left: 52px;" class="search-content1">
                        <select class="form-control" name="province" ng-model="selectedProvince"
                                ng-options="x.provinceName for x in provinceList"
                                ng-change="changeSelectedProvincebychaoguan(selectedProvince)">
                            <option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
                        </select>
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 cond-div class1" ng-show="!isSuperManager">
                    <div style="float:left;padding: 9px 6px;width: 52px;white-space: nowrap;"
                         ng-bind="'COMMON_PROVINCE'|translate"></div>
                    <div style="margin-left: 52px;" class="search-content1">
                        <select class="form-control" name="province" ng-model="selectedProvince"
                                ng-options="x.authName for x in provinceList"
                                ng-change="changeSelectedProvince(selectedProvince)">
                            <option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
                        </select>
                    </div>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class2" ng-show="isSuperManager">
                    <div style="float:left;padding: 9px 6px;width: 52px;white-space: nowrap;"
                         ng-bind="'CITY'|translate"></div>
                    <div style="margin-left: 52px;" class="search-content2">
                        <select class="form-control" name="city" id="selectedCity" ng-model="selectedCity"
                                ng-options="x.cityName for x in subCityList"
                                ng-disabled="selectedProvince===null||selectedProvince.provinceID==='000'">
                            <option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
                        </select>
                    </div>
                </div>

                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2 cond-div class2" ng-show="!isSuperManager">
                    <div style="float:left;padding: 9px 6px;width: 52px;white-space: nowrap;"
                         ng-bind="'CITY'|translate"></div>
                    <div style="margin-left: 52px;" class="search-content4">
                        <select class="form-control" name="city" id="selectedCity" ng-model="selectedCity"
                                ng-options="x.authName for x in subCityList"
                                ng-disabled="selectedProvince===null||selectedProvince.provinceID==='000'">
                            <option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
                        </select>
                    </div>
                </div>

                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 cond-div class4">
                    <div style="float:left;padding: 9px 6px;width: 80px;white-space: nowrap;"
                         ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></div>
                    <div style="margin-left: 80px;" class="search-content4">
                        <select class="form-control" name="serviceType" ng-model="serviceType"
                                ng-options="x.id as x.name for x in serviceTypeChoise"></select>
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 cond-div class4">
                    <div style="float:left;padding: 9px 6px;width: 52px;white-space: nowrap;"
                         ng-bind="'ENTERPRISE_TYPE'|translate"></div>
                    <div style="margin-left: 80px;" class="search-content4">
                        <select class="form-control" name="subProvinceType" ng-model="subProvinceType"
                                ng-options="x.id as x.name for x in subProvinceTypeSelect" style="max-width:200px">
                        </select>
                    </div>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3  time cond-div class3" style="min-width: 256px;">
                    <div style="float:left;padding: 9px 6px;width: 52px;white-space: nowrap;"
                         ng-bind="'QUERYORDERDETAIL_TIME'|translate"></div>
                    <div style="margin-left: 52px;position: relative;margin-top: 7px;"
                         class="input-daterange input-group search-content3" id="datepicker">
                        <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off"
                               id="start" ng-keyup="searchOn()"/>
                        <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                        <input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off"
                               id="end" ng-keyup="searchOn()"/>
                    </div>
                </div>

                <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div class5"
                     style="max-width:55px;margin-left:1px;padding-top: 14px !important">
                    <button ng-click="queryEnterpriseStatInfo()" type="submit" ng-disabled="initSel.search"
                            class="btn search-btn">
                        <icon class="search-iocn"></icon>
                        <span ng-bind="'COMMON_SEARCH'|translate"></span>
                    </button>
                </div>

            </div>
        </form>
    </div>

    <div class="add-table">
        <button id="exportSpokesList" type="submit" class="btn add-btn" ng-click="exportFile()">
            <icon class="export-icon"></icon>
            <span ng-bind="'COMMON_EXPORT'|translate"></span>
        </button>
    </div>

    <div style="font-weight:bold;padding:0px 20px 10px 20px;font-size:14px" ng-bind="'MONTHSTATISTICS'|translate"></div>
    <div class="coorPeration-table">
        <table class="table table-striped table-hover">
            <thead>
            <tr>
                <th style="width:7%" ng-bind="'ENTERPRISE_TYPE'|translate"></th>
                <th style="width:8%" ng-bind="'COMMON_PROVINCE'|translate"></th>
                <th style="width:8%" ng-bind="'CITY'|translate"></th>
                <th style="width:8%" ng-bind="'YEARMONTH'|translate"></th>
                <th style="width:8%" ng-bind="'OTHER_PROVICE_CONTENTAUDIT_FIRSTTYPE'|translate"></th>
                <th style="width:8%" ng-bind="'OTHER_PROVICE_CONTENTAUDIT_SECONDTYPE'|translate"></th>
                <th style="width:8%" ng-bind="'ENTERPRISE_COUNT'|translate"></th>
                <th style="width:8%" ng-bind="'MEMBERCOUNT'|translate"></th>
                <th style="width:8%" ng-bind="'OTHER_PROVICE_USECOUNT'|translate"></th>
                <th style="width:8%" ng-bind="'CMCC_USE'|translate"></th>
                <th style="width:8%" ng-bind="'CUCC_USE'|translate"></th>
                <th style="width:8%" ng-bind="'CTCC_USE'|translate"></th>
                <!-- <th style="width:8%" ng-bind="'EXPERIENCECOUNT'|translate"></th>
                <th style="width:8%" ng-bind="'REALCOUNT'|translate"></th> -->
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="item in StatInfoListData">
                <td><span title="{{subProvinceTypeMap[item.subProvinceType]}}">{{subProvinceTypeMap[item.subProvinceType]}}</span>  </td>

                <td><span title="{{provinceList2[item.provinceID]}}">{{provinceList2[item.provinceID]}}</span></td>

                <td><span title="{{cityList2[item.cityID]}}">{{cityList2[item.cityID]}}</span></td>
                <td><span title="{{getTime(item.statMonth)}}">{{getTime(item.statMonth)}}</span></td>
                <td><span title="{{getServiceType(item.serviceType)}}">{{getServiceType(item.serviceType)}}</span></td>
                <td><span title="{{getSubServType(item.subServType, item.hangupType)}}">{{getSubServType(item.subServType, item.hangupType)}}</span></td>
                <td><span title="{{item.enterpriseCount}}">{{item.enterpriseCount}}</span></td>
                <td><span title="{{item.memberCount}}">{{item.memberCount}}</span></td>
                <td><span title="{{item.useCount}}">{{item.useCount}}</span></td>
                <td><span title="{{item.useCountMobile}}">{{item.useCountMobile}}</span></td>
                <td><span title="{{item.useCountUnicom}}">{{item.useCountUnicom}}</span></td>
                <td><span title="{{item.useCountTelecom}}">{{item.useCountTelecom}}</span></td>
                <!-- <td><span title="{{item.experienceCount}}">{{item.experienceCount}}</span></td>
                <td><span title="{{item.tureUseCount}}">{{item.tureUseCount}}</span></td> -->
            </tr>
            <tr ng-show="StatInfoListData.length<=0">
                <td style="text-align:center" colspan="13" ng-bind="'COMMON_NODATA'|translate"></td>
            </tr>
            </tbody>
        </table>
    </div>

    <div>
        <ptl-page tableId="0" change="queryEnterpriseStatInfo('justPage')"></ptl-page>
    </div>
</div>

<!--小弹出框-->
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                    </p>
                </div>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>

</body>

</html>