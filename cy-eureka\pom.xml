<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>cn.caiyin</groupId>
	<artifactId>cy-eureka</artifactId>
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<name>cy-eureka</name>
	<description>C<PERSON>yin register center server</description>

	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.4.1</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>21</java.version>
		<spring-cloud.version>2024.0.0</spring-cloud.version>
	</properties>

	<profiles>
		<profile>
			<!-- 本地开发环境 -->
			<id>dev_local</id>
			<properties>
				<cy_eureka.url>http://127.0.0.1:19000/eureka/</cy_eureka.url>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<!-- 开发环境 -->
			<id>dev</id>
			<properties>
				<cy_eureka.url>http://**************:19000/eureka/</cy_eureka.url>
			</properties>
		</profile>
		<profile>
			<!-- 测试环境 -->
			<id>test</id>
			<properties>
				<cy_eureka.url>http://10.124.130.248:31001/eureka/,http://10.124.130.249:31001/eureka/</cy_eureka.url>
			</properties>
		</profile>
		<profile>
			<!-- 生产环境 -->
			<id>production</id>
			<properties>
				<cy_eureka.url>http://10.124.70.218:19000/eureka/,http://10.124.70.219:19000/eureka/,http://10.124.70.220:19000/eureka/</cy_eureka.url>
				<!--<cy_eureka.url>http://10.124.35.47:19000/eureka/,http://10.124.35.48:19000/eureka/,http://10.124.35.49:19000/eureka/,http://10.124.35.7:19000/eureka/,http://10.124.35.8:19000/eureka/</cy_eureka.url>-->
			</properties>
		</profile>
	</profiles>

	<dependencies>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-server</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-jxpath</groupId>
					<artifactId>commons-jxpath</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>spring-cloud-starter-hystrix</artifactId>-->
		<!--</dependency>-->
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>-->
				<!--spring-cloud-starter-hystrix-dashboard-->
			<!--</artifactId>-->
		<!--</dependency>-->

		<!--<dependency>-->
			<!--<groupId>org.springframework.boot</groupId>-->
			<!--<artifactId>spring-boot-starter-test</artifactId>-->
			<!--<scope>test</scope>-->
		<!--</dependency>-->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<version>4.0.4</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>
		
		<dependency>
   			<groupId>net.logstash.logback</groupId>
  			<artifactId>logstash-logback-encoder</artifactId>
   			<version>4.11</version>
		</dependency>
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<artifactId>netty-codec-http</artifactId>
				<groupId>io.netty</groupId>
				<version>4.1.108.Final</version>
			</dependency>

			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>33.4.0-jre</version>
			</dependency>
			<dependency>
				<groupId>org.apache.httpcomponents</groupId>
				<artifactId>httpclient</artifactId>
				<version>4.5.14</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.properties</include>
					<include>**/*.yml</include>
					<include>**/*.xml</include>
				</includes>
				<filtering>true</filtering>
			</resource>
		</resources>
		<pluginManagement>
			<plugins>
				<plugin>
					<artifactId>maven-resources-plugin</artifactId>
					<configuration>
						<encoding>utf-8</encoding>
						<useDefaultDelimiters>true</useDefaultDelimiters>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>0.4.11</version>
				<configuration>
					<imageName>caiyin/${project.artifactId}</imageName>
					<imageTags>
						<imageTag>latest</imageTag>
					</imageTags>
					<dockerDirectory>src/main/docker</dockerDirectory>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
			</plugin>
		</plugins>
	</build>


</project>
