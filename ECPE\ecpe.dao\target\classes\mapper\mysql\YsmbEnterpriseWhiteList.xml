<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.YsmbEnterpriseWhiteListMapper">
	<resultMap id="ysmbEnterpriseWhiteList"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.YsmbEnterpriseWhiteListWrapper">
		<result property="id" column="id" />
		<result property="contentID" column="contentID" />
		<result property="enterpriseID" column="enterpriseID" />
	</resultMap>


	<insert id="addYsmbEnterpriseWhite" parameterType="com.huawei.jaguar.dsdp.ecpe.dao.domain.YsmbEnterpriseWhiteListWrapper"
			keyProperty="id" useGeneratedKeys="true">
		insert into ecpe_t_ysmb_enterprise_whitelist
		(
		contentID,
		enterpriseID
		)
		values
			(
			#{contentID},
			#{enterpriseID}
			)
	</insert>

	<select id="queryYsmbEnterpriseWhiteList" resultMap="ysmbEnterpriseWhiteList">
		select
		*
		from ecpe_t_ysmb_enterprise_whitelist t
		WHERE
		t.contentID = #{contentID}
	</select>
</mapper>
