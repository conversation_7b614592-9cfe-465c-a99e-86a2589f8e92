<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryStatMapper">

	<resultMap id="delveryStatMap" type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryStatWrapper">
		<result property="msisdn" column="msisdn" />
		<result property="templateID" column="templateID" />
		<result property="enterpriseID" column="enterpriseID" />
		<result property="lastDeliveryTime" column="lastDeliveryTime" />
		<result property="daycnt" column="daycnt" />
	</resultMap>
	
	<sql id="thirdpartyDeliveryStatCol">
		msisdn,
		templateID,
		enterpriseID,
		lastDeliveryTime,
		daycnt
	</sql>
	
	<select id="queryDeliveryStat" resultMap="delveryStatMap">
		select
		<include refid="thirdpartyDeliveryStatCol"/>
		from ecpe_t_thirdparty_delivery_stat
		where enterpriseID=#{enterpriseID}
		and msisdn=#{msisdn}
		and templateID=#{templateID}
	</select>
	
	<update id="updateDeliveryStat">
		update ecpe_t_thirdparty_delivery_stat set daycnt=#{daycnt},
		lastDeliveryTime=#{lastDeliveryTime}
		where enterpriseID=#{enterpriseID}
		and msisdn=#{msisdn}
		and templateID=#{templateID}
	</update>
	
	<insert id="insertDeliveryStat">
		insert into ecpe_t_thirdparty_delivery_stat 
		(<include refid="thirdpartyDeliveryStatCol"/>)
		values
		(
		#{msisdn},
		#{templateID},
		#{enterpriseID},
		#{lastDeliveryTime},
		#{daycnt}
		)
	</insert>
</mapper>