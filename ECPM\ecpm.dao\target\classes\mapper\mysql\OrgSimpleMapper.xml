<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.OrgSimpleMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgSimpleWrapper" >
        <id column="ID" property="id" jdbcType="INTEGER" />
        <result column="orgName" property="orgName" jdbcType="VARCHAR" />
        <result column="orgType" property="orgType" jdbcType="INTEGER" />
        <result column="enterpriseID" property="enterpriseID" jdbcType="INTEGER" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="operatorID" property="operatorID" jdbcType="INTEGER" />
        <result column="lastupdatetime" property="lastupdatetime" jdbcType="TIMESTAMP" />
        <result column="extInfo" property="extInfo" jdbcType="VARCHAR" />
        <result column="reserved1" property="reserved1" jdbcType="VARCHAR" />
        <result column="reserved2" property="reserved2" jdbcType="VARCHAR" />
        <result column="reserved3" property="reserved3" jdbcType="VARCHAR" />
        <result column="reserved4" property="reserved4" jdbcType="VARCHAR" />
        <result column="reserved5" property="reserved5" jdbcType="VARCHAR" />
        <result column="reserved6" property="reserved6" jdbcType="VARCHAR" />
        <result column="reserved7" property="reserved7" jdbcType="VARCHAR" />
        <result column="reserved8" property="reserved8" jdbcType="VARCHAR" />
        <result column="reserved9" property="reserved9" jdbcType="VARCHAR" />
        <result column="reserved10" property="reserved10" jdbcType="VARCHAR" />
        <result column="oriOrgID" property="oriOrgID" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, orgName, orgType, enterpriseID, createTime, operatorID, lastupdatetime, extInfo, 
        reserved1, reserved2, reserved3, reserved4, reserved5, reserved6, reserved7, reserved8, 
        reserved9, reserved10, branchType, oriOrgID
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_org_simple
        where ID = #{id,jdbcType=INTEGER}
    </select>


    <select id="selectByOrgCode" resultMap="BaseResultMap" parameterType="java.lang.String" >

        SELECT
            os.*
        FROM
            ecpm_t_org_simple os
                LEFT JOIN ecpm_t_content_org co ON co.ownerID = os.id
                LEFT JOIN ecpm_t_org_rel orl ON orl.orgID = os.id
                LEFT JOIN ecpm_t_member m ON m.id = orl.id
        WHERE
            orl.orgCode like concat('%',#{orgCode},'%')
          AND m.msisdn = #{msisdn}
            LIMIT 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
        delete from ecpm_t_org_simple
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgSimpleWrapper" >
        insert into ecpm_t_org_simple (ID, orgName, orgType, 
            enterpriseID, createTime, operatorID, 
            lastupdatetime, extInfo, reserved1, 
            reserved2, reserved3, reserved4, 
            reserved5, reserved6, reserved7, 
            reserved8, reserved9, reserved10,
            branchType, oriOrgID, pxBeyondPackageSwitch, gdBeyondPackageSwitch
            )
        values (#{id,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, #{orgType,jdbcType=INTEGER}, 
            #{enterpriseID,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{operatorID,jdbcType=INTEGER}, 
            #{lastupdatetime,jdbcType=TIMESTAMP}, #{extInfo,jdbcType=VARCHAR}, #{reserved1,jdbcType=VARCHAR}, 
            #{reserved2,jdbcType=VARCHAR}, #{reserved3,jdbcType=VARCHAR}, #{reserved4,jdbcType=VARCHAR}, 
            #{reserved5,jdbcType=VARCHAR}, #{reserved6,jdbcType=VARCHAR}, #{reserved7,jdbcType=VARCHAR}, 
            #{reserved8,jdbcType=VARCHAR}, #{reserved9,jdbcType=VARCHAR}, #{reserved10,jdbcType=VARCHAR},
            #{branchType,jdbcType=INTEGER}, #{oriOrgID,jdbcType=INTEGER}, #{pxBeyondPackageSwitch,jdbcType=VARCHAR}, #{gdBeyondPackageSwitch,jdbcType=VARCHAR}
            )
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgSimpleWrapper" >
        insert into ecpm_t_org_simple
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="ID != null" >
                ID,
            </if>
            <if test="orgName != null" >
                orgName,
            </if>
            <if test="orgType != null" >
                orgType,
            </if>
            <if test="enterpriseID != null" >
                enterpriseID,
            </if>
            <if test="createTime != null" >
                createTime,
            </if>
            <if test="operatorID != null" >
                operatorID,
            </if>
            <if test="lastupdatetime != null" >
                lastupdatetime,
            </if>
            <if test="extInfo != null" >
                extInfo,
            </if>
            <if test="reserved1 != null" >
                reserved1,
            </if>
            <if test="reserved2 != null" >
                reserved2,
            </if>
            <if test="reserved3 != null" >
                reserved3,
            </if>
            <if test="reserved4 != null" >
                reserved4,
            </if>
            <if test="reserved5 != null" >
                reserved5,
            </if>
            <if test="reserved6 != null" >
                reserved6,
            </if>
            <if test="reserved7 != null" >
                reserved7,
            </if>
            <if test="reserved8 != null" >
                reserved8,
            </if>
            <if test="reserved9 != null" >
                reserved9,
            </if>
            <if test="reserved10 != null" >
                reserved10,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="ID != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="orgName != null" >
                #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null" >
                #{orgType,jdbcType=INTEGER},
            </if>
            <if test="enterpriseID != null" >
                #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operatorID != null" >
                #{operatorID,jdbcType=INTEGER},
            </if>
            <if test="lastupdatetime != null" >
                #{lastupdatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="extInfo != null" >
                #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="reserved1 != null" >
                #{reserved1,jdbcType=VARCHAR},
            </if>
            <if test="reserved2 != null" >
                #{reserved2,jdbcType=VARCHAR},
            </if>
            <if test="reserved3 != null" >
                #{reserved3,jdbcType=VARCHAR},
            </if>
            <if test="reserved4 != null" >
                #{reserved4,jdbcType=VARCHAR},
            </if>
            <if test="reserved5 != null" >
                #{reserved5,jdbcType=VARCHAR},
            </if>
            <if test="reserved6 != null" >
                #{reserved6,jdbcType=VARCHAR},
            </if>
            <if test="reserved7 != null" >
                #{reserved7,jdbcType=VARCHAR},
            </if>
            <if test="reserved8 != null" >
                #{reserved8,jdbcType=VARCHAR},
            </if>
            <if test="reserved9 != null" >
                #{reserved9,jdbcType=VARCHAR},
            </if>
            <if test="reserved10 != null" >
                #{reserved10,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgSimpleWrapper" >
        update ecpm_t_org_simple
        <set >
            <if test="orgName != null" >
                orgName = #{orgName,jdbcType=VARCHAR},
            </if>
            <if test="orgType != null" >
                orgType = #{orgType,jdbcType=INTEGER},
            </if>
            <if test="enterpriseID != null" >
                enterpriseID = #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="operatorID != null" >
                operatorID = #{operatorID,jdbcType=INTEGER},
            </if>
            <if test="lastupdatetime != null" >
                lastupdatetime = #{lastupdatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="extInfo != null" >
                extInfo = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="reserved1 != null" >
                reserved1 = #{reserved1,jdbcType=VARCHAR},
            </if>
            <if test="reserved2 != null" >
                reserved2 = #{reserved2,jdbcType=VARCHAR},
            </if>
            <if test="reserved3 != null" >
                reserved3 = #{reserved3,jdbcType=VARCHAR},
            </if>
            <if test="reserved4 != null" >
                reserved4 = #{reserved4,jdbcType=VARCHAR},
            </if>
            <if test="reserved5 != null" >
                reserved5 = #{reserved5,jdbcType=VARCHAR},
            </if>
            <if test="reserved6 != null" >
                reserved6 = #{reserved6,jdbcType=VARCHAR},
            </if>
            <if test="reserved7 != null" >
                reserved7 = #{reserved7,jdbcType=VARCHAR},
            </if>
            <if test="reserved8 != null" >
                reserved8 = #{reserved8,jdbcType=VARCHAR},
            </if>
            <if test="reserved9 != null" >
                reserved9 = #{reserved9,jdbcType=VARCHAR},
            </if>
            <if test="reserved10 != null" >
                reserved10 = #{reserved10,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgSimpleWrapper" >
        update ecpm_t_org_simple
        set orgName = #{orgName,jdbcType=VARCHAR},
            orgType = #{orgType,jdbcType=INTEGER},
            enterpriseID = #{enterpriseID,jdbcType=INTEGER},
            operatorID = #{operatorID,jdbcType=INTEGER},
            lastupdatetime = #{lastupdatetime,jdbcType=TIMESTAMP},
            extInfo = #{extInfo,jdbcType=VARCHAR},
            reserved1 = #{reserved1,jdbcType=VARCHAR},
            reserved2 = #{reserved2,jdbcType=VARCHAR},
            reserved3 = #{reserved3,jdbcType=VARCHAR},
            reserved4 = #{reserved4,jdbcType=VARCHAR},
            reserved5 = #{reserved5,jdbcType=VARCHAR},
            reserved6 = #{reserved6,jdbcType=VARCHAR},
            reserved7 = #{reserved7,jdbcType=VARCHAR},
            reserved8 = #{reserved8,jdbcType=VARCHAR},
            reserved9 = #{reserved9,jdbcType=VARCHAR},
            reserved10 = #{reserved10,jdbcType=VARCHAR},
            pxBeyondPackageSwitch = #{pxBeyondPackageSwitch,jdbcType=VARCHAR},
            gdBeyondPackageSwitch = #{gdBeyondPackageSwitch,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER} or oriOrgID = #{id,jdbcType=INTEGER}
    </update>

	<!--删除组织信息 -->
	<delete id="deleteOrgSimple">
		delete from ecpm_t_org_simple where ID in (
		<foreach collection="list" item="id" separator=",">
			#{id}
		</foreach>
		)
	</delete>
	
	<select id="BatchSelectByPrimaryKey" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_org_simple
        where ID in (
		<foreach collection="list" item="id" separator=",">
			#{id}
		</foreach>
		)
    </select>
    
    <select id="queryListForBranch" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_org_simple
        where branchType != '22' and (reserved2 = '1' or  reserved8 = '1')and oriOrgID in (
		<foreach collection="list" item="id" separator=",">
			#{id}
		</foreach>
		)
    </select>
    
    <select id="queryAllForBranch" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_org_simple
        where branchType != '22'
    </select>
    
    <select id="queryListForBranchByOriOrgID" resultMap="BaseResultMap" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_org_simple
        where oriOrgID = #{oriOrgID} or id = #{oriOrgID}
    </select>

    <select id="queryListOrgSimpleAndContentOrg" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentSimpleWrapper" >
       select s.branchType ,s.Id ,GROUP_CONCAT(o.orgCode) as orgCode
        from ecpm_t_org_simple s, ecpm_t_content_org o
        where s.Id = o.ownerID and s.oriOrgID = #{oriOrgID} group by  branchType,Id
    </select>

    <select id="queryOrgByEnterpriseId" resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from ecpm_t_org_simple
        where enterpriseID = #{enterpriseID}
        <if test="branchType != null" >
            and branchType = #{branchType}
        </if>
        <if test="orgType != null" >
            and orgType = #{orgType}
        </if>
    </select>

    <select id="getOrgCount" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            ecpm_t_org_simple
        WHERE
            ID = #{orgID,jdbcType=INTEGER}
    </select>


</mapper>