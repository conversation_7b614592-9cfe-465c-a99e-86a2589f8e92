<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper2.EnterpriseMapper">
    <resultMap id="enterpriseWrapper" type="com.huawei.jaguar.dsdp.bill.dao.domain2.EnterpriseWrapper">
        <result property="id" column="id" />
        <result property="enterpriseCode" column="enterpriseCode" />
        <result property="enterpriseName" column="enterpriseName" />
        <result property="enterpriseDesc" column="enterpriseDesc" />
        <result property="enterpriseType" column="enterpriseType" />
        <result property="organizationID" column="organizationID" />
        <result property="custID" column="custID" />
        <result property="businessLicenseID" column="businessLicenseID" />
        <result property="businessLicenseURL" column="businessLicenseURL" />
        <result property="idCardPositiveURL" column="IDCardPositiveURL" />
        <result property="idCardOppositeURL" column="IDCardOppositeURL" />
        <result property="contract" column="contract" />
        <result property="auditStatus" column="auditStatus" />
        <result property="auditDesc" column="auditDesc" />
        <result property="msisdn" column="msisdn" />
        <result property="createTime" column="createTime" />
        <result property="operatorID" column="operatorID" />
        <result property="lastUpdateTime" column="lastUpdateTime" />
        <result property="parentEnterpriseID" column="parentEnterpriseID" />
        <result property="parentEnterpriseName" column="parentEnterpriseName" />
        <result property="provinceID" column="provinceID" />
        <result property="status" column="status" />
        <result property="extInfo" column="extInfo" />
        <result property="reserved1" column="reserved1" />
        <result property="reserved2" column="reserved2" />
        <result property="reserved3" column="reserved3" />
        <result property="reserved4" column="reserved4" />
        <result property="reserved5" column="reserved5" />
        <result property="reserved6" column="reserved6" />
        <result property="reserved7" column="reserved7" />
        <result property="reserved8" column="reserved8" />
        <result property="reserved9" column="reserved9" />
        <result property="reserved10" column="reserved10"/>
        <result property="businessStatus" column="businessStatus"/>
        <result property="cityName" column="cityName"/>
        <result property="provinceName" column="provinceName"/>
        <result property="servTypesString" column="servTypesString"/>

    </resultMap>

    <select id="queryEnterpriseList" resultMap="enterpriseWrapper">
        SELECT
        t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
        t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,
        t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
        t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
        t.reserved10,t.parentEnterpriseName,businessStatus,t.countyID
        from dsum_t_enterprise t
        where (t.reserved2 != 1 or t.reserved2 is null)
        <trim prefix="and" prefixOverrides="and|or">

            <if test="createStartTime != null and createEndTime == null">
                and t.createTime <![CDATA[ >= ]]> #{createStartTime}
            </if>

            <if test="createStartTime == null and createEndTime != null">
                and t.createTime <![CDATA[ <= ]]> #{createEndTime}
            </if>

            <if test="organizationID !=null  and organizationID !=''">
                and t.organizationID like concat("%", #{organizationID}, "%")
            </if>
            <if test="channelSrc !=null  and channelSrc !=''">
                and t.reserved10 = #{channelSrc}
            </if>
            <if test="enterpriseCode !=null and enterpriseCode !='' ">
                and t.enterpriseCode = #{enterpriseCode}
            </if>

            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>
			
            <if test="parentEnterpriseID !=null">
                and t.parentEnterpriseID = #{parentEnterpriseID}
            </if>
            <if test="parentEnterpriseIDs !=null">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseID" index="index" collection="parentEnterpriseIDs" open="(" separator=","
                         close=")">
                    #{parentEnterpriseID}
                </foreach>
            </if>
            <if test="enterpriseTypeList != null and enterpriseTypeList.size()>0">
                and t.enterpriseType in
                <foreach item="enterpriseType" index="index" collection="enterpriseTypeList" open="(" separator=","
                    close=")">
                    #{enterpriseType}
                </foreach>
            </if>
            <choose>
                <when test="statusList != null and statusList.size()>0">
                    and t.status in
                    <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>

                    <if test="isQueryAllStatus == null or isQueryAllStatus == 0">
                        and (t.status = 1

                            <if test="lastDelDate != null and lastDelDate != ''">
                                or lastUpdateTime > #{lastDelDate}
                            </if>

                            )
                    </if>
                </otherwise>
            </choose>
        </trim>
        <if test="servType !=null ">
            and es.servType = #{servType}
        </if>
        GROUP BY  t.id
        <if test="sortField !=null and sortField !='' and sortType !=null and sortType !=''">
            order by ${sortType} ${sortField}
        </if>
        <if test="pageNum !=null and pageSize !=null ">
            limit #{pageNum},#{pageSize}
        </if>

    </select>

    <select id="queryEnterpriseListByDsum" resultMap="enterpriseWrapper">
        SELECT
        t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
        t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,
        t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
        t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
        t.reserved10,t.parentEnterpriseName,businessStatus,t.countyID
        from dsum_t_enterprise t
        where (t.enterpriseType=5 and t.reserved10='111')
        <trim prefix="and" prefixOverrides="and|or">
            <if test="end != null">
                and t.createTime <![CDATA[ <= ]]> #{end}
            </if>

            <if test="begin != null">
                and (t.status = 1 or t.lastUpdateTime <![CDATA[ >= ]]> #{begin} )
            </if>
        </trim>
        GROUP BY  t.id
        order by t.lastUpdateTime desc
            </select>

    <select id="queryEnterpriseIDsByProvinceIDs" resultType="java.lang.Integer">
        SELECT t.id from dsum_t_enterprise t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>

            <if test="id !=null  and id !=''">
                and t.id = #{id}
            </if>
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>
                )
            </if>
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>

                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or provinceID is null
                </if>
                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (t.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or cityID is null
                </if>
                )
            </if>

            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (t.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or countyID is null
                </if>
                )
            </if>
            <if test="isEp == 1">
                and t.enterpriseType = 5
            </if>
            <if test="isEp != 1">
                and t.enterpriseType != 5
            </if>
            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>
            <if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseID" index="index" collection="parentEnterpriseIDs" open="(" separator="," close=")">
                    #{parentEnterpriseID}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="queryEnterpriseListFromDsum" resultMap="enterpriseWrapper">
        SELECT
        t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
        t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,
        t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
        t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
        t.reserved10,t.parentEnterpriseName,businessStatus,t.countyID,GROUP_CONCAT(DISTINCT es2.servType) servTypesString
        from dsum_t_enterprise t
        LEFT JOIN dsum_t_enterprise_service es ON es.enterpriseID = t.id
        and es.effectiveTime <![CDATA[ <= ]]> now()
        and es.expiryTime <![CDATA[ >= ]]> now()
        LEFT JOIN dsum_t_enterprise_service es2 ON es2.enterpriseID = t.id
        and es2.effectiveTime <![CDATA[ <= ]]> now()
        and es2.expiryTime <![CDATA[ >= ]]> now()
        where (t.reserved2 != 1 or t.reserved2 is null)
        <trim prefix="and" prefixOverrides="and|or">
            <if test="enterpriseIDs != null and enterpriseIDs.size()>0">
                and t.id in
                <foreach item="id" index="index" collection="enterpriseIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>

            <if test="channelSrc !=null  and channelSrc !=''">
                and t.reserved10 = #{channelSrc}
            </if>

            <if test="parentEnterpriseName !=null  and parentEnterpriseName !=''">
                and t.parentEnterpriseName like concat("%", #{parentEnterpriseName}, "%")
            </if>

            <if test="createStartTime !=null and createEndTime !=null">
                and (t.createTime <![CDATA[ <= ]]> #{createEndTime}
                and t.createTime <![CDATA[ >= ]]> #{createStartTime})
            </if>

            <if test="createStartTime != null and createEndTime == null">
                and t.createTime <![CDATA[ >= ]]> #{createStartTime}
            </if>

            <if test="createStartTime == null and createEndTime != null">
                and t.createTime <![CDATA[ <= ]]> #{createEndTime}
            </if>

            <if test="organizationID !=null  and organizationID !=''">
                and t.organizationID like concat("%", #{organizationID}, "%")
            </if>

            <if test="enterpriseCodeLike !=null  and enterpriseCodeLike !=''">
                and t.enterpriseCode like concat("%", #{enterpriseCodeLike  }, "%")
            </if>

            <if test="cityIDs != null and cityIDs.size()>0">
                and t.cityID in
                <foreach item="cityIDs" index="index" collection="cityIDs" open="(" separator="," close=")">
                    #{cityIDs}
                </foreach>
            </if>
            <if test="countyIDs != null and countyIDs.size()>0">
                and t.countyID in
                <foreach item="countyIDs" index="index" collection="countyIDs" open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
            <if test="enterpriseCode !=null and enterpriseCode !='' ">
                and t.enterpriseCode = #{enterpriseCode}
            </if>

            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>

            <if test="isZyzq !=null and isZyzq == 0">
                and ((t.reserved10 != '111' and t.reserved10 != '112') OR t.reserved10 IS NULL )
            </if>

            <if test="isZyzq !=null and isZyzq == 1">
                and t.reserved10 = '111'
            </if>
            <if test="isZyzq !=null and isZyzq == 2">
                and t.reserved10 = '112'
            </if>

            <if test="parentEnterpriseID !=null">
                and t.parentEnterpriseID = #{parentEnterpriseID}
            </if>

            <if test="provinceIDs !=null and provinceIDs.size>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceIDs" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>

                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or t.provinceID is null
                </if>
                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (t.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or t.cityID is null
                </if>

                )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (t.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>

                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or t.countyID is null
                </if>
                )
            </if>
            <if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseIDs" index="index" collection="parentEnterpriseIDs" open="("
                         separator="," close=")">
                    #{parentEnterpriseIDs}
                </foreach>
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and t.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and t.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>
            <if test="auditStatusList != null and auditStatusList.size()>0">
                and t.auditStatus in
                <foreach item="auditStatusList" index="index" collection="auditStatusList" open="(" separator=","
                         close=")">
                    #{auditStatusList}
                </foreach>
            </if>

            <if test="auditStatus !=null ">
                and t.auditStatus = #{auditStatus}
            </if>

            <if test="enterpriseTypeList != null and enterpriseTypeList.size()>0">
                and t.enterpriseType in
                <foreach item="enterpriseType" index="index" collection="enterpriseTypeList" open="(" separator=","
                         close=")">
                    #{enterpriseType}
                </foreach>
            </if>
            <choose>
                <when test="statusList != null and statusList.size()>0">
                    and t.status in
                    <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>

                    <if test="isQueryAllStatus == null or isQueryAllStatus == 0">
                        and (t.status = 1

                        <if test="lastDelDate != null and lastDelDate != ''">
                            or lastUpdateTime > #{lastDelDate}
                        </if>

                        )
                    </if>
                </otherwise>
            </choose>
        </trim>
        <if test="servType !=null ">
            and es.servType = #{servType}
        </if>
        GROUP BY  t.id
        <if test="sortField !=null and sortField !='' and sortType !=null and sortType !=''">
            order by ${sortType} ${sortField}
        </if>
    </select>

    <select id="queryEnterpriseInfo" resultMap="enterpriseWrapper">
        SELECT
            t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
            t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
            t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
            t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
            t.reserved10,t.parentEnterpriseName,t.businessStatus,t.countyID,c.cityName,p.provinceName
        from dsum_t_enterprise t
                 LEFT JOIN dsum_t_city c ON c.cityID = t.cityID
                 LEFT JOIN dsum_t_province p ON p.provinceID = t.provinceID
        where t.id = #{id} and t.status in (1,3)
    </select>

    <select id="queryEnterpriseListCount" resultType="java.lang.Integer">
        SELECT
        count(0) from
        (select t.id from dsum_t_enterprise t
        LEFT JOIN dsum_t_enterprise_service es ON es.enterpriseID = t.id
        and es.effectiveTime <![CDATA[ <= ]]> now()
        and es.expiryTime <![CDATA[ >= ]]> now()
        where (t.reserved2 != 1 or t.reserved2 is null)
        <trim prefix="and" prefixOverrides="and|or">
            <if test="enterpriseIDs != null and enterpriseIDs.size()>0">
                and t.id in
                <foreach item="id" index="index" collection="enterpriseIDs" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like concat("%", #{enterpriseName}, "%")
            </if>

            <if test="channelSrc !=null  and channelSrc !=''">
                and t.reserved10 = #{channelSrc}
            </if>

            <if test="parentEnterpriseName !=null  and parentEnterpriseName !=''">
                and t.parentEnterpriseName like concat("%", #{parentEnterpriseName}, "%")
            </if>

            <if test="createStartTime !=null and createEndTime !=null">
                and (t.createTime <![CDATA[ <= ]]> #{createEndTime}
                and t.createTime <![CDATA[ >= ]]> #{createStartTime})
            </if>
            <if test="effectiveTime !=null ">
                and t.createTime &lt;= #{effectiveTimeEnd}
                and (t.status = 1 or t.lastupdatetime &gt;= #{effectiveTimeStart})
            </if>
            <if test="createStartTime != null and createEndTime == null">
                and t.createTime <![CDATA[ >= ]]> #{createStartTime}
            </if>

            <if test="createStartTime == null and createEndTime != null">
                and t.createTime <![CDATA[ <= ]]> #{createEndTime}
            </if>
            <if test="enterpriseCodeLike !=null  and enterpriseCodeLike !=''">
                and t.enterpriseCode like concat("%", #{enterpriseCodeLike  }, "%")
            </if>
            <if test="organizationID !=null  and organizationID !=''">
                and t.organizationID like concat("%", #{organizationID}, "%")
            </if>

            <if test="cityIDs != null and cityIDs.size()>0">
                and t.cityID in
                <foreach item="cityIDs" index="index" collection="cityIDs" open="(" separator="," close=")">
                    #{cityIDs}
                </foreach>
            </if>
            <if test="countyIDs != null and countyIDs.size()>0">
                and t.countyID in
                <foreach item="countyIDs" index="index" collection="countyIDs" open="(" separator="," close=")">
                    #{countyIDs}
                </foreach>
            </if>
            <if test="enterpriseCode !=null and enterpriseCode !='' ">
                and t.enterpriseCode = #{enterpriseCode}
            </if>

            <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>

            <if test="isZyzq !=null and isZyzq == 0">
                and ((t.reserved10 != '111' and t.reserved10 != '112') OR t.reserved10 IS NULL )
            </if>

            <if test="isZyzq !=null and isZyzq == 1">
                and t.reserved10 = '111'
            </if>
            <if test="isZyzq !=null and isZyzq == 2">
                and t.reserved10 = '112'
            </if>
            <if test="parentEnterpriseID !=null">
                and t.parentEnterpriseID = #{parentEnterpriseID}
            </if>

            <if test="provinceIDs !=null and provinceIDs.size>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceIDs" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and t.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                and (t.provinceID in
                <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator=","
                         close=")">
                    #{provinceID}
                </foreach>
                <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                    or provinceID is null
                </if>

                )
            </if>
            <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                and (t.cityID in
                <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                    #{cityID}
                </foreach>
                <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                    or cityID is null
                </if>
                )
            </if>
            <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                and (t.countyID in
                <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                    #{countyID}
                </foreach>
                <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                    or countyID is null
                </if>
                )
            </if>
            <if test="parentEnterpriseIDs != null and parentEnterpriseIDs.size()>0">
                and t.parentEnterpriseID in
                <foreach item="parentEnterpriseIDs" index="index" collection="parentEnterpriseIDs" open="("
                         separator="," close=")">
                    #{parentEnterpriseIDs}
                </foreach>
            </if>

            <if test="auditStatusList != null and auditStatusList.size()>0">
                and t.auditStatus in
                <foreach item="auditStatusList" index="index" collection="auditStatusList" open="(" separator=","
                         close=")">
                    #{auditStatusList}
                </foreach>
            </if>

            <if test="auditStatus !=null ">
                and t.auditStatus = #{auditStatus}
            </if>
            <if test="enterpriseTypeList != null and enterpriseTypeList.size()>0">
                and t.enterpriseType in
                <foreach item="enterpriseType" index="index" collection="enterpriseTypeList" open="(" separator=","
                         close=")">
                    #{enterpriseType}
                </foreach>
            </if>
            <choose>
                <when test="statusList != null and statusList.size()>0">
                    and t.status in
                    <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
                        #{status}
                    </foreach>
                </when>
                <otherwise>
                    <if test="isQueryAllStatus == null or isQueryAllStatus == 0">
                        and t.status = 1
                    </if>
                </otherwise>
            </choose>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and t.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and t.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
            </if>
        </trim>
        <if test="servType !=null ">
            and es.servType = #{servType}
        </if>
        GROUP BY  t.id ) t

        <!--        <if test="sortField !=null and sortField !='' and sortType !=null and sortType !=''">-->
        <!--            order by ${sortType} ${sortField}-->
        <!--        </if>-->

    </select>

    <select id="queryEnterpriseInfoWithParent" resultMap="enterpriseWrapper">
        SELECT
            t.id,t.enterpriseCode,t.enterpriseName,t.enterpriseDesc,t.enterpriseType,t.organizationID,t.custID,t.businessLicenseID,
            t.businessLicenseURL,t.msisdn,t.createTime,t.operatorID,t.lastUpdateTime,t.parentEnterpriseID,t.provinceID,t.cityID,t.extInfo,
            t.idCardPositiveURL,t.idCardOppositeURL,t.auditStatus,t.auditDesc,t.contract,t.status,t.extInfo,
            t.reserved1,t.reserved2,t.reserved3,t.reserved4,t.reserved5,t.reserved6,t.reserved7,t.reserved8,t.reserved9,
            t.reserved10,t.parentEnterpriseName,t.businessStatus,t.countyID from dsum_t_enterprise t where t.id = #{id} and t.status in (1, 3)
                                                                                                       and t.parentEnterpriseID =#{parentEnterpriseID}
    </select>


    <resultMap id="industryList" type="com.huawei.jaguar.dsdp.bill.model.Industry">
        <result property="industryID" column="industryID" />
        <result property="industryName" column="industryName" />
        <result property="isSensitiveIndustry" column="isSensitiveIndustry" />
    </resultMap>

    <!--查询所属行业信息-->
    <select id="queryIndustryList" resultMap="industryList">
		select industryID,industryName,isSensitiveIndustry from dsum_t_industry order by isSensitiveIndustry desc,industryID asc
	</select>

</mapper>