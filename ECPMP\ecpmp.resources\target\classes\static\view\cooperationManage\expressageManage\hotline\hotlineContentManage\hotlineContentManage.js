var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common"])
app.controller('hotlineContentController', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {
        $scope.number = 70;
        $scope.addressNumber = 200;
        $scope.dsc = "HOTLINE_CONTENTDESC";
        $scope.addressTip = "ADDRESS_CONTENTDESC";
        $scope.msg = "请输入彩印内容1~62字";
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.subEnterpriseID = $.cookie('subEnterpriseID') || '';
        $scope.subEnterpriseName = $.cookie('subEnterpriseName') || '';
        //获取enterpriseName
        $scope.enterpriseName = $.cookie('enterpriseName');
        $scope.initSel = {
                startTime: "",
                endTime: "",
                search:false,
            };
        //判断最终调接口的enterpriseID,enterpriseName
        if ($scope.subEnterpriseID && $scope.enterpriseType == 3) {
        	$scope.parEntenterpriseID = $scope.enterpriseID;
            $scope.enterpriseID = $scope.subEnterpriseID;
            $scope.enterpriseName = $scope.subEnterpriseName;
        }
        //标题
        $scope.contentTitle = '';
        //标题校验
        $scope.contentTitleValidate = true;

        $scope.contentValidate = true;
        //图片数量
        $scope.picLength = 0;
        //视频数量
        $scope.videoLength = 0;
        //文本框的个数
        $scope.ctnTextMount = 0;
        $scope.hotContentInfoListData = [];
        $scope.selectedList = [];
        $scope.hotMsisdnVali = true;
        $scope.contentVali = true;
        $scope.operatorID = $.cookie('accountID');
        $scope.hotlineMsisdnAdd = '';
        $scope.hotlineMsisdnDel = '';
        $scope.hotlineList = [];
        $scope.contentBelongOrgList = [];
        $scope.chooseAllAddHotLine = false;
        //新增热线号码是否可以点击批量接口
        $scope.hasChoseAddHotLine = false;
        $scope.hasChoseDelHotLine = false;
        $scope.$watch('hotlineList', function (newVal, oldVal) {
            $scope.hasChoseAddHotLine = false;
            if (newVal.length > 0) {
                //判断是否至少已经勾选了一项
                for (var i in newVal) {
                    if (newVal[i].checked) {
                        $scope.hasChoseAddHotLine = true;
                        break;
                    }
                }
                var str = JSON.stringify(newVal);
                var index = str.indexOf('"checked":false');
                //同步全选按钮
                $scope.chooseAllAddHotLine = index === -1 ? true : false;
            }
        }, true);
        if ($scope.operate != 'detail') {
            $scope.$watch('contentList', function (newVal, oldVal) {
                $scope.ctnTextSumLength = 0;
                $scope.picLength = 0;
                $scope.videoLength = 0;
                $scope.ctnTextSum = "";
                $scope.ctnTextMount = 0;
                for (var i in newVal) {
                    if (newVal[i].frameTxt == undefined) {
                        newVal[i].frameTxt = "";
                    }
                    if (newVal[i].filesize == '' || newVal[i].filesize == undefined) {
                        newVal[i].filesize = 0;
                    }
                    if (newVal[i].frameType == 1) {
                        $scope.picLength += 1;
                    } else if (newVal[i].frameType == 3) {
                        $scope.videoLength += 1;
                    } else {
                        $scope.ctnTextMount += 1;
                    }
                    $scope.ctnTextSumLength += newVal[i].frameTxt.length;
                    $scope.ctnTextSum += newVal[i].frameTxt;
                }
                // //删除的话，就去再次校验敏感词
                // if (newVal.length < oldVal.length) {
                //     $scope.sensitiveCheck($scope.ctnTextSum, 2)
                // }
            }, true)
        }
        $scope.$watch('contentBelongOrgList', function (newVal, oldVal) {
            $scope.hasChoseDelHotLine = false;
            if (newVal.length > 0) {
                for (var i in newVal) {
                    if (newVal[i].checked) {
                        $scope.hasChoseDelHotLine = true;
                        break;
                    }
                }
                var str = JSON.stringify(newVal);
                var index = str.indexOf('"checked":false');
                //同步全选按钮
                $scope.chooseAllDelHotLine = index === -1 ? true : false;
            }
        }, true)
        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'importContentTemplate'
        };
        $scope.errorInfo = '';
        $scope.fileUrl = '';
        // 上传excel  END
        $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
            if (broadData.file !== "") {
                $scope.fileName = broadData.file.name;
            } else {
                $scope.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.errorInfo = broadData.errorInfo;
            $scope.fileUrl = fileUrl;
        });
        // 上传video
        $scope.accepttypeVideo = "mp4,3gp";
        $scope.isValidateVideo = true;
        $scope.filesizeVideo = 2;
        $scope.mimetypesVideo = ".mp4,.3gp,.MP4,.3GP";
        $scope.autoVideo = true;
        $scope.isCreateThumbnailVideo = false;
        $scope.uploadurlVideo = '/qycy/ecpmp/ecpmpServices/fileService/uploadVideo';
        $scope.uploadDescVideo = "仅支持mp4,3gp格式的文件";
        $scope.numlimitVideo = 3;
        $scope.urlListVideo = [];
        $scope.uploadParamVideo = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms',
            use: 'video'
        };
        // 上传Video  END
        $scope.$on("uploadifyidVideo", function (event, fileUrl, index, broadData) {

            $scope.uploaderVideo = broadData.uploader;

            if (fileUrl) {
                $scope.contentList.push({
                    frameType: 3,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).watch,
                    frameTxt: "",
                    filesize: broadData.file.size
                })
            }
            $scope.errorInfoVideo = broadData.errorInfo;
        });

        //内容限制
        $scope.frameContentMaxNum = 3;
        $scope.frameContentMaxLength = 1000;
        //上传图片
        $scope.accepttypeImg = "jpg,gif,JPG,GIF";
        $scope.isValidateImg = true;
        $scope.filesizeImg = 0.2;
        $scope.imgMaxMum = 3;
        $scope.mimetypesImg = ".jpg,.gif,.JPG,.GIF";
        $scope.isCreateThumbnailImg = false;
        $scope.uploadurlImg = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.numlimitImg = 1000;
        $scope.uploadParamImg = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'ebanhanceMms'
        };

        $scope.maxSizeError = false;
        $scope.$on("uploadifyidImg", function (event, fileUrl, fileData) {
            $scope.uploaderImg = fileData.uploader;
            if (fileUrl) {
                $scope.maxSizeError = false;
                var totalSize = 0;
                for (var j in $scope.contentList) {
                    var tepm = $scope.contentList[j];
                    if (tepm.frameType === 1) {
                        totalSize += tepm.filesize;
                    }
                }
                if ($scope.totalFileSizeImg && totalSize + fileData.file.size > $scope.totalFileSizeImg * 1024 * 1024) {
                    $scope.maxSizeError = true;
                    return;
                }


                $scope.contentList.push({
                    frameType: 1,
                    framePicUrl: fileUrl,
                    formatFramePicUrl: CommonUtils.formatPic(fileUrl).review,
                    frameTxt: "",
                    filesize: fileData.file.size
                })
            } else if (fileData != '' || fileData != undefined) {
            }
        });
        $scope.contentList = [];

        //彩印内容类型
        $scope.typeMap = {
            "7": "交互彩印"
        };
        //状态信息
        $scope.hotlineStatusMap = {
            "1": "审核失败",
            "2": "待审核",
            "3": "审核通过",
            "4": "审核驳回"
        };
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            },
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];
        $scope.isIntelligentCallMap = [
             {
            	 id:0,
            	 name:"无需外呼语音"
             },
             {
            	 id:1,
            	 name:"指令1语音外呼"
             }];
        $scope.addressVali=true;
        $scope.addressSensitive=false;
        $scope.addressDesc='';
        $scope.addressSensitiveStr = "";
        $scope.planTimeFlag = '';
        
        //配置的企业id
        $scope.ids = ["30000093", "直客企业id", "直客企业id", "子企业id", "子企业id"];
        $scope.queryHotLineList('', 'search');
        $scope.queryHotlineContentInfoList();
        //下拉框(投递方式)
        $scope.subServTypeChoise = [
            {
                id: "7",
                name: "交互彩印"
            }
        ];


        //REQ-113 REQ-122
        //查询企业服务开关
        $scope.queryPlatformStatus();

        //查询所属行业列表
        $scope.queryIndustry($scope);

        $scope.initBusinessURLContainer($scope);

        //判断内容长度
        $scope.checkenterpriseid();

        //校验标题
        $scope.checkContentTitle = function (contentTitle) {
            if ($scope.addHotlineContentInfo.subServType == 8) {
                $scope.contentTitleValidate = $scope.validate(contentTitle, 20, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);
            } else {
                $scope.contentTitleValidate = $scope.validate(contentTitle, 9, /^[\u4E00-\u9FA5A-Za-z0-9\s]+$/, true);

            }
        };

        $('#addHotlineContent').on('hidden.bs.modal', function (e) {
            $rootScope.$apply(function () {
                if ($scope.uploaderVideo) {
                    $scope.uploaderVideo.reset();
                }
                if ($scope.uploaderImg) {
                    $scope.uploaderImg.reset();
                }

                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.showUpload = false;
                $scope.businessLicenseURL_ = "";
                $scope.fileUrl_ = "";
                $scope.urlList_ = [];
                //清空表单验证
                $scope.myForm.$setPristine();
            })
            $scope.contentList = [];
        });
        $('#addHotlineContent').on('show.bs.modal', function (e) {
            $scope.showUpload = true;
        })

    };
    
	$scope.importCont = function () {
	    $('#importContentPop').modal();
	    $('#importContentPop').on('hidden.bs.modal', function () {
	      $rootScope.$apply(function () {
	        $("#filePicker").find("span").text("导入文件");
	        if ($scope.uploader) {
	          $scope.uploader.reset();
	        }
	        $scope.errorInfo = "";
	        $scope.fileName = "";
	        $scope.fileUrl = "";
	      })
	    });
	  };
	  
	  $scope.$on("uploadifyid_2", function (event, fileUrl, index, broadData) {
		    if (broadData.file !== "") {
		      $scope.fileName = broadData.file.name;
		    } else {
		      $scope.fileName = "";
		    }
		    $scope.uploader = broadData.uploader;
		    $scope.errorInfo = broadData.errorInfo;
		    $scope.fileUrl = fileUrl;
		  });
      
    $scope.importContent = function () {
	    var req = {
	      "enterpriseID": $scope.enterpriseID,
	      "enterpriseName": $scope.enterpriseName,
	      "parentEnterpriseID":$scope.parEntenterpriseID,
	      "operatorID": $.cookie('accountID'),
	      "path": $scope.fileUrl
	    };
	    RestClientUtil.ajaxRequest({
	      type: 'POST',
	      url: "/ecpmp/ecpmpServices/intelligentCallService/batchAddContent",
	      data: JSON.stringify(req),
	      success: function (data) {
	        $rootScope.$apply(function () {
	          var result = data.result;
	          $('#importContentPop').modal("hide");
	          if (result.resultCode == '**********') {
	            $scope.tip = "导入成功";
	            $('#myModal').modal();
	          } else if (data.failedNum) {
	            $scope.tip = data.failedNum + "条导入失败";
	            $('#myModal').modal();
	          } else {
	            $scope.tip = result.resultCode;
	            $('#myModal').modal();
	          }
	          $scope.queryHotlineContentInfoList();
	        })
	      },
	      error: function () {
	        $rootScope.$apply(function () {
	          $scope.tip = '**********';
	          $('#myModal').modal();
	        })
	      }
	    });
	  }
    
    //内容长度校验
    $scope.checkenterpriseid = function () {
        var arr = $scope.ids;
        var enterpriseId = $scope.enterpriseID;
        if (arr.toString().indexOf(enterpriseId) > -1) {
            $scope.flag = true;
            $scope.number = 748;
            $scope.dsc = "HOTLINE_CONTENTDESC1";
            $scope.msg = "请输入彩印内容1~750字";
        }
    }
    
    $('#start').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });

    $('#end').on('changeDate', function () {
        $rootScope.$apply(function () {
            $scope.searchOn();
        }
        )
    });
    
  //判断搜索按钮是否置灰
    $scope.searchOn = function () {
        var startTime = document.getElementById("start").value;
        var endTime = document.getElementById("end").value;

        if (startTime !== '')
        {
            $scope.initSel.startTime = startTime.substring(0,4) + startTime.substring(5,7)+ startTime.substring(8,10) + '000000';
        }
        else
        {
            $scope.initSel.startTime = "";
        }

        if (endTime !== '')
        {
            $scope.initSel.endTime = endTime.substring(0,4) + endTime.substring(5,7)+ endTime.substring(8,10) + '235959';
        }
        else
        {
            $scope.initSel.endTime = "";
        }
        $scope.initSel.search = false;
    }
    
    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        clearBtn: true,
        language: "zh-CN",
        autoclose: true
    });

    $scope.changeSubServerType = function (val) {

        $scope.showUpload = false;
        setTimeout(function () {
            $scope.showUpload = true;
        }, 1);
        $scope.errorInfoVideo = "";
        $scope.errorInfo = "";
        $scope.contentTitleValidate = true;
        $scope.contentTitle = "";
        $scope.contentList = [];
        $scope.maxSizeError = false;
        $scope.addHotlineContentInfo.content = "";
        $scope.isSensitive = false;
        $scope.contentVali = true;

        var subServTypeChoise = $("#select").val();
        if (val) {
            subServTypeChoise = "string:" + val;
        }
        //判断是否为挂机短信
        if ("string:4" == subServTypeChoise) {
            $scope.number = 750;
            $scope.dsc = "HOTLINE_CONTENTDESC1";
            $scope.msg = "请输入彩印内容1~750字";
        } else {
            $("#filePickerVideo div:eq(1)").attr("style", "position: absolute; top: 0px; left: 0px; width: 107px; height: 35px; overflow: hidden; bottom: auto; right: auto;");
            $("#filePickerImg div:eq(1)").attr("style", "position: absolute; top: 0px; left: 0px; width: 137px; height: 35px; overflow: hidden; bottom: auto; right: auto;");
            $scope.number = 70;
            $scope.dsc = "HOTLINE_CONTENTDESC";
            $scope.msg = "请输入彩印内容1~62字";
        }

        //彩信标题20字，图片5张，大小280K格式为jpg，jpeg，png不区分大小写
        if ("string:8" == subServTypeChoise) {
            $scope.totalFileSizeImg = 0.28;
            $scope.filesizeImg = 0.3;
            $scope.mimetypesImg = ".jpg,.jpeg,.png,.JPG,.JPEG,.PNG";
            $scope.accepttypeImg = "jpg,jpeg,png";
            $scope.imgMaxMum = 5;
            $scope.frameContentMaxNum = 5;
            $scope.frameContentMaxLength = 750;
            $scope.uploadParamImg = {
                enterpriseId: $scope.enterpriseID,
                fileUse: 'ebanhanceMms_cx'
            };

        } else {
            $scope.filesizeImg = 0.2;
            $scope.totalFileSizeImg = null;
            $scope.accepttypeImg = "jpg,gif";
            $scope.mimetypesImg = ".jpg,.gif,.JPG,.GIF";
            $scope.imgMaxMum = 3;
            $scope.frameContentMaxNum = 3;
            $scope.frameContentMaxLength = 1000;
            $scope.uploadParamImg = {
                enterpriseId: $scope.enterpriseID,
                fileUse: 'ebanhanceMms'
            };
        }
        $scope.addHotlineContentInfo.subContentList = [];
        if("string:7" == subServTypeChoise){
            $scope.addHotlineContentInfo.subContentList.push({
                subContentType: 1,
                replyvalid: false,
                replyisensitive: "",
                content: "",
                instruct: ""
            });
        }
        $scope.checkenterpriseid();
    };

    $scope.addTextCtn = function () {
        $scope.contentList.push({
            framePicUrl: "",
            formatFramePicUrl: "",
            frameTxt: "",
            filesize: "",
            frameTxtValidate: true
        })
        $scope.contentValidate = false;

    };
    $scope.deleteCtnOrPic = function (index) {
        $scope.contentList.splice(index, 1);

        for (let i = 0; i < $scope.contentList.length; i++) {
            let temp = $scope.contentList[i];
            if (temp.frameType != 3 && temp.frameType != 1 && !temp.frameTxt) {
                $scope.contentValidate = false;
                return;
            }
        }

        $scope.contentValidate = true;
    };


    //导入号码
    $scope.importHotLinePop = function (item) {
        $scope.selectedItem = item;
        $scope.importContentID = item.contentID;
        $scope.contentVal = item.content;
        $('#impotHotLineNoPop').modal();
        $('#impotHotLineNoPop').on('hidden.bs.modal', function () {
            $rootScope.$apply(function () {
                $("#filePicker").find("span").text("导入文件");
                if ($scope.uploader) {
                    $scope.uploader.reset();
                }
                $scope.errorInfo = "";
                $scope.fileName = "";
                $scope.fileUrl = "";
            })
        });
    };
    $scope.commitImportHotLineNo = function () {
        var req = {
            "templateID": $scope.selectedItem.contentID,
            "enterpriseID": $scope.enterpriseID,
            "operatorID": $scope.operatorID,
            "path": $scope.fileUrl,
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/batchAddTemplateHotlineRel",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $('#impotHotLineNoPop').modal("hide");
                        $scope.tip = "导入成功";
                        $('#myModal').modal();
                    } else if (data.url) {
                        $('#impotHotLineNoPop').modal("hide");
                        $scope.tip = data.failNum + "条导入失败，请查看失败文件";
                        $('#myModal').modal();
                        var req1 = {
                            "param": {
                                "path": data.url,
                                "token": $scope.token,
                                "isExport": 0
                            },
                            "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
                            "method": "get"
                        }
                        CommonUtils.exportFile(req1);
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                    $scope.queryHotLineList('', 'search');
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
    $scope.goback = function () {
        $('.close').click();
    }
    // 新增热线内容弹窗
    $scope.addHotlineContent = function () {
        $scope.operate = 'add';
        $scope.planTime = "2220/11/18 20:00";
        $scope.addHotlineContentInfo = {};
        $scope.addHotlineContentInfo.subContentList = [];
        $scope.planTimeFlag = '';
        //交互ussd 错误指令内容
        $scope.errorSubContent = {
            ID: "",
            instruct: "",
            subContentType: 2,
            content: "您输入的内容有误，感谢您的参与！",
            contentVali: true,
            isSensitive: false,
            contentDesc: ""
        };
        //交互ussd 闪信内容
        $scope.flashSubContent = {
            ID: "",
            instruct: "",
            subContentType: 3,
            content: "",
            contentVali: true,
            isSensitive: false,
            contentDesc: ""
        };
        $scope.addHotlineContentInfo.contentVali = true;
        $scope.addHotlineContentInfo.isSensitive = false;
        $scope.errorcontentVali = true;
        $scope.addHotlineContentInfo.content = '';
        $scope.isSensitive = false;
        $scope.hotMsisdnVali = true;
        $scope.hotMsisdnExist = false;
        $scope.contentVali = true;
        $scope.addHotlineContentInfo.subServType = "7";
        $scope.changeSubServerType($scope.addHotlineContentInfo.subServType);
        $scope.isIntelligentCallMap = [{id:0,name:"无需外呼语音"},{id:1,name:"指令1语音外呼"}];
        $scope.isIntelligentCall = $scope.isIntelligentCallMap[1];
        $scope.changeIsIntelligentCall($scope.isIntelligentCall);
        $scope.address="";
        $scope.trackingNum="";
        $scope.userNum="";
        $scope.callee="";
        //初始化所属行业
        $scope.selectedIndustryID = "";
        $scope.selectedIndustry = "";
        //初始化营业执照
        $scope.businessLicenseURL_ = "";
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];

        $scope.hotMsisdnDesc = '';
        $scope.contentDesc = '';
        $('#addHotlineContent').modal();

    };
    
    // 编辑热线内容弹窗
    $scope.updateHotlineContent = function (item) {
        $scope.addHotlineContentInfo = {};
        $scope.addHotlineContentInfo.contentVali = true;
        $scope.addHotlineContentInfo.isSensitive = false;
        $scope.addressVali = true;
        $scope.addressSensitive = false;
        $scope.addressSensitiveStr = '';
        $scope.addressDesc = '';
        

        $scope.businessLicenseURL_ = "";
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];
        $scope.urlList_2 = [];

        //待审核状态不可修改
        if (item.approveStatus == 2) {
            $scope.tip = "**********";
            $('#myModal').modal();
            return;
        }
        $scope.operate = 'update';
        $scope.contentVali = true;
        $scope.hotMsisdnVali = true;
        $scope.hotMsisdnExist = false;

        $scope.address = item.address;
		$scope.callee = item.receiveNum;
		if($scope.callee != null && $scope.callee.indexOf('86') == 0) {
			$scope.callee = $scope.callee.substring(2,$scope.callee.length);
		}
		$scope.replyValue = item.reply;
		$scope.userNum = item.userNum;
		if($scope.userNum != null && $scope.userNum.indexOf('86') == 0) {
			$scope.userNum = $scope.userNum.substring(2,$scope.userNum.length);
		}
		$scope.trackingNum = item.trackingNum;
		
		if(item.planTime != null && item.planTime != '' && item.planTime != undefined){
    		var year = item.planTime.slice(0, 4);
            var month = item.planTime.slice(4, 6);
            var day = item.planTime.slice(6, 8);
            var hour = item.planTime.slice(8, 10);
            var minute = item.planTime.slice(10, 12);
            $scope.planTime = year + "/" + month + "/" + day + " " + hour + ":" + minute;
            $scope.planTimeFlag = $scope.planTime;
    	} else {
    		$scope.planTime = "2220/11/18 20:00";
    		$scope.planTimeFlag = "";
    	}
        
        var req = {
		        "enterpriseID": $scope.enterpriseID,
		        "contentIDList": [item.contentID],
		        "servTypeList": [2],
		        "contentTypeList": [1, 2],
		        "pageParameter": {
		            "pageNum": 1,
		            "pageSize": 100,
		            "isReturnTotal": "1",
		        }
		    };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********' && parseInt(result.totalAmount) == 1) {
                        var contentDetail = result.contentInfoList[0];
                        $scope.addHotlineContentInfo.subServType = contentDetail.subServType.toString();
                        $scope.changeSubServerType($scope.addHotlineContentInfo.subServType);
                        $scope.addHotlineContentInfo.contentID = contentDetail.contentID;
                        $scope.addHotlineContentInfo.content = contentDetail.content || '';
                        $scope.hotMsisdnDesc = '';
                        $scope.contentDesc = '';
                        var platforms = contentDetail.platforms;
                        $scope.contentTitle = contentDetail.contentTitle;
                        $scope.contentList = [];
                        for (var j in contentDetail.contentFrameMappingList) {
                            var tepm = contentDetail.contentFrameMappingList[j];
                            if (tepm.frameType == 1) {
                                $scope.contentList.push({
                                    frameType: 1,
                                    framePicUrl: tepm.framePicUrl,
                                    formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                                    filesize: tepm.framePicSize
                                });
                            } else if (tepm.frameType == 3) {
                                $scope.contentList.push({
                                    frameType: 3,
                                    framePicUrl: tepm.framePicUrl,
                                    formatFramePicUrl: CommonUtils.formatPic(tepm.framePicUrl).watch,
                                    filesize: tepm.framePicSize
                                });
                            } else {
                                $scope.contentList.push({
                                    frameType: 2,
                                    frameTxt: tepm.frameTxt,
                                    frameTxtValidate: true
                                });
                            }
                        }
                        //初始化所属行业
                        $scope.selectedIndustryID = contentDetail.industryType;
                        $scope.industryListOld  = contentDetail.industryType;
                        if ($scope.industryList) {
                            jQuery.each($scope.industryList, function (i, e) {
                                if (e.industryID == contentDetail.industryType) {
                                    $scope.selectedIndustry = e;
                                }
                            });
                            $scope.changeIsSensitive($scope.selectedIndustry)
                        }
                        $scope.businessLicenseURLOld_ = "";
                        //初始化营业执照
                        if (contentDetail.businessLicenseURL) {
                            $scope.businessLicenseURLOld_ = contentDetail.businessLicenseURL;
                            $scope.businessLicenseURL_ = contentDetail.businessLicenseURL;
                            $scope.fileUrl_ = CommonUtils.formatPic(contentDetail.businessLicenseURL).review;
                            $scope.urlList_ = [$scope.fileUrl_];
                            $scope.urlList_2 = [$scope.fileUrl_];
                        }
                        //交互ussd
                        $scope.addHotlineContentInfo.subContentList = [];
                        //交互ussd 闪信内容
                        $scope.flashSubContent = {
                            subContentType: 3,
                            contentVali: true,
                            isSensitive: false,
                            contentDesc: ""
                        };
                        $scope.flashSubContentOld = {
                            subContentType: 3,
                            contentVali: true,
                            isSensitive: false,
                            contentDesc: ""
                        };
	                    $scope.addHotlineContentInfo.subContentListOld = [];
	                    $scope.isIntelligentCallMap = [{id:0,name:"无需外呼语音"}];
	                    $scope.isIntelligentCall = $scope.isIntelligentCallMap[0];
            		  for (var o in contentDetail.subset) {
            	            //为闪信指令
            	            let object = contentDetail.subset[o];
            	            if (!object.instruct) {
            	                //交互ussd 闪信内容
            	                $scope.flashSubContent = {
            	                    ID: object.contentID,
            	                    subContentType: 3,
            	                    content: object.content,
            	                    contentVali: true,
            	                    isSensitive: false,
            	                    contentDesc: ""
            	                };
            	                $scope.flashSubContentOld = {
            	                    ID: object.contentID,
            	                    subContentType: 3,
            	                    content: object.content,
            	                    contentVali: true,
            	                    isSensitive: false,
            	                    contentDesc: ""
            	                };
            	            } else if (object.instruct === "$$ERROR") {
            	                //错误指令
            	                //交互ussd 错误指令内容
            	                $scope.errorSubContent = {
            	                    ID: object.contentID,
            	                    instruct: "",
            	                    subContentType: 2,
            	                    content: object.content,
            	                    contentVali: true,
            	                    isSensitive: false,
            	                    contentDesc: ""
            	                };
            	                $scope.errorSubContentOld = $scope.errorSubContent;
            	            } else {
            	            	$scope.isIntelligentCallMap.push({
            	                	 id:Number(o)+1,
            	                	 name:"指令"+(Number(o)+1)+"语音外呼"
            	            	});
            	            	if($scope.replyValue == object.instruct)
            	            	{
            	            		$scope.replyIndex = Number(o);
            	            		$scope.isIntelligentCallIndex = Number(o)+1;
            	            		$scope.isIntelligentCall = $scope.isIntelligentCallMap[Number(o)+1];
            	            	}
            	                //普通指令
            	                $scope.addHotlineContentInfo.subContentList.push({
            	                    ID: object.contentID,
            	                    subContentType: 1,
            	                    replyvalid: false,
            	                    replyisensitive: "",
            	                    content: object.content,
            	                    instruct: object.instruct
            	                });
            	                $scope.addHotlineContentInfo.subContentListOld.push({
            	                    ID: object.contentID,
            	                    subContentType: 1,
            	                    replyvalid: false,
            	                    replyisensitive: "",
            	                    content: object.content,
            	                    instruct: object.instruct
            	                });
            	            }
            	        }
                        $scope.contentOld =contentDetail.content;
                        $('#addHotlineContent').modal();
                    } else {
                        $('#myModal').modal();
                        return;
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                    return;
                })
            }
        });
    };
    // 删除热线内容弹窗
    $scope.deleteHotlineContent = function (item) {
        $scope.selectedItemDel = item;
        $scope.operate = 'delete';
        $('#deleteHotlineContent').modal();
    };
    
    //投递内容
    $scope.delivery = function (item) {
    	var deliveryReq = {
                "msgtype": "7",
                "src": 10658086,
                "biztype": "1",
                "deliveryContentList":[
                      {
                    	  "scalling":item.userNum,
                    	  "target":item.receiveNum,
                    	  "template":item.contentID,
                    	  "called":item.receiveNum,
                    	  "direction":"MO",
                    	  "event":"No Answer"
                      }]
            };
    	$scope.submitDelivery(deliveryReq);
    }
    
    $scope.submitDelivery = function(deliveryReq)
    {
    	RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/deliveryService/delivery",
            data: JSON.stringify(deliveryReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                    	$scope.tip = '**********';
                        $('#myModal').modal();
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //暂停热线内容
    $scope.suspendHotlineContent = function (item, operaterType) {
        console.log(item);
        var removeReq = {
            "operaterType": operaterType,   //操作类型：0启动，1暂停
            "servType": "2",
            "contentID": item.contentID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/suspendContent",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.queryHotlineContentInfoList();
                    } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }
    // 新增号码弹窗
    $scope.addHotLinePop = function (item) {
        $scope.hotlineMsisdnAdd = "";
        if ($scope.hotlineList.length == 0) {
            $scope.tip = "1030120800";
            $('#myModal').modal();
            return;
        }
        $scope.selectedItem = item;
        $scope.pageInfo[1].pageSize = '10';
        $scope.queryHotLineList(item, 'search');
        $('#addHotlinePop').modal();
    };
    // 号码管理弹窗
    $scope.manageHotLinePop = function (item) {
        $scope.hotlineMsisdnDel = "";
        $scope.selectedItem = item;
        $scope.pageInfo[2].pageSize = '10';
        $scope.queryContentRelObjectList(item, 'search');
        $('#manageHotLinePop').modal();
    };
    $scope.queryContentRelObjectList = function (item, condition) {
        $scope.hasChoseDelHotLine = false;
        if (condition != 'justPage') {
            var req = {
                "enterpriseID": parseInt($scope.enterpriseID),
                "contentID": item.contentID,
                "ownerType": 2,
                "hotlineNo": $scope.hotlineMsisdnDel,
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[2].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[2].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[2].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[2].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/queryContentRelObjectList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.contentBelongOrgList = result.contentBelongOrgList || [];
                        if ($scope.contentBelongOrgList.length > 0) {
                            for (var i in $scope.contentBelongOrgList) {
                                $scope.contentBelongOrgList[i].checked = false;
                            }
                        }
                        $scope.pageInfo[2].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[2].totalPage = $scope.pageInfo[2].totalCount !== 0 ? Math.ceil($scope.pageInfo[2].totalCount / parseInt($scope.pageInfo[2].pageSize)) : 1;
                    } else {
                        $scope.contentBelongOrgList = [];
                        $scope.pageInfo[2].currentPage = 1;
                        $scope.pageInfo[2].totalCount = 0;
                        $scope.pageInfo[2].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    $scope.queryHotLineList = function (item, condition) {
        $scope.hasChoseAddHotLine = false;
        if (condition != 'justPage') {
            var req = {
                "enterpriseID": parseInt($scope.enterpriseID),
                "servTypeList": [2],
                "hotlineNo": $scope.hotlineMsisdnAdd,
                "page": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[1].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[1].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.page.pageNum = parseInt($scope.pageInfo[1].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[1].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/queryHotlineList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.hotlineList = result.hotlineList || [];
                        for (var i in $scope.hotlineList) {
                            $scope.hotlineList[i].checked = false;
                        }
                        $scope.pageInfo[1].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[1].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[1].pageSize)) : 1;
                    } else {
                        $scope.hotlineList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    $scope.selectAllHotLine = function () {
        if ($scope.chooseAllAddHotLine) {
            for (var i in $scope.hotlineList) {
                $scope.hotlineList[i].checked = true;
            }
        } else {
            for (var i in $scope.hotlineList) {
                $scope.hotlineList[i].checked = false;
            }
        }
    }
    $scope.selectAllDelHotLine = function () {
        if ($scope.chooseAllDelHotLine) {
            for (var i in $scope.contentBelongOrgList) {
                $scope.contentBelongOrgList[i].checked = true;
            }
        } else {
            for (var i in $scope.contentBelongOrgList) {
                $scope.contentBelongOrgList[i].checked = false;
            }
        }
    }
    $scope.singleOrBatchAddHotLine = function (condition, item) {
        //单个添加热线号码
        var req = {
            "templateHotlineRel": {
                "templateID": $scope.selectedItem.contentID,
                "enterpriseID": parseInt($scope.enterpriseID)
            }
        };
        if (condition == 'single') {
            req.templateHotlineRel.hotlineList = [item.hotlineNo.toString()]
        } else if (condition == 'batch') {//批量添加
            req.templateHotlineRel.hotlineList = [];
            for (var i in $scope.hotlineList) {
                if ($scope.hotlineList[i].checked) {
                    req.templateHotlineRel.hotlineList.push($scope.hotlineList[i].hotlineNo.toString())
                }
            }
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/addTemplateHotlineRel",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    $scope.tip = data.resultCode;
                    if (data.resultCode == '**********') {
                        $scope.tip = '添加成功';
                        $('#myModal').modal();
                        $scope.queryHotLineList($scope.selectedItem, 'search');
                    } else {
                        $scope.tip = data.resultCode;
                        if ($scope.tip == '1030120051') {
                            $scope.tip = '热线内容中热线号码已关联';
                        }
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });

    }
    $scope.sureDelHotLine = function (condition, item) {
        $scope.singleOrBatch = condition;
        $scope.delItem = item;
        $('#deleteHotLinePop').modal();
    }
    $scope.singleOrBatchDelHotLine = function (condition) {
        $('#deleteHotLinePop').modal('hide');
        //单个删除热线号码
        var req = {
            "templateHotlineRel": {
                "templateID": $scope.selectedItem.contentID,
                "enterpriseID": parseInt($scope.enterpriseID)
            }
        };
        if (condition == 'single') {
            req.templateHotlineRel.hotlineList = [$scope.delItem.hotlineNo.toString()]
        } else if (condition == 'batch') {//批量添加
            req.templateHotlineRel.hotlineList = [];
            for (var i in $scope.contentBelongOrgList) {
                if ($scope.contentBelongOrgList[i].checked) {
                    req.templateHotlineRel.hotlineList.push($scope.contentBelongOrgList[i].hotlineNo.toString())
                }
            }
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteTemplateHotlineRel ",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.tip = '删除成功';
                        $('#myModal').modal();
                        $scope.queryContentRelObjectList($scope.selectedItem, 'search');
                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });

    }
    /*校验各个字段*/
    $scope.validate = function (context, maxlength, reg) {
        if (!context) {
            return false;
        } else {
            if (context.length > maxlength) {
                return false;
            } else {
                if (!reg.test(context)) {
                    return false;
                } else {
                    return true;
                }
            }
        }
    };
    
    $scope.checkAddress = function () {
        $scope.addressVali = true;
        if ($scope.address) {
            if ($scope.address.length > $scope.addressNumber) {
                $scope.addressVali = false;
            } else {
                $scope.addressVali = true;
            }
        } else {
            $scope.sensitiveWords = [];
            $scope.addressSensitive = false;
            $scope.addressSensitiveStr = "";
            $scope.addressVali = false;
        }
        if (!$scope.addressVali) {
            $scope.addressDesc = $scope.addressTip;
        } else {
            $scope.addressDesc = "";
        }
    };

    //彩印内容必填，最长62个字
    $scope.checkHotlineContent = function () {
        $scope.contentVali = true;
        if ($scope.addHotlineContentInfo.content) {
            if ($scope.addHotlineContentInfo.content.length > $scope.number) {
                $scope.contentVali = false;
            } else {
                $scope.contentVali = true;
            }
        } else {
            $scope.sensitiveWords = [];
            $scope.isSensitive = false;
            $scope.sensitiveWordsStr = "";
            $scope.contentVali = false;
        }
        if (!$scope.contentVali) {
            $scope.contentDesc = $scope.dsc;
        } else {
            $scope.contentDesc = "";
        }
    };
    //彩印内容必填，最长62个字
    $scope.checkHotlineContentUSSD = function (content,type) {
        content.contentVali = true;
        if (content.content) {
            if (content.content.length > 62) {
                content.contentVali = false;
            } else {
                content.contentVali = true;
            }
        }
        else {
            content.sensitiveWords = [];
            content.isSensitive = false;
            content.sensitiveWordsStr = "";
            if(!type){
                content.contentVali = false;
            }
        }
        if (!content.contentVali) {
        	content.contentDesc = $scope.dsc;
        } else {
            content.contentDesc = "";
        }
    };
    
    $scope.addressCheck = function() {
    	$scope.checkAddress();
    	if ($scope.addressVali) {
            $scope.sensitiveWords = [];
            $scope.addressSensitive = false;
            $scope.addressSensitiveStr = "";
            var req = {
                "content": $scope.address || '',
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '1030120017') {
                            $scope.sensitiveWords = result.sensitiveWords || [];
                            if ($scope.sensitiveWords.length > 0) {
                                $scope.addressSensitive = true;
                                $scope.addressSensitiveStr = $scope.sensitiveWords.join('、');
                            } else {
                                $scope.addressSensitive = false;
                            }
                        } else if (data.resultCode == '**********') {
                            $scope.sensitiveWords = [];
                            $scope.addressSensitive = false;
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                            $scope.tip = '**********';
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }
    };

    //确定提交前敏感词校验
    $scope.beforeCommit = function () {
        //彩信 增彩另外校验
        if ($scope.addHotlineContentInfo.subServType == 7) {
            $scope.createHotlineContent();
            return;
        }
    }
    //新增(编辑)热线内容
    $scope.createHotlineContent = function () {
        $scope.addHotlineContentInfo.operatorID = parseInt($scope.operatorID);
        $scope.addHotlineContentInfo.enterpriseID = parseInt($scope.enterpriseID);
        $scope.addHotlineContentInfo.enterpriseName = $scope.enterpriseName;
        $scope.addHotlineContentInfo.subServType = parseInt($scope.addHotlineContentInfo.subServType);
        $scope.addHotlineContentInfo.signature = $scope.addHotlineContentInfo.signature;
        $scope.addHotlineContentInfo.industryType = $scope.selectedIndustryID;
        $scope.addHotlineContentInfo.businessLicenseURL = $scope.businessLicenseURL_;
        $scope.addHotlineContentInfo.platforms = '111';
        // if($scope.hotlineList){
        //   jQuery.each($scope.hotlineList, function (i, e) {
        //         if (e.hotlineNo == $scope.addHotlineContentInfo.hotlineNo) {
        //           $scope.addHotlineContentInfo.hotlineID = e.id;
        //         }
        //       });
        // }
        //交互ussd 添加字内容
        if ($scope.addHotlineContentInfo.subServType == 7) {
            //再次校验内容
            $scope.sensitiveCheckUSSD($scope.addHotlineContentInfo);
            if (!$scope.contentVali || !$scope.hotMsisdnVali) {
                return;
            }
            var tempArry = [];
            //校验指令是否有重复
            for(var f = 0;f<$scope.addHotlineContentInfo.subContentList.length;f++){
                if(!tempArry.includes($scope.addHotlineContentInfo.subContentList[f].instruct)) {
                    tempArry.push($scope.addHotlineContentInfo.subContentList[f].instruct)
                }
            }
            if(tempArry.length!=$scope.addHotlineContentInfo.subContentList.length){
                $scope.tip = "存在重复指令";
                $('#myModal').modal();
                return;
            }

            //判断地址信息
            var reg_num=/^(\{\S[^{}]*\})+$/;
  		    if(!reg_num.test($scope.address)){
  			    $scope.tip = '请按要求填写地址信息';
  			    $('#myModal').modal();
  	            return;
  		    }

            //判断第一次和第二次提交内容是否一样
            var flag = false;
            if($scope.addHotlineContentInfo.subContentListOld&&$scope.addHotlineContentInfo.subContentListOld.length == $scope.addHotlineContentInfo.subContentList.length){
                for(var i = 0;i<$scope.addHotlineContentInfo.subContentListOld.length;i++){
                    if($scope.addHotlineContentInfo.subContentListOld[i].content!=$scope.addHotlineContentInfo.subContentList[i].content){
                        flag = true;
                    }
                    if($scope.addHotlineContentInfo.subContentListOld[i].instruct!=$scope.addHotlineContentInfo.subContentList[i].instruct){
                        flag = true;
                    }
                }
            }else {
                flag = true;
            }
            if((!flag&&($scope.flashSubContentOld&&$scope.flashSubContentOld.content != $scope.flashSubContent.content))||(!$scope.flashSubContentOld&&$scope.flashSubContent.content)){
                flag = true;
            }
            if(!flag&&$scope.contentOld !=$scope.addHotlineContentInfo.content){
                flag = true;
            }

            if(!flag&&$scope.contentOld !=$scope.addHotlineContentInfo.content){
                flag = true;
            }
            if(!flag&&$scope.businessLicenseURL_ !=$scope.businessLicenseURLOld_){
                flag = true;
            }
            if(!flag&&$scope.selectedIndustryID !=$scope.industryListOld){
                flag = true;
            }

            if($scope.operate == 'update' && $scope.planTimeFlag != '')
  		  	{
      		  	var planTimeTepm = $scope.planTime.replace("\/","").replace("\/","").replace(":","").replace(" ","");
      		  	$scope.planTimeFlag = planTimeTepm + "00";
      		  	var year = planTimeTepm.slice(0, 4);
  	            var month = planTimeTepm.slice(4, 6);
  	            var day = planTimeTepm.slice(6, 8);
  	            if (Number(month) == 2)
  	            {
  	            	if(Number(year) % 4 == 0 && Number(year) % 100 != 0 || Number(year) % 400 == 0)
  	            	{
  	            		if(Number(day) > 29)
  	            		{
  	            			$scope.tip = '预计下发时间格式错误';
  	  	    			  	$('#myModal').modal();
  	  	    			  	return;
  	            		}
  	            	} else {
  	            		if(Number(day) > 28)
  	            		{
  	            			$scope.tip = '预计下发时间格式错误';
  	  	    			  	$('#myModal').modal();
  	  	    			  	return;
  	            		}
  	            	}
  	            }
  	            if(Number(month) >= 11)
  	        	{
  	            	month = "" + Number(month)-1;
  	        	} else if (Number(month) <= 10)
  	    		{
  	        		month = "0" + (Number(month)-1);
  	    		}
  	            
  	            var hour = planTimeTepm.slice(8, 10);
  	            var minute = planTimeTepm.slice(10, 12);
  	            var second = planTimeTepm.slice(12, 14);
  	            var planDate = new Date(year, month, day, hour, minute, second);
  	            var now = new Date();
  	            if(planDate < now) {
  	            	$scope.tip = '预计下发时间不能小于当前时间';
  	    			  $('#myModal').modal();
  	    	          return;
  	            }
  		  	}
            
            if(!flag){
            	if($scope.operate == 'update')
        		{
            		$scope.submitWltService(null);
        		}
            	return;
            }
            
            //添加错误指令内容
            /*$scope.addHotlineContentInfo.subContentList.push({
                ID: $scope.errorSubContent.ID,
                instruct: $scope.errorSubContent.instruct,
                content: $scope.errorSubContent.content,
                subContentType: 2
            });*/
        }
        var req = {
            "hotlineContent": $scope.addHotlineContentInfo
        };
        var serviceUrl = '/ecpmp/ecpmpServices/hotlineService/addHotlineContent';
        if ($scope.operate == 'update') {
            serviceUrl = '/ecpmp/ecpmpServices/hotlineService/updateHotlineContent';
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: serviceUrl,
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                    	$scope.submitWltService(result.contentID);
                    } else {
                    	$scope.queryHotlineContentInfoList();
                        $('#addHotlineContentCancel').click();
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#addHotlineContentCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
        
    }
    
    $scope.submitWltService = function(contentID) {
    	var submitWltServiceReq = {};
    	if($scope.operate == 'delete')
    	{
    		submitWltServiceReq = {
    				"wltServiceInfo" : {
    					"ussdCalloutAction":2,
	    				"wltUssdCalloutList":[{
	    					"contentID":contentID
	    				}]
    	    		}
    		};
    	} else {
    		var userNumTemp = $scope.userNum;
        	if($scope.userNum.indexOf('86') != 0) {
        		userNumTemp = '86' + $scope.userNum;
      	  	}
        	var calleeTemp = $scope.callee;
        	if($scope.callee.indexOf('86') != 0) {
        		calleeTemp = '86' + $scope.callee;
        	}
        	submitWltServiceReq = {
        		"wltServiceInfo":{
        			"enterpriseID":$scope.parEntenterpriseID,
        			"serviceType":1,
        			"wltUssdCalloutList":[]
        		}
        	};
    		var wltUssdCallout = {
    				"contentID":contentID,
        			"address":$scope.address,
        			"receiveNum":calleeTemp,
        			"userNum":userNumTemp,
        			"trackingNum":$scope.trackingNum
    		};
        	if($scope.isIntelligentCallIndex != '')
    		{
        		for(var f = 0;f<$scope.addHotlineContentInfo.subContentList.length;f++){
        			if(f == $scope.isIntelligentCallIndex-1)
        			{
        				wltUssdCallout.reply = $scope.addHotlineContentInfo.subContentList[f].instruct;
        				break;
        			}
        		}
    		}
        	if($scope.operate == 'update')
    		{
        		wltUssdCallout.contentID = $scope.addHotlineContentInfo.contentID;
        		wltUssdCallout.planTime = $scope.planTimeFlag;
        		submitWltServiceReq.wltServiceInfo.ussdCalloutAction = 1;
    		} else {
    			submitWltServiceReq.wltServiceInfo.ussdCalloutAction = 0;
    		}
        	if(wltUssdCallout.contentID != undefined
        			&& wltUssdCallout.contentID != null)
    		{
        		submitWltServiceReq.wltServiceInfo.wltUssdCalloutList[0] = wltUssdCallout;
    		}
    	}
    	console.log(submitWltServiceReq);
		RestClientUtil.ajaxRequest({
  	      type: 'POST',
  	      url: "/ecpmp/ecpmpServices/intelligentCallService/submitWltService",
  	      data: JSON.stringify(submitWltServiceReq),
  	      success: function (result) {
  	        $rootScope.$apply(function () {
  	          var data = result.result;
  	          if (data.resultCode == '**********') {
  	        	  $scope.tip = '**********';
                  $('#myModal').modal();
  	        	  $scope.queryHotlineContentInfoList();
  	        	  $('#addHotlineContentCancel').click();
  	          } else {
  	        	  $scope.queryHotlineContentInfoList();
  	        	  $('#addHotlineContentCancel').click();
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
  	          }
  	        })
  	      },
  	      error: function () {
  	        $rootScope.$apply(function () {
  	        	$('#addHotlineContentCancel').click();
                  $scope.tip = '**********';
                  $('#myModal').modal();
  	        })
  	      }
  	    });
    }

    //删除热线内容
    $scope.delHotlineContent = function () {
        var item = $scope.selectedItemDel;
        console.log(item);
        var removeReq = {
            "operaterType": "2",    //1名片，2热线，3广告
            "id": item.contentID
        };

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/hotlineService/deleteHotlineContent",
            data: JSON.stringify(removeReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $('#deleteHotlineContentCancel').click();
                        $scope.submitWltService(item.contentID);
                        $scope.queryHotlineContentInfoList();
                    } else {
                        $('#deleteHotlineContentCancel').click();
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $('#deleteHotlineContentCancel').click();
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //查询热线内容列表
    $scope.queryHotlineContentInfoList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "content": $scope.content || '',
                "enterpriseID": $scope.ententerpriseID,
                "gerRefUssd": 1,
                "planTimeStart":$scope.getTime($scope.initSel.startTime),
                "planTimeEnd":$scope.getTime($scope.initSel.endTime),
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": parseInt($scope.pageInfo[0].pageSize),
                    "isReturnTotal": "1",
                }
            };
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryHotlineContentInfoListTemp = angular.copy(req);
        } else {
            //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.queryHotlineContentInfoListTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/intelligentCallService/queryWltService",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.hotContentInfoListData = result.wltServiceInfo.wltUssdCalloutList || [];
                        angular.forEach($scope.hotContentInfoListData, function (e, i) {
                        	var wltUssdCallout = angular.copy(e);
                        	if (e.planTime != null && e.planTime != '' && e.planTime != undefined) {
                  				var year = e.planTime.slice(0, 4);
                  	            var month = e.planTime.slice(4, 6);
                  	            if(Number(month) >= 11)
            	            	{
                	            	month = "" + Number(month)-1;
            	            	} else if (Number(month) <= 10)
        	            		{
            	            		month = "0" + (Number(month)-1);
        	            		}
                  	            var day = e.planTime.slice(6, 8);
                  	            var hour = e.planTime.slice(8, 10);
                  	            var minute = e.planTime.slice(10, 12);
                  	            var second = e.planTime.slice(12, 14);
                  	            var planDate = new Date(year, month, day, hour, minute, second);
                  	            var now = new Date();
                  	            if(planDate > now) {
                  	            	wltUssdCallout.showEdit = true;
                  	            } else {
                  	            	wltUssdCallout.showEdit = false;
                  	            }
                  		     } else {
                  		    	wltUssdCallout.showEdit = true;
                  		     }
                        	 $scope.hotContentInfoListData[i] = wltUssdCallout;
                  	    });
                        $scope.pageInfo[0].totalCount = parseInt(result.wltServiceInfo.wltUssdTotalCount) || 0;
                        $scope.pageInfo[0].totalPage = result.wltServiceInfo.wltUssdTotalCount !== 0 ? Math.ceil(result.wltServiceInfo.wltUssdTotalCount / parseInt($scope.pageInfo[0].pageSize)) : 1;
                    } else {
                        $scope.hotContentInfoListData = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.hotContentInfoListData = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    }

    //REQ-113 REQ-122
    //初始化营业执照上传容器
    $scope.initBusinessURLContainer = function ($scope) {
        $scope.showUpload = false;
        $scope.fileUrl_ = "";
        $scope.urlList_ = [];
        $scope.urlList_2 = [];
        //初始化营业执照上传容器
        $scope.filePicker_ = "filePicker_";
        $scope.accepttype_ = "jpg,jpeg,png";
        $scope.isValidate_ = false;
        $scope.filesize_ = 20;
        $scope.mimetypes_ = ".jpg,.jpeg,.png";
        $scope.isCreateThumbnail_ = true;
        $scope.uploadurl_ = '/qycy/ecpmp/ecpmpServices/fileService/uploadImg';
        $scope.uploadDesc_ = "仅支持一张图片，仅支持jpg，jpeg，png格式";
        $scope.numlimit_ = 1;
        $scope.uploadParam_ = {
            enterpriseId: $scope.enterpriseID || '',
            fileUse: 'businessLicense'
        };

        $scope.$on("uploadifyid_1", function (event, fileUrl_) {
            if (fileUrl_) {
                $scope.urlList_ = [fileUrl_];
                $scope.urlList_2 = [CommonUtils.formatPic(fileUrl_).review];
            } else {
                $scope.urlList_ = [];
                $scope.urlList_2 = [];
            }
            $scope.businessLicenseURL_ = fileUrl_;
        });
    }


    //所属行业是否为敏感行业
    $scope.changeIsSensitive = function (selectedIndustry) {
        if (selectedIndustry) {
            $scope.selectedIndustryID = selectedIndustry.industryID;
            $scope.isSensitiveIndustry = selectedIndustry.isSensitiveIndustry;
        } else {
            $scope.selectedIndustryID = '';
            $scope.selectedIndustryName = '';
        }
    };
    $scope.changeIsIntelligentCall = function (isIntelligentCall) {
        if (isIntelligentCall) {
            $scope.isIntelligentCallIndex = isIntelligentCall.id;
        } else {
            $scope.isIntelligentCallIndex = '';
        }
    };
    //审核意见合并
    $scope.getApproveIdea = function (item) {
        let approveIdea = item.approveIdea||"";
        if (item.subset && item.subset.length > 0) {
            for (let i = 0; i<item.subset.length; i++) {
                if(item.subset[i].approveIdea){
                    if(approveIdea){
                        approveIdea +="|"+item.subset[i].approveIdea;
                    }else {
                        approveIdea =item.subset[i].approveIdea;

                    }

                }
            }
        }
        return approveIdea;
    }
    //展示预计下发时间
    $scope.getTime = function (time) {
    	if(time != null && time != '' && time != undefined){
    		var year = time.slice(0, 4);
            var month = time.slice(4, 6);
            var day = time.slice(6, 8);
            var hour = time.slice(8, 10);
            var minute = time.slice(10, 12);
            var second = time.slice(12, 14);
            return year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
    	} else {
    		return "";
    	}
    }
    //查询所属行业
    $scope.queryIndustry = function ($scope) {
        //默认非敏感行业:0-非敏感；1-敏感
        $scope.selectedIndustryID = '';
        $scope.isSensitiveIndustry = '';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryIndustryList",
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '**********') {
                        $scope.industryList = data.industryList;
                    } else {
                        $scope.tip = data.result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    //查询企业服务开关
    $scope.queryPlatformStatus = function () {
        var queryServiceControlReq = {
            "enterpriseID": $scope.enterpriseID
        }
        $scope.signatureRequired = '0';
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceControl",
            data: JSON.stringify(queryServiceControlReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.platformStatus = result.platformStatus;

                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = "**********";
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.showBusinessURL = function () {

        $("#showBusinessURL").modal('show');

    }

    $scope.hideBusinessURL = function () {

        $("#showBusinessURL").modal('hide');

    }

    //新增指令回复
    $scope.addMsisdn = function () {
        if ($scope.addHotlineContentInfo.subContentList.length < 3) {
            $scope.addHotlineContentInfo.subContentList.push({
                ID: "",
                instruct: "",
                content: "",
                replyvalid: false,
                replyisensitive: "",
                subContentType: 1
            });
            $scope.isIntelligentCallMap.push({
	           	 id:$scope.addHotlineContentInfo.subContentList.length,
	           	 name:"指令"+($scope.addHotlineContentInfo.subContentList.length)+"语音外呼"
            });
        }
    };
    //新增指令回复
    $scope.delMsisdn = function () {
        if ($scope.addHotlineContentInfo.subContentList.length > 1) {
            $scope.addHotlineContentInfo.subContentList.pop();
            
            $scope.isIntelligentCallMap.pop();
            if($scope.isIntelligentCallIndex > $scope.addHotlineContentInfo.subContentList.length)
            {
            	$scope.isIntelligentCall = $scope.isIntelligentCallMap[$scope.addHotlineContentInfo.subContentList.length];
            }
        }
    };
//ussd
    $scope.replyCheckissen = function (que) {
        $scope.replycontentDesc = '';
        var replyContent = "";
        $scope.replycontentDesc = 'HOTLINE_CONTENTDESC';
        replyContent = $scope.addHotlineContentInfo.subContentList[que].content;
        if (replyContent.length > 62) {
            $scope.addHotlineContentInfo.subContentList[que].replyvalid = true;
            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
        } else if (replyContent) {
            $scope.addHotlineContentInfo.subContentList[que].replyvalid = false;
            $scope.sensitiveChecknew(replyContent, que);
        }else {
            $scope.addHotlineContentInfo.subContentList[que].replyvalid = true;
            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
        }
    };
    //敏感词校验
    $scope.sensitiveChecknew = function (content, que) {
        var sensitiveWords = [];
        var sensitiveStr = "";
        var req = {
            "content": content
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030120017') {
                        sensitiveWords = result.sensitiveWords || [];
                        if (sensitiveWords.length > 0) {
                            sensitiveStr = sensitiveWords.join('、');
                        }
                        else {
                            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = "";
                        }
                        if (sensitiveStr) {
                            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = sensitiveStr;

                        }
                        else {
                            $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
                        }
                    } else if (data.resultCode == '**********') {
                        sensitiveWords = [];
                        $scope.addHotlineContentInfo.subContentList[que].replyisensitive = '';
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });

    };
    
    $scope.showAddressModal = function() {
    	$('#addressModal').modal();
    };

    //敏感词校验USSD
    $scope.sensitiveCheckUSSD = function (content,type) {

        $scope.checkHotlineContentUSSD(content,type);
         if (content.contentVali) {
	        content.sensitiveWords = [];
	        content.isSensitive = false;
	        content.sensitiveWordsStr = "";
	        var req = {
	            "content": content.content || ''
	        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/commonService/sensitiveCheck",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030120017') {
                        content.sensitiveWords = result.sensitiveWords || [];
                        if (content.sensitiveWords.length > 0) {
                            content.isSensitive = true;
                            content.sensitiveWordsStr = content.sensitiveWords.join('、');

                        } else {
                            content.isSensitive = false;
                        }
                    } else if (data.resultCode == '**********') {
                        content.sensitiveWords = [];
                        content.isSensitive = false;
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
       }
    };

}]);
app.config(['$locationProvider', function ($locationProvider) {
    $locationProvider.html5Mode({
        enabled: true,
        requireBase: false
    });
}]);
