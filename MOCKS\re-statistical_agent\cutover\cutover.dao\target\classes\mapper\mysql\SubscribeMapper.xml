<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.cutover.dao.ecpe.mapper.SubscribeMapper">

	<resultMap id="subscribeWrapper"
		type="com.huawei.jaguar.cutover.dao.domain.SubscribeWrapper">
		<result property="id" column="id" javaType="java.lang.Long" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="productID" column="productID" javaType="java.lang.Integer" />
		<result property="orderItemID" column="orderItemID" javaType="java.lang.String" />
		<result property="orderID" column="orderID" javaType="java.lang.String" />
		<result property="status" column="status" javaType="java.lang.Integer" />
		<result property="productType" column="productType" javaType="java.lang.Integer" />		
		<result property="isExperience" column="isExperience" javaType="java.lang.Integer" />		
		<result property="servType" column="servType" javaType="java.lang.Integer" />		
		<result property="subServType" column="subServType" javaType="java.lang.Integer" />		
		<result property="isLimit" column="isLimit" javaType="java.lang.Integer" />		
		<result property="chargeType" column="chargeType" javaType="java.lang.Integer" />		
		<result property="amount" column="amount" javaType="java.lang.Long" />
		<result property="actualUseAmount" column="actualUseAmount" javaType="java.lang.Long" />
		<result property="memberCount" column="memberCount" javaType="java.lang.Long" />
		<result property="actualUseMemberCount" column="actualUseMemberCount" javaType="java.lang.Long" />
		<result property="maxAmountPerPerson" column="maxAmountPerPerson" javaType="java.lang.Long" />
		<result property="effictiveTime" column="effictiveTime"
			javaType="java.util.Date" />
		<result property="expireTime" column="expireTime" javaType="java.util.Date" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
		<result property="lastUpdateTime" column="lastUpdateTime"
			javaType="java.util.Date" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="reserved5" column="reserved5" javaType="java.lang.String" />
		<result property="reserved6" column="reserved6" javaType="java.lang.String" />
		<result property="reserved7" column="reserved7" javaType="java.lang.String" />
		<result property="reserved8" column="reserved8" javaType="java.lang.String" />
		<result property="reserved9" column="reserved9" javaType="java.lang.String" />
		<result property="reserved10" column="reserved10" javaType="java.lang.String" />	
	</resultMap>



	<!--查询订购关系列表 -->
	<select id="querySubscribeList" resultMap="subscribeWrapper">
		select
		ID,
		enterpriseID,
		productID,
		orderItemID,
		orderID,
		status,
		productType,
		isExperience,
		servType,
		subServType,
		isLimit,
		chargeType,
		amount,
		actualUseAmount,
		memberCount,
		actualUseMemberCount,
		maxAmountPerPerson,
		effictiveTime,
		expireTime,
		createTime,
		operatorID,
		lastUpdateTime,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10
		from
		ecpe_t_subscribe
		<trim prefix="where" prefixOverrides="and|or">
		<if test="id!=null">
			and id = #{id}
		</if>
		<if test="enterpriseID!=null">
			and enterpriseID = #{enterpriseID}
		</if>
		<if test="productID!=null">
			and productID = #{productID}
		</if>
		<if test="orderItemID!=null and orderItemID!=''">
			and orderItemID = #{orderItemID}
		</if>
		<if test="orderID!=null and orderID!='' ">
			and orderID = #{orderID}
		</if>
		<if test="createTime!=null">
			and createTime = #{createTime}
		</if>
		<if test="effictiveTime!=null and effictiveTime!='' and expireTime!=null and expireTime!=''">
		    and ((effictiveTime <![CDATA[ >= ]]>
			#{effictiveTime} and effictiveTime <![CDATA[ <= ]]>
			#{expireTime})
			or (expireTime <![CDATA[ >= ]]>
			#{effictiveTime} and expireTime <![CDATA[ <= ]]>
			#{expireTime})
			or ((effictiveTime <![CDATA[ <= ]]>
			#{effictiveTime} and expireTime <![CDATA[ >= ]]>
				#{expireTime}))
			)
		</if>
        <if test="effictiveTime!=null and effictiveTime!='' and (expireTime ==null or expireTime =='')">
			 and expireTime <![CDATA[ >= ]]> #{effictiveTime}
	    </if>
		<if test="(effictiveTime==null or effictiveTime =='') and expireTime!=null and expireTime!=''">
			and effictiveTime <![CDATA[ <= ]]> #{expireTime}
		</if>			
		<if test="lastUpdateTime!=null and lastUpdateTime!='' ">
			and lastUpdateTime = #{lastUpdateTime}
		</if>
		<if test="status!=null">
			and status = #{status}
		</if>
		<if test="operatorID!=null">
			and operatorID = #{operatorID}
		</if>
		<if test="servType!=null">
			and servType = #{servType}
		</if>
		<if test="subServType!=null">
		       <if test="subServType==1 || subServType==2">
			         and (subServType = #{subServType} or  subServType =3)
			   </if>
			   <if test="subServType!=1 and subServType!=2">
			   	     and subServType = #{subServType} 			   
			   </if>			   		    
		</if>
		<if test="chargeType!=null">
			and chargeType = #{chargeType}
		</if>
		<if test="productType!=null">
			and productType = #{productType}
		</if>
		<if test="isExperience!=null">
			and isExperience = #{isExperience}
		</if>
		<if test="isLimit!=null">
			and isLimit = #{isLimit}
		</if>
		<if test="reserved1!=null  and reserved1!=''">
			and reserved1 = #{reserved1}
		</if>
		<if test="reserved2!=null  and reserved2!=''">
			and reserved2 = #{reserved2}
		</if>
		<if test="reserved3!=null  and reserved3!=''">
			and reserved3 = #{reserved3}
		</if>
		<if test="reserved4!=null  and reserved4!=''">
			and reserved4 = #{reserved4}
		</if>
		<if test="reserved5!=null  and reserved5!=''">
			and reserved5 = #{reserved5}
		</if>
		<if test="reserved6!=null  and reserved6!=''">
			and reserved6 = #{reserved6}
		</if>
		<if test="reserved7!=null  and reserved7!=''">
			and reserved7 = #{reserved7}
		</if>
		<if test="reserved8!=null  and reserved8!=''">
			and reserved8 = #{reserved8}
		</if>
		<if test="reserved9!=null  and reserved9!=''">
			and reserved9 = #{reserved9}
		</if>
		<if test="reserved10!=null  and reserved10!=''">
			and reserved10 = #{reserved10}
		</if>
		</trim>
		order by lastUpdateTime desc,id desc
	</select>

</mapper>