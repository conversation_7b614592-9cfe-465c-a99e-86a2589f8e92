/*
 * 头部功能组件
 *
 * choseindex: 默认选中的标签  最终展示的list中的第choseindex个  menu-index="2"
 * pageUrl: 页面路径
 * listIndex: 在menuTabList.json中取出applyList的第listIndex个元素 list-index="1"
 * */
angular.module("top.menu", ["util.ajax"]).directive("topMenu", function (RestClientUtil, $parse) {
  return {
    restrict: 'AE',
    templateUrl: '/qycy/ecpmp/directives/topMenu/topMenu.html',
//    templateUrl: '../../directives/topMenu/topMenu.html',
    scope: {
      choseindex: "@choseIndex",
      pageurl: "@pageUrl",
      listindex: "@listIndex",
      applyval:"@applyVal"
    },
    replace: true,
    transclude: true,
    controller: function ($scope, $element, $attrs, $rootScope) {

      $scope.goToUrl = function (url) {
        window.location.href = url;
      };
//      $.getJSON("../../assets/menuTabList.json", function (data) {
      
      $scope.init= function(){
    	  $.getJSON("/qycy/ecpmp/assets/menuTabList.json", function (data) {
    	        $rootScope.$apply(function () {
    	          $scope.list = [];
    	          var listTemp = data.tabList;
    	          console.log("applyVal", $scope.applyval);
    	          var applyTab = data.applyList[$scope.listindex];
    	          if ($scope.pageurl === applyTab.pageUrl) {
    	            var value = $scope.applyval?JSON.parse($scope.applyval):applyTab.value;
    	            for (var i = 0; i < value.length; i++) {
    	              var index = value[i];
    	              if (($scope.listindex === "54" || $scope.listindex === "52") && i == 1) {
    	                listTemp[index].url = listTemp[index].url + "?isSecondEnter=true"
    	              }
    	              $scope.list.push(listTemp[index]);
    	            }
    	          }
    	          console.log("list", $scope.list);
    	        })

    	      }) 
      }
      $scope.init()
      $scope.$watch('applyval',function(newValue,oldValue){
    	  console.log(newValue)
    	  if(newValue!=oldValue){
    		  $scope.init()
    	  }
      })

    }

  }
})