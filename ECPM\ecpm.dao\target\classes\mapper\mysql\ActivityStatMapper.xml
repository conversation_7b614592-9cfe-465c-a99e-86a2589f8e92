<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ActivityStatMapper">
	<resultMap id="activityStatWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ActivityStatWrapper">
		<result property="activityID" column="ID" javaType="java.lang.Integer" />
		<result property="activityName" column="activityName" javaType="java.lang.String" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="personCount" column="personCount" javaType="java.lang.Integer" />
		<result property="screenCount" column="screenCount" javaType="java.lang.Integer" />
		<result property="endPhoneCount" column="endPhoneCount"
			javaType="java.lang.Integer" />
		<result property="effectiveTime" column="effectivetime"
			javaType="java.util.Date" />
		<result property="expireTime" column="expiretime" javaType="java.util.Date" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updatetime" javaType="java.util.Date" />
		<result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="rewardCount" column="rewardCount" javaType="java.lang.Integer" />
	</resultMap>


	<select id="queryActivityStat" resultMap="activityStatWrapper">
		select
		t.ID,t.activityName,s.personCount,s.screenCount,s.endPhoneCount,t.effectivetime,t.expiretime,t.enterpriseName,s.enterpriseID,t.reserved1,t.reserved3,s.rewardCount
		from ecpm_t_activity_stat s , ecpm_t_activity t
		where t.ID = s.activityID
		<if test="activityID != null">
			and t.ID = #{activityID}
		</if>
		<if test="activityName != null and activityName !=''">
			and lower(t.activityName) like #{activityName}
		</if>
		<if test="enterpriseID != null">
			and s.enterpriseID = #{enterpriseID}
		</if>
		<if test="authEnterpriseIDs !=null and authEnterpriseIDs.size>0">
			and s.enterpriseID in
			<foreach item="enterpriseID" index="index" collection="authEnterpriseIDs" open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		<if test="processStatus!=null and processStatus == 1">
			and t.effectivetime <![CDATA[ > ]]>
			#{now}
		</if>
		<if test="processStatus!=null and processStatus == 2">
			and t.effectivetime <![CDATA[ < ]]>
			#{now}
			and t.expiretime <![CDATA[ > ]]>
			#{now}
		</if>
		<if test="processStatus!=null and processStatus == 3">
			and t.expiretime <![CDATA[ < ]]>
			#{now}
		</if>
		<if test="enterpriseName != null and enterpriseName !=''">
			and lower(t.enterpriseName) like #{enterpriseName}
		</if>
		<if test="enterpriseType != null and enterpriseType !=''">
			and t.reserved2 = #{enterpriseType}
		</if>
		<if test="agentEnterpriseName != null and agentEnterpriseName !=''">
			and lower(t.reserved1) like #{agentEnterpriseName}
		</if>
		<if test="reserved3 != null and reserved3 !=''">
			and t.reserved3 = #{reserved3}
		</if>
		order by t.updateTime ,t.ID desc
		limit #{pageNum},#{pageSize}
	</select>

	<select id="queryActivityStatCount" resultType="java.lang.Integer">
		select
		count(1)
		from ecpm_t_activity_stat s , ecpm_t_activity t
		where t.ID = s.activityID
		<if test="activityID != null">
			and t.ID = #{activityID}
		</if>
		<if test="activityName != null and activityName !=''">
			and lower(t.activityName) like #{activityName}
		</if>
		<if test="enterpriseID != null">
			and s.enterpriseID = #{enterpriseID}
		</if>
		<if test="authEnterpriseIDs !=null and authEnterpriseIDs.size>0">
			and s.enterpriseID in
			<foreach item="enterpriseID" index="index" collection="authEnterpriseIDs" open="(" separator="," close=")">
				#{enterpriseID}
			</foreach>
		</if>
		<if test="processStatus!=null and processStatus == 1">
			and t.effectivetime <![CDATA[ > ]]>
			#{now}
		</if>
		<if test="processStatus!=null and processStatus == 2">
			and t.effectivetime <![CDATA[ < ]]>
			#{now}
			and t.expiretime <![CDATA[ > ]]>
			#{now}
		</if>
		<if test="processStatus!=null and processStatus == 3">
			and t.expiretime <![CDATA[ < ]]>
			#{now}
		</if>
		<if test="enterpriseName != null and enterpriseName !=''">
			and lower(t.enterpriseName) like #{enterpriseName}
		</if>
		<if test="enterpriseType != null and enterpriseType !=''">
			and t.reserved2 = #{enterpriseType}
		</if>
		<if test="agentEnterpriseName != null and agentEnterpriseName !=''">
			and lower(t.reserved1) like #{agentEnterpriseName}
		</if>
		<if test="reserved3 != null and reserved3 !=''">
			and t.reserved3 = #{reserved3}
		</if>
		order by t.updateTime ,t.ID desc
	</select>

	<insert id="insertActivityStat">
		insert into ecpm_t_activity_stat
		(
		enterpriseID,
		activityID,
		personCount,
		screenCount,
		endPhoneCount,
		createTime,
		updatetime
		)
		values
		(
		#{enterpriseID},
		#{activityID},
		#{personCount},
		#{screenCount},
		#{endPhoneCount},
		#{now},
		#{now}
		)
	</insert>

	<select id="queryActivityStatExist" resultType="java.lang.Integer">
		select count(1) from ecpm_t_activity_stat where activityID = #{activityID}
		and enterpriseID = #{enterpriseID}
	</select>

	<update id="updateActivityStat">
		update ecpm_t_activity_stat set
		<trim suffixOverrides=","
			suffix="where activityID = #{activityID} and enterpriseID = #{enterpriseID}">
			<if test="screenCount!=null">
				screenCount= screenCount + #{screenCount},
			</if>
			<if test="endPhoneCount!=null">
				endPhoneCount= endPhoneCount+ #{endPhoneCount},
			</if>
			<if test="now != null">
				updatetime= #{now},
			</if>
		</trim>
	</update>

	<select id="queryActivityStatByID" resultMap="activityStatWrapper">
		select screenCount,endPhoneCount from ecpm_t_activity_stat where activityID
		= #{activityID} and enterpriseID = #{enterpriseID}
	</select>
	
	<update id="batchUpdateActivityStatReward" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index"
			item="activityStatWrapper" open="" separator=";">
			update ecpm_t_activity_stat set
			rewardCount = rewardCount + 1,
			updateTime= #{activityStatWrapper.now}
			where activityID =#{activityStatWrapper.activityID}
		</foreach>
	</update>
	
	<update id="updateActivityStatReward">
		update ecpm_t_activity_stat set  personCount = (case 
					when IFNULL(personCount, 0)= 0
					then  1
					else  personCount+1
					end ) where enterpriseID = #{enterpriseID} and activityID = #{activityID};
	</update>

</mapper>