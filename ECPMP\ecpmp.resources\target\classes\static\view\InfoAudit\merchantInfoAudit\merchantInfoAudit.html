<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">
	<title>查看活动</title>

	<link href="../../../css/bootstrap.min.css" rel="stylesheet"/>
	<link href="../../../css/mian.css" rel="stylesheet"/>
	<link href="../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../css/searchList.css" rel="stylesheet"/>

	<script type="text/javascript" src="../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/angular.min.js"></script>

	<script type="text/javascript" src="../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
					src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="merchantInfoAudit.js"></script>
	
    <link rel="stylesheet" type="text/css" href="../../../directives/preview/preview.css" />

	<!-- 引入分页组件 -->
	<link rel="stylesheet" type="text/css" href="../../../directives/page/page.css"/>
	<link href="../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../directives/page/page.js"></script>
	<script type="text/javascript" src="../../../directives/topMenu/topMenu.js"></script>
</head>
<body ng-app="cyApp" ng-controller="infoAudit" class="body-min-width">
	<div ng-init="init();">

		<div class="cooperation-manage">
			<div class="cooperation-head"><span class="frist-tab" ng-bind="'MENU_INFOAUDIT'|translate" ></span>&nbsp;&gt;&nbsp;<span
							class="second-tab">商户信息审核</span></div>
			<div class="cooperation-search">
				<form class="form-horizontal">
					<div class="form-group">
						<label for="merchantName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"
									 style="white-space:nowrap" ng-bind="'QUERYORDERDETAIL_ENTERPRISENAME'|translate"></label>
						<div class="cond-div col-lg-2 col-md-3 col-sm-3 col-xs-3">
							<input type="text" id="merchantName" class="form-control" placeholder="{{'MERCHANT_PLEASEINPUTMERCHANTNAME'|translate}}" 
										 ng-model="merchantName">
						</div>
						<label for="exampleInputName2" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"
									 style="white-space:nowrap" ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></label>

						<div class="cond-div col-lg-2 col-md-3 col-sm-3 col-xs-3">
							<select class="form-control" name="status" ng-model="selectedStatus"
											ng-options="x.value as x.name for x in statusList">
								<option value="">不限</option>
							</select>
						</div>

						<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2">
							<button type="submit" class="btn search-btn" ng-click="queryMerchantInfoList()" style="float: right">
								<icon class="search-iocn"></icon>
								{{"COMMON_SEARCH"|translate}}
							</button>
						</div>
					</div>
				</form>
			</div>

			<div class="coorPeration-table">
				<table class="table table-striped table-hover" >
					<thead>
					<tr>
						<th ng-bind="'QUERYORDERDETAIL_ENTERPRISENAME'|translate"></th>
						<th ng-bind="'MERCHANT_CONTRACT'|translate"></th>
						<th ng-bind="'MERCHANT_CONTRACTNUM'|translate"></th>
						<th ng-bind="'ENTERPRISE_BUSINESSLICENSE'|translate"></th>
						<th ng-bind="'MERCHANT_CARDPOSITIVE'|translate"></th>
						<th ng-bind="'MERCHANT_CARDOPPOSITE'|translate"></th>
						<th ng-bind="'CONTENTAUDIT_AUDITSTATE'|translate"></th>
						<th>审核原因</th>
						<th  style="width:20%" ng-bind="'COMMON_OPERATE'|translate"></th>
					</tr>
					</thead>
					<tbody>
					<tr ng-repeat="item in MerchantInfoList">
						<td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
						<td title="{{item.contract}}">{{item.contract}}</td>
						<td title="{{item.msisdn}}">{{item.msisdn}}</td>
						<td><a ng-click="item.businessLicenseURL?picturePop(item,'businessLicense'):false" title="{{item.businessLicenseName}}" ng-style="{{item.style1}}">查看图片</a></td>
						<td><a ng-click="item.idCardPositiveURL?picturePop(item,'idCardPositive'):false" title="{{item.idCardPositiveName}}" ng-style="{{item.style2}}">查看图片</a></td>
						<td><a ng-click="item.idCardOppositeURL?picturePop(item,'idCardOpposite'):false" title="{{item.idCardOppositeName}}" ng-style="{{item.style3}}">查看图片</a></td>
						<td title="{{statusMap[item.auditStatus]}}"><span class="ec_status">{{statusMap[item.auditStatus]}}</span></td>
						<td title="{{item.auditDesc}}">{{item.auditDesc}}</td>
						<td>
							<div class="handle" ng-show="item.auditStatus==1">
								<ul>
									<li ng-click="czs(item)" class="c_green">
										<icon class="past-icon"></icon>
										通过
									</li>
									<li ng-click="cz(item)" class="c_red">
										<icon class="reject-icon"></icon>
										驳回
									</li>
								</ul>
							</div>
						</td>
					</tr>

					<tr ng-show="MerchantInfoList.length<=0">
					    <td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
				    </tr>
					</tbody>
				</table>
			</div>
			
			<div>
				<ptl-page tableId="0" change="queryMerchantInfoList('justPage')"></ptl-page>
			</div>
		</div>

		<!--小弹出框-->
		<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog modal-sm" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
						<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
					</div>
					<div class="modal-body">
						<div class="text-center"><p style='font-size: 16px;color:#383838'>
							{{tip|translate}}
						</p></div>
					</div>
					<div class="modal-footer">
						<!-- <button type="submit" class="btn btn-primary search-btn">保存</button> -->
						<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
					</div>
				</div>
			</div>
		</div>
		
		
		
		<!--查看图片弹出框-->
		<div class="modal fade bs-example-modal-sm" id="picUrlListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
            <div class="modal-dialog modal-sm model-lg model-md" role="document" style="width:40%">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="myModalLabel">预览</h4>
                    </div>
                    <div class="modal-body">
                        <div class="img-wrap" >
                            <img  ng-src="{{pictureUrl}}" alt="加载失败">
                        </div>
                        <p ng-show="!pictureUrl">暂无图片，请先上传</p>
                    </div>
                    <div class="modal-footer" style="text-align:center">
                        <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_BACK'|translate"></button>
                    </div>
                </div>
            </div>
        </div>
		
		
		<!--审批弹出框-->
		<div class="modal fade" id="impoMebrPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
										aria-hidden="true">&times;</span></button>
						<h4 class="modal-title" id="myModalLabel">审核</h4>
					</div>

					<div class="modal-body">
						<form class="form-horizontal" name='myForm' novalidate>
							<div class="form-group">
								<label class="col-sm-2 control-label">审核原因:</label>

								<div class="cond-div col-sm-6">
									<input type="text" class="form-control" placeholder="请输入审核原因" name="checkDesc" ng-model="checkDesc"
												 ng-change="checkEmpty()" required/>
								</div>
								<div class="col-sm-3">
									<span id="info" style="color:red;padding-left: 55px" ng-show="isEmpty&&auditStatus==3&&!isFirstTime">
										<span class="">审核原因必填</span>
									</span>
								</div>
							</div>
						</form>
					</div>
					<div class="modal-footer">
						<button class="btn bg_purple" ng-click="checkMerchantInfo()" type="submit"
										ng-disabled="(isFirstTime||isEmpty)&&auditStatus==3" ng-bind="'COMMON_OK'|translate">
						</button>
						<button class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_CANCLE'|translate"></button>
					</div>
				</div>
			</div>
		</div>


	</div>

</body>
</html>