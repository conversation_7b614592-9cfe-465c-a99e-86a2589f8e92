<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MHWhiteCollectMapper">
    <resultMap id="MHWhiteWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MHWhiteWrapper">
        <result property="whitePhoneId" column="whitePhoneId"/>
        <result property="commandId" column="commandId"/>
        <result property="whiteType" column="whiteType"/>
        <result property="whiteContent" column="whiteContent"/>
        <result property="province" column="province"/>
		<result property="startTime" column="startTime"/>
		<result property="endTime" column="endTime" />
		<result property="status" column="status"/>
		<result property="objectType" column="objectType"/>
		<result property="createTime" column="createTime"/>
		<result property="updateTime" column="updateTime" />
		<result property="checkFailReason" column="checkFailReason" />
	</resultMap>


	<insert id="insertMHWhiteCollect">
		INSERT INTO ecpm_t_mhwhite_collect
		(
			whitePhoneId,
			addCommandId,
			whiteType,
			whiteContent,
			province,
			startTime,
			endTime
		)
		VALUES
		(
			#{whitePhoneId},
			#{commandId},
			#{whiteType},
			#{whiteContent},
			#{province},
			#{startTime},
			#{endTime}
		)
	</insert>


	<update id="updateMHWhiteCollect">
		UPDATE ecpm_t_mhwhite_collect  SET deleteCommandId = #{commandId}  WHERE whitePhoneId = #{whitePhoneId}
	</update>
</mapper>