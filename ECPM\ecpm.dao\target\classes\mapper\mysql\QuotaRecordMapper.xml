<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.QuotaRecordMapper">

	<resultMap id="quotaRecordWrapper"
		type="com.huawei.jaguar.dsdp.ecpm.dao.domain.QuotaRecordWrapper">
		<result property="id" column="id" javaType="java.lang.Long" />
		<result property="operateCode" column="operateCode" javaType="java.lang.String" />
		<result property="operateType" column="operateType" javaType="java.lang.Integer" />
		<result property="subscribeId" column="subscribeId" javaType="java.lang.Long" />
		<result property="useCount" column="useCount" javaType="java.lang.Long" />
		<result property="restoreCount" column="restoreCount" javaType="java.lang.Long" />
		<result property="extInfo" column="extInfo" javaType="java.lang.String" />
		<result property="reserved1" column="reserved1" javaType="java.lang.String" />
		<result property="reserved2" column="reserved2" javaType="java.lang.String" />
		<result property="reserved3" column="reserved3" javaType="java.lang.String" />
		<result property="reserved4" column="reserved4" javaType="java.lang.String" />
		<result property="createTime" column="createTime" javaType="java.util.Date" />
		<result property="updateTime" column="updateTime" javaType="java.util.Date" />
	</resultMap>

	<select id="queryQuotaRecord" resultMap="quotaRecordWrapper">
		SELECT
		t2.id, t2.operateCode,t2.operateType,t2.subscribeId,t2.useCount,t2.restoreCount,t2.createTime,t2.updateTime,
 		t2.reserved1,t2.reserved2,t2.reserved3, t2.reserved4
		FROM
		ecpm_t_quota_record t2
		WHERE
		t2.operateCode =#{operateCode}
		AND
		t2.useCount>t2.restoreCount
		and t2.operateType = 1
		ORDER BY t2.createTime DESC
		LIMIT 1
	</select> 

	<insert id="insertQuotaRecord">
		INSERT INTO ecpm_t_quota_record ( operateCode, operateType,
		subscribeId, useCount, restoreCount, extInfo, reserved1,reserved2,
		reserved3, reserved4, createTime, updateTime) VALUES
		(
		#{operateCode},
		#{operateType},
		#{subscribeId},
		#{useCount},
		#{restoreCount},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{createTime},
		#{updateTime}
		)
	</insert>
	
	<update id="updateQuotaRecord">
		update ecpm_t_quota_record
		set
		<if test="restoreCount != null">
			restoreCount=restoreCount + #{restoreCount},
		</if>
		<if test="updateTime != null">
			updateTime=#{updateTime}
		</if>
		where subscribeId = #{subscribeId} 
		and operateType=#{operateType} 
		and operateCode=#{operateCode}
	</update> 
	
	<select id="queryQuotaRecordByOperateCode" resultMap="quotaRecordWrapper">
		SELECT
		id,
		operateCode,
		operateType,
		subscribeId,
		useCount,
		restoreCount,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime
		from
		ecpm_t_quota_record where operateCode=#{operateCode} and useCount > restoreCount
	</select>
	
	<update id="updateQuotaRecordByID">
		update ecpm_t_quota_record set restoreCount = restoreCount + #{restoreCount}, updateTime = #{updateTime} where ID = #{id}
	</update>

</mapper>