
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>活动管理</title>
<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
<link href="../../../../css/reset.css" rel="stylesheet" />

<link href="../../../../css/searchList.css" rel="stylesheet"/>
<link href="../../../../css/activityManageList.css" rel="stylesheet" />
<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" 
	src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css" />
<script type="text/javascript" src="../../../../directives/page/page.js"></script>
<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
<link href="../../../../css/activityStaticList.css" rel="stylesheet" />
<script type="text/javascript" src="activityStatisticsListCtrl.js"></script>
</head>
<body ng-app='myApp' style="min-width:1024px;" ng-controller='actStatListController' ng-init="init();">
    <div class="cooperation-manage" >
    <div ng-show="businessStatus !=1">
        <div class="cooperation-head" ng-show="isSuperManager">
        	<span class="frist-tab" ng-bind="'COMMON_INFOSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;
        	<span class="second-tab" ng-bind="'COMMON_SECONDENTERPRISEACTSTATISTICS'|translate"></span>
        </div>
        <div class="cooperation-head" ng-show="isAgent">
        	<span class="frist-tab" ng-bind="'DATA_STATISTICS'|translate"></span>&nbsp;&gt;&nbsp;
        	<span class="second-tab" ng-bind="'ACTIVITY_STATISTICS'|translate"></span>
        </div>
		<top:menu chose-index="5" page-url="/qycy/ecpmp/view/InfoStatistics/agentStatistics" list-index="42"
				  ng-show="isSuperManager"></top:menu>
		<div class="cooperation-search">
			<form class="form-horizontal">
				<div class="form-group">
					<div ng-show="isSuperManager">
						<label for="agentEnterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" 
							ng-bind="'ENTERPRISE_AGENTNAME'|translate" style="white-space:nowrap"></label>

						<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" >
							<input type="text" id="enterpriseName" class="form-control" 
								placeholder="{{'ENTERPRISE_PLEASEINPUTAGENTNAME'|translate}}" ng-model="agentEnterpriseName">
						</div>

						<label for="enterpriseName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" 
							ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate" style="white-space:nowrap"></label>

						<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" >
							<input type="text" id="enterpriseName" class="form-control" 
								placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME'|translate}}" ng-model="enterpriseName">
						</div>
					
						<label for="activityName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"  
							ng-bind="'COMMON_ACTIVITYNAME'|translate" style="white-space:nowrap"></label>

						<div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" >
							<input type="text" id="activityName" class="form-control" 
								placeholder="{{'COMMON_PLEASEINPUTACTIVITYMANAGE'|translate}}" ng-model="activityName">
						</div>
					
						<label for="processStatus" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" 
							style="white-space:nowrap" ng-bind="'COMMON_STATUS'|translate"></label>
	                    <div class="cond-div col-lg-3 col-md-3 col-sm-3 col-xs-3" >
		                	<select id="processStatus" class="form-control" ng-model="processStatus" 
		                		ng-options="x.id as x.name for x in actStatusChoise" >
		                		<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
		                	</select>
	                    </div>
					</div>

					<div ng-show="isAgent">
						<label for="enterpriseName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label" 
							ng-bind="'ENTERPRISE_SUBENTERPRISENAME'|translate" style="white-space:nowrap"></label>

						<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" >
							<input type="text" id="enterpriseName" class="form-control" 
								placeholder="{{'ENTERPRISE_PLEASEINPUTSUBENTERPRISENAME'|translate}}" ng-model="enterpriseName">
						</div>
					
						<label for="activityName" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"  
							ng-bind="'COMMON_ACTIVITYNAME'|translate" style="white-space:nowrap"></label>

						<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" >
							<input type="text" id="activityName" class="form-control" 
								placeholder="{{'COMMON_PLEASEINPUTACTIVITYMANAGE'|translate}}" ng-model="activityName">
						</div>
						
						<label for="activityID" class="col-lg-1 col-md-2 col-sm-2 col-xs-2 control-label"  
							ng-bind="'ACTIVITY_ID2'|translate" style="white-space:nowrap"></label>

						<div class="cond-div col-lg-2 col-md-4 col-sm-4 col-xs-4" >
							<input type="text" id="activityID" class="form-control" 
								placeholder="{{'ACTIVITY_PLEASEINPUTACTIVITYID'|translate}}" ng-model="activityID">
						</div>
					</div>
					
					<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2" ng-show="isSuperManager">
						<button type="submit" class="btn search-btn" ng-click="queryactStatList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
					<div class="cond-div col-lg-1 col-md-2 col-sm-2 col-xs-2" ng-show="isAgent">
						<button type="submit" class="btn search-btn" ng-click="queryactStatList()" style="float: right">
							<icon class="search-iocn"></icon>
							<span ng-bind="'COMMON_SEARCH'|translate"></span>
						</button>
					</div>
				</div>
			</form>
		</div>
        <div class="add-table">
            <button id="exportactivityList" type="submit" class="btn add-btn" ng-click="exportFile()">
            	<icon class="export-icon"></icon>
            <span ng-bind="'COMMON_EXPORT'|translate"></span>
            </button>
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'COMMON_SECONDENTERPRISEACTSTATISTICS2'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover" style="table-layout:fixed">
                <thead>
                    <tr>
                    	<th style="width: 11%;" ng-bind="'ENTERPRISE_AGENTID'|translate" ng-show="isSuperManager"></th>
                        <th style="width: 11%;" ng-bind="'ENTERPRISE_AGENTNAME'|translate" ng-show="isSuperManager"></th>
                        <th style="width: 11%;" ng-bind="'ENTERPRISE_SUBENTERPRISEID'|translate"></th>
                        <th style="width: 10%;" ng-bind="'ENTERPRISE_ENTERPRISENAME2'|translate"></th>
                        <!--<th style="width: 9%;" ng-bind="'ACTIVITY_ID'|translate"></th>-->
                        <th style="width: 9%;" ng-bind="'COMMON_ACTIVITYNAME'|translate"></th>
                        <th style="width: 9%;" ng-bind="'ACTIVITY_STATUS'|translate"></th>
                        <th style="width: 11%;" ng-bind="'ACTIVITY_EXPIRYDATE'|translate"></th>
                        <th style="width: 11%;" ng-bind="'ACTIVITY_SPOKEMANTOTALNUM'|translate"></th>
                        <th style="width: 14%;" ng-bind="'ACTIVITY_PXTOTALNUM'|translate"></th>
                        <th style="width: 13%;" ng-bind="'ACTIVITY_AWARD_TOTALNUM'|translate"></th>
                        <th style="width: 13%;" ng-bind="'ACTIVITY_GJNUM'|translate"></th>
                        <th ng-bind="'COMMON_OPERATE'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in activityStatList">
                    	<td title="{{item.agentEnterpriseID}}" ng-show="isSuperManager">{{item.agentEnterpriseID}}</td>
                        <td title="{{item.agentEnterpriseName}}" ng-show="isSuperManager">{{item.agentEnterpriseName}}</td>
                        <td title="{{item.enterpriseID}}">{{item.enterpriseID}}</td>
                        <td title="{{item.enterpriseName}}">{{item.enterpriseName}}</td>
                        <!--<td title="{{item.activityID}}">{{item.activityID}}</td>-->
                        <td title="{{item.activityName}}">{{item.activityName}}</td>
                        <td title="{{formatStatus(item.startDate,item.endDate)}}">
                        	{{formatStatus(item.startDate,item.endDate)}}
                        </td>
                        <td title="{{formatDate(item.startDate,item.endDate)}}">
                        	{{formatDate(item.startDate,item.endDate)}}
                        </td>
                        <td title="{{item.spokeCount}}">{{item.spokeCount}}</td>
                        <td title="{{item.screenCount}}">{{item.screenCount}}</td>
                        <td title="{{item.rewardCount}}">{{item.rewardCount}}</td>
                        <td title="{{item.endPhoneCount}}">{{item.endPhoneCount}}</td>
                        <td>
                            <div class="handle">
                                <ul>
                                    <li class="query" ng-click="gotoDetail(item)">
                                        <icon class="query-icon"></icon><span ng-bind="'COMMON_WATCHDETAIL'|translate"></span></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-if="activityStatList.length<=0 && isSuperManager">
                        <td style="text-align:center" colspan="13" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                    <tr ng-if="activityStatList.length<=0 && isAgent">
                        <td style="text-align:center" colspan="11" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    <div>
        <ptl-page tableId="0" change="queryactStatList('justPage')"></ptl-page>
      </div>
    </div>
    </div>

	<!--查询详情弹框-->
	<div class="modal fade" id="querySpokesmanList" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" style="overflow-x: auto;">
		<div role="document" class="modal-dialog" style="width: 1024px;">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
									aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_ACTIVITYSEARCH'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="form-horizontal">
						<div class="form-group">
							<div class="row">
								<label class="col-lg-3 col-xs-3 col-sm-3 col-md-3 control-label" style="padding-top:7px"
									ng-bind="'SPOKES_MSISDN2'|translate"></label>
								<div class="col-lg-5 col-xs-6 col-sm-6 col-md-5">
									<input autocomplete="off"  type="text" class="form-control" id=""
										placeholder="{{'SPOKES_PLEASEINPUTMSISDN2'|translate}}" ng-model="initSel.msisdn">
								</div>
								<div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
									<button class="btn bg_purple search-btn btn1" ng-click="querySpokesmanList()">
										<span class="icon btnIcon search"></span>
										<span ng-bind="'COMMON_SEARCH'|translate"></span>
									</button>
								</div>
							</div>
						</div>
					</div>

					<div class="add-table" style="margin-left:22px;margin-top:12px;">
						<button type="submit" class="btn add-btn" ng-click="ensureToSend()">
				        	<icon class="add-iocn"></icon>
				        	<span ng-bind="'SPOKES_SENDNOTIFY'|translate"></span>
				        </button>
					</div>

					<div class="coorPeration-table" style="max-height: 530px;overflow: auto">
						<table class="table table-striped table-hover">
							<thead>
			                    <tr>
			                    	<th ng-bind="'COMMON_ACTIVITYNAME'|translate"></th>
			                        <th ng-bind="'SPOKES_MSISDN2'|translate"></th>
			                        <th ng-bind="'ACTIVITY_AREA'|translate"></th>
			                        <th ng-bind="'SPOKES_SPOKESTIME'|translate"></th>
			                        <th ng-bind="'SPOKES_SPOKESDAYS'|translate"></th>
			                        <th ng-bind="'SPOKES_SPOKESSCREENCOUNT'|translate"></th>
			                        <th ng-bind="'SPOKES_SPOKESENDPHONECOUNT'|translate"></th>
			                    </tr>
			                </thead>
			                <tbody>
			                    <tr ng-repeat="item in spokesmanList">
			                    	<td title="{{selectedItem.activityName}}">{{selectedItem.activityName}}</td>
			                        <td title="{{item.msisdn}}">{{item.msisdn}}</td>
			                        <td title="{{formatRegion(item.province,item.city)}}">{{formatRegion(item.province,item.city)}}</td>
			                        <td title="{{formatDateSpokes(item.startTime,item.endTime)}}">{{formatDateSpokes(item.startTime,item.endTime)}}</td>
			                        <td title="{{item.dayCount}}">{{item.dayCount}}</td>
			                        <td title="{{item.screenCount}}">{{item.screenCount}}</td>
			                        <td title="{{item.endPhoneCount}}">{{item.endPhoneCount}}</td>
			                    </tr>
			                    <tr ng-show="spokesmanList.length<=0">
			                        <td style="text-align:center" colspan="7" ng-bind="'COMMON_NODATA'|translate"></td>
			                    </tr>
			                </tbody>
						</table>
					</div>
					<div>
						<ptl-page tableId="1" change="querySpokesmanList('justPage')"></ptl-page>
					</div>
				</div>
				<div class="modal-footer">
				</div>
			</div>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="ensureToSend" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center">
						<p style='font-size: 16px;color:#383838'>{{tip|translate}}</p>
					</div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn " data-dismiss="modal" 
						aria-label="Close" ng-click="sendWinningNotify()" ng-bind="'COMMON_OK'|translate">
					</button>
					<button type="submit" class="btn " data-dismiss="modal" 
						aria-label="Close" ng-bind="'COMMON_CANCLE'|translate">
					</button>
				</div>
			</div>
		</div>
	</div>
	
	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{tip|translate}}</p>
					</div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate">
					</button>
				</div>
			</div>
		</div>
	</div>
		<!-- 业务未开通 -->
	<div ng-show="businessStatus ==1" class="modal fade bs-example-modal-sm" id="Modalisaengt" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" >
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center" style="text-align: center;">
						<p style='font-size: 18px;color:#383838'>
							业务未开通
						</p>
					</div>
				</div>
				<div class="modal-footer">
					<button type="submit" class="btn btn-primary search-btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
	
    
</body>
</html>