<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
	namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdpartyDeliveryMapper">
	<resultMap id="DeliveryModel"
		type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdpartyDeliveryWrapper">
		<result property="id" column="ID" />
		<result property="taskID" column="taskID" />
		<result property="target" column="target" />
		<result property="templateID" column="templateID" />
		<result property="argv" column="argv" />
		<result property="callIdentifier" column="callIdentifier" />
		<result property="scalling" column="scalling" />
		<result property="called" column="called" />
		<result property="direction" column="direction" />
		<result property="event" column="event" />
		<result property="timeStamp" column="timeStamp" />
		<result property="transactionID" column="transactionID" />
		<result property="sentResult" column="sentResult" />
		<result property="deliveryResult" column="deliveryResult" />
		<result property="ussdSentResult" column="ussdSentResult" />
		<result property="ussdDeliveryResult" column="ussdDeliveryResult" />
		<result property="insertTime" column="insertTime" />
		<result property="updateTime" column="updateTime" />
		<result property="notifyUrl" column="notifyurl" />
		<result property="msgType" column="msgtype" />
		<result property="src" column="src" />

		<result property="ussdPushTime" column="ussdPushTime" />
		<result property="pushTime" column="pushTime" />
		<result property="ussdReviceNotifyTime" column="ussdReviceNotifyTime" />
		<result property="reviceNotifyTime" column="reviceNotifyTime" />
		<result property="subscribeID" column="subscribeID" />
		<result property="orderID" column="orderID"/>
		<result property="ruleID" column="ruleID"/>
		<result property="notifyCount" column="notifyCount"/>
		<result property="deliveryChannel" column="deliveryChannel"/>
		<result property="platform" column="platform"/>
		<result property="msg" column="msg"/>
		<result property="chargeNum" column="chargeNum"/>
		<result property="timeoutDate" column="timeoutDate"/>
		<result property="parentTaskID" column="parentTaskID"/>
		<result property="processStatus" column="processStatus"/>
		<result property="telA" column="telA"/>
		<result property="provider" column="provider"/>


	</resultMap>

	<sql id="thirdpartyDeliveryCol">
		ID,
		taskID,
		target,
		templateID,
		argv,
		callIdentifier,
		scalling,
		called,
		direction,
		event,
		timeStamp,
		transactionID,
		sentResult,
		deliveryResult,
		ussdSentResult,
		ussdDeliveryResult,
		insertTime,
		updateTime,
		ussdPushTime,
		pushTime,
		ussdReviceNotifyTime,
		reviceNotifyTime,
		ruleID,
		deliveryChannel,
		platform
	</sql>

	<sql id="thirdpartyDeliveryCol2">
		ID,
		taskID,
		target,
		templateID,
		argv,
		callIdentifier,
		scalling,
		called,
		direction,
		event,
		timeStamp,
		transactionID,
		sentResult,
		deliveryResult,
		ussdSentResult,
		ussdDeliveryResult,
		insertTime,
		updateTime,
		ussdPushTime,
		pushTime,
		ussdReviceNotifyTime,
		reviceNotifyTime,
		ruleID,
		deliveryChannel,
		timeoutDate,
		platform,
		chargeNum,
		parentTaskID,
		telA
	</sql>

	<select id="getNextDeliveryID" resultType="java.lang.Long">
		select next value for MYCATSEQ_ECPE_T_THIRDPARTY_DELIVERY
	</select>
	
	<insert id="batchInsertThirdpartyDelivery">
		insert into ecpe_t_thirdparty_delivery
		(
			<include refid="thirdpartyDeliveryCol2"/>
		) 
		values
		<foreach collection="list" item="deliveryWrapper"
            separator=",">
		(
		#{deliveryWrapper.id},
		#{deliveryWrapper.taskID},
		#{deliveryWrapper.target},
		#{deliveryWrapper.templateID},
		#{deliveryWrapper.argv},
		#{deliveryWrapper.callIdentifier},
		#{deliveryWrapper.scalling},
		#{deliveryWrapper.called},
		#{deliveryWrapper.direction},
		#{deliveryWrapper.event},
		#{deliveryWrapper.timeStamp},
		#{deliveryWrapper.transactionID},
		#{deliveryWrapper.sentResult},
		#{deliveryWrapper.deliveryResult},
		#{deliveryWrapper.ussdSentResult},
		#{deliveryWrapper.ussdDeliveryResult},
		#{deliveryWrapper.insertTime},
		#{deliveryWrapper.updateTime},
		#{deliveryWrapper.ussdPushTime},
		#{deliveryWrapper.pushTime},
		#{deliveryWrapper.ussdReviceNotifyTime},
		#{deliveryWrapper.reviceNotifyTime},
		#{deliveryWrapper.ruleID},
		#{deliveryWrapper.deliveryChannel},
		#{deliveryWrapper.timeoutDate},
		#{deliveryWrapper.platform},
		#{deliveryWrapper.chargeNum},
		#{deliveryWrapper.parentTaskID},
		#{deliveryWrapper.telA}
		)
		</foreach>
	</insert>
	
	<update id="updateDeliverySendResult">
		update ecpe_t_thirdparty_delivery 
		set 
		<trim suffixOverrides=",">
			<if test="transactionID !=null">
				transactionID=#{transactionID},
			</if>
			<if test="transactionID == ''">
				transactionID = NULL,
			</if>
			<if test="sentResult !=null">
				sentResult=#{sentResult},
			</if>
			<if test="ussdSentResult !=null">
				ussdSentResult=#{ussdSentResult},
			</if>
			<if test="updateTime !=null">
				updateTime=#{updateTime},
			</if>
			<if test="ussdPushTime !=null">
				ussdPushTime=#{ussdPushTime},
			</if>
			<if test="pushTime !=null">
				pushTime=#{pushTime},
			</if>
			<if test="ussdReviceNotifyTime !=null">
				ussdReviceNotifyTime=#{ussdReviceNotifyTime},
			</if>
			<if test="reviceNotifyTime !=null">
				reviceNotifyTime=#{reviceNotifyTime},
			</if>
			<if test="deliveryResult !=null and deliveryResult!=''">
				deliveryResult = #{deliveryResult},
			</if>
			<if test="ussdDeliveryResult !=null and ussdDeliveryResult!=''">
				ussdDeliveryResult = #{ussdDeliveryResult},
			</if>
			<if test="ruleID !=null">
				ruleID = #{ruleID},
			</if>
			<if test="templateID !=null">
				templateID = #{templateID},
			</if>
			<if test="reply!=null">
				reply = #{reply},
			</if>
			<if test="deliveryChannel !=null and deliveryChannel!=''">
				deliveryChannel = #{deliveryChannel},
			</if>
			<if test="provider !=null">
            	provider = #{provider},
            </if>
		</trim>
		where ID=#{id}
		<if test="insertTime !=null">
		     and insertTime = #{insertTime}
		</if>
	</update>
	
	<update id="updateDeliveryByID">
		UPDATE ecpe_t_thirdparty_delivery SET
		<trim suffixOverrides="," suffix="where transactionID = #{transactionID}">
			<if test="deliveryResult !=null and deliveryResult!=''">
				deliveryResult = #{deliveryResult},
			</if>
			<if test="ussdDeliveryResult !=null and ussdDeliveryResult!=''">
				ussdDeliveryResult = #{ussdDeliveryResult},
			</if>
			<if test="sentResult !=null and sentResult!=''">
				sentResult = #{sentResult},
			</if>
			<if test="updateTime !=null">
            	updateTime = #{updateTime},
            </if>
            <if test="ussdReviceNotifyTime !=null">
            	ussdReviceNotifyTime = #{ussdReviceNotifyTime},
            </if>
			<if test="reviceNotifyTime !=null">
            	reviceNotifyTime = #{reviceNotifyTime},
            </if>
		</trim>
	</update>
	
	<select id="queryDeliveryByID" resultMap="DeliveryModel">
		select ID,
		taskID,
		target,
		templateID,
		argv,
		callIdentifier,
		scalling,
		called,
		direction,
		event,
		timeStamp,
		transactionID,
		sentResult,
		deliveryResult,
		ussdSentResult,
		ussdDeliveryResult,
		insertTime,
		updateTime,
		ussdPushTime,
		pushTime,
		ussdReviceNotifyTime,
		reviceNotifyTime,
		subscribeID,
		orderID,
		ruleID,
		deliveryChannel,
		platform,
		chargeNum,
		timeoutDate,
		parentTaskID,
		reply,
		telA
		  from ecpe_t_thirdparty_delivery
		 where transactionID = #{transactionID}
	</select>

	
	<select id="queryDeliveryForSchedule" resultMap="DeliveryModel">
		(select t1.ID,
		t1.taskID,
		t1.target,
		t1.templateID,
		t1.argv,
		t1.callIdentifier,
		t1.scalling,
		t1.called,
		t1.direction,
		t1.event,
		t1.timeStamp,
		t1.sentResult,
		t1.deliveryResult,
		t1.ussdSentResult,
		t1.ussdDeliveryResult,
		t1.insertTime,
		t1.updateTime,
		t1.ussdPushTime,
		t1.pushTime,
		t1.ussdReviceNotifyTime,
		t1.reviceNotifyTime,
		t1.subscribeID,
		t1.orderID,
		t1.ruleID,
		t1.transactionID,
		t.notifyurl,
		t.msgtype,
		t.src,
		t.notifyCount,
		t.enterpriseID,
		t1.msg,
		t1.chargeNum,
		t1.parentTaskID,
		t1.reply,
		t.processStatus,
		t1.telA,
		t1.provider,
		t1.platform
		FROM ecpe_t_thirdparty_delivery partition(p#{partition}) t1,
		ecpe_t_thirdparty_delivery_task partition(p#{partition}) t
		WHERE t1.taskID = t.ID
		and t.processStatus=0		
		and t.notifyStatus=0	
		and  now() <![CDATA[ >= ]]> t1.timeoutDate
		LIMIT #{limitQueryNum})

		UNION ALL
		(select t1.ID,
		t1.taskID,
		t1.target,
		t1.templateID,
		t1.argv,
		t1.callIdentifier,
		t1.scalling,
		t1.called,
		t1.direction,
		t1.event,
		t1.timeStamp,
		t1.sentResult,
		t1.deliveryResult,
		t1.ussdSentResult,
		t1.ussdDeliveryResult,
		t1.insertTime,
		t1.updateTime,
		t1.ussdPushTime,
		t1.pushTime,
		t1.ussdReviceNotifyTime,
		t1.reviceNotifyTime,
		t1.subscribeID,
		t1.orderID,
		t1.ruleID,
		t1.transactionID,
		t.notifyurl,
		t.msgtype,
		t.src,
		t.notifyCount,
		t.enterpriseID,
		t1.msg,
		t1.chargeNum,
		t1.parentTaskID,
		t1.reply,
		t.processStatus,
		t1.telA,
		t1.provider,
		t1.platform
		FROM ecpe_t_thirdparty_delivery partition(p#{partition}) t1,
		ecpe_t_thirdparty_delivery_task partition(p#{partition}) t
		WHERE t1.taskID = t.ID
		and t.processStatus=3		
		and t.notifyCount <![CDATA[  < ]]>#{notifyCount}
		LIMIT #{limitQueryNum})
		
		UNION ALL
		(select t1.ID,
		t1.taskID,
		t1.target,
		t1.templateID,
		t1.argv,
		t1.callIdentifier,
		t1.scalling,
		t1.called,
		t1.direction,
		t1.event,
		t1.timeStamp,
		t1.sentResult,
		t1.deliveryResult,
		t1.ussdSentResult,
		t1.ussdDeliveryResult,
		t1.insertTime,
		t1.updateTime,
		t1.ussdPushTime,
		t1.pushTime,
		t1.ussdReviceNotifyTime,
		t1.reviceNotifyTime,
		t1.subscribeID,
		t1.orderID,
		t1.ruleID,
		t1.transactionID,
		t.notifyurl,
		t.msgtype,
		t.src,
		t.notifyCount,
		t.enterpriseID,
		t1.msg,
		t1.chargeNum,
		t1.parentTaskID,
		t1.reply,
		t.processStatus,
		t1.telA,
		t1.provider,
		t1.platform
		FROM ecpe_t_thirdparty_delivery partition(p#{partition}) t1,
		ecpe_t_thirdparty_delivery_task partition(p#{partition}) t
		WHERE t1.taskID = t.ID
		and t.processStatus=1
		and t.notifyCount <![CDATA[  < ]]>#{notifyCount}	
		and t.updateTime <![CDATA[ < ]]>#{queryxSecond}
		LIMIT #{limitQueryNum})
		order by insertTime,notifyCount
		LIMIT #{limitQueryNum}
	</select>

	<update id="updateSubscribeIdAndOrderId" parameterType="java.util.List">
		<foreach close=";" collection="list" index="index" item="delivery"
			open="" separator=";">
			update ecpe_t_thirdparty_delivery
			<trim prefix="set" suffixOverrides=",">
					subscribeID=#{delivery.subscribeID},
					orderID=#{delivery.orderID}
			</trim>
			where ID=#{delivery.id} 
			<if test="delivery.insertTime !=null">
				and insertTime = #{delivery.insertTime}
			</if>
		</foreach>
	</update>
	
	<select id="queryDeliveryOverstockData" resultMap="DeliveryModel">
		select t1.ID,
		t.ID AS taskID,
		t1.target,
		t1.templateID,
		t1.argv,
		t1.callIdentifier,
		t1.scalling,
		t1.called,
		t1.direction,
		t1.event,
		t1.timeStamp,
		t1.sentResult,
		t1.deliveryResult,
		t1.ussdSentResult,
		t1.ussdDeliveryResult,
		t1.insertTime,
		t1.updateTime,
		t1.ussdPushTime,
		t1.pushTime,
		t1.ussdReviceNotifyTime,
		t1.reviceNotifyTime,
		t1.subscribeID,
		t1.orderID,
		t1.ruleID,
		t1.transactionID,
		t.notifyurl,
		t.msgtype,
		t.src,
		t.notifyCount,
		t.enterpriseID
		FROM ecpe_t_thirdparty_delivery_task_history t LEFT join
		ecpe_t_thirdparty_delivery_history t1
		on t1.taskID = t.ID
		LIMIT #{limitQueryNum}
	</select>
	
	<delete id="deleteThirdpartyDeliveryHistoryByID" parameterType="java.lang.Long">
	    delete from ecpe_t_thirdparty_delivery_history where ID=#{ID}
	</delete>

	<select id="selectThirdpartyDeliveryHistoryByID" parameterType="java.lang.Long" resultType="java.lang.Integer">
		select msgtype from ecpe_t_thirdparty_delivery_task_history  where ID=#{ID}
	</select>

	<delete id="batchDeleteThirdpartyDeliveryHistoryByIDs" parameterType="java.util.List">
		delete from ecpe_t_thirdparty_delivery_history where ID in
		<foreach item="id" index="index" collection="list"
				 open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>