var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n"])
//自定义filter,格式化日期
app.filter('newDate', function () {
    return function (date) {
        var new_date = date.substr(0, 4) + "-" + date.substr(4, 2) + "-" + date.substr(6.2);
        return new_date;
    }
});
app.controller('EnterpriselistCtrl', function ($scope, $rootScope, $filter, $location, RestClientUtil) {
    //初始化参数
    $scope.init = function () {
        $scope.id = $location.search().id || '';
        $.cookie("enterpriseType", 5, {path: '/'});
        $scope.selectedProvince = null;
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,//总页数
                "totalCount": 0,// 总条数
                "pageSize": "10",//每页展示个数
                "currentPage": 1// 当前页码
            }
        ];
        //搜索时初始化参数
        $scope.initSel = {
            enterpriseName: "",//企业名称
            organizationID: "",//企业机构代码
            provinceName: "0"//归属地
        };
        $scope.isZyzq = "";
        $scope.isZyzqChoise = [
            {
                id: 0,
                name: "分省"
            },
            {
                id: 1,
                name: "集客"
            },
            {
                id: 2,
                name: "移动云PAAS"
            },
            {
                id: 3,
                name: "咪咕音乐"
            },
        ];
        $scope.servTypeChoise = [
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            }
        ];
        $scope.channelSelectMap = [];
        $scope.enterpriseTypeList = localStorage.getItem("enterpriseTypeList")
        if ($scope.enterpriseTypeList != null) {
            angular.forEach(JSON.parse($scope.enterpriseTypeList), function (item) {
                if ("113" === item.fieldVal) {
                    $scope.channelSelectMap.push({"id": 3, "name": "咪咕音乐"});
                }
                if ("112" === item.fieldVal) {
                    $scope.channelSelectMap.push({"id": 2, "name": "移动云PAAS"});
                }
                if ("111" === item.fieldVal) {
                    $scope.channelSelectMap.push({"id": 1, "name": "集客"});
                }
                if ("0" === item.fieldVal) {
                    $scope.channelSelectMap.push({"id": 0, "name": "省份"});
                }
            });
        }
        $scope.isZyzqChoise = $scope.channelSelectMap;
        $scope.isZyzqChoise.forEach(function (value, index, array) {
            if (value.id === 0) {
                $scope.servTypeChoise.push({
                    id: 5,
                    name: "热线彩印省份版"
                });
            }
        })

        var loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = (loginRoleType == 'superrManager');
        $scope.isNormalMangager = (loginRoleType == 'normalMangager');
        $scope.isProvincial = (loginRoleType == 'provincial');
        $scope.queryProvinceAndCity();
        $scope.enterpriseList();
        $scope.showCountyProIds = ["10"];
        //$scope.queryAllCountyList();
    };
    $scope.formatDate = function (str) {
        if (!str) {
            return 'format error';
        }
        var newDateStr = "";
        newDateStr = str.substring(0, 4) + "-" + str.substring(4, 6) + "-" + str.substring(6, 8) + " " + str.substring(8, 10) + ":" + str.substring(10, 12);
        return newDateStr;
    }
    $scope.dateTimeFormate = function (date) {
        if (!date) {
            return
        } else {
            var obj = {};
            var d = new Date(date);
            obj.year = d.getFullYear();
            obj.month = ('0' + (d.getMonth() + 1)).slice(-2);
            obj.day = ('0' + (d.getDate())).slice(-2);
            obj.hour = ('0' + (d.getHours())).slice(-2);
            obj.minutes = ('0' + (d.getMinutes())).slice(-2);
            obj.seconds = ('0' + (d.getSeconds())).slice(-2);
            return obj
        }
    }

    /*获取服务器时间*/
    $scope.getServerTime = function () {
        var gmt_time = $.ajax({async: false, cache: false}).getResponseHeader("Date");
        var local_time = new Date(gmt_time);
        return $scope.dateTimeFormate(local_time);
    }
    $scope.querySyncServiceRule = function ($scope) {
        var serverTime = $scope.getServerTime();
        $scope.nowTime = "" + serverTime.year + serverTime.month + serverTime.day + serverTime.hour + serverTime.minutes + serverTime.seconds;
        //下拉框(投递方式)
        $scope.subServTypeChoise = [];
        var req = {
            "enterpriseID": parseInt($scope.id)
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/querySyncServiceRule",
            async: false,
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    console.log(result)
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.subServerTypeList = result.syncServiceRuleList || [];
                        var serverTypeMap = new Map();
                        // 业务设置页面是否显示名片异网投递配置
                        var showMpPlatformConfig = false;
                        // 业务设置页面是否显示热线异网投递配置
                        var showRxPlatformConfig = false;
                        for (var i in $scope.subServerTypeList) {
                            var item = $scope.subServerTypeList[i];
                            //开通或暂停状态才能投递
                            if ((item.status == 1 || item.status == 3) && $scope.nowTime <= item.expiryTime && $scope.nowTime >= item.effectiveTime) {
                                if (item.servType == 1) {
                                    serverTypeMap.set("mp", "23");
                                    showMpPlatformConfig = true;
                                } else if (item.servType == 5) {
                                    serverTypeMap.set("rxsf", "40");
                                    showRxPlatformConfig = true;
                                } else if (item.servType == 2) {
                                    //中移政企额外判断开关reserve4
                                    if ($scope.reserved10 == "111"
                                        && ($scope.serviceControl == null || $scope.serviceControl.reserved4 == null || $scope.serviceControl.reserved4.indexOf("2") == -1)) {
                                        continue;
                                    }

                                    serverTypeMap.set("rx", "30,49")
                                }
                            }
                        }

                        $.cookie("showMpPlatformConfig", showMpPlatformConfig, {path: '/'});
                        $.cookie("showRxPlatformConfig", showRxPlatformConfig, {path: '/'});
                        var serverTypeString = "[22, 24, 7";
                        if (serverTypeMap.get("mp")) {
                            serverTypeString = serverTypeString + "," + serverTypeMap.get("mp");
                        }
                        if (serverTypeMap.get("rxsf")) {
                            serverTypeString = serverTypeString + "," + serverTypeMap.get("rxsf");
                        }
                        if (serverTypeMap.get("rx")) {
                            serverTypeString = serverTypeString + "," + serverTypeMap.get("rx");
                        }
                        serverTypeString = serverTypeString + "]";

                        if ($scope.reserved10 === "111") {
                            serverTypeString = serverTypeString
                                .replaceAll("40", "76")
                                .replaceAll(",30", "")
                                .replaceAll(",49", "");
                        }
                        $scope.proSupServerType = serverTypeString;
                        $.cookie("proSupServerType", serverTypeString, {path: '/'});
                        $.cookie("enterpriseID", $scope.id, {path: '/'});
                        $.cookie("enterpriseName", $scope.enterpriseName, {path: '/'});
                        if ($scope.goToType == "toMingpian") {
                            location.href = '../../../zhikeManage/groupManage/groupList/groupList.html?enterpriseType=provincial';
                        } else if ($scope.goToType == "toHotLine") {
                            if ($scope.reserved10 === "111") {
                                if ($scope.isSuperManager){
                                    $.cookie("loginRoleType", "superrManager", {path: '/'});
                                } else if ($scope.isNormalMangager){
                                    $.cookie("loginRoleType", "normalMangager", {path: '/'});
                                } else {
                                    $.cookie("loginRoleType", "provincial", {path: '/'});
                                }
                                console.log("$.cookie('reserved4') = " + $.cookie('reserved4'));
                                $.cookie("reserved4", $.cookie('reserved4'), {path: '/'});
                                location.href = '../../../provincialManage/ProvincialColorPrintHotline/provinceGroupList/provinceGroupList.html?enterpriseType=provincial';
                            } else {
                                location.href = '../../../provincialManage/hotLineColorPrint/hotLineManage/HotlineManagement.html';
                            }
                        } else if ($scope.goToType == "toProvinceHotline") {
                            location.href = '../../../provincialManage/ProvincialColorPrintHotline/provinceGroupList/provinceGroupList.html?enterpriseType=provincial';
                        }
                    } else {
                        $scope.subServerTypeList = [];
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    }

    $scope.getCurrentDate = function () {
        var today = new Date();
        var year = today.getFullYear()+'';
        var month = today.getMonth() + 1;
        month = month < 10 ? '0'+month : month;
        var day = today.getDate() < 10 ? '0'+today.getDate() : today.getDate();
        var hours = today.getHours() < 10 ? '0'+today.getHours() : today.getHours();
        var mins = today.getMinutes() < 10 ? '0'+today.getMinutes() : today.getMinutes();
        var secs = today.getSeconds() < 10 ? '0'+today.getSeconds() : today.getSeconds();

        return year + month + day + hours + mins + secs;
    }
    $scope.queryOrderList = function () {
        var now = $scope.getCurrentDate();
        var req = {
            "subscribeInfo": {
                "enterpriseID":  $scope.id,
                // "servType": 1,
                "effictiveTime": now,
                "expireTime": now
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1024
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                if (data.result.resultCode == '1030100000') {
                    $scope.orderListData = data.subscribeInfoList || [].concat();
                    $scope.orderData = {"1_4": {},"1_5":{},"1_6":{},"2_4":{},"2_5":{},"2_6":{}}
                    for(let i = 0;i<$scope.orderListData.length;i++){
                        var o = $scope.orderListData[i];
                        if(o.status == null || o.status == 1){
                            if($scope.orderData[o.servType + "_" + o.productType]){
                                $scope.orderData[o.servType + "_" + o.productType][o.productID] =o.reservedsEcpmp.reserved8||1;
                            }
                        }
                    }
                    $scope.ydyMp = false;
                    for(let key in $scope.orderData["1_6"]){
                        if($scope.orderData["1_4"][$scope.orderData["1_6"][key]]){
                            $scope.ydyMp = true;
                            break;
                        }
                    }
                    $scope.ydyRx = false;
                    for(let key in $scope.orderData["2_6"]){
                        if($scope.orderData["2_4"][$scope.orderData["2_6"][key]]){
                            $scope.ydyRx = true;
                            break;
                        }
                    }
                    // if ($scope.isSuperManager)
                    // {
                        $.removeCookie('proSupServerType', { path: '/'});
                        $scope.querySyncServiceRule($scope);
                    // }
                } else {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                }

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });

    }

    $scope.querySyncServiceRuleList = function ($scope) {
        var req = {
            "enterpriseID": parseInt($scope.id)
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/contentService/queryServiceRuleList",
            data: JSON.stringify(req),
            async:false,
            success: function (result) {
                $scope.serviceControl = result.serviceControl || {};
                $.removeCookie('reserved4', { path: '/'});
                $.cookie('reserved4', $scope.serviceControl.reserved4,{ path: '/'});
            }
        })
    }

    //跳转至企业详情页面
    $scope.toDetail = function (item) {
        $.cookie("enterpriseID", item.id, {path: '/'});
        $.cookie("enterpriseName", item.enterpriseName, {path: '/'});
        location.href = '../createEnterprise/createEnterprise.html';
    }


    $scope.toMingpian = function (item) {
        $scope.id = item.id;
        $scope.enterpriseName = item.enterpriseName;
        $scope.reserved10 = item.reservedsEcpmp.reserved10;
        $scope.goToType = "toMingpian";
        if ($scope.reserved10 == "112"){
            $scope.queryOrderList()
        } else {
            $scope.querySyncServiceRule($scope);
        }
    }

    $scope.toHotLine = function (item) {
        $scope.id = item.id;
        $scope.enterpriseName = item.enterpriseName;
        $scope.reserved10 = item.reservedsEcpmp.reserved10;
        $scope.goToType = "toHotLine";
        $scope.querySyncServiceRuleList($scope);
        if ($scope.reserved10 == "112"){
            $scope.queryOrderList()
        } else {
            $scope.querySyncServiceRule($scope);
        }
    }

    $scope.toProvinceHotline = function (item) {
        $scope.id = item.id;
        $scope.enterpriseName = item.enterpriseName;
        $scope.reserved10 = item.reservedsEcpmp.reserved10;
        $scope.goToType = "toProvinceHotline";
        $scope.querySyncServiceRule($scope);
    }

    //912数据割接接口
    $scope.dataCutover = function () {
        RestClientUtil.ajaxRequest({
            type: 'GET',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/ecpmTContentCutover",
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1030100000') {
                        alert(result.result.resultDesc);
                    } else {
                        alert(result.result.resultDesc);
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }
    $scope.changeSelectedIsZyzq = function () {
        if ($scope.isZyzq === 1 || $scope.isZyzq === 2) {
            $scope.servTypeChoise = [
                {
                    id: 1,
                    name: "名片彩印"
                },
                {
                    id: 2,
                    name: "热线彩印"
                }
            ];
        } else {
            $scope.servTypeChoise = [
                {
                    id: 1,
                    name: "名片彩印"
                },
                {
                    id: 2,
                    name: "热线彩印"
                },
                {
                    id: 5,
                    name: "热线彩印省份版"
                },
            ];
        }
    }
    //获取queryEnterpriseList接口的数据
    $scope.enterpriseList = function (condition) {
        if (condition != 'justPage') {
            var req = {
                "enterpriseType": 5,
                "sortType": 2,
                "sortField": 1,
                "enterpriseName": $scope.enterpriseName,
                "enterpriseID": $scope.enterpriseID,
                "enterpriseCodeLike": $scope.enterpriseCodeLike,
                "provinceIDs": [],
                "cityIDs": [],
                "countyIDs": [],
                "pageParameter": {
                    "pageNum": 1,
                    "pageSize": $scope.pageInfo[0].pageSize,
                    "isReturnTotal": "1"
                }
            };
            if ($scope.isSuperManager || $scope.isNormalMangager) {
                if ($scope.isZyzq != null) {
                    req.isZyzq = $scope.isZyzq;
                    sessionStorage.setItem("isZyzq", JSON.stringify(req.isZyzq));
                }
            }
            if ($scope.isSuperManager) {
                if ($scope.selectedProvince == undefined) {
                    req.provinceIDs = [];
                } else {
                    req.provinceIDs = [$scope.selectedProvince.provinceID];
                }
                if ($scope.selectedCity == undefined) {
                    req.cityIds = [];
                } else {
                    req.cityIDs = [$scope.selectedCity.cityID];
                }
                if ($scope.selectedCounty == undefined) {
                    req.countyIDs = [];
                } else {
                    req.countyIDs = [$scope.selectedCounty.countyID];
                }
            }
            if ($scope.isProvincial || $scope.isNormalMangager) {
                var provinceIds = [];
                if ($scope.selectedProvince == undefined) {
                    // jQuery.each($scope.provinceList, function (i, e) {
                    //     provinceIds[i] = e.fieldVal;
                    // });
                } else {
                    provinceIds = [$scope.selectedProvince.fieldVal];
                }
                req.provinceIDs = provinceIds;
                if ($scope.selectedCity == undefined) {
                    // var cityIds = [];
                    // if ($scope.selectedProvince == undefined) {
                    //     jQuery.each($scope.cityList, function (i, e) {
                    //         cityIds[i] = e.fieldVal;
                    //     });
                    // }
                    // req.cityIDs = cityIds;
                } else {
                    req.cityIDs = [$scope.selectedCity.fieldVal];
                }
                if ($scope.selectedCounty == undefined) {
                    req.countyIDs = [];
                } else {
                    req.countyIDs = [$scope.selectedCounty.countyID];
                }
            }
            if ($scope.isSuperManager) {
                if ($scope.isZyzq == '' && $scope.enterpriseName == undefined && req.provinceIDs.length == 0 && req.cityIDs.length == 0 && req.countyIDs.length == 0) {
                    $scope.enterpriseName = "";
                } else {
                    sessionStorage.setItem("cacheEnterpriseName", $scope.enterpriseName == undefined ? "" : $scope.enterpriseName);
                    sessionStorage.setItem("cacheProvinceIDs", JSON.stringify(req.provinceIDs));
                    sessionStorage.setItem("cacheCityIDs", JSON.stringify(req.cityIDs));
                    sessionStorage.setItem("cacheCountyIDs", JSON.stringify(req.countyIDs));
                    sessionStorage.setItem("isZyzq", JSON.stringify(req.isZyzq));

                }
                if (sessionStorage.getItem("cacheEnterpriseName")) {
                    $scope.enterpriseName = sessionStorage.getItem("cacheEnterpriseName") || "";
                    req.enterpriseName = sessionStorage.getItem("cacheEnterpriseName") || "";
                } else {
                    $scope.enterpriseName = "";
                }

                if (sessionStorage.getItem("isZyzq")) {
                    $scope.isZyzq = parseInt(sessionStorage.getItem("isZyzq"));
                    req.isZyzq = parseInt(sessionStorage.getItem("isZyzq"));
                } else {
                    $scope.isZyzq = "";
                }

                if (sessionStorage.getItem("cacheProvinceIDs")) {
                    // req.provinceIDs = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
                } else {
                    req.provinceIDs = req.provinceIDs;
                }
                if (sessionStorage.getItem("cacheCityIDs")) {
                    // req.cityIDs = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
                } else {
                    req.cityIDs = req.cityIDs;
                }
                if (sessionStorage.getItem("cacheCountyIDs")) {
                    req.countyIDs = JSON.parse(sessionStorage.getItem("cacheCountyIDs"));
                } else {
                    req.countyIDs = req.countyIDs;
                }
            }

            if ($scope.isProvincial || $scope.isNormalMangager) {
                if ($scope.enterpriseName == undefined) {
                    $scope.enterpriseName = "";
                } else {
                    sessionStorage.setItem("cacheEnterpriseName", $scope.enterpriseName == undefined ? "" : $scope.enterpriseName);
                    sessionStorage.setItem("cacheProvinceIDs", JSON.stringify(req.provinceIDs));
                    sessionStorage.setItem("cacheCityIDs", JSON.stringify(req.cityIDs));
                    sessionStorage.setItem("cacheCountyIDs", JSON.stringify(req.countyIDs));
                }
                if (sessionStorage.getItem("cacheEnterpriseName")) {
                    $scope.enterpriseName = sessionStorage.getItem("cacheEnterpriseName") || "";
                    req.enterpriseName = sessionStorage.getItem("cacheEnterpriseName") || "";
                } else {
                    $scope.enterpriseName = "";
                }
                if (sessionStorage.getItem("cacheProvinceIDs")) {
                    // req.provinceIDs = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
                } else {
                    req.provinceIDs = req.provinceIDs;
                }
                if (sessionStorage.getItem("cacheCityIDs")) {
                    // req.cityIDs = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
                } else {
                    req.cityIDs = req.cityIDs;
                }
                if (sessionStorage.getItem("cacheCountyIDs")) {
                    req.countyIDs = JSON.parse(sessionStorage.getItem("cacheCountyIDs"));
                } else {
                    req.countyIDs = req.countyIDs;
                }
            }


            $scope.pageInfo[0].currentPage = 1;
            $scope.reqTemp = angular.copy(req);
        } else {
            //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.reqTemp;
            req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        req.servType = $scope.servType;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1030100000') {
                        $scope.queryEnterpriseList = result.enterpriseList;
                        $scope.queryEnterpriseList.forEach(function (value, index, array) {
                            //识别是否中移和移动云
                            if (value.reservedsEcpmp != null && (value.reservedsEcpmp.reserved10 === "111" || value.reservedsEcpmp.reserved10 === "112")) {
                                value.provinceHotlineMenu = 1;
                            }
                            if (value.servTypes) {
                                value.servTypes.forEach(function (servType) {
                                    if (servType === "1" && value.reservedsEcpmp != null && value.reservedsEcpmp.reserved10 === "111") {
                                        value.cardStatus = 1;
                                    }
                                    if (servType === "2" && value.reservedsEcpmp != null && value.reservedsEcpmp.reserved10 === "111") {
                                        value.hotlineStatus = 1;
                                    }
                                    if (servType === "1") {
                                        value.cardType = 1;
                                    }
                                    if (servType === "2") {
                                        value.hotlineType = 1;
                                    }
                                    if (servType === "5") {
                                        value.provinceHotlineType = 1;
                                    }
                                })
                            }
                        });
                        //获取页面的总条数与总页面
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        if ($scope.pageInfo[0].totalCount == 0) {
                            // $scope.pageInfo[0].totalPage=0;
                            $scope.pageInfo[0].currentPage = 1;
                            $scope.pageInfo[0].totalCount = 0;
                            $scope.pageInfo[0].totalPage = 1;
                        } else {
                            $scope.pageInfo[0].totalPage = Math.ceil(parseInt(result.totalNum) / parseInt($scope.pageInfo[0].pageSize));
                        }
                    } else {
                        $scope.queryEnterpriseList = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }

    //搜索省份改变时，找到对应的市
    $scope.changeSelectedProvince = function (selectedProvince) {
        $scope.subCityList = null;
        $scope.showCountyFlag = false;
        if (selectedProvince) {
            if ($scope.isProvincial || $scope.isNormalMangager) {
                $scope.subCityList = $scope.cityList.filter(function (a) {
                    return a.parentAuthID == selectedProvince.id;
                });
                if ($scope.showCountyProIds.includes(selectedProvince.fieldVal)) {
                    $scope.showCountyFlag = true;
                } else {
                    $scope.showCountyFlag = false;
                }
            }
            if ($scope.isSuperManager) {
                jQuery.each($scope.cityList, function (i, e) {
                    if (e.key == selectedProvince.provinceID) {
                        $scope.subCityList = e;
                    }
                    if ($scope.showCountyProIds.includes(selectedProvince.provinceID)) {
                        $scope.showCountyFlag = true;
                    } else {
                        $scope.showCountyFlag = false;
                    }
                });
            }
        }
        if (!selectedProvince) {
            $scope.subCityList = null;
        }
    }
    //搜索城市改变时，找到对应的区县
    $scope.changeSelectedCity = function (selectedCity) {
        $scope.subCountyList = null;
        if (selectedCity) {
            if ($scope.isSuperManager) {
                if ($scope.showCountyProIds.includes(selectedCity.provinceID)) {
                    $scope.setSubCountyList(selectedCity.cityID);
                }
            }
            if ($scope.isProvincial || $scope.isNormalMangager) {
                $scope.setSubCountyList(selectedCity.fieldVal);

            }
        }
        if (!selectedCity) {
            $scope.subCountyList = null;
        }
    }

    $scope.setSubCountyList = function (cityID) {
        var req = {
            cityID: cityID
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCountyList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if (result.result.resultCode == '1030100000') {
                        $scope.subCountyListBak = result.countyList;

                        //非超管，展示管理员有的区县数据权限
                        if ($scope.isProvincial || $scope.isNormalMangager) {
                            $scope.subCountyList = [];
                            jQuery.each($scope.cacheCountyList, function (i, e) {
                                for (var j = 0; j < $scope.subCountyListBak.length; j++) {
                                    if ($scope.subCountyListBak[j].countyID == e.fieldVal) {
                                        $scope.subCountyList.push($scope.subCountyListBak[j])
                                    }
                                }
                            });
                            if (sessionStorage.getItem("cacheCountyIDs") && sessionStorage.getItem("cacheCountyIDs") !== "null" && sessionStorage.getItem("cacheCountyIDs") !== "undefind") {
                                jQuery.each($scope.subCountyList, function (i, e) {
                                    var countyIDs = JSON.parse(sessionStorage.getItem("cacheCountyIDs"));
                                    if (e.countyID == countyIDs[0]) {
                                        $rootScope.$applyAsync(function () {
                                            $scope.selectedCounty = e;
                                            console.log("区县是：" + JSON.stringify($scope.selectedCounty))
                                        });
                                    }
                                });
                            }
                        }
                        if ($scope.isSuperManager) {
                            $scope.subCountyList = $scope.subCountyListBak
                            if (sessionStorage.getItem("cacheCountyIDs") && sessionStorage.getItem("cacheCountyIDs") !== "null" && sessionStorage.getItem("cacheCountyIDs") !== "undefind"
                                && sessionStorage.getItem("cacheAllcountyList") && sessionStorage.getItem("cacheAllcountyList") !== "null" && sessionStorage.getItem("cacheAllcountyList") !== "undefind") {
                                var cacheAllcountyList = JSON.parse(sessionStorage.getItem("cacheAllcountyList"));
                                jQuery.each($scope.subCountyList, function (i, e) {
                                    var countyIDs = JSON.parse(sessionStorage.getItem("cacheCountyIDs"));
                                    if (e.countyID == countyIDs[0]) {
                                        $rootScope.$applyAsync(function () {
                                            $scope.selectedCounty = e;
                                            console.log("区县是：" + JSON.stringify($scope.selectedCounty))
                                        });
                                    }
                                });
                            }
                        }

                    } else {
                        $scope.tip = result.result.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function (data) {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        })
    }

    //省市联动时调用，用于转化格式
    $scope.mapToList = function (map) {
        var result = [];
        jQuery.each(map, function (_, o) {
//        	if(_=='000'){
//      		  return;
//      	    }
            if (o) {
                o.key = o[0].provinceID;
                result.push(o);
            }
        });
        return result;
    };

    //
    $scope.queryAllCountyList = function () {

    }

    //省市联动方法
    $scope.queryProvinceAndCity = function () {
        $scope.provinceList = JSON.parse(localStorage.getItem("provinceList"));
        $scope.cityList = JSON.parse(localStorage.getItem("cityList"));
        $scope.cacheCountyList = JSON.parse(localStorage.getItem("countyList"));

        if ($scope.isSuperManager) {
            if (sessionStorage.getItem("cacheProvinceIDs") && sessionStorage.getItem("cacheProvinceIDs") !== "null" && sessionStorage.getItem("cacheProvinceIDs") !== "undefind") {
                jQuery.each($scope.provinceList, function (i, e) {
                    var provinceIDs = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
                    if ([e.provinceID] == provinceIDs[0]) {
                        $rootScope.$applyAsync(function () {
                            $scope.selectedProvince = e;
                            $scope.changeSelectedProvince(e);
                        });
                    }
                });
            }

            if (sessionStorage.getItem("cacheCityIDs") && sessionStorage.getItem("cacheCityIDs") !== "null" && sessionStorage.getItem("cacheCityIDs") !== "undefind") {
                jQuery.each($scope.cityList, function (i, e) {
                    jQuery.each(e, function (i, subCity) {
                        var cityIDs = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
                        if (subCity.cityID == cityIDs[0]) {
                            $rootScope.$applyAsync(function () {
                                $scope.selectedCity = subCity;
                                $scope.changeSelectedCity($scope.selectedCity);
                            });
                        }
                    });
                });
            }
            /*if (sessionStorage.getItem("cacheCountyIDs") && sessionStorage.getItem("cacheCountyIDs") !== "null" && sessionStorage.getItem("cacheCountyIDs") !== "undefind"
            &&sessionStorage.getItem("cacheAllcountyList") && sessionStorage.getItem("cacheAllcountyList") !== "null" && sessionStorage.getItem("cacheAllcountyList") !== "undefind")
            {
                var cacheAllcountyList = JSON.parse(sessionStorage.getItem("cacheAllcountyList"));
                jQuery.each(cacheAllcountyList,function(i,e){

                        var countyIDs = JSON.parse(sessionStorage.getItem("cacheCountyIDs"));
                        if(e.countyID == countyIDs[0]){
                            $rootScope.$applyAsync(function () {

                                $scope.selectedCounty = e;
                                console.log("区县是："+$scope.selectedCounty)
                            });
                        }

                });
            }*/
        }

        if ($scope.isProvincial || $scope.isNormalMangager) {
            if (sessionStorage.getItem("cacheProvinceIDs") && sessionStorage.getItem("cacheProvinceIDs") !== "null" && sessionStorage.getItem("cacheProvinceIDs") !== "undefind") {
                jQuery.each($scope.provinceList, function (i, e) {
                    var provinceList = JSON.parse(sessionStorage.getItem("cacheProvinceIDs"));
                    if (provinceList.length == 1) {
                        jQuery.each(provinceList, function (a, b) {
                            if (e.fieldVal == b) {
                                $rootScope.$applyAsync(function () {
                                    $scope.selectedProvince = e;
                                    $scope.changeSelectedProvince(e);
                                });
                            }
                        });
                    }

                });
            }

            if (sessionStorage.getItem("cacheCityIDs") && sessionStorage.getItem("cacheCityIDs") !== "null" && sessionStorage.getItem("cacheCityIDs") !== "undefind") {
                jQuery.each($scope.cityList, function (i, e) {
                    var cityList = JSON.parse(sessionStorage.getItem("cacheCityIDs"));
                    if (cityList.length == 1) {
                        jQuery.each(cityList, function (a, b) {
                            if (e.fieldVal == b) {
                                $rootScope.$applyAsync(function () {
                                    $scope.selectedCity = e;
                                    $scope.changeSelectedCity($scope.selectedCity);
                                });
                            }
                        });
                    }
                });
            }
        }


        $scope.provinceList2 = {};
        $scope.cityList2 = {};
        $scope.countyList2 = {}
        if ($scope.isProvincial || $scope.isNormalMangager) {
            jQuery.each($scope.provinceList, function (i, e) {
                $scope.provinceList2[e.fieldVal] = e.authName;
            });
            jQuery.each($scope.cityList, function (i, e) {
                $scope.cityList2[e.fieldVal] = e.authName;
            });
            jQuery.each($scope.cacheCountyList, function (i, e) {
                $scope.countyList2[e.fieldVal] = e.authName;
            });
        }
        if ($scope.isSuperManager) {
            jQuery.each($scope.provinceList, function (i, e) {
                $scope.provinceList2[e.provinceID] = e.provinceName;
            });
            $scope.cityList = $scope.mapToList($scope.cityList);
            for (var a = 0; a < $scope.cityList.length; a++) {
                jQuery.each($scope.cityList[a], function (i, e) {
                    $scope.cityList2[e.cityID] = e.cityName;
                });
            }
            var req = {
                cityID: ""
            };
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCountyList",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        if (result.result.resultCode == '1030100000') {
                            $scope.countyList3 = result.countyList;
                            jQuery.each($scope.countyList3, function (i, e) {
                                $scope.countyList2[e.countyID] = e.countyName;
                            });
                            sessionStorage.setItem("cacheAllcountyList", JSON.stringify($scope.countyList3));
                        } else {
                            $scope.tip = result.result.resultCode;
                            $('#myModal').modal();
                        }
                    })

                },
                error: function () {
                    $rootScope.$apply(function (data) {
                            $scope.tip = "1030120500";
                            $('#myModal').modal();
                        }
                    )
                }
            })

        }
    };
})
