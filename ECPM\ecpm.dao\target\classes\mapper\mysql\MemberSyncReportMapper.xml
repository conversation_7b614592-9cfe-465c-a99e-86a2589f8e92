<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.MemberSyncReportMapper">
	    <resultMap id="memberSyncReportWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MemberSyncReportWrapper">
        <result property="ID" column="ID" javaType="java.lang.Integer" />
        <result property="memberID" column="memberID" javaType="java.lang.Long" />
        <result property="operType" column="operType" javaType="java.lang.Integer" />
        <result property="notifyStatus" column="notifyStatus" javaType="java.lang.Integer" />
        <result property="confirmTime" column="confirmTime" javaType="java.util.Date" />
        <result property="confirmLog" column="confirmLog" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
		<result property="subscribeResult" column="subscribeResult" javaType="java.lang.String" />
		<result property="errDesc" column="errDesc" javaType="java.lang.String" />
		<result property="retryCount" column="retryCount" javaType="java.lang.Integer" />
		<result property="syncTime" column="syncTime" javaType="java.util.Date" />
		<result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
		<result property="msisdn" column="msisdn" javaType="java.lang.String" />
    </resultMap>
	
	<insert id="insert" >
		INSERT INTO ecpm_t_member_sync_report
		(
		memberID,
		operType,
		notifyStatus,
		confirmTime,
		confirmLog,
		msisdn,
		createTime,
		updateTime,
		subscribeResult,
		enterpriseID
		)
		VALUES
			(
			#{memberID},
			#{operType},
			#{notifyStatus},
			#{confirmTime},
			#{confirmLog},
			#{msisdn},
			now(),
			now(),
			#{subscribeResult},
			#{enterpriseID}
			)
	</insert>
	
	<select id="queryByMap" resultMap="memberSyncReportWrapper">
		select ID,memberID,
		operType,
		notifyStatus,
		confirmTime,
		confirmLog,
		createTime,
		updateTime
		from ecpm_t_member_sync_report
		<trim prefix="where" prefixOverrides="and|or">
			<if test="memberId != null">
				and memberId=#{memberId}
			</if>
			<if test="operType != null">
				and operType=#{operType}
			</if>
			<if test="notifyStatus != null">
				and notifyStatus=#{notifyStatus}
			</if>
		</trim>
	</select>
	
	<update id="updateByMap">
		update ecpm_t_member_sync_report set
		<if test="notifyStatus!=null">
			notifyStatus = #{notifyStatus},
		</if>
		<if test="subscribeResult!=null and subscribeResult!=''">
			subscribeResult= #{subscribeResult},
		</if>
		<if test="errDesc!=null and errDesc!=''">
			errDesc= #{errDesc},
		</if>
		<if test="syncTime!=null">
			syncTime= #{syncTime},
		</if>
		<if test="retryCount!=null">
			retryCount= #{retryCount},
		</if>
		updateTime = now()
		where ID = #{id}
	</update>

	<select id="selectByNotifyStatusAndSubscribeResult" resultMap="memberSyncReportWrapper">
		SELECT
			ID,
			memberID,
			operType,
			notifyStatus,
			confirmTime,
			confirmLog,
			createTime,
			updateTime,
			subscribeResult,
			errDesc,
			if(retryCount is not null,retryCount,0) retryCount,
			syncTime,
			enterpriseID,
			msisdn
		FROM
			ecpm_t_member_sync_report
		WHERE
			notifyStatus = 0
		  and subscribeResult is not null
	</select>
</mapper>