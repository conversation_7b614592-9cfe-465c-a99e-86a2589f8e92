<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>订单查询</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/createOrder.css" rel="stylesheet">
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="queryQuotaDetail.js"></script>
    <style>
        [ng-cloak] {
            display: none !important;
        }

        .check-li span {
            vertical-align: middle;
        }

        .form-horizontal .control-label {
            text-align: right;
            padding-top: 7px;
            margin-bottom: 0;
        }

    </style>
</head>

<div ng-app="myApp" ng-init="init();" ng-controller="quotaController" class="body-min-width">
    <div class="cooperation-manage">
        <div class="cooperation-head">
            <span class="frist-tab" ng-bind="'COMMON_SECONDENTERPRISEMANAGE'|translate" ng-show="isSuperManager"></span>
            <span class="frist-tab" ng-bind="'ENTERPRISE_SUBENTERPRISEMANAGE'|translate" ng-show="isAgent"></span>
            &nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'COMMON_QUOTAMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
            <span class="second-tab" ng-bind="'VIEW_QUOTA'|translate"></span>
        </div>

        <div class="cooper-title" ng-bind="'CREATEORDER_ORDERINFO'|translate"></div>

        <div class="cooper-tab">
            <form class="form-horizontal" name="orderBase" novalidate>
                <div class="form-group">
                    <div>
                        <!--企业名称-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"
                               ng-bind="'CREATEORDER_ENTERPRISENAME'|translate"></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3"><p ng-bind="subEnterpriseName"></p></div>

                        <!--业务类别-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_SERVTYPE'|translate"></span>
                        </label>

                        <div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
                            <span ng-show="orderInfo.servType===1" ng-bind="'CONTENTAUDIT_MINGPIAN'|translate"></span>
                            <span ng-show="orderInfo.servType===2" ng-bind="'CONTENTAUDIT_HOTLINE'|translate"></span>
                            <span ng-show="orderInfo.servType===3" ng-bind="'CONTENTAUDIT_ADVERTISE'|translate"></span>
                            <span ng-show="orderInfo.servType===4" ng-bind="'GROUP_SEND'|translate"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div>
                        <!--是否体验版-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_EXPERIENCE'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">
                            <span ng-show="orderInfo.isExperience===0" ng-bind="'NO'|translate"></span>
                            <span ng-show="orderInfo.isExperience===1" ng-bind="'YES'|translate"></span>
                        </div>

                        <!--订单名称-->
                        <label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">订单名称：</label>

                        <div class="col-lg-3 col-xs-4 col-sm-3 col-md-3">
                            <input type="text" ng-model="orderInfo.extInfo.quotaOrderName" class="form-control"
                                   disabled/>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <div>
                        <!--订单金额-->
                        <label for="amount" class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
                            <span ng-bind="'CREATEORDER_AMOUNT'|translate"></span></label>

                        <div class="col-lg-3 col-xs-3 col-sm-3 col-md-3">
                            <span ng-bind="orderInfo.amount"></span>
                            <span ng-bind="'COMMON_YUAN'|translate"></span>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <!-- 业务配额代码 -->
        <div class="cooper-title">
            <span class="red">*</span>
            <span ng-bind="'CREATEORDER_ORDERITEMLIST'|translate"></span>
        </div>

        <div class="cooper-tab">
            <form class="form-horizontal" name="orderItemDomain" novalidate>
                <div ng-show="cmcc">
                    <!--移动-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="check-li">
                            <span>移动：</span>
                        </li>
                    </div>
                    <!--屏显配额-->
                    <div class="form-group" ng-show="hasPX_cmcc">
                        <div class="col-lg-2 col-xs-3  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="orderInfo.servType != 3 && orderInfo.servType != 4"></span>
                            <span ng-show="orderInfo.servType == 4">屏显：</span>
                            <!--<span ng-bind="'ORDER_GG_QUOTA'|translate" ng-show="orderInfo.servType == 3"></span>：-->
                            <span ng-bind="'CREATEORDER_GUANGGAOANDPEIE'|translate" ng-show="orderInfo.servType == 3"></span>
                        </div>
                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isPXLimit&&orderInfo.servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isPXanci">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="pinxian_anci"></span>
                        </div>
                        <!--按人/包月-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isPXbaoyue&&orderInfo.servType != 3">
                            <li class="check-li">
                                <span class="redio-btn checked"></span>
                                <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                            </li>
                            <li class="check-li">
                                <span ng-bind="baoyue" style="margin-right: 0"></span>
                                <span ng-bind="'CREATEORDER_PRESON'|translate"></span>
                            </li>
                            <li class="check-li" style="margin-left: 20px">
                                <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                            </li>
                            <li class="check-li">
                                <span ng-bind="productName"></span>
                            </li>
                        </div>
                    </div>


                    <!--挂机短信-->
                    <div class="form-group" ng-if="hasGD" ng-show="orderInfo.servType != 3">
                        <div class="check-li col-xs-2 control-label">
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGDLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGDanci">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="guaduanAmount"></span>
                        </div>

                        <!--按人/包月-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGDbaoyue">
                            <li class="check-li">
                                <span class="redio-btn checked"></span>
                                <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                            </li>
                            <li class="check-li">
                                <span ng-bind="gdbaoyue" style="margin-right: 0"></span>
                                <span ng-bind="'CREATEORDER_PRESON'|translate"></span>
                            </li>
                            <li class="check-li" style="margin-left: 20px">
                                <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                            </li>
                            <li class="check-li">
                                <span ng-bind="gdproductName"></span>
                            </li>
                        </div>
                    </div>


                    <div class="form-group" ng-if="hasGC" ng-show="orderInfo.servType != 3">
                      <!--挂机彩信-->
                        <div class="check-li col-xs-2 control-label" ng-show="orderInfo.servType != 4&&orderInfo.servType != 3">
                            <span ng-bind="'ORDER_GJCXBYTIMES'|translate"></span><span>：</span>
                        </div>
                        <div class="check-li col-xs-2 control-label" ng-show="orderInfo.servType == 4">
                           	<span>彩信</span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGCLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGCanci">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="guacaiAmount"></span>
                        </div>

                        <!--按人/包月-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGCbaoyue">
                            <li class="check-li">
                                <span class="redio-btn checked"></span>
                                <span ng-bind="'CREATEORDER_ANREN'|translate"></span>
                            </li>
                            <li class="check-li">
                                <span ng-bind="gcbaoyue" style="margin-right: 0"></span>
                                <span ng-bind="'CREATEORDER_PRESON'|translate"></span>
                            </li>
                            <li class="check-li" style="margin-left: 20px">
                                <span ng-bind="'TAOCAN_CHOOSE'|translate"></span>
                            </li>
                            <li class="check-li">
                                <span ng-bind="gcproductName"></span>
                            </li>
                        </div>
                    </div>
                    <!--增彩群发-->
                    <div class="form-group" ng-if="hasZC" ng-show="orderInfo.servType == 4 || orderInfo.servType == 2">
                        <div class="check-li col-xs-2 control-label" ng-show="orderInfo.servType == 4">
                            <span ng-bind="'ZENGCAISERVICE'|translate"></span><span>：</span>
                        </div>
                        <div class="check-li col-xs-2 control-label" ng-show="orderInfo.servType == 2">
                            <span>挂机增彩</span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isZCLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isZCanci">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="ZCAmount"></span>
                        </div>

                    </div>

                    <!--短信群发-->
                    <div class="form-group" ng-if="hasGroupSendSMSCMCC" ng-show="orderInfo.servType == 4">
                        <div class="check-li col-xs-2 control-label">
                            <span >短信</span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="groupSendSMSCMCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="!groupSendSMSCMCCNoLimit">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="groupSendSMSCMCCAmount"></span>
                        </div>

                    </div>
                </div>
                <div ng-show="cucc">
                    <!--联通-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="check-li">
                            <span>联通：</span>
                        </li>
                    </div>
                    
                    <!--屏显配额-->
                    <div class="form-group" ng-show="hasPX_cucc">
                        <div class="col-lg-2 col-xs-3  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="orderInfo.servType != 4"></span>
                            <span ng-show="orderInfo.servType == 4">屏显：</span>
                        </div>
                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isPXLimit_cucc&&orderInfo.servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isPXanci_cucc">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="pinxian_anci_cucc"></span>
                        </div>
                    </div>
                    <!--短信群发-->
                    <div class="form-group" ng-if="hasGroupSendSMSCUCC" ng-show="orderInfo.servType == 4">
                        <div class="check-li col-xs-2 control-label">
                            <span >短信</span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="groupSendSMSCUCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="!groupSendSMSCUCCNoLimit">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="groupSendSMSCUCCAmount"></span>
                        </div>

                    </div>
                    <!--addby hyj 20191107 top-->
                    <!--挂机短信-->
                    <div class="form-group" ng-if="hasGDCUCC" ng-show="orderInfo.servType == 2">
                        <div class="check-li col-xs-2 control-label">
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGDLimitCUCC">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGDanciCUCC">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="guaduanAmountCUCC"></span>
                        </div>

                    </div>

                    <!--addby hyj 20191107 end-->
                </div>
                <div ng-show="ctcc">
                    <!--电信-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="check-li">
                            <span>电信：</span>
                        </li>
                    </div>
                    <!--屏显配额-->
                    <div class="form-group" ng-show="hasPX_ctcc">
                        <div class="col-lg-2 col-xs-3  col-sm-2 col-md-2 control-label">
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="orderInfo.servType != 4"></span>
                            <span ng-show="orderInfo.servType == 4">屏显：</span>
                        </div>
                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isPXLimit_ctcc&&orderInfo.servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isPXanci_ctcc">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="pinxian_anci_ctcc"></span>
                        </div>
                    </div>
                    <!--短信群发-->
                    <div class="form-group" ng-if="hasGroupSendSMSCTCC" ng-show="orderInfo.servType == 4">
                        <div class="check-li col-xs-2 control-label">
                            <span >短信</span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="groupSendSMSCTCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="!groupSendSMSCTCCNoLimit">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="groupSendSMSCTCCAmount"></span>
                        </div>

                    </div>

                    <!--addby hyj 20191107 top-->
                    <!--挂机短信-->
                    <div class="form-group" ng-if="hasGDCTCC" ng-show="orderInfo.servType == 2">
                        <div class="check-li col-xs-2 control-label">
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span><span>：</span>
                        </div>

                        <!--不限-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGDLimitCTCC">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                <span ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span>
                            </li>
                        </div>
                        <!--按次-->
                        <div class="col-lg-10 col-xs-9 col-sm-10 col-md-10" ng-show="isGDanciCTCC">
                            <li class="check-li">
                                <span class="redio-btn checked"> </span>
                                <span ng-bind="'CREATEORDER_ANCI'|translate"></span>
                            </li>
                            <span ng-bind="guaduanAmountCTCC"></span>
                        </div>

                    </div>

                    <!--addby hyj 20191107 end-->

                </div>

                <!--有效期-->
                <div class="form-group">
                    <div class="col-xs-2 control-label">
                        <span ng-bind="'CREATEORDER_TIME'|translate"></span>
                    </div>
                    <div class="input-daterange input-group col-xs-3" id="datepicker"
                         style="padding-left: 15px;padding-right: 15px;min-width: 415px">
                        <input class="input-md form-control" ng-value="orderInfo.effictiveTime|newdate:'ymd'" disabled/>
                        <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                        <input class="input-md form-control" ng-value="orderInfo.expireTime|newdate:'ymd'" disabled/>
                    </div>
                </div>
                <!--创建时间-->
                <div class="form-group">
                    <div class="col-xs-2 control-label">
                        <span ng-bind="'ITEM_EFFICTIVETIME'|translate"></span>：
                    </div>
                    <div class="col-xs-2">
                        <span ng-bind="orderInfo.createTime|newdate:'ymdhm'"></span>
                    </div>
                </div>

                <!--更新时间-->
                <div class="form-group">
                    <div class="col-xs-2 control-label">
                        <span ng-bind="'UPDATETIME'|translate"></span>：
                    </div>
                    <div class="col-xs-2">
                        <span ng-bind="orderInfo.lastUpdateTime|newdate:'ymdhm'"></span>
                    </div>
                </div>
                <!--创建账号-->
                <div class="form-group">
                    <div class="col-xs-2 control-label">
                        <span ng-bind="'CREATEACCOUNT'|translate"></span>：
                    </div>
                    <div class="col-xs-2">
                        <span ng-bind="orderInfo.extInfo.creatorID"></span>
                    </div>
                </div>
                <!--更新账号-->
                <div class="form-group">
                    <div class="col-xs-2 control-label">
                        <span ng-bind="'UPDATE_ACCOUNT'|translate"></span>：
                    </div>
                    <div class="col-xs-2">
                        <span ng-bind="orderInfo.extInfo.editorID"></span>
                    </div>
                </div>
            </form>
        </div>
        <div class="form-group">
            <div class="col-lg-3 col-xs-4  col-sm-4 col-md-4 col-lg-offset-2 col-xs-offset-2 col-sm-offset-2 col-md-offset-2"
                 style="padding-left: 30px;">
                <button type="submit" class="btn btn-back" ng-click="goBack()"
                        ng-bind="'COMMON_BACK'|translate"></button>
            </div>
        </div>
    </div>

    <!--小弹出框-->
    <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                </div>
                <div class="modal-body">
                    <div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
                    </p></div>
                </div>
                <div class="modal-footer" style="text-align:center">
                    <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                            ng-bind="'COMMON_OK'|translate"
                            ng-click="sure(eventType)"></button>
                </div>
            </div>
        </div>
    </div>

</div>

</html>