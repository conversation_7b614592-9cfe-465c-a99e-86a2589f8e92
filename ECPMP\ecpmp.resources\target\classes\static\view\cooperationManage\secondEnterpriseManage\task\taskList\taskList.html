<!DOCTYPE html>
<html>

<head>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge" charset="UTF-8">
	<title>任务管理</title>
	<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
	<link href="../../../../../css/reset.css" rel="stylesheet"/>
	<link href="../../../../../css/searchList.css" rel="stylesheet"/>
	<link href="../../../../../css/addContent.css" rel="stylesheet" />

	<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
	<script type="text/javascript"
			src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
	<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
	<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
	<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
	<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
	<script type="text/javascript" src="../../../../../frameworkJs/webuploader.js"></script>
	<script src="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
	<link href="../../../../../directives/cy-uploadifyfile/cy-uploadifyfile.css" rel="stylesheet"/>
	<script src="../../../../../directives/cy-uploadify/cy-uploadify.js"></script>
	<link href="../../../../../directives/cy-uploadify/cy-uploadify.css" rel="stylesheet" />
	<link href="../../../../../css/laydate.css" rel="stylesheet" type="text/css" />

	<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css"/>
	<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
	<script type="text/javascript" src="taskListCtrl.js"></script>
	<script type="text/javascript" src="../../../../../frameworkJs/laydate.js"></script>
	<style>
		.switch {
			display: table-cell;
		}
		.col-lg-2.col-xs-2.col-sm-2.col-md-2.control-label{
			padding-top: 5px;
		}

		.col-lg-4.col-xs-5.col-sm-5.col-md-5{

			padding-left: 0px;
		}
		.col-lg-4.col-xs-4.col-sm-4.col-md-4{
			padding-top: 7px;
			width: 39.333333%;
			padding-left: 0px;
		}
		.form-group {
			margin-bottom: 20px;
		}

		.form-group label {
			text-align: right;
		}

		.addMsinsdn{
			display: inline-block;
			width: 23px;
			height: 23px;
			background: url(../../../../../assets/images/add.png) no-repeat;
			vertical-align: middle;
			background-position: 0 0;
		}
		.deleteMsinsdn{
			display: inline-block;
			width: 23px;
			height: 23px;
			background: url(../../../../../assets/images/delete.png) no-repeat;
			vertical-align: middle;
			background-position: 0 0;
		}
		.switch{
			display: table-cell;
		}
		.datCursor{
			cursor: default !important;
		}
	</style>
</head>

<body ng-app="myApp" class="body-min-width">
<div class="cooperation-manage container-fluid" ng-controller="TaskCtrl" ng-init="init();" ng-cloak>
	<!-- 管理员登陆查看直客 -->
	<div  class="cooperation-head">
		<span class="frist-tab" >代理商管理 > 子企业管理 > 企业通知</span>
	</div>
	<top:menu chose-index="1" page-url="/qycy/ecpmp/view/cooperationManage/secondEnterpriseManage/task"
			  list-index="78"></top:menu>

	<div class="cooperation-search">
		<form class="form-horizontal">
			<div class="form-group">
				<label for="taskName" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
					   style="white-space:nowrap" ng-bind="'TASKNAME'|translate"></label>

				<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
					<input type="text" id="taskName" class="form-control"
						   placeholder="{{'INPUTTASKNAME'|translate}}"
						   ng-model="taskName">
				</div>

				<label for="status" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
					   style="white-space:nowrap" ng-bind="'GROUP_STATUS'|translate"></label>

				<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
					<select class="form-control" name="status" ng-model="status"
							ng-options="x.id as x.name for x in auditStatusChoise">
						<option value="" ng-bind="'COMMON_ALL'|translate"></option>
					</select>
				</div>

				<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
					<button type="submit" class="btn search-btn" ng-click="taskList()">
						<icon class="search-iocn"></icon>
						<span ng-bind="'COMMON_SEARCH'|translate"></span>
					</button>
				</div>
			</div>
		</form>
	</div>
	<div class="add-table">
		<button type="submit" class="btn add-btn" ng-click="toAdd()">
			<icon class="add-iocn"></icon>
			<span ng-bind="'ADDTASK'|translate"></span>
		</button>
		<button class="btn add-btn" ng-click="export()">
			<icon class="add-iocn"></icon>
			<span ng-bind="'DETAIL_EXPORT'|translate"></span>
		</button>
	</div>

	<div class="coorPeration-table">
		<table class="table table-striped table-hover">
			<thead>
			<tr>
				<th ng-bind="'TASKID'|translate"></th>
				<th ng-bind="'TASKNAME'|translate"></th>
				<th ng-bind="'CONTENTAUDIT_CONTENTNUM'|translate"></th>
				<th ng-bind="'CONTENTAUDIT_BUSINESSTYPE'|translate"></th>
				<th ng-bind="'TASKCREATETIME'|translate"></th>
				<th ng-bind="'PORT'|translate"></th>
				<th ng-bind="'GROUP_STATUS'|translate"></th>
				<th ng-bind="'TASKSUCCESSRATE'|translate"></th>
				<th style="width:18.5%" ng-bind="'COMMON_OPERATE'|translate"></th>
			</tr>
			</thead>
			<tbody>
			<tr ng-repeat="item in queryTaskList">
				<td><span title="{{item.objectID}}">{{item.objectID}}</span></td>
				<td><span title="{{item.enterpriseName}}">{{item.taskName}}</span></td>
				<td><span title="{{item.contentId}}">{{item.contentId}}</span></td>
				<td title="{{serviceTypeMap[item.serviceType]?serviceTypeMap[item.serviceType]:'－'}}">
					{{serviceTypeMap[item.serviceType]?serviceTypeMap[item.serviceType]:'－'}}</td>
				<td title="{{formatDate(item.createTime)}}">{{formatDate(item.createTime)}}</td>
				<td title="{{item.src}}">{{item.src}}</td>
				<td title="{{statusMap[item.status]?statusMap[item.status]:'－'}}">
					{{statusMap[item.status]?statusMap[item.status]:'－'}}</td>
				<td ng-if="item.status==4 || item.status==5">
						<span title="{{item.successNum+'/'+item.total}}">
							{{item.successNum+'/'+item.total}}
						</span>
				</td>
				<td ng-if="item.status !=4 && item.status !=5">
					<span title="－">－</span>
				</td>
				<td>
					<div class="handle">
						<ul>
							<li class="query" ng-click="toDetail(item)"
								ng-show="item.status==3 || item.status==4|| item.status==6
								 || item.status==7 || item.status==9">
								<icon class="query-icon"></icon>
								<span ng-bind="'COMMON_WATCH'|translate"></span>
							</li>
							<li class="edit" ng-click="toModify(item)" ng-show="item.status==2">
								<icon class="edit-icon"></icon>
								<span ng-bind="'GROUP_EDIT'|translate"></span>
							</li>
							<li class="edit" ng-click="popDelete(item)" ng-show="item.status==3">
								<icon class="delete-icon"></icon>
								<span>取消</span>
							</li>
							<li class="query" ng-click="popFailList(item)" ng-show="(item.status==4 && (item.successNum !=item.total)) || item.status==5">
								<icon class="query-icon"></icon>
								<span ng-bind="'TASKFAILLISL'|translate"></span>
							</li>
						</ul>
					</div>
				</td>
			</tr>
			<tr ng-show="queryTaskList.length<=0">
				<td style="text-align:center" colspan="7" ng-bind="'COMMON_NODATA'|translate"></td>
			</tr>
			</tbody>
		</table>
	</div>
	<div>
		<ptl-page tableId="0" change="taskList('justPage')"></ptl-page>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						{{tip|translate}}
					</p></div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate">
					</button>
				</div>
			</div>
		</div>
	</div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="deletePop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'CONFIRM_THEDELETION'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838'>
						请确认是否取消群发任务
					</p></div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-click="toDelete(deleteItem)"
							ng-bind="'COMMON_OK'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_CANCLE'|translate"></button>
				</div>
			</div>
		</div>
	</div>

	<div class="modal fade" id="failListPop" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div role="document" class="modal-dialog" style="width:1100px">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'TASKFAILLISL'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="cooperation-search">
						<form class="form-horizontal">
							<div class="form-group">
								<label for="newStatus" class="province col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label"
									   style="white-space:nowrap" ng-bind="'GROUP_DEAL_STATUS'|translate"></label>
				
								<div class="cond-div col-lg-2 col-md-2 col-sm-2 col-xs-2">
									<select class="form-control" name="newStatus" ng-model="newStatus"
											ng-options="x.id as x.name for x in newAuditStatusChoise">
										<option value="" ng-bind="'ENTERPRISE_NOLIMITED'|translate"></option>
									</select>
								</div>
				
								<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
									<button type="submit" class="btn search-btn" ng-click="queryFailList()">
										<span ng-bind="'COMMON_SEARCH'|translate"></span>
									</button>
								</div>
								<div class="cond-div col-lg-1 col-md-1 col-sm-1 col-xs-1">
									<button type="submit" class="btn search-btn" ng-click="exportFile()">
										<span ng-bind="'DETAIL_EXPORT'|translate"></span>
									</button>
								</div>
							</div>
						</form>
					</div>
					<div class="coorPeration-table" style="max-height: 530px;overflow: auto">
						<table class="table table-striped table-hover">
							<thead>
							<tr>
								<th ng-bind="'COMMON_NUMBER'|translate" style="width:140px;"></th>
								<th ng-bind="'GROUP_STATUS'|translate"></th>
								<th ng-bind="'TASKERRDESC'|translate"></th>
							</tr>
							</thead>
							<tbody>
							<tr ng-repeat="item in failList">
								<td><span title="{{item.msisdn}}">{{item.msisdn}}</span></td>
								<td title="{{newStatusMap[item.status]?newStatusMap[item.status]:'－'}}">
									{{newStatusMap[item.status]?newStatusMap[item.status]:'－'}}</td>
								<td><span title="{{item.errdesc}}">{{item.errdesc}}</span></td>
							</tr>
							<tr ng-show="failList.length<=0">
								<td style="text-align:center" colspan="2" ng-bind="'COMMON_NODATA'|translate"></td>
							</tr>
							</tbody>
						</table>
					</div>
					<div>
						<ptl-page tableId="1" change="queryFailList('justPage')"></ptl-page>
					</div>
				</div>
				<div class="modal-footer">
				</div>
			</div>
		</div>
	</div>



	<div class="modal fade" id="createTask" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div role="document" class="modal-dialog" style="width:732px">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">创建任务</h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal" name="myForm" novalidate>
						<div class="form-group">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon style="color:red">*</icon>
									<span ng-bind="'TASKNAME'|translate"></span>：
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
									<input class="form-control"
										   type="text" id="taskName2" name="taskName" ng-model="groupSendTaskInfo.taskName"
										   placeholder="{{'INPUTTASKNAME2'|translate}}"
										   ng-blur="checkTaskName(groupSendTaskInfo.taskName)"
										   ng-disabled="operate =='detail'"
										   ng-class="{'redBorder':!taskNameValidate}"
										   title={{groupSendTaskInfo.taskName}}
										   autocomplete="off">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
										 ng-show="!taskNameValidate">
									<span class="redFont" ng-show="!taskNameValidate">{{'TASKNAMEDESC'|translate}}</span>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon style="color:red">*</icon>
									<span >内容编号</span>：
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
									<input class="form-control"
										   type="number" id="contentId" name="contentId" ng-model="groupSendTaskInfo.contentId"
										   placeholder="请输入内容编号"
										   ng-blur="checkTaskContentId(groupSendTaskInfo.contentId)"
										   ng-disabled="operate =='detail'"
										   ng-class="{'redBorder':!taskNameValidate}"
										   title={{groupSendTaskInfo.taskName}}
										   autocomplete="off">
								</div>
								<div class="col-lg-3 col-xs-3  col-sm-3 col-md-3">
									<span class="redFont" ng-show="validate.contentId_null" style="color:red;">
										请输入内容编号</span>
									<span class="redFont" ng-show="validate.contentId_statue" style="color:red;">
										请输入内容审核状态为审核通过，状态为启用的的内容</span>
									<span class="redFont" ng-show="validate.contentId_scene" style="color:red;">
										内容应用场景不存在</span>
									<span class="redFont" ng-show="validate.isTempContent" style="color:red;">
										模板内容不允许新增任务</span>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="row">
								<label for="" class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
									<icon>*</icon>
									<span>发送类型</span>：
								</label>
								<div class="col-lg-6 col-xs-6  col-sm-6 col-md-6">
									<label style="width: 73px;margin-top: 7px;"><input class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="taskType" ng-model="groupSendTaskInfo.taskType" type="radio" value="1"><span>立即发送</span></label>
									<label style="width: 88px;margin-top: 7px;"><input class="check-btn checked-btn" style="margin-right: 2px;height: 11px;width: 15px;" name="taskType" ng-model="groupSendTaskInfo.taskType" type="radio" value="2"><span>定时发送</span></label>
								</div>
							</div>
						</div>


						<div class="form-group" ng-show="operate !='detail'">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
									<span ng-bind="'RECEIVED'|translate"></span>：
								</label>
								<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
									<icon ng-show="groupSendTaskInfo.groupSendTaskMsisdn.length==0" ng-click="addMsisdn()"
										  class="addMsinsdn">
									</icon>
									<div style="overflow:hidden;" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
										<input class="form-control" style="float:left;margin-bottom:11px"
											   type="text"  id="{{'msisdn'+$index}}" name="{{'msisdn'+$index}}" ng-model="item.msisdn"
											   placeholder="{{'ENTERPRISE_PLEASEINPUTRECEIVENUM'|translate}}"
											   ng-blur="checkMsisdnList(groupSendTaskInfo.groupSendTaskMsisdn)"
											   ng-disabled="operate =='detail'"
											   title={{item.msisdn}}
											   pattern="[0-9]*[0-9][0-9]*$"
											   ng-class="{'redBorder':!msisdnListValidate}">
										</input>
									</div>
								</div>
								<div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">
									<div style="overflow:hidden;padding:1px 0px 10px 0px" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
										<icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length<50" ng-click="addMsisdn()"
											  class="addMsinsdn">
										</icon>
										<icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length>=50" ng-click=""
											  class="addMsinsdn">
										</icon>
										<icon ng-click="deleteMsisdn($index)"
											  class="deleteMsinsdn">
										</icon>
									</div>
								</div>
								<div  class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="padding:1px 0px 10px 0px"
									  ng-show="(myForm['txtContent'+$index].$dirty && myForm['msisdn'+$index].$invalid) ||!msisdnListValidate"
									  ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">
									<!-- <span ng-show="myForm['msisdn'+$index].$error.pattern" ng-bind="'RECEIVEDDESC'|translate"></span> -->
									<span style="color:red;" class="redFont" ng-show="!msisdnListValidate">{{msisdnListDesc}}</span>
								</div>
							</div>
						</div>
						<div class="form-group" ng-show="operate !='detail'">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
									<span ng-bind="'COMMON_FILENAME'|translate"></span>：
								</label>
								<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">
									<input type="text" class="form-control" ng-model="fileNameExcel"
										   placeholder="请导入.xlsx,.xls格式文件" style="width: 100%;" ng-disabled="true"/>
								</div>
								<cy:uploadifyfile filelistid="fileListExcel" filepickerid="filePickerExcel" accepttype="accepttypeExcel"
												  uploadifyid="uploadifyidExcel" validate="isValidateExcel" filesize="filesizeExcel"
												  mimetypes="mimetypesExcel"
												  formdata="uploadParamExcel" uploadurl="uploadurlExcel" desc="uploadDescExcel" numlimit="numlimitExcel"
												  urllist="urlListExcel" createthumbnail="isCreateThumbnailExcel" auto="auto" fileUse="msidsnList"
												  style="margin-left: 15px;float: left;padding-top: 7px;">
								</cy:uploadifyfile>
								<div class="downloadRow" style="margin: 10px 0 0 29px;">
									<a target="_blank" href="/qycy/ecpmp/assets/importMsisdnTemplate.xlsx" class="downMod"
									   style="margin-right: 40px;"
									   ng-bind="'GROUP_IPTMBRDOWNLOD'|translate"></a>
									</a>
								</div>
							</div>
						</div>
						<div class="form-group" ng-show="contentScene">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
									<!--<icon style="color:red">*</icon>-->
									<span >端口号:</span>
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4" style="padding-top: 0px">
									<label style="	width: 300px;margin-right: 10px;margin-top: 6px;font-size: 16px; text-align: left;" ng-repeat="item in ports">
										<input class="" style="width: 20px;height:15px;margin-right: 5px;"
										   type="radio" ng-value="item" value="item" name="contentId" ng-model="groupSendTaskInfo.port"
												  autocomplete="off">{{item}}</label>
								</div>
							</div>
						</div>
						<div class="form-group" ng-show="groupSendTaskInfo.taskType != 1">
							<div class="row">
								<label class="col-lg-2 col-xs-2  col-sm-2 col-md-2 control-label">
									<span ng-bind="'SEND_TIME_DELAY'|translate"></span>：
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4 time" style="position: relative">
									<input readonly="true" ng-disabled="operate =='detail'"
										   id="datepicker"  autocomplete="off" type="text"  name="timingTime"
										   ng-focus="openDate()"
										   ng-model="groupSendTaskInfo.timingTimeTemp"  class="form-control" ng-class="{'datCursor':operate !='detail'}">
									<i class="glyphicon glyphicon-calendar fa fa-calendar"></i>
									<span style="color:red" ng-show="timeError">
								<img src="../../../../../assets/images/reject-icon.png" width="20" height="20"
									 align="absmiddle">
										<!--<span ng-show="myForm.time.$error.required"-->
										<!--ng-bind="'CONTENT_PUSHTIMEREQUIRE'|translate"></span>-->
								<span ng-show="timeError" ng-bind="'CONTENT_TIMEERRORTIP'|translate"></span>
							</span>
								</div>
							</div>
						</div>
					</form>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn btn-primary search-btn" data-dismiss="modal"  ng-click="createTask()"
							ng-disabled= "!groupSendTaskInfo.contentId||!groupSendTaskInfo.taskName||!taskNameValidate||validate.contentId_null||validate.contentId_statue||validate.contentId_scene||validate.isTempContent"
							ng-bind="'COMMON_SAVE'|translate"></button>
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_CANCLE'|translate"></button>
				</div>
			</div>
		</div>
	</div>


	<div class="modal fade" id="detailTask" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div role="document" class="modal-dialog" style="width:732px">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
							aria-hidden="true">&times;</span></button>
					<h4 class="modal-title">查看</h4>
				</div>
				<div class="modal-body">
					<form class="form-horizontal" name="myForm" novalidate>
						<div class="form-group">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon style="color:red">*</icon>
									<span ng-bind="'TASKNAME'|translate"></span>：
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
									<input class="form-control"
										   type="text" id="taskName2" name="taskName" ng-model="groupSendTaskInfo.taskName"
										   placeholder="{{'INPUTTASKNAME2'|translate}}"
										   ng-blur="checkTaskName(groupSendTaskInfo.taskName)"
										   ng-disabled="true"
										   ng-class="{'redBorder':!taskNameValidate}"
										   title={{groupSendTaskInfo.taskName}}
										   autocomplete="off">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
										 ng-show="!taskNameValidate">
									<span class="redFont" ng-show="!taskNameValidate">{{'TASKNAMEDESC'|translate}}</span>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon style="color:red">*</icon>
									<span >内容编号</span>：
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
									<input class="form-control"

										   type="text"  name="contentId" ng-model="groupSendTaskInfo.contentId"
										   placeholder="请输入内容编号"
										   ng-blur="checkTaskContentId(groupSendTaskInfo.contentId)"
										   ng-disabled="true"
										   ng-class="{'redBorder':!taskNameValidate}"
										   title={{groupSendTaskInfo.taskName}}
										   autocomplete="off">
								</div>
								<div class="col-lg-3 col-xs-3  col-sm-3 col-md-3">
									<span class="redFont" ng-show="validate.contentId_null" style="color:red;">
										请输入内容编号</span>
									<span class="redFont" ng-show="validate.contentId_statue" style="color:red;">
										请输入内容状态为审核通过的内容</span>
									<span class="redFont" ng-show="validate.contentId_scene" style="color:red;">
										内容应用场景不存在</span>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label"><icon style="color:red">*</icon>
									<span >状态:</span>：
								</label>
								<div class="col-lg-4 col-xs-4  col-sm-4 col-md-4">
									<input class="form-control"
										   type="text"  name="status" ng-model="groupSendTaskInfo.status"
										   placeholder="{{'INPUTTASKNAME2'|translate}}"
										   ng-blur="checkTaskName(groupSendTaskInfo.taskName)"
										   ng-disabled="true"
										   ng-class="{'redBorder':!taskNameValidate}"
										   title={{groupSendTaskInfo.taskName}}
										   autocomplete="off">
									<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle"
										 ng-show="!taskNameValidate">
									<span class="redFont" ng-show="!taskNameValidate">{{'TASKNAMEDESC'|translate}}</span>
								</div>
							</div>
						</div>

						<!--<div class="form-group" ng-show="operate !='detail'">-->
							<!--<div class="row">-->
								<!--<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">-->
									<!--<span ng-bind="'RECEIVED'|translate"></span>：-->
								<!--</label>-->
								<!--<div class="col-lg-4 col-xs-4 col-sm-4 col-md-4">-->
									<!--<icon ng-show="groupSendTaskInfo.groupSendTaskMsisdn.length==0" ng-click="addMsisdn()"-->
										  <!--class="addMsinsdn">-->
									<!--</icon>-->
									<!--<div style="overflow:hidden;" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">-->
										<!--<input class="form-control" style="float:left;margin-bottom:11px"-->
											   <!--type="text"  id="{{'msisdn'+$index}}" name="{{'msisdn'+$index}}" ng-model="item.msisdn"-->
											   <!--placeholder="{{'ENTERPRISE_PLEASEINPUTRECEIVENUM'|translate}}"-->
											   <!--ng-blur="checkMsisdnList(groupSendTaskInfo.groupSendTaskMsisdn)"-->
											   <!--ng-disabled="true"-->
											   <!--title={{item.msisdn}}-->
											   <!--pattern="[0-9]*[0-9][0-9]*$"-->
											   <!--ng-class="{'redBorder':!msisdnListValidate}">-->
										<!--</input>-->
									<!--</div>-->
								<!--</div>-->
								<!--&lt;!&ndash;<div class="col-lg-2 col-xs-2  col-sm-2 col-md-2">&ndash;&gt;-->
									<!--&lt;!&ndash;<div style="overflow:hidden;padding:1px 0px 10px 0px" ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">&ndash;&gt;-->
										<!--&lt;!&ndash;<icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length<50" ng-click="addMsisdn()"&ndash;&gt;-->
											  <!--&lt;!&ndash;class="addMsinsdn">&ndash;&gt;-->
										<!--&lt;!&ndash;</icon>&ndash;&gt;-->
										<!--&lt;!&ndash;<icon ng-show="$index == 0 && groupSendTaskInfo.groupSendTaskMsisdn.length>=50" ng-click=""&ndash;&gt;-->
											  <!--&lt;!&ndash;class="addMsinsdn">&ndash;&gt;-->
										<!--&lt;!&ndash;</icon>&ndash;&gt;-->
										<!--&lt;!&ndash;<icon ng-click="deleteMsisdn($index)"&ndash;&gt;-->
											  <!--&lt;!&ndash;class="deleteMsinsdn">&ndash;&gt;-->
										<!--&lt;!&ndash;</icon>&ndash;&gt;-->
									<!--&lt;!&ndash;</div>&ndash;&gt;-->
								<!--&lt;!&ndash;</div>&ndash;&gt;-->
								<!--<div  class="col-lg-3 col-xs-3  col-sm-3 col-md-3" style="padding:1px 0px 10px 0px"-->
									  <!--ng-show="(myForm['txtContent'+$index].$dirty && myForm['msisdn'+$index].$invalid) ||!msisdnListValidate"-->
									  <!--ng-repeat="item in groupSendTaskInfo.groupSendTaskMsisdn">-->
									<!--<img src="../../../../../assets/images/reject-icon.png" width="20" height="20" align="absmiddle">-->
									<!--&lt;!&ndash; <span ng-show="myForm['msisdn'+$index].$error.pattern" ng-bind="'RECEIVEDDESC'|translate"></span> &ndash;&gt;-->
									<!--<span style="color:red;" class="redFont" ng-show="!msisdnListValidate">{{msisdnListDesc}}</span>-->
								<!--</div>-->
							<!--</div>-->
						<!--</div>-->
						<div class="form-group ">
							<div class="row">
								<label class="col-lg-2 col-xs-2 col-sm-2 col-md-2 control-label">
									<span ng-bind="'RECEIVED'|translate" class="ng-binding">接收号码</span>：
								</label>
								<div class="col-lg-4 col-xs-5  col-sm-5 col-md-5" style="padding-top: 7px;">
									<a ng-click="exportFile2()" title="" style="margin-right: 40px;" ng-bind="'MSISDNDOWNLOD'|translate" class="ng-binding">已有号码下载</a>
								</div>
							</div>
						</div>


					</form>
				</div>
				<div class="modal-footer" style="text-align:center">

					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
							ng-bind="'COMMON_CANCLE'|translate"></button>
				</div>
			</div>
		</div>
	</div>

</div>
</body>

</html>