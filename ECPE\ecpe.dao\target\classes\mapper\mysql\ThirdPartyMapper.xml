<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpe.dao.mapper.ThirdPartyMapper">

	<resultMap type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdPartyWrapper" id="thirdPartyWrapper">
		<result property="id" column="ID" javaType="java.lang.Integer"/>
		<result property="platformID" column="platformID" javaType="java.lang.Integer"/>
		<result property="accessAccount" column="accessAccount" javaType="java.lang.String"/>
		<result property="accessPassword" column="accessPassword" javaType="java.lang.String"/>
		<result property="callbackUrl" column="callbackUrl" javaType="java.lang.String"/>
		<result property="approveCallbackUrl" column="approveCallbackUrl" javaType="java.lang.String"/>
		<result property="thirdAccount" column="thirdAccount" javaType="java.lang.String"/>
		<result property="thirdPassword" column="thirdPassword" javaType="java.lang.String"/>
		<result property="fluid" column="fluid" javaType="java.lang.String"/>
		<result property="createTime" column="createTime" javaType="java.util.Date"/>
		<result property="updateTime" column="updateTime" javaType="java.util.Date"/>
		<result property="operatorID" column="operatorID" javaType="java.lang.Integer"/>
		<result property="status" column="status" javaType="java.lang.Integer"/>
		<result property="whiteListMaxNum" column="whiteListMaxNum" javaType="java.lang.Integer"/>
		<result property="fluidSwitch" column="fluidSwitch" javaType="java.lang.Integer"/>
		<result property="msgType" column="msgType" javaType="java.lang.String"/>
		<result property="feeType" column="feeType" javaType="java.lang.Integer"/>
		<result property="isModifyTemplateByIntf" column="isModifyTemplateByIntf" javaType="java.lang.Integer"/>
		<result property="andCYFlag" column="andCYFlag" javaType="java.lang.Integer"/>
		<result property="callEventFlag" column="callEventFlag" javaType="java.lang.Integer"/>
		<result property="hotlineTempQuerySwitch" column="hotlineTempQuerySwitch" javaType="java.lang.Integer"/>
		<result property="ussdDelay" column="ussdDelay"/>
		<result property="feedbackUrl" column="feedbackUrl"/>
		<result property="groupSendNotifyUrl" column="groupSendNotifyUrl" javaType="java.lang.String"/>
		<result property="cmppAccount" column="cmppAccount" javaType="java.lang.String"/>
	</resultMap>
	<resultMap type="com.huawei.jaguar.dsdp.ecpe.dao.domain.ThirdPartyAccessWrapper" id="thirdPartyAccessWrapper">
		<result property="platformID" column="platformID" javaType="java.lang.Integer"/>
		<result property="status" column="status" javaType="java.lang.Integer"/>
		<result property="fluidSwitch" column="fluidSwitch" javaType="java.lang.Integer"/>
		<result property="fluid" column="fluid" javaType="java.lang.String"/>
	</resultMap>

	<!--新增第三方接入信息 -->
	<insert id="addThirdParty">
		INSERT INTO
		ecpe_t_thirdparty_access
		(
		ID,
		platformID,
		accessAccount,
		accessPassword,
		callbackUrl,
		approveCallbackUrl,
		thirdAccount,
		thirdPassword,
		fluid,
		createTime,
		updateTime,
		operatorID,
		status,
		whiteListMaxNum,
		fluidSwitch,
		msgType,
		feeType,
		isModifyTemplateByIntf,
		andCYFlag,
		callEventFlag,
		hotlineTempQuerySwitch,
		ussdDelay,
		groupSendNotifyUrl,
		diffnetUssdSwitch,
		enterpriseApproveCallbackUrl
		)
		VALUES
		(
		#{id},
		#{platformID},
		#{accessAccount},
		#{accessPassword},
		#{callbackUrl},
		#{approveCallbackUrl},
		#{thirdAccount},
		#{thirdPassword},
		#{fluid},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{status},
		#{whiteListMaxNum},
		#{fluidSwitch},
		#{msgType},
		#{feeType},
		#{isModifyTemplateByIntf},
		#{andCYFlag},
		#{callEventFlag},
		#{hotlineTempQuerySwitch},
		#{ussdDelay},
		#{groupSendNotifyUrl},
		#{diffnetUssdSwitch},
		#{enterpriseApproveCallbackUrl}
		)
	</insert>

	<!--更新第三方接入信息 -->
	<update id="updateThirdParty">
		UPDATE ecpe_t_thirdparty_access SET
		<trim suffixOverrides="," suffix="where ID=#{id}">
			<if test="platformID!=null">
				platformID = #{platformID},
			</if>
			<if test="accessAccount!=null  and accessAccount!=''">
				accessAccount = #{accessAccount},
			</if>
			<if test="accessPassword!=null and accessPassword!=''">
				accessPassword = #{accessPassword},
			</if>
			<if test="callbackUrl!=null and callbackUrl!=''">
            	callbackUrl = #{callbackUrl},
            </if>
            <if test="approveCallbackUrl!=null">
            	approveCallbackUrl = #{approveCallbackUrl},
            </if>
            <if test="thirdAccount!=null and thirdAccount!=''">
            	thirdAccount = #{thirdAccount},
            </if>
            <if test="thirdPassword!=null and thirdPassword!=''">
            	thirdPassword = #{thirdPassword},
            </if>
            <if test="fluid!=null">
            	fluid = #{fluid},
            </if>
            <if test="createTime!=null">
            	createTime = #{createTime},
            </if>
            <if test="updateTime!=null">
            	updateTime = #{updateTime},
            </if>
			<if test="operatorID!=null">
				operatorID = #{operatorID},
			</if>
			<if test="status!=null">
				status = #{status},
			</if>
			<if test="whiteListMaxNum!=null">
				whiteListMaxNum = #{whiteListMaxNum},
			</if>
			<if test="fluidSwitch!=null">
				fluidSwitch = #{fluidSwitch},
			</if>
			<if test="msgType!=null and msgType != ''">
				msgType = #{msgType},
			</if>
			<if test="feeType!=null">
				feeType = #{feeType},
			</if>
			<if test="isModifyTemplateByIntf!=null">
				isModifyTemplateByIntf = #{isModifyTemplateByIntf},
			</if>
			<if test="andCYFlag!=null">
				andCYFlag = #{andCYFlag},
			</if>
			<if test="callEventFlag!=null">
				callEventFlag = #{callEventFlag},
			</if>
			<if test="hotlineTempQuerySwitch!=null">
				hotlineTempQuerySwitch = #{hotlineTempQuerySwitch},
			</if>
			<if test="groupSendNotifyUrl!=null">
				groupSendNotifyUrl = #{groupSendNotifyUrl},
			</if>
			<if test="feedbackUrl!=null">
				feedbackUrl = #{feedbackUrl},
			</if>
			<if test="enterpriseApproveCallbackUrl!=null">
				enterpriseApproveCallbackUrl = #{enterpriseApproveCallbackUrl},
			</if>
			<if test="diffnetUssdSwitch!=null">
				diffnetUssdSwitch = #{diffnetUssdSwitch}
			</if>
		</trim>
	</update>

	<!--查询第三方接入信息 -->
	<select id="queryThirdPartyById" resultMap="thirdPartyWrapper">
		select
		ID,
		platformID,
		accessAccount,
		accessPassword,
		callbackUrl,
		approveCallbackUrl,
		thirdAccount,
		thirdPassword,
		fluid,
		createTime,
		updateTime,
		operatorID,
		status,
		whiteListMaxNum,
		fluidSwitch,
		msgType,
		feeType,
		isModifyTemplateByIntf,
		andCYFlag,
		callEventFlag,
		hotlineTempQuerySwitch,
		groupSendNotifyUrl,
		diffnetUssdSwitch
		from
		ecpe_t_thirdparty_access
		where ID = #{Id}
	</select>

	<select id="queryThirdPartyByPlatformID" resultMap="thirdPartyWrapper">
		select
		ID,
		platformID,
		accessAccount,
		accessPassword,
		callbackUrl,
		approveCallbackUrl,
		thirdAccount,
		thirdPassword,
		fluid,
		createTime,
		updateTime,
		operatorID,
		status,
		whiteListMaxNum,
		fluidSwitch,
		msgType,
		feeType,
		isModifyTemplateByIntf,
		andCYFlag,
		callEventFlag,
		hotlineTempQuerySwitch,
		feedbackUrl,
		groupSendNotifyUrl,
		diffnetUssdSwitch
		from
		ecpe_t_thirdparty_access
		where platformID = #{platformID}
	</select>

	<!-- lwx595992 根据platformID查询第三方接入状态，和流控开关 -->
	<select id="get3rdAccessByPlatformID" resultMap="thirdPartyAccessWrapper">
		select
		platformID,status,fluidSwitch,fluid,msgType,andCYFlag
		from
		ecpe_t_thirdparty_access
		where accessAccount = #{accessAccount}
	</select>

	<select id="batchQueryThirdPartyByPlatformID" resultMap="thirdPartyWrapper">
		select
		ID,
		platformID,
		accessAccount,
		accessPassword,
		callbackUrl,
		approveCallbackUrl,
		thirdAccount,
		thirdPassword,
		fluid,
		createTime,
		updateTime,
		operatorID,
		status,
		whiteListMaxNum,
		fluidSwitch,
		msgType,
		feeType,
		isModifyTemplateByIntf,
		andCYFlag,
		callEventFlag,
		hotlineTempQuerySwitch,
		groupSendNotifyUrl,
		diffnetUssdSwitch
		from
		ecpe_t_thirdparty_access
		where platformID in
		<foreach collection="list" item="platformID" index="index" open="(" separator="," close=")">
			#{platformID}
		</foreach>
	</select>



	<sql id="thirdPartyColumn">
		t.ID,
		t.platformID,
		t.accessAccount,
		t.accessPassword,
		t.callbackUrl,
		t.approveCallbackUrl,
		t.thirdAccount,
		t.thirdpassword,
		t.fluid,
		t.createTime,
		t.updateTime,
		t.operatorID,
		t.status,
		t.whiteListMaxNum,
		t.fluidSwitch,
		t.msgType,
		t.feeType,
		t.isModifyTemplateByIntf,
		t.andCYFlag,
		t.callEventFlag,
		t.hotlineTempQuerySwitch,
		t.ussdDelay,
		t.cmppAccount,
		t.cmppIpAndPort,
		t.diffnetUssdSwitch
    </sql>

    <select id="queryThirdParty" resultMap="thirdPartyWrapper">
		select
		<include refid="thirdPartyColumn"/>
		from ecpe_t_thirdparty_access t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="platformID != null">
				and t.platformID = #{platformID}
			</if>
			<if test="accessAccount !=null  and accessAccount !=''">
				and t.accessAccount = #{accessAccount}
			</if>
			<if test="cmppAccount != null">
				and t.cmppAccount = #{cmppAccount}
			</if>
		</trim>
    </select>

	<select id="queryThirdPartyCmppList" resultMap="thirdPartyWrapper">
		SELECT
		id,
		platformID,
		accessAccount,
		accessPassword,
		cmppAccount
		FROM
		ecpe_t_thirdparty_access t
		WHERE
		t.cmppAccount IS NOT NULL
		and t.cmppStatus = 1;
	</select>

	<select id="queryThirdPartyByCmppAccount" resultMap="thirdPartyWrapper">
		select
		id,
		platformID,
		accessAccount,
		accessPassword,
		cmppAccount
		from ecpe_t_thirdparty_access t
		<trim prefix="where" prefixOverrides="and|or">
			<if test="cmppAccount != null  and cmppAccount != ''">
				and t.cmppAccount = #{cmppAccount}
			</if>
			<if test="cmppStatus != null  and cmppStatus != ''">
				and t.cmppStatus = #{cmppStatus}
			</if>
		</trim>
	</select>

</mapper>