<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ContentMapper">
    <resultMap id="contentWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="contCode" column="contCode" javaType="java.lang.String" />
        <result property="contRuleID" column="contRuleID" javaType="java.lang.String" />
        <result property="thirdPartyType" column="thirdpartyType" javaType="java.lang.Integer" />
        <result property="servType" column="servType" javaType="java.lang.Integer" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="blackWhiteListType" column="blackwhiteListType" javaType="java.lang.Integer" />
        <result property="content" column="content" javaType="java.lang.String" />
        <result property="contentTitle" column="contentTitle" javaType="java.lang.String" />
        <result property="chargeType" column="chargeType" javaType="java.lang.Integer" />
        <result property="deliveryDate" column="deliveryDate" javaType="java.lang.String" />
        <result property="approveStatus" column="approveStatus" javaType="java.lang.Integer" />
        <result property="approveIdea" column="approveIdea" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="enterpriseName" column="enterpriseName" javaType="java.lang.String" />
        <result property="enterpriseCode" column="enterpriseCode" javaType="java.lang.String" />
        <result property="status" column="status" javaType="java.lang.Integer" />
        <result property="sceneDesc" column="sceneDesc" javaType="java.lang.String" />
        <result property="deliveryType" column="deliveryType" javaType="java.lang.String" />
        <result property="industryType" column="industryType" javaType="java.lang.String" />
        <result property="applyProperty" column="applyProperty" javaType="java.lang.Integer" />
        <result property="unicomDelivery" column="unicomDelivery" javaType="java.lang.Integer" />
        <result property="scene" column="scene" javaType="java.lang.Integer" />
        <result property="extInfo" column="extInfo" javaType="java.lang.String" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
        <result property="operatorID" column="operatorID" javaType="java.lang.Integer" />
        <result property="syncStatus" column="syncStatus" javaType="java.lang.Integer" />
        <result property="frequencyID" column="frequencyID" javaType="java.lang.String" />
        <result property="statID" column="statID" javaType="java.lang.String" />
        <result property="maxPushPerDay" column="maxPushPerDay" javaType="java.lang.Integer" />
        <result property="pushInterval" column="pushInterval" javaType="java.lang.Integer" />
        <result property="auditTime" column="auditTime" javaType="java.util.Date" />
        <result property="deductTime" column="deductTime" javaType="java.util.Date" />
        <result property="subServTypeEachMemberCall" column="subServTypeEachMemberCall" javaType="java.lang.Long" />
        <result property="reserved5" column="reserved5" javaType="java.lang.String" />
        <result property="reserved6" column="reserved6" javaType="java.lang.String" />
        <result property="reserved7" column="reserved7" javaType="java.lang.String" />
        <result property="reserved8" column="reserved8" javaType="java.lang.String" />
        <result property="reserved9" column="reserved9" javaType="java.lang.String" />
        <result property="reserved10" column="reserved10" javaType="java.lang.String" />
        <result property="parentID" column="parentID" javaType="java.lang.Long" />
        <result property="auditor" column="auditor" javaType="java.lang.String" />
        <result property="instruct" column="instruct" javaType="java.lang.String" />
        <result property="replyParentID" column="replyParentID" javaType="java.lang.Long" />
        <result property="replyApproveStatus" column="replyApproveStatus" javaType="java.lang.Integer" />
        <result property="enterpriseType" column="enterpriseType" javaType="java.lang.Integer" />
        <result property="pkgID" column="pkgID" javaType="java.lang.Integer" />
        <result property="contentType" column="contentType" javaType="java.lang.Integer" />
        <result property="pkgName" column="pkgName" javaType="java.lang.String" />
        <result property="pkgPrice" column="pkgPrice" javaType="java.lang.Integer" />
        <result property="unicomApproveStatus" column="unicomApproveStatus" javaType="java.lang.Integer" />
        <result property="unicomApproveIdea" column="unicomApproveIdea" javaType="java.lang.String" />
        <result property="unicomApproveTime" column="unicomApproveTime" javaType="java.util.Date" />
        <result property="telecomApproveStatus" column="telecomApproveStatus" javaType="java.lang.Integer" />
        <result property="telecomApproveIdea" column="telecomApproveIdea" javaType="java.lang.String" />
        <result property="telecomApproveTime" column="telecomApproveTime" javaType="java.util.Date" />
        <result property="mobileApproveStatus" column="mobileApproveStatus" javaType="java.lang.Integer" />
        <result property="mobileApproveIdea" column="mobileApproveIdea" javaType="java.lang.String" />
        <result property="mobileApproveTime" column="mobileApproveTime" javaType="java.util.Date" />
        <result property="templateNum" column="templateNum" javaType="java.lang.Integer" />
        <result property="isMonthByQuota" column="isMonthByQuota" javaType="java.lang.Integer" />
        <result property="batchNo" column="batchNo" javaType="java.lang.String" />
        <result property="supportEnterpriseType" column="supportEnterpriseType" javaType="java.lang.Integer" />
        <result property="refYsmbID" column="refYsmbID" javaType="java.lang.String" />
        <result property="dealStatus" column="dealStatus" javaType="java.lang.Integer" />
        <result property="branchType" column="branchType" javaType="java.lang.Integer" />
        <result property="oriContentID" column="oriContentID" javaType="java.lang.Long" />
        <result property="hangupType" column="hangupType" javaType="java.lang.Integer" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
        <result property="startTime" column="startTime" javaType="java.lang.String" />
        <result property="endTime" column="endTime" javaType="java.lang.String" />
        <result property="certificateUrl" column="certificateUrl" javaType="java.lang.String" />
    </resultMap>

    <resultMap id="SubServTypeCountWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.SubServTypeCountWrapper">
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="count" column="count" javaType="java.lang.Long" />
    </resultMap>

    <resultMap id="MonquotaDeliveryStatWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.MonquotaDeliveryStatWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="memberID" column="memberID" javaType="java.lang.Long" />
        <result property="ydpxDeliveryCount" column="ydpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltpxDeliveryCount" column="ltpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxpxDeliveryCount" column="dxpxDeliveryCount" javaType="java.lang.Integer" />
        <result property="pxDeliveryStatus" column="pxDeliveryStatus" javaType="java.lang.Integer" />
        <result property="pxDiffDeliveryStatus" column="pxDiffDeliveryStatus" javaType="java.lang.Integer" />
        <result property="ydgdDeliveryCount" column="ydgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="ltgdDeliveryCount" column="ltgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="dxgdDeliveryCount" column="dxgdDeliveryCount" javaType="java.lang.Integer" />
        <result property="gdDeliveryStatus" column="gdDeliveryStatus" javaType="java.lang.Integer" />
        <result property="gdDiffDeliveryStatus" column="gdDiffDeliveryStatus" javaType="java.lang.Integer" />
        <result property="ydgcDeliveryCount" column="ydgcDeliveryCount" javaType="java.lang.Integer" />
        <result property="gcDeliveryStatus" column="gcDeliveryStatus" javaType="java.lang.Integer" />
        <result property="msisdn" column="msisdn" javaType="java.lang.String" />
    </resultMap>

    <resultMap id="contentTemplateWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentTemplateWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="templateID" column="templateID" javaType="java.lang.Long" />
        <result property="contRuleID" column="contRuleID" javaType="java.lang.String" />
        <result property="servType" column="servType" javaType="java.lang.Integer" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="content" column="content" javaType="java.lang.String" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="signature" column="reserved10" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="contentType" column="contentType" javaType="java.lang.Integer" />
    </resultMap>

	<select id="queryContetAll" resultMap="contentWrapper">
		select * from ecpm_t_content where enterpriseType is null limit 500;
	</select>

    <select id="queryZYZQRXSFContentList" resultMap="contentWrapper">
        SELECT
            c.*
        FROM
            ecpm_t_content c
                LEFT JOIN ecpm_t_enterprise_simple es ON es.id = c.enterpriseID
                LEFT JOIN ecpm_t_sync_service_rule ssr ON ssr.enterpriseID = c.enterpriseID
        WHERE es.enterpriseType = 5 and  es.reserved10=111 and c.servType = 5 and ssr.servType = 5 and ssr.status = 1
    </select>

    <select id="queryContentByPkgId" resultMap="contentWrapper">
        select * from ecpm_t_content where pkgID = #{pkgID} and (status != 2 || status is null) and enterpriseID = #{enterpriseID};
    </select>

	<update id="updateContentWrapperBatch">
    update ecpm_t_content set
	    <trim prefix="enterpriseType=case" suffix="end">
	    <foreach collection="list" item="contentWrapper"  index="index" >
	        when id=#{contentWrapper.id} then #{contentWrapper.enterpriseType}
	    </foreach>
	    </trim>
	    <where>
	        <foreach collection="list" item="contentWrapper" separator="or" index="index">
	            id=#{contentWrapper.id}
	        </foreach>
	    </where>
	</update>

    <insert id="insetContent">
        INSERT INTO ecpm_t_content
        (ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentType,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        approveIdea,
        enterpriseID,
        enterpriseName,
        enterpriseCode,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        createTime,
        updateTime,
        operatorID,
        syncStatus,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        deductTime,
        belongTemplateID,
        parentID,
        enterpriseType,
        instruct,
        replyParentID,
        replyApproveStatus,
        pkgID,
        pkgName,
        pkgPrice,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
        mobileApproveIdea,
        mobileApproveTime,
        certificateUrl,
        remindGroupID
        <if test="isMonthByQuota != null">
            ,isMonthByQuota
        </if>
        <if test="batchNo != null">
            ,batchNo
        </if>
        <if test="supportEnterpriseType != null">
            ,supportEnterpriseType
        </if>
        <if test="refYsmbID != null">
            ,refYsmbID
        </if>
        , branchType,
        oriContentID
        ,hangupType
        ,variableNames
        ,ysmbType
        )
		VALUES
		(
		#{id},
		#{contCode},
		#{contRuleID},
		#{thirdPartyType},
		#{servType},
		#{subServType},
		#{blackWhiteListType},
		#{content},
		#{contentType},
		#{contentTitle},
		#{chargeType},
		#{deliveryDate},
		#{approveStatus},
		#{approveIdea},
		#{enterpriseID},
		#{enterpriseName},
		#{enterpriseCode},
		#{status},
		#{sceneDesc},
		#{deliveryType},
		#{industryType},
		#{applyProperty},
		#{unicomDelivery},
		#{scene},
		#{extInfo},
		#{reserved1},
		#{reserved2},
		#{reserved3},
		#{reserved4},
		#{reserved5},
		#{reserved6},
		#{reserved7},
		#{reserved8},
		#{reserved9},
		#{reserved10},
		#{createTime},
		#{updateTime},
		#{operatorID},
		#{syncStatus},
		#{frequencyID},
		#{statID},
		#{maxPushPerDay},
		#{pushInterval},
		#{deductTime},
		#{belongTemplateID},
		#{parentID},
		#{enterpriseType},
		#{instruct},
		#{replyParentID},
		#{replyApproveStatus},
		#{pkgID},
		#{pkgName},
		#{pkgPrice},
		#{unicomApproveStatus},
		#{unicomApproveIdea},
		#{unicomApproveTime},
		#{telecomApproveStatus},
		#{telecomApproveIdea},
		#{telecomApproveTime},
		#{mobileApproveStatus},
		#{mobileApproveIdea},
		#{mobileApproveTime},
		#{certificateUrl},
		#{remindGroupID}
        <if test="isMonthByQuota != null">
            ,#{isMonthByQuota}
        </if>
        <if test="batchNo != null">
            ,#{batchNo}
        </if>
        <if test="supportEnterpriseType != null">
            ,#{supportEnterpriseType}
        </if>
        <if test="refYsmbID != null">
            ,#{refYsmbID}
        </if>
        <if test="branchType != null">
            ,#{branchType}
        </if>
        <if test="branchType == null">
            ,2
        </if>
        ,#{oriContentID}
        ,#{hangupType}
        ,#{variableNames}
        ,#{ysmbType}
		)
	</insert>

    <select id="queryTempContentNum" resultMap ="contentWrapper">
            SELECT count(1) templateNum, id FROM (
                SELECT
                if(tc.parentID is not null,tc.parentID,tc.id) id,
                    tc.id templateContentID,
                    vc.id valContentID,
                    co.msisdn,
                    vc.servType,
                    vc.subServType,
                    tc.content templateText,
                    vc.content valText,
                    vc.status,
                    vc.approveStatus
                FROM
                    ecpm_t_content tc
                    JOIN ecpm_t_content vc ON vc.contentType = 3
                    AND vc.belongTemplateID = tc.id
                    JOIN ecpm_t_content_org co ON co.cyContID = vc.id
                WHERE
                    tc.contentType = 2
                    <if test="approveStatus != null">
                        AND vc.approveStatus =  #{approveStatus}
                    </if>
                    <if test="contentIds != null and contentIds.size()>0">
                        AND ( tc.id in
                        <foreach item="id" index="index" collection="contentIds" open="(" separator=","
                                 close=")">
                            #{id}
                        </foreach>
                        OR tc.parentID in
                        <foreach item="id" index="index" collection="contentIds" open="(" separator=","
                                 close=")">
                            #{id}
                        </foreach>
                    </if>
			)) a GROUP BY id
    </select>


    <select id="queryContetInfoList"  resultMap="contentWrapper">
        SELECT
        t1.id,t1.contCode,t1.contRuleID,t1.thirdpartyType,t1.servType,t1.subServType,t1.blackwhiteListType,
        t1.content,t1.contentTitle,t1.chargeType,t1.deliveryDate,t1.approveStatus,t1.syncStatus,t1.approveIdea,t1.enterpriseID,t1.enterpriseName,t1.enterpriseCode,t1.status,t1.sceneDesc,
        t1.deliveryType,t1.industryType,t1.applyProperty,t1.unicomDelivery,t1.scene,t1.frequencyID,t1.statID,t1.extInfo,t1.reserved1,t1.reserved2,t1.reserved3,
        t1.reserved4,t1.reserved7,t1.reserved9,t1.reserved10,t1.parentID,t1.createTime,t1.updateTime,t1.operatorID,t1.maxPushPerDay,t1.pushInterval,t1.auditTime,t1.deductTime,t1.contentType,t1.auditor,t1.enterpriseType,        
        t1.instruct,t1.replyParentID,t1.replyApproveStatus,t1.pkgID,t1.pkgName,t1.pkgPrice,t1.unicomApproveStatus,t1.unicomApproveIdea,t1.unicomApproveTime,t1.telecomApproveStatus,t1.telecomApproveIdea,t1.telecomApproveTime,
        t1.certificateUrl,t1.isMonthByQuota,t1.batchNo,t1.supportEnterpriseType,t1.refYsmbID,t1.dealStatus, t1.mobileApproveStatus, t1.mobileApproveIdea, t1.mobileApproveTime, t1.branchType, t1.oriContentID,t1.hangupType,t1.variableNames,
        t1.ysmbType
        FROM ecpm_t_content t1 WHERE t1.`ID` IN (SELECT id FROM (SELECT t.id FROM ecpm_t_content t
        	<choose>
        		<when test="(provinceIdList != null and provinceIdList.size() > 0) or (cityIdList != null and cityIdList.size() > 0)">
        			FORCE INDEX(idx_content_reserved)
        		</when>
        		<when test="supportEnterpriseType != null or (batchNo != null and batchNo != '')">
        		</when>
        		<otherwise>
        			FORCE INDEX(idx_content_createTime, PRIMARY, index_escspr)
        		</otherwise>
        	</choose>
            LEFT JOIN ecpm_t_enterprise_simple e ON e.ID=t.enterpriseID
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID=e.parentEnterpriseID
            </if>
        <trim prefix="where" prefixOverrides="and|or">
            <if test="ids !=null and ids.size > 0">
                and t.id in
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="ysmbType != null">
                and  SUBSTRING(t.ysmbType,#{ysmbType},1) = 1
            </if>
            <if test="platform != null">
                and t.reserved7 = #{platform}
            </if>

            <if test="oriContentID != null">
                and t.oriContentID = #{oriContentID}
            </if>
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="supportEnterpriseType != null">
                and t.supportEnterpriseType = #{supportEnterpriseType}
            </if>

            <if test="approveStatus !=null">
                and ((t.approveStatus = #{approveStatus} and (t.servType != 1 and t.servType != 5)) 
                or (t.mobileApproveStatus = #{approveStatus} and (t.servType = 1 or t.servType = 5)))
            </if>
            <if test="servTypeList != null and servTypeList.size()>0">
                and t.servType in
                <foreach item="servType" index="index" collection="servTypeList" open="(" separator="," close=")">
                    #{servType}
                </foreach>
            </if>
            <if test="subServTypeList != null and subServTypeList.size()>0">
                and t.subServType in
                <foreach item="subServType" index="index" collection="subServTypeList" open="(" separator="," close=")">
                    #{subServType}
                </foreach>
            </if>
            <if test="contentTypeList != null and contentTypeList.size()>0">
                and t.contentType in
                <foreach item="contentType" index="index" collection="contentTypeList" open="(" separator="," close=")">
                    #{contentType}
                </foreach>
            </if>


            <if test="enterpriseIdList != null and enterpriseIdList.size()>0">
                and t.enterpriseID in
                <foreach item="enterpriseID" index="index" collection="enterpriseIdList" open="(" separator=","
                    close=")">
                    #{enterpriseID}
                </foreach>
            </if>
            <if test="content !=null  and content !=''">
                and CONCAT("【", IF(t.reserved10 IS NOT NULL , t.reserved10, ''), "】", t.content) like "%"#{content}"%"
            </if>
            <if test="contentTitile !=null  and contentTitile !=''">
                and t.contentTitle like "%"#{contentTitile}"%"
            </if>
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like "%"#{enterpriseName}"%"
            </if>
            <if test="getWithArgv !=null  and getWithArgv == 0 ">
                and t.content not like "%"#{#*#}"%"
            </if>
            <if test="hotlineNo !=null  and hotlineNo !=''">
                and t.id in (select cyContID from ecpm_t_content_org t1
                where t1. hotlineNo = #{hotlineNo})
            </if>
            <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
                AND (
                    (
                    e.enterpriseType != 5 and e.enterpriseType != 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
            or
            (
            e.enterpriseType = 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e2.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
                    or
                    (
                        e.enterpriseType = 5
                    <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                        and (e.provinceID in
                        <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                            #{provinceID}
                        </foreach>
                        <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                            or e.provinceID is null
                        </if>
                        )
                    </if>
                    <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                        and (e.cityID in
                        <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                            #{cityID}
                        </foreach>
                        <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                            or e.cityID is null
                        </if>

                        )
                    </if>
                    <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                        and (e.countyID in
                        <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                            #{countyID}
                        </foreach>
                        <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                            or e.countyID is null
                        </if>
                        )
                    </if>
                    )
                )
            </if>
            and (t.status != 2 or t.status is null)
            <if test="getSubContent != null and getSubContent == 1">
            	and t.content is not null and t.subServType != 3
            </if>
            <if test="getSubContent == null or (getSubContent != null and getSubContent == 0)">
            	and t.parentID is null
            </if>
            and t.replyParentID is null
            <if test="countyIdList != null and countyIdList.size > 0">
                AND (e.countyID IS NULL
                OR e.countyID IN
                <foreach item="countyIdList" index="index" collection="countyIdList"
                         open="(" separator="," close=")">
                    #{countyIdList}
                </foreach>
                )
            </if>
            <if test="batchNo != null and batchNo != ''">
                and t.batchNo = #{batchNo}
            </if>

            <if test="isMonthByQuota != null and isMonthByQuota != ''">
                and (t.isMonthByQuota = #{isMonthByQuota}
                <if test="isMonthByQuota == 0">
                    or isMonthByQuota is null
                </if>
               )
            </if>

            <if test="isRefYsmb != null and isRefYsmb == 0">
                and t.refYsmbID is null
            </if>
            <if test="isRefYsmb != null and isRefYsmb == 1">
                and t.refYsmbID is not null
            </if>
            <if test="branchType != null and branchType != ''">
                and t.branchType = #{branchType}
            </if>
            <if test="hangupType != null and hangupType != ''">
                <if test="hangupType == 1">and t.hangupType = #{hangupType}</if>
                <if test="hangupType == 2">and (t.hangupType = #{hangupType} or t.hangupType is null)</if>
                <if test="hangupType == 3">and t.hangupType = #{hangupType}</if>
            </if>
            <if test="hangupType == '' ">
                and t.hangupType is null
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and (e.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach> or e.enterpriseType != 5 or e.enterpriseType is null)
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and (e.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach> or e.enterpriseType != 5 or e.enterpriseType is null)
            </if>
        </trim>
        order by createTime desc
        limit #{pageNum},#{pageSize}) s) ORDER BY createTime DESC
    </select>

    <select id="countContetInfoList" resultType="java.lang.Integer">
        select count(1) from ecpm_t_content t
        <choose>
        		<when test="(provinceIdList != null and provinceIdList.size() > 0) or (cityIdList != null and cityIdList.size() > 0)">
        			FORCE INDEX(idx_content_reserved)
        		</when>
        		<when test="supportEnterpriseType != null or (batchNo != null and batchNo != '')">
        		</when>
        		<otherwise>
        			FORCE INDEX(idx_content_createTime, PRIMARY)
        		</otherwise>
        	</choose>
        left join ecpm_t_enterprise_simple e on e.id = t.enterpriseID
        <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
            LEFT JOIN ecpm_t_enterprise_simple e2 ON e2.ID=e.parentEnterpriseID
        </if>
        <trim prefix="where" prefixOverrides="and|or">
            <if test="ids !=null and ids.size > 0">
                and t.id in
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="ysmbType != null">
                and  SUBSTRING(t.ysmbType,#{ysmbType},1) = 1
            </if>
            <if test="platform != null">
                and t.reserved7 = #{platform}
            </if>

            <if test="supportEnterpriseType != null">
                and t.supportEnterpriseType = #{supportEnterpriseType}
            </if>
            <if test="approveStatus !=null">
                and ((t.approveStatus = #{approveStatus} and (t.servType != 1 and t.servType != 5)) 
                or (t.mobileApproveStatus = #{approveStatus} and (t.servType = 1 or t.servType = 5)))
            </if>
            <if test="servTypeList != null and servTypeList.size()>0">
                and t.servType in
                <foreach item="servType" index="index" collection="servTypeList" open="(" separator="," close=")">
                    #{servType}
                </foreach>
            </if>
            <if test="subServTypeList != null and subServTypeList.size()>0">
                and t.subServType in
                <foreach item="subServType" index="index" collection="subServTypeList" open="(" separator="," close=")">
                    #{subServType}
                </foreach>
            </if>
            <if test="contentTypeList != null and contentTypeList.size()>0">
                and t.contentType in
                <foreach item="contentType" index="index" collection="contentTypeList" open="(" separator="," close=")">
                    #{contentType}
                </foreach>
            </if>
            <if test="enterpriseIdList != null and enterpriseIdList.size()>0">
                and t.enterpriseID in
                <foreach item="enterpriseID" index="index" collection="enterpriseIdList" open="(" separator=","
                    close=")">
                    #{enterpriseID}
                </foreach>
            </if>
            <if test="content !=null  and content !=''">
                and CONCAT("【", IF(t.reserved10 IS NOT NULL , t.reserved10, ''), "】", t.content) like "%"#{content}"%"
            </if>
            <if test="contentTitile !=null  and contentTitile !=''">
                and t.contentTitle like "%"#{contentTitile}"%"
            </if>
            <if test="hangupType != null and hangupType != ''">
                <if test="hangupType == 1">and t.hangupType = #{hangupType}</if>
                <if test="hangupType == 2">and (t.hangupType = #{hangupType} or t.hangupType is null)</if>
                <if test="hangupType == 3">and t.hangupType = #{hangupType}</if>
            </if>
            <if test="hangupType == '' ">
                and t.hangupType is null
            </if>
            <if test="enterpriseName !=null  and enterpriseName !=''">
                and t.enterpriseName like "%"#{enterpriseName}"%"
            </if>
            <if test="hotlineNo !=null  and hotlineNo !=''">
                and t.id in (select cyContID from ecpm_t_content_org t1
                where t1. hotlineNo = #{hotlineNo})
            </if>
            <if test="(enterpriseDataAuthList !=null and enterpriseDataAuthList.size > 0) or (provinceDataAuthList != null and provinceDataAuthList.size() > 0)">
                AND (
                (
                e.enterpriseType != 5 and e.enterpriseType != 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
            or
            (
            e.enterpriseType = 3
            <if test="enterpriseDataAuthList != null and enterpriseDataAuthList.size()>0">
                and e2.provinceID in
                <foreach item="provinceID" index="index" collection="enterpriseDataAuthList" open="(" separator="," close=")">
                    #{provinceID}
                </foreach>
            </if>
            )
                or
                (
                e.enterpriseType = 5
                <if test="provinceDataAuthList != null and provinceDataAuthList.size()>0">
                    and (e.provinceID in
                    <foreach item="provinceID" index="index" collection="provinceDataAuthList" open="(" separator="," close=")">
                        #{provinceID}
                    </foreach>
                    <if test="provinceDataAuthListORNull !=null  and provinceDataAuthListORNull !=''">
                        or e.provinceID is null
                    </if>
                    )
                </if>
                <if test="cityDataAuthList != null and cityDataAuthList.size()>0">
                    and (e.cityID in
                    <foreach item="cityID" index="index" collection="cityDataAuthList" open="(" separator="," close=")">
                        #{cityID}
                    </foreach>
                    <if test="cityDataAuthListORNull !=null  and cityDataAuthListORNull !=''">
                        or e.cityID is null
                    </if>

                    )
                </if>
                <if test="countyDataAuthList != null and countyDataAuthList.size()>0">
                    and (e.countyID in
                    <foreach item="countyID" index="index" collection="countyDataAuthList" open="(" separator="," close=")">
                        #{countyID}
                    </foreach>
                    <if test="countyDataAuthListORNull !=null  and countyDataAuthListORNull !=''">
                        or e.countyID is null
                    </if>
                    )
                </if>
                )
                )
            </if>
            and (t.status != 2 or t.status is null)
            and t.parentID is null
            and t.replyParentID is null
            <if test="countyIdList != null and countyIdList.size > 0">
                AND (e.countyID IS NULL
                OR e.countyID IN
                <foreach item="countyIdList" index="index" collection="countyIdList"
                         open="(" separator="," close=")">
                    #{countyIdList}
                </foreach>
                )
            </if>
            <if test="batchNo != null and batchNo != ''">
                and t.batchNo = #{batchNo}
            </if>
            <if test="isRefYsmb != null and isRefYsmb == 0">
                and t.refYsmbID is null
            </if>
            <if test="isRefYsmb != null and isRefYsmb == 1">
                and t.refYsmbID is not null
            </if>
            <if test="branchType != null and branchType != ''">
                and t.branchType = #{branchType}
            </if>
            <if test="isMonthByQuota != null and isMonthByQuota != ''">
                and (t.isMonthByQuota = #{isMonthByQuota}
                <if test="isMonthByQuota == 0">
                    or isMonthByQuota is null
                </if>
                )
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and (e.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach> or e.enterpriseType != 5 or e.enterpriseType is null)
            </if>

            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and (e.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach> or e.enterpriseType != 5 or e.enterpriseType is null)
            </if>
        </trim>
    </select>

    <select id="getCycontId" resultType="java.lang.Long">
        SELECT
        t.id
        FROM
        ecpm_t_content t
        WHERE
        t.parentID = #{cyContId} and (t.status != 2 or t.status is null) 
        <if test="contentType != null">
            AND t.contentType = #{contentType}
        </if>
        <if test="id != null">
            AND t.ID != #{ID}
        </if>
        <if test="servType != null">
            AND t.servType = #{servType}
        </if>
        AND t.subServType = #{subServType}
    </select>

    <select id="getConcentID" resultType="java.lang.Long">
		select
		nextval('ecpm_seq_content');
	</select>

    <update id="updateContentByContentId">
		update ecpm_t_content set contCode=#{contCode},
		approveStatus=#{approveStatus}, updateTime=#{updateTime}
        <if test="replyApproveStatus != null">
            ,replyApproveStatus=#{replyApproveStatus}
        </if>
        <if test="unicomApproveStatus != null">
            ,unicomApproveStatus=#{unicomApproveStatus}
        </if>
        <if test="telecomApproveStatus != null">
            ,telecomApproveStatus=#{telecomApproveStatus}
        </if>
        <if test="mobileApproveStatus != null">
            ,mobileApproveStatus=#{mobileApproveStatus}
        </if>
		 where
		id=#{id}
	</update>

    <update id="updateStatusStop">
		update ecpm_t_content set
		status=#{status},updateTime=#{updateTime} where
		ID=#{id} or parentID = #{id}
		or replyParentID = #{id}
	</update>
	
    <update id="updateContentByContCode">
        update ecpm_t_content set
        status=#{status},approveIdea=#{approveIdea},updateTime=#{updateTime}
        <if test="approveStatus != null">
            ,approveStatus=#{approveStatus}
        </if>
        <if test="auditTime != null">
            ,auditTime=#{auditTime}
        </if>
        <if test="unicomApproveStatus != null">
            ,unicomApproveStatus=#{unicomApproveStatus}
        </if>
        <if test="unicomApproveIdea != null">
            ,unicomApproveIdea=#{unicomApproveIdea}
        </if>
        <if test="unicomApproveTime != null">
            ,unicomApproveTime=#{unicomApproveTime}
        </if>
        <if test="telecomApproveStatus != null">
            ,telecomApproveStatus=#{telecomApproveStatus}
        </if>
        <if test="telecomApproveIdea != null">
            ,telecomApproveIdea=#{telecomApproveIdea}
        </if>
        <if test="telecomApproveTime != null">
            ,telecomApproveTime=#{telecomApproveTime}
        </if>
        <if test="mobileApproveStatus != null">
            ,mobileApproveStatus=#{mobileApproveStatus}
        </if>
        <if test="mobileApproveIdea != null">
            ,mobileApproveIdea=#{mobileApproveIdea}
        </if>
        <if test="mobileApproveIdea == null">
            ,mobileApproveIdea=null
        </if>
        <if test="mobileApproveTime != null">
            ,mobileApproveTime=#{mobileApproveTime}
        </if>
        <if test="deductTime != null">
            ,deductTime=#{deductTime}
        </if>
        <if test="reserved3 != null">
            ,reserved3=#{reserved3}
        </if>
        <if test="syncStatus != null">
            ,syncStatus=#{syncStatus}
        </if>
        <if test="replyApproveStatus != null">
            ,replyApproveStatus=#{replyApproveStatus}
        </if>
        where contCode=#{contCode}
    </update>

    <update id="updateContentUnicomByContCode">
        update ecpm_t_content set
          unicomApproveStatus=#{unicomApproveStatus}
        ,unicomApproveIdea=#{unicomApproveIdea}
        ,unicomApproveTime=#{unicomApproveTime}
        where contCode=#{contCode}
    </update>

    <update id="updateContentTelecomByContCode">
        update ecpm_t_content set
        telecomApproveStatus=#{telecomApproveStatus}
        ,telecomApproveIdea=#{telecomApproveIdea}
        ,telecomApproveTime=#{telecomApproveTime}
        where contCode=#{contCode}
    </update>
    
    <update id="updateContentByContCodeisAuditor">
        update ecpm_t_content set
        auditor=#{auditor}
        where contCode=#{contCode}
    </update>
    

    <!--<update id="updateContentByContId">
        update ecpm_t_content set
        status=#{status},approveIdea=#{approveIdea},updateTime=#{updateTime}
        <if test="deductTime != null">
            ,deductTime=#{deductTime}
        </if>
        <if test="reserved3 != null">
            ,reserved3=#{reserved3}
        </if>
        <if test="syncStatus != null">
            ,syncStatus=#{syncStatus}
        </if>
        <if test="approveStatus != null">
            ,approveStatus=#{approveStatus}
        </if>
        <if test="auditTime != null">
            ,auditTime=#{auditTime}
        </if>
        <if test="unicomApproveStatus != null">
            ,unicomApproveStatus=#{unicomApproveStatus}
        </if>
        <if test="unicomApproveIdea != null">
            ,unicomApproveIdea=#{unicomApproveIdea}
        </if>
        <if test="unicomApproveTime != null">
            ,unicomApproveTime=#{unicomApproveTime}
        </if>
        <if test="telecomApproveStatus != null">
            ,telecomApproveStatus=#{telecomApproveStatus}
        </if>
        <if test="telecomApproveIdea != null">
            ,telecomApproveIdea=#{telecomApproveIdea}
        </if>
        <if test="telecomApproveTime != null">
            ,telecomApproveTime=#{telecomApproveTime}
        </if>
        <if test="mobileApproveStatus != null">
            ,mobileApproveStatus=#{mobileApproveStatus}
        </if>
        <if test="mobileApproveIdea != null">
            ,mobileApproveIdea=#{mobileApproveIdea}
        </if>
        <if test="mobileApproveIdea == null">
            ,mobileApproveIdea=null
        </if>
        <if test="mobileApproveTime != null">
            ,mobileApproveTime=#{mobileApproveTime}
        </if>
        where id=#{id}
    </update>-->

    <update id="updateContentByContId">
        update ecpm_t_content set
        status=#{status},updateTime=#{updateTime}
        <if test="deductTime != null">
            ,deductTime=#{deductTime}
        </if>
        <if test="reserved3 != null">
            ,reserved3=#{reserved3}
        </if>
        <if test="syncStatus != null">
            ,syncStatus=#{syncStatus}
        </if>
        <if test="approveStatus != null">
            ,approveStatus=#{approveStatus}
        </if>
        <if test="auditTime != null">
            ,auditTime=#{auditTime}
        </if>
        <if test="unicomApproveStatus != null">
            ,unicomApproveStatus=#{unicomApproveStatus}
        </if>
        <!--<if test="unicomApproveIdea != null">
            ,unicomApproveIdea=#{unicomApproveIdea}
        </if>-->
        <if test="unicomApproveTime != null">
            ,unicomApproveTime=#{unicomApproveTime}
        </if>
        <if test="telecomApproveStatus != null">
            ,telecomApproveStatus=#{telecomApproveStatus}
        </if>
        <!--<if test="telecomApproveIdea != null">
            ,telecomApproveIdea=#{telecomApproveIdea}
        </if>-->
        <if test="telecomApproveTime != null">
            ,telecomApproveTime=#{telecomApproveTime}
        </if>
        <if test="mobileApproveStatus != null">
            ,mobileApproveStatus=#{mobileApproveStatus}
        </if>
        <if test="mobileApproveTime != null">
            ,mobileApproveTime=#{mobileApproveTime}
        </if>
        where id=#{id}
    </update>

    <update id="updateParentCallerIdeaByContId">
        update ecpm_t_content set
        auditor=NULLIF(CONCAT(ifnull(#{auditor},''),ifnull(auditor,'||||')),'||||'),
        approveIdea=NULLIF(CONCAT(ifnull(#{auditOpinion},''),ifnull(approveIdea,'||||')),'||||'),
        mobileApproveIdea=NULLIF(CONCAT(ifnull(#{auditOpinion},''),ifnull(mobileApproveIdea,'||||')),'||||')
        <if test="reserved7 == '110' or reserved7 == '111' or reserved7 == '011' or reserved7 == '010'">
            ,unicomApproveIdea=NULLIF(CONCAT(ifnull(#{auditOpinion},''),ifnull(unicomApproveIdea,'||||')),'||||')
        </if>
        <if test="reserved7 == '101' or reserved7 == '111' or reserved7 == '011' or reserved7 == '001'">
            ,telecomApproveIdea=NULLIF(CONCAT(ifnull(#{auditOpinion},''),ifnull(telecomApproveIdea,'||||')),'||||')
        </if>
        where id=#{id}
    </update>

    <update id="updateParentCalledIdeaByContId">
        update ecpm_t_content set
        auditor=NULLIF(CONCAT(ifnull(auditor,'||||'),ifnull(#{auditor},'')),'||||'),
        approveIdea=NULLIF(CONCAT(ifnull(approveIdea,'||||'),ifnull(#{auditOpinion},'')),'||||'),
        mobileApproveIdea=NULLIF(CONCAT(ifnull(mobileApproveIdea,'||||'),ifnull(#{auditOpinion},'')),'||||')
        <if test="reserved7 == '110' or reserved7 == '111' or reserved7 == '011' or reserved7 == '010'">
            ,unicomApproveIdea=NULLIF(CONCAT(ifnull(unicomApproveIdea,'||||'),ifnull(#{auditOpinion},'')),'||||')
        </if>
        <if test="reserved7 == '101' or reserved7 == '111' or reserved7 == '011' or reserved7 == '001'">
            ,telecomApproveIdea=NULLIF(CONCAT(ifnull(telecomApproveIdea,'||||'),ifnull(#{auditOpinion},'')),'||||')
        </if>
        where id=#{id}
    </update>

    <update id="updateDiffNetworkContentByContId">
        update ecpm_t_content set
        id=#{id}
        <if test="unicomApproveStatus != null">
            ,unicomApproveStatus=#{unicomApproveStatus}
        </if>
        <if test="unicomApproveIdea != null">
            ,unicomApproveIdea=#{unicomApproveIdea}
        </if>
        <if test="unicomApproveTime != null">
            ,unicomApproveTime=#{unicomApproveTime}
        </if>
        <if test="telecomApproveStatus != null">
            ,telecomApproveStatus=#{telecomApproveStatus}
        </if>
        <if test="telecomApproveIdea != null">
            ,telecomApproveIdea=#{telecomApproveIdea}
        </if>
        <if test="telecomApproveTime != null">
            ,telecomApproveTime=#{telecomApproveTime}
        </if>
        <if test="mobileApproveStatus != null">
            ,mobileApproveStatus=#{mobileApproveStatus}
        </if>
        <if test="mobileApproveIdea != null">
            ,mobileApproveIdea=#{mobileApproveIdea}
        </if>
        <if test="mobileApproveIdea == null">
            ,mobileApproveIdea=null
        </if>
        <if test="mobileApproveTime != null">
            ,mobileApproveTime=#{mobileApproveTime}
        </if>
        where id=#{id}
    </update>

    <update id="updateContByCodeForDeduct">
		update ecpm_t_content set deductTime=#{deductTime}
		where contCode=#{contCode}
	</update>

    <update id="updateContentSyncStatusById">
        update ecpm_t_content set updateTime=#{updateTime}
        <if test="reserved3 != null">
            ,reserved3=#{reserved3}
        </if>
        <if test="status != null">
            ,status=#{status}
        </if>
        <if test="syncStatus != null">
            ,syncStatus=#{syncStatus}
        </if>
        <if test="approveStatus != null">
            ,approveStatus=#{approveStatus}
        </if>
        <if test="auditTime != null">
            ,auditTime=#{auditTime}
        </if>
        <if test="replyApproveStatus != null">
            ,replyApproveStatus=#{replyApproveStatus}
        </if>
        where id=#{id}
    </update>


    <update id="updateContentSyncStatusAndIdeaById">
        update ecpm_t_content set updateTime=#{updateTime}
        <if test="reserved3 != null">
            ,reserved3=#{reserved3}
        </if>
        <if test="status != null">
            ,status=#{status}
        </if>
        <if test="syncStatus != null">
            ,syncStatus=#{syncStatus}
        </if>
        <if test="approveStatus != null">
            ,approveStatus=#{approveStatus}
        </if>
        <if test="auditTime != null">
            ,auditTime=#{auditTime}
        </if>
        <if test="replyApproveStatus != null">
            ,replyApproveStatus=#{replyApproveStatus}
        </if>
        ,approveIdea = #{approveIdea}
        ,mobileApproveIdea = #{mobileApproveIdea}
        ,unicomApproveIdea = #{unicomApproveIdea}
        ,telecomApproveIdea = #{telecomApproveIdea}
        ,auditor = #{auditor}
        where id=#{id}
    </update>

    <update id="updatePartContentByContentId">
        update ecpm_t_content set
        updateTime = now()
        <if test="unicomApproveStatus != null">
            ,unicomApproveStatus=#{unicomApproveStatus}
        </if>
        <if test="telecomApproveStatus != null">
            ,telecomApproveStatus=#{telecomApproveStatus}
        </if>
        <if test="mobileApproveStatus != null">
            ,mobileApproveStatus=#{mobileApproveStatus}
        </if>
        <if test="status != null">
            , status= #{status}
        </if>
        <if test="contCode != null">
            , contCode= #{contCode}
        </if>
        <if test="blackWhiteListType !=null">
            , blackwhiteListType = #{blackWhiteListType}
        </if>
        <if test="content !=null">
            , content= #{content}
        </if>
        <if test="contentType !=null">
            , contentType= #{contentType}
        </if>
        <if test="contentTitle !=null">
            , contentTitle= #{contentTitle}
        </if>
        <if test="deliveryDate !=null">
            , deliveryDate= #{deliveryDate}
        </if>
        <if test="syncStatus != null">
            ,syncStatus=#{syncStatus}
        </if>
        <if test="approveStatus !=null">
            , approveStatus= #{approveStatus}
        </if>
        <if test="industryType !=null">
            , industryType= #{industryType}
        </if>
        <if test="applyProperty !=null">
            , applyProperty= #{applyProperty}
        </if>
        <if test="unicomDelivery !=null">
            , unicomDelivery= #{unicomDelivery}
        </if>
        <if test="scene !=null">
            , scene= #{scene}
        </if>
        <if test="sceneDesc !=null">
            , scenedesc= #{sceneDesc}
        </if>
        <if test="frequencyID !=null">
            , frequencyID = #{frequencyID}
        </if>
        <if test="statID !=null">
            , statID= #{statID}
        </if>
        <if test="maxPushPerDay !=null">
            , maxPushPerDay= #{maxPushPerDay}
        </if>
        <if test="pushInterval!=null">
            , pushInterval= #{pushInterval}
        </if>
        <if test="operatorID!=null">
            , operatorID= #{operatorID}
        </if>
        <if test="deductTime!=null">
            , deductTime= #{deductTime}
        </if>
        <if test="reserved1!=null">
            , reserved1= #{reserved1}
        </if>
        <if test="reserved4!=null">
            , reserved4= #{reserved4}
        </if>
        <if test="reserved5!=null">
            , reserved5= #{reserved5}
        </if>
        <if test="auditTime!=null">
            , auditTime= #{auditTime}
        </if>
        <if test="reserved7!=null">
            , reserved7= #{reserved7}
        </if>
        <if test="reserved10!=null">
            , reserved10= #{reserved10}
        </if>
        <if test="reserved9!=null">
            , reserved9= #{reserved9}
        </if>
        <if test="instruct!=null">
            , instruct= #{instruct}
        </if>
        <if test="replyApproveStatus != null">
            ,replyApproveStatus=#{replyApproveStatus}
        </if>
        <if test="pkgID != null">
            ,pkgID=#{pkgID}
        </if>
        <if test="pkgName != null">
            ,pkgName=#{pkgName}
        </if>
        <if test="pkgPrice != null">
            ,pkgPrice=#{pkgPrice}
        </if>
        <if test="refYsmbID != null and refYsmbID != '-1'">
            ,refYsmbID=#{refYsmbID}
        </if>
        <if test="refYsmbID != null and refYsmbID == '-1'">
            ,refYsmbID=null
            ,dealStatus=null
        </if>
        <if test="mobileApproveIdea == 'null' ">
            ,mobileApproveIdea=null
        </if>
        <if test="telecomApproveIdea == 'null' ">
            ,telecomApproveIdea=null
        </if>
        <if test="unicomApproveIdea == 'null' ">
            ,unicomApproveIdea=null
        </if>
        <if test="approveIdea == 'null' ">
            ,approveIdea=null
        </if>
        <if test="auditor == 'null' ">
            ,auditor=null
        </if>
        <if test="suspendUserType!=null">
            ,suspendUserType= #{suspendUserType}
        </if>
        <if test="certificateUrl!=null and certificateUrl != ''">
            ,certificateUrl=#{certificateUrl}
        </if>

        <if test="variableNames!=null and variableNames != ''">
            ,variableNames=#{variableNames}
        </if>
        <if test="ysmbType!=null and ysmbType != ''">
            ,ysmbType=#{ysmbType}
        </if>
        where id=#{id}
    </update>
    <update id="updateCByIdSynStatuSetNull">
        update ecpm_t_content set
        updateTime = now()
        <if test="status != null">
            , status= #{status}
        </if>
        <if test="contCode != null">
            , contCode= #{contCode}
        </if>
        <if test="blackWhiteListType !=null">
            , blackwhiteListType = #{blackWhiteListType}
        </if>
        <if test="content !=null">
            , content= #{content}
        </if>
        <if test="contentType !=null">
            , contentType= #{contentType}
        </if>
        <if test="contentTitle !=null">
            , contentTitle= #{contentTitle}
        </if>
        <if test="deliveryDate !=null">
            , deliveryDate= #{deliveryDate}
        </if>
        <if test="approveStatus !=null">
            , approveStatus= #{approveStatus}
        </if>
        <if test="industryType !=null">
            , industryType= #{industryType}
        </if>
        <if test="applyProperty !=null">
            , applyProperty= #{applyProperty}
        </if>
        <if test="unicomDelivery !=null">
            , unicomDelivery= #{unicomDelivery}
        </if>
        <if test="scene !=null">
            , scene= #{scene}
        </if>
        <if test="frequencyID !=null">
            , frequencyID = #{frequencyID}
        </if>
        <if test="statID !=null">
            , statID= #{statID}
        </if>
        <if test="maxPushPerDay !=null">
            , maxPushPerDay= #{maxPushPerDay}
        </if>
        <if test="pushInterval!=null">
            , pushInterval= #{pushInterval}
        </if>
        <if test="operatorID!=null">
            , operatorID= #{operatorID}
        </if>
        <if test="deductTime!=null">
            , deductTime= #{deductTime}
        </if>
        <if test="reserved1!=null">
            , reserved1= #{reserved1}
        </if>
        <if test="reserved4!=null">
            , reserved4= #{reserved4}
        </if>
        <if test="reserved5!=null">
            , reserved5= #{reserved5}
        </if>
        <if test="auditTime!=null">
            , auditTime= #{auditTime}
        </if>
        <if test="reserved7!=null">
            , reserved7= #{reserved7}
        </if>
        <if test="reserved10!=null">
            , reserved10= #{reserved10}
        </if>
        <if test="reserved9!=null">
            , reserved9= #{reserved9}
        </if>
        <if test="instruct!=null">
            , instruct= #{instruct}
        </if>
        <if test="replyApproveStatus != null">
            ,replyApproveStatus=#{replyApproveStatus}
        </if>
        <if test="pkgID != null">
            ,pkgID=#{pkgID}
        </if>
        <if test="pkgName != null">
            ,pkgName=#{pkgName}
        </if>
        <if test="pkgPrice != null">
            ,pkgPrice=#{pkgPrice}
        </if>
        <if test="syncStatus == null">
            ,syncStatus=null
        </if>
        <if test="certificateUrl != null">
            ,certificateUrl=#{certificateUrl}
        </if>
        where id=#{id}
    </update>

    <select id="queryContentByContCode" resultMap="contentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		enterpriseType,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
		reserved6,
		reserved7,
		reserved8,
		reserved9,
		reserved10,
		parentID,
		createTime,
		updateTime,
		operatorID,
		syncStatus,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		operateCode,
		instruct,
		replyParentID,
		replyApproveStatus,
		pkgID,
		pkgName,
		pkgPrice,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		supportEnterpriseType,
		mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		isMonthByQuota,
		remindGroupID,
		branchType,
		oriContentID,
		refYsmbID,
		belongTemplateID,
		hangupType
		from
		ecpm_t_content where contCode=#{contCode}
	</select>

    <select id="queryContentByContID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		syncStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		enterpriseType,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		belongTemplateID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
		parentID,
		createTime,
		updateTime,
		operatorID,
		instruct,
		replyParentID,
		replyApproveStatus,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		mobileApproveStatus,
        mobileApproveIdea,
        mobileApproveTime,
		certificateUrl,
		isMonthByQuota,
		batchNo,
		refYsmbID,
		supportEnterpriseType,
		dealStatus,
		branchType,
		oriContentID,
        pkgID,
        hangupType,
        suspendUserType
		from ecpm_t_content
		where ID=#{contID}
	</select>
	
	<select id="getByreplyParentID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		syncStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		belongTemplateID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
		parentID,
		createTime,
		updateTime,
		operatorID,
		instruct,
		replyParentID,
		replyApproveStatus,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
		from ecpm_t_content
		where replyParentID = #{contentId}
		AND (status!=2 or status is null)
	</select>

    <select id="getAllByreplyParentID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		syncStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		belongTemplateID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
		parentID,
		createTime,
		updateTime,
		operatorID,
		instruct,
		replyParentID,
		replyApproveStatus,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
		from ecpm_t_content
		where replyParentID = #{contentId}
	</select>

    <select id="queryContentByEnterpriseID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		*
		from ecpm_t_content
		where enterpriseID=#{enterpriseID}
	</select>

    <select id="querySynSuccessContentByEnterpriseID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">

        SELECT
            *
        FROM
            ecpm_t_content c
                LEFT JOIN ecpm_t_enterprise_simple es ON es.id = c.enterpriseID
        WHERE
            ( es.id = #{enterpriseID} OR es.parentEnterpriseID = #{enterpriseID} )
          AND c.approveStatus = 3
          AND c.syncStatus = 6
          AND c.contentType IN ( 1, 3 )
          AND c.STATUS = 0
          AND c.servType = #{servType}
    </select>

    <select id="queryContentByContIDsAndServType" resultMap="contentWrapper" parameterType="java.util.List">
        SELECT
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
		contentType,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        approveIdea,
        enterpriseID,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved7,
        parentID,
        createTime,
        updateTime,
        operatorID,
        syncStatus,
        deductTime,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
        isMonthByQuota,
        hangupType
        from ecpm_t_content
        <trim prefix="where" prefixOverrides="and|or">
            <if test="contentIDs !=null and contentIDs.size() > 0">
                and ID in
                <foreach item="contentID" index="index" collection="contentIDs" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
            </if>
            <if test="servType != null">
                and servType = #{servType}
            </if>
        </trim>
    </select>

    <!--返回每个子业务类型对应的成员数量 -->
    <select id="queryMemberCountBySubServType" resultMap="contentWrapper">
        select
        count(t3.ID)
        subServTypeEachMemberCall,t3.subServType,t3.servType,t3.enterpriseID
        from
        (
        SELECT
        t.servType,t.subServType,t.approveStatus,t2.ID,t.enterpriseID
        from
        ecpm_t_content t,ecpm_t_content_org t1,ecpm_t_org_rel t2
        where t.id =
        t1.cyContID
        and t2.orgID = t1.ownerID
        and t2.enterpriseID =
        t.enterpriseID
        and
        t.subServType =#{subServType}
        and t.servType
        =#{servType}
        and t.chargeType = #{chargeType}
        and
        t.enterpriseID =
        #{enterpriseID}
        <if test="approveStatus !=null and approveStatus !=3">
            and t.approveStatus !=3
        </if>
        <if test="approveStatus !=null and approveStatus ==3">
            and t.approveStatus =3
        </if>
        ) t3
        group by
        t3.subServType,t3.servType,t3.enterpriseID
    </select>

    <select id="queryContentListOrgID" resultMap="contentWrapper">
        SELECT
        t1.ID,
        t1.deductTime,
        t2.orgCode,
        t2.ownerID,
        t1.contCode,
        t1.servType,
        t1.enterpriseID,
        t1.subServType,
        t1.chargeType,
        t1.approveStatus,
        t1.parentID
        from
        ecpm_t_content t1,ecpm_t_content_org t2
        where
        t1.ID=t2.cyContID
        and
        t2.ownerType = t1.servType
        and (t1.subServType = 1
        or t1.subServType = 2
        or t1.subServType = 4
        or t1.subServType = 8)
        <if test="chargeType !=null">
            and t1.chargeType = #{chargeType}
        </if>

        <if test="approveStatus !=null and approveStatus !=3">
            and t1.approveStatus !=3
        </if>
        <if test="approveStatus !=null and approveStatus ==3">
            and t1.approveStatus =3
        </if>
        <if test="approveStatusList != null and approveStatusList.size() > 0">
            and
            <foreach collection="approveStatusList" item="approveStatus" open="(" separator="or" close=")">
                t1.approveStatus = #{approveStatus}
            </foreach>
        </if>
        and
        t2.ownerID=#{orgID}
        <if test="servType !=null">
            and t1.servType = #{servType}
        </if>
        <if test="enterpriseID !=null">
            and t1.enterpriseID
            = #{enterpriseID}
        </if>
    </select>

    <select id="queryContentListOrgIDAndServType" resultMap="contentWrapper">
        SELECT
        t1.ID,
        t1.deductTime,
        t2.orgCode,
        t2.ownerID,
        t1.contCode,
        t1.servType,
        t1.enterpriseID,
        if(t1.subServType = 1 and t1.parentID is not null,3,t1.subServType) subServType,
        t1.chargeType,
        t1.approveStatus,
        t1.parentID,
        t1.status,
        t1.pkgID,
        t1.contentType,
        t1.isMonthByQuota,
        t1.reserved7
        from
        ecpm_t_content t1,ecpm_t_content_org t2
        where
        t1.ID= t2.cyContID
        and t1.approveStatus in (2,3)
        and (t1.status in (0,1,3) or t1.status is null)
        and t2.ownerID=#{orgID}
        and t1.servType = #{servType}
        <if test="enterpriseID !=null">
            and t1.enterpriseID = #{enterpriseID}
        </if>
        and (t1.subServType = 1 or t1.parentID is null)
    </select>

    <select id="queryContentByActivityID" resultMap="contentWrapper">
		select
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		syncStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		belongTemplateID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved7,
		reserved9,
		reserved10,
		createTime,
		updateTime,
		operatorID,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
		from
		ecpm_t_content where ID in
		(select contentID from
		ecpm_t_activity_contentrel where activityID =
		#{activityID})

	</select>

    <update id="auditSuccess">
        update ecpm_t_content
        set
        approveStatus = #{approveStatus},
        status = #{status},
        updateTime = #{updateTime},
        operatorID = #{operatorID},
        audittime = #{auditTime},
        <if test="reserved3 != null">
            reserved3 = #{reserved3},
        </if>
        <if test="contCode != null and contCode !=''">
            ,contCode=#{contCode}
        </if>
        <if test="mobileApproveIdea != null">
            mobileApproveIdea = #{mobileApproveIdea},
        </if>
        <if test="mobileApproveIdea == null">
            mobileApproveIdea=null,
        </if>
        approveIdea=#{approveIdea}
        where ID = #{ID}
    </update>

    <select id="queryContentOrderQuotask" resultMap="contentWrapper">
		SELECT
		t.ID,
		t.contCode,
		t.contRuleID,
		t.thirdpartyType,
		t.servType,
		t.subServType,
		t.blackwhiteListType,
		t.content,
		t.contentTitle,
		t.chargeType,
		t.deliveryDate,
		t.approveStatus,
		t.approveIdea,
		t.enterpriseID,
		t.status,
		t.sceneDesc,
		t.deliveryType,
		t.industryType,
		t.applyProperty,
		t.unicomDelivery,
		t.scene,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.createTime,
		t.updateTime,
		t.operatorID,
		t.syncStatus,
		t.statID,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
		from ecpm_t_content t
		where
		t.approveStatus=3
		and
		t.chargeType=1
		and t.thirdpartyType=0
		and t.servType=1
		and
		t.`status`=0
	</select>

    <select id="queryContetInfoListForMemberCnt" resultMap="contentWrapper">
        select
        t.id,t.contCode,t.contRuleID,t.thirdpartyType,t.servType,t.subServType,t.blackwhiteListType,
        t.content,t.contentTitle,t.chargeType,t.deliveryDate,t.approveStatus,t.syncStatus,t.approveIdea,t.enterpriseID,t.enterpriseName,t.enterpriseCode,t.status,t.sceneDesc,
        t.deliveryType,t.industryType,t.applyProperty,t.unicomDelivery,t.scene,t.frequencyID,t.statID,t.extInfo,t.reserved1,t.reserved2,t.reserved3,
        t.reserved4,t.reserved7,t.createTime,t.updateTime,t.operatorID,t.maxPushPerDay,t.pushInterval,t.auditTime,t.deductTime,t.contentType,
        t.unicomApproveStatus,t.unicomApproveIdea,t.unicomApproveTime,t.telecomApproveStatus,t.telecomApproveIdea,t.telecomApproveTime,	t.mobileApproveStatus,
		t.mobileApproveIdea,
		t.mobileApproveTime,
		t.hangupType,
        t.branchType,
        t.parentID,
        t.isMonthByQuota,
        t.certificateUrl
        from ecpm_t_content
        t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servType !=null">
                and t.servType = #{servType}
            </if>
            <if test="chargeType !=null">
                and t.chargeType = #{chargeType}
            </if>
            <if test="subServType !=null">
                and t.subServType = #{subServType}
            </if>
            <if test="status !=null">
                and t.status = #{status}
            </if>
            <if test="approveStatus !=null">
                and t.approveStatus = #{approveStatus}
            </if>
            <if test="thirdPartyType !=null">
                and t.thirdpartyType = #{thirdPartyType}
            </if>
        </trim>
    </select>

    <!--返回每个子业务类型对应的成员数量 -->
    <select id="queryMemberForContCount" resultType="java.lang.Integer">
        SELECT
        count(distinct(t2.ID))
        from
        ecpm_t_content t,ecpm_t_content_org
        t1,ecpm_t_org_rel t2
        where t.id =
        t1.cyContID
        and t2.orgID = t1.ownerID
        <if test="contIdList !=null and contIdList.size() > 0">
            and t.id in
            <foreach collection="contIdList" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <insert id="insertContNotify">
        insert into ecpm_t_cont_notify
        (contID,
        thirdpartyType,
        auditResult,
        auditDesc,
        auditTime,
        enterpriseID,
        enterpriseCode,
        notifyStatus,
        notifyFailTimes,
        lastnotifyTime,
        createTime,
        updateTime,
        platform,
        reserved1,
        notifyType)
        VALUES
        (#{contID},
        #{thirdPartyType},
        #{auditResult},
        #{auditDesc},
        #{auditTime},
        #{enterpriseID},
        #{enterpriseCode},
        null,
        0,
        null,
        #{createTime},
        #{updateTime},
        #{platform},
        #{reserved1},
        #{notifyType}
         )
        <trim prefix="ON DUPLICATE KEY UPDATE" prefixOverrides=",">
            <if test="thirdPartyType !=null">
                ,thirdpartyType = #{thirdPartyType}
            </if>
            <if test="enterpriseID !=null">
                ,enterpriseID = #{enterpriseID}
            </if>
            <if test="enterpriseCode !=null  and enterpriseCode !=''">
                ,enterpriseCode = #{enterpriseCode}
            </if>
            <if test="auditResult !=null">
                ,auditResult = #{auditResult}
            </if>
            <if test="auditTime !=null">
                ,auditTime = #{auditTime}
            </if>
            ,auditDesc = #{auditDesc}
            ,notifyStatus = null
            ,notifyFailTimes = 0
            ,lastnotifyTime = null
            <if test="updateTime !=null">
                ,updateTime = #{updateTime}
            </if>            
        </trim>
    </insert>
    <delete id="deleteContentByID" parameterType="java.lang.Long">
		delete from
		ecpm_t_content where ID=#{id}
	</delete>


    <!-- 根据企业ID、业务类型、业务子类型、计费类型、status为1.暂停查询ecpm_t_content表获得内容信息列表，获得内容对应规则ID -->
    <!-- lwx595992 -->
    <select id="getContentsByID" resultMap="contentWrapper">
        select t.ID ,
        t.contRuleID , t.subServType ,t.approveStatus from
        ecpm_t_content t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servType !=null">
                and t.servType = #{servType}
            </if>
            <if test="chargeType !=null">
                and t.chargeType = #{chargeType}
            </if>
            <if test="subServType !=null">
                and t.subServType = #{subServType}
            </if>
            <if test="status !=null">
                and t.status = #{status}
            </if>
            <if test="approveStatus !=null">
                and t.approveStatus = #{approveStatus}
            </if>
            <if test="subServTypes !=null and subServTypes.size() > 0">
                and t.subServType in
                <foreach collection="subServTypes" item="subServType" open="(" separator="," close=")">
                    #{subServType}
                </foreach>
            </if>
        </trim>
    </select>

    <update id="updateCntStatusByIDs">
        update ecpm_t_content
        set status = #{status},
        updateTime = #{updateTime}
        where ID in
        <foreach collection="ids" item="ID" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </update>

    <update id="updateCntStatusByID">
        update ecpm_t_content
        set status = 1
        where ID = #{id}
    </update>



    <!-- 根据企业ID、业务类型、业务子类型（1主叫）、审批通过、计费类型为按成员查询ecpm_t_content表 -->
    <select id="getMemberTotalBycontent" resultType="java.lang.Integer">
        select count(distinct(orgID)) from ecpm_t_org_rel where orgID in
        (select o.ownerID from ecpm_t_content t , ecpm_t_content_org o
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servType !=null">
                and t.servType = #{servType}
            </if>
            <if test="chargeType !=null">
                and t.chargeType = #{chargeType}
            </if>
            <if test="subServType !=null">
                and t.subServType = #{subServType}
            </if>
            <if test="approveStatus !=null">
                and t.approveStatus = #{approveStatus}
            </if>
            and t.ID = o.cyContID
        </trim>
        )
    </select>


    <select id="queryMemberCountByContentID" resultType="java.lang.Long">
		select
		count(*) as count from ecpm_t_content
		t,ecpm_t_content_org
		org,ecpm_t_org_rel rel,ecpm_t_member
		m,ecpm_t_member_subscribe
		ms,ecpm_t_serv_product p
		where t.ID = #{contID} and t.ID
		= org.cyContID
		and org.ownerId =rel.orgID and rel.id = m.id
		and
		m.id = ms.memberID and
		(ms.status = 3 or ms.status = 5) and ms.productCode =
		p.productCode and p.servType =1
	</select>

    <update id="updateDeductTimeByContID" parameterType="java.util.List">
        update ecpm_t_content t set t.deducttime = now() where t.ID in
        <foreach item="contID" index="index" collection="list" open="(" separator="," close=")">
            #{contID}
        </foreach>
    </update>

    <select id="queryContentListBytEnterpriseIDs" resultMap="contentWrapper">
        select
        t1.contRuleID,
        t1.enterpriseID,
        t1.ID,
        t1.chargeType,
        t1.servType,
        t1.subServType,
        t1.parentID,
        t1.isMonthByQuota,
        t1.contentType,
        t1.hangupType
        from
        ecpm_t_content t1
        where
        t1.enterpriseID in
        <foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="(" separator="," close=")">
            #{enterpriseID}
        </foreach>
        and t1.servType != 2
        UNION ALL
        SELECT 
        NULL AS contRuleID,
        enterpriseID,
        NULL AS ID,
        1 AS chargeType,
        servType,
        subServType,
        NULL AS parentID,
        0 AS isMonthByQuota,
        contentType,
        hangupType
        FROM 
        (
        SELECT
        t.enterpriseID,
        t.servType,
        t.subServType,
        t.hangupType,
        t.contentType
         FROM
        ecpm_t_content t
        WHERE
        t.enterpriseID IN 
		<foreach item="enterpriseID" index="index" collection="enterpriseIDList" open="(" separator="," close=")">
            #{enterpriseID}
        </foreach>
        AND t.servType = 2
        GROUP BY t.enterpriseID, t.servType, t.subServType,t.hangupType,t.contentType) t2
        limit #{pageNum},#{pageSize}
    </select>

    <select id="countContentListBytEnterpriseIDs" resultType="java.lang.Integer" parameterType="java.util.List">
        select count(1) from (select
        t1.contRuleID,
        t1.enterpriseID,
        t1.ID,
        t1.chargeType,
        t1.servType,
        t1.subServType,
        t1.parentID,
        t1.isMonthByQuota,
        t1.hangupType
        from ecpm_t_content t1
        where t1.enterpriseID in
        <foreach item="enterpriseID" index="index" collection="list" open="(" separator="," close=")">
            #{enterpriseID}
        </foreach>
        and t1.servType != 2
        UNION ALL
        SELECT 
        NULL AS contRuleID,
        enterpriseID,
        NULL AS ID,
        1 AS chargeType,
        servType,
        subServType,
        NULL AS parentID,
        0 AS isMonthByQuota,
        hangupType
        FROM 
        (
        SELECT
        t.enterpriseID,
        t.servType,
        t.subServType,
        t.hangupType
         FROM
        ecpm_t_content t
        WHERE
        t.enterpriseID IN 
		<foreach item="enterpriseID" index="index" collection="list" open="(" separator="," close=")">
            #{enterpriseID}
        </foreach>
        AND t.servType = 2
        GROUP BY t.enterpriseID, t.servType, t.subServType, t.hangupType,t.contentType) t2) s
    </select>

    <select id="queryContentByBelongTemplateID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		syncStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		belongTemplateID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		certificateUrl,
		hangupType
		from ecpm_t_content where
		belongTemplateID=#{belongTemplateID}
	</select>

    <update id="updateContentByCyContID">
		update ecpm_t_content
		set contRuleID=#{contRuleID}, thirdpartyType=#{thirdPartyType}, servType=#{servType},
		subServType=#{subServType}, blackwhiteListType=#{blackWhiteListType}, reserved1=#{reserved1},
		reserved3=#{reserved3},contentTitle=#{contentTitle}, chargeType=#{chargeType}, deliveryDate=#{deliveryDate},
		syncStatus=#{syncStatus}, approveIdea=#{approveIdea}, enterpriseID=#{enterpriseID},
		enterpriseName=#{enterpriseName}, enterpriseCode=#{enterpriseCode}, status=#{status},
		sceneDesc=#{sceneDesc}, deliveryType=#{deliveryType}, industryType=#{industryType},
		applyProperty=#{applyProperty}, unicomDelivery=#{unicomDelivery}, scene=#{scene},
		auditTime=#{auditTime}, deductTime=#{deductTime}, operatorID=#{operatorID},
		content=#{content}, approveStatus=#{approveStatus},
		updateTime=#{updateTime},certificateUrl=#{certificateUrl}
		<if test="reserved7 != null">
            ,reserved7 = #{reserved7}
        </if>
		<if test="reserved9 != null">
            ,reserved9 = #{reserved9}
        </if>
		<if test="reserved10 != null">
            ,reserved10 = #{reserved10}
        </if>
        <if test="unicomApproveStatus != null">
            ,unicomApproveStatus=#{unicomApproveStatus}
        </if>
        <if test="telecomApproveStatus != null">
            ,telecomApproveStatus=#{telecomApproveStatus}
        </if>
        <if test="mobileApproveStatus != null">
            ,mobileApproveStatus=#{mobileApproveStatus}
        </if>
		,enterpriseType=#{enterpriseType} 
		where id=#{id}
	</update>

    <select id="countMemberNumberByContId" resultType="java.lang.Long">
        select count(t2.msisdn) from ecpm_t_org_rel
        t1,ecpm_t_member_subscribe t2,ecpm_t_serv_product t3
        where t1.orgID in
        <foreach item="orgID" index="index" collection="ownerIDList" open="(" separator="," close=")">
            #{orgID}
        </foreach>
        and t2.status in (1,3,5)
        and t1.id = t2.memberID
        and t2.productCode = t3.productCode
        <if test="servType != null">
            and t3.servType = #{servType}
        </if>
    </select>

    <select id="queryMemberIDList" resultType="java.lang.Long">
        select t2.memberID
        from ecpm_t_org_rel t1,
        ecpm_t_member_subscribe t2,
        ecpm_t_serv_product t3
        where t1.orgID in
        <foreach item="orgID" index="index" collection="ownerIDList" open="(" separator="," close=")">
            #{orgID}
        </foreach>
        and t2.status not in (2, 4)
        and t1.id = t2.memberID
        and t2.productCode = t3.productCode
        <if test="servType != null">
            and t3.servType = #{servType}
        </if>
    </select>


    <select id="queryContentForServiceRuleCancel" resultMap="contentWrapper">
        SELECT
        	t.ID,
        	t.contCode,
        	t.contRuleID,
        	t.thirdpartyType,
        	t.servType,
        	t.subServType,
        	t.blackwhiteListType,
        	t.content,
        	t.contentType,
        	t.contentTitle,
        	t.chargeType,
        	t.deliveryDate,
        	t.approveStatus,
        	t.syncStatus,
        	t.approveIdea,
        	t.enterpriseID,
        	t.enterpriseName,
        	t.enterpriseCode,
        	t.status,
        	t.sceneDesc,
        	t.deliveryType,
        	t.industryType,
        	t.applyProperty,
        	t.unicomDelivery,
        	t.scene,
        	t.frequencyID,
        	t.statID,
        	t.maxPushPerDay,
        	t.pushInterval,
        	t.auditTime,
        	t.deductTime,
        	t.belongTemplateID,
        	t.reserved7,
        	t.unicomApproveStatus,
        	t.unicomApproveIdea,
        	t.unicomApproveTime,
        	t.telecomApproveStatus,
        	t.telecomApproveIdea,
        	t.telecomApproveTime,
        	t.mobileApproveStatus,
			t.mobileApproveIdea,
			t.mobileApproveTime,
			t.hangupType
        from
        	ecpm_t_content t
        	left join ecpm_t_content a on t.belongTemplateID = a.ID
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servType != null">
                and t.servType = #{servType}
            </if>
            <if test="subServType != null and subServType != 3">
                and t.subServType = #{subServType}
	            and t.parentID is null
	            and a.parentID is null
            </if>
            <if test="subServType != null and subServType == 3">
            	and ( 
            		t.subServType = 3
            		or (
            			t.subServType in (1, 2)
			            and (
			            	t.parentID is not null or a.parentID is not null
			            )
            		)
            	)
            </if>
            and (t.contentType = 1 or t.contentType = 3)
            and (t.status is null or t.status != 2)
        </trim>
    </select>

    <select id="queryContentForServiceRuleRenew" resultMap="contentWrapper">
        SELECT
        	t.ID,
        	t.contCode,
        	t.contRuleID,
        	t.thirdpartyType,
        	t.servType,
        	t.subServType,
        	t.blackwhiteListType,
        	t.content,
        	t.contentType,
        	t.contentTitle,
        	t.chargeType,
        	t.deliveryDate,
        	t.approveStatus,
        	t.syncStatus,
        	t.approveIdea,
        	t.enterpriseID,
        	t.enterpriseName,
        	t.enterpriseCode,
        	t.status,
        	t.sceneDesc,
        	t.deliveryType,
        	t.industryType,
        	t.applyProperty,
        	t.unicomDelivery,
        	t.scene,
        	t.frequencyID,
        	t.statID,
        	t.maxPushPerDay,
        	t.pushInterval,
        	t.auditTime,
        	t.deductTime,
        	t.belongTemplateID,
        	t.reserved7,
        	t.unicomApproveStatus,
        	t.unicomApproveIdea,
        	t.unicomApproveTime,
        	t.telecomApproveStatus,
        	t.telecomApproveIdea,
        	t.telecomApproveTime,
        	t.mobileApproveStatus,
			t.mobileApproveIdea,
			t.mobileApproveTime,
			t.hangupType
        from
        	ecpm_t_content t
        left join ecpm_t_content a on t.belongTemplateID = a.ID
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servType != null">
                and t.servType = #{servType}
            </if>
            <if test="subServType != null and subServType != 3">
                and t.subServType = #{subServType}
	            and t.parentID is null
	            and a.parentID is null
            </if>
            <if test="subServType != null and subServType == 3">
            	and ( 
            		t.subServType = 3
            		or (
            			t.subServType in (1, 2)
			            and (
			            	t.parentID is not null or a.parentID is not null
			            )
            		)
            	)
            </if>
            and t.status = 1
            and (t.contentType = 1 or t.contentType = 3)
        </trim>
    </select>

    <select id="queryContentForDelEnerviceRule" resultMap="contentWrapper">
        SELECT
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentType,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        syncStatus,
        approveIdea,
        enterpriseID,
        enterpriseName,
        enterpriseCode,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        auditTime,
        deductTime,
        belongTemplateID,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
        from
        ecpm_t_content t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servType !=null">
                and t.servType = #{servType}
            </if>
            <if test="subServType !=null">
                and t.subServType = #{subServType}
            </if>
            and (t.status = 0 or t.status is null)
            and (t.approveStatus = 3 or
            t.approveStatus = 2)
        </trim>
    </select>

    <select id="queryContentByStatusAndActivityID" resultMap="contentWrapper">
        select
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        approveIdea,
        enterpriseID,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved7,
        createTime,
        updateTime,
        operatorID,
        syncStatus,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
        from
        ecpm_t_content
        where 1 = 1
        <if test="status != null">
            and STATUS = #{status}
        </if>
        and ID in
        (select contentID
        from
        ecpm_t_activity_contentrel where activityID =
        #{activityID})
    </select>
 
    <update id="batchUpdateContentList">
        <foreach collection="list" item="contentWrapper" separator=";">
            update ecpm_t_content set
            approveStatus=#{contentWrapper.approveStatus}
            ,mobileApproveStatus=#{contentWrapper.approveStatus}
            ,approveIdea=#{contentWrapper.approveIdea}
            ,updateTime=#{contentWrapper.updateTime}
            ,auditTime=#{contentWrapper.auditTime}
            ,contCode = null,
            unicomApproveStatus=#{contentWrapper.unicomApproveStatus},
            telecomApproveStatus=#{contentWrapper.telecomApproveStatus},
            unicomApproveIdea=#{contentWrapper.unicomApproveIdea},
            telecomApproveIdea=#{contentWrapper.telecomApproveIdea}
            where ID =
            #{contentWrapper.id}
        </foreach>
    </update>

	<update id="batchUpdateApproveStatus">
        <foreach collection="list" item="contentWrapper" separator=";">
            update ecpm_t_content set
            unicomApproveStatus=#{contentWrapper.unicomApproveStatus},telecomApproveStatus=#{contentWrapper.telecomApproveStatus},updateTime=#{contentWrapper.updateTime}
            where ID =
            #{contentWrapper.id}
        </foreach>
    </update>
    
    <update id="updateContentDeductTimeByContID">
		update ecpm_t_content t set t.deducttime = null where
		t.ID = #{contID}
	</update>

    <select id="queryContRuleID" resultMap="contentWrapper">
        SELECT
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentType,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        approveIdea,
        enterpriseID,
        enterpriseName,
        enterpriseCode,
        enterpriseType,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        parentID,
        createTime,
        updateTime,
        operatorID,
        syncStatus,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        auditTime,
        deductTime,
        operateCode,
        instruct,
        replyParentID,
        replyApproveStatus,
        pkgID,
        pkgName,
        pkgPrice,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		isMonthByQuota,
		hangupType
        FROM ecpm_t_content t
        where 1 = 1
        <if test="status != null">
            and t.status = #{status}
        </if>
        <if test="enterpriseID != null">
            and t.enterpriseID = #{enterpriseID}
        </if>
        <if test="servType !=null">
            and t.servType = #{servType}
        </if>
        <if test="list !=null and list.size() > 0">
            and t.subServType in
            <foreach item="subServType" index="index" collection="list" open="(" separator="," close=")">
                #{subServType}
            </foreach>
        </if>
        <if test="statusList !=null and statusList.size() > 0">
            and t.status in
            <foreach item="status" index="index" collection="statusList" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="chargeType !=null">
            and t.chargeType = #{chargeType}
        </if>
        <if test="isMonthByQuota != null">
            and t.isMonthByQuota = #{isMonthByQuota}
        </if>
    </select>

    <update id="updateStatusToEnable">
        update ecpm_t_content t set
        t.status=#{status},t.updateTime=#{now}
        where t.ID in
        <foreach item="ID" index="index" collection="list" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </update>

    <select id="queryContentIDByContRuleID" resultType="java.lang.Long" parameterType="java.lang.String">
        select
        t.ID
        FROM ecpm_t_content t
        where t.status = 1
        <if test="contRuleID != null and contRuleID !=''">
            and t.contRuleID = #{contRuleID}
        </if>
    </select>

    <select id="queryContentID" resultType="java.lang.Long">
        select contentID from ecpm_t_activity_contentrel t where t.activityID
        in
        (
        select t2.ID from ecpm_t_activity t2 where t2.enterpriseID
        =#{enterpriseID}

        <if test="isPrematureTerminatedCon != null">
            and t2.isPrematureTerminated =
            #{isPrematureTerminatedCon}
        </if>

        AND t2.ID IN (
        SELECT ID FROM ( SELECT t1.ID FROM ecpm_t_activity t1 ) AS temp )
        )
    </select>

    <select id="searchEnterpriseServType" resultMap="contentWrapper" parameterType="java.lang.Integer">
		SELECT
		t.enterpriseID,
		t.servType,
		t.subServType,
		t.chargeType,
		t.reserved7,
		t.hangupType,
		e.enterpriseType
		FROM ecpm_t_content t
		LEFT JOIN ecpm_t_enterprise_simple e ON t.enterpriseID=e.id
		WHERE  t.approveStatus=3
		AND t.chargeType=1
		AND (t.servType=1 OR t.servtype=2 OR t.servtype=5)
		AND t.subServtype IS NOT NULL
		AND t.status=#{status}
        and t.contenttype != 6
		GROUP BY t.enterpriseID,t.servType,t.subServType,t.reserved7,t.hangupType
	</select>

    <select id="queryContentForServiceRulePause" resultMap="contentWrapper">
        SELECT
	        t.ID,
	        t.contCode,
	        t.contRuleID,
	        t.thirdpartyType,
	        t.servType,
	        t.subServType,
	        t.blackwhiteListType,
	        t.content,
	        t.contentType,
	        t.contentTitle,
	        t.chargeType,
	        t.deliveryDate,
	        t.approveStatus,
	        t.syncStatus,
	        t.approveIdea,
	        t.enterpriseID,
	        t.enterpriseName,
	        t.enterpriseCode,
	        t.status,
	        t.sceneDesc,
	        t.deliveryType,
	        t.industryType,
	        t.applyProperty,
	        t.unicomDelivery,
	        t.scene,
	        t.frequencyID,
	        t.statID,
	        t.maxPushPerDay,
	        t.pushInterval,
	        t.auditTime,
	        t.deductTime,
	        t.belongTemplateID,
	        t.reserved7,
	        t.unicomApproveStatus,
	        t.unicomApproveIdea,
	        t.unicomApproveTime,
	        t.telecomApproveStatus,
	        t.telecomApproveIdea,
	        t.telecomApproveTime,
        	t.mobileApproveStatus,
			t.mobileApproveIdea,
			t.mobileApproveTime,
			t.hangupType
        from
        	ecpm_t_content t
        	left join ecpm_t_content a on t.belongTemplateID = a.ID
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servType != null">
                and t.servType = #{servType}
            </if>
            <if test="subServType != null and subServType != 3">
                and t.subServType = #{subServType}
	            and t.parentID is null
	            and a.parentID is null
            </if>
            <if test="subServType != null and subServType == 3">
            	and ( 
            		t.subServType = 3
            		or (
            			t.subServType in (1, 2)
			            and (
			            	t.parentID is not null or a.parentID is not null
			            )
            		)
            	)
            </if>
            and (t.status = 0 or t.status is null)
            and (t.contentType = 1 or t.contentType = 3)
            and t.approveStatus in(1,2,3,4)
        </trim>
    </select>

    <select id="queryContentForDefaultFrequency" resultMap="contentWrapper">
        SELECT
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentType,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        syncStatus,
        approveIdea,
        enterpriseID,
        enterpriseName,
        enterpriseCode,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        auditTime,
        deductTime,
        belongTemplateID,
        reserved7,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
        from
        ecpm_t_content t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="servType !=null">
                and t.servType = #{servType}
            </if>
            and t.status = 0
            and
            (t.contentType = 1 or t.contentType = 3)
            and t.approveStatus=3
        </trim>
    </select>

    <update id="updateContentReservedByContID">
		update ecpm_t_content t set t.reserved1 = #{reserved1}, t.deductTime = #{deductTime}, t.operateCode = #{operateCode} where
		t.ID = #{id}
	</update>

    <update id="updateContentByEnterpriseID">
        update ecpm_t_content t set t.reserved4 =
        CASE t.enterpriseID
        <foreach collection="list" item="item" index="index" open="" close="">
            WHEN #{item.enterpriseID} THEN #{item.reserved4}
        </foreach>
        END,
        t.reserved5 =
        CASE t.enterpriseID
        <foreach collection="list" item="item" index="index" open="" close="">
            WHEN #{item.enterpriseID} THEN #{item.reserved5}
        </foreach>
        END
        where t.enterpriseID in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.enterpriseID}
        </foreach>
    </update>

    <select id="getRuleIds" resultType="java.lang.String">
        select t.contRuleID  from ecpm_t_content t, ecpm_t_content_org t1
        where t.ID=t1.cyContID
        and t.approveStatus='3'
        and t1.msisdn in
        <foreach collection="list" item="msisdn" index="index" open="(" close=")" separator=",">
            #{msisdn}
        </foreach>
    </select>

    <update id="updateEnterpriseName">
		update ecpm_t_content t
		set t.updateTime = #{now},
		t.enterpriseName = #{enterpriseName}
		where
		t.enterpriseID = #{enterpriseID}
	</update>

    <select id="queryContentListBytEnterpriseID" resultMap="contentWrapper">
		select
			t1.ID,
			t1.contCode,
			t1.contRuleID,
			t1.servType,
			t1.subServType,
			t1.blackwhiteListType,
			t1.content,
			t1.contentTitle,
			t1.enterpriseID,
			t1.enterpriseName,
			t1.enterpriseCode,
			t1.chargeType,
			t1.deliveryDate,
			t1.industrytype,
			t1.applyproperty,
			t1.statID,
			t1.frequencyID,
			t1.maxPushPerDay,
			t1.pushInterval,
			t1.reserved7,
			t1.status,
			t1.isMonthByQuota,
			t1.hangupType,
            t1.contentType
		from ecpm_t_content t1
		where
			t1.enterpriseID = #{enterpriseID}
		and t1.approveStatus=3
		and (t1.status = 0 or (t1.status in (0,3) and t1.subServType in (34,35, 36)))
	    and t1.servType in(1,5) and t1.contentType != 2

		UNION

		select
			t2.ID,
			t2.contCode,
			t2.contRuleID,
			t2.servType,
			t2.subServType,
			t2.blackwhiteListType,
			t2.content,
			t2.contentTitle,
			t2.enterpriseID,
			t2.enterpriseName,
			t2.enterpriseCode,
			t2.chargeType,
			t2.deliveryDate,
			t2.industrytype,
			t2.applyproperty,
			t2.statID,
			t2.frequencyID,
			t2.maxPushPerDay,
			t2.pushInterval,
			t2.reserved7,
			t2.status,
			t2.isMonthByQuota,
			t2.hangupType,
            t2.contentType
		from ecpm_t_content t2  left join ecpm_t_activity_contentrel t3 on t2.ID = t3.contentID
		left join ecpm_t_activity t4 on t3.activityID = t4.ID
		where
			t2.enterpriseID = #{enterpriseID}
		and t2.approveStatus=3
		and t2.status = 0
		and t2.servType =3
		and t4.effectivetime <![CDATA[ < ]]>
			now()
		and t4.expiretime <![CDATA[ > ]]>
			now()
	</select>

    <update id="updateContentControlByEnterpriseID">
		update ecpm_t_content t
		set t.reserved7 = #{reserved7}
		where
		    t.id=#{id}
		and	t.enterpriseID = #{enterpriseID}
	</update>


    <select id="selectSubByContID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		ID,
		servType,
		subServType,
		contentType,
		chargeType,
		enterpriseID,
		operatorID,
		pkgID,
		pkgName,
		pkgPrice
		from ecpm_t_content
		where ID=#{contID}
	</select>
    <select id="queryContentByContRuleID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        select
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        approveIdea,
        enterpriseID,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved7,
        parentID,
        createTime,
        updateTime,
        operatorID,
        syncStatus,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
        mobileApproveIdea,
        mobileApproveTime,
        isMonthByQuota,
        hangupType
        FROM ecpm_t_content
        where 1 = 1
        <if test="contRuleID != null and contRuleID !=''">
            and contRuleID = #{contRuleID}
        </if>
    </select>

	<select id="queryContentByRuleID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        select  * 
        FROM ecpm_t_content
        where 1 = 1
        and contRuleID = #{contRuleID}
    </select>
    
    <select id="queryContentByRuleIDList" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        select  * 
        FROM ecpm_t_content
        where enterpriseType = 3
        and servType = 1
    </select>
    
    <!--根据父级id查询主叫或被叫的内容实体-->
    <select id="getScreenContentWrapper" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        SELECT
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentType,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        syncStatus,
        approveIdea,
        enterpriseID,
        enterpriseName,
        enterpriseCode,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        auditTime,
        deductTime,
        belongTemplateID,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        parentID,
        createTime,
        updateTime,
        operatorID,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
        mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
        from ecpm_t_content
        WHERE (status != 2 or status is null)
        AND parentID = #{cyContId}
        AND servType = #{servType}
        AND subServType = #{subServType}
    </select>
    <select id="queryContentByEnterpriseIDs" resultMap="contentWrapper" parameterType="java.util.Map">
        SELECT
	        ID,
	        contCode,
	        contRuleID,
	        thirdpartyType,
	        servType,
	        subServType,
	        blackwhiteListType,
	        content,
	        contentTitle,
	        chargeType,
	        deliveryDate,
	        approveStatus,
	        approveIdea,
	        enterpriseID,
	        status,
	        sceneDesc,
	        deliveryType,
	        industryType,
	        applyProperty,
	        unicomDelivery,
	        scene,
	        extInfo,
	        reserved1,
	        reserved2,
	        reserved3,
	        reserved4,
	        reserved7,
	        createTime,
	        updateTime,
	        operatorID,
	        syncStatus,
	        deductTime,
            unicomApproveStatus,
            unicomApproveIdea,
            unicomApproveTime,
            telecomApproveStatus,
            telecomApproveIdea,
            telecomApproveTime,
        	mobileApproveStatus,
			mobileApproveIdea,
			mobileApproveTime,
			hangupType
        from ecpm_t_content
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseIDs !=null and enterpriseIDs.size() > 0">
                and enterpriseID in
                <foreach item="enterpriseID" index="index" collection="enterpriseIDs" open="(" separator="," close=")">
                    #{enterpriseID}
                </foreach>
            </if>
            <if test="servType != null">
                and servType = #{servType}
            </if>
            <if test="notFilter == null">
            	and syncStatus =6 and approveStatus = 3
            </if>
        </trim>
    </select>

    <select id="queryContentListByIDs" resultMap="contentWrapper" parameterType="java.util.Map">
        SELECT
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        approveIdea,
        enterpriseID,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved7,
        createTime,
        updateTime,
        operatorID,
        syncStatus,
        deductTime,
        unicomApproveStatus,
        unicomApproveIdea,
        unicomApproveTime,
        telecomApproveStatus,
        telecomApproveIdea,
        telecomApproveTime,
        mobileApproveStatus,
        mobileApproveIdea,
        mobileApproveTime,
        hangupType
        from ecpm_t_content
        <trim prefix="where" prefixOverrides="and|or">
            <if test="ids !=null and ids.size() > 0">
                and ID in
                <foreach item="ID" index="index" collection="ids" open="(" separator="," close=")">
                    #{ID}
                </foreach>
            </if>
        </trim>
        limit 0, #{limit}
    </select>

    <select id="queryContentByOrg" resultMap="contentWrapper">
        SELECT
        t1.ID,
        t1.deductTime,
        t2.orgCode,
        t2.ownerID,
        t1.contCode,
        t1.servType,
        t1.enterpriseID,
        if(t1.subServType = 1 and t1.parentID is not null,3,t1.subServType) subServType,
        t1.chargeType,
        t1.approveStatus,
        t1.parentID,
        t1.pkgID,
		t1.pkgName,
		t1.pkgPrice
        from
        ecpm_t_content t1,ecpm_t_content_org t2
        where
        t1.ID=t2.cyContID
        and t1.chargeType = #{chargeType}
        and t2.orgCode is not NULL
        and t2.ownerID=#{orgID}
        <if test="enterpriseID !=null">
            and t1.enterpriseID
            = #{enterpriseID}
        </if>
        and (t1.subServType = 1 or t1.parentID is null)
    </select>
    <select id="queryContentByOrgId" resultMap="contentWrapper">
        SELECT
        t1.ID,
        t1.deductTime,
        t2.orgCode,
        t2.ownerID,
        t1.contCode,
        t1.servType,
        t1.enterpriseID,
        if(t1.subServType = 1 and t1.parentID is not null,3,t1.subServType) subServType,
        t1.chargeType,
        t1.approveStatus,
        t1.parentID,
        t1.pkgID,
		t1.pkgName,
		t1.pkgPrice
        from
        ecpm_t_content t1,ecpm_t_content_org t2
        where
        t1.ID=t2.cyContID
        and t1.chargeType = #{chargeType}
        and t1.approveStatus in (2,3)
        and (t1.status in (0,1,3) or t1.status is null)
        and t2.ownerID=#{orgID}
        and (t1.subServType = 1 or t1.parentID is null)
    </select>
    <select id= "countMemberNumber" resultType="java.lang.Integer">
    	SELECT COUNT(1) FROM `ecpm_t_member_subscribe` s
		WHERE s.memberID IN
		(SELECT o.ID FROM `ecpm_t_org_rel` o
		WHERE o.orgID IN (SELECT c.ownerID  FROM `ecpm_t_content_org` c
		WHERE c.cyContID = #{contid}))
    </select>
        <select id="queryContentbyoRGid" resultMap="contentWrapper">
		SELECT
		t.ID,
		t.contCode,
		t.contRuleID,
		t.thirdpartyType,
		t.servType,
		t.subServType,
		t.blackwhiteListType,
		t.content,
		t.contentTitle,
		t.chargeType,
		t.deliveryDate,
		t.approveStatus,
		t.approveIdea,
		t.enterpriseID,
		t.status,
		t.sceneDesc,
		t.deliveryType,
		t.industryType,
		t.applyProperty,
		t.unicomDelivery,
		t.scene,
		t.extInfo,
		t.reserved1,
		t.reserved2,
		t.reserved3,
		t.reserved4,
		t.reserved7,
		t.createTime,
		t.auditTime,
		t.updateTime,
		t.operatorID,
		t.syncStatus,
		t.statID,
		t.pkgID,
		t.pkgName,
		t.pkgPrice,
		t.unicomApproveStatus,
		t.unicomApproveIdea,
		t.unicomApproveTime,
		t.telecomApproveStatus,
		t.telecomApproveIdea,
		t.telecomApproveTime,
		t.mobileApproveStatus,
		t.mobileApproveIdea,
		t.mobileApproveTime,
		t.parentID,
		t.isMonthByQuota
		from ecpm_t_content t , ecpm_t_content_org  g
        where t.ID=g.cyContID and g.ownerID=#{orgID} and g.ownerType IN (1,3);
	</select>
	<select id="getContentByEnterpriseIDs" resultMap="contentWrapper" parameterType="java.util.Map">
        SELECT
	        ID,
	        contCode,
	        contRuleID,
	        thirdpartyType,
	        servType,
	        subServType,
	        blackwhiteListType,
	        content,
	        contentTitle,
            if(chargeType is null,1,chargeType) chargeType,
	        deliveryDate,
	        approveStatus,
	        approveIdea,
	        enterpriseID,
	        status,
	        sceneDesc,
	        deliveryType,
	        industryType,
	        applyProperty,
	        unicomDelivery,
	        scene,
	        extInfo,
	        reserved1,
	        reserved2,
	        reserved3,
	        reserved4,
	        reserved7,
	        createTime,
	        updateTime,
	        operatorID,
	        syncStatus,
	        deductTime,
            parentID,
            isMonthByQuota,
            hangupType
        from ecpm_t_content
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseIDs !=null and enterpriseIDs.size() > 0">
                and enterpriseID in
                <foreach item="enterpriseID" index="index" collection="enterpriseIDs" open="(" separator="," close=")">
                    #{enterpriseID}
                </foreach>
            </if>
            <if test="status != null and status ==0">
                and (status = 0 or status is null)
            </if>
             <if test="status != null and status ==1">
                and status = 1
            </if>
            <if test="servType != null">
                and servType =  #{servType}
            </if>
            and servType not in (2,4)
        </trim>
    </select>
     <select id="queryContentByEnterpriseCode" resultMap="contentWrapper">
        SELECT
        t.id,t.contCode,t.contRuleID,t.thirdpartyType,t.servType,t.subServType,t.blackwhiteListType,
        t.content,t.contentTitle,t.chargeType,t.deliveryDate,t.approveStatus,t.syncStatus,t.approveIdea,t.enterpriseID,t.enterpriseName,t.enterpriseCode,t.status,t.sceneDesc,
        t.deliveryType,t.industryType,t.applyProperty,t.unicomDelivery,t.scene,t.frequencyID,t.statID,t.extInfo,t.reserved1,t.reserved2,t.reserved3,
        t.reserved4,t.reserved7,t.reserved9,t.reserved10,t.createTime,t.updateTime,t.operatorID,t.maxPushPerDay,t.pushInterval,t.auditTime,t.deductTime,t.contentType,t.auditor,t.enterpriseType,        
        t.instruct,t.replyParentID,t.replyApproveStatus,t.pkgID,t.pkgName,t.pkgPrice,
        t.unicomApproveStatus,t.unicomApproveIdea,t.unicomApproveTime,t.telecomApproveStatus,t.telecomApproveIdea,t.telecomApproveTime,
        t.mobileApproveStatus,
		t.mobileApproveIdea,
		t.mobileApproveTime,
		t.hangupType
        FROM ecpm_t_content t
        <trim prefix="where" prefixOverrides="and|or">
            <if test="enterpriseCode != null">
                and t.enterpriseCode = #{enterpriseCode}
            </if>
            <if test="approveStatus !=null">
                and t.approveStatus = #{approveStatus}
            </if>
             <if test="enterpriseType !=null">
                and t.enterpriseType = #{enterpriseType}
            </if>
            and (t.status != 2 or t.status is null)
        </trim>
    </select>
    <!--根据父级id查询子内容实体-->
    <select id="getContentWrapperByParentID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        SELECT
        ID,
        contCode,
        contRuleID,
        thirdpartyType,
        servType,
        subServType,
        blackwhiteListType,
        content,
        contentType,
        contentTitle,
        chargeType,
        deliveryDate,
        approveStatus,
        syncStatus,
        approveIdea,
        enterpriseID,
        enterpriseName,
        enterpriseCode,
        status,
        sceneDesc,
        deliveryType,
        industryType,
        applyProperty,
        unicomDelivery,
        scene,
        frequencyID,
        statID,
        maxPushPerDay,
        pushInterval,
        auditTime,
        deductTime,
        belongTemplateID,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
        parentID,
        createTime,
        updateTime,
        operatorID,
        branchType,
        hangupType
        from ecpm_t_content
        WHERE (status != 2 or status is null) and contentType != 3
        AND parentID = #{cotnentId}
    </select>
    
    <select id="queryContentByTempList" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		syncStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		belongTemplateID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		createTime,
		updateTime,
		operatorID,
		hangupType
		from ecpm_t_content where
		<if test="belongTemplateIDList !=null and belongTemplateIDList.size > 0">
                belongTemplateID  in
                <foreach collection="belongTemplateIDList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
       </if>
	</select>
	
	<delete id="deleteContentByIDList" parameterType="java.lang.Long">
		update ecpm_t_content set
		status=2,updateTime=sysdate() where 
		<if test="contentIdList !=null and contentIdList.size > 0">
                id  in
                <foreach collection="contentIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
       </if>
	</delete>
	
	<select id="queryTempContent" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.TempContentWrapper">
		SELECT
		c.ID,
		c.belongTemplateID,
		c.status,
		c.content,
		o.msisdn,
		c.subServType,
		c.approveStatus,
		c.mobileApproveStatus,
		c.unicomApproveStatus,
		c.telecomApproveStatus
		from ecpm_t_content c, ecpm_t_content_org o where
		c.id = o.cyContID 
		<if test="belongTemplateIDList !=null and belongTemplateIDList.size > 0">
                and belongTemplateID  in
                <foreach collection="belongTemplateIDList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
       </if>
	</select>
    <select id="querySynServiceRule" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SynBusinessServiceRuleWrapper">
           SELECT
                c.enterpriseID,
				e.enterpriseCode,
                c.enterpriseName,
                CASE
                    e.reserved10
                    WHEN 111 THEN
                    15
                    WHEN 112 THEN
                    25
                    ELSE e.enterpriseType
                END enterpriseType,
                fe.id fatherEnterpriseID,
				fe.enterpriseCode fatherEnterpriseCode,
                fe.enterpriseName fatherEnterpriseName,
                c.servType,
                c.contRuleID,
                c.chargeType,
                c.STATUS,
                c.approveStatus,
                c.createTime,
                c.contCode,
            IF 
            	(c.subServType = 4 AND c.hangupType=1, 14, c.subServType) subServType,
            IF
                ( fe.provinceID IS NULL, e.provinceID, fe.provinceID ) provinceID,
            IF
                ( fcity.proviceName IS NULL, city.proviceName, fcity.proviceName ) proviceName,
            IF
                ( fe.cityID IS NULL, e.cityID, fe.cityID ) cityID,
            IF
                ( fcity.cityName IS NULL, city.cityName, fcity.cityName ) cityName,
                r.chargeMsisdn
            FROM
                ecpm_t_content c
                LEFT JOIN ecpm_t_sync_service_rule r ON r.enterpriseID = c.enterpriseID
                AND r.servType = c.servType
                AND ((c.parentID IS NULL AND r.subServType = c.subServType) OR (c.parentID IS NOT NULL AND c.subServType IN(1,2) AND r.subServType=3))
                LEFT JOIN ecpm_t_enterprise_simple e ON c.enterpriseID = e.id
                LEFT JOIN ecpm_t_province_city city ON city.provinceID = e.provinceID
                AND city.cityID = e.cityID
                LEFT JOIN ecpm_t_enterprise_simple fe ON fe.id = e.parentEnterpriseID
                LEFT JOIN ecpm_t_province_city fcity ON fcity.provinceID = fe.provinceID
                AND fcity.cityID = fe.cityID
            WHERE
                c.servType IN ( 1, 3, 5 )  and c.contentType != 6
            GROUP BY
                c.enterpriseID,
								c.enterpriseCode,
                c.enterpriseName,
                c.enterpriseType,
                fatherEnterpriseID,
								fatherEnterpriseCode,
                fatherEnterpriseName,
                c.servType,
                c.contRuleID,
                c.chargeType,
                c.STATUS,
                c.approveStatus,
                c.createTime,
                c.contCode,
                c.subServType,
                c.hangupType,
                provinceID,
                proviceName,
                cityID,
                cityName,
                r.chargeMsisdn
    </select>
	<select id="queryTemplateVarContent" parameterType="java.util.Map" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.TemplateVarContentWrapper">
        SELECT
			tc.id templateContentID,
			vc.id valContentID,
			co.msisdn,
			vc.servType,
			vc.subServType,
			tc.content templateText,
			vc.content valText,
			vc.status,
			vc.approveStatus,
			vc.mobileApproveStatus,
			vc.unicomApproveStatus,
			vc.telecomApproveStatus,
			vc.mobileApproveIdea,
			vc.approveIdea,
			tc.parentID,
			tc.belongTemplateID,
			tc.refYsmbID,
			tc.oriContentID,
			tc.enterpriseType,
			tc.enterpriseID,
        co.provinceCode,
        co.provinceName,
        co.cityCode,
        co.cityName
		FROM
			ecpm_t_content tc
			JOIN ecpm_t_content vc ON vc.contentType = 3 
			AND vc.belongTemplateID = tc.id
			JOIN ecpm_t_content_org co ON co.cyContID = vc.id 
		WHERE
			tc.contentType = 2 
			AND ((tc.subServType <![CDATA[<>]]> 3 AND tc.id = #{id}) OR tc.parentID = #{id})
	        <if test="msisdn != null">
	        	AND INSTR(co.msisdn, #{msisdn})
	        </if>
        <if test="approveStatus != null">
            AND  vc.mobileApproveStatus = #{approveStatus}
        </if>

		ORDER BY vc.createTime DESC
        limit #{startIndex}, #{pageSize}
    </select>
	
	<select id="queryTemplateVarContentCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT
			count(1)
		FROM
			ecpm_t_content tc
			JOIN ecpm_t_content vc ON vc.contentType = 3 
			AND vc.belongTemplateID = tc.id
			JOIN ecpm_t_content_org co ON co.cyContID = vc.id 
		WHERE
			tc.contentType = 2 
			AND ((tc.subServType <![CDATA[<>]]> 3 AND tc.id = #{id}) OR tc.parentID = #{id})
	        <if test="msisdn != null">
	        	AND INSTR(co.msisdn, #{msisdn})
	        </if>
        <if test="approveStatus != null">
            AND vc.mobileApproveStatus = #{approveStatus}
        </if>
    </select>
    
    <select id="queryContetInfoForH5" resultMap="contentWrapper">
        SELECT
        t.id,t.contCode,t.contRuleID,t.thirdpartyType,t.servType,t.subServType,t.blackwhiteListType,
        t.content,t.contentTitle,t.chargeType,t.deliveryDate,t.approveStatus,t.syncStatus,t.approveIdea,t.enterpriseID,t.enterpriseName,t.enterpriseCode,t.status,t.sceneDesc,
        t.deliveryType,t.industryType,t.applyProperty,t.unicomDelivery,t.scene,t.frequencyID,t.statID,t.extInfo,t.reserved1,t.reserved2,t.reserved3,
        t.reserved4,t.reserved7,t.reserved9,t.reserved10,t.parentID,t.createTime,t.updateTime,t.operatorID,t.maxPushPerDay,t.pushInterval,t.auditTime,t.deductTime,t.contentType,t.auditor,t.enterpriseType,        
        t.instruct,t.replyParentID,t.replyApproveStatus,t.pkgID,t.pkgName,t.pkgPrice,t.unicomApproveStatus,t.unicomApproveIdea,t.unicomApproveTime,t.telecomApproveStatus,t.telecomApproveIdea,t.telecomApproveTime,
        t.mobileApproveStatus,
		t.mobileApproveIdea,
		t.mobileApproveTime,t.certificateUrl,t.hangupType
        FROM ecpm_t_content t
        where 
        t.enterpriseCode = #{enterpriseCode}
        and t.servType = #{servType}
        and t.subServType = #{subServType}
        and t.chargeType = #{chargeType}
        and t.enterpriseType = #{enterpriseType}
        and (t.status != 2 or t.status is null)
    </select>

    <select id="queryStripMonthContentOrgByOrgID" resultMap="contentWrapper">
        SELECT a.id,a.subServType,b.orgCode,a.contentType,b.msisdn
        FROM ecpm_t_content a
        LEFT JOIN ecpm_t_content_org b ON a.ID = b.cyContID
        WHERE b.ownerType=1 AND a.isMonthByQuota=1 AND a.approveStatus=3 AND a.status=0 AND b.ownerID = #{id};
    </select>
    <select id="queryStripMonthContentOrgByOrgID2" resultMap="contentWrapper">
        SELECT a.id,a.subServType,b.orgCode,a.contentType,b.msisdn
        FROM ecpm_t_content a
                 LEFT JOIN ecpm_t_content_org b ON a.ID = b.cyContID
        WHERE b.ownerType=1 AND a.isMonthByQuota=1 AND a.approveStatus=3 AND b.ownerID = #{id};
    </select>
    <select id="queryMonquotaDeliveryStat" resultMap="MonquotaDeliveryStatWrapper">
        SELECT a.ID,a.memberID,a.ydpxDeliveryCount,a.ltpxDeliveryCount,a.dxpxDeliveryCount,a.pxDeliveryStatus,
        a.pxDiffDeliveryStatus,a.ydgdDeliveryCount,a.ltgdDeliveryCount,a.dxgdDeliveryCount,a.gdDeliveryStatus,
        a.gdDiffDeliveryStatus,a.ydgcDeliveryCount,a.gcDeliveryStatus,b.msisdn,a.callDeliveryStatus, a.calledDeliveryStatus
        FROM ecpm_t_monquota_delivery_stat a
        LEFT JOIN ecpm_t_member b ON a.memberID = b.ID
        LEFT JOIN ecpm_t_org_rel c ON b.ID = c.ID
        WHERE c.orgID = #{id} AND a.status is null;
    </select>

 	<select id="queryMonquotaByMemberID" resultMap="MonquotaDeliveryStatWrapper">
        SELECT a.ID,a.memberID,a.ydpxDeliveryCount,a.ltpxDeliveryCount,a.dxpxDeliveryCount,a.pxDeliveryStatus,
        a.pxDiffDeliveryStatus,a.ydgdDeliveryCount,a.ltgdDeliveryCount,a.dxgdDeliveryCount,a.gdDeliveryStatus,
        a.gdDiffDeliveryStatus,a.ydgcDeliveryCount,a.gcDeliveryStatus,b.msisdn,a.callDeliveryStatus, a.calledDeliveryStatus
        FROM ecpm_t_monquota_delivery_stat a
        LEFT JOIN ecpm_t_member b ON a.memberID = b.ID
        LEFT JOIN ecpm_t_org_rel c ON b.ID = c.ID
        WHERE a.memberID = #{id} AND a.status is null;
    </select>

    <select id="queryMonquotaByCyContId" resultMap="MonquotaDeliveryStatWrapper">
        SELECT a.ID,a.memberID,a.ydpxDeliveryCount,a.ltpxDeliveryCount,a.dxpxDeliveryCount,a.pxDeliveryStatus,
               a.pxDiffDeliveryStatus,a.ydgdDeliveryCount,a.ltgdDeliveryCount,a.dxgdDeliveryCount,a.gdDeliveryStatus,
               a.gdDiffDeliveryStatus,a.ydgcDeliveryCount,a.gcDeliveryStatus,b.msisdn,a.callDeliveryStatus, a.calledDeliveryStatus
        FROM ecpm_t_monquota_delivery_stat a
                 LEFT JOIN ecpm_t_content_org b ON a.msisdn = b.msisdn
        WHERE b.cycontId = #{id};
    </select>

    <update id="updateDeliveryStatus">
    	<foreach close=";" collection="list" index="index" item="wrapper"
			open="" separator=";">
			update ecpm_t_monquota_delivery_stat set
	        updateTime = now()
	        <if test="wrapper.pxDeliveryStatus != null">
	            ,pxDeliveryStatus = #{wrapper.pxDeliveryStatus}
	        </if>
	        <if test="wrapper.pxDiffDeliveryStatus != null">
	            ,pxDiffDeliveryStatus = #{wrapper.pxDiffDeliveryStatus}
	        </if>
	        <if test="wrapper.gdDeliveryStatus != null">
	            ,gdDeliveryStatus = #{wrapper.gdDeliveryStatus}
	        </if>
	        <if test="wrapper.gdDiffDeliveryStatus != null">
	            ,gdDiffDeliveryStatus = #{wrapper.gdDiffDeliveryStatus}
	        </if>
	        <if test="wrapper.gcDeliveryStatus != null">
	            ,gcDeliveryStatus = #{wrapper.gcDeliveryStatus}
	        </if>
	        <if test="wrapper.callDeliveryStatus != null">
	            ,callDeliveryStatus = #{wrapper.callDeliveryStatus}
	        </if>
	        <if test="wrapper.calledDeliveryStatus != null">
	            ,calledDeliveryStatus = #{wrapper.calledDeliveryStatus}
	        </if>
	        where ID = #{wrapper.id} and status is null
		</foreach>
    </update>

    <update id="updateStatusByEnterprse">
        update ecpm_t_content  force index(idx_content_enid_subtype) set
        status = #{status}
        where enterpriseId = #{enterpriseId}
        and status not in (2,3)
        and servType = #{servType}
        <if test="chargeType != null">
            and chargeType = #{chargeType}
        </if>
        <if test="subServType != null">
            and subServType = #{subServType}
        </if>



    </update>

    <update id="updateRefYsmbContDealStatus">
		update 
			ecpm_t_content 
		set
			dealStatus = #{dealStatus},
			updateTime = #{updateTime} 
		where
			1 = 1
			<if test="id != null">
	            and id = #{id}
	        </if>
			<if test="refYsmbID != null">
	            and (refYsmbID1_vr = #{refYsmbID} or refYsmbID2_vr = #{refYsmbID})
	            and (STATUS <![CDATA[<>]]> 2 or STATUS is null)
	        </if>
	</update>

    <update id="batchUpdateRefYsmbContByID">
		update 
			ecpm_t_content 
		set
			dealStatus = #{dealStatus},
			updateTime = #{updateTime} 
		where
			id in
            <foreach item="id" index="index" collection="contentIds" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
	</update>
	
	<select id="queryRefYsmbContList"  resultMap="contentWrapper">
        SELECT
	        t.id,t.contCode,t.contRuleID,t.thirdpartyType,t.servType,t.subServType,t.blackwhiteListType,
	        t.content,t.contentTitle,t.chargeType,t.deliveryDate,t.approveStatus,t.syncStatus,t.approveIdea,t.enterpriseID,t.enterpriseName,t.enterpriseCode,t.status,t.sceneDesc,
	        t.deliveryType,t.industryType,t.applyProperty,t.unicomDelivery,t.scene,t.frequencyID,t.statID,t.extInfo,t.reserved1,t.reserved2,t.reserved3,
	        t.reserved4,t.reserved7,t.reserved9,t.reserved10,t.parentID,t.createTime,t.updateTime,t.operatorID,t.maxPushPerDay,t.pushInterval,t.auditTime,t.deductTime,t.contentType,t.auditor,t.enterpriseType,        
	        t.instruct,t.replyParentID,t.replyApproveStatus,t.pkgID,t.pkgName,t.pkgPrice,t.unicomApproveStatus,t.unicomApproveIdea,t.unicomApproveTime,t.telecomApproveStatus,t.telecomApproveIdea,t.telecomApproveTime,t.mobileApproveStatus,
		t.mobileApproveIdea,
		t.mobileApproveTime,
	        t.certificateUrl,t.isMonthByQuota,t.batchNo,t.supportEnterpriseType,t.refYsmbID,t.dealStatus
        FROM 
        	ecpm_t_content t
		WHERE 
			t.dealStatus = 0
			and (t.status = 0 or t.status is null)
			and t.approveStatus <![CDATA[<>]]> 2
			and t.mobileApproveStatus <![CDATA[<>]]> 2
			and t.telecomApproveStatus <![CDATA[<>]]> 2
			and t.unicomApproveStatus <![CDATA[<>]]> 2
        ORDER BY updateTime
        LIMIT 0, #{pageSize}
    </select>
	
	<select id="queryYsmbList"  resultMap="contentWrapper">
        SELECT
	        t.id,t.contCode,t.contRuleID,t.thirdpartyType,t.servType,t.subServType,t.blackwhiteListType,
	        t.content,t.contentTitle,t.chargeType,t.deliveryDate,t.approveStatus,t.syncStatus,t.approveIdea,t.enterpriseID,t.enterpriseName,t.enterpriseCode,t.status,t.sceneDesc,
	        t.deliveryType,t.industryType,t.applyProperty,t.unicomDelivery,t.scene,t.frequencyID,t.statID,t.extInfo,t.reserved1,t.reserved2,t.reserved3,
	        t.reserved4,t.reserved7,t.reserved9,t.reserved10,t.parentID,t.createTime,t.updateTime,t.operatorID,t.maxPushPerDay,t.pushInterval,t.auditTime,t.deductTime,t.contentType,t.auditor,t.enterpriseType,        
	        t.instruct,t.replyParentID,t.replyApproveStatus,t.pkgID,t.pkgName,t.pkgPrice,t.unicomApproveStatus,t.unicomApproveIdea,t.unicomApproveTime,t.telecomApproveStatus,t.telecomApproveIdea,t.telecomApproveTime,t.mobileApproveStatus,
		t.mobileApproveIdea,
		t.mobileApproveTime,
	        t.certificateUrl,t.isMonthByQuota,t.batchNo,t.supportEnterpriseType,t.refYsmbID,t.dealStatus
        FROM 
        	ecpm_t_content t
		WHERE 
			id in
            <foreach item="id" index="index" collection="refYsmbIDs" open="(" separator=","
                     close=")">
                #{id}
            </foreach>
			and t.status <![CDATA[<>]]> 2
			and t.approveStatus = 3
    </select>

    <select id="queryOrgRefContent"  resultMap="contentWrapper">
        SELECT
        t.id,t.contCode,t.contRuleID,t.thirdpartyType,t.servType,t.subServType,t.blackwhiteListType,
        t.content,t.contentTitle,t.chargeType,t.deliveryDate,t.approveStatus,t.syncStatus,t.approveIdea,t.enterpriseID,t.enterpriseName,t.enterpriseCode,t.status,t.sceneDesc,
        t.deliveryType,t.industryType,t.applyProperty,t.unicomDelivery,t.scene,t.frequencyID,t.statID,t.extInfo,t.reserved1,t.reserved2,t.reserved3,
        t.reserved4,t.reserved7,t.reserved9,t.reserved10,t.parentID,t.createTime,t.updateTime,t.operatorID,t.maxPushPerDay,t.pushInterval,t.auditTime,t.deductTime,t.contentType,t.auditor,t.enterpriseType,
        t.instruct,t.replyParentID,t.replyApproveStatus,t.pkgID,t.pkgName,t.pkgPrice,t.unicomApproveStatus,t.unicomApproveIdea,t.unicomApproveTime,t.telecomApproveStatus,t.telecomApproveIdea,t.telecomApproveTime,t.mobileApproveStatus,
		t.mobileApproveIdea,
		t.mobileApproveTime,
        t.certificateUrl,t.isMonthByQuota,t.batchNo,t.supportEnterpriseType,t.refYsmbID,t.dealStatus
        FROM ecpm_t_content t WHERE id in  (SELECT * FROM ((
            SELECT
                if(c.parentID is null,c.id ,c.parentID) id
            FROM
                ecpm_t_content_org co
                    LEFT JOIN ecpm_t_content c ON c.id = co.cyContID
            WHERE
                ownerID = #{orgId} and (c.parentID is null or (c.subServType = 1))
              and c.approveStatus = 3
              and Status !=2
              and contentType = 1
                limit #{page.pageNum}, #{page.pageSize})
            ) a
        );
    </select>


    <select id="queryContetByIds"  resultMap="contentWrapper">
        SELECT
	        t.id,t.contCode,t.contRuleID,t.thirdpartyType,t.servType,t.subServType,t.blackwhiteListType,
	        t.content,t.contentTitle,t.chargeType,t.deliveryDate,t.approveStatus,t.syncStatus,t.approveIdea,t.enterpriseID,t.enterpriseName,t.enterpriseCode,t.status,t.sceneDesc,
	        t.deliveryType,t.industryType,t.applyProperty,t.unicomDelivery,t.scene,t.frequencyID,t.statID,t.extInfo,t.reserved1,t.reserved2,t.reserved3,
	        t.reserved4,t.reserved7,t.reserved9,t.reserved10,t.parentID,t.createTime,t.updateTime,t.operatorID,t.maxPushPerDay,t.pushInterval,t.auditTime,t.deductTime,t.contentType,t.auditor,t.enterpriseType,        
	        t.instruct,t.replyParentID,t.replyApproveStatus,t.pkgID,t.pkgName,t.pkgPrice,t.unicomApproveStatus,t.unicomApproveIdea,t.unicomApproveTime,t.telecomApproveStatus,t.telecomApproveIdea,t.telecomApproveTime,
	        t.certificateUrl,t.isMonthByQuota,t.batchNo,t.supportEnterpriseType,t.refYsmbID,t.dealStatus, t.mobileApproveStatus, t.mobileApproveIdea, t.mobileApproveTime,t.hangupType
        FROM 
        	ecpm_t_content t
        WHERE 
        	t.id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        limit #{pageNum},#{pageSize}
    </select>

    <select id="queryContetByIdsForCount" resultType="java.lang.Integer">
        SELECT 
        	count(1) 
        FROM 
       		ecpm_t_content t
        WHERE 
        	t.id IN
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
    
    <select id="queryContentForRemind" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        select  * 
        FROM ecpm_t_content where subServType in (34, 35, 36)
    </select>
    
    <select id="queryContetByRemindGroupID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        select  * 
        FROM ecpm_t_content t
		<trim prefix="where" prefixOverrides="and|or">
            <if test="remindGroupIDList !=null and remindGroupIDList.size > 0">
                and t.remindGroupID in
                <foreach collection="remindGroupIDList" item="remindGroupID" open="(" separator="," close=")">
                    #{remindGroupID}
                </foreach>
            </if>
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="contentType != null">
                and t.contentType = #{contentType}
            </if>
       </trim>
    </select>
     <select id="queryContentForSZFZ" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        select  * 
        FROM ecpm_t_content t
		<trim prefix="where" prefixOverrides="and|or">
            <if test="subServTypeList !=null and subServTypeList.size > 0">
                and t.subServType in
                <foreach collection="subServTypeList" item="subServType" open="(" separator="," close=")">
                    #{subServType}
                </foreach>
            </if>
            <if test="enterpriseID != null">
                and t.enterpriseID = #{enterpriseID}
            </if>
            <if test="servTypeList !=null and servTypeList.size > 0">
                and t.servType in
                <foreach collection="servTypeList" item="servType" open="(" separator="," close=")">
                    #{servType}
                </foreach>
            </if>
       </trim>
    </select>
    <select id="queryContentForBranch" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
         SELECT
        t1.id,t1.contCode,t1.contRuleID,t1.thirdpartyType,t1.servType,t1.subServType,t1.blackwhiteListType,
        t1.content,t1.contentTitle,t1.chargeType,t1.deliveryDate,t1.approveStatus,t1.syncStatus,t1.approveIdea,t1.enterpriseID,t1.enterpriseName,t1.enterpriseCode,t1.status,t1.sceneDesc,
        t1.deliveryType,t1.industryType,t1.applyProperty,t1.unicomDelivery,t1.scene,t1.frequencyID,t1.statID,t1.extInfo,t1.reserved1,t1.reserved2,t1.reserved3,
        t1.reserved4,t1.reserved7,t1.reserved9,t1.reserved10,t1.parentID,t1.createTime,t1.updateTime,t1.operatorID,t1.maxPushPerDay,t1.pushInterval,t1.auditTime,t1.deductTime,t1.contentType,t1.auditor,t1.enterpriseType,        
        t1.instruct,t1.replyParentID,t1.replyApproveStatus,t1.pkgID,t1.pkgName,t1.pkgPrice,t1.unicomApproveStatus,t1.unicomApproveIdea,t1.unicomApproveTime,t1.telecomApproveStatus,t1.telecomApproveIdea,t1.telecomApproveTime,
        t1.certificateUrl,t1.isMonthByQuota,t1.batchNo,t1.supportEnterpriseType,t1.refYsmbID,t1.dealStatus, t1.mobileApproveStatus, t1.mobileApproveIdea, t1.mobileApproveTime, t1.branchType, t1.oriContentID, t1.hangupType
        FROM ecpm_t_content t1
		where t1.oriContentID = #{oriContentID} and t1.parentID is null
    </select>
    
    <select id="queryContentForSubBranch" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
         SELECT
        t1.id,t1.contCode,t1.contRuleID,t1.thirdpartyType,t1.servType,t1.subServType,t1.blackwhiteListType,
        t1.content,t1.contentTitle,t1.chargeType,t1.deliveryDate,t1.approveStatus,t1.syncStatus,t1.approveIdea,t1.enterpriseID,t1.enterpriseName,t1.enterpriseCode,t1.status,t1.sceneDesc,
        t1.deliveryType,t1.industryType,t1.applyProperty,t1.unicomDelivery,t1.scene,t1.frequencyID,t1.statID,t1.extInfo,t1.reserved1,t1.reserved2,t1.reserved3,
        t1.reserved4,t1.reserved7,t1.reserved9,t1.reserved10,t1.parentID,t1.createTime,t1.updateTime,t1.operatorID,t1.maxPushPerDay,t1.pushInterval,t1.auditTime,t1.deductTime,t1.contentType,t1.auditor,t1.enterpriseType,        
        t1.instruct,t1.replyParentID,t1.replyApproveStatus,t1.pkgID,t1.pkgName,t1.pkgPrice,t1.unicomApproveStatus,t1.unicomApproveIdea,t1.unicomApproveTime,t1.telecomApproveStatus,t1.telecomApproveIdea,t1.telecomApproveTime,
        t1.certificateUrl,t1.isMonthByQuota,t1.batchNo,t1.supportEnterpriseType,t1.refYsmbID,t1.dealStatus, t1.mobileApproveStatus, t1.mobileApproveIdea, t1.mobileApproveTime, t1.branchType, t1.oriContentID, t1.hangupType
        FROM ecpm_t_content t1
		where t1.oriContentID = #{oriContentID} and t1.subServType = #{subServType}
    </select>
    <select id="queryreserved2" resultType="java.lang.String">
        SELECT
            t.reserved2
        FROM
            ecpm_t_member t
                LEFT JOIN ecpm_t_org_rel t1 ON t.id = t1.id
       where t.msisdn = #{userNumber}
    </select>
    
    <select id="queryContentForDiff" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
         SELECT
        t1.id,t1.contCode,t1.contRuleID,t1.thirdpartyType,t1.servType,t1.subServType,t1.blackwhiteListType,
        t1.content,t1.contentTitle,t1.chargeType,t1.deliveryDate,t1.approveStatus,t1.syncStatus,t1.approveIdea,t1.enterpriseID,t1.enterpriseName,t1.enterpriseCode,t1.status,t1.sceneDesc,
        t1.deliveryType,t1.industryType,t1.applyProperty,t1.unicomDelivery,t1.scene,t1.frequencyID,t1.statID,t1.extInfo,t1.reserved1,t1.reserved2,t1.reserved3,
        t1.reserved4,t1.reserved7,t1.reserved9,t1.reserved10,t1.parentID,t1.createTime,t1.updateTime,t1.operatorID,t1.maxPushPerDay,t1.pushInterval,t1.auditTime,t1.deductTime,t1.contentType,t1.auditor,t1.enterpriseType,        
        t1.instruct,t1.replyParentID,t1.replyApproveStatus,t1.pkgID,t1.pkgName,t1.pkgPrice,t1.unicomApproveStatus,t1.unicomApproveIdea,t1.unicomApproveTime,t1.telecomApproveStatus,t1.telecomApproveIdea,t1.telecomApproveTime,
        t1.certificateUrl,t1.isMonthByQuota,t1.batchNo,t1.supportEnterpriseType,t1.refYsmbID,t1.dealStatus, t1.mobileApproveStatus, t1.mobileApproveIdea, t1.mobileApproveTime, t1.branchType, t1.oriContentID, t1.hangupType
        FROM ecpm_t_content t1
		where 
		(t1.status != 2 or t1.status is null) and 
	    <foreach collection="list" item="item" open="(" separator="or" close=")" index="index">
	            id=#{item.contentID}
	   </foreach>    
    </select>
    
    <select id="queryContentByRefYsmbID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
         SELECT
        t1.id,t1.contCode,t1.contRuleID,t1.thirdpartyType,t1.servType,t1.subServType,t1.blackwhiteListType,
        t1.content,t1.contentTitle,t1.chargeType,t1.deliveryDate,t1.approveStatus,t1.syncStatus,t1.approveIdea,t1.enterpriseID,t1.enterpriseName,t1.enterpriseCode,t1.status,t1.sceneDesc,
        t1.deliveryType,t1.industryType,t1.applyProperty,t1.unicomDelivery,t1.scene,t1.frequencyID,t1.statID,t1.extInfo,t1.reserved1,t1.reserved2,t1.reserved3,
        t1.reserved4,t1.reserved7,t1.reserved9,t1.reserved10,t1.parentID,t1.createTime,t1.updateTime,t1.operatorID,t1.maxPushPerDay,t1.pushInterval,t1.auditTime,t1.deductTime,t1.contentType,t1.auditor,t1.enterpriseType,        
        t1.instruct,t1.replyParentID,t1.replyApproveStatus,t1.pkgID,t1.pkgName,t1.pkgPrice,t1.unicomApproveStatus,t1.unicomApproveIdea,t1.unicomApproveTime,t1.telecomApproveStatus,t1.telecomApproveIdea,t1.telecomApproveTime,
        t1.certificateUrl,t1.isMonthByQuota,t1.batchNo,t1.supportEnterpriseType,t1.refYsmbID,t1.dealStatus, t1.mobileApproveStatus, t1.mobileApproveIdea, t1.mobileApproveTime, t1.branchType, t1.oriContentID, t1.hangupType
        FROM ecpm_t_content t1
		where t1.refYsmbID1_vr = #{contentID} or t1.refYsmbID2_vr = #{contentID}
    </select>

    <select id="searchContentAndEnterpriseSimple" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        SELECT
            t.*
        FROM  ecpm_t_content t
        <if test="enterpriseType != null and enterpriseType != 3">
            LEFT JOIN ecpm_t_enterprise_simple e ON t.enterpriseID = e.id
        </if>
        <if test="enterpriseType != null and enterpriseType == 3">
            LEFT JOIN ecpm_t_enterprise_simple s ON t.enterpriseID = s.id
            left join ecpm_t_enterprise_simple e on e.id=s.parentEnterpriseID
        </if>
        <trim prefix="where" prefixOverrides="and|or">
                and (t.contentType = 6 or (t.contentType != 6 and t.enterpriseType = #{enterpriseType}))
            <if test="serverTypeList != null and serverTypeList.size()>0">
                and t.servType in
                <foreach item="serverType" index="index" collection="serverTypeList"
                         open="(" separator="," close=")">
                    #{serverType}
                </foreach>
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
                or t.enterpriseType != 5)))
            </if>
            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (t.enterpriseID is null or e.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach> or t.enterpriseType != 5 or t.enterpriseType is null)))
            </if>
            <if test="cityValList != null and cityValList.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.cityID in
                <foreach item="city" index="index" collection="cityValList"
                         open="(" separator="," close=")">
                    #{city}
                </foreach>
                or e.cityID is null)))
            </if>
            <if test="districtsAndCountiesList != null and districtsAndCountiesList.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.countyID in
                <foreach item="districtsAndCounty" index="index" collection="districtsAndCountiesList"
                         open="(" separator="," close=")">
                    #{districtsAndCounty}
                </foreach>
                or e.countyID is null)))
            </if>
            <if test="enterpriseID != null">
                and (e.id = #{enterpriseID} or t.enterpriseID = #{enterpriseID} or t.enterpriseID is null)
            </if>
            <if test="contentIDArr !=null and contentIDArr.size > 0">
                and (t.ID in
                <foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
                or t.parentID in
                <foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
                or t.oriContentID in
                <foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
                )
            </if>
            <if test="fieldValList !=null and fieldValList.size > 0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.provinceID in
                <foreach collection="fieldValList" item="fieldVal" open="(" separator="," close=")">
                    #{fieldVal}
                </foreach>
                or e.provinceID is null)))
            </if>
        </trim>
    </select>

    <update id="updateStatusAndApproveStatus">
        update ecpm_t_content set
            status=#{status}
            ,approveStatus=#{approveStatus}
            where
            ID=#{id} or parentID = #{id}  or oriContentID = #{id}
    </update>

    <update id="updateBranchContentStatus">
        update ecpm_t_content set
            status=#{status}
        where
            oriContentID = #{id}
    </update>
    
    
    <select id="queryContentTemplateList" resultMap="contentTemplateWrapper">
    
    	SELECT t.id,t.contRuleID,t.templateID,t.enterpriseID,t.servType,t.subServType,t.content,t.createTime, t.contentType,t.reserved10
		FROM (
			SELECT c0.id,c0.contRuleID,c0.id templateID,c0.enterpriseID,c0.servType,c0.subServType,c0.content,c0.createTime,c0.contentType,c0.enterpriseType,c0.reserved10
			FROM ecpm_t_content c0
			WHERE c0.id IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
			UNION
			SELECT c1.id,c1.contRuleID,c1.parentID templateID,c1.enterpriseID,c1.servType,c1.subServType,c1.content,c1.createTime,c1.contentType,c1.enterpriseType,c1.reserved10
			FROM ecpm_t_content c1
			WHERE c1.parentID IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
			UNION
			SELECT c2.id,c2.contRuleID,c2.refYsmbID1_vr templateID,c2.enterpriseID,c2.servType,c2.subServType,c2.content,c2.createTime,c2.contentType,c2.enterpriseType,c2.reserved10
			FROM ecpm_t_content c2
			WHERE c2.refYsmbID1_vr IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
			UNION
			SELECT c3.id,c3.contRuleID,c3.refYsmbID2_vr templateID,c3.enterpriseID,c3.servType,c3.subServType,c3.content,c3.createTime,c3.contentType,c3.enterpriseType,c3.reserved10
			FROM ecpm_t_content c3
			WHERE c3.refYsmbID2_vr IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
			UNION
			SELECT c4.id,c4.contRuleID,fc4.refYsmbID1_vr templateID,c4.enterpriseID,c4.servType,c4.subServType,c4.content,c4.createTime,c4.contentType,c4.enterpriseType,c4.reserved10
			FROM ecpm_t_content c4
			JOIN ecpm_t_content fc4 ON fc4.id=c4.parentID
			WHERE fc4.refYsmbID1_vr IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach> AND c4.subServType=1
			UNION
			SELECT c5.id,c5.contRuleID,fc5.refYsmbID2_vr templateID,c5.enterpriseID,c5.servType,c5.subServType,c5.content,c5.createTime,c5.contentType,c5.enterpriseType,c5.reserved10
			FROM ecpm_t_content c5
			JOIN ecpm_t_content fc5 ON fc5.id=c5.parentID
			WHERE fc5.refYsmbID2_vr IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach> AND c5.subServType=2
			UNION
			SELECT c6.id,c6.contRuleID,c6.oriContentID templateID,c6.enterpriseID,c6.servType,c6.subServType,c6.content,c6.createTime,c6.contentType,c6.enterpriseType,c6.reserved10
			FROM ecpm_t_content c6
			WHERE c6.oriContentID IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
			UNION
			SELECT c7.id,c7.contRuleID,c7.belongTemplateID templateID,c7.enterpriseID,c7.servType,c7.subServType,c7.content,c7.createTime,c7.contentType,c7.enterpriseType,c7.reserved10
			FROM ecpm_t_content c7
			WHERE c7.belongTemplateID IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
			UNION
			SELECT c8.id,c8.contRuleID,tc8.parentID templateID,c8.enterpriseID,c8.servType,c8.subServType,c8.content,c8.createTime,c8.contentType,c8.enterpriseType,c8.reserved10
			FROM ecpm_t_content c8
			JOIN ecpm_t_content tc8 ON tc8.id=c8.belongTemplateID
			WHERE tc8.parentID IN 
				<foreach collection="contentIDArr" item="contentID" open="(" separator="," close=")">
                    #{contentID}
                </foreach>
		) t
    	<if test="enterpriseType != null and enterpriseType != 3">
            LEFT JOIN ecpm_t_enterprise_simple e ON t.enterpriseID = e.id
        </if>
        <if test="enterpriseType != null and enterpriseType == 3">
            LEFT JOIN ecpm_t_enterprise_simple s ON t.enterpriseID = s.id
            left join ecpm_t_enterprise_simple e on e.id=s.parentEnterpriseID
        </if>
        <trim prefix="where" prefixOverrides="and|or">
                and (t.contentType = 6 or (t.contentType != 6 and t.enterpriseType = #{enterpriseType}))
            <if test="serverTypeList != null and serverTypeList.size()>0">
                and t.servType in
                <foreach item="serverType" index="index" collection="serverTypeList"
                         open="(" separator="," close=")">
                    #{serverType}
                </foreach>
            </if>
            <if test="DAuthEnterpriseTypeIN != null and DAuthEnterpriseTypeIN.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.reserved10 in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach>
                or t.enterpriseType != 5)))
            </if>
            <if test="DAuthEnterpriseTypeNotIN != null and DAuthEnterpriseTypeNotIN.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (t.enterpriseID is null or e.reserved10 not in
                <foreach item="dAuth" index="index" collection="DAuthEnterpriseTypeNotIN"
                         open="(" separator="," close=")">
                    #{dAuth}
                </foreach> or t.enterpriseType != 5 or t.enterpriseType is null)))
            </if>
            <if test="cityValList != null and cityValList.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.cityID in
                <foreach item="city" index="index" collection="cityValList"
                         open="(" separator="," close=")">
                    #{city}
                </foreach>
                or e.cityID is null)))
            </if>
            <if test="districtsAndCountiesList != null and districtsAndCountiesList.size()>0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.countyID in
                <foreach item="districtsAndCounty" index="index" collection="districtsAndCountiesList"
                         open="(" separator="," close=")">
                    #{districtsAndCounty}
                </foreach>
                or e.countyID is null)))
            </if>
            <if test="enterpriseID != null">
                and (e.id = #{enterpriseID} or t.enterpriseID = #{enterpriseID} or t.enterpriseID is null)
            </if>
            <if test="fieldValList !=null and fieldValList.size > 0">
                and (t.contentType = 6 or (t.contentType != 6 and (e.provinceID in
                <foreach collection="fieldValList" item="fieldVal" open="(" separator="," close=")">
                    #{fieldVal}
                </foreach>
                )))
            </if>
        </trim>
    	
        
        
    </select>

    <update id="updateContentOrgProvinceAndCity">
        update ecpm_t_content_org set
        <trim suffixOverrides="," suffix="where msisdn = #{msisdn}">
            <if test="provinceCode!=null and provinceCode!=''">
                provinceCode= #{provinceCode},
            </if>
            <if test="provinceName!=null and provinceName!=''">
                provinceName= #{provinceName},
            </if>
            <if test="cityCode!=null and cityCode!=''">
                cityCode= #{cityCode},
            </if>
            <if test="cityName!=null and cityName!=''">
                cityName= #{cityName},
            </if>
        </trim>
    </update>

    <select id="getContentListById" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
		SELECT
		ID,
		contCode,
		contRuleID,
		thirdpartyType,
		servType,
		subServType,
		blackwhiteListType,
		content,
		contentType,
		contentTitle,
		chargeType,
		deliveryDate,
		approveStatus,
		syncStatus,
		approveIdea,
		enterpriseID,
		enterpriseName,
		enterpriseCode,
		status,
		sceneDesc,
		deliveryType,
		industryType,
		applyProperty,
		unicomDelivery,
		scene,
		frequencyID,
		statID,
		maxPushPerDay,
		pushInterval,
		auditTime,
		deductTime,
		belongTemplateID,
		extInfo,
		reserved1,
		reserved2,
		reserved3,
		reserved4,
		reserved5,
        reserved6,
        reserved7,
        reserved8,
        reserved9,
        reserved10,
		parentID,
		createTime,
		updateTime,
		operatorID,
		instruct,
		replyParentID,
		replyApproveStatus,
		unicomApproveStatus,
		unicomApproveIdea,
		unicomApproveTime,
		telecomApproveStatus,
		telecomApproveIdea,
		telecomApproveTime,
		mobileApproveStatus,
		mobileApproveIdea,
		mobileApproveTime,
		hangupType
		from ecpm_t_content
		where
		ID=#{contentId} or parentID = #{contentId}  or oriContentID = #{contentId}
	</select>

    <select id="getContentAndContDeliverytime" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ContentWrapper">
        select a.id,
               a.content,
               a.contentTitle,
               a.chargeType,
               a.deliveryDate,
               a.reserved7,
               a.reserved10,
               a.pkgID,
               a.certificateUrl,
               a.reserved9,
               a.parentID,
               a.subServType,
               a.approveStatus,
               a.syncStatus,
               b.startTime,
               b.endTime
               from ecpm_t_content a
            INNER JOIN ecpm_t_cont_deliverytime b
                on a.ID = b.cyContID
                       WHERE (a.ID = #{id} or a.parentID = #{id}) and status = 0

    </select>

    <select id="getOrgCodeByCyContIDs" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.OrgIDAndCodeWrapper">
        SELECT
        DISTINCT co.orgCode,co.ownerID orgId
        FROM
        ecpm_t_org_simple os
        left JOIN ecpm_t_content_org co ON os.ID = co.ownerID
        left join ecpm_t_content c ON c.ID = co.cyContID
        WHERE
            (os.id = #{orgID} or oriOrgID = #{orgID})
        AND c.servType = #{servType}
        <if test="subServType!=null and subServType!='' and subServType!= 3 ">
            AND c.subServType = #{subServType}
        </if>
        <if test="subServType!=null and subServType!='' and subServType == 3 ">
            AND c.subServType in (1,2)
        </if>
    </select>

    <select id="getContentByMsisdn" resultType="com.huawei.jaguar.dsdp.ecpm.model.MemberContent">
        SELECT
            orl.orgID,
            m.msisdn,
            c.contentType,
            c.id contentId,
            c.enterpriseId,
            if(c.reserved10 is null, c.content,CONCAT("【",c.reserved10,"】",c.content)) content,
            c.mobileApproveStatus,
            c.unicomApproveStatus,
            c.telecomApproveStatus,
            c.mobileApproveIdea,
            c.deliveryDate,
            c.subServType
        FROM
            ecpm_t_member m
                LEFT JOIN ecpm_t_org_rel orl on m.id = orl.ID
                LEFT JOIN ecpm_t_content_org cor on cor.ownerID = orl.orgID and (cor.msisdn is null or cor.msisdn = m.msisdn)
                LEFT JOIN ecpm_t_content c on c.id = cor.cyContID
        WHERE
            m.msisdn = #{msisdn}
          and c.contentType in (1,3);
    </select>

    <update id="updateModify">
        update ecpm_t_content set isModify = 1, modifyDate = now() where id = #{contentId}
    </update>
</mapper>