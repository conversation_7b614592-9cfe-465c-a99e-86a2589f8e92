<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.DeductionRecordMapper">
    <resultMap id="deductionRecordWrapper" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.DeductionRecordWrapper">
        <result property="id" column="ID" javaType="java.lang.Long" />
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.Integer" />
        <result property="contentID" column="contentID" javaType="java.lang.Long" />
        <result property="servType" column="servType" javaType="java.lang.Integer" />
        <result property="subServType" column="subServType" javaType="java.lang.Integer" />
        <result property="memberID" column="memberID" javaType="java.lang.Long" />
        <result property="subscribeID" column="subscribeID" javaType="java.lang.Long" />
        <result property="status" column="status" javaType="java.lang.Integer" />
        <result property="processStatus" column="processStatus" javaType="java.lang.Integer" />
        <result property="extInfo" column="extInfo" javaType="java.lang.String" />
        <result property="reserved1" column="reserved1" javaType="java.lang.String" />
        <result property="reserved2" column="reserved2" javaType="java.lang.String" />
        <result property="reserved3" column="reserved3" javaType="java.lang.String" />
        <result property="reserved4" column="reserved4" javaType="java.lang.String" />
        <result property="createTime" column="createTime" javaType="java.util.Date" />
        <result property="updateTime" column="updateTime" javaType="java.util.Date" />
    </resultMap>
    <sql id="deductionRecordResult">
    ID,enterpriseID,contentID,servType,subServType,memberID,subscribeID,
    status,processStatus,extInfo,reserved1,reserved2,reserved3,reserved4,
    createTime,updateTime
    </sql>

    <select id="queryDeductionRecordByCond" resultMap="deductionRecordWrapper">
        select
        <include refid="deductionRecordResult" />
        from
        ecpm_t_deduction_record
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id !=null">
                and ID = #{id}
            </if>
            <if test="enterpriseID!=null">
                and enterpriseID = #{enterpriseID}
            </if>
            <if test="contentID !=null">
                and contentID = #{contentID}
            </if>
            <if test="contentIdListitem != null and contentIdListitem.size() > 0">
                and
                <foreach collection="contentIdList" item="contentID" open="(" separator="or" close=")">
                    contentID = #{contentID}
                </foreach>
            </if>
            <if test="servType != null">
                and servType = #{servType}
            </if>
            <if test="subServType != null">
                and subServType = #{subServType}
            </if>
            <if test="memberID !=null">
                and memberID = #{memberID}
            </if>
            <if test="subscribeID!=null">
                and subscribeID = #{subscribeID}
            </if>
            <if test="createTime !=null">
                and createTime = #{createTime}
            </if>
            <if test="updateTime != null">
                and updateTime = #{updateTime}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="processStatus != null">
                and processStatus = #{processStatus}
            </if>
            <if test="extInfo !=null and extInfo != ''">
                and extInfo = #{extInfo}
            </if>
            <if test="reserved1!=null and reserved1 != ''">
                and reserved1 = #{reserved1}
            </if>
            <if test="reserved2 !=null and reserved2 != ''">
                and reserved2 = #{reserved2}
            </if>
            <if test="reserved3 != null and reserved3 != ''">
                and reserved3 = #{reserved3}
            </if>
            <if test="reserved4 != null and reserved4 != ''">
                and reserved4 = #{reserved4}
            </if>
        </trim>
    </select>
    <select id="countDeductionRecordByCond" resultType="java.lang.Long">
        select
        count(distinct memberID)
        from
        ecpm_t_deduction_record
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id !=null">
                and ID = #{id}
            </if>
            <if test="enterpriseID!=null">
                and enterpriseID = #{enterpriseID}
            </if>
            <if test="contentID !=null">
                and contentID = #{contentID}
            </if>
            <if test="servType != null">
                and servType = #{servType}
            </if>
            <if test="subServType != null">
                and subServType = #{subServType}
            </if>
            <if test="memberID !=null">
                and memberID = #{memberID}
            </if>
            <if test="subscribeID!=null">
                and subscribeID = #{subscribeID}
            </if>
            <if test="statusList != null and statusList.size() >0">
                and status in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="processStatusList != null and processStatusList.size() > 0">
                and
                <foreach collection="processStatusList" item="processStatus" open="(" separator="and" close=")">
                    processStatus != #{processStatus}
                </foreach>

            </if>
        </trim>
    </select>

    <select id="queryDetailDeductionMemberCountByContentID" resultMap="deductionRecordWrapper">
        select
        t.ID as contentID,
        t.enterpriseID,
        t.servType,
        t.subServType,
        ms.memberID
        from ecpm_t_content
        t,ecpm_t_content_org
        org,ecpm_t_org_rel rel,ecpm_t_member
        m,ecpm_t_member_subscribe
        ms,ecpm_t_serv_product p
        where t.ID = #{contID} and t.ID
        = org.cyContID
        and org.ownerId =rel.orgID and rel.id = m.id
        and
        m.id = ms.memberID
        and ms.productCode =
        p.productCode and (p.servType =1 or p.servType = 5)
        <if test="uselessStatusList != null and uselessStatusList.size()>0">
            and
            <foreach collection="uselessStatusList" item="uselessStatus" open="(" separator="and" close=")">
                ms.status != #{uselessStatus}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and
            <foreach item="status" collection="statusList" open="(" separator="or" close=")">
                ms.status = #{status}
            </foreach>
        </if>
    </select>

	<select id="queryDetailDeductionMemberCountByOrgId" resultMap="deductionRecordWrapper">
        select
        t.ID as contentID,
        t.enterpriseID,
        t.servType,
        t.subServType,
        ms.memberID
        from ecpm_t_content
        t,ecpm_t_org_rel rel,ecpm_t_member
        m,ecpm_t_member_subscribe
        ms,ecpm_t_serv_product p
        where  t.ID = #{contID} and rel.id = m.id
        and
        m.id = ms.memberID
        and ms.productCode =
        p.productCode and (p.servType =1 or p.servType = 5)
		<if test="orgIdList != null and orgIdList.size()>0">
            and
            <foreach item="orgId" collection="orgIdList" open="(" separator="or" close=")">
                rel.orgID = #{orgId}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size()>0">
            and
            <foreach item="status" collection="statusList" open="(" separator="or" close=")">
                ms.status = #{status}
            </foreach>
        </if>
    </select>
    
    <insert id="insert">
	insert into ecpm_t_deduction_record
	(enterpriseID,contentID,servType,subServType,memberID,subscribeId,
    status,processStatus,extInfo,reserved1,reserved2,reserved3,reserved4,
    createTime,updateTime) values (
    {enterpriseID},#{contentID},#{servType},#{subServType},#{memberID},#{subscribeID},
    #{status},#{processStatus},#{extInfo},#{reserved1},#{reserved2},#{reserved3},#{reserved4},
    #{createTime},#{updateTime})
	</insert>

    <update id="update">
        update ecpm_t_deduction_record
        set updateTime = #{updateTime}
        <if test="subscribeID != null">
            ,subscribeId = #{subscribeID}
        </if>
        <if test="status != null">
            ,status = #{status}
        </if>
        <if test="processStatus != null">
            ,processStatus = #{processStatus}
        </if>
        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null">
                ID = #{id}
            </if>
            <if test="contentID != null">
                and contentID = #{contentID}
            </if>
            <if test="memberID != null">
                and memberID = #{memberID}
            </if>
            <if test="memberIdList != null and memberIdList.size() > 0">
                and memberID in
                <foreach collection="memberIdList" item="memberID" open="(" separator="," close=")">
                    #{memberID}
                </foreach>
            </if>
            <if test="enterpriseID != null">
                and enterpriseID = #{enterpriseID}
            </if>
            <if test="contentIdList != null and contentIdList.size() > 0">
                and
                <foreach collection="contentIdList" item="contentID" open="(" separator="or" close=")">
                    contentID = #{contentID}
                </foreach>
            </if>
            <if test="statusCond != null">
                and status = #{statusCond}
            </if>
            <if test="statusCondObverse != null">
                and status != #{statusCondObverse}
            </if>
            <if test="processStatusCond != null">
                and processStatus = #{processStatusCond}
            </if>
        </trim>
        <if test="limitNum != null">
            ORDER BY id DESC limit #{limitNum}
        </if>
    </update>
    <insert id="insertDeductionRecordList">
        insert into ecpm_t_deduction_record
        (
        enterpriseID,
        contentID,
        servType,
        subServType,
        memberID,
        subscribeID,
        status,
        processStatus,
        extInfo,
        reserved1,
        reserved2,
        reserved3,
        reserved4,
        createTime,
        updateTime
        )
        values
        <foreach collection="list" item="deductionRecordWrapper" separator=",">
            (
            #{deductionRecordWrapper.enterpriseID},
            #{deductionRecordWrapper.contentID},
            #{deductionRecordWrapper.servType},
            #{deductionRecordWrapper.subServType},
            #{deductionRecordWrapper.memberID},
            #{deductionRecordWrapper.subscribeID},
            #{deductionRecordWrapper.status},
            #{deductionRecordWrapper.processStatus},
            #{deductionRecordWrapper.extInfo},
            #{deductionRecordWrapper.reserved1},
            #{deductionRecordWrapper.reserved2},
            #{deductionRecordWrapper.reserved3},
            #{deductionRecordWrapper.reserved4},
            #{deductionRecordWrapper.createTime},
            #{deductionRecordWrapper.updateTime}
            )
        </foreach>
    </insert>

    <update id="updateDeductionRecordList">
        <foreach close=";" collection="list" index="index" item="recordWrapper" open="" separator=";">
            update ecpm_t_deduction_record
            <trim prefix="set" suffixOverrides=",">
                <if test="recordWrapper.status != null">
                    status = #{recordWrapper.status},
                </if>
                <if test="recordWrapper.processStatus != null">
                    processStatus = #{recordWrapper.processStatus},
                </if>
                <if test="recordWrapper.subscribeID != null">
                    subscribeID = #{recordWrapper.subscribeID},
                </if>
                <if test="recordWrapper.updateTime != null">
                    updateTime = #{recordWrapper.updateTime}
                </if>
            </trim>
            where contentID = #{recordWrapper.contentID}
            and memberID = #{recordWrapper.memberID}
            and status = 1
            and processStatus = 2
        </foreach>
    </update>
    
    <update id="updateDeductionRecordListSuccess">
        <foreach close=";" collection="list" index="index" item="recordWrapper" open="" separator=";">
            update ecpm_t_deduction_record
            <trim prefix="set" suffixOverrides=",">
                <if test="recordWrapper.status != null">
                    status = 2,
                </if>
                <if test="recordWrapper.processStatus != null">
                    processStatus = 3,
                </if>
                <if test="recordWrapper.updateTime != null">
                    updateTime = #{recordWrapper.updateTime}
                </if>
            </trim>
            where contentID = #{recordWrapper.contentID}
            and memberID = #{recordWrapper.memberID}
            and status = 1
            and processStatus = 2
        </foreach>
    </update>
    
    <update id="updateDeductionRecordListError">
        <foreach close=";" collection="list" index="index" item="recordWrapper" open="" separator=";">
            update ecpm_t_deduction_record
            <trim prefix="set" suffixOverrides=",">
                <if test="recordWrapper.status != null">
                    status = 3,
                </if>
                <if test="recordWrapper.processStatus != null">
                    processStatus = 4,
                </if>
                <if test="recordWrapper.updateTime != null">
                    updateTime = #{recordWrapper.updateTime}
                </if>
            </trim>
            where contentID = #{recordWrapper.contentID}
            and memberID = #{recordWrapper.memberID}
            and status = 1
            and processStatus = 2
        </foreach>
    </update>
    

    <update id="updateDeductionRecordListWithPending">
        <foreach close=";" collection="list" index="index" item="recordWrapper" open="" separator=";">
            update ecpm_t_deduction_record
            <trim prefix="set" suffixOverrides=",">
                <if test="recordWrapper.status != null">
                    status = #{recordWrapper.status},
                </if>
                <if test="recordWrapper.processStatus != null">
                    processStatus = #{recordWrapper.processStatus},
                </if>
                <if test="recordWrapper.updateTime != null">
                    updateTime = #{recordWrapper.updateTime}
                </if>
            </trim>
            where contentID = #{recordWrapper.contentID}
            and memberID = #{recordWrapper.memberID}
            and status = 1
            and processStatus = 1
        </foreach>
    </update>
</mapper>