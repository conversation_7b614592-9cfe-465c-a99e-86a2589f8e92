<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
	<property name="APP_Name" value="cy-config-server" />
	<property name="LOG_HOME" value="/logs" />
    <contextName>${APP_Name}</contextName>

	<jmxConfigurator/>
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">

        <encoder>
            <pattern>%d{yyyy-MM-dd} %d{HH:mm:ss.SSSZ} %-5level %logger{36} [${APP_Name}, %16X{X-B3-TraceId}, %16X{X-B3-SpanId}, %5X{X-Span-Export}], [%15.15t] : %m%n%wEx</pattern>
        </encoder>

    </appender>
    
   	   	<!-- 按照每天生成日志文件 -->
    <appender name="FILE"  class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
               <!--日志文件输出的文件名-->
               <FileNamePattern>${LOG_HOME}/${APP_Name}/%d{yyyy-MM-dd}.%i.log</FileNamePattern>
               <!-- 超过N天的都删除 -->
               <MaxHistory>10</MaxHistory>
               <maxFileSize>100MB</maxFileSize>
        </rollingPolicy>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
        	<customFields>{"servicename":"${APP_Name}"}</customFields>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
        <!-- <appender-ref ref="logstash" /> -->
    </root>


    
</configuration>