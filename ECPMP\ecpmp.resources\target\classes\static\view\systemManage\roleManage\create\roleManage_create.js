var app = angular.module("myApp", ["util.ajax", "angularI18n", "service.common"]);
app.controller("roleManageCreateCtrl", function ($scope, $rootScope, RestClientUtil) {
  $scope.openFirst = true;
  $scope.openSecond = true;
  $scope.openThird = true;
  $scope.openFourth = true;
  $scope.provinceList = [];
  $scope.provinceList2 = [];
  $scope.cityList = [];
  $scope.showCountyProIds = ["10"];
  $scope.countyList = [];
  $scope.cityIDs = [];
  $scope.rebuildFirstList = [];
  $scope.rebuildSecondList = [];
  $scope.roleName = "";
  $scope.roleDesc = "";
  $scope.error = "";
  $scope.errorType = 0;
  $scope.isSubmit = false;
  $scope.allChecked =false;
  $scope.allCityChecked = false;
  $scope.allChecked2 =false;
  $scope.enterpriseTypeList = [];
  $scope.init = function () {
    $scope.operatorID = $.cookie('accountID');
    //获取社会合作管理权限
    $scope.getAuthInfoList({
      "authBelongType": 1,
      "authCategory": 1,
      "authName": "",
      "authType": 1
    }, function (data) {
      if (data && data.result && data.result.resultCode === "**********") {
        $scope.social_fAuthInfoList = data.functionAuthList;
        //重新构建社会合作管理权限
        $scope.rebuildFirstList = $scope.rebuildList($scope.social_fAuthInfoList);
      } else {
        $scope.tip = data.result.resultCode;
        $("#roleModal").modal();
      }
    }, function (data) {
      $scope.tip = data.result.resultCode;
      $("#roleModal").modal();
    });
    //获取分省合作管理权限
    $scope.getAuthInfoList({
      "authBelongType": 2,
      "authCategory": 1,
      "authName": "",
      "authType": 1
    }, function (data) {
      if (data && data.result && data.result.resultCode === "**********") {
        $scope.province_fAuthInfoList = data.functionAuthList;
        //重新构建社会合作管理权限
        $scope.rebuildSecondList = $scope.rebuildList($scope.province_fAuthInfoList);
      } else {
        $scope.tip = data.result.resultCode;
        $("#roleModal").modal();
      }
    }, function (data) {
      $scope.tip = data.result.resultCode;
      $("#roleModal").modal();
    });
    //获取分省合作数据权限
    $scope.getAuthInfoList({
      authCategory: 2
    }, function (data) {
      if (data && data.result && data.result.resultCode === "**********") {
        $scope.dAuthInfoList = data.dateAuthList;

        angular.forEach($scope.dAuthInfoList, function (item, index) {
          if (item.fieldName == "provinceID") {
            item.checked = false;
            $scope.provinceList.push(item)
          }
          if(item.fieldName == "reserved10" && item.tableName == "dsum_t_enterprise"){
              $scope.enterpriseTypeList.push(item);
          }
        });
        //获取化企业数据权限
        $scope.dAuthInfoList2 = angular.copy(data.dateAuthList);
        angular.forEach($scope.dAuthInfoList2, function (item, index) {
          if (item.fieldName == "provinceID") {
            item.checked = false;
            $scope.provinceList2.push(item)
          }
        });

      } else {
        $scope.tip = data.result.resultCode;
        $("#roleModal").modal();
      }
    }, function (data) {
      $scope.tip = data.result.resultCode;
      $("#roleModal").modal();
    });
  };

  $scope.getAuthInfoList = function (req, success, error) {
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryAuths",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          success(data);
        })
      },
      error: function (data) {
        $rootScope.$apply(function () {
          error();
        })
      }
    })
  }

  $scope.rebuildList = function (oldList) {
    var newlist = [];
    angular.forEach(oldList, function (item) {
      if (item.parentAuthID == null) {
        //重构后的社会合作管理权限
        newlist.push(item)
      }
    });
    angular.forEach(newlist, function (rebuildData) {
      rebuildData.childList = [];
      angular.forEach(oldList, function (oldData) {
        if (oldData.parentAuthID === rebuildData.id) {
          oldData.checked = false;
          rebuildData.childList.push(oldData)
        }
      })
    });
    return newlist;
  };
//判断是否已经选中全地市
  $scope.isAllCityChecked =function(){
  var allCityChecked =true ;
  angular.forEach($scope.cityList, function (city) {
       if(!city.checked){
          allCityChecked=false;
        }
          })
      $scope.allCityChecked = allCityChecked;
  }
    //判断是否已经选中全区县
  $scope.isAllCountyChecked =function(){
      var allCountyChecked =true ;
      angular.forEach($scope.countyList, function (county) {
          if(!county.checked){
              allCountyChecked=false;
          }
      })
      $scope.allCountyChecked = allCountyChecked;
  }
  //选择省份时  筛选城市
  $scope.chooseProvince = function (item) {
    if ($scope.errorType===1) {
      $scope.errorType = 0;
      $scope.error = "";
    }
    if ($scope.errorType===2) {
        $scope.errorType = 0;
        $scope.error = "";
      }
    var flag = true;
    item.checked = !item.checked;
    //如果想选中
    if (item.checked) {
      angular.forEach($scope.dAuthInfoList, function (rowData) {
        if (rowData.fieldName == "cityID" && rowData.parentAuthID === item.id) {
          rowData.checked = false; //是否被选中的标识位
          $scope.cityList.push(rowData)
        }
        //判断是否所有省全选
        if(rowData.fieldName == "provinceID" && !rowData.checked){
             flag = false;
        }
          //控制是否展示区县
          if($scope.showCountyProIds.includes(item.fieldVal)){
              $scope.showCountyFlag = true;
          }
      })

    }
    //取消省份勾选
    if (!item.checked) {
      for (var i = 0; i < $scope.cityList.length; i++) {
        if ($scope.cityList[i].parentAuthID === item.id) {
            //清除对应的区县
            for (var j = 0; j < $scope.countyList.length; j++) {
                if ($scope.countyList[j].parentAuthID == $scope.cityList[i].fieldVal) {
                    $scope.countyList[j].checked = false;
                    $scope.countyList.splice(j--, 1)
                }
            }
          $scope.cityList[i].checked = false;
          $scope.cityList.splice(i--, 1)
        }
      }
      //判断是否所有省全选
      angular.forEach($scope.dAuthInfoList, function (rowData) {
         if(rowData.fieldName == "provinceID" && !rowData.checked){
                flag = false;
          }
       })
        //控制是否展示区县
        if($scope.showCountyProIds.includes(item.fieldVal)&&$scope.countyList.length===0){
            $scope.showCountyFlag = false;
        }
    }
    $scope.allChecked = flag;
    if ($scope.cityList.length===0 && $scope.errorType===2) {
      $scope.errorType = 0;
      $scope.error = "";
    }
     $scope.isAllCityChecked();
  };


   //全国全选功能
    $scope.chooseAllProvince = function () {
      if ($scope.errorType === 1) {
            $scope.errorType = 0;
            $scope.error = "";
          }

          //判断是否已经选中全国

          //已经选中，现在点击表明想取消
          if ($scope.allChecked) {
           angular.forEach($scope.dAuthInfoList, function (rowData) {

               if (rowData.fieldName == "provinceID" || rowData.fieldName == "cityID"){
                   rowData.checked = false;
               }
            })
            $scope.cityList = [];
           $scope.allChecked = false;
            $scope.checkedProvinces = [];
            $scope.showCountyFlag = false;
            $scope.countyList = [];
          }
          //选中全国
          else {
           angular.forEach($scope.dAuthInfoList, function (rowData) {
           if (rowData.fieldName == "provinceID"){
              rowData.checked = true;

           } //所有市显示出来

            if (rowData.fieldName == "cityID" && !$scope.cityList.includes(rowData) ){
                        $scope.cityList.push(rowData);
            }

            })
                $scope.allChecked = true;
              $scope.showCountyFlag = true;
          }
         
          if ($scope.cityList.length===0 && $scope.errorType===2) {
            $scope.errorType = 0;
            $scope.error = "";
          }
      $scope.allCityChecked = false;
    };


    //全地市全选功能
       $scope.chooseAllCity = function () {
           if (2 === $scope.errorType) {
               $scope.error = "";
           }
            //取消全选
          if ($scope.allCityChecked) {
           angular.forEach($scope.cityList, function (city) {
                         city.checked = false;
                  })
                $scope.allCityChecked = false;
                $scope.allCountyChecked = false;
               $scope.checkedCitys = [];
              $scope.countyList = [];
          }
          //全选
          else{
              $scope.cityIDs=[];
          angular.forEach($scope.cityList, function (city) {
                  city.checked = true;
                  //汇总有区县信息的地市编码
                  /*if ($scope.showCountyProIds.includes(city.parentAuthID.toString())) {
                      $scope.cityIDs.push(city.fieldVal)

                  }*/
                  angular.forEach($scope.showCountyProIds, function (proId) {
                      if(proId==city.parentAuthID){
                          $scope.cityIDs.push(city.fieldVal)
                      }
                  })
           })
              //全选地市时，若选中的地市有区县信息，则查询
              if($scope.cityIDs.length!=0){
                  $scope.countyList = [];
                  $scope.queryCounty();
                  $scope.showCountyFlag = true;
              }
           $scope.allCityChecked = true;
           $scope.checkedCitys = $scope.cityList;
          }
        };
        $scope.showAllCityChecked=function(){
            if(!$scope.allChecked && $scope.cityList.length==0){
            return false;
            }
            return true;
            }
    $scope.showAllCountyChecked=function(){
        if($scope.countyList.length==0){
            return false;
        }
        return true;
    }
    //全区县全选功能
    $scope.chooseAllCounty = function () {
        if ($scope.errorType === 2) {
            $scope.errorType = 0;
            $scope.error = "";
        }
        //取消全选
        if ($scope.allCountyChecked) {
            angular.forEach($scope.countyList, function (county) {
                county.checked = false;
            })
            $scope.allCountyChecked = false;
            $scope.checkedCountys = [];
        }
        //全选
        else{
            angular.forEach($scope.countyList, function (county) {
                county.checked = true;
            })
            $scope.allCountyChecked = true;
            $scope.checkedCountys = $scope.countyList;
        }
    };
    //查询区县信息
    $scope.queryCounty = function () {
        $scope.getAuthInfoList({
            authCategory: 2,
            dauthType: 2,
            cityIDs: $scope.cityIDs
        }, function (data) {
            if (data && data.result && data.result.resultCode === "**********") {
                $scope.dAuthInfoList3 = data.dateAuthList;
                angular.forEach($scope.dAuthInfoList3, function (item, index) {
                    $scope.countyList.push(item)

                });
                //查询出新的区县，则肯定为非全选状态
                $scope.allCountyChecked = false;
            } else {
                $scope.tip = data.result.resultCode;
                $("#roleModal").modal();
            }
        }, function (data) {
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
        });
    };
   //点击城市勾选框
  $scope.chooseChild = function (item, errorType) {
    if (errorType === $scope.errorType) {
      $scope.error = "";
    }
    item.checked = !item.checked;
    var allCityChecked =true ;
     angular.forEach($scope.cityList, function (city) {
               if(!city.checked){
                  allCityChecked=false;
                }
                  })
      $scope.allCityChecked = allCityChecked;
      if (item.checked) {
              /*if ($scope.showCountyProIds.includes(item.parentAuthID.toString())) {
                  //rowData.checked = false; //是否被选中的标识位
                  console.log("查区县")
                  $scope.cityIDs=[];
                  $scope.cityIDs.push(item.fieldVal)
                  $scope.queryCounty();

              }*/
          angular.forEach($scope.showCountyProIds, function (proId) {
              if(proId==item.parentAuthID){
                  $scope.cityIDs=[];
                  $scope.cityIDs.push(item.fieldVal)
                  $scope.queryCounty();
              }
          })

      }
      if(!item.checked){
          var sid = item.fieldVal
          $scope.cityIDs =$scope.cityIDs.filter(param => param != sid)
          for (var i = 0; i < $scope.countyList.length; i++) {
              if ($scope.countyList[i].parentAuthID == item.fieldVal) {
                  $scope.countyList[i].checked = false;
                  $scope.countyList.splice(i--, 1)
              }
          }
          $scope.isAllCountyChecked();
      }



  };
    //点击区县勾选框
    $scope.chooseCounty = function (item,errorType) {
        if (errorType === $scope.errorType) {
            $scope.error = "";
        }
        item.checked = !item.checked;
        var allCountyChecked =true ;
        angular.forEach($scope.countyList, function (county) {
            if(!county.checked){
                allCountyChecked=false;
            }
        })
        $scope.allCountyChecked = allCountyChecked;
    }

  $scope.goBack = function () {
    window.location.href = "../list/roleManage_list.html"
  };

  //点击提交  创建角色
  $scope.createRole = function () {
    $scope.selected_fAuthInfoList = [];
    $scope.selected_dAuthInfoList = [];
    $scope.checkedProvinceList = [];
    $scope.checkedCityList = [];
    $scope.checkedProvinceList2 = [];
    $scope.checkedCountyList=[];
    var eAuth = [];
    var pAuth = [];
    var hAuth = [];
    var sAuth = [];
    var cAuth = [];
    angular.forEach($scope.rebuildFirstList, function (rebuildData) {
      angular.forEach(rebuildData.childList, function (childData) {
        if (childData.checked) {
          childData.operatorID = $scope.operatorID;
          $scope.selected_fAuthInfoList.push(childData);
          if (childData.authBelongType == 1)
          {
        	  eAuth.push(childData);
        	  if (childData.parentAuthID == 5 || childData.parentAuthID == 11)
              {
        		  sAuth.push(childData);
              }
        	  else
              {
        		  if (childData.id == 2003)
        	      {
            		  cAuth.push(childData);
        	      }
        		  else
        	      {
            		  hAuth.push(childData);
        	      }
              }
          }  
          else if (childData.authBelongType == 2)
          {
        	  pAuth.push(childData);
          }
        }
      })
    });
    angular.forEach($scope.provinceList2, function (item) {
      if (item.checked) {
        item.operatorID = $scope.operatorID;
        $scope.checkedProvinceList2.push(item);
      }
    });

    angular.forEach($scope.rebuildSecondList, function (rebuildData) {
      angular.forEach(rebuildData.childList, function (childData) {
        if (childData.checked) {
          childData.operatorID = $scope.operatorID;
          $scope.selected_fAuthInfoList.push(childData);
          if (childData.authBelongType == 1)
          {
        	  eAuth.push(childData);
          }  
          else if (childData.authBelongType == 2)
          {
        	  pAuth.push(childData);
          }
        }
      })
    });
    var provinceMap = new Map();
    var cityMap = new Map();
    angular.forEach($scope.provinceList, function (item) {
      if (item.checked) {
        item.operatorID = $scope.operatorID;
        $scope.checkedProvinceList.push(item);
        $scope.selected_dAuthInfoList.push(item);
        provinceMap.set(item.id, "1");
      }
    });
    angular.forEach($scope.cityList, function (item) {
      if (item.checked) {
        item.operatorID = $scope.operatorID;
        $scope.checkedCityList.push(item);
        $scope.selected_dAuthInfoList.push(item);
        provinceMap.delete(item.parentAuthID);
        cityMap.set(item.fieldVal, "1");
      }
    });
      angular.forEach($scope.countyList, function (item) {
          if (item.checked) {
              item.operatorID = $scope.operatorID;
              $scope.checkedCountyList.push(item);
              $scope.selected_dAuthInfoList.push(item);
              cityMap.delete(item.parentAuthID.toString());
          }
      });
      $scope.selected_enterpriseTypeList = [];
      //企业类型权限
     //是否选择移动云
      $scope.selected_enterpriseType_ydy = false;
      //是否选择其他企业类型
      $scope.selected_enterpriseType_other = false;
      angular.forEach($scope.enterpriseTypeList, function (item) {
          if (item.checked) {
              if(item.fieldVal == "112"|| item.fieldVal == "113"){
                  $scope.selected_enterpriseType_ydy = true;
              }else {
                  $scope.selected_enterpriseType_other = true;
              }
              $scope.selected_enterpriseTypeList.push(item)
          }
      });
    //选择了省  但是未选择市
    if ($scope.checkedProvinceList.length > 0 && $scope.checkedCityList.length === 0) {
      $scope.errorType = 2;
      $scope.error = "您已选择数据权限（省），请选择至少一个数据权限（市）";
      return;
    } else {
      $scope.error = "";
      $scope.errorType = 0;
    }

//    $scope.selected_fAuthInfoList_copy = angular.copy($scope.selected_fAuthInfoList);
//    $scope.selected_dAuthInfoList_copy = angular.copy($scope.selected_dAuthInfoList);
//    angular.forEach($scope.selected_fAuthInfoList, function (item) {
//      item.operatorID = $scope.operatorID;
//    });
//    angular.forEach($scope.selected_dAuthInfoList, function (item) {
//      item.operatorID = $scope.operatorID;
//    });

    if ($scope.selected_fAuthInfoList.length === 0 && $scope.selected_dAuthInfoList.length === 0 && $scope.checkedProvinceList2.length === 0) {
      $scope.errorType = 1;
      $scope.error = "请选择至少一个权限";
      return;
    } else {
      $scope.error = "";
      $scope.errorType = 0;
    }
    
    //i.	“社会合作管理”下，若只选中“系统管理”栏下权限,不校验
    if (sAuth.length != 0 && hAuth.length == 0 && cAuth.length == 0)
    {
    	$scope.error = "";
        $scope.errorType = 0;
    }
    //ii.	“社会合作管理”下，若选中“彩印内容审核”权限，且未选中“系统管理”栏下权限以外的权限，则限制企业数据权限、分省合作数据权限必选一个
    else if (cAuth.length != 0 && hAuth.length == 0 && $scope.checkedProvinceList2.length === 0 && $scope.selected_dAuthInfoList.length === 0 && !$scope.selected_enterpriseType_ydy)
    {
    	$scope.errorType = 1;
        $scope.error = "请选择至少一个数据权限";
        return;
    }
    else
    {
    	if (eAuth.length != 0 && $scope.checkedProvinceList2.length === 0 && hAuth.length != 0 && !$scope.selected_enterpriseType_ydy) {
            $scope.errorType = 1;
            $scope.error = "请选择至少一个企业数据权限";
            return;
        } else {
            $scope.error = "";
            $scope.errorType = 0;
        }
    }


      if (pAuth.length != 0 && $scope.selected_enterpriseTypeList.length === 0) {
          $scope.errorType = 1;
          $scope.error = "请选择企业类型";
          return;
      } else {
          $scope.error = "";
          $scope.errorType = 0;
      }
    //勾选了移动云以外的企业类型
    if (pAuth.length != 0 && $scope.selected_dAuthInfoList.length === 0 && $scope.selected_enterpriseType_other) {
        $scope.errorType = 1;
        $scope.error = "请选择至少一个分省数据权限";
        return;
    } else {
        $scope.error = "";
        $scope.errorType = 0;
    }
    if (provinceMap.size != 0)
    {
    	 $scope.errorType = 2;
         $scope.error = "您已选择数据权限（省），请选择至少一个该省下的数据权限（市）";
         return;
       } else {
         $scope.error = "";
         $scope.errorType = 0;
    }
      $scope.selected_dAuthInfoList = $scope.selected_dAuthInfoList.concat($scope.selected_enterpriseTypeList);


    var req = {
      role: {
        roleName: $scope.roleName,
        roleDesc: $scope.roleDesc,
        functionAuthList: $scope.selected_fAuthInfoList,
        dateAuthList: $scope.selected_dAuthInfoList,
        enterpriseDateAuthList:$scope.checkedProvinceList2,
        operatorID: $scope.operatorID
      }
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/createRole",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          if (data && data.result && data.result.resultCode === "**********") {
            $scope.tip = "创建成功";
            $scope.isSubmit = true;
            $("#roleModal").modal();
          } else {
            $scope.isSubmit = false;
            $scope.tip = data.result.resultCode;
            $("#roleModal").modal();
          }
        })
      },
      error: function (data) {
        $rootScope.$apply(function () {
          $scope.isSubmit = false;
          $scope.tip = data.result.resultCode;
          $("#roleModal").modal();
        })
      }
    })
  }

  //企业数据权限全国全选功能
  $scope.chooseAllProvince2 = function () {
    if ($scope.errorType === 1) {
          $scope.errorType = 0;
          $scope.error = "";
    }
    //已经选中，现在点击表明想取消
    if ($scope.allChecked2) {
     angular.forEach($scope.dAuthInfoList2, function (rowData) {
             rowData.checked = false;//选中的省取消，但要显示所有省
      })
     $scope.allChecked2 = false;
      $scope.checkedProvinces2 = [];
    }
    else {
     angular.forEach($scope.dAuthInfoList2, function (rowData) {
         if (rowData.fieldName == "provinceID"){
            rowData.checked = true;

         }
     })
     $scope.allChecked2 = true;
    }
  };
  //企业数据权限选择省份
  $scope.chooseProvince2 = function (item) {
    if ($scope.errorType===1) {
      $scope.errorType = 0;
      $scope.error = "";
    }
    var flag = true;
    item.checked = !item.checked;
    //如果想选中
    if (item.checked) {
      angular.forEach($scope.dAuthInfoList2, function (rowData) {
        //判断是否所有省全选
        if(rowData.fieldName == "provinceID" && !rowData.checked){
             flag = false;
        }
      })
    }
    //取消省份勾选
    if (!item.checked) {
      //判断是否所有省全选
      angular.forEach($scope.dAuthInfoList2, function (rowData) {
         if(rowData.fieldName == "provinceID" && !rowData.checked){
                flag = false;
          }
       })
    }
    $scope.allChecked2 = flag;
  };


})