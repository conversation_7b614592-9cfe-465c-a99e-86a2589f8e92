<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SharedPackageOrderMapper">

    <insert id="insert" keyProperty="ID" useGeneratedKeys="true">
        insert into ecpm_t_sharedPackage_order(subEnterpriseID,quota,operType,effectivetime,createTime,updateTime)
        values (#{subEnterpriseID},${quota},#{operType},#{effectivetime},#{createTime},#{updateTime})
    </insert>

    <select id="getDeductionCount" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(quota),0)
        FROM
            ecpm_t_sharedPackage_order
        WHERE
            subEnterpriseID = #{subEnterpriseID}
            AND operType = '2'
            AND MONTH ( effectivetime ) = MONTH (DATE_ADD( CURRENT_DATE, INTERVAL 1 MONTH ))
            AND YEAR ( effectivetime ) = YEAR (DATE_ADD( CURRENT_DATE, INTERVAL 1 MONTH ));
    </select>

    <select id="getDataGroupByEnterpriseID" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SharedPackageOrderWrapper">
        SELECT
            subEnterpriseID,
            SUM( CASE WHEN operType = '1' THEN quota ELSE - quota END ) AS quota
        FROM
            ecpm_t_sharedPackage_order
        WHERE DATE_FORMAT(effectivetime,'%Y-%m') &lt;= DATE_FORMAT(NOW(),'%Y-%m')
        GROUP BY subEnterpriseID;
    </select>

</mapper>

