var app = angular.module("myApp", ["util.ajax", "page", "angularI18n", "cy.uploadify", "cy.uploadifyfile", "top.menu", "service.common"])
app.controller('rxBlacklistCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', 'CommonUtils', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {
    $scope.init = function () {

        $scope.contentDescText = "请输入正确的号码";
        $scope.isSuperManager = false;
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');

        $scope.blackWhiteList = [];
        $scope.selectedList = [];
        $scope.hotlineNoVali = true;
        $scope.operatorID = $.cookie('accountID');
        
        $scope.errorInfo = "";
        $scope.fileUrl = "";
        // 上传excel
        $scope.accepttype = "xlsx";
        $scope.isValidate = true;
        $scope.filesize = 20;
        $scope.mimetypes = ".xlsx,.xls";
        $scope.auto = true;
        $scope.isCreateThumbnail = false;
        $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
        $scope.uploadDesc = "必填，仅支持xlsx格式的文件";
        $scope.numlimit = 1;
        $scope.urlList = [];
        $scope.uploadParam = {
            enterpriseId: 'deliveryNoBlackList',
            fileUse: 'batchCreateRxBlackList'
        };
        
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '10',
                "currentPage": 1
            }
        ];

        //初始化搜索条件
        $scope.initSel = {
        	hotlineNo: ""
        };

        $scope.queryDeliveryNoBlackList();
    };
    
    $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
	    if (broadData.file !== "") {
	      $scope.fileName = broadData.file.name;
	    } else {
	      $scope.fileName = "";
	    }
	    $scope.uploader = broadData.uploader;
	    $scope.errorInfo = broadData.errorInfo;
	    $scope.fileUrl = fileUrl;
	});
    
    // 新增弹窗
    $scope.addDeliveryNoBlackList = function () {
        $scope.addDeliveryNoBlackListInfo = {};
        $('#addDeliveryNoBlackList').modal();
        
        $scope.hotlineNoVali = true;
    };

    $scope.beforeCommit = function () {
        var addReq = {
            "operType": 1,
            "servType": 2,
            "hotlineNoList": [$scope.addDeliveryNoBlackListInfo.hotlineNo]
        };
        $scope.saveDeliveryNoBlackList(addReq);
        
        setTimeout(function () {
        	$('#addDeliveryNoBlackListCancel').click();
            $scope.queryDeliveryNoBlackList();
        },1200);
    };

    // 删除、批量删除按钮事件
    $scope.deleteDeliveryNoBlackList = function (item) {
        $scope.selectedItemDel = item;
        $('#deleteDeliveryNoBlackList').modal();
    };
    // 删除号码投递黑名单
    $scope.delDeliveryNoBlackList = function () {
        var item = $scope.selectedItemDel;
        var removeReq = {
        		"operType": 2,
        		"servType": 2
        };
        if (item) {
        	removeReq.hotlineNoList = [item.hotlineNo];
        } else {
        	removeReq.hotlineNoList = $scope.selectedListTemp;
        }
        $scope.saveDeliveryNoBlackList(removeReq);
    };
    $scope.saveDeliveryNoBlackList = function (req) {
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/blackWhiteService/saveDeliveryNoBlackList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode == '1010100000') {
                    	if (req.operType == 1) {
                    		$('#addDeliveryNoBlackListCancel').click();
                    	} else {
                    		$('#deleteDeliveryNoBlackListCancel').click();
                            $scope.tip = "删除成功";
                            $('#myModal').modal();
                    	}
                        $scope.queryDeliveryNoBlackList('justPage');
                    } else if(result.resultCode == '1010120085') {
	                    $scope.tip = "与白名单、红名单冲突";
	                    $('#myModal').modal();
	                } else if(result.resultCode == '1010120086') {
	                    $scope.tip = "已存在记录";
	                    $('#myModal').modal();
	                } else {
                        $scope.tip = result.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                	if (req.operType == 1) {
                		$('#addDeliveryNoBlackListCancel').click();
                	} else {
                		$('#deleteDeliveryNoBlackListCancel').click();
                	}
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };
    // 校验热线号码
    $scope.checkHotlineNo = function () {
        $scope.hotlineNoVali = true;
        if ($scope.addDeliveryNoBlackListInfo.hotlineNo) {
        	var reg = /^[0-9]{11}$/;
            if (!reg.test($scope.addDeliveryNoBlackListInfo.hotlineNo)) {
            	$scope.hotlineNoVali = false;
            };
        } else {
            $scope.hotlineNoVali = false;
        }
    };
    $scope.generateReq = function () {
    	var req = {
            "hotlineNo": $scope.initSel.hotlineNo || '',
            "servType": 2,
            "page": {
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[0].pageSize),
                "isReturnTotal": "1",
            }
        };
    	return req;
    };
    //查询号码投递黑名单列表
    $scope.queryDeliveryNoBlackList = function (condition) {
        $scope.selectedList = [];
        $scope.selectedListTemp = [];
        if (condition != 'justPage') {
            var req = $scope.generateReq();
            $scope.pageInfo[0].currentPage = 1;
            $scope.queryDeliveryNoBlackListTemp = angular.copy(req);
        } else {
            //如果只是分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
            var req = $scope.queryDeliveryNoBlackListTemp;
            req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
            req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
        }
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/blackWhiteService/queryDeliveryNoBlackList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                	$scope.allChoose = false;
                    var data = result.result;
                    if (data.resultCode == '1010100000') {
                        $scope.blackWhiteList = result.blackWhiteList || [];
                        $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
                        $scope.pageInfo[0].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
                        if($scope.blackWhiteList.length === 0 && $scope.pageInfo[0].totalCount > 0 && $scope.pageInfo[0].currentPage > 1) {
                            $scope.pageInfo[0].currentPage = $scope.pageInfo[0].currentPage - 1;
                            $scope.queryDeliveryNoBlackList('justPage');
                        }
                    } else {
                        $scope.blackWhiteList = [];
                        $scope.pageInfo[0].currentPage = 1;
                        $scope.pageInfo[0].totalCount = 0;
                        $scope.pageInfo[0].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.blackWhiteList = [];
                    $scope.pageInfo[0].currentPage = 1;
                    $scope.pageInfo[0].totalCount = 0;
                    $scope.pageInfo[0].totalPage = 1;
                    $scope.tip = '1030120500';
                    $('#myModal').modal();
                })
            }
        });
    };

    //改变选择框
    $scope.changeSelected = function (item) {
      if ($.inArray(item.hotlineNo, $scope.selectedListTemp) == -1) {
        $scope.selectedListTemp.push(item.hotlineNo);
        $scope.selectedList.push(item.hotlineNo);
      } else {
        $scope.selectedListTemp.splice($.inArray(item.hotlineNo, $scope.selectedListTemp), 1);
        $scope.selectedList.splice($.inArray(item.hotlineNo, $scope.selectedList), 1);
      }
      if ($scope.selectedListTemp.length == $scope.blackWhiteList.length) {
        $scope.allChoose = true;
      } else {
        $scope.allChoose = false;
      }
    };

    //更改全选框
    $scope.ifSelected = function () {
        $scope.allChoose = !$scope.allChoose;
      angular.forEach($scope.selectedListTemp, function (itemTemp) {
        $scope.selectedList.splice($.inArray(itemTemp.hotlineNo, $scope.selectedList), 1);
      });
      if ($scope.allChoose) {
        $scope.selectedListTemp = [];
        angular.forEach($scope.blackWhiteList, function (item) {
          item.checked = true;
          $scope.selectedList.push(item.hotlineNo);
          $scope.selectedListTemp.push(item.hotlineNo);
        })
      } else {
        angular.forEach($scope.blackWhiteList, function (item) {
            item.checked = false;
            $scope.selectedListTemp = [];
        })

      }
    };
    $scope.canclePop = function(event) {
      event.preventDefault()
    }
    // 导入
    $scope.impoDeliveryNoBlackList = function () {
        $('#importPop').modal();
        $('#importPop').on('hidden.bs.modal', function () {
          $rootScope.$apply(function () {
            $("#filePicker").find("span").text("导入文件");
            if ($scope.uploader) {
              $scope.uploader.reset();
            }
            $scope.errorInfo = "";
            $scope.fileName = "";
            $scope.fileUrl = "";
          })
        });
    };
    $scope.import = function () {
      var req = {
          "path": $scope.fileUrl,
          "servType": 2
      };
      RestClientUtil.ajaxRequest({
        type: 'POST',
        url: "/ecpmp/ecpmpServices/blackWhiteService/importDeliveryNoBlackList",
        data: JSON.stringify(req),
        success: function (data) {
          $rootScope.$apply(function () {
            var result = data.result;
            if (result.resultCode == '1030100000') {
              $('#importPop').modal("hide");
              $scope.tip = "导入成功";
              $('#myModal').modal();
              $scope.queryDeliveryNoBlackList();
            } else if(result.resultCode == '1010120085') {
                $('#importPop').modal("hide");
                $scope.tip = "与白名单、红名单冲突";
                $('#myModal').modal();
            } else if(result.resultCode == '1010120086') {
                $('#importPop').modal("hide");
                $scope.tip = "已存在记录";
                $('#myModal').modal();
            } else if(result.resultCode == '1030120050') {
                $('#importPop').modal("hide");
                $scope.tip = "请导入正确的号码";
                $('#myModal').modal();
            } else {
              $scope.tip = result.resultCode;
              $('#myModal').modal();
            }
          })
        },
        error: function () {
          $rootScope.$apply(function () {
            $scope.tip = '1030120500';
            $('#myModal').modal();
          })
        }
      });
    };
}]);
app.filter("formatDate", function () {
    return function (date) {
        if (date) {
            return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
        }
        return "";
    }
})