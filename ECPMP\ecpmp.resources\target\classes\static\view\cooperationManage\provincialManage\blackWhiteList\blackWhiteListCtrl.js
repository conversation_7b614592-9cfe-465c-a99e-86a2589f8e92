var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n", "top.menu", "cy.uploadifyfile","service.common"])
//自定义filter,格式化日期
app.filter('newDate', function () {
  return function (date) {
    var new_date = date.substr(0, 4) + "-" + date.substr(4, 2) + "-" + date.substr(6.2);
    return new_date;
  }
});
app.controller('blackWhiteListCtrl', function ($scope, $rootScope, $filter, $location, RestClientUtil,CommonUtils) {
  //初始化参数
  $scope.init = function () {
    $scope.token = $.cookie("token");
    $scope.id = $location.search().id || '';
    $scope.selectedProvince = null;
    $scope.uniqueTip = "";
    //获取enterpriseID
    $scope.enterpriseID = parseInt($.cookie('enterpriseID'));
    $scope.enterpriseName = $.cookie('enterpriseName') || '180';
    $scope.accountID = $.cookie('accountID') || '1000';
    $scope.isSuperManager = false;
    var loginRoleType = $.cookie('loginRoleType');
    $scope.enterpriseType=$.cookie('enterpriseType');
    if($scope.enterpriseType =='3'){
    	$scope.enterpriseID = parseInt($.cookie('subEnterpriseID'));
    }
    $scope.isSuperManager = (loginRoleType == 'superrManager' || loginRoleType == 'normalMangager');
    $scope.isAgent = (loginRoleType=='agent');
    $scope.isProvincial = (loginRoleType=='provincial');
    $scope.isZhike = (loginRoleType=='zhike');
    $scope.errorInfo = "";
    $scope.fileUrl = "";
    // 上传excel
    $scope.accepttype = "xlsx";
    $scope.isValidate = true;
    $scope.filesize = 20;
    $scope.mimetypes = ".xlsx,.xls";
    $scope.auto = true;
    $scope.isCreateThumbnail = false;
    $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadFile';
    $scope.uploadDesc = "必填，仅支持xlsx格式的文件";
    $scope.numlimit = 1;
    $scope.urlList = [];
    $scope.uploadParam = {
      enterpriseId: $scope.enterpriseID,
      fileUse: 'batchCreateMember'
    };

    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1, //总页数
        "totalCount": 0, // 总条数
        "pageSize": "10", //每页展示个数
        "currentPage": 1 // 当前页码
      }
    ];
    //搜索时初始化参数
    $scope.initSel = {
      enterpriseName: "", //企业名称
      organizationID: "", //企业机构代码
      provinceName: "0" //归属地
    };
    $scope.queryBlackWhiteListFun();
    $scope.id = JSON.parse($.cookie("enterpriseID"));
    $scope.queryEnterpriseDetails($scope);
    if ($scope.enterpriseType =='5' && $scope.isSuperManager)
    {
        $scope.proSupServerType = $.cookie('proSupServerType');
    }
    if($scope.enterpriseInfo && $scope.enterpriseInfo.enterpriseType =='5' && $scope.reserved10 != null && $scope.reserved10 != undefined
			&& ($scope.reserved10 == '111' || $scope.reserved10 == '112')) {
		  $scope.subServTypeList = [
	      	// {id:1,name:"主叫彩印"},
	      	{id:2,name:"被叫彩印"},
	      	{id:3,name:"主被叫彩印"},
	      	{id:4,name:"被叫挂机短信"},
	      	{id:8,name:"挂机彩信"},
	      	{id:16,name:"增强彩信"}
	      ];
	  } else {
		  $scope.subServTypeList = [
	      	// {id:1,name:"主叫彩印"},
	      	{id:2,name:"被叫彩印"},
	      	{id:3,name:"主被叫彩印"},
	      	{id:4,name:"被叫挂机短信"},
	      	{id:14,name:"主叫挂机短信"},
	      	{id:8,name:"挂机彩信"},
	      	{id:16,name:"增强彩信"}
	      ];
	  }
  };

  $scope.canclePop = function(event) {
    event.preventDefault()
  }
  $scope.$on("uploadifyid", function (event, fileUrl, index, broadData) {
    if (broadData.file !== "") {
      $scope.fileName = broadData.file.name;
    } else {
      $scope.fileName = "";
    }
    $scope.uploader = broadData.uploader;
    $scope.errorInfo = broadData.errorInfo;
    $scope.fileUrl = fileUrl;
  });
  // 上传excel  END


  //下拉框(名单类型)
  $scope.listTypeChoise = [
    {
      id: 1,
      name: "黑名单"
    },
    {
      id: 2,
      name: "白名单"
    }
  ];
  //下拉框(业务类型)
  $scope.businessTypeChoise = [
    {
      id: 3,
      name: "广告彩印"
    },
    {
      id: 2,
      name: "热线彩印"
    }
  ];
  $scope.businessTypeChoise2 = [
    {
      id: 1,
      name: "名片彩印"
    },
    // {
    //   id: 3,
    //   name: "广告彩印"
    // },
    {
      id: 2,
      name: "热线彩印"
    }
  ];
  //直客搜索下拉框
  $scope.businessTypeChoiseZk = [
    {
      id: 1,
      name: "名片彩印"
    },
    {
      id: 3,
      name: "广告彩印"
    },
    {
      id: 2,
      name: "热线彩印"
    }
  ];
  //下拉框(业务形态)
  $scope.subServTypeChoise = [
    {
      id: 2,
      name: "被叫屏显"
    },
    {
      id: 4,
      name: "挂机短信"
    },
    {
      id: 8,
      name: "挂机彩信"
    }
  ];
  $scope.servTypeMap = {
    1: "名片彩印",
    2: "热线彩印",
    // 3: "广告彩印",
  };
  $scope.subServTypeMap = {
    // 1: "主叫屏显",
    // 2: "被叫屏显",
    // 4: "挂机短信",
    // 8: "挂机彩信"
	1: "主叫彩印",
	2: "被叫彩印",
	3: "主被叫彩印",
	4: "被叫挂机短信",
	8: "挂机彩信",
	14: "主叫挂机短信",
	16: "增强彩信"
  };
  // select 数据
  $scope.selectBusList = [
	// {
	//   id: 1,
	//   name: "名片彩印"
	// },
	{
	  id: 2,
	  name: "热线彩印"
	}
	// {
	//   id: 3,
	//   name: "广告彩印"
	// }  
  ];
  // selectChange
  $scope.selectChange = function(){
	if($scope.addInfo.businessType == "" || $scope.addInfo.businessType == null || $scope.addInfo.businessType == undefined){
	  if ($scope.isSuperManager) {
		  if($scope.enterpriseInfo.enterpriseType =='5' && $scope.reserved10 != null && $scope.reserved10 != undefined 
					&& ($scope.reserved10 == '111' || $scope.reserved10 == '112')) {
			  $scope.subServTypeList = [
				// {id:1,name:"主叫彩印"},
				{id:2,name:"被叫彩印"},
				{id:3,name:"主被叫彩印"},
				{id:4,name:"被叫挂机短信"},
				{id:8,name:"挂机彩信"},
				{id:16,name:"增强彩信"}
			];
		  } else {
			  $scope.subServTypeList = [
  				// {id:1,name:"主叫彩印"},
  				{id:2,name:"被叫彩印"},
  				{id:3,name:"主被叫彩印"},
  				{id:4,name:"被叫挂机短信"},
  			  	{id:14,name:"主叫挂机短信"},
  				{id:8,name:"挂机彩信"},
  				{id:16,name:"增强彩信"}
  			];
		  }
	  } else if ($scope.isAgent) {
		  $scope.subServTypeList = [
  				// {id:1,name:"主叫彩印"},
  				{id:2,name:"被叫彩印"},
  				{id:3,name:"主被叫彩印"},
  				{id:4,name:"被叫挂机短信"},
  			  	{id:14,name:"主叫挂机短信"},
  				{id:8,name:"挂机彩信"},
  				{id:16,name:"增强彩信"}
  			];
	  } else if($scope.isProvincial) {
		  if($scope.enterpriseInfo.enterpriseType =='5' && $scope.reserved10 != null && $scope.reserved10 != undefined 
					&& ($scope.reserved10 == '111' || $scope.reserved10 == '112')) {
			  $scope.subServTypeList = [
					// {id:1,name:"主叫彩印"},
					{id:2,name:"被叫彩印"},
					{id:3,name:"主被叫彩印"},
					{id:4,name:"被叫挂机短信"},
					{id:8,name:"挂机彩信"},
					{id:16,name:"增强彩信"}
				];
		  } else {
			  $scope.subServTypeList = [
  				// {id:1,name:"主叫彩印"},
  				{id:2,name:"被叫彩印"},
  				{id:3,name:"主被叫彩印"},
  				{id:4,name:"被叫挂机短信"},
  			  	{id:14,name:"主叫挂机短信"},
  				{id:8,name:"挂机彩信"},
  				{id:16,name:"增强彩信"}
  			];
		  }
	  } else {
		  $scope.subServTypeList = [
				// {id:1,name:"主叫彩印"},
				{id:2,name:"被叫彩印"},
				{id:3,name:"主被叫彩印"},
				{id:4,name:"挂机短信"},
				{id:8,name:"挂机彩信"},
				{id:16,name:"增强彩信"}
			];
	  }
	}
	if($scope.addInfo.businessType == "2"){
		if ($scope.isSuperManager 
				|| $scope.isAgent
				|| $scope.isProvincial
				|| ($scope.enterpriseInfo.enterpriseType =='5' && $scope.reserved10 != null && $scope.reserved10 != undefined 
						&& ($scope.reserved10 == '111' || $scope.reserved10 == '112'))) {
				$scope.subServTypeList = [
      					// {id:1,name:"主叫彩印"},
      					{id:2,name:"被叫彩印"},
      					{id:3,name:"主被叫彩印"},
      					{id:4,name:"被叫挂机短信"},
      					{id:8,name:"挂机彩信"},
      					{id:16,name:"增强彩信"}
      				];
		  } else {
			  $scope.subServTypeList = [
					// {id:1,name:"主叫彩印"},
					{id:2,name:"被叫彩印"},
					{id:3,name:"主被叫彩印"},
					{id:4,name:"挂机短信"},
					{id:8,name:"挂机彩信"}
				];
		  }
	}
	// if($scope.addInfo.businessType == "3"){
	// 	$scope.subServTypeList = [
	// 		{id:4,name:"挂机短信"},
	// 		{id:8,name:"挂机彩信"},
	// 	]
	// }
  };
  
  $scope.queryEnterpriseDetails = function ($scope) {
	    var req = {};
	    req.id = $scope.id;
	    var pageParameter = {};
	    pageParameter.pageNum = 1;
	    pageParameter.pageSize = 1000;
	    pageParameter.isReturnTotal = 1;
	    req.pageParameter = angular.copy(pageParameter);
	    /*查询企业列表*/
	    RestClientUtil.ajaxRequest({
	      type: 'POST',
	      url: "/ecpmp/ecpmpServices/enterpriseManageService/queryEnterpriseInfo",
	      data: JSON.stringify(req),
	      success: function (data) {
	        $rootScope.$apply(function () {
	          var result = data.result;
	          if (result.resultCode == '**********') {
	            $scope.enterpriseInfo = data.enterprise;
	            $scope.reserved10 = $scope.enterpriseInfo.reservedsEcpmp.reserved10;
	          } else {
	              $scope.tip = result.resultCode;
	              $('#myModal').modal();
	          }
	        })
	      },
	      error:function(){
	          $rootScope.$apply(function(){
	              $scope.tip='1030120500';
	              $('#myModal').modal();
	              }
	          )
	      }
	    });
	  };
  
  //获取queryEnterpriseList接口的数据
  $scope.queryBlackWhiteListFun = function (condition) {
	$scope.selectedListTemp = [];
	$scope.selectedList = [];
	$scope.allChoose = false;
    $('#delOrgCancel').click();
    $('.addMemCancel').click();
    if (condition != 'justPage') {
      var req = {
        "blackWhite": {
          "blackWhiteListType": $scope.listType,
          "servType": $scope.businessType,
          "msisdn": $scope.msisdn,
          "enterpriseID": $scope.enterpriseID
        },
        "page": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1",
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      var req = $scope.reqTemp;
      req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/blackWhiteService/queryBlackWhiteList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          if (result.result.resultCode == '**********') {
            $scope.queryBlackWhiteList = result.blackWhiteList;
            //获取页面的总条数与总页面
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            if ($scope.pageInfo[0].totalCount == 0) {
              // $scope.pageInfo[0].totalPage=0;
              $scope.pageInfo[0].currentPage = 1;
              $scope.pageInfo[0].totalCount = 0;
              $scope.pageInfo[0].totalPage = 1;
            } else {
              $scope.pageInfo[0].totalPage = Math.ceil(parseInt(result.totalNum) / parseInt($scope.pageInfo[0].pageSize));
            }
          } else {
            $scope.queryBlackWhiteList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = result.result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function (data) {
          $scope.tip = "1030120500";
          $('#myModal').modal();
        })
      }
    })
  }

  //改变选择框
  $scope.changeSelected = function (item) {
    if ($.inArray(item, $scope.selectedListTemp) == -1) {
      $scope.selectedListTemp.push(item);
      $scope.selectedList.push(item);
    } else {
      $scope.selectedListTemp.splice($.inArray(item, $scope.selectedListTemp), 1);
      $scope.selectedList.splice($.inArray(item, $scope.selectedList), 1);
    }
    if ($scope.selectedListTemp.length == $scope.queryBlackWhiteList.length) {
      $scope.allChoose = true;
    } else {
      $scope.allChoose = false;
    }
  };

  //更改全选框
  $scope.ifSelected = function () {
    angular.forEach($scope.selectedListTemp, function (itemTemp) {
      $scope.selectedList.splice($.inArray(itemTemp, $scope.selectedList), 1);

    });
      $scope.allChoose = !$scope.allChoose;
    if ($scope.allChoose) {
      $scope.selectedListTemp = [];
      angular.forEach($scope.queryBlackWhiteList, function (item) {
        item.checked = true;
        $scope.selectedList.push(item);
        $scope.selectedListTemp.push(item);
      })
    } else {
      angular.forEach($scope.queryBlackWhiteList, function (item) {
        item.checked = false;
        $scope.selectedListTemp = [];
      })

    }
  };
  
  //批量删除
  $scope.removeMemberPop =  function (item) {
	  if (item != 'all') {
		  $scope.oneSelect = true;
		  $scope.deleteSelect = item;
	  }else{
		  $scope.oneSelect = false; 
	  };
	  $('#deleteGroupPop').modal();
  };
  //删除
  $scope.toDelete = function (item) {
	$scope.oneSelect = true; 
    $scope.deleteSelect = item;
    $('#deleteGroupPop').modal();
  };
  //删除名单
  $scope.removeOrg = function (i) {
    var item = $scope.deleteSelect;
    if (i != 'all') {
    	var removeReq = {
	      "blackWhiteList": [
	        {
	          "blackWhiteListType": item.blackWhiteListType,
	          "msisdn": item.msisdn,
	          "enterpriseID": $scope.enterpriseID,
	          "servType": item.servType,
	          "subServType": item.subServType
	        }
	      ]
	    };
      } else {
    	  var removeReq = {
	      "blackWhiteList": $scope.selectedListTemp
	    };
      };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/blackWhiteService/deleteBlackWhiteList",
      data: JSON.stringify(removeReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            // $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
            $scope.queryBlackWhiteListFun();
            $('#delOrgCancel').click();
            $scope.tip = "删除成功";
            $('#myModal').modal();
          } else {
            $scope.tip = result.resultCode;
            $('#delOrgCancel').click();
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#delOrgCancel').click();
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  };
  //新增黑白名单
  $scope.toAddMen = function (id) {
    $scope.checkUnique = true;
    $scope.addInfo = {
      "msisdn": "",
      "subServType": "",
      "servType": ""
    }
    //重置表单校验
    $scope.blackListForm.$setPristine();
    $scope.blackListForm.$setUntouched();
    $scope.whiteListForm.$setPristine();
    $scope.whiteListForm.$setUntouched();
    $scope.selectChange();
    $('#' + id).modal();
  };
  //新增黑白名单
  $scope.addBlackWhiteList = function (type) {
    var addReq = {
      "organizationID": $scope.organizationID,
      "blackWhiteList": [
        {
          "blackWhiteListType": type,
          "msisdn": $scope.addInfo.msisdn,
          "servType": $scope.addInfo.businessType == undefined ? type == 3 ? 2 : 1 : $scope.addInfo.businessType,
          "subServType": $scope.addInfo.listType == undefined ? 1 : $scope.addInfo.listType,
          "enterpriseID": $scope.enterpriseID
        }
      ]
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/blackWhiteService/addBlackWhiteList",
      data: JSON.stringify(addReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            // $scope.selectedList.splice($.inArray(item.id, $scope.selectedList), 1);
            $scope.queryBlackWhiteListFun();
          } else if(result.resultCode == '1010120087') {
              $scope.tip = "与号码投递黑名单冲突";
              $('#myModal').modal();
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  };
  
  // (change)=" changeValue($event.target.value)"

  /*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };

  $scope.msisdnValidate = true;
  $scope.checkMsisdn = function () {
    if($scope.addInfo.msisdn != ""){
      $scope.msisdnValidate = $scope.validate($scope.addInfo.msisdn, 11, /[0-9]{11}$/);
      if(!$scope.msisdnValidate){
        $scope.uniqueTip = "号码必填，仅支持11位数字输入";
        $scope.checkUnique = false;
      }
    }
  }
  // 黑白名单号码唯一性校验
  $scope.checkDataUnique = function (type) {
    $scope.uniqueTip = "";
    $scope.checkUnique = true;
    $scope.checkMsisdn();
    if(!$scope.msisdnValidate){
      return;
    }
    var uniqueReq = {
      "serviceType":5,
      "content":$scope.addInfo.msisdn,
      "enterpriseID":$scope.enterpriseID,
      "extension": 
        {
          "blackwhiteListType": type,
          "servType": $scope.addInfo.businessType == undefined ? 1 : $scope.addInfo.businessType,
          "subServType": $scope.addInfo.listType == undefined ? 1 : $scope.addInfo.listType,
        }
    };

    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/commonService/checkDataUnique",
      data: JSON.stringify(uniqueReq),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
          }else if (result.resultCode == '1030120018') { 
            $scope.uniqueTip = "该类型下名单号码已存在";
            $scope.checkUnique = false;
          }else if (result.resultCode == '1030120000') { 
            $scope.tip = result.resultCode;
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }

  $scope.formatDate = function (str) {
    if (!str) {
      return 'format error';
    }
    var newDateStr = "";
    newDateStr = str.substr(0, 4) + '-' + str.substr(4, 2) + '-' + str.substr(6, 2);
    return newDateStr;

  }

  //搜索省份改变时，找到对应的市
  $scope.changeSelectedProvince = function (selectedProvince) {
    $scope.subCityList = null;
    if (selectedProvince) {
      jQuery.each($scope.cityList, function (i, e) {
        if (e.key == selectedProvince.provinceID) {
          $scope.subCityList = e;
          $scope.selectedCity = $scope.subCityList[0];
        }
      });
    }
    if (!selectedProvince) {
      $scope.subCityList = null;
    }
  }

  //导入成员
  $scope.impotMeb = function () {
    $('#impoMebrPop').modal();
    $('#impoMebrPop').on('hidden.bs.modal', function () {
      $rootScope.$apply(function () {
        $("#filePicker").find("span").text("导入文件");
        if ($scope.uploader) {
          $scope.uploader.reset();
        }
        $scope.errorInfo = "";
        $scope.fileName = "";
        $scope.fileUrl = "";
      })

    });
  };
  $scope.importMember = function () {
    var req = {
      "enterpriseID": $scope.enterpriseID,
      "operatorID": $scope.accountID,
      "orgID": $scope.orgID,
      "path": $scope.fileUrl,
      "servType": 1
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/blackWhiteService/batchAddBlackWhite",
      data: JSON.stringify(req),
      success: function (data) {
        $rootScope.$apply(function () {
          var result = data.result;
          if (result.resultCode == '**********') {
            $('#impoMebrPop').modal("hide");
            $scope.tip = "导入成功";
            $('#myModal').modal();
          } else if (data.failedListUrl) {
            $('#impoMebrPop').modal("hide");
            $scope.tip = data.failedNum + "条导入失败，请查看失败文件";
            $('#myModal').modal();
            var req = {
              "param": {
                "path": data.failedListUrl,
                "token": $scope.token,
                "isExport": 0
              },
              "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
              "method": "get"
            }
            CommonUtils.exportFile(req);
          } else {
            $scope.tip = result.resultCode;
            $('#myModal').modal();
          }
          $scope.queryBlackWhiteListFun();
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip = '1030120500';
          $('#myModal').modal();
        })
      }
    });
  }

})