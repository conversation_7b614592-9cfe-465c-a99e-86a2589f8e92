<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<link rel="stylesheet" type="text/css" href="../../../../css/bootstrap.min.css" />
<link href="../../../../css/reset.css" rel="stylesheet" />
<link href="../../../../css/mian.css" rel="stylesheet"/>
<link href="../../../../css/datepicker3.css" rel="stylesheet"/>
<link href="../../../../css/layout.css" rel="stylesheet"/>
<link href="../../../../css/searchList.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../service/utils/service-ajax.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/moment/moment.min.js"></script>
<link rel="stylesheet" href="../../../../css/font-awesome.min.css"/>
<script type="text/javascript" src="../../../../service/utils/service-common.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.min.js"></script>
<script type="text/javascript" src="../../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"></script>
<link href="../../../../css/bootstrap-datepicker.css" rel="stylesheet">
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../directives/page/page.css"/>
<script type="text/javascript" src="../../../../directives/page/page.js"></script>
<link href="../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="queryOrderList.js"></script>

	<style>
		.daterangepicker td, .daterangepicker th {
			width:auto;
		}
		.calendar-i{
			position: absolute;
			bottom: 10px;
			right: 24px;
			top: auto;
			cursor: pointer;
		}
		body,html{
			overflow: auto;
		}
		.cooperation-manage .add-table .add-btn .export-icon {
			background: url(../../../../assets/images/btnIcons18.png)no-repeat;
			vertical-align: middle;
   	 		background-position: -107px 0px;
		}

		@media (max-width: 1366px){
			.class1 {
				max-width: 280px;
				min-width: 180px;
				padding-right: 0!important;
			}
			.class1 div:nth-child(1){
				padding: 6px 0!important;
				width: 56px!important;
			}
			.search-content1{
				margin-left: 60px!important;
			}
			.search-content2{
				margin-left: 32px!important;
			}
			.search-content3{
				margin-left: 32px!important;
			}
			.class2 {
				max-width: 280px;
				min-width: 180px;
				padding-right: 0!important;
			}
			.class2 div:nth-child(1){
				padding: 6px 0!important;
				width: 28px!important;
			}
			.class3 {
				padding-right: 0!important;
			}
			.class3 div:nth-child(1){
				padding: 6px 0!important;
				width: 28px!important;
			}
		} 
		
		@media (max-width: 1280px){
			.class1 {
				max-width: 230px;
				min-width: 190px;
				padding-right: 0!important;
			}
			.class1 div:nth-child(1){
				padding: 6px 0!important;
				width: 56px!important;
			}
			.search-content1{
				margin-left: 60px!important;
			}
			.search-content2{
				margin-left: 32px!important;
			}
			.search-content3{
				margin-left: 32px!important;
			}
			.class2 {
				max-width: 230px;
				min-width: 190px;
				padding-right: 0!important;
			}
			.class2 div:nth-child(1){
				padding: 6px 0!important;
				width: 28px!important;
			}
			.class3 {
				padding-right: 0!important;
			}
			.class3 div:nth-child(1){
				padding: 6px 0!important;
				width: 28px!important;
			}
    	} 

	    @media (max-width: 1100px){
			.class1 {
				max-width: 200px;
				min-width: 180px;
				padding-right: 0!important;
			}
			.class1 div:nth-child(1){
				padding: 6px 0!important;
				width: 56px!important;
			}
			.search-content1{
				margin-left: 60px!important;
			}
			.search-content2{
				margin-left: 32px!important;
			}
			.search-content3{
				margin-left: 32px!important;
			}
			.class2 {
				max-width: 200px;
				min-width: 180px;
				padding-right: 0!important;
			}
			.class2 div:nth-child(1){
				padding: 6px 0!important;
				width: 28px!important;
			}
			.class3 {
				padding-right: 0!important;
			}
			.class3 div:nth-child(1){
				padding: 6px 0!important;
				width: 28px!important;
			}
    	} 

	</style>
</head>
<body ng-app='myApp' ng-controller='orderListController' ng-init="init();" class="body-min-width">
    <div class="cooperation-manage" >
        <div class="cooperation-head"><span class="frist-tab" ng-bind="'QUERYORDERDETAIL_H5SET'|translate" ></span>&nbsp;&gt;&nbsp;<span class="second-tab" ng-bind="'QUERYORDERDETAIL_ORDERSET'|translate" ></span></div>
	   
		<div class="cooperation-search">
            <form class="form-horizontal">
				<div class="form-group">
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 cond-div class1">
						<div style="float:left;padding: 6px 12px;width: 80px;" ng-bind="'ORDER_CODE'|translate"></div>
						<div style="margin-left: 80px;" class="search-content1">
							<input type="text" autocomplete="off" class="form-control" id="orderCode" placeholder="{{'QUERYORDERDETAIL_ORDERCODEPLACEHOLDER'|translate}}"   ng-model="initSel.orderCode">
						</div>
					</div>
					
					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 cond-div class1">
						<div style="float:left;padding: 6px 12px;width: 80px;" ng-bind="'QUERYORDERDETAIL_ENTERPRISENAME'|translate"></div>
						<div style="margin-left: 80px;" class="search-content1">
							<input type="text" autocomplete="off" class="form-control" id="enterpriseName" placeholder="{{'QUERYORDERDETAIL_ENTERPRISENAMEPLACEHOLDER'|translate}}" ng-model="initSel.enterpriseName">
						</div>
					</div>
					
					<div class="col-lg-2 col-md-2 col-sm-2 col-xs-2  cond-div class2">
						<div style="float:left;padding: 6px 12px;width: 52px;text-align: right;" ng-bind="'ITEM_PAYSTATUS'|translate"></div>
						<div style="margin-left: 52px;" class="search-content2">
							<select id="payStatus" class="form-control" ng-model="initSel.payStatus"
							ng-options="x.id as x.name for x in payStatusChoise"></select>
						</div>
					</div>

					<div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 time cond-div class3" style="min-width: 285px;" >
						<div style="float:left;padding: 6px 12px;width: 52px;text-align: right;" ng-bind="'QUERYORDERDETAIL_TIME'|translate"></div>
						<div style="margin-left: 52px;position: relative;" id="datepicker" class="input-daterange input-group search-content3">
								<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="start"/>
								<span class="input-group-addon" ng-bind="'TO'|translate"></span>
								<input type="text" onfocus="this.blur()" class="input-md form-control" autocomplete="off" id="end"/>
							</div>
					</div>

					<div class="col-lg-1 col-md-1 col-sm-1 col-xs-1 cond-div" style="max-width:100px">
						<button ng-click="queryOrderList()" type="submit" class="btn search-btn" ng-disabled="initSel.search"><icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span></button>
					</div>
				</div>
			</form>
        </div>

		<div class="add-table">
			<button ng-click="exportFile()" id="exportSpokesList" type="submit" class="btn add-btn" ><icon class="export-icon"></icon><span ng-bind="'COMMON_EXPORTORDER'|translate"></span></button>
		</div>
		
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th style="width:20%" ng-bind="'ITEM_ORDERCODE'|translate"></th>
                        <th style="width:15%" ng-bind="'ITEM_EFFICTIVETIME'|translate"></th>
                        <th style="width:17%" ng-bind="'ITEM_ENTERPRISENAME'|translate"></th>
                        <th style="width:12%" ng-bind="'ITEM_QUOTAAMOUNT'|translate"></th>
                        <th style="width:12%" ng-bind="'ITEM_AMOUNT'|translate"></th>
                        <th style="width:12%" ng-bind="'ITEM_PAYCHANNEL'|translate"></th>
                        <th style="width:12%" ng-bind="'ITEM_PAYSTATUS'|translate"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in orderListData">
                        <td><span title="{{item.orderCode}}">{{item.orderCode}}</span></td>
                        <td><span title="{{getTime(item.createTime)}}">{{getTime(item.createTime)}}</span></td>
                      	<td><span title="{{item.enterpriseName}}">{{item.enterpriseName}}</span></td> 
                        <td><span title="{{item.quotaAmount=='9223372036854775807'? '不限':item.quotaAmount}}">{{item.quotaAmount=='9223372036854775807'? '不限':item.quotaAmount}}</span></td>
                        <td><span title="{{item.amount}}">{{item.amount}}</span></td>
                        <td><span title="{{getPayChannel(item.payChannel)}}">{{getPayChannel(item.payChannel)}}</span></td>
                        <td><span title="{{getPayStatus(item.payStatus)}}">{{getPayStatus(item.payStatus)}}</span></td>
                    </tr>
                    <tr ng-show="orderListData.length<=0">
                        <td style="text-align:center" colspan="9" ng-bind="'COMMON_NODATA'|translate"></td>
                    </tr>
                </tbody>
            </table>
        </div>
   
		<div>
			<ptl-page tableId="0" change="queryOrderList('justPage')"></ptl-page>
		</div>
	
    </div>

	<!--小弹出框-->
	<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		<div class="modal-dialog modal-sm" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
					<h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
				</div>
				<div class="modal-body">
					<div class="text-center"><p style='font-size: 16px;color:#383838' ng-bind="tip|translate">
						</p>
					</div>
				</div>
				<div class="modal-footer" style="text-align:center">
					<button type="submit" class="btn " data-dismiss="modal" aria-label="Close" ng-bind="'COMMON_OK'|translate"></button>
				</div>
			</div>
		</div>
	</div>
    
</body>
</html>