<!DOCTYPE html>
<html>
  <head lang="en">
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta http-equiv="X-UA-Compatible" content="IE=11" />
    <title>号码认证</title>
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../css/bootstrap.min.css"
    />
    <link href="../../../css/reset.css" rel="stylesheet" />
    <link href="../../../css/searchList.css" rel="stylesheet" />
    <script
      type="text/javascript"
      src="../../../frameworkJs/angular.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../frameworkJs/jquery-3.5.0.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../frameworkJs/jquery.cookie.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../frameworkJs/bootstrap.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../frameworkJs/angular-translate/angular-translate.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../service/angular-i18n/angular-i18n.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../service/utils/service-ajax.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../service/utils/service-common.js"
    ></script>
    <!-- 引入菜单组件 -->
    <link href="../../../directives/topMenu/topMenu.css" rel="stylesheet" />
    <script
      type="text/javascript"
      src="../../../directives/topMenu/topMenu.js"
    ></script>
    <!--分页-->
    <script
      type="text/javascript"
      src="../../../directives/page/page.js"
    ></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../directives/page/page.css"
    />
    <script
      type="text/javascript"
      src="../../../frameworkJs/webuploader.js"
    ></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../directives/preview/preview.css"
    />
    <script src="../../../directives/cy-uploadifyfile/cy-uploadifyfile.js"></script>
    <link
      href="../../../directives/cy-uploadifyfile/cy-uploadifyfile.css"
      rel="stylesheet"
    />
    <script src="../../../directives/cy-uploadify/cy-uploadify.js"></script>
    <link
      href="../../../directives/cy-uploadify/cy-uploadify.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../css/font-awesome.min.css"
    />
    <script type="text/javascript" src="numberAuthentication.js"></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="../../../css/hotlineContentManage.css"
    />
    <script
      type="text/javascript"
      src="../../../frameworkJs/bootstrap-datepicker.min.js"
    ></script>
    <script
      type="text/javascript"
      src="../../../frameworkJs/bootstrap-datepicker.zh-CN.min.js"
    ></script>
    <link href="../../../css/bootstrap-datepicker.css" rel="stylesheet" />
    <link href="../../../css/datepicker3.css" rel="stylesheet" />
    <script
      type="text/javascript"
      src="../../../frameworkJs/daterangepicker/daterangepicker.min.js"
    ></script>
    <link
      rel="stylesheet"
      href="../../../css/daterangepicker.min.css"
      rel="stylesheet"
    />

    <style>
      .cooperation-manage .coorPeration-table th,
      td {
        padding-left: 20px !important;
      }

      .handle ul li icon.view-icon {
        background-position: -55px 0;
      }

      .table th.adjustable-width {
        width: 25%;
      }

      /* media for adjustable search-table width  */
      @media (max-width: 1850px) {
        .table th.adjustable-width {
          width: 28%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      @media (max-width: 1600px) {
        .table th.adjustable-width {
          width: 30%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      @media (max-width: 1300px) {
        .table th.adjustable-width {
          width: 33%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      @media (max-width: 1100px) {
        .table th.adjustable-width {
          width: 42%;
        }

        .handle ul li {
          margin-right: 10px;
        }
      }

      .label-supply {
        display: inline-block;
        float: left;
        padding-right: 15px;
        padding-left: 15px;
      }
      .clearf:after {
        content: "";
        clear: both;
        height: 0;
        display: block;
      }

      .date-picker-container {
        position: relative;
        display: inline-block;
      }

      .date-picker-container input {
        padding-right: 30px;
      }

      .date-picker-container i {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
      }

      .input-daterange {
        padding-top: 0px !important;
      }
      /* 状态标签样式 */
      .status-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
        font-weight: 500;
      }

      .status-pending {
        background-color: #fcf8e3;
        color: #8a6d3b;
        border: 1px solid #faebcc;
      }

      .status-success {
        background-color: #dff0d8;
        color: #3c763d;
        border: 1px solid #d6e9c6;
      }

      .status-reject {
        background-color: #f2dede;
        color: #a94442;
        border: 1px solid #ebccd1;
      }
    </style>
  </head>

  <body
    ng-app="myApp"
    ng-controller="numberAuthenticationController"
    ng-init="init()"
  >
    <div class="cooperation-manage" style="overflow-x: scroll">
      <div class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_BUSSINESSMANAGE'|translate">
        </span
        >&nbsp;&gt;&nbsp;<span class="second-tab">号码认证</span>
      </div>

      <div class="cooperation-search">
        <form class="form-horizontal">
          <div
            class="form-group form-inline"
            style="margin-left: 0px; margin-right: 0px"
          >
            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="enterpriseName">公司名称</label>
            </div>
            <div
              class="col-lg-2 col-md-2 col-sm-2 col-xs-2"
              style="width: 21.666667%"
            >
              <input
                ng-model="searchParams.enterpriseName"
                type="text"
                autocomplete="off"
                class="form-control"
                id="enterpriseName"
                placeholder="请输入公司名称"
              />
            </div>

            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="auditStatus">审核状态</label>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width: 16%">
              <select
                style="max-width: 200px; width: 100%"
                class="form-control"
                ng-model="searchParams.auditStatus"
                id="auditStatus"
                ng-options="x.id as x.name for x in auditStatusOptions"
              ></select>
            </div>

            <div
              class="control-label label-supply"
              style="padding-right: 5px; padding-left: 5px"
            >
              <label for="start">首次审核时间</label>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="width: 30%">
              <div class="input-daterange input-group" id="datepicker">
                <input
                  type="text"
                  class="input-md form-control"
                  autocomplete="off"
                  id="start"
                  ng-keyup="searchOn()"
                  placeholder="开始日期"
                />
                <span class="input-group-addon" ng-bind="'TO'|translate"></span>
                <input
                  type="text"
                  class="input-md form-control"
                  autocomplete="off"
                  id="end"
                  ng-keyup="searchOn()"
                  placeholder="结束日期"
                />
              </div>
            </div>

            <div class="col-lg-2 col-xs-2 col-sm-2 col-md-2">
              <button
                ng-click="queryNumberAuthenticationList()"
                type="submit"
                class="btn search-btn"
              >
                <icon class="search-iocn"></icon>
                <span ng-bind="'COMMON_SEARCH'|translate"></span>
              </button>
            </div>
            <div class="clearf"></div>
          </div>
        </form>
      </div>

      <div class="add-table">
        <button
          id="exportNumberAuthenticationList"
          class="btn add-btn"
          ng-click="exportNumberAuthentication()"
        >
          <icon class="export-icon"></icon>
          <span
            style="color: #705de1"
            ng-bind="'DETAIL_EXPORT'|translate"
            style="margin-left: 5px"
          ></span>
        </button>
      </div>

      <div style="margin-left: 20px; margin-bottom: 20px">
        <p style="font-size: 16px; font-weight: 500">号码认证信息</p>
      </div>

      <div class="coorPeration-table">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th style="width: 15%">公司名称</th>
              <th style="width: 10%">品牌费</th>
              <th style="width: 10%">号码量</th>
              <th style="width: 15%">创建时间</th>
              <th style="width: 15%">首次审核时间</th>
              <th style="width: 15%">审核状态</th>
              <th style="width: 15%" class="adjustable-width">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="item in numberAuthenticationListData">
              <td>
                <span title="{{item.enterpriseName}}"
                  >{{item.enterpriseName}}</span
                >
              </td>
              <td>
                <span title="{{item.brandFeeFlag}}"
                  >{{item.brandFeeFlag == 0 ? '无' : '有'}}</span
                >
              </td>
              <td>
                <span title="{{item.msisdnQuantity}}"
                  >{{item.msisdnQuantity}}</span
                >
              </td>
              <td
                ng-bind="item.createTime|date:'yyyy-MM-dd HH:mm:ss'"
                title="{{item.createTime|date:'yyyy-MM-dd HH:mm:ss'}}"
              ></td>
              <td
                ng-bind="item.firstAuditTime|date:'yyyy-MM-dd HH:mm:ss'"
                title="{{item.firstAuditTime|date:'yyyy-MM-dd HH:mm:ss'}}"
              ></td>
              <td>
                <span
                  ng-class="{
                        'status-badge status-pending': item.auditStatus == '1',
                        'status-badge status-success': item.auditStatus == '2',
                        'status-badge status-reject': item.auditStatus == '3'
                    }"
                  title="{{getAuditStatusText(item.auditStatus)}}"
                >
                  {{getAuditStatusText(item.auditStatus) || '-'}}
                </span>
              </td>
              <td>
                <div class="handle">
                  <ul>
                    <!-- 查看 -->
                    <li class="view" ng-click="viewNumberAuthentication(item)">
                      <span style="color: #705de1">查看</span>
                    </li>
                  </ul>
                </div>
              </td>
            </tr>
            <!-- 没有数据时显示 -->
            <tr ng-show="numberAuthenticationListData.length<=0">
              <td
                style="text-align: center"
                colspan="7"
                ng-bind="'COMMON_NODATA'|translate"
              ></td>
            </tr>
          </tbody>
        </table>
      </div>

      <div>
        <ptl-page
          tableId="0"
          change="queryNumberAuthenticationList('justPage')"
        ></ptl-page>
      </div>
    </div>

    <!--小弹出框-->
    <div
      class="modal fade bs-example-modal-sm"
      id="myModal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="myModalLabel"
    >
      <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
            <h4
              class="modal-title"
              id="myModalLabel"
              ng-bind="'COMMON_TIP'|translate"
            ></h4>
          </div>
          <div class="modal-body">
            <div class="text-center">
              <p style="font-size: 16px; color: #383838">{{tip|translate}}</p>
            </div>
          </div>
          <div class="modal-footer">
            <button
              type="submit"
              class="btn"
              data-dismiss="modal"
              aria-label="Close"
              ng-bind="'COMMON_OK'|translate"
            ></button>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
