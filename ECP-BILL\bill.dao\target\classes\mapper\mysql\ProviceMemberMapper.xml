<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.bill.dao.mapper.ProviceMemberMapper">
    <resultMap id="proviceMemberMapper" type="com.huawei.jaguar.dsdp.bill.dao.domain.ProviceMemberWrapper">
        <result property="enterpriseID" column="enterpriseID" javaType="java.lang.String"/>
        <result property="enterpriseCode" column="enterpriseCode" javaType="java.lang.String"/>
        <result property="enterpriseName" column="enterpriseName" javaType="java.lang.String"/>
        <result property="enterpriseType" column="enterpriseType" javaType="java.lang.Integer"/>
        <result property="parentEnterpriseID" column="parentEnterpriseID" javaType="java.lang.String"/>
        <result property="parentEnterpriseCode" column="parentEnterpriseCode" javaType="java.lang.String"/>
        <result property="parentEnterpriseName" column="parentEnterpriseName" javaType="java.lang.String"/>
        <result property="serviceType" column="servType" javaType="java.lang.Integer"/>
        <result property="subServiceType" column="subServType" javaType="java.lang.Integer"/>
        <result property="msisdn" column="msisdn" javaType="java.lang.String"/>
        <result property="msisdnType" column="msisdnType" javaType="java.lang.String"/>
        <result property="memberGroup" column="memberGroup" javaType="java.lang.String"/>
        <result property="effictiveTime" column="effictiveTime" javaType="java.lang.String"/>
        <result property="chargeNumber" column="chargeMsisdn" javaType="java.lang.String"/>
        <result property="chargeType" column="chargeType" javaType="java.lang.Integer"/>
        <result property="businessID" column="businessID" javaType="java.lang.String"/>
        <result property="pkgID" column="pkgID" javaType="java.lang.String"/>
        <result property="contentApproveStatus" column="approveStatus" javaType="java.lang.String"/>
        <result property="contentId" column="contentId" javaType="java.lang.Long"/>



    </resultMap>


    <select id="queryProviceMemberContent" resultMap="proviceMemberMapper">
        select
        DISTINCT CONCAT(
             IF(es.parentEnterpriseID IS NULL, rel.enterpriseID, es.parentEnterpriseID ),
            '_',mem.msisdn,
            '_',IF( con.chargeType IS NULL, '', con.chargeType ),
            '_',IF(con.servType is null,'',con.servType),
            '_',if(con.parentID is null, IF(con.subServType is null,'',con.subServType), 3)
            ) businessID,
        con.servType,
        IF( con.parentID IS NULL, con.id, con.parentId ) contentId,
        if(con.parentID is null, con.subServType, 3) subServType,
        (select rule.chargeMsisdn from ecpm_t_sync_service_rule rule where rule.servType = con.servType and rule.subServType = if(con.parentID is null, con.subServType, 3) and rule.enterpriseID = con.enterpriseID and NOW() BETWEEN rule.effectiveTime and rule.expiryTime GROUP BY rule.chargeMsisdn) chargeMsisdn,
        mem.msisdn,
        rel.orgID memberGroup,
        mem.reserved2 msisdnType,
        con.chargeType,
        con.pkgID,
        con.approveStatus,
        con.oriContentID oriContentID,
        con.parentID parentID,
        IFNULL(tem.parentID,tem.id) belongTemplateID,
        con.contentType contentType
        from  ecpm_t_member mem
        LEFT JOIN ecpm_t_org_rel rel on mem.ID = rel.ID
        LEFT JOIN ecpm_t_content_org conorg on rel.orgID = conorg.ownerID AND(conorg.msisdn IS NULL OR conorg.msisdn=mem.msisdn)
        LEFT JOIN ecpm_t_content con on con.id = conorg.cyContID
        LEFT JOIN ecpm_t_member_subscribe sub on sub.memberID = mem.ID
        LEFT JOIN ecpm_t_enterprise_simple es ON es.ID = rel.enterpriseID
        LEFT JOIN ecpm_t_content tem ON tem.id=con.belongTemplateID
        where
         sub.`status` in (3,5)
        <if test="enterpriseType ==null || enterpriseType != 5">
            and con.approveStatus = 3
            and con.`status` = 0
        </if>
        <if test="orgID !=null and orgID != ''">
            and rel.orgID = #{orgID}
        </if>
        <if test="enterpriseID !=null and enterpriseID != ''">
            and rel.enterpriseID = #{enterpriseID}
        </if>
        <if test="contentID !=null and contentID != ''">
            and (con.id = #{contentID} or con.parentID = #{contentID})
        </if>
        <if test="msisdn !=null and msisdn != ''">
            and mem.msisdn = #{msisdn}
        </if>
        <if test="memberID !=null and memberID != ''">
            and mem.id = #{memberID}
        </if>

    </select>

    <select id="queryProviceMember" resultMap="proviceMemberMapper">
        select
        DISTINCT CONCAT(
        IF(es.parentEnterpriseID IS NULL, rel.enterpriseID, es.parentEnterpriseID ),
        '_',mem.msisdn,
        '_',IF( con.chargeType IS NULL, '', con.chargeType ),
        '_',IF(con.servType is null,'',con.servType),
        '_',if(con.parentID is null, IF(con.subServType is null,'',con.subServType), 3)
        ) businessID,
        con.servType,
        IF( con.parentID IS NULL, con.id, con.parentId ) contentId,
        if(con.parentID is null, con.subServType, 3) subServType,
        (select rule.chargeMsisdn from ecpm_t_sync_service_rule rule where rule.servType = con.servType and rule.subServType = if(con.parentID is null, con.subServType, 3) and rule.enterpriseID = con.enterpriseID and NOW() BETWEEN rule.effectiveTime and rule.expiryTime GROUP BY rule.chargeMsisdn) chargeMsisdn,
        mem.msisdn,
        rel.orgID memberGroup,
        mem.reserved2 msisdnType,
        con.chargeType,
        con.pkgID,
        con.approveStatus,
        con.oriContentID oriContentID,
        con.parentID parentID,
        IFNULL(tem.parentID,tem.id) belongTemplateID,
        con.contentType contentType
        from  ecpm_t_member mem
        LEFT JOIN ecpm_t_org_rel rel on mem.ID = rel.ID
        LEFT JOIN ecpm_t_content_org conorg on rel.orgID = conorg.ownerID AND(conorg.msisdn IS NULL OR conorg.msisdn=mem.msisdn)
        LEFT JOIN ecpm_t_content con on con.id = conorg.cyContID
        LEFT JOIN ecpm_t_member_subscribe sub on sub.memberID = mem.ID
        LEFT JOIN ecpm_t_enterprise_simple es ON es.ID = rel.enterpriseID
        LEFT JOIN ecpm_t_content tem ON tem.id=con.belongTemplateID
        where
        sub.`status` in (3,5)
        <if test="enterpriseType ==null || enterpriseType != 5">
            and con.approveStatus = 3
            and con.`status` = 0
        </if>
        <if test="orgID !=null and orgID != ''">
            and rel.orgID = #{orgID}
        </if>
        <if test="enterpriseID !=null and enterpriseID != ''">
            and rel.enterpriseID = #{enterpriseID}
        </if>
        <if test="contentID !=null and contentID != ''">
            and (con.id = #{contentID} or con.parentID = #{contentID})
        </if>
        <if test="msisdn !=null and msisdn != ''">
            and mem.msisdn = #{msisdn}
        </if>
        <if test="memberID !=null and memberID != ''">
            and mem.id = #{memberID}
        </if>
        LIMIT  #{pageNum}, #{pageSize}
    </select>

    <select id="queryProviceMemberCount" resultType="java.lang.Integer">
        SELECT COUNT(1) total
        from (
        select
        DISTINCT CONCAT(
        IF(es.parentEnterpriseID IS NULL, rel.enterpriseID, es.parentEnterpriseID ),
        '_',mem.msisdn,
        '_',IF( con.chargeType IS NULL, '', con.chargeType ),
        '_',IF(con.servType is null,'',con.servType),
        '_',if(con.parentID is null, IF(con.subServType is null,'',con.subServType), 3)
        ) businessID,
        con.servType,
        if(con.parentID is null, con.subServType, 3) subServType,
        (select rule.chargeMsisdn from ecpm_t_sync_service_rule rule where rule.servType = con.servType and rule.subServType = if(con.parentID is null, con.subServType, 3) and rule.enterpriseID = con.enterpriseID and NOW() BETWEEN rule.effectiveTime and rule.expiryTime GROUP BY rule.chargeMsisdn) chargeMsisdn,
        mem.msisdn,
        rel.orgID memberGroup,
        mem.reserved2 msisdnType,
        con.chargeType,
        con.pkgID,
        con.approveStatus
        from  ecpm_t_member mem
        LEFT JOIN ecpm_t_org_rel rel on mem.ID = rel.ID
        LEFT JOIN ecpm_t_content_org conorg on rel.orgID = conorg.ownerID AND(conorg.msisdn IS NULL OR conorg.msisdn=mem.msisdn)
        LEFT JOIN ecpm_t_content con on con.id = conorg.cyContID
        LEFT JOIN ecpm_t_member_subscribe sub on sub.memberID = mem.ID
        LEFT JOIN ecpm_t_enterprise_simple es ON es.ID = rel.enterpriseID
        LEFT JOIN ecpm_t_content tem ON tem.id=con.belongTemplateID
        where
        sub.`status` in (3,5)
        <if test="enterpriseType ==null || enterpriseType != 5">
            and con.approveStatus = 3
            and con.`status` = 0
        </if>
        <if test="orgID !=null and orgID != ''">
            and rel.orgID = #{orgID}
        </if>
        <if test="enterpriseID !=null and enterpriseID != ''">
            and rel.enterpriseID = #{enterpriseID}
        </if>
        <if test="contentID !=null and contentID != ''">
            and (con.id = #{contentID} or con.parentID = #{contentID})
        </if>
        <if test="msisdn !=null and msisdn != ''">
            and mem.msisdn = #{msisdn}
        </if>
        <if test="memberID !=null and memberID != ''">
            and mem.id = #{memberID}
        </if>
        ) t
    </select>

</mapper>