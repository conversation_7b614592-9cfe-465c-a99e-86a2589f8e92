var app = angular.module("myApp", ["util.ajax","page","angularI18n"])
app.controller("updateAccountController", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    $scope.operatorID = $.cookie('accountID');
    $scope.accountID = $location.$$search.accountID;
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      },
      {
          "totalPage": 1,
          "totalCount": 0,
          "pageSize": '10',
          "currentPage": 1
      }
    ];
    $scope.queryDepartment();
    $scope.queryAccountList();
    $scope.rePasswordValidate = true;
    $scope.emailValidate = true;
    console.log($scope.emailValidate)
    $scope.roleName = "";
    $scope.email = "";
    $scope.managerRightType =1;
    $scope.isMiguStaff = '0';
    $scope.accountStatus = 0;
    $scope.originAccountStatus = 0;
  };

    $scope.queryDepartment = function(){
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/accountManageService/queryOaDeptList",
            data: null,
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.oaDeptInfoList = result.oaDeptInfoList;}
                })
            }
        })
    };
    //角色查询弹出
    $scope.selOAAccount = function () {
        $scope.deptId = null;
        $scope.realName = null;
        $scope.queryOAAccount();
        $("#selectOAAccount").modal();
    };

    $scope.clickOAAccount = function (item) {
        if(item.isMiguStaff===1){
            $scope.tip = '该账号已被绑定';
            $('#myModal').modal();
            return;
        }
        $scope.oaAccount = item.oaAccount;
        $scope.fullName = item.realName;
        $scope.msisdn = item.contactPhone;
        $scope.email = item.email;
        $('#selectOAAccount').modal("hide");
    };
    //OA账号查询
    $scope.queryOAAccount = function (condition) {
        var req = {
            "deptId": $scope.deptId,
            "realName": $scope.realName,
            "pageParameter": {
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[1].pageSize),
                "isReturnTotal": "1"
            }
        };
        $scope.pageInfo[1].currentPage = 1;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/accountManageService/queryOaAccountList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        $scope.accountInfoList = result.accountInfoList;
                        $scope.pageInfo[1].totalCount = parseInt(result.totalAmount) || 0;
                        $scope.pageInfo[1].totalPage = Math.ceil(result.totalAmount / parseInt($scope.pageInfo[1].pageSize));
                    } else {
                        $scope.roleList = [];
                        $scope.pageInfo[1].currentPage = 1;
                        $scope.pageInfo[1].totalCount = 0;
                        $scope.pageInfo[1].totalPage = 1;
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.roleContentInfoData = [];
                    $scope.pageInfo[1].currentPage = 1;
                    $scope.pageInfo[1].totalCount = 0;
                    $scope.pageInfo[1].totalPage = 1;
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    //是否是咪咕员工选择
    $scope.changeIsOAAccount = function (val) {
        if(!val){
            val = 0;
        }
        $('.OA-account .redio-li').eq(val).find('span').removeClass('checked');
        $('.OA-account .redio-li').eq(val===0?1:0).find('span').addClass('checked');
        $scope.isMiguStaff = val;
    };
    $scope.oaAccountValidate = true;
    $scope.checkOaAccount = function (oaAccount) {
        if(oaAccount == "" && $scope.isMiguStaff === 1){
            $scope.oaAccountValidate = false;
        }else{
            $scope.oaAccountValidate = $scope.validate(oaAccount, 32, /^(?![0-9]+$)[0-9A-Za-z]{1,32}$/);
        }
    }
    $scope.queryAccountList = function () {
    var req={
      "accountQueryType": 1,
      "accountID": $scope.accountID,
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/queryAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
            if(data.resultCode=='**********'){
              $scope.accountInfo = result.accountInfo;
              $scope.accountID = result.accountInfo.accountID;
              $scope.accountName = result.accountInfo.accountName;
              $scope.accountType = result.accountInfo.accountType;
              $scope.fullName = result.accountInfo.fullName;
              $scope.msisdn = result.accountInfo.msisdn;
              $scope.email = result.accountInfo.email;
              $scope.password = result.accountInfo.password;
              $scope.selectedRole = $scope.accountInfo.roleList;
              $scope.isMiguStaff = $scope.accountInfo.isMiguStaff;
              $scope.accountStatus = $scope.accountInfo.accountStatus || "";
              $scope.originAccountStatus = $scope.accountInfo.accountStatus || "";
              $scope.changeIsOAAccount($scope.isMiguStaff);
              $scope.oaAccount = $scope.accountInfo.oaAccount;
              $scope.managerRightType =  $scope.accountInfo.managerRightType==null?1:$scope.accountInfo.managerRightType;
              $scope.changeBlackwhiteListType($scope.managerRightType);
            }else{
              $scope.tip=data.resultCode;
                $('#myModal').modal();
            }
        })
      },
      error:function(){
        $rootScope.$apply(function () {
          $scope.tip="**********";
            $('#myModal').modal();
        })
      }
    });
  }
    //账号类型选择
    $scope.changeBlackwhiteListType = function (val) {
        $('.black-white .redio-li').eq(val==0?1:0).find('span').removeClass('checked');
        $('.black-white .redio-li').eq(val).find('span').addClass('checked');
        $scope.managerRightType = val;

    }

  $scope.goBack = function (){
    location.href = "../administrator.html"
  };

  //角色查询弹出
  $scope.selAccount = function () {
    $scope.queryRoleList();
    $("#selectAccount").modal();
  };
  //角色查询
  $scope.queryRoleList = function (condition) {
    if (condition != 'justPage') {
      var req = {
        "roleName": $scope.roleName,
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.roleReq = angular.copy(req);
    } else {
      var req = $scope.roleReq;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/roleManageService/queryRoleList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.roleList = result.roleList;
            //组装角色权限
            angular.forEach($scope.roleList, function (item) {
              item.authNameString = "";
              angular.forEach(item.functionAuthList, function (row1) {
                if (row1.parentAuthID !== null) {
                  item.authNameString = item.authNameString + row1.authName + "/"
                }
              });
              angular.forEach(item.dateAuthList, function (row2) {
                if (row2.fieldName === "cityID") {
                  item.authNameString = item.authNameString + row2.authName + "/"
                }
              });
              item.authNameString = item.authNameString.substr(0, item.authNameString.length - 1)
            });
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize));
          } else {
            $scope.roleList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.roleContentInfoData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }

  $scope.updateAccount = function () {
    var req = {
      "accountInfo":{
        "operatorID": $scope.operatorID,
        "accountType": $scope.accountType,
        "accountID": $scope.accountID,
        "accountName": $scope.accountName,
        "roleList": $scope.selectedRole,
        "fullName": $scope.fullName,
        "msisdn": $scope.msisdn,
        "email": $scope.email,
        "password": $scope.password || "",
          "managerRightType": $scope.managerRightType,
      }
    };
    if($scope.accountStatus == 1) { // 激活
        req.accountInfo["accountStatus"] = 1;
    }
      if($scope.isMiguStaff==1){
          req.accountInfo.isMiguStaff = $scope.isMiguStaff;
          req.accountInfo.oaAccount = $scope.oaAccount;
      }
      var pwd = $scope.password;
      var rePwd = $scope.rePassword;
      if( pwd != rePwd){
          return
      }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/accountManageService/updateAccount",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $scope.tip = 'COMMON_SAVESUCCESS';
            $('#myModal').modal();
          } else {
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.tip='**********';
          $('#myModal').modal();
        })
      }
    });
  }

  // 联系人
  $scope.fullNameValidate = true;
  $scope.checkFullName = function (fullName) {
    $scope.fullNameValidate = $scope.validate(fullName, 32, /[A-Za-z\u4e00-\u9fa5]{1,32}$/);
  }

  // 手机号
  $scope.msisdnValidate = true;
  $scope.checkMsisdn = function (msisdn) {
    $scope.msisdnValidate = $scope.validate(msisdn, 11, /[0-9]{11}$/);
  }

  // 邮箱
  $scope.emailValidate = true;
  $scope.checkEmail = function (email) {
    if(email == ""){
      $scope.emailValidate = true;
      console.log($scope.emailValidate)
    }else if($scope.emailValidate = $scope.validate(email, 64, /^(?=\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$).{0,64}$/)){
      $scope.emailValidate = true;
    }
  }

  $scope.closeTip = function () {
    if($scope.tip == "COMMON_SAVESUCCESS"){
      window.history.back();
    }
  }

  /*验证密码，通过后台调用checkPwdRule接口 */
  $scope.checkPassword = function (condition) {
    $scope.condition =condition;
      $scope.passwordValidate = true;
      $scope.passwordValidateDesc = "";
      var pwd = $scope.password;
      var rePwd = $scope.rePassword;
      if(!$scope.password){
        $scope.checkRePassword(pwd, rePwd,$scope.condition);
        return;
      }else{
        var checkPwdRuleReq = {};
          checkPwdRuleReq.password = pwd;
          RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
            data: JSON.stringify(checkPwdRuleReq),
            success: function (data) {
              $rootScope.$apply(function () {
                var result = data.result;
                if (result.resultCode != '**********') {
                  $scope.passwordValidate = false;
                    var passwordRuleList = data.passwordRuleList;
                    if(result.resultCode == '1030120000'){
                      $scope.passwordValidateDesc ='';
                    }else{
                      if(!passwordRuleList){
                        $scope.passwordValidateDesc = result.resultDesc;
                      }else{
                        for (var i = 0; i < passwordRuleList.length; i++) {
                                $scope.passwordValidateDesc  = $scope.passwordValidateDesc + passwordRuleList[i].ruleName;
                                // $scope.passwordValidateDesc  =  passwordRuleList[i].ruleName;

                                // $scope.passwordValidateDesc  = $scope.passwordValidateDesc + ";";
                                }
                        $scope.passwordValidateDesc=$scope.passwordValidateDesc.substring(0, $scope.passwordValidateDesc.length - 1);
                      }
                    }
                } else {
                if(!!rePwd){
                  $scope.checkRePassword(pwd, rePwd,$scope.condition);
                }
                }
              })
            },
            error:function(){
                $rootScope.$apply(function(){
                    $scope.tip='**********';
                    $('#myModal').modal();
                    }
                )
            }
          });
      }
    };
    $scope.checkRePassword = function (pwd, rePwd,condition) {
      $scope.condition =condition;
      $scope.rePasswordValidate = true;
      if (!rePwd) {
        if(!pwd){

        }else{
          $scope.rePasswordValidate = false;
        }
      }else{
        if (pwd != rePwd) {
          $scope.rePasswordValidate = false;
        }
      }
    }
  //添加角色
  $scope.roleAdd = function (role) {
    $scope.selectedRole = [];
    $scope.roleName_selected = role.roleName;
    $scope.selectedRole.push(role);
    $('#selectAccount').click();
  }

  /*校验各个字段*/
  $scope.validate = function (context, maxlength, reg) {
    if (!context) {
      return false;
    } else {
      if (context.length > maxlength) {
        return false;
      } else {
        if (!reg.test(context)) {
          return false;
        } else {
          return true;
        }
      }
    }
  };
   //账号状态
   $scope.getAccountStatus = function (accountStatus) {
        switch(accountStatus) {
            case 1:
             return "正常";
            case 2:
             return "冻结";
            case 3:
             return "停用";
            default:
             return "";
        }
   }
   $scope.changeStatus = function () {
     $scope.accountStatus = $scope.accountStatus == 2 ? 1: 2;
   }
});
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}])
