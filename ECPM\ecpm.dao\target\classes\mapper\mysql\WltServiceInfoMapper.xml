<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.WltServiceInfoMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltServiceInfoWrapper" >
        <id column="ID" property="ID" jdbcType="BIGINT" />
        <result column="enterpriseID" property="enterpriseID" jdbcType="INTEGER" />
        <result column="serviceType" property="serviceType" jdbcType="INTEGER" />
        <result column="pkgType" property="pkgType" jdbcType="INTEGER" />
        <result column="pkgQuota" property="pkgQuota" jdbcType="INTEGER" />
        <result column="pkgPrice" property="pkgPrice" jdbcType="REAL" />
    </resultMap>

    <sql id="Base_Column_List" >
        ID, enterpriseID, serviceType, pkgType, pkgQuota, pkgPrice
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_wlt_service_info
        where ID = #{ID,jdbcType=BIGINT}
    </select>
    
    <select id="selectByEnterpriseID" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_wlt_service_info
        where enterpriseID = #{enterpriseID,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
        delete from ecpm_t_wlt_service_info
        where ID = #{ID,jdbcType=BIGINT}
    </delete>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltServiceInfoWrapper" >
        insert into ecpm_t_wlt_service_info (ID, enterpriseID, serviceType, pkgType, 
            pkgQuota, pkgPrice)
        values (#{ID,jdbcType=BIGINT},#{enterpriseID,jdbcType=INTEGER}, #{serviceType,jdbcType=INTEGER}, #{pkgType,jdbcType=INTEGER}, 
            #{pkgQuota,jdbcType=INTEGER}, #{pkgPrice,jdbcType=REAL})
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltServiceInfoWrapper" >
        insert into ecpm_t_wlt_service_info
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="ID != null" >
                ID,
            </if>
            <if test="enterpriseID != null" >
                enterpriseID,
            </if>
            <if test="serviceType != null" >
                serviceType,
            </if>
            <if test="pkgType != null" >
                pkgType,
            </if>
            <if test="pkgQuota != null" >
                pkgQuota,
            </if>
            <if test="pkgPrice != null" >
                pkgPrice,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="ID != null" >
                #{ID,jdbcType=BIGINT},
            </if>
            <if test="enterpriseID != null" >
                #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="serviceType != null" >
                #{serviceType,jdbcType=INTEGER},
            </if>
            <if test="pkgType != null" >
                #{pkgType,jdbcType=INTEGER},
            </if>
            <if test="pkgQuota != null" >
                #{pkgQuota,jdbcType=INTEGER},
            </if>
            <if test="pkgPrice != null" >
                #{pkgPrice,jdbcType=REAL},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltServiceInfoWrapper" >
        update ecpm_t_wlt_service_info
        <set >
            <if test="enterpriseID != null" >
                enterpriseID = #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="serviceType != null" >
                serviceType = #{serviceType,jdbcType=INTEGER},
            </if>
            <if test="pkgType != null" >
                pkgType = #{pkgType,jdbcType=INTEGER},
            </if>
            <if test="pkgQuota != null" >
                pkgQuota = #{pkgQuota,jdbcType=INTEGER},
            </if>
            <if test="pkgPrice != null" >
                pkgPrice = #{pkgPrice,jdbcType=REAL},
            </if>
        </set>
        where ID = #{ID,jdbcType=BIGINT}
    </update>
    
    <update id="updateByEnterpriseID" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltServiceInfoWrapper" >
        update ecpm_t_wlt_service_info
        <set >
            <if test="ID != null" >
                ID = #{ID,jdbcType=BIGINT},
            </if>
            <if test="serviceType != null" >
                serviceType = #{serviceType,jdbcType=INTEGER},
            </if>
            <if test="pkgType != null" >
                pkgType = #{pkgType,jdbcType=INTEGER},
            </if>
            <if test="pkgQuota != null" >
                pkgQuota = #{pkgQuota,jdbcType=INTEGER},
            </if>
            <if test="pkgPrice != null" >
                pkgPrice = #{pkgPrice,jdbcType=REAL},
            </if>
        </set>
        where enterpriseID = #{enterpriseID,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltServiceInfoWrapper" >
        update ecpm_t_wlt_service_info
        set serviceType = #{serviceType,jdbcType=INTEGER},
            enterpriseID = #{enterpriseID,jdbcType=INTEGER},
            pkgType = #{pkgType,jdbcType=INTEGER},
            pkgQuota = #{pkgQuota,jdbcType=INTEGER},
            pkgPrice = #{pkgPrice,jdbcType=REAL}
        where ID = #{ID,jdbcType=BIGINT}
    </update>
</mapper>