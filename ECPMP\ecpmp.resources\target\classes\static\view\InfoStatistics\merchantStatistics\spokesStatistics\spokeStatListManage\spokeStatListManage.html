
<!DOCTYPE html>
<html>
<head lang="en">
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="ie=edge">
<meta http-equiv="X-UA-Compatible" content="IE=11" />
<title>代言人统计</title>
<link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css" />
<link href="../../../../../css/reset.css" rel="stylesheet" />
<link rel="stylesheet" type="text/css" href="../../../../../css/searchList.css" />
<link rel="stylesheet" type="text/css" href="../../../../../css/spokeStatListManage.css" />
<script type="text/javascript" src="../../../../../frameworkJs/angular.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
<script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
<script type="text/javascript" 
	src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
<script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
<!-- 引入分页组件 -->
<link rel="stylesheet" type="text/css" href="../../../../../directives/page/page.css" />
<link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
<script type="text/javascript" src="../../../../../directives/page/page.js"></script>
<script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
<script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
<script type="text/javascript" src="spokeStatListCtrl.js"></script>
</head>
<body ng-app='myApp' ng-controller='spokeStatListController' ng-init="init();">
    <div class="cooperation-manage" >
        <div class="cooperation-head"><span class="frist-tab" ng-bind="'COMMON_INFOSTATISTICS'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'COMMON_MERCHANTSTATISTICS'|translate"></span></div>
        <top:menu chose-index="1" page-url="/qycy/ecpmp/view/InfoStatistics/merchantStatistics/spokesStatistics/spokeStatListManage" 
        	list-index="14"></top:menu>
        <div class="cooperation-search">
            <form class="form-horizontal">
                <div class="form-group form-inline">
                	<label for="msisdn" class="col-lg-1 col-md-1 col-sm-1 col-xs-1 control-label" style="white-space:nowrap" 
                		ng-bind="'SPOKES_MSISDN'|translate">
	                	</label>
	                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2">
                            <input autocomplete="off" type="text" class="form-control" id="msisdn" 
                            placeholder="{{'SPOKES_PLEASEINPUTMSISDN'|translate}}" 
                            ng-model="querySpokeStatCond.msisdn">
                    </div>
                    <div class="col-lg-1 col-md-1 col-sm-1 col-xs-1">
	                    <button ng-click="queryspokeStatList()" type="submit" class="btn search-btn">
	                    <icon class="search-iocn"></icon><span ng-bind="'COMMON_SEARCH'|translate"></span>
	                    </button>
	                </div>
                </div>
            </form>
        </div>
        <div class="add-table">
            <button type="submit" class="btn add-btn" ng-click="exportFile()" id="exportSpokeStatList">
            	<icon class="export-iocn"></icon>
            	<span ng-bind="'COMMON_EXPORT'|translate"></span>
            </button>
        </div>
        <div style="margin-left: 20px;margin-bottom: 20px;">
            <p style="font-size: 16px;font-weight: 500;" ng-bind="'COMMON_SPOKESSTATISTICS'|translate"></p>
        </div>
        <div class="coorPeration-table">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th ng-bind="'SPOKES_MSISDN'|translate"></th>
                        <th ng-bind="'SPOKES_LOCATION'|translate"></th>
                        <th ng-bind="'SPOKES_FIRSTPARTICIPATETIME'|translate" style="width: 13%;"></th>
                        <th ng-bind="'SPOKES_ACTIVITYCOUNT'|translate"></th>
                        <th ng-bind="'SPOKES_REWARDCOUNT'|translate"></th>
                        <th ng-bind="'SPOKES_DAYCOUNT'|translate"></th>
                        <th ng-bind="'SPOKES_SCREENCOUNT'|translate"style="width: 15%;"></th>
                        <th ng-bind="'SPOKES_ENDPHONECOUNT'|translate"style="width: 15%;"></th>
                        <th ng-bind="'COMMON_OPERATE'|translate" style="width:15%"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="item in spokeStatListData">
                        <td title="{{item.msisdn}}">{{item.msisdn}}</td>
                        <!-- <td title="{{item.provinceID ?item.provinceID:'不限'}}{{item.cityID?'-'+item.cityID:''}}">
                        	{{item.provinceID ?item.provinceID:'不限'}}{{item.cityID?'-'+item.cityID:''}}</td> -->
                        <td title="{{item.province?item.province.provinceName:'ENTERPRISE_NOLIMITED'|translate}}{{item.city?'-'+item.city.cityName:''}}">
                        	{{item.province?item.province.provinceName:'ENTERPRISE_NOLIMITED'|translate}}{{item.city?'-'+item.city.cityName:''}}</td>
                        <td title="{{formatDate(item.firstParticipateTime)}}">{{formatDate(item.firstParticipateTime)}}</td>
                        <td title="{{item.activityCount}}">{{item.activityCount}}</td>
                        <td title="{{item.rewardCount}}">{{item.rewardCount}}</td>
                        <td title="{{item.dayCount}}">{{item.dayCount}}</td>
                        <td title="{{item.screenCount}}">{{item.screenCount}}</td>
                        <td title="{{item.endPhoneCount}}">{{item.endPhoneCount}}</td>
                        <td>
                            <div class="handle">
                                <ul>
                                    <li class="query" ng-click="gotoDetail(item)">
                                        <icon class="query-icon"></icon>
                                        <span ng-bind="'COMMON_WATCHDETAIL'|translate"></span></li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr ng-show="spokeStatListData.length<=0">
                        <td style="text-align:center" colspan="9"ng-bind="'COMMON_NODATA'|translate" ></td>
                    </tr>
                </tbody>
            </table>
        </div>
    	<div>
        <ptl-page tableId="0" change="queryspokeStatList('justPage')"></ptl-page>
      </div>
    </div>

<!--小弹出框-->
        <div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
                <div class="modal-dialog modal-sm" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
                        </div>
                        <div class="modal-body">
                            <div class="text-center"><p style='font-size: 16px;color:#383838'>
                                {{tip|translate}}
                            </p></div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn " data-dismiss="modal" aria-label="Close" 
                            	ng-bind="'COMMON_OK'|translate"></button>
                        </div>
                    </div>
                </div>
            </div>
    
</body>
</html>