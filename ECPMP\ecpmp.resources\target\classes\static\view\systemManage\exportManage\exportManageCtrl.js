var app = angular.module("myApp", ["util.ajax", "page", "angularI18n"])
app.controller("ExportManagementController", function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {

    $scope.enterpriseType = "";
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 1,
        "totalCount": 0,
        "pageSize": '10',
        "currentPage": 1
      }
    ];

    $scope.operatorID = $.cookie('accountID');
    $scope.isSuperManager = ($scope.operatorID === '1000');
    $scope.roleID = $.cookie('roleID');
    $scope.designatedRoleID = false;
    $scope.roleIDs = ['10002','337209'];
    for(let i = 0 ; i < $scope.roleIDs.length ; i++){
        if($scope.roleID !== undefined){
            if($scope.roleID === $scope.roleIDs[i]){
                $scope.designatedRoleID = true;
                break;
            }
        }
    }
    if ($scope.isSuperManager)
    {
        $scope.fileTypeShow = false;
    	$scope.fileType = '';
    }else if($scope.designatedRoleID){
        $scope.fileTypeShow = false;
        $scope.fileType = '';
    } else {
        $scope.fileTypeShow = true;
    	$scope.fileType = '7';
    }
    
    //下拉框
    $scope.exportStatusList = getExportStatus();

    $scope.queryExportTaskInfoList();
  };

//初始化搜索条件
  $scope.initSel = {
      startTime: "",
      endTime: "",
      search: false,
  };

    $scope.fileTypeMap={

        "1":"直客热线统计明细",

        "2":"代理商热线统计明细",

        "3":"分省企业热线统计明细",

        "4":"直客名片/广告统计明细",

        "5":"代理商名片/广告统计明细",

        "6":"分省企业名片/热线省份版统计明细",

        "7":"变量模板清单",
        "10":"异网投递通道指定内容导入失败清单",
        "11":"异网通道内容--定制设置",
        "12":"异网通道内容--默认设置",
        "13":"异网通道内容--特殊设置"
    };

    $scope.fileTypeSelect = [
        {
            id: "",
            name: "不限"
        },
        {
            id: "1",
            name: "直客热线统计明细"
        },
        {
            id: "2",
            name:"代理商热线统计明细"
        },{
            id: "3",
            name:"分省企业热线统计明细"
        },{
            id: "4",
            name:"直客名片/广告统计明细"
        },{
            id: "5",
            name:"代理商名片/广告统计明细"
        },{
            id: "6",
            name:"分省企业名片/热线省份版统计明细"
        },{
            id: "7",
            name:"变量模板清单"
        },{
            id: "10",
            name:"异网投递通道指定内容导入失败清单"
        },{
            id: "11",
            name:"异网通道内容--定制设置"
        },{
            id: "12",
            name:"异网通道内容--默认设置"
        },{
            id: "13",
            name:"异网通道内容--特殊设置"
        }
    ];
    
  $scope.exportTaskInfoData = [];

  $scope.showDownloadByCreateTimeAndTaskStatus = function (createTime,taskStatus){
      var nowdate = new Date();
      nowdate.setDate(nowdate.getDate() - 10);
      var y = nowdate.getFullYear();
      var m = nowdate.getMonth() + 1 < 10 ? "0" + (nowdate.getMonth() + 1) : nowdate.getMonth() + 1;
      var d = nowdate.getDate() < 10 ? "0" + nowdate.getDate() : nowdate.getDate();
      var hh = nowdate.getHours() < 10 ? "0" + nowdate.getHours() : nowdate.getHours();            //时
      var mm = nowdate.getMinutes() < 10 ? "0" + nowdate.getMinutes() : nowdate.getMinutes();          //分
      var ss = nowdate.getSeconds() < 10 ? "0" + nowdate.getSeconds() : nowdate.getSeconds();          //秒
      var endTime = y + '-' + m + '-' + d + " " + hh + ':' + mm + ':' + ss;
      var createTimes = createTime.substring(0, 10).split('-');
      var endTimes = endTime.substring(0, 10).split('-');
      createTime = createTimes[1] + '/' + createTimes[2] + '/' + createTimes[0] + ' ' + createTime.substring(10, 19);
      endTime = endTimes[1] + '/' + endTimes[2] + '/' + endTimes[0] + ' ' + endTime.substring(10, 19);
      var a = (Date.parse(endTime) - Date.parse(createTime)) / 3600 / 1000;
      if (a > 0 || taskStatus !== 2) {
          return true
      } else {
          return false
      }
  }

  //查询
  $scope.queryExportTaskInfoList = function (condition) {
    var req = {};
    if (condition != 'justPage') {
      req = {
        "startTime": $scope.initSel.startTime,
        "endTime": $scope.initSel.endTime,
        "fileName": $scope.fileName,
        "taskType": $scope.fileType,
        "sequenceType":1,
        "page": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.queryExportTaskListTemp = angular.copy(req);
    } else {
      req = $scope.queryExportTaskListTemp;
      req.page.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.page.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/exportTaskService/queryExportTaskList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          console.log(result);
          if (data.resultCode == '**********') {
            $scope.exportTaskInfoData = result.exportTaskInfoList || [];
            $scope.pageInfo[0].totalCount = parseInt(result.totalNum) || 0;
            $scope.pageInfo[0].totalPage = result.totalNum !== 0 ? Math.ceil(result.totalNum / parseInt($scope.pageInfo[0].pageSize)) : 1;
          } else {
            $scope.exportTaskInfoData = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $scope.exportTaskInfoData = [];
          $scope.pageInfo[0].currentPage = 1;
          $scope.pageInfo[0].totalCount = 0;
          $scope.pageInfo[0].totalPage = 1;
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }
  
  
  $('.input-daterange').datepicker({
	  format: "yyyy-mm-dd",
      weekStart: 0,
      clearBtn: true,
      language: "zh-CN",
      autoclose: true
  });

  $('#start').on('changeDate', function () {
      $rootScope.$apply(function () {
          $scope.searchOn();
      }
      )
  });

  $('#end').on('changeDate', function () {
      $rootScope.$apply(function () {
          $scope.searchOn();
      }
      )
  });
  
  //判断搜索按钮是否置灰
  $scope.searchOn = function () {
      $scope.initSel.startTime = document.getElementById("start").value;
      $scope.initSel.endTime = document.getElementById("end").value;

      if ($scope.initSel.startTime === '' && $scope.initSel.endTime === '') {
          $scope.initSel.search = false;
      }
      else if ($scope.initSel.startTime !== '' && $scope.initSel.endTime !== '') {
          $scope.initSel.search = false;
      }
      else {
          $scope.initSel.search = true;
      }
  }
  
  //重置密码弹窗
  $scope.resPwd = function (item) {
    $scope.accountItem = item;
    $("#resPassword").modal();
  }

  //重置密码
  $scope.resetPassword = function () {
    var req = {
      account: {
        accountName: $scope.accountItem.accountName,
        accountType: $scope.accountItem.accountType

      },
      token: "noNeedCheck"
    };
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/loginService/resetPwd",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '**********') {
            $('#resetBlack').click();
            $scope.tip = "重置密码成功";
            $('#myModal').modal();
          } else {
            $('#resetBlack').click();
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
          $('#resetBlack').click();
          $scope.tip = '**********';
          $('#myModal').modal();
        })
      }
    });
  }

});

app.filter("formatExportStatus", function () {
  return function (taskStatus) {
	  return getExportStatus()[taskStatus].name
  }
});

app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return format(date);
    }
    return "";
  }
});

function add0(m){
	return m<10?'0'+m:m 
}

function format(timestamp)
{
	var time = new Date(timestamp);
	var y = time.getFullYear();
	var m = time.getMonth()+1;
	var d = time.getDate();
	var h = time.getHours();
	var mm = time.getMinutes();
	var s = time.getSeconds();
	return y+'-'+add0(m)+'-'+add0(d)+' '+add0(h)+':'+add0(mm)+':'+add0(s);
}

function getExportStatus(){
	return exportStatusList = [
	      {
	        id: "0",
	        name: "待处理"
	      },
	      {
	        id: "1",
	        name: "导出中"
	      },
	      {
	        id: "2",
	        name: "已导出"
	      },
	      {
	        id: "4",
	        name: "失败"
	      }
	    ]
}