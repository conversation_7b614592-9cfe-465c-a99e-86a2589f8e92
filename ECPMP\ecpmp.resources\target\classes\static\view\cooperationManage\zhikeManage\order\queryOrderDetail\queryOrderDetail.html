<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>订单查询</title>
    <link rel="stylesheet" type="text/css" href="../../../../../css/bootstrap.min.css"/>
    <link href="../../../../../css/datepicker3.css" rel="stylesheet" type="text/css"/>
    <link href="../../../../../css/reset.css" rel="stylesheet"/>
    <link href="../../../../../css/orderDetail.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery-3.5.0.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/jquery.cookie.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap.min.js"></script>
    <script src="../../../../../frameworkJs/angular.min.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/bootstrap-datepicker.js"></script>
    <script type="text/javascript" src="../../../../../frameworkJs/angular-translate/angular-translate.js"></script>
    <script type="text/javascript"
            src="../../../../../frameworkJs/angular-translate-loader-static-files/angular-translate-loader-static-files.min.js"></script>
    <script type="text/javascript" src="../../../../../service/angular-i18n/angular-i18n.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-common.js"></script>
    <script type="text/javascript" src="../../../../../service/utils/service-ajax.js"></script>
    <link href="../../../../../directives/topMenu/topMenu.css" rel="stylesheet"/>
    <script type="text/javascript" src="../../../../../directives/topMenu/topMenu.js"></script>
    <script type="text/javascript" src="queryOrderDetailCtrl.js"></script>
    <style>
        [ng-cloak] {
            display: none !important;
        }

        label {
            min-width: 200px;
        }
    </style>
</head>

<body style="min-width:1024px;" ng-app='myApp' ng-controller='orderDetailController' ng-init="init();">
<div class="order-manage">
    <div class="cooperation-head" ng-if="isSuperManager && enterpriseType == '1'">
        <span class="frist-tab" ng-bind="'CREATEORDER_ZHIKEMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'CREATEORDER_ORDERMANAGE'|translate"></span>
    </div>
    <div ng-if="isSuperManager && enterpriseType == '2'" class="cooperation-head">
        <span class="frist-tab" ng-bind="'COMMON_AGENTMANAGE'|translate"></span>&nbsp;&gt;&nbsp;
        <span class="second-tab" ng-bind="'ORDER_MANAGE'|translate"></span>
    </div>
    <top:menu chose-index="2" page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/queryOrderDetail"
              list-index="2"
              ng-if="isSuperManager && enterpriseType == '1'"></top:menu>
    <top:menu chose-index="2"
              page-url="/qycy/ecpmp/view/cooperationManage/zhikeManage/order/orderList/orderList"
              list-index="51" ng-if="isSuperManager && enterpriseType == '2'"></top:menu>
    <div class="cooper-messsage" ng-cloak>
        <div class="cooper-title">
            <span ng-bind="'ORDER_INFORMATION'|translate"></span>
        </div>
        <div class="cooper-tab">
            <form class="form-horizontal" name="myForm" novalidate>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ENTERPRISE_ENTERPRISENAME'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="initOrderInfo.enterpriseName"></p>
                        <span style="color:red">
								</span>
                    </div>
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_CODE'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="initOrderInfo.orderCode"></p>
                        <span style="color:red">
								</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_ISEXPVERSION'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p>{{initOrderInfo.isExperienceVersion==1?'是':'否'}}</p>
                    </div>
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_BUSINESSTYPE'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="businessTypeMap[servType]"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_AMOUNT_NOCNY'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="initOrderInfo.orderAmount"></p>
                    </div>
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_NAME'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="initOrderInfo.orderName"></p>
                    </div>
                </div>
                <div id="MXANDDD" class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_TRANSLATE'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <a href="{{initOrderInfo.transferAttachURL.download}}" download
                           target="_blank">{{initOrderInfo.imgName1}}</a>
                    </div>
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_DETAILINFO'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <a href="{{initOrderInfo.orderDetailURL.download}}" download
                           target="_blank">{{initOrderInfo.imgName2}}</a>
                    </div>
                </div>
                
                <div id="TYDD" class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'CREATEORDER_ORDEREXPERIENCE'|translate"></span></label>

                    <div class="col-xs-2">
                        <a href="{{initOrderInfo.transferAttachURL.download}}" download
                           target="_blank">{{initOrderInfo.imgName1}}</a>
                    </div>
                </div>
                
            </form>
        </div>
        <div style="padding-left:50px" class="cooper-title">
            <span ng-bind="'ORDER_QUOTA'|translate"></span>
        </div>
        <div class="cooper-tab">
            <form class="form-horizontal" name="myForm1" novalidate>
                <div ng-show="hasCMCC===true" style="margin-bottom:40px;">
                    <!--移动-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                            <span>移动：</span>
                        </li>
                    </div>
                    <div class="form-group" ng-if="hasPX">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="servType != 3 && servType != 4"></span>
                            <span ng-show="servType == 4">屏显：</span>
                            <!--<span ng-bind="'ORDER_GG_QUOTA'|translate" ng-show="servType == 3"></span>：-->
                            <span ng-bind="'CREATEORDER_GUANGGAOANDPEIE'|translate" ng-show="servType == 3"></span>
                          </label>
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="px_noLimit_cmcc===true&&servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="initOrderInfo.pxByTimes!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.pxByTimes}}</span>
                        </div>
                        <!-- 按人包月 -->
                        <!--修改 start-->
                        <!--
                        <div class="col-xs-9"
                             ng-show="initOrderInfo.caller.memberCount!==''||initOrderInfo.called.memberCount!==''&&servType != 3">
                        -->
                            <div class="col-xs-9"
                                 ng-show="initOrderInfo.caller.memberCount!==''||initOrderInfo.called.memberCount!==''||initOrderInfo.callerAndcalled.memberCount!==''&&servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                {{'CREATEORDER_ANREN'|translate}}
                            </li>
                            <!-- 老数据的主叫 -->
                            <li class="check-li"
                                ng-show="initOrderInfo.caller.memberCount!==''&&initOrderInfo.isOldData">
                                <span ng-bind="'CREATEORDER_CALLER'|translate"></span>{{initOrderInfo.caller.memberCount}}人{{initOrderInfo.caller.pxAmountPerPerson}}{{'CREATEORDER_CIREN'|translate}}
                            </li>
                            <!-- 老数据的被叫 -->
                            <li class="check-li"
                                ng-show="initOrderInfo.called.memberCount!==''&&initOrderInfo.isOldData">
                                <span ng-bind="'CREATEORDER_CALLED'|translate"></span>{{initOrderInfo.called.memberCount}}人{{initOrderInfo.called.pxAmountPerPerson}}{{'CREATEORDER_CIREN'|translate}}
                            </li>
                            <!-- 套餐版的主被叫 -->
                            <li class="check-li" ng-show="!initOrderInfo.isOldData">
                                <span style="margin-right:20px;">{{initOrderInfo.quantity}}</span>
                                <span style="margin-right:20px;">套餐选择</span>
                                <span style="margin-right:20px;">{{initOrderInfo.taocanName}}</span>
                            </li>
                        </div>
                        <!--修改 end-->
                    </div>
                    <div class="form-group" ng-if="hasGD" ng-show="servType != 3">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gd_noLimit_cmcc===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="initOrderInfo.guaduanAmount!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.guaduanAmount}}</span>
                        </div>

                        <!-- 按人包月 -->
                        <div class="col-xs-9"
                             ng-show="gd_ByLimit_cmcc===true">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                {{'CREATEORDER_ANREN'|translate}}
                            </li>

                            <li class="check-li" ng-show="gd_ByLimit_cmcc===true">
                                <span style="margin-right:20px;">{{initOrderInfo.gdquantity}}</span>
                                <span style="margin-right:20px;">套餐选择</span>
                                <span style="margin-right:20px;">{{initOrderInfo.gdtaocanName}}</span>
                            </li>
                        </div>

                    </div>



                    <div class="form-group" ng-if="hasGC" ng-show="servType != 3">
                        <label for="" class="col-xs-2 control-label"  ng-show="servType != 4&&servType != 3">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJCXBYTIMES'|translate"></span>：</label>
						<label for="" class="col-xs-2 control-label" ng-show="servType == 4">
                            <icon>*</icon>
                            <span>彩信</span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gc_noLimit_cmcc===true&&servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="initOrderInfo.guacaiAmount!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.guacaiAmount}}</span>
                        </div>

                        <!-- 按人包月 -->
                        <div class="col-xs-9"
                             ng-show="gc_ByLimit_cmcc===true&&servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                {{'CREATEORDER_ANREN'|translate}}
                            </li>

                            <li class="check-li" ng-show="gc_ByLimit_cmcc===true">
                                <span style="margin-right:20px;">{{initOrderInfo.gcquantity}}</span>
                                <span style="margin-right:20px;">套餐选择</span>
                                <span style="margin-right:20px;">{{initOrderInfo.gctaocanName}}</span>
                            </li>
                        </div>

                    </div>

                    <div class="form-group" ng-if="hasZC" ng-show="servType == 4 || servType == 2">
                         <label for="" class="col-xs-2 control-label" ng-show="servType == 4">
                            <icon>*</icon>
                            <span ng-bind="'ZENGCAISERVICE'|translate"></span>：</label>
                        <label for="" class="col-xs-2 control-label" ng-show="servType == 2">
                            <icon>*</icon>
                            <span>挂机增彩</span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="zx_noLimit_cmcc===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="zx_noLimit_cmcc ===false">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.zcAmount}}</span>
                        </div>

                    </div>
                    <div class="form-group" ng-if="hasGroupSendSMSCMCC" ng-show="servType == 4">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span >短信</span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="groupSendSMSCMCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="!groupSendSMSCMCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.groupSendSMSCMCCAmount}}</span>
                        </div>

                    </div>
                </div>
                <div ng-show="hasCUCC===true" style="margin-bottom:40px;">
                    <!--联通-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                            <span>联通：</span>
                        </li>
                    </div>

                    <div class="form-group" ng-if="hasPXCUCC">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="servType != 4"></span>
                            <span ng-show="servType == 4">屏显：</span>
                        </label>
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="px_noLimit_cucc===true&&servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="initOrderInfo.pxByTimes_cucc!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.pxByTimes_cucc}}</span>
                        </div>
                    </div>
                    <!--群发短信-->
                    <div class="form-group" ng-if="hasGroupSendSMSCUCC" ng-show="servType == 4">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span >短信</span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="groupSendSMSCUCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="!groupSendSMSCUCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.groupSendSMSCUCCAmount}}</span>
                        </div>

                    </div>
                    <!--addby hyj 20191107 top-->
                    <div class="form-group" ng-if="hasGDCUCC" ng-show="servType == 2">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gd_noLimit_cmccCUCC===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="initOrderInfo.guaduanAmountCUCC!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.guaduanAmountCUCC}}</span>
                        </div>
                    </div>
                    <!--addby hyj 20191107 end-->
                </div>
                <div ng-show="hasCTCC===true" style="margin-bottom:40px;">
                    <!--电信-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                            <span>电信：</span>
                        </li>
                    </div>
                    
                    <div class="form-group" ng-if="hasPXCTCC">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="servType != 4"></span>
                            <span ng-show="servType == 4">屏显：</span>
                        </label>
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="px_noLimit_ctcc===true&&servType != 3">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="initOrderInfo.pxByTimes_ctcc!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.pxByTimes_ctcc}}</span>
                        </div>
                    </div>
					<!--群发短信-->
                    <div class="form-group" ng-if="hasGroupSendSMSCTCC" ng-show="servType == 4">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span >短信</span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="groupSendSMSCTCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="!groupSendSMSCTCCNoLimit">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.groupSendSMSCTCCAmount}}</span>
                        </div>

                    </div>
                    <!--addby hyj 20191107 top-->
                    <div class="form-group" ng-if="hasGDCTCC" ng-show="servType == 2">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span>：</label>

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gd_noLimit_cmccCTCC===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="initOrderInfo.guaduanAmountCTCC!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{initOrderInfo.guaduanAmountCTCC}}</span>
                        </div>
                    </div>
                    <!--addby hyj 20191107 end-->
                </div>

                <div class="form-group">
                    <label class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'COMMON_EXPIRYDATE'|translate"></span>：
                    </label>

                    <div class="col-xs-9">
                        <div class="data-time">
                            <div class="input-group date  star-time" aria-disabled="true">
                                <input type="text" class="form-control" ng-model="initOrderInfo.effictiveTime" disabled>
                            </div>
                            <span class="to" ng-bind="'TO'|translate"></span>

                            <div class="input-group date  end-time" aria-disabled="true">
                                <input type="text" class="form-control" ng-model="initOrderInfo.expireTime" disabled>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'COMMON_STATUS'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <li class="check-li" ng-bind="statusMap[initOrderInfo.orderStatus]"></li>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!--增补订单-->
    <div ng-if="hasAddOrder" class="cooper-messsage" ng-cloak ng-repeat="orderitem in addOrderListInfo">
        <hr>
        <div class="cooper-title">
            <span ng-bind="'ORDER_ADDTIONORDER'|translate"></span><span>{{$index+1}}</span>
        </div>
        <div class="cooper-tab">
            <form class="form-horizontal" name="myForm" novalidate>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'CREATEORDER_ENTERPRISENAME'|translate"></span></label>

                    <div class="col-xs-2">
                        <p ng-bind="initOrderInfo.enterpriseName"></p>
                        <span style="color:red">
									</span>
                    </div>
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_CODE'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="orderitem.orderCode"></p>
                        <span style="color:red">
									</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_ISEXPVERSION'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p>{{initOrderInfo.isExperienceVersion==1?'是':'否'}}</p>
                    </div>
                    <!--业务类别-->
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_BUSINESSTYPE'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="businessTypeMap[servType]"></p>
                    </div>
                </div>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_AMOUNT_NOCNY'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="orderitem.amount"></p>
                    </div>
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_NAME'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <p ng-bind="orderitem.orderName"></p>
                    </div>
                </div>
                
                <!-- 转账明细和订单明细 -->
                <div ng-if="initOrderInfo.isExperienceVersion!=1" class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_TRANSLATE'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <a href="{{orderitem.transferAttachURL.download}}" download target="_blank">{{orderitem.imgName1}}</a>
                    </div>
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'ORDER_DETAILINFO'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <a href="{{orderitem.orderDetailURL.download}}" download
                           target="_blank">{{orderitem.imgName2}}</a>
                    </div>
                </div>
                <!-- 体验申请单 -->
                <div  ng-if="initOrderInfo.isExperienceVersion==1"  class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'CREATEORDER_ORDEREXPERIENCE'|translate"></span></label>

                    <div class="col-xs-2">
                        <a href="{{orderitem.transferAttachURL.download}}" download target="_blank">{{orderitem.imgName1}}</a>
                    </div>
                </div>
                
            </form>
        </div>
        <div style="padding-left:50px" class="cooper-title">
            <!--业务配额-->
            <span ng-bind="'ORDER_QUOTA'|translate"></span>
        </div>
        <div class="cooper-tab">
            <form class="form-horizontal" name="myForm1" novalidate>
                <div ng-show="orderitem.hasAddCMCC===true" style="margin-bottom:40px;">
                    <!--移动-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                            <span>移动：</span>
                        </li>
                    </div>
                    <div class="form-group" ng-if="orderitem.hasAddPX">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="orderitem.servType != 3 && orderitem.servType != 4"></span>
                            <span ng-show="orderitem.servType == 4">屏显：</span>
                            <!--<span ng-bind="'ORDER_GG_QUOTA'|translate" ng-show="orderitem.servType == 3"></span>：-->
                            <span ng-bind="'CREATEORDER_GUANGGAOANDPEIE'|translate" ng-show="orderitem.servType == 3"></span>
                        </label>

                        <div class="col-xs-9" ng-show="orderitem.addPX_noLimit_cmcc===true&&orderitem.servType!='3'">
                            <li class="check-li"><span class="redio-btn checked"> </span><span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!--按次-->
                        <div class="col-xs-9" ng-show="orderitem.pxByTimes!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span>{{orderitem.pxByTimes}}</span>
                        </div>
                        <!-- 按人包月 -->
                        <!--修改 20190905 start-->
                        <!--
                        <div class="col-xs-9"
                             ng-show="orderitem.caller.memberCount!==''||orderitem.called.memberCount!==''&&orderitem.servType!='3'">
                            -->
                        <div class="col-xs-9"
                             ng-show="orderitem.caller.memberCount!==''||orderitem.called.memberCount!==''||orderitem.callerAndcalled.memberCount!==''&&orderitem.servType!='3'">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                {{'CREATEORDER_ANREN'|translate}}
                            </li>
                            <!-- 老数据的主叫 -->
                            <li class="check-li" ng-show="orderitem.caller.memberCount!==''&&orderitem.isOldData">
                                <span ng-bind="'CREATEORDER_CALLER'|translate"></span>{{orderitem.caller.memberCount}}人{{orderitem.caller.pxAmountPerPerson}}{{'CREATEORDER_CIREN'|translate}}
                            </li>
                            <!-- 老数据的被叫 -->
                            <li class="check-li" ng-show="orderitem.called.memberCount!==''&&orderitem.isOldData">
                                <span ng-bind="'CREATEORDER_CALLED'|translate"></span>{{orderitem.called.memberCount}}人{{orderitem.called.pxAmountPerPerson}}{{'CREATEORDER_CIREN'|translate}}
                            </li>
                            <!-- 套餐版的主被叫 -->
                            <li class="check-li" ng-show="!orderitem.isOldData">
                                <span style="margin-right:20px;">{{orderitem.quantity}}</span>
                                <span style="margin-right:20px;">套餐选择</span>
                                <span style="margin-right:20px;">{{orderitem.taocanName}}</span>
                            </li>
                        </div>
                        <!--修改 20190905 end-->
                    </div>
                    <div class="form-group" ng-if="orderitem.hasAddGD" ng-show="orderitem.servType!='3'">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span>：</label>


                       <!-- <div class="col-xs-2">
                            <li class="check-li" ng-show="orderitem.guaduanAmount===''"><span
                                    class="check-btn checked-btn checked"> </span> {{'ENTERPRISE_NOLIMITED'|translate}}
                            </li>
                            <li class="check-li" ng-show="orderitem.guaduanAmount!==''">
                                {{orderitem.guaduanAmount}}
                            </li>
                        </div>
-->
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gd_noLimit_cmcc===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="orderitem.guaduanAmount!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.guaduanAmount}}</span>
                        </div>
                        <!-- 按人包月 -->
                        <div class="col-xs-9"
                             ng-show="orderitem.hasAddGDBy">
                            <li class="check-li"><span class="redio-btn checked"> </span>
                                {{'CREATEORDER_ANREN'|translate}}
                            </li>
                            <li class="check-li">
                                <span style="margin-right:20px;">{{orderitem.gdquantity}}</span>
                                <span style="margin-right:20px;">套餐选择</span>
                                <span style="margin-right:20px;">{{orderitem.gdtaocanName}}</span>
                            </li>
                        </div>
                    </div>
                    <!--挂彩-->
                    <div class="form-group" ng-if="hasGC" ng-show="orderitem.servType != 3">
                        <label for="" class="col-xs-2 control-label"  ng-show="orderitem.servType != 4&&orderitem.servType != 3">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJCXBYTIMES'|translate"></span>：</label>
						<label for="" class="col-xs-2 control-label" ng-show="orderitem.servType == 4">
                            <icon>*</icon>
                            <span>彩信</span>：</label>
                   <!--     <div class="col-xs-2">
                           <li class="check-li" ng-show="orderitem.guacaiAmount===''"><span
                                    class="check-btn checked-btn checked"> </span> {{'ENTERPRISE_NOLIMITED'|translate}}
                            </li>
                            <li class="check-li" ng-show="orderitem.guacaiAmount!==''">
                                {{orderitem.guacaiAmount}}
                            </li>
                        </div>-->

                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gc_noLimit_cmcc===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="orderitem.guacaiAmount!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.guacaiAmount}}</span>
                        </div>
                        <!-- 按人包月 -->
                        <div class="col-xs-9"
                             ng-show="orderitem.hasAddGCBy">

                            <li class="check-li"><span class="redio-btn checked"> </span>
                                {{'CREATEORDER_ANREN'|translate}}
                            </li>

                            <li class="check-li">
                                <span style="margin-right:20px;">{{orderitem.gcquantity}}</span>
                                <span style="margin-right:20px;">套餐选择</span>
                                <span style="margin-right:20px;">{{orderitem.gctaocanName}}</span>
                            </li>
                        </div>

                    </div>

                    <div class="form-group" ng-if="orderitem.hasAddZC" ng-show="orderitem.servType == 4 || orderitem.servType == 2">
                         <label for="" class="col-xs-2 control-label" ng-show="orderitem.servType == 4">
                            <icon>*</icon>
                            <span ng-bind="'ZENGCAISERVICE'|translate"></span>：</label>
                        <label for="" class="col-xs-2 control-label" ng-show="orderitem.servType == 2">
                            <icon>*</icon>
                            <span>挂机增彩</span>：</label>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.zcAmount}}</span>
                        </div>

                    </div>
                    <div class="form-group" ng-if="orderitem.hasAddGroupSendSMSCMCC" ng-show="orderitem.servType == 4">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span >短信</span>：</label>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.groupSendSMSCUCCAmount}}</span>
                        </div>

                    </div>
                </div>

                <div ng-show="orderitem.hasAddCUCC===true" style="margin-bottom:40px;">
                    <!--联通-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                            <span>联通：</span>
                        </li>
                    </div>
                    <div class="form-group" ng-if="orderitem.hasGroupSendSMSCUCC" ng-show="orderitem.servType == 4">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span >短信</span>：</label>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.groupSendSMSCUCCAmount}}</span>
                        </div>
                    </div>
                    <div class="form-group" ng-if="orderitem.hasAddPXCUCC">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="orderitem.servType != 4"></span>
                            <span ng-show="orderitem.servType == 4">屏显：</span>
                        </label>
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="orderitem.addPX_noLimit_cucc===true&&orderitem.servType!='3'">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="orderitem.pxByTimes_cucc!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.pxByTimes_cucc}}</span>
                        </div>
                    </div>
                    <!--addby hyj 20191107 top-->
                    <div class="form-group" ng-if="orderitem.hasAddGDCUCC" ng-show="orderitem.servType=='2'">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span>：</label>
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gd_noLimit_cmccCUCC===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="orderitem.guaduanAmountCUCC!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.guaduanAmountCUCC}}</span>
                        </div>
                    </div>
                    <!--addby hyj 20191107 end-->
                </div>
                <div ng-show="orderitem.hasAddCTCC===true" style="margin-bottom:40px;">
                    <!--电信-->
                    <div class="form-group" style="margin-left: 55px">
                        <li class="col-lg-2 col-xs-2 col-sm-2 col-md-2 check-li">
                            <span>电信：</span>
                        </li>
                    </div>
                    <div class="form-group" ng-if="orderitem.hasGroupSendSMSCTCC" ng-show="orderitem.servType == 4">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span >短信</span>：</label>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.groupSendSMSCTCCAmount}}</span>
                        </div>
                    </div>
                    <div class="form-group" ng-if="orderitem.hasAddPXCTCC">
                        <label class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_PX_QUOTA'|translate" ng-show="orderitem.servType != 4"></span>
                            <span ng-show="orderitem.servType == 4">屏显：</span>
                        </label>
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="orderitem.addPX_noLimit_ctcc===true&&orderitem.servType!='3'">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>
                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="orderitem.pxByTimes_ctcc!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.pxByTimes_ctcc}}</span>
                        </div>
                    </div>
                    <!--addby hyj 20191107 top-->
                    <div class="form-group" ng-if="orderitem.hasAddGDCTCC" ng-show="orderitem.servType=='2'">
                        <label for="" class="col-xs-2 control-label">
                            <icon>*</icon>
                            <span ng-bind="'ORDER_GJDXBYTIMES'|translate"></span>：</label>
                        <!-- 不限 -->
                        <div class="col-xs-9" ng-show="gd_noLimit_cmccCTCC===true">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'ENTERPRISE_NOLIMITED'|translate"></span></li>
                        </div>

                        <!-- 按次 -->
                        <div class="col-xs-9" ng-show="orderitem.guaduanAmountCTCC!==''">
                            <li class="check-li"><span class="redio-btn checked"> </span> <span
                                    ng-bind="'CREATEORDER_ANCI'|translate"></span></li>
                            <span style="margin-right:20px;">{{orderitem.guaduanAmountCTCC}}</span>
                        </div>
                    </div>
                    <!--addby hyj 20191107 end-->
                </div>
                <div class="form-group">
                    <label class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'COMMON_EXPIRYDATE'|translate"></span>：</label>

                    <div class="col-xs-9">
                        <div class="data-time">
                            <div class="input-group date  star-time" aria-disabled="true">
                                <input type="text" class="form-control" ng-model="orderitem.effictiveTime"
                                       disabled>
                            </div>
                            <span class="to" ng-bind="'TO'|translate"></span>

                            <div class="input-group date  end-time" aria-disabled="true">
                                <input type="text" class="form-control" ng-model="orderitem.expireTime" disabled>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="" class="col-xs-2 control-label">
                        <icon>*</icon>
                        <span ng-bind="'COMMON_STATUS'|translate"></span>：</label>

                    <div class="col-xs-2">
                        <li class="check-li" ng-bind="statusMap[initOrderInfo.orderStatus]"></li>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <div class="order-btn">
        <button type="submit" class="btn btn-back" ng-click="gotoList()"><span ng-bind="'COMMON_BACK'|translate"></span>
        </button>
    </div>
</div>

<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel" ng-bind="'COMMON_TIP'|translate"></h4>
            </div>
            <div class="modal-body">
                <div class="text-center"><p style='font-size: 16px;color:#383838'>
                    {{tip|translate}}
                </p></div>
            </div>
            <div class="modal-footer" style="text-align:center">
                <button type="submit" class="btn " data-dismiss="modal" aria-label="Close"
                        ng-bind="'COMMON_OK'|translate"></button>
            </div>
        </div>
    </div>
</div>

</body>

</html>