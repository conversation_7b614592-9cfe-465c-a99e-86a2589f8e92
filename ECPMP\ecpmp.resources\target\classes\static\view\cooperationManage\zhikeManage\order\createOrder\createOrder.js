var app = angular.module("myApp", ["util.ajax", "service.common", "angularI18n", "top.menu", "cy.uploadifyfile"]);
app.controller('orderController', function ($scope, $rootScope, $location, RestClientUtil, CommonUtils) {

    $scope.accepttype = "jpg,jpeg,png,xlsx,doc,pdf";
    $scope.isValidate = true;
    $scope.filesize = 20;
    $scope.mimetypes = ".jpg,.jpeg,.png,.xlsx,.doc,.pdf";
    $scope.auto = true;
    $scope.isCreateThumbnail = true;
    $scope.uploadurl = '/qycy/ecpmp/ecpmpServices/fileService/uploadAllTypeFile';
    $scope.uploadDesc = "必填，仅支持一张图片，仅支持jpg，jpeg，png，pdf格式";
    $scope.numlimit = 1;
    $scope.urlList1 = [];
    $scope.urlList2 = [];
    $scope.button_disabled = "";
    $scope.button_disabled_val = 1;


    $scope.$on("uploadifyid1", function (event, fileUrl, index, broadData) {
        if (broadData) {
            if (broadData.file !== "") {
                $scope.orderDetailURL.fileName = broadData.file.name;
            } else {
                $scope.orderDetailURL.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.orderDetailURL.errorInfo = broadData.errorInfo;
        }
        $scope.orderDetailURL.upload = fileUrl;
    });

    $scope.$on("uploadifyid2", function (event, fileUrl, index, broadData) {
        if (broadData) {
            if (broadData.file !== "") {
                $scope.transferAttachURL.fileName = broadData.file.name;
            } else {
                $scope.transferAttachURL.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.transferAttachURL.errorInfo = broadData.errorInfo;
        }
        $scope.transferAttachURL.upload = fileUrl;
    });

    //体验申请单
    $scope.$on("uploadifyid3", function (event, fileUrl, index, broadData) {
        if (broadData) {
            if (broadData.file !== "") {
                $scope.transferAttachURL.fileName = broadData.file.name;
            } else {
                $scope.transferAttachURL.fileName = "";
            }
            $scope.uploader = broadData.uploader;
            $scope.transferAttachURL.errorInfo = broadData.errorInfo;
        }
        $scope.transferAttachURL.upload = fileUrl;
    });

    //初始化页面
    $scope.init = function () {
        $scope.isTransferAttachURL = false;
        $scope.isOrderDetailURL = false;
        //新增挂短消费类型
        $scope.chargeTypeGuaDuan = 0;
        $scope.chargeTypeGuaDuanCUCC = 0;     //联通挂短计费方式
        $scope.chargeTypeGuaDuanCTCC = 0;     //电信挂短计费方式
        $scope.chargeTypeGuaCai = 0;
        $scope.chargeType = 0;
        $scope.chargeType_cucc = 0;
        $scope.chargeType_ctcc = 0;
        $scope.isAcDesc = true;
        $scope.isGdDesc = true;
        $scope.isGdDescCUCC = true;
        $scope.isGdDescCTCC = true;
        $scope.isGcDesc = true;
        $scope.isAcDesc_ctcc = true;
        $scope.isAcDesc_cucc = true;

        $scope.gjzcchargeType = 0;
        
        $scope.zcchargeType = 0;
        
        $scope.cxchargeTypeEx = 0;

        $scope.smschargeTypeEx = 0;
        $scope.smschargeTypeExCUCC = 0;
        $scope.smschargeTypeExCTCC = 0;
        $scope.screenchargeTypeEx = 0;
        $scope.screenchargeTypeExCUCC = 0;
        $scope.screenchargeTypeExCTCC = 0;
        //初始化分页信息
        $scope.pageInfo = [
            {
                "totalPage": 1,
                "totalCount": 0,
                "pageSize": '1000',
                "currentPage": 1
            }
        ];

        //默认企业名称
        $scope.enterpriseID = $.cookie('enterpriseID') || '';
        $scope.uploadParam = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'transferAttach'
        };
        $scope.uploadParam2 = {
            enterpriseId: $scope.enterpriseID,
            fileUse: 'orderDetail'
        };
        $scope.enterpriseName = $.cookie('enterpriseName') || '';
        $scope.isSuperManager = false;
        $scope.operatorID = $.cookie('accountID') || '';
        $scope.enterpriseType = $.cookie('enterpriseType') || '';
        $scope.loginRoleType = $.cookie('loginRoleType');
        $scope.isSuperManager = ($scope.loginRoleType == 'superrManager' || $scope.loginRoleType == 'normalMangager');
        if ($scope.isSuperManager == false) {
            location.href = '../orderList/orderList.html';
        }

        //订单的生成日期获取
        var time = CommonUtils.getServerTime();
        var year = time.year;
        var month = time.month;
        var day = time.day;
        var hour = time.hour;
        var minutes = time.minutes;
        var seconds = time.seconds;

        $scope.servertime = year + month + day + hour + minutes + seconds;

        //0:非体验版 1:体验版
        $scope.isExperience = 0;
        $scope.button_disabled = "orderBase.amount.$invalid || effictiveTime==='' ||expireTime ===''"
            + "||(servType != '4' && ((cmcc && postPingXianCMCC && chargeType==0 && desc)"
            + "|| !orderDetailURL.upload ||!transferAttachURL.upload || orderDetailURL.errorInfo|| transferAttachURL.errorInfo"
            + "||(cmcc && postPingXianCMCC && chargeType==1 && (orderItemDomain.anci.$invalid || acDesc))"
            + "||(cmcc && postPingXianCMCC && chargeType==2 && (orderItemDomain.peirenyue.$invalid || byDesc))"
            + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==0 && gdesc)"
            + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==1 && (orderItemDomain.guaduanInput.$invalid || gdDesc))"
            + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==2 && (orderItemDomain.guaDuanPrice.$invalid || gdByDesc))"
            + "||(cmcc && postGuaCai && chargeTypeGuaCai==0 && gcesc)"
            + "||(cmcc && postGuaCai && chargeTypeGuaCai==1 && (orderItemDomain.guacaiInput.$invalid || gcDesc))"
            + "||(cmcc && postGuaCai && chargeTypeGuaCai==2 && (orderItemDomain.guaCaiPrice.$invalid || gcByDesc))"
            + "||(cucc && postPingXianCUCC && chargeType_cucc==0  && desc_cucc)"
            + "||(cucc && postPingXianCUCC && chargeType_cucc==1  && (orderItemDomain.anci_cucc.$invalid || acDesc_cucc))"
            + "||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && desc_ctcc)"
            + "||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && (orderItemDomain.anci_ctcc.$invalid || acDesc_ctcc))"
            + "||!(cmcc || cucc || ctcc)"
            + "||(!(cmcc && (postPingXianCMCC || (servType !='4'&& postGuaCai) || postGuaDuan || postZengCaiCMCC)) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)) && servType !='3')"
            + "||(servType =='3' && !(cmcc && postPingXianCMCC) && isExperience == 1)"
            + "||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))"
            + "|| (servType == '4' && ((isExperience == 0 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid) || (isExperience == 1 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid)))";


        //默认为1：名片彩印
        $scope.servType = 1;

        //默认屏显配额是配置的
        $scope.postPingXianCMCC = true;
        $scope.postPingXianCUCC = true;
        $scope.postPingXianCTCC = true;
        //默认增彩认配额是配置的
        $scope.postZengCaiCMCC = true;
        $scope.postZengCaiCUCC = true;
        $scope.postZengCaiCTCC = true;
        //默认挂机短信是配置的
        $scope.postGuaDuan = true;
        $scope.postGuaDuanCUCC = true;                  //联通挂短配置
        $scope.postGuaDuanCTCC = true;                  //电信挂短配置
        //默认挂机彩信是配置的
        $scope.postGuaCai = true;

        //屏显配额子业务类型
        $scope.subServType = 5;
        //默认屏显配额是不限，字段值为
        $scope.pingxianisLimit = 0;
        $scope.pingxianisLimit_cucc = 0;
        $scope.pingxianisLimit_ctcc = 0;
        //默认挂机短信是不限，字段值为
        $scope.guaduanisLimit = 0;
        $scope.guaduanisLimitCUCC = 0;     //联通挂短是否限量
        $scope.guaduanisLimitCTCC = 0;     //联通挂短是否限量
        //默认挂机彩信是不限，字段值为
        $scope.guacaiisLimit = 0;

        //有效期
        $scope.effictiveTime = "";
        $scope.expireTime = "";

        //上传的配置
        $scope.transferAttachURL = {};
        $scope.orderDetailURL = {};
//    $scope.transferAttachURL.upload = "/home/<USER>/uploadFile/10000017/orderDetail/20190509173214/down_arrow_16px_1205405_easyicon.net.png";
//    $scope.orderDetailURL.upload = "/home/<USER>/uploadFile/10000017/orderDetail/20190509173214/down_arrow_16px_1205405_easyicon.net.png"
        //下拉框(业务类别)
        $scope.servTypeChoise = [
            {
                id: 1,
                name: "名片彩印"
            },
            {
                id: 2,
                name: "热线彩印"
            },
            {
                id: 3,
                name: "广告彩印"
            }
        ];
        //直客和代理商共用同一个页面,企业通知只有代理商有
        if ($scope.enterpriseType == '2') {
            var s = {
                id: 4,
                name: "企业通知"
            };
            $scope.servTypeChoise.push(s);
        }
        $scope.cmcc = true;
        $scope.cucc = true;
        $scope.ctcc = true;
        $scope.queryProductInfoList('init');

    };

    $scope.chooseCMCC = function () {
        $scope.cmcc = !$scope.cmcc;
    };

    $scope.chooseCUCC = function () {
        $scope.cucc = !$scope.cucc;
    };
    $scope.chooseCTCC = function () {
        $scope.ctcc = !$scope.ctcc;
        console.log($scope.ctcc + "" + $scope.cucc + "" + $scope.cmcc);
    };

    $scope.showOrderName = function (i) {
        $scope.toInit();

        //点击订单类型清空上传地址
        $scope.transferAttachURL.fileName = "";
        $scope.orderDetailURL.fileName = "";
        $scope.orderDetailURL.upload = "";
        $scope.transferAttachURL.upload = "";

        if (i == 1) {
            $scope.isExperience = 1;

            $("#ZZMX").css("display", "none");
            $("#DDMX").css("display", "none");
            $("#TYSQD").css("visibility", "visible");
            //校验是体验订单
            $scope.button_disabled = "orderBase.amount.$invalid || effictiveTime==='' ||expireTime ===''"
                + "||(servType != '4' && ((cmcc && postPingXianCMCC && chargeType==0 && desc)"
                + "|| !transferAttachURL.upload || transferAttachURL.errorInfo"
                + "||(cmcc && postPingXianCMCC && chargeType==1 && (orderItemDomain.anci.$invalid || acDesc))"
                + "||(cmcc && postPingXianCMCC && chargeType==2 && (orderItemDomain.peirenyue.$invalid || byDesc))"
                + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==0 && gdesc)"
                + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==1 && (orderItemDomain.guaduanInput.$invalid || gdDesc))"
                + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==2 && (orderItemDomain.guaDuanPrice.$invalid || gdByDesc))"
                + "||(cmcc && postGuaCai && chargeTypeGuaCai==0 && gcesc)"
                + "||(cmcc && postGuaCai && chargeTypeGuaCai==1 && (orderItemDomain.guacaiInput.$invalid || gcDesc))"
                + "||(cmcc && postGuaCai && chargeTypeGuaCai==2 && (orderItemDomain.guaCaiPrice.$invalid || gcByDesc))"
                + "||(cucc && postPingXianCUCC && chargeType_cucc==0  && desc_cucc)"
                + "||(cucc && postPingXianCUCC && chargeType_cucc==1  && (orderItemDomain.anci_cucc.$invalid || acDesc_cucc))"
                + "||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && desc_ctcc)"
                + "||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && (orderItemDomain.anci_ctcc.$invalid || acDesc_ctcc))"
                + "||!(cmcc || cucc || ctcc)"
                + "||(!(cmcc && (postPingXianCMCC || (servType !='4'&& postGuaCai) || postGuaDuan || (servType =='4'&& postZengCaiCMCC))) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)) && servType !='3')"
                + "||(servType =='3' && !(cmcc && postPingXianCMCC) && isExperience == 1)"
                + "||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))"
                + "|| (servType == '4' && ((isExperience == 0 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid) || (isExperience == 1 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid)))";
        }
        else if (i == 0) {
            $scope.isExperience = 0;
            $("#ZZMX").css("display", "");
            $("#DDMX").css("display", "");
            $("#TYSQD").css("visibility", "hidden");
            //校验是体验订单
            $scope.transferAttachURL.upload = "";
            $scope.transferAttachURL.fileName = "";
            $scope.button_disabled = "orderBase.amount.$invalid || effictiveTime==='' ||expireTime ===''"
                + "||(servType != '4' && ((cmcc && postPingXianCMCC && chargeType==0 && desc)"
                + "|| !orderDetailURL.upload ||!transferAttachURL.upload || orderDetailURL.errorInfo|| transferAttachURL.errorInfo"
                + "||(cmcc && postPingXianCMCC && chargeType==1 && (orderItemDomain.anci.$invalid || acDesc))"
                + "||(cmcc && postPingXianCMCC && chargeType==2 && (orderItemDomain.peirenyue.$invalid || byDesc))"
                + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==0 && gdesc)"
                + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==1 && (orderItemDomain.guaduanInput.$invalid || gdDesc))"
                + "||(cmcc && postGuaDuan && chargeTypeGuaDuan==2 && (orderItemDomain.guaDuanPrice.$invalid || gdByDesc))"
                + "||(cmcc && postGuaCai && chargeTypeGuaCai==0 && gcesc)"
                + "||(cmcc && postGuaCai && chargeTypeGuaCai==1 && (orderItemDomain.guacaiInput.$invalid || gcDesc))"
                + "||(cmcc && postGuaCai && chargeTypeGuaCai==2 && (orderItemDomain.guaCaiPrice.$invalid || gcByDesc))"
                + "||(cucc && postPingXianCUCC && chargeType_cucc==0  && desc_cucc)"
                + "||(cucc && postPingXianCUCC && chargeType_cucc==1  && (orderItemDomain.anci_cucc.$invalid || acDesc_cucc))"
                + "||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && desc_ctcc)"
                + "||(ctcc && postPingXianCTCC && chargeType_ctcc==1  && (orderItemDomain.anci_ctcc.$invalid || acDesc_ctcc))"
                + "||!(cmcc || cucc || ctcc)"
                + "||(!(cmcc && (postPingXianCMCC || (servType !='4'&& postGuaCai) || postGuaDuan || (servType =='4'&& postZengCaiCMCC))) && !(cucc && (postPingXianCUCC || postGuaDuanCUCC)) && !(ctcc && (postPingXianCTCC || postGuaDuanCTCC)) && servType !='3')"
                + "||(servType =='3' && !(cmcc && postPingXianCMCC) && isExperience == 1)"
                + "||(!(cmcc && postPingXianCMCC) && !(cucc && postPingXianCUCC) && !(ctcc && postPingXianCTCC) && isExperience == 0 && servType =='3')))"
                + "|| (servType == '4' && ((isExperience == 0 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid) || (isExperience == 1 && zcchargeTypeEx ==1 && orderItemDomain.zcanci.$invalid)))";
        }

        var servTypeName;
        var experience;

        if ($scope.servType == 1 || $scope.servType == 2) {
            if ($scope.servType == 1) {
                servTypeName = "名片彩印";
            } else if ($scope.servType == 2) {
                servTypeName = "热线彩印";
            }
            $scope.changePingXianType(1);
            $scope.changePingXianTypeCUCC(1);
            $scope.changePingXianTypeCTCC(1);

            $scope.pingxianBuxianProductList = $scope.filterProductInfoList($scope.servType,
                3, 0, 1, 0, $scope.isExperience, 1, 1);
            $scope.pingxianBuxianProductCUCC = $scope.filterProductInfoList($scope.servType,
                3, 0, 1, 0, $scope.isExperience, 1, 2);
            $scope.pingxianBuxianProductCTCC = $scope.filterProductInfoList($scope.servType,
                3, 0, 1, 0, $scope.isExperience, 1, 3);

            $scope.guaDuanProductList = $scope.filterProductInfoList($scope.servType,
                4, 0, 1, 0, $scope.isExperience, 1, 1);
            $scope.guaCaiProductList = $scope.filterProductInfoList($scope.servType,
                8, 0, 1, 0, $scope.isExperience, 1, 1);

            if ($scope.guaDuanProductList.length <= 0) {
                $scope.guaduanDesc = "暂无此款产品";
                $scope.isGdDesc = false;
            } else {
                $scope.guaduanDesc = "";
                $scope.isGdDesc = true;
            }
            if ($scope.guaCaiProductList.length <= 0) {
                $scope.guacaiDesc = "暂无此款产品";
                $scope.isGcDesc = false;
            } else {
                $scope.GCPrice = $scope.guaCaiProductList[0].unitPrice;
                $scope.guacaiDesc = "";
                $scope.isGcDesc = true;
            }

        }

        //add 20191106
        if ($scope.servType == 2) {
            $scope.guaDuanProductListCUCC = $scope.filterProductInfoList($scope.servType,
                4, 0, 1, 0, $scope.isExperience, 1, 2);                                         //联通挂短不限产品集

            $scope.guaDuanProductListCTCC = $scope.filterProductInfoList($scope.servType,
                4, 0, 1, 0, $scope.isExperience, 1, 3);                             //电信挂短不限产品集

            //联通挂短不限
            if ($scope.guaDuanProductListCUCC.length <= 0) {
                $scope.guaduanDescCUCC = "暂无此款产品";
                $scope.isGdDescCUCC = false;
            } else {
                $scope.guaduanDescCUCC = "";
                $scope.isGdDescCUCC = true;
            }

            //电信挂短不限
            if ($scope.guaDuanProductListCTCC.length <= 0) {
                $scope.guaduanDescCTCC = "暂无此款产品";
                $scope.isGdDescCTCC = false;
            } else {
                $scope.guaduanDescCTCC = "";
                $scope.isGdDescCTCC = true;
            }
            //挂机增彩
            $scope.gjzengcaiProductList = $scope.filterProductInfoList($scope.servType,
                    16, 0, 1, $scope.gjzcchargeTypeEx, $scope.isExperience, 1, 1);
            $scope.gjzcPrice = $scope.gjzengcaiProductList[0].unitPrice;

        }

        if ($scope.servType == 3) {
            servTypeName = "广告彩印";
            //默认挂机短信是配置的
            $scope.postGuaDuan = false;
            $scope.postGuaDuanCUCC = false;         //联通挂短
            $scope.postGuaDuanCTCC = false;         //电信挂短
            //默认挂机彩信是配置的
            $scope.postGuaCai = false;
            //默认挂机彩信是配置的


            $scope.changePingXianType(2);
            $scope.changePingXianTypeCUCC(2);
            $scope.changePingXianTypeCTCC(2);
        }

        if ($scope.servType == 4) {
            servTypeName = "企业通知";
            $scope.cmcc = true;
            $scope.cucc = true;
            $scope.ctcc = true;
            if ($scope.isExperience == 1) {
                $scope.zengcaiProductList = $scope.filterProductInfoList($scope.servType,
                    16, 0, 1, $scope.zcchargeTypeEx, $scope.isExperience, 1, 1);
            }
            else {
                $scope.zengcaiProductList = $scope.filterProductInfoList($scope.servType,
                    16, 0, 1, $scope.zcchargeTypeEx, $scope.isExperience, 1, 1);
            }
            $scope.ZCPrice = $scope.zengcaiProductList[0].unitPrice;
            
            $scope.caixinProductList = $scope.filterProductInfoList($scope.servType,
                    8, 0, 1, $scope.cxchargeTypeEx, $scope.isExperience, 1, 1);
            $scope.cxPrice = $scope.caixinProductList[0].unitPrice;

            $scope.groupSMScmccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1,$scope.smschargeTypeEx, $scope.isExperience, 1, 1);
            $scope.groupSMScuccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, $scope.smschargeTypeExCUCC, $scope.isExperience, 1, 2);
            $scope.groupSMSctccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, $scope.smschargeTypeExCTCC, $scope.isExperience, 1, 3);
            $scope.groupSmscmccPrice = $scope.groupSMScmccProductList[0].unitPrice;
            $scope.groupSmscuccPrice = $scope.groupSMScuccProductList[0].unitPrice;
            $scope.groupSmsctccPrice = $scope.groupSMSctccProductList[0].unitPrice;
            //屏显
            $scope.groupScreencmccProductList = $scope.filterProductInfoList($scope.servType,
            		3, 0, 1, $scope.screenchargeTypeEx, $scope.isExperience, 1, 1);
            $scope.groupScreencuccProductList = $scope.filterProductInfoList($scope.servType,
            		3, 0, 1, $scope.screenchargeTypeExCUCC, $scope.isExperience, 1, 2);
            $scope.groupScreenctccProductList = $scope.filterProductInfoList($scope.servType,
            		3, 0, 1, $scope.screenchargeTypeExCTCC, $scope.isExperience, 1, 3);
            $scope.groupScreencmccPrice = $scope.groupScreencmccProductList[0].unitPrice;
            $scope.groupScreencuccPrice = $scope.groupScreencuccProductList[0].unitPrice;
            $scope.groupScreenctccPrice = $scope.groupScreenctccProductList[0].unitPrice;
        }

        if ($scope.isExperience === 1) {
            experience = "体验版";
            //增加，在广告彩印业务类别下 切换体验与非体验，改变联通,电信屏显状态
            $scope.postPingXianCUCC = false;
            $scope.postPingXianCTCC = false;
            if ($scope.servType == 4) {
                $scope.postPingXianCUCC = true;
                $scope.postPingXianCTCC = true;
            }
        }
        else {
            experience = "非体验版";
            //增加，在广告彩印业务类别下 切换体验与非体验，改变联通,电信屏显状态
            if ($scope.servType != 4) {
                $scope.postPingXianCUCC = true;
                $scope.postPingXianCTCC = true;
                $scope.cucc = true;
                $scope.ctcc = true;
            }

        }
        $scope.orderName = servTypeName + "_" + experience + "_主订单_" + $scope.servertime;

    };

    //是否展示屏显的配置
    $scope.showPingXianCMCC = function () {
        $scope.postPingXianCMCC = !$scope.postPingXianCMCC;
    };

    $scope.showPingXianCUCC = function () {
        $scope.postPingXianCUCC = !$scope.postPingXianCUCC;
    };

    $scope.showPingXianCTCC = function () {
        $scope.postPingXianCTCC = !$scope.postPingXianCTCC;
    };

    //是否展示增彩的配置
    $scope.showZengCaiCMCC = function () {
        $scope.postZengCaiCMCC = !$scope.postZengCaiCMCC;
    };

    $scope.showZengCaiCUCC = function () {
        $scope.postZengCaiCUCC = !$scope.postZengCaiCUCC;
    };

    $scope.showZengCaiCTCC = function () {
        $scope.postZengCaiCTCC = !$scope.postZengCaiCTCC;
    };

    //是否展示挂短的配置
    $scope.showGuaDuan = function () {
        $scope.postGuaDuan = !$scope.postGuaDuan;
    };

    $scope.showGuaDuanCUCC = function () {
        $scope.postGuaDuanCUCC = !$scope.postGuaDuanCUCC;
    };

    $scope.showGuaDuanCTCC = function () {
        $scope.postGuaDuanCTCC = !$scope.postGuaDuanCTCC;
    };

    //是否展示挂彩的配置
    $scope.showGuaCai = function () {
        $scope.postGuaCai = !$scope.postGuaCai;
    };

    // 移动屏显
    $scope.changePingXianType = function (i) {
        $scope.anci = "";
        $scope.desc = "";
        $scope.acDesc = "";
        $scope.byDesc = "";
        $scope.isAcDesc = false;
        $scope.isByDesc = false;
        //不限
        if (i == 1) {
            $scope.pingxianisLimit = 0;
            $scope.chargeType = 0;
            $scope.pingxianBuxianProductList = $scope.filterProductInfoList($scope.servType,
                3, 0, 1, 0, $scope.isExperience, 1, 1);
            if ($scope.pingxianBuxianProductList.length <= 0) {
                $scope.desc = "暂无此款产品";
            } else {
                $scope.desc = "";
            }
        }
        else if (i == 2) {// 按次
            $scope.isByDesc = false;
            $scope.pingxianisLimit = 1;
            $scope.chargeType = 1;
            if ($scope.servType == 3) {
                $scope.anciProductList = $scope.filterProductInfoList($scope.servType, 10, 0, 1, 1,
                    $scope.isExperience, 1, 1);
            } else {
                $scope.anciProductList = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 1,
                    $scope.isExperience, 1, 1);
            }
            if ($scope.anciProductList.length > 0) {
                $scope.PXPrice = $scope.anciProductList[0].unitPrice;
            }
            if ($scope.anciProductList.length <= 0) {
                $scope.acDesc = "暂无此款产品";
                $scope.isAcDesc = false;
            } else {
                $scope.acDesc = "";
                $scope.isAcDesc = true;
            }
        }
        else if (i == 3) {// 包月
            $scope.pingxianisLimit = 1;
            $scope.chargeType = 2;
            //传参，子业务类型由'2'改为''
            $scope.baoyueProductList = $scope.filterProductInfoList($scope.servType, 2, 2, 1, 1, $scope.isExperience, 2, 1).concat(
                $scope.filterProductInfoList($scope.servType, 1, 2, 1, 1, $scope.isExperience, 2, 1),
                $scope.filterProductInfoList($scope.servType, 3, 2, 1, 1, $scope.isExperience, 2, 1)
            );

//            $scope.baoyueProductList = $scope.filterProductInfoList($scope.servType, 2, 2, 1, 1, $scope.isExperience, 2, 1);
            $scope.productID = $scope.baoyueProductList.length > 0 ? $scope.baoyueProductList[0].objectID : "";
            if ($scope.baoyueProductList.length <= 0) {
                $scope.byDesc = "暂无此款产品";
                $scope.isByDesc = false;
            } else {
                $scope.unitPrice = $scope.baoyueProductList[0].unitPrice;
                $scope.memberCount = $scope.baoyueProductList[0].memberCount;
                $scope.byDesc = "";
                $scope.isByDesc = true;
            }
        }
    };
    
    $scope.guajiZengCaiType = function (i) {
        //不限
        if (i == 1) {
            $scope.gjzcchargeTypeEx = 0;
            $scope.zengcaiProductList = $scope.filterProductInfoList($scope.servType,
                16, 0, 1, 0, $scope.isExperience, 1, 1);
            $scope.gjzcanci = '';
        }
        //按次
        else if (i == 2) {
            $scope.gjzcchargeTypeEx = 1;
            $scope.gjzengcaiProductList = $scope.filterProductInfoList($scope.servType,
                16, 0, 1, 1, $scope.isExperience, 1, 1);
            $scope.gjzcPrice = $scope.gjzengcaiProductList[0].unitPrice;
        }

    };

    $scope.changeZengCaiType = function (i) {
        //不限
        if (i == 1) {
            $scope.zcchargeTypeEx = 0;
            $scope.zengcaiProductList = $scope.filterProductInfoList($scope.servType,
                16, 0, 1, 0, $scope.isExperience, 1, 1);
            $scope.zcanci = '';
        }
        //按次
        else if (i == 2) {
            $scope.zcchargeTypeEx = 1;
            $scope.zengcaiProductList = $scope.filterProductInfoList($scope.servType,
                16, 0, 1, 1, $scope.isExperience, 1, 1);
            $scope.ZCPrice = $scope.zengcaiProductList[0].unitPrice;
        }

    };
    $scope.changeCaixinType = function (i) {
    	//不限
    	if (i == 1) {
    		$scope.cxchargeTypeEx = 0;
    		$scope.caixinProductList = $scope.filterProductInfoList($scope.servType,
    				8, 0, 1, 0, $scope.isExperience, 1, 1);
    		$scope.cxanci = '';
    	}
    	//按次
    	else if (i == 2) {
    		$scope.cxchargeTypeEx = 1;
    		$scope.caixinProductList = $scope.filterProductInfoList($scope.servType,
    				8, 0, 1, 1, $scope.isExperience, 1, 1);
    		$scope.cxPrice = $scope.caixinProductList[0].unitPrice;
    	}
    	
    };
    $scope.changeGroupSendSMSType = function (i) {
        //不限
        if (i == 1) {
            $scope.smschargeTypeEx = 0;
            $scope.groupSMScmccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, 0, $scope.isExperience, 1, 1);
            $scope.groupSMSCMCCanciEx = '';
            $scope.groupSendSMSCMCCanci = '';
        }
        //按次
        else if (i == 2) {
            $scope.smschargeTypeEx = 1;
            $scope.groupSMScmccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, 1, $scope.isExperience, 1, 1);
            $scope.groupSmscmccPrice = $scope.groupSMScmccProductList[0].unitPrice;
        }

    }
    
    $scope.changeGroupSendSMSTypeCUCC = function (i) {
        //不限
        if (i == 1) {
            $scope.smschargeTypeExCUCC = 0;
            $scope.groupSMScuccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, 0, $scope.isExperience, 1, 2);
            $scope.groupSMSCUCCanciEx = '';
            $scope.groupSendSMSCUCCanci = '';
        }
        //按次
        else if (i == 2) {
            $scope.smschargeTypeExCUCC = 1;
            $scope.groupSMScuccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, 1, $scope.isExperience, 1, 2);
            $scope.groupSmscuccPrice = $scope.groupSMScuccProductList[0].unitPrice;
        }

    }
    
    $scope.changeGroupSendSMSTypeCTCC = function (i) {
        //不限
        if (i == 1) {
            $scope.smschargeTypeExCTCC = 0;
            $scope.groupSMSctccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, 0, $scope.isExperience, 1, 3);
            $scope.groupSMSCTCCanciEx = '';
            $scope.groupSendSMSCUCCanci = '';
        }
        //按次
        else if (i == 2) {
            $scope.smschargeTypeExCTCC = 1;
            $scope.groupSMSctccProductList = $scope.filterProductInfoList($scope.servType,
                17, 0, 1, 1, $scope.isExperience, 1, 3);
            $scope.groupSmsctccPrice = $scope.groupSMSctccProductList[0].unitPrice;
        }

    }
    $scope.changeGroupSendScreenType = function (i) {
    	//不限
    	if (i == 1) {
    		$scope.screenchargeTypeEx = 0;
    		$scope.groupScreencmccProductList = $scope.filterProductInfoList($scope.servType,
    				3, 0, 1, 0, $scope.isExperience, 1, 1);
    		$scope.groupScreenCMCCanciEx = '';
    		$scope.groupSendScreenCMCCanci = '';
    	}
    	//按次
    	else if (i == 2) {
    		$scope.screenchargeTypeEx = 1;
    		$scope.groupScreencmccProductList = $scope.filterProductInfoList($scope.servType,
    				3, 0, 1, 1, $scope.isExperience, 1, 1);
    		$scope.groupScreencmccPrice = $scope.groupScreencmccProductList[0].unitPrice;
    	}
    	
    }
    
    $scope.changeGroupSendScreenTypeCUCC = function (i) {
    	//不限
    	if (i == 1) {
    		$scope.screenchargeTypeExCUCC = 0;
    		$scope.groupScreencuccProductList = $scope.filterProductInfoList($scope.servType,
    				3, 0, 1, 0, $scope.isExperience, 1, 2);
    		$scope.groupScreenCUCCanciEx = '';
    		$scope.groupSendScreenCUCCanci = '';
    	}
    	//按次
    	else if (i == 2) {
    		$scope.screenchargeTypeExCUCC = 1;
    		$scope.groupScreencuccProductList = $scope.filterProductInfoList($scope.servType,
    				3, 0, 1, 1, $scope.isExperience, 1, 2);
    		$scope.groupScreencuccPrice = $scope.groupScreencuccProductList[0].unitPrice;
    	}
    	
    }
    
    $scope.changeGroupSendScreenTypeCTCC = function (i) {
    	//不限
    	if (i == 1) {
    		$scope.screenchargeTypeExCTCC = 0;
    		$scope.groupScreenctccProductList = $scope.filterProductInfoList($scope.servType,
    				3, 0, 1, 0, $scope.isExperience, 1, 3);
    		$scope.groupScreenCTCCanciEx = '';
    		$scope.groupSendScreenCTCCanci = '';
    	}
    	//按次
    	else if (i == 2) {
    		$scope.screenchargeTypeExCTCC = 1;
    		$scope.groupScreenctccProductList = $scope.filterProductInfoList($scope.servType,
    				3, 0, 1, 1, $scope.isExperience, 1, 3);
    		$scope.groupScreenctccPrice = $scope.groupScreenctccProductList[0].unitPrice;
    	}
    	
    }

    // 联通屏显
    $scope.changePingXianTypeCUCC = function (i) {
        $scope.anci_cucc = "";
        $scope.desc_cucc = "";
        $scope.acDesc_cucc = "";
        $scope.isAcDesc_cucc = false;
        //不限
        if (i == 1) {
            $scope.pingxianisLimit_cucc = 0;
            $scope.chargeType_cucc = 0;
            $scope.pingxianBuxianProductCUCC = $scope.filterProductInfoList($scope.servType,
                3, 0, 1, 0, $scope.isExperience, 1, 2);
            if ($scope.pingxianBuxianProductCUCC.length <= 0) {
                $scope.desc_cucc = "暂无此款产品";
            } else {
                $scope.desc_cucc = "";
            }
        }
        else if (i == 2) {// 按次
            $scope.pingxianisLimit_cucc = 1;
            $scope.chargeType_cucc = 1;
            if ($scope.servType == 3) {
                //广告类型
                $scope.anciProductCUCC = $scope.filterProductInfoList($scope.servType, 2, 0, 1, 1,
                    $scope.isExperience, 1, 2);
            } else {
                $scope.anciProductCUCC = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 1,
                    $scope.isExperience, 1, 2);
            }
            if ($scope.anciProductCUCC.length <= 0) {
                $scope.acDesc_cucc = "暂无此款产品";
                $scope.isAcDesc_cucc = false;
            } else {
                $scope.PXPrice_cucc = $scope.anciProductCUCC[0].unitPrice;
                $scope.acDesc_cucc = "";
                $scope.isAcDesc_cucc = true;
            }
        }
    };

    // 电信屏显
    $scope.changePingXianTypeCTCC = function (i) {
        $scope.anci_ctcc = "";
        $scope.desc_ctcc = "";
        $scope.acDesc_ctcc = "";
        $scope.isAcDesc_ctcc = false;
        //不限
        if (i == 1) {
            $scope.pingxianisLimit_ctcc = 0;
            $scope.chargeType_ctcc = 0;
            $scope.pingxianBuxianProductCUCC = $scope.filterProductInfoList($scope.servType,
                3, 0, 1, 0, $scope.isExperience, 1, 3);
            if ($scope.pingxianBuxianProductCUCC.length <= 0) {
                $scope.desc_ctcc = "暂无此款产品";
            } else {
                $scope.desc_ctcc = "";
            }
        }
        else if (i == 2) {//按次
            $scope.pingxianisLimit_ctcc = 1;
            $scope.chargeType_ctcc = 1;
            if ($scope.servType == 3) {
                //广告类型
                $scope.anciProductCTCC = $scope.filterProductInfoList($scope.servType, 2, 0, 1, 1,
                    $scope.isExperience, 1, 3);
            } else {
                $scope.anciProductCTCC = $scope.filterProductInfoList($scope.servType, 3, 0, 1, 1,
                    $scope.isExperience, 1, 3);
            }

            if ($scope.anciProductCTCC.length <= 0) {
                $scope.acDesc_ctcc = "暂无此款产品";
                $scope.isAcDesc_ctcc = false;
            } else {
                $scope.PXPrice_ctcc = $scope.anciProductCTCC[0].unitPrice;
                $scope.acDesc_ctcc = "";
                $scope.isAcDesc_ctcc = true;
            }
        }
    };

    $scope.exportFile = function (downloadUrl) {
        var req = {
            "param": {
                "path": downloadUrl,
                "token": $.cookie("token"),
                "isExport": 0
            },
            "url": "/qycy/ecpmp/ecpmpServices/fileService/downloadFile",
            "method": "get"
        };
        CommonUtils.exportFile(req);
    };

    //改：增加按次 包月  20190618
    //移动挂短
    $scope.changeGuaDuanType = function (i) {
        $scope.gdesc = "";
        $scope.gdDesc = "";
        $scope.gdByDesc = "";
        $scope.isGdDesc = false;
        $scope.isGDByDesc = false;
        //不限
        if (i == 1) {
            $scope.chargeTypeGuaDuan = 0;
            $scope.guaduanisLimit = 0;
            $scope.guaDuanProductList = $scope.filterProductInfoList($scope.servType,
                4, 0, 1, 0, $scope.isExperience, 1, 1);

            if ($scope.guaDuanProductList.length <= 0) {
                $scope.gdesc = "暂无此款产品";
            } else {
                $scope.gdesc = "";
            }
        }
        else if (i == 2) {// 按次
            $scope.isGdDesc = false;
            $scope.chargeTypeGuaDuan = 1;
            $scope.guaduanisLimit = 1;

            $scope.anciGuaDuanProductList = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 1,
                $scope.isExperience, 1, 1);

            if ($scope.anciGuaDuanProductList.length > 0) {
                $scope.GDPrice = $scope.anciGuaDuanProductList[0].unitPrice;
                $scope.gdDesc = "";
                $scope.isGdDesc = true;
            }
            if ($scope.anciGuaDuanProductList.length <= 0) {
                $scope.gdDesc = "暂无此款产品";
                $scope.isGdDesc = false;
            }
        }
        else if (i == 3) {// 包月
            $scope.chargeTypeGuaDuan = 2;
            $scope.guaduanisLimit = 1;
            $scope.guaDuanBaoyueProductList = $scope.filterProductInfoList($scope.servType, 4, 2, 1, 1,
                $scope.isExperience, 2, 1);

            $scope.gdproductID = $scope.guaDuanBaoyueProductList.length > 0 ? $scope.guaDuanBaoyueProductList[0].objectID : "";

            if ($scope.guaDuanBaoyueProductList.length <= 0) {
                $scope.gdByDesc = "暂无此款产品";
                $scope.isGDByDesc = false;
            } else {
                $scope.gdunitPrice = $scope.guaDuanBaoyueProductList[0].unitPrice;
                $scope.gdmemberCount = $scope.guaDuanBaoyueProductList[0].memberCount;
                $scope.gdByDesc = "";
                $scope.isGDByDesc = true;
            }
        }
    };

    //联通挂短 20191106
    $scope.changeGuaDuanTypeCUCC = function (i) {
        $scope.gdescCUCC = "";                  //不限
        $scope.gdDescCUCC = "";                 //按次
        $scope.isGdDescCUCC = false;            //按次
        //不限
        if (i == 1) {
            $scope.chargeTypeGuaDuanCUCC = 0;    //挂短计费方式
            $scope.guaduanisLimitCUCC = 0;       //挂短是否限量
            $scope.guaDuanProductListCUCC = $scope.filterProductInfoList($scope.servType,
                4, 0, 1, 0, $scope.isExperience, 1, 2);         //联通挂短不限产品集

            if ($scope.guaDuanProductListCUCC.length <= 0) {
                $scope.gdescCUCC = "暂无此款产品";
            } else {
                $scope.gdescCUCC = "";
            }
        }
        else if (i == 2) {// 按次
            $scope.isGdDescCUCC = false;
            $scope.chargeTypeGuaDuanCUCC = 1;
            $scope.guaduanisLimitCUCC = 1;

            $scope.anciGuaDuanProductListCUCC = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 1,
                $scope.isExperience, 1, 2);                    //联通挂短限量产品集

            if ($scope.anciGuaDuanProductListCUCC.length > 0) {
                $scope.GDPriceCUCC = $scope.anciGuaDuanProductListCUCC[0].unitPrice;      // 单价
                $scope.gdDescCUCC = "";
                $scope.isGdDescCUCC = true;
            }
            if ($scope.anciGuaDuanProductListCUCC.length <= 0) {
                $scope.gdDescCUCC = "暂无此款产品";
                $scope.isGdDescCUCC = false;
            }
        }
    };

    //电信挂短 20191106
    $scope.changeGuaDuanTypeCTCC = function (i) {
        $scope.gdescCTCC = "";                  //不限
        $scope.gdDescCTCC = "";                 //按次
        $scope.isGdDescCTCC = false;            //按次
        //不限
        if (i == 1) {
            $scope.chargeTypeGuaDuanCTCC = 0;    //挂短计费方式
            $scope.guaduanisLimitCTCC = 0;       //挂短是否限量
            $scope.guaDuanProductListCTCC = $scope.filterProductInfoList($scope.servType,
                4, 0, 1, 0, $scope.isExperience, 1, 3);         //电信挂短不限产品集

            if ($scope.guaDuanProductListCTCC.length <= 0) {
                $scope.gdescCTCC = "暂无此款产品";
            } else {
                $scope.gdescCTCC = "";
            }
        }
        else if (i == 2) {// 按次
            $scope.isGdDescCTCC = false;
            $scope.chargeTypeGuaDuanCTCC = 1;
            $scope.guaduanisLimitCTCC = 1;

            $scope.anciGuaDuanProductListCTCC = $scope.filterProductInfoList($scope.servType, 4, 0, 1, 1,
                $scope.isExperience, 1, 3);                    //电信挂短限量产品集

            if ($scope.anciGuaDuanProductListCTCC.length > 0) {
                $scope.GDPriceCTCC = $scope.anciGuaDuanProductListCTCC[0].unitPrice;      // 单价
                $scope.gdDescCTCC = "";
                $scope.isGdDescCTCC = true;
            }
            if ($scope.anciGuaDuanProductListCTCC.length <= 0) {
                $scope.gdDescCTCC = "暂无此款产品";
                $scope.isGdDescCTCC = false;
            }
        }
    };


    //改：增加按次 包月  20190619
    //移动挂彩
    $scope.changeGuaCaiType = function (i) {
        $scope.gcesc = "";
        $scope.gcDesc = "";
        $scope.gcByDesc = "";
        $scope.isGcDesc = false;
        $scope.isGCByDesc = false;
        //不限
        if (i == 1) {
            $scope.chargeTypeGuaCai = 0;
            $scope.guacaiisLimit = 0;
            $scope.guaCaiProductList = $scope.filterProductInfoList($scope.servType,
                8, 0, 1, 0, $scope.isExperience, 1, 1);

            if ($scope.guaCaiProductList.length <= 0) {
                $scope.gcesc = "暂无此款产品";
            } else {
                $scope.gcesc = "";
            }
        }
        else if (i == 2) {// 按次
            $scope.isGcDesc = false;
            $scope.chargeTypeGuaCai = 1;
            $scope.guacaiisLimit = 1;

            $scope.anciGuaCaiProductList = $scope.filterProductInfoList($scope.servType, 8, 0, 1, 1,
                $scope.isExperience, 1, 1);

            if ($scope.anciGuaCaiProductList.length > 0) {
                $scope.GCPrice = $scope.anciGuaCaiProductList[0].unitPrice;
                $scope.gcDesc = "";
                $scope.isGcDesc = true;

            }
            if ($scope.anciGuaCaiProductList.length <= 0) {
                $scope.gcDesc = "暂无此款产品";
                $scope.isGcDesc = false;
            }
        }
        else if (i == 3) {// 包月
            $scope.chargeTypeGuaCai = 2;
            $scope.guacaiisLimit = 1;
            $scope.guaCaiBaoyueProductList = $scope.filterProductInfoList($scope.servType, 8, 2, 1, 1,
                $scope.isExperience, 2, 1);

            $scope.gcproductID = $scope.guaCaiBaoyueProductList.length > 0 ? $scope.guaCaiBaoyueProductList[0].objectID : "";

            if ($scope.guaCaiBaoyueProductList.length <= 0) {
                $scope.gcByDesc = "暂无此款产品";
                $scope.isGCByDesc = false;
            } else {
                $scope.gcunitPrice = $scope.guaCaiBaoyueProductList[0].unitPrice;
                $scope.gcmemberCount = $scope.guaCaiBaoyueProductList[0].memberCount;
                $scope.gcByDesc = "";
                $scope.isGCByDesc = true;
            }
        }
    };

    $scope.accMul = function (arg1, arg2) {
        if (!arg1) {
            arg1 = 0;
        }

        if (!arg2) {
            arg2 = 0;
        }

        var m = 0;
        var s1 = arg1.toString();
        var s2 = arg2.toString();
        try {
            m += s1.split(".")[1].length
        } catch (e) {
        }
        try {
            m += s2.split(".")[1].length
        } catch (e) {
        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
    };

    $scope.getPorductUnitPrice = function () {

        var productName = $('#productName').val();

        // $('#productName option[value="'+productName+'"]').attr('selected','selected');

        productName = productName.replace('number:', '');

        $scope.productID = productName;

        for (var i = 0, len = $scope.baoyueProductList.length; i < len; i++) {
            var product = $scope.baoyueProductList[i];
            if (product.objectID == $scope.productID) {
                $scope.unitPrice = product.unitPrice;
                $scope.memberCount = product.memberCount;
            }
        }
    };

    //挂短
    $scope.getPorductPrice = function () {
        var gdProductNam = $('#gdProductName').val();
        gdProductNam = gdProductNam.replace('number:', '');

        $scope.gdproductID = gdProductNam;
        for (var i = 0, len = $scope.guaDuanBaoyueProductList.length; i < len; i++) {
            var product = $scope.guaDuanBaoyueProductList[i];
            if (product.objectID == $scope.gdproductID) {
                $scope.gdunitPrice = product.unitPrice;
                $scope.gdmemberCount = product.memberCount;
            }
        }
    };

    //挂彩
    $scope.getPorductGCPrice = function () {
        var gcProductName = $('#gcProductName').val();
        gcProductName = gcProductName.replace('number:', '');

        $scope.gcproductID = gcProductName;
        for (var i = 0, len = $scope.guaCaiBaoyueProductList.length; i < len; i++) {
            var product = $scope.guaCaiBaoyueProductList[i];
            if (product.objectID == $scope.gcproductID) {
                $scope.gcunitPrice = product.unitPrice;
                $scope.gcmemberCount = product.memberCount;
            }
        }
    };

    $scope.accSum = function (count) {
        return $scope.accMul($scope.accMul(count, $scope.unitPrice), $scope.memberCount);
    };

    $scope.accGDSum = function (count) {
        return $scope.accMul($scope.accMul(count, $scope.gdunitPrice), $scope.gdmemberCount);
    };

    $scope.accGCSum = function (count) {
        return $scope.accMul($scope.accMul(count, $scope.gcunitPrice), $scope.gcmemberCount);
    };

    $('.input-daterange').datepicker({
        format: "yyyy-mm-dd",
        weekStart: 0,
        language: "zh-CN",
        autoclose: true
    });

    $('#start').on('changeDate', function () {
        var startTime = document.getElementById("start").value;

        $rootScope.$apply(function () {
            $scope.effictiveTime = startTime.substring(0, 4) + startTime.substring(5, 7) + startTime.substring(8, 10) + '000000';
        })
    });

    $('#end').on('changeDate', function () {
        var endTime = document.getElementById("end").value;

        $rootScope.$apply(function () {
            $scope.expireTime = endTime.substring(0, 4) + endTime.substring(5, 7) + endTime.substring(8, 10) + '235959';
        })
    });

    //后续post的函数
    $scope.createOrder = function () {
        $('#formSub').attr('disabled', 'true');
        var req = {
            "order": {
                "orderType": 1,
                "transferAttachURL": $scope.transferAttachURL.upload,
                "orderDetailURL": $scope.orderDetailURL.upload,
                "orderName": $scope.orderName,
                "amount": $scope.amount,
                "enterpriseID": $scope.enterpriseID,
                "enterpriseName": $scope.enterpriseName,
                "servType": $scope.servType,
                "payStatus": 3,
                "effictiveTime": $scope.effictiveTime,
                "isExperience": $scope.isExperience,
                "expireTime": $scope.expireTime,
                "operatorID": $scope.operatorID,
                "orderItemList": []
            }
        };

        //防止企业通知类型时创建不必要子项
        if ($scope.servType != "4") {
            if ($scope.pingxianBuxianProductList) {
                //移动屏显配额：不限
                if ($scope.cmcc && $scope.postPingXianCMCC && $scope.pingxianisLimit == 0 && $scope.pingxianBuxianProductList.length > 0) {
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0,
                        "productID": $scope.pingxianBuxianProductList[0].objectID
                    });
                }
            }

            if ($scope.anciProductList) {
                //移动屏显配额：按次
                if ($scope.cmcc && $scope.postPingXianCMCC && $scope.pingxianisLimit == 1 && $scope.chargeType == 1 && $scope.anciProductList.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.PXPrice,
                            "quantity": $scope.anci,
                            "totalAmount": $scope.accMul($scope.PXPrice, $scope.anci),
                            "productID": $scope.anciProductList[0].objectID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.anci,
                            "totalAmount": 0,
                            "productID": $scope.anciProductList[0].objectID
                        });
                    }
                }
            }
            if ($scope.baoyueProductList) {
                //移动屏显配额：按人包月
                if ($scope.cmcc && $scope.postPingXianCMCC && $scope.pingxianisLimit == 1 && $scope.chargeType == 2 && $scope.baoyueProductList.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.unitPrice,
                            "quantity": $scope.peirenyue,
                            "totalAmount": $scope.accMul($scope.accMul(parseInt($scope.peirenyue), $scope.unitPrice), $scope.memberCount),
                            "productID": $scope.productID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.peirenyue,
                            "totalAmount": 0,
                            "productID": $scope.productID
                        });
                    }
                }
            }


            if ($scope.guaDuanProductList) {
                //移动挂机短信配额：不限
                if ($scope.cmcc && $scope.postGuaDuan && $scope.guaduanisLimit == 0 && $scope.guaDuanProductList.length > 0) {
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0,
                        "productID": $scope.guaDuanProductList[0].objectID
                    });
                }
            }

            if ($scope.anciGuaDuanProductList) {
                //移动挂机短信配额：按次
                if ($scope.cmcc && $scope.postGuaDuan && $scope.chargeTypeGuaDuan == 1 && $scope.guaduanisLimit == 1 && $scope.anciGuaDuanProductList.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.GDPrice,
                            "quantity": $scope.guaduanInput,
                            "totalAmount": $scope.accMul($scope.GDPrice, $scope.guaduanInput),
                            "productID": $scope.anciGuaDuanProductList[0].objectID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.guaduanInput,
                            "totalAmount": 0,
                            "productID": $scope.anciGuaDuanProductList[0].objectID
                        });
                    }
                }
            }

            if ($scope.guaDuanBaoyueProductList) {
                //移动挂机短信配额：按人包月
                if ($scope.cmcc && $scope.postGuaDuan && $scope.chargeTypeGuaDuan == 2 && $scope.guaduanisLimit == 1 && $scope.guaDuanBaoyueProductList.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.gdunitPrice,
                            "quantity": $scope.guaDuanPrice,
                            "totalAmount": $scope.accMul($scope.accMul(parseInt($scope.guaDuanPrice), $scope.gdunitPrice), $scope.gdmemberCount),
                            "productID": $scope.gdproductID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.guaDuanPrice,
                            "totalAmount": 0,
                            "productID": $scope.gdproductID
                        });
                    }
                }

            }

            if ($scope.guaCaiProductList) {
                //移动挂彩短信配额：不限
                if ($scope.cmcc && $scope.postGuaCai && $scope.guacaiisLimit == 0 && $scope.guaCaiProductList.length > 0) {
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0,
                        "productID": $scope.guaCaiProductList[0].objectID
                    });
                }
            }

            if ($scope.anciGuaCaiProductList) {
                //移动挂彩短信配额：按次
                if ($scope.cmcc && $scope.postGuaCai && $scope.chargeTypeGuaCai == 1 && $scope.guacaiisLimit == 1 && $scope.anciGuaCaiProductList.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.GCPrice,
                            "quantity": $scope.guacaiInput,
                            "totalAmount": $scope.accMul($scope.GCPrice, $scope.guacaiInput),
                            "productID": $scope.anciGuaCaiProductList[0].objectID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.guacaiInput,
                            "totalAmount": 0,
                            "productID": $scope.anciGuaCaiProductList[0].objectID
                        });
                    }
                }
            }

            if ($scope.guaCaiBaoyueProductList) {
                //移动挂机彩信配额：按人包月
                if ($scope.cmcc && $scope.postGuaCai && $scope.chargeTypeGuaCai == 2 && $scope.guacaiisLimit == 1 && $scope.guaCaiBaoyueProductList.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.gcunitPrice,
                            "quantity": $scope.guaCaiPrice,
                            "totalAmount": $scope.accMul($scope.accMul(parseInt($scope.guaCaiPrice), $scope.gcunitPrice), $scope.gcmemberCount),
                            "productID": $scope.gcproductID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.guaCaiPrice,
                            "totalAmount": 0,
                            "productID": $scope.gcproductID
                        });
                    }
                }

            }

            //联通屏显配额：不限
            if ($scope.cucc && $scope.postPingXianCUCC && $scope.pingxianisLimit_cucc == 0 && $scope.pingxianBuxianProductCUCC.length > 0) {
                if ($scope.isExperience === 0) {
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0,
                        "productID": $scope.pingxianBuxianProductCUCC[0].objectID
                    });
                }
            }

            //联通屏显配额：按次
            if ($scope.cucc && $scope.postPingXianCUCC && $scope.pingxianisLimit_cucc == 1 && $scope.chargeType_cucc == 1 && $scope.anciProductCUCC.length > 0) {
                if ($scope.isExperience === 0) {
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.PXPrice_cucc,
                        "quantity": $scope.anci_cucc,
                        "totalAmount": $scope.accMul($scope.PXPrice_cucc, $scope.anci_cucc),
                        "productID": $scope.anciProductCUCC[0].objectID
                    });
                }
            }


            // add 20191106 top
            if ($scope.guaDuanProductListCUCC) {
                //联通挂机短信配额：不限
                if ($scope.cucc && $scope.postGuaDuanCUCC && $scope.guaduanisLimitCUCC == 0 && $scope.guaDuanProductListCUCC.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": 1,
                            "totalAmount": 0,
                            "productID": $scope.guaDuanProductListCUCC[0].objectID
                        });
                    }
                }
            }

            if ($scope.anciGuaDuanProductListCUCC) {
                //联通挂机短信配额：按次
                if ($scope.cucc && $scope.postGuaDuanCUCC && $scope.chargeTypeGuaDuanCUCC == 1 && $scope.guaduanisLimitCUCC == 1 && $scope.anciGuaDuanProductListCUCC.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.GDPriceCUCC,
                            "quantity": $scope.guaduanInputCUCC,
                            "totalAmount": $scope.accMul($scope.GDPriceCUCC, $scope.guaduanInputCUCC),
                            "productID": $scope.anciGuaDuanProductListCUCC[0].objectID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.guaduanInputCUCC,
                            "totalAmount": 0,
                            "productID": $scope.anciGuaDuanProductListCUCC[0].objectID
                        });
                    }
                }
            }
            //add 20191106 end

            //电信屏显配额：不限
            if ($scope.ctcc && $scope.postPingXianCTCC && $scope.pingxianisLimit_ctcc == 0 && $scope.pingxianBuxianProductCTCC.length > 0) {
                if ($scope.isExperience === 0) {
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": 0,
                        "quantity": 1,
                        "totalAmount": 0,
                        "productID": $scope.pingxianBuxianProductCTCC[0].objectID
                    });
                }
            }

            //电信屏显配额：按次
            if ($scope.ctcc && $scope.postPingXianCTCC && $scope.pingxianisLimit_ctcc == 1 && $scope.chargeType_ctcc == 1 && $scope.anciProductCTCC.length > 0) {
                if ($scope.isExperience === 0) {
                    req.order.orderItemList.push({
                        "operatorID": $scope.operatorID,
                        "unitPrice": $scope.PXPrice_ctcc,
                        "quantity": $scope.anci_ctcc,
                        "totalAmount": $scope.accMul($scope.PXPrice_ctcc, $scope.anci_ctcc),
                        "productID": $scope.anciProductCTCC[0].objectID
                    });
                }
            }


            // add 20191106 top
            if ($scope.guaDuanProductListCTCC) {
                //联通挂机短信配额：不限
                if ($scope.ctcc && $scope.postGuaDuanCTCC && $scope.guaduanisLimitCTCC == 0 && $scope.guaDuanProductListCTCC.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": 1,
                            "totalAmount": 0,
                            "productID": $scope.guaDuanProductListCTCC[0].objectID
                        });
                    }
                }
            }

            if ($scope.anciGuaDuanProductListCTCC) {
                //联通挂机短信配额：按次
                if ($scope.ctcc && $scope.postGuaDuanCTCC && $scope.chargeTypeGuaDuanCTCC == 1 && $scope.guaduanisLimitCTCC == 1 && $scope.anciGuaDuanProductListCTCC.length > 0) {
                    if ($scope.isExperience === 0) {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": $scope.GDPriceCTCC,
                            "quantity": $scope.guaduanInputCTCC,
                            "totalAmount": $scope.accMul($scope.GDPriceCTCC, $scope.guaduanInputCTCC),
                            "productID": $scope.anciGuaDuanProductListCTCC[0].objectID
                        });
                    } else {
                        req.order.orderItemList.push({
                            "operatorID": $scope.operatorID,
                            "unitPrice": 0,
                            "quantity": $scope.guaduanInputCTCC,
                            "totalAmount": 0,
                            "productID": $scope.anciGuaDuanProductListCTCC[0].objectID
                        });
                    }
                }
            }
            if ($scope.cmcc && $scope.postZengCaiCMCC && $scope.gjzengcaiProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.gjzcPrice||0) : 0,
                    "quantity": $scope.gjzcanci?$scope.gjzcanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.gjzcPrice, $scope.gjzcanci) : 0,
                    "productID": $scope.gjzengcaiProductList[0].objectID
                });
            }
        }
        //add 20191106 end
        //增彩
        if ($scope.cmcc && $scope.postZengCaiCMCC && $scope.servType == "4" && $scope.zengcaiProductList.length > 0) {
            //非体验按次
            if ($scope.isExperience === 0 && $scope.zcchargeTypeEx == 1 && $scope.zcanci != '') {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.ZCPrice,
                    "quantity": $scope.zcanci,
                    "totalAmount": $scope.accMul($scope.ZCPrice, $scope.zcanci),
                    "productID": $scope.zengcaiProductList[0].objectID
                });
            }
            //非体验  不限
            else if ($scope.isExperience === 0 && $scope.zcchargeTypeEx == 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": 0,
                    "quantity": 1,
                    "totalAmount": 0,
                    "productID": $scope.zengcaiProductList[0].objectID
                });
            }
            //体验  不限
            else if ($scope.isExperience === 1 && $scope.zcchargeTypeEx == 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": 0,
                    "quantity": 1,
                    "totalAmount": 0,
                    "productID": $scope.zengcaiProductList[0].objectID
                });
            }
            //体验  按次
            else if ($scope.isExperience === 1 && $scope.zcchargeTypeEx == 1 && $scope.zcanci != '') {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": 0,
                    "quantity": $scope.zcanci,
                    "totalAmount": 0,
                    "productID": $scope.zengcaiProductList[0].objectID
                });
            }
        }

        //群发短信
        if ($scope.servType == "4") {
            if ($scope.cmcc && $scope.postGuaDuan && $scope.groupSMScmccProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.groupSmscmccPrice||0) : 0,
                    "quantity": $scope.groupSendSMSCMCCanci?$scope.groupSendSMSCMCCanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.groupSmscmccPrice, $scope.groupSendSMSCMCCanci) : 0,
                    "productID": $scope.groupSMScmccProductList[0].objectID
                });
            }
            if ($scope.cucc && $scope.postGuaDuanCUCC && $scope.groupSMScuccProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.groupSmscuccPrice||0) : 0,
                    "quantity": $scope.groupSendSMSCUCCanci?$scope.groupSendSMSCUCCanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.groupSmscuccPrice, $scope.groupSendSMSCUCCanci) : 0,
                    "productID": $scope.groupSMScuccProductList[0].objectID
                });
            }
            if ($scope.ctcc && $scope.postGuaDuanCTCC && $scope.groupSMSctccProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.groupSmsctccPrice||0) : 0,
                    "quantity": $scope.groupSendSMSCTCCanci?$scope.groupSendSMSCTCCanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.groupSmsctccPrice, $scope.groupSendSMSCTCCanci) : 0,
                    "productID": $scope.groupSMSctccProductList[0].objectID
                });
            }
            if ($scope.cmcc  && $scope.postPingXianCMCC && $scope.groupScreencmccProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.groupScreencmccPrice||0) : 0,
                    "quantity": $scope.groupSendScreenCMCCanci?$scope.groupSendScreenCMCCanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.groupScreencmccPrice, $scope.groupSendScreenCMCCanci) : 0,
                    "productID": $scope.groupScreencmccProductList[0].objectID
                });
            }
            if ($scope.cucc  && $scope.postPingXianCUCC && $scope.groupScreencuccProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.groupScreencuccPrice||0) : 0,
                    "quantity": $scope.groupSendScreenCUCCanci?$scope.groupSendScreenCUCCanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.groupScreencuccPrice, $scope.groupSendScreenCUCCanci) : 0,
                    "productID": $scope.groupScreencuccProductList[0].objectID
                });
            }
            if ($scope.ctcc && $scope.postPingXianCTCC && $scope.groupScreenctccProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.groupScreenctccPrice||0) : 0,
                    "quantity": $scope.groupSendScreenCTCCanci?$scope.groupSendScreenCTCCanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.groupScreenctccPrice, $scope.groupSendScreenCTCCanci) : 0,
                    "productID": $scope.groupScreenctccProductList[0].objectID
                });
            }
            if ($scope.cmcc && $scope.postGuaCai && $scope.caixinProductList.length > 0) {
                req.order.orderItemList.push({
                    "operatorID": $scope.operatorID,
                    "unitPrice": $scope.isExperience === 0 ? ($scope.cxPrice||0) : 0,
                    "quantity": $scope.cxanci?$scope.cxanci:1,
                    "totalAmount": $scope.isExperience === 0 ? $scope.accMul($scope.gjzcPrice, $scope.cxanci) : 0,
                    "productID": $scope.caixinProductList[0].objectID
                });
            }
        }


        // 若没有选取当前可用的产品，则弹框提示
        if (req.order.orderItemList.length <= 0) {
            $scope.tip = "ORDERITEMISNULL";
            $('#myModal').modal();
        }
        else {
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/orderManageService/createOrder",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '1030100000') {
                            $scope.tip = "CREATE_ORDER_SUCCESS_MSG";
                            $('#myModal').modal();

                            setTimeout(function () {
                                location.href = '../orderList/orderList.html';
                            }, 1000);
                        }

                        else {
                            $scope.tip = data.resultCode;
                            $('#myModal').modal();
                        }
                    })
                },
                error: function () {
                    $rootScope.$apply(function () {
                            $('#formSub').removeAttr('disabled');
                            $scope.tip = "1030120500";
                            $('#myModal').modal();
                        }
                    )
                }
            });
        }
    };

    //返回按钮的处理
    $scope.goBack = function () {
        //跳转到上一层
        location.href = '../orderList/orderList.html';
    };

    /*
     * servType: 业务类型
     * subServType: 子业务类型
     * productType: 产品类型
     * isUse: 是否使用
     * isLimit: 是否限量
     * isExperience: 是否体验版
     * chargeType: 计费类型
     * telco: 运营商类型
     * */
    $scope.filterProductInfoList = function (servType, subServType, productType, isUse, isLimit, isExperience, chargeType, telco) {
        var temp = false;
        return $scope.allProductList.filter(function (a) {
            temp = a.servType == servType &&
                a.productType == productType &&
                a.isUse == isUse &&
                a.isLimit == isLimit &&
                a.isExperience == isExperience &&
                a.chargeType == chargeType
                && a.reservedsEcpmp.reserved1 == telco;
            if (subServType !== "") {
                temp = a.subServType == subServType && temp
            }
            return temp;
        });
    };

    $scope.queryOrderList = function () {
        var req = {
            "subscribeInfo": {
                "enterpriseID": $scope.enterpriseID,
                "servType": $scope.servType,
                "isExperience": 1
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": 1
            }
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/querySubscribeList",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    if (data.result.resultCode == '1030100000') {
                        $scope.orderListData = data.subscribeInfoList || [];
                        /*  if ($scope.servType == '3') {
                              $scope.cmcc = true;
                              $scope.cucc = false;
                              $scope.ctcc = false;
                          } else {
                              $scope.cmcc = true;
                              $scope.cucc = true;
                              $scope.ctcc = true;
                          }*/
                        $scope.cmcc = true;
                        $scope.cucc = true;
                        $scope.ctcc = true;
                        if ($scope.orderListData.length === 0) {
                            $scope.showOrderName()
                        } else {
                            $scope.showOrderName(0)
                        }

                    } else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })

            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = data.resultCode;
                    $('#myModal').modal();
                })
            }
        });

    }
    $scope.queryProductInfoList = function (condition) {
        var req = {
            "productQuery": {
                "isUse": 1
            },
            "pageParameter": {
                "pageNum": 1,
                "pageSize": parseInt($scope.pageInfo[0].pageSize),
                "isReturnTotal": "1"
            }
        };
        $scope.condition = condition;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/orderManageService/queryProductList",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '1030100000') {
                        $scope.allProductList = result.productList;
                        $scope.queryOrderList();
                    }
                    else {
                        $scope.tip = data.resultCode;
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = "1030120500";
                        $('#myModal').modal();
                    }
                )
            }
        });
    }

    //切换业务列别和是否体验版时将配额数据清空
    $scope.toInit = function () {
        $scope.chargeTypeGuaDuan = 0;
        $scope.chargeTypeGuaDuanCUCC = 0;       //联通挂短计费方式
        $scope.chargeTypeGuaDuanCTCC = 0;       //电信挂短计费方式
        $scope.chargeTypeGuaCai = 0;
        $scope.chargeType = 0;
        $scope.chargeType_cucc = 0;
        $scope.chargeType_ctcc = 0;
        
        $scope.gjzcchargeTypeEx = 0;	//挂机增彩
        
        $scope.cxchargeTypeEx = 0;     //群发彩信
        $scope.zcchargeTypeEx = 0;		//群发增彩
        
        $scope.screenchargeTypeEx = 0;
        $scope.screenchargeTypeExCUCC = 0;
        $scope.screenchargeTypeExCTCC = 0;
        
        $scope.smschargeTypeEx = 0;
        $scope.smschargeTypeExCUCC = 0;
        $scope.smschargeTypeExCTCC = 0;

        $scope.pingxianisLimit = 0;
        $scope.guaduanisLimit = 0;
        $scope.guaduanisLimitCUCC = 0;     //联通挂短是否限量
        $scope.guaduanisLimitCTCC = 0;     //电信挂短是否限量
        $scope.guacaiisLimit = 0;

        //默认屏显配额是配置的
        $scope.postPingXianCMCC = true;
        //$scope.postPingXianCUCC = true;
        //$scope.postPingXianCTCC = true;

        //默认挂机短信是配置的
        $scope.postGuaDuan = true;
        $scope.postGuaDuanCUCC = true;
        $scope.postGuaDuanCTCC = true;
        //默认挂机彩信是配置的
        $scope.postGuaCai = true;

        $scope.anci = "";
        $scope.desc = "";
        $scope.acDesc = "";
        $scope.byDesc = "";
        $scope.isAcDesc = false;
        $scope.isByDesc = false;

        $scope.gdesc = "";
        $scope.gdescCUCC = "";
        $scope.gdescCTCC = "";
        $scope.gdDesc = "";
        $scope.gdDescCUCC = "";
        $scope.gdDescCTCC = "";
        $scope.gdByDesc = "";
        $scope.isGdDesc = false;
        $scope.isGdDescCUCC = false;
        $scope.isGdDescCTCC = false;
        $scope.isGDByDesc = false;

        $scope.gcesc = "";
        $scope.gcDesc = "";
        $scope.gcByDesc = "";
        $scope.isGcDesc = false;
        $scope.isGCByDesc = false;

        $scope.zengcaiProductList = [];
        $scope.ZCPrice = '';
        $scope.zcanciExperience = '';
        
        $scope.gjzengcaiProductList = [];
        $scope.gjzcPrice = '';
    }
});