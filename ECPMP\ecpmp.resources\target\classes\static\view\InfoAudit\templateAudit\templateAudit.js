var app = angular.module("myApp", ["util.ajax", 'page', "angularI18n"])
app.controller('templateAuditCtrl', ['$scope', '$rootScope', '$location', 'RestClientUtil', function ($scope, $rootScope, $location, RestClientUtil) {
  $scope.init = function () {
    //初始化分页信息
    $scope.pageInfo = [
      {
        "totalPage": 2,
        "totalCount": 20,
        "pageSize": '10',
        "currentPage": 1
      }
    ];
    $scope.enterpriseID = "";
    $scope.enterpriseName = "";
    $scope.contentID = "";
    $scope.transContentID = "";
    $scope.templateContent = "";
    $scope.isSuperManager = false;

    $scope.getApproveStatus = function (approveStatus) {
      if (approveStatus == 1) {
        return "审核失败";
      }
      else if (approveStatus == 2) {
        return "待审核";
      }
      else if (approveStatus == 3) {
        return "审核通过";
      }
      else if (approveStatus == 4) {
        return "审核驳回";
      }
    }

    var loginRoleType=$.cookie('loginRoleType');
    $scope.isSuperManager = (loginRoleType=='superrManager'||loginRoleType=='normalMangager');
    if ($scope.isSuperManager) {
      $scope.enterpriseID = "";
    } else {
      $scope.enterpriseID = $.cookie('enterpriseID');
    }
    $scope.getContentInfoList();
  };
  $scope.getContentInfoList = function (condition) {
    var contentIDList = null;

    if ($scope.contentID !== "") {
      var rs = /^[0-9]*$/.test($scope.contentID);
      if (!rs) {
        contentIDList = [-1];
      } else {
        contentIDList = [$scope.contentID];
      }
    }
    var req;
    if (condition != 'justPage') {
      req = {
        "enterpriseID": $scope.enterpriseID,
        "enterpriseName": $scope.enterpriseName,
        "contentIDList": contentIDList,
        "contentName": $scope.templateContent,
        "contentTypeList":[2],
        "pageParameter": {
          "pageNum": 1,
          "pageSize": parseInt($scope.pageInfo[0].pageSize),
          "isReturnTotal": "1"
        }
      };
      $scope.pageInfo[0].currentPage = 1;
      $scope.reqTemp = angular.copy(req);
    } else {
      //如果是只分页的话，请求里的搜索条件还是上次点击搜索按钮所保存的值
      req = $scope.reqTemp;
      req.pageParameter.pageNum = parseInt($scope.pageInfo[0].currentPage);
      req.pageParameter.pageSize = parseInt($scope.pageInfo[0].pageSize);
    }
    RestClientUtil.ajaxRequest({
      type: 'POST',
      url: "/ecpmp/ecpmpServices/contentService/queryContentInfoList",
      data: JSON.stringify(req),
      success: function (result) {
        $rootScope.$apply(function () {
          var data = result.result;
          if (data.resultCode == '1030100000') {
            $scope.contentInfoList = result.contentInfoList;
            $scope.pageInfo[0].totalCount = parseInt(result.totalAmount) || 0;
            $scope.pageInfo[0].totalPage = $scope.pageInfo[0].totalCount!==0 ? Math.ceil($scope.pageInfo[0].totalCount / parseInt($scope.pageInfo[0].pageSize)):1;
          } else {
            $scope.contentInfoList = [];
            $scope.pageInfo[0].currentPage = 1;
            $scope.pageInfo[0].totalCount = 0;
            $scope.pageInfo[0].totalPage = 1;
            $scope.tip = data.resultCode;
            $('#myModal').modal();
          }
        })
      },
      error: function () {
        $rootScope.$apply(function () {
              $scope.tip = "**********";
              $('#myModal').modal();
            }
        )
      }
    });
  }
}]);
app.config(['$locationProvider', function ($locationProvider) {
  $locationProvider.html5Mode({
    enabled: true,
    requireBase: false
  });
}]);

app.filter("formatDate", function () {
  return function (date) {
    if (date) {
      return date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8) + " " + date.substring(8, 10) + ":" + date.substring(10, 12);
    }
    return "";
  }
})