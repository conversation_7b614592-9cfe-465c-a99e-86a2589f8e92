<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.WltIntelligentCallRecordMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltIntelligentCallRecordWrapper" >
        <id column="requestId" property="requestId" jdbcType="VARCHAR" />
        <result column="enterpriseID" property="enterpriseID" jdbcType="INTEGER" />
        <result column="trackingNum" property="trackingNum" jdbcType="VARCHAR" />
        <result column="caller" property="caller" jdbcType="VARCHAR" />
        <result column="callee" property="callee" jdbcType="VARCHAR" />
        <result column="serviceType" property="serviceType" jdbcType="VARCHAR" />
        <result column="sceneID" property="sceneID" jdbcType="VARCHAR" />
        <result column="text" property="text" jdbcType="VARCHAR" />
        <result column="language" property="language" jdbcType="VARCHAR" />
        <result column="gender" property="gender" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="address" property="address" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="INTEGER" />
        <result column="planTime" property="planTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        requestId, enterpriseID, trackingNum, caller, callee, serviceType, sceneID, text, 
        language, gender, createTime, address, status, planTime
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select 
        <include refid="Base_Column_List" />
        from ecpm_t_wlt_intelligent_call_record
        where requestId = #{requestId,jdbcType=VARCHAR}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
        delete from ecpm_t_wlt_intelligent_call_record
        where requestId = #{requestId,jdbcType=VARCHAR}
    </delete>

    <insert id="insert" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltIntelligentCallRecordWrapper" >
        insert into ecpm_t_wlt_intelligent_call_record (requestId, enterpriseID, trackingNum,
        	caller, callee, serviceType, sceneID, text, language,
        	gender, createTime, address, status, planTime
            )
        values (#{requestId,jdbcType=VARCHAR}, #{enterpriseID,jdbcType=INTEGER}, #{trackingNum,jdbcType=VARCHAR},
        	#{caller,jdbcType=VARCHAR}, #{callee,jdbcType=VARCHAR},  #{serviceType,jdbcType=VARCHAR}, 
        	#{sceneID,jdbcType=VARCHAR}, #{text,jdbcType=VARCHAR}, #{language,jdbcType=VARCHAR}, 
        	#{gender,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{address,jdbcType=VARCHAR}, 
        	#{status,jdbcType=INTEGER}, #{planTime,jdbcType=TIMESTAMP}
            )
    </insert>

    <insert id="insertSelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltIntelligentCallRecordWrapper" >
        insert into ecpm_t_wlt_intelligent_call_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="requestId != null" >
                requestId,
            </if>
            <if test="enterpriseID != null" >
                enterpriseID,
            </if>
            <if test="trackingNum != null" >
                trackingNum,
            </if>
            <if test="caller != null" >
                caller,
            </if>
            <if test="callee != null" >
                callee,
            </if>
            <if test="serviceType != null" >
                serviceType,
            </if>
            <if test="sceneID != null" >
                sceneID,
            </if>
            <if test="text != null" >
                text,
            </if>
            <if test="language != null" >
                language,
            </if>
            <if test="gender != null" >
                gender,
            </if>
            <if test="createTime != null" >
                createTime,
            </if>
            <if test="address != null" >
                address,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="planTime != null" >
                planTime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="requestId != null" >
                #{requestId,jdbcType=VARCHAR},
            </if>
            <if test="enterpriseID != null" >
                #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="trackingNum != null" >
                #{trackingNum,jdbcType=VARCHAR},
            </if>
            <if test="caller != null" >
                #{caller,jdbcType=VARCHAR},
            </if>
            <if test="callee != null" >
                #{callee,jdbcType=VARCHAR},
            </if>
            <if test="serviceType != null" >
                #{serviceType,jdbcType=VARCHAR},
            </if>
            <if test="sceneID != null" >
                #{sceneID,jdbcType=VARCHAR},
            </if>
            <if test="text != null" >
                #{text,jdbcType=VARCHAR},
            </if>
            <if test="language != null" >
                #{language,jdbcType=VARCHAR},
            </if>
            <if test="gender != null" >
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null" >
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                #{status,jdbcType=INTEGER},
            </if>
            <if test="planTime != null" >
                #{planTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltIntelligentCallRecordWrapper" >
        update ecpm_t_wlt_intelligent_call_record
        <set >
        	<if test="enterpriseID != null" >
                enterpriseID = #{enterpriseID,jdbcType=INTEGER},
            </if>
            <if test="trackingNum != null" >
                trackingNum = #{trackingNum,jdbcType=VARCHAR},
            </if>
            <if test="caller != null" >
                caller = #{caller,jdbcType=VARCHAR},
            </if>
            <if test="callee != null" >
                callee = #{callee,jdbcType=VARCHAR},
            </if>
            <if test="serviceType != null" >
                serviceType = #{serviceType,jdbcType=VARCHAR},
            </if>
            <if test="sceneID != null" >
                sceneID = #{sceneID,jdbcType=VARCHAR},
            </if>
            <if test="text != null" >
                text = #{text,jdbcType=VARCHAR},
            </if>
            <if test="language != null" >
                language = #{language,jdbcType=VARCHAR},
            </if>
            <if test="gender != null" >
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                createTime = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="address != null" >
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="status != null" >
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="planTime != null" >
                planTime = #{planTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where requestId = #{requestId,jdbcType=VARCHAR}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.WltIntelligentCallRecordWrapper" >
        update ecpm_t_wlt_intelligent_call_record
        set enterpriseID = #{enterpriseID,jdbcType=INTEGER},
        	trackingNum = #{trackingNum,jdbcType=VARCHAR},
        	caller = #{caller,jdbcType=VARCHAR},
            callee = #{callee,jdbcType=VARCHAR},
            serviceType = #{serviceType,jdbcType=VARCHAR},
            sceneID = #{sceneID,jdbcType=VARCHAR},
            text = #{text,jdbcType=VARCHAR},
            language = #{language,jdbcType=VARCHAR},
            gender = #{gender,jdbcType=VARCHAR},
            createTime = #{createTime,jdbcType=TIMESTAMP},
            address = #{address,jdbcType=VARCHAR},
            status = #{status,jdbcType=INTEGER}
            planTime = #{planTime,jdbcType=TIMESTAMP},
        where requestId = #{requestId,jdbcType=VARCHAR}
    </update>
    
    <select id="queryCallRecordList" resultMap="BaseResultMap">
    	select
    	<include refid="Base_Column_List" />
    	from ecpm_t_wlt_intelligent_call_record
    	where 1=1
   		<if test="phoneNum != null and phoneNum != ''">
   			and (caller like concat("%", #{phoneNum}, "%") or callee like concat("%", #{phoneNum}, "%"))
   		</if>
   		<if test="enterpriseID != null">
   			and enterpriseID = #{enterpriseID}
   		</if>
   		<if test="trackingNum != null and trackingNum != ''">
   			and trackingNum like concat("%", #{trackingNum}, "%")
   		</if>
   		<if test="planTimeStart != null and planTimeStart != ''">
   			and planTime <![CDATA[ >= ]]> #{planTimeStart}
   		</if>
   		<if test="planTimeEnd != null and planTimeEnd != ''">
   			and planTime <![CDATA[ <= ]]> #{planTimeEnd}
   		</if>
   		ORDER BY createTime DESC
   		<if test="pageNum != null and pageSize != null">
            limit #{pageNum},#{pageSize}
        </if>
    </select>
    
    <select id="queryCallRecordListCount" resultType="java.lang.Integer">
    	select count(1) from (select
    	<include refid="Base_Column_List" />
    	from ecpm_t_wlt_intelligent_call_record
    	where 1=1
   		<if test="phoneNum != null and phoneNum != ''">
   			and (caller like concat("%", #{phoneNum}, "%") or callee like concat("%", #{phoneNum}, "%"))
   		</if>
   		<if test="enterpriseID != null">
   			and enterpriseID = #{enterpriseID}
   		</if>
   		<if test="trackingNum != null and trackingNum != ''">
   			and trackingNum like concat("%", #{trackingNum}, "%")
   		</if>
   		<if test="planTimeStart != null and planTimeStart != ''">
   			and planTime <![CDATA[ >= ]]> #{planTimeStart}
   		</if>
   		<if test="planTimeEnd != null and planTimeEnd != ''">
   			and planTime <![CDATA[ <= ]]> #{planTimeEnd}
   		</if>
   		)a
    </select>
    
    <select id="queryWaitDealIntelligentCall" resultMap="BaseResultMap">
    	select
    	<include refid="Base_Column_List" />
    	from ecpm_t_wlt_intelligent_call_record
    	where planTime <![CDATA[ < ]]> NOW() AND status=0
    </select>
</mapper>