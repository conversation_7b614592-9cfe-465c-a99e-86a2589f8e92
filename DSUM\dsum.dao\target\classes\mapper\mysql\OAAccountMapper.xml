<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsum.dao.mapper.OAAccountMapper">
	<resultMap id="accountMap"
		type="com.huawei.jaguar.dsum.dao.domain.OAAccountWrapper">
		<result property="oaAccount" column="oaAccount" />
		<result property="realName" column="realName" />
		<result property="deptID" column="deptID" />
		<result property="contactPhone" column="contactPhone" />
		<result property="email" column="email" />
		<result property="status" column="status"/>
		<result property="deptName" column="deptName" />
		<result property="isMiguStaff" column="isMiguStaff"/>
		<result property="accountName" column="accountName" />
	</resultMap>


	<select id="queryOaAccountList" resultMap="accountMap">

		SELECT
		oa.*,ac.accountName,ac.isMiguStaff
		FROM
			(SELECT oa.*,od.deptName FROM dsum_t_oa_account oa
			LEFT JOIN dsum_t_oa_dept od ON oa.deptID = od.deptID
			<trim prefix="where" prefixOverrides="and|or">
				<if test="deptId != null and deptId != ''">
					and  od.deptId = #{deptId}
				</if>
				<if test="realName != null and realName != ''">
					and  oa.realName like CONCAT("%",#{realName},"%")
				</if>
			</trim>
			limit #{pageNo},#{pageSize}
			) oa
		LEFT JOIN dsum_t_account ac on oa.oaAccount = ac.oaAccount
	</select>

	<select id="queryOaAccountListTotal" resultType="java.lang.Integer">
		SELECT
		 count(*)
		FROM
		dsum_t_oa_account oa
		LEFT JOIN dsum_t_oa_dept od ON oa.deptID = od.deptID
		LEFT JOIN dsum_t_account ac on oa.oaAccount = ac.oaAccount
		<trim prefix="where" prefixOverrides="and|or">
			<if test="deptId != null and deptId != ''">
				and  od.deptId = #{deptId}
			</if>
			<if test="realName != null and realName != ''">
				and  oa.realName = #{realName}
			</if>
		</trim>
	</select>

	<select id="queryOaDeptList" resultType="com.huawei.jaguar.dsum.dao.domain.OADeptWrapper">
		SELECT
			*
		FROM
			dsum_t_oa_dept
	</select>
	<select id="deleteALLOAAccount">
		DELETE FROM dsum_t_oa_account where status = 99
	</select>

	<update id="updateOAaccountStatus">
		update dsum_t_oa_account set status = 99
	</update>
	<select id="deleteALLOADept">
		DELETE FROM dsum_t_oa_dept
	</select>

	<select id="updateAccountWhenOAAccountNull">
	update dsum_t_account set isMiguStaff = 0,oaAccount =null
	WHERE
		oaAccount NOT IN ( SELECT oaAccount FROM dsum_t_oa_account )
		AND  isMiguStaff = 1
	</select>

	<select id="deleteAccountRoleWhenAccountNull">
	Delete from dsum_t_account_role where accountID not in (select accountID from dsum_t_account)
	</select>
	<insert id="insertOAAccount">
		insert into dsum_t_oa_account
		(
		oaAccount,
		realName,
		deptID,
		contactPhone,
		email,
		status
		)
		values
		<foreach collection="list" item="OAAccountWrapper"
				 separator=",">
			(
			#{OAAccountWrapper.oaAccount},
			#{OAAccountWrapper.realName},
			#{OAAccountWrapper.deptID},
			#{OAAccountWrapper.contactPhone},
			#{OAAccountWrapper.email},
			#{OAAccountWrapper.status}
			)
		</foreach>
	</insert>

	<insert id="insertOADept">
		insert ignore into dsum_t_oa_dept
		(
		deptID,
		deptName
		)
		values
		<foreach collection="list" item="OADeptWrapper"
				 separator=",">
			(
			#{OADeptWrapper.deptID},
			#{OADeptWrapper.deptName}
			)
		</foreach>
	</insert>

	<update id="updateAccountStatus">
		UPDATE dsum_t_account a,
			dsum_t_oa_account oa
		SET a.STATUS = IF( oa.STATUS = 10, 1, 2 )
		WHERE
			oa.oaAccount = a.oaAccount
		and a.isMiguStaff = 1
		and	a.oaAccount is not null
	</update>
</mapper>