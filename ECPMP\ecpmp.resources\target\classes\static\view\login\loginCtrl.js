var app = angular.module("myApp", ["util.ajax", "angularI18n"])
app.controller('loginController', function ($scope, $rootScope, $location, RestClientUtil) {
    $scope.init = function () {
        $scope.isGroupRemind = false;
        $scope.bodyHide = true;
        if ($scope.getQueryVariable("isGroupRemind")) {
            $scope.isGroupRemind = $scope.getQueryVariable("isGroupRemind") === "true";
            //数字反诈登录页
            if($scope.isGroupRemind){
                $(".login .login-cont").css("background" ,"url(../../assets/images/login-img2.png)no-repeat center center");
                $(".login .login-cont").css("background-size","100% 100%")
                $(".faviconIco").attr("href","../../assets/images/favicon2.ico");
            }
        }
        if ($scope.getQueryVariable("stsUrl")) {
            window.location.href = $scope.getQueryVariable("stsUrl") + "?service=" + $scope.getQueryVariable("service")
            return;
        }

        //金库跳转时
        if ($scope.getQueryVariable("ticket")) {
        	var ticket = $scope.getQueryVariable("ticket");
        	var req = {
                    "ticket": ticket || '',
                };
        	RestClientUtil.ajaxRequest({
        		async: false,
                type: 'POST',
                url: "/ecpmp/ecpmpServices/JKService/ticketToToken",
                data: JSON.stringify(req),
                success: function (result) {
                    var data = result.result;
                    console.log(result);
                    if (data.resultCode == '**********') {
                    	$.cookie('token', result.token, {path: '/', secure: false});
                        $.cookie('loginRoleType', result.loginRoleType, {path: '/', secure: false});
                        $.cookie('accountID', result.accountID, {path: '/', secure: false});
                        $.cookie('accountName', result.accountName, {path: '/', secure: false});
                    } 
                    $scope.tokenAfter();
                },
                error: function () {
                }
            });
        }
        else
        {
        	if ($scope.getQueryVariable("token")) {
                $.cookie('token', $scope.getQueryVariable("token"), {path: '/', secure: false});
                $.cookie('loginRoleType', $scope.getQueryVariable("loginRoleType"), {path: '/', secure: false});
                $.cookie('accountID', $scope.getQueryVariable("accountID"), {path: '/', secure: false});
                $.cookie('accountName', $scope.getQueryVariable("accountName"), {path: '/', secure: false});
                $.cookie('source', $scope.getQueryVariable("source"), {path: '/', secure: false});
                $.cookie('enterpriseType', $scope.getQueryVariable("enterpriseType"), {path: '/', secure: false});
            }
            $scope.tokenAfter();
        }
        // 判断是否展示登录表单
        $scope.isShowLogin = true;
        if($scope.getQueryVariable("token")) {
            $scope.isShowLogin = false;
        }else{
            $scope.isShowLogin = true;
        }
    };

    $scope.tokenAfter = function (variable) {
    	var req = {};
        if ($.cookie("token") != '' && $.cookie("token") != null) {
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/loginService/tokenAuth",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '**********') {
                            $.cookie('enterpriseID', result.token.enterpriseID, {path: '/', secure: false});
                            $scope.queryPC($.cookie('loginRoleType'), result.token.roleList);
                            $scope.bodyHide = false;
                            if($.cookie('loginRoleType') == 'subEnterprise'){ // 子企业
                               $.cookie('enterpriseType', 3, {path: '/', secure: false});
                            }
                            return;
                        } else {
                            $.removeCookie('accountID', {path: '/'});
                            $.removeCookie('enterpriseID', {path: '/'});
                            $.removeCookie('enterpriseName', {path: '/'});
                            $.removeCookie("token", {path: '/'});
                            $.removeCookie("isGroupRemind", {path: '/'});
                            $scope.bodyHide = false;
                            $scope.isShowLogin = true;
                        }
                    })
                },
                error: function (result) {
                    $scope.bodyHide = false;
                }

            });
        }else {
            $scope.bodyHide = false;
        }
     // $.removeCookie('token',{ path: '/'});
        $scope.userInfo = {
            userAccount: '',
            pwd: '',
            validateCode: ''
        };
        $scope.ValidateCodeInfon = {
            a: Math.random()
        };
        $scope.getValidateCode();

        $scope.getLoginSmsEnable();
       
    };
    
    $scope.getQueryVariable = function (variable) {
        const query = window.location.search.substring(1);
        const vars = query.split("&");
        for (let i = 0; i < vars.length; i++) {
            const pair = vars[i].split("=");
            if (pair[0] == variable) {
                return pair[1];
            }
        }
        return (false);
    };

    $scope.getValidateCode = function () {
        window.localStorage.setItem('new', uuid());
        $scope.verifycodeImg = "/qycy/ecpmp/ecpmpServices/loginService/getVerificationCode?o=" + window.localStorage.getItem('old') + "&n=" + window.localStorage.getItem('new');
        window.localStorage.setItem('old', window.localStorage.getItem('new'));
    };

    function uuid() {
        return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
            (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
        )
    };
    //数组去重
    $scope.uniq = function (array) {
        var temp = [];
        var l = array.length;
        for (var i = 0; i < l; i++) {
            for (var j = i + 1; j < l; j++) {
                if (array[i].id === array[j].id) {
                    i++;
                    j = i;
                }
            }
            temp.push(array[i]);
        }
        return temp;
    };
    //省市联动时调用，用于转化格式
    $scope.mapToList = function (map) {
        var result = [];
        jQuery.each(map, function (_, o) {
            if (_ == '000') {
                return;
            }
            if (o) {
                o.key = o[0].provinceID;
                result.push(o);
            }
        });
        return result;
    };
    $scope.loginSure = function () {
        const password = $scope.userInfo.pwd.trim();
        const encrypt = new JSEncrypt();
        const key = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANW45vowD1WF7WGJO///YmziLY/wROrIsuKy9YVoi2k8F64o7a0yEw9YmvqOBIN4eTBXMBgxNRK7vAcMbVnh74sCAwEAAQ==";
        encrypt.setPublicKey(key);
        var req1 = {
            "account": {
                "accountName": $scope.userInfo.userAccount.trim(),
                "accountType": 3,
            },
            "password": encrypt.encrypt(password),
            "verifyCode": $scope.userInfo.validateCode.trim(),
            "newverifyCode": window.localStorage.getItem('new'),
            "isGroupRemind": $scope.isGroupRemind,
            "smsVerify": $scope.smsVerify
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/loginService/login",
            data: JSON.stringify(req1),
            success: function (result) {
                $rootScope.$apply(function () {
                    var data = result.result;
                    if (data.resultCode == '**********') {
                        //校验密码是否符合规则

                        $scope.loginRe = result;
                        $scope.checkPwdRule(password);
                        $scope.pwd = password;
                        return;
                    } else {
                        $scope.tip = data.resultCode;
                    }
                    $scope.userInfo.validateCode = "";
                    $scope.getValidateCode();
                    $('#myModal').modal();
                })

            },
            error: function (result) {
                $rootScope.$apply(function () {
                    //配置了**********为接口异常情况下提示
                    $scope.tip = "**********";
                    $scope.userInfo.validateCode = "";
                    $scope.getValidateCode();
                    $('#myModal').modal();
                })
            }
        });
    };
    // 修改密码弹窗
    $scope.updatePwd = function () {
        $scope.repwd = "";
        $scope.confirmPwd = "";
        $scope.pwdValidate = true;
        $scope.rePasswordValidate = true;
        $scope.newPasswordValidate = true;
        $scope.originPwdValidate = true;
        $('#accountManage').modal();
    };
    //原密码与新密码校验
    $scope.checkNewPwd = function () {
        var pwd = $scope.pwd;
        var repwd = $scope.repwd;
        $scope.newPasswordValidate = true;
        $scope.rePasswordValidate = true;
        if (pwd == repwd && pwd != "") {
            $scope.tip = "NEWPWDEQUALSORIGINPWD";
            $scope.newPasswordValidate = false;
            $scope.rePasswordValidate = true;
            $('#myModal').modal();
        }
    };
    //修改密码
    $scope.confirmUpdate = function () {

        const encrypt = new JSEncrypt();
        const key = "MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBANW45vowD1WF7WGJO///YmziLY/wROrIsuKy9YVoi2k8F64o7a0yEw9YmvqOBIN4eTBXMBgxNRK7vAcMbVnh74sCAwEAAQ==";
        encrypt.setPublicKey(key);


        var req = {
            "accountID": $scope.loginRe.accountInfo.accountID,
            "oldPwd":encrypt.encrypt($scope.pwd),
            "newPwd":encrypt.encrypt($scope.repwd)
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/accountManageService/changePwd",
            data: JSON.stringify(req),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    console.log(result + "123")
                    if (result.resultCode == '**********') {
                        if ($scope.pwd == $scope.repwd) {
                            $scope.checkNewPwd();
                        } else {
                            $('#accountManage').modal("hide");
                            $scope.tip = "UPDATEPWDSUCCESS";
                            $('#myModal').modal();
                            setTimeout(function () {
                                location.reload();
                            }, 1000);
                        }
                    } else {
                        if (result.resultCode == '**********') {
                            $scope.tip = "ORIGINPWDISWRONG";
                        }
                        $('#myModal').modal();
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                    $scope.tip = '**********';
                    $('#myModal').modal();
                })
            }
        });
    };
    //新密码与确认密码校验
    $scope.checkRePassword = function () {
        var repwd = $scope.repwd;
        var confirmPwd = $scope.confirmPwd;
        $scope.rePasswordValidate = true;
        if (!confirmPwd) {
            $scope.rePasswordValidate = false;
        }
        if (repwd != confirmPwd) {
            $scope.rePasswordValidate = false;
        }
    };

    //检查密码
    $scope.checkNewPwdRule = function () {
        $scope.pwdValidate = true;
        $scope.newPwdRuleValidate = "";
        var rePwd = $scope.repwd;
        var confirmPwd = $scope.confirmPwd;
        var checkPwdRuleReq = {};
        checkPwdRuleReq.password = rePwd;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
            data: JSON.stringify(checkPwdRuleReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode != '**********') {
                        $scope.pwdValidate = false;
                        var passwordRuleList = data.passwordRuleList;
                        if (result.resultCode == '1030120000') {
                            $scope.newPwdRuleValidate = "密码必填";
                        } else {
                            if (!passwordRuleList) {
                                $scope.newPwdRuleValidate = result.resultDesc;
                            } else {
                                for (var i = 0; i < passwordRuleList.length; i++) {
                                    $scope.newPwdRuleValidate = $scope.newPwdRuleValidate + passwordRuleList[i].ruleName;
                                    // $scope.passwordValidateDesc = passwordRuleList[i].ruleName;
                                    // $scope.newPwdRuleValidate  = $scope.newPwdRuleValidate + ";";
                                }
                                $scope.newPwdRuleValidate.substring(0, $scope.newPwdRuleValidate.length - 1);
                            }
                        }
                    } else {
                        if (confirmPwd) {
                            $scope.checkRePassword();
                        }
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };

    //检查密码
    $scope.checkPwdRule = function (pwd) {
        var checkPwdRuleReq = {};
        checkPwdRuleReq.password = pwd;
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/enterpriseManageService/checkPwdRule",
            data: JSON.stringify(checkPwdRuleReq),
            success: function (data) {
                $rootScope.$apply(function () {
                    var result = data.result;
                    if (result.resultCode != '**********') {
                        var passwordRuleList = data.passwordRuleList;
                        if (result.resultCode == '1030120000') {
                            $scope.updatePwd();
                        } else {
                            $scope.updatePwd();
                        }
                        $scope.getValidateCode();
                    } else {
                        $scope.direct($scope.loginRe);
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function () {
                        $scope.tip = '**********';
                        $('#myModal').modal();
                    }
                )
            }
        });
    };
 // 检查密码有效性
    $scope.checkPwdValid = function (menuUrl) {
         var req = {
           accountID: $.cookie("accountID")
         };
         RestClientUtil.ajaxRequest({
                                 type: 'POST',
                                 url: "/ecpmp/ecpmpServices/accountManageService/checkChangeRequired",
                                 data: JSON.stringify(req),
                                 success: function (result) {
                                         var data = result.result;
                                         if (data.resultCode == '**********') {
                                            if(result.changeRequired) {
                                                // 需要修改密码
                                                $('#checkPwdModal').modal();
                                            }else{ // 不需要修改
                                                window.location.href = menuUrl;
                                            }
                                         }
                                 },
                                 error: function (result) {

                                 }
                             });
    };
    $scope.handleCancelPwd = () => {
        debugger
      // 取消直接跳转页面
      window.location.href = $scope.menuUrl;
    };
    $scope.handleConfirmPwd = () => {
     // 确认跳转密码修改页面
     window.location.href = "./changePwd/changePwd.html"
    }
    $scope.direct = function (result) {
        $.removeCookie('source', {path: '/'});

        if (result.accountInfo.managerRightType === 0) {
            $scope.tip = '请通过4a管控平台访问';
            $scope.getValidateCode();
            $('#myModal').modal();
            return;
        }


        var secureFlag = (result.cookie.secure === 'true'),
            roleList = result.accountInfo.roleList,
            loginRoleType = "";
        //判断登录用户的类型
        if (roleList && roleList.length > 0) {
            for (var i in roleList) {
                //超管
                if (roleList[i].roleID === 1000) {
                    $.cookie('roleID', roleList[i].roleID, {path: '/'});
                    loginRoleType = 'superrManager';
                    break;
                } else if (roleList[i].roleID === 1001) {//直客
                    $.cookie('roleID', roleList[i].roleID, {path: '/'});
                    loginRoleType = "zhike";
                    break;
                } else if (roleList[i].roleID === 1002 || roleList[i].roleID === 300085) {//代理商
                    $.cookie('roleID', roleList[i].roleID, {path: '/'});
                    loginRoleType = "agent";
                    break;
                } else if (roleList[i].roleID === 1003) {//分省企业
                    $.cookie('roleID', roleList[i].roleID, {path: '/'});
                    loginRoleType = "provincial";
                    break;
                } else if (roleList[i].roleID === 1005) {// 子企业账号
                     $.cookie('roleID', roleList[i].roleID, {path: '/'});
                     $.cookie('enterpriseType', 3, {path: '/'});
                     loginRoleType = "subEnterprise";
                     break;
                 } else {
                    $.cookie('roleID', roleList[i].roleID, {path: '/'});
                    loginRoleType = 'normalMangager';
                }
            }
        }
        if (result.cookie.domian === '') {
            $.cookie('token', result.cookie.token, {path: result.cookie.path, secure: secureFlag});
            $.cookie('loginRoleType', loginRoleType, {path: result.cookie.path, secure: secureFlag});
            // $.cookie('accountInfo', JSON.stringify(result.accountInfo), { path: result.cookie.path, secure: secureFlag });
            $.cookie('accountID', result.accountInfo.accountID, {
                path: result.cookie.path,
                secure: secureFlag
            });
            $.cookie('accountName', result.accountInfo.accountName, {
                path: result.cookie.path,
                secure: secureFlag
            });
            $.cookie('isGroupRemind', result.accountInfo.isGroupRemind, {
                path: result.cookie.path,
                secure: secureFlag
            });
        } else {
            $.cookie('loginRoleType', loginRoleType, {
                path: result.cookie.path,
                secure: secureFlag,
                domain: result.cookie.domian
            });
            $.cookie('isGroupRemind', result.accountInfo.isGroupRemind, {
                path: result.cookie.path,
                secure: secureFlag,
                domain: result.cookie.domian
            });
            $.cookie('token', result.cookie.token, {
                path: result.cookie.path,
                secure: secureFlag,
                domain: result.cookie.domian
            });
            // $.cookie('accountInfo', JSON.stringify(result.accountInfo), { path: result.cookie.path, secure: secureFlag, domain: result.cookie.domian });
            $.cookie('accountID', result.accountInfo.accountID, {
                path: result.cookie.path,
                secure: secureFlag,
                domain: result.cookie.domian
            });
            $.cookie('accountName', result.accountInfo.accountName, {
                path: result.cookie.path,
                secure: secureFlag,
                domain: result.cookie.domian
            });
        }
        if (result.accountInfo.enterpriseID) {
            $.cookie('enterpriseID', result.accountInfo.enterpriseID, {
                path: result.cookie.path,
                secure: secureFlag
            });
        }

        var provinceList = [],
            cityList = [];
        countyList = [];
        if (loginRoleType === "superrManager") {
            //超管
            /*查询省份*/
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
                data: JSON.stringify({}),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '**********') {
                            $scope.provinceList = data.provinceList;
                            if ($scope.provinceList != null && $scope.provinceList.length > 0) {
                                var provinceIDs = [];
                                angular.forEach($scope.provinceList, function (item) {
                                    provinceIDs.push(item.provinceID);
                                });
                                /*查询地市*/
                                RestClientUtil.ajaxRequest({
                                    type: 'POST',
                                    url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
                                    data: JSON.stringify({"provinceIDs": provinceIDs}),
                                    success: function (data) {
                                        $rootScope.$apply(function () {
                                            var result2 = data.result;
                                            if (result2.resultCode == '**********') {
                                                $scope.cityList = $scope.mapToList(data.cityList);
                                                localStorage.setItem("provinceList", JSON.stringify($scope.provinceList));
                                                localStorage.setItem("cityList", JSON.stringify($scope.cityList));
                                               if (result.directUrl) {
                                                    $scope.menuUrl = result.directUrl + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
                                                    $scope.checkPwdValid($scope.menuUrl);
                                                  } else {
                                                    $scope.menuUrl = '../menu/menu.html' + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
                                                    $scope.checkPwdValid($scope.menuUrl);
                                               }
                                            } else {
                                                $scope.tip = result2.resultCode;
                                                $('#myModal').modal();
                                            }
                                        })
                                    },
                                    error: function (data) {
                                        $rootScope.$apply(function () {
                                            //配置了**********为接口异常情况下提示
                                            $scope.tip = "**********";
                                            $('#myModal').modal();
                                        })
                                    }
                                });
                            }
                        } else {
                            $scope.tip = "**********";
                            $('#myModal').modal();
                        }
                    })
                },
                error: function (data) {
                    $rootScope.$apply(function () {
                        //配置了**********为接口异常情况下提示
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    })
                }
            });
        } else {
            angular.forEach(result.accountInfo.roleList, function (item) {
                angular.forEach(item.dataAuthList, function (row) {
                    if (row.fieldName === "provinceID") {
                        provinceList.push(row)
                    }
                    if (row.fieldName === "cityID") {
                        cityList.push(row)
                    }
                    if (row.fieldName === "countyID") {
                        countyList.push(row)
                    }
                });
            });
            $scope.provinceList = $scope.uniq(provinceList);
            $scope.cityList = $scope.uniq(cityList);
            $scope.countyList = $scope.uniq(countyList);
            localStorage.setItem("provinceList", JSON.stringify($scope.provinceList));
            localStorage.setItem("cityList", JSON.stringify($scope.cityList));
            localStorage.setItem("countyList", JSON.stringify($scope.countyList));
            if (result.directUrl) {
                $scope.menuUrl = result.directUrl
                + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
                $scope.checkPwdValid($scope.menuUrl);
            } else {
                $scope.menuUrl = '../menu/menu.html'
                + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
                $scope.checkPwdValid($scope.menuUrl);
            }
        }
        $scope.enterpriseTypeList = [];
        $scope.dataAuthList = [];

        // 企业类型缓存
        angular.forEach(roleList, function (item) {
            angular.forEach(item.dataAuthList, function (row) {
                if(row.fieldName == "reserved10" && row.tableName == "dsum_t_enterprise"){
                    $scope.enterpriseTypeList.push(row);
                }
                if(row.sourceType == 2){
                    $scope.dataAuthList.push(row);
                }
            });
        });
        localStorage.setItem("enterpriseTypeList", JSON.stringify($scope.enterpriseTypeList));
        localStorage.setItem("dataAuthList", JSON.stringify($scope.dataAuthList));

        return;
    };
    $scope.loginAction = function () {


        var req = {};
        if ($.cookie("token") != '' && $.cookie("token") != null) {
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/loginService/tokenAuth",
                data: JSON.stringify(req),
                success: function (result) {
                    $rootScope.$apply(function () {
                        var data = result.result;
                        if (data.resultCode == '**********') {
                            $scope.LoginAlreadyName = $.cookie('accountName');
                            $('#myModal1').modal();
                            setTimeout(function () {
                                window.location.href = '../menu/menu.html'
                                + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
                            }, 2500)
                            return;
                        } else {
                            $.removeCookie('accountID', {path: '/'});
                            $.removeCookie('enterpriseID', {path: '/'});
                            $.removeCookie('enterpriseName', {path: '/'});
                            $scope.loginSure();
                        }
                    })
                },
                error: function (result) {
                    $scope.loginSure();
                }

            });
        } else {
            $scope.loginSure();
        }
    }

    $scope.queryPC = function (loginRoleType, roleList) {
        var provinceList = [],
            cityList = [];
        countyList = [];
        if (loginRoleType === "superrManager") {
            //超管
            /*查询省份*/
            RestClientUtil.ajaxRequest({
                type: 'POST',
                url: "/ecpmp/ecpmpServices/enterpriseManageService/queryProvinceList",
                data: JSON.stringify({}),
                success: function (data) {
                    $rootScope.$apply(function () {
                        var result = data.result;
                        if (result.resultCode == '**********') {
                            $scope.provinceList = data.provinceList;
                            if ($scope.provinceList != null && $scope.provinceList.length > 0) {
                                var provinceIDs = [];
                                angular.forEach($scope.provinceList, function (item) {
                                    provinceIDs.push(item.provinceID);
                                });
                                /*查询地市*/
                                RestClientUtil.ajaxRequest({
                                    type: 'POST',
                                    url: "/ecpmp/ecpmpServices/enterpriseManageService/queryCityList",
                                    data: JSON.stringify({"provinceIDs": provinceIDs}),
                                    success: function (data) {
                                        $rootScope.$apply(function () {
                                            var result2 = data.result;
                                            if (result2.resultCode == '**********') {
                                                $scope.cityList = $scope.mapToList(data.cityList);
                                                localStorage.setItem("provinceList", JSON.stringify($scope.provinceList));
                                                localStorage.setItem("cityList", JSON.stringify($scope.cityList));

                                                if (result.directUrl) {
                                                    window.location.href = result.directUrl
                                                    + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
                                                } else {
                                                    window.location.href = '../menu/menu.html'
                                                    + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
                                                }
                                            } else {
                                                $scope.tip = result2.resultCode;
                                                $('#myModal').modal();
                                            }
                                        })
                                    },
                                    error: function (data) {
                                        $rootScope.$apply(function () {
                                            //配置了**********为接口异常情况下提示
                                            $scope.tip = "**********";
                                            $('#myModal').modal();
                                        })
                                    }
                                });
                            }
                        } else {
                            $scope.tip = "**********";
                            $('#myModal').modal();
                        }
                    })
                },
                error: function (data) {
                    $rootScope.$apply(function () {
                        //配置了**********为接口异常情况下提示
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    })
                }
            });
        } else {
            angular.forEach(roleList, function (item) {
                angular.forEach(item.dataAuthList, function (row) {
                    if (row.fieldName === "provinceID") {
                        provinceList.push(row)
                    }
                    if (row.fieldName === "cityID") {
                        cityList.push(row)
                    }
                    if (row.fieldName === "countyID") {
                        countyList.push(row)
                    }
                });
            });
            $scope.provinceList = $scope.uniq(provinceList);
            $scope.cityList = $scope.uniq(cityList);
            $scope.countyList = $scope.uniq(countyList);
            localStorage.setItem("provinceList", JSON.stringify($scope.provinceList));
            localStorage.setItem("cityList", JSON.stringify($scope.cityList));
            localStorage.setItem("countyList", JSON.stringify($scope.countyList));
            window.location.href = '../menu/menu.html'
                + ($.cookie("isGroupRemind")==='true'?'?isGroupRemind=true':'');
        }

        $scope.enterpriseTypeList = [];
        $scope.dataAuthList = [];

        // 企业类型缓存
        angular.forEach(roleList, function (item) {
            angular.forEach(item.dataAuthList, function (row) {
                if(row.fieldName == "reserved10" && row.tableName == "dsum_t_enterprise"){
                    $scope.enterpriseTypeList.push(row);
                }
                if(row.sourceType == 2){
                    $scope.dataAuthList.push(row);
                }
            });
        });
        localStorage.setItem("enterpriseTypeList", JSON.stringify($scope.enterpriseTypeList));
        localStorage.setItem("dataAuthList", JSON.stringify($scope.dataAuthList));

    }

    //获取短信验证
    $scope.getSmsVerifyCode = function () {
        var smsReq = {
            "account": {
                "accountName": $scope.userInfo.userAccount.trim(),
                "accountType": 3
            },
            "verifyType": $scope.isGroupRemind ? "7":"6"
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/loginService/getSmsVerifyCode",
            data: JSON.stringify(smsReq),
            success: function (result) {
                $rootScope.$apply(function () {
                    $scope.timeChange();
                })
            },
            error: function () {
                $rootScope.$apply(function (result) {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });
    };
    $scope.loginSmsEnable = false;
    $scope.getUserInfo = function () {
        if(!$scope.loginSmsEnable){
            return;
        }
        var req = {
           "accountName": $scope.userInfo.userAccount,
        };
        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/accountManageService/queryAccountMsisdn",
            data: JSON.stringify(req),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode==='**********'){
                        if(result.msisdn === "000****0000"){
                            $scope.msisdn = null;
                            return;
                        }
                        $scope.msisdn = result.msisdn;
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (result) {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });

    }
    $scope.getLoginSmsEnable= function () {

        RestClientUtil.ajaxRequest({
            type: 'POST',
            url: "/ecpmp/ecpmpServices/accountManageService/queryLoginSmsEnable",
            data: JSON.stringify(),
            success: function (result) {
                $rootScope.$apply(function () {
                    if(result.result.resultCode==='**********'){
                        $scope.loginSmsEnable = result.loginSmsEnable==="true";
                    }
                })
            },
            error: function () {
                $rootScope.$apply(function (result) {
                        $scope.tip = "**********";
                        $('#myModal').modal();
                    }
                )
            }
        });

    }
    //定时器
    $scope.timeChange = function () {
        $("#getSmsCode").addClass("disabled");
        var count = 60;
        $scope.timer = window.setInterval(function () {
            count -= 1;
            $("#getSmsCode").text(count + "s");
            if (count <= 0) {
                $("#getSmsCode").removeClass("disabled");
                window.clearInterval($scope.timer);
                $("#getSmsCode").text("获取短信验证");
            }
        }, 1000);
    };
})