<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.ConfigMapper" >

    <resultMap id="BaseResultMap" type="com.huawei.jaguar.dsdp.ecpm.dao.domain.ConfigWrapper" >
        <id column="cfgId" property="cfgId" jdbcType="INTEGER" />
        <result column="cfgKey" property="cfgKey" jdbcType="VARCHAR" />
        <result column="cfgValue" property="cfgValue" jdbcType="VARCHAR" />
        <result column="desc" property="desc" jdbcType="VARCHAR" />
        <result column="createTime" property="createTime" jdbcType="TIMESTAMP" />
        <result column="updateTime" property="updateTime" jdbcType="TIMESTAMP" />
    </resultMap>

    <sql id="Base_Column_List" >
        cfgId, cfgKey, cfgValue, desc, createTime, updateTime
    </sql>

    <select id="queryByKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
        select *
        from ecpm_t_config
        where cfgKey = #{cfgKey,jdbcType=VARCHAR}
    </select>

    <update id="updateByKey" parameterType="com.huawei.jaguar.dsdp.ecpm.dao.domain.ConfigWrapper" >
        update 
        	ecpm_t_config
        set 
        	cfgValue = #{cfgValue,jdbcType=VARCHAR},
        	updateTime = #{updateTime,jdbcType=TIMESTAMP}
        where 
        	cfgKey = #{cfgKey,jdbcType=VARCHAR}
    </update>
</mapper>