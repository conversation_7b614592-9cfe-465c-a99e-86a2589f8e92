angular.module("cy.icon.content", []).directive("cyIconContent", function () {
  return {
    restrict: "AE",
    templateUrl: "/qycy/ecpmp/directives/cy-icon-content/cy-icon-content.html",
    replace: true,
    scope: {
      tipContent: "@", // 内容
      position: "@", // 位置配置
      contentType: "@", // 内容枚举
      imgUrl: "@", // 图片地址
    },
    controller: [
      "$scope",
      "$sce", // 注入$sce服务用于HTML内容信任
      "$timeout", // 注入$timeout服务用于延迟
      function ($scope, $sce, $timeout) {
        $scope.isShow = false;
        const $ = jQuery;

        // 生成唯一ID，用于区分不同实例
        $scope.tooltipId = "tooltip_" + Math.random().toString(36).substr(2, 9);

        if (!$scope.imgUrl) {
          $scope.imgUrl = "/qycy/ecpmp/assets/images/ques.png";
        }

        // 处理提示内容
        function processContent(content) {
          if (!content) return "";
          // 将\n转换为<br/>
          content = content.replace(/\n/g, "<br/>");
          // 信任HTML内容
          return $sce.trustAsHtml(content);
        }
        // 从localStorage中获取提示内容
        function getHintContentFromStorage(type) {
          let contents = {};
          try {
            // 获取localStorage中的数据
            const hintData =
              JSON.parse(localStorage.getItem("hintContent")) || [];
            // 如果hintData为空，则返回空字符串
            if (!hintData) return "";
            hintData.forEach((item) => {
              if (item.type && item.hintContent) {
                contents[item.type] = item.hintContent;
              }
            });
          } catch (e) {
            console.error("解析提示内容数据失败:", e);
          }

          if (type == 3) {
            // 表示是主被叫
            const mainContent = "主叫彩印：<br/>" + contents[1] || "";
            const calleeContent = "被叫彩印：<br/>" + contents[2] || "";

            return mainContent && calleeContent
              ? mainContent + "<br/>" + calleeContent
              : mainContent || calleeContent;
          }
          // 表示是其他类型
          return contents[type] || "";
        }
        // 将滚动容器滚动到顶部的函数
        function resetScroll() {
          $timeout(function () {
            // 使用jQuery寻找当前tooltip实例的滚动容器
            var tooltipContainer = $(`#${$scope.tooltipId} .tooltip-inner`);
            if (tooltipContainer.length > 0) {
              tooltipContainer.scrollTop(0);
            }
          }, 50); // 延迟50毫秒以确保DOM已更新
        }
        if (!$scope.tipContent && $scope.contentType) {
          $scope.tipContent = getHintContentFromStorage($scope.contentType);
        }

        // 处理HTML内容
        $scope.processedContent = processContent($scope.tipContent);

        // 监听tipContent的变化
        $scope.$watch("tipContent", function (newVal) {
          $scope.processedContent = processContent(newVal);
        });
        // 监听contentType的变化
        $scope.$watch("contentType", function (newVal) {
          $scope.tipContent = getHintContentFromStorage(newVal);
          $scope.processedContent = processContent($scope.tipContent);
          // 内容更新后，重置滚动条位置
          resetScroll();
        });

        // 设置默认位置
        if (!$scope.position) {
          $scope.position = "right-top";
        }

        // 验证位置值是否合法
        var validPositions = [
          "top-left",
          "top",
          "top-right",
          "right-top",
          "right",
          "right-bottom",
          "bottom-left",
          "bottom",
          "bottom-right",
          "left-top",
          "left",
          "left-bottom",
        ];

        if (validPositions.indexOf($scope.position) === -1) {
          $scope.position = "top";
        }

        var hideTimer = null;

        $scope.showTip = function () {
          if (hideTimer) {
            clearTimeout(hideTimer);
            hideTimer = null;
          }
          $scope.$evalAsync(function () {
            $scope.isShow = true;
            // 显示提示时也重置滚动条位置
            resetScroll();
          });
        };

        $scope.startHide = function () {
          hideTimer = setTimeout(function () {
            $scope.$evalAsync(function () {
              $scope.isShow = false;
            });
          }, 100);
        };

        // 清理定时器
        $scope.$on("$destroy", function () {
          if (hideTimer) {
            clearTimeout(hideTimer);
          }
        });
      },
    ],
  };
});
