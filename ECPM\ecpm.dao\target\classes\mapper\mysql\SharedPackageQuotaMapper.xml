<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.jaguar.dsdp.ecpm.dao.mapper.SharedPackageQuotaMapper">

    <insert id="insertOrUpdate" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO ecpm_t_sharedPackage_quota ( subEnterpriseID, quota, useCount, status, `month`, createTime, updateTime )
        VALUES(#{subEnterpriseID},${quota},${useCount},#{status},#{month},#{createTime},#{updateTime})
        ON DUPLICATE KEY UPDATE quota = quota + ${quota};
    </insert>

    <select id="getCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM ecpm_t_sharedPackage_quota
        WHERE subEnterpriseID = #{subEnterpriseID} AND `type` = 1
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>

    </select>
    <update id="updateStatus">
        UPDATE ecpm_t_sharedPackage_quota set status = #{status} WHERE subEnterpriseID = #{subEnterpriseID}
    </update>

    <select id="qureyDataByStatusAndMonth" resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SharedPackageQuotaWrapper">
        SELECT *
        FROM ecpm_t_sharedPackage_quota
        WHERE month = #{month} AND `type` = 1
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <select id="getExistsQuota" resultType="java.lang.Integer">
        SELECT
            IFNULL(SUM(quota),0)
        FROM
            ecpm_t_sharedPackage_quota
        WHERE
            `month` = DATE_FORMAT( CURRENT_DATE, '%Y%m' )
          AND subEnterpriseID = #{subEnterpriseID} AND `type` = 1;
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ecpm_t_sharedPackage_quota (subEnterpriseID, quota, useCount, status, month, createTime, updateTime)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.subEnterpriseID}, #{item.quota}, #{item.useCount}, #{item.status}, #{item.month}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <select id="getRemainAvailableQuota" resultType="java.lang.Integer">
        SELECT
            quota-useCount
        FROM
            ecpm_t_sharedPackage_quota
        WHERE
            subEnterpriseID = #{subEnterpriseID}
            AND `month` = DATE_FORMAT( CURRENT_DATE, '%Y%m' )
            AND `type` = 1
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <update id="updateUseCount">
        UPDATE ecpm_t_sharedPackage_quota set useCount = useCount + #{realExceedQuota}
        WHERE subEnterpriseID = #{subEnterpriseID} AND `month` = DATE_FORMAT( CURRENT_DATE, '%Y%m' )
    </update>






    <select id="selectSharePackage"
            resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SharedPackageQuotaWrapper">
        SELECT ID, subEnterpriseID, quota, useCount, status, `month`, createTime, updateTime,`type`
        FROM ecpm_t_sharedPackage_quota
        WHERE subEnterpriseID = #{subEnterpriseID}
          AND `month` = #{month}
          AND `type` = #{type}
          <if test="status != null and status != ''">
            AND status = #{status}
          </if>
    </select>

    <update id="updateQuota">
        UPDATE ecpm_t_sharedPackage_quota set quota = #{quota}
        WHERE ID = #{ID}
    </update>

    <insert id="insert">
        INSERT INTO ecpm_t_sharedPackage_quota (subEnterpriseID, quota, useCount, status, `month`, createTime, updateTime, `type`)
        VALUES
        (#{subEnterpriseID}, #{quota}, #{useCount}, #{status}, #{month}, #{createTime}, #{updateTime}, #{type})
    </insert>

    <select id="getResidualQuota" resultType="java.lang.Integer">
        SELECT
        quota-useCount
        FROM
        ecpm_t_sharedPackage_quota
        WHERE
        subEnterpriseID = #{subEnterpriseID}
        AND `month` = DATE_FORMAT( CURRENT_DATE, '%Y%m' )
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="type != null">
            AND `type` = #{type}
        </if>
    </select>

    <select id="selectListByMonth"
            resultType="com.huawei.jaguar.dsdp.ecpm.dao.domain.SharedPackageQuotaWrapper">
        select
            ID, subEnterpriseID, quota, useCount, status, `month`, createTime, updateTime, `type`
        from ecpm_t_sharedPackage_quota
        where
            CONVERT(`month`,SIGNED) <![CDATA[ >= ]]> CONVERT(#{month},SIGNED)
        <if test="type != null">
            AND `type` = #{type}
        </if>
        <if test="subEnterpriseID != null and subEnterpriseID != ''">
            AND `subEnterpriseID` = #{subEnterpriseID}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <delete id="deleteById">
        delete from ecpm_t_sharedPackage_quota where ID= #{ID}
    </delete>
</mapper>
